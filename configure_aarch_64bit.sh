#!/bin/sh

echo_usage()
{
    cat <<-END
usage: 
    ./configure_unix_x86_64bit.sh [-h] [-b <CMAKE_BUILD_TYPE> | -c <CMAKE_BUILD_TYPE> | -r <CMAKE_BUILD_TYPE>] [-m <model>] [-q 4 | -q 5]
        
configure , build or rebuild xunit

CMAKE_BUILD_TYPE:
    1. Release:             high optimization level, no debug info, code or asserts.
    2. Debug:               No optimization, asserts enabled, [custom debug (output) code enabled],
                            debug info included in executable (so you can step through the code with a
                            debugger and have address to source-file:line-number translation).
    3. RelWithDebInfo:      optimized, *with* debug info, but no debug (output) code or asserts.
    4. MinSizeRel:          same as Release but optimizing for size rather than speed.

Options:
    -h                      help
    -b <CMAKE_BUILD_TYPE>   build
    -c <CMAKE_BUILD_TYPE>   cmake configure, do not build.
    -r <CMAKE_BUILD_TYPE>   clean and build
    -m <model>              model, like Phoenix, Apple etc.
    -q <qt version>         choose qt version. 4 or 5
    -Q <qt path>            QT path, default is /opt/Qt4.8.7/4.8.7/gcc_64/bin/qmake.
    -B <boost path>         BOOST path, default is /opt/boost_1_73_0.

examples:

     Qt4
    ./configure_unix_x86_64bit.sh -Q /opt/Qt4.8.7/4.8.7/gcc_64/bin/qmake -B /opt/boost_1_73_0 -r Release -q 4 -m Phoenix
    ./configure_unix_x86_64bit.sh -Q /opt/Qt4.8.7/4.8.7/gcc_64/bin/qmake -B /opt/boost_1_73_0 -r Debug -q 4 -m Phoenix
    ./configure_unix_x86_64bit.sh -Q /opt/Qt4.8.7/4.8.7/gcc_64/bin/qmake -B /opt/boost_1_73_0 -b Release -q 4 -m Phoenix
    ./configure_unix_x86_64bit.sh -Q /opt/Qt4.8.7/4.8.7/gcc_64/bin/qmake -B /opt/boost_1_73_0 -b Debug -q 4 -m Phoenix
    ./configure_unix_x86_64bit.sh -Q /opt/Qt4.8.7/4.8.7/gcc_64/bin/qmake -B /opt/boost_1_73_0 -r RelWithDebInfo -q 4 -m Phoenix

     Qt5
    ./configure_unix_x86_64bit.sh -Q /opt/Qt/5.15.2/gcc_64 -B /opt/boost_1_75_0 -r Release -q 5 -m Phoenix
    ./configure_unix_x86_64bit.sh -Q /opt/Qt/5.15.2/gcc_64 -B /opt/boost_1_75_0 -r Debug -q 5 -m Phoenix
    ./configure_unix_x86_64bit.sh -Q /opt/Qt/5.15.2/gcc_64 -B /opt/boost_1_75_0 -b Release -q 5 -m Phoenix
    ./configure_unix_x86_64bit.sh -Q /opt/Qt/5.15.2/gcc_64 -B /opt/boost_1_75_0 -b Debug -q 5 -m Phoenix
    ./configure_unix_x86_64bit.sh -Q /opt/Qt/5.15.2/gcc_64 -B /opt/boost_1_75_0 -r RelWithDebInfo -q 5 -m Phoenix

END
    exit 0
}

while getopts "hb:c:r:m:q:Q:B:" arg
do
    case $arg in
        h)  echo_usage;;
        b)  BUILD="-b"
            BUILDTYPE=$OPTARG;;
        c)  BUILD="-c"
            BUILDTYPE=$OPTARG;;
        r)  BUILD="-r"
            BUILDTYPE=$OPTARG;;
        m)  MODEL=$OPTARG;;
        q)  BUILD_QT=$OPTARG;;
        Q)  QTPATH=$OPTARG;;
        B)  BOOSTPATH=$OPTARG;;
    esac
done

deal_paras()
{
    if [ "$BUILD_QT" = "" ]; then
        BUILD_QT="5"
    fi

    if [ "$BUILD_QT" = "4" ]; then
        QTPARA="QT_QMAKE_EXECUTABLE"
        if [ ! -f "$QTPATH" ]; then
            QTPATH="/opt/Qt4.8.7/4.8.7/gcc_64/bin/qmake"
        fi
        if [ ! -d "$BOOSTPATH" ]; then
            BOOSTPATH="/opt/boost_1_73_0"
        fi
    elif [ "$BUILD_QT" = "5" ]; then
        QTPARA="CMAKE_PREFIX_PATH"
        if [ ! -d "$QTPATH" ]; then
            QTPATH="/opt/Qt/5.15.2/aarch64"
        fi
        if [ ! -d "$BOOSTPATH" ]; then
            BOOSTPATH="/opt/boost_1_75_0"
        fi
    fi

    if [ -z "$BUILD" ]; then
        BUILD="-r";
    fi

    if [ -z "$BUILDTYPE" ]; then
        BUILDTYPE="Release";
    fi

    if [ "$MODEL" = "" ]; then
        MODEL="Grape"
    fi
}

setup_color() 
{
    # Only use colors if connected to a terminal
    if [ -t 1 ]; then
            RED=$(printf '\033[31m')
            GREEN=$(printf '\033[32m')
            YELLOW=$(printf '\033[33m')
            BLUE=$(printf '\033[34m')
            BOLD=$(printf '\033[1m')
            RESET=$(printf '\033[m')
    else
            RED=""
            GREEN=""
            YELLOW=""
            BLUE=""
            BOLD=""
            RESET=""
    fi
}

configure_common()
{
    CMAKE_COMMON_PARA="\
-S . \
-G Ninja \
-B build/${MODEL} \
-DCMAKE_TOOLCHAIN_FILE=./cmake/arm_toolchain.cmake \
-DBUILD_TAR=aarch \
-DCMAKE_BUILD_TYPE=${BUILDTYPE} \
-DMODEL=${MODEL} \
-DUSE_GRAPHICS_DEBUG=OFF \
-DUSE_RUN_MODE_VIRTUAL=OFF \
-DUSE_VIRTUAL_KEYBOARD=ON \
-DUSE_KEYBOARD=ON \
-DUSE_PINYINIME=ON \
-DUSE_SIMULATEORIENTATION=ON \
-DUSE_POWERKEY=OFF \
-DUSE_POWERBUTTONMonitor=ON \
-DUSE_FINGERPRINT=OFF \
-DUSE_OPENFINGERPRINT=OFF \
-DUSE_TARGET_PALM=OFF \
-DUSE_USB_IO=OFF \
-DUSE_4D=OFF \
-DUSE_IIMAGE=OFF \
-DUSE_TIRPC=ON \
-DUSE_PW_SOUND=ON \
-DUSE_IMGVERSION=OFF \
-DUSE_ZEUS=ON \
-DUSE_USCONTROLENGINE=ON \
-DUSE_WALLFILTER=ON \
-DUSE_AJECPLDUPDATE=OFF \
-DUSE_OPENGLES=ON \
-DUSE_OPENGL=OFF \
-DUSE_SONOVTI=OFF \
-DUSE_AUTOBLINE=ON \
-DUSE_DOPPLERADJUST=ON \
-DUSE_ELEMENTDETECT=ON \
-DXUNITDIR=$PWD
"
}

configure_grape()
{
    CMAKE_MODEL_PARA=" \
-DUSE_IIMAGE=ON \
-DUSE_PWTRACE=ON \
-DUSE_HIGHDENSITY_INT=ON \
-DUSE_VIRTUAL_KEYBOARD=OFF \
-DUSE_AUTOBLINE=ON \
-DUSE_SONOVTI=OFF \
-DUSE_IMT=ON \
-DUSE_AUTODIAPH=ON \
-DUSE_AUTOCYSTIC=ON \
-DUSE_AUTOBLADDER=ON \
-DUSE_SONOTHYROID=ON \
-DUSE_RTIMT=ON \
-DUSE_SONOOB=ON \
-DUSE_SONONERVE=ON \
-DUSE_PANORAMIC=ON \
-DUSE_SONOMSK=ON \
-DUSE_AUTOEF=ON \
-DUSE_SONOVTI=ON \
-DUSE_SONOVF=ON \
-DUSE_FREEM=ON
"
}

configure_neweco()
{
    CMAKE_MODEL_PARA=" \
-DUSE_IIMAGE=ON \
-DUSE_PWTRACE=ON \
-DUSE_HIGHDENSITY_INT=ON \
-DUSE_VIRTUAL_KEYBOARD=OFF \
-DUSE_AUTOBLINE=ON \
-DUSE_SONOVTI=OFF \
-DUSE_IMT=ON \
-DUSE_AUTODIAPH=ON \
-DUSE_AUTOCYSTIC=ON \
-DUSE_AUTOBLADDER=ON \
-DUSE_SONOTHYROID=ON \
-DUSE_RTIMT=ON \
-DUSE_SONOOB=ON \
-DUSE_SONONERVE=ON \
-DUSE_PANORAMIC=ON \
-DUSE_SONOMSK=ON \
-DUSE_AUTOEF=ON \
-DUSE_SONOVTI=ON \
-DUSE_SONOVF=ON \
-DUSE_FREEM=ON
"
}

configure_grapeTablet()
{
    CMAKE_MODEL_PARA=" \
-DUSE_IIMAGE=ON \
-DUSE_PWTRACE=ON \
-DUSE_KEYBOARD=OFF \
-DUSE_HIGHDENSITY_INT=ON \
-DUSE_PINYINIME=ON
"
}

do_conifgure()
{
    if [ "$BUILD" != "-b" ]; then
        rm -rf build/${MODEL}
        mkdir -p build/${MODEL}

        cmake ${CMAKE_COMMON_PARA}${CMAKE_MODEL_RELEASE}${CMAKE_MODEL_PARA}
        echo "${YELLOW}cmake ${CMAKE_COMMON_PARA}${CMAKE_MODEL_RELEASE}${CMAKE_MODEL_PARA}${RESET}"

        echo "-- QTPATH=$QTPATH"
        echo "-- BOOSTPATH=$BOOSTPATH"
        echo "-- BUILD=$BUILD"
        echo "-- BUILDTYPE=$BUILDTYPE"
        echo "-- MODEL=${RED}$MODEL${RESET}"
    fi
}

do_build()
{
    cd build/${MODEL}

    if ! ninja ; then
        exit 1;
    fi
    
    cd ..
}

main()
{
    SECONDS=0

    setup_color

    deal_paras "$@"

    configure_common

    if [ "$MODEL" = "Grape" ]; then
        configure_grape
    elif [ "$MODEL" = "GrapeTablet" ]; then
        configure_grapeTablet
    elif [ "$MODEL" = "NewECO" ]; then
        configure_neweco
    fi
   
    do_conifgure

    if [ "$BUILD" != "-c" ]; then
        do_build
    fi

    duration=$SECONDS
    echo "$(($duration / 60)) minutes and $(($duration % 60)) seconds elapsed."

    exit 0;
}

main "$@"
