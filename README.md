# 超声软件工程

## 目录
1. [开发环境配置](#开发环境配置)
2. [编译说明](#编译说明)
3. [开发流程](#开发流程)
4. [调试配置](#调试配置)

## 开发环境配置

### 依赖环境
- Qt Creator
- CMake
- Git

### 目录结构
```
├── ci/            - 持续集成脚本
├── cmake/         - cmake脚本
├── src/           - 源代码
├── tests/         - 测试代码
├── ts/            - 翻译文件
└── res/           - 资源文件
```

## 编译说明

### 支持的产品型号
- SonoMax
- Apple
- SonoBook9
- SR9
- USAPI

### 编译脚本
提供多个平台的编译脚本：
- configure_apple_ios_64bit.sh     - iOS ARM 64位
- configure_unix_x86_64bit.sh      - Linux x86 64位
- configure_windows_x86_32bit.sh   - Windows x86 32位
- configure_windows_x86_64bit.sh   - Windows x86 64位

使用方法：
```bash
./configure_unix_x86_64bit.sh -h  # 获取帮助信息
```

## 开发流程

### Git分支策略
- pangu: 主开发分支
- feature_*: 特性分支
- hotfix_*: 热修复分支
- *_Release: 发布分支

### 代码提交规范
提交信息格式：
```
[类型]:简要描述

实现方案：xxx
影响范围：xxx
Test Case:xxx
```
类型包括：FUNC(新功能)、BUG(缺陷修复)、IMPROVE(改进)等

## 调试配置

### 日志配置
- 配置文件：res/ini/applogger.ini
- 日志级别：ERROR > WARN > INFO > DEBUG
- 支持log4qt和qDebug两种写法

### 虚拟数据调试
1. res/ini/setting.ini: IsIODeviceVirtual=true
2. res/modelfiles/apple/modelini/modelconfig.ini: SupportFreezeOutage=false

### 键盘快捷键
- CTRL + ALT + D: 切换键盘映射功能

# 版本交付工作流步骤

```xx
                           hotfix_*
                          /    
                         /             
                        /            
                       /            
                    ---SonoAir_V1.3_Release      --SonoAir_V1.4_Release       --SonoAir_V1.*_Release
                   /                            /                            /
                  /                            /                            /
--------------------------------------------------------------------------------------------------pangu
   \                         \                                                               \
    \                         \                                                               \
     \                         ----------------------SR9_v0.7.3_Release                        feature_*
      \                                         \                   \
       ---SonoAir_V1.3_cn_Release                \                   \                     
                                                  \                   ---SR9_v0.7.8_Release   
                                                   \
                                                    ---SR9_v0.7.7_Release
                                                                 
```

1. feature 分支上编写代码、提交、推送到该远程仓库；
2. 发起 feature 分支到 pangu 分支的合并请求；
3. 开发经理或代码评审委员会成员对合并的代码进行评审；
4. 提出评审建议，通过或不通过？通过继续下一步流程；不通过返回feature分支进行修改重复以上流程（此时之前的合并请求如果没有关闭，不用重新提交合并申请）。
5. feature 分支代码合并到 pangu 分支；
6. 在某个合适的时间点从 pangu 分支创建 release 分支，并在 release 分支上进行测试；
7. 产生 issues 和 bug featrue
8. bug 未修复完之前，重复第一步
9. cherry-pick（遴选） 方式从 pangu 中挑选某些功能到 release 分支；
10. 发布 release 分支。

分支版本说明： http://192.168.20.40:8090/pages/viewpage.action?pageId=12124797

# 文件说明


* [ci](ci) 持续集成脚本目录
* [cmake](cmake) cmake脚本目录
* [src](src) 源代码目录
* [tests](tests) 测试代码目录（单元测试、自动化测试等）
* [ts](ts) 翻译文件
* [commitTemplate.txt](commitTemplate.txt) git提交模板
* [configure_apple_ios_64bit.sh](configure_apple_ios_64bit.sh) 用于编译苹果ios系统ARM架构64位软件
* [configure_unix_x86_64bit.sh](configure_unix_x86_64bit.sh) 用于编译linux系统x86架构64位软件
* [configure_windows_x86_32bit.sh](configure_windows_x86_32bit.sh) 用于编译windows系统x86架构32位软件
* [configure_windows_x86_64bit.sh](configure_windows_x86_64bit.sh) 用于编译windows系统x86架构64位软件
* [update_ts_file.sh](update_ts_file.sh) 用于更新翻译


# 运行虚拟参数数据配置

1、在res/ini/setting.ini中打开虚拟数据功能，新增或修改IsIODeviceVirtual=true
2、在res/modelfiles/apple/modelini/modelconfig.ini中关闭冻结断电功能，修改SupportFreezeOutage=false
3、Windows环境下，如果运行插件失败，可用mainmoduletest代替启动调试

# git提交格式

参考commitTemplate.txt
详见http://192.168.20.40:8090/pages/viewpage.action?pageId=2097975

在命令行中提交如何换行

只输入一个`"`,之后就Enter都不会触发提交操作，直到输入第二个`"`，再按Enter提交git

```git
// 步骤一: 输入第一行
git commit -m "[FUNC]:Short summary of the change

// 步骤二: 按Enter 再按回车空一行
git commit -m "[FUNC]:Short summary of the change
空行
实现方案：xxxxxxxx

// 步骤三: 输入完毕，补齐引号，提交！
git commit -m "[FUNC]:Short summary of the change

实现方案：xxxxxxxx
影响范围：xxxxxxxx
Test Case:xxxxxxxx"
```

# 日志配置

资源目录res/ini/applogger.ini

* `consoleLog`    废弃
* `fileLog`       用来控制log的打开和关闭，默认为打开

res/ini/logger.conf文件用来配置log信息

* logger.rootLogger=DEBUG,Console,Default   用于Debug调试
* logger.rootLogger=INFO,Default            用于Release版本（默认）

根据需要上面的配置选择一个，注释掉另外一个，也可以根据下边的级别自己配置

默认有以下4中级别
* `ERROR` 为严重错误 主要是程序的错误
* `WARN` 为一般警告，比如session丢失
* `INFO` 为一般要显示的信息，比如登录登出
* `DEBUG` 为程序的调试信息

同时支持log4qt写法和qDebug写法
```log
//log4qt 写法
Log4Qt::Logger * log = Log4Qt::Logger::rootLogger();
log->debug("hello world.");

//qDebug 写法
qDebug() << "hello world.";
```

# 关闭键盘按键映射超声功能

超声程序启动默认会把键盘案件注册为超声功能，具体参见`SystemShortcut.cpp`

可通过 `CTRL + ALT + D` 组合键关闭，再按恢复，方便调试和功能切换。

