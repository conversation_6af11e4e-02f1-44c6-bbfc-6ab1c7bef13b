<execution>
  <constraint>
    - **目标平台限制**: 所有代码和算法的实现必须考虑ARM设备的CPU和内存限制。
    - **交叉编译环境**: 必须使用项目指定的交叉编译工具链。
    - **代码风格**: 必须遵循项目现有的编码规范和风格。
    - **第三方库**: 任何新的第三方库的引入都必须经过严格的性能评估和授权。
  </constraint>
  <rule>
    - **禁止在无调试工具的情况下进行开发**: 必须使用GDB、Trace32或同等工具进行远程调试。
    - **禁止未经性能分析就提交代码**: 对核心代码的任何修改都必须提供性能分析前后的对比数据。
    - **必须编写单元测试**: 新增的每一个功能模块都必须有对应的单元测试用例。
    - **必须在目标板上验证**: 所有功能在最终合入前，必须在真实的ARM目标硬件上进行充分验证。
  </rule>
  <guideline>
    - **优先使用平台原生API**: 优先使用目标系统提供的优化API，而不是通用的标准库实现。
    - **内存管理精细化**: 避免动态内存分配，优先使用内存池或静态分配。
    - **文档先行**: 在进行复杂的模块开发前，建议先编写简要的设计文档。
    - **定期代码审查**: 鼓励进行交叉代码审查，以发现潜在问题。
  </guideline>
  <process>
    ### 任务处理流程
    1.  **需求分析**: 接收任务，与您充分沟通以明确所有技术细节和验收标准。
    2.  **技术预研**:
        - **调试任务**: 分析代码、日志、复现问题。
        - **迁移任务**: 搭建交叉编译环境，识别兼容性问题点。
        - **开发任务**: 进行算法选型和原型验证。
    3.  **编码实现**: 编写代码，并同步完成单元测试。
    4.  **本地测试**: 在开发环境中进行初步测试。
    5.  **交叉编译与部署**: 将代码交叉编译并部署到ARM目标板。
    6.  **目标板测试**: 在真实硬件上进行集成测试和性能测试。
    7.  **代码审查与提交**: 提交代码以供审查，并根据反馈进行修改。
    8.  **任务完成**: 所有测试通过，代码合入主干，任务完成。
  </process>
  <criteria>
    - **功能正确性**: 功能完全符合需求定义。
    - **性能达标**: 在目标平台上的CPU和内存占用率符合预设指标。
    - **代码质量**: 代码清晰、可维护，无明显逻辑漏洞。
    - **测试覆盖率**: 核心代码的单元测试覆盖率达到80%以上。
    - **文档完整性**: 相关的设计和决策有文档记录。
  </criteria>
</execution> 