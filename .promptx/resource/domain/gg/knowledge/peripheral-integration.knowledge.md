# 外设集成专业知识

## 硬件抽象层设计

### 设备接口抽象
```cpp
#include <memory>
#include <vector>
#include <functional>
#include <QObject>
#include <QTimer>

// 设备状态枚举
enum class DeviceState {
    Disconnected,
    Connecting,
    Connected,
    Error,
    Busy
};

// 设备类型枚举
enum class DeviceType {
    SerialDevice,
    USBDevice,
    NetworkDevice,
    BluetoothDevice
};

// 抽象设备接口
class IDevice : public QObject {
    Q_OBJECT
    
public:
    virtual ~IDevice() = default;
    
    virtual bool initialize() = 0;
    virtual bool connect() = 0;
    virtual void disconnect() = 0;
    virtual bool isConnected() const = 0;
    virtual DeviceState getState() const = 0;
    virtual DeviceType getType() const = 0;
    virtual QString getDeviceInfo() const = 0;
    
    virtual bool sendData(const QByteArray &data) = 0;
    virtual QByteArray receiveData() = 0;
    
    virtual bool setParameter(const QString &key, const QVariant &value) = 0;
    virtual QVariant getParameter(const QString &key) const = 0;
    
signals:
    void stateChanged(DeviceState state);
    void dataReceived(const QByteArray &data);
    void errorOccurred(const QString &error);
    void parameterChanged(const QString &key, const QVariant &value);
};

// 设备工厂模式
class DeviceFactory {
public:
    using CreateFunction = std::function<std::unique_ptr<IDevice>(const QString&)>;
    
    static DeviceFactory& instance() {
        static DeviceFactory factory;
        return factory;
    }
    
    void registerDevice(DeviceType type, CreateFunction createFunc) {
        creators_[type] = createFunc;
    }
    
    std::unique_ptr<IDevice> createDevice(DeviceType type, const QString &config) {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second(config);
        }
        return nullptr;
    }
    
private:
    std::map<DeviceType, CreateFunction> creators_;
};
```

### USB设备管理
```cpp
#include <QUsbDevice>
#include <QUsbEndpoint>
#include <QUsbInterface>
#include <libusb-1.0/libusb.h>

class USBDevice : public IDevice {
    Q_OBJECT
    
public:
    struct USBDeviceInfo {
        uint16_t vendorId;
        uint16_t productId;
        QString manufacturer;
        QString product;
        QString serialNumber;
        uint8_t busNumber;
        uint8_t deviceAddress;
    };
    
    explicit USBDevice(const USBDeviceInfo &info, QObject *parent = nullptr);
    ~USBDevice() override;
    
    bool initialize() override;
    bool connect() override;
    void disconnect() override;
    bool isConnected() const override;
    DeviceState getState() const override;
    DeviceType getType() const override { return DeviceType::USBDevice; }
    QString getDeviceInfo() const override;
    
    bool sendData(const QByteArray &data) override;
    QByteArray receiveData() override;
    
    bool setParameter(const QString &key, const QVariant &value) override;
    QVariant getParameter(const QString &key) const override;
    
    // USB特定方法
    bool claimInterface(int interfaceNumber);
    bool releaseInterface(int interfaceNumber);
    bool bulkTransfer(uint8_t endpoint, const QByteArray &data, int timeout = 1000);
    QByteArray bulkReceive(uint8_t endpoint, int maxSize = 1024, int timeout = 1000);
    
    static QList<USBDeviceInfo> enumerateDevices();
    static QList<USBDeviceInfo> findDevices(uint16_t vendorId, uint16_t productId);
    
private slots:
    void checkDeviceStatus();
    
private:
    bool openDevice();
    void closeDevice();
    QString getStringDescriptor(uint8_t index);
    
    USBDeviceInfo deviceInfo_;
    libusb_context *context_ = nullptr;
    libusb_device_handle *handle_ = nullptr;
    DeviceState state_ = DeviceState::Disconnected;
    QTimer *statusTimer_;
    
    // 配置参数
    QMap<QString, QVariant> parameters_;
    int currentInterface_ = -1;
    uint8_t bulkInEndpoint_ = 0x81;
    uint8_t bulkOutEndpoint_ = 0x01;
};

// USB设备枚举
QList<USBDevice::USBDeviceInfo> USBDevice::enumerateDevices() {
    QList<USBDeviceInfo> devices;
    
    libusb_context *ctx = nullptr;
    if (libusb_init(&ctx) != 0) {
        return devices;
    }
    
    libusb_device **deviceList;
    ssize_t count = libusb_get_device_list(ctx, &deviceList);
    
    for (ssize_t i = 0; i < count; ++i) {
        libusb_device *device = deviceList[i];
        libusb_device_descriptor desc;
        
        if (libusb_get_device_descriptor(device, &desc) == 0) {
            USBDeviceInfo info;
            info.vendorId = desc.idVendor;
            info.productId = desc.idProduct;
            info.busNumber = libusb_get_bus_number(device);
            info.deviceAddress = libusb_get_device_address(device);
            
            // 获取字符串描述符
            libusb_device_handle *handle;
            if (libusb_open(device, &handle) == 0) {
                unsigned char buffer[256];
                
                if (libusb_get_string_descriptor_ascii(handle, desc.iManufacturer, 
                                                     buffer, sizeof(buffer)) > 0) {
                    info.manufacturer = QString::fromUtf8(reinterpret_cast<char*>(buffer));
                }
                
                if (libusb_get_string_descriptor_ascii(handle, desc.iProduct, 
                                                     buffer, sizeof(buffer)) > 0) {
                    info.product = QString::fromUtf8(reinterpret_cast<char*>(buffer));
                }
                
                if (libusb_get_string_descriptor_ascii(handle, desc.iSerialNumber, 
                                                     buffer, sizeof(buffer)) > 0) {
                    info.serialNumber = QString::fromUtf8(reinterpret_cast<char*>(buffer));
                }
                
                libusb_close(handle);
            }
            
            devices.append(info);
        }
    }
    
    libusb_free_device_list(deviceList, 1);
    libusb_exit(ctx);
    
    return devices;
}
```

### 通信协议实现
```cpp
#include <QStateMachine>
#include <QState>
#include <QFinalState>
#include <QTimer>
#include <QQueue>

// 协议帧结构
struct ProtocolFrame {
    uint8_t header[2] = {0xAA, 0x55};  // 帧头
    uint8_t length;                     // 数据长度
    uint8_t command;                    // 命令码
    QByteArray data;                    // 数据
    uint8_t checksum;                   // 校验和
    uint8_t footer = 0x0D;              // 帧尾
    
    QByteArray toByteArray() const {
        QByteArray frame;
        frame.append(reinterpret_cast<const char*>(header), 2);
        frame.append(length);
        frame.append(command);
        frame.append(data);
        frame.append(checksum);
        frame.append(footer);
        return frame;
    }
    
    static ProtocolFrame fromByteArray(const QByteArray &data) {
        ProtocolFrame frame;
        if (data.size() < 6) return frame;
        
        frame.header[0] = data[0];
        frame.header[1] = data[1];
        frame.length = data[2];
        frame.command = data[3];
        
        if (data.size() >= 6 + frame.length) {
            frame.data = data.mid(4, frame.length);
            frame.checksum = data[4 + frame.length];
            frame.footer = data[5 + frame.length];
        }
        
        return frame;
    }
    
    bool isValid() const {
        if (header[0] != 0xAA || header[1] != 0x55) return false;
        if (footer != 0x0D) return false;
        if (data.size() != length) return false;
        
        // 计算校验和
        uint8_t sum = length + command;
        for (char byte : data) {
            sum += static_cast<uint8_t>(byte);
        }
        
        return checksum == sum;
    }
};

// 协议处理器
class ProtocolProcessor : public QObject {
    Q_OBJECT
    
public:
    explicit ProtocolProcessor(QObject *parent = nullptr);
    
    void processIncomingData(const QByteArray &data);
    void sendCommand(uint8_t command, const QByteArray &data = QByteArray());
    
    void setDevice(IDevice *device) { device_ = device; }
    
signals:
    void frameReceived(const ProtocolFrame &frame);
    void commandResponse(uint8_t command, const QByteArray &data);
    void errorOccurred(const QString &error);
    
private slots:
    void onDeviceDataReceived(const QByteArray &data);
    void processFrameQueue();
    void handleTimeout();
    
private:
    void parseBuffer();
    uint8_t calculateChecksum(const ProtocolFrame &frame) const;
    
    IDevice *device_ = nullptr;
    QByteArray receiveBuffer_;
    QQueue<ProtocolFrame> frameQueue_;
    QTimer *processTimer_;
    QTimer *timeoutTimer_;
    
    // 状态机
    QStateMachine *stateMachine_;
    QState *idleState_;
    QState *waitingState_;
    QState *processingState_;
    
    static constexpr int FRAME_TIMEOUT = 5000;  // 5秒超时
    static constexpr int MAX_FRAME_SIZE = 1024;
};

void ProtocolProcessor::processIncomingData(const QByteArray &data) {
    receiveBuffer_.append(data);
    
    // 限制缓冲区大小
    if (receiveBuffer_.size() > MAX_FRAME_SIZE * 10) {
        receiveBuffer_.clear();
        emit errorOccurred("接收缓冲区溢出");
        return;
    }
    
    parseBuffer();
}

void ProtocolProcessor::parseBuffer() {
    while (receiveBuffer_.size() >= 6) {  // 最小帧长度
        // 查找帧头
        int headerIndex = -1;
        for (int i = 0; i < receiveBuffer_.size() - 1; ++i) {
            if (receiveBuffer_[i] == static_cast<char>(0xAA) && 
                receiveBuffer_[i + 1] == static_cast<char>(0x55)) {
                headerIndex = i;
                break;
            }
        }
        
        if (headerIndex == -1) {
            receiveBuffer_.clear();
            break;
        }
        
        // 移除帧头之前的数据
        if (headerIndex > 0) {
            receiveBuffer_.remove(0, headerIndex);
        }
        
        // 检查是否有完整帧
        if (receiveBuffer_.size() < 6) break;
        
        uint8_t length = receiveBuffer_[2];
        int frameSize = 6 + length;
        
        if (receiveBuffer_.size() < frameSize) break;
        
        // 解析帧
        QByteArray frameData = receiveBuffer_.left(frameSize);
        ProtocolFrame frame = ProtocolFrame::fromByteArray(frameData);
        
        if (frame.isValid()) {
            frameQueue_.enqueue(frame);
            emit frameReceived(frame);
        } else {
            emit errorOccurred("无效的协议帧");
        }
        
        receiveBuffer_.remove(0, frameSize);
    }
}
```

### 设备发现机制
```cpp
#include <QNetworkInterface>
#include <QUdpSocket>
#include <QTimer>
#include <QThread>

// 设备发现服务
class DeviceDiscoveryService : public QObject {
    Q_OBJECT
    
public:
    struct DiscoveredDevice {
        QString name;
        QString type;
        QString address;
        QVariantMap properties;
        QDateTime lastSeen;
    };
    
    explicit DeviceDiscoveryService(QObject *parent = nullptr);
    ~DeviceDiscoveryService() override;
    
    void startDiscovery();
    void stopDiscovery();
    bool isDiscovering() const { return discovering_; }
    
    QList<DiscoveredDevice> getDiscoveredDevices() const;
    void clearDiscoveredDevices();
    
signals:
    void deviceDiscovered(const DiscoveredDevice &device);
    void deviceLost(const QString &address);
    void discoveryStateChanged(bool discovering);
    
private slots:
    void broadcastDiscoveryRequest();
    void handleDiscoveryResponse();
    void checkDeviceTimeout();
    
private:
    void setupNetworkDiscovery();
    void setupSerialDiscovery();
    void setupUSBDiscovery();
    
    bool discovering_ = false;
    QTimer *broadcastTimer_;
    QTimer *timeoutTimer_;
    QUdpSocket *udpSocket_;
    
    QList<DiscoveredDevice> discoveredDevices_;
    QMutex devicesMutex_;
    
    static constexpr int DISCOVERY_PORT = 12345;
    static constexpr int BROADCAST_INTERVAL = 2000;  // 2秒
    static constexpr int DEVICE_TIMEOUT = 10000;     // 10秒
};

// 网络设备发现
void DeviceDiscoveryService::setupNetworkDiscovery() {
    udpSocket_ = new QUdpSocket(this);
    
    if (!udpSocket_->bind(QHostAddress::Any, DISCOVERY_PORT)) {
        qWarning() << "无法绑定UDP端口" << DISCOVERY_PORT;
        return;
    }
    
    connect(udpSocket_, &QUdpSocket::readyRead, 
            this, &DeviceDiscoveryService::handleDiscoveryResponse);
    
    broadcastTimer_ = new QTimer(this);
    connect(broadcastTimer_, &QTimer::timeout, 
            this, &DeviceDiscoveryService::broadcastDiscoveryRequest);
}

void DeviceDiscoveryService::broadcastDiscoveryRequest() {
    QJsonObject request;
    request["type"] = "discovery_request";
    request["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonDocument doc(request);
    QByteArray data = doc.toJson(QJsonDocument::Compact);
    
    // 广播到所有网络接口
    const QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();
    for (const QNetworkInterface &interface : interfaces) {
        if (interface.flags() & QNetworkInterface::IsUp &&
            interface.flags() & QNetworkInterface::CanBroadcast) {
            
            const QList<QNetworkAddressEntry> entries = interface.addressEntries();
            for (const QNetworkAddressEntry &entry : entries) {
                if (entry.ip().protocol() == QAbstractSocket::IPv4Protocol) {
                    udpSocket_->writeDatagram(data, entry.broadcast(), DISCOVERY_PORT);
                }
            }
        }
    }
}

void DeviceDiscoveryService::handleDiscoveryResponse() {
    while (udpSocket_->hasPendingDatagrams()) {
        QByteArray data;
        data.resize(udpSocket_->pendingDatagramSize());
        QHostAddress sender;
        quint16 senderPort;
        
        udpSocket_->readDatagram(data.data(), data.size(), &sender, &senderPort);
        
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(data, &error);
        
        if (error.error != QJsonParseError::NoError) {
            continue;
        }
        
        QJsonObject response = doc.object();
        if (response["type"].toString() == "discovery_response") {
            DiscoveredDevice device;
            device.name = response["name"].toString();
            device.type = response["device_type"].toString();
            device.address = sender.toString();
            device.lastSeen = QDateTime::currentDateTime();
            
            // 解析设备属性
            QJsonObject props = response["properties"].toObject();
            for (auto it = props.begin(); it != props.end(); ++it) {
                device.properties[it.key()] = it.value().toVariant();
            }
            
            QMutexLocker locker(&devicesMutex_);
            
            // 检查是否已存在
            auto existing = std::find_if(discoveredDevices_.begin(), 
                                       discoveredDevices_.end(),
                                       [&device](const DiscoveredDevice &d) {
                                           return d.address == device.address;
                                       });
            
            if (existing != discoveredDevices_.end()) {
                existing->lastSeen = device.lastSeen;
            } else {
                discoveredDevices_.append(device);
                emit deviceDiscovered(device);
            }
        }
    }
}
```

### 实时性能优化
```cpp
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QElapsedTimer>

// 高性能数据处理线程
class HighPerformanceDataProcessor : public QThread {
    Q_OBJECT
    
public:
    explicit HighPerformanceDataProcessor(QObject *parent = nullptr);
    ~HighPerformanceDataProcessor() override;
    
    void addData(const QByteArray &data);
    void setProcessingCallback(std::function<void(const QByteArray&)> callback);
    
    void start(Priority priority = HighPriority);
    void stop();
    
    // 性能统计
    struct PerformanceStats {
        qint64 totalProcessed = 0;
        qint64 averageProcessingTime = 0;
        qint64 maxProcessingTime = 0;
        qint64 minProcessingTime = LLONG_MAX;
        double throughput = 0.0;  // 字节/秒
    };
    
    PerformanceStats getStats() const;
    void resetStats();
    
signals:
    void dataProcessed(const QByteArray &result);
    void performanceUpdate(const PerformanceStats &stats);
    
protected:
    void run() override;
    
private:
    void processData();
    void updateStats(qint64 processingTime, qint64 dataSize);
    
    QQueue<QByteArray> dataQueue_;
    QMutex queueMutex_;
    QWaitCondition dataAvailable_;
    
    std::function<void(const QByteArray&)> processingCallback_;
    
    bool running_ = false;
    PerformanceStats stats_;
    QElapsedTimer statsTimer_;
    
    static constexpr int MAX_QUEUE_SIZE = 1000;
    static constexpr int STATS_UPDATE_INTERVAL = 1000;  // 1秒
};

void HighPerformanceDataProcessor::run() {
    running_ = true;
    statsTimer_.start();
    
    while (running_) {
        QMutexLocker locker(&queueMutex_);
        
        if (dataQueue_.isEmpty()) {
            dataAvailable_.wait(&queueMutex_, 100);  // 100ms超时
            continue;
        }
        
        QByteArray data = dataQueue_.dequeue();
        locker.unlock();
        
        QElapsedTimer timer;
        timer.start();
        
        if (processingCallback_) {
            processingCallback_(data);
        }
        
        qint64 processingTime = timer.elapsed();
        updateStats(processingTime, data.size());
        
        emit dataProcessed(data);
    }
}

void HighPerformanceDataProcessor::addData(const QByteArray &data) {
    QMutexLocker locker(&queueMutex_);
    
    if (dataQueue_.size() >= MAX_QUEUE_SIZE) {
        // 队列满时，移除最老的数据
        dataQueue_.dequeue();
    }
    
    dataQueue_.enqueue(data);
    dataAvailable_.wakeOne();
}

// 内存池优化
template<typename T>
class MemoryPool {
public:
    explicit MemoryPool(size_t blockSize = 1024, size_t poolSize = 100) 
        : blockSize_(blockSize), poolSize_(poolSize) {
        
        pool_.reserve(poolSize);
        for (size_t i = 0; i < poolSize; ++i) {
            pool_.emplace_back(std::make_unique<T[]>(blockSize));
            freeBlocks_.push(pool_.back().get());
        }
    }
    
    T* acquire() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (freeBlocks_.empty()) {
            return new T[blockSize_];  // 紧急分配
        }
        
        T* block = freeBlocks_.top();
        freeBlocks_.pop();
        return block;
    }
    
    void release(T* block) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // 检查是否属于池
        bool belongsToPool = false;
        for (const auto& poolBlock : pool_) {
            if (poolBlock.get() == block) {
                belongsToPool = true;
                break;
            }
        }
        
        if (belongsToPool) {
            freeBlocks_.push(block);
        } else {
            delete[] block;  // 释放紧急分配的内存
        }
    }
    
private:
    size_t blockSize_;
    size_t poolSize_;
    std::vector<std::unique_ptr<T[]>> pool_;
    std::stack<T*> freeBlocks_;
    std::mutex mutex_;
};

// 使用示例
class OptimizedSerialDevice : public IDevice {
private:
    MemoryPool<uint8_t> memoryPool_;
    HighPerformanceDataProcessor *processor_;
    
public:
    OptimizedSerialDevice() : memoryPool_(4096, 50) {
        processor_ = new HighPerformanceDataProcessor(this);
        processor_->setProcessingCallback([this](const QByteArray &data) {
            processHighSpeedData(data);
        });
        processor_->start();
    }
    
private:
    void processHighSpeedData(const QByteArray &data) {
        uint8_t *buffer = memoryPool_.acquire();
        
        // 高速数据处理
        memcpy(buffer, data.data(), data.size());
        
        // 处理完成后释放
        memoryPool_.release(buffer);
    }
};
```

## 最佳实践总结

### 1. 设备管理原则
- 使用工厂模式统一设备创建
- 实现设备状态机管理连接状态
- 提供统一的设备接口抽象
- 支持热插拔检测和自动重连

### 2. 通信协议设计
- 定义清晰的帧结构和校验机制
- 实现超时重传和错误恢复
- 支持异步通信和回调处理
- 提供协议版本兼容性

### 3. 性能优化策略
- 使用内存池减少内存分配开销
- 实现高优先级数据处理线程
- 采用缓存机制提高数据访问速度
- 监控性能指标并及时调优

### 4. 错误处理机制
- 实现分层错误处理体系
- 提供详细的错误信息和恢复建议
- 支持错误日志记录和分析
- 建立设备健康状态监控 