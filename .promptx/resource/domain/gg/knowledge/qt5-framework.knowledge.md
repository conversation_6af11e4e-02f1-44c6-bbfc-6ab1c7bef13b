# Qt5.15.2 框架专业知识

## Qt核心架构

### Qt对象系统
```cpp
#include <QObject>
#include <QMetaObject>
#include <QMetaProperty>

class Device : public QObject {
    Q_OBJECT
    Q_PROPERTY(QString name READ name WRITE setName NOTIFY nameChanged)
    Q_PROPERTY(bool connected READ isConnected NOTIFY connectionChanged)
    
public:
    explicit Device(QObject *parent = nullptr);
    
    QString name() const { return name_; }
    bool isConnected() const { return connected_; }
    
public slots:
    void setName(const QString &name);
    void connectDevice();
    void disconnectDevice();
    
signals:
    void nameChanged(const QString &name);
    void connectionChanged(bool connected);
    void dataReceived(const QByteArray &data);
    
private:
    QString name_;
    bool connected_ = false;
};

// 元对象系统使用
const QMetaObject *metaObj = device->metaObject();
for (int i = 0; i < metaObj->propertyCount(); ++i) {
    QMetaProperty prop = metaObj->property(i);
    qDebug() << prop.name() << ":" << prop.read(device);
}
```

### 信号槽机制优化
```cpp
// 类型安全的连接
connect(device, &Device::dataReceived, 
        this, &MainWindow::processData);

// Lambda表达式连接
connect(button, &QPushButton::clicked, [this]() {
    device_->connectDevice();
});

// 跨线程连接
connect(worker, &Worker::resultReady,
        this, &MainWindow::handleResult, Qt::QueuedConnection);

// 单次连接
connect(timer, &QTimer::timeout,
        this, &MainWindow::initialize, Qt::SingleShotConnection);

// 连接管理
QMetaObject::Connection conn = connect(device, &Device::dataReceived,
                                     this, &MainWindow::processData);
// 稍后断开
disconnect(conn);
```

### 多线程编程
```cpp
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QThreadPool>
#include <QtConcurrent>

// Worker对象 + moveToThread（推荐）
class Worker : public QObject {
    Q_OBJECT
    
public slots:
    void doWork() {
        // 执行工作
        emit resultReady(result);
    }
    
signals:
    void resultReady(const QString &result);
};

// 使用Worker
Worker *worker = new Worker;
QThread *thread = new QThread;
worker->moveToThread(thread);

connect(thread, &QThread::started, worker, &Worker::doWork);
connect(worker, &Worker::resultReady, this, &MainWindow::handleResult);
connect(worker, &Worker::resultReady, thread, &QThread::quit);
connect(thread, &QThread::finished, worker, &Worker::deleteLater);
connect(thread, &QThread::finished, thread, &QThread::deleteLater);

thread->start();

// QtConcurrent使用
QFuture<QString> future = QtConcurrent::run([](){ 
    return processLargeData(); 
});
QFutureWatcher<QString> *watcher = new QFutureWatcher<QString>;
connect(watcher, &QFutureWatcher<QString>::finished, [=](){
    QString result = watcher->result();
    watcher->deleteLater();
});
watcher->setFuture(future);
```

### 线程同步
```cpp
#include <QMutex>
#include <QReadWriteLock>
#include <QSemaphore>
#include <QWaitCondition>

class ThreadSafeCounter {
private:
    mutable QMutex mutex_;
    int count_ = 0;
    
public:
    void increment() {
        QMutexLocker locker(&mutex_);
        ++count_;
    }
    
    int value() const {
        QMutexLocker locker(&mutex_);
        return count_;
    }
};

// 读写锁
class SharedData {
private:
    mutable QReadWriteLock lock_;
    QStringList data_;
    
public:
    QStringList getData() const {
        QReadLocker locker(&lock_);
        return data_;
    }
    
    void addData(const QString &item) {
        QWriteLocker locker(&lock_);
        data_.append(item);
    }
};
```

### 串口通信
```cpp
#include <QSerialPort>
#include <QSerialPortInfo>

class SerialDevice : public QObject {
    Q_OBJECT
    
public:
    explicit SerialDevice(QObject *parent = nullptr) : QObject(parent) {
        serial_ = new QSerialPort(this);
        
        connect(serial_, &QSerialPort::readyRead, this, &SerialDevice::readData);
        connect(serial_, &QSerialPort::errorOccurred, this, &SerialDevice::handleError);
    }
    
    QStringList availablePorts() {
        QStringList ports;
        const auto infos = QSerialPortInfo::availablePorts();
        for (const QSerialPortInfo &info : infos) {
            ports << info.portName();
        }
        return ports;
    }
    
    bool openPort(const QString &portName, qint32 baudRate = QSerialPort::Baud9600) {
        serial_->setPortName(portName);
        serial_->setBaudRate(baudRate);
        serial_->setDataBits(QSerialPort::Data8);
        serial_->setParity(QSerialPort::NoParity);
        serial_->setStopBits(QSerialPort::OneStop);
        serial_->setFlowControl(QSerialPort::NoFlowControl);
        
        if (serial_->open(QIODevice::ReadWrite)) {
            emit portOpened();
            return true;
        } else {
            emit errorOccurred(serial_->errorString());
            return false;
        }
    }
    
    void sendData(const QByteArray &data) {
        if (serial_->isOpen()) {
            serial_->write(data);
        }
    }
    
private slots:
    void readData() {
        QByteArray data = serial_->readAll();
        emit dataReceived(data);
    }
    
    void handleError(QSerialPort::SerialPortError error) {
        if (error != QSerialPort::NoError) {
            emit errorOccurred(serial_->errorString());
        }
    }
    
private:
    QSerialPort *serial_;
    
signals:
    void portOpened();
    void dataReceived(const QByteArray &data);
    void errorOccurred(const QString &error);
};
```

### 网络编程
```cpp
#include <QTcpServer>
#include <QTcpSocket>
#include <QUdpSocket>
#include <QNetworkAccessManager>

// TCP服务器
class TcpServer : public QTcpServer {
    Q_OBJECT
    
protected:
    void incomingConnection(qintptr socketDescriptor) override {
        QTcpSocket *socket = new QTcpSocket(this);
        socket->setSocketDescriptor(socketDescriptor);
        
        connect(socket, &QTcpSocket::readyRead, [=]() {
            QByteArray data = socket->readAll();
            processData(data);
            socket->write("ACK\n");
        });
        
        connect(socket, &QTcpSocket::disconnected, socket, &QTcpSocket::deleteLater);
        
        clients_.append(socket);
        emit clientConnected(socket->peerAddress().toString());
    }
    
private:
    void processData(const QByteArray &data) {
        // 处理接收到的数据
    }
    
    QList<QTcpSocket*> clients_;
    
signals:
    void clientConnected(const QString &address);
};

// TCP客户端
class TcpClient : public QObject {
    Q_OBJECT
    
public:
    explicit TcpClient(QObject *parent = nullptr) : QObject(parent) {
        socket_ = new QTcpSocket(this);
        
        connect(socket_, &QTcpSocket::connected, this, &TcpClient::connected);
        connect(socket_, &QTcpSocket::disconnected, this, &TcpClient::disconnected);
        connect(socket_, &QTcpSocket::readyRead, this, &TcpClient::readData);
    }
    
public slots:
    void connectToHost(const QString &host, quint16 port) {
        socket_->connectToHost(host, port);
    }
    
    void sendData(const QByteArray &data) {
        if (socket_->state() == QAbstractSocket::ConnectedState) {
            socket_->write(data);
        }
    }
    
private slots:
    void readData() {
        QByteArray data = socket_->readAll();
        emit dataReceived(data);
    }
    
private:
    QTcpSocket *socket_;
    
signals:
    void connected();
    void disconnected();
    void dataReceived(const QByteArray &data);
};
```

### 自定义控件开发
```cpp
#include <QWidget>
#include <QPainter>
#include <QMouseEvent>

class CustomGauge : public QWidget {
    Q_OBJECT
    Q_PROPERTY(double value READ value WRITE setValue NOTIFY valueChanged)
    Q_PROPERTY(double minimum READ minimum WRITE setMinimum)
    Q_PROPERTY(double maximum READ maximum WRITE setMaximum)
    
public:
    explicit CustomGauge(QWidget *parent = nullptr);
    
    double value() const { return value_; }
    double minimum() const { return min_; }
    double maximum() const { return max_; }
    
    QSize sizeHint() const override;
    QSize minimumSizeHint() const override;
    
public slots:
    void setValue(double value);
    void setRange(double min, double max);
    
signals:
    void valueChanged(double value);
    
protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    
private:
    void updateValue(const QPoint &pos);
    double valueFromAngle(double angle) const;
    double angleFromValue(double value) const;
    
    double value_ = 0.0;
    double min_ = 0.0;
    double max_ = 100.0;
    bool dragging_ = false;
};

void CustomGauge::paintEvent(QPaintEvent *event) {
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    QRect rect = this->rect().adjusted(10, 10, -10, -10);
    
    // 绘制背景圆弧
    painter.setPen(QPen(Qt::lightGray, 8));
    painter.drawArc(rect, 30 * 16, 300 * 16);
    
    // 绘制值圆弧
    double ratio = (value_ - min_) / (max_ - min_);
    int spanAngle = static_cast<int>(300 * 16 * ratio);
    
    painter.setPen(QPen(Qt::blue, 8));
    painter.drawArc(rect, 30 * 16, spanAngle);
    
    // 绘制数值文本
    painter.setPen(Qt::black);
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.drawText(rect, Qt::AlignCenter, QString::number(value_, 'f', 1));
}
```

### 性能优化技巧
```cpp
// 1. 对象池模式
template<typename T>
class ObjectPool {
private:
    std::stack<std::unique_ptr<T>> pool_;
    std::mutex mutex_;
    
public:
    std::unique_ptr<T> acquire() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (pool_.empty()) {
            return std::make_unique<T>();
        } else {
            auto obj = std::move(pool_.top());
            pool_.pop();
            return obj;
        }
    }
    
    void release(std::unique_ptr<T> obj) {
        std::lock_guard<std::mutex> lock(mutex_);
        pool_.push(std::move(obj));
    }
};

// 2. 智能缓存
class ImageCache {
private:
    QCache<QString, QPixmap> cache_;
    
public:
    ImageCache() {
        cache_.setMaxCost(10 * 1024 * 1024); // 10MB
    }
    
    QPixmap getImage(const QString &path) {
        QPixmap *cached = cache_.object(path);
        if (cached) {
            return *cached;
        }
        
        QPixmap pixmap(path);
        cache_.insert(path, new QPixmap(pixmap), pixmap.width() * pixmap.height() * 4);
        return pixmap;
    }
};

// 3. 绘制优化
class OptimizedWidget : public QWidget {
protected:
    void paintEvent(QPaintEvent *event) override {
        QPainter painter(this);
        
        // 设置渲染提示
        painter.setRenderHint(QPainter::Antialiasing, true);
        
        // 只绘制需要更新的区域
        QRect updateRect = event->rect();
        painter.setClipRect(updateRect);
        
        // 使用缓存的绘制内容
        if (cachedPixmap_.isNull() || cachedPixmap_.size() != size()) {
            cachedPixmap_ = QPixmap(size());
            cachedPixmap_.fill(Qt::transparent);
            
            QPainter cachePainter(&cachedPixmap_);
            drawStaticContent(&cachePainter);
        }
        
        painter.drawPixmap(0, 0, cachedPixmap_);
        drawDynamicContent(&painter);
    }
    
private:
    QPixmap cachedPixmap_;
    
    void drawStaticContent(QPainter *painter) {
        // 绘制静态内容
    }
    
    void drawDynamicContent(QPainter *painter) {
        // 绘制动态内容
    }
};
``` 