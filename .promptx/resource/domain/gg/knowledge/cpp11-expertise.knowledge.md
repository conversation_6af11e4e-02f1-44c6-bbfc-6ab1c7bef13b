# C++11 专业知识体系

## 现代C++核心特性

### 智能指针 (Smart Pointers)
```cpp
// unique_ptr - 独占所有权
std::unique_ptr<Device> device = std::make_unique<Device>();
auto device2 = std::move(device); // 移动语义

// shared_ptr - 共享所有权
std::shared_ptr<Resource> resource = std::make_shared<Resource>();
std::weak_ptr<Resource> weak_ref = resource; // 避免循环引用

// 自定义删除器
auto file_deleter = [](FILE* f) { if(f) fclose(f); };
std::unique_ptr<FILE, decltype(file_deleter)> file(fopen("data.txt", "r"), file_deleter);
```

### 移动语义 (Move Semantics)
```cpp
class DataBuffer {
private:
    std::vector<uint8_t> data_;
    
public:
    // 移动构造函数
    DataBuffer(DataBuffer&& other) noexcept 
        : data_(std::move(other.data_)) {}
    
    // 移动赋值操作符
    DataBuffer& operator=(DataBuffer&& other) noexcept {
        if (this != &other) {
            data_ = std::move(other.data_);
        }
        return *this;
    }
    
    // 完美转发
    template<typename... Args>
    void emplace_data(Args&&... args) {
        data_.emplace_back(std::forward<Args>(args)...);
    }
};
```

### Lambda 表达式
```cpp
// 基本lambda
auto multiply = [](int a, int b) -> int { return a * b; };

// 捕获列表
int factor = 10;
auto scale = [factor](int value) { return value * factor; }; // 按值捕获
auto scale_ref = [&factor](int value) { return value * factor; }; // 按引用捕获

// 在STL算法中的应用
std::vector<int> numbers = {1, 2, 3, 4, 5};
std::for_each(numbers.begin(), numbers.end(), [](int& n) { n *= 2; });

// 设备事件处理
device->setEventHandler([this](const DeviceEvent& event) {
    this->handleDeviceEvent(event);
});
```

### 并发编程 (Concurrency)
```cpp
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <atomic>

class ThreadSafeQueue {
private:
    mutable std::mutex mutex_;
    std::queue<std::string> queue_;
    std::condition_variable condition_;
    
public:
    void push(const std::string& data) {
        std::lock_guard<std::mutex> lock(mutex_);
        queue_.push(data);
        condition_.notify_one();
    }
    
    bool wait_and_pop(std::string& popped_value) {
        std::unique_lock<std::mutex> lock(mutex_);
        while(queue_.empty()) {
            condition_.wait(lock);
        }
        popped_value = queue_.front();
        queue_.pop();
        return true;
    }
};

// 异步任务执行
std::future<int> future_result = std::async(std::launch::async, []() {
    // 耗时计算
    return complex_calculation();
});

// 原子操作
std::atomic<bool> is_running{true};
std::atomic<int> counter{0};
```

### 类型推导 (Type Deduction)
```cpp
// auto关键字
auto value = 42; // int
auto ptr = std::make_unique<Device>(); // std::unique_ptr<Device>
auto lambda = [](int x) { return x * 2; }; // lambda类型

// decltype
template<typename T, typename U>
auto add(T t, U u) -> decltype(t + u) {
    return t + u;
}

// 尾置返回类型
template<typename Container>
auto get_size(const Container& c) -> decltype(c.size()) {
    return c.size();
}
```

### 范围for循环 (Range-based for)
```cpp
std::vector<Device*> devices = getConnectedDevices();

// 基本范围for
for (const auto& device : devices) {
    device->update();
}

// 引用修改
for (auto& device : devices) {
    device->reset();
}

// 自定义类型支持
class DeviceContainer {
public:
    std::vector<Device>::iterator begin() { return devices_.begin(); }
    std::vector<Device>::iterator end() { return devices_.end(); }
    std::vector<Device>::const_iterator begin() const { return devices_.cbegin(); }
    std::vector<Device>::const_iterator end() const { return devices_.cend(); }
    
private:
    std::vector<Device> devices_;
};
```

### 强类型枚举 (Scoped Enums)
```cpp
enum class DeviceType : uint8_t {
    UNKNOWN = 0,
    USB_DEVICE = 1,
    SERIAL_DEVICE = 2,
    NETWORK_DEVICE = 3
};

enum class ConnectionStatus {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    ERROR
};

// 使用
DeviceType type = DeviceType::USB_DEVICE;
if (type == DeviceType::USB_DEVICE) {
    // 处理USB设备
}
```

### 可变参数模板 (Variadic Templates)
```cpp
// 日志系统
template<typename... Args>
void log(const std::string& format, Args&&... args) {
    printf(format.c_str(), std::forward<Args>(args)...);
}

// 完美转发构造
template<typename T, typename... Args>
std::unique_ptr<T> make_unique(Args&&... args) {
    return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
}

// 递归模板展开
template<typename T>
void print(T&& t) {
    std::cout << t << std::endl;
}

template<typename T, typename... Args>
void print(T&& t, Args&&... args) {
    std::cout << t << " ";
    print(std::forward<Args>(args)...);
}
```

## 内存管理最佳实践

### RAII原则应用
```cpp
class FileHandler {
private:
    FILE* file_;
    
public:
    explicit FileHandler(const std::string& filename) 
        : file_(fopen(filename.c_str(), "r")) {
        if (!file_) {
            throw std::runtime_error("Failed to open file");
        }
    }
    
    ~FileHandler() {
        if (file_) {
            fclose(file_);
        }
    }
    
    // 禁止拷贝
    FileHandler(const FileHandler&) = delete;
    FileHandler& operator=(const FileHandler&) = delete;
    
    // 允许移动
    FileHandler(FileHandler&& other) noexcept : file_(other.file_) {
        other.file_ = nullptr;
    }
    
    FileHandler& operator=(FileHandler&& other) noexcept {
        if (this != &other) {
            if (file_) fclose(file_);
            file_ = other.file_;
            other.file_ = nullptr;
        }
        return *this;
    }
    
    FILE* get() const { return file_; }
};
```

### 异常安全保证
```cpp
class DeviceManager {
private:
    std::vector<std::unique_ptr<Device>> devices_;
    
public:
    // 强异常安全保证
    void addDevice(std::unique_ptr<Device> device) {
        if (!device) {
            throw std::invalid_argument("Device cannot be null");
        }
        
        // 先验证设备
        device->validate(); // 可能抛出异常
        
        // 如果验证成功，才添加到容器
        devices_.push_back(std::move(device)); // 不会抛出异常
    }
    
    // 基本异常安全保证
    void updateAllDevices() {
        for (auto& device : devices_) {
            try {
                device->update();
            } catch (const std::exception& e) {
                // 记录错误但继续处理其他设备
                logError("Device update failed: " + std::string(e.what()));
            }
        }
    }
};
```

### 性能优化技巧
```cpp
// 1. 避免不必要的拷贝
void processData(const std::vector<uint8_t>& data) { // 传引用
    // 处理数据
}

// 2. 使用移动语义
std::vector<uint8_t> createLargeBuffer() {
    std::vector<uint8_t> buffer(1024 * 1024);
    // 填充数据
    return buffer; // 返回值优化 + 移动语义
}

// 3. 就地构造
std::vector<Device> devices;
devices.emplace_back("device1", DeviceType::USB_DEVICE); // 直接构造，避免临时对象

// 4. 预留容量
std::vector<int> numbers;
numbers.reserve(1000); // 避免多次重新分配

// 5. 使用适当的容器
std::unordered_map<std::string, Device*> device_map; // O(1)查找
std::deque<Command> command_queue; // 高效的首尾插入删除
```

## 模板元编程基础

### SFINAE (Substitution Failure Is Not An Error)
```cpp
#include <type_traits>

// 检测类型是否有特定成员函数
template<typename T>
class has_update_method {
private:
    template<typename U>
    static auto test(int) -> decltype(std::declval<U>().update(), std::true_type{});
    
    template<typename>
    static std::false_type test(...);
    
public:
    static constexpr bool value = decltype(test<T>(0))::value;
};

// 条件编译
template<typename T>
typename std::enable_if<has_update_method<T>::value, void>::type
update_if_possible(T& obj) {
    obj.update();
}

template<typename T>
typename std::enable_if<!has_update_method<T>::value, void>::type
update_if_possible(T& obj) {
    // 什么都不做
}
```

### 类型萃取 (Type Traits)
```cpp
template<typename T>
class DeviceTraits {
public:
    using value_type = typename T::value_type;
    using pointer = typename T::pointer;
    using reference = typename T::reference;
    
    static constexpr bool is_thread_safe = T::thread_safe;
    static constexpr size_t buffer_size = T::default_buffer_size;
};

// 特化
template<>
class DeviceTraits<USBDevice> {
public:
    using value_type = uint8_t;
    using pointer = uint8_t*;
    using reference = uint8_t&;
    
    static constexpr bool is_thread_safe = false;
    static constexpr size_t buffer_size = 1024;
};
```

## 编译期计算
```cpp
// constexpr函数
constexpr int factorial(int n) {
    return n <= 1 ? 1 : n * factorial(n - 1);
}

// 编译期常量
constexpr int buffer_size = factorial(5); // 120

// constexpr构造函数
class Point {
private:
    int x_, y_;
    
public:
    constexpr Point(int x, int y) : x_(x), y_(y) {}
    constexpr int x() const { return x_; }
    constexpr int y() const { return y_; }
    constexpr int distance_squared() const { return x_ * x_ + y_ * y_; }
};

constexpr Point origin(0, 0);
constexpr int dist = origin.distance_squared(); // 编译期计算
``` 