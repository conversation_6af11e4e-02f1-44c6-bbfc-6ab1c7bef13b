<execution>
  <constraint>
    ## 质量标准约束
    - **零缺陷目标**：关键功能模块必须达到零缺陷标准
    - **性能基准**：所有性能指标必须满足既定基准要求
    - **安全标准**：必须通过安全漏洞扫描和渗透测试
    - **兼容性要求**：必须在所有目标平台上稳定运行
    - **可维护性标准**：代码必须符合团队编码规范和可维护性要求
  </constraint>

  <rule>
    ## 质量保证强制规则
    - **代码审查强制**：所有代码必须经过至少2人的代码审查
    - **自动化测试强制**：所有提交必须通过完整的自动化测试套件
    - **性能基准强制**：性能退化超过5%的提交必须被拒绝
    - **安全扫描强制**：所有代码必须通过静态安全分析工具检查
    - **文档同步强制**：代码变更必须同步更新相关技术文档
  </rule>

  <guideline>
    ## 质量保证指导原则
    - **预防优于修复**：在开发早期发现和解决问题
    - **持续改进**：基于质量度量数据持续优化开发流程
    - **全员质量意识**：每个开发人员都是质量的责任人
    - **工具辅助**：充分利用自动化工具提高质量保证效率
    - **客户价值导向**：所有质量活动都以提升客户价值为目标
  </guideline>

  <process>
    ## 全面质量保证流程

    ### 开发阶段质量控制
    ```mermaid
    flowchart TD
        A[代码编写] --> B[单元测试]
        B --> C[静态代码分析]
        C --> D[代码格式检查]
        D --> E[安全漏洞扫描]
        E --> F{质量检查通过?}
        F -->|否| G[问题修复]
        G --> B
        F -->|是| H[代码提交]
        H --> I[同行代码审查]
        I --> J{审查通过?}
        J -->|否| K[修改建议]
        K --> G
        J -->|是| L[集成测试]
    ```

    ### 代码质量检查清单
    
    #### 1. 静态代码分析
    ```bash
    # Clang-Tidy 全面检查
    clang-tidy \
        --checks='-*,modernize-*,performance-*,readability-*,bugprone-*' \
        --header-filter='.*' \
        src/**/*.cpp -- -std=c++11 -I/path/to/qt5/include
    
    # CppCheck 深度分析
    cppcheck \
        --enable=all \
        --std=c++11 \
        --platform=unix64 \
        --suppress=missingIncludeSystem \
        --xml-version=2 \
        src/
    
    # SonarQube 代码质量分析
    sonar-scanner \
        -Dsonar.projectKey=your-project \
        -Dsonar.sources=src \
        -Dsonar.cfamily.build-wrapper-output=bw-output
    ```

    #### 2. 内存安全检查
    ```bash
    # AddressSanitizer (ASan)
    g++ -fsanitize=address -fno-omit-frame-pointer -g -O1 \
        -o your_app src/*.cpp $(pkg-config --cflags --libs Qt5Core Qt5Widgets)
    
    # MemorySanitizer (MSan)
    clang++ -fsanitize=memory -fno-omit-frame-pointer -g -O1 \
        -o your_app src/*.cpp $(pkg-config --cflags --libs Qt5Core Qt5Widgets)
    
    # Valgrind 全面内存检查
    valgrind \
        --tool=memcheck \
        --leak-check=full \
        --show-leak-kinds=all \
        --track-origins=yes \
        --verbose \
        ./your_app
    ```

    #### 3. 线程安全检查
    ```bash
    # ThreadSanitizer (TSan)
    g++ -fsanitize=thread -g -O1 \
        -o your_app src/*.cpp $(pkg-config --cflags --libs Qt5Core Qt5Widgets)
    
    # Helgrind (Valgrind)
    valgrind \
        --tool=helgrind \
        --verbose \
        ./your_app
    ```

    ### 性能质量保证
    ```mermaid
    graph TD
        A[性能基准建立] --> B[性能测试执行]
        B --> C[性能数据收集]
        C --> D[性能分析]
        D --> E{性能达标?}
        E -->|否| F[性能优化]
        F --> B
        E -->|是| G[性能报告]
        G --> H[性能基准更新]
    ```

    #### 性能测试工具链
    ```bash
    # Perf 性能分析
    perf record -g ./your_app
    perf report
    
    # Callgrind 调用图分析
    valgrind --tool=callgrind ./your_app
    kcachegrind callgrind.out.*
    
    # Intel VTune Profiler
    vtune -collect hotspots -app-args ./your_app
    
    # Qt Creator Profiler
    # 在Qt Creator中使用内置的性能分析工具
    ```

    ### 外设兼容性测试
    ```mermaid
    flowchart LR
        A[设备清单] --> B[自动化测试]
        B --> C[手动验证]
        C --> D[兼容性矩阵]
        D --> E[问题跟踪]
        E --> F[修复验证]
        F --> G[兼容性报告]
    ```

    #### 外设测试策略
    ```cpp
    // 设备兼容性测试框架
    class DeviceCompatibilityTester {
    public:
        struct TestResult {
            std::string device_name;
            std::string test_case;
            bool passed;
            std::string error_message;
            std::chrono::milliseconds response_time;
        };
        
        // 批量设备测试
        std::vector<TestResult> runCompatibilityTests(
            const std::vector<DeviceInfo>& devices);
        
        // 压力测试
        TestResult runStressTest(const DeviceInfo& device, 
                               std::chrono::minutes duration);
        
        // 并发测试
        TestResult runConcurrencyTest(
            const std::vector<DeviceInfo>& devices);
    };
    ```

    ### 安全质量保证
    ```mermaid
    graph TD
        A[安全需求分析] --> B[威胁建模]
        B --> C[安全编码审查]
        C --> D[静态安全分析]
        D --> E[动态安全测试]
        E --> F[渗透测试]
        F --> G{安全评估通过?}
        G -->|否| H[安全加固]
        H --> C
        G -->|是| I[安全报告]
    ```

    #### 安全检查工具
    ```bash
    # Clang Static Analyzer
    scan-build make
    
    # Flawfinder
    flawfinder src/
    
    # Cppcheck 安全检查
    cppcheck --enable=all --std=c++11 src/
    
    # OWASP 依赖检查
    dependency-check --project "Your Project" --scan src/
    ```

    ### 文档质量保证
    ```mermaid
    flowchart TD
        A[文档需求] --> B[文档编写]
        B --> C[技术审查]
        C --> D[格式检查]
        D --> E[完整性验证]
        E --> F{文档质量合格?}
        F -->|否| G[文档修订]
        G --> C
        F -->|是| H[文档发布]
    ```

    #### 文档质量标准
    - **API文档覆盖率**：≥95%的公共接口有文档
    - **代码注释率**：复杂逻辑注释覆盖率≥80%
    - **架构文档完整性**：包含系统架构、模块设计、接口规范
    - **用户文档可用性**：安装指南、使用手册、故障排除

    ### 持续集成质量门禁
    ```yaml
    # .github/workflows/quality-gate.yml
    quality_gate:
      runs-on: ubuntu-latest
      steps:
        - name: Code Quality Check
          run: |
            # 静态分析
            clang-tidy --checks='-*,modernize-*,performance-*' src/
            
            # 单元测试
            mkdir build && cd build
            cmake .. -DCMAKE_BUILD_TYPE=Debug
            make && ctest --output-on-failure
            
            # 覆盖率检查
            gcov src/*.cpp
            lcov --capture --directory . --output-file coverage.info
            
        - name: Security Scan
          run: |
            cppcheck --enable=all src/
            
        - name: Performance Benchmark
          run: |
            ./performance_tests
            
        - name: Quality Gate Decision
          run: |
            # 检查所有质量指标
            python scripts/quality_gate_check.py
    ```
  </process>

  <criteria>
    ## 质量评价标准

    ### 代码质量指标
    - ✅ **静态分析**：0个高级别问题，≤5个中等级别问题
    - ✅ **代码覆盖率**：单元测试≥80%，集成测试≥70%
    - ✅ **圈复杂度**：平均≤8，最大≤15
    - ✅ **重复代码率**：≤3%
    - ✅ **技术债务比率**：≤5%

    ### 性能质量指标
    - ✅ **响应时间**：外设操作≤10ms，UI响应≤100ms
    - ✅ **吞吐量**：数据处理≥1000条/秒
    - ✅ **资源使用**：内存≤512MB，CPU≤30%
    - ✅ **并发性能**：支持≥10个并发设备连接
    - ✅ **稳定性**：连续运行≥72小时无内存泄漏

    ### 安全质量指标
    - ✅ **漏洞扫描**：0个高危漏洞，≤3个中危漏洞
    - ✅ **代码审计**：100%关键代码路径安全审查
    - ✅ **输入验证**：100%外部输入验证覆盖
    - ✅ **权限控制**：最小权限原则100%实施
    - ✅ **数据保护**：敏感数据100%加密传输和存储

    ### 兼容性质量指标
    - ✅ **平台兼容性**：Windows/Linux/macOS 100%支持
    - ✅ **设备兼容性**：目标设备列表100%兼容
    - ✅ **版本兼容性**：Qt5.15.x系列100%兼容
    - ✅ **编译器兼容性**：GCC/Clang/MSVC 100%支持

    ### 可维护性指标
    - ✅ **文档完整性**：API文档≥95%，架构文档100%
    - ✅ **代码规范性**：编码规范100%遵循
    - ✅ **模块耦合度**：平均耦合度≤7
    - ✅ **代码可读性**：代码审查通过率≥95%
  </criteria>
</execution> 