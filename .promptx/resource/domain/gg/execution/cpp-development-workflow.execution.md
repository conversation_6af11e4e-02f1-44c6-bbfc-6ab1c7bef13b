<execution>
  <constraint>
    ## 技术约束条件
    - **C++11标准限制**：必须严格遵循C++11标准，不使用更高版本特性
    - **Qt5.15.2兼容性**：所有代码必须与Qt5.15.2版本完全兼容
    - **跨平台要求**：代码必须支持Windows、Linux、macOS平台
    - **性能要求**：外设通信延迟不超过10ms，内存使用效率≥85%
    - **编译器支持**：支持GCC 4.8+、Clang 3.3+、MSVC 2013+
  </constraint>

  <rule>
    ## 强制性开发规则
    - **RAII原则强制**：所有资源管理必须遵循RAII，禁止手动内存管理
    - **异常安全保证**：所有函数必须提供基本异常安全保证，关键函数提供强异常安全保证
    - **线程安全要求**：所有多线程访问的代码必须线程安全，使用适当的同步机制
    - **代码审查强制**：所有代码提交前必须通过同行评审和静态分析
    - **单元测试覆盖**：核心功能单元测试覆盖率≥80%，关键路径≥95%
  </rule>

  <guideline>
    ## 开发指导原则
    - **现代C++优先**：优先使用C++11特性，避免C风格编程
    - **Qt最佳实践**：遵循Qt官方编程指南和最佳实践
    - **性能与可读性平衡**：在保证性能的前提下优化代码可读性
    - **渐进式重构**：持续改进代码质量，避免大规模重构
    - **文档驱动开发**：重要接口和复杂逻辑必须有充分文档
  </guideline>

  <process>
    ## C++外设开发标准流程

    ### 阶段1：需求分析与设计 (20%)
    ```mermaid
    flowchart TD
        A[需求收集] --> B[硬件规格分析]
        B --> C[接口协议设计]
        C --> D[架构设计]
        D --> E[API设计]
        E --> F[设计评审]
        F --> G{评审通过?}
        G -->|否| D
        G -->|是| H[设计文档]
    ```
    
    **关键输出**：
    - 硬件接口规范文档
    - 软件架构设计文档
    - API接口定义
    - 类图和时序图

    ### 阶段2：环境搭建与框架搭建 (15%)
    ```mermaid
    graph LR
        A[开发环境] --> B[Qt Creator配置]
        A --> C[CMake构建系统]
        A --> D[版本控制设置]
        B --> E[项目模板]
        C --> E
        D --> E
        E --> F[基础框架代码]
    ```
    
    **标准配置**：
    - Qt Creator + CMake + Git
    - 代码格式化：clang-format
    - 静态分析：clang-tidy, cppcheck
    - 单元测试：Google Test

    ### 阶段3：核心模块开发 (40%)
    ```mermaid
    flowchart TD
        A[硬件抽象层] --> B[设备驱动接口]
        B --> C[通信协议实现]
        C --> D[数据处理模块]
        D --> E[状态管理模块]
        E --> F[错误处理模块]
        F --> G[单元测试]
        G --> H{测试通过?}
        H -->|否| A
        H -->|是| I[模块集成]
    ```
    
    **开发优先级**：
    1. 硬件抽象层（HAL）
    2. 通信协议栈
    3. 数据处理引擎
    4. 错误处理机制
    5. 状态管理系统

    ### 阶段4：用户界面开发 (15%)
    ```mermaid
    graph TD
        A[UI需求分析] --> B[界面原型设计]
        B --> C[Qt Widgets/QML选择]
        C --> D[自定义控件开发]
        D --> E[信号槽连接]
        E --> F[数据绑定]
        F --> G[界面测试]
    ```
    
    **Qt5.15.2 UI开发要点**：
    - 使用Qt Designer进行界面设计
    - 自定义控件继承适当的基类
    - 合理使用信号槽机制
    - 注意跨线程UI更新

    ### 阶段5：集成测试与优化 (10%)
    ```mermaid
    flowchart LR
        A[模块集成] --> B[系统测试]
        B --> C[性能测试]
        C --> D[压力测试]
        D --> E[兼容性测试]
        E --> F[优化调整]
        F --> G[最终验证]
    ```

    ## 代码质量保证流程
    
    ### 静态代码分析
    ```bash
    # clang-tidy检查
    clang-tidy src/*.cpp -- -std=c++11 -I/path/to/qt5/include
    
    # cppcheck检查
    cppcheck --enable=all --std=c++11 src/
    
    # 代码格式化
    clang-format -i src/*.cpp src/*.h
    ```
    
    ### 动态分析
    ```bash
    # Valgrind内存检查
    valgrind --tool=memcheck --leak-check=full ./your_app
    
    # AddressSanitizer
    g++ -fsanitize=address -g -o your_app src/*.cpp
    
    # ThreadSanitizer
    g++ -fsanitize=thread -g -o your_app src/*.cpp
    ```

    ## 外设集成最佳实践

    ### 设备发现与连接
    ```cpp
    class DeviceManager {
    public:
        // 异步设备发现
        std::future<std::vector<DeviceInfo>> discoverDevicesAsync();
        
        // 设备连接管理
        std::unique_ptr<Device> connectDevice(const DeviceInfo& info);
        
        // 连接状态监控
        void setConnectionMonitor(std::function<void(DeviceStatus)> callback);
    };
    ```

    ### 通信协议实现
    ```cpp
    class ProtocolHandler {
        // 协议解析
        virtual std::optional<Message> parseMessage(const QByteArray& data) = 0;
        
        // 消息序列化
        virtual QByteArray serializeMessage(const Message& msg) = 0;
        
        // 错误处理
        virtual void handleError(ProtocolError error) = 0;
    };
    ```

    ### 线程安全设计
    ```cpp
    class ThreadSafeDeviceController {
    private:
        mutable std::mutex device_mutex_;
        std::condition_variable condition_;
        std::atomic<bool> is_connected_{false};
        
    public:
        void sendCommand(const Command& cmd) {
            std::lock_guard<std::mutex> lock(device_mutex_);
            // 发送命令实现
        }
    };
    ```
  </process>

  <criteria>
    ## 质量评价标准

    ### 代码质量指标
    - ✅ 编译警告数量 = 0
    - ✅ 静态分析问题 ≤ 5个（非关键）
    - ✅ 单元测试覆盖率 ≥ 80%
    - ✅ 关键路径测试覆盖率 ≥ 95%
    - ✅ 内存泄漏检测通过
    - ✅ 线程安全检测通过

    ### 性能指标
    - ✅ 设备响应时间 ≤ 10ms
    - ✅ 内存使用效率 ≥ 85%
    - ✅ CPU使用率 ≤ 30%（正常负载）
    - ✅ 并发连接数 ≥ 10个设备

    ### 稳定性指标
    - ✅ 连续运行24小时无崩溃
    - ✅ 异常恢复成功率 ≥ 95%
    - ✅ 设备重连成功率 ≥ 98%
    - ✅ 数据传输错误率 ≤ 0.1%

    ### 可维护性指标
    - ✅ 代码复杂度（圈复杂度）≤ 10
    - ✅ 函数长度 ≤ 50行
    - ✅ 类耦合度 ≤ 7
    - ✅ 接口文档完整性 ≥ 90%
  </criteria>
</execution> 