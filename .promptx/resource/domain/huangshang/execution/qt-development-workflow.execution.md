<execution>
  <constraint>
    ## Qt5.15.2技术约束
    - **版本兼容性**：必须确保与Qt5.15.2 LTS版本的完全兼容
    - **C++标准限制**：支持C++17标准，避免使用C++20特性
    - **平台兼容性**：支持Windows、Linux、macOS三大主流平台
    - **内存管理**：严格遵循Qt对象树内存管理机制
    - **线程安全**：UI操作必须在主线程中执行
    - **信号槽约束**：连接必须类型匹配，避免跨线程直接连接
  </constraint>

  <rule>
    ## Qt开发强制规则
    - **RAII原则**：所有资源必须遵循RAII（Resource Acquisition Is Initialization）原则
    - **对象父子关系**：所有Qt对象必须正确设置父子关系，避免内存泄漏
    - **信号槽连接**：使用新式信号槽语法，避免字符串连接方式
    - **线程安全规则**：UI更新必须通过QMetaObject::invokeMethod或信号槽机制
    - **异常处理**：Qt环境下慎用异常，优先使用错误码和状态检查
    - **国际化支持**：所有用户可见字符串必须使用tr()函数包装
  </rule>

  <guideline>
    ## Qt开发指导原则
    - **组件化设计**：将复杂UI拆分为独立的可重用组件
    - **MVC架构**：分离数据模型、视图显示和控制逻辑
    - **响应式设计**：适配不同分辨率和DPI设置
    - **性能优先**：优化渲染性能，避免不必要的重绘
    - **用户体验**：提供流畅的交互体验和及时的用户反馈
    - **可维护性**：保持代码清晰、注释完整、结构合理
  </guideline>

  <process>
    ## Qt UI开发完整流程

    ### Phase 1: 项目初始化与环境搭建
    ```mermaid
    flowchart TD
        A[创建Qt项目] --> B[配置CMakeLists.txt]
        B --> C[设置项目结构]
        C --> D[配置Qt模块]
        D --> E[设置编译选项]
        E --> F[初始化版本控制]
        
        style A fill:#e1f5fe
        style F fill:#e8f5e9
    ```

    #### 1.1 项目结构标准化
    ```
    project_root/
    ├── src/
    │   ├── main.cpp
    │   ├── mainwindow.h/.cpp
    │   ├── ui/
    │   │   ├── widgets/
    │   │   ├── dialogs/
    │   │   └── forms/
    │   ├── models/
    │   ├── controllers/
    │   └── utils/
    ├── resources/
    │   ├── images/
    │   ├── styles/
    │   └── translations/
    ├── tests/
    └── CMakeLists.txt
    ```

    #### 1.2 CMakeLists.txt配置模板
    ```cmake
    cmake_minimum_required(VERSION 3.16)
    project(ProjectName VERSION 1.0.0)

    set(CMAKE_CXX_STANDARD 17)
    set(CMAKE_CXX_STANDARD_REQUIRED ON)

    find_package(Qt5 5.15.2 REQUIRED COMPONENTS 
        Core Widgets Gui Network Sql
    )

    set(CMAKE_AUTOMOC ON)
    set(CMAKE_AUTORCC ON)
    set(CMAKE_AUTOUIC ON)
    ```

    ### Phase 2: UI设计与原型开发
    ```mermaid
    graph LR
        A[需求分析] --> B[界面设计]
        B --> C[原型制作]
        C --> D[用户测试]
        D --> E[设计迭代]
        E --> F[最终确认]
        
        style A fill:#fff3e0
        style F fill:#e8f5e9
    ```

    #### 2.1 UI设计工作流
    - **需求分析**：用户故事 → 功能清单 → 界面需求
    - **原型设计**：线框图 → 交互原型 → 视觉设计
    - **技术评估**：可行性分析 → 性能评估 → 技术选型
    - **设计规范**：颜色系统 → 字体规范 → 组件库

    #### 2.2 Qt Designer使用规范
    ```cpp
    // 推荐的UI文件加载方式
    class MainWindow : public QMainWindow
    {
        Q_OBJECT

    public:
        MainWindow(QWidget *parent = nullptr);
        ~MainWindow();

    private:
        Ui::MainWindow *ui;
        void setupConnections();
        void setupStyles();
    };
    ```

    ### Phase 3: 核心组件开发
    ```mermaid
    flowchart TD
        A[基础组件] --> B[业务组件]
        B --> C[复合组件]
        C --> D[组件测试]
        D --> E[组件集成]
        
        A --> A1[按钮组件]
        A --> A2[输入组件]
        A --> A3[显示组件]
        
        B --> B1[数据表格]
        B --> B2[图表组件]
        B --> B3[表单组件]
        
        C --> C1[工具栏]
        C --> C2[状态栏]
        C --> C3[侧边栏]
    ```

    #### 3.1 组件开发标准
    ```cpp
    // 标准组件基类
    class BaseWidget : public QWidget
    {
        Q_OBJECT

    public:
        explicit BaseWidget(QWidget *parent = nullptr);
        virtual ~BaseWidget() = default;

    protected:
        virtual void setupUI() = 0;
        virtual void setupConnections() = 0;
        virtual void setupStyles() = 0;
        
        // 事件处理
        void paintEvent(QPaintEvent *event) override;
        void resizeEvent(QResizeEvent *event) override;
        void mousePressEvent(QMouseEvent *event) override;

    signals:
        void dataChanged();
        void errorOccurred(const QString &error);

    private slots:
        void onDataChanged();
    };
    ```

    #### 3.2 信号槽连接最佳实践
    ```cpp
    // 新式信号槽语法（推荐）
    connect(button, &QPushButton::clicked, 
            this, &MainWindow::onButtonClicked);
    
    // Lambda表达式连接
    connect(button, &QPushButton::clicked, [this]() {
        // 处理点击事件
        updateUI();
    });
    
    // 跨线程连接
    connect(worker, &WorkerThread::dataReady,
            this, &MainWindow::updateData,
            Qt::QueuedConnection);
    ```

    ### Phase 4: 样式与主题系统
    ```mermaid
    graph TD
        A[样式系统设计] --> B[QSS样式表]
        B --> C[主题切换]
        C --> D[响应式适配]
        D --> E[样式测试]
        
        A --> A1[颜色系统]
        A --> A2[字体系统]
        A --> A3[间距系统]
        
        B --> B1[组件样式]
        B --> B2[布局样式]
        B --> B3[动画效果]
    ```

    #### 4.1 QSS样式组织
    ```cpp
    // 样式管理器
    class StyleManager : public QObject
    {
        Q_OBJECT

    public:
        enum Theme { Light, Dark, Auto };
        
        static StyleManager* instance();
        void setTheme(Theme theme);
        void loadStyleSheet(const QString &filename);
        
    signals:
        void themeChanged(Theme theme);
        
    private:
        void applyLightTheme();
        void applyDarkTheme();
        void detectSystemTheme();
    };
    ```

    #### 4.2 响应式设计实现
    ```cpp
    // DPI感知设置
    void setupHighDPI() {
        QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
        QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
        QApplication::setHighDpiScaleFactorRoundingPolicy(
            Qt::HighDpiScaleFactorRoundingPolicy::PassThrough);
    }

    // 响应式布局
    void MainWindow::resizeEvent(QResizeEvent *event) {
        QMainWindow::resizeEvent(event);
        
        // 根据窗口大小调整布局
        if (width() < 800) {
            setCompactLayout();
        } else {
            setNormalLayout();
        }
    }
    ```

    ### Phase 5: 数据绑定与模型视图
    ```mermaid
    flowchart LR
        A[数据模型] --> B[视图组件]
        B --> C[数据绑定]
        C --> D[更新机制]
        D --> E[性能优化]
        
        A --> A1[QAbstractItemModel]
        A --> A2[QStandardItemModel]
        A --> A3[自定义模型]
        
        B --> B1[QTableView]
        B --> B2[QListView]
        B --> B3[QTreeView]
    ```

    #### 5.1 自定义模型实现
    ```cpp
    class CustomTableModel : public QAbstractTableModel
    {
        Q_OBJECT

    public:
        explicit CustomTableModel(QObject *parent = nullptr);
        
        // 必须实现的方法
        int rowCount(const QModelIndex &parent = QModelIndex()) const override;
        int columnCount(const QModelIndex &parent = QModelIndex()) const override;
        QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
        QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
        
        // 可编辑模型
        bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
        Qt::ItemFlags flags(const QModelIndex &index) const override;
        
    private:
        QList<QList<QVariant>> m_data;
    };
    ```

    ### Phase 6: 性能优化与调试
    ```mermaid
    graph TD
        A[性能分析] --> B[瓶颈识别]
        B --> C[优化方案]
        C --> D[实施优化]
        D --> E[效果验证]
        
        A --> A1[内存分析]
        A --> A2[CPU分析]
        A --> A3[渲染分析]
        
        C --> C1[算法优化]
        C --> C2[缓存策略]
        C --> C3[异步处理]
    ```

    #### 6.1 性能优化策略
    ```cpp
    // 避免频繁重绘
    void CustomWidget::paintEvent(QPaintEvent *event) {
        static QPixmap cache;
        static bool needsUpdate = true;
        
        if (needsUpdate) {
            cache = QPixmap(size());
            cache.fill(Qt::transparent);
            
            QPainter cachePainter(&cache);
            drawContent(&cachePainter);
            needsUpdate = false;
        }
        
        QPainter painter(this);
        painter.drawPixmap(0, 0, cache);
    }

    // 延迟加载
    void LazyLoadWidget::showEvent(QShowEvent *event) {
        QWidget::showEvent(event);
        
        if (!m_initialized) {
            initializeContent();
            m_initialized = true;
        }
    }
    ```

    ### Phase 7: 测试与部署
    ```mermaid
    flowchart TD
        A[单元测试] --> B[集成测试]
        B --> C[UI测试]
        C --> D[性能测试]
        D --> E[兼容性测试]
        E --> F[部署准备]
        F --> G[发布部署]
        
        style A fill:#e3f2fd
        style G fill:#e8f5e9
    ```

    #### 7.1 Qt测试框架使用
    ```cpp
    class TestMainWindow : public QObject
    {
        Q_OBJECT

    private slots:
        void initTestCase();
        void cleanupTestCase();
        void testButtonClick();
        void testDataModel();
        void testPerformance();
    };

    void TestMainWindow::testButtonClick() {
        MainWindow window;
        QTest::mouseClick(window.button(), Qt::LeftButton);
        QCOMPARE(window.getStatus(), "clicked");
    }
    ```

    #### 7.2 部署配置
    ```cpp
    // 应用程序信息设置
    void setupApplicationInfo() {
        QApplication::setApplicationName("ApplicationName");
        QApplication::setApplicationVersion("1.0.0");
        QApplication::setOrganizationName("Company");
        QApplication::setOrganizationDomain("company.com");
    }
    ```
  </process>

  <criteria>
    ## Qt开发质量标准

    ### 代码质量标准
    - ✅ 遵循Qt编码规范和C++最佳实践
    - ✅ 内存管理正确，无内存泄漏
    - ✅ 信号槽连接正确，无悬挂指针
    - ✅ 异常安全，错误处理完善
    - ✅ 代码注释完整，文档齐全

    ### 性能标准
    - ✅ 应用启动时间 < 3秒
    - ✅ UI响应时间 < 100ms
    - ✅ 内存使用合理，无内存泄漏
    - ✅ CPU使用率在正常范围内
    - ✅ 渲染帧率稳定 > 30fps

    ### 用户体验标准
    - ✅ 界面美观，符合平台设计规范
    - ✅ 交互流畅，反馈及时
    - ✅ 支持键盘导航和快捷键
    - ✅ 错误提示友好，帮助信息完整
    - ✅ 支持国际化和本地化

    ### 兼容性标准
    - ✅ 支持目标平台的所有版本
    - ✅ 适配不同分辨率和DPI设置
    - ✅ 兼容不同的Qt版本（5.15.x）
    - ✅ 第三方库集成稳定
    - ✅ 部署包完整，依赖清晰

    ### 维护性标准
    - ✅ 代码结构清晰，模块化程度高
    - ✅ 单元测试覆盖率 > 80%
    - ✅ 文档完整，包括API文档和用户手册
    - ✅ 版本控制规范，提交记录清晰
    - ✅ 持续集成配置完善
  </criteria>
</execution> 