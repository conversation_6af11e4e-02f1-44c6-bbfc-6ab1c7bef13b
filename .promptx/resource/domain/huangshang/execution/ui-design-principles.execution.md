<execution>
  <constraint>
    ## UI设计技术约束
    - **平台一致性**：必须遵循各平台的设计规范（Windows、macOS、Linux）
    - **可访问性要求**：符合WCAG 2.1 AA级别的可访问性标准
    - **性能限制**：动画帧率不低于30fps，响应时间不超过100ms
    - **分辨率适配**：支持从1024x768到4K及以上的分辨率
    - **色彩空间**：支持sRGB色彩空间，考虑色盲用户需求
    - **字体渲染**：确保在不同DPI下的字体清晰度
  </constraint>

  <rule>
    ## UI设计强制规则
    - **用户优先原则**：所有设计决策必须以用户体验为核心
    - **一致性原则**：界面元素在整个应用中保持风格和行为一致
    - **反馈原则**：每个用户操作都必须有明确的视觉反馈
    - **容错原则**：提供撤销机制，防止用户误操作造成损失
    - **可预测性原则**：界面行为符合用户的心理模型和预期
    - **渐进式披露**：复杂功能分层展示，避免信息过载
  </rule>

  <guideline>
    ## UI设计指导原则
    - **简洁性**：去除不必要的元素，突出核心功能
    - **层次性**：通过视觉层次引导用户注意力
    - **美观性**：追求视觉美感，提升用户满意度
    - **功能性**：美观不能牺牲功能性和可用性
    - **适应性**：设计能够适应不同设备和环境
    - **可维护性**：设计系统化，便于后续维护和扩展
  </guideline>

  <process>
    ## UI设计完整流程

    ### Phase 1: 用户研究与需求分析
    ```mermaid
    flowchart TD
        A[用户调研] --> B[需求分析]
        B --> C[用户画像]
        C --> D[使用场景]
        D --> E[功能优先级]
        E --> F[设计目标]
        
        style A fill:#e8f5e9
        style F fill:#e1f5fe
    ```

    #### 1.1 用户研究方法
    ```mermaid
    mindmap
      root((用户研究))
        定量研究
          问卷调查
          数据分析
          A/B测试
          热力图分析
        定性研究
          用户访谈
          焦点小组
          可用性测试
          用户观察
        竞品分析
          功能对比
          交互分析
          视觉分析
          技术分析
    ```

    #### 1.2 用户画像构建
    ```cpp
    // 用户画像数据结构
    struct UserPersona {
        QString name;
        int age;
        QString occupation;
        QString techProficiency;  // 技术熟练度
        QStringList goals;        // 使用目标
        QStringList painPoints;   // 痛点
        QStringList preferences;  // 偏好
        QString platform;         // 主要使用平台
    };
    ```

    ### Phase 2: 信息架构设计
    ```mermaid
    graph TD
        A[内容清单] --> B[信息分类]
        B --> C[层级结构]
        C --> D[导航设计]
        D --> E[页面流程]
        E --> F[交互模式]
        
        style A fill:#fff3e0
        style F fill:#e8f5e9
    ```

    #### 2.1 信息架构原则
    - **分类原则**：按功能、用户、任务等维度分类
    - **层次原则**：建立清晰的信息层次结构
    - **导航原则**：提供多种导航方式，满足不同用户习惯
    - **搜索原则**：为复杂应用提供强大的搜索功能

    #### 2.2 导航设计模式
    ```mermaid
    graph LR
        A[导航模式] --> B[标签导航]
        A --> C[侧边栏导航]
        A --> D[面包屑导航]
        A --> E[下拉菜单]
        A --> F[工具栏]
        
        B --> B1[适用于并列功能]
        C --> C1[适用于层次结构]
        D --> D1[适用于深层导航]
        E --> E1[适用于功能分组]
        F --> F1[适用于常用操作]
    ```

    ### Phase 3: 界面布局设计
    ```mermaid
    flowchart TD
        A[栅格系统] --> B[布局结构]
        B --> C[响应式设计]
        C --> D[组件布局]
        D --> E[留白设计]
        E --> F[视觉层次]
        
        style A fill:#e3f2fd
        style F fill:#e8f5e9
    ```

    #### 3.1 栅格系统设计
    ```cpp
    // Qt中的栅格布局实现
    class GridLayoutManager : public QObject
    {
        Q_OBJECT
        
    public:
        enum GridType {
            Grid12Column,   // 12列栅格
            Grid16Column,   // 16列栅格
            Grid24Column    // 24列栅格
        };
        
        GridLayoutManager(GridType type, QWidget *parent = nullptr);
        
        void setColumnSpacing(int spacing);
        void setRowSpacing(int spacing);
        void setMargins(int left, int top, int right, int bottom);
        
        QGridLayout* createLayout();
        void addWidget(QWidget *widget, int row, int column, int rowSpan = 1, int columnSpan = 1);
        
    private:
        GridType m_gridType;
        int m_columnCount;
        int m_columnSpacing;
        int m_rowSpacing;
        QMargins m_margins;
    };
    ```

    #### 3.2 响应式布局实现
    ```cpp
    class ResponsiveLayout : public QLayout
    {
        Q_OBJECT
        
    public:
        enum BreakPoint {
            XSmall = 480,   // 手机
            Small = 768,    // 平板
            Medium = 1024,  // 小屏笔记本
            Large = 1440,   // 大屏显示器
            XLarge = 1920   // 超大屏
        };
        
        ResponsiveLayout(QWidget *parent = nullptr);
        
        void addWidget(QWidget *widget, BreakPoint minWidth, BreakPoint maxWidth = XLarge);
        void setLayoutForBreakPoint(BreakPoint breakPoint, QLayout *layout);
        
    protected:
        void resizeEvent(QResizeEvent *event) override;
        
    private:
        void updateLayout();
        BreakPoint getCurrentBreakPoint() const;
        
        QMap<BreakPoint, QLayout*> m_layouts;
        BreakPoint m_currentBreakPoint;
    };
    ```

    ### Phase 4: 视觉设计系统
    ```mermaid
    graph TD
        A[设计语言] --> B[颜色系统]
        B --> C[字体系统]
        C --> D[图标系统]
        D --> E[组件库]
        E --> F[动效系统]
        
        A --> A1[品牌识别]
        A --> A2[设计原则]
        A --> A3[视觉风格]
        
        B --> B1[主色调]
        B --> B2[辅助色]
        B --> B3[语义色]
        
        C --> C1[字体层次]
        C --> C2[字重选择]
        C --> C3[行高间距]
    ```

    #### 4.1 颜色系统设计
    ```cpp
    class ColorSystem : public QObject
    {
        Q_OBJECT
        
    public:
        enum ColorRole {
            Primary,        // 主色
            Secondary,      // 次要色
            Success,        // 成功色
            Warning,        // 警告色
            Error,          // 错误色
            Info,           // 信息色
            Background,     // 背景色
            Surface,        // 表面色
            OnPrimary,      // 主色上的文字
            OnSecondary,    // 次要色上的文字
            OnBackground,   // 背景上的文字
            OnSurface       // 表面上的文字
        };
        
        enum ColorVariant {
            Light,          // 浅色变体
            Main,           // 主色
            Dark,           // 深色变体
            Contrast        // 对比色
        };
        
        static ColorSystem* instance();
        
        QColor getColor(ColorRole role, ColorVariant variant = Main) const;
        void setTheme(const QString &themeName);
        void setColorScheme(const QMap<ColorRole, QColor> &colors);
        
    signals:
        void themeChanged(const QString &themeName);
        
    private:
        QMap<ColorRole, QMap<ColorVariant, QColor>> m_colorScheme;
        QString m_currentTheme;
        
        void initializeDefaultColors();
        void initializeDarkColors();
        void initializeLightColors();
    };
    ```

    #### 4.2 字体系统设计
    ```cpp
    class TypographySystem : public QObject
    {
        Q_OBJECT
        
    public:
        enum FontRole {
            DisplayLarge,    // 大标题
            DisplayMedium,   // 中标题
            DisplaySmall,    // 小标题
            HeadlineLarge,   // 大标题
            HeadlineMedium,  // 中标题
            HeadlineSmall,   // 小标题
            TitleLarge,      // 大标题
            TitleMedium,     // 中标题
            TitleSmall,      // 小标题
            BodyLarge,       // 大正文
            BodyMedium,      // 中正文
            BodySmall,       // 小正文
            LabelLarge,      // 大标签
            LabelMedium,     // 中标签
            LabelSmall       // 小标签
        };
        
        static TypographySystem* instance();
        
        QFont getFont(FontRole role) const;
        void setFontFamily(const QString &fontFamily);
        void setFontScale(qreal scale);
        
    private:
        QString m_fontFamily;
        qreal m_fontScale;
        QMap<FontRole, QFont> m_fonts;
        
        void initializeFonts();
        void updateFonts();
    };
    ```

    ### Phase 5: 交互设计
    ```mermaid
    flowchart TD
        A[交互模式] --> B[状态设计]
        B --> C[转场动画]
        C --> D[反馈机制]
        D --> E[手势支持]
        E --> F[键盘导航]
        
        A --> A1[点击交互]
        A --> A2[拖拽交互]
        A --> A3[滚动交互]
        A --> A4[悬停交互]
        
        B --> B1[默认状态]
        B --> B2[悬停状态]
        B --> B3[激活状态]
        B --> B4[禁用状态]
        B --> B5[加载状态]
    ```

    #### 5.1 交互状态管理
    ```cpp
    class InteractionStateManager : public QObject
    {
        Q_OBJECT
        
    public:
        enum State {
            Normal,
            Hovered,
            Pressed,
            Focused,
            Disabled,
            Loading,
            Error
        };
        
        InteractionStateManager(QWidget *widget);
        
        void setState(State state);
        State currentState() const;
        
        void setStateStyle(State state, const QString &styleSheet);
        void setStateAnimation(State fromState, State toState, QPropertyAnimation *animation);
        
    signals:
        void stateChanged(State oldState, State newState);
        
    private slots:
        void onAnimationFinished();
        
    private:
        QWidget *m_widget;
        State m_currentState;
        QMap<State, QString> m_stateStyles;
        QMap<QPair<State, State>, QPropertyAnimation*> m_animations;
        
        void applyState(State state);
        void playTransition(State fromState, State toState);
    };
    ```

    #### 5.2 动画系统设计
    ```cpp
    class AnimationSystem : public QObject
    {
        Q_OBJECT
        
    public:
        enum AnimationType {
            FadeIn,
            FadeOut,
            SlideIn,
            SlideOut,
            ScaleIn,
            ScaleOut,
            Bounce,
            Elastic
        };
        
        enum EasingType {
            Linear,
            EaseIn,
            EaseOut,
            EaseInOut,
            EaseInBack,
            EaseOutBack,
            EaseInOutBack
        };
        
        static AnimationSystem* instance();
        
        QPropertyAnimation* createAnimation(QWidget *widget, 
                                          AnimationType type, 
                                          int duration = 300,
                                          EasingType easing = EaseInOut);
        
        void playAnimation(QWidget *widget, AnimationType type);
        void stopAnimation(QWidget *widget);
        
    private:
        QMap<QWidget*, QPropertyAnimation*> m_activeAnimations;
        
        QEasingCurve getEasingCurve(EasingType type) const;
        void setupFadeAnimation(QPropertyAnimation *animation, AnimationType type);
        void setupSlideAnimation(QPropertyAnimation *animation, AnimationType type);
        void setupScaleAnimation(QPropertyAnimation *animation, AnimationType type);
    };
    ```

    ### Phase 6: 可访问性设计
    ```mermaid
    graph TD
        A[可访问性] --> B[视觉可访问性]
        B --> C[听觉可访问性]
        C --> D[运动可访问性]
        D --> E[认知可访问性]
        E --> F[技术可访问性]
        
        B --> B1[色彩对比度]
        B --> B2[字体大小]
        B --> B3[色盲友好]
        
        C --> C1[视觉反馈]
        C --> C2[文字描述]
        
        D --> D1[键盘导航]
        D --> D2[大按钮]
        D --> D3[手势简化]
        
        E --> E1[清晰标签]
        E --> E2[一致性]
        E --> E3[简化操作]
        
        F --> F1[屏幕阅读器]
        F --> F2[语音识别]
        F --> F3[高对比度]
    ```

    #### 6.1 可访问性实现
    ```cpp
    class AccessibilityManager : public QObject
    {
        Q_OBJECT
        
    public:
        static AccessibilityManager* instance();
        
        void setAccessibleName(QWidget *widget, const QString &name);
        void setAccessibleDescription(QWidget *widget, const QString &description);
        void setAccessibleRole(QWidget *widget, QAccessible::Role role);
        
        void enableHighContrast(bool enable);
        void setFontScale(qreal scale);
        void enableKeyboardNavigation(bool enable);
        
        bool isHighContrastEnabled() const;
        qreal fontScale() const;
        bool isKeyboardNavigationEnabled() const;
        
    signals:
        void accessibilitySettingsChanged();
        
    private:
        bool m_highContrastEnabled;
        qreal m_fontScale;
        bool m_keyboardNavigationEnabled;
        
        void updateAccessibilitySettings();
        void applyHighContrastTheme();
        void applyFontScale();
    };
    ```

    ### Phase 7: 用户测试与迭代
    ```mermaid
    flowchart TD
        A[原型测试] --> B[可用性测试]
        B --> C[A/B测试]
        C --> D[数据分析]
        D --> E[问题识别]
        E --> F[设计迭代]
        F --> G[再次测试]
        
        style A fill:#e8f5e9
        style G fill:#e1f5fe
    ```

    #### 7.1 测试方法论
    ```cpp
    class UsabilityTester : public QObject
    {
        Q_OBJECT
        
    public:
        struct TestScenario {
            QString name;
            QString description;
            QStringList tasks;
            QStringList successCriteria;
            int expectedDuration;
        };
        
        struct TestResult {
            QString scenarioName;
            bool completed;
            int duration;
            int errorCount;
            int satisfactionScore;
            QStringList feedback;
        };
        
        UsabilityTester(QObject *parent = nullptr);
        
        void addScenario(const TestScenario &scenario);
        void startTest(const QString &scenarioName);
        void recordAction(const QString &action);
        void recordError(const QString &error);
        void finishTest(int satisfactionScore, const QStringList &feedback);
        
        TestResult getTestResult(const QString &scenarioName) const;
        QList<TestResult> getAllResults() const;
        
    signals:
        void testStarted(const QString &scenarioName);
        void testFinished(const TestResult &result);
        
    private:
        QList<TestScenario> m_scenarios;
        QList<TestResult> m_results;
        QString m_currentScenario;
        QElapsedTimer m_timer;
        int m_errorCount;
        QStringList m_actions;
    };
    ```
  </process>

  <criteria>
    ## UI设计质量标准

    ### 用户体验标准
    - ✅ 用户能够在3秒内理解界面主要功能
    - ✅ 核心任务完成率 > 95%
    - ✅ 用户满意度评分 > 4.0/5.0
    - ✅ 错误率 < 5%
    - ✅ 学习成本 < 10分钟

    ### 视觉设计标准
    - ✅ 色彩对比度符合WCAG 2.1 AA标准
    - ✅ 字体大小不小于14px
    - ✅ 点击目标不小于44x44px
    - ✅ 界面元素间距符合8px栅格系统
    - ✅ 品牌一致性得分 > 90%

    ### 交互设计标准
    - ✅ 响应时间 < 100ms
    - ✅ 动画流畅度 > 30fps
    - ✅ 支持键盘导航
    - ✅ 提供明确的操作反馈
    - ✅ 错误处理机制完善

    ### 技术实现标准
    - ✅ 支持多分辨率适配
    - ✅ 内存使用合理
    - ✅ 渲染性能优化
    - ✅ 兼容性测试通过
    - ✅ 可访问性测试通过

    ### 可维护性标准
    - ✅ 设计系统完整
    - ✅ 组件复用率 > 80%
    - ✅ 设计文档完整
    - ✅ 代码注释清晰
    - ✅ 版本控制规范
  </criteria>
</execution> 