# Qt5.15.2 专业知识体系

## 核心架构与模块

### Qt5架构概览
```mermaid
graph TD
    A[Qt5 Framework] --> B[Qt Core]
    A --> C[Qt GUI]
    A --> D[Qt Widgets]
    A --> E[Qt Quick]
    A --> F[Qt Network]
    A --> G[Qt SQL]
    A --> H[Qt Multimedia]
    A --> I[Qt WebEngine]
    
    B --> B1[QObject系统]
    B --> B2[信号槽机制]
    B --> B3[元对象系统]
    B --> B4[事件系统]
    
    C --> C1[QPainter]
    C --> C2[图形渲染]
    C --> C3[字体系统]
    C --> C4[图像处理]
    
    D --> D1[传统桌面控件]
    D --> D2[布局管理]
    D --> D3[样式系统]
    D --> D4[主题支持]
    
    E --> E1[QML声明式UI]
    E --> E2[JavaScript引擎]
    E --> E3[场景图渲染]
    E --> E4[动画系统]
```

### Qt5.15.2 LTS特性
- **长期支持版本**：提供3年的商业支持和bug修复
- **C++17完全支持**：支持现代C++特性，包括auto、lambda、智能指针等
- **高DPI支持**：完善的4K/5K显示器支持，自动缩放机制
- **跨平台一致性**：Windows、macOS、Linux、Android、iOS统一API
- **WebEngine集成**：基于Chromium的现代Web技术支持
- **多媒体增强**：完整的音频、视频、相机支持

## 核心编程概念

### 1. QObject系统
```cpp
// QObject是Qt对象系统的基类
class MyObject : public QObject
{
    Q_OBJECT  // 必须包含此宏

public:
    explicit MyObject(QObject *parent = nullptr);
    
    // 属性系统
    Q_PROPERTY(QString name READ name WRITE setName NOTIFY nameChanged)
    
    QString name() const { return m_name; }
    void setName(const QString &name);

signals:
    void nameChanged(const QString &name);

private:
    QString m_name;
};

// 实现
void MyObject::setName(const QString &name)
{
    if (m_name != name) {
        m_name = name;
        emit nameChanged(name);  // 发射信号
    }
}
```

### 2. 信号槽机制
```cpp
// 新式信号槽连接（Qt5推荐）
connect(button, &QPushButton::clicked, this, &MainWindow::onButtonClicked);

// Lambda表达式连接
connect(button, &QPushButton::clicked, [this]() {
    statusBar()->showMessage("Button clicked!");
});

// 跨线程连接
connect(worker, &Worker::finished, this, &MainWindow::onWorkerFinished, Qt::QueuedConnection);

// 一次性连接
connect(timer, &QTimer::timeout, this, &MainWindow::onTimeout, Qt::SingleShotConnection);
```

### 3. 元对象系统
```cpp
// 运行时类型信息
QObject *obj = new QPushButton("Click me");
qDebug() << obj->metaObject()->className();  // 输出: QPushButton

// 动态属性
obj->setProperty("customProperty", "value");
QVariant value = obj->property("customProperty");

// 动态方法调用
QMetaObject::invokeMethod(obj, "click", Qt::QueuedConnection);
```

## UI开发技术栈

### 1. Qt Widgets开发
```cpp
// 主窗口类
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onActionTriggered();
    void onButtonClicked();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupCentralWidget();
    void setupConnections();
    
    Ui::MainWindow *ui;
    QAction *m_openAction;
    QAction *m_saveAction;
    QToolBar *m_toolBar;
    QStatusBar *m_statusBar;
};

// 实现
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    setupUI();
    setupConnections();
}

void MainWindow::setupUI()
{
    // 设置窗口属性
    setWindowTitle("Qt5.15.2 Application");
    setMinimumSize(800, 600);
    
    // 设置中心部件
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    // 创建布局
    QVBoxLayout *layout = new QVBoxLayout(centralWidget);
    
    // 添加控件
    QPushButton *button = new QPushButton("Click Me", this);
    layout->addWidget(button);
    
    // 连接信号槽
    connect(button, &QPushButton::clicked, this, &MainWindow::onButtonClicked);
}
```

### 2. 布局管理系统
```cpp
// 垂直布局
QVBoxLayout *vLayout = new QVBoxLayout();
vLayout->addWidget(widget1);
vLayout->addWidget(widget2);
vLayout->addStretch();  // 添加弹性空间

// 水平布局
QHBoxLayout *hLayout = new QHBoxLayout();
hLayout->addWidget(leftWidget);
hLayout->addWidget(rightWidget, 1);  // 拉伸因子为1

// 网格布局
QGridLayout *gridLayout = new QGridLayout();
gridLayout->addWidget(widget1, 0, 0);  // 行0，列0
gridLayout->addWidget(widget2, 0, 1);  // 行0，列1
gridLayout->addWidget(widget3, 1, 0, 1, 2);  // 跨两列

// 表单布局
QFormLayout *formLayout = new QFormLayout();
formLayout->addRow("Name:", nameLineEdit);
formLayout->addRow("Email:", emailLineEdit);
formLayout->addRow("Phone:", phoneLineEdit);
```

### 3. 样式系统 (QSS)
```cpp
// 基础样式设置
QString buttonStyle = R"(
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 14px;
        font-weight: bold;
    }
    
    QPushButton:hover {
        background-color: #2980b9;
    }
    
    QPushButton:pressed {
        background-color: #21618c;
    }
    
    QPushButton:disabled {
        background-color: #bdc3c7;
        color: #7f8c8d;
    }
)";

button->setStyleSheet(buttonStyle);

// 全局样式
QString globalStyle = R"(
    QMainWindow {
        background-color: #ecf0f1;
    }
    
    QMenuBar {
        background-color: #34495e;
        color: white;
        border: none;
    }
    
    QMenuBar::item {
        background-color: transparent;
        padding: 5px 10px;
    }
    
    QMenuBar::item:selected {
        background-color: #2c3e50;
    }
    
    QStatusBar {
        background-color: #34495e;
        color: white;
        border: none;
    }
)";

qApp->setStyleSheet(globalStyle);
```

## 高级特性

### 1. 模型视图架构
```cpp
// 自定义表格模型
class CustomTableModel : public QAbstractTableModel
{
    Q_OBJECT

public:
    explicit CustomTableModel(QObject *parent = nullptr);
    
    // 必须实现的方法
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    
    // 可编辑模型
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    
    // 数据操作
    void addRow(const QStringList &data);
    void removeRow(int row);
    void clear();

private:
    QList<QStringList> m_data;
    QStringList m_headers;
};

// 使用模型
CustomTableModel *model = new CustomTableModel(this);
QTableView *view = new QTableView(this);
view->setModel(model);

// 添加数据
model->addRow({"John", "25", "Engineer"});
model->addRow({"Jane", "30", "Designer"});
```

### 2. 自定义控件开发
```cpp
// 自定义按钮控件
class CustomButton : public QWidget
{
    Q_OBJECT

public:
    explicit CustomButton(const QString &text, QWidget *parent = nullptr);
    
    void setText(const QString &text);
    QString text() const;
    
    void setIcon(const QIcon &icon);
    QIcon icon() const;

signals:
    void clicked();

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void enterEvent(QEvent *event) override;
    void leaveEvent(QEvent *event) override;

private:
    QString m_text;
    QIcon m_icon;
    bool m_pressed;
    bool m_hovered;
    
    void updateStyle();
};

// 实现绘制
void CustomButton::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制背景
    QColor bgColor = m_pressed ? QColor("#2980b9") : 
                     m_hovered ? QColor("#3498db") : QColor("#ecf0f1");
    painter.fillRect(rect(), bgColor);
    
    // 绘制边框
    painter.setPen(QPen(QColor("#bdc3c7"), 1));
    painter.drawRect(rect().adjusted(0, 0, -1, -1));
    
    // 绘制文本
    painter.setPen(QColor("#2c3e50"));
    painter.drawText(rect(), Qt::AlignCenter, m_text);
}
```

### 3. 多线程编程
```cpp
// 工作线程
class WorkerThread : public QThread
{
    Q_OBJECT

public:
    explicit WorkerThread(QObject *parent = nullptr);
    void setData(const QStringList &data);

signals:
    void progressChanged(int percentage);
    void finished(const QStringList &result);
    void error(const QString &errorMessage);

protected:
    void run() override;

private:
    QStringList m_inputData;
    QStringList m_result;
    bool m_abort;
};

// 实现
void WorkerThread::run()
{
    m_abort = false;
    m_result.clear();
    
    for (int i = 0; i < m_inputData.size(); ++i) {
        if (m_abort) {
            return;
        }
        
        // 模拟耗时操作
        QThread::msleep(100);
        
        // 处理数据
        QString processed = processData(m_inputData[i]);
        m_result.append(processed);
        
        // 发射进度信号
        int progress = (i + 1) * 100 / m_inputData.size();
        emit progressChanged(progress);
    }
    
    emit finished(m_result);
}

// 在主线程中使用
WorkerThread *worker = new WorkerThread(this);
connect(worker, &WorkerThread::progressChanged, progressBar, &QProgressBar::setValue);
connect(worker, &WorkerThread::finished, this, &MainWindow::onWorkerFinished);
connect(worker, &WorkerThread::finished, worker, &QObject::deleteLater);

worker->setData(inputData);
worker->start();
```

## 性能优化技巧

### 1. 内存管理
```cpp
// 使用智能指针
std::unique_ptr<QWidget> widget = std::make_unique<QWidget>();
std::shared_ptr<QObject> object = std::make_shared<QObject>();

// Qt对象树内存管理
QWidget *parent = new QWidget();
QWidget *child = new QWidget(parent);  // 自动管理内存

// 延迟删除
object->deleteLater();  // 在事件循环中安全删除
```

### 2. 渲染优化
```cpp
// 减少重绘
class OptimizedWidget : public QWidget
{
public:
    OptimizedWidget(QWidget *parent = nullptr);

protected:
    void paintEvent(QPaintEvent *event) override;

private:
    mutable QPixmap m_cache;
    mutable bool m_cacheValid;
    
    void invalidateCache();
};

void OptimizedWidget::paintEvent(QPaintEvent *event)
{
    if (!m_cacheValid || m_cache.size() != size()) {
        m_cache = QPixmap(size());
        m_cache.fill(Qt::transparent);
        
        QPainter cachePainter(&m_cache);
        cachePainter.setRenderHint(QPainter::Antialiasing);
        
        // 绘制到缓存
        drawContent(&cachePainter);
        m_cacheValid = true;
    }
    
    QPainter painter(this);
    painter.drawPixmap(0, 0, m_cache);
}
```

### 3. 事件处理优化
```cpp
// 事件过滤器
class EventFilter : public QObject
{
    Q_OBJECT

public:
    explicit EventFilter(QObject *parent = nullptr);

protected:
    bool eventFilter(QObject *watched, QEvent *event) override;

private:
    QElapsedTimer m_timer;
    int m_eventCount;
};

bool EventFilter::eventFilter(QObject *watched, QEvent *event)
{
    // 限制事件频率
    if (event->type() == QEvent::MouseMove) {
        if (!m_timer.hasExpired(16)) {  // 限制为60fps
            return true;  // 过滤掉事件
        }
        m_timer.restart();
    }
    
    return QObject::eventFilter(watched, event);
}
```

## 跨平台开发

### 1. 平台特定代码
```cpp
// 平台检测
#ifdef Q_OS_WINDOWS
    // Windows特定代码
    #include <windows.h>
    void setWindowsSpecificFeatures();
#elif defined(Q_OS_MACOS)
    // macOS特定代码
    #include <Cocoa/Cocoa.h>
    void setMacOSSpecificFeatures();
#elif defined(Q_OS_LINUX)
    // Linux特定代码
    #include <X11/Xlib.h>
    void setLinuxSpecificFeatures();
#endif

// 运行时平台检测
QString platform = QSysInfo::productType();
if (platform == "windows") {
    // Windows代码
} else if (platform == "macos") {
    // macOS代码
} else if (platform == "ubuntu") {
    // Ubuntu代码
}
```

### 2. 高DPI支持
```cpp
// 在main函数中启用高DPI
int main(int argc, char *argv[])
{
    // Qt5.6+
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    // Qt5.14+
    QApplication::setHighDpiScaleFactorRoundingPolicy(
        Qt::HighDpiScaleFactorRoundingPolicy::PassThrough);
    
    QApplication app(argc, argv);
    
    // 获取DPI信息
    qreal dpiX = app.primaryScreen()->logicalDotsPerInchX();
    qreal dpiY = app.primaryScreen()->logicalDotsPerInchY();
    qreal devicePixelRatio = app.primaryScreen()->devicePixelRatio();
    
    qDebug() << "DPI:" << dpiX << "x" << dpiY;
    qDebug() << "Device Pixel Ratio:" << devicePixelRatio;
    
    return app.exec();
}
```

## 调试与测试

### 1. 调试技巧
```cpp
// 调试输出
qDebug() << "Debug message";
qWarning() << "Warning message";
qCritical() << "Critical message";

// 条件调试
Q_ASSERT(pointer != nullptr);
Q_ASSERT_X(condition, "function", "message");

// 性能测试
QElapsedTimer timer;
timer.start();
// 执行代码
qint64 elapsed = timer.elapsed();
qDebug() << "Elapsed time:" << elapsed << "ms";
```

### 2. 单元测试
```cpp
// 测试类
class TestWidget : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void testWidgetCreation();
    void testSignalSlot();
    void testUserInteraction();
};

void TestWidget::testUserInteraction()
{
    QWidget widget;
    QPushButton button("Test", &widget);
    
    // 模拟用户点击
    QTest::mouseClick(&button, Qt::LeftButton);
    
    // 验证结果
    QCOMPARE(button.text(), QString("Test"));
}

// 运行测试
QTEST_MAIN(TestWidget)
#include "test_widget.moc"
```

## 最佳实践

### 1. 代码组织
```cpp
// 头文件组织
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QtWidgets/QMainWindow>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QPushButton>
#include <QtCore/QTimer>

QT_BEGIN_NAMESPACE
class QLabel;
class QProgressBar;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow() override;

private slots:
    void onButtonClicked();
    void onTimerTimeout();

private:
    void setupUI();
    void setupConnections();
    
    QWidget *m_centralWidget;
    QVBoxLayout *m_layout;
    QPushButton *m_button;
    QLabel *m_label;
    QProgressBar *m_progressBar;
    QTimer *m_timer;
};

#endif // MAINWINDOW_H
```

### 2. 资源管理
```cpp
// 资源文件 (resources.qrc)
<!DOCTYPE RCC>
<RCC version="1.0">
    <qresource>
        <file>images/icon.png</file>
        <file>styles/default.qss</file>
        <file>translations/app_zh_CN.qm</file>
    </qresource>
</RCC>

// 使用资源
QIcon icon(":/images/icon.png");
QFile styleFile(":/styles/default.qss");
styleFile.open(QFile::ReadOnly);
QString style = styleFile.readAll();
qApp->setStyleSheet(style);
```

### 3. 国际化支持
```cpp
// 在代码中使用tr()
QString message = tr("Hello, World!");
QString formatted = tr("Processing %1 of %2 files").arg(current).arg(total);

// 动态语言切换
void MainWindow::changeLanguage(const QString &locale)
{
    static QTranslator translator;
    
    qApp->removeTranslator(&translator);
    
    if (translator.load(QString(":/translations/app_%1.qm").arg(locale))) {
        qApp->installTranslator(&translator);
        ui->retranslateUi(this);  // 重新翻译UI
    }
}
```

这个知识体系涵盖了Qt5.15.2的核心概念、高级特性、性能优化、跨平台开发、调试测试和最佳实践，为Qt UI开发提供了全面的技术支持。 