<execution>
  <constraint>
    ## C++编译环境约束
    - **编译器要求**：支持C++17或更高标准，推荐GCC 7+或Clang 5+
    - **Qt版本要求**：Qt 5.12以上版本，确保图形API稳定性
    - **数学库依赖**：Eigen3用于矩阵运算，GLM用于OpenGL数学计算
    - **平台兼容性**：支持Windows/Linux/macOS的跨平台编译
    
    ## 性能约束边界
    - **实时渲染要求**：关键操作必须在16ms内完成（60FPS）
    - **内存使用限制**：单个图形对象内存占用不超过1MB
    - **计算精度要求**：几何计算相对误差小于1e-9
    - **并发安全性**：多线程环境下的图形计算安全性
  </constraint>

  <rule>
    ## 代码结构强制规则
    - **类设计原则**：每个几何图元必须继承自基础图元类
    - **命名规范**：使用CamelCase命名类，snake_case命名变量
    - **文件组织**：头文件和实现文件分离，模板定义在头文件中
    - **错误处理**：使用异常处理机制，定义专门的几何计算异常类
    
    ## 算法选择规则
    - **O(n²)以下**：单次操作算法复杂度不超过O(n²)
    - **数值稳定性优先**：选择数值稳定的算法实现
    - **Qt兼容性优先**：优先使用Qt提供的图形算法
    - **测试驱动**：每个核心算法必须有对应的单元测试
  </rule>

  <guideline>
    ## 开发指导原则
    - **渐进式优化**：先实现功能正确性，再进行性能优化
    - **文档驱动**：复杂算法需要详细的数学推导文档
    - **模块化设计**：将图形操作分解为可复用的基础模块
    - **用户体验优先**：图形界面响应性优于算法复杂度
    
    ## Qt集成指南
    - **坐标系统**：统一使用Qt的坐标系统，避免频繁转换
    - **事件处理**：使用Qt的事件系统实现图形交互
    - **渲染优化**：合理使用QPainter的缓存机制
    - **内存管理**：利用Qt的父子对象机制管理图形对象生命周期
  </guideline>

  <process>
    ## 图形问题解决完整流程
    
    ### Step 1: 需求分析与几何建模 (深度理解阶段)
    
    ```mermaid
    flowchart TD
        A[接收图形需求] --> B{需求类型识别}
        B -->|2D图形| C[2D几何建模]
        B -->|3D图形| D[3D几何建模]
        B -->|变换操作| E[变换矩阵设计]
        
        C --> F[选择2D算法]
        D --> G[选择3D算法]
        E --> H[设计变换管线]
        
        F --> I[数据结构设计]
        G --> I
        H --> I
    ```
    
    **具体执行步骤**：
    1. **需求澄清**：明确图形的类型、精度要求、性能目标
    2. **数学建模**：将问题转换为几何数学表达式
    3. **算法调研**：选择最适合的计算几何算法
    4. **复杂度分析**：评估时间和空间复杂度
    
    ### Step 2: C++核心实现 (算法编码阶段)
    
    ```mermaid
    flowchart TD
        J[设计类架构] --> K[实现基础几何类]
        K --> L[实现变换算法]
        L --> M[性能关键路径优化]
        M --> N[单元测试编写]
        N --> O{测试通过?}
        O -->|否| K
        O -->|是| P[代码审查]
    ```
    
    **核心实现清单**：
    ```cpp
    // 基础几何类设计示例
    class GeometryBase {
    public:
        virtual ~GeometryBase() = default;
        virtual void transform(const QTransform& matrix) = 0;
        virtual QRectF boundingRect() const = 0;
        virtual bool contains(const QPointF& point) const = 0;
    };
    
    // 具体图元实现
    class PolygonGeometry : public GeometryBase {
        std::vector<QPointF> vertices_;
        // 实现具体的几何运算
    };
    ```
    
    ### Step 3: Qt图形界面集成 (UI集成阶段)
    
    ```mermaid
    flowchart TD
        Q[创建QGraphicsItem子类] --> R[实现paint方法]
        R --> S[处理鼠标事件]
        S --> T[集成到QGraphicsScene]
        T --> U[优化渲染性能]
        U --> V[用户交互测试]
    ```
    
    **Qt集成要点**：
    ```cpp
    class CustomGraphicsItem : public QGraphicsItem {
        std::unique_ptr<GeometryBase> geometry_;
    public:
        QRectF boundingRect() const override;
        void paint(QPainter* painter, 
                  const QStyleOptionGraphicsItem* option,
                  QWidget* widget) override;
        void mousePressEvent(QGraphicsSceneMouseEvent* event) override;
    };
    ```
    
    ### Step 4: 性能优化与测试 (质量保证阶段)
    
    ```mermaid
    flowchart TD
        W[性能基准测试] --> X{是否满足要求?}
        X -->|否| Y[识别性能瓶颈]
        Y --> Z[算法优化]
        Z --> W
        X -->|是| AA[边界条件测试]
        AA --> BB[用户验收测试]
        BB --> CC[文档完善]
    ```
    
    **优化策略**：
    - **热点分析**：使用profiler识别性能瓶颈
    - **SIMD优化**：对向量运算使用SSE/AVX指令
    - **缓存友好**：优化数据结构的内存布局
    - **GPU加速**：考虑使用OpenGL进行并行计算
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 功能正确性
    - ✅ 几何计算结果数学正确
    - ✅ 边界条件处理完善
    - ✅ 数值精度满足要求
    - ✅ 异常情况处理得当
    
    ### 性能指标
    - ✅ 实时渲染帧率稳定在60FPS
    - ✅ 内存使用量在合理范围内
    - ✅ CPU使用率不超过单核80%
    - ✅ 响应延迟小于100ms
    
    ### 代码质量
    - ✅ 代码结构清晰易维护
    - ✅ 单元测试覆盖率>90%
    - ✅ 文档完整准确
    - ✅ 符合C++最佳实践
    
    ### 用户体验
    - ✅ 图形界面响应流畅
    - ✅ 交互操作直观自然
    - ✅ 错误提示友好清晰
    - ✅ 跨平台表现一致
  </criteria>
</execution> 