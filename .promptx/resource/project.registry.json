{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-25T00:47:18.236Z", "updatedAt": "2025-07-25T00:47:18.266Z", "resourceCount": 20}, "resources": [{"id": "cpp-development-workflow", "source": "project", "protocol": "execution", "name": "Cpp Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/gg/execution/cpp-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-25T00:47:18.241Z", "updatedAt": "2025-07-25T00:47:18.241Z", "scannedAt": "2025-07-25T00:47:18.241Z", "path": "domain/gg/execution/cpp-development-workflow.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/gg/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-07-25T00:47:18.241Z", "updatedAt": "2025-07-25T00:47:18.241Z", "scannedAt": "2025-07-25T00:47:18.241Z", "path": "domain/gg/execution/quality-assurance.execution.md"}}, {"id": "gg", "source": "project", "protocol": "role", "name": "Gg 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/gg/gg.role.md", "metadata": {"createdAt": "2025-07-25T00:47:18.242Z", "updatedAt": "2025-07-25T00:47:18.242Z", "scannedAt": "2025-07-25T00:47:18.242Z", "path": "domain/gg/gg.role.md"}}, {"id": "cpp11-expertise", "source": "project", "protocol": "knowledge", "name": "Cpp11 Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/gg/knowledge/cpp11-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-25T00:47:18.243Z", "updatedAt": "2025-07-25T00:47:18.243Z", "scannedAt": "2025-07-25T00:47:18.243Z", "path": "domain/gg/knowledge/cpp11-expertise.knowledge.md"}}, {"id": "peripheral-integration", "source": "project", "protocol": "knowledge", "name": "Peripheral Integration 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/gg/knowledge/peripheral-integration.knowledge.md", "metadata": {"createdAt": "2025-07-25T00:47:18.244Z", "updatedAt": "2025-07-25T00:47:18.244Z", "scannedAt": "2025-07-25T00:47:18.244Z", "path": "domain/gg/knowledge/peripheral-integration.knowledge.md"}}, {"id": "qt5-framework", "source": "project", "protocol": "knowledge", "name": "Qt5 Framework 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/gg/knowledge/qt5-framework.knowledge.md", "metadata": {"createdAt": "2025-07-25T00:47:18.245Z", "updatedAt": "2025-07-25T00:47:18.245Z", "scannedAt": "2025-07-25T00:47:18.245Z", "path": "domain/gg/knowledge/qt5-framework.knowledge.md"}}, {"id": "cpp-expert-thinking", "source": "project", "protocol": "thought", "name": "Cpp Expert Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/gg/thought/cpp-expert-thinking.thought.md", "metadata": {"createdAt": "2025-07-25T00:47:18.246Z", "updatedAt": "2025-07-25T00:47:18.246Z", "scannedAt": "2025-07-25T00:47:18.246Z", "path": "domain/gg/thought/cpp-expert-thinking.thought.md"}}, {"id": "qt-development-workflow", "source": "project", "protocol": "execution", "name": "Qt Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/huangshang/execution/qt-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-25T00:47:18.248Z", "updatedAt": "2025-07-25T00:47:18.248Z", "scannedAt": "2025-07-25T00:47:18.248Z", "path": "domain/huangshang/execution/qt-development-workflow.execution.md"}}, {"id": "ui-design-principles", "source": "project", "protocol": "execution", "name": "Ui Design Principles 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/huangshang/execution/ui-design-principles.execution.md", "metadata": {"createdAt": "2025-07-25T00:47:18.249Z", "updatedAt": "2025-07-25T00:47:18.249Z", "scannedAt": "2025-07-25T00:47:18.249Z", "path": "domain/huangshang/execution/ui-design-principles.execution.md"}}, {"id": "huangshang", "source": "project", "protocol": "role", "name": "Huangshang 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/huangshang/huangshang.role.md", "metadata": {"createdAt": "2025-07-25T00:47:18.250Z", "updatedAt": "2025-07-25T00:47:18.250Z", "scannedAt": "2025-07-25T00:47:18.250Z", "path": "domain/huangshang/huangshang.role.md"}}, {"id": "qt5-expertise", "source": "project", "protocol": "knowledge", "name": "Qt5 Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/huangshang/knowledge/qt5-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-25T00:47:18.251Z", "updatedAt": "2025-07-25T00:47:18.251Z", "scannedAt": "2025-07-25T00:47:18.251Z", "path": "domain/huangshang/knowledge/qt5-expertise.knowledge.md"}}, {"id": "ui-development-best-practices", "source": "project", "protocol": "knowledge", "name": "Ui Development Best Practices 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/huangshang/knowledge/ui-development-best-practices.knowledge.md", "metadata": {"createdAt": "2025-07-25T00:47:18.252Z", "updatedAt": "2025-07-25T00:47:18.252Z", "scannedAt": "2025-07-25T00:47:18.252Z", "path": "domain/huangshang/knowledge/ui-development-best-practices.knowledge.md"}}, {"id": "qt-ui-thinking", "source": "project", "protocol": "thought", "name": "Qt Ui Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/huangshang/thought/qt-ui-thinking.thought.md", "metadata": {"createdAt": "2025-07-25T00:47:18.253Z", "updatedAt": "2025-07-25T00:47:18.253Z", "scannedAt": "2025-07-25T00:47:18.253Z", "path": "domain/huangshang/thought/qt-ui-thinking.thought.md"}}, {"id": "tiger-driver-principle", "source": "project", "protocol": "execution", "name": "Tiger Driver Principle 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/tiger-driver/execution/tiger-driver-principle.execution.md", "metadata": {"createdAt": "2025-07-25T00:47:18.254Z", "updatedAt": "2025-07-25T00:47:18.254Z", "scannedAt": "2025-07-25T00:47:18.254Z", "path": "domain/tiger-driver/execution/tiger-driver-principle.execution.md"}}, {"id": "tiger-driver-knowledge", "source": "project", "protocol": "knowledge", "name": "Tiger Driver Knowledge 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/tiger-driver/knowledge/tiger-driver-knowledge.knowledge.md", "metadata": {"createdAt": "2025-07-25T00:47:18.262Z", "updatedAt": "2025-07-25T00:47:18.262Z", "scannedAt": "2025-07-25T00:47:18.262Z", "path": "domain/tiger-driver/knowledge/tiger-driver-knowledge.knowledge.md"}}, {"id": "tiger-driver-personality", "source": "project", "protocol": "thought", "name": "Tiger Driver Personality 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/tiger-driver/thought/tiger-driver-personality.thought.md", "metadata": {"createdAt": "2025-07-25T00:47:18.263Z", "updatedAt": "2025-07-25T00:47:18.263Z", "scannedAt": "2025-07-25T00:47:18.263Z", "path": "domain/tiger-driver/thought/tiger-driver-personality.thought.md"}}, {"id": "tiger-driver", "source": "project", "protocol": "role", "name": "Tiger Driver 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/tiger-driver/tiger-driver.role.md", "metadata": {"createdAt": "2025-07-25T00:47:18.263Z", "updatedAt": "2025-07-25T00:47:18.263Z", "scannedAt": "2025-07-25T00:47:18.263Z", "path": "domain/tiger-driver/tiger-driver.role.md"}}, {"id": "graphics-workflow", "source": "project", "protocol": "execution", "name": "Graphics Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/tuxingdashi/execution/graphics-workflow.execution.md", "metadata": {"createdAt": "2025-07-25T00:47:18.264Z", "updatedAt": "2025-07-25T00:47:18.264Z", "scannedAt": "2025-07-25T00:47:18.264Z", "path": "role/tuxingdashi/execution/graphics-workflow.execution.md"}}, {"id": "graphics-thinking", "source": "project", "protocol": "thought", "name": "Graphics Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/tuxingdashi/thought/graphics-thinking.thought.md", "metadata": {"createdAt": "2025-07-25T00:47:18.265Z", "updatedAt": "2025-07-25T00:47:18.265Z", "scannedAt": "2025-07-25T00:47:18.265Z", "path": "role/tuxingdashi/thought/graphics-thinking.thought.md"}}, {"id": "tux<PERSON><PERSON><PERSON>", "source": "project", "protocol": "role", "name": "Tuxingdashi 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/tuxingdashi/tuxingdashi.role.md", "metadata": {"createdAt": "2025-07-25T00:47:18.265Z", "updatedAt": "2025-07-25T00:47:18.265Z", "scannedAt": "2025-07-25T00:47:18.265Z", "path": "role/tuxingdashi/tuxingdashi.role.md"}}], "stats": {"totalResources": 20, "byProtocol": {"execution": 6, "role": 4, "knowledge": 6, "thought": 4}, "bySource": {"project": 20}}}