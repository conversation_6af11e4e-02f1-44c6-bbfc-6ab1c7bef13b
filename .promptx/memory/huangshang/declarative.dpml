<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1752475415411_fzw8659d0" time="2025/07/14 14:43">
    <content>
      ColorM准备模式采样门分离问题修复：采样门位置由BCDGeometryController::adjustedMidAndHalfLines函数控制，但该函数缺少坐标转换逻辑。修复方案：在adjustedMidAndHalfLines函数中添加与扫查线相同的坐标转换逻辑 adjustedMidLine = midLine * bRxLNum / cRxLNum，确保采样门与扫查线使用统一的B图坐标系统，解决采样门仍在外部的问题。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1752485504029_gkve5nifa" time="2025/07/14 17:31">
    <content>
      ColorM准备模式扫查线与采样门分离问题已成功修复。根本原因是在支持任意线密度的Grape机型上，扫查线使用C_RX_LNUMStr参数进行坐标计算，而采样门在BCDGeometryController::adjustedMidAndHalfLines函数中缺少相应的坐标转换。修复方案是在该函数的isSupportAnyDensity()分支中添加坐标转换逻辑：midLine = midLine * bRxLNum / cRxLNum，确保两者使用统一的B图坐标系统。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1752486808793_1wmxc3eek" time="2025/07/14 17:53">
    <content>
      ColorM准备模式扫查线分离问题完善修复：1.使用qRound进行浮点数坐标转换提高精度：midLine = qRound((double)midLine * bRxLNum / cRxLNum)；2.完善边界检查逻辑，防止调整后坐标仍超出边界；3.添加双向边界保护，确保左右边界调整不会互相冲突；4.当边界冲突时使用区域中心点作为安全位置。这些改进解决了扫查线超出边界和采样门未完全贴合的问题。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1752487352968_qq210pzu3" time="2025/07/14 18:02">
    <content>
      ColorM准备模式问题调试日志添加完成：1.在BCDGeometryController中添加adjustedMidAndHalfLines、roiGeometry2SonoParameters、onResetRegionAndCoordTransform函数的详细日志；2.在ROIScanLineWidget的setupRoiScanLine函数中添加扫查线计算日志；3.在SharedGlyphsWidget的scanLineAndDepthScreenPos函数中添加坐标转换日志；4.所有日志都使用ColorM_Debug前缀便于过滤，记录了线密度参数、坐标转换过程、边界调整等关键信息。这些日志将帮助定位扫查线与采样门分离问题的根本原因。
    </content>
    <tags>#其他</tags>
  </item>
</memory>