# 陈述性记忆

## 高价值记忆（评分 ≥ 7）

- 2025/07/03 11:11 START
peripheral模块重构试点发现：hardwaremonitor模块架构基本正确，接口类IHardwareMonitorManager是纯虚接口，实现类HardWareMonitorManager在实现模块中。主要问题是接口模块中包含了ihardwaremonitormanager.cpp文件，这违反了接口-实现分离原则。该.cpp文件只包含构造函数和析构函数的空实现，应该被移除。 --tags peripheral refactor architecture interface implementation
--tags #其他 #评分:8 #有效期:长期
- END



- 2025/07/03 11:20 START
peripheral模块重构进展：已成功完成4个子模块的接口-实现分离重构：hardwaremonitor(0个.cpp)、motor(0个.cpp)、common(0个.cpp)、ecconnector(0个.cpp)。主要工作包括：1)删除接口模块中的.cpp文件；2)修改接口类为纯虚接口；3)将具体实现类迁移到实现模块；4)更新CMakeLists.txt配置。剩余模块：media(6个)、printer(8个)、bt(9个)、disk(12个)、systeminfo(22个)、bfiodevice(30个)、keyboard(30个)。 --tags peripheral refactor progress interface implementation separation
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/07 14:51 START
ARM平台宽景功能崩溃分析：主要表现为运行1秒后卡死，5秒后崩溃。关键发现包括：1)内存访问违例(SIGSEGV+SIGILL)；2)堆栈严重损坏；3)内存资源紧张(仅21MB可用)；4)图像处理库libusimageprocess.so中OptimizeNetWork和DSCNetWork崩溃；5)Qt容器构造失败。需要从内存管理、编译配置、第三方库兼容性等角度进行系统性分析。 --tags ARM宽景崩溃 内存问题 图像处理 调试分析
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/08 09:51 START
Grape设备电源按键20秒卡死问题分析：根本原因是BeamFormerGrape::wake()中固定的1秒FPGA等待+复杂硬件初始化导致总计13-29秒阻塞。关键代码：Util::usleep(1000000)+USB设备重连+探头识别+状态机锁定。解决方案：1)异步唤醒优化；2)FPGA等待改为状态检测；3)电源按键队列机制；4)添加用户反馈界面。StateFilterLocker在执行期间阻塞所有电源按键事件是直接原因。 --tags Grape电源管理 FPGA唤醒 20秒卡死 BeamFormerGrape 异步优化
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/11 10:56 START
Apple机型不支持AnyDensity功能，NewECO机型支持AnyDensity功能。这是分析ColorM准备模式扫查线问题的关键信息。 --tags AnyDensity Apple NewECO 机型配置
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/11 11:00 START
用户刚才在BCDGeometryController::isSupportAnyDensity()函数中添加了return false的调试代码，现在已经删除。需要重新分析NewECO机型ColorM准备模式扫查线分离问题的真正原因。 --tags 调试代码 BCDGeometryController NewECO 扫查线问题
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/14 13:34 START
ColorM准备模式扫查线分离问题修复：根本原因是NewECO机型支持AnyDensity时，ColorM模式使用B_RX_LNUM进行坐标变换但扫查线位置仍使用C图参数，导致坐标系统不匹配。修复方案：添加坐标转换逻辑 adjustedMScanLine = mScanLine * bRxLNum / cRxLNum，确保扫查线位置正确转换到B图坐标系统。同时添加了详细的调试日志用于问题诊断。 --tags ColorM扫查线 坐标转换 NewECO AnyDensity 调试修复
--tags #其他 #评分:8 #有效期:长期
- END