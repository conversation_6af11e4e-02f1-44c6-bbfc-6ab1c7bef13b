#ifndef HARDWAREMONITORMANAGER_H
#define HARDWAREMONITORMANAGER_H

#include "hardwaremonitorbusiness_global.h"
#include "ihardwaremonitormanager.h"
#include "hardwaremonitor.h"

class BatteryMonitor;
class VoltageMonitor;
class FpgaStatusMonitor;
class HardwareMonitor;
class ISystemInfo;
class FpgaStatusInfoWidget;
class TemperatureMonitor;
class BoardCombMonitor;

/**
 * @brief hardWareMonitorManager: 硬件监控管理器
 *
 * 用途：实现IHardwareMonitorManager接口，整合BatteryMonitor、VoltageMonitor和FpgaStatusMonitor功能，
 * 并提供创建UI组件的方法
 */
class USF_HARDWAREMONITOR_BUSINESS_EXPORT HardWareMonitorManager : public IHardwareMonitorManager
{
    Q_OBJECT
public:
    explicit HardWareMonitorManager(ISystemInfo* systemInfo, QObject* parent = nullptr);
    virtual ~HardWareMonitorManager();

    /**
     * @brief 启动监控
     * @return 成功返回true，否则返回false
     */
    virtual void start() override;

    /**
     * @brief 停止监控
     * @return 成功返回true，否则返回false
     */
    virtual void stop() override;

    virtual void setSonoParamters(ISonoParameters* sonoParamter);

    virtual int volatilevlue2CTNumber(float value);

    virtual bool checkCWVolatile(float safeVoltage, int times);

public slots:
    virtual void onNewHWInfo(unsigned char* info, int length);

signals:
    void newHWInfo(unsigned char*, int);

protected slots:
    virtual void onECGInfoChanged(unsigned char data);

private:
    /**
     * @brief 初始化所有信号槽连接
     */
    void initializeConnections();

private:
    HardWareMonitorDialog* m_HardWareMonitor{nullptr};
    BatteryMonitor* m_BatteryMonitor{nullptr};
    ISystemInfo* m_SystemInfo{nullptr};
    VoltageMonitor* m_VoltageMonitor{nullptr};
    FpgaStatusMonitor* m_FpgaStatusMonitor{nullptr};
    TemperatureMonitor* m_TemperatureMonitor{nullptr};
    BoardCombMonitor* m_BoardCombMonitor{nullptr};
    ISonoParameters* m_SonoParameter{nullptr};
};

#endif // HARDWAREMONITORMANAGER_H
