#include "voltagemonitor.h"
#include "batterypropertynames.h"
#include "isysteminfo.h"
#include "ibatteryinfo.h"
#include "hardwaredatadef.h"

VoltageMonitor::VoltageMonitor(ISystemInfo* systemInfo, QObject* parent)
    : QObject(parent)
{
    m_SystemInfo = systemInfo;
    m_voltageData = new VoltageData;
    m_SendFFTimer = new QTimer(this);
    m_GetDataTimer = new QTimer(this);
}

VoltageMonitor::~VoltageMonitor()
{
    stopPolling();
    if (m_voltageData != nullptr)
    {
        delete m_voltageData;
        m_voltageData = nullptr;
    }
}

void VoltageMonitor::initialize(int milliseconds)
{
    stopPolling();

    disconnect(m_SendFFTimer, nullptr, this, nullptr);
    disconnect(m_GetDataTimer, nullptr, this, nullptr);

    m_SendFFTimer->setInterval(milliseconds);
    m_GetDataTimer->setInterval(milliseconds);

    connect(m_SendFFTimer, &QTimer::timeout, this, &VoltageMonitor::onUpdateSendTimer);
    connect(m_GetDataTimer, &QTimer::timeout, this, &VoltageMonitor::onUpdateGetTimer);
    m_SendFFTimer->start();
    m_GetDataTimer->start();
}

void VoltageMonitor::stopPolling()
{
    m_GetDataTimer->stop();
    m_SendFFTimer->stop();
}

float VoltageMonitor::getCWVolatile()
{
    return m_voltageData->AVPP_T1V70;
}

void VoltageMonitor::onUpdateSendTimer()
{
    m_SystemInfo->batteryInfo()->sendFFCmd();
}

void VoltageMonitor::onUpdateGetTimer()
{
    QVariant data = m_SystemInfo->getADCInfo();
    voltageDataParse(data);

    emit VoltageDataUpdated(*m_voltageData);
}

void VoltageMonitor::voltageDataParse(QVariant data)
{
    // 获取16进制字符串数据
    QString hexString = data.toString();

    if (hexString.length() >= 60)
    {
        bool ok;

        // 1. VDD1V
        int data1 = hexString.midRef(0, 2).toInt(&ok, 16);
        int data0 = hexString.midRef(2, 2).toInt(&ok, 16);
        m_voltageData->VDD1V = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 1.0f;

        // 2. VDD1V2
        data1 = hexString.midRef(4, 2).toInt(&ok, 16);
        data0 = hexString.midRef(6, 2).toInt(&ok, 16);
        m_voltageData->VDD1V2 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 1.0f;

        // 3. VDD1V35
        data1 = hexString.midRef(8, 2).toInt(&ok, 16);
        data0 = hexString.midRef(10, 2).toInt(&ok, 16);
        m_voltageData->VDD1V35 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 1.0f;

        // 4. DVDDAUX_1V8
        data1 = hexString.midRef(12, 2).toInt(&ok, 16);
        data0 = hexString.midRef(14, 2).toInt(&ok, 16);
        m_voltageData->DVDDAUX_1V8 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 1.0f;

        // 5. DVDD_5V_T1V25
        data1 = hexString.midRef(16, 2).toInt(&ok, 16);
        data0 = hexString.midRef(18, 2).toInt(&ok, 16);
        m_voltageData->DVDD_5V_T1V25 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 4.0f;

        // 6. VDD3V3_T1V65
        data1 = hexString.midRef(20, 2).toInt(&ok, 16);
        data0 = hexString.midRef(22, 2).toInt(&ok, 16);
        m_voltageData->VDD3V3_T1V65 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 2.0f;

        // 7. DVDD_2V5
        data1 = hexString.midRef(24, 2).toInt(&ok, 16);
        data0 = hexString.midRef(26, 2).toInt(&ok, 16);
        m_voltageData->DVDD_2V5 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 1.0f;

        // 8. AVCC_2V3
        data1 = hexString.midRef(28, 2).toInt(&ok, 16);
        data0 = hexString.midRef(30, 2).toInt(&ok, 16);
        m_voltageData->AVCC_2V3 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 1.0f;

        // 9. AVCC_3V6_T1V8
        data1 = hexString.midRef(32, 2).toInt(&ok, 16);
        data0 = hexString.midRef(34, 2).toInt(&ok, 16);
        m_voltageData->AVCC_3V6_T1V8 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 2.0f;

        // 10. AVPP_T1V70
        data1 = hexString.midRef(36, 2).toInt(&ok, 16);
        data0 = hexString.midRef(38, 2).toInt(&ok, 16);
        m_voltageData->AVPP_T1V70 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 41.176f;

        // 11. AVNN_T2V04
        data1 = hexString.midRef(40, 2).toInt(&ok, 16);
        data0 = hexString.midRef(42, 2).toInt(&ok, 16);
        m_voltageData->AVNN_T2V04 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * (-34.3f);

        // 12. DVDD_12V_T1V625
        data1 = hexString.midRef(44, 2).toInt(&ok, 16);
        data0 = hexString.midRef(46, 2).toInt(&ok, 16);
        m_voltageData->DVDD_12V_T1V625 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 7.383f;

        // 13. DVDD_N12V_T1V88
        data1 = hexString.midRef(48, 2).toInt(&ok, 16);
        data0 = hexString.midRef(50, 2).toInt(&ok, 16);
        m_voltageData->DVDD_N12V_T1V88 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * (-6.383f);

        // 14. AVCC_5V6_T1V4
        data1 = hexString.midRef(52, 2).toInt(&ok, 16);
        data0 = hexString.midRef(54, 2).toInt(&ok, 16);
        m_voltageData->AVCC_5V6_T1V4 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * 4.0f;

        // 15. AVCC_N5V6_T1V867
        data1 = hexString.midRef(56, 2).toInt(&ok, 16);
        data0 = hexString.midRef(58, 2).toInt(&ok, 16);
        m_voltageData->AVCC_N5V6_T1V867 = ((data1 * 256 + data0) / 4096.0f) * 3.3f * (-3.0f);
    }
}
