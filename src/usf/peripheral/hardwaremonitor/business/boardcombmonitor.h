#ifndef BOARDCOMBMONITOR_H
#define BOARDCOMBMONITOR_H

#include "hardwaremonitorbusiness_global.h"
#include <QObject>
#include "infostruct.h"

class ISonoParameters;

struct BoardCombStatus
{
    FpgaFirmWareType fpgaFirmWareType; // 固件类型
    MainBoardType mainBoardType;       // 主板类型
    ProbeBoardType probeBoardType;     // 探头板类型
    BoardCombType boardCombType;       // 板卡组合类型
};

class USF_HARDWAREMONITOR_BUSINESS_EXPORT BoardCombMonitor : public QObject
{
    Q_OBJECT
public:
    explicit BoardCombMonitor(QObject* parent = nullptr);
    ~BoardCombMonitor();

    bool isSupportVirtual();
    bool isHardwareMatch();
    bool isSoftWareMatch();
    void setSonoParamters(ISonoParameters* sonoParamter);
    void emitDate();

private:
    void parseFpgaPacket(unsigned char* info, int length);

    QStringList generateBoardInfoList();
    QString mainBoardTypeString(MainBoardType type);
    QString probeBoardTypeString(ProbeBoardType status);
    QString boardCombTypeString(BoardCombType type);
    QString probeBoardStatusString(ProbeBoardType status);
    QString fpgaFirmWareTypeString(FpgaFirmWareType type);
    QString cpuNameString();
    QString softWareChannel();
    QString matchResult();

signals:
    void boardCombDataUpdated(const QStringList& boardInfoList);

public slots:
    void onFpgaInfoPacketReceived(unsigned char* info, int length);

private:
    ISonoParameters* m_SonoParam{nullptr};
    BoardCombData m_BoardCombData{};
    BoardCombStatus m_BoardCombStatus{};
    bool m_HasShownWarning{false};
};

#endif
