#ifndef VOLTAGEMONITOR_H
#define VOLTAGEMONITOR_H

#include "hardwaremonitorbusiness_global.h"
#include <QObject>
#include <QVariant>
#include <QTimer>

class ISystemInfo;
struct VoltageData;

/**
 * @brief VoltageMonitor: 电压监控器
 *
 * 用途：负责监控系统电压状态并提供电压信息数据
 */
class USF_HARDWAREMONITOR_BUSINESS_EXPORT VoltageMonitor : public QObject
{
    Q_OBJECT

public:
    explicit VoltageMonitor(ISystemInfo* systemInfo, QObject* parent = nullptr);
    ~VoltageMonitor();

    void initialize(int milliseconds);

    void stopPolling();

    float getCWVolatile();

signals:
    void VoltageDataUpdated(const VoltageData adcInfo);

private slots:
    void onUpdateSendTimer();
    void onUpdateGetTimer();

private:
    void voltageDataParse(QVariant data);

    ISystemInfo* m_SystemInfo{nullptr};
    QTimer* m_SendFFTimer{nullptr};
    QTimer* m_GetDataTimer{nullptr};
    VoltageData* m_voltageData{nullptr};
};

#endif // VOLTAGEMONITOR_H
