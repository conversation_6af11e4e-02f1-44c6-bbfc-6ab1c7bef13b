#include "boardcombmonitor.h"
#include "hardwaredatadef.h"
#include "sonoparameters.h"
#include "parameter.h"
#include "bfpnames.h"
#include "messageboxframe.h"
#include <QProcess>
#include <QFile>
#include <QTextStream>

BoardCombMonitor::BoardCombMonitor(QObject* parent)
    : QObject(parent)
{
}

BoardCombMonitor::~BoardCombMonitor()
{
}

bool BoardCombMonitor::isSupportVirtual()
{
    if (m_BoardCombStatus.probeBoardType == ProbeBoardType::NO_PROBE_BOARD)
    {
        return true;
    }
    else
    {
        if (isHardwareMatch())
        {
            return true;
        }
    }
    return false;
}

bool BoardCombMonitor::isHardwareMatch()
{
    // 根据板卡组合类型判断应该匹配的主板类型
    switch (m_BoardCombStatus.boardCombType)
    {
    case BoardCombType::CH16_3SOCKET_80Elem:
        return m_BoardCombStatus.mainBoardType == MainBoardType::CH16_BOARD;

    case BoardCombType::CH32_3SOCKET_80Elem:
    case BoardCombType::CH32_2SOCKET_80Elem_1SOCKET_128Elem:
        return m_BoardCombStatus.mainBoardType == MainBoardType::CH32_BOARD;

    case BoardCombType::CH64_3SOCKET_128Elem_156PIN:
    case BoardCombType::CH64_3SOCKET_128Elem_260PIN:
    case BoardCombType::CH64_3SOCKET_128Elem_160PIN:
        return m_BoardCombStatus.mainBoardType == MainBoardType::CH64_BOARD;

    default:
        return false;
    }
}

bool BoardCombMonitor::isSoftWareMatch()
{
    if (nullptr == m_SonoParam)
    {
        return false;
    }

    switch (m_BoardCombStatus.boardCombType)
    {
    case BoardCombType::CH16_3SOCKET_80Elem:
        return m_SonoParam->pIV(BFPNames::Channel16EnableStr) == 1;

    case BoardCombType::CH32_3SOCKET_80Elem:
    case BoardCombType::CH32_2SOCKET_80Elem_1SOCKET_128Elem:
        return m_SonoParam->pIV(BFPNames::Channel32EnableStr) == 1;

    case BoardCombType::CH64_3SOCKET_128Elem_156PIN:
    case BoardCombType::CH64_3SOCKET_128Elem_260PIN:
    case BoardCombType::CH64_3SOCKET_128Elem_160PIN:
        return m_SonoParam->pIV(BFPNames::Channel64EnableStr) == 1;

    default:
        return false;
    }
}

void BoardCombMonitor::setSonoParamters(ISonoParameters* sonoParamter)
{
    m_SonoParam = sonoParamter;
}

void BoardCombMonitor::emitDate()
{
    emit boardCombDataUpdated(generateBoardInfoList());
}

void BoardCombMonitor::onFpgaInfoPacketReceived(unsigned char* info, int length)
{
    parseFpgaPacket(info, length);
    if (!m_HasShownWarning)
    {
        m_HasShownWarning = true;
        if (!isSoftWareMatch())
        {
            MessageBoxFrame::warningNonModal("通道数与探头转接板不符");
        }
    }
    emitDate();
}

void BoardCombMonitor::parseFpgaPacket(unsigned char* info, int length)
{
    Q_UNUSED(length)

    if (nullptr == info)
    {
        return;
    }
    // 获取第84、85字节的数据并直接赋值给位域结构体
    unsigned short boardCombData = static_cast<unsigned short>((info[85] << 8) | info[84]);
    unsigned short fpgaFirmWareData = static_cast<unsigned short>(info[88]);

    memcpy(&m_BoardCombData, &boardCombData, sizeof(boardCombData));

    m_BoardCombStatus.mainBoardType = static_cast<MainBoardType>(m_BoardCombData.PCBID);
    m_BoardCombStatus.probeBoardType = static_cast<ProbeBoardType>(m_BoardCombData.PRB_NUM);
    m_BoardCombStatus.boardCombType =
        static_cast<BoardCombType>((m_BoardCombData.L_H_PRB << 2) | m_BoardCombData.PRB_CH_SET);
    m_BoardCombStatus.fpgaFirmWareType = static_cast<FpgaFirmWareType>(fpgaFirmWareData);
    m_SonoParam->setPV(BFPNames::ChannelIndexStr, (int)m_BoardCombStatus.boardCombType);
}

QStringList BoardCombMonitor::generateBoardInfoList()
{
    QStringList infoList;

    infoList.append(mainBoardTypeString(m_BoardCombStatus.mainBoardType));
    infoList.append(probeBoardTypeString(m_BoardCombStatus.probeBoardType));
    infoList.append(boardCombTypeString(m_BoardCombStatus.boardCombType));
    infoList.append(probeBoardStatusString(m_BoardCombStatus.probeBoardType));
    infoList.append(fpgaFirmWareTypeString(m_BoardCombStatus.fpgaFirmWareType));
    infoList.append(cpuNameString());
    infoList.append(softWareChannel());
    infoList.append(matchResult());

    return infoList;
}

QString BoardCombMonitor::mainBoardTypeString(MainBoardType type)
{
    switch (type)
    {
    case MainBoardType::CH16_BOARD:
        return "16CH 主板";
    case MainBoardType::CH32_BOARD:
        return "32CH 主板";
    case MainBoardType::CH64_BOARD:
        return "64CH 主板";
    default:
        return "未知主板类型";
    }
}

QString BoardCombMonitor::probeBoardTypeString(ProbeBoardType status)
{
    switch (status)
    {
    case ProbeBoardType::NO_PROBE_BOARD:
        return "无探头板连接";
    case ProbeBoardType::ONE_PROBE_SOCKET:
        return "1插座";
    case ProbeBoardType::TWO_PROBE_SOCKET:
        return "2插座";
    case ProbeBoardType::THREE_PROBE_SOCKET:
        return "3插座";
    default:
        return "未知连接状态";
    }
}

QString BoardCombMonitor::boardCombTypeString(BoardCombType type)
{
    switch (type)
    {
    case BoardCombType::CH16_3SOCKET_80Elem:
        return "16CH+3插\\80基元";
    case BoardCombType::CH32_3SOCKET_80Elem:
        return "32CH+3插\\80基元";
    case BoardCombType::CH32_2SOCKET_80Elem_1SOCKET_128Elem:
        return "32CH+2插\\80基元+1插\\128基元";
    case BoardCombType::CH64_3SOCKET_128Elem_156PIN:
        return "64CH+3插\\128基元\\156PIN";
    case BoardCombType::CH64_3SOCKET_128Elem_260PIN:
        return "64CH+3插\\128基元\\260PIN";
    case BoardCombType::CH64_3SOCKET_128Elem_160PIN:
        return "64CH+3插\\128基元\\160PIN";
    default:
        return "未知板卡组合";
    }
}

QString BoardCombMonitor::probeBoardStatusString(ProbeBoardType status)
{
    switch (status)
    {
    case ProbeBoardType::NO_PROBE_BOARD:
        return "探头板未在位";
    case ProbeBoardType::ONE_PROBE_SOCKET:
    case ProbeBoardType::TWO_PROBE_SOCKET:
    case ProbeBoardType::THREE_PROBE_SOCKET:
        return "探头板在位";
    default:
        return "未知连接状态";
    }
}

QString BoardCombMonitor::fpgaFirmWareTypeString(FpgaFirmWareType type)
{
    switch (type)
    {
    case FpgaFirmWareType::ECO:
        return "ECO";
    case FpgaFirmWareType::EBIT:
        return "EBIT";
    default:
        return "未知固件信息";
    }
}

QString BoardCombMonitor::cpuNameString()
{
#ifdef Q_OS_WIN
    QString cmd = "wmic cpu get name";
    QProcess p;
    p.start(cmd);
    p.waitForFinished();
    QString result = QString::fromLocal8Bit(p.readAllStandardOutput());
    result = result.trimmed();
    return result;
#else
    QFile file("/sys/firmware/devicetree/base/compatible");
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        return QString();
    }

    QTextStream in(&file);
    QString result = in.readAll().trimmed();
    file.close();
    if (result.contains("rk3588", Qt::CaseInsensitive))
        return "RK3588";
    if (result.contains("rk3568", Qt::CaseInsensitive))
        return "RK3568";
    return result;
#endif
}

QString BoardCombMonitor::softWareChannel()
{
    if (nullptr != m_SonoParam)
    {
        if (m_SonoParam->pIV(BFPNames::Channel16EnableStr) == 1)
        {
            return "16";
        }
        else if (m_SonoParam->pIV(BFPNames::Channel32EnableStr) == 1)
        {
            return "32";
        }
        else if (m_SonoParam->pIV(BFPNames::Channel64EnableStr) == 1)
        {
            return "64";
        }
        else if (m_SonoParam->pIV(BFPNames::Channel128EnableStr) == 1)
        {
            return "128";
        }
    }
    return "未知通道数";
}

QString BoardCombMonitor::matchResult()
{
    if (isSoftWareMatch())
        return "通道数与探头转接板匹配";
    return "通道数与探头转接板不符";
}
