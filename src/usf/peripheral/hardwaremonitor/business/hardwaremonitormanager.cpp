#include "hardwaremonitormanager.h"
#include "batterymonitor.h"
#include "voltagemonitor.h"
#include "fpgastatusmonitor.h"
#include "temperaturemonitor.h"
#include "hardwaremonitor.h"
#include "batteryinfowidget.h"
#include "voltageinfowidget.h"
#include "fpgastatusinfowidget.h"
#include "temperatureinfowidget.h"
#include "hardwaredatadef.h"
#include "ecginfowidget.h"
#include "boardcombmonitor.h"
#include "boardcombinfowidget.h"
#include <QtMath>
#include "util.h"

HardWareMonitorManager::HardWareMonitorManager(ISystemInfo* systemInfo, QObject* parent)
    : IHardwareMonitorManager(parent)
    , m_SystemInfo(systemInfo)
    , m_VoltageMonitor(new VoltageMonitor(systemInfo, this))
{
    m_HardWareMonitor = new HardWareMonitorDialog();

    m_SystemInfo = systemInfo;
    m_BoardCombMonitor = new BoardCombMonitor(this);
    connect(this, &HardWareMonitorManager::newHWInfo, m_BoardCombMonitor, &BoardCombMonitor::onFpgaInfoPacketReceived);
}

HardWareMonitorManager::~HardWareMonitorManager()
{
    if (m_HardWareMonitor != nullptr)
    {
        delete m_HardWareMonitor;
        m_HardWareMonitor = nullptr;
    }
}

void HardWareMonitorManager::start()
{
    m_BatteryMonitor = new BatteryMonitor(m_SystemInfo, this);
    m_FpgaStatusMonitor = new FpgaStatusMonitor(m_SonoParameter, this);
    m_TemperatureMonitor = new TemperatureMonitor(m_SonoParameter, this);

    initializeConnections();

    m_BatteryMonitor->initialize();
    m_VoltageMonitor->initialize(100);
    m_FpgaStatusMonitor->initialize();
    m_TemperatureMonitor->initialize();

    m_BoardCombMonitor->emitDate();
    m_HardWareMonitor->show();
}

void HardWareMonitorManager::stop()
{
    m_HardWareMonitor->hide();
}

void HardWareMonitorManager::initializeConnections()
{

    // 电池信息连接
    BatteryInfoWidget* batteryWidget = m_HardWareMonitor->batteryInfoWidget();
    connect(m_BatteryMonitor, &BatteryMonitor::batteryDataUpdated, batteryWidget,
            &BatteryInfoWidget::onBatteryInfoUpdated);
    connect(m_BatteryMonitor, &BatteryMonitor::batteryConnectionChanged, batteryWidget,
            &BatteryInfoWidget::onBatteryStateChanged);
    connect(m_BatteryMonitor, &BatteryMonitor::adapterConnectionChanged, batteryWidget,
            &BatteryInfoWidget::onAdapterStateChanged);

    // 电压信息连接
    connect(m_VoltageMonitor, &VoltageMonitor::VoltageDataUpdated, m_HardWareMonitor->voltageInfoWidget(),
            &VoltageInfoWidget::onVoltageInfoUpdated);

    // FPGA状态信息连接
    connect(m_HardWareMonitor->fpgaStatusInfoWidget(), &FpgaStatusInfoWidget::sendParamsRequest, m_FpgaStatusMonitor,
            &FpgaStatusMonitor::onSyncSignalSend);

    // 温度监控连接
    TemperatureInfoWidget* tempetaturewidget = m_HardWareMonitor->temperatureInfoWidget();
    connect(this, &HardWareMonitorManager::newHWInfo, m_TemperatureMonitor,
            &TemperatureMonitor::onFpgaInfoPacketReceived);
    connect(tempetaturewidget, &TemperatureInfoWidget::sendConfigRequest, m_TemperatureMonitor,
            &TemperatureMonitor::onConfigRequest);
    connect(m_TemperatureMonitor, &TemperatureMonitor::CPUTempDataUpdate, tempetaturewidget,
            &TemperatureInfoWidget::onCPUTempDataUpdated);
    connect(m_TemperatureMonitor, &TemperatureMonitor::FPGATempDataUpdated, tempetaturewidget,
            &TemperatureInfoWidget::onTemperatureDataUpdated);
    connect(m_TemperatureMonitor, &TemperatureMonitor::pushButtonClicked, tempetaturewidget,
            &TemperatureInfoWidget::onButtonClicked);

    // 组板信息连接
    BoardCombInfoWidget* boardCombInfoWidget = m_HardWareMonitor->boardCombInfoWidget();
    connect(m_BoardCombMonitor, &BoardCombMonitor::boardCombDataUpdated, boardCombInfoWidget,
            &BoardCombInfoWidget::onBoardCombDateUpdated);
}

void HardWareMonitorManager::setSonoParamters(ISonoParameters* sonoParamter)
{
    m_SonoParameter = sonoParamter;
    m_BoardCombMonitor->setSonoParamters(sonoParamter);
}

int HardWareMonitorManager::volatilevlue2CTNumber(float value)
{
    return 255 - ((value - 0.4) * 255 / 69.6);
}

bool HardWareMonitorManager::checkCWVolatile(float safeVoltage, int times)
{
    m_VoltageMonitor->initialize(100);
    for (int i = 0; i < times; i++)
    {
        if (qAbs(m_VoltageMonitor->getCWVolatile() - safeVoltage) < 0.7)
        {
            m_VoltageMonitor->stopPolling();
            return true;
        }
        Util::artisticSleep(100);
    }

    m_VoltageMonitor->stopPolling();
    return false;
}

void HardWareMonitorManager::onNewHWInfo(unsigned char* info, int length)
{
    emit newHWInfo(info, length);
}

void HardWareMonitorManager::onECGInfoChanged(unsigned char data)
{

    if (m_HardWareMonitor == nullptr)
        return;
    if (m_HardWareMonitor->ECGGInfoWidget())
    {
        m_HardWareMonitor->ECGGInfoWidget()->onECGDataReceived(data);
    }
}
