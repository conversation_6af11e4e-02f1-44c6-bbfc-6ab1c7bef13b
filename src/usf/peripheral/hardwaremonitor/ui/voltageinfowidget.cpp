#include "voltageinfowidget.h"
#include "ui_voltageinfowidget.h"
#include "hardwaredatadef.h"

#include <QShowEvent>
#include <QHideEvent>

VoltageInfoWidget::VoltageInfoWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::VoltageInfoWidget)
{
    ui->setupUi(this);
}

VoltageInfoWidget::~VoltageInfoWidget()
{
    delete ui;
}

void VoltageInfoWidget::onVoltageInfoUpdated(VoltageData data)
{
    ui->labelVDD1V->setText(QString(QString::number(data.VDD1V, 'f', 3) + " V"));
    ui->labelVDD1V2->setText(QString(QString::number(data.VDD1V2, 'f', 3) + " V"));
    ui->labelVDD1V35->setText(QString(QString::number(data.VDD1V35, 'f', 3) + " V"));
    ui->labelDVDDAUX_1V8->setText(QString(QString::number(data.DVDDAUX_1V8, 'f', 3) + " V"));
    ui->labelDVDD_5V_T1V25->setText(QString(QString::number(data.DVDD_5V_T1V25, 'f', 3) + " V"));
    ui->labelVDD3V3_T1V65->setText(QString(QString::number(data.VDD3V3_T1V65, 'f', 3) + " V"));
    ui->labelDVDD_2V5->setText(QString(QString::number(data.DVDD_2V5, 'f', 3) + " V"));
    ui->labelAVCC_2V3->setText(QString(QString::number(data.AVCC_2V3, 'f', 3) + " V"));
    ui->labelAVCC_3V6_T1V8->setText(QString(QString::number(data.AVCC_3V6_T1V8, 'f', 3) + " V"));
    ui->labelAVPP_T1V70->setText(QString(QString::number(data.AVPP_T1V70, 'f', 3) + " V"));
    ui->labelAVNN_T2V04->setText(QString(QString::number(data.AVNN_T2V04, 'f', 3) + " V"));
    ui->labelDVDD_12V_T1V625->setText(QString(QString::number(data.DVDD_12V_T1V625, 'f', 3) + " V"));
    ui->labelDVDD_N12V_T1V88->setText(QString(QString::number(data.DVDD_N12V_T1V88, 'f', 3) + " V"));
    ui->labelAVCC_5V6_T1V4->setText(QString(QString::number(data.AVCC_5V6_T1V4, 'f', 3) + " V"));
    ui->labelAVCC_N5V6_T1V867->setText(QString(QString::number(data.AVCC_N5V6_T1V867, 'f', 3) + " V"));
}

void VoltageInfoWidget::retranslateUi()
{
}
