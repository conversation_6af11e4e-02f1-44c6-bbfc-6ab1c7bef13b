#ifndef VOLTAGEINFOWIDGET_H
#define VOLTAGEINFOWIDGET_H

#include <QWidget>
#include <QTimer>
#include "basewidget.h"
#include "hardwaremonitor_global.h"
struct VoltageData;

namespace Ui
{
class VoltageInfoWidget;
}

/**
 * @brief 电压信息显示Widget
 */
class USF_PER_HARDWAREMONITOR_UI_EXPORT VoltageInfoWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit VoltageInfoWidget(QWidget* parent = nullptr);
    ~VoltageInfoWidget();

public slots:
    /**
     * @brief 处理电压信息更新信号
     * @param VoltageData 更新的电压信息
     */
    void onVoltageInfoUpdated(VoltageData data);

protected:
    void retranslateUi();

private:
    Ui::VoltageInfoWidget* ui;
};

#endif // VOLTAGEINFOWIDGET_H
