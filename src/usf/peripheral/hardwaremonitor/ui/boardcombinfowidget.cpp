#include "boardcombinfowidget.h"
#include "ui_boardcombinfowidget.h"
#include <QProcess>
#include <QFile>

BoardCombInfoWidget::BoardCombInfoWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::BoardCombInfoWidget)
{
    ui->setupUi(this);
}

BoardCombInfoWidget::~BoardCombInfoWidget()
{
    delete ui;
}

void BoardCombInfoWidget::onBoardCombDateUpdated(const QStringList& infoList)
{
    if (infoList.length() == 8)
    {
        ui->labelMainBoardInfo->setText(infoList.at(0));
        ui->labelProbeSocketNum->setText(infoList.at(1));
        ui->labelProbeBoardInfo->setText(infoList.at(2));
        ui->labelIsProbePulggedIn->setText(infoList.at(3));
        ui->labelFpgaFirmWare->setText(infoList.at(4));
        ui->labelCpuName->setText(infoList.at(5));
        ui->labelSoftWareChannel->setText(infoList.at(6));
        ui->labelMatchResult->setText(infoList.at(7));
    }
    ui->labelCpuArchitecture->setText(QSysInfo::currentCpuArchitecture());
    ui->labelKernelType->setText(QSysInfo::kernelType());
    ui->labelKernelVersion->setText(QSysInfo::kernelVersion());
    ui->labelOperateSystem->setText(QSysInfo::prettyProductName());
    ui->labelProductType->setText(QSysInfo::productType());
    ui->labelProductVersion->setText(QSysInfo::productVersion());
    ui->labelMachineHostName->setText(QSysInfo::machineHostName());
}

void BoardCombInfoWidget::retranslateUi()
{
}
