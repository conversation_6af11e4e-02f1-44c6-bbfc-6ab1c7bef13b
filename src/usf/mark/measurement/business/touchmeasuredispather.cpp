#include "touchmeasuredispather.h"
#include "abstractstate.h"
#include "applicationinfo.h"
#include "baseruler.h"
#include "calcmeasurement.h"
#include "inputmeasureprocesscoordinator.h"
#include "istatemanager.h"
#include "measmeasurement.h"
#include "measureglyphs.h"
#include "measureglyphscontrol.h"
#include "measurementdef.h"
#include "measurementitemproperty.h"
#include "measurestaterecorder.h"
#include "overlay.h"
#include "resource.h"
#include "screenmeasresult.h"
#include "screenmeasurecontroller.h"
#include "setting.h"
#include "stateeventnames.h"
#include "stepmeasureprocesscoordinator.h"
#include "studymeasurement.h"
#include "touchmeasureprocesscoordinator.h"
#include "glyphscontrolmanager.h"
#include "basechildmeasurement.h"

TouchMeasureDispather::TouchMeasureDispather()
    : m_StateManager(NULL)
{
    connect(this, SIGNAL(runNext(IMeasurement*)), this, SLOT(runNextMeasure(IMeasurement*)),
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
}

TouchMeasureDispather::~TouchMeasureDispather()
{
}

bool TouchMeasureDispather::run(IMeasurement* measure, bool needInteraction)
{
    if (measure->type() >= 3 || measure->type() < 0)
    {
        return false;
    }

    m_CallCalcMeasurement = nullptr;

    runMeasure(measure, needInteraction);

    return true;
}

void TouchMeasureDispather::stop()
{
    if (!m_MeasureCoordinator.isNull())
    {
        m_MeasureCoordinator->setFinished(false);
    }

    setIsMeasuring(false);
    m_Measure = nullptr;
}

void TouchMeasureDispather::reset()
{
    if (!m_MeasureCoordinator.isNull())
    {
        m_MeasureCoordinator->setFinished(false);
        m_MeasureCoordinator->stopConnection();
    }
    m_MeasureCoordinator.reset();
    setIsMeasuring(false);
    m_Measure = nullptr;
}

void TouchMeasureDispather::resetInEasyview()
{
    if (m_MeasureCoordinator != NULL)
    {
        BaseRuler* ruler = qobject_cast<BaseRuler*>(m_MeasureCoordinator->ruler());
        if (ruler != nullptr)
        {
            ruler->stop();
        }
    }
    m_MeasureCoordinator.reset();
    setIsMeasuring(false);
    m_Measure = nullptr;
}

void TouchMeasureDispather::clear()
{
    bool isAutoAlg = false;
    bool needRunAgain = false;
    bool isFreeze = false;
    // clear and clear agin will show lines.
    IMeasurement* measure = currentMeasurement();
    IRuler* ruler = NULL;
    if (measure != nullptr)
    {
        MeasMeasurement* meas = dynamic_cast<MeasMeasurement*>(measure);

        if (meas != NULL)
            ruler = dynamic_cast<MeasMeasurement*>(measure)->currentRuler();
        if (ruler != NULL)
        {
            isAutoAlg = ruler->isAutoAlgrithm();
            needRunAgain = ruler->getIsClearRunAgainMeasure();
            isFreeze = ruler->context()->isFreeze();
        }
    }

    if (!m_MeasureCoordinator.isNull())
    {
        if (m_MeasureCoordinator->glyphsControl() != NULL)
        {
            // when auto algorithm measuremnt clear element. show real cursor and stop measuremnt.
            // cant do next part func for run current measurement.
            if (isAutoAlg && m_IsMeasuring)
                stopCurrentMeasure();
        }
        else
            m_MeasureCoordinator->stopConnection();
    }

    m_MeasureCoordinator.reset();
    setIsMeasuring(false);
    // update measure one times
    measure = currentMeasurement();

    //新的测量交互下不支持clear后继续测量,但是快速测量下需要这个行为
    //开启rtimt,按clear此时图元会被清除但是rtimt仍要保持运行
    //所以新增m_ClearRunAgain属性,即便被clear也能保持运行
    if (measure != nullptr && measure->step() != Measurement::Int_Invalid) // 当前测量不为空且已经初始化过
    {
        if (measure->packageRegion() == Measurement::Region_Quick)
        {
            if (measure->step() != Measurement::Step_Completed && !isAutoAlg)
            {
                IMeasurement* tmp = NULL;
                if (m_CallCalcMeasurement != NULL && m_CallCalcMeasurement->containMeasurement(measure))
                {
                    tmp = m_CallCalcMeasurement;
                }
                GlyphsCtl::ControlType controlType = (GlyphsCtl::ControlType)ruler->glyphsControlType();
                if (controlType != GlyphsCtl::NoneType)
                {
                    MeasureGlyphsControl* glyphsControl =
                        GlyphsControlManager::instance().getChildGlyphsControl<MeasureGlyphsControl>(controlType);
                    glyphsControl->setCursorToCenter(measure->imageType());
                }
                run(measure);
                if (tmp != NULL)
                    m_CallCalcMeasurement = tmp;
            }
        }
        // needRunAgain属性只有在测量状态下有效!
        else if (needRunAgain && measure->step() != Measurement::Step_Completed && !isFreeze)
        {
            if (m_StateManager->currentState()->name() == StateEventNames::MeasurementState ||
                (ruler != nullptr &&
                 (ruler->id() == Measurement::Ruler_RealTimeAutoTrace || ruler->id() == Measurement::Ruler_SonoVTI)))
            {
                run(measure);
            }
        }
    }
}

void TouchMeasureDispather::onLeftButtonDoubleClicked(const QPoint& pos)
{
    if (!m_MeasureCoordinator.isNull() && isMeasuring())
    {
        m_MeasureCoordinator->onLeftButtonDoubleClicked(pos);
    }
}

void TouchMeasureDispather::onLeftButtonPressed(const QPoint& pos)
{
    if (!m_MeasureCoordinator.isNull() && isMeasuring())
    {
        m_MeasureCoordinator->onLeftButtonPressed(pos);
    }
}

void TouchMeasureDispather::onLeftButtonReleased(const QPoint& pos)
{
    if (!m_MeasureCoordinator.isNull() && isMeasuring())
    {
        m_MeasureCoordinator->onLeftButtonReleased(pos);
    }
}

void TouchMeasureDispather::onRightButtonPressed(const QPoint& pos)
{
    if (!m_MeasureCoordinator.isNull() && isMeasuring())
    {
        m_MeasureCoordinator->onRightButtonPressed(pos);
    }
}

void TouchMeasureDispather::onRightButtonReleased(const QPoint& pos)
{
    if (!m_MeasureCoordinator.isNull() && isMeasuring())
    {
        m_MeasureCoordinator->onRightButtonReleased(pos);
    }
}

void TouchMeasureDispather::onMovedAction(const QPoint& offset, const QPoint& pos)
{
    qDebug() << "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@" << offset << "pos:" << pos;
    if (!m_MeasureCoordinator.isNull() && isMeasuring())
    {
        m_MeasureCoordinator->onMovedAction(offset, pos);
    }
}

void TouchMeasureDispather::onTouchPressed(const QPoint& pt)
{
    if (!m_MeasureCoordinator.isNull() && isMeasuring())
    {
        m_MeasureCoordinator->onTouchPressed(pt);
    }
}

void TouchMeasureDispather::onTouchReleased(const QPoint& pos)
{
    if (!m_MeasureCoordinator.isNull() && isMeasuring())
    {
        m_MeasureCoordinator->onTouchReleased(pos);
    }
}

bool TouchMeasureDispather::isBindReleaseTypeControl()
{
    if (!m_MeasureCoordinator.isNull() && isMeasuring())
    {
        return m_MeasureCoordinator->isBindReleaseTypeControl();
    }
    return false;
}

bool TouchMeasureDispather::hasGlyphsControl() const
{
    if (!m_MeasureCoordinator.isNull())
    {
        return m_MeasureCoordinator->glyphsControl() != nullptr;
    }
    return false;
}

QPoint TouchMeasureDispather::moveOffsetStep() const
{
    if (!m_MeasureCoordinator.isNull() && m_MeasureCoordinator->glyphsControl() != nullptr)
    {
        return m_MeasureCoordinator->glyphsControl()->moveOffsetStep();
    }
    Q_ASSERT(false);
    return QPoint();
}

void TouchMeasureDispather::rotate(qreal counterClockWiseAngle)
{
    if (!m_MeasureCoordinator.isNull())
    {
        m_MeasureCoordinator->rotate(counterClockWiseAngle);
    }
}

QPoint TouchMeasureDispather::measureCursorPos() const
{
    if (!m_MeasureCoordinator.isNull())
    {
        return m_MeasureCoordinator->glyphsControl()->overlay().currentPos().toPoint();
    }
    return QPoint();
}

void TouchMeasureDispather::onNotifyCursorInOut(bool mouseIsOut, const QPoint& pt)
{
    if (!m_MeasureCoordinator.isNull())
    {
        m_MeasureCoordinator->onNotifyCursorInOut(mouseIsOut, pt);
    }
}

bool TouchMeasureDispather::ableMoveOutControlRect() const
{
    if (!m_MeasureCoordinator.isNull())
    {
        return m_MeasureCoordinator->ableOutOfLimitRect();
    }
    return true;
}

void TouchMeasureDispather::setGraphicsView(QGraphicsView* view)
{
    m_View = view;
}

void TouchMeasureDispather::setScreenResultWidget(QWidget* screenWidget)
{
    m_ScreenResultWidget = screenWidget;
}

IMeasurement* TouchMeasureDispather::currentMeasurement()
{
    if (!m_MeasureCoordinator.isNull())
    {
        return m_MeasureCoordinator->measMeasurement();
    }

    if (m_Measure != nullptr)
    {
        return m_Measure;
    }

    return nullptr;
}

void TouchMeasureDispather::runMeasMeasure(IMeasurement* measure, bool needInteraction)
{
    IRuler* ruler = measure->currentRuler();
    StudyMeasurement* parent = dynamic_cast<StudyMeasurement*>(measure->parentMeasurement());

    if (ruler != NULL && ruler->canRun())
    {
        bool unFinished = (!m_MeasureCoordinator.isNull() && !m_MeasureCoordinator->finished());
        MeasMeasurement* currentMeasure =
            m_MeasureCoordinator.isNull() ? NULL : m_MeasureCoordinator->measMeasurement();

        if (m_StateRecorder->isNewGroup())
        {
            if (currentMeasure != NULL && currentMeasure == measure)
            {
                if (unFinished)
                    m_MeasureCoordinator->setFinished(false);

                m_MeasureCoordinator.reset();
            }
            else if (unFinished)
            {
                m_MeasureCoordinator->setFinished(false);

                if (m_Measure != nullptr)
                    m_Measure->stop();

                m_MeasureCoordinator.reset();
            }
        }
        else
        {
            if (unFinished)
                m_MeasureCoordinator->setFinished(false);

            m_MeasureCoordinator.reset();
            removeCurrentMeasMeasurement(measure, measure->layer());
        }

        createMeasureCoordinator(ruler);

        Q_ASSERT(m_View != nullptr);
        m_MeasureCoordinator->setGraphicsView(m_View);
        m_MeasureCoordinator->setScreenResultWidget(m_ScreenResultWidget);
        m_MeasureCoordinator->setStateRecorder(m_StateRecorder);
        MeasMeasurement* measmeasurement = dynamic_cast<MeasMeasurement*>(measure);
        Q_ASSERT(measmeasurement != NULL);
        m_MeasureCoordinator->setMeasMeasurement(measmeasurement);

        IMeasurement* screenGroupMeas = getScreenGroupMeasurement(measure);
        if (screenGroupMeas != nullptr)
        {
            m_StateRecorder->setScreenGroupMeasurement(screenGroupMeas);
        }

        BaseChildMeasurement* childMeasurement = dynamic_cast<BaseChildMeasurement*>(measure);
        Q_ASSERT(childMeasurement != NULL);
        IMeasurement* measGroup = getGroupMeasurement(childMeasurement);
        if (measGroup != nullptr)
        {
            m_StateRecorder->setMeasGroupMeasurement(measGroup);
        }

        m_StateRecorder->setLeftRight(measure->leftRight());
        m_StateRecorder->setFarMidNear(measure->farMidNear());
        m_StateRecorder->setFetusIndex(measure->fetusIndex());

        m_MeasureCoordinator->setScreenMeasureController(m_ScreenMeasureController);

        connect(m_MeasureCoordinator.get(), &MeasureProcessCoordinator::measureComplated, this,
                &TouchMeasureDispather::onMeasurementCompleted, Qt::UniqueConnection);

        // real time measure, parent is NULL
        if (parent != NULL)
        {
            if (Resource::intrapartumIds().contains(m_StateRecorder->getFirstParent(parent)->id()))
            {
                m_StateRecorder->measGroupMeasurement()->setLayer(measure->layer());
                m_StateRecorder->measGroupMeasurement()->setLayerTime(measure->layerTime());
            }
        }

        measure->start();

        GlyphsCtl::ControlType controlType = (GlyphsCtl::ControlType)ruler->glyphsControlType();
        if (controlType != GlyphsCtl::NoneType)
        {
            MeasureGlyphsControl* glyphsControl =
                GlyphsControlManager::instance().getChildGlyphsControl<MeasureGlyphsControl>(controlType);

            Q_ASSERT(glyphsControl != nullptr);
            glyphsControl->setMeasurement(measure);
            m_MeasureCoordinator->setGlyphsControl(glyphsControl);
        }
        m_MeasureCoordinator->setRuler(ruler);

        setIsMeasuring(needInteraction);

        m_MeasureCoordinator->calculateImageOriginCoordinates(false);
        ruler->setMeasurement(measure);
        ruler->start();

        measure->setStep(Measurement::Step_Started);

        bool isRealTimeMeasurement =
            measure->inherits("RealTimeMeasMeasurement") || ruler->inherits("RealTimeSonothyroidRuler") ||
            ruler->inherits("RTIMTRuler") || ruler->inherits("AutoEFTraceMeasureRuler") ||
            ruler->inherits("LungAutoLineBRuler") || ruler->type() == Measurement::RulerType_SonVTI ||
            (ruler->inherits("SonoAAARuler") &&
             (m_MeasureCoordinator->glyphsControl() != NULL && !m_MeasureCoordinator->glyphsControl()->beginEdit()));

        m_MeasureCoordinator->setAbleControlCursorInBegin(!isRealTimeMeasurement && needInteraction);
        setIsRTMeasuring(isRealTimeMeasurement);

        if (currentMeasurement()->packageRegion() == Measurement::Region_Quick ||
            ruler->type() == Measurement::RulerType_SonoVFLine || ruler->type() == Measurement::RulerType_SonoVFTrace)
            m_MeasureCoordinator->backupResultMeasure();

        m_MeasureCoordinator->setIsResultGenerate(false);
        if (controlType != GlyphsCtl::NoneType)
        {
            emit measurementStarted();
        }
        m_MeasureCoordinator->onBeginAction();

        m_Measure = measure;
    }
}

void TouchMeasureDispather::runCalcMeasure(IMeasurement* measure, bool needInteraction)
{
    if (m_StateRecorder->isNewGroup() && !m_MeasureCoordinator.isNull() && !m_MeasureCoordinator->finished())
    {
        m_MeasureCoordinator->setFinished(false);
    }

    m_StateRecorder->setScreenGroupMeasurement(measure);

    BaseChildMeasurement* childMeasurement = dynamic_cast<BaseChildMeasurement*>(measure);
    Q_ASSERT(childMeasurement != NULL);
    m_StateRecorder->setMeasGroupMeasurement(getGroupMeasurement(childMeasurement));

    if (measure->isEnabled())
    {
        measure->start();

        m_CallCalcMeasurement = measure;
        runMeasMeasure(measure->relatedMeasMeasurements().first());
    }
}

void TouchMeasureDispather::runStudyMeasure(IMeasurement* measure)
{
    if (measure->measOrder() != Measurement::Order_Repeated && measure->measOrder() != Measurement::Order_Next)
    {
        return;
    }

    if (!m_MeasureCoordinator.isNull() && !m_MeasureCoordinator->finished())
    {
        m_MeasureCoordinator->setFinished(false);
    }
    m_MeasureCoordinator.reset();

    m_StateRecorder->reset();

    IMeasurement* nextMeas = m_StateRecorder->nextChild();
    if (nextMeas != nullptr && measure->visibleEnabledChildren().indexOf(nextMeas) >= 0)
    {
        if (nextMeas->type() != Measurement::MT_Calc)
        {
            m_CallCalcMeasurement = nullptr;
        }
        runMeasure(nextMeas);
    }
    else
    {
        IMeasurement* defMeas = measure->defaultChildMeasurement();
        if (defMeas != NULL && defMeas->isEnabled())
        {
            if (defMeas->type() != Measurement::MT_Calc)
            {
                m_CallCalcMeasurement = nullptr;
            }
            runMeasure(defMeas);
        }
    }
}

void TouchMeasureDispather::modifyMeasMeasure(IMeasurement* measure)
{
    qDebug() << "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@" << PRETTY_FUNCTION;

    IRuler* ruler = measure->currentRuler();
    if (ruler != NULL && measure->isEnabled() && ruler->canRun())
    {
        createMeasureCoordinator(ruler);

        MeasMeasurement* measmeasurement = dynamic_cast<MeasMeasurement*>(measure);
        Q_ASSERT(measmeasurement != NULL);
        m_MeasureCoordinator->setMeasMeasurement(measmeasurement);
        m_MeasureCoordinator->setStateRecorder(m_StateRecorder);

        measure->update();

        GlyphsCtl::ControlType controlType = (GlyphsCtl::ControlType)ruler->glyphsControlType();

        MeasureGlyphsControl* glyphsControl =
            GlyphsControlManager::instance().getChildGlyphsControl<MeasureGlyphsControl>(controlType);

        Q_ASSERT(glyphsControl != nullptr);

        m_MeasureCoordinator->setGlyphsControl(glyphsControl);

        ruler->start();
        m_MeasureCoordinator->setRuler(ruler);
    }
}

IMeasurement* TouchMeasureDispather::getScreenGroupMeasurement(IMeasurement* measure)
{
    if (!measure->isVisible() && measure->region() != Measurement::Region_AAO)
    {
        // 此时，calcMeasurment管理ScreenGroup设置
        return nullptr;
    }
    StudyMeasurement* study = dynamic_cast<StudyMeasurement*>(measure->parentMeasurement());
    if (study != nullptr)
    {
        if (study->isMeasureGroup() && !study->containMeasurementContainer())
        {
            return study;
        }
    }
    return measure;
}

IMeasurement* TouchMeasureDispather::getGroupMeasurement(BaseChildMeasurement* measure)
{
    if (!measure->isVisible() && measure->region() != Measurement::Region_AAO)
    {
        // 此时，calcMeasurment管理GroupMeasurement设置
        return nullptr;
    }
    StudyMeasurement* parent = dynamic_cast<StudyMeasurement*>(measure->parentMeasurement());
    if (parent != nullptr)
    {
        if (parent->isMeasureGroup())
        {
            return m_StateRecorder->getGroupMeasurement(parent);
        }
    }

    return measure;
}

IMeasurement* TouchMeasureDispather::nextNeedRunMeasurement()
{
    if (!m_MeasureCoordinator.isNull())
    {
        MeasMeasurement* measMeasure = m_MeasureCoordinator->measMeasurement();
        if (measMeasure->isVisible())
        {
            IMeasurement* parent = measMeasure->parentMeasurement();

            if (parent == nullptr)
                return nullptr;

            Q_ASSERT(parent->type() == Measurement::MT_Study);
            if (parent->type() == Measurement::MT_Study)
            {
                StudyMeasurement* study = dynamic_cast<StudyMeasurement*>(parent);
                if (study->measOrder() == Measurement::Order_Repeated)
                {
                    m_StateRecorder->reset();
                    m_IsRepeat = true;
                    return measMeasure;
                }
                else
                {
                    return study->nextRunMeasurement(m_MeasureCoordinator->measureOrderIndex(),
                                                     m_MeasureCoordinator->measMeasurement());
                }
            }
        }
        else // is run calc measurement
        {
            if (m_CallCalcMeasurement != nullptr)
            {
                IMeasurement* next = m_CallCalcMeasurement->nextRunMeasurement(
                    m_MeasureCoordinator->measureOrderIndex(), m_MeasureCoordinator->measMeasurement());
                if (next != nullptr)
                {
                    return next;
                }
                else
                {
                    IMeasurement* parent = m_CallCalcMeasurement->parentMeasurement();
                    Q_ASSERT(parent != nullptr);

                    StudyMeasurement* study = dynamic_cast<StudyMeasurement*>(parent);
                    if (study->measOrder() == Measurement::Order_Repeated)
                    {
                        m_StateRecorder->reset();
                        m_IsRepeat = true;
                        return m_CallCalcMeasurement;
                    }
                }
            }
        }
    }
    return nullptr;
}

void TouchMeasureDispather::removeCurrentMeasMeasurement(IMeasurement* measure, QVariant layer)
{
    int currentIndex = measure->currentMeasureOrderIndex();
    if (currentIndex >= 0)
    {
        // 删除测量图元
        m_ScreenMeasureController->removeGlyph(measure->composedId(), measure->leftRight(), measure->farMidNear(),
                                               measure->fetusIndex(), currentIndex, layer);
        // 删除测量数据
        measure->clearResultAt(measure->leftRight(), measure->farMidNear(), measure->fetusIndex(), currentIndex);
    }
    else
    {
        qDebug() << PRETTY_FUNCTION << "caption: " << measure->caption() << "index: " << currentIndex;
        //        Q_ASSERT(false);
    }
}

void TouchMeasureDispather::removeCurrentMeasMeasurement(IMeasurement* measure)
{
    QList<IMeasurement*> measList = measure->relatedMeasMeasurements();
    if (measList.isEmpty())
    {
        return;
    }

    int currentIndex = measure->currentMeasureOrderIndex();
    if (currentIndex >= 0)
    {
        foreach (IMeasurement* meas, measList)
        {
            // 删除测量图元，需要使用依赖的测量，进行依次清除。
            m_ScreenMeasureController->removeGlyph(meas->composedId(), meas->leftRight(), meas->farMidNear(),
                                                   meas->fetusIndex(), currentIndex, meas->layer());
        }
        // 删除测量数据, 可以直接使用calc进行
        measure->clearResultAt(measure->leftRight(), measure->farMidNear(), measure->fetusIndex(), currentIndex);
    }
    else
    {
        qDebug() << PRETTY_FUNCTION << currentIndex;
    }
}

void TouchMeasureDispather::onMeasurementCompleted(bool continueMeasure, bool isSure)
{
    if (isSure)
    {
        IMeasurement* nextMeasurement = nextNeedRunMeasurement();
        if (nextMeasurement != nullptr)
        {
            if (continueMeasure)
            {
                StudyMeasurement* study = dynamic_cast<StudyMeasurement*>(nextMeasurement->parentMeasurement());
                // 计算型测量需要保证结果都在同一组
                if (!m_IsRepeat && !m_MeasureCoordinator->measMeasurement()->isVisible() &&
                    m_CallCalcMeasurement != nullptr)
                {
                    bool isMeasureGroup = true;
                    StudyMeasurement* study = dynamic_cast<StudyMeasurement*>(nextMeasurement->parentMeasurement());
                    if (study != nullptr)
                        isMeasureGroup = study->isMeasureGroup();

                    /*----a
                     *      ----b
                     *      ----c
                     *----d
                     *
                     *a是个测量文件夹 下面有b,c两个测量项,d是一个测量项
                     *
                     *   如果b, c没有设置MeasureGroup属性,也要每次设置NewGroup为false吗
                     *
                     *   如果每次都设置false,多次测量b,c,d之后计算项的结果异常
                     *
                     */
                    if (isMeasureGroup)
                    {
                        m_StateRecorder->setIsNewGroup(false);
                    }
                }
                else if (study != NULL && study->type() == Measurement::MT_Study && study->isMeasureGroup())
                {
                    bool isMeasureGroup = true;
                    StudyMeasurement* study = dynamic_cast<StudyMeasurement*>(nextMeasurement->parentMeasurement());
                    if (study != nullptr)
                        isMeasureGroup = study->isMeasureGroup();

                    /*----a
                     *      ----b
                     *      ----c
                     *----d
                     *
                     *a是个测量文件夹 下面有b,c两个测量项,d是一个测量项
                     *
                     *   如果b, c没有设置MeasureGroup属性,也要每次设置NewGroup为false吗
                     *
                     *   如果每次都设置false,多次测量b,c,d之后计算项的结果异常
                     *
                     */
                    if (isMeasureGroup)
                    {
                        m_StateRecorder->setIsNewGroup(false);
                    }
                }
                else
                {
                    m_StateRecorder->setIsNewGroup(true);
                }

                m_StateRecorder->setNextChild(nextMeasurement);

                // 快速测量不支持继续测量
                if (!m_MeasureCoordinator.isNull())
                {
                    MeasMeasurement* measMeasure = m_MeasureCoordinator->measMeasurement();
                    if (measMeasure->packageRegion() == Measurement::Region_Quick)
                    {
                        m_StateRecorder->setNextChild(nullptr);
                    }
                }
                if (nextMeasurement->packageRegion() == Measurement::Region_Quick)
                {
                    if (nextMeasurement == currentMeasurement())
                        m_StateRecorder->setIsNewGroup(true);
                    else if (nextMeasurement->type() != Measurement::MT_Meas)
                        m_StateRecorder->setIsNewGroup(true);
                    else
                        m_StateRecorder->setIsNewGroup(false);
                }

                emit runNext(nextMeasurement);
            }
        }
        else
        {
            m_StateRecorder->setNextChild(nullptr);
            m_NeedSetCursorPos = true;
            setIsMeasuring(false);
        }

        if (nextMeasurement == NULL)
            emit resetQuickMeasureToolInactived(-1);
    }
    else
    {
        m_StateRecorder->setNextChild(nullptr);
        m_NeedSetCursorPos = true;
        setIsMeasuring(false);
    }

    m_IsRepeat = false;
}

void TouchMeasureDispather::createMeasureCoordinator(IRuler* ruler)
{
    if (/*ruler->id() == Measurement::Ruler_DpDevideDtLineD
             ||*/
        ruler->id() == Measurement::Ruler_RapSelectD || ruler->id() == Measurement::Ruler_ColorAliasingVelocity)
    {
        m_MeasureCoordinator.reset(new InputMeasureProcessCoordinator);
    }
    else
    {
        m_MeasureCoordinator.reset(new TouchMeasureProcessCoordinator);
    }

    m_MeasureCoordinator->setTouchMeasureDispather(this);
}

bool TouchMeasureDispather::getIsSupportOutOfRange() const
{
    return m_IsSupportOutOfRange;
}

void TouchMeasureDispather::setIsSupportOutOfRange(bool IsSupportOutOfRange)
{
    m_IsSupportOutOfRange = IsSupportOutOfRange;
}

void TouchMeasureDispather::measurementAttributeChange()
{
    if (m_MeasureCoordinator == NULL || m_StateRecorder == NULL)
    {
        return;
    }

    MeasMeasurement* measure = m_MeasureCoordinator->measMeasurement();
    if (measure == NULL)
    {
        return;
    }

    m_MeasureCoordinator->updateAttribute();
    m_MeasureCoordinator->clearResult();
    m_StateRecorder->setLeftRight(measure->leftRight());
    m_StateRecorder->setFarMidNear(measure->farMidNear());
    m_StateRecorder->setFetusIndex(measure->fetusIndex());
}

bool TouchMeasureDispather::needSetCursorPos() const
{
    return m_NeedSetCursorPos;
}

void TouchMeasureDispather::setNeedSetCursorPos(bool value)
{
    m_NeedSetCursorPos = value;
}

MeasureGlyphsControl* TouchMeasureDispather::currentGlyphsControl()
{
    if (m_MeasureCoordinator == NULL)
        return NULL;

    return m_MeasureCoordinator->glyphsControl();
}

void TouchMeasureDispather::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void TouchMeasureDispather::stopCurrentMeasure()
{
    IMeasurement* meas = currentMeasurement();
    // m_MeasureCoordinator will not be null after end measure.
    if (meas == NULL)
        return;

    //如果是AI测量,执行失败没有生成结果那么就把当前子节点的值清除掉
    MeasureStateRecorder* recorder = &MeasureStateRecorder::instance();
    MeasMeasurement* m = dynamic_cast<MeasMeasurement*>(meas);
    if (m != NULL)
    {
        recorder->setChildNumber(
            Measurement::MeasurementChildNumber(recorder->childNumber() - m->currentRuler()->getGroupNumber()));
        recorder->clearCurrentMeasureID();
    }

    if (!m_MeasureCoordinator.isNull())
    {
        if (!m_MeasureCoordinator.isNull() && !m_MeasureCoordinator->finished())
            m_MeasureCoordinator->setFinished(false);

        m_MeasureCoordinator->stopConnection();
    }
    setIsMeasuring(false);
}

void TouchMeasureDispather::runNextMeasure(IMeasurement* nextMeasurement)
{
    runMeasure(nextMeasurement);
}
