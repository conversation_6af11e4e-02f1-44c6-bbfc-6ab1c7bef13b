#ifndef SONOAAARULER_H
#define SONOAAARULER_H

#include "baseruler.h"
#include "alginstance.h"

class LineMeasure;
class AIRectLinesMeasureGlyphsControl;

class SonoAAARuler : public BaseRTImageRuler
{
    Q_OBJECT
public:
    explicit SonoAAARuler(GlyphsCtl::ControlType type, QObject* parent = 0);

    virtual void initGlyphsControl();
    void notFound();

    enum LINETYPE
    {
        Distance = 0,
        Length,
        Width
    };

    enum INDEX
    {
        TYPE = 0,
        FMN,
        X1,
        Y1,
        X2,
        Y2,
        X3,
        Y3,
        X4,
        Y4,
        X5,
        Y5,
        WIDTH,
        HEIGHT,
    };

    QVector<int> runAlg();
    void stop();
    void updateResult();
    void setImageValidArea(const QRect& ImageValidArea);

    void setControl(AIRectLinesMeasureGlyphsControl* Control);
    AIRectLinesMeasureGlyphsControl* control() const;

    bool isFreezeRun() const;
    void setIsFreezeRun(bool isFreezeRun);

protected:
    virtual bool calcResult(QGraphicsItem* glyph, QPointF currentCursorPos);

private:
    void calDataColor(int index, qreal value);
    void hideOnResultList(int index);

private:
    AIRectLinesMeasureGlyphsControl* m_Control;
    SonoAAAAlg* m_Alg;
    QString m_ModelPath;
    QRect m_ImageValidArea;
    bool m_isFreezeRun;
    bool m_BeginEdit;
};

#endif // SONOAAARULER_H
