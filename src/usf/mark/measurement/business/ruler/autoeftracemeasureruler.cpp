#include "autoeftracemeasureruler.h"
#include "measurecontext.h"
#include "messageboxframe.h"
#include "imeasurement.h"
#include "glyphscontrolmanager.h"
#include <QTime>
#include <qmath.h>
#include "bfpnames.h"
#include "imageeventargs.h"
#include "istatemanager.h"
#include "logger.h"
#include "stateeventnames.h"
#include "toolnames.h"
#include <QThread>
#include "util.h"
#ifdef USE_PWTRACE
#include "traceanalysis.h"
#endif
#include "glyphscontrolmanager.h"
#include "usfobject.h"
#include "autoefmeasureglyphscontrol.h"

AutoEFTraceMeasureRuler::AutoEFTraceMeasureRuler(GlyphsCtl::ControlType type, QObject* parent)
    : BaseImageRuler(type, parent)
{
    m_Id = Measurement::Ruler_AutoEF;
    m_Type = Measurement::RulerType_AutoEF;

    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_AutoEF_EDV);
        item.setName("AutoEFEDV");
        item.setUnit(Measurement::Unit_ml);
        item.setUnitType(Measurement::UnitType_Volume);
        m_Result.append(item);
    }

    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_AutoEF_ESV);
        item.setName("AutoEFESV");
        item.setUnit(Measurement::Unit_ml);
        item.setUnitType(Measurement::UnitType_Volume);
        m_Result.append(item);
    }

    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_AutoEF_EF);
        item.setName("AutoEF");
        item.setUnit(Measurement::Unit_100percent);
        item.setUnitType(Measurement::UnitType_None);
        m_Result.append(item);
    }

    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RadiusArray);
        item.setName("RadiusArray");
        item.setUnit(Measurement::Unit_cm);
        item.setUnitType(Measurement::UnitType_Distance);
        item.setValueEditable(false);
        m_Result.append(item);
    }

    setIsAutoAlgrithm(true);
    m_AutoEf = AlgInstance::algInstance().getAutoEfAlg();

    m_vEDV = 0.0f;
    m_vESV = 0.0f;
    m_vEF = 0.0f;
}

AutoEFTraceMeasureRuler::~AutoEFTraceMeasureRuler()
{
    if (m_NotingState != nullptr)
    {
        delete m_NotingState;
        m_NotingState = nullptr;
    }

    m_AutoEf->release();
}

void AutoEFTraceMeasureRuler::onNewImage(ImageEventArgs* imageEventArgs)
{
    // AutoEF执行结束后，AutoEFOnStr设置为false，确保DSC图像处理及时执行，无需再处理多余数据
    //刷新左右双副图像
    //    qDebug() << "tjy111" << PRETTY_FUNCTION << imageEventArgs->imageType()
    //             << imageEventArgs->getFrameStartTime() << imageEventArgs->frameIndex();
    if (!m_IsRefreshed && m_IsFinished)
    {
        if (imageEventArgs->imageRenderPartition() == Partition_Left)
        {
            emit setAutoEF(false);
            m_IsLeftRefreshed = true;
        }
        else if (imageEventArgs->imageRenderPartition() ==
                 Partition_Right) //算法识别完后，先刷新右侧的识别结果，再利用返回的信息刷新左侧的识别结果
        {
            emit drawPartitionLeft();
            m_IsRightRefreshed = true;
        }
        m_IsRefreshed = m_IsLeftRefreshed & m_IsRightRefreshed;
        return;
    }

    if (m_IsCompleted)
    {
        // B and ECG all need callback
        onCallBackNewImage(imageEventArgs);
        return;
    }

    if (imageEventArgs->imageType() == ImageEventArgs::ImageECG)
    {
        if (!m_IsFinished)
        {
            m_CurrentIndex++;
            m_Context->setCurrentIndex(m_CurrentIndex);
        }
        return;
    }

    if (m_CurrentIndex >= m_endIndex && !m_IsFinished)
    {
        m_Control->setIsClickedAutoEF(false);
        m_Control->setIsEdit(true);
        m_IsClicked = false;
        emit finished();
        return;
    }

    //保证onNewImage的信号被断开时，onNewImage内的代码不会被执行; 保证在ECG打开的时候，同一帧B图像只会跑一次算法
    //图像数据过滤，只有B图像数据产生时，才继续处理
    if (m_IsRefreshed || imageEventArgs->isNull() || m_FrameIndexList.contains(imageEventArgs->frameIndex()) ||
        imageEventArgs->imageType() != ImageEventArgs::ImageType::ImageB)
    {
        return;
    }
    m_FrameIndexList.append(imageEventArgs->frameIndex());

    if (m_Control->isClickedAutoEF())
    {
        m_Context->setBImage(QImage(imageEventArgs->imageData(), imageEventArgs->width(), imageEventArgs->height(),
                                    QImage::Format_RGB32));
    }

    m_Control->setActiveB(m_Context->autoEFActiveLayout());

    QImage img(imageEventArgs->imageData(), imageEventArgs->width(), imageEventArgs->height(), QImage::Format_RGB32);

    m_FrameTimeStamp = imageEventArgs->getFrameStartTime();
    setFrameTimeStamp(m_FrameTimeStamp);
    m_Control->setCurrentTimeStamp(m_FrameTimeStamp);

    m_Control->setDefaultWidth(imageEventArgs->width());

    getPath(img);

    m_Control->getAutoEFData()[m_FrameTimeStamp].setFrameIndex(imageEventArgs->frameIndex());

    emit drawPath(imageEventArgs->getFrameStartTime());

    if (!m_Context->isEcgOn())
    {
        m_CurrentIndex++;
        m_Context->setCurrentIndex(m_CurrentIndex);
    }
}

void AutoEFTraceMeasureRuler::onCallBackNewImage(ImageEventArgs* imageEventArgs)
{
    qint64 timestamp = imageEventArgs->getFrameStartTime();

    if (m_Control == nullptr)
    {
        return;
    }

    setFrameTimeStamp(timestamp);
    m_Control->setCurrentTimeStamp(timestamp);
    emit drawPath(m_FrameTimeStamp);
}

int AutoEFTraceMeasureRuler::initLVDetect()
{
    int ret = -1;

    QString a2c_mnn_path_detect = Resource::mnnDir() + "/lv2.mnn";
    QString a4c_mnn_path_detect = Resource::mnnDir() + "/lv4.mnn";
    QString a2c_clcache = Resource::mnnDir() + "/model_lv2.cache";
    QString a4c_clcache = Resource::mnnDir() + "/model_lv4.cache";

    if (Resource::functionalConfigurationSettingValue("AutoEFA4C", Resource::ValueType::MeasurementId)
            .contains(m_Measurement->id()))
    {
        m_AutoEf->initDetect(a4c_mnn_path_detect.toStdString().c_str(), A4C, 0.6f, a4c_clcache.toStdString().c_str());
    }
    else if (Resource::functionalConfigurationSettingValue("AutoEFA2C", Resource::ValueType::MeasurementId)
                 .contains(m_Measurement->id()))
    {
        m_AutoEf->initDetect(a2c_mnn_path_detect.toStdString().c_str(), A2C, 0.6f, a2c_clcache.toStdString().c_str());
    }

    return ret;
}

void AutoEFTraceMeasureRuler::updateSVIndex(AutoEFData::ResultType Type, qint64 index)
{
    if (m_Control == nullptr)
    {
        return;
    }

    m_Control->showAnchor();

    if (Type == AutoEFData::ResultTypeEDV)
    {
        m_Context->setAutoEFEDCurIndex(index);
    }
    else if (Type == AutoEFData::ResultTypeESV)
    {
        m_Context->setAutoEFESCurIndex(index);
    }

    m_Control->updateSVIndex(Type, index);
    // need use TimeStamp from control for select ed or es index
    setFrameTimeStamp(m_Control->getCurrentTimeStamp());
}

void AutoEFTraceMeasureRuler::setControl(AutoEFMeasureGlyphsControl* control)
{
    m_Control = control;
}

void AutoEFTraceMeasureRuler::initGlyphsControl()
{
    if (m_Control == nullptr)
    {
        return;
    }

    m_Control->hideAnchor();

    if (m_IsFinished)
    {
        m_Control->setCanEdit(false);
        m_Control->setIsClickedAutoEF(false);
        m_Context->setAutoEFHasResult(true);
        m_startIndex = m_Context->startIndex();
        m_endIndex = m_Context->endIndex();
        m_Control->setActiveB(m_Context->autoEFActiveLayout());
        m_IsCompleted = true;
        // BaseRuler::resetResult will clear result, need add this place not in load
        setResult();
    }
    else
    {
        m_Control->getAutoEFData().clear();
        m_Control->setCanEdit(true);
        emit setAutoEF(false);
        m_CurrentIndex = m_startIndex;
        m_Context->setCurrentIndex(m_CurrentIndex);
        m_Context->setAutoEFStartEndIndexs(m_startIndex, m_endIndex);
        m_Control->setActiveB(m_Context->autoEFActiveLayout());
        m_IsCompleted = false;
    }
    m_Control->setStartEndTimeStamp(m_Context->startTimeStamp(), m_Context->endTimeStamp());
}

void AutoEFTraceMeasureRuler::onStop()
{
    disconnectSignals();

    // need reset false
    if (m_Control == nullptr)
    {
        return;
    }
    m_Control->setIsEdit(false);
    m_Control->setCurrentTimeStamp(-1);
}

void AutoEFTraceMeasureRuler::onRun()
{
    onStop();
    setIsStopMeasure(false);
    connect(m_Context, SIGNAL(onActivePartitionChanged(QVariant)), this, SLOT(setActivePartitionChanged(QVariant)),
            Qt::UniqueConnection);
    connect(m_Context, SIGNAL(AutoEFCompleted()), this, SLOT(AutoEFCompleted()));
    connect(this, SIGNAL(drawPath(qint64)), this, SLOT(drawAutoEFPath(qint64)),
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
    connect(m_Context, SIGNAL(newImage(ImageEventArgs*)), this, SLOT(onNewImage(ImageEventArgs*)),
            Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
    if (m_IsClicked)
    {
        if (m_NotingState == nullptr)
        {
            emit blockTouch(true);
            m_NotingState = new StateFilterLocker(m_StateManager, StateEventNames::NothingStateFilter());
        }
        connect(m_Context, SIGNAL(currentIndexChanged(int)), this, SLOT(onCurrentIndexChanged(int)),
                Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
        connect(this, SIGNAL(finished()), this, SLOT(onFinished()),
                Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
        connect(this, SIGNAL(drawPartitionLeft()), this, SLOT(onDrawPartitionLeft()),
                Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
        if (!m_AutoEf->isInitialized())
        {
            initLVDetect();
        }
        m_IsRefreshed = m_IsLeftRefreshed = m_IsRightRefreshed = false;
    }
    m_MeasurementId = m_Measurement->id();
    m_FrameIndexList.clear();
}

void AutoEFTraceMeasureRuler::onFinished()
{
    resetAlgorithm();
    m_IsFinished = true;

    if (m_Control == nullptr || m_Control->getAutoEFData().size() < 2 ||
        m_Control->isNoPath(m_Control->getAutoEFData()))
    {
        if (m_Control != nullptr)
        {
            m_Control->endMeasure();
        }
        emit blockTouch(false);
        if (m_NotingState != nullptr)
        {
            delete m_NotingState;
            m_NotingState = nullptr;
        }

        MessageBoxFrame::tipWarningNonModal(tr("There is no matched LV area to measure."));
        ApplicationInfo::instance().controlCursorDirectVisible(true);
        return;
    }

    //设置AutoEFCurLayoutTool,AutoEFSingleBTool可用
    m_StateManager->setIsSupportAutoEF(true);
    m_StateManager->setIsSupportAutoEFSingle(true);

    connect(m_Context, SIGNAL(updateESVIndex(qint64)), this, SLOT(onUpdateESVIndex(qint64)), Qt::UniqueConnection);
    connect(m_Context, SIGNAL(updateEDVIndex(qint64)), this, SLOT(onUpdateEDVIndex(qint64)), Qt::UniqueConnection);
    connect(m_Context, SIGNAL(changeFrameIndex(qint64)), this, SLOT(onChangeFrameIndex(qint64)), Qt::UniqueConnection);

    emit setAutoEF(true);
    emit setBBCImageOn(true);

    // need setAutoEFHasResult first
    m_Context->setAutoEFHasResult(true);
    m_Context->setAutoEFActiveLayout(Layout_1x2_1);
    m_Control->setActiveB(Layout_1x2_1);

    m_Control->showAnchor();
    m_Control->calcVolume();
    m_Control->updateAutoEFResult();

    qint64 esvTimeStamp = m_Control->getEsvFrameTimeStamp();

    m_Control->setIsEdit(true);
    drawImageAndPath(Partition_Right, esvTimeStamp, Layout_1x2_2, m_Context->currentIndex());
    m_Context->setCurrentIndex(m_Context->getFrameIndexByFrameTimestamp(esvTimeStamp), !m_IsEasyView);
}

void AutoEFTraceMeasureRuler::onDrawPartitionLeft()
{
    qint64 esvTimeStamp = m_Control->getEsvFrameTimeStamp();
    qint64 edvTimeStamp = m_Control->getEdvFrameTimeStamp();
    drawImageAndPath(Partition_Left, edvTimeStamp, Layout_1x2_1, m_Context->currentIndex());
    m_Context->setCurrentIndex(m_Context->getFrameIndexByFrameTimestamp(edvTimeStamp), !m_IsEasyView);
    m_Context->setAutoEFIndexs(m_Context->getFrameIndexByFrameTimestamp(edvTimeStamp),
                               m_Context->getFrameIndexByFrameTimestamp(esvTimeStamp));
    m_Context->setAutoEFEDCurIndex(edvTimeStamp);
    m_Context->setAutoEFESCurIndex(esvTimeStamp);
    setFrameTimeStamp(edvTimeStamp);

    //创建底部菜单
    m_USFObject->sendSyncEventTo(UEventType::TOOL_RUNAUTOEF);
    emit blockTouch(false);
    if (m_NotingState != nullptr)
    {
        delete m_NotingState;
        m_NotingState = nullptr;
    }
}

void AutoEFTraceMeasureRuler::onUpdateESVIndex(qint64 index)
{
    updateSVIndex(AutoEFData::ResultTypeESV, index);
}

void AutoEFTraceMeasureRuler::onUpdateEDVIndex(qint64 index)
{
    updateSVIndex(AutoEFData::ResultTypeEDV, index);
}

void AutoEFTraceMeasureRuler::onChangeFrameIndex(qint64 index)
{
    setCurActiveIndex(index);
    m_Control->setActiveB(m_Context->autoEFActiveLayout());
    QMap<qint64, AutoEFData> data = m_Control->getAutoEFData();
    setFrameTimeStamp(index);
    m_Control->setCurrentTimeStamp(index);
    emit drawPath(index);
}

void AutoEFTraceMeasureRuler::AutoEFCompleted()
{
    if (m_Context->hasAutoEFResult())
    {
        m_IsCompleted = true;
        m_USFObject->sendSyncEventTo(UEventType::TOOL_STOPAUTOEF);
        disconnect(m_Context, SIGNAL(updateESVIndex(qint64)), this, SLOT(onUpdateESVIndex(qint64)));
        disconnect(m_Context, SIGNAL(updateEDVIndex(qint64)), this, SLOT(onUpdateEDVIndex(qint64)));
        onStop();
    }
}

bool AutoEFTraceMeasureRuler::calcResult(QGraphicsItem* glyph, QPointF currentCursorPos)
{
    Q_UNUSED(currentCursorPos);

    // normal condition
    if (m_Control->getCanEdit())
    {
        m_Control->getAutoEFResult().clear();
        m_vEDV = m_Control->getVolume(m_Control->getEdvFrameTimeStamp());
        m_vESV = m_Control->getVolume(m_Control->getEsvFrameTimeStamp());
        setResult();

        QList<qreal> resultList;
        resultList.append(m_vEDV);
        resultList.append(m_vESV);
        m_Control->getAutoEFResult().insert(m_FrameTimeStamp, resultList);
    }

    return true;
}

void AutoEFTraceMeasureRuler::resetAlgorithm()
{
    m_AutoEf->release();
}

void AutoEFTraceMeasureRuler::setCurActiveIndex(qint64 index)
{
    int activeB = m_Context->autoEFActiveLayout();
    switch (activeB)
    {
    case Layout_1x1_1:
        if (m_Context->oldPartitionChanged() == Layout_1x2_1)
        {
            m_Context->setAutoEFEDCurIndex(index);
        }
        else if (m_Context->oldPartitionChanged() == Layout_1x2_2)
        {
            m_Context->setAutoEFESCurIndex(index);
        }

        break;
    case Layout_1x2_1:
        m_Context->setAutoEFEDCurIndex(index);
        break;
    case Layout_1x2_2:
        m_Context->setAutoEFESCurIndex(index);
        break;
    default:
        break;
    }
}

void AutoEFTraceMeasureRuler::drawImageAndPath(int pation, qint64 timeStmp, CurLayout activeLayout, int curIndex)
{
    if (m_Context != nullptr && m_Control != nullptr)
    {
        int activeB = m_Control->getActiveB();
        m_Context->setImageRenderPartition(pation);
        m_Control->setActiveB(activeLayout);

        setFrameTimeStamp(timeStmp);
        m_Control->setCurrentTimeStamp(timeStmp);

        m_Control->drawPath(timeStmp);
        m_Control->setActiveB(activeB);
    }
}

void AutoEFTraceMeasureRuler::setIsCompleted(bool IsCompleted)
{
    m_IsCompleted = IsCompleted;
}

QString AutoEFTraceMeasureRuler::getMeasurementId() const
{
    return m_MeasurementId;
}

void AutoEFTraceMeasureRuler::createGlyphsControl()
{
    if (m_Control == nullptr)
    {
        m_Control =
            GlyphsControlManager::instance().getChildGlyphsControl<AutoEFMeasureGlyphsControl>(m_GlyphsControlType);
    }
}

void AutoEFTraceMeasureRuler::endMeasure(const QString content)
{
    MessageBoxFrame::tipWarningNonModal(content);
    resetAlgorithm();
    m_IsSupport = false;
}

bool AutoEFTraceMeasureRuler::canRun()
{
    return m_IsSupport;
}

void AutoEFTraceMeasureRuler::setIsSupport(bool isSupport)
{
    m_IsSupport = isSupport;
}

void AutoEFTraceMeasureRuler::setIsEasyView(bool isEasyView)
{
    m_IsEasyView = isEasyView;
}

bool AutoEFTraceMeasureRuler::calcEFIndex()
{
    bool isEnoughFrames = false;

    if (m_Context == nullptr)
    {
        return false;
    }

    if (m_Context->isEcgOn())
    {
        int count = m_Context->frameCount() - 1;
        m_CurrentIndex = m_Context->currentIndex();

        int size = calcRWaveList(m_CurrentIndex);
        if (m_Context->frameCount() >= m_endIndex - m_startIndex && m_startIndex >= 0)
        {
            isEnoughFrames = true;
        }
        else
        {
            count = (count > size) ? size : count;
            calcRWaveList(count);

            if (m_Context->frameCount() >= m_endIndex - m_startIndex && m_startIndex >= 0)
            {
                isEnoughFrames = true;
            }
        }
    }
    else
    {
        m_Context->calTime(m_Context->currentIndex(), m_startIndex, m_endIndex, isEnoughFrames);
    }

    /*
     * AutoEF功能只有在下列条件满足的情况才是可用的:
     * 1)选择了一个保存的电影文件或者多帧冻结
     * 2)相控阵探头采集的图像
     * 3)单B图像
     * 4)电影长度≥1个R-R周期或≥2s
     */
    if (!isEnoughFrames)
    {
        return false;
    }
    m_IsSupport = true;
    return true;
}

void AutoEFTraceMeasureRuler::disconnectSignals()
{
    disconnect(this, SIGNAL(finished()), this, SLOT(onFinished()));
    disconnect(this, SIGNAL(drawPartitionLeft()), this, SLOT(onDrawPartitionLeft()));
    disconnect(m_Context, SIGNAL(AutoEFCompleted()), this, SLOT(AutoEFCompleted()));
}

bool AutoEFTraceMeasureRuler::getIsFinished() const
{
    return m_IsFinished;
}

void AutoEFTraceMeasureRuler::setIsFinished(bool isFinished)
{
    m_IsFinished = isFinished;
}

void AutoEFTraceMeasureRuler::setResult()
{
    setValue(0, m_vEDV * std::pow(m_Context->pixelSizeCM(), 3));
    setValue(1, m_vESV * std::pow(m_Context->pixelSizeCM(), 3));
}

qint64 AutoEFTraceMeasureRuler::getCurrentTimeStamp() const
{
    return m_Control->getCurrentTimeStamp();
}

bool AutoEFTraceMeasureRuler::getPath(const QImage& img)
{
    if (img.isNull())
        return false;

    m_Image = Util::toGrayImage(img);

    int pntsNum = 0;
    int pnts[4000] = {0};
    float area = 0, volume = 0;
    if (!m_Image.isNull())
    {
        m_AutoEf->detect((unsigned char*)m_Image.bits(), m_Image.width(), m_Image.height(), pntsNum, &pnts[0], area,
                         volume);
    }

    if (pntsNum > 0)
    {
        //点击测量，则重新过算法，获取点集
        if (!m_IsFinished)
        {
            if (!m_Control->getAutoEFData()[m_FrameTimeStamp].getESDPointList().isEmpty())
                m_Control->getAutoEFData()[m_FrameTimeStamp].getESDPointList().clear();

            for (int i = 0; i < pntsNum; i += 5)
            {
                QList<QPointF>& list = m_Control->getAutoEFData()[m_FrameTimeStamp].getESDPointList();
                list.append(QPointF(pnts[i * 2], pnts[i * 2 + 1]));
            }

            m_Control->getAutoEFData()[m_FrameTimeStamp].setImageWidth(m_Image.width());
        }
        return true;
    }
    else
    {
        return false;
    }
}

void AutoEFTraceMeasureRuler::startRTMeasureForGlyhs(const QString& filePath)
{
    QFileInfo info(filePath);
    QString rtimtFileName(info.path() + "/" + info.baseName() + "/" + "autoefglyhs.bin");

    if (!QFile::exists(rtimtFileName) || m_Control == nullptr)
    {
        return;
    }

    m_Control->getAutoEFData().clear();
    QFile file(rtimtFileName);
    if (file.open(QIODevice::ReadOnly))
    {
        QDataStream in(&file);
        quint32 size;
        // QString id;
        in >> m_MeasurementId;
        in >> size;

        qint64 key;
        AutoEFData value;
        for (quint32 i = 0; i < size; ++i)
        {
            in >> key >> value;
            m_Control->getAutoEFData().insert(key, value);
        }
        in >> m_Control->getAutoEFResult();
    }
    file.close();

    if (m_Control->getAutoEFResult().size() > 0)
    {
        QList<qreal> res = m_Control->getAutoEFResult().value(m_Control->getAutoEFResult().firstKey());
        m_vEDV = res[0];
        m_vESV = res[1];
    }

    onCreateGlyphs();
    m_Context->setAutoEFHasResult(true);
    m_IsFinished = true;
    m_IsClicked = false;
    m_IsRefreshed = true;
}

void AutoEFTraceMeasureRuler::doSavedCineForGlyhs(const QString& fileName)
{
    QFileInfo info(fileName);
    QString rtimtFileName(info.path() + "/" + info.baseName() + "/" + "autoefglyhs.bin");
    bool ret = false;
    QFile file(rtimtFileName);
    if (file.open(QFile::WriteOnly | QIODevice::Text))
    {
        QDataStream out(&file);
        out << m_Measurement->id();
        out << m_Control->getAutoEFData().size();

        // 写入哈希的每一个键值对
        for (QMap<qint64, AutoEFData>::iterator iter = m_Control->getAutoEFData().begin();
             iter != m_Control->getAutoEFData().end(); iter++)
        {
            out << iter.key() << iter.value();
        }
        out << m_Control->getAutoEFResult();

        ret = out.status() == QDataStream::Ok;
    }

    Util::fSync(file);
    file.close();

    if (!ret)
    {
        QFile::remove(rtimtFileName);
    }
}

void AutoEFTraceMeasureRuler::setActivePartitionChanged(QVariant value)
{
    int activeB = m_Control->getActiveB();
    m_Context->setLastLayoutForAutoEF(activeB);
    m_Control->setActiveB(value.toInt());
    m_Context->setCurLayoutForAutoEF(value.toInt());
    qint64 curTimeStamp = 0;

    switch (value.toInt())
    {
    case Layout_1x1_1:
        curTimeStamp = (activeB == Layout_1x2_1 ? m_Context->autoEFEDCurIndex() : m_Context->autoEFESCurIndex());
        break;
    case Layout_1x2_1:
        curTimeStamp = m_Context->autoEFEDCurIndex();
        break;
    case Layout_1x2_2:
        curTimeStamp = m_Context->autoEFESCurIndex();
        break;
    }

    if (curTimeStamp < 0)
    {
        curTimeStamp = 0;
    }

    setFrameTimeStamp(curTimeStamp);
    m_Control->setCurrentTimeStamp(curTimeStamp);

    m_Control->updateLimitRect();
}

void AutoEFTraceMeasureRuler::slotIsClickedAutoEF()
{
    m_CurrentIndex = -1;
    m_IsFinished = false;
    m_IsClicked = true;
    if (m_Control != nullptr)
    {
        //先清除其他测量图元
        m_Control->resetData();
        onCreateGlyphs();
        m_Control->setIsClickedAutoEF(true);
    }
}

void AutoEFTraceMeasureRuler::onChange2OneLayout(int oldLayout)
{
    qint64 index = m_Context->getFramestampByIndex(m_Context->currentIndex());
    if (oldLayout == Layout_1x2_1)
    {
        index = m_Context->autoEFEDCurIndex();
    }
    else if (oldLayout == Layout_1x2_2)
    {
        index = m_Context->autoEFESCurIndex();
    }

    if (index >= m_Context->getFramestampByIndex(m_startIndex) && index <= m_Context->getFramestampByIndex(m_endIndex))
    {
        m_Control->setOneMeasureVisible();
    }
    else
    {
        m_Control->setTwoMeasureVisible(false);
    }

    m_Control->setActiveB(Layout_1x1_1);
    m_Control->drawPath(index);
}

void AutoEFTraceMeasureRuler::onUpdateRightLayout()
{
    int index = m_Context->getFrameIndexByFrameTimestamp(m_Context->autoEFESCurIndex());
    drawImageAndPath(Partition_Right, m_Context->autoEFESCurIndex(), Layout_1x2_2, index);
}

void AutoEFTraceMeasureRuler::onUpdateLeftLayout()
{
    int index = m_Context->getFrameIndexByFrameTimestamp(m_Context->autoEFEDCurIndex());
    drawImageAndPath(Partition_Left, m_Context->autoEFEDCurIndex(), Layout_1x2_1, index);
}

void AutoEFTraceMeasureRuler::onCreateGlyphs()
{
    //播放过程中点击clear需要重新绘制图元
    if (m_Control->measureIsNull())
    {
        m_Control->createDefaultGlyphs();
    }
}

void AutoEFTraceMeasureRuler::onUpdateActiveLayout()
{
    qint64 curTimeStamp = 0;
    CurLayout layout = (CurLayout)m_Context->autoEFActiveLayout();
    int curIndex = -1;
    if (m_Context->autoEFActiveLayout() == Layout_1x2_1)
    {
        curIndex = m_Context->getFrameIndexByFrameTimestamp(m_Context->autoEFEDCurIndex());
        curTimeStamp = m_Context->autoEFEDCurIndex();
    }
    else if (m_Context->autoEFActiveLayout() == Layout_1x2_2)
    {
        curIndex = m_Context->getFrameIndexByFrameTimestamp(m_Context->autoEFESCurIndex());
        curTimeStamp = m_Context->autoEFESCurIndex();
    }

    m_Context->setImageRenderPartition(m_Context->autoEFActiveLayout());
    m_Control->setActiveB(layout);
    m_Context->setCurrentIndex(curIndex, !m_IsEasyView);
    m_Control->setCurrentTimeStamp(curTimeStamp);

    //恢复到默认
    m_Context->setImageRenderPartition(Partition_Default);
}

void AutoEFTraceMeasureRuler::onUpdateUnActiveLayout()
{
    m_Context->drawUnActiveLayout();
}

void AutoEFTraceMeasureRuler::drawAutoEFPath(qint64 timeStamp)
{
    if (m_Control != nullptr && !isStopMeasure())
    {
        m_Control->drawPath(timeStamp);
    }
}

int AutoEFTraceMeasureRuler::calcRWaveList(int frameIndex)
{
    QByteArray data = m_Context->getWholeFrameData(WAVE, frameIndex);
    int num = data.size();

    QList<int> rwaveList;

#ifdef USE_PWTRACE
    uchar* array = new uchar[num];
    if (ECGAnalysis((unsigned char*)data.data(), num, array, 0.0f) == 0)
    {
        for (int i = 0; i < num; i++)
        {
            if ((int)array[i] == RWave)
            {
                rwaveList.append(i);
            }
        }
    }
    delete[] array;
#endif

    int size = rwaveList.size();

    if (size >= 2)
    {
        int gap = 0;
        if (rwaveList[size - 1] >= frameIndex)
        {
            gap = num - rwaveList[size - 1];
        }
        else
        {
            if (frameIndex < num)
            {
                gap = frameIndex - rwaveList[size - 1];
            }
            else
            {
                gap = num - rwaveList[size - 1];
            }
        }

        QList<int> gapList;
        for (int i = 1; i < size; i++)
        {
            gapList.prepend(rwaveList[i] - rwaveList[i - 1]);
        }
        int start = frameIndex - gap - gapList[0];
        //总是向前找RWave
        if (start >= 0)
        {
            m_startIndex = start;
            m_endIndex = frameIndex;
        }
        else
        {
            m_startIndex = m_endIndex = -1;
        }
    }
    else
    {
        m_startIndex = m_endIndex = -1;
    }
    return num;
}

void AutoEFTraceMeasureRuler::onCurrentIndexChanged(int index)
{
    qint64 timeStamp = m_Context->getFramestampByIndex(index);
    setFrameTimeStamp(timeStamp);
    m_Control->setCurrentTimeStamp(timeStamp);
    emit drawPath(timeStamp);
}
