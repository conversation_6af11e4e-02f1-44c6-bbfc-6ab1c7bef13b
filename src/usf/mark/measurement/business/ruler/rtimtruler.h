#ifndef RTIMTRULER_H
#define RTIMTRULER_H

#include "usfmeasurementbusiness_global.h"
#include "alginstance.h"
#include "baseruler.h"
#include "qdebug.h"
#include "resource.h"
#include "rtimtglyphscontrol.h"
#include <QImage>
#include <QPainterPath>
#include <QThread>
#include <QTimer>

class AlgThread;
class RtimtAlg;
class InitThread : public QThread
{
    Q_OBJECT
    friend class RTIMTRuler;

public:
    InitThread(QObject* parent = 0)
        : QThread(parent)
        , width(0)
        , height(0)
    {
        m_RtimtAlg = AlgInstance::algInstance().getRtimtAlg();
    }

signals:
    void initFinish();

protected:
    void run()
    {
        m_RtimtAlg->removeViewontextRtimt();
        QString mnn_path = Resource::mnnDir() + "/sinet_rtimt_512_384.mnn";
        m_RtimtAlg->createViewontextRtimt(mnn_path.toLatin1(), width, height);
        emit initFinish();
    }

private:
    int width;
    int height;
    RtimtAlg* m_RtimtAlg;
};

class ImtCurve;
class ImtCurveData;
class USF_MARK_MEASUREMENT_BUSINESS_EXPORT RTIMTRuler : public BaseRTImageRuler
{
    Q_OBJECT
public:
    RTIMTRuler(GlyphsCtl::ControlType type, QObject* parent = 0);
    virtual ~RTIMTRuler();
    virtual bool canRun();
    virtual void start();
    virtual void onRun();
    virtual void onStop();
    /**
     * @brief refreshGlyphs
     * 重新计算测量结果，刷新测量线
     */
    void refreshGlyphs();
    void refreshGlyphsOnFreeze();
    void clearGlyphs();
    void clearDatas();
    /**
     * @brief restoreActiveGlyphs 暂时恢复图元
     */
    void restoreActiveGlyphs();
    /**
     * @brief resetActiveGlyphs 重置清除图元
     */
    void resetActiveGlyphs();
    bool isCurrentImageEmpty();
    void geneCurrentImage(QImage& image);
    void setIsNeedMouse(bool isNotNeedMouse);
    void setFrameIndex(const int frameIndex);
    int getFrameIndex() const;
    void setFrontIndex(int);
    int getFrontIndex();
    void updateMaxFrameGlyphs(int, int, int, int, QPainterPath, QPainterPath, QPainterPath, QPainterPath, QPointF,
                              qint64);
    RTIMTGlyphsControl* getControl()
    {
        return m_Control;
    }
    void setControl(RTIMTGlyphsControl*);
    void easyViewRunAlg();
    void runInTread();
    void setSonoParameters(SonoParameters* sonoParameters);
    void startRTMeasure(const QString& filePath);
    void doSavedCine(const QString& fileName);
    void setIsRealTime(bool isRealTime);
    /**
     * @brief clearFileResult
     *
     * 谨慎调用
     */
    void clearFileResult();

public:
    AlgThread* m_AlgThread;
    MaxFrameGlData getMaxGlData() const;
    qint64 getMaxTimeStamp();
public slots:
    void showGraphicsPaths();
    void initFinish();

protected:
    virtual bool calcResult(QGraphicsItem* glyph, QPointF currentCursorPos);

private:
    /**
     * 生成自动ROI框
     * @param x_center_still
     * @param y_begin_still
     * @param y_end_still
     */
    QRect createAutoRoi(int width, int height, int x_center_still, int y_begin_still, int y_end_still);
    /**
     * 返回一个默认roi宽，百分比格式
     * @param imgHeight 图像区高度
     * @return
     */
    QRectF getDefaultRioRect(int imgWidth, int imgHeight);
    void loadTestImageFile();
    void calcImtMeasure();
    void useAPI_createRTImtContext();
    void useAPI_getMarginValues();
    void useAPI_rtImtMeasure();
    void useAPI_rtImtRoiCalc();
    void useAPI_rtImtCycle();
    void useAPI_removeRTImtContext();
    void updateResultData();
    void updateRulerResult();
    void updateRulerResult(QList<qreal>& resultList);
    void recordDataForCompare(int, int, int, int, QPainterPath, QPainterPath, QPainterPath, QPainterPath, QPointF,
                              ImtCurve*);
    void checkFileResult();
    void calcOneFrameData(QPainterPath antIntimaPathPoints, QPainterPath antAdventitiaPathPoints,
                          QPainterPath postIntimaPathPoints, QPainterPath postAdventitiaPathPoints);
    void updateCycleAntList(qreal data);
    void updateCyclePostList(qreal data);
    void updateCycleAntData(int index, qreal data);
    void updateCyclePostData(int index, qreal data);
    //计算IMT最大帧是哪一帧，需求需要冻结后图像跳转到这个最大帧
    void calcMaxDataFrame(QList<qreal>& dataList, QList<int>& frameList, QList<int>& frontList);
    void updateCycle1PostData(qreal data);
    void updateCycle1AntData(qreal data);
    void updateCycle2PostData(qreal data);
    void updateCycle2AntData(qreal data);
    void updateCycle3PostData(qreal data);
    void updateCycle3AntData(qreal data);
    void updateCycle4PostData(qreal data);
    void updateCycle4AntData(qreal data);
    void updateCycle5PostData(qreal data);
    void updateCycle5AntData(qreal data);
    void updateCycle6PostData(qreal data);
    void updateCycle6AntData(qreal data);
    void updateMeanPostData(qreal data);
    void updateMeanAntData(qreal data);
    void updateMaxPostData(qreal data);
    void updateMaxAntData(qreal data);
    void updateSDPostData(qreal data);
    void updateSDAntData(qreal data);
    void update_last_origin(int* input_array, int length, int new_value);
    void update_last_origin(float* input_array, int length, float new_value);
    void update_last(int* input_array, int length, int new_value);
    void update_last(float* input_array, int length, float new_value);
    void update_last(double* input_array, int length, double new_value);
    // mode=1为取input和boudary的较小值，mode=0为取input和boundary的较大值
    int constraint(int input, int boundary, int mode);
    qreal getMaxValue(QList<qreal>& list);
    qreal getMeanValue(QList<qreal>& list);
    qreal getSdValue(QList<qreal>& list);
    void initAPIPreData();
    void deleteAPIPreData();
    void drawMaxFrameGl(ImtCurve*);
    void setInitValue(qreal value);
    RTIMTGlyphsControl* getGlyphsControl();

    inline bool hasValidValue(QList<qreal> resultList)
    {
        for (int i = 0; i < resultList.count(); i++)
        {
            if (resultList[i] > 0)
                return true;
        }
        return false;
    }

    SonoParameters* m_SonoParameters{nullptr};
    float m_DefaultRoiWidth{0.0f};  //默认框宽
    float m_DefaultRoiHeight{0.0f}; //默认框高
    const qreal INIT_RECT_WIDTH_MM{15.0f};
    const qreal INIT_RECT_HEIGHT_MM{10.0f};
    int m_FrameIndex;
    int m_FrontIndex;
    bool m_InitedAI;
    QTimer m_Timer;
    bool m_AutoRoi;
    QList<qreal> oneCycleDataListAnt;
    QList<qreal> oneCycleDataListPost;
    QList<qreal> m_CycleAntList;
    QList<qreal> m_CyclePostList;
    QList<int> oneCycleFrameListAnt;
    QList<int> oneCycleFrameListPost;
    QList<int> oneCycleFrontListPost;
    QList<int> oneCycleGlyphs_xLeftPos_List;
    QList<int> oneCycleGlyphs_yTopPos_List;
    QList<int> oneCycleGlyphs_width_List;
    QList<int> oneCycleGlyphs_height_List;
    QList<QPainterPath> oneCycleGlyphs_ant_intimaPath_List;
    QList<QPainterPath> oneCycleGlyphs_ant_adventitiaPath_List;
    QList<QPainterPath> oneCycleGlyphs_post_intimaPath_List;
    QList<QPainterPath> oneCycleGlyphs_post_adventitiaPath_List;
    QList<QPointF> oneCycleGlyphs_ant_intimaFirstPt_List;
    QList<ImtCurveData> oneCycleGlyphs_ImtCurveData_List;
    QList<qint64> oneCycleGlyphs_TimeStampData_List;
    bool m_UpdateCycle;
    qreal m_Cycle1PostData;
    qreal m_Cycle1AntData;
    qreal m_Cycle2PostData;
    qreal m_Cycle2AntData;
    qreal m_Cycle3PostData;
    qreal m_Cycle3AntData;
    qreal m_Cycle4PostData;
    qreal m_Cycle4AntData;
    qreal m_Cycle5PostData;
    qreal m_Cycle5AntData;
    qreal m_Cycle6PostData;
    qreal m_Cycle6AntData;
    qreal m_MeanPostData;
    qreal m_MeanAntData;
    qreal m_MaxPostData;
    qreal m_MaxAntData;
    qreal m_SDPostData;
    qreal m_SDAntData;
    bool m_UseRealData;
    bool m_RealDataCanSend;
    QImage m_CurrentImage;
    int x_center_still;
    int y_begin_still;
    int y_end_still;
    int file_idx;
    int interval;
    int duration_determine;
    int* p_middle_ys_diff;
    float* angles_diff;
    int* x_centers;
    int* y_begins;
    int* y_ends;
    int duration_roi_calculate;
    int use_still_roi;
    int change_still_roi;
    int num_frames_since_still;
    int left_margin_x;
    int right_margin_x;
    int lower_margin_y;
    int upper_margin_y;
    qreal m_MaxData;
    double* lumen_diameters;
    int duration_lumen_diameter_compute;
    IMOutput im_output;
    IMOutput im_output1;
    IMCycle cycle;
    IMCycle cycle1;
    IMRoibox roibox;
    // 2023-05-10,这里必须多定义一份IMRoibox,不然会崩溃报错*** stack smashing detected ***: terminated，
    // 2023-05-10,这个API出现 内存地址越界写 的问题。已反馈给算法组，目前没有给出解决方案。
    IMRoibox roibox_magic;
    int* p_middle_ys;
    float* angles;
    int m_MagicValue{0};
    QList<QImage> m_TestImageList;
    int m_TestImageIndex;
    bool m_Init{false};
    RTIMTGlyphsControl* m_Control{NULL};
    InitThread m_InitThread;
    QPointF m_LastPos;
    int m_LoadIndex{0};
    RtimtAlg* m_RtimtAlg;
};

class AlgThread : public QThread
{
    Q_OBJECT
public:
    AlgThread(QObject* parent = 0)
        : QThread(parent)
        , ruler(nullptr)
    {
    }

    void setRuler(RTIMTRuler* value);

signals:
    void initFinish();

protected:
    void run()
    {
        ruler->easyViewRunAlg();
    }
    RTIMTRuler* ruler;
};

#endif // RTIMTRULER_H
