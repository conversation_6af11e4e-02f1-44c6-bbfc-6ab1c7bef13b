/*
 * =====================================================================================
 *
 *       Filename:  AutoEFTraceMeasureRuler.h
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  2024年02月20日 15时35分27秒
 *       RevIsion:  none
 *       Compiler:  gcc
 *
 *         Author:  PengFangMei (), <EMAIL>
 *   Organization:
 *
 * =====================================================================================
 */

#ifndef AUTOEF_TRACE_MEASURE_RULER_H

#define AUTOEF_TRACE_MEASURE_RULER_H

#include "usfmeasurementbusiness_global.h"
#include "autoefdata.h"
#include "autoefmeasureglyphscontrol.h"
#include "baseruler.h"
#include "qtimer.h"
#include "statefilterlocker.h"
#include <QImage>
#include <QLineF>
#include "alginstance.h"
#include "infostruct.h"

class QPainterPath;
class ImageEventArgs;
class USF_MARK_MEASUREMENT_BUSINESS_EXPORT AutoEFTraceMeasureRuler : public BaseImageRuler
{
    Q_OBJECT
public:
    enum FrameType
    {
        TWOD = 0,  // B + C
        WAVE = 1,  //波形
        SOUND = 2, //声音
    };
    /**
     * @brief ECG波峰枚举
     */
    enum ECGWaveEnum
    {
        NWave = 0,
        PWave = 1,
        QWave = 2,
        RWave = 3,
        SWave = 4,
        TWave = 5
    };
    typedef QList<QVariant> RadiusArray;
    explicit AutoEFTraceMeasureRuler(GlyphsCtl::ControlType type, QObject* parent = 0);

    virtual ~AutoEFTraceMeasureRuler();

    virtual void initGlyphsControl();

    virtual void onStop();

    virtual void onRun();
    /**
     * @brief 过算法，获取A4C/A2C的path
     *
     * @param curIndex  当前电影帧下标
     * @param img 当前电影图像。
     *
     * @return
     */
    bool getPath(const QImage& img);
    void doSavedCineForGlyhs(const QString& fileName);
    void startRTMeasureForGlyhs(const QString& filePath);
    qint64 getCurrentTimeStamp() const;

    bool getIsFinished() const;
    void setIsFinished(bool IsFinIshed);
    void setResult();
    int initLVDetect();
    void updateSVIndex(AutoEFData::ResultType, qint64 index);
    void setControl(AutoEFMeasureGlyphsControl*);
    void setIsEasyView(bool IsEasyView);
    bool calcEFIndex();
    void disconnectSignals();
    void setIsSupport(bool IsSupport);

    QString getMeasurementId() const;

    void createGlyphsControl();

    void endMeasure(const QString content);

    virtual bool canRun();
    int calcRWaveList(int frameIndex);

    void setIsCompleted(bool IsCompleted);

public slots:
    void onNewImage(ImageEventArgs* imageEventArgs);
    void onCallBackNewImage(ImageEventArgs* imageEventArgs);
    void onFinished();
    void onDrawPartitionLeft();
    void onUpdateESVIndex(qint64 index);
    void onUpdateEDVIndex(qint64 index);
    void onChangeFrameIndex(qint64 index);
    void AutoEFCompleted();
    void setActivePartitionChanged(QVariant value);
    void slotIsClickedAutoEF();
    //双幅切到单幅,画当前帧图元
    void onChange2OneLayout(int oldLayout);
    //回调AutoEF双幅图片时,刷右边图像,画右边图元
    void onUpdateRightLayout();
    //回调AutoEF双幅图片时,刷左边图像,画左边图元
    void onUpdateLeftLayout();
    //回调, 创建图元对象
    void onCreateGlyphs();

    void onUpdateActiveLayout();
    void onUpdateUnActiveLayout();

    void drawAutoEFPath(qint64);

    void onCurrentIndexChanged(int index);

protected:
    virtual bool calcResult(QGraphicsItem* glyph, QPointF currentCursorPos);

private:
    //算法重置
    void resetAlgorithm();
    void setCurActiveIndex(qint64 index);
    void drawImageAndPath(int pation, qint64 timeStmp, CurLayout activeLayout, int curIndex = -1);

private:
    AutoEFMeasureGlyphsControl* m_Control{nullptr};
    float m_vEDV, m_vESV, m_vEF;
    int m_startIndex{-1};
    int m_endIndex{-1};
    int m_EDIndex{-1};
    int m_ESIndex{-1};
    bool m_IsFinished{false};
    int m_CurrentIndex{-1};
    // add for dIstinguIsh click or callback
    bool m_IsClicked{false};
    // add for in easyview setcurrentindex not to update freeze bar
    bool m_IsEasyView{false};
    // add for avoid callback pic and clicked will changed image
    bool m_IsSupport{false};
    // add for dIstinguIsh A4C or A2C
    QString m_MeasurementId;
    // add for ignore post event while running for click
    StateFilterLocker* m_NotingState{nullptr};
    AutoEfAlg* m_AutoEf{nullptr};
    // add for ecg
    QImage m_Image;
    QList<int> m_FrameIndexList;
    //识别完成后双幅图像刷新结束
    bool m_IsRefreshed{false};
    bool m_IsLeftRefreshed{false};
    bool m_IsRightRefreshed{false};
    // autoef complete
    bool m_IsCompleted{false};
};

#endif /* end of include guard: A4C_TRACE_MEASURE_RULER_H */
