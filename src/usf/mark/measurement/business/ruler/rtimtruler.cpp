#include "rtimtruler.h"
#include "bfpnames.h"
#include "imtcurve.h"
#include "measurecontext.h"
#include "measurementdef.h"
#include "rtimtglyphscontrol.h"
#include "setting.h"
#include "util.h"
#include <QDir>
#include <QElapsedTimer>
#include <QImage>
#include <QTime>
#include <math.h>
#include "glyphscontrolmanager.h"
#include "messageboxframe.h"

RTIMTRuler::RTIMTRuler(GlyphsCtl::ControlType type, QObject* parent)
    : BaseRTImageRuler(type, parent)
    , x_center_still(-1)
    , y_begin_still(-1)
    , y_end_still(-1)
    , file_idx(0)
    , interval(5)
    , duration_determine(2)
    , p_middle_ys_diff(NULL)
    , angles_diff(NULL)
    , x_centers(NULL)
    , y_begins(NULL)
    , y_ends(NULL)
    , duration_roi_calculate(5)
    , use_still_roi(0)
    , change_still_roi(0)
    , num_frames_since_still(0)
    , left_margin_x(0)
    , right_margin_x(0)
    , lower_margin_y(0)
    , upper_margin_y(0)
    , lumen_diameters(NULL)
    , duration_lumen_diameter_compute(3)
    , p_middle_ys(NULL)
    , angles(NULL)
    , m_TestImageIndex(0)
    , m_Cycle1PostData(0)
    , m_Cycle1AntData(0)
    , m_Cycle2PostData(0)
    , m_Cycle2AntData(0)
    , m_Cycle3PostData(0)
    , m_Cycle3AntData(0)
    , m_Cycle4PostData(0)
    , m_Cycle4AntData(0)
    , m_Cycle5PostData(0)
    , m_Cycle5AntData(0)
    , m_Cycle6PostData(0)
    , m_Cycle6AntData(0)
    , m_MeanPostData(0)
    , m_MeanAntData(0)
    , m_MaxPostData(0)
    , m_MaxAntData(0)
    , m_SDPostData(0)
    , m_SDAntData(0)
    , m_UseRealData(false)
    , m_AutoRoi(true)
    , m_UpdateCycle(false)
    , m_RealDataCanSend(false)
    , m_FrameIndex(-1)
    , m_FrontIndex(-1)
    , m_MaxData(0)
{
    m_Timer.setSingleShot(true);
    m_Timer.setInterval(3000);
    connect(&m_Timer, SIGNAL(timeout()), this, SLOT(showGraphicsPaths()));

    setIsAutoAlgrithm(true);

    loadTestImageFile();
    initAPIPreData();
    m_Id = Measurement::Ruler_RTIMT;
    m_Type = Measurement::RulerType_RTIMT;

    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle1_Post);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle1_Post]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle1_Ant);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle1_Ant]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle2_Post);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle2_Post]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle2_Ant);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle2_Ant]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle3_Post);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle3_Post]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle3_Ant);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle3_Ant]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle4_Post);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle4_Post]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle4_Ant);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle4_Ant]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle5_Post);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle5_Post]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle5_Ant);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle5_Ant]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle6_Post);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle6_Post]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Cycle6_Ant);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Cycle6_Ant]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Mean_Post);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Mean_Post]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Mean_Ant);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Mean_Ant]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Max_Post);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Max_Post]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_Max_Ant);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_Max_Ant]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_SD_Post);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_SD_Post]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }
    {
        RulerResultItem item;
        item.setId(Measurement::RulerResult_RTIMT_SD_Ant);
        item.setName(Measurement::RulerResultText[Measurement::RulerResult_RTIMT_SD_Ant]);
        item.setUnit(Measurement::Unit_mm);
        item.setUnitType(Measurement::UnitType_Distance);
        m_Result.append(item);
    }

    m_ClearRunAgain = true;

    m_AlgThread = new AlgThread();
    m_AlgThread->setRuler(this);

    m_RtimtAlg = AlgInstance::algInstance().getRtimtAlg();
}

RTIMTRuler::~RTIMTRuler()
{
    deleteAPIPreData();
    if (m_RtimtAlg->isInitialized())
    {
        useAPI_removeRTImtContext();
    }
}

bool RTIMTRuler::canRun()
{
    bool isSingleB = m_SonoParameters->pIV(BFPNames::LayoutStr) == Layout_1x1 &&
                     m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanModeB;

    if (!isSingleB && m_Control->isRealTime())
    {
        MessageBoxFrame::tipWarningNonModal(tr("Please measure in B mode!"));
    }

    return isSingleB || !m_Control->isRealTime();
}

void RTIMTRuler::start()
{
    resetResult();
    onRun();

    if (m_Control != nullptr)
    {
        m_Control->setLimitImageType(m_ImageType);
        m_Control->setCanMeasInMultiRegion(m_Context->isMultiBImageParaEqual());
        m_Control->setLimitStyle(GlyphsControl::UNION_IMAGE_TYPE);
        m_Control->setDefaultRoiSize(INIT_RECT_WIDTH_MM / m_Context->pixelSizeMM(),
                                     INIT_RECT_HEIGHT_MM / m_Context->pixelSizeMM());
        m_AutoRoi = true;
        initGlyphsControl();
    }
}

void RTIMTRuler::onRun()
{
    clearDatas();
    //这个算法的初始化比较耗时,放在线程中并且只初始化一次.
    //还要避免重复初始化导致线程重入
    if (!m_RtimtAlg->isInitialized() && !m_RtimtAlg->isBeginInit())
    {
        useAPI_createRTImtContext();
    }

    setInitValue(0.0);
    BaseRTImageRuler::onRun();
}

void RTIMTRuler::onStop()
{
    BaseRTImageRuler::onStop();
    if (m_RtimtAlg->isInitialized())
    {
        useAPI_removeRTImtContext();
    }
}

void RTIMTRuler::refreshGlyphs()
{
    if (NULL == m_Control)
    {
        return;
    }
    if (NULL == m_Control->activeGlyphs())
    {
        if (m_Context->isFreeze())
        {
            m_Control->setIsRealTime(false);
        }
        m_Control->createDefaultROI(m_Control->defaultPos());
        m_AutoRoi = true;
    }

    //编辑状态不走算法
    if (m_Control->isEditStatus())
    {
        m_AutoRoi = false;
        m_Control->setAIText(ImtCurve::AI_Manual);
        m_Timer.start();
        return;
    }

    //手动移动状态 走算法 启动timer
    if (m_Control->isMoving())
    {
        m_AutoRoi = false;
        m_Control->setAIText(ImtCurve::AI_Manual);
        m_Timer.start();
    }
    m_Control->setIsMoving(false);

    calcImtMeasure();
    m_Control->upateImtGlyphs();
    m_Control->storeActiveGlyphs();
}

void RTIMTRuler::refreshGlyphsOnFreeze()
{
    if (NULL == m_Control)
    {
        return;
    }

    refreshGlyphs();
}

void RTIMTRuler::clearGlyphs()
{
    if (NULL == m_Control)
    {
        return;
    }

    m_Control->clearActiveGlyphs();
}

void RTIMTRuler::clearDatas()
{
    m_MagicValue = 0;
    m_MaxData = 0;
    m_FrameIndex = -1;
    m_FrontIndex = -1;
    m_CycleAntList.clear();
    m_CyclePostList.clear();
    oneCycleDataListAnt.clear();
    oneCycleDataListPost.clear();
    oneCycleFrameListAnt.clear();
    oneCycleFrameListPost.clear();
    oneCycleFrontListPost.clear();
    m_Cycle1PostData = 0;
    m_Cycle1AntData = 0;
    m_Cycle2PostData = 0;
    m_Cycle2AntData = 0;
    m_Cycle3PostData = 0;
    m_Cycle3AntData = 0;
    m_Cycle4PostData = 0;
    m_Cycle4AntData = 0;
    m_Cycle5PostData = 0;
    m_Cycle5AntData = 0;
    m_Cycle6PostData = 0;
    m_Cycle6AntData = 0;
    m_MeanPostData = 0;
    m_MeanAntData = 0;
    m_MaxPostData = 0;
    m_MaxAntData = 0;
    m_SDPostData = 0;
    m_SDAntData = 0;
    oneCycleGlyphs_xLeftPos_List.clear();
    oneCycleGlyphs_yTopPos_List.clear();
    oneCycleGlyphs_width_List.clear();
    oneCycleGlyphs_height_List.clear();
    oneCycleGlyphs_ant_intimaPath_List.clear();
    oneCycleGlyphs_ant_adventitiaPath_List.clear();
    oneCycleGlyphs_post_intimaPath_List.clear();
    oneCycleGlyphs_post_adventitiaPath_List.clear();
    oneCycleGlyphs_ant_intimaFirstPt_List.clear();
    oneCycleGlyphs_ImtCurveData_List.clear();
    oneCycleGlyphs_TimeStampData_List.clear();
    if (m_Control != NULL && m_Control->isRealTime())
    {
        file_idx = 0;
        duration_lumen_diameter_compute = 3;
    }
    else
    {
        file_idx = interval + duration_determine;
        duration_lumen_diameter_compute = 1;
    }
    if (m_Control != NULL)
    {
        m_Control->clearDatas();
    }
}

void RTIMTRuler::restoreActiveGlyphs()
{
    if (m_Control != NULL)
    {
        m_Control->restoreCurrentActiveGlyphs();
    }
}

void RTIMTRuler::resetActiveGlyphs()
{
    if (m_Control != NULL)
    {
        m_Control->resetActiveGlyphs();
    }
}

bool RTIMTRuler::isCurrentImageEmpty()
{
    return m_Context->bImage().isNull();
}

void RTIMTRuler::geneCurrentImage(QImage& image)
{
    image = m_Context->bImage();
}

void RTIMTRuler::setIsNeedMouse(bool isNeedMouse)
{
    if (m_Control != NULL)
    {
        m_Control->setIsNeedMouse(isNeedMouse);
    }
}

void RTIMTRuler::setFrameIndex(const int frameIndex)
{
    m_FrameIndex = frameIndex;
}

int RTIMTRuler::getFrameIndex() const
{
    return m_FrameIndex;
}

void RTIMTRuler::setFrontIndex(int front)
{
    m_FrontIndex = front;
}

int RTIMTRuler::getFrontIndex()
{
    return m_FrontIndex;
}

void RTIMTRuler::updateMaxFrameGlyphs(int xLeftPos, int yTopPos, int width, int height, QPainterPath ant_intimaPath,
                                      QPainterPath ant_adventitiaPath, QPainterPath post_intimaPath,
                                      QPainterPath post_adventitiaPath, QPointF ant_intimaFirstPt, qint64 timeStamp)
{
    if (m_Control != NULL)
        m_Control->m_MaxGlData.setMaxData(xLeftPos, yTopPos, width, height, ant_intimaPath, ant_adventitiaPath,
                                          post_intimaPath, post_adventitiaPath, ant_intimaFirstPt, timeStamp);
}

void RTIMTRuler::setControl(RTIMTGlyphsControl* control)
{
    m_Control = control;
}

void RTIMTRuler::easyViewRunAlg()
{
    static int count = 0;
    if (count >= 0)
    {
        count = 0;
        calcImtMeasure();
        m_Control->upateImtGlyphs();
    }
    else
    {
        count++;
    }
}

void RTIMTRuler::runInTread()
{
    m_AlgThread->start();
}

void RTIMTRuler::setSonoParameters(SonoParameters* sonoParameters)
{
    m_SonoParameters = sonoParameters;
    getGlyphsControl()->setSonoParameters(sonoParameters);
}

void RTIMTRuler::startRTMeasure(const QString& filePath)
{
    m_Control->m_FileResultMap.clear();
    m_Control->m_MaxGlData.resetGlData();
    QFileInfo info(filePath);
    QString rtimtFileName(info.path() + "/" + info.baseName() + "/" + "rtimt.bin");

    if (!QFile::exists(rtimtFileName))
    {
        return;
    }

    QFile file(rtimtFileName);
    if (file.open(QIODevice::ReadOnly))
    {
        QDataStream in(&file);
        in >> m_Control->m_FileResultMap;
        in >> m_Control->m_MaxGlData;
        if (in.status() != QDataStream::Ok)
        {
            m_Control->m_FileResultMap.clear();
        }
    }
    file.close();

    return;
}

void RTIMTRuler::doSavedCine(const QString& fileName)
{
    QFileInfo info(fileName);
    QString rtimtFileName(info.path() + "/" + info.baseName() + "/" + "rtimt.bin");
    bool ret = false;
    QFile file(rtimtFileName);
    if (file.open(QFile::WriteOnly | QFile::Truncate))
    {
        QDataStream out(&file);
        out << m_Control->m_FileResultMap;
        out << m_Control->m_MaxGlData;
        ret = out.status() == QDataStream::Ok;
    }

    Util::fSync(file);
    file.close();

    if (!ret)
    {
        QFile::remove(rtimtFileName);
    }
}

void RTIMTRuler::setIsRealTime(bool isRealTime)
{
    m_Control->setIsRealTime(isRealTime);
}

void RTIMTRuler::clearFileResult()
{
    m_Control->m_FileResultMap.clear();
}

void RTIMTRuler::showGraphicsPaths()
{
    m_AutoRoi = true;
    if (m_Control != NULL)
    {
        m_Control->showGraphicsPaths(true);
        m_Control->setAIText(ImtCurve::AI_Auto);
        m_Control->setIsRealTimeMove(true);
        m_Control->setIsMoving(false);
    }
}

void RTIMTRuler::initFinish()
{
    m_MagicValue = 0;
}

bool RTIMTRuler::calcResult(QGraphicsItem* glyph, QPointF currentCursorPos)
{
    Q_UNUSED(glyph)
    Q_UNUSED(currentCursorPos)
    return true;
}

QRect RTIMTRuler::createAutoRoi(int width, int height, int x_center_still, int y_begin_still, int y_end_still)
{
    int halfWidth = (int)(m_DefaultRoiWidth / 2 * width);
    int halfHeight = (int)(m_DefaultRoiHeight / 2 * height);
    int yCenterStill = (y_begin_still + y_end_still) / 2;

    int left_bbox_x = constraint(x_center_still - halfWidth, 15, 0); // 0取较大值，1取较小值
    int right_bbox_x = constraint(x_center_still + halfWidth, width - 15, 1);
    int lower_bbox_y = constraint(yCenterStill - halfHeight, 15, 0);
    int upper_bbox_y = constraint(yCenterStill + halfHeight, height - 15, 1);

    return QRect(left_bbox_x, lower_bbox_y, right_bbox_x - left_bbox_x, upper_bbox_y - lower_bbox_y);
}

QRectF RTIMTRuler::getDefaultRioRect(int imgWidth, int imgHeight)
{
    float pixMM = m_Context->pixelSizeMM();
    float imgWidthMM = imgWidth * pixMM;
    float imgHeightMM = imgHeight * pixMM;
    m_DefaultRoiWidth = INIT_RECT_WIDTH_MM / imgWidthMM;
    m_DefaultRoiHeight = INIT_RECT_HEIGHT_MM / imgHeightMM;

    return QRectF(0.5f - m_DefaultRoiWidth / 2, 0.5f - m_DefaultRoiHeight / 2, 0.5f + m_DefaultRoiWidth / 2,
                  0.5f + m_DefaultRoiHeight / 2);
}

qint64 RTIMTRuler::getMaxTimeStamp()
{
    return m_Control->m_MaxGlData.timeStamp();
}

MaxFrameGlData RTIMTRuler::getMaxGlData() const
{
    return m_Control->m_MaxGlData;
}

void RTIMTRuler::loadTestImageFile()
{
    return;
    QDir dir = QDir("./11_every4_960");
    QFileInfoList parentFileList = dir.entryInfoList(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);
    foreach (QFileInfo fileInfo, parentFileList)
    {
        QString fileName = fileInfo.fileName();
        fileName = fileInfo.absoluteFilePath();
        QFile file(fileName);
        if (file.exists())
        {
            QImage img(file.fileName());
            QImage rgb = img.convertToFormat(QImage::Format_RGB888);
            m_TestImageList.append(rgb);
        }
    }
    if (m_TestImageList.count() > 0)
    {
        m_CurrentImage = m_TestImageList.at(0);
    }
}

void RTIMTRuler::calcImtMeasure()
{
    QImage img;

#ifdef SYS_APPLE
    img = m_Context->image();
#else
    img = m_Context->bImage().isNull() ? m_Context->image() : m_Context->bImage();
#endif

    if (img.isNull())
    {
        return;
    }

    m_CurrentImage = img.convertToFormat(QImage::Format_RGB888);
    //    m_CurrentImage = img;
    if (m_TestImageList.count() > 0)
    {
        m_CurrentImage = m_TestImageList.at(m_TestImageIndex);
        m_TestImageIndex++;
        if (m_TestImageIndex >= m_TestImageList.count())
        {
            m_TestImageIndex = 0;
        }
    }

    if (!m_RtimtAlg->isInitialized())
    {
        return;
    }
    getDefaultRioRect(m_CurrentImage.width(), m_CurrentImage.height());
    useAPI_getMarginValues();
    useAPI_rtImtMeasure();
    useAPI_rtImtRoiCalc();
    useAPI_rtImtCycle();

    updateResultData();
    checkFileResult();

    file_idx++;
}

void RTIMTRuler::setInitValue(qreal value)
{
    for (int i = 0; i < m_Result.count(); i++)
    {
        setValue(i, value);
    }
}

RTIMTGlyphsControl* RTIMTRuler::getGlyphsControl()
{
    if (m_Control == NULL)
    {
        m_Control = GlyphsControlManager::instance().getChildGlyphsControl<RTIMTGlyphsControl>(m_GlyphsControlType);
    }

    return m_Control;
}

void RTIMTRuler::useAPI_createRTImtContext()
{
    m_InitThread.width = m_Context->bImage().width();
    m_InitThread.height = m_Context->bImage().height();
    connect(&m_InitThread, SIGNAL(initFinish()), this, SLOT(initFinish()));
    m_InitThread.start();
}

void RTIMTRuler::useAPI_getMarginValues()
{
    left_margin_x = 0;
    right_margin_x = m_CurrentImage.width();
    lower_margin_y = 0;
    upper_margin_y = m_CurrentImage.height();

    m_RtimtAlg->getMarginValues(m_CurrentImage.bits(), m_CurrentImage.width(), m_CurrentImage.height(), left_margin_x,
                                right_margin_x, lower_margin_y,
                                upper_margin_y); //引用传递

    //如果返回margin的值和默认值小于<30，直接使用默认值
    left_margin_x < 30 ? left_margin_x = 0 : left_margin_x;
    m_CurrentImage.width() - right_margin_x < 30 ? right_margin_x = m_CurrentImage.width() : right_margin_x;
    lower_margin_y < 30 ? lower_margin_y = 0 : lower_margin_y;
    m_CurrentImage.height() - upper_margin_y < 30 ? upper_margin_y = m_CurrentImage.height() : upper_margin_y;
}

void RTIMTRuler::useAPI_rtImtMeasure()
{
    m_RtimtAlg->intimaMeasureRtimt(m_CurrentImage.bits(), m_CurrentImage.width(), m_CurrentImage.height(),
                                   left_margin_x, right_margin_x, lower_margin_y, upper_margin_y,
                                   right_margin_x - left_margin_x, upper_margin_y - lower_margin_y, left_margin_x,
                                   lower_margin_y, &im_output);

    if (im_output.x_center_latest == -1 && im_output.y_begin_latest == -1 && im_output.y_end_latest == -1)
    {
        x_center_still = -1;
        y_begin_still = -1;
        y_end_still = -1;
    }

    update_last(x_centers, duration_roi_calculate, im_output.x_center_latest);
    update_last(y_begins, duration_roi_calculate, im_output.y_begin_latest);
    update_last(y_ends, duration_roi_calculate, im_output.y_end_latest);
    update_last(lumen_diameters, duration_lumen_diameter_compute, im_output.upper_lower_diffs_mean);
    update_last_origin(p_middle_ys, (interval + duration_determine), im_output.p_middle_y);
    update_last_origin(angles, (interval + duration_determine), im_output.angle);

    if (file_idx >= interval)
    {
        // 当输入帧数大于interval时，开始更新p_middle_ys_diff，angles_diff
        /*p_middle_ys_diff = update_last(p_middle_ys_diff, duration_determine, im_output.p_middle_y -
        p_middle_ys[duration_determine - 1]);
        angles_diff = update_last(angles_diff, duration_determine, im_output.angle - angles[duration_determine - 1]);*/
        update_last(p_middle_ys_diff, duration_determine, im_output.p_middle_y - p_middle_ys[duration_determine - 1]);
        update_last(angles_diff, duration_determine, im_output.angle - angles[duration_determine - 1]);
    }
}

void RTIMTRuler::useAPI_rtImtRoiCalc()
{
    if (file_idx >= (duration_determine + interval - 1))
    {
        m_RtimtAlg->roibboxCalculateRtimt(p_middle_ys_diff, angles_diff, x_centers, y_begins, y_ends,
                                          duration_determine, duration_roi_calculate, use_still_roi, change_still_roi,
                                          num_frames_since_still, x_center_still, y_begin_still, y_end_still, &roibox);

        use_still_roi = roibox.use_still_roi;
        change_still_roi = roibox.change_still_roi;
        num_frames_since_still = roibox.num_frames_since_still;
        x_center_still = roibox.x_center_still;
        y_begin_still = roibox.y_begin_still;
        y_end_still = roibox.y_end_still;
    }
}

void RTIMTRuler::useAPI_rtImtCycle()
{
    if (num_frames_since_still >= duration_lumen_diameter_compute)
    {
        int local_minmax[10] = {0};
        for (int i = 0; i < 10; i++)
        {
            local_minmax[i] = -1;
            ;
        }

        m_RtimtAlg->cardiacCycleDetection(lumen_diameters, duration_lumen_diameter_compute,
                                          (file_idx - (duration_lumen_diameter_compute - 1)), local_minmax, &cycle);

        lumen_diameters = cycle.lumen_diameters;
        m_UpdateCycle = true;
    }
}

void RTIMTRuler::useAPI_removeRTImtContext()
{
    m_RtimtAlg->removeViewontextRtimt();
}

void RTIMTRuler::updateResultData()
{
    //刷新roi图元, 刷新4条内膜轮廓线图元, 刷新测量结果展示
    m_RealDataCanSend = false;
    m_UseRealData = false;
    // x_center_still, y_begin_still, y_end_still在均不为-1的时候画roi框和满足要求的内膜轮廓
    if (x_center_still != -1 && y_begin_still != -1 && y_end_still != -1)
    {
        m_UseRealData = true; // 算法识别成功
    }

    m_MagicValue++;
    if (m_MagicValue <= 2 || m_MagicValue >= 100)
    {
        m_MagicValue = 2;
    }
    ImtCurve* imtRect = static_cast<ImtCurve*>(m_Control->activeGlyphs());
    if (imtRect == nullptr)
    {
        return;
    }
    QRectF qrectf = imtRect->LimitSceneRect();

    int xLeft = qrectf.left();
    int yTop = qrectf.top();
    int xRight = qrectf.right();
    int yBottom = qrectf.bottom();

    xLeft = qBound(0, xLeft, m_CurrentImage.width() - 1);
    yTop = qBound(0, yTop, m_CurrentImage.height() - 1);
    xRight = qBound(0, xRight, m_CurrentImage.width() - 1);
    yBottom = qBound(0, yBottom, m_CurrentImage.height() - 1);

    int roiWidth = xRight - xLeft + 1;
    if (roiWidth < 1)
    {
        return;
    }

    QPointF ant_intimaFirstPt(0, 15);
    QPointF ant_adventitiaFirstPt(0, 20);
    QPointF post_intimaFirstPt(0, 45);
    QPointF post_adventitiaFirstPt(0, 40);

    m_LastPos = QPointF(xLeft, yTop);

    if (m_UseRealData)
    {
        ant_intimaFirstPt = QPointF(0, 0);
        ant_adventitiaFirstPt = QPointF(0, 0);
        post_intimaFirstPt = QPointF(0, 0);
        post_adventitiaFirstPt = QPointF(0, 0);
    }
    else
    {
        ant_intimaFirstPt = QPointF(0, m_MagicValue + 15);
        ant_adventitiaFirstPt = QPointF(0, m_MagicValue + 20);
        post_intimaFirstPt = QPointF(0, m_MagicValue * 2 + 45);
        post_adventitiaFirstPt = QPointF(0, m_MagicValue * 2 + 40);
    }

    QPainterPath ant_intimaPath(ant_intimaFirstPt), ant_adventitiaPath(ant_adventitiaFirstPt);
    QPainterPath post_intimaPath(post_intimaFirstPt), post_adventitiaPath(post_adventitiaFirstPt);

    int* coord_list = im_output.coord_list;
    int* index_list = im_output.index_list;

    int xLeftPos = xLeft;
    int yTopPos = yTop;
    int xRightPos = xRight;
    int yBottomPos = yBottom;
    int width = 0;
    int height = 0;

    if (m_AutoRoi)
    {
        QRect autoRoi =
            createAutoRoi(m_CurrentImage.width(), m_CurrentImage.height(), x_center_still, y_begin_still, y_end_still);
        xLeftPos = autoRoi.left();
        yTopPos = autoRoi.top();
        xRightPos = autoRoi.right();
        yBottomPos = autoRoi.bottom();
    }

    int cSize = 20;
    if (m_UseRealData)
    {
        if (xLeftPos < left_margin_x + cSize)
        {
            xLeftPos = left_margin_x + cSize;
        }

        if (xRightPos > right_margin_x)
        {
            xRightPos = right_margin_x;
        }

        width = xRightPos - xLeftPos;
        height = yBottomPos - yTopPos;

        int x = 0;
        int y = 0;
        bool first_ant_intima_point = true;
        bool first_ant_adventitia_point = true;
        bool first_post_intima_point = true;
        bool first_post_adventitia_point = true;

        if (index_list[0] != -1 && index_list[1] != -1 && index_list[2] != -1 && index_list[3] != -1)
        {
            for (int j = index_list[0]; j <= index_list[1]; j++)
            {
                if ((j - index_list[0]) % 2 == 0)
                {
                    x = coord_list[j] - xLeftPos;
                    y = coord_list[j + 1] - yTopPos;
                    if (x > width || x < 0 || y > height || y < 0)
                    {
                        continue;
                    }

                    if (first_ant_adventitia_point)
                    {
                        first_ant_adventitia_point = false;
                        QPainterPath newPath(QPointF(x, y));
                        ant_adventitiaPath = newPath;
                    }
                    ant_adventitiaPath.lineTo(x, y);
                }
            }
            for (int j = index_list[2]; j <= index_list[3]; j++)
            {
                if ((j - index_list[2]) % 2 == 0)
                {
                    x = coord_list[j] - xLeftPos;
                    y = coord_list[j + 1] - yTopPos;
                    if (x > width || x < 0 || y > height || y < 0)
                    {
                        continue;
                    }

                    if (first_ant_intima_point)
                    {
                        first_ant_intima_point = false;
                        QPainterPath newPath(QPointF(x, y));
                        ant_intimaPath = newPath;
                    }
                    ant_intimaPath.lineTo(x, y);
                }
            }
        }

        if (index_list[4] != -1 && index_list[5] != -1 && index_list[6] != -1 && index_list[7] != -1)
        {
            std::vector<cv::Point> contour_upper;
            for (int j = index_list[4]; j <= index_list[5]; j++)
            {
                if ((j - index_list[4]) % 2 == 0)
                {
                    x = coord_list[j] - xLeftPos;
                    y = coord_list[j + 1] - yTopPos;
                    if (x > width || x < 0 || y > height || y < 0)
                    {
                        continue;
                    }

                    if (first_post_intima_point)
                    {
                        first_post_intima_point = false;
                        QPainterPath newPath(QPointF(x, y));
                        post_intimaPath = newPath;
                    }
                    post_intimaPath.lineTo(x, y);
                }
            }
            for (int j = index_list[6]; j <= index_list[7]; j++)
            {
                if ((j - index_list[6]) % 2 == 0)
                {
                    x = coord_list[j] - xLeftPos;
                    y = coord_list[j + 1] - yTopPos;
                    if (x > width || x < 0 || y > height || y < 0)
                    {
                        continue;
                    }

                    if (first_post_adventitia_point)
                    {
                        first_post_adventitia_point = false;
                        QPainterPath newPath(QPointF(x, y));
                        post_adventitiaPath = newPath;
                    }
                    post_adventitiaPath.lineTo(x, y);
                }
            }
        }
        m_LastPos = QPointF(xLeftPos, yTopPos);
        if (m_FrameTimeStamp == m_Control->m_MaxGlData.timeStamp())
        {
            drawMaxFrameGl(imtRect);
        }
        else
        {
            m_Control->setLastPos(QPointF(xLeftPos, yTopPos));
            imtRect->setPos(xLeftPos, yTopPos);
            imtRect->setSize(width, height);
            imtRect->setPath(ant_intimaPath, ant_adventitiaPath, post_intimaPath, post_adventitiaPath,
                             ant_intimaFirstPt);
        }
        m_Control->syncAnchorToGlyphsPos();
        QList<QPointF> newPoints;
        newPoints.append(QPointF(xLeftPos, yTopPos));
        newPoints.append(QPointF(xLeftPos + width, yTopPos + height));
        imtRect->setGlyphsControlPoints(newPoints);

        // update ruler result
        if (m_UpdateCycle)
        {
            if (m_Control->isRealTime())
            {
                recordDataForCompare(xLeftPos, yTopPos, width, height, ant_intimaPath, ant_adventitiaPath,
                                     post_intimaPath, post_adventitiaPath, ant_intimaFirstPt, imtRect);
                calcMaxDataFrame(oneCycleDataListPost, oneCycleFrameListPost, oneCycleFrontListPost);
                qreal postMeanData = getMaxValue(oneCycleDataListPost);
                qreal antMeanData = getMaxValue(oneCycleDataListAnt);
                updateCyclePostList(postMeanData * m_Context->pixelSizeMM());
                //            antMeanData = postMeanData;// test 前膜很难打出来，暂时用后膜的数据。
                updateCycleAntList(antMeanData * m_Context->pixelSizeMM());
                updateRulerResult();
                oneCycleDataListPost.clear();
                oneCycleDataListAnt.clear();
            }
            m_UpdateCycle = false;
        }
    }
    else
    {
        QPainterPath emptyPath;
        QPointF emptyPoint;

        if (m_FrameTimeStamp == m_Control->m_MaxGlData.timeStamp() && !m_Control->isRealTime())
        {
            drawMaxFrameGl(imtRect);
        }
        else
        {
            imtRect->setPath(emptyPath, emptyPath, emptyPath, emptyPath, emptyPoint);
        }
    }

    if (m_UseRealData)
    {
        m_RealDataCanSend = true;
    }

    m_Context->setRealTimeMeasureFrameIndex(m_FrameIndex);
    m_Context->setRealTimeMeasureFrontIndex(m_FrontIndex);
}

void RTIMTRuler::updateRulerResult()
{
    QList<qreal> resultList;
    resultList.append(m_Cycle1PostData);
    resultList.append(m_Cycle1AntData);
    resultList.append(m_Cycle2PostData);
    resultList.append(m_Cycle2AntData);
    resultList.append(m_Cycle3PostData);
    resultList.append(m_Cycle3AntData);
    resultList.append(m_Cycle4PostData);
    resultList.append(m_Cycle4AntData);
    resultList.append(m_Cycle5PostData);
    resultList.append(m_Cycle5AntData);
    resultList.append(m_Cycle6PostData);
    resultList.append(m_Cycle6AntData);
    resultList.append(m_MeanPostData);
    resultList.append(m_MeanAntData);
    resultList.append(m_MaxPostData);
    resultList.append(m_MaxAntData);
    resultList.append(m_SDPostData);
    resultList.append(m_SDAntData);

    int resultCount = resultList.count();
    if (resultCount > m_Result.count())
    {
        resultCount = m_Result.count();
    }

    for (int i = 0; i < resultCount; ++i)
    {
        setValue(i, resultList[i]);
    }
}

void RTIMTRuler::updateRulerResult(QList<qreal>& resultList)
{
    int resultCount = resultList.count();

    if (resultCount > m_Result.count())
    {
        resultCount = m_Result.count();
    }

    for (int i = 0; i < resultCount; ++i)
    {
        setValue(i, resultList[i]);
    }
}

void RTIMTRuler::recordDataForCompare(int xLeftPos, int yTopPos, int width, int height, QPainterPath ant_intimaPath,
                                      QPainterPath ant_adventitiaPath, QPainterPath post_intimaPath,
                                      QPainterPath post_adventitiaPath, QPointF ant_intimaFirstPt, ImtCurve* imtRect)
{
    // use_still_roi = 1：只有当固定roi框生成后才能计算对应框内的内膜厚度最大值
    // im_output.thickness_max != -1: 只有当roi框内内膜分割部分厚度可计算时才能输出厚度最大值
    // 若满足上述两条件，在图像中央显示厚度最大值
    if (use_still_roi == 1 && im_output.thickness_max_far != -1 && m_Control->isRealTime())
    {
        double max_near = im_output.thickness_max_near;
        double max_far = im_output.thickness_max_far;

        if (oneCycleDataListAnt.count() >= 60)
        {
            oneCycleDataListAnt.removeFirst();
            oneCycleFrameListAnt.removeFirst();
            oneCycleDataListPost.removeFirst();
            oneCycleFrontListPost.removeFirst();
            oneCycleFrameListPost.removeFirst();
            oneCycleGlyphs_TimeStampData_List.removeFirst();
            oneCycleGlyphs_xLeftPos_List.removeFirst();
            oneCycleGlyphs_yTopPos_List.removeFirst();
            oneCycleGlyphs_width_List.removeFirst();
            oneCycleGlyphs_height_List.removeFirst();
            oneCycleGlyphs_ant_intimaPath_List.removeFirst();
            oneCycleGlyphs_ant_adventitiaPath_List.removeFirst();
            oneCycleGlyphs_post_intimaPath_List.removeFirst();
            oneCycleGlyphs_post_adventitiaPath_List.removeFirst();
            oneCycleGlyphs_ant_intimaFirstPt_List.removeFirst();
            oneCycleGlyphs_ImtCurveData_List.removeFirst();
        }

        oneCycleDataListAnt.append(max_near);
        oneCycleFrameListAnt.append(m_FrameIndex);
        oneCycleFrontListPost.append(m_FrontIndex);
        oneCycleDataListPost.append(max_far);
        oneCycleFrameListPost.append(m_FrameIndex);
        oneCycleGlyphs_TimeStampData_List.append(m_FrameTimeStamp);
        qDebug() << __FUNCTION__ << __LINE__ << m_FrameIndex;
        oneCycleGlyphs_xLeftPos_List.append(xLeftPos);
        oneCycleGlyphs_yTopPos_List.append(yTopPos);
        oneCycleGlyphs_width_List.append(width);
        oneCycleGlyphs_height_List.append(height);
        oneCycleGlyphs_ant_intimaPath_List.append(ant_intimaPath);
        oneCycleGlyphs_ant_adventitiaPath_List.append(ant_adventitiaPath);
        oneCycleGlyphs_post_intimaPath_List.append(post_intimaPath);
        oneCycleGlyphs_post_adventitiaPath_List.append(post_adventitiaPath);
        oneCycleGlyphs_ant_intimaFirstPt_List.append(ant_intimaFirstPt);

        ImtCurveData* imtData = dynamic_cast<ImtCurveData*>(imtRect->glyphsData().get());
        Q_ASSERT(imtData != nullptr);
        oneCycleGlyphs_ImtCurveData_List.append(*imtData);
    }
}

void RTIMTRuler::checkFileResult()
{
    if (nullptr == m_SonoParameters)
    {
        return;
    }

    QList<qreal> resultList;
    qint64 key = m_FrameTimeStamp;
    if (m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        if (m_Control->m_FileResultMap.count() > Setting::instance().defaults().cineCacheSize())
        {
            m_Control->m_FileResultMap.remove(m_Control->m_FileResultMap.firstKey());
        }
        resultList.append(m_Cycle1PostData);
        resultList.append(m_Cycle1AntData);
        resultList.append(m_Cycle2PostData);
        resultList.append(m_Cycle2AntData);
        resultList.append(m_Cycle3PostData);
        resultList.append(m_Cycle3AntData);
        resultList.append(m_Cycle4PostData);
        resultList.append(m_Cycle4AntData);
        resultList.append(m_Cycle5PostData);
        resultList.append(m_Cycle5AntData);
        resultList.append(m_Cycle6PostData);
        resultList.append(m_Cycle6AntData);
        resultList.append(m_MeanPostData);
        resultList.append(m_MeanAntData);
        resultList.append(m_MaxPostData);
        resultList.append(m_MaxAntData);
        resultList.append(m_SDPostData);
        resultList.append(m_SDAntData);
        if (hasValidValue(resultList))
            m_Control->m_FileResultMap.insert(m_FrameTimeStamp, resultList);
    }
    else
    {
        for (int i = 0; i < resultList.count(); i++)
            resultList.append(0);

        if (m_Control->m_FileResultMap.isEmpty())
            return;

        if (m_Control->m_FileResultMap.contains(key))
        {
            resultList = m_Control->m_FileResultMap.value(key);
        }
        else
        {
            if (!m_UseRealData)
                return;

            QList<qreal> aList;
            qint64 needKey = m_Control->m_FileResultMap.firstKey();
            foreach (const qint64& k, m_Control->m_FileResultMap.keys())
            {
                if (key < k)
                    break;
                else
                    needKey = k;
            }

            QMap<qint64, QList<qreal>>::const_iterator lowerIT = m_Control->m_FileResultMap.constFind(needKey);

            if (m_UseRealData && !hasValidValue(lowerIT.value()))
            {
                for (QMap<qint64, QList<qreal>>::const_iterator it = lowerIT; it != m_Control->m_FileResultMap.begin();
                     it--)
                {
                    if (hasValidValue(it.value()))
                    {
                        updateRulerResult(resultList);
                        break;
                    }
                }
                key = lowerIT.key();
            }
            resultList = lowerIT.value();
        }
        updateRulerResult(resultList);
    }
}

void RTIMTRuler::calcOneFrameData(QPainterPath ant_intimaPath, QPainterPath ant_adventitiaPath,
                                  QPainterPath post_intimaPath, QPainterPath post_adventitiaPath)
{
    return;

    QPolygonF antIntimaPathPoints = ant_intimaPath.toSubpathPolygons().value(0);
    QPolygonF antAdventitiaPathPoints = ant_adventitiaPath.toSubpathPolygons().value(0);
    QPolygonF postIntimaPathPoints = post_intimaPath.toSubpathPolygons().value(0);
    QPolygonF postAdventitiaPathPoints = post_adventitiaPath.toSubpathPolygons().value(0);

    int antIntimaPathPointsCount = antIntimaPathPoints.count();
    int antAdventitiaPointsCount = antAdventitiaPathPoints.count();
    int postIntimaPathPointsCount = postIntimaPathPoints.count();
    int postAdventitiaPathPointsCount = postAdventitiaPathPoints.count();
    int antCount = qMin(antIntimaPathPointsCount, antAdventitiaPointsCount);
    int postCount = qMin(postIntimaPathPointsCount, postAdventitiaPathPointsCount);

    QList<qreal> tmplistA;
    for (int i = 0; i < antCount; i++)
    {
        if (antIntimaPathPoints.at(i).x() == antAdventitiaPathPoints.at(i).x())
        {
            qreal data = qAbs(antIntimaPathPoints.at(i).y() - antAdventitiaPathPoints.at(i).y());
            tmplistA.append(data);
        }
    }
    getMeanValue(tmplistA);
    getSdValue(tmplistA);

    QList<qreal> tmplistP;
    for (int i = 0; i < postCount; i++)
    {
        if (postIntimaPathPoints.at(i).x() == postAdventitiaPathPoints.at(i).x())
        {
            qreal data = qAbs(postIntimaPathPoints.at(i).y() - postAdventitiaPathPoints.at(i).y());
            tmplistP.append(data);
        }
    }
    getMeanValue(tmplistP);
    getSdValue(tmplistP);
}

void RTIMTRuler::updateCycleAntList(qreal data)
{
    m_CycleAntList.insert(0, data);
    if (m_CycleAntList.count() >= 7)
    {
        m_CycleAntList.removeLast();
    }
    int count = m_CycleAntList.count();
    for (int i = 0; i < count; i++)
    {
        updateCycleAntData(i, m_CycleAntList.value(i));
    }
    qreal antMaxData = getMaxValue(m_CycleAntList);
    updateMaxAntData(antMaxData);
    qreal antMeanData = getMeanValue(m_CycleAntList);
    updateMeanAntData(antMeanData);
    qreal antSdData = getSdValue(m_CycleAntList);
    updateSDAntData(antSdData);
}

void RTIMTRuler::updateCyclePostList(qreal data)
{
    m_CyclePostList.insert(0, data);
    if (m_CyclePostList.count() >= 7)
    {
        m_CyclePostList.removeLast();
    }
    int count = m_CyclePostList.count();
    for (int i = 0; i < count; i++)
    {
        updateCyclePostData(i, m_CyclePostList.value(i));
    }

    qreal postMaxData = getMaxValue(m_CyclePostList);
    updateMaxPostData(postMaxData);
    qreal postMeanData = getMeanValue(m_CyclePostList);
    updateMeanPostData(postMeanData);
    qreal postSdData = getSdValue(m_CyclePostList);
    updateSDPostData(postSdData);
}

void RTIMTRuler::updateCycleAntData(int index, qreal data)
{
    int cycleIndex = index + 1;
    if (cycleIndex > 6)
    {
        return;
    }

    switch (cycleIndex)
    {
    case 1:
        updateCycle1AntData(data);
        break;
    case 2:
        updateCycle2AntData(data);
        break;
    case 3:
        updateCycle3AntData(data);
        break;
    case 4:
        updateCycle4AntData(data);
        break;
    case 5:
        updateCycle5AntData(data);
        break;
    case 6:
        updateCycle6AntData(data);
        break;
    default:
        break;
    }
}

void RTIMTRuler::updateCyclePostData(int index, qreal data)
{
    int cycleIndex = index + 1;
    if (cycleIndex > 6)
    {
        return;
    }

    switch (cycleIndex)
    {
    case 1:
        updateCycle1PostData(data);
        break;
    case 2:
        updateCycle2PostData(data);
        break;
    case 3:
        updateCycle3PostData(data);
        break;
    case 4:
        updateCycle4PostData(data);
        break;
    case 5:
        updateCycle5PostData(data);
        break;
    case 6:
        updateCycle6PostData(data);
        break;
    default:
        break;
    }
}

void RTIMTRuler::calcMaxDataFrame(QList<qreal>& dataList, QList<int>& frameList, QList<int>& frontList)
{
    qreal maxData = 0;
    int maxFrameIndex = 0;
    int maxFrontIndex = 0;
    int xLeftPosMax = 0;
    int yTopPosMax = 0;
    int widthMax = 0;
    int heightMax = 0;
    QPainterPath ant_intimaPath_max;
    QPainterPath ant_adventitiaPath_max;
    QPainterPath post_intimaPath_max;
    QPainterPath post_adventitiaPath_max;
    QPointF ant_intimaFirstPt_max;
    ImtCurveData imtCurveData_max;
    qint64 timeStamp = 0;

    for (int i = 0; i < dataList.length(); i++)
    {
        if (maxData <= dataList.value(i))
        {
            maxData = dataList.value(i);
            maxFrameIndex = frameList.value(i);
            maxFrontIndex = frontList.value(i);
            xLeftPosMax = oneCycleGlyphs_xLeftPos_List.value(i);
            yTopPosMax = oneCycleGlyphs_yTopPos_List.value(i);
            widthMax = oneCycleGlyphs_width_List.value(i);
            heightMax = oneCycleGlyphs_height_List.value(i);
            ant_intimaPath_max = oneCycleGlyphs_ant_intimaPath_List.value(i);
            ant_adventitiaPath_max = oneCycleGlyphs_ant_adventitiaPath_List.value(i);
            post_intimaPath_max = oneCycleGlyphs_post_intimaPath_List.value(i);
            post_adventitiaPath_max = oneCycleGlyphs_post_adventitiaPath_List.value(i);
            ant_intimaFirstPt_max = oneCycleGlyphs_ant_intimaFirstPt_List.value(i);
            imtCurveData_max = oneCycleGlyphs_ImtCurveData_List.value(i);
            timeStamp = oneCycleGlyphs_TimeStampData_List.value(i);
        }
    }

    if (m_MaxData <= maxData)
    {
        // bug 66860 预防措施，图元和最大值同时存在有效值的时候，才记录新的最大值和图元。
        if ((ant_intimaPath_max.toSubpathPolygons().value(0).count() > 1 &&
             ant_adventitiaPath_max.toSubpathPolygons().value(0).count() > 1) ||
            (post_intimaPath_max.toSubpathPolygons().value(0).count() > 1 &&
             post_adventitiaPath_max.toSubpathPolygons().value(0).count() > 1))
        {
            m_MaxData = maxData;
            updateMaxFrameGlyphs(xLeftPosMax, yTopPosMax, widthMax, heightMax, ant_intimaPath_max,
                                 ant_adventitiaPath_max, post_intimaPath_max, post_adventitiaPath_max,
                                 ant_intimaFirstPt_max, timeStamp);
        }
    }

    oneCycleFrameListPost.clear();
    oneCycleGlyphs_TimeStampData_List.clear();
    oneCycleFrontListPost.clear();
    oneCycleFrameListAnt.clear();
    oneCycleGlyphs_xLeftPos_List.clear();
    oneCycleGlyphs_yTopPos_List.clear();
    oneCycleGlyphs_width_List.clear();
    oneCycleGlyphs_height_List.clear();
    oneCycleGlyphs_ant_intimaPath_List.clear();
    oneCycleGlyphs_ant_adventitiaPath_List.clear();
    oneCycleGlyphs_post_intimaPath_List.clear();
    oneCycleGlyphs_post_adventitiaPath_List.clear();
    oneCycleGlyphs_ant_intimaFirstPt_List.clear();
    oneCycleGlyphs_ImtCurveData_List.clear();
}

void RTIMTRuler::updateCycle1PostData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle1PostData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle1AntData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle1AntData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle2PostData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle2PostData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle2AntData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle2AntData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle3PostData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle3PostData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle3AntData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle3AntData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle4PostData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle4PostData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle4AntData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle4AntData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle5PostData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle5PostData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle5AntData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle5AntData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle6PostData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle6PostData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateCycle6AntData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_Cycle6AntData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateMeanPostData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_MeanPostData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateMeanAntData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_MeanAntData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateMaxPostData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_MaxPostData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateMaxAntData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_MaxAntData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateSDPostData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_SDPostData = data;
    //    updateRulerResult();
}

void RTIMTRuler::updateSDAntData(qreal data)
{
    if (data < 0.001 || qIsNaN(data) || qIsInf(data))
    {
        return;
    }
    m_SDAntData = data;
    //    updateRulerResult();
}

void RTIMTRuler::update_last_origin(int* input_array, int length, int new_value)
{
    for (int i = 0; i < length - 1; i++)
    {
        input_array[i] = input_array[i + 1];
    }
    input_array[length - 1] = new_value;
}

void RTIMTRuler::update_last_origin(float* input_array, int length, float new_value)
{
    for (int i = 0; i < length - 1; i++)
    {
        input_array[i] = input_array[i + 1];
    }
    input_array[length - 1] = new_value;
}

void RTIMTRuler::update_last(int* input_array, int length, int new_value)
{
    for (int i = 0; i < length - 1; i++)
    {
        input_array[i] = input_array[i + 1];
    }
    input_array[length - 1] = new_value;
}

void RTIMTRuler::update_last(float* input_array, int length, float new_value)
{
    for (int i = 0; i < length - 1; i++)
    {
        input_array[i] = input_array[i + 1];
    }
    input_array[length - 1] = new_value;
}

void RTIMTRuler::update_last(double* input_array, int length, double new_value)
{
    for (int i = 0; i < length - 1; i++)
    {
        input_array[i] = input_array[i + 1];
    }
    input_array[length - 1] = new_value;
}

int RTIMTRuler::constraint(int input, int boundary, int mode)
{
    if (mode == 0)
    {
        return int((input < boundary) ? boundary : input);
    }
    else
    {
        return int((input > boundary) ? boundary : input);
    }
}

qreal RTIMTRuler::getMaxValue(QList<qreal>& list)
{
    qreal max = 0;
    for (int i = 0; i < list.length(); i++)
    {
        if (max < list.value(i))
        {
            max = list.value(i);
        }
    }
    return max;
}

qreal RTIMTRuler::getMeanValue(QList<qreal>& list)
{
    qreal sum = 0;
    qreal avg = 0;
    int invalidNum = 0;
    for (int i = 0; i < list.length(); i++)
    {
        if (list[i] > 0)
        {
            sum = sum + list[i];
        }
        else
        {
            invalidNum++;
        }
    }
    avg = sum / (list.length() - invalidNum);

    return avg;
}

qreal RTIMTRuler::getSdValue(QList<qreal>& list)
{
    QList<qreal> calcList;
    for (int i = 0; i < list.length(); i++)
    {
        if (list.at(i) > 0)
        {
            calcList.append(list.at(i));
        }
    }

    float sum = 0;
    for (int i = 0; i < calcList.size(); i++)
    {
        sum += calcList.at(i);
    }
    double average = (float)sum / calcList.size();
    float total = 0;
    for (int i = 0; i < calcList.size(); i++)
    {
        total += (calcList.at(i) - average) * (calcList.at(i) - average);
    }
    float standardDeviation = (float)sqrt(total / calcList.size());
    return standardDeviation;
}

void RTIMTRuler::initAPIPreData()
{
    p_middle_ys_diff = new int[duration_determine];
    angles_diff = new float[duration_determine];
    for (int i = 0; i < duration_determine; i++)
    {
        p_middle_ys_diff[i] = -10000;
        angles_diff[i] = -10000.0;
    }

    x_centers = new int[duration_roi_calculate];
    y_begins = new int[duration_roi_calculate];
    y_ends = new int[duration_roi_calculate];
    for (int i = 0; i < duration_roi_calculate; i++)
    {
        x_centers[i] = -1;
        y_begins[i] = -1;
        y_ends[i] = -1;
    }

    lumen_diameters = new double[duration_lumen_diameter_compute];
    for (int i = 0; i < duration_lumen_diameter_compute; i++)
    {
        lumen_diameters[i] = -1.0;
    }

    p_middle_ys = new int[duration_determine + interval];
    angles = new float[duration_determine + interval];
    for (int i = 0; i < (duration_determine + interval); i++)
    {
        p_middle_ys[i] = -1;
        angles[i] = 10000.0;
    }
}

void RTIMTRuler::deleteAPIPreData()
{
    delete[] p_middle_ys_diff;
    delete[] angles_diff;
    delete[] lumen_diameters;
    delete[] p_middle_ys;
    delete[] angles;
}

void RTIMTRuler::drawMaxFrameGl(ImtCurve* imtRect)
{
    imtRect->setPos(m_Control->m_MaxGlData.xLeftPosMax(), m_Control->m_MaxGlData.yTopPosMax());
    imtRect->setSize(m_Control->m_MaxGlData.widthMax(), m_Control->m_MaxGlData.heightMax());
    imtRect->setPath(m_Control->m_MaxGlData.ant_intimaPath_max(), m_Control->m_MaxGlData.ant_adventitiaPath_max(),
                     m_Control->m_MaxGlData.post_intimaPath_max(), m_Control->m_MaxGlData.post_adventitiaPath_max(),
                     m_Control->m_MaxGlData.ant_intimaFirstPt_max());

    QList<qreal> res = m_Control->m_FileResultMap.value(m_Control->m_MaxGlData.timeStamp());
    updateRulerResult(res);
}

void AlgThread::setRuler(RTIMTRuler* value)
{
    ruler = value;
}
