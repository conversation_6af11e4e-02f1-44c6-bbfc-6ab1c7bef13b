#include <QString>
#include <QStringList>
#include <QDir>
#include "burnutil.h"
#include "util.h"

//光盘信息
void BurnUtil::getDiscInfo(int& discStatus, int& discType, qulonglong& discSize, qulonglong& totaldiscSize)
{
    Q_UNUSED(discStatus);
    Q_UNUSED(discType);
    Q_UNUSED(discSize);
    Q_UNUSED(totaldiscSize);
    //    Util::getCmdOut("cdrecord dev=/dev/sr0 -minfo");
}

bool BurnUtil::DVDWriterExists()
{
    return true;
    //    return Util::System("cdrecord -scanbus") == 0;
}

bool BurnUtil::DVDExists()
{
    return true;
    //    return Util::System("dvd+rw-mediainfo /dev/sr0") == 0;
}

bool BurnUtil::isDiscWriterExist()
{
    //判断是否有光驱
    return false;
}

bool BurnUtil::mountBurn(const QString& mountDev, const QString& burnDir)
{
    Q_UNUSED(mountDev);
    Q_UNUSED(burnDir);
    return true;
}

void BurnUtil::umount(const QString& burnDir)
{
    Q_UNUSED(burnDir);
}

int BurnUtil::makeISO(const QString& imagePath)
{
    Q_UNUSED(imagePath);
    return 0;
    //    Util::System(QString("mkisofs -V CD -J -jcharset=utf8 -r -o %1/data.iso ").arg(imagePath) + imagePath);
}

int BurnUtil::makeAddtionalISO(const QString& imagePath)
{
    Q_UNUSED(imagePath);
    return 0;
    //    return Util::System(QString("mkisofs -v -V CD -J -jcharset=utf8 -r -o %1/data.iso -C `cdrecord dev=/dev/sr0
    //    -msinfo`  -M  /dev/sr0 ").arg(imagePath) + imagePath);
}

void BurnUtil::stopISO()
{
    //    Util::System(QString("killall -9 mkisofs"));
}

void BurnUtil::ejectDevice()
{
    //    Util::System("eject -s /dev/sr0");
}

// 刻录DVD
//覆盖
int BurnUtil::burnDVD(const QString& imagePath)
{
    Q_UNUSED(imagePath);
    return 0;
    //    return Util::System("growisofs -speed=4 -Z /dev/sr0 -R -J " + imagePath + " && eject -s /dev/sr0");
}

//追加
int BurnUtil::burnAddtionalDVD(const QString& imagePath)
{
    Q_UNUSED(imagePath);
    return 0;
    //    return Util::System("growisofs -speed=4 -M /dev/sr0 -R -J " + imagePath + " && eject -s /dev/sr0");
}

int BurnUtil::sealBurnDvd(const QString& imagePath)
{
    Q_UNUSED(imagePath);
    return 0;
    //    return Util::System("growisofs -speed=4 -dvd-compat -Z /dev/sr0 -R -J " + imagePath + " && eject -s
    //    /dev/sr0");
}

int BurnUtil::burnCD(const QString& imagePath)
{
    Q_UNUSED(imagePath);
    return 0;
    //    return Util::System(QString("cdrecord speed=16 -v dev=/dev/sr0 -eject -tao -multi %1/data.iso  && eject -s
    //    /dev/sr0").arg(imagePath));
}

int BurnUtil::burnAddtionalCD(const QString& imagePath)
{
    Q_UNUSED(imagePath);
    return 0;
    //    return Util::System(QString("cdrecord speed=16 -v dev=/dev/sr0 -eject -tao -multi
    //    %1/data.iso").arg(imagePath));
}
