#ifndef SUPPORTBURNUTIL_H
#define SUPPORTBURNUTIL_H
#include "usfcommoncoresupport_global.h"

class QMutex;
class USF_COMMON_CORE_SUPPORT_EXPORT BurnUtil
{
public:
    BurnUtil();

    enum DISKSTATE
    {
        Empty,
        Appendable,
        Full
    };
    enum DISKTYPE
    {
        DVD,
        CD,
        DVDDL,
        UnkownType
    };

    /**
     * @brief getDiscInfo 获取光盘信息  status, disctype, discsize
     */
    static void getDiscInfo(int& discStatus, int& discType, qulonglong& discSize, qulonglong& totaldiscSize);
    /**
     * @brief DVDWriterExists
     * @return
     */
    static bool DVDWriterExists();
    /**
     * @brief DVDExists
     * @return
     */
    static bool DVDExists();
    static bool isDiscWriterExist();
    /**
     * @brief mount
     * @param burnDir
     * @param diskDir
     * @return
     */
    static bool mountBurn(const QString& mountDev, const QString& burnDir);
    /**
     * @brief umount
     * @param burnDir
     */
    static void umount(const QString& burnDir);
    /**
     * @brief ejectDevice 弹出光盘
     */
    static void ejectDevice();
    /**
     * @brief makeISO 覆盖制作ISO
     */
    static int makeISO(const QString& imagePath);
    /**
     * @brief makeAddtionalISO 追加制作ISO
     */
    static int makeAddtionalISO(const QString& imagePath);
    /**
     * @brief stopISO
     */
    static void stopISO();
    /**
     * @brief burnDVD  覆盖刻录DVD
     */
    static int burnDVD(const QString& imagePath);
    /**
     * @brief burnAddtionalDVD  追加刻录DVD
     */
    static int burnAddtionalDVD(const QString& imagePath);
    /**
     * @brief sealBurnDvd  封盘刻录DVD
     * @return
     */
    static int sealBurnDvd(const QString& imagePath);
    /**
     * @brief burnCD  覆盖刻录CD
     */
    static int burnCD(const QString& imagePath);
    /**
     * @brief burnAddtionalCD  追加刻录CD
     */
    static int burnAddtionalCD(const QString& imagePath);

private:
    static QMutex m_burnCDMutex;
    static QMutex m_burnDVDMutex;
};

#endif // SUPPORTBURNUTIL_H
