#include <QMutex>
#include <QString>
#include <QStringList>
#include <QDir>
#include "burnutil.h"
#include "util.h"
#include <QRegExp>
#ifdef SYS_WINDOWS
#include "unistd_windows.h"
#else
#include <unistd.h>
#endif

#ifdef Q_OS_LINUX
#include <sys/mount.h>
#include <linux/cdrom.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#endif
QMutex BurnUtil::m_burnCDMutex;
QMutex BurnUtil::m_burnDVDMutex;

//光盘信息
void BurnUtil::getDiscInfo(int& discStatus, int& discType, qulonglong& discSize, qulonglong& totaldiscSize)
{
    QString discInfo = Util::getCmdOut("cdrskin dev=/dev/sr0 -minfo");
    QRegExp rex("disk status:\\s*([\\w/]*)");
    rex.indexIn(discInfo);
    if (!rex.cap(1).contains("empty"))
    {
        discStatus = Appendable;
    }
    else
    {
        discStatus = Empty;
    }
    rex.setPattern("Mounted media class:\\s*([\\w]*)");
    rex.indexIn(discInfo);
    if (rex.cap(1).contains("DVD"))
    {
        rex.setPattern("Mounted media type:\\s*([\\w-+\\/]*)");
        rex.indexIn(discInfo);
        if (rex.cap(1).contains("DVD+R/DL") || rex.cap(1).contains("DVD-R/DL"))
        {
            discType = DVDDL;
        }
        else
        {
            discType = DVD;
        }
    }
    else if (rex.cap(1).contains("CD"))
    {
        discType = CD;
    }
    else
    {
        discType = UnkownType;
    }
    rex.setPattern("Remaining writable size:\\s*([\\d]*)");
    if (rex.indexIn(discInfo) != -1)
    {
        discSize = rex.cap(1).toULongLong() * 2;
    }
    else
    {
        if (discType == DVD)
        {
            discSize = 2298496 * 2;
        }
        else if (discType == CD)
        {
            discSize = 359843 * 2;
        }
        else if (discType == DVDDL)
        {
            discSize = 4156112 * 2;
        }
    }
    rex.setPattern("ATIP start of lead out:\\s([\\d]*)");
    if (rex.indexIn(discInfo) != -1)
    {
        totaldiscSize = rex.cap(1).toULongLong() * 2;
    }
    else
    {
        if (discType == DVD)
        {
            totaldiscSize = 2298496 * 2;
        }
        else if (discType == CD)
        {
            totaldiscSize = 359843 * 2;
        }
        else if (discType == DVDDL)
        {
            totaldiscSize = 4156112 * 2;
        }
    }
}

bool BurnUtil::DVDWriterExists()
{
    return Util::System("cdrskin -scanbus") == 0;
}

bool BurnUtil::DVDExists()
{
    return Util::System("dvd+rw-mediainfo /dev/sr0") == 0;
}

bool BurnUtil::isDiscWriterExist()
{
    if (access("/dev/sr0", F_OK) == 0)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool BurnUtil::mountBurn(const QString& mountDev, const QString& burnDir)
{
    int ret = mount(mountDev.toStdString().data(), burnDir.toStdString().data(), "iso9660", MS_RDONLY, NULL);
    return ret == 0;
}

void BurnUtil::umount(const QString& burnDir)
{
    Util::System(QString("umount -fl %1").arg(burnDir));
}

void BurnUtil::ejectDevice()
{
    Util::System("eject -s /dev/sr0");
}

int BurnUtil::makeISO(const QString& imagePath)
{
    return Util::System(QString("mkisofs -V CD -J -jcharset=utf8 -r -o %1/data.iso ").arg(imagePath) + imagePath);
}

int BurnUtil::makeAddtionalISO(const QString& imagePath)
{
    return Util::System(
        QString("mkisofs -v -V CD -J -jcharset=utf8 -r -o %1/data.iso -C `cdrskin dev=/dev/sr0 -msinfo`  -M  /dev/sr0 ")
            .arg(imagePath) +
        imagePath);
}

void BurnUtil::stopISO()
{
    Util::System(QString("killall -9 mkisofs"));
}

// 刻录DVD
//覆盖
int BurnUtil::burnDVD(const QString& imagePath)
{
    int res = -1;
    if (!m_burnDVDMutex.tryLock())
    {
        return res;
    }
    res = Util::System("growisofs -speed=4 -Z /dev/sr0 -R -J " + imagePath);
    m_burnDVDMutex.unlock();
    return WEXITSTATUS(res);
}

//追加
int BurnUtil::burnAddtionalDVD(const QString& imagePath)
{
    int res = -1;
    if (!m_burnDVDMutex.tryLock())
    {
        return res;
    }
    res = Util::System("growisofs -speed=4 -M /dev/sr0 -R -J " + imagePath);
    m_burnDVDMutex.unlock();
    return WEXITSTATUS(res);
}

int BurnUtil::sealBurnDvd(const QString& imagePath)
{
    int res = -1;
    if (!m_burnDVDMutex.tryLock())
    {
        return res;
    }
    res = Util::System("growisofs -speed=4 -dvd-compat -Z /dev/sr0 -R -J " + imagePath);
    m_burnDVDMutex.unlock();
    return WEXITSTATUS(res);
}

int BurnUtil::burnCD(const QString& imagePath)
{
    int res = -1;
    if (!m_burnCDMutex.tryLock())
    {
        return res;
    }
    res = Util::System(QString("cdrskin speed=16 -v dev=/dev/sr0 -eject -tao -multi %1/data.iso ").arg(imagePath));
    m_burnCDMutex.unlock();
    return WEXITSTATUS(res);
}

int BurnUtil::burnAddtionalCD(const QString& imagePath)
{
    int res = -1;
    if (!m_burnCDMutex.tryLock())
    {
        return res;
    }
    res = Util::System(QString("cdrskin speed=16 -v dev=/dev/sr0 -eject -tao -multi %1/data.iso").arg(imagePath));
    m_burnCDMutex.unlock();
    return WEXITSTATUS(res);
}
