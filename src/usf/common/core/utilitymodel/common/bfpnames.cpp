#include "bfpnames.h"

QString BFPNames::FreezeStr = "Freeze";
QString BFPNames::FrequencyCompoundingStr = "FrequencyCompounding";
QString BFPNames::FrequencyCompoundingOtherStr = "FrequencyCompoundingOther";
QString BFPNames::SocketStr = "Socket";
QString BFPNames::ProbeCodeStr = "ProbeCode";
QString BFPNames::FreqIndexColorBinStr = "FreqIndexColorBin";
QString BFPNames::ColorLineDensityStr = "ColorLineDensity";
QString BFPNames::ColorLineDensitySNStr = "ColorLineDensitySN";
QString BFPNames::TDILineDensityStr = "TDILineDensity";
QString BFPNames::MVILineDensityStr = "MVILineDensity";
QString BFPNames::ElastoColorLineDensityStr = "ElastoColorLineDensity";
QString BFPNames::SteeringAngleStr = "SteeringAngle";
QString BFPNames::DopSteeringAngleStr = "DopSteeringAngle";
QString BFPNames::LinearStr = "Linear";
QString BFPNames::FocusNumBStr = "FocusNumB";
QString BFPNames::FocusPosBStr = "FocusPosB";
QString BFPNames::FocusNumMStr = "FocusNumM";
QString BFPNames::FocusPosMStr = "FocusPosM";
QString BFPNames::DeepStr = "Deep";
QString BFPNames::HalfHeightStr = "HalfHeight";
QString BFPNames::ImageModeStr = "ImageMode";
QString BFPNames::SyncModeStr = "SyncMode";
QString BFPNames::HighDensityStr = "HighDensity";
QString BFPNames::CQYZStr = "CQYZ";
QString BFPNames::MVelocityStr = "MVelocity";
QString BFPNames::DVelocityStr = "DVelocity";
QString BFPNames::DVelocityTDIStr = "DVelocityTDI";
QString BFPNames::CWDVelocityStr = "CWDVelocity";
QString BFPNames::FreeMVelocityStr = "FreeMVelocity";
QString BFPNames::ColorImageModeStr = "ColorImageMode";
QString BFPNames::FreqSpectrumStr = "FreqSpectrum";
QString BFPNames::LowVelocityBloodStr = "LowVelocityBlood";
QString BFPNames::BaseLineStr = "BaseLine";
QString BFPNames::BaseLineDTDIStr = "BaseLineDTDI";
QString BFPNames::BaseLineCWDStr = "BaseLineCWD";
QString BFPNames::GainColorStr = "GainColor";
QString BFPNames::GainPDStr = "GainPD";
QString BFPNames::GainSNStr = "GainSN";
QString BFPNames::GainTDIStr = "GainTDI";
QString BFPNames::GainMVIStr = "GainMVI";
QString BFPNames::CETStr = "CET";
QString BFPNames::PDCETStr = "PDCET";
QString BFPNames::SNCETStr = "SNCET";
QString BFPNames::DPDCETStr = "DPDCET";
QString BFPNames::TDICETStr = "TDICET";
QString BFPNames::MVICETStr = "MVICET";
QString BFPNames::TNRStr = "TNR";
QString BFPNames::TNFtrStr = "TNFtr";
QString BFPNames::ConfigDoneStr = "ConfigDone";
QString BFPNames::DynamicRangeStr = "DynamicRange";
QString BFPNames::FourDDynamicRangeStr = "FourDDynamicRange";
QString BFPNames::FourDStr = "FourD";
QString BFPNames::FrameAvgStr = "FrameAvg";
QString BFPNames::AccCountColorStr = "AccCountColor";
QString BFPNames::AccCountDopStr = "AccCountDop";
QString BFPNames::AccCountPDStr = "AccCountPD";
QString BFPNames::AccCountSNStr = "AccCountSN";
QString BFPNames::AccCountTDIStr = "AccCountTDI";
QString BFPNames::AccCountMVIStr = "AccCountMVI";
QString BFPNames::SampleRateDopStr = "SampleRateDop";
QString BFPNames::SampleRateDopSNStr = "SampleRateDopSN";
QString BFPNames::SampleRateDopTDIStr = "SampleRateDopTDI";
QString BFPNames::SampleRateDopTMStr = "SampleRateDopTM";
QString BFPNames::SampleRateDopElastoStr = "SampleRateDopElasto";
QString BFPNames::SampleRateDopMVIStr = "SampleRateDopMVI";
QString BFPNames::DSampleRateDopStr = "DSampleRateDop";
QString BFPNames::DSampleRateDopTDIStr = "DSampleRateDopTDI";
QString BFPNames::DSampleRateDopTMStr = "DSampleRateDopTM";
QString BFPNames::WallFilterDopStr = "WallFilterDop";
QString BFPNames::WallFilterDopTDIStr = "WallFilterDopTDI";
QString BFPNames::WallFilterCWDStr = "WallFilterCWD";
QString BFPNames::WallFilterColorStr = "WallFilterColor";
QString BFPNames::WallFilterPDStr = "WallFilterPD";
QString BFPNames::WallFilterSNStr = "WallFilterSN";
QString BFPNames::WallFilterMVIStr = "WallFilterMVI";
QString BFPNames::WallThresholdStr = "WallThreshold";
QString BFPNames::WallThresholdPDStr = "WallThresholdPD";
QString BFPNames::WallThresholdTDIStr = "WallThresholdTDI";
QString BFPNames::WallThresholdMVIStr = "WallThresholdMVI";
QString BFPNames::SampleVolumeStr = "SampleVolume";
QString BFPNames::SampleVolumeTDIStr = "SampleVolumeTDI";
QString BFPNames::TriplexModeStr = "TriplexMode";
QString BFPNames::TriplexModeTDIStr = "TriplexModeTDI";
QString BFPNames::FrameAvgColorStr = "FrameAvgColor";
QString BFPNames::FrameAvgPDStr = "FrameAvgPD";
QString BFPNames::FrameAvgSNStr = "FrameAvgSN";
QString BFPNames::FrameAvgTDIStr = "FrameAvgTDI";
QString BFPNames::FrameAvgMVIStr = "FrameAvgMVI";
QString BFPNames::CVRTStr = "CVRT";
QString BFPNames::PDCVRTStr = "PDCVRT";
QString BFPNames::SNCVRTStr = "SNCVRT";
QString BFPNames::DPDCVRTStr = "DPDCVRT";
QString BFPNames::TDICVRTStr = "TDICVRT";
QString BFPNames::MVICVRTStr = "MVICVRT";
QString BFPNames::ScpdStr = "Scpd";
QString BFPNames::ScpdTrapeStr = "ScpdTrape";
QString BFPNames::ScpdOtherStr = "ScpdOther";
QString BFPNames::AcousticPowerTestCodeStr = "AcousticPowerTestCode";
QString BFPNames::MScanLineStr = "MScanLine";
QString BFPNames::DScanLineStr = "DScanLine";
QString BFPNames::DScanLineTDIStr = "DScanLineTDI";
QString BFPNames::StartLineStr = "StartLine";
QString BFPNames::StopLineStr = "StopLine";
QString BFPNames::FourDStartLineStr = "FourDStartLine";
QString BFPNames::FourDStopLineStr = "FourDStopLine";
QString BFPNames::StartLineColorStr = "StartLineColor";
QString BFPNames::StopLineColorStr = "StopLineColor";
QString BFPNames::TopBorderColorStr = "TopBorderColor";
QString BFPNames::BottomBorderColorStr = "BottomBorderColor";
QString BFPNames::SampleDepthDopStr = "SampleDepthDop";
QString BFPNames::AngleSpacingStr = "AngleSpacing";
QString BFPNames::LineSpacingStr = "LineSpacing";
QString BFPNames::GainStr = "Gain";
QString BFPNames::GainThiStr = "GainThi";
QString BFPNames::FourDGainStr = "FourDGain";
QString BFPNames::TGC8Str = "TGC8";
QString BFPNames::TGC1Str = "TGC1";
QString BFPNames::TGC2Str = "TGC2";
QString BFPNames::TGC3Str = "TGC3";
QString BFPNames::TGC4Str = "TGC4";
QString BFPNames::TGC5Str = "TGC5";
QString BFPNames::TGC6Str = "TGC6";
QString BFPNames::TGC7Str = "TGC7";
QString BFPNames::SlopeDisStr = "SlopeDis";
QString BFPNames::PerpendicularDisStr = "PerpendicularDis";
QString BFPNames::ZoomDepthStr = "ZoomDepth";
QString BFPNames::TransferSignalStr = "TransferSignal";
QString BFPNames::SpectralInvertStr = "SpectralInvert";
QString BFPNames::SmoothStr = "Smooth";
QString BFPNames::FocusNumCStr = "FocusNumC";
QString BFPNames::FocusPosCStr = "FocusPosC";
QString BFPNames::Deep2Str = "Deep2";
QString BFPNames::HalfHeight2Str = "HalfHeight2";
QString BFPNames::GainDopStr = "GainDop";
QString BFPNames::GainDopTMStr = "GainDopTM";
QString BFPNames::GainDopTDIStr = "GainDopTDI";
QString BFPNames::GainDopCWDStr = "GainDopCWD";
QString BFPNames::VolumeStr = "Volume";
QString BFPNames::VolumeTDIStr = "VolumeTDI";
QString BFPNames::VolumeCWDStr = "VolumeCWD";
QString BFPNames::MBStr = "MB";
QString BFPNames::ElastoMBStr = "ElastoMB";
QString BFPNames::BStearingAngleStr = "BStearingAngle";
QString BFPNames::FreqIndexFrequencyCompoundingStr = "FreqIndexFrequencyCompounding";
QString BFPNames::CRCOpenFlagStr = "CRCOpenFlag";
QString BFPNames::FocusPosFrequencyCompoundingStr = "FocusPosFrequencyCompounding";
QString BFPNames::AcousticPowerBStr = "AcousticPowerB";
QString BFPNames::AcousticPowerBThiStr = "AcousticPowerBThi";
QString BFPNames::AcousticPowerColorStr = "AcousticPowerColor";
QString BFPNames::AcousticPowerTDIStr = "AcousticPowerTDI";
QString BFPNames::AcousticPowerDopStr = "AcousticPowerDop";
QString BFPNames::AcousticPowerElastoStr = "AcousticPowerElasto";
QString BFPNames::AcousticPowerMStr = "AcousticPowerM";
QString BFPNames::AcousticPowerMThiStr = "AcousticPowerMThi";
QString BFPNames::MPEnStr = "MPEn";
QString BFPNames::BFEnStr = "BFEn";
QString BFPNames::DscEnStr = "DscEn";
QString BFPNames::DspEnStr = "DspEn";
QString BFPNames::DbfEnStr = "DbfEn";
QString BFPNames::PwrEnStr = "PwrEn";
QString BFPNames::UsbEnStr = "UsbEn";
QString BFPNames::ProgStr = "Prog";
QString BFPNames::CFGStartStr = "CFGStart";
QString BFPNames::BEStartStr = "BEStart";
QString BFPNames::WRClrStr = "WRClr";
QString BFPNames::LCDLightStr = "LCDLight";
QString BFPNames::CVLTStr = "CVLT";
QString BFPNames::PDCVLTStr = "PDCVLT";
QString BFPNames::SNCVLTStr = "SNCVLT";
QString BFPNames::DPDCVLTStr = "DPDCVLT";
QString BFPNames::TDICVLTStr = "TDICVLT";
QString BFPNames::MVICVLTStr = "MVICVLT";
QString BFPNames::ExceedLowBloodStr = "ExceedLowBlood";
QString BFPNames::DSCStr = "DSC";
QString BFPNames::CFVelocityThresholdStr = "CFVelocityThreshold";
QString BFPNames::PDVelocityThresholdStr = "PDVelocityThreshold";
QString BFPNames::SNVelocityThresholdStr = "SNVelocityThreshold";
QString BFPNames::TDIVelocityThresholdStr = "TDIVelocityThreshold";
QString BFPNames::MVIVelocityThresholdStr = "MVIVelocityThreshold";
QString BFPNames::StateClearFlagStr = "StateClearFlag";
QString BFPNames::ColorRegionThresholdStr = "ColorRegionThreshold";
QString BFPNames::PDRegionThresholdStr = "PDRegionThreshold";
QString BFPNames::SNRegionThresholdStr = "SNRegionThreshold";
QString BFPNames::TDIRegionThresholdStr = "TDIRegionThreshold";
QString BFPNames::MVIRegionThresholdStr = "MVIRegionThreshold";
QString BFPNames::WrResetStr = "WrReset";
QString BFPNames::CHETStr = "CHET";
QString BFPNames::PDCHETStr = "PDCHET";
QString BFPNames::SNCHETStr = "SNCHET";
QString BFPNames::DPDCHETStr = "DPDCHET";
QString BFPNames::TDICHETStr = "TDICHET";
QString BFPNames::MVICHETStr = "MVICHET";
QString BFPNames::WTStr = "WT";
QString BFPNames::CTGCStr = "CTGC";
QString BFPNames::PDCTGCStr = "PDCTGC";
QString BFPNames::SNCTGCStr = "SNCTGC";
QString BFPNames::DPDCTGCStr = "DPDCTGC";
QString BFPNames::TDICTGCStr = "TDICTGC";
QString BFPNames::MVICTGCStr = "MVICTGC";
QString BFPNames::BCosAngleStr = "BCosAngle";
QString BFPNames::BTgAngleStr = "BTgAngle";
QString BFPNames::DSPResetStr = "DSPReset";
QString BFPNames::DBFResetStr = "DBFReset";
QString BFPNames::DBFInfoStr = "DBFInfo";
QString BFPNames::TestSignalStr = "TestSignal";
QString BFPNames::TestParameterStr = "TestParameter";
QString BFPNames::BPulseNumStr = "BPulseNum";
QString BFPNames::CPulseNumStr = "CPulseNum";
QString BFPNames::DPulseNumStr = "DPulseNum";
QString BFPNames::DPulseNumTDIStr = "DPulseNumTDI";
QString BFPNames::CWDPluseNumStr = "CWDPluseNum";
QString BFPNames::BloodEffectionStr = "BloodEffection";
QString BFPNames::BloodEffectionPDStr = "BloodEffectionPD";
QString BFPNames::BloodEffectionSNStr = "BloodEffectionSN";
QString BFPNames::BloodEffectionTDIStr = "BloodEffectionTDI";
QString BFPNames::BloodEffectionMVIStr = "BloodEffectionMVI";
QString BFPNames::LogCompressionStr = "LogCompression";
QString BFPNames::HighFreqPhasedProbeStr = "HighFreqPhasedProbe";
QString BFPNames::PhaseProbeIdStr = "PhaseProbeId";
QString BFPNames::THIStr = "THI";
QString BFPNames::ImageZoomCoefBinStr = "ImageZoomCoefBin";
QString BFPNames::UniformityTgc00Str = "UniformityTgc00";
QString BFPNames::UniformityTgc01Str = "UniformityTgc01";
QString BFPNames::UniformityTgc02Str = "UniformityTgc02";
QString BFPNames::UniformityTgc03Str = "UniformityTgc03";
QString BFPNames::UniformityTgc04Str = "UniformityTgc04";
QString BFPNames::UniformityTgc05Str = "UniformityTgc05";
QString BFPNames::UniformityTgc06Str = "UniformityTgc06";
QString BFPNames::UniformityTgc07Str = "UniformityTgc07";
QString BFPNames::UniformityTgc08Str = "UniformityTgc08";
QString BFPNames::UniformityTgc09Str = "UniformityTgc09";
QString BFPNames::UniformityTgc10Str = "UniformityTgc10";
QString BFPNames::UniformityTgc11Str = "UniformityTgc11";
QString BFPNames::UniformityTgc12Str = "UniformityTgc12";
QString BFPNames::UniformityTgc13Str = "UniformityTgc13";
QString BFPNames::UniformityTgc14Str = "UniformityTgc14";
QString BFPNames::UniformityTgc15Str = "UniformityTgc15";
QString BFPNames::DebugPara0Str = "DebugPara0";
QString BFPNames::DebugPara3Str = "DebugPara3";
QString BFPNames::DebugPara2Str = "DebugPara2";
QString BFPNames::DebugPara1Str = "DebugPara1";
QString BFPNames::AnalogTgc0Str = "AnalogTgc0";
QString BFPNames::AnalogTgc1Str = "AnalogTgc1";
QString BFPNames::AnalogTgc2Str = "AnalogTgc2";
QString BFPNames::AnalogTgc3Str = "AnalogTgc3";
QString BFPNames::AnalogTgc4Str = "AnalogTgc4";
QString BFPNames::AnalogTgc5Str = "AnalogTgc5";
QString BFPNames::AnalogTgc6Str = "AnalogTgc6";
QString BFPNames::AnalogTgc7Str = "AnalogTgc7";
QString BFPNames::AnalogTgc8Str = "AnalogTgc8";
QString BFPNames::AnalogTgc9Str = "AnalogTgc9";
QString BFPNames::AnalogTgc10Str = "AnalogTgc10";
QString BFPNames::AnalogTgc11Str = "AnalogTgc11";
QString BFPNames::AnalogTgc12Str = "AnalogTgc12";
QString BFPNames::AnalogTgc13Str = "AnalogTgc13";
QString BFPNames::AnalogTgc14Str = "AnalogTgc14";
QString BFPNames::AnalogTgc15Str = "AnalogTgc15";
QString BFPNames::FrequencyOfTransmit1Str = "FrequencyOfTransmit1";
QString BFPNames::HighFreqOfTransmit1Str = "HighFreqOfTransmit1";
QString BFPNames::PulseNumOfTransmit1Str = "PulseNumOfTransmit1";
QString BFPNames::FocusNumOfTransmit1Str = "FocusNumOfTransmit1";
QString BFPNames::FilterCoefOfTransmit1Str = "FilterCoefOfTransmit1";
QString BFPNames::VHSiStr = "VHSi";
QString BFPNames::LeftStr = "Left";
QString BFPNames::UpStr = "Up";
QString BFPNames::MBColorStr = "MBColor";
QString BFPNames::COverlapStr = "COverlap";
QString BFPNames::ElastoMBColorStr = "ElastoMBColor";
QString BFPNames::MBPDStr = "MBPD";
QString BFPNames::MBSNStr = "MBSN";
QString BFPNames::MBTDIStr = "MBTDI";
QString BFPNames::MBMVIStr = "MBMVI";
QString BFPNames::ShutDownStr = "ShutDown";
QString BFPNames::NotReduceGainStr = "NotReduceGain";
QString BFPNames::FrequencyOfTransmit2Str = "FrequencyOfTransmit2";
QString BFPNames::HighFreqOfTransmit2Str = "HighFreqOfTransmit2";
QString BFPNames::PulseNumOfTransmit2Str = "PulseNumOfTransmit2";
QString BFPNames::FocusNumOfTransmit2Str = "FocusNumOfTransmit2";
QString BFPNames::RotationStr = "Rotation";
QString BFPNames::FilterCoefOfTransmit2Str = "FilterCoefOfTransmit2";
QString BFPNames::AlphaStr = "Alpha";
QString BFPNames::BetaStr = "Beta";
QString BFPNames::FrequencyOfTransmit3Str = "FrequencyOfTransmit3";
QString BFPNames::HighFreqOfTransmit3Str = "HighFreqOfTransmit3";
QString BFPNames::PulseNumOfTransmit3Str = "PulseNumOfTransmit3";
QString BFPNames::FocusNumOfTransmit3Str = "FocusNumOfTransmit3";
QString BFPNames::FilterCoefOfTransmit3Str = "FilterCoefOfTransmit3";
QString BFPNames::GammaStr = "Gamma";
QString BFPNames::WtStartPointStr = "WtStartPoint";
QString BFPNames::FrequencyOfTransmit4Str = "FrequencyOfTransmit4";
QString BFPNames::HighFreqOfTransmit4Str = "HighFreqOfTransmit4";
QString BFPNames::PulseNumOfTransmit4Str = "PulseNumOfTransmit4";
QString BFPNames::FocusNumOfTransmit4Str = "FocusNumOfTransmit4";
QString BFPNames::FilterCoefOfTransmit4Str = "FilterCoefOfTransmit4";
QString BFPNames::LeeMCoefStr = "LeeMCoef";
QString BFPNames::LeeSCoefStr = "LeeSCoef";
QString BFPNames::MFCStr = "MFC";
QString BFPNames::CWEnStr = "CWEn";
QString BFPNames::TDIEnStr = "TDIEn";
QString BFPNames::PWEnhanceStr = "PWEnhance";
QString BFPNames::PWEnhanceTMStr = "PWEnhanceTM";
QString BFPNames::PWEnhanceTDIStr = "PWEnhanceTDI";
QString BFPNames::PWEnhanceCWDStr = "PWEnhanceCWD";
QString BFPNames::LeeEnStr = "LeeEn";
QString BFPNames::HF_AlphaStr = "HF_Alpha";
QString BFPNames::HF_Alpha_IncStr = "HF_Alpha_Inc";
QString BFPNames::IIR_ONStr = "IIR_ON";
QString BFPNames::HFStr = "HF";
QString BFPNames::HFIncStr = "HFInc";
QString BFPNames::PixelRatioStr = "PixelRatio";
QString BFPNames::PixelRatioTMStr = "PixelRatioTM";
QString BFPNames::PixelRatioTDIStr = "PixelRatioTDI";
QString BFPNames::PixelRatioCWDStr = "PixelRatioCWD";
QString BFPNames::GateSegmentStr = "GateSegment";
QString BFPNames::GateSegmentTDIStr = "GateSegmentTDI";
QString BFPNames::GateSegmentCWDStr = "GateSegmentCWD";
QString BFPNames::Bi_CPAStr = "Bi_CPA";
QString BFPNames::DynStartDepthBinStr = "DynStartDepthBin";
QString BFPNames::DynDBRateStr = "DynDBRate";
QString BFPNames::DynDBCountStr = "DynDBCount";
QString BFPNames::WtCoefficienceStr = "WtCoefficience";
QString BFPNames::TransmitPowerPositiveVolStr = "TransmitPowerPositiveVol";
QString BFPNames::TransmitPowerNegativeVolStr = "TransmitPowerNegativeVol";
QString BFPNames::CWTransmitPowerPositiveVolStr = "CWTransmitPowerPositiveVol";
QString BFPNames::CWTransmitPowerNegativeVolStr = "CWTransmitPowerNegativeVol";
QString BFPNames::LeeGainStr = "LeeGain";
QString BFPNames::LeeShiftStr = "LeeShift";
QString BFPNames::PWDynamicRangeStr = "PWDynamicRange";
QString BFPNames::PWDynamicRangeTMStr = "PWDynamicRangeTM";
QString BFPNames::PWDynamicRangeTDIStr = "PWDynamicRangeTDI";
QString BFPNames::PWDynamicRangeCWDStr = "PWDynamicRangeCWD";
QString BFPNames::DPProbeImageSwitchStr = "DPProbeImageSwitch";
QString BFPNames::DPProbeIdentityStr = "DPProbeIdentity";
QString BFPNames::HV_OnStr = "HV_On";
QString BFPNames::MEProbStr = "MEProb";
QString BFPNames::AFCoef0Str = "AFCoef0";
QString BFPNames::AFCoef1Str = "AFCoef1";
QString BFPNames::AFCoef2Str = "AFCoef2";
QString BFPNames::AFCoef3Str = "AFCoef3";
QString BFPNames::PhasedProbeTgc0Str = "PhasedProbeTgc0";
QString BFPNames::PhasedProbeTgc1Str = "PhasedProbeTgc1";
QString BFPNames::PhasedProbeTgc2Str = "PhasedProbeTgc2";
QString BFPNames::PhasedProbeTgc3Str = "PhasedProbeTgc3";
QString BFPNames::PhasedProbeTgc4Str = "PhasedProbeTgc4";
QString BFPNames::PhasedProbeTgc5Str = "PhasedProbeTgc5";
QString BFPNames::PhasedProbeTgc6Str = "PhasedProbeTgc6";
QString BFPNames::PhasedProbeTgc7Str = "PhasedProbeTgc7";
QString BFPNames::AFCoef4Str = "AFCoef4";
QString BFPNames::TDIWallfilterStr = "TDIWallfilter";
QString BFPNames::AFGainStr = "AFGain";
QString BFPNames::AFShiftStr = "AFShift";
QString BFPNames::Image1FGC0Str = "Image1FGC0";
QString BFPNames::Image1FGC1Str = "Image1FGC1";
QString BFPNames::Image1FGC2Str = "Image1FGC2";
QString BFPNames::Image1FGC3Str = "Image1FGC3";
QString BFPNames::Image2FGC0Str = "Image2FGC0";
QString BFPNames::Image2FGC1Str = "Image2FGC1";
QString BFPNames::Image2FGC2Str = "Image2FGC2";
QString BFPNames::Image2FGC3Str = "Image2FGC3";
QString BFPNames::Image3FGC0Str = "Image3FGC0";
QString BFPNames::Image3FGC1Str = "Image3FGC1";
QString BFPNames::Image3FGC2Str = "Image3FGC2";
QString BFPNames::Image3FGC3Str = "Image3FGC3";
QString BFPNames::Image4FGC0Str = "Image4FGC0";
QString BFPNames::Image4FGC1Str = "Image4FGC1";
QString BFPNames::Image4FGC2Str = "Image4FGC2";
QString BFPNames::Image4FGC3Str = "Image4FGC3";
QString BFPNames::HFSZStr = "HFSZ";
QString BFPNames::HFSSPStr = "HFSSP";
QString BFPNames::ZoomOnStr = "ZoomOn";
QString BFPNames::ColorInvertStr = "ColorInvert";
QString BFPNames::CWDShiftStr = "CWDShift";
QString BFPNames::ColorMStr = "ColorM";
QString BFPNames::FilterCpdStr = "FilterCpd";
QString BFPNames::CWDSampleRateStr = "CWDSampleRate";
QString BFPNames::CWDDynamicRangeStr = "CWDDynamicRange";
QString BFPNames::ExtremeLowBloodStr = "ExtremeLowBlood";
QString BFPNames::ZoomInTopOffsetStr = "ZoomInTopOffset";
QString BFPNames::CWDFftStr = "CWDFft";
QString BFPNames::CWDIterationsStr = "CWDIterations";
QString BFPNames::TriplexRefreshStr = "TriplexRefresh";
QString BFPNames::FilterCpdCoeStr = "FilterCpdCoe";
QString BFPNames::BFPipelineNumberStr = "BFPipelineNumber";
QString BFPNames::LRInvertStr = "LRInvert";
QString BFPNames::CpdMinValueStr = "CpdMinValue";
QString BFPNames::CpdMaxValueStr = "CpdMaxValue";
QString BFPNames::CCosAngleStr = "CCosAngle";
QString BFPNames::CTgAngleStr = "CTgAngle";
QString BFPNames::BSteeringScanStr = "BSteeringScan";
QString BFPNames::CDucyStr = "CDucy";
QString BFPNames::CFilterCoefStr = "CFilterCoef";
QString BFPNames::CompensationCoefStr = "CompensationCoef";
QString BFPNames::APPrfStr = "APPrf";
QString BFPNames::APDutyRatioStr = "APDutyRatio";
QString BFPNames::ConvexCpdSteerStr = "ConvexCpdSteer";
QString BFPNames::Prf_D_Triplex_WorkStr = "Prf_D_Triplex_Work";
QString BFPNames::CFMAnologTgc0Str = "CFMAnologTgc0";
QString BFPNames::CFMAnologTgc1Str = "CFMAnologTgc1";
QString BFPNames::CFMAnologTgc2Str = "CFMAnologTgc2";
QString BFPNames::CFMAnologTgc3Str = "CFMAnologTgc3";
QString BFPNames::CFMAnologTgc4Str = "CFMAnologTgc4";
QString BFPNames::CFMAnologTgc5Str = "CFMAnologTgc5";
QString BFPNames::CFMAnologTgc6Str = "CFMAnologTgc6";
QString BFPNames::CFMAnologTgc7Str = "CFMAnologTgc7";
QString BFPNames::CFMAnologTgc8Str = "CFMAnologTgc8";
QString BFPNames::CFMAnologTgc9Str = "CFMAnologTgc9";
QString BFPNames::CFMAnologTgc10Str = "CFMAnologTgc10";
QString BFPNames::CFMAnologTgc11Str = "CFMAnologTgc11";
QString BFPNames::CFMAnologTgc12Str = "CFMAnologTgc12";
QString BFPNames::CFMAnologTgc13Str = "CFMAnologTgc13";
QString BFPNames::CFMAnologTgc14Str = "CFMAnologTgc14";
QString BFPNames::CFMAnologTgc15Str = "CFMAnologTgc15";
QString BFPNames::CFMDigitalTgc0Str = "CFMDigitalTgc0";
QString BFPNames::CFMDigitalTgc1Str = "CFMDigitalTgc1";
QString BFPNames::CFMDigitalTgc2Str = "CFMDigitalTgc2";
QString BFPNames::CFMDigitalTgc3Str = "CFMDigitalTgc3";
QString BFPNames::CFMDigitalTgc4Str = "CFMDigitalTgc4";
QString BFPNames::CFMDigitalTgc5Str = "CFMDigitalTgc5";
QString BFPNames::CFMDigitalTgc6Str = "CFMDigitalTgc6";
QString BFPNames::CFMDigitalTgc7Str = "CFMDigitalTgc7";
QString BFPNames::CFMDigitalTgc8Str = "CFMDigitalTgc8";
QString BFPNames::CFMDigitalTgc9Str = "CFMDigitalTgc9";
QString BFPNames::CFMDigitalTgc10Str = "CFMDigitalTgc10";
QString BFPNames::CFMDigitalTgc11Str = "CFMDigitalTgc11";
QString BFPNames::CFMDigitalTgc12Str = "CFMDigitalTgc12";
QString BFPNames::CFMDigitalTgc13Str = "CFMDigitalTgc13";
QString BFPNames::CFMDigitalTgc14Str = "CFMDigitalTgc14";
QString BFPNames::CFMDigitalTgc15Str = "CFMDigitalTgc15";
QString BFPNames::PacketSizeStr = "PacketSize";
QString BFPNames::PacketSizePDStr = "PacketSizePD";
QString BFPNames::PacketSizeSNStr = "PacketSizeSN";
QString BFPNames::PacketSizeTDIStr = "PacketSizeTDI";
QString BFPNames::PacketSizeMVIStr = "PacketSizeMVI";
QString BFPNames::DummyEnSampleStr = "DummyEnSample";
QString BFPNames::DummyEnSampleTMStr = "DummyEnSampleTM";
QString BFPNames::DummyEnSamplePDStr = "DummyEnSamplePD";
QString BFPNames::DummyEnSampleSNStr = "DummyEnSampleSN";
QString BFPNames::DummyEnSampleTDIStr = "DummyEnSampleTDI";
QString BFPNames::DummyEnSampleMVIStr = "DummyEnSampleMVI";
QString BFPNames::AudioFilterCoefSelStr = "AudioFilterCoefSel";
QString BFPNames::AudioFilterCoefSelTMStr = "AudioFilterCoefSelTM";
QString BFPNames::AudioFilterCoefSelTDIStr = "AudioFilterCoefSelTDI";
QString BFPNames::AudioFilterCoefSelCWDStr = "AudioFilterCoefSelCWD";
QString BFPNames::WFGainControlStr = "WFGainControl";
QString BFPNames::WFGainControlPDStr = "WFGainControlPD";
QString BFPNames::WFGainControlSNStr = "WFGainControlSN";
QString BFPNames::WFGainControlTDIStr = "WFGainControlTDI";
QString BFPNames::WFGainControlMVIStr = "WFGainControlMVI";
QString BFPNames::CLFGainStr = "CLFGain";
QString BFPNames::CLFShiftStr = "CLFShift";
QString BFPNames::CLFCoef0Str = "CLFCoef0";
QString BFPNames::CLFCoef1Str = "CLFCoef1";
QString BFPNames::CLFCoef2Str = "CLFCoef2";
QString BFPNames::CAFGainStr = "CAFGain";
QString BFPNames::CAFShiftStr = "CAFShift";
QString BFPNames::CAFCoef0Str = "CAFCoef0";
QString BFPNames::CAFCoef1Str = "CAFCoef1";
QString BFPNames::CAFCoef2Str = "CAFCoef2";
QString BFPNames::CAFCoef3Str = "CAFCoef3";
QString BFPNames::LComeBackStr = "LComeBack";
QString BFPNames::LComeBackPDStr = "LComeBackPD";
QString BFPNames::LComeBackSNStr = "LComeBackSN";
QString BFPNames::LComeBackTDIStr = "LComeBackTDI";
QString BFPNames::LComeBackMVIStr = "LComeBackMVI";
QString BFPNames::AComeBackStr = "AComeBack";
QString BFPNames::AComeBackPDStr = "AComeBackPD";
QString BFPNames::AComeBackSNStr = "AComeBackSN";
QString BFPNames::AComeBackTDIStr = "AComeBackTDI";
QString BFPNames::AComeBackMVIStr = "AComeBackMVI";
QString BFPNames::CFMIIReStr = "CFMIIRe";
QString BFPNames::PDIIReStr = "PDIIRe";
QString BFPNames::SNIIReStr = "SNIIRe";
QString BFPNames::TDIIIReStr = "TDIIIRe";
QString BFPNames::MVIIIReStr = "MVIIIRe";
QString BFPNames::HprfEnStr = "HprfEn";
QString BFPNames::FCA_SwitchStr = "FCA_Switch";
QString BFPNames::AffiliatedChipWriteStr = "AffiliatedChipWrite";
QString BFPNames::AffiliatedChipReadStr = "AffiliatedChipRead";
QString BFPNames::AffiliatedChipSelectSignalStr = "AffiliatedChipSelectSignal";
QString BFPNames::AffiliatedChipAddrStr = "AffiliatedChipAddr";
QString BFPNames::AffiliatedChipWriteDataStr = "AffiliatedChipWriteData";
QString BFPNames::FlashThresholdStr = "FlashThreshold";
QString BFPNames::LowThresholdStr = "LowThreshold";
QString BFPNames::MedThresholdStr = "MedThreshold";
QString BFPNames::HoleFillEnStr = "HoleFillEn";
QString BFPNames::MedFltBypStr = "MedFltByp";
QString BFPNames::CTxPulseDutyBinStr = "CTxPulseDutyBin";
QString BFPNames::FlashSupressStr = "FlashSupress";
QString BFPNames::CPriorityStr = "CPriority";
QString BFPNames::FCA_DeltaStr = "FCA_Delta";
QString BFPNames::FCA_GammaStr = "FCA_Gamma";
QString BFPNames::FCA_ThresholdUpLimitStr = "FCA_ThresholdUpLimit";
QString BFPNames::FCA_ThresholdLowLimitStr = "FCA_ThresholdLowLimit";
QString BFPNames::AdapPostProcSmoothStr = "AdapPostProcSmooth";
QString BFPNames::AdapPostProcStr = "AdapPostProc";
QString BFPNames::FGP0Str = "FGP0";
QString BFPNames::FGP1Str = "FGP1";
QString BFPNames::FGP2Str = "FGP2";
QString BFPNames::FGP3Str = "FGP3";
QString BFPNames::CFMQuitSamp_HeadStr = "CFMQuitSamp_Head";
QString BFPNames::CFMQuitSamp_TailStr = "CFMQuitSamp_Tail";
QString BFPNames::ScaningSignalEntireCompensationStr = "ScaningSignalEntireCompensation";
QString BFPNames::ScaningSignalIndividualCompensationStr = "ScaningSignalIndividualCompensation";
QString BFPNames::GaussFilterSwitchStr = "GaussFilterSwitch";
QString BFPNames::ElastoGaussFilterSwitchStr = "ElastoGaussFilterSwitch";
QString BFPNames::ThiModeStr = "ThiMode";
QString BFPNames::CfmApertureModeStr = "CfmApertureMode";
QString BFPNames::CfmDemodulationStr = "CfmDemodulation";
QString BFPNames::CFMVelLevelStr = "CFMVelLevel";
QString BFPNames::CFMVelLevelTMStr = "CFMVelLevelTM";
QString BFPNames::WaferNumDiffStr = "WaferNumDiff";
QString BFPNames::MaxTransmitApertureControlStr = "MaxTransmitApertureControl";
QString BFPNames::MinTransmitApertureControlStr = "MinTransmitApertureControl";
QString BFPNames::FNumDecimalStr = "FNumDecimal";
QString BFPNames::FNumIntegerStr = "FNumInteger";
QString BFPNames::FrequencyOfTransmit5Str = "FrequencyOfTransmit5";
QString BFPNames::HighFreqOfTransmit5Str = "HighFreqOfTransmit5";
QString BFPNames::PulseNumOfTransmit5Str = "PulseNumOfTransmit5";
QString BFPNames::FocusNumOfTransmit5Str = "FocusNumOfTransmit5";
QString BFPNames::FilterCoefOfTransmit5Str = "FilterCoefOfTransmit5";
QString BFPNames::FrequencyOfTransmit6Str = "FrequencyOfTransmit6";
QString BFPNames::HighFreqOfTransmit6Str = "HighFreqOfTransmit6";
QString BFPNames::PulseNumOfTransmit6Str = "PulseNumOfTransmit6";
QString BFPNames::FilterCoefOfTransmit6Str = "FilterCoefOfTransmit6";
QString BFPNames::TransmitPulseEx6Str = "TransmitPulseEx6";
QString BFPNames::ThiMode6Str = "ThiMode6";
QString BFPNames::FrequencyOfTransmit7Str = "FrequencyOfTransmit7";
QString BFPNames::HighFreqOfTransmit7Str = "HighFreqOfTransmit7";
QString BFPNames::PulseNumOfTransmit7Str = "PulseNumOfTransmit7";
QString BFPNames::TransmitPulseEx7Str = "TransmitPulseEx7";
QString BFPNames::FilterCoefOfTransmit7Str = "FilterCoefOfTransmit7";
QString BFPNames::ThiMode7Str = "ThiMode7";
QString BFPNames::NeedleModeStr = "NeedleMode";
QString BFPNames::NeedleAngleIndexStr = "NeedleAngleIndex";
QString BFPNames::NeedleDynamicRangeStr = "NeedleDynamicRange";
QString BFPNames::NeedleGainStr = "NeedleGain";
QString BFPNames::CfmMaxTransmitApertureControlBinStr = "CfmMaxTransmitApertureControlBin";
QString BFPNames::VideoInvertStr = "VideoInvert";
QString BFPNames::CfmMinTransmitApertureControlBinStr = "CfmMinTransmitApertureControlBin";
QString BFPNames::VideoInvertOfDStr = "VideoInvertOfD";
QString BFPNames::CfmFNumDecimalStr = "CfmFNumDecimal";
QString BFPNames::CfmFNumIntegerStr = "CfmFNumInteger";
QString BFPNames::BCRelativePosStr = "BCRelativePos";
QString BFPNames::ParaPresetTimeStr = "ParaPresetTime";
QString BFPNames::CqyzOfDStr = "CqyzOfD";
QString BFPNames::PrtOfBStr = "PrtOfB";
QString BFPNames::PrtOfCStr = "PrtOfC";
QString BFPNames::PrtOfEStr = "PrtOfE";
QString BFPNames::PrtOfDStr = "PrtOfD";
QString BFPNames::MSampleRateStr = "MSampleRate";
QString BFPNames::DSampleRateStr = "DSampleRate";
QString BFPNames::BCSampleRateStr = "BCSampleRate";
QString BFPNames::BFrameNumStr = "BFrameNum";
QString BFPNames::MFStitchStr = "MFStitch";
QString BFPNames::HBStitchStr = "HBStitch";
QString BFPNames::TotalFrameNumStr = "TotalFrameNum";
QString BFPNames::ECGSignalStr = "ECGSignal";
QString BFPNames::PWMDutyRatioStr = "PWMDutyRatio";
QString BFPNames::FourDBurnSignalStr = "FourDBurnSignal";
QString BFPNames::CFMEnergeCtlStr = "CFMEnergeCtl";
QString BFPNames::MaxGainStr = "MaxGain";
QString BFPNames::BTgcGainStr = "BTgcGain";
QString BFPNames::NeedleSSampleAdjustStr = "NeedleSSampleAdjust";
QString BFPNames::CfmEngCtrl0Str = "CfmEngCtrl0";
QString BFPNames::CfmEngCtrl1Str = "CfmEngCtrl1";
QString BFPNames::CfmEngCtrl2Str = "CfmEngCtrl2";
QString BFPNames::CfmEngCtrl3Str = "CfmEngCtrl3";
QString BFPNames::CfmEngCtrl4Str = "CfmEngCtrl4";
QString BFPNames::CfmEngCtrl5Str = "CfmEngCtrl5";
QString BFPNames::CfmEngCtrl6Str = "CfmEngCtrl6";
QString BFPNames::CfmEngCtrl7Str = "CfmEngCtrl7";
QString BFPNames::CfmEngCtrl8Str = "CfmEngCtrl8";
QString BFPNames::CfmEngCtrl9Str = "CfmEngCtrl9";
QString BFPNames::CfmEngCtrl10Str = "CfmEngCtrl10";
QString BFPNames::CfmEngCtrl11Str = "CfmEngCtrl11";
QString BFPNames::CfmEngCtrl12Str = "CfmEngCtrl12";
QString BFPNames::CfmFlashVltStr = "CfmFlashVlt";
QString BFPNames::CfmFlashSlopeStr = "CfmFlashSlope";
QString BFPNames::CfmNoiseEltStr = "CfmNoiseElt";
QString BFPNames::CfmNoiseVltStr = "CfmNoiseVlt";
QString BFPNames::CfmNoiseSlopeStr = "CfmNoiseSlope";
QString BFPNames::CStaticOptimizationStr = "CStaticOptimization";
QString BFPNames::ProbeInvertStr = "ProbeInvert";
QString BFPNames::FixedLineNumStr = "FixedLineNum";
QString BFPNames::ElemModeStr = "ElemMode";
QString BFPNames::FixedLineTypeStr = "FixedLineType";
QString BFPNames::FixedLineStartupStr = "FixedLineStartup";
QString BFPNames::WTByPassStr = "WTByPass";
QString BFPNames::ElemSignalStr = "ElemSignal";
QString BFPNames::CfmSlopeStr = "CfmSlope";
QString BFPNames::WaferNumDetectedStr = "WaferNumDetected";
QString BFPNames::PFrSd_PwrOldWtStr = "PFrSd_PwrOldWt";
QString BFPNames::EtBfWfStr = "EtBfWf";
QString BFPNames::NewWallThresholdStr = "NewWallThreshold";
QString BFPNames::ImgFrzStr = "ImgFrz";
QString BFPNames::CfmPersistentThresholdStr = "CfmPersistentThreshold";
QString BFPNames::WrapEnStr = "WrapEn";
QString BFPNames::CLFEnStr = "CLFEn";
QString BFPNames::CAFEnStr = "CAFEn";
QString BFPNames::DopOutGainStr = "DopOutGain";
QString BFPNames::TDOutGainStr = "TDOutGain";
QString BFPNames::CWOutGainStr = "CWOutGain";
QString BFPNames::DopFrequencyStr = "DopFrequency";
QString BFPNames::DopFilterCoefStr = "DopFilterCoef";
QString BFPNames::DopTxFNumStr = "DopTxFNum";
QString BFPNames::DopRvFNumStr = "DopRvFNum";
QString BFPNames::DopAudioSegmentStr = "DopAudioSegment";
QString BFPNames::TDAudioSegmentStr = "TDAudioSegment";
QString BFPNames::CWAudioSegmentStr = "CWAudioSegment";
QString BFPNames::DopDemodulationFilterCoefStr = "DopDemodulationFilterCoef";
QString BFPNames::DopDemodulationFreqStr = "DopDemodulationFreq";
QString BFPNames::DopPulseDutyStr = "DopPulseDuty";
QString BFPNames::DopSampleNumStr = "DopSampleNum";
QString BFPNames::TDSampleNumStr = "TDSampleNum";
QString BFPNames::CWSampleNumStr = "CWSampleNum";
QString BFPNames::DopMaxApertureStr = "DopMaxAperture";
QString BFPNames::WrapEnQStr = "WrapEnQ";
QString BFPNames::DopAudioPostGainStr = "DopAudioPostGain";
QString BFPNames::TDAudioPostGainStr = "TDAudioPostGain";
QString BFPNames::CWAudioPostGainStr = "CWAudioPostGain";
QString BFPNames::DopAudioPreGainStr = "DopAudioPreGain";
QString BFPNames::TDAudioPreGainStr = "TDAudioPreGain";
QString BFPNames::CWAudioPreGainStr = "CWAudioPreGain";
QString BFPNames::DopTimeFilterStr = "DopTimeFilter";
QString BFPNames::TDTimeFilterStr = "TDTimeFilter";
QString BFPNames::CWTimeFilterStr = "CWTimeFilter";
QString BFPNames::DopFilterLengthStr = "DopFilterLength";
QString BFPNames::TDFilterLengthStr = "TDFilterLength";
QString BFPNames::CWFilterLengthStr = "CWFilterLength";
QString BFPNames::DopVelocityFilterStr = "DopVelocityFilter";
QString BFPNames::TDVelocityFilterStr = "TDVelocityFilter";
QString BFPNames::CWVelocityFilterStr = "CWVelocityFilter";
QString BFPNames::DopMidGainStr = "DopMidGain";
QString BFPNames::TDMidGainStr = "TDMidGain";
QString BFPNames::CWMidGainStr = "CWMidGain";
QString BFPNames::VersionInforStr = "VersionInfor";
QString BFPNames::VersionDisplayModeStr = "VersionDisplayMode";
QString BFPNames::MotherboardShieldStr = "MotherboardShield";
QString BFPNames::ExtensionPlateShieldStr = "ExtensionPlateShield";
QString BFPNames::InspectionModeStr = "InspectionMode";
QString BFPNames::UnifTgcEnStr = "UnifTgcEn";
QString BFPNames::InspectionNumStr = "InspectionNum";
QString BFPNames::DopFNumDecimalStr = "DopFNumDecimal";
QString BFPNames::DopFNumIntegerStr = "DopFNumInteger";
QString BFPNames::XCoordinateStr = "XCoordinate";
QString BFPNames::YCoordinateStr = "YCoordinate";
QString BFPNames::XCoordinateEnStr = "XCoordinateEn";
QString BFPNames::YCoordinateEnStr = "YCoordinateEn";
QString BFPNames::LineNoShieldStr = "LineNoShield";
QString BFPNames::DopMinTransmitApertureStr = "DopMinTransmitAperture";
QString BFPNames::MinReceiveApertureStr = "MinReceiveAperture";
QString BFPNames::MaxReceiveApertureStr = "MaxReceiveAperture";
QString BFPNames::ReceiveFNumDecimalStr = "ReceiveFNumDecimal";
QString BFPNames::ReceiveFNumIntegerStr = "ReceiveFNumInteger";
QString BFPNames::CfmMinReceiveApertureStr = "CfmMinReceiveAperture";
QString BFPNames::CfmMaxReceiveApertureStr = "CfmMaxReceiveAperture";
QString BFPNames::CfmReceiveFNumDecimalStr = "CfmReceiveFNumDecimal";
QString BFPNames::CfmReceiveFNumIntegerStr = "CfmReceiveFNumInteger";
QString BFPNames::DopMinReceiveApertureStr = "DopMinReceiveAperture";
QString BFPNames::DopMaxReceiveApertureStr = "DopMaxReceiveAperture";
QString BFPNames::DopReceiveFNumDecimalStr = "DopReceiveFNumDecimal";
QString BFPNames::DopReceiveFNumIntegerStr = "DopReceiveFNumInteger";
QString BFPNames::ShieldSPPositionStr = "ShieldSPPosition";
QString BFPNames::ShieldSPEnStr = "ShieldSPEn";
QString BFPNames::TeeFlagStr = "TeeFlag";
QString BFPNames::PixelsWithinFocusStr = "PixelsWithinFocus";
QString BFPNames::DopAccumulateNumStr = "DopAccumulateNum";
QString BFPNames::ECGDlyStr = "ECGDly";
QString BFPNames::MECGDlyStr = "MECGDly";
QString BFPNames::CWECGDlyStr = "CWECGDly";
QString BFPNames::FreeMECGDlyStr = "FreeMECGDly";
QString BFPNames::DECGDlyStr = "DECGDly";
QString BFPNames::DTDIECGDlyStr = "DTDIECGDly";
QString BFPNames::CEmissionFrequencyCoefStr = "CEmissionFrequencyCoef";
QString BFPNames::CTransmitPulseDutyCoefStr = "CTransmitPulseDutyCoef";
QString BFPNames::NeedleEmissionFrequencyCoefStr = "NeedleEmissionFrequencyCoef";
QString BFPNames::NeedleTransmitPulseDutyCoefStr = "NeedleTransmitPulseDutyCoef";
QString BFPNames::CSteeringAngleCodingStr = "CSteeringAngleCoding";
QString BFPNames::NeedleSteeringAngleCodingStr = "NeedleSteeringAngleCoding";
QString BFPNames::BSteeringAngleCodingStr = "BSteeringAngleCoding";
QString BFPNames::CWeightedCurveTypeStr = "CWeightedCurveType";
QString BFPNames::NeedleWeightedCurveTypeStr = "NeedleWeightedCurveType";
QString BFPNames::DWeightedCurveTypeStr = "DWeightedCurveType";
QString BFPNames::UnzoomedCQYZStr = "UnzoomedCQYZ";
QString BFPNames::ZoomDepthPixelStr = "ZoomDepthPixel";
QString BFPNames::THISelStr = "THISel";
QString BFPNames::FScpdStr = "FScpd";
QString BFPNames::ThiMode4Str = "ThiMode4";
QString BFPNames::ThiMode3Str = "ThiMode3";
QString BFPNames::ThiMode2Str = "ThiMode2";
QString BFPNames::ThiMode1Str = "ThiMode1";
QString BFPNames::MachineIdentityStr = "MachineIdentity";
QString BFPNames::LinePackageCountStr = "LinePackageCount";
QString BFPNames::LineDataModeStr = "LineDataMode";
QString BFPNames::InfoLineEnableSignalStr = "InfoLineEnableSignal";
QString BFPNames::TableIDStr = "TableID";
QString BFPNames::DebugPara5Str = "DebugPara5";
QString BFPNames::AudioTest_LRStr = "AudioTest_LR";
QString BFPNames::AudioTest_ToneStr = "AudioTest_Tone";
QString BFPNames::BTriplexPrt_DeltaStr = "BTriplexPrt_Delta";
QString BFPNames::ThiTxDummyStr = "ThiTxDummy";
QString BFPNames::FFocusPrtDeltaStr = "FFocusPrtDelta";
QString BFPNames::VesselThresholdStr = "VesselThreshold";
QString BFPNames::DetailWeightBinStr = "DetailWeightBin";
QString BFPNames::MinWeightBinStr = "MinWeightBin";
QString BFPNames::MaxWeightBinStr = "MaxWeightBin";
QString BFPNames::EdgeWeightBinStr = "EdgeWeightBin";
QString BFPNames::ECGDynamicRangeStr = "ECGDynamicRange";
QString BFPNames::SpecFrhStr = "SpecFrh";
QString BFPNames::CWSwithCodeStr = "CWSwithCode";
QString BFPNames::LineCpdParaStr = "LineCpdPara";
QString BFPNames::DLineCorrectParaStr = "DLineCorrectPara";
QString BFPNames::MLineCorrectParaStr = "MLineCorrectPara";
QString BFPNames::TDLineCorrectParaStr = "TDLineCorrectPara";
QString BFPNames::MDynamicRangeStr = "MDynamicRange";
QString BFPNames::BWeightedCurveTypeStr = "BWeightedCurveType";
QString BFPNames::CWGainReductionStr = "CWGainReduction";
QString BFPNames::CWGainReductionSlopeStr = "CWGainReductionSlope";
QString BFPNames::VideoSOCutHStr = "VideoSOCutH";
QString BFPNames::VideoSOCutVStr = "VideoSOCutV";
QString BFPNames::VideoEOCutHStr = "VideoEOCutH";
QString BFPNames::VideoEOCutVStr = "VideoEOCutV";
QString BFPNames::VideoCprsHSizeStr = "VideoCprsHSize";
QString BFPNames::VideoCprsVSizeStr = "VideoCprsVSize";
QString BFPNames::VideoCprsCoefStr = "VideoCprsCoef";
QString BFPNames::VideoFectureStr = "VideoFecture";
QString BFPNames::VideoSwitchStr = "VideoSwitch";
QString BFPNames::VideoCprsEnStr = "VideoCprsEn";
QString BFPNames::UpLoadBuffFrzStr = "UpLoadBuffFrz";
QString BFPNames::BTxEndClampStr = "BTxEndClamp";
QString BFPNames::CTxEndClampStr = "CTxEndClamp";
QString BFPNames::BTxApertureCoefStr = "BTxApertureCoef";
QString BFPNames::CTxApertureCoefStr = "CTxApertureCoef";
QString BFPNames::DTxApertureCoefStr = "DTxApertureCoef";
QString BFPNames::MGainStr = "MGain";
QString BFPNames::MGainThiStr = "MGainThi";
QString BFPNames::ElastoDRStr = "ElastoDR";
QString BFPNames::ElastoSmoothFilterStr = "ElastoSmoothFilter";
QString BFPNames::ElastoEnStr = "ElastoEn";
QString BFPNames::ElastoStretchGainStr = "ElastoStretchGain";
QString BFPNames::ElastoEUniformityTgc0Str = "ElastoEUniformityTgc0";
QString BFPNames::ElastoEUniformityTgc1Str = "ElastoEUniformityTgc1";
QString BFPNames::ElastoEUniformityTgc2Str = "ElastoEUniformityTgc2";
QString BFPNames::ElastoEUniformityTgc3Str = "ElastoEUniformityTgc3";
QString BFPNames::ElastoEUniformityTgc4Str = "ElastoEUniformityTgc4";
QString BFPNames::ElastoEUniformityTgc5Str = "ElastoEUniformityTgc5";
QString BFPNames::ElastoEUniformityTgc6Str = "ElastoEUniformityTgc6";
QString BFPNames::ElastoEUniformityTgc7Str = "ElastoEUniformityTgc7";
QString BFPNames::ElastoEUniformityTgc8Str = "ElastoEUniformityTgc8";
QString BFPNames::ElastoEUniformityTgc9Str = "ElastoEUniformityTgc9";
QString BFPNames::ElastoEUniformityTgc10Str = "ElastoEUniformityTgc10";
QString BFPNames::ElastoEUniformityTgc11Str = "ElastoEUniformityTgc11";
QString BFPNames::ElastoEUniformityTgc12Str = "ElastoEUniformityTgc12";
QString BFPNames::ElastoEUniformityTgc13Str = "ElastoEUniformityTgc13";
QString BFPNames::ElastoEUniformityTgc14Str = "ElastoEUniformityTgc14";
QString BFPNames::ElastoEUniformityTgc15Str = "ElastoEUniformityTgc15";
QString BFPNames::ElastoFrameGapStr = "ElastoFrameGap";
QString BFPNames::ElastoProcessDelayStr = "ElastoProcessDelay";
QString BFPNames::ElastoDebug4Str = "ElastoDebug4";
QString BFPNames::ElastoMedianPostStr = "ElastoMedianPost";
QString BFPNames::ElastoStrainFilterStr = "ElastoStrainFilter";
QString BFPNames::ElastoDigitalGainRangeStr = "ElastoDigitalGainRange";
QString BFPNames::ElastoMedianPreStr = "ElastoMedianPre";
QString BFPNames::ElastoDebug3Str = "ElastoDebug3";
QString BFPNames::ElastoDebug1Str = "ElastoDebug1";
QString BFPNames::ElastoDebug2Str = "ElastoDebug2";
QString BFPNames::ElastoDebug5Str = "ElastoDebug5";
QString BFPNames::ElastoThresholdPreStr = "ElastoThresholdPre";
QString BFPNames::ElastoThresholdPostStr = "ElastoThresholdPost";
QString BFPNames::ElastoStrainGainStr = "ElastoStrainGain";
QString BFPNames::ElastoPersistentCoefStr = "ElastoPersistentCoef";
QString BFPNames::ElastoAxialCoefStr = "ElastoAxialCoef";
QString BFPNames::ElastoLateralCoefStr = "ElastoLateralCoef";
QString BFPNames::ElastoSearchSizeStr = "ElastoSearchSize";
QString BFPNames::ElastoKernelSizeStr = "ElastoKernelSize";
QString BFPNames::NRebootStr = "NReboot";
QString BFPNames::ERebootStr = "EReboot";
QString BFPNames::KeyBoardLightEnStr = "KeyBoardLightEn";
QString BFPNames::KeyBoardLightLevelStr = "KeyBoardLightLevel";
QString BFPNames::FreeMModeStr = "FreeMMode";
QString BFPNames::ConvexRadiusStr = "ConvexRadius";
QString BFPNames::CWTxFocusDisStr = "CWTxFocusDis";
QString BFPNames::CWRvFocusPosStr = "CWRvFocusPos";
QString BFPNames::MDFGainStr = "MDFGain";
QString BFPNames::MDFShiftStr = "MDFShift";
QString BFPNames::MDFCGainStr = "MDFCGain";
QString BFPNames::MDFCShiftStr = "MDFCShift";
QString BFPNames::AFE_LPF_FCutOffStr = "AFE_LPF_FCutOff";
QString BFPNames::AFE_PDWN_VCA_PGAStr = "AFE_PDWN_VCA_PGA";
QString BFPNames::AFE_PGA_HPF_DISStr = "AFE_PGA_HPF_DIS";
QString BFPNames::AFE_PGA_GAINStr = "AFE_PGA_GAIN";
QString BFPNames::AFE_LNA_HPF_DISStr = "AFE_LNA_HPF_DIS";
QString BFPNames::AFE_Auto_Low_Pass_ENStr = "AFE_Auto_Low_Pass_EN";
QString BFPNames::AFE_LNA_GAINStr = "AFE_LNA_GAIN";
QString BFPNames::AFE_ACT_TERM_ENStr = "AFE_ACT_TERM_EN";
QString BFPNames::AFE_InImpedenceStr = "AFE_InImpedence";
QString BFPNames::AFE_HPF_FCutOffStr = "AFE_HPF_FCutOff";
QString BFPNames::AFE_LNA_BiasStr = "AFE_LNA_Bias";
QString BFPNames::AFE_CW_HPF_ENStr = "AFE_CW_HPF_EN";
QString BFPNames::AFE_CW_HPF_FB_RESStr = "AFE_CW_HPF_FB_RES";
QString BFPNames::AFE_DIS_CW_AMPStr = "AFE_DIS_CW_AMP";
QString BFPNames::AFE_CW_SUM_AMP_GAINStr = "AFE_CW_SUM_AMP_GAIN";
QString BFPNames::AFE_CW_ACT_TERM_ENStr = "AFE_CW_ACT_TERM_EN";
QString BFPNames::AFE_CW_InImpedenceStr = "AFE_CW_InImpedence";
QString BFPNames::AFE_PGA_HI_FREQStr = "AFE_PGA_HI_FREQ";
QString BFPNames::AFE_PGA_CLAMP_LVLStr = "AFE_PGA_CLAMP_LVL";
QString BFPNames::AFE_CW_LNA_GAINStr = "AFE_CW_LNA_GAIN";
QString BFPNames::DebugPara6Str = "DebugPara6";
QString BFPNames::DebugPara7Str = "DebugPara7";
QString BFPNames::DebugPara8Str = "DebugPara8";
QString BFPNames::DebugPara9Str = "DebugPara9";
QString BFPNames::BlockDCFilterSwitch1Str = "BlockDCFilterSwitch1";
QString BFPNames::BlockDCFilterSwitch2Str = "BlockDCFilterSwitch2";
QString BFPNames::FrameScapeEnableStr = "FrameScapeEnable";
QString BFPNames::FrameScapeEnableTMStr = "FrameScapeEnableTM";
QString BFPNames::BFrameScapeTimeStr = "BFrameScapeTime";
QString BFPNames::CFrameScapeTimeStr = "CFrameScapeTime";
QString BFPNames::CWGainStr = "CWGain";
QString BFPNames::TransmitPulseEx1Str = "TransmitPulseEx1";
QString BFPNames::TransmitPulseEx2Str = "TransmitPulseEx2";
QString BFPNames::TransmitPulseEx3Str = "TransmitPulseEx3";
QString BFPNames::TransmitPulseEx4Str = "TransmitPulseEx4";
QString BFPNames::TransmitPulseEx5Str = "TransmitPulseEx5";
QString BFPNames::ClampStr = "Clamp";
QString BFPNames::AFE_LOW_POWERStr = "AFE_LOW_POWER";
QString BFPNames::START_GAIN_0Str = "START_GAIN_0";
QString BFPNames::STOP_GAIN_0Str = "STOP_GAIN_0";
QString BFPNames::POS_STEP_0Str = "POS_STEP_0";
QString BFPNames::NEG_STEP_0Str = "NEG_STEP_0";
QString BFPNames::START_GAIN_POSITIONStr = "START_GAIN_POSITION";
QString BFPNames::STOP_GAIN_POSITIONStr = "STOP_GAIN_POSITION";
QString BFPNames::POSITION_STEP_NUMStr = "POSITION_STEP_NUM";
QString BFPNames::POSITION_STEP_CLKStr = "POSITION_STEP_CLK";
QString BFPNames::START_GAIN_1Str = "START_GAIN_1";
QString BFPNames::STOP_GAIN_1Str = "STOP_GAIN_1";
QString BFPNames::POS_STEP_1Str = "POS_STEP_1";
QString BFPNames::NEG_STEP_1Str = "NEG_STEP_1";
QString BFPNames::START_GAIN_POSITION_2Str = "START_GAIN_POSITION_2";
QString BFPNames::STOP_GAIN_POSITION_2Str = "STOP_GAIN_POSITION_2";
QString BFPNames::POSITION_STEP_NUM_2Str = "POSITION_STEP_NUM_2";
QString BFPNames::POSITION_STEP_CLK_2Str = "POSITION_STEP_CLK_2";
QString BFPNames::START_GAIN_DStr = "START_GAIN_D";
QString BFPNames::STOP_GAIN_DStr = "STOP_GAIN_D";
QString BFPNames::POS_STEP_DStr = "POS_STEP_D";
QString BFPNames::NEG_STEP_DStr = "NEG_STEP_D";
QString BFPNames::START_GAIN_POSITION_DStr = "START_GAIN_POSITION_D";
QString BFPNames::STOP_GAIN_POSITION_DStr = "STOP_GAIN_POSITION_D";
QString BFPNames::POSITION_STEP_NUM_DStr = "POSITION_STEP_NUM_D";
QString BFPNames::POSITION_STEP_CLK_DStr = "POSITION_STEP_CLK_D";
QString BFPNames::BCRelativePosInDModeStr = "BCRelativePosInDMode";
QString BFPNames::BFSRAStr = "BFSRA";
QString BFPNames::CfmAliasThresholdStr = "CfmAliasThreshold";
QString BFPNames::TdiAliasThresholdStr = "TdiAliasThreshold";
QString BFPNames::CfmTransmitClampStr = "CfmTransmitClamp";
QString BFPNames::ColorPixelFilterStr = "ColorPixelFilter";
QString BFPNames::PdPixelFilterStr = "PdPixelFilter";
QString BFPNames::MVIPixelFilterStr = "MVIPixelFilter";
QString BFPNames::DSCMidLineDiffStr = "DSCMidLineDiff";
QString BFPNames::EdgeStr = "Edge";
QString BFPNames::EdgeIncStr = "EdgeInc";
QString BFPNames::FreqIndexBStr = "FreqIndexB";
QString BFPNames::Pen_prbStr = "Pen_prb";
QString BFPNames::ProbeDimension1P25Str = "ProbeDimension1P25";
QString BFPNames::SRAIDStr = "SRAID";
QString BFPNames::SCPDIDStr = "SCPDID";
QString BFPNames::SuperHFreqStr = "SuperHFreq";
QString BFPNames::SpklSmooth_CaseSelStr = "SpklSmooth_CaseSel";
QString BFPNames::SpklSooth_alphaStr = "SpklSooth_alpha";
QString BFPNames::BModeDutyStr = "BModeDuty";
QString BFPNames::ATGC_GATEStr = "ATGC_GATE";
QString BFPNames::SoGataCompensationStr = "SoGataCompensation";
QString BFPNames::TxGataCompensationStr = "TxGataCompensation";
QString BFPNames::LogCompressionHarStr = "LogCompressionHar";
QString BFPNames::UDLayoutValueStr = "UDLayoutValue";
QString BFPNames::UTGC_FOCUSPOS_RelatedStr = "UTGC_FOCUSPOS_Related";
QString BFPNames::AutoPowerDown_OFFStr = "AutoPowerDown_OFF";
QString BFPNames::WaferNumCodeStr = "WaferNumCode";
QString BFPNames::WeightSlopeStr = "WeightSlope";
QString BFPNames::ADC_HPF_ENStr = "ADC_HPF_EN";
QString BFPNames::ADC_HPF_ROUND_ENStr = "ADC_HPF_ROUND_EN";
QString BFPNames::ADC_HPF_CORNER_FREQStr = "ADC_HPF_CORNER_FREQ";
QString BFPNames::DataSyncIDStr = "DataSyncID";
QString BFPNames::CurvedExpandingCodeStr = "CurvedExpandingCode";
QString BFPNames::CurvedExpandingOnStr = "CurvedExpandingOn";
QString BFPNames::LaparoscopicProbeIdenStr = "LaparoscopicProbeIden";
QString BFPNames::FocusThresholdStr = "FocusThreshold";
QString BFPNames::DataCompare_CompartorStr = "DataCompare_Compartor";
QString BFPNames::DataCompare_LengthStr = "DataCompare_Length";
QString BFPNames::DataCompare_RouteStr = "DataCompare_Route";
QString BFPNames::DataCompare_StartPointStr = "DataCompare_StartPoint";
QString BFPNames::DataCompare_SwitchStr = "DataCompare_Switch";
QString BFPNames::DataCompare_ThresholdStr = "DataCompare_Threshold";
QString BFPNames::MulitSlotReadStr = "MulitSlotRead";
QString BFPNames::NeedleTHIStr = "NeedleTHI";
QString BFPNames::NeedleLogCompressionStr = "NeedleLogCompression";
QString BFPNames::ProbeButton1ClearFlagStr = "ProbeButton1ClearFlag";
QString BFPNames::ProbeButton2ClearFlagStr = "ProbeButton2ClearFlag";
QString BFPNames::ProbeButton3ClearFlagStr = "ProbeButton3ClearFlag";
QString BFPNames::SF_AFCoef0Str = "SF_AFCoef0";
QString BFPNames::SF_AFCoef1Str = "SF_AFCoef1";
QString BFPNames::SF_AFCoef2Str = "SF_AFCoef2";
QString BFPNames::SF_AFCoef3Str = "SF_AFCoef3";
QString BFPNames::SF_AFCoef4Str = "SF_AFCoef4";
QString BFPNames::SF_AFGainStr = "SF_AFGain";
QString BFPNames::SF_AFShiftStr = "SF_AFShift";
QString BFPNames::SF_FlashMaxThresStr = "SF_FlashMaxThres";
QString BFPNames::SF_LeeEnStr = "SF_LeeEn";
QString BFPNames::SF_LeeGainStr = "SF_LeeGain";
QString BFPNames::SF_LeeMCoefStr = "SF_LeeMCoef";
QString BFPNames::SF_LeeSCoefStr = "SF_LeeSCoef";
QString BFPNames::SF_LeeShiftStr = "SF_LeeShift";
QString BFPNames::SF_MergeModeStr = "SF_MergeMode";
QString BFPNames::SF_MinCpdThresholdStr = "SF_MinCpdThreshold";
QString BFPNames::SF_TissueMaxThresStr = "SF_TissueMaxThres";
QString BFPNames::SF_TissueStraightModeStr = "SF_TissueStraightMode";
QString BFPNames::SFEnStr = "SFEn";
QString BFPNames::SFFrameGainStr = "SFFrameGain";
QString BFPNames::SFMinThresholdStr = "SFMinThreshold";
QString BFPNames::SFStraightFlagStr = "SFStraightFlag";
QString BFPNames::DRChangeTimesSFlowStr = "DRChangeTimesSFlow";
QString BFPNames::DRRateOfChangeSFlowStr = "DRRateOfChangeSFlow";
QString BFPNames::DRStartDbSFlowStr = "DRStartDbSFlow";
QString BFPNames::DRStartDepthSFlowStr = "DRStartDepthSFlow";
QString BFPNames::LogCompressionSFlowStr = "LogCompressionSFlow";
QString BFPNames::SimulateProbeHWCode1Str = "SimulateProbeHWCode1";
QString BFPNames::SimulateProbeHWCode2Str = "SimulateProbeHWCode2";
QString BFPNames::SimulateProbeSocketStr = "SimulateProbeSocket";
QString BFPNames::IsSimulateProbeStr = "IsSimulateProbe";
QString BFPNames::DataSamplingStartStr = "DataSamplingStart";
QString BFPNames::DataSamplingADChannelStr = "DataSamplingADChannel";
QString BFPNames::DataSamplingLengthStr = "DataSamplingLength";
QString BFPNames::DataSamplingTypeStr = "DataSamplingType";
QString BFPNames::TrapezoidalModeStr = "TrapezoidalMode";
QString BFPNames::HPRFAnalogTgc0Str = "HPRFAnalogTgc0";
QString BFPNames::HPRFAnalogTgc1Str = "HPRFAnalogTgc1";
QString BFPNames::HPRFAnalogTgc2Str = "HPRFAnalogTgc2";
QString BFPNames::HPRFAnalogTgc3Str = "HPRFAnalogTgc3";
QString BFPNames::HPRFAnalogTgc4Str = "HPRFAnalogTgc4";
QString BFPNames::HPRFAnalogTgc5Str = "HPRFAnalogTgc5";
QString BFPNames::HPRFAnalogTgc6Str = "HPRFAnalogTgc6";
QString BFPNames::HPRFAnalogTgc7Str = "HPRFAnalogTgc7";
QString BFPNames::HPRFDynamicFocusStartStr = "HPRFDynamicFocusStart";
QString BFPNames::HPRFWeightStartStr = "HPRFWeightStart";
QString BFPNames::DopSampleDepthWithClockSizeStr = "DopSampleDepthWithClockSize";
QString BFPNames::HoloDBaseLine2Str = "HoloDBaseLine2";
QString BFPNames::HoloDBaseLine3Str = "HoloDBaseLine3";
QString BFPNames::HoloDBaseLine4Str = "HoloDBaseLine4";
QString BFPNames::HoloDSampleDepthWithClockSize2Str = "HoloDSampleDepthWithClockSize2";
QString BFPNames::HoloDSampleDepthWithClockSize3Str = "HoloDSampleDepthWithClockSize3";
QString BFPNames::HoloDSampleDepthWithClockSize4Str = "HoloDSampleDepthWithClockSize4";
QString BFPNames::HoloDScanLine2Str = "HoloDScanLine2";
QString BFPNames::HoloDScanLine3Str = "HoloDScanLine3";
QString BFPNames::HoloDScanLine4Str = "HoloDScanLine4";
QString BFPNames::HoloENStr = "HoloEN";
QString BFPNames::HoloIndexStr = "HoloIndex";
QString BFPNames::HoloNumStr = "HoloNum";
QString BFPNames::HoloPRT2Str = "HoloPRT2";
QString BFPNames::HoloPRT3Str = "HoloPRT3";
QString BFPNames::HoloPRT4Str = "HoloPRT4";
QString BFPNames::HoloPW_TX_FOCUS_DIS2Str = "HoloPW_TX_FOCUS_DIS2";
QString BFPNames::HoloPW_TX_FOCUS_DIS3Str = "HoloPW_TX_FOCUS_DIS3";
QString BFPNames::HoloPW_TX_FOCUS_DIS4Str = "HoloPW_TX_FOCUS_DIS4";
QString BFPNames::PWFocusPosCom1Str = "PWFocusPosCom1";
QString BFPNames::PWFocusPosCom2Str = "PWFocusPosCom2";
QString BFPNames::PWFocusPosCom3Str = "PWFocusPosCom3";
QString BFPNames::TXLensDelayValStr = "TXLensDelayVal";
QString BFPNames::RXLensDelayValStr = "RXLensDelayVal";
QString BFPNames::TxSyncSwitchStr = "TxSyncSwitch";
QString BFPNames::ZoomCenterLineChooseStr = "ZoomCenterLineChoose";
QString BFPNames::TX_STEP_DISTStr = "TX_STEP_DIST";
QString BFPNames::TX_STEP_DIST_RECIPStr = "TX_STEP_DIST_RECIP";
QString BFPNames::B_RX_FNUMStr = "B_RX_FNUM";
QString BFPNames::B_RX_LINE_ANGLE_INTERVALStr = "B_RX_LINE_ANGLE_INTERVAL";
QString BFPNames::B_RX_LNUMStr = "B_RX_LNUM";
QString BFPNames::B_RX_OFFSET_COEFStr = "B_RX_OFFSET_COEF";
QString BFPNames::B_TX_LINE_ANGLE_INTERVALStr = "B_TX_LINE_ANGLE_INTERVAL";
QString BFPNames::B_TX_LNUMStr = "B_TX_LNUM";
QString BFPNames::B_TX_OFFSET_COEFStr = "B_TX_OFFSET_COEF";
QString BFPNames::RX_STEP_DISTStr = "RX_STEP_DIST";
QString BFPNames::RX_STEP_DIST_RECIStr = "RX_STEP_DIST_RECI";
QString BFPNames::C_RX_FNUMStr = "C_RX_FNUM";
QString BFPNames::C_RX_LINE_ANGLE_INTERVALStr = "C_RX_LINE_ANGLE_INTERVAL";
QString BFPNames::C_RX_LNUMStr = "C_RX_LNUM";
QString BFPNames::C_RX_OFFSET_COEFStr = "C_RX_OFFSET_COEF";
QString BFPNames::C_TX_LINE_ANGLE_INTERVALStr = "C_TX_LINE_ANGLE_INTERVAL";
QString BFPNames::C_TX_LNUMStr = "C_TX_LNUM";
QString BFPNames::C_TX_OFFSET_COEFStr = "C_TX_OFFSET_COEF";
QString BFPNames::C_WALL_OPTIONStr = "C_WALL_OPTION";
QString BFPNames::MAX_RADIUS_ANGLEStr = "MAX_RADIUS_ANGLE";
QString BFPNames::PROB_ELEM_NUMStr = "PROB_ELEM_NUM";
QString BFPNames::SHVSW_NOStr = "SHVSW_NO";
QString BFPNames::EHVSW_NOStr = "EHVSW_NO";
QString BFPNames::HVSW_ConfigStr = "HVSW_Config";
QString BFPNames::HVSW_DNStr = "HVSW_DN";
QString BFPNames::HVSW_NUMStr = "HVSW_NUM";
QString BFPNames::ChannelConnectionModeStr = "ChannelConnectionMode";
QString BFPNames::ChannelIdentifierStr = "ChannelIdentifier";
QString BFPNames::AngleSpacingCStr = "AngleSpacingC";
QString BFPNames::LineSpacingCStr = "LineSpacingC";
QString BFPNames::LensDepthStr = "LensDepth";
QString BFPNames::FocDepthStr = "FocDepth";
QString BFPNames::SAMP_DIST_RECIPStr = "SAMP_DIST_RECIP";
QString BFPNames::PA_VERT_DIST_EnableStr = "PA_VERT_DIST_Enable";
QString BFPNames::PA_VERT_DISTStr = "PA_VERT_DIST";
QString BFPNames::DSCCenterLineStr = "DSCCenterLine";
QString BFPNames::HTGC_LNUM_PER_SEGMENTStr = "HTGC_LNUM_PER_SEGMENT";
QString BFPNames::HTGC_LNUM_PER_SEGMENT_RECIPStr = "HTGC_LNUM_PER_SEGMENT_RECIP";
QString BFPNames::ACW_RX_STEP_DIST_RECIPStr = "ACW_RX_STEP_DIST_RECIP";
QString BFPNames::ACW_DCWStr = "ACW_DCW";
QString BFPNames::CW_HV_SafeStr = "CW_HV_Safe";
QString BFPNames::CW_TX_CHAN_SELStr = "CW_TX_CHAN_SEL";
QString BFPNames::CW_TX_RX_SWITCHStr = "CW_TX_RX_SWITCH";
QString BFPNames::TRANSMIT_APERTURE_ADD_SYMMETRYStr = "TRANSMIT_APERTURE_ADD_SYMMETRY";
QString BFPNames::ELEMENT_CHANNEL_MAPPINGStr = "ELEMENT_CHANNEL_MAPPING";
QString BFPNames::D_RX_FNUMStr = "D_RX_FNUM";
QString BFPNames::Before_Zoom_B_RX_LNUMStr = "Before_Zoom_B_RX_LNUM";
QString BFPNames::PB_PitchStr = "PB_Pitch";
QString BFPNames::BTXFnumCoefStr = "BTXFnumCoef";
QString BFPNames::AmplifyingFrameBoundaryDepthZoomStr = "AmplifyingFrameBoundaryDepthZoom";
QString BFPNames::Txbf2CRCStr = "Txbf2CRC";
QString BFPNames::TxbfCRCStr = "TxbfCRC";
QString BFPNames::CPLD2EnFlag_QBit10Str = "CPLD2EnFlag_QBit10";
QString BFPNames::CPLDCRCStr = "CPLDCRC";
QString BFPNames::DspCRCStr = "DspCRC";
QString BFPNames::Tx1Code1Str = "Tx1Code1";
QString BFPNames::Tx1Code1LenStr = "Tx1Code1Len";
QString BFPNames::Tx1Code2Str = "Tx1Code2";
QString BFPNames::Tx1Code2LenStr = "Tx1Code2Len";
QString BFPNames::Tx1TransverseFilterLenStr = "Tx1TransverseFilterLen";
QString BFPNames::Tx2Code1Str = "Tx2Code1";
QString BFPNames::Tx2Code1LenStr = "Tx2Code1Len";
QString BFPNames::Tx2Code2Str = "Tx2Code2";
QString BFPNames::Tx2Code2LenStr = "Tx2Code2Len";
QString BFPNames::Tx2TransverseFilterLenStr = "Tx2TransverseFilterLen";
QString BFPNames::Tx3Code1Str = "Tx3Code1";
QString BFPNames::Tx3Code1LenStr = "Tx3Code1Len";
QString BFPNames::Tx3Code2Str = "Tx3Code2";
QString BFPNames::Tx3Code2LenStr = "Tx3Code2Len";
QString BFPNames::Tx3TransverseFilterLenStr = "Tx3TransverseFilterLen";
QString BFPNames::Tx4Code1Str = "Tx4Code1";
QString BFPNames::Tx4Code1LenStr = "Tx4Code1Len";
QString BFPNames::Tx4Code2Str = "Tx4Code2";
QString BFPNames::Tx4Code2LenStr = "Tx4Code2Len";
QString BFPNames::Tx4TransverseFilterLenStr = "Tx4TransverseFilterLen";
QString BFPNames::TxCodeBCPosParaStr = "TxCodeBCPosPara";
QString BFPNames::TxCodeCompensationStr = "TxCodeCompensation";
QString BFPNames::TxCodeSwitchStr = "TxCodeSwitch";
QString BFPNames::TxCodeUTGC1Str = "TxCodeUTGC1";
QString BFPNames::TxCodeUTGC10Str = "TxCodeUTGC10";
QString BFPNames::TxCodeUTGC11Str = "TxCodeUTGC11";
QString BFPNames::TxCodeUTGC12Str = "TxCodeUTGC12";
QString BFPNames::TxCodeUTGC13Str = "TxCodeUTGC13";
QString BFPNames::TxCodeUTGC14Str = "TxCodeUTGC14";
QString BFPNames::TxCodeUTGC15Str = "TxCodeUTGC15";
QString BFPNames::TxCodeUTGC16Str = "TxCodeUTGC16";
QString BFPNames::TxCodeUTGC2Str = "TxCodeUTGC2";
QString BFPNames::TxCodeUTGC3Str = "TxCodeUTGC3";
QString BFPNames::TxCodeUTGC4Str = "TxCodeUTGC4";
QString BFPNames::TxCodeUTGC5Str = "TxCodeUTGC5";
QString BFPNames::TxCodeUTGC6Str = "TxCodeUTGC6";
QString BFPNames::TxCodeUTGC7Str = "TxCodeUTGC7";
QString BFPNames::TxCodeUTGC8Str = "TxCodeUTGC8";
QString BFPNames::TxCodeUTGC9Str = "TxCodeUTGC9";
QString BFPNames::CodeTransimitDoubleEnableStr = "CodeTransimitDoubleEnable";
QString BFPNames::CodeTransimitEnableStr = "CodeTransimitEnable";
QString BFPNames::EncodeTX_GAINStr = "EncodeTX_GAIN";
QString BFPNames::TransverseFilterStr = "TransverseFilter";
QString BFPNames::Contrast_AccumulationStr = "Contrast_Accumulation";
QString BFPNames::Contrast_AFCoef0Str = "Contrast_AFCoef0";
QString BFPNames::Contrast_AFCoef1Str = "Contrast_AFCoef1";
QString BFPNames::Contrast_AFCoef2Str = "Contrast_AFCoef2";
QString BFPNames::Contrast_AFCoef3Str = "Contrast_AFCoef3";
QString BFPNames::Contrast_AFCoef4Str = "Contrast_AFCoef4";
QString BFPNames::Contrast_AFGainStr = "Contrast_AFGain";
QString BFPNames::Contrast_AFShiftStr = "Contrast_AFShift";
QString BFPNames::Contrast_LeeEnStr = "Contrast_LeeEn";
QString BFPNames::Contrast_LeeGainStr = "Contrast_LeeGain";
QString BFPNames::Contrast_LeeMCoefStr = "Contrast_LeeMCoef";
QString BFPNames::Contrast_LeeSCoefStr = "Contrast_LeeSCoef";
QString BFPNames::Contrast_LeeShiftStr = "Contrast_LeeShift";
QString BFPNames::Contrast_ModeStr = "Contrast_Mode";
QString BFPNames::Contrast_VisualStr = "Contrast_Visual";
QString BFPNames::ContrastATgc0Str = "ContrastATgc0";
QString BFPNames::ContrastATgc1Str = "ContrastATgc1";
QString BFPNames::ContrastATgc2Str = "ContrastATgc2";
QString BFPNames::ContrastATgc3Str = "ContrastATgc3";
QString BFPNames::ContrastATgc4Str = "ContrastATgc4";
QString BFPNames::ContrastATgc5Str = "ContrastATgc5";
QString BFPNames::ContrastATgc6Str = "ContrastATgc6";
QString BFPNames::ContrastATgc7Str = "ContrastATgc7";
QString BFPNames::ContrastLogCopressionStr = "ContrastLogCopression";
QString BFPNames::ContrastSWStr = "ContrastSW";
QString BFPNames::ContrastUTgc00Str = "ContrastUTgc00";
QString BFPNames::ContrastUTgc01Str = "ContrastUTgc01";
QString BFPNames::ContrastUTgc02Str = "ContrastUTgc02";
QString BFPNames::ContrastUTgc03Str = "ContrastUTgc03";
QString BFPNames::ContrastUTgc04Str = "ContrastUTgc04";
QString BFPNames::ContrastUTgc05Str = "ContrastUTgc05";
QString BFPNames::ContrastUTgc06Str = "ContrastUTgc06";
QString BFPNames::ContrastUTgc07Str = "ContrastUTgc07";
QString BFPNames::ContrastUTgc08Str = "ContrastUTgc08";
QString BFPNames::ContrastUTgc09Str = "ContrastUTgc09";
QString BFPNames::ContrastUTgc10Str = "ContrastUTgc10";
QString BFPNames::ContrastUTgc11Str = "ContrastUTgc11";
QString BFPNames::ContrastUTgc12Str = "ContrastUTgc12";
QString BFPNames::ContrastUTgc13Str = "ContrastUTgc13";
QString BFPNames::ContrastUTgc14Str = "ContrastUTgc14";
QString BFPNames::ContrastUTgc15Str = "ContrastUTgc15";
QString BFPNames::ContrastWTStartPointStr = "ContrastWTStartPoint";
QString BFPNames::ContrastDynamicStr = "ContrastDynamic";
QString BFPNames::ContrastWeightStr = "ContrastWeight";
QString BFPNames::ContrastGainStr = "ContrastGain";
QString BFPNames::ContrastModeStr = "ContrastMode";
QString BFPNames::FlashStr = "Flash";
QString BFPNames::DRChangeTimesHarStr = "DRChangeTimesHar";
QString BFPNames::DRRateOfChangeHarStr = "DRRateOfChangeHar";
QString BFPNames::DRStartDbHarStr = "DRStartDbHar";
QString BFPNames::DRStartDepthHarStr = "DRStartDepthHar";
QString BFPNames::VolumeOffsetOfFreqSpectrumStr = "VolumeOffsetOfFreqSpectrum";
QString BFPNames::ZoomEndWaferLineFirstPartStr = "ZoomEndWaferLineFirstPart";
QString BFPNames::ZoomEndWaferLineSecondPartStr = "ZoomEndWaferLineSecondPart";
QString BFPNames::ZoomStartWaferLineFirstPartStr = "ZoomStartWaferLineFirstPart";
QString BFPNames::ZoomStartWaferLineSecondPartStr = "ZoomStartWaferLineSecondPart";
QString BFPNames::ColorEnFlagStr = "ColorEnFlag";
QString BFPNames::BWAnalogTgcCompensation0Str = "BWAnalogTgcCompensation0";
QString BFPNames::BWAnalogTgcCompensation1Str = "BWAnalogTgcCompensation1";
QString BFPNames::BWAnalogTgcCompensation2Str = "BWAnalogTgcCompensation2";
QString BFPNames::BWAnalogTgcCompensation3Str = "BWAnalogTgcCompensation3";
QString BFPNames::BWAnalogTgcCompensation4Str = "BWAnalogTgcCompensation4";
QString BFPNames::BWAnalogTgcCompensation5Str = "BWAnalogTgcCompensation5";
QString BFPNames::BWAnalogTgcCompensation6Str = "BWAnalogTgcCompensation6";
QString BFPNames::BWAnalogTgcCompensation7Str = "BWAnalogTgcCompensation7";
QString BFPNames::BWUniformTgcCompensation0Str = "BWUniformTgcCompensation0";
QString BFPNames::BWUniformTgcCompensation1Str = "BWUniformTgcCompensation1";
QString BFPNames::BWUniformTgcCompensation10Str = "BWUniformTgcCompensation10";
QString BFPNames::BWUniformTgcCompensation11Str = "BWUniformTgcCompensation11";
QString BFPNames::BWUniformTgcCompensation12Str = "BWUniformTgcCompensation12";
QString BFPNames::BWUniformTgcCompensation13Str = "BWUniformTgcCompensation13";
QString BFPNames::BWUniformTgcCompensation14Str = "BWUniformTgcCompensation14";
QString BFPNames::BWUniformTgcCompensation15Str = "BWUniformTgcCompensation15";
QString BFPNames::BWUniformTgcCompensation2Str = "BWUniformTgcCompensation2";
QString BFPNames::BWUniformTgcCompensation3Str = "BWUniformTgcCompensation3";
QString BFPNames::BWUniformTgcCompensation4Str = "BWUniformTgcCompensation4";
QString BFPNames::BWUniformTgcCompensation5Str = "BWUniformTgcCompensation5";
QString BFPNames::BWUniformTgcCompensation6Str = "BWUniformTgcCompensation6";
QString BFPNames::BWUniformTgcCompensation7Str = "BWUniformTgcCompensation7";
QString BFPNames::BWUniformTgcCompensation8Str = "BWUniformTgcCompensation8";
QString BFPNames::BWUniformTgcCompensation9Str = "BWUniformTgcCompensation9";
QString BFPNames::ECGDrawStr = "ECGDraw";
QString BFPNames::StartDepthDeltaLineStr = "StartDepthDeltaLine";
QString BFPNames::StaticOptimizationEnStr = "StaticOptimizationEn";
QString BFPNames::ResetImageRefreshFlagStr = "ResetImageRefreshFlag";
QString BFPNames::ResetFreqSpectrumStr = "ResetFreqSpectrum";
QString BFPNames::FreqSpectrumTrigStr = "FreqSpectrumTrig";
QString BFPNames::BCPosOnTdiStr = "BCPosOnTdi";
QString BFPNames::MediateChoiceStr = "MediateChoice";
QString BFPNames::RefreshOnMoveLineStr = "RefreshOnMoveLine";
QString BFPNames::ECGLRInvertStr = "ECGLRInvert";
QString BFPNames::ECGStartLineStr = "ECGStartLine";
QString BFPNames::ECGStopLineStr = "ECGStopLine";
QString BFPNames::NewECGDlyStr = "NewECGDly";
QString BFPNames::PrtOfMStr = "PrtOfM";
QString BFPNames::PrtOfMinStr = "PrtOfMin";
QString BFPNames::MLineFundamentalHarmonicComplexStr = "MLineFundamentalHarmonicComplex";
QString BFPNames::MaxDepthFocusNumberStr = "MaxDepthFocusNumber";
QString BFPNames::DynamicCompensationStr = "DynamicCompensation";
QString BFPNames::DualApoEnStr = "DualApoEn";
QString BFPNames::DualBModeStr = "DualBMode";
QString BFPNames::AcousticPowerEnStr = "AcousticPowerEn";
QString BFPNames::PLFIR_Coef0Str = "PLFIR_Coef0";
QString BFPNames::PLFIR_Coef1Str = "PLFIR_Coef1";
QString BFPNames::PLFIR_Coef2Str = "PLFIR_Coef2";
QString BFPNames::PLFIR_ShiftStr = "PLFIR_Shift";
QString BFPNames::PLFIR_GainStr = "PLFIR_Gain";
QString BFPNames::PAFIR_Coef0Str = "PAFIR_Coef0";
QString BFPNames::PAFIR_Coef1Str = "PAFIR_Coef1";
QString BFPNames::PAFIR_Coef2Str = "PAFIR_Coef2";
QString BFPNames::PAFIR_Coef3Str = "PAFIR_Coef3";
QString BFPNames::PAFIR_ShiftStr = "PAFIR_Shift";
QString BFPNames::PAFIR_GainStr = "PAFIR_Gain";
QString BFPNames::PAComeBackStr = "PAComeBack";
QString BFPNames::PLComeBackStr = "PLComeBack";
QString BFPNames::DynFlowModeStr = "DynFlowMode";
QString BFPNames::MVIModeStr = "MVIMode";
QString BFPNames::DynFlowDynamicRangeStr = "DynFlowDynamicRange";
QString BFPNames::MVIDynamicRangeStr = "MVIDynamicRange";
QString BFPNames::HDCPAWallFilterStr = "HDCPAWallFilter";
QString BFPNames::HDCPACETStr = "HDCPACET";
QString BFPNames::HDCPAWFGainCtrlStr = "HDCPAWFGainCtrl";
QString BFPNames::MVIPostGainStr = "MVIPostGain";
QString BFPNames::ReadProbeLicenceStr = "ReadProbeLicence";
QString BFPNames::BSteeringAngle2CodingStr = "BSteeringAngle2Coding";
QString BFPNames::DynDBRateReciprocalStr = "DynDBRateReciprocal";
QString BFPNames::TGC9Str = "TGC9";
QString BFPNames::TGC10Str = "TGC10";
QString BFPNames::TGC11Str = "TGC11";
QString BFPNames::TGC12Str = "TGC12";
QString BFPNames::PALM_Link_ModeStr = "PALM_Link_Mode";
QString BFPNames::ArbitraryWaveStr = "ArbitraryWave";
QString BFPNames::RXGateStr = "RXGate";
QString BFPNames::USPipeLineVersionStr = "USPipeLineVersion";
QString BFPNames::BRvApertureCoefStr = "BRvApertureCoef";
QString BFPNames::CRvApertureCoefStr = "CRvApertureCoef";
QString BFPNames::DRvApertureCoefStr = "DRvApertureCoef";
QString BFPNames::IImageTypeStr = "IImageType";
QString BFPNames::PWScanLineDeltaStr = "PWScanLineDelta";
QString BFPNames::DTx1_TriplexStr = "DTx1_Triplex";
QString BFPNames::DTx2_TriplexStr = "DTx2_Triplex";
QString BFPNames::DTx3_TriplexStr = "DTx3_Triplex";
QString BFPNames::GateDepth1Str = "GateDepth1";
QString BFPNames::GateDepth2Str = "GateDepth2";
QString BFPNames::VAFunctionStr = "VAFunction";
QString BFPNames::SonoAVStr = "SonoAV";
QString BFPNames::ADFreqMHzStr = "ADFreqMHz";
QString BFPNames::ADOffsetTimeNsStr = "ADOffsetTimeNs";
QString BFPNames::SonicSpeedStr = "SonicSpeed";
QString BFPNames::TssIncrementStr = "TssIncrement";
QString BFPNames::MTriggeredAPStr = "MTriggeredAP";
QString BFPNames::CTriggeredAPStr = "CTriggeredAP";
QString BFPNames::SystemScanModeStr = "SystemScanMode";
QString BFPNames::ProbeIdStr = "ProbeId";
QString BFPNames::ScanWidthStr = "ScanWidth";
QString BFPNames::FocusNumShowStr = "FocusNumShow";
QString BFPNames::ImageZoomCoefOtherStr = "ImageZoomCoefOther";
QString BFPNames::ImageZoomCoefBStr = "ImageZoomCoefB";
QString BFPNames::ImageZoomCoefStr = "ImageZoomCoef";
QString BFPNames::IsFullScreenZoomInStr = "IsFullScreenZoomIn";
QString BFPNames::FullScreenZoomInIndexStr = "FullScreenZoomInIndex";
QString BFPNames::IsSecondGearFullScreenZoomInStr = "IsSecondGearFullScreenZoomIn";
QString BFPNames::IsMenuVisibleStr = "IsMenuVisible";
QString BFPNames::DRDiffWithCompoundOnStr = "DRDiffWithCompoundOn";
QString BFPNames::DRDiffWithSraOnStr = "DRDiffWithSraOn";
QString BFPNames::DRDiffWithThiOnStr = "DRDiffWithThiOn";
QString BFPNames::ScpdOnStr = "ScpdOn";
QString BFPNames::FcpdOnStr = "FcpdOn";
QString BFPNames::CompoundByFPGAStr = "CompoundByFPGA";
QString BFPNames::DynStartDepthStr = "DynStartDepth";
QString BFPNames::DynStartDBStr = "DynStartDB";
QString BFPNames::DynEndDepthStr = "DynEndDepth";
QString BFPNames::DynEndDBStr = "DynEndDB";
QString BFPNames::FreqSettingIdsStr = "FreqSettingIds";
QString BFPNames::AFSettingIdsStr = "AFSettingIds";
QString BFPNames::LeeSettingIdsStr = "LeeSettingIds";
QString BFPNames::CLFStrIdsStr = "CLFStrIds";
QString BFPNames::CAFStrIdsStr = "CAFStrIds";
QString BFPNames::TDILFStrIdsStr = "TDILFStrIds";
QString BFPNames::TDIAFStrIdsStr = "TDIAFStrIds";
QString BFPNames::PDLFStrIdsStr = "PDLFStrIds";
QString BFPNames::SNLFStrIdsStr = "SNLFStrIds";
QString BFPNames::MVILFStrIdsStr = "MVILFStrIds";
QString BFPNames::PDAFStrIdsStr = "PDAFStrIds";
QString BFPNames::SNAFStrIdsStr = "SNAFStrIds";
QString BFPNames::MVIAFStrIdsStr = "MVIAFStrIds";
QString BFPNames::iImageLibStr = "iImageLib";
QString BFPNames::iImageStr = "iImage";
QString BFPNames::iImageShowStr = "iImageShow";
QString BFPNames::iImageModelNoStr = "iImageModelNo";
QString BFPNames::iImageStrengthStr = "iImageStrength";
QString BFPNames::iImageSmoothStr = "iImageSmooth";
QString BFPNames::iImageEdgeStr = "iImageEdge";
QString BFPNames::iImageEdge2Str = "iImageEdge2";
QString BFPNames::iImageEdge3Str = "iImageEdge3";
QString BFPNames::iImageEdge4Str = "iImageEdge4";
QString BFPNames::iImageContrastS1Str = "iImageContrastS1";
QString BFPNames::iImageContrastK1Str = "iImageContrastK1";
QString BFPNames::iImageContrastS2Str = "iImageContrastS2";
QString BFPNames::ParaFileNamesStr = "ParaFileNames";
QString BFPNames::ParaFileNameStr = "ParaFileName";
QString BFPNames::SettingIndexStr = "SettingIndex";
QString BFPNames::NoiseSuppressionStr = "NoiseSuppression";
QString BFPNames::EdgeCVStr = "EdgeCV";
QString BFPNames::DetailStr = "Detail";
QString BFPNames::LimitEnabledStr = "LimitEnabled";
QString BFPNames::LimitOvershootreductionStr = "LimitOvershootreduction";
QString BFPNames::LimitUndershootreductionStr = "LimitUndershootreduction";
QString BFPNames::StrengthEnabledStr = "StrengthEnabled";
QString BFPNames::StrengthFinestructuresStr = "StrengthFinestructures";
QString BFPNames::StrengthLargestructuresStr = "StrengthLargestructures";
QString BFPNames::TemporalEnabledStr = "TemporalEnabled";
QString BFPNames::TemporalStrengthStr = "TemporalStrength";
QString BFPNames::TemporalHomogeneityStr = "TemporalHomogeneity";
QString BFPNames::LineiImageCVSettingIdsStr = "LineiImageCVSettingIds";
QString BFPNames::LineiImageTypeStr = "LineiImageType";
QString BFPNames::iImageTemporalThresholdStr = "iImageTemporalThreshold";
QString BFPNames::iImageTemporalWeightStr = "iImageTemporalWeight";
QString BFPNames::iImageMapStr = "iImageMap";
QString BFPNames::LineiImageSettingIdsStr = "LineiImageSettingIds";
QString BFPNames::SmoothSettingIdsStr = "SmoothSettingIds";
QString BFPNames::RvFNoStr = "RvFNo";
QString BFPNames::RvPWFNoStr = "RvPWFNo";
QString BFPNames::TxFNoStr = "TxFNo";
QString BFPNames::CPDSteerStr = "CPDSteer";
QString BFPNames::TrapezoidalCPDSteerStr = "TrapezoidalCPDSteer";
QString BFPNames::TrapezoidalCPDSteer2Str = "TrapezoidalCPDSteer2";
QString BFPNames::PersistDiffWithCpdOnStr = "PersistDiffWithCpdOn";
QString BFPNames::PersistDiffWithSraOnStr = "PersistDiffWithSraOn";
QString BFPNames::PersistentDeltaTHIStr = "PersistentDeltaTHI";
QString BFPNames::PersistentDeltaCFMStr = "PersistentDeltaCFM";
QString BFPNames::PersistentDeltaPDStr = "PersistentDeltaPD";
QString BFPNames::PersistentDeltaSNStr = "PersistentDeltaSN";
QString BFPNames::PersistentDeltaTDIStr = "PersistentDeltaTDI";
QString BFPNames::PersistentDeltaMVIStr = "PersistentDeltaMVI";
QString BFPNames::FlowStr = "Flow";
QString BFPNames::FlowTDIStr = "FlowTDI";
QString BFPNames::ZoomedCQYZStr = "ZoomedCQYZ";
QString BFPNames::ZoomMultiIndexStr = "ZoomMultiIndex";
QString BFPNames::ZoomMidLineStr = "ZoomMidLine";
QString BFPNames::ZoomHalfLinesStr = "ZoomHalfLines";
QString BFPNames::ZoomMidDepthMMStr = "ZoomMidDepthMM";
QString BFPNames::ZoomHalfDepthMMStr = "ZoomHalfDepthMM";
QString BFPNames::ScrollStr = "Scroll";
QString BFPNames::IsScrollStr = "IsScroll";
QString BFPNames::ActiveBStr = "ActiveB";
QString BFPNames::FPSStr = "FPS";
QString BFPNames::FPSForAPStr = "FPSForAP";
QString BFPNames::HarmonicStr = "Harmonic";
QString BFPNames::PRFColorKHZStr = "PRFColorKHZ";
QString BFPNames::PRFDopKHZStr = "PRFDopKHZ";
QString BFPNames::BaseLineColorStr = "BaseLineColor";
QString BFPNames::BaseLineDPDStr = "BaseLineDPD";
QString BFPNames::BaseLineTDIStr = "BaseLineTDI";
QString BFPNames::BaseLineMVIStr = "BaseLineMVI";
QString BFPNames::DopplerThetaStr = "DopplerTheta";
QString BFPNames::DopplerThetaTDIStr = "DopplerThetaTDI";
QString BFPNames::DopplerThetaCWDStr = "DopplerThetaCWD";
QString BFPNames::RoiMidLineStr = "RoiMidLine";
QString BFPNames::RoiHalfLinesStr = "RoiHalfLines";
QString BFPNames::RoiMidDepthMMStr = "RoiMidDepthMM";
QString BFPNames::RoiHalfDepthMMStr = "RoiHalfDepthMM";
QString BFPNames::RoiMidLineSNStr = "RoiMidLineSN";
QString BFPNames::RoiHalfLinesSNStr = "RoiHalfLinesSN";
QString BFPNames::RoiMidDepthMMSNStr = "RoiMidDepthMMSN";
QString BFPNames::RoiHalfDepthMMSNStr = "RoiHalfDepthMMSN";
QString BFPNames::SteeringAngleSNStr = "SteeringAngleSN";
QString BFPNames::RoiMidLineTDIStr = "RoiMidLineTDI";
QString BFPNames::RoiHalfLinesTDIStr = "RoiHalfLinesTDI";
QString BFPNames::RoiMidDepthMMTDIStr = "RoiMidDepthMMTDI";
QString BFPNames::RoiHalfDepthMMTDIStr = "RoiHalfDepthMMTDI";
QString BFPNames::RoiMidLineFourDStr = "RoiMidLineFourD";
QString BFPNames::RoiHalfLinesFourDStr = "RoiHalfLinesFourD";
QString BFPNames::RoiMidDepthMMFourDStr = "RoiMidDepthMMFourD";
QString BFPNames::RoiHalfDepthMMFourDStr = "RoiHalfDepthMMFourD";
QString BFPNames::RoiMidLineMVIStr = "RoiMidLineMVI";
QString BFPNames::RoiHalfLinesMVIStr = "RoiHalfLinesMVI";
QString BFPNames::RoiMidDepthMMMVIStr = "RoiMidDepthMMMVI";
QString BFPNames::RoiHalfDepthMMMVIStr = "RoiHalfDepthMMMVI";
QString BFPNames::FourDKnotLineStr = "FourDKnotLine";
QString BFPNames::FourDKnotDepthMMStr = "FourDKnotDepthMM";
QString BFPNames::RoiMidLineElastoStr = "RoiMidLineElasto";
QString BFPNames::RoiHalfLinesElastoStr = "RoiHalfLinesElasto";
QString BFPNames::RoiMidDepthMMElastoStr = "RoiMidDepthMMElasto";
QString BFPNames::RoiHalfDepthMMElastoStr = "RoiHalfDepthMMElasto";
QString BFPNames::SampleVolumeMMStr = "SampleVolumeMM";
QString BFPNames::SampleVolumeTDIMMStr = "SampleVolumeTDIMM";
QString BFPNames::DopplerStartDepthMMStr = "DopplerStartDepthMM";
QString BFPNames::DopplerTDIStartDepthMMStr = "DopplerTDIStartDepthMM";
QString BFPNames::CMaxVelCMSStr = "CMaxVelCMS";
QString BFPNames::DMaxVelCMSStr = "DMaxVelCMS";
QString BFPNames::PRFColorStr = "PRFColor";
QString BFPNames::PRFDopStr = "PRFDop";
QString BFPNames::PRFCWDStr = "PRFCWD";
QString BFPNames::PRFElastoStr = "PRFElasto";
QString BFPNames::BGammaStr = "BGamma";
QString BFPNames::BRejectionStr = "BRejection";
QString BFPNames::BTHIGammaStr = "BTHIGamma";
QString BFPNames::BTHIRejectionStr = "BTHIRejection";
QString BFPNames::MGammaStr = "MGamma";
QString BFPNames::MRejectionStr = "MRejection";
QString BFPNames::PwGammaStr = "PwGamma";
QString BFPNames::PwRejectionStr = "PwRejection";
QString BFPNames::PwTDIGammaStr = "PwTDIGamma";
QString BFPNames::PwTDIRejectionStr = "PwTDIRejection";
QString BFPNames::CwdGammaStr = "CwdGamma";
QString BFPNames::CwdRejectionStr = "CwdRejection";
QString BFPNames::BGammaPosStr = "BGammaPos";
QString BFPNames::BGammaStartStr = "BGammaStart";
QString BFPNames::BGammaStepStr = "BGammaStep";
QString BFPNames::THIStateStr = "THIState";
QString BFPNames::ColorInvertStateStr = "ColorInvertState";
QString BFPNames::DPDInvertStateStr = "DPDInvertState";
QString BFPNames::TDIInvertStateStr = "TDIInvertState";
QString BFPNames::ElastoInvertStateStr = "ElastoInvertState";
QString BFPNames::MVIInvertStateStr = "MVIInvertState";
QString BFPNames::BColorMapIndexStr = "BColorMapIndex";
QString BFPNames::BTDIColorMapIndexStr = "BTDIColorMapIndex";
QString BFPNames::CfColorMapIndexStr = "CfColorMapIndex";
QString BFPNames::PdColorMapIndexStr = "PdColorMapIndex";
QString BFPNames::DpdColorMapIndexStr = "DpdColorMapIndex";
QString BFPNames::TDIColorMapIndexStr = "TDIColorMapIndex";
QString BFPNames::PwColorMapIndexStr = "PwColorMapIndex";
QString BFPNames::PwTDIColorMapIndexStr = "PwTDIColorMapIndex";
QString BFPNames::MColorMapIndexStr = "MColorMapIndex";
QString BFPNames::CwdColorMapIndexStr = "CwdColorMapIndex";
QString BFPNames::ElastoColorMapIndexStr = "ElastoColorMapIndex";
QString BFPNames::CPColorMapIndexStr = "CPColorMapIndex";
QString BFPNames::MVIColorMapIndexStr = "MVIColorMapIndex";
QString BFPNames::MVIType1ColorMapIndexStr = "MVIType1ColorMapIndex";
QString BFPNames::MVIType2ColorMapIndexStr = "MVIType2ColorMapIndex";
QString BFPNames::MVIType3ColorMapIndexStr = "MVIType3ColorMapIndex";
QString BFPNames::BGrayCurveIndexStr = "BGrayCurveIndex";
QString BFPNames::BTHIGrayCurveIndexStr = "BTHIGrayCurveIndex";
QString BFPNames::MGrayCurveIndexStr = "MGrayCurveIndex";
QString BFPNames::PwGrayCurveIndexStr = "PwGrayCurveIndex";
QString BFPNames::PwTDIGrayCurveIndexStr = "PwTDIGrayCurveIndex";
QString BFPNames::CwdGrayCurveIndexStr = "CwdGrayCurveIndex";
QString BFPNames::EGrayCurveIndexStr = "EGrayCurveIndex";
QString BFPNames::VarColorMapIndexStr = "VarColorMapIndex";
QString BFPNames::FourDColorMapIndexStr = "FourDColorMapIndex";
QString BFPNames::BColorMapIndexShowStr = "BColorMapIndexShow";
QString BFPNames::CfColorMapIndexShowStr = "CfColorMapIndexShow";
QString BFPNames::BGrayCurveIndexShowStr = "BGrayCurveIndexShow";
QString BFPNames::BGammaShowStr = "BGammaShow";
QString BFPNames::BRejectionShowStr = "BRejectionShow";
QString BFPNames::CFModeStr = "CFMode";
QString BFPNames::CfContrastStr = "CfContrast";
QString BFPNames::PdContrastStr = "PdContrast";
QString BFPNames::DpdContrastStr = "DpdContrast";
QString BFPNames::ImageSizeStr = "ImageSize";
QString BFPNames::RenderImageSizeStr = "RenderImageSize";
QString BFPNames::LayoutBImageSizeStr = "LayoutBImageSize";
QString BFPNames::BImageSizeStr = "BImageSize";
QString BFPNames::MImageSizeStr = "MImageSize";
QString BFPNames::DImageSizeStr = "DImageSize";
QString BFPNames::DataBitCountStr = "DataBitCount";
QString BFPNames::PointNumPerLineStr = "PointNumPerLine";
QString BFPNames::ColorPointNumAfterReduceStr = "ColorPointNumAfterReduce";
QString BFPNames::RedundantPointNumPerLineStr = "RedundantPointNumPerLine";
QString BFPNames::StartDepthMMStr = "StartDepthMM";
QString BFPNames::DepthMMStr = "DepthMM";
QString BFPNames::PixelSizeMMStr = "PixelSizeMM";
QString BFPNames::MPixelSizeSecStr = "MPixelSizeSec";
QString BFPNames::MPixelSizeMMStr = "MPixelSizeMM";
QString BFPNames::DPixelSizeCMSStr = "DPixelSizeCMS";
QString BFPNames::DPixelSizeSecStr = "DPixelSizeSec";
QString BFPNames::DBaseLineYPosStr = "DBaseLineYPos";
QString BFPNames::DBaseLineWidthStr = "DBaseLineWidth";
QString BFPNames::ZoomDepthMMStr = "ZoomDepthMM";
QString BFPNames::ScrollDepthMMStr = "ScrollDepthMM";
QString BFPNames::HotLogoDepthMMStr = "HotLogoDepthMM";
QString BFPNames::HotLogoLineStr = "HotLogoLine";
QString BFPNames::AIOStr = "AIO";
QString BFPNames::IsBiopsyVisibleStr = "IsBiopsyVisible";
QString BFPNames::ZoomSelectStr = "ZoomSelect";
QString BFPNames::BiopsyAngleStr = "BiopsyAngle";
QString BFPNames::BiopsyXPosMMStr = "BiopsyXPosMM";
QString BFPNames::BiopsyYPosMMStr = "BiopsyYPosMM";
QString BFPNames::BiopsyAngleOffsetStr = "BiopsyAngleOffset";
QString BFPNames::BiopsyXPosMMOffsetStr = "BiopsyXPosMMOffset";
QString BFPNames::BiopsyYPosMMOffsetStr = "BiopsyYPosMMOffset";
QString BFPNames::IsCenterLineVisibleStr = "IsCenterLineVisible";
QString BFPNames::TDIBMenuShowStr = "TDIBMenuShow";
QString BFPNames::IsDopplerScanLineVisibleStr = "IsDopplerScanLineVisible";
QString BFPNames::IsCWDScanLineVisibleStr = "IsCWDScanLineVisible";
QString BFPNames::IsMLineVisibleStr = "IsMLineVisible";
QString BFPNames::IsRoiVisibleStr = "IsRoiVisible";
QString BFPNames::IsUDBMStr = "IsUDBM";
QString BFPNames::GrayMapStr = "GrayMap";
QString BFPNames::MGrayMapStr = "MGrayMap";
QString BFPNames::PwGrayMapStr = "PwGrayMap";
QString BFPNames::CwdGrayMapStr = "CwdGrayMap";
QString BFPNames::TGCStr = "TGC";
QString BFPNames::MIStr = "MI";
QString BFPNames::TISStr = "TIS";
QString BFPNames::TIBStr = "TIB";
QString BFPNames::ExamModeIdStr = "ExamModeId";
QString BFPNames::ExamModeTypeStr = "ExamModeType";
QString BFPNames::ExamModeCaptionStr = "ExamModeCaption";
QString BFPNames::ProbeConnectedStr = "ProbeConnected";
QString BFPNames::ImageRectsStr = "ImageRects";
QString BFPNames::ImageRegionsStr = "ImageRegions";
QString BFPNames::BSamplePointsStr = "BSamplePoints";
QString BFPNames::BTHISamplePointsStr = "BTHISamplePoints";
QString BFPNames::MSamplePointsStr = "MSamplePoints";
QString BFPNames::PwSamplePointsStr = "PwSamplePoints";
QString BFPNames::PwTDISamplePointsStr = "PwTDISamplePoints";
QString BFPNames::CwdSamplePointsStr = "CwdSamplePoints";
QString BFPNames::ESamplePointsStr = "ESamplePoints";
QString BFPNames::BRejectStr = "BReject";
QString BFPNames::SraGainColorDeltaStr = "SraGainColorDelta";
QString BFPNames::TxOffStr = "TxOff";
QString BFPNames::LayoutStr = "Layout";
QString BFPNames::RectStr = "Rect";
QString BFPNames::WeightedCurveStr = "WeightedCurve";
QString BFPNames::FCA_AlphaStr = "FCA_Alpha";
QString BFPNames::CVIESettingIndexStr = "CVIESettingIndex";
QString BFPNames::CVIESettingNameStr = "CVIESettingName";
QString BFPNames::ColorJudgeThresholdStr = "ColorJudgeThreshold";
QString BFPNames::ImageOptimizationAlgStr = "ImageOptimizationAlg";
QString BFPNames::MRD_AlphaStr = "MRD_Alpha";
QString BFPNames::MRD_BetaStr = "MRD_Beta";
QString BFPNames::MRD_GammaStr = "MRD_Gamma";
QString BFPNames::Threshold1Str = "Threshold1";
QString BFPNames::Threshold2Str = "Threshold2";
QString BFPNames::Threshold3Str = "Threshold3";
QString BFPNames::Edge1Str = "Edge1";
QString BFPNames::Edge2Str = "Edge2";
QString BFPNames::Edge3Str = "Edge3";
QString BFPNames::Smooth1Str = "Smooth1";
QString BFPNames::Smooth2Str = "Smooth2";
QString BFPNames::Smooth3Str = "Smooth3";
QString BFPNames::Iteration1Str = "Iteration1";
QString BFPNames::Iteration2Str = "Iteration2";
QString BFPNames::Iteration3Str = "Iteration3";
QString BFPNames::ContrastStr = "Contrast";
QString BFPNames::OptimizationLevelStr = "OptimizationLevel";
QString BFPNames::EdgeThiStr = "EdgeThi";
QString BFPNames::EdgeThiIncStr = "EdgeThiInc";
QString BFPNames::FreqIndexBShowStr = "FreqIndexBShow";
QString BFPNames::ColorLineChangingStr = "ColorLineChanging";
QString BFPNames::FreqIndexColorStr = "FreqIndexColor";
QString BFPNames::FreqIndexPDStr = "FreqIndexPD";
QString BFPNames::FreqIndexSNStr = "FreqIndexSN";
QString BFPNames::FreqIndexTDIStr = "FreqIndexTDI";
QString BFPNames::FreqIndexDopStr = "FreqIndexDop";
QString BFPNames::FreqIndexTDStr = "FreqIndexTD";
QString BFPNames::FreqIndexCWDStr = "FreqIndexCWD";
QString BFPNames::FreqIndexElastoStr = "FreqIndexElasto";
QString BFPNames::FreqIndexMVIStr = "FreqIndexMVI";
QString BFPNames::AcousticPowerBShowStr = "AcousticPowerBShow";
QString BFPNames::ECGDlyShowStr = "ECGDlyShow";
QString BFPNames::GainShowStr = "GainShow";
QString BFPNames::EdgeShowStr = "EdgeShow";
QString BFPNames::BWGainDeltaStr = "BWGainDelta";
QString BFPNames::AdapPostProcDeltaStr = "AdapPostProcDelta";
QString BFPNames::ECGPosShowStr = "ECGPosShow";
QString BFPNames::ECGEnStr = "ECGEn";
QString BFPNames::ECGVelocityStr = "ECGVelocity";
QString BFPNames::ECGGainStr = "ECGGain";
QString BFPNames::ECGPosStr = "ECGPos";
QString BFPNames::ECGInvertStr = "ECGInvert";
QString BFPNames::SampleRateDopShowStr = "SampleRateDopShow";
QString BFPNames::DSampleRateDopShowStr = "DSampleRateDopShow";
QString BFPNames::PixelRatioShowStr = "PixelRatioShow";
QString BFPNames::PWEnhanceShowStr = "PWEnhanceShow";
QString BFPNames::PWDynamicRangeShowStr = "PWDynamicRangeShow";
QString BFPNames::DummyEnSampleShowStr = "DummyEnSampleShow";
QString BFPNames::CFMVelLevelShowStr = "CFMVelLevelShow";
QString BFPNames::GainDopShowStr = "GainDopShow";
QString BFPNames::ColorCoefStr = "ColorCoef";
QString BFPNames::PDCoefStr = "PDCoef";
QString BFPNames::SNCoefStr = "SNCoef";
QString BFPNames::TDICoefStr = "TDICoef";
QString BFPNames::MVICoefStr = "MVICoef";
QString BFPNames::CVRTDeltaStr = "CVRTDelta";
QString BFPNames::CVLTDeltaStr = "CVLTDelta";
QString BFPNames::CETDeltaStr = "CETDelta";
QString BFPNames::AudioFilterCoefSelShowStr = "AudioFilterCoefSelShow";
QString BFPNames::TransmitStrIdsStr = "TransmitStrIds";
QString BFPNames::ColorFreqStrIdsStr = "ColorFreqStrIds";
QString BFPNames::PDFreqStrIdsStr = "PDFreqStrIds";
QString BFPNames::SNFreqStrIdsStr = "SNFreqStrIds";
QString BFPNames::TDIFreqStrIdsStr = "TDIFreqStrIds";
QString BFPNames::DopFreqStrIdsStr = "DopFreqStrIds";
QString BFPNames::TDFreqStrIdsStr = "TDFreqStrIds";
QString BFPNames::CWDFreqStrIdsStr = "CWDFreqStrIds";
QString BFPNames::ElastoFreqStrIdsStr = "ElastoFreqStrIds";
QString BFPNames::MVIFreqStrIdsStr = "MVIFreqStrIds";
QString BFPNames::ColorTransmitStrIdsStr = "ColorTransmitStrIds";
QString BFPNames::DynDeltaOnXContrastStr = "DynDeltaOnXContrast";
QString BFPNames::BFreqDeltaStr = "BFreqDelta";
QString BFPNames::TransmitStepDisStr = "TransmitStepDis";
QString BFPNames::TransmitBeamDevEleNumStr = "TransmitBeamDevEleNum";
QString BFPNames::ReceiveFocusDisStr = "ReceiveFocusDis";
QString BFPNames::ReceiveBeamDevEleNumStr = "ReceiveBeamDevEleNum";
QString BFPNames::ReceiveUnitVariFocusNumStr = "ReceiveUnitVariFocusNum";
QString BFPNames::WallFilterSettingStr = "WallFilterSetting";
QString BFPNames::CPAWallFilterSettingStr = "CPAWallFilterSetting";
QString BFPNames::CfmFnumStr = "CfmFnum";
QString BFPNames::ElastoFnumStr = "ElastoFnum";
QString BFPNames::CfmRxFnumStr = "CfmRxFnum";
QString BFPNames::ElastoRxFnumStr = "ElastoRxFnum";
QString BFPNames::MaxSteeringAngleStr = "MaxSteeringAngle";
QString BFPNames::ColorFreqDeltaStr = "ColorFreqDelta";
QString BFPNames::CFMRoi_DeltaStr = "CFMRoi_Delta";
QString BFPNames::CfmTransmitStepDisStr = "CfmTransmitStepDis";
QString BFPNames::CfmTransmitBeamDevEleNumStr = "CfmTransmitBeamDevEleNum";
QString BFPNames::CfmReceiveFocusDisStr = "CfmReceiveFocusDis";
QString BFPNames::CfmReceiveBeamDevEleNumStr = "CfmReceiveBeamDevEleNum";
QString BFPNames::CfmReceiveUnitVariFocusNumStr = "CfmReceiveUnitVariFocusNum";
QString BFPNames::CWScanLineDeltaStr = "CWScanLineDelta";
QString BFPNames::CWMidDepthMMDeltaStr = "CWMidDepthMMDelta";
QString BFPNames::CTxPulseDutyStr = "CTxPulseDuty";
QString BFPNames::CTxPulseDuty_PWStr = "CTxPulseDuty_PW";
QString BFPNames::CfmMaxTransmitApertureControlStr = "CfmMaxTransmitApertureControl";
QString BFPNames::CfmMaxTransmitApertureControl_PWStr = "CfmMaxTransmitApertureControl_PW";
QString BFPNames::CfmMaxTransmitApertureControlElastoStr = "CfmMaxTransmitApertureControlElasto";
QString BFPNames::CfmMinTransmitApertureControlStr = "CfmMinTransmitApertureControl";
QString BFPNames::CfmMinTransmitApertureControl_PWStr = "CfmMinTransmitApertureControl_PW";
QString BFPNames::CfmMinTransmitApertureControlElastoStr = "CfmMinTransmitApertureControlElasto";
QString BFPNames::TgcDelta0Str = "TgcDelta0";
QString BFPNames::TgcDelta1Str = "TgcDelta1";
QString BFPNames::TgcDelta2Str = "TgcDelta2";
QString BFPNames::TgcDelta3Str = "TgcDelta3";
QString BFPNames::TgcDelta4Str = "TgcDelta4";
QString BFPNames::TgcDelta5Str = "TgcDelta5";
QString BFPNames::TgcDelta6Str = "TgcDelta6";
QString BFPNames::TgcDelta7Str = "TgcDelta7";
QString BFPNames::TriplexTgcDelta0Str = "TriplexTgcDelta0";
QString BFPNames::TriplexTgcDelta1Str = "TriplexTgcDelta1";
QString BFPNames::TriplexTgcDelta2Str = "TriplexTgcDelta2";
QString BFPNames::TriplexTgcDelta3Str = "TriplexTgcDelta3";
QString BFPNames::TriplexTgcDelta4Str = "TriplexTgcDelta4";
QString BFPNames::TriplexTgcDelta5Str = "TriplexTgcDelta5";
QString BFPNames::TriplexTgcDelta6Str = "TriplexTgcDelta6";
QString BFPNames::TriplexTgcDelta7Str = "TriplexTgcDelta7";
QString BFPNames::ThiTriplexTgcDelta0Str = "ThiTriplexTgcDelta0";
QString BFPNames::ThiTriplexTgcDelta1Str = "ThiTriplexTgcDelta1";
QString BFPNames::ThiTriplexTgcDelta2Str = "ThiTriplexTgcDelta2";
QString BFPNames::ThiTriplexTgcDelta3Str = "ThiTriplexTgcDelta3";
QString BFPNames::ThiTriplexTgcDelta4Str = "ThiTriplexTgcDelta4";
QString BFPNames::ThiTriplexTgcDelta5Str = "ThiTriplexTgcDelta5";
QString BFPNames::ThiTriplexTgcDelta6Str = "ThiTriplexTgcDelta6";
QString BFPNames::ThiTriplexTgcDelta7Str = "ThiTriplexTgcDelta7";
QString BFPNames::ThiTgcDelta0Str = "ThiTgcDelta0";
QString BFPNames::ThiTgcDelta1Str = "ThiTgcDelta1";
QString BFPNames::ThiTgcDelta2Str = "ThiTgcDelta2";
QString BFPNames::ThiTgcDelta3Str = "ThiTgcDelta3";
QString BFPNames::ThiTgcDelta4Str = "ThiTgcDelta4";
QString BFPNames::ThiTgcDelta5Str = "ThiTgcDelta5";
QString BFPNames::ThiTgcDelta6Str = "ThiTgcDelta6";
QString BFPNames::ThiTgcDelta7Str = "ThiTgcDelta7";
QString BFPNames::DTgcDelta0Str = "DTgcDelta0";
QString BFPNames::DTgcDelta1Str = "DTgcDelta1";
QString BFPNames::DTgcDelta2Str = "DTgcDelta2";
QString BFPNames::DTgcDelta3Str = "DTgcDelta3";
QString BFPNames::DTgcDelta4Str = "DTgcDelta4";
QString BFPNames::DTgcDelta5Str = "DTgcDelta5";
QString BFPNames::DTgcDelta6Str = "DTgcDelta6";
QString BFPNames::DTgcDelta7Str = "DTgcDelta7";
QString BFPNames::DTgcDelta8Str = "DTgcDelta8";
QString BFPNames::DTgcDelta9Str = "DTgcDelta9";
QString BFPNames::DTgcDelta10Str = "DTgcDelta10";
QString BFPNames::DTgcDelta11Str = "DTgcDelta11";
QString BFPNames::DTgcDelta12Str = "DTgcDelta12";
QString BFPNames::DTgcDelta13Str = "DTgcDelta13";
QString BFPNames::DTgcDelta14Str = "DTgcDelta14";
QString BFPNames::DTgcDelta15Str = "DTgcDelta15";
QString BFPNames::TriplexDTgcDelta0Str = "TriplexDTgcDelta0";
QString BFPNames::TriplexDTgcDelta1Str = "TriplexDTgcDelta1";
QString BFPNames::TriplexDTgcDelta2Str = "TriplexDTgcDelta2";
QString BFPNames::TriplexDTgcDelta3Str = "TriplexDTgcDelta3";
QString BFPNames::TriplexDTgcDelta4Str = "TriplexDTgcDelta4";
QString BFPNames::TriplexDTgcDelta5Str = "TriplexDTgcDelta5";
QString BFPNames::TriplexDTgcDelta6Str = "TriplexDTgcDelta6";
QString BFPNames::TriplexDTgcDelta7Str = "TriplexDTgcDelta7";
QString BFPNames::TriplexDTgcDelta8Str = "TriplexDTgcDelta8";
QString BFPNames::TriplexDTgcDelta9Str = "TriplexDTgcDelta9";
QString BFPNames::TriplexDTgcDelta10Str = "TriplexDTgcDelta10";
QString BFPNames::TriplexDTgcDelta11Str = "TriplexDTgcDelta11";
QString BFPNames::TriplexDTgcDelta12Str = "TriplexDTgcDelta12";
QString BFPNames::TriplexDTgcDelta13Str = "TriplexDTgcDelta13";
QString BFPNames::TriplexDTgcDelta14Str = "TriplexDTgcDelta14";
QString BFPNames::TriplexDTgcDelta15Str = "TriplexDTgcDelta15";
QString BFPNames::ThiTriplexDTgcDelta0Str = "ThiTriplexDTgcDelta0";
QString BFPNames::ThiTriplexDTgcDelta1Str = "ThiTriplexDTgcDelta1";
QString BFPNames::ThiTriplexDTgcDelta2Str = "ThiTriplexDTgcDelta2";
QString BFPNames::ThiTriplexDTgcDelta3Str = "ThiTriplexDTgcDelta3";
QString BFPNames::ThiTriplexDTgcDelta4Str = "ThiTriplexDTgcDelta4";
QString BFPNames::ThiTriplexDTgcDelta5Str = "ThiTriplexDTgcDelta5";
QString BFPNames::ThiTriplexDTgcDelta6Str = "ThiTriplexDTgcDelta6";
QString BFPNames::ThiTriplexDTgcDelta7Str = "ThiTriplexDTgcDelta7";
QString BFPNames::ThiTriplexDTgcDelta8Str = "ThiTriplexDTgcDelta8";
QString BFPNames::ThiTriplexDTgcDelta9Str = "ThiTriplexDTgcDelta9";
QString BFPNames::ThiTriplexDTgcDelta10Str = "ThiTriplexDTgcDelta10";
QString BFPNames::ThiTriplexDTgcDelta11Str = "ThiTriplexDTgcDelta11";
QString BFPNames::ThiTriplexDTgcDelta12Str = "ThiTriplexDTgcDelta12";
QString BFPNames::ThiTriplexDTgcDelta13Str = "ThiTriplexDTgcDelta13";
QString BFPNames::ThiTriplexDTgcDelta14Str = "ThiTriplexDTgcDelta14";
QString BFPNames::ThiTriplexDTgcDelta15Str = "ThiTriplexDTgcDelta15";
QString BFPNames::ThiDTgcDelta0Str = "ThiDTgcDelta0";
QString BFPNames::ThiDTgcDelta1Str = "ThiDTgcDelta1";
QString BFPNames::ThiDTgcDelta2Str = "ThiDTgcDelta2";
QString BFPNames::ThiDTgcDelta3Str = "ThiDTgcDelta3";
QString BFPNames::ThiDTgcDelta4Str = "ThiDTgcDelta4";
QString BFPNames::ThiDTgcDelta5Str = "ThiDTgcDelta5";
QString BFPNames::ThiDTgcDelta6Str = "ThiDTgcDelta6";
QString BFPNames::ThiDTgcDelta7Str = "ThiDTgcDelta7";
QString BFPNames::ThiDTgcDelta8Str = "ThiDTgcDelta8";
QString BFPNames::ThiDTgcDelta9Str = "ThiDTgcDelta9";
QString BFPNames::ThiDTgcDelta10Str = "ThiDTgcDelta10";
QString BFPNames::ThiDTgcDelta11Str = "ThiDTgcDelta11";
QString BFPNames::ThiDTgcDelta12Str = "ThiDTgcDelta12";
QString BFPNames::ThiDTgcDelta13Str = "ThiDTgcDelta13";
QString BFPNames::ThiDTgcDelta14Str = "ThiDTgcDelta14";
QString BFPNames::ThiDTgcDelta15Str = "ThiDTgcDelta15";
QString BFPNames::DopFreqDeltaStr = "DopFreqDelta";
QString BFPNames::DopFNumStr = "DopFNum";
QString BFPNames::DopTransmitStepDisStr = "DopTransmitStepDis";
QString BFPNames::DopTransmitBeamDevEleNumStr = "DopTransmitBeamDevEleNum";
QString BFPNames::DopReceiveFocusDisStr = "DopReceiveFocusDis";
QString BFPNames::DopReceiveBeamDevEleNumStr = "DopReceiveBeamDevEleNum";
QString BFPNames::DopReceiveUnitVariFocusNumStr = "DopReceiveUnitVariFocusNum";
QString BFPNames::QFlowModeStr = "QFlowMode";
QString BFPNames::QFlowOnStr = "QFlowOn";
QString BFPNames::QBeamOnStr = "QBeamOn";
QString BFPNames::XContrastValueStr = "XContrastValue";
QString BFPNames::BCImagesOnStr = "BCImagesOn";
QString BFPNames::AdjustmentOfBStr = "AdjustmentOfB";
QString BFPNames::AdjustmentOfCStr = "AdjustmentOfC";
QString BFPNames::AdjustmentOfDStr = "AdjustmentOfD";
QString BFPNames::RvTxBStr = "RvTxB";
QString BFPNames::RvTxCStr = "RvTxC";
QString BFPNames::RvTxDStr = "RvTxD";
QString BFPNames::PrtDeltaOnLowDensityStr = "PrtDeltaOnLowDensity";
QString BFPNames::ImagePixelBitsStr = "ImagePixelBits";
QString BFPNames::TriplexFrequencyDeltaTr1Str = "TriplexFrequencyDeltaTr1";
QString BFPNames::TriplexFilterCoefDeltaTr1Str = "TriplexFilterCoefDeltaTr1";
QString BFPNames::TriplexFrequencyDeltaTr2Str = "TriplexFrequencyDeltaTr2";
QString BFPNames::TriplexFilterCoefDeltaTr2Str = "TriplexFilterCoefDeltaTr2";
QString BFPNames::TriplexFrequencyDeltaTr3Str = "TriplexFrequencyDeltaTr3";
QString BFPNames::TriplexFilterCoefDeltaTr3Str = "TriplexFilterCoefDeltaTr3";
QString BFPNames::TriplexFrequencyDeltaTr4Str = "TriplexFrequencyDeltaTr4";
QString BFPNames::TriplexFilterCoefDeltaTr4Str = "TriplexFilterCoefDeltaTr4";
QString BFPNames::ROISteerAnglesStr = "ROISteerAngles";
QString BFPNames::TICEnStr = "TICEn";
QString BFPNames::TICStr = "TIC";
QString BFPNames::ThresholdStr = "Threshold";
QString BFPNames::DTraceSmoothStr = "DTraceSmooth";
QString BFPNames::ImageNumStr = "ImageNum";
QString BFPNames::SopInstanceIdStr = "SopInstanceId";
QString BFPNames::SexInfoStr = "SexInfo";
QString BFPNames::ImageZoomRatioStr = "ImageZoomRatio";
QString BFPNames::ImageLeftTopPointXStr = "ImageLeftTopPointX";
QString BFPNames::ImageLeftTopPointYStr = "ImageLeftTopPointY";
QString BFPNames::ElastoStrainThreshold1Str = "ElastoStrainThreshold1";
QString BFPNames::ElastoStrainThreshold2Str = "ElastoStrainThreshold2";
QString BFPNames::ElastoStrainThreshold3Str = "ElastoStrainThreshold3";
QString BFPNames::ElastoStrainThreshold4Str = "ElastoStrainThreshold4";
QString BFPNames::ElastoStrainThreshold5Str = "ElastoStrainThreshold5";
QString BFPNames::ElastoStrainThreshold6Str = "ElastoStrainThreshold6";
QString BFPNames::ElastoFrameAverageNStr = "ElastoFrameAverageN";
QString BFPNames::ElastoStrainThresholdDownStr = "ElastoStrainThresholdDown";
QString BFPNames::ElastoStrainThresholdUpStr = "ElastoStrainThresholdUp";
QString BFPNames::ElastoThresholdXStr = "ElastoThresholdX";
QString BFPNames::ElastoThresholdYStr = "ElastoThresholdY";
QString BFPNames::ElastoTransparencyStr = "ElastoTransparency";
QString BFPNames::ElastoDepthDeltaStr = "ElastoDepthDelta";
QString BFPNames::ElastoStrainMaxStr = "ElastoStrainMax";
QString BFPNames::ElastoQualityLevelStr = "ElastoQualityLevel";
QString BFPNames::ElastoRebootTimeStr = "ElastoRebootTime";
QString BFPNames::ElastoStrainListStr = "ElastoStrainList";
QString BFPNames::ElastoStrainStr = "ElastoStrain";
QString BFPNames::ElastoStrainIndexStr = "ElastoStrainIndex";
QString BFPNames::IsElastoUnderThreshold1Str = "IsElastoUnderThreshold1";
QString BFPNames::FreeMLineNumStr = "FreeMLineNum";
QString BFPNames::FreeMLineNoStr = "FreeMLineNo";
QString BFPNames::FreeMStartLine1Str = "FreeMStartLine1";
QString BFPNames::FreeMStartDepthMM1Str = "FreeMStartDepthMM1";
QString BFPNames::FreeMAngle1Str = "FreeMAngle1";
QString BFPNames::FreeMStartLine2Str = "FreeMStartLine2";
QString BFPNames::FreeMStartDepthMM2Str = "FreeMStartDepthMM2";
QString BFPNames::FreeMAngle2Str = "FreeMAngle2";
QString BFPNames::FreeMStartLine3Str = "FreeMStartLine3";
QString BFPNames::FreeMStartDepthMM3Str = "FreeMStartDepthMM3";
QString BFPNames::FreeMAngle3Str = "FreeMAngle3";
QString BFPNames::FreeMBlockStr = "FreeMBlock";
QString BFPNames::FocusCombineStartStr = "FocusCombineStart";
QString BFPNames::FocusCombineLenStr = "FocusCombineLen";
QString BFPNames::iImageEffectStr = "iImageEffect";
QString BFPNames::GainTDI_DeltaStr = "GainTDI_Delta";
QString BFPNames::GainColorTM_DeltaStr = "GainColorTM_Delta";
QString BFPNames::TGCMaxStr = "TGCMax";
QString BFPNames::TGCMinStr = "TGCMin";
QString BFPNames::LmpStr = "Lmp";
QString BFPNames::LmpGaStr = "LmpGa";
QString BFPNames::LmpEddStr = "LmpEdd";
QString BFPNames::CurvedPanoramicEnableStr = "CurvedPanoramicEnable";
QString BFPNames::CurvedPanoramicLengthStr = "CurvedPanoramicLength";
QString BFPNames::CurvedPanoramicImageSizeStr = "CurvedPanoramicImageSize";
QString BFPNames::CurvedPanoramicPointListStr = "CurvedPanoramicPointList";
QString BFPNames::CurvedPanoramicROIVisibleStr = "CurvedPanoramicROIVisible";
QString BFPNames::LinearAreaRectStr = "LinearAreaRect";
QString BFPNames::AutoBrightNessStr = "AutoBrightNess";
QString BFPNames::Up_0Str = "Up_0";
QString BFPNames::Up_1Str = "Up_1";
QString BFPNames::Up_2Str = "Up_2";
QString BFPNames::Up_3Str = "Up_3";
QString BFPNames::Left_0Str = "Left_0";
QString BFPNames::Left_1Str = "Left_1";
QString BFPNames::Left_2Str = "Left_2";
QString BFPNames::Left_3Str = "Left_3";
QString BFPNames::ImageZoomCoef_0Str = "ImageZoomCoef_0";
QString BFPNames::ImageZoomCoef_1Str = "ImageZoomCoef_1";
QString BFPNames::ImageZoomCoef_2Str = "ImageZoomCoef_2";
QString BFPNames::ImageZoomCoef_3Str = "ImageZoomCoef_3";
QString BFPNames::StartDepthMM_0Str = "StartDepthMM_0";
QString BFPNames::StartDepthMM_1Str = "StartDepthMM_1";
QString BFPNames::StartDepthMM_2Str = "StartDepthMM_2";
QString BFPNames::StartDepthMM_3Str = "StartDepthMM_3";
QString BFPNames::PixelSizeMM_0Str = "PixelSizeMM_0";
QString BFPNames::PixelSizeMM_1Str = "PixelSizeMM_1";
QString BFPNames::PixelSizeMM_2Str = "PixelSizeMM_2";
QString BFPNames::PixelSizeMM_3Str = "PixelSizeMM_3";
QString BFPNames::Rotation_0Str = "Rotation_0";
QString BFPNames::Rotation_1Str = "Rotation_1";
QString BFPNames::Rotation_2Str = "Rotation_2";
QString BFPNames::Rotation_3Str = "Rotation_3";
QString BFPNames::BSteeringScan_0Str = "BSteeringScan_0";
QString BFPNames::BSteeringScan_1Str = "BSteeringScan_1";
QString BFPNames::BSteeringScan_2Str = "BSteeringScan_2";
QString BFPNames::BSteeringScan_3Str = "BSteeringScan_3";
QString BFPNames::StartLine_0Str = "StartLine_0";
QString BFPNames::StartLine_1Str = "StartLine_1";
QString BFPNames::StartLine_2Str = "StartLine_2";
QString BFPNames::StartLine_3Str = "StartLine_3";
QString BFPNames::StressEchoEnStr = "StressEchoEn";
QString BFPNames::StressEchoLevelStr = "StressEchoLevel";
QString BFPNames::StressEchoProjectionStr = "StressEchoProjection";
QString BFPNames::StressEchoTimer1EnStr = "StressEchoTimer1En";
QString BFPNames::StressEchoTimer2EnStr = "StressEchoTimer2En";
QString BFPNames::StressEchoTimer1Str = "StressEchoTimer1";
QString BFPNames::StressEchoTimer2Str = "StressEchoTimer2";
QString BFPNames::FourDThresholdStr = "FourDThreshold";
QString BFPNames::FourDRenderStr = "FourDRender";
QString BFPNames::FourDSmoothStr = "FourDSmooth";
QString BFPNames::FourDFrameRateStr = "FourDFrameRate";
QString BFPNames::FourDPaletteStr = "FourDPalette";
QString BFPNames::FourDLightStr = "FourDLight";
QString BFPNames::FourDVirtualHDOnStr = "FourDVirtualHDOn";
QString BFPNames::FourDDirectionSetStr = "FourDDirectionSet";
QString BFPNames::FourDQualityIndexStr = "FourDQualityIndex";
QString BFPNames::FourDQualityValueStr = "FourDQualityValue";
QString BFPNames::FourDReadLightAngleIndexStr = "FourDReadLightAngleIndex";
QString BFPNames::FourDAngleRatioIndexStr = "FourDAngleRatioIndex";
QString BFPNames::FourDAngleRatioStr = "FourDAngleRatio";
QString BFPNames::FourDDataiImageParasIndexStr = "FourDDataiImageParasIndex";
QString BFPNames::FourDParasSettingIndexStr = "FourDParasSettingIndex";
QString BFPNames::FourDRenderModeIndexStr = "FourDRenderModeIndex";
QString BFPNames::FourDSliceNumStr = "FourDSliceNum";
QString BFPNames::FourDMotorAngleStr = "FourDMotorAngle";
QString BFPNames::FourDRoiRatioStr = "FourDRoiRatio";
QString BFPNames::FourDRoiZoomValueStr = "FourDRoiZoomValue";
QString BFPNames::FourDRectMinRatioStr = "FourDRectMinRatio";
QString BFPNames::FourDRectRatioStr = "FourDRectRatio";
QString BFPNames::FourDRectMaxHeightStr = "FourDRectMaxHeight";
QString BFPNames::FourDMaxVolumeCountStr = "FourDMaxVolumeCount";
QString BFPNames::FourDCinProcessingStr = "FourDCinProcessing";
QString BFPNames::TransformationAxisStr = "TransformationAxis";
QString BFPNames::FourDStartHeightStr = "FourDStartHeight";
QString BFPNames::FourDKnotPosChangedStr = "FourDKnotPosChanged";
QString BFPNames::ShowFourDWidgetStr = "ShowFourDWidget";
QString BFPNames::FourDGroupParasStr = "FourDGroupParas";
QString BFPNames::FourDGrayCurveIndexStr = "FourDGrayCurveIndex";
QString BFPNames::FourDGammaStr = "FourDGamma";
QString BFPNames::FourDSamplePointsStr = "FourDSamplePoints";
QString BFPNames::QuadplexModeStr = "QuadplexMode";
QString BFPNames::QuadplexRefreshEnStr = "QuadplexRefreshEn";
QString BFPNames::ContrastLevelStrStr = "ContrastLevelStr";
QString BFPNames::AxialSizeStrStr = "AxialSizeStr";
QString BFPNames::ContrastLevelStr = "ContrastLevel";
QString BFPNames::AxialSizeStr = "AxialSize";
QString BFPNames::LateralSizeStr = "LateralSize";
QString BFPNames::LowFilterAxialSigmaStr = "LowFilterAxialSigma";
QString BFPNames::LowFilterLateralSigmaStr = "LowFilterLateralSigma";
QString BFPNames::ECGDisplayFpsStr = "ECGDisplayFps";
QString BFPNames::ECGInvertHorizontalStr = "ECGInvertHorizontal";
QString BFPNames::ECGInvertVerticalStr = "ECGInvertVertical";
QString BFPNames::ECGStartPosStr = "ECGStartPos";
QString BFPNames::ECGHRValueStr = "ECGHRValue";
QString BFPNames::ZoomPercentStr = "ZoomPercent";
QString BFPNames::BRXLnumStr = "BRXLnum";
QString BFPNames::CRXLnumStr = "CRXLnum";
QString BFPNames::AnyDensityEnStr = "AnyDensityEn";
QString BFPNames::ColorAnyDensityEnStr = "ColorAnyDensityEn";
QString BFPNames::CSteeringAngleStr = "CSteeringAngle";
QString BFPNames::LineSpacingMMStr = "LineSpacingMM";
QString BFPNames::AngleSpacingRadStr = "AngleSpacingRad";
QString BFPNames::AngleSpacingRadForCStr = "AngleSpacingRadForC";
QString BFPNames::DataSaveEnStr = "DataSaveEn";
QString BFPNames::CFMSlopCStr = "CFMSlopC";
QString BFPNames::CFMSlopPDStr = "CFMSlopPD";
QString BFPNames::DynamicRangeForPDStr = "DynamicRangeForPD";
QString BFPNames::PaVertDistEnableStr = "PaVertDistEnable";
QString BFPNames::BloodEnableFlagStr = "BloodEnableFlag";
QString BFPNames::TrapezoidalAngleStr = "TrapezoidalAngle";
QString BFPNames::dopplerDSCMethodStr = "dopplerDSCMethod";
QString BFPNames::FPGAPlatformTypeStr = "FPGAPlatformType";
QString BFPNames::BSStartPointStr = "BSStartPoint";
QString BFPNames::BSSlopeStr = "BSSlope";
QString BFPNames::SpecFreshStr = "SpecFresh";
QString BFPNames::SupportCQYZExtStr = "SupportCQYZExt";
QString BFPNames::CQYZLevelStr = "CQYZLevel";
QString BFPNames::ImageModesStr = "ImageModes";
QString BFPNames::ImageLayoutNumStr = "ImageLayoutNum";
QString BFPNames::ImageModeUpFilterStr = "ImageModeUpFilter";
QString BFPNames::ImageModeLeftFilterStr = "ImageModeLeftFilter";
QString BFPNames::ImageModeRotationFilterStr = "ImageModeRotationFilter";
QString BFPNames::ImageModeScaleFilterStr = "ImageModeScaleFilter";
QString BFPNames::ImageRenderRectsStr = "ImageRenderRects";
QString BFPNames::ImageModeRectsStr = "ImageModeRects";
QString BFPNames::FourDWinLayoutStr = "FourDWinLayout";
QString BFPNames::FourDVisualDirectionStr = "FourDVisualDirection";
QString BFPNames::FourDLeftPara2DBckStr = "FourDLeftPara2DBck";
QString BFPNames::FourDLeftPara2DBiasStr = "FourDLeftPara2DBias";
QString BFPNames::FourDLeftPara2DPosStr = "FourDLeftPara2DPos";
QString BFPNames::FourDLeftPara3DBckStr = "FourDLeftPara3DBck";
QString BFPNames::FourDLeftPara3DBiasStr = "FourDLeftPara3DBias";
QString BFPNames::FourDLeftPara3DPosStr = "FourDLeftPara3DPos";
QString BFPNames::FreeHand3DModeStr = "FreeHand3DMode";
QString BFPNames::FreeHand3DRoiOnStr = "FreeHand3DRoiOn";
QString BFPNames::FreeHand3DVolmnSpacingStr = "FreeHand3DVolmnSpacing";
QString BFPNames::FreeHand3DRotateAxisStr = "FreeHand3DRotateAxis";
QString BFPNames::FreeHand3DRoiRectStr = "FreeHand3DRoiRect";
QString BFPNames::LGCEnStr = "LGCEn";
QString BFPNames::LGCControlEnStr = "LGCControlEn";
QString BFPNames::LGC0Str = "LGC0";
QString BFPNames::LGC1Str = "LGC1";
QString BFPNames::LGC2Str = "LGC2";
QString BFPNames::LGC3Str = "LGC3";
QString BFPNames::LGC4Str = "LGC4";
QString BFPNames::LGC5Str = "LGC5";
QString BFPNames::LGC6Str = "LGC6";
QString BFPNames::LGC7Str = "LGC7";
QString BFPNames::LGCMAXStr = "LGCMAX";
QString BFPNames::LGCMINStr = "LGCMIN";
QString BFPNames::FreeMLinesStr = "FreeMLines";
QString BFPNames::PostBGainStr = "PostBGain";
QString BFPNames::PostPWGainStr = "PostPWGain";
QString BFPNames::PostCWGainStr = "PostCWGain";
QString BFPNames::PostMGainStr = "PostMGain";
QString BFPNames::MGainShowStr = "MGainShow";
QString BFPNames::PostBDynamicRangeStr = "PostBDynamicRange";
QString BFPNames::PostPWDynamicRangeStr = "PostPWDynamicRange";
QString BFPNames::PostMDynamicRangeStr = "PostMDynamicRange";
QString BFPNames::PostCWDynamicRangeStr = "PostCWDynamicRange";
QString BFPNames::PostTGCStr = "PostTGC";
QString BFPNames::PostLGC0Str = "PostLGC0";
QString BFPNames::PostLGC1Str = "PostLGC1";
QString BFPNames::PostLGC2Str = "PostLGC2";
QString BFPNames::PostLGC3Str = "PostLGC3";
QString BFPNames::PostLGC4Str = "PostLGC4";
QString BFPNames::PostLGC5Str = "PostLGC5";
QString BFPNames::PostLGC6Str = "PostLGC6";
QString BFPNames::PostLGC7Str = "PostLGC7";
QString BFPNames::TripleModeResetEnStr = "TripleModeResetEn";
QString BFPNames::RedundantPointsStr = "RedundantPoints";
QString BFPNames::CQYZGrowingUpStr = "CQYZGrowingUp";
QString BFPNames::RenderWidgetRectsStr = "RenderWidgetRects";
QString BFPNames::RenderWidgetSizeStr = "RenderWidgetSize";
QString BFPNames::DSCImageRectsStr = "DSCImageRects";
QString BFPNames::DSCImageSizeStr = "DSCImageSize";
QString BFPNames::DSCImageZoomOnStr = "DSCImageZoomOn";
QString BFPNames::FixedSWImageZoomCofStr = "FixedSWImageZoomCof";
QString BFPNames::MDisplayFormatStr = "MDisplayFormat";
QString BFPNames::DDisplayFormatStr = "DDisplayFormat";
QString BFPNames::DTDIDisplayFormatStr = "DTDIDisplayFormat";
QString BFPNames::ImageRenderLayoutsStr = "ImageRenderLayouts";
QString BFPNames::ImageGLRenderLayoutsStr = "ImageGLRenderLayouts";
QString BFPNames::TwoDImageScaleFactorStr = "TwoDImageScaleFactor";
QString BFPNames::WaveImageScaleFactorStr = "WaveImageScaleFactor";
QString BFPNames::RenderBImageSizeStr = "RenderBImageSize";
QString BFPNames::ProbeDSCImageZoomCofStr = "ProbeDSCImageZoomCof";
QString BFPNames::DepthCMListStr = "DepthCMList";
QString BFPNames::MECGDlyDeltasStr = "MECGDlyDeltas";
QString BFPNames::FreeMECGDlyDeltasStr = "FreeMECGDlyDeltas";
QString BFPNames::DECGDlyDeltasStr = "DECGDlyDeltas";
QString BFPNames::CWDECGDlyDeltasStr = "CWDECGDlyDeltas";
QString BFPNames::DTDIECGDlyDeltasStr = "DTDIECGDlyDeltas";
QString BFPNames::AFE_LPF_FCutOff_ShowStr = "AFE_LPF_FCutOff_Show";
QString BFPNames::AFE_LNA_GAIN_ShowStr = "AFE_LNA_GAIN_Show";
QString BFPNames::AFE_HPF_FCutOff_ShowStr = "AFE_HPF_FCutOff_Show";
QString BFPNames::PrtOfB_DeltaStr = "PrtOfB_Delta";
QString BFPNames::FrameScapeTimeShowStr = "FrameScapeTimeShow";
QString BFPNames::FrameScapeTimeStr = "FrameScapeTime";
QString BFPNames::FrameScapeTimeSlowStr = "FrameScapeTimeSlow";
QString BFPNames::SampleRateDopSlowStr = "SampleRateDopSlow";
QString BFPNames::SampleRateDopNormalStr = "SampleRateDopNormal";
QString BFPNames::SampleRateDopFastStr = "SampleRateDopFast";
QString BFPNames::GainDeltaStr = "GainDelta";
QString BFPNames::DynamicRangeDeltaStr = "DynamicRangeDelta";
QString BFPNames::GainDelta1Str = "GainDelta1";
QString BFPNames::DynamicRangeDelta1Str = "DynamicRangeDelta1";
QString BFPNames::FlipLRStr = "FlipLR";
QString BFPNames::Q_IMAGEStr = "Q_IMAGE";
QString BFPNames::WallFilterStr = "WallFilter";
QString BFPNames::WallThresholdSStr = "WallThresholdS";
QString BFPNames::D_TxFNoStr = "D_TxFNo";
QString BFPNames::RenderImageZoomCofStr = "RenderImageZoomCof";
QString BFPNames::RealDepthMMStr = "RealDepthMM";
QString BFPNames::EasyPlayImageZoomCofStr = "EasyPlayImageZoomCof";
QString BFPNames::MVIDBIndexStr = "MVIDBIndex";
QString BFPNames::PWSoundDelayTimeStr = "PWSoundDelayTime";
QString BFPNames::PWWaveImageDelayTimeStr = "PWWaveImageDelayTime";
QString BFPNames::PW2DImageDelayTimeStr = "PW2DImageDelayTime";
QString BFPNames::PWECGDelayTimeStr = "PWECGDelayTime";
QString BFPNames::MWaveImageDelayTimeStr = "MWaveImageDelayTime";
QString BFPNames::M2DImageDelayTimeStr = "M2DImageDelayTime";
QString BFPNames::MECGDelayTimeStr = "MECGDelayTime";
QString BFPNames::ECGDelayTimeStr = "ECGDelayTime";
QString BFPNames::ECG2DImageDelayTimeStr = "ECG2DImageDelayTime";
QString BFPNames::BECGDelayTimeStr = "BECGDelayTime";
QString BFPNames::LineImageDebugStr = "LineImageDebug";
QString BFPNames::HeartBeatStr = "HeartBeat";
QString BFPNames::AutoFreezeThreStr = "AutoFreezeThre";
QString BFPNames::PanZoomSelectStr = "PanZoomSelect";
QString BFPNames::PanZoomMidPixelStr = "PanZoomMidPixel";
QString BFPNames::PanZoomHalfPixelStr = "PanZoomHalfPixel";
QString BFPNames::PanZoomMidDepthPixelStr = "PanZoomMidDepthPixel";
QString BFPNames::PanZoomHalfDepthPixelStr = "PanZoomHalfDepthPixel";
QString BFPNames::PanZoomOffsetDepthPixelStr = "PanZoomOffsetDepthPixel";
QString BFPNames::PanZoomOffsetWidthPixelStr = "PanZoomOffsetWidthPixel";
QString BFPNames::FreezePanZoomStr = "FreezePanZoom";
QString BFPNames::ChangeStatusStr = "ChangeStatus";
QString BFPNames::PanZoomDrawOverStr = "PanZoomDrawOver";
QString BFPNames::ChisonFourDMouseStateStr = "ChisonFourDMouseState";
QString BFPNames::PanZoomCineFreezePanZoomStr = "PanZoomCineFreezePanZoom";
QString BFPNames::DisplayStartXForPanZoomStr = "DisplayStartXForPanZoom";
QString BFPNames::DisplayStartYForPanZoomStr = "DisplayStartYForPanZoom";
QString BFPNames::DisplayWidthForPanZoomStr = "DisplayWidthForPanZoom";
QString BFPNames::DisplayHeightForPanZoomStr = "DisplayHeightForPanZoom";
QString BFPNames::TGCPOINTSStr = "TGCPOINTS";
QString BFPNames::TGCStartYStr = "TGCStartY";
QString BFPNames::TGCROIWIDTHStr = "TGCROIWIDTH";
QString BFPNames::TGCROIHEIGHTStr = "TGCROIHEIGHT";
QString BFPNames::DepthLogoVisibleStr = "DepthLogoVisible";
QString BFPNames::GainLogoVisibleStr = "GainLogoVisible";
QString BFPNames::TgcLogoVisibleStr = "TgcLogoVisible";
QString BFPNames::BufferIndexStartPosStr = "BufferIndexStartPos";
QString BFPNames::BufferIndexEndPosStr = "BufferIndexEndPos";
QString BFPNames::CinePlaySpeedAdjustStr = "CinePlaySpeedAdjust";
QString BFPNames::CineNavigateStr = "CineNavigate";
QString BFPNames::CommentFontSizeChangeStr = "CommentFontSizeChange";
QString BFPNames::MeasureResultFontSizeStr = "MeasureResultFontSize";
QString BFPNames::EnableFreezeAfterProbeFoundStr = "EnableFreezeAfterProbeFound";
QString BFPNames::RenderImageTopMarginStr = "RenderImageTopMargin";
QString BFPNames::CompoundDebugStr = "CompoundDebug";
QString BFPNames::CurvedExapandingStr = "CurvedExapanding";
QString BFPNames::DSCParameterSaverStr = "DSCParameterSaver";
QString BFPNames::ColorDataTypeStr = "ColorDataType";
QString BFPNames::LGCRectStr = "LGCRect";
QString BFPNames::LGCPOINTSStr = "LGCPOINTS";
QString BFPNames::SRIEnableStr = "SRIEnable";
QString BFPNames::SRINoiseLevelStr = "SRINoiseLevel";
QString BFPNames::SRIEdgeEnhanceStr = "SRIEdgeEnhance";
QString BFPNames::SRIFilterStrengthStr = "SRIFilterStrength";
QString BFPNames::SRIDRAdjustStr = "SRIDRAdjust";
QString BFPNames::SRIHfnoiseStr = "SRIHfnoise";
QString BFPNames::SRIEdgeThresholdStr = "SRIEdgeThreshold";
QString BFPNames::SRIDetailPreservationStr = "SRIDetailPreservation";
QString BFPNames::SRIEdgeRampDownStr = "SRIEdgeRampDown";
QString BFPNames::SRIEdgeDirectionThreshStr = "SRIEdgeDirectionThresh";
QString BFPNames::SRIHoleFillerThreshStr = "SRIHoleFillerThresh";
QString BFPNames::SRIOverallStrengthStr = "SRIOverallStrength";
QString BFPNames::SRINoiseFilterTypeStr = "SRINoiseFilterType";
QString BFPNames::SRIEdgeFilterTypeStr = "SRIEdgeFilterType";
QString BFPNames::IsNavigationEllipseHiddenStr = "IsNavigationEllipseHidden";
QString BFPNames::ShowNavigationEllipseStr = "ShowNavigationEllipse";
QString BFPNames::NeedleSizeStr = "NeedleSize";
QString BFPNames::PowerThresholdStr = "PowerThreshold";
QString BFPNames::PowerThresholdPDStr = "PowerThresholdPD";
QString BFPNames::PowerThresholdSNStr = "PowerThresholdSN";
QString BFPNames::PowerThresholdTDIStr = "PowerThresholdTDI";
QString BFPNames::PowerThresholdMVIStr = "PowerThresholdMVI";
QString BFPNames::IsDPDColorStr = "IsDPDColor";
QString BFPNames::CWDSampleRateBakStr = "CWDSampleRateBak";
QString BFPNames::ChangedByAdjustmentStr = "ChangedByAdjustment";
QString BFPNames::HPrfTopGateDeltaStr = "HPrfTopGateDelta";
QString BFPNames::SonoRemoteStr = "SonoRemote";
QString BFPNames::SonoHelpStr = "SonoHelp";
QString BFPNames::DopAnalogTgc0Str = "DopAnalogTgc0";
QString BFPNames::DopAnalogTgc1Str = "DopAnalogTgc1";
QString BFPNames::DopAnalogTgc2Str = "DopAnalogTgc2";
QString BFPNames::DopAnalogTgc3Str = "DopAnalogTgc3";
QString BFPNames::DopAnalogTgc4Str = "DopAnalogTgc4";
QString BFPNames::DopAnalogTgc5Str = "DopAnalogTgc5";
QString BFPNames::DopAnalogTgc6Str = "DopAnalogTgc6";
QString BFPNames::DopAnalogTgc7Str = "DopAnalogTgc7";
QString BFPNames::DopAnalogTgc8Str = "DopAnalogTgc8";
QString BFPNames::DopAnalogTgc9Str = "DopAnalogTgc9";
QString BFPNames::DopAnalogTgc10Str = "DopAnalogTgc10";
QString BFPNames::DopAnalogTgc11Str = "DopAnalogTgc11";
QString BFPNames::DopAnalogTgc12Str = "DopAnalogTgc12";
QString BFPNames::DopAnalogTgc13Str = "DopAnalogTgc13";
QString BFPNames::DopAnalogTgc14Str = "DopAnalogTgc14";
QString BFPNames::DopAnalogTgc15Str = "DopAnalogTgc15";
QString BFPNames::IsEnableAdjustROIStr = "IsEnableAdjustROI";
QString BFPNames::IsAutoNeedleAngleEnableStr = "IsAutoNeedleAngleEnable";
QString BFPNames::OptimizeFPSStr = "OptimizeFPS";
QString BFPNames::MBIncStr = "MBInc";
QString BFPNames::IsSonoPWOnStr = "IsSonoPWOn";
QString BFPNames::IsSmallPWStr = "IsSmallPW";
QString BFPNames::PreColorPersistenceEnableStr = "PreColorPersistenceEnable";
QString BFPNames::ColorPersistenceAlgorithmStr = "ColorPersistenceAlgorithm";
QString BFPNames::ColorPersistenceHighStr = "ColorPersistenceHigh";
QString BFPNames::ColorPersistenceLowStr = "ColorPersistenceLow";
QString BFPNames::ColorPersistenceANStr = "ColorPersistenceAN";
QString BFPNames::ColorPersistenceRatioStr = "ColorPersistenceRatio";
QString BFPNames::ColorPersistenceUsingN_1Str = "ColorPersistenceUsingN_1";
QString BFPNames::ISCFMIIREnableStr = "ISCFMIIREnable";
QString BFPNames::ColorEDAEnableStr = "ColorEDAEnable";
QString BFPNames::ColorEDASESizeStr = "ColorEDASESize";
QString BFPNames::ColorEDASETypeStr = "ColorEDASEType";
QString BFPNames::SoundPlayerDeviceNameStr = "SoundPlayerDeviceName";
QString BFPNames::SoundPlayerSampleRateStr = "SoundPlayerSampleRate";
QString BFPNames::SoundPlayerChannelCountStr = "SoundPlayerChannelCount";
QString BFPNames::SoundPlayerSampleSizeStr = "SoundPlayerSampleSize";
QString BFPNames::HideTCGForCineStr = "HideTCGForCine";
QString BFPNames::HideLGCForCineStr = "HideLGCForCine";
QString BFPNames::ArbitraryWaveFormExtendStr = "ArbitraryWaveFormExtend";
QString BFPNames::ArbitraryWaveForm1Str = "ArbitraryWaveForm1";
QString BFPNames::ArbitraryWaveForm2Str = "ArbitraryWaveForm2";
QString BFPNames::ArbitraryWaveForm3Str = "ArbitraryWaveForm3";
QString BFPNames::ArbitraryWaveForm4Str = "ArbitraryWaveForm4";
QString BFPNames::ArbitraryWaveForm6Str = "ArbitraryWaveForm6";
QString BFPNames::ArbitraryWaveForm7Str = "ArbitraryWaveForm7";
QString BFPNames::SonoNeedleStr = "SonoNeedle";
QString BFPNames::ColorTransparencyStr = "ColorTransparency";
QString BFPNames::TDITransparencyStr = "TDITransparency";
QString BFPNames::SNColorMapIndexStr = "SNColorMapIndex";
QString BFPNames::SupportTDIStr = "SupportTDI";
QString BFPNames::MVIShowStr = "MVIShow";
QString BFPNames::MVITransparencyStr = "MVITransparency";
QString BFPNames::MVITypeStr = "MVIType";
QString BFPNames::ShowBInROIStr = "ShowBInROI";
QString BFPNames::IsSupportIntegerDepthStr = "IsSupportIntegerDepth";
QString BFPNames::StartScanLineColorStr = "StartScanLineColor";
QString BFPNames::StopScanLineColorStr = "StopScanLineColor";
QString BFPNames::StartScanDisColorStr = "StartScanDisColor";
QString BFPNames::StopScanDisColorStr = "StopScanDisColor";
QString BFPNames::LineSpacingColorStr = "LineSpacingColor";
QString BFPNames::PWSecondSampDatNumStr = "PWSecondSampDatNum";
QString BFPNames::MF_Coef_Fundamental_Steer0Str = "MF_Coef_Fundamental_Steer0";
QString BFPNames::MF_Coef_Fundamental_SteerXStr = "MF_Coef_Fundamental_SteerX";
QString BFPNames::MF_Coef_Harmonic_Steer0Str = "MF_Coef_Harmonic_Steer0";
QString BFPNames::MF_Coef_Harmonic_SteerXStr = "MF_Coef_Harmonic_SteerX";
QString BFPNames::CPDSteer2Str = "CPDSteer2";
QString BFPNames::CPDSteer3Str = "CPDSteer3";
QString BFPNames::CPDSteer4Str = "CPDSteer4";
QString BFPNames::CPDSteer5Str = "CPDSteer5";
QString BFPNames::TCPDSteerStr = "TCPDSteer";
QString BFPNames::CPDSteer_CEUSStr = "CPDSteer_CEUS";
QString BFPNames::DScanLineStartXStr = "DScanLineStartX";
QString BFPNames::DScanLineStartYStr = "DScanLineStartY";
QString BFPNames::DScanLineEndXStr = "DScanLineEndX";
QString BFPNames::DScanLineEndYStr = "DScanLineEndY";
QString BFPNames::DScanLineFirstDivideYStr = "DScanLineFirstDivideY";
QString BFPNames::DScanLineSecondDivideYStr = "DScanLineSecondDivideY";
QString BFPNames::PostRawDataEnableStr = "PostRawDataEnable";
QString BFPNames::BiopsyVerifyStr = "BiopsyVerify";
QString BFPNames::BiopsyAngleChooseStr = "BiopsyAngleChoose";
QString BFPNames::BiopsyAngleIndexStr = "BiopsyAngleIndex";
QString BFPNames::BiopsyAngleMaxStr = "BiopsyAngleMax";
QString BFPNames::BiopsyAngleMinStr = "BiopsyAngleMin";
QString BFPNames::BiopsyPositionMaxStr = "BiopsyPositionMax";
QString BFPNames::BiopsyPositionMinStr = "BiopsyPositionMin";
QString BFPNames::BiopsyNeedSaveStr = "BiopsyNeedSave";
QString BFPNames::RTIMTStr = "RTIMT";
QString BFPNames::BMVIBImagesStr = "BMVIBImages";
QString BFPNames::Save8BitBeforeColorMapEnableStr = "Save8BitBeforeColorMapEnable";
QString BFPNames::SonoNerveStr = "SonoNerve";
QString BFPNames::SonoNerveIsShowStr = "SonoNerveIsShow";
QString BFPNames::SonoNerveTransStr = "SonoNerveTrans";
QString BFPNames::SonoNervePartStr = "SonoNervePart";
QString BFPNames::SupportAdjustTGCByMouseStr = "SupportAdjustTGCByMouse";
QString BFPNames::SonoThyroidStr = "SonoThyroid";
QString BFPNames::SonoCardiacStr = "SonoCardiac";
QString BFPNames::SonoCardiacSecRecognizeStr = "SonoCardiacSecRecognize";
QString BFPNames::SonoCardiacSecRatingStr = "SonoCardiacSecRating";
QString BFPNames::SonoCardiacSectionStr = "SonoCardiacSection";
QString BFPNames::SonoCarotidGuideStr = "SonoCarotidGuide";
QString BFPNames::SonoCarotidDirectionStr = "SonoCarotidDirection";
QString BFPNames::SonoCarotidLRStr = "SonoCarotidLR";
QString BFPNames::RawDataFormatStr = "RawDataFormat";
QString BFPNames::SonoMSKStr = "SonoMSK";
QString BFPNames::SonoMSKIsShowStr = "SonoMSKIsShow";
QString BFPNames::SonoMSKTransStr = "SonoMSKTrans";
QString BFPNames::SonoMSKPartStr = "SonoMSKPart";
QString BFPNames::AutoEFOnStr = "AutoEFOn";
QString BFPNames::AutoEFCurLayoutStr = "AutoEFCurLayout";
QString BFPNames::ESFrameStr = "ESFrame";
QString BFPNames::EDFrameStr = "EDFrame";
QString BFPNames::ESFrameStampStr = "ESFrameStamp";
QString BFPNames::EDFrameStampStr = "EDFrameStamp";
QString BFPNames::AutoEFStartFrameStr = "AutoEFStartFrame";
QString BFPNames::AutoEFEndFrameStr = "AutoEFEndFrame";
QString BFPNames::ESCurIndexStr = "ESCurIndex";
QString BFPNames::EDCurIndexStr = "EDCurIndex";
QString BFPNames::HasAutoEFResultStr = "HasAutoEFResult";
QString BFPNames::ImageRenderPartitionStr = "ImageRenderPartition";
QString BFPNames::AutoEFActivePartitionStr = "AutoEFActivePartition";
QString BFPNames::SonoAAAStr = "SonoAAA";
QString BFPNames::SonoAAAMessageStr = "SonoAAAMessage";
QString BFPNames::OpenElementCheckStr = "OpenElementCheck";
QString BFPNames::SonoVFStr = "SonoVF";
QString BFPNames::ColorVHSITypeStr = "ColorVHSIType";
QString BFPNames::ColorHoleFillingDBStr = "ColorHoleFillingDB";
QString BFPNames::ColorOverlapWeightStr = "ColorOverlapWeight";
QString BFPNames::ColorMappedBDataCallbackEnableStr = "ColorMappedBDataCallbackEnable";
QString BFPNames::EFastModeONStr = "EFastModeON";
QString BFPNames::FastModeONStr = "FastModeON";
QString BFPNames::AutoBLineStr = "AutoBLine";
QString BFPNames::SonoVTIStr = "SonoVTI";
QString BFPNames::PictureModeONStr = "PictureModeON";
QString BFPNames::Txbf2EnFlag_QBit10Str = "Txbf2EnFlag_QBit10";
QString BFPNames::Txbf1EnFlag_QBit10Str = "Txbf1EnFlag_QBit10";
QString BFPNames::DSPEnFlag_QBit10Str = "DSPEnFlag_QBit10";
QString BFPNames::TDIPulseNumStr = "TDIPulseNum";
QString BFPNames::PWMDutyRatio_Q5Str = "PWMDutyRatio_Q5";
QString BFPNames::ParaPresetTime_XBFStr = "ParaPresetTime_XBF";
QString BFPNames::ADCHalfEnableStr = "ADCHalfEnable";
QString BFPNames::CpdThiModeFlagStr = "CpdThiModeFlag";
QString BFPNames::SF_FlowPassThresStr = "SF_FlowPassThres";
QString BFPNames::ProbeCodeBurnSlotStr = "ProbeCodeBurnSlot";
QString BFPNames::AudioTest_ENStr = "AudioTest_EN";
QString BFPNames::CalSelStr = "CalSel";
QString BFPNames::DTHIModeStr = "DTHIMode";
QString BFPNames::DTHITxFreq1Str = "DTHITxFreq1";
QString BFPNames::DTHITxFreq2Str = "DTHITxFreq2";
QString BFPNames::DTHITxFreq3Str = "DTHITxFreq3";
QString BFPNames::DTHITxFreq4Str = "DTHITxFreq4";
QString BFPNames::DTHITxFreq5Str = "DTHITxFreq5";
QString BFPNames::CEUSFarFocusSwitchStr = "CEUSFarFocusSwitch";
QString BFPNames::CEUSFoucsSwitchStr = "CEUSFoucsSwitch";
QString BFPNames::SF_TissueGainStr = "SF_TissueGain";
QString BFPNames::ChannelIdentifier_128Str = "ChannelIdentifier_128";
QString BFPNames::CWModeStr = "CWMode";
QString BFPNames::SpectrumHornSwitchStr = "SpectrumHornSwitch";
QString BFPNames::CodeTransmitionLength11Str = "CodeTransmitionLength11";
QString BFPNames::CodeTransmitionLength12Str = "CodeTransmitionLength12";
QString BFPNames::CodeTransmitionLength21Str = "CodeTransmitionLength21";
QString BFPNames::CodeTransmitionLength22Str = "CodeTransmitionLength22";
QString BFPNames::CodeTransmitionLength31Str = "CodeTransmitionLength31";
QString BFPNames::CodeTransmitionLength32Str = "CodeTransmitionLength32";
QString BFPNames::CodeTransmitionLength41Str = "CodeTransmitionLength41";
QString BFPNames::CodeTransmitionLength42Str = "CodeTransmitionLength42";
QString BFPNames::CodeTransmitionValue11Str = "CodeTransmitionValue11";
QString BFPNames::CodeTransmitionValue12Str = "CodeTransmitionValue12";
QString BFPNames::CodeTransmitionValue21Str = "CodeTransmitionValue21";
QString BFPNames::CodeTransmitionValue22Str = "CodeTransmitionValue22";
QString BFPNames::CodeTransmitionValue31Str = "CodeTransmitionValue31";
QString BFPNames::CodeTransmitionValue32Str = "CodeTransmitionValue32";
QString BFPNames::CodeTransmitionValue41Str = "CodeTransmitionValue41";
QString BFPNames::CodeTransmitionValue42Str = "CodeTransmitionValue42";
QString BFPNames::DopSteeringDirectionStr = "DopSteeringDirection";
QString BFPNames::FourBeamInterLockTransFlagStr = "FourBeamInterLockTransFlag";
QString BFPNames::BSteerAngle2CosStr = "BSteerAngle2Cos";
QString BFPNames::BSteerAngle2TanStr = "BSteerAngle2Tan";
QString BFPNames::TXFNUM_FIVESTEERStr = "TXFNUM_FIVESTEER";
QString BFPNames::SWETransFreqStr = "SWETransFreq";
QString BFPNames::TwoDSWEModeStr = "TwoDSWEMode";
QString BFPNames::SWEOffOnStr = "SWEOffOn";
QString BFPNames::PSWEModeStr = "PSWEMode";
QString BFPNames::SWETransMinApertureStr = "SWETransMinAperture";
QString BFPNames::SWETransPulseStr = "SWETransPulse";
QString BFPNames::SWEContinueStr = "SWEContinue";
QString BFPNames::SWEReverseTransPluseStr = "SWEReverseTransPluse";
QString BFPNames::SWETimeSampleStr = "SWETimeSample";
QString BFPNames::SWEMultiFocPulsKeepStr = "SWEMultiFocPulsKeep";
QString BFPNames::SWEMultiFocEnStr = "SWEMultiFocEn";
QString BFPNames::CEUSNoiseCollectionStr = "CEUSNoiseCollection";
QString BFPNames::ContrastTypeStr = "ContrastType";
QString BFPNames::LensCompensateFlagStr = "LensCompensateFlag";
QString BFPNames::ZoneFocusFlagStr = "ZoneFocusFlag";
QString BFPNames::SWETimerModeTempStr = "SWETimerModeTemp";
QString BFPNames::SWESampleDepthWithClockSizeStr = "SWESampleDepthWithClockSize";
QString BFPNames::TrackWaveTXFocusEnableStr = "TrackWaveTXFocusEnable";
QString BFPNames::TrackWaveTXFocusOffsetTapStr = "TrackWaveTXFocusOffsetTap";
QString BFPNames::SWETransMaxApertureStr = "SWETransMaxAperture";
QString BFPNames::SWETransMaxApertureCoefStr = "SWETransMaxApertureCoef";
QString BFPNames::SWETransMaxApertureCoef2Str = "SWETransMaxApertureCoef2";
QString BFPNames::SweHarmonicEnableStr = "SweHarmonicEnable";
QString BFPNames::HDCPAWallThrStr = "HDCPAWallThr";
QString BFPNames::SMIPostGainStr = "SMIPostGain";
QString BFPNames::LensCompLengthStr = "LensCompLength";
QString BFPNames::NeedleDensityStr = "NeedleDensity";
QString BFPNames::XCorrCutOffSelStr = "XCorrCutOffSel";
QString BFPNames::NeedleLogSelStr = "NeedleLogSel";
QString BFPNames::LensCutDepthStr = "LensCutDepth";
QString BFPNames::XCorrStepStr = "XCorrStep";
QString BFPNames::XCorrOnStr = "XCorrOn";
QString BFPNames::XCorrCoefThresholdStr = "XCorrCoefThreshold";
QString BFPNames::AmplificationCoefStr = "AmplificationCoef";
QString BFPNames::SWESampleDepthWithClockSizeBottomStr = "SWESampleDepthWithClockSizeBottom";
QString BFPNames::TissueAmplificationCoeffStr = "TissueAmplificationCoeff";
QString BFPNames::BDensityTagStr = "BDensityTag";
QString BFPNames::CDensityTagStr = "CDensityTag";
QString BFPNames::ColorVarianceModeStr = "ColorVarianceMode";
QString BFPNames::AdaptiveWallFilterStr = "AdaptiveWallFilter";
QString BFPNames::CWPostGainStr = "CWPostGain";
QString BFPNames::BPostproBypassStr = "BPostproBypass";
QString BFPNames::CPostproBypassStr = "CPostproBypass";
QString BFPNames::BOverlapWetStr = "BOverlapWet";
QString BFPNames::StripRemoveEnableStr = "StripRemoveEnable";
QString BFPNames::StripRemoveFPGAStr = "StripRemoveFPGA";
QString BFPNames::FiveSocketFlagStr = "FiveSocketFlag";
QString BFPNames::LGC_OnStr = "LGC_On";
QString BFPNames::BSteeringAngle3CodingStr = "BSteeringAngle3Coding";
QString BFPNames::TXFNUM_SEVENSTEERStr = "TXFNUM_SEVENSTEER";
QString BFPNames::BTxClampLastStr = "BTxClampLast";
QString BFPNames::BTxClampPreStr = "BTxClampPre";
QString BFPNames::SWEF0Str = "SWEF0";
QString BFPNames::SWEF1Str = "SWEF1";
QString BFPNames::SWEF2Str = "SWEF2";
QString BFPNames::SWEF3Str = "SWEF3";
QString BFPNames::BTX1_DutySelStr = "BTX1_DutySel";
QString BFPNames::BTX2_DutySelStr = "BTX2_DutySel";
QString BFPNames::BTX3_DutySelStr = "BTX3_DutySel";
QString BFPNames::BTX4_DutySelStr = "BTX4_DutySel";
QString BFPNames::BTX5_DutySelStr = "BTX5_DutySel";
QString BFPNames::BTX6_DutySelStr = "BTX6_DutySel";
QString BFPNames::BTX7_DutySelStr = "BTX7_DutySel";
QString BFPNames::IndependentChanDutyOnStr = "IndependentChanDutyOn";
QString BFPNames::WaferLengthYStr = "WaferLengthY";
QString BFPNames::OneDimensionXProbeOperationModeStr = "OneDimensionXProbeOperationMode";
QString BFPNames::OneDimensionXProbeFlagStr = "OneDimensionXProbeFlag";
QString BFPNames::XBF_FPGAStr = "XBF_FPGA";
QString BFPNames::CWDFreqCoefStr = "CWDFreqCoef";
QString BFPNames::CWDAccumulateNumStr = "CWDAccumulateNum";
QString BFPNames::CWD2stFilterCoefficientSwitchStr = "CWD2stFilterCoefficientSwitch";
QString BFPNames::CWD1stExtractingFactorStr = "CWD1stExtractingFactor";
QString BFPNames::CWD2stExtractingFactorStr = "CWD2stExtractingFactor";
QString BFPNames::CWD1stFilterCoefficientStr = "CWD1stFilterCoefficient";
QString BFPNames::CWD2stFilterCoefficientStr = "CWD2stFilterCoefficient";
QString BFPNames::BSteeringAngle4CodingStr = "BSteeringAngle4Coding";
QString BFPNames::BSteeringAngle5CodingStr = "BSteeringAngle5Coding";
QString BFPNames::MF_PostScaleStr = "MF_PostScale";
QString BFPNames::CVLT100Str = "CVLT100";
QString BFPNames::SonoNeedleV2EnStr = "SonoNeedleV2En";
QString BFPNames::CHET100Str = "CHET100";
QString BFPNames::CVRT100Str = "CVRT100";
QString BFPNames::CTGC100Str = "CTGC100";
QString BFPNames::CfmNoiseSlope100Str = "CfmNoiseSlope100";
QString BFPNames::CfmNoiseVlt100Str = "CfmNoiseVlt100";
QString BFPNames::CfmFlashSlope100Str = "CfmFlashSlope100";
QString BFPNames::CfmFlashVlt100Str = "CfmFlashVlt100";
QString BFPNames::CFMEnergeCtl100Str = "CFMEnergeCtl100";
QString BFPNames::CfmEngCtrl0_100Str = "CfmEngCtrl0_100";
QString BFPNames::CfmEngCtrl1_100Str = "CfmEngCtrl1_100";
QString BFPNames::CfmEngCtrl2_100Str = "CfmEngCtrl2_100";
QString BFPNames::CfmEngCtrl3_100Str = "CfmEngCtrl3_100";
QString BFPNames::CfmEngCtrl4_100Str = "CfmEngCtrl4_100";
QString BFPNames::CfmEngCtrl5_100Str = "CfmEngCtrl5_100";
QString BFPNames::CfmEngCtrl6_100Str = "CfmEngCtrl6_100";
QString BFPNames::CfmEngCtrl7_100Str = "CfmEngCtrl7_100";
QString BFPNames::CfmEngCtrl8_100Str = "CfmEngCtrl8_100";
QString BFPNames::CfmEngCtrl9_100Str = "CfmEngCtrl9_100";
QString BFPNames::CfmEngCtrl10_100Str = "CfmEngCtrl10_100";
QString BFPNames::CfmEngCtrl11_100Str = "CfmEngCtrl11_100";
QString BFPNames::CfmEngCtrl12_100Str = "CfmEngCtrl12_100";
QString BFPNames::SWE_ZoneNumStr = "SWE_ZoneNum";
QString BFPNames::Press_LineNoStr = "Press_LineNo";
QString BFPNames::BCRelativePosInTrapezoidalModeStr = "BCRelativePosInTrapezoidalMode";
QString BFPNames::BPointNumPerLineStr = "BPointNumPerLine";
QString BFPNames::CPointNumPerLineStr = "CPointNumPerLine";
QString BFPNames::PWPointNumPerLineStr = "PWPointNumPerLine";
QString BFPNames::NPointNumPerLineStr = "NPointNumPerLine";
QString BFPNames::SystemFanControlStr = "SystemFanControl";
QString BFPNames::CPUFanControlStr = "CPUFanControl";
QString BFPNames::BottomFanControlStr = "BottomFanControl";
QString BFPNames::VS_ModeStr = "VS_Mode";
QString BFPNames::VS_MLAStr = "VS_MLA";
QString BFPNames::PhasedAngleStr = "PhasedAngle";
QString BFPNames::Delta_triplexStr = "Delta_triplex";
QString BFPNames::ImageDataSaveStepStr = "ImageDataSaveStep";
QString BFPNames::Rv4DFNoStr = "Rv4DFNo";
QString BFPNames::TriplexTxFNoStr = "TriplexTxFNo";
QString BFPNames::IsBHorizontalRulerVisibleStr = "IsBHorizontalRulerVisible";
QString BFPNames::NeedleStrIdsStr = "NeedleStrIds";
QString BFPNames::NeedleIndexStr = "NeedleIndex";
QString BFPNames::ChisonFourDPaletteStr = "ChisonFourDPalette";
QString BFPNames::ChisonFourDThresholdStr = "ChisonFourDThreshold";
QString BFPNames::ChisonFourDRenderStr = "ChisonFourDRender";
QString BFPNames::ChisonFourDClipCurvePointsStr = "ChisonFourDClipCurvePoints";
QString BFPNames::CpaDBIndexStr = "CpaDBIndex";
QString BFPNames::CXFNumStr = "CXFNum";
QString BFPNames::IsFocusFreqDependStr = "IsFocusFreqDepend";
QString BFPNames::IsTxCodeEnabledStr = "IsTxCodeEnabled";
QString BFPNames::LCDLightShowStr = "LCDLightShow";
QString BFPNames::Compound1TypeStr = "Compound1Type";
QString BFPNames::CompoundMethodStr = "CompoundMethod";
QString BFPNames::AmplifyingFrameBoundaryDepthStr = "AmplifyingFrameBoundaryDepth";
QString BFPNames::NeedleAngleStr = "NeedleAngle";
QString BFPNames::Compound_BetaStr = "Compound_Beta";
QString BFPNames::Compound_GammaStr = "Compound_Gamma";
QString BFPNames::ManualWeightForCenterStr = "ManualWeightForCenter";
QString BFPNames::ManualWeightForLeftStr = "ManualWeightForLeft";
QString BFPNames::ManualWeightForRightStr = "ManualWeightForRight";
QString BFPNames::ManualWeight2FramesForCenterStr = "ManualWeight2FramesForCenter";
QString BFPNames::ManualWeight2FramesForSteeringStr = "ManualWeight2FramesForSteering";
QString BFPNames::PersistenceTypeStr = "PersistenceType";
QString BFPNames::LineImageSteeringDebugStr = "LineImageSteeringDebug";
QString BFPNames::LineColorMapDebugStr = "LineColorMapDebug";
QString BFPNames::TeeProbeTemperatureStr = "TeeProbeTemperature";
QString BFPNames::TeeProbeAngleStr = "TeeProbeAngle";
QString BFPNames::PatientTemperatureStr = "PatientTemperature";
QString BFPNames::TeeProbeTemperatureWarningStr = "TeeProbeTemperatureWarning";
QString BFPNames::PanZoomAreaMidPixelStr = "PanZoomAreaMidPixel";
QString BFPNames::PanZoomAreaHalfPixelStr = "PanZoomAreaHalfPixel";
QString BFPNames::PanZoomAreaMidDepthPixelStr = "PanZoomAreaMidDepthPixel";
QString BFPNames::PanZoomAreaHalfDepthPixelStr = "PanZoomAreaHalfDepthPixel";
QString BFPNames::RevLNumHighBStr = "RevLNumHighB";
QString BFPNames::RevLNumMidBStr = "RevLNumMidB";
QString BFPNames::RevLNumLowBStr = "RevLNumLowB";
QString BFPNames::RevLNumHighCStr = "RevLNumHighC";
QString BFPNames::RevLNumMidCStr = "RevLNumMidC";
QString BFPNames::RevLNumLowCStr = "RevLNumLowC";
QString BFPNames::RevLNumHighSWEStr = "RevLNumHighSWE";
QString BFPNames::RevLNumMidSWEStr = "RevLNumMidSWE";
QString BFPNames::RevLNumLowSWEStr = "RevLNumLowSWE";
QString BFPNames::RevLNumHighBZoomStr = "RevLNumHighBZoom";
QString BFPNames::RevLNumMidBZoomStr = "RevLNumMidBZoom";
QString BFPNames::RevLNumLowBZoomStr = "RevLNumLowBZoom";
QString BFPNames::IsRevLNumLowB_TDI_SaveStr = "IsRevLNumLowB_TDI_Save";
QString BFPNames::RevLNumLowB_TDIStr = "RevLNumLowB_TDI";
QString BFPNames::RevLNumLowBZoom_TDIStr = "RevLNumLowBZoom_TDI";
QString BFPNames::RevLNumBZoomStr = "RevLNumBZoom";
QString BFPNames::RevLNumFourDStr = "RevLNumFourD";
QString BFPNames::ScanDisDopplerStr = "ScanDisDoppler";
QString BFPNames::LineDensityCStr = "LineDensityC";
QString BFPNames::LineDensityPDStr = "LineDensityPD";
QString BFPNames::LineDensityDPDStr = "LineDensityDPD";
QString BFPNames::FHILevelStr = "FHILevel";
QString BFPNames::FHILevelOfTDIStr = "FHILevelOfTDI";
QString BFPNames::FHILevel1IndexsStr = "FHILevel1Indexs";
QString BFPNames::FHILevel2IndexsStr = "FHILevel2Indexs";
QString BFPNames::TxIndex5Str = "TxIndex5";
QString BFPNames::TxIndex7Str = "TxIndex7";
QString BFPNames::FACWStr = "FACW";
QString BFPNames::MFCBlockUpdatedStr = "MFCBlockUpdated";
QString BFPNames::DopSteerAngleIndexStr = "DopSteerAngleIndex";
QString BFPNames::BMIIRFilterMatchRTLStr = "BMIIRFilterMatchRTL";
QString BFPNames::BDepthDeltaStr = "BDepthDelta";
QString BFPNames::CDepthDeltaStr = "CDepthDelta";
QString BFPNames::PWDepthDeltaStr = "PWDepthDelta";
QString BFPNames::PWDopplerStartPoint0Str = "PWDopplerStartPoint0";
QString BFPNames::PWDopplerStartPoint1Str = "PWDopplerStartPoint1";
QString BFPNames::PWDopplerStartPoint2Str = "PWDopplerStartPoint2";
QString BFPNames::PWDopplerStartPoint3Str = "PWDopplerStartPoint3";
QString BFPNames::OverlappingMBStr = "OverlappingMB";
QString BFPNames::InterpolatorOrderStr = "InterpolatorOrder";
QString BFPNames::TXFNo_Steer1Str = "TXFNo_Steer1";
QString BFPNames::TxWeightDebugEnableStr = "TxWeightDebugEnable";
QString BFPNames::TxWeightValueStr = "TxWeightValue";
QString BFPNames::TxWeightPosStr = "TxWeightPos";
QString BFPNames::TxWeightSaveDataEnableStr = "TxWeightSaveDataEnable";
QString BFPNames::FreqCenterStr = "FreqCenter";
QString BFPNames::PlaneAreaMethodStr = "PlaneAreaMethod";
QString BFPNames::FocusOffsetStr = "FocusOffset";
QString BFPNames::ZminStr = "Zmin";
QString BFPNames::ZmaxStr = "Zmax";
QString BFPNames::Zmin_2Str = "Zmin_2";
QString BFPNames::Zmax_2Str = "Zmax_2";
QString BFPNames::TxNumPerRxBeamStr = "TxNumPerRxBeam";
QString BFPNames::MinTxNumPerRxBeamStr = "MinTxNumPerRxBeam";
QString BFPNames::AmplifyStr = "Amplify";
QString BFPNames::WindowOffsetStr = "WindowOffset";
QString BFPNames::MaxAlphaStr = "MaxAlpha";
QString BFPNames::MinAlphaStr = "MinAlpha";
QString BFPNames::AlphaStepNumStr = "AlphaStepNum";
QString BFPNames::SendData66Str = "SendData66";
QString BFPNames::DelayOffsetStr = "DelayOffset";
QString BFPNames::TxFNo_ZeusStr = "TxFNo_Zeus";
QString BFPNames::OrderHighDensityStr = "OrderHighDensity";
QString BFPNames::OrderColorLineDensityStr = "OrderColorLineDensity";
QString BFPNames::OrderLineDensityPDStr = "OrderLineDensityPD";
QString BFPNames::OrderLineDensityDPDStr = "OrderLineDensityDPD";
QString BFPNames::ChannelIndexStr = "ChannelIndex";
QString BFPNames::HighDensityInCStr = "HighDensityInC";
QString BFPNames::HighDensityInTDIStr = "HighDensityInTDI";
QString BFPNames::LineSpaceCodeStr = "LineSpaceCode";
QString BFPNames::FourDEnterModeStr = "FourDEnterMode";
QString BFPNames::ChisonFourDGroupParasStr = "ChisonFourDGroupParas";
QString BFPNames::DetailWeightDeltaSraOnStr = "DetailWeightDeltaSraOn";
QString BFPNames::DetailWeightDeltaSraCpdOffStr = "DetailWeightDeltaSraCpdOff";
QString BFPNames::EnhanceStr = "Enhance";
QString BFPNames::VirtualVertexTrapezoidalModeStr = "VirtualVertexTrapezoidalMode";
QString BFPNames::BTxFNoStr = "BTxFNo";
QString BFPNames::CTxFNoStr = "CTxFNo";
QString BFPNames::UTGC_SegmentStr = "UTGC_Segment";
QString BFPNames::PW_DummyCountStr = "PW_DummyCount";
QString BFPNames::Triplex_DummyCountStr = "Triplex_DummyCount";
QString BFPNames::SoundOutputTrigEnableStr = "SoundOutputTrigEnable";
QString BFPNames::PowerThresholdEnStr = "PowerThresholdEn";
QString BFPNames::PowerThresholdLevel1Str = "PowerThresholdLevel1";
QString BFPNames::PowerThresholdlevel2Str = "PowerThresholdlevel2";
QString BFPNames::PWRSync1Str = "PWRSync1";
QString BFPNames::PWRSync2Str = "PWRSync2";
QString BFPNames::MBSync1Str = "MBSync1";
QString BFPNames::MBSync2Str = "MBSync2";
QString BFPNames::MBSync3Str = "MBSync3";
QString BFPNames::MBSyncEnableStr = "MBSyncEnable";
QString BFPNames::PWRSyncEnableStr = "PWRSyncEnable";
QString BFPNames::Channel32EnableStr = "Channel32Enable";
QString BFPNames::Channel16EnableStr = "Channel16Enable";
QString BFPNames::Channel64EnableStr = "Channel64Enable";
QString BFPNames::Channel128EnableStr = "Channel128Enable";
QString BFPNames::OrderLineDensityTDIStr = "OrderLineDensityTDI";
QString BFPNames::SonoPleuraStr = "SonoPleura";
QString BFPNames::LineDensityTDIStr = "LineDensityTDI";
QString BFPNames::PWRSync1ShowValueStr = "PWRSync1ShowValue";
QString BFPNames::PWRSync2ShowValueStr = "PWRSync2ShowValue";
QString BFPNames::MBSync1ShowValueStr = "MBSync1ShowValue";
QString BFPNames::MBSync2ShowValueStr = "MBSync2ShowValue";
QString BFPNames::MBSync3ShowValueStr = "MBSync3ShowValue";
QString BFPNames::iImageEffectRotationStr = "iImageEffectRotation";
QString BFPNames::PTBFrmSelfEnStr = "PTBFrmSelfEn";
QString BFPNames::PTBFrmSelfStr = "PTBFrmSelf";
QString BFPNames::FanControlSysStr = "FanControlSys";
QString BFPNames::FanControlCPUStr = "FanControlCPU";
QString BFPNames::ProbeAssessmentStr = "ProbeAssessment";
QString BFPNames::DepthShowMMStr = "DepthShowMM";
QString BFPNames::BGainStr = "BGain";
QString BFPNames::ScanDisMLineStr = "ScanDisMLine";
QString BFPNames::LineSpacingDensityBStr = "LineSpacingDensityB";
QString BFPNames::ProbeModeNameStr = "ProbeModeName";
QString BFPNames::PresetProbeNameStr = "PresetProbeName";
QString BFPNames::ImageTranslateXStr = "ImageTranslateX";
QString BFPNames::ImageTranslateYStr = "ImageTranslateY";
QString BFPNames::ProbeAliasNameStr = "ProbeAliasName";
QString BFPNames::ProbeAliasTypeStr = "ProbeAliasType";
QString BFPNames::MF_64TapStr = "MF_64Tap";
QString BFPNames::PetTypeForECGStr = "PetTypeForECG";
QString BFPNames::Color_MFCStr = "Color_MFC";
QString BFPNames::PWFocusPosCom4Str = "PWFocusPosCom4";
QString BFPNames::HVBToHVAEnableFlagStr = "HVBToHVAEnableFlag";
QString BFPNames::TXCloseStr = "TXClose";
QString BFPNames::SWE_FocusIntervelStr = "SWE_FocusIntervel";
QString BFPNames::MultiAcousticPowerStr = "MultiAcousticPower";
QString BFPNames::VedioPrintTrigStr = "VedioPrintTrig";
QString BFPNames::PWDummyCountStr = "PWDummyCount";
QString BFPNames::VoltageTempThresholdEnableStr = "VoltageTempThresholdEnable";
QString BFPNames::VoltageTempThresholdLev1Str = "VoltageTempThresholdLev1";
QString BFPNames::VoltageTempThresholdLev2Str = "VoltageTempThresholdLev2";
QString BFPNames::SWEGlobalRepeatStr = "SWEGlobalRepeat";
QString BFPNames::SWELocalRepeatStr = "SWELocalRepeat";
QString BFPNames::ClampLevelStr = "ClampLevel";
QString BFPNames::SWEResetStr = "SWEReset";
QString BFPNames::CWSaftVoltageFlagStr = "CWSaftVoltageFlag";
QString BFPNames::PWGroupStr = "PWGroup";
QString BFPNames::PWGroup_FrameScanStr = "PWGroup_FrameScan";
QString BFPNames::PW_Group_BTxNumsStr = "PW_Group_BTxNums";
QString BFPNames::PW_Group_CTxNumsStr = "PW_Group_CTxNums";
QString BFPNames::PW_Group_DTxNumsStr = "PW_Group_DTxNums";
QString BFPNames::PW_Group_GapWaitT_BStr = "PW_Group_GapWaitT_B";
QString BFPNames::PW_Group_BDumyTxsStr = "PW_Group_BDumyTxs";
QString BFPNames::PW_Group_BDumyTxsTHIStr = "PW_Group_BDumyTxsTHI";
QString BFPNames::PW_Group_CDumyTxsStr = "PW_Group_CDumyTxs";
QString BFPNames::PW_Group_BDummyLastPRTStr = "PW_Group_BDummyLastPRT";
QString BFPNames::PW_Group_CDummyLastPRTStr = "PW_Group_CDummyLastPRT";
QString BFPNames::PW_Group_Setuptime_AfterPWStr = "PW_Group_Setuptime_AfterPW";
QString BFPNames::PW_Group_GapWaitT_CStr = "PW_Group_GapWaitT_C";
QString BFPNames::PAIR_Coef0Str = "PAIR_Coef0";
QString BFPNames::PAIR_Coef1Str = "PAIR_Coef1";
QString BFPNames::PAIR_Coef2Str = "PAIR_Coef2";
QString BFPNames::PAIR_Coef3Str = "PAIR_Coef3";
QString BFPNames::SMIModeStr = "SMIMode";
QString BFPNames::StripRemoveEnStr = "StripRemoveEn";
QString BFPNames::TCpdSteerStr = "TCpdSteer";

QString BFPNames::SpacialCompoundStr = "SpacialCompound";

QStringList BFPNames::UniformityTgcStrs = QStringList()
<< "UniformityTgc00"
<< "UniformityTgc01"
<< "UniformityTgc02"
<< "UniformityTgc03"
<< "UniformityTgc04"
<< "UniformityTgc05"
<< "UniformityTgc06"
<< "UniformityTgc07"
<< "UniformityTgc08"
<< "UniformityTgc09"
<< "UniformityTgc10"
<< "UniformityTgc11"
<< "UniformityTgc12"
<< "UniformityTgc13"
<< "UniformityTgc14"
<< "UniformityTgc15";

QStringList BFPNames::AnalogTgcStrs = QStringList()
<< "AnalogTgc0"
<< "AnalogTgc1"
<< "AnalogTgc2"
<< "AnalogTgc3"
<< "AnalogTgc4"
<< "AnalogTgc5"
<< "AnalogTgc6"
<< "AnalogTgc7"
<< "AnalogTgc8"
<< "AnalogTgc9"
<< "AnalogTgc10"
<< "AnalogTgc11"
<< "AnalogTgc12"
<< "AnalogTgc13"
<< "AnalogTgc14"
<< "AnalogTgc15";

QStringList BFPNames::PhasedProbeTgcStrs = QStringList()
<< "PhasedProbeTgc0"
<< "PhasedProbeTgc1"
<< "PhasedProbeTgc2"
<< "PhasedProbeTgc3"
<< "PhasedProbeTgc4"
<< "PhasedProbeTgc5"
<< "PhasedProbeTgc6"
<< "PhasedProbeTgc7";

QStringList BFPNames::ElastoDigitalTgcStrs = QStringList()
<< "CFMDigitalTgc0"
<< "CFMDigitalTgc1"
<< "CFMDigitalTgc2"
<< "CFMDigitalTgc3"
<< "CFMDigitalTgc4"
<< "CFMDigitalTgc5"
<< "CFMDigitalTgc6"
<< "CFMDigitalTgc7"
<< "CFMDigitalTgc8"
<< "CFMDigitalTgc9"
<< "CFMDigitalTgc10"
<< "CFMDigitalTgc11"
<< "CFMDigitalTgc12"
<< "CFMDigitalTgc13"
<< "CFMDigitalTgc14"
<< "CFMDigitalTgc15";

QStringList BFPNames::ElastoAnologTgcStrs = QStringList()
<< "AnalogTgc0"
<< "AnalogTgc1"
<< "AnalogTgc2"
<< "AnalogTgc3"
<< "AnalogTgc4"
<< "AnalogTgc5"
<< "AnalogTgc6"
<< "AnalogTgc7"

<< "CFMAnologTgc0"
<< "CFMAnologTgc1"
<< "CFMAnologTgc2"
<< "CFMAnologTgc3"
<< "CFMAnologTgc4"
<< "CFMAnologTgc5"
<< "CFMAnologTgc6"
<< "CFMAnologTgc7";

QStringList BFPNames::ElastoUniformityTgcStrs = QStringList()
<< "ElastoEUniformityTgc0"
<< "ElastoEUniformityTgc1"
<< "ElastoEUniformityTgc2"
<< "ElastoEUniformityTgc3"
<< "ElastoEUniformityTgc4"
<< "ElastoEUniformityTgc5"
<< "ElastoEUniformityTgc6"
<< "ElastoEUniformityTgc7"
<< "ElastoEUniformityTgc8"
<< "ElastoEUniformityTgc9"
<< "ElastoEUniformityTgc10"
<< "ElastoEUniformityTgc11"
<< "ElastoEUniformityTgc12"
<< "ElastoEUniformityTgc13"
<< "ElastoEUniformityTgc14"
<< "ElastoEUniformityTgc15"

<< "UniformityTgc00"
<< "UniformityTgc01"
<< "UniformityTgc02"
<< "UniformityTgc03"
<< "UniformityTgc04"
<< "UniformityTgc05"
<< "UniformityTgc06"
<< "UniformityTgc07"
<< "UniformityTgc08"
<< "UniformityTgc09"
<< "UniformityTgc10"
<< "UniformityTgc11"
<< "UniformityTgc12"
<< "UniformityTgc13"
<< "UniformityTgc14"
<< "UniformityTgc15";

QStringList BFPNames::LGCStrs = QStringList()
<< BFPNames::LGC0Str
<< BFPNames::LGC1Str
<< BFPNames::LGC2Str
<< BFPNames::LGC3Str
<< BFPNames::LGC4Str
<< BFPNames::LGC5Str
<< BFPNames::LGC6Str
<< BFPNames::LGC7Str;

QStringList BFPNames::PostLGCStrs = QStringList()
<< BFPNames::PostLGC0Str
<< BFPNames::PostLGC1Str
<< BFPNames::PostLGC2Str
<< BFPNames::PostLGC3Str
<< BFPNames::PostLGC4Str
<< BFPNames::PostLGC5Str
<< BFPNames::PostLGC6Str
<< BFPNames::PostLGC7Str;

