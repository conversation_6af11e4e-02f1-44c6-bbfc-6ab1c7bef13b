#include "modelconfig.h"
#include "assertlog.h"
#include "resource.h"
#include <QDebug>

const char* ModelConfig::ParaNames[] = {"RealTHI",
                                        "CommentSearchNumber", // comment查询次数
                                        "HasFocusBlock",
                                        "SocketCount",
                                        "DynamicRangeStep",
                                        "DynamicRangeBase",
                                        "SocketMap",
                                        "ColorInvert",
                                        "IsRealUSBFps",
                                        "PWUpdateWaitTimeMS",
                                        "USBWriteTimeMs",
                                        "BColorMapIndex",
                                        "ImageUnstableTimeMs",
                                        "ImageShapeUnstableTimeMs",
                                        "DLineImageUnstableTimeMs",
                                        "IsFPGAVideo",
                                        "USBReadTimeOutMs",
                                        "StandbyEnabled",
                                        "IsSupportLid",
                                        "BatteryWarningLightEnabled",
                                        "ROILeftLineDelta",
                                        "CheckSataDevice",
                                        "CopyRightStartDate",
                                        "EnvelopeTraceStep",
                                        "CanWaveModeScroll",
                                        "WaveLenAddress",
                                        "IsMoreItemsPresetMode",
                                        "HPrfCW",
                                        "ProbeCodeOffset",
                                        "BMUpDownFlip",
                                        "ElastographyPixelSumAddress",
                                        "ElastographyNumSumAddress",
                                        "ElastographyPixelSumAddressMapped",
                                        "ElastographyNumSumAddressMapped",
                                        "FlashClearInterval",
                                        "FocusesCombine",
                                        "FocusesCombineDataBlock",
                                        "MeasurementClearStr",
                                        "ProbeSteadyTimeMs",
                                        "IsDelayRefreshModeGlyphs",
                                        "CanFreezeSend",
                                        "MenuMaxShownInStressEcho",
                                        "ButtomMenuCols",
                                        "MainWindowResolution",
                                        "RenderImageSize",
                                        "RenderWidgetSize",
                                        "DSCImageSize",
                                        "DSCImageZoomOn",
                                        "CurvedWidth",
                                        "LeftMenuHeight",
                                        "PixelLenStep",
                                        "IsLineiImage",
                                        "RedundantPoints",
                                        "NeedShowLoadingSlider",
                                        "LineiImageLevelIds",
                                        "PWSoundFps",
                                        "PWSoundPeriodTimeOut",
                                        "PWSoundPeriodPrepareTimeOut",
                                        "ProbeIntervalUseHighDensity",
                                        "PwBackup2DImage",
                                        "PWOffsetWidth",
                                        "ShowMMC",
                                        "FrameControl",
                                        "TimeInterval",
                                        "DepthCoef",
                                        "MinMAD",
                                        "MaxMAD",
                                        "MaxTimes",
                                        "AutoFreezeSwitch",
                                        "AutoFreezeTime",
                                        "AutoFreezeSensitiveThreshold",
                                        "AutoFreezeFrameThreshold",
                                        "AutoFreezeCheckRange1",
                                        "AutoFreezeCheckRange2",
                                        "LinePackageCount",
                                        "ProbeUsbBusPortIds",
                                        "SelectProbeOnProbeChanged",
                                        "MouseInteraction",
                                        "LeftMenuAlwaysShow",
                                        "MeasureTipBindWithGlyphs",
                                        "ProbeSelectedResetTGC",
                                        "ShowDefaultBottomCenterROIAGItem",
                                        "DisplayAutoTraceTAMAXLine",
                                        "DisplayAutoTraceTAMEANLine",
                                        "RollMenuWithMouse",
                                        "FingerPrintType",
                                        "Useudev4Battery",
                                        "SystemScanModeToolUpdateToolValue",
                                        "MeasureMouseActionXStep",
                                        "MeasureMouseActionYStep",
                                        "ROIRegionHeightFactor",
                                        "SupportFreezeOutage",
                                        "IsTraceBall",
                                        "DepthTipIconPos",
                                        "GainTipIconPos",
                                        "TgcTipIconPos",
                                        "SkTipIconPos",
                                        "SKTipIconMargin",
                                        "SKTipIconShowDelayTime",
                                        "LockScreenTipPos",
                                        "IsSupportVideoOut",
                                        "DRTypeForB",
                                        "ModelFilesUseIni",
                                        "UseProbeOrganismSelection",
                                        "SupportSonoEyeProbes",
                                        "IsCreateLeftTopWidget",
                                        "IsFixedDepth",
                                        "MaxProbeButtonCount",
                                        "BatteryConnectType",
                                        "SerialPortName",
                                        "MotorPortName",
                                        "BatteryChargeThreshold",
                                        "VID",
                                        "PID",
                                        "UltrasoundDeviceCanDisable",
                                        "UltrasoundDeviceTimeoutCount",
                                        "UltrasoundDeviceTimeoutMaxInterval",
                                        "UltrasoundKeyboardPhyId",
                                        "FanWarningThreshold",
                                        "FanCheckOn",
                                        "BatteryCommoundTime",
                                        "KBUpdateReboot",
                                        "IsSupportTouchPad",
                                        "OrganismZoomFactor",
                                        "OrganismHoverZoomFactor",
                                        "SupportCRCCheckSum",
                                        "SupportRedundantData",
                                        "IsSupportAnyDensity",
                                        "IsSupportFHISwitch",
                                        "Channel",
                                        "RxFreqMul",
                                        "TxFreqMul",
                                        "GainAdjustStep",
                                        "GainAdjustWidth",
                                        "SelectFrameSpeed",
                                        "SelectFrameMaxStep",
                                        "OutGainDelta",
                                        "SupportBiopsyProbes",
                                        "SuperNeedleEx",
                                        "TraceMaxPoints",
                                        "BPostEnable",
                                        "CPostEnable",
                                        "DscMethod",
                                        "TGCLimitMinValue",
                                        "TGCLimitMaxValue",
                                        "IsAjeCPLD",
                                        "TgcToolPostProcess",
                                        "IsGrayMapHorMove",
                                        "IsPWDynamicRangeAdjustmentEnable",
                                        "CWPush2ZeusPointsUnit",
                                        "IsSupportVS",
                                        "VSBlockCheck",
                                        "VSBlockLimitNum",
                                        "IsSupportVSCalculateBlockData",
                                        "RxStepClockMultiplicity",
                                        "TxStepClockMultiplicity",
                                        "FocusSamplingClockCount",
                                        "IsPCIEWithDoubleBuffer",
                                        "IsCallBackDataOfBfIODevice",
                                        "IsRDMAIODevice",
                                        "IsPCIECallBackDirectConnect",
                                        "SoundPlayingIntervals",
                                        "IsRotateButtonMachine",
                                        "BtnOperationMode",
                                        "HwUpateMode",
                                        "HwUpateModeUI",
                                        "PresetShortcutEnable",
                                        "LightBeltEnable",
                                        "NavigationHotKeyDisplayQuantity",
                                        "ADCDataAcquisitonPathCount",
                                        "SoftRunDir",
                                        "IntelligentInterconnectionIsEnabled",
                                        "KeyboardMode",
                                        "InputLettersByEmbbededKBD",
                                        "IsAllImageModeSupportECG"};

ModelConfig& ModelConfig::instance()
{
    static ModelConfig _instance;
    return _instance;
}

void ModelConfig::save()
{
    qDebug() << "File 'modelconfig.ini' cannot be modified";
    ASSERT_LOG(false);
    return;
}

ModelConfig::ModelConfig()
    : ModelHashSettings()
{
    // 2024-08-20 Modify by AlexWang 加载多个配置文件到同一份hash内存中
    QStringList fileNames = QStringList() << Resource::modelConfigFileName(Resource::appSubDir)
                                          << Resource::modelConfigFileName(Resource::presetSubDir);
    setFileNames(fileNames);
    loadFromFiles();
}

const char* ModelConfig::paraName(int key) const
{
    return ParaNames[key];
}
