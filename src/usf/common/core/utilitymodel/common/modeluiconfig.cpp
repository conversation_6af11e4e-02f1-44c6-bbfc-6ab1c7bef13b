#include "modeluiconfig.h"
#include "iniutil.h"
#include "resource.h"
#include "util.h"
#include <QColor>
#include <QFile>
#include <QStringList>

// all names
const char* ModelUiConfig::m_allParamNames[] = {"ContentMargin_top",
                                                "ContentMargin_left",
                                                "ContentMargin_bottom",
                                                "ContentMargin_right",
                                                "FullContentMargin_top",
                                                "FullContentMargin_left",
                                                "FullContentMargin_bottom",
                                                "FullContentMargin_right",
                                                "HasStatusBar",
                                                "StatusBarWidgetHeight",
                                                "ShowTopWidgetSeparatorLine",
                                                "ShowTopWidgetLine",
                                                "ShowProbeIconText",
                                                "ShowProbeFrameTitle",
                                                "FreezeBarType",
                                                "FreezeBarAtRightRightConatiner",
                                                "FreezeSliderWidth",
                                                "FreezeSliderMoreHeight",
                                                "FreezeSliderRadius",
                                                "FreezeSliderBackgroundRadius",
                                                "FreezeSliderBackground",
                                                "FreezeSliderIcon",
                                                "FreezeSliderEndIcon",
                                                "FreezeSliderSpaceH",
                                                "FreezeSliderSpaceV",
                                                "MeasureResultFrameHeight",
                                                "MWBottomLeftWidth",
                                                "MWBottomRightWidth",
                                                "MWTopRightHeight",
                                                "FocalPointSize",
                                                "MajorRulerLength",
                                                "MinorRulerLength",
                                                "StressEchoWorkWidget_top",
                                                "StressEchoWorkWidget_Left",
                                                "StressEchoTemplatePreviewWidgetLevelViewWidth",
                                                "StressEchoTemplatePreviewWidgetCellWidth",
                                                "StressEchoTemplatePreviewWidgetCellHeight",
                                                "HIPFontSize",
                                                "HIPArcRectSize",
                                                "CommentMenuItemHeight",
                                                "MeasureTreeItemHeight",
                                                "TableViewItemHeight",
                                                "SystemStatusModelColumn",
                                                "ProbeSelectionItemSpacing",
                                                "ZoomThumbnailInnerWidth",
                                                "WebViewCss",
                                                "FolderSmallIcon",
                                                "MovieIcon",
                                                "MovieActiveIcon",
                                                "EasyMovieIcon",
                                                "EasyMovieActiveIcon",
                                                "LogoFile",
                                                "DiskIcon",
                                                "BsIcon",
                                                "EnIcon",
                                                "CapsIcon",
                                                "UpIcon",
                                                "UpDisableIcon",
                                                "DownIcon",
                                                "DownDisableIcon",
                                                "LeftIcon",
                                                "RightIcon",
                                                "Dy0Icon",
                                                "Dy1Icon",
                                                "Dy2Icon",
                                                "Dy3Icon",
                                                "Dy4Icon",
                                                "DywGif",
                                                "Chongdian0Gif",
                                                "AdapterIcon",
                                                "AdapterdyIcon",
                                                "LjtIcon",
                                                "UsbIcon",
                                                "UsbOffIcon",
                                                "ExitIcon",
                                                "SnowPic",
                                                "WlonIcon",
                                                "WloffIcon",
                                                "W2Icon",
                                                "ManagerIcon",
                                                "ManagerGif",
                                                "ExpandIcon",
                                                "ExpandSendIcon",
                                                "ExpandDeleteIcon",
                                                "CinePauseIcon",
                                                "CinePlayIcon",
                                                "CineChooseIcon",
                                                "CineStopIcon",
                                                "SaveScreenIcon",
                                                "SaveMovieIcon",
                                                "OrderLevelIcon",
                                                "OrderProjectionIcon",
                                                "OrderCustomIcon",
                                                "SingleWidgetIcon",
                                                "MultiWidgetsIcon",
                                                "SelectPreviousIcon",
                                                "SelectNextIcon",
                                                "BluetoothIcon",
                                                "FourDLayoutFourIcon",
                                                "FourDLayoutTwoIcon",
                                                "FourDLayoutSignalIcon",
                                                "FourDDirectionUpIcon",
                                                "FourDDirectionDownIcon",
                                                "FourDDirectionLeftIcon",
                                                "FourDDirectionFrontIcon",
                                                "FourDDirectionBackIcon",
                                                "FourDDirectionRightIcon",
                                                "FourDXIcon",
                                                "FourDXIcon1",
                                                "FourDYIcon",
                                                "FourDYIcon1",
                                                "FourDZIcon",
                                                "FourDZIcon1",
                                                "HD3DXIcon",
                                                "HD3DXIcon1",
                                                "HD3DYIcon",
                                                "HD3DYIcon1",
                                                "HD3DZIcon",
                                                "HD3DZIcon1",
                                                "FourDROIXIcon",
                                                "FourDROIXIcon1",
                                                "FourDROIYIcon",
                                                "FourDROIYIcon1",
                                                "FourDROIZIcon",
                                                "FourDROIZIcon1",
                                                "FourDMenuIcon",
                                                "FourDMenuIcon1",
                                                "MeasureLineType",
                                                "TitleRenderColor",
                                                "TitleFreezeColor",
                                                "MeasureColor",
                                                "MeasureResultListEndLineOmitEnabled",
                                                "ActiveMeasureColor",
                                                "RoiColor",
                                                "RoiActiveColor",
                                                "RoiFreezeColor",
                                                "MLineColor",
                                                "MLineFreezeColor",
                                                "BiopsyColor",
                                                "BiopsyFreezeColor",
                                                "DopplerSegmentLineColor",
                                                "DopplerSegmentLineFreezeColor",
                                                "DopplerDivideLineColor",
                                                "DopplerDivideLineFreezeColor",
                                                "DopplerCrossLineColor",
                                                "DopplerCrossLineFreezeColor",
                                                "ZoomSelectColor",
                                                "ZoomSelectActiveColor",
                                                "ZoomThumbnailBoundColor",
                                                "ZoomThumbnailInnerColor",
                                                "ZoomThumbnailInnerActiveColor",
                                                "ZoomThumbnailOuterColor",
                                                "TgcColor",
                                                "IMTIntimaColor",
                                                "IMTAdventitiaColor",
                                                "FreezeBorderColor",
                                                "FreezeSliderColor",
                                                "FreezeActiveBarColor",
                                                "FreezeInactiveBarColor",
                                                "ImageLabelBackgroundColor",
                                                "ImageLabelHoverColor",
                                                "ImageLabelInactiveColor",
                                                "ImageLabelTextColor",
                                                "ImageLabelActiveColor",
                                                "ImageLabelBorderColor",
                                                "ImageCineActiveColor",
                                                "ImageLabelMultiSelectedColor",
                                                "IsSupportCalendarHoverStyle",
                                                "ClickLabelActiveColor",
                                                "RoiScanLineColor",
                                                "RoiScanLineFreezeColor",
                                                "ReportTextColor",
                                                "ReportBKColor",
                                                "ReportBottomColor",
                                                "ReportEditColor",
                                                "ReportEditBorderColor",
                                                "CurveArrowColor",
                                                "SliderToolButtonBorderColor",
                                                "CurvedPanoColor",
                                                "BodyMarkSelectedColor",
                                                "FourDCubicColor",
                                                "FourDKnotColor",
                                                "FocalPointColor",
                                                "FreeMLine1",
                                                "FreeMLine2",
                                                "FreeMLine3",
                                                "CurFreeMLine",
                                                "PenStyle",
                                                "PreviewWindowHeight",
                                                "TopWindowLeftSpacerWidth",
                                                "TopWindowRightSpacerWidth",
                                                "IsPinyinWidgetLongstanding",
                                                "IsFullScreenDlgTransposed",
                                                "IsSupportRotation",
                                                "ImageClipWidgetSupportSlide",
                                                "ShowArrowControlGlyphs",
                                                "SupportTouchScreen",
                                                "PreMonthIcon",
                                                "NextMonthIcon",
                                                "BaseComboBoxBkColor",
                                                "LogoFile_zh",
                                                "MeasurementMenuMaxHeight",
                                                "MeasurementMenuShowReport",
                                                "MeasurementItemMenuBtnSpacing",
                                                "ReportCaptionWidth",
                                                "DialogDisplayOnMainScreen",
                                                "ArchiveManagerThumbnailRowAndColumn",
                                                "FourDRightWidgetThumbnailRowAndColumn",
                                                "ReportEditWidgetThumbnailRowAndColumn",
                                                "ReportDisplayFullWidgetThumbnailRowAndColumn",
                                                "RotatableBodyMarkWidgetThumbnailRowAndColumn",
                                                "ShowROIAdjustItem",
                                                "HasMenuFrame",
                                                "MenuDisplayAtBottom",
                                                "GraphicsFont",
                                                "BrightnessIcon",
                                                "ArrowColor",
                                                "ActiveArrowColor",
                                                "ArrowCursorColor",
                                                "FullScreenTitleBarShowLogo",
                                                "OrganismDefaultIcon",
                                                "OnlyHaveTouchScreen",
                                                "SonoZoomAligment",
                                                "StressEchoMultiPlay",
                                                "StressEchoMultiPlayOn",
                                                "StressEchoOrderCustom",
                                                "StressEchoOrderCustomOn",
                                                "StressEchoOrderVertical",
                                                "StressEchoOrderVerticalOn",
                                                "StressEchoPause",
                                                "StressEchoPauseOn",
                                                "StressEchoSaveScreen",
                                                "StressEchoSaveScreenOn",
                                                "StressEchoSingalPlay",
                                                "StressEchoSingalPlayOn",
                                                "StressEchoOrderHorizontal",
                                                "StressEchoOrderHorizontalOn",
                                                "StressEchoStop",
                                                "StressEchoStopOn",
                                                "StressEchoPlay",
                                                "StressEchoPlayOn",
                                                "StressEchoNext",
                                                "StressEchoNextOn",
                                                "StressEchoNextDis",
                                                "StressEchoPre",
                                                "StressEchoPreOn",
                                                "StressEchoPreDis",
                                                "AutoEFEDFreezeSliderIcon",
                                                "AutoEFESFreezeSliderIcon",
                                                "AuxiliaryBtnWidgetColCnt"};

ModelUiConfig::ModelUiConfig()
{
    m_DefaultCommonSettings[ContentMargin_top] = 5;
    m_DefaultCommonSettings[ContentMargin_left] = 5;
    m_DefaultCommonSettings[ContentMargin_bottom] = 5;
    m_DefaultCommonSettings[ContentMargin_right] = 5;
    m_DefaultCommonSettings[FullContentMargin_top] = 5;
    m_DefaultCommonSettings[FullContentMargin_left] = 5;
    m_DefaultCommonSettings[FullContentMargin_bottom] = 5;
    m_DefaultCommonSettings[FullContentMargin_right] = 5;
    m_DefaultCommonSettings[HasStatusBar] = false;
    m_DefaultCommonSettings[StatusBarWidgetHeight] = 60;
    m_DefaultCommonSettings[ShowTopWidgetSeparatorLine] = false;
    m_DefaultCommonSettings[ShowTopWidgetLine] = true;
    m_DefaultCommonSettings[FullScreenTitleBarShowLogo] = true;
    m_DefaultCommonSettings[ShowProbeIconText] = false;
    m_DefaultCommonSettings[ShowProbeFrameTitle] = true;
    m_DefaultCommonSettings[FreezeBarType] = 0;
    m_DefaultCommonSettings[FreezeBarAtRightRightConatiner] = false;
    m_DefaultCommonSettings[FreezeSliderWidth] = 4;
    m_DefaultCommonSettings[FreezeSliderMoreHeight] = 0;
    m_DefaultCommonSettings[FreezeSliderRadius] = 0;
    m_DefaultCommonSettings[FreezeSliderBackgroundRadius] = 5;
    m_DefaultCommonSettings[FreezeSliderBackground] = ":/images/3D/res/images/3D/freezebar_background.png";
    m_DefaultCommonSettings[FreezeSliderIcon] = "";
    m_DefaultCommonSettings[AutoEFEDFreezeSliderIcon] = "";
    m_DefaultCommonSettings[AutoEFESFreezeSliderIcon] = "";
    m_DefaultCommonSettings[FreezeSliderEndIcon] = "";
    m_DefaultCommonSettings[FreezeSliderSpaceH] = -1;
    m_DefaultCommonSettings[FreezeSliderSpaceV] = -1;
    m_DefaultCommonSettings[MeasureResultFrameHeight] = 20;
    m_DefaultCommonSettings[MWBottomLeftWidth] = 163;
    m_DefaultCommonSettings[MWBottomRightWidth] = 163;
    m_DefaultCommonSettings[MWTopRightHeight] = 500;
    m_DefaultCommonSettings[FocalPointSize] = 11;
    m_DefaultCommonSettings[MajorRulerLength] = 22;
    m_DefaultCommonSettings[MinorRulerLength] = 9;
    m_DefaultCommonSettings[StressEchoWorkWidget_top] = 0;
    m_DefaultCommonSettings[StressEchoWorkWidget_left] = 4;
    m_DefaultCommonSettings[StressEchoTemplatePreviewWidgetLevelViewWidth] = 80;
    m_DefaultCommonSettings[StressEchoTemplatePreviewWidgetCellWidth] = 30;
    m_DefaultCommonSettings[StressEchoTemplatePreviewWidgetCellHeight] = 30;
    m_DefaultCommonSettings[HIPFontSize] = 16;
    m_DefaultCommonSettings[HIPArcRectSize] = 18;
    m_DefaultCommonSettings[CommentMenuItemHeight] = 28;
    m_DefaultCommonSettings[MeasureTreeItemHeight] = 26;
    m_DefaultCommonSettings[TableViewItemHeight] = -1;
    m_DefaultCommonSettings[SystemStatusModelColumn] = 8;
    m_DefaultCommonSettings[ProbeSelectionItemSpacing] = 152;
    m_DefaultCommonSettings[ZoomThumbnailInnerWidth] = 10;
    m_DefaultCommonSettings[PreviewWindowHeight] = 265;
    m_DefaultCommonSettings[TopWindowLeftSpacerWidth] = 290;
    m_DefaultCommonSettings[TopWindowRightSpacerWidth] = 303;
    m_DefaultCommonSettings[IsPinyinWidgetLongstanding] = false;
    m_DefaultCommonSettings[IsSupportRotation] = false;
    m_DefaultCommonSettings[IsFullScreenDlgTransposed] = false;
    m_DefaultCommonSettings[IsSupportCalendarHoverStyle] = false;
    m_DefaultCommonSettings[ImageClipWidgetSupportSlide] = true;
    m_DefaultCommonSettings[ShowArrowControlGlyphs] = true;
    m_DefaultCommonSettings[SupportTouchScreen] = true;
    m_DefaultCommonSettings[PreMonthIcon] = ":/images/m600_left_icon_on.png";
    m_DefaultCommonSettings[NextMonthIcon] = ":/images/m600_right_icon_on.png";
    m_DefaultCommonSettings[WebViewCss] = "./res/qss/webview_eco.css";
    m_DefaultCommonSettings[FolderSmallIcon] = ":/images/folder_small.png";
    m_DefaultCommonSettings[MovieIcon] = ":/images/movie.png";
    m_DefaultCommonSettings[MovieActiveIcon] = ":/images/movie_active.png";
    m_DefaultCommonSettings[EasyMovieIcon] = ":/images/easymovie.png";
    m_DefaultCommonSettings[EasyMovieActiveIcon] = ":/images/easymovie_active.png";
    m_DefaultCommonSettings[LogoFile] = "./res/icon/logo.png";
    m_DefaultCommonSettings[DiskIcon] = ":/images/disk.png";
    m_DefaultCommonSettings[BsIcon] = ":/images/bs.png";
    m_DefaultCommonSettings[EnIcon] = ":/images/en.png";
    m_DefaultCommonSettings[CapsIcon] = ":/images/capslock.png";
    m_DefaultCommonSettings[UpIcon] = ":/images/up.png";
    m_DefaultCommonSettings[UpDisableIcon] = ":/images/up_disable.png";
    m_DefaultCommonSettings[DownIcon] = ":/images/down.png";
    m_DefaultCommonSettings[DownDisableIcon] = ":/images/down_disable.png";
    m_DefaultCommonSettings[LeftIcon] = ":/images/left_scroll.png";
    m_DefaultCommonSettings[RightIcon] = ":/images/right_scroll.png";
    m_DefaultCommonSettings[Dy0Icon] = ":/images/dy0.png";
    m_DefaultCommonSettings[Dy1Icon] = ":/images/dy1.png";
    m_DefaultCommonSettings[Dy2Icon] = ":/images/dy2.png";
    m_DefaultCommonSettings[Dy3Icon] = ":/images/dy3.png";
    m_DefaultCommonSettings[Dy4Icon] = ":/images/dy4.png";
    m_DefaultCommonSettings[DywGif] = ":/images/dyw.gif";
    m_DefaultCommonSettings[Chongdian0Gif] = ":/images/chongdian0.gif";
    m_DefaultCommonSettings[AdapterIcon] = ":/images/adapter.png";
    m_DefaultCommonSettings[AdapterdyIcon] = ":/images/res/images/adapterdy.png";
    m_DefaultCommonSettings[LjtIcon] = ":/images/ljt.png";
    m_DefaultCommonSettings[UsbIcon] = ":/images/usb.png";
    m_DefaultCommonSettings[UsbOffIcon] = ":/images/usboff.png";
    m_DefaultCommonSettings[ExitIcon] = ":/images/exit.png";
    m_DefaultCommonSettings[SnowPic] = ":/images/snow.png";
    m_DefaultCommonSettings[WlonIcon] = ":/images/wlon.png";
    m_DefaultCommonSettings[WloffIcon] = ":/images/wloff.png";
    m_DefaultCommonSettings[W2Icon] = ":/images/w2.png";
    m_DefaultCommonSettings[ManagerIcon] = ":/images/manager.png";
    m_DefaultCommonSettings[ManagerGif] = ":/images/res/images/manager.gif";
    m_DefaultCommonSettings[ExpandIcon] = ":/images/expand_normal.png";
    m_DefaultCommonSettings[ExpandSendIcon] = ":/images/send_normal.png";
    m_DefaultCommonSettings[ExpandDeleteIcon] = ":/images/delete_normal.png";
    m_DefaultCommonSettings[CinePauseIcon] = ":/images/cine_pause.png";
    m_DefaultCommonSettings[CinePlayIcon] = ":/images/cine_play.png";
    m_DefaultCommonSettings[CineChooseIcon] = ":/images/res/images/icon_on.png";
    m_DefaultCommonSettings[CineStopIcon] = ":/images/cine_stop.png";
    m_DefaultCommonSettings[SaveScreenIcon] = ":/images/save_screen.png";
    m_DefaultCommonSettings[SaveMovieIcon] = ":/images/saving.gif";
    m_DefaultCommonSettings[OrderLevelIcon] = ":/images/order_horizontal.png";
    m_DefaultCommonSettings[OrderProjectionIcon] = ":/images/order_vertical.png";
    m_DefaultCommonSettings[OrderCustomIcon] = ":/images/order_custom.png";
    m_DefaultCommonSettings[SingleWidgetIcon] = ":/images/singleWidget.png";
    m_DefaultCommonSettings[MultiWidgetsIcon] = ":/images/multiWidgets.png";
    m_DefaultCommonSettings[SelectPreviousIcon] = ":/images/left_scroll_ebit.png";
    m_DefaultCommonSettings[SelectNextIcon] = ":/images/right_scroll_ebit.png";
    m_DefaultCommonSettings[FourDLayoutFourIcon] = ":/images/RRRVR.png";
    m_DefaultCommonSettings[FourDLayoutTwoIcon] = ":/images/AVR.png";
    m_DefaultCommonSettings[FourDLayoutSignalIcon] = ":/images/VR.png";
    m_DefaultCommonSettings[FourDDirectionUpIcon] = ":/images/dir_up.png";
    m_DefaultCommonSettings[FourDDirectionDownIcon] = ":/images/dir_down.png";
    m_DefaultCommonSettings[FourDDirectionLeftIcon] = ":/images/dir_left.png";
    m_DefaultCommonSettings[FourDDirectionFrontIcon] = ":/images/dir_front.png";
    m_DefaultCommonSettings[FourDDirectionBackIcon] = ":/images/dir_back.png";
    m_DefaultCommonSettings[FourDDirectionRightIcon] = ":/images/dir_right.png";
    m_DefaultCommonSettings[FourDXIcon] = ":/images/X2.png";
    m_DefaultCommonSettings[FourDXIcon1] = ":/images/X1.png";
    m_DefaultCommonSettings[FourDYIcon] = ":/images/Y2.png";
    m_DefaultCommonSettings[FourDYIcon1] = ":/images/Y1.png";
    m_DefaultCommonSettings[FourDZIcon] = ":/images/Z2.png";
    m_DefaultCommonSettings[FourDZIcon1] = ":/images/Z1.png";
    m_DefaultCommonSettings[HD3DXIcon] = ":/images/HD3DX2.png";
    m_DefaultCommonSettings[HD3DXIcon1] = ":/images/HD3DX1.png";
    m_DefaultCommonSettings[HD3DYIcon] = ":/images/HD3DY2.png";
    m_DefaultCommonSettings[HD3DYIcon1] = ":/images/HD3DY1.png";
    m_DefaultCommonSettings[HD3DZIcon] = ":/images/HD3DZ2.png";
    m_DefaultCommonSettings[HD3DZIcon1] = ":/images/HD3DZ1.png";
    m_DefaultCommonSettings[FourDROIXIcon] = ":/images/ROIX2.png";
    m_DefaultCommonSettings[FourDROIXIcon1] = ":/images/ROIX1.png";
    m_DefaultCommonSettings[FourDROIYIcon] = ":/images/ROIY2.png";
    m_DefaultCommonSettings[FourDROIYIcon1] = ":/images/ROIY1.png";
    m_DefaultCommonSettings[FourDROIZIcon] = ":/images/ROIZ2.png";
    m_DefaultCommonSettings[FourDROIZIcon1] = ":/images/ROIZ1.png";
    m_DefaultCommonSettings[FourDMenuIcon] = ":/images/MENU2.png";
    m_DefaultCommonSettings[FourDMenuIcon1] = ":/images/MENU1.png";
    m_DefaultCommonSettings[BrightnessIcon] = ":/images/res/images/brightness.png";
    m_DefaultCommonSettings[OrganismDefaultIcon] = "cardiacdifficult";
    m_DefaultCommonSettings[OnlyHaveTouchScreen] = false;
    m_DefaultCommonSettings[SonoZoomAligment] = true;
    m_DefaultCommonSettings[MeasureLineType] = 0;
    m_DefaultCommonSettings[LogoFile_zh] = "./res/icon/logo.png";
    m_DefaultCommonSettings[MeasurementMenuMaxHeight] = 820;
    m_DefaultCommonSettings[MeasurementMenuShowReport] = true;
    m_DefaultCommonSettings[MeasurementItemMenuBtnSpacing] = 10;
    m_DefaultCommonSettings[ReportCaptionWidth] = 300;
    m_DefaultCommonSettings[DialogDisplayOnMainScreen] = true;
    m_DefaultCommonSettings[ArchiveManagerThumbnailRowAndColumn] = "1,7";
    m_DefaultCommonSettings[FourDRightWidgetThumbnailRowAndColumn] = "6,2";
    m_DefaultCommonSettings[ReportEditWidgetThumbnailRowAndColumn] = "6,1";
    m_DefaultCommonSettings[ReportDisplayFullWidgetThumbnailRowAndColumn] = "6,1";
    m_DefaultCommonSettings[RotatableBodyMarkWidgetThumbnailRowAndColumn] = "6,1";
    m_DefaultCommonSettings[ShowROIAdjustItem] = true;
    m_DefaultCommonSettings[HasMenuFrame] = true;
    m_DefaultCommonSettings[MenuDisplayAtBottom] = false;
    m_DefaultCommonSettings[GraphicsFont] = "Source Han Sans CN Normal";
    m_DefaultCommonSettings[BluetoothIcon] = ":/res/images/bluetooth/bluetooth_icon.png";
    m_DefaultCommonSettings[StressEchoMultiPlayIcon] = ":/stressecho/stressechomultiplay.png";
    m_DefaultCommonSettings[StressEchoMultiPlayOnIcon] = ":/stressecho/stressechomultiplayon.png";
    m_DefaultCommonSettings[StressEchoOrderCustomIcon] = ":/stressecho/stressechoordercustom.png";
    m_DefaultCommonSettings[StressEchoOrderCustomOnIcon] = ":/stressecho/stressechoordercustomon.png";
    m_DefaultCommonSettings[StressEchoOrderVerticalIcon] = ":/stressecho/stressechoordervertical.png";
    m_DefaultCommonSettings[StressEchoOrderVerticalOnIcon] = ":/stressecho/stressechoorderverticalon.png";
    m_DefaultCommonSettings[StressEchoPauseIcon] = ":/stressecho/stressechopause.png";
    m_DefaultCommonSettings[StressEchoPauseOnIcon] = ":/stressecho/stressechoplayon.png";
    m_DefaultCommonSettings[StressEchoSaveScreenIcon] = ":/stressecho/stressechosavescreen.png";
    m_DefaultCommonSettings[StressEchoSaveScreenOnIcon] = ":/stressecho/stressechosavescreenon.png";
    m_DefaultCommonSettings[StressEchoSingalPlayIcon] = ":/stressecho/stressechosingalplay.png";
    m_DefaultCommonSettings[StressEchoSingalPlayOnIcon] = ":/stressecho/stressechosingalplayon.png";
    m_DefaultCommonSettings[StressEchoOrderHorizontalIcon] = ":/stressecho/stressechoorderhorizontal.png";
    m_DefaultCommonSettings[StressEchoOrderHorizontalOnIcon] = ":/stressecho/stressechoorderhorizontalon.png";
    m_DefaultCommonSettings[StressEchoStopIcon] = ":/stressecho/stressechostop.png";
    m_DefaultCommonSettings[StressEchoStopOnIcon] = ":/stressecho/stressechostopon.png";
    m_DefaultCommonSettings[StressEchoPlayIcon] = ":/stressecho/stressechoplay.png";
    m_DefaultCommonSettings[StressEchoPlayOnIcon] = ":/stressecho/stressechoplayon.png";
    m_DefaultCommonSettings[StressEchoNextIcon] = ":/stressecho/next.png";
    m_DefaultCommonSettings[StressEchoNextOnIcon] = ":/stressecho/next_on.png";
    m_DefaultCommonSettings[StressEchoNextDisIcon] = ":/stressecho/next_dis.png";
    m_DefaultCommonSettings[StressEchoPreIcon] = ":/stressecho/pre.png";
    m_DefaultCommonSettings[StressEchoPreOnIcon] = ":/stressecho/pre_on.png";
    m_DefaultCommonSettings[StressEchoPreDisIcon] = ":/stressecho/pre_dis.png";
    m_DefaultCommonSettings[MeasureResultListEndLineOmitEnabled] = false;
    m_DefaultColorSettings[TitleRenderColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[TitleFreezeColor] = QColor(Qt::white);
    m_DefaultColorSettings[MeasureColor] = QColor(Qt::white);
    m_DefaultColorSettings[ActiveMeasureColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[RoiColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[RoiActiveColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[RoiFreezeColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[MLineColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[MLineFreezeColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[BiopsyColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[BiopsyFreezeColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[DopplerSegmentLineColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[DopplerSegmentLineFreezeColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[DopplerDivideLineColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[DopplerDivideLineFreezeColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[DopplerCrossLineColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[DopplerCrossLineFreezeColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[ZoomSelectColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[ZoomSelectActiveColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[ZoomThumbnailBoundColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[ZoomThumbnailInnerColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[ZoomThumbnailInnerActiveColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[ZoomThumbnailOuterColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[TgcColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[IMTIntimaColor] = QColor(135, 206, 250);
    m_DefaultColorSettings[IMTAdventitiaColor] = QColor(255, 200, 0);
    m_DefaultColorSettings[FreezeBorderColor] = QColor(106, 106, 106);
    m_DefaultColorSettings[FreezeSliderColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[FreezeActiveBarColor] = QColor(106, 106, 106);
    m_DefaultColorSettings[FreezeInactiveBarColor] = QColor(0, 0, 0);
    m_DefaultColorSettings[ImageLabelBackgroundColor] = QColor(0, 0, 0);
    m_DefaultColorSettings[ImageLabelHoverColor] = QColor(96, 161, 217);
    m_DefaultColorSettings[ImageLabelInactiveColor] = QColor(106, 106, 106);
    m_DefaultColorSettings[ImageLabelTextColor] = QColor(Qt::white);
    m_DefaultColorSettings[ImageLabelActiveColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[ImageLabelBorderColor] = QColor(157, 156, 161);
    m_DefaultColorSettings[ImageCineActiveColor] = QColor(96, 161, 217);
    m_DefaultColorSettings[ImageLabelMultiSelectedColor] = QColor(Qt::green);
    m_DefaultColorSettings[ClickLabelActiveColor] = QColor(44, 67, 108);
    m_DefaultColorSettings[RoiScanLineColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[RoiScanLineFreezeColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[ReportTextColor] = QColor(Qt::white);
    m_DefaultColorSettings[ReportBkColor] = QColor(59, 59, 59);
    m_DefaultColorSettings[ReportBottomColor] = QColor(255, 183, 44);
    m_DefaultColorSettings[ReportEditColor] = QColor(59, 59, 59);
    m_DefaultColorSettings[BodyMarkSelectedColor] = QColor(22, 49, 52);
    m_DefaultColorSettings[ReportEditBorderColor] = QColor(Qt::white);
    m_DefaultColorSettings[CurveArrowColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[SliderToolButtonBorderColor] = QColor(Qt::gray);
    m_DefaultColorSettings[FreeMLine1] = QColor(255, 245, 240);
    m_DefaultColorSettings[FreeMLine2] = QColor(0, 238, 0);
    m_DefaultColorSettings[FreeMLine3] = QColor(58, 95, 205);
    m_DefaultColorSettings[CurFreeMLine] = QColor(255, 185, 15);
    m_DefaultColorSettings[CurvedPanoColor] = QColor(Qt::yellow);
    m_DefaultColorSettings[FourDCubicColor] = QColor(73, 136, 31);
    m_DefaultColorSettings[FourDKnotColor] = QColor(Qt::green);
    m_DefaultColorSettings[BaseComboBoxBkColor] = QColor(38, 49, 67);
    m_DefaultColorSettings[FocalPointColor] = QColor(61, 118, 183);
    m_DefaultColorSettings[ArrowColor] = QColor(22, 183, 230);
    m_DefaultColorSettings[ActiveArrowColor] = QColor(188, 143, 75);
    m_DefaultColorSettings[ArrowCursorColor] = QColor(22, 183, 230);
    m_DefaultCommonSettings[PenStyle] = 0; // 0:solid,1:dot
    m_DefaultCommonSettings[AuxiliaryBtnWidgetColCnt] = 4;
    m_commonSettings.setFileName(Resource::modelUiConfigFileName());
    qDebug() << PRETTY_FUNCTION << Resource::modelUiConfigFileName();
    m_commonSettings.setGroupName("CommonData");

    m_colorSettings.setFileName(Resource::modelUiConfigFileName());
    m_colorSettings.setGroupName("Color");

    m_commonSettings.load();
    m_colorSettings.load();

    //    m_TopWidgetSize = QSize(1920, 100);
}

ModelUiConfig& ModelUiConfig::instance()
{
    static ModelUiConfig m_modelUiConfig;
    return m_modelUiConfig;
}

void ModelUiConfig::setValue(ModelUiConfig::Parameter param, const QVariant& value)
{
    QString name = m_allParamNames[param];

    if (m_DefaultCommonSettings.contains(param))
    {
        // process common data
        m_commonSettings[name] = value;
    }
    else if (m_DefaultColorSettings.contains(param))
    {
        // process color data
        QColor color(value.toUInt());
        m_colorSettings[name] = IniUtil::toRgbStr(color);
    }
}

QVariant ModelUiConfig::value(ModelUiConfig::Parameter param)
{
    QString name = m_allParamNames[param];
    QVariant retVal = QVariant(QVariant::Invalid);

    if (m_DefaultCommonSettings.contains(param))
    {
        // process common data
        retVal = m_commonSettings[name];
        if (!retVal.isValid())
        {
            return m_DefaultCommonSettings[param];
        }
    }
    else if (m_DefaultColorSettings.contains(param))
    {
        // process color data
        QVariant v = m_colorSettings[name];
        if (!v.isValid())
        {
            retVal = m_DefaultColorSettings[param].rgb();
        }
        else
        {
            QColor color;
            IniUtil::toQColor(color, v.toStringList());
            retVal = color.rgb();
        }
    }

    return retVal;
}

bool ModelUiConfig::readRowAndColumn(Parameter param, int& row, int& column)
{
    QString rowAndColumnStr = value(param).toString();
    if (rowAndColumnStr.count(",") == 1)
    {
        QStringList rowAndColumn = rowAndColumnStr.split(",");
        row = rowAndColumn.at(0).trimmed().toInt();
        column = rowAndColumn.at(1).trimmed().toInt();
        return true;
    }
    row = 0;
    column = 0;
    return false;
}
