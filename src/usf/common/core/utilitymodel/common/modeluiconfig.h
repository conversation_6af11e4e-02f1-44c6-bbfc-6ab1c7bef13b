#ifndef MODELUICONFIG_H
#define MODELUICONFIG_H

#include "hashsettings.h"
#include "usfcommoncoreutilitymodel_global.h"

class USF_COMMON_CORE_UTILITYMODEL_EXPORT ModelUiConfig
{
public:
    enum Parameter
    {
        ContentMargin_top,
        ContentMargin_left,
        ContentMargin_bottom,
        ContentMargin_right,
        FullContentMargin_top,
        FullContentMargin_left,
        FullContentMargin_bottom,
        FullContentMargin_right,
        HasStatusBar,
        StatusBarWidgetHeight,

        ShowTopWidgetSeparatorLine,
        ShowTopWidgetLine,
        ShowProbeIconText,
        ShowProbeFrameTitle,

        FreezeBarType,
        FreezeBarAtRightRightConatiner,
        FreezeSliderWidth,
        FreezeSliderMoreHeight,
        FreezeSliderRadius,
        FreezeSliderBackgroundRadius,
        FreezeSliderBackground,
        FreezeSliderIcon,
        FreezeSliderEndIcon,
        FreezeSliderSpaceH,
        FreezeSliderSpaceV,
        MeasureResultFrameHeight,

        MWBottomL<PERSON>tWidth,
        MWBottomRightWidth,
        MWTopRightHeight,

        FocalPointSize,   //焦点大小
        MajorRulerLength, //主标尺长度
        MinorRulerLength, //副标尺长度

        StressEchoWorkWidget_top,
        StressEchoWorkWidget_left,
        StressEchoTemplatePreviewWidgetLevelViewWidth,
        StressEchoTemplatePreviewWidgetCellWidth,
        StressEchoTemplatePreviewWidgetCellHeight,
        HIPFontSize,
        HIPArcRectSize,

        CommentMenuItemHeight, // 注释菜单项高度
        MeasureTreeItemHeight, // 测量菜单项高度
        TableViewItemHeight,   // QTableWidget

        SystemStatusModelColumn, //系统状态栏图标排列列数,sonobook为6,其他机型都为3
        ProbeSelectionItemSpacing,

        ZoomThumbnailInnerWidth,

        WebViewCss,
        FolderSmallIcon,
        MovieIcon,
        MovieActiveIcon,
        EasyMovieIcon,
        EasyMovieActiveIcon,
        LogoFile,
        DiskIcon,
        BsIcon,
        EnIcon,
        CapsIcon,
        UpIcon,
        UpDisableIcon,
        DownIcon,
        DownDisableIcon,
        LeftIcon,
        RightIcon,
        Dy0Icon,
        Dy1Icon,
        Dy2Icon,
        Dy3Icon,
        Dy4Icon,
        DywGif,
        Chongdian0Gif,
        AdapterIcon,
        AdapterdyIcon,
        LjtIcon,
        UsbIcon,
        UsbOffIcon,
        ExitIcon,
        SnowPic,
        WlonIcon,
        WloffIcon,
        W2Icon,
        ManagerIcon,
        ManagerGif,
        ExpandIcon,
        ExpandSendIcon,
        ExpandDeleteIcon,
        CinePauseIcon,
        CinePlayIcon,
        CineChooseIcon,
        CineStopIcon,
        SaveScreenIcon,
        SaveMovieIcon,
        OrderLevelIcon,
        OrderProjectionIcon,
        OrderCustomIcon,
        SingleWidgetIcon,
        MultiWidgetsIcon,
        SelectPreviousIcon,
        SelectNextIcon,
        BluetoothIcon,
        FourDLayoutFourIcon,
        FourDLayoutTwoIcon,
        FourDLayoutSignalIcon,
        FourDDirectionUpIcon,
        FourDDirectionDownIcon,
        FourDDirectionLeftIcon,
        FourDDirectionFrontIcon,
        FourDDirectionBackIcon,
        FourDDirectionRightIcon,
        FourDXIcon,
        FourDXIcon1,
        FourDYIcon,
        FourDYIcon1,
        FourDZIcon,
        FourDZIcon1,
        HD3DXIcon,
        HD3DXIcon1,
        HD3DYIcon,
        HD3DYIcon1,
        HD3DZIcon,
        HD3DZIcon1,
        FourDROIXIcon,
        FourDROIXIcon1,
        FourDROIYIcon,
        FourDROIYIcon1,
        FourDROIZIcon,
        FourDROIZIcon1,
        FourDMenuIcon,
        FourDMenuIcon1,
        MeasureLineType,                     //测量线类型
        TitleRenderColor,                    //标尺
        TitleFreezeColor,                    //画面冻结
        MeasureColor,                        //测量结束
        MeasureResultListEndLineOmitEnabled, //实时测量结果列表最后一项的分割线是否隐藏
        ActiveMeasureColor,                  //测量中
        RoiColor,                            // Roi默认颜色
        RoiActiveColor,                      // Roi选中
        RoiFreezeColor,                      // Roi冻结
        MLineColor,                          // M线颜色
        MLineFreezeColor,                    // M线冻结颜色
        BiopsyColor,                         //穿刺线颜色
        BiopsyFreezeColor,                   //穿刺线冻结颜色
        DopplerSegmentLineColor,
        DopplerSegmentLineFreezeColor,
        DopplerDivideLineColor,
        DopplerDivideLineFreezeColor,
        DopplerCrossLineColor,
        DopplerCrossLineFreezeColor,
        ZoomSelectColor,
        ZoomSelectActiveColor,
        ZoomThumbnailBoundColor,
        ZoomThumbnailInnerColor,
        ZoomThumbnailInnerActiveColor,
        ZoomThumbnailOuterColor,
        TgcColor,
        IMTIntimaColor,
        IMTAdventitiaColor,
        FreezeBorderColor,
        FreezeSliderColor,
        FreezeActiveBarColor,
        FreezeInactiveBarColor,
        ImageLabelBackgroundColor,
        ImageLabelHoverColor,
        ImageLabelInactiveColor,
        ImageLabelTextColor,
        ImageLabelActiveColor,
        ImageLabelBorderColor,
        ImageCineActiveColor,
        ImageLabelMultiSelectedColor,
        IsSupportCalendarHoverStyle,
        ClickLabelActiveColor,
        RoiScanLineColor,
        RoiScanLineFreezeColor,
        ReportTextColor,
        ReportBkColor,
        ReportBottomColor,
        ReportEditColor,
        ReportEditBorderColor,
        CurveArrowColor,

        SliderToolButtonBorderColor,
        CurvedPanoColor,
        BodyMarkSelectedColor,
        FourDCubicColor,
        FourDKnotColor,
        FocalPointColor,

        FreeMLine1,
        FreeMLine2,
        FreeMLine3,
        CurFreeMLine,

        PenStyle,

        PreviewWindowHeight,
        TopWindowLeftSpacerWidth,
        TopWindowRightSpacerWidth,
        IsPinyinWidgetLongstanding,
        IsFullScreenDlgTransposed,
        IsSupportRotation,           //是否支持屏幕旋转
        ImageClipWidgetSupportSlide, // 控制存图区支持滑动
        ShowArrowControlGlyphs,      // 显示箭头功能的控制图元，包括旋转和删除
        SupportTouchScreen,
        PreMonthIcon,
        NextMonthIcon,
        BaseComboBoxBkColor,
        LogoFile_zh,
        MeasurementMenuMaxHeight,
        MeasurementMenuShowReport,
        MeasurementItemMenuBtnSpacing,
        ReportCaptionWidth,
        DialogDisplayOnMainScreen, // 多屏幕机型，非全屏对话框的显示位置配置。true - 始终显示在主屏幕；false -
                                   // 终显示在副屏幕
        ArchiveManagerThumbnailRowAndColumn,          // 归档页面缩略图行列数配置
        FourDRightWidgetThumbnailRowAndColumn,        // 4D下右侧缩略图行列数配置
        ReportEditWidgetThumbnailRowAndColumn,        // 报告编辑页面缩略图区行列数配置
        ReportDisplayFullWidgetThumbnailRowAndColumn, // 报告预览页面缩略图区行列数配置
        RotatableBodyMarkWidgetThumbnailRowAndColumn, // 体标缩略图区行列数数配置
        ShowROIAdjustItem,                            // ROI图元调节图标， 默认显示
        HasMenuFrame, // 左侧菜单是否存在独立的菜单面板，比如sonoAir、SR9
        MenuDisplayAtBottom, // 左侧菜单弹出的menu显示策略控制，比如SonoAir、SR9显示menu显示位置不在正下方
        GraphicsFont, // 图元字体
        BrightnessIcon,
        ArrowColor,
        ActiveArrowColor,
        ArrowCursorColor,
        FullScreenTitleBarShowLogo,
        OrganismDefaultIcon, // 探头界面探头器官默认图标配置
        OnlyHaveTouchScreen, // 纯触摸（机器上不配备touchPad）设备，例如P9
        SonoZoomAligment, // true为左上角右上角显示，比如SonoAir;false为左上角左下角显示，比如P9
        StressEchoMultiPlayIcon,
        StressEchoMultiPlayOnIcon,
        StressEchoOrderCustomIcon,
        StressEchoOrderCustomOnIcon,
        StressEchoOrderVerticalIcon,
        StressEchoOrderVerticalOnIcon,
        StressEchoPauseIcon,
        StressEchoPauseOnIcon,
        StressEchoSaveScreenIcon,
        StressEchoSaveScreenOnIcon,
        StressEchoSingalPlayIcon,
        StressEchoSingalPlayOnIcon,
        StressEchoOrderHorizontalIcon,
        StressEchoOrderHorizontalOnIcon,
        StressEchoStopIcon,
        StressEchoStopOnIcon,
        StressEchoPlayIcon,
        StressEchoPlayOnIcon,
        StressEchoNextIcon,
        StressEchoNextOnIcon,
        StressEchoNextDisIcon,
        StressEchoPreIcon,
        StressEchoPreOnIcon,
        StressEchoPreDisIcon,
        AutoEFEDFreezeSliderIcon,
        AutoEFESFreezeSliderIcon,
        AuxiliaryBtnWidgetColCnt,
        ParamCount
    };

public:
    static ModelUiConfig& instance(void);
    /**
     * @brief setValue
     * @param param
     * @param value 颜色值设置时要设置QRgb类型，内部会自动转为QStringList存储
     */
    void setValue(ModelUiConfig::Parameter param, const QVariant& value);
    /**
     * @brief value
     * @param param 颜色值的返回为QRgb类型，也就是unsigned int类型
     * @return
     */
    QVariant value(ModelUiConfig::Parameter param);

    /**
     * @brief readRowAndColumn 读取行列数配置
     * @param param
     * @param row
     * @param column
     */
    bool readRowAndColumn(ModelUiConfig::Parameter param, int& row, int& column);

    void save();
    void load();

private:
    ModelUiConfig();

private:
    HashSettings m_commonSettings;
    HashSettings m_colorSettings;
    static const char* m_allParamNames[ParamCount];
    QHash<int, QVariant> m_DefaultCommonSettings;
    QHash<int, QColor> m_DefaultColorSettings;
};

#endif // MODELCOLOR_H
