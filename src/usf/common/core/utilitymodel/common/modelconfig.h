#ifndef MODELCONFIG_H
#define MODELCONFIG_H
#include "usfcommoncoreutilitymodel_global.h"

#include "modelhashsettings.h"
#include <QObject>

/**
 * @brief The ModelConfig class 定义和机型相关的配置信息，软件运行期不允许被设置，是出厂时就确定的配置，
 * 如果要修改，要使用ModelSettings类
 */
class USF_COMMON_CORE_UTILITYMODEL_EXPORT ModelConfig : public ModelHashSettings
{
    Q_OBJECT
public:
    enum Paras
    {
        RealTHI,
        CommentSearchNumber, // comment查询次数
        HasFocusBlock,
        SocketCount,
        DynamicRangeStep,
        DynamicRangeBase,
        /**
         * 探头插槽映射，探头选择界面的探头顺序，
         * 和实际整机的探头插槽可能顺序不同，需要做映射，
         * 目前ECO60需要颠倒探头码采集的顺序
         */
        SocketMap,
        /**
         * PC平台的FPGA的color颜色和ECO、Tiger的颜色是相反的，在做colormap load时
         * 需要针对两种情况load不同的顺序，默认值:false
         * ECO、 Tiger：false，QBit:true
         */
        ColorInvert,
        /**
         * QBit的USB上传的FPS是随真实FPS变化的fps，控制电影缓存是否按照USB上传的FPS，存入电影
         * 这样电影中每帧数据都是独立的，不会出现混合帧,
         * 真实的fps，会使用整帧DSC的图像上传，图像过渡状态控制的比较好
         */
        IsRealUSBFps,
        /**
         * 进入PW时的等待时间,ECO:500,EBit:1000
         */
        PWUpdateWaitTimeMS,
        /**
         * USB每次下发所用的时间，EBit:2ms,ECO:35ms(可能最新的FPGA解决了传输速度慢的问题，但尚未测试)
         */
        USBWriteTimeMs,
        // BChroma的默认值,ECO使用原来的,EBit使用配置文件中的值
        BColorMapIndex,
        /**
         * 图像模式切换时的不稳定时间，EBit:50ms,ECO:300ms，由于EBit是整帧DSC模式，
         * 并且IsRealUSBFps=true，不稳定过度状态的时间比较少
         */
        ImageUnstableTimeMs,
        /**
         * 图像形状参数调节时的不稳定时间，EBit:50ms,ECO:600ms，由于EBit是整帧DSC模式，
         * 并且IsRealUSBFps=true，不稳定过度状态的时间比较少
         */
        ImageShapeUnstableTimeMs,
        /**
         * PW模式，DScanLine 调节时，图像会闪烁，单独控制时间，EBit:30ms,ECO:200ms
         */
        DLineImageUnstableTimeMs,
        // video是否是GPIO还是fpga控制
        IsFPGAVideo,
        /**
         * USB 读超时的时间，ECO:200ms, EBit、QBit:2000ms
         */
        USBReadTimeOutMs,
        /**
         * 是否支持Standby功能，由于目前EBit standby后，唤醒时问题较多，暂时不开放，默认都是不开启的
         * 等需要测试时，控制ini文件即可打开支持
         */
        StandbyEnabled,
        /**
         * 是否支持合屏待机功能，当前只有Grape机型需要配置该项。
         * 默认值为true，不支持的机型在ini文件设为false即可
         */
        IsSupportLid,
        /*
         *是否支持低电警告灯控制,当前版本只有EBit支持
         */
        BatteryWarningLightEnabled,
        /**
         * roi与实际血流左边界的误差值，eco 的血左侧边界比ROI向右偏离1
         * EBit的线阵探头血左侧边界比ROI向右偏离4,ebit处理直接在控制字下发处做了处理，所以此值为0
         * QBit的roi和血流没有偏离，此值为0
         */
        ROILeftLineDelta,
        /*
         * 是否默认内置SATA硬盘
         */
        CheckSataDevice,
        /*
         *软件版权起始时间
         */
        CopyRightStartDate,
        /**
         * 手动包络，轨迹球的移动响应步进。
         * 经过测试，qbit机器的轨迹球，是目前公司最快的轨迹球，鼠标移动速度基本是ECO的轨迹球的2.x倍
         * 之前的手动包络移动步进是(6,6)，qbit5的需要调整为(12,12)，这样qbit5在进行手动包络时，描迹会
         * 比较安全，否则会随意移动回退
         */
        EnvelopeTraceStep,
        CanWaveModeScroll,
        WaveLenAddress,
        /**
         * 区分eco系列和ebit系列等的后台设置,例如:tabs,groups,parameters
         */
        IsMoreItemsPresetMode,
        HPrfCW,
        /**
         * 是否为无偏差探头码,目前只有sonobook6
         */
        ProbeCodeOffset,
        /**
          * 区分eco系列和其他系列机型B/M上下布局时,B翻转时,M模式标尺是否支持上下翻转,false支持不上下翻转,
            true支持上下翻转
          */
        BMUpDownFlip,
        /**
         *弹性成像压力值在图像数据的位置
         */
        ElastographyPixelSumAddress,
        ElastographyNumSumAddress,
        ElastographyPixelSumAddressMapped,
        ElastographyNumSumAddressMapped,
        /**
         *升级FPGA擦除Flash时所需的间隔时间,带有弹性成像的fpga比较大,需要的时间有所增加
         */
        FlashClearInterval,
        /**
         * 是否进行焦点拼接
         */
        FocusesCombine,
        //焦点拼接关联数据块名称
        FocusesCombineDataBlock,
        MeasurementClearStr,
        /** after beamformer open, the time wait for find probe;TK1:5000 ms, other:1500 ms
         */
        ProbeSteadyTimeMs,
        /**
         * tk1 need delay refresh ModeGlyphs after SystemScanmode changed, Omap use video refresh, it was
         * controled by videorender.TK1:true, other:false
         */
        IsDelayRefreshModeGlyphs,
        /**
         * When freeze state, if controltable or blockdata can be sended. TK1:true, other:false
         * TK1:when set preset, if stop transfer signal, it's very slowly, because usb read's thread will timeout,
         * so usb writing data will wait read timeout.Now when set preset, don't stop transfer signal, but Freeze
         * system for less time.
         */
        CanFreezeSend,
        MenuMaxShownInStressEcho,
        /** 底部菜单按键列数
         */
        ButtomMenuCols,
        /** 屏幕的大小尺寸定义
         */
        MainWindowResolution,
        /** 渲染图像大小尺寸定义
         */
        RenderImageSize,
        /** 渲染区窗体的尺寸定义
         */
        RenderWidgetSize,
        /** DSC图像大小
         */
        DSCImageSize,
        /** 开启DSC图像缩放功能
         */
        DSCImageZoomOn,
        /** 宽景成像显示区的宽度
         */
        CurvedWidth,
        /** LeftMenu Height
         */
        LeftMenuHeight,
        /**
         * 将PixelLen分档,支持0.5cm深度调节
         */
        PixelLenStep,
        /**
         * 是否为新的iImage方式,目前只有sonobook6v2支持
         */
        IsLineiImage,
        /**
         * 设置FPGA传输冗余字符数,由FPGA DMA芯片决定
         */
        RedundantPoints,
        NeedShowLoadingSlider,
        /**
         * 优化档位列表
         */
        LineiImageLevelIds,
        /**
         * pw 声音FPS
         */
        PWSoundFps,
        PWSoundPeriodTimeOut,
        PWSoundPeriodPrepareTimeOut,
        /**
         * 标记探头线间距使用四舍五入算法还是舍位法
         */
        ProbeIntervalUseHighDensity,
        /**
         * 标记进入pw模式前，备份2D Image
         * 在进入PW UDMode前备份2DImage，
         * 并在进入之后将数据拷贝到UDMode的上部分。
         */
        PwBackup2DImage,
        PWOffsetWidth,
        ShowMMC,
        FrameControl,
        TimeInterval,
        DepthCoef,
        MinMAD,
        MaxMAD,
        MaxTimes,
        AutoFreezeSwitch,
        AutoFreezeTime,
        AutoFreezeSensitiveThreshold,
        AutoFreezeFrameThreshold,
        AutoFreezeCheckRange1, //检测范围比例1
        AutoFreezeCheckRange2, //检测范围比例2
        LinePackageCount,
        ProbeUsbBusPortIds,
        SelectProbeOnProbeChanged,
        MouseInteraction, /*鼠标还是触摸交互*/
        /**
         * Apple SonoAir机型，主界面始终显示左侧菜单，为true，其他支持菜单隐藏的机型，默认为false。
         */
        LeftMenuAlwaysShow,
        MeasureTipBindWithGlyphs,
        ProbeSelectedResetTGC, /*重选预设值是否重置TGC*/
        /**
         * ROIAdjustGraphicItem
         *是否显示ROI框下方小圆圈，用于iOS平台无需显示C++绘画的小圆圈
         */
        ShowDefaultBottomCenterROIAGItem,
        DisplayAutoTraceTAMAXLine,  // 显示autotrace TAMAX线
        DisplayAutoTraceTAMEANLine, // 显示autotrace TAMEAN线
        RollMenuWithMouse,
        FingerPrintType, // 不同指纹供应商
        Useudev4Battery,
        SystemScanModeToolUpdateToolValue,
        MeasureMouseActionXStep,
        MeasureMouseActionYStep,
        ROIRegionHeightFactor,
        SupportFreezeOutage, //支持冻结断电
        IsTraceBall,         //区分设备为轨迹球或触摸板
        DepthTipIconPos,
        GainTipIconPos,
        TgcTipIconPos,
        SkTipIconPos,
        SKTipIconMargin,
        SKTipIconShowDelayTime,
        LockScreenTipPos,
        IsSupportVideoOut, //是否支持Video口输出，目前Apple机型不支持Video口
        DRTypeForB, // 0:代表Apple以及后续一切和Apple的B所用动态范围算法一致，1则代表和SR9的一致
        ModelFilesUseIni,          //频率文件，MITI等等文件是否以二进制形式保存
        UseProbeOrganismSelection, // 使用人体器官探头选择界面
        SupportSonoEyeProbes,      //支持的SonoEye探头的Pid:Vid 组，如：04b4:00f2,04b4:00f1
        IsCreateLeftTopWidget,     //是否需要创建leftTopMenuWidgetContainer
        IsFixedDepth,
        MaxProbeButtonCount,
        BatteryConnectType,
        SerialPortName,
        MotorPortName,
        BatteryChargeThreshold,
        VID,
        PID,
        UltrasoundDeviceCanDisable,
        UltrasoundDeviceTimeoutCount,
        UltrasoundDeviceTimeoutMaxInterval,
        UltrasoundKeyboardPhyId,
        FanWarningThreshold,
        FanCheckOn,
        BatteryCommoundTime,
        KBUpdateReboot,
        IsSupportTouchPad,       // Determine whether the current model supports touchpad.
        OrganismZoomFactor,      // 探头选择界面预设ICON缩放系数
        OrganismHoverZoomFactor, // 探头选择界面预设在hover状态下的ICON缩放系数
        SupportCRCCheckSum,      //控制表下发是否支持crc校验
        SupportRedundantData,    //下发数据块是否支持冗余数据
        IsSupportAnyDensity,     //是否支持任意线密度
        IsSupportFHISwitch,      //是否支持谐波切换,从lotus同步参数的时候增加
        Channel,                 //通道数
        RxFreqMul,               // Rx步进频率倍率(1~16) default 4
        TxFreqMul,               // Tx步进频率倍率(1~16) default 2
        //            Rx焦距采样时钟数(1~32) default 8
        GainAdjustStep,
        GainAdjustWidth,
        SelectFrameSpeed,   // 选帧分为快速选择和缓慢选择，当速度大于该值视为快调
        SelectFrameMaxStep, // 选帧最大步进，总帧数小于可调范围的宽度时使用最大步进，随着总帧数和调节范围宽度的倍数关系递减
        OutGainDelta, // 输出增益增量值。PW模式下调节的输出增益加上该增量值即为实际发给Zeus的输出增益值
        SupportBiopsyProbes, //支持穿刺功能的探头
        SuperNeedleEx, //部分机型比如xbit的superneedle会比sonoair多出一些参数，此变量用于区分是否支持多出来的参数，以后如果fpga统一后可以去除
        TraceMaxPoints, //机器性能限制导致一些自动包络算法返回的包络点的个数要有限制否则会导致机器卡顿
        // Zeus 后处理优化控制参数
        BPostEnable,
        CPostEnable,
        // Zeus DSC插值方法控制参数
        DscMethod, // 默认值为:0，ATOM机型为：1
        TGCLimitMinValue,
        TGCLimitMaxValue,
        IsAjeCPLD, // CPLD文件是否为aje文件,true代表是，该类型的CPLD升级用的是厂家提供的升级程序（读写寄存器的方式）。默认值为：false，Atom机型为true
        TgcToolPostProcess, // TgcTool是否可以冻结下调节，默认是false，SonoAir机型默认false；Atom机型设置为true
        IsGrayMapHorMove, // GrayMap是否支持牛顿5点法横向调节
        IsPWDynamicRangeAdjustmentEnable,
        CWPush2ZeusPointsUnit,
        IsSupportVS,     //是否支持虚拟源布线。默认是false
        VSBlockCheck,    //虚拟源布线数据块是否支持回读，默认是false
        VSBlockLimitNum, //虚拟源布线数据块每次下发的限制大小（Atom受PCIE驱动的限制），单位是M，目前Atom设置为16。
        IsSupportVSCalculateBlockData, //是否支持虚拟源计算data194、data195、data66数据块;不支持的情况，是直接读取的数据块文件
        RxStepClockMultiplicity, //接收步进时钟倍数
        TxStepClockMultiplicity, //发射步进时钟倍数
        FocusSamplingClockCount, //焦距采样时钟倍数
        IsPCIEWithDoubleBuffer,
        IsCallBackDataOfBfIODevice, // Atom的PCIE数据采用回调的方式，设置为true，默认是false
        IsRDMAIODevice,             // Atom的PCIE RDMA的方式，设置为true，默认是false
        IsPCIECallBackDirectConnect, //测试时使用，让Atom通过PCIE回调的数据不通过队列，直接调用receiver，默认是false
        SoundPlayingIntervals, //系统声音播放间隔，分别对应的操作:General, CW, Thi, Freq
        IsRotateButtonMachine,
        BtnOperationMode,
        HwUpateMode,
        HwUpateModeUI,
        PresetShortcutEnable,
        LightBeltEnable,
        NavigationHotKeyDisplayQuantity,
        ADCDataAcquisitonPathCount, // atom是两块fpga分别存一半通道, grape是1块fpga存所有通道，默认为1，ATOM时要配置为2
        SoftRunDir, // linux系统中，运行程序所挂载的文件系统地址，执行df命令返回内容的头部信息
        IntelligentInterconnectionIsEnabled,
        KeyboardMode,              //虚拟键盘输入逻辑 0为便携式  1为推车式
        InputLettersByEmbbededKBD, //是否使用机器自带键盘输入
        IsAllImageModeSupportECG,  //是否所有图像模式都支持ECG，SonoAir只有B模式支持
        ParasCount
    };
    static ModelConfig& instance();
    void save() override;

private:
    ModelConfig();

protected:
    const char* paraName(int key) const override;

private:
    static const char* ParaNames[ParasCount];
};

#endif // MODELCONFIG_H
