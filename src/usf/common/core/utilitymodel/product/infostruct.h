#ifndef INFOSTRUCT_H
#define INFOSTRUCT_H

//#include <unistd.h>
#include "usfcommoncoreutilitymodel_global.h"
#include <QFile>
#include <QHash>
#include <QList>
#include <QPoint>
#include <QString>
#include <QStringList>
#include <QVariant>
#include <QVector>
#include <math.h>

/**
 * @brief The CWGainParam struct
 * 深度与增益对应关系数据结构
 */
struct USF_COMMON_CORE_UTILITYMODEL_EXPORT CWGainParam
{
    float depthCM;
    int gain;
    CWGainParam();
    CWGainParam(float dposCM, int dgain);
};

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT ElementDetectParas
{
    /*!
     * 基元检测需要过滤的基元
     */
    QVector<int> WaferFilter;

    /**
     * @brief MinThreshold 基元检测结果的正常阈值的最小值
     */
    int MinThreshold;

    /**
     * @brief MaxThreshold 基元检测结果的正常阈值的最大值
     */
    int MaxThreshold;

    /**
     * @brief UsedPointNum
     */
    int UsedPointNum;

    /**
     * @brief OneWaferAddDoubleLines  基元检测单个基元对应的线数在2的基础上左减右加OneWaferAddDoubleLines根线，
     * OneWaferAddDoubleLines=0时，一个基元对应2根线；OneWaferAddDoubleLines=1时，一个基元对应4根线
     */
    int OneWaferAddDoubleLines;
};

enum Density
{
    MediumDensity = 0,
    LowDensity = 1,
    HighDensity = 2,
    DensityNumber = 3
};

/*!
 * \brief 探头信息结构
 */
class USF_COMMON_CORE_UTILITYMODEL_EXPORT ProbeDataInfo
{
public:
    enum ProbeAppType
    {
        Human,
        Animal,
        All
    };
    enum FourDFlakyType // 4D挡光片类型
    {
        Bilateral, // 双挡光片
        Unilateral // 单挡光片
    };
    enum FourDProbeType // 4D探头类型
    {
        Abdominal,     // 腹部
        Intracavitary, // 腔内
        Other          // 其它
    };

    /*!
     * 唯一性，不变性
     */
    int Id;
    /*!
     * 探头名
     */
    QString Name;
    /*!
     * 探头显示名
     */
    QString ShowName;
    /*!
     * 探头显示频率的字符串数组
     */
    QStringList Freqs;
    /*!
     * 探头显示频率的字符串数组
     */
    QStringList FreqsOnThi;
    /**
     * @brief FreqsColor 彩色模式显示频率
     */
    QStringList FreqsColor;
    /**
     * @brief FreqsDop PW模式显示频率
     */
    QStringList FreqsDop;
    /*!
     * 探头频率下发控制表中对应的编码值数组
     */
    QVector<int> FreqIndexes;
    /*!
     * 探头频率下发控制表中对应的实际频率
     */
    QVector<float> FreqMHzes;
    /*!
     * 16档声功率，如果为空，则按照0~255平均分成16档计算，否则使用具体值
     */
    QVector<int> BAcousticPowers;
    /*!
     * 探头码，用于下发给控制表的码值
     */
    int Code;
    /*!
     * 从硬件上传的图像数据中，获取的硬件探头码，目前还没有读取硬件探头码
     */
    int HwCode; // hardware code from usb data
    /**
     * @brief HwCodes PC平台的机器，探头码，已经使用2个字节来表示，嵌入式平台目前还是1个字节来表示
     */
    QVector<int> HwCodes;
    /**
     * @brief CwGainParams 保存深度及对应增益值
     */
    QList<CWGainParam> CwGainParams;
    /*!
     * 探头基元数
     */
    int WaferNum;
    /*!
     * 探头基元长度
     */
    float WaferLength;
    /*!
     * 一般探头探头基元长度同 WaferLength，相控阵比较特殊，相控阵探头的WaferLength是用来描述
     * 角间隔的弧度值，而Pitch值在ECO、EBit机型中没有用到，QBit机型中需要使用探头的物理Pitch值
     * 进行一些计算，作为下发使用
     */
    float Pitch;
    /*!
     * 探头基元半径
     */
    float WaferRadius;
    /**
     * @brief 4D fan radius
     */
    float FanRadius;
    /*!
     * 探头最大显示深度，目前使用 \a MaxCQYZ 来控制最大显示深度
     */
    float MaxDepthCM;
    /*!
     * 探头最小显示深度，目前使用 \a MinCQYZ 来控制最小显示深度
     */
    float MinDepthCM;
    /*!
     * 探头最大抽取因子
     */
    int MaxCQYZ;
    /*!
     * 探头最小抽取因子
     */
    int MinCQYZ;
    /*!
     * X轴正方向和穿刺线方向的顺时针角度
     */
    int BiopsyAngle;
    /*!
     * 以图像显示区正中间为原点，穿刺线起点的X坐标
     */
    float BiopsyXPosMM;
    /*!
     * 以图像显示区正中间为原点，穿刺线起点的Y坐标
     */
    float BiopsyYPosMM;
    /*!
     * X轴正方向和穿刺线方向的顺时针角度的偏移 // added by Jin Yuqi
     */
    int BiopsyAngleOffset;
    /*!
     * 以图像显示区正中间为原点，穿刺线起点的X坐标的偏移 // added by Jin Yuqi
     */
    float BiopsyXPosMMOffset;
    /*!
     * 以图像显示区正中间为原点，穿刺线起点的Y坐标的偏移 // added by Jin Yuqi
     */
    float BiopsyYPosMMOffset;
    /*!
     * X轴正方向和穿刺线方向的顺时针角度的偏移的范围 // added by Jin Yuqi
     */
    int BiopsyAngleOffsetRange;
    /*!
     * 以图像显示区正中间为原点，穿刺线起点的X坐标的范围 // added by Jin Yuqi
     */
    float BiopsyXPosMMOffsetRange;
    /*!
     * 以图像显示区正中间为原点，穿刺线起点的Y坐标的范围 // added by Jin Yuqi
     */
    float BiopsyYPosMMOffsetRange;
    /*!
     * 该探头应用类型
     */
    ProbeAppType AppType;
    /*!
     * 该探头是否为线阵
     */
    bool IsLinear;
    /*!
     * 该探头是否为像控阵
     */
    bool IsPhasedArray;
    /*!
     * 该探头是否为反向控制
     */
    bool IsReverseControl;
    /*!
     * 相控阵探头开角
     */
    double PhasedAngle;
    /*!
     * 该探头是否支持高密度
     */
    bool IsSupportHighDensity;
    /**
     * @brief
     */
    bool IsFourD;
    /**
     * @brief IsDimensionProbe 容积探头
     */
    bool IsDimensionProbe;
    /**
     * @brief IsPencilProbe 铅笔探头
     */
    bool IsPencilProbe;
    /*!
     * 该探头的最大显示深度是否在23CM以上，否则为false
     */
    bool IsDeep;
    /*!
     * Atom机型上该值=1时，用于标识探头内部有高压开关
     * 其他机型表示：该探头是否为高基元探头,基元数大于128为true
     */
    bool IsMEProbe;
    /*!
     * compound开启时,探头最大的焦点数.
     */
    int MaxFocusCompoundOn;
    /*!
     * compound关闭时,探头最大的焦点数.
     */
    int MaxFocusCompoundOff;
    /*!
     * 该探头对应的图标
     */
    QString IconName;
    /*!
     * 该探头对应的快捷图标
     */
    QString ShortCutIconName;

    /*!
     * 该探头对应块数据的路径名
     */
    QString DynFileName;
    /*!
     * 由于在调节compound 和 freq时，图像亮度变化太大，现在在调节compound 和 freq 时，
     * 自动调节增益，目前每一个compound和freq都对应一个gain系数(coefficient)，
     * GainNew = GainOld * GCoeNew / GCoeOld;
     */
    int gainCoe(int compound, int freq) const;
    bool isNull() const;
    /*!
     * gain系数
     */
    QList<QList<int>> m_GainCoes;
    /*!
     * 线阵探头的Cos偏转角度
     */
    int CosAngle;
    /*!
     * 线阵探头的Tg偏转角度
     */
    int TgAngle;
    int NoUseLine;
    /**
     * @brief WaferNumDiff 探头左右两边去掉的基元数
     * example:D3C60L-S 96基元，
     * WaferNumDiff=(128-96)/2=16
     * FPGA控制字默认值为128
     */
    int WaferNumDiff;

    /**
     * @brief WaferBegin 基元起始位
     * WaferNumDiff不能满足相控阵探头， 相控阵探头有效基元32，
     * 分布在低位64基元的中间，或者高位64基元的中间，
     * 因此引入WaferBegin和WaferEnd 标记起始和结束基元
     */
    int WaferBegin;

    /**
     * @brief WaferEnd 基元结束位
     */
    int WaferEnd;

    /**
     * @brief IsHighFreqPhasedProbe 是否为高频相控阵探头
     */
    bool IsHighFreqPhasedProbe;

    /**
     * @brief HighFreq 是否是高频探头
     */
    bool HighFreq;

    /**
     * @brief SuperHFreq 是否是超高频探头
     */
    bool SuperHFreq;

    /**
     * @brief IsTemperatureDetected 是否需要探头温度检测
     */
    bool IsTemperatureDetected;
    int MinProbeTemperature;

    /*!
     * 所有探头的prflist
     */
    QList<QVector<int>> PrfListContainer;

    /*!
     * 非相控阵探头是否支持CW模式
     */
    bool IsNonPhasedProbeCW;

    /*!
     * 不同深度需要不同的AdjustmentOfB
     */
    QVector<int> AdjustmentOfBs;

    /*!
     * @brief Clamp 探头发射脉冲间是否插入clamp
     * 硬件表示方法：0是插入clamp(默认值)，1是不插入clamp
     * 字面意思和实际下发的值是反的,所以xml中定义Clamp参数时TrueValue=0
     * 软件表示方法：1是插入clamp(默认值)，0是不插入clamp
     */
    bool Clamp;

    /**
     * 掌超的血流频率
     *
     * */
    QString CFM_Freq;
    /**
     * 掌超的PW频率
     *
     * */
    QString PW_Freq;
    QString FreqCWD;
    int WeightSlope;
    bool TxSyncSwitch;
    int BFSRA;
    int CfmTransmitClamp;
    int DSCMidLineDiff;
    int RXGate; // 补偿

    bool isTeeProbe; //是否是Tee探头

    QVector<int> NeedleAngles;

    ElementDetectParas ElementDetectPara;
    /**
     * @brief 起始高压开关号
     */
    int SHVSW_NO;
    /**
     * @brief 终止高压开关号
     */
    int EHVSW_NO;
    /**
     * @brief 高压开关数
     */
    int HVSW_NUM;
    /**
     * @brief 单配置线高压开关位数
     */
    int HVSW_DN;
    /**
     * @brief 高压开关配置方式
     */
    int HVSW_Config;

    /**
     * @brief RevLNum 三档线密度数 系列参数
     * 数组下标 与 enum Density: MediumDensity, LowDensity, HighDensity 对应
     */
    int RevLNumB[DensityNumber];
    int RevLNumC[DensityNumber];
    int RevLNumSWE[DensityNumber];
    int RevLNumZoomB[DensityNumber];
    int RevLNum4D[DensityNumber];
    int RevLNum4Beams[DensityNumber];

    double TxRatioB4Beams[DensityNumber];
    double RxRatioB4Beams[DensityNumber];

    bool IsRevLNumLowB_TDI_Save;
    int RevLNumLowB_TDI;
    int RevLNumLowBZoom_TDI;

    int LensCutDepth;
    int TRANSMIT_APERTURE_ADD_SYMMETRY;

    double FreqCenter; //中心频率，做XBF计算data194，data195的时候增加的

    FourDFlakyType FourD_FlakyType; // 挡光片类型，实现4D时添加的
    FourDProbeType FourD_ProbeType; // 探头类型分为腹部、腔内和其它，实现4D时添加的

    double probeWide() const
    {
        return WaferLength * WaferNum;
    }

    int waferDiff() const
    {
        return WaferNumDiff == 128 ? 0 : WaferNumDiff;
    }

    int waferNumWork() const
    {
        if (IsPhasedArray)
        {
            return WaferEnd - WaferBegin + 1;
        }
        else
        {
            return WaferNum - 2 * waferDiff();
        }
    }

    QVector<int> DynamicCompensationList;

    bool operator<(const ProbeDataInfo& other) const;
    bool operator<=(const ProbeDataInfo& other) const;
    bool operator==(const ProbeDataInfo& other) const;
    bool operator>(const ProbeDataInfo& other) const;
    bool operator>=(const ProbeDataInfo& other) const;
};

/*!
 * \brief 探头类型
 */
enum ProbeTypeInfomation
{
    /*!
     * 凸阵
     */
    SonoProbeCurved,
    /*!
     * 线阵
     */
    SonoProbeLinear,
    /*!
     * 相控阵
     */
    SonoProbePhasedArray
};

/*!
 * \brief 探头信息，目前不需要使用此结构，请使用 \a ProbeDataInfo 表示探头结构
 *
 * \sa ProbeDataInfo
 */
struct ProbeInformation
{
public:
    QString m_strName;
    int m_code;
    ProbeTypeInfomation m_type;
    double m_curvature;
    double m_elementPitch;
    int m_numberOfElements;
    float m_txFreq; // current freqency in allTxFreq.
    int m_biopsyAngle;
    double m_biopsyDepthMM;
    float m_allTxFreq[4];

    ProbeInformation()
    {
        m_strName = "";
        m_code = 0;
        m_type = SonoProbeLinear;
        m_curvature = 0;
        m_elementPitch = 0;
        m_numberOfElements = 0;
        m_txFreq = 0;
        m_biopsyAngle = 0;
        m_biopsyDepthMM = 0;
        memset(m_allTxFreq, 0, sizeof(m_allTxFreq));
    }
};
/*!
 * \brief 表示图像工作的模式
 */
enum ImageModeType
{
    //    MODE_B = 0x00,
    //    MODE_M = 0x01,
    //    MODE_BM = 0x03,
    //    MODE_2B = 0x02,
    //    MODE_4B = 0x04,

    /// <summary>
    /// 单B、单C
    /// </summary>
    MODE_B = 0x00,
    /// <summary>
    /// 单M
    /// </summary>
    MODE_M = 0x01,
    /// <summary>
    /// 放大
    /// </summary>
    MODE_ZOOM = 0x02,
    /// <summary>
    /// 左右BM
    /// </summary>
    MODE_BM = 0x03, //左右BM
                    /// <summary>
                    /// 双B、双C，控制哪一个是活动图像，要累加 0~1
                    /// </summary>
    MODE_2B = 0x04,
    /// <summary>
    /// 4B、4C，控制哪一个是活动图像，要累加 0~3
    /// </summary>
    MODE_4B = 0x08,
    /// <summary>
    /// 上下BM、上下B+D、上下B+C+D
    /// </summary>
    MODE_UDBM = 0x0c, //上下BM
                      /// <summary>
                      /// B/BC
                      /// </summary>
    MODE_LBRC = 0x06,
    /// <summary>
    /// BC/B
    /// </summary>
    MODE_LCRB = 0x07,
};

/*!
 * \brief 同步模式
 */
enum SyncModeType
{
    Sync_None = 0x00,
    /// <summary>
    /// 血流模式
    /// </summary>
    Sync_C = 0x01,
    /// <summary>
    /// 频谱多普勒模式，二同步B|D
    /// </summary>
    Sync_D = 0x02,
    /// <summary>
    /// 三同步 B | C | D
    /// </summary>
    Sync_CD = 0x03,
    /// <summary>
    /// 存在M模式
    /// </summary>
    Sync_M = 0x04,
    /// <summary>
    /// CM模式
    /// </summary>
    Sync_CM = 0x05,
};

enum ColorImageModeType
{
    Color_None = 0x00,
    /// <summary>
    /// 能量
    /// </summary>
    Color_PD = 0x01,
    /// <summary>
    /// 速度
    /// </summary>
    Color_CF = 0X02,
    /// <summary>
    /// 方差 Square Deviation
    /// </summary>
    Color_SD = 0X04,
};

/// <summary>
/// modified by daixiao, 2010-11-12
/// </summary>
enum SystemScanModeDescription
{
    B_Mode = 0,
    M_Mode = 1,
    PW_Mode = 2,
    CF_Mode = 3,
    DPD = 4,                        // Directional Power Doppler
    PD = 5,                         // Power Doppler
    PWDColorDoppler = 6,            // PW + CF
    PWDDirectionalPowerDoppler = 7, // PW + DPD
    PWDPowerDoppler = 8,            // PW + PD
    CWD = 9,                        // Continuous-Wave Doppler
    CWDColorDoppler = 10,           // CWD + CF
    CWDDirectionalPowerDoppler = 11,
    CWDPowerDoppler = 12,
    TissuePWDDoppler = 13,
    TissueColorDoppler = 14,

    BM_Mode = 15,
    LRBM_Mode = 16, // left-right bm mode.
    UDBM_Mode = 17, // up-down bm mode.
    FourD_Mode = 18,
    Elasto_Mode = 19,
    ScanMode_None = 20,
};

enum SystemScanMode
{
    SystemScanModeB,
    SystemScanMode2B,
    SystemScanMode4B,
    SystemScanModeLRBM,
    SystemScanModeUDBM,
    SystemScanModeM,
    SystemScanModeColorDoppler,
    SystemScanModePowerDoppler,  // CPA
    SystemScanModeDPowerDoppler, // Directional Power Doppler (DPD)
    SystemScanModeBPW,
    SystemScanModeColorPW,
    SystemScanModePowerPW,
    SystemScanModeDPowerPW,
    SystemScanModeCWD,             // Continuous-Wave Doppler
    SystemScanModeCWDColorDoppler, // CWD + CF
    SystemScanModeCWDDirectionalPowerDoppler,
    SystemScanModeCWDPowerDoppler,
    SystemScanModeTissueDoppler, // TDI,Tissue Doppler Imaging
    SystemScanModeTissuePW,      // TDI+PW
    SystemScanModeColorM,
    SystemScanModeColorLRBM,
    SystemScanModeColorUDBM,
    SystemScanModePDM,
    SystemScanModePDLRBM,
    SystemScanModePDUDBM,
    SystemScanModeDPDM,
    SystemScanModeDPDLRBM,
    SystemScanModeDPDUDBM,
    SystemScanModeTDIM,
    SystemScanModeTDILRBM,
    SystemScanModeTDIUDBM,
    SystemScanModeE,
    SystemScanModeLRFreeM,
    SystemScanModeUDFreeM,
    SystemScanModeFourDPre,
    SystemScanModeFourDLive,
    SystemScanModeFourD,
    SystemScanModeLRBBC,
    SystemScanModeElasto,
    SystemScanModeBBE,
    SystemScanModeUDBBC,
    SystemScanModeFreeHand3D,
    SystemScanModeCP, // Curved Panoramic
    SystemScanModeAV,
    SystemScanModeSonoNeedle,
    SystemScanModeMVI,
    SystemScanModeMVIPW,
    SystemScanModeSonoNerve,
    SystemScanModeNone
};

const QString SystemScanModeArray[] = {"SystemScanModeB",
                                       "SystemScanMode2B",
                                       "SystemScanMode4B",
                                       "SystemScanModeLRBM",
                                       "SystemScanModeUDBM",
                                       "SystemScanModeM",
                                       "SystemScanModeColorDoppler",
                                       "SystemScanModePowerDoppler",
                                       "SystemScanModeDPowerDoppler", // Directional Power Doppler
                                       "SystemScanModeBPW",
                                       "SystemScanModeColorPW",
                                       "SystemScanModePowerPW",
                                       "SystemScanModeDPowerPW",
                                       "SystemScanModeCWD",             // Continuous-Wave Doppler
                                       "SystemScanModeCWDColorDoppler", // CWD + CF
                                       "SystemScanModeCWDDirectionalPowerDoppler",
                                       "SystemScanModeCWDPowerDoppler",
                                       "SystemScanModeTissueDoppler", // TDI,Tissue Doppler Imaging
                                       "SystemScanModeTissuePW",      // TDI+PW
                                       "SystemScanModeColorM",
                                       "SystemScanModeColorLRBM",
                                       "SystemScanModeColorUDBM",
                                       "SystemScanModePDM",
                                       "SystemScanModePDLRBM",
                                       "SystemScanModePDUDBM",
                                       "SystemScanModeDPDM",
                                       "SystemScanModeDPDLRBM",
                                       "SystemScanModeDPDUDBM",
                                       "SystemScanModeTDIM",
                                       "SystemScanModeTDILRBM",
                                       "SystemScanModeTDIUDBM",
                                       "SystemScanModeE",
                                       "SystemScanModeLRFreeM",
                                       "SystemScanModeUDFreeM",
                                       "SystemScanModeFourDPre",
                                       "SystemScanModeFourDLive",
                                       "SystemScanModeFourD",
                                       "SystemScanModeLRBBC",
                                       "SystemScanModeElasto",
                                       "SystemScanModeBBE",
                                       "SystemScanModeUDBBC",
                                       "SystemScanModeFreeHand3D",
                                       "SystemScanModeCP", // Curved Panoramic
                                       "SystemScanModeAV",
                                       "SystemScanModeSonoNeedle",
                                       "SystemScanModeMVI",
                                       "SystemScanModeMVIPW",
                                       "SystemScanModeSonoNerve",
                                       "SystemScanModeNone"};

enum Layout
{
    Layout_1x1 = 1,
    Layout_Min = Layout_1x1,
    Layout_1x2 = 2,
    Layout_2x2 = 4,
    Layout_Max = Layout_2x2
};

enum CurLayout
{
    Layout_1x1_1 = 0, // 单幅
    Layout_1x2_1 = 1, // 双幅的左边
    Layout_1x2_2 = 2  // 双幅的右边
};

enum imagePatition //记录图片应刷给哪个区域
{
    Partition_Default = 0, // 默认，CurLayout的值是哪个，则将图像刷给哪个区
    Partition_Left = 1,    // 将图像刷到左边
    Partition_Right = 2,   // 将图像刷到右边
    Partition_None = 4     // 无AutoEF功能
};

enum CompoundDebug
{
    CompoundDebug0 = 1,
    CompoundDebug1 = 2,
    CompoundDebug2 = 4,
    CompoundDebugOff = 7,
};

enum LayoutFormat
{
    Format_1x1 = 0,
    Format_1x2_UD = 1,
    Format_1x2_LR = 2,
    Format_2x2 = 3
};

enum MDisplayFormat
{
    Up_Down_2_1 = 0,
    Up_Down_1_1,
    Up_Down_1_2,
    Left_Right_2_1,
    Left_Right_1_1,
    Left_Right_1_2,
    // just for 4B or Layout_2x2
    Left_Right_Up_Down_1_1_1_1
};

struct ImageLayoutSize
{
    ImageLayoutSize()
        : m_Row(0)
        , m_Col(0)
    {
    }
    ImageLayoutSize(int row, int col)
        : m_Row(row)
        , m_Col(col)
    {
    }
    int m_Row;
    int m_Col;
};

struct ImageLayoutStretchs
{
    QString m_RowStretchs;
    QString m_ColStretchs;
};

struct ImageLayoutInfo
{
    ImageLayoutSize m_LayoutSize;
    ImageLayoutStretchs m_Stretchs;
};

/*!
 * \brief 定义TGC的段数
 */
enum
{
#ifdef USE_TGC_12
    TGC_COUNT = 12,
#else
#ifdef USE_TGC_10
    TGC_COUNT = 10,
#else
    TGC_COUNT = 8,
#endif
#endif
    LGC_COUNT = 8,
    LGC_HEIGTH = 80
};

namespace ParasStruct
{
/*!
 * \brief 控制表中，\a ImageFormer 中的大部分外部需要访问的参数
 * \todo 增加校验字段、版本字段或者校验方法（通过一些字段的值进行判断），导入preset进行校验 2012-10-10
 */
#define CT_PARA_CHECK (0x2143)

struct ControlTablePara
{
public:
    ImageModeType ImageMode;
    int AcousticPower;    // 声功率 0~15
    int FocusNum;         // 焦点数 1~4
    int FocusPos;         // 焦点位置，0~15
    uchar TGC[TGC_COUNT]; // TGC
    int Gain;             // B型总增益0~255
    int MScanLine;        // M线号,与当前起止线号相关
    int MVelocity;        // M速度 1~4
    int CQYZ;             // 抽取因子
    uchar Scroll;         // 卷行
    uchar LogCompression;
    uchar MFC;
    uchar DYF;
    int ScanWidth; // 扫描宽度,0~3,直接控制StartLine，StopLine
    int StartLine;
    int StopLine;
    int DynamicRange; // 动态范围 0~15
    bool HighDensity; // 高密度(半间距)/低密度(整间距)
    int Smooth;
    int Edge;
    int LineAvg;
    int FrameAvg;
    int FreqIndex;                // 发射频率索引
    bool WideBand;                // 宽带
    int ZoomMultiple;             // 放大倍数 1~4
    bool Harmonic;                // Harmonic 谐波
    SyncModeType SyncMode;        // 同步模式
    bool Left;                    // 左
    bool Up;                      // 右
    int Gama;                     // gama reject 0~3
    int BEffect;                  // 0~4
    int VHSi;                     // C发射频率索引
    unsigned char Reserve15[256]; //灰阶表
    int BReject;                  // 0~16
                                  //以下为新增加
    uchar SynRecAperture;         // Synthetic Receive Aperture 2011-6-28 added

    uchar HF_Alpha;
    uchar IIR_ON;
    uchar HF;

    int SpacialCompound; // Spacial Compound 2011-6-28 added
    int BGrayCurveIndex; //记录GamaCurve的Index值 2011-6-28 added
    bool NotReduceGain;  //是否不抑制增益 2011-11-4 added

    /// <summary>
    /// 多普勒声音 0~255
    /// </summary>
    uchar Volume;
    uchar CWDVolume;
    /// <summary>
    /// 血流声功率
    /// </summary>
    uchar AcousticPowerColor;
    //    uchar FocusPattern;            //add by daixiao, 2010-11-09
    //    uchar ImageOptimizationLevel;  //add by daixiao, 2010-11-15
    uchar AnalogTgc[8];

    uchar BSteeringAngle;
    bool ExceedLowBlood;
    uchar CTGC;
    uchar TestSignal;
    uchar TestParameter;
    uchar BPulseNum; // default 1
    uchar CPulseNum; // default 4
    uchar DPulseNum; // default 5
    uchar WT;

    /// <summary>
    /// 血流焦点数 1~4
    /// </summary>
    int FocusNumColor;
    /// <summary>
    /// 血流焦点位置，0~15，最大值与焦点数相关
    /// </summary>
    int FocusPosColor;
    uchar UniformityTgc[16];

    unsigned char Reserve3[8];

    uchar ColorPriority;    // daixiao,2010-11-16
    uchar PDColorPriority;  // daixiao, 2010-11-16
    uchar DPDColorPriority; // daixiao, 2010-11-17
    uchar DynamicRangeDop;
    uchar DynamicRangeCWD;
    uchar NoiseRejection;
    uchar NoiseRejectionDop;
    uchar NoiseRejectionCWD;
    /// <summary>
    /// 血流发射频率索引
    /// </summary>
    int FreqIndexColor;
    /// <summary>
    /// 多普勒发射频率索引
    /// </summary>
    int FreqIndexDop;
    uchar DigitalGain;
    uchar DynStartDepth; // CM
    uchar DynStartDB;    // Index 0~15
    uchar DynEndDepth;   // CM
    uchar DynEndDB;      // Index 0~15
    //    bool HID;
    /// <summary>
    /// Sunshine 新控制表
    /// 是否开启频率复合（fcpd） (SRA)
    /// </summary>
    bool FrequencyCompounding;
    bool FrequencyCompoundingBackUp;
    /// <summary>
    /// Sunshine 新控制表
    /// 频率复合发射频率
    /// </summary>
    uchar FreqIndexFrequencyCompounding;
    /// <summary>
    /// Sunshine 新控制表
    /// 频率复合焦点位置组合
    /// </summary>
    uchar FocusPosFrequencyCompounding;
    signed char DynamicOffsetOfCPD; // scpd
    signed char DynamicOffsetOfSRA; // fcpd
    uchar Reserve5[4];
    /// <summary>
    /// 由于M，C，D模式下只有单焦点，但保存时需要保存B模式下的焦点数
    /// 故此参数用以保存B模式下的焦点数
    /// daixiao,2011-3-9
    /// </summary>
    int FocusNumB;
    /// <summary>
    /// 此参数保存B模式下焦点位置
    /// daixiao, 2011-3-9
    /// </summary>
    int FocusPosB;

    uchar Scpd; // Compound
    uchar ScpdBackUp;
    uchar AcousticPowerTestCode;
    uchar SingleBeamWave;
    uchar SingleBeamWaveBackup;
    uchar LCDLight;
    uchar UploadInterval;

    uchar DutyRatioOfTransmit1; //发射1占空比
    uchar PulseNumOfTransmit1;  //发射1脉冲数
    uchar HighFreqOfTransmit1;  //发射1高低频标识
    uchar FrequencyOfTransmit1; //发射1频率
    uchar FocusNumOfTransmit1;  //发射1焦点数
    uchar FilterCoefOfTransmit1;
    uchar FocusPos1OfTransmit1; //发射1焦点位置1
    uchar FocusPos2OfTransmit1; //发射1焦点位置2
    uchar FocusPos3OfTransmit1; //发射1焦点位置3
    uchar FocusPos4OfTransmit1; //发射1焦点位置4

    uchar DutyRatioOfTransmit2; //发射2占空比
    uchar PulseNumOfTransmit2;  //发射2脉冲数
    uchar HighFreqOfTransmit2;  //发射2高低频标识
    uchar FrequencyOfTransmit2; //发射2频率
    uchar FocusNumOfTransmit2;  //发射2焦点数
    uchar FilterCoefOfTransmit2;
    uchar FocusPos1OfTransmit2; //发射2焦点位置1
    uchar FocusPos2OfTransmit2; //发射2焦点位置2
    uchar FocusPos3OfTransmit2; //发射2焦点位置3
    uchar FocusPos4OfTransmit2; //发射2焦点位置4

    uchar ImageZoomCoef; // 0~100

    //    //电压信息，added by daixiao, 2011-10-26
    //    double v1;
    //    double v2;
    //    double v3;
    //    double v4;
    //    double v5;
    //    double v6;
    //    double v7;
    //    double v8;
    // 以上64个字节未使用，用作以下，Reserve0之前的参数 //gongdl 2012-09-11
    short BRejection;
    short MRejection;
    short PwRejection;
    short CwdRejection;
    // NewtonIinterpolationAlgorithm file sample points
    // pt1~pt4 byte 0~3: 0~255
    // pt5     byte 4~5: ushort 0~256
    uchar BSamplePoints[6];
    uchar MSamplePoints[6];
    uchar PwSamplePoints[6];
    uchar CwdSamplePoints[6];
    uchar BGamma;
    uchar MGamma;
    uchar PwGamma;
    uchar CwdGamma;
    bool THIState;
    uchar MGrayCurveIndex;
    uchar PwGrayCurveIndex;
    uchar CwdGrayCurveIndex;
    uchar CfContrast;
    uchar PdContrast;
    uchar DpdContrast;

    uchar DutyRatioOfTransmit3; //发射3占空比
    uchar PulseNumOfTransmit3;  //发射3脉冲数
    uchar HighFreqOfTransmit3;  //发射3高低频标识
    uchar FrequencyOfTransmit3; //发射3频率
    uchar FocusNumOfTransmit3;  //发射3焦点数
    uchar FilterCoefOfTransmit3;
    uchar FocusPos1OfTransmit3; //发射3焦点位置1
    uchar FocusPos2OfTransmit3; //发射3焦点位置2
    uchar FocusPos3OfTransmit3; //发射3焦点位置3
    uchar FocusPos4OfTransmit3; //发射3焦点位置4

    uchar DutyRatioOfTransmit4; //发射4占空比
    uchar PulseNumOfTransmit4;  //发射4脉冲数
    uchar HighFreqOfTransmit4;  //发射4高低频标识
    uchar FrequencyOfTransmit4; //发射4频率
    uchar FocusNumOfTransmit4;  //发射4焦点数
    uchar FilterCoefOfTransmit4;
    uchar FocusPos1OfTransmit4; //发射4焦点位置1
    uchar FocusPos2OfTransmit4; //发射4焦点位置2
    uchar FocusPos3OfTransmit4; //发射4焦点位置3
    uchar FocusPos4OfTransmit4; //发射4焦点位置4

    uchar ThreeSync;

    int CFVelocityThreshold;
    int ColorRegionThreshold;

    /// <summary>
    /// Scan mode
    /// daixiao, 2010-11-12
    /// </summary>
    SystemScanMode ScanMode;
    /// <summary>
    /// 血流显示模式
    /// </summary>
    ColorImageModeType ColorImageMode;
    /// <summary>
    /// 偏转角度
    /// </summary>
    int SteeringAngle;
    /// <summary>
    /// 深/浅
    /// </summary>
    bool Deep;
    /// <summary>
    /// D速度 0~2
    /// </summary>
    int DVelocity;
    /// <summary>
    /// 频谱多普勒基线0~6
    /// </summary>
    int BaseLine;
    int BaseLineCWD;
    /// <summary>
    /// 血流基线0~6
    /// </summary>
    int BaseLineColor;
    /// <summary>
    /// 低速血流/高速血流
    /// </summary>
    uchar LowVelocityBlood;
    /// <summary>
    /// 频谱
    /// </summary>
    bool FreqSpectrum;
    bool CWDUpdate;
    /// <summary>
    /// 频谱增益
    /// </summary>
    int GainDop;
    int GainCWD;
    /// <summary>
    /// 血流增益
    /// </summary>
    int GainColor;
    /// <summary>
    /// Power Doppler Gain
    /// </summary>
    uchar PDGain;
    uchar DPDGain;
    uchar Reserve8[6];

    /// <summary>
    /// 多普勒采样率
    /// </summary>
    int SampleRateDop;
    /// <summary>
    /// D模式多普勒采样率
    /// </summary>
    int DSampleRateDop;
    /// <summary>
    /// PD mode doppler sample rate
    /// daixiao, 2010-11-11
    /// </summary>
    uchar PDSampleRateDop;
    uchar DPDSampleRateDop;
    uchar CWDSampleRate;
    uchar Trapezoid;
    uchar Reserve9[8];
    /// <summary>
    /// 积累次数
    /// </summary>
    int AccCount;
    /// <summary>
    /// 多普勒积累次数
    /// </summary>
    int AccCountDop;
    /// <summary>
    /// 壁滤波D
    /// </summary>
    int WallFiltterDop;
    /// <summary>
    /// 壁滤波C
    /// </summary>
    int WallFiltterColor;
    uchar WallFilterPD;
    uchar WallFilterDPD;
    uchar WallFilterCWD;
    uchar Reserve10[9];
    /// <summary>
    /// 壁墙门槛
    /// </summary>
    int WallThreshold;
    /// <summary>
    /// 采样容积
    /// </summary>
    int SampleVolume;
    float SampleVolumeMM; //毫米数
    /// <summary>
    /// 帧相关C
    /// </summary>
    int FrameAvgColor;
    uchar FrameAvgPD;
    uchar FrameAvgDPD;
    uchar Reserve11[6];
    /// <summary>
    /// D扫描线
    /// </summary>
    int DScanLine;
    /// <summary>
    /// 血流采样框起始线号
    /// </summary>
    int StartLineColor;
    /// <summary>
    /// 血流采样框终止线号
    /// </summary>
    int StopLineColor;
    /// <summary>
    /// 血流采样框上边界
    /// </summary>
    int TopBorderColor;
    /// <summary>
    /// 血流采样框下边界
    /// </summary>
    int BottomBorderColor;
    /// <summary>
    /// 多普勒采样深度
    /// </summary>
    int SampleDepthDop;
    /// <summary>
    /// 多普勒血流偏转角度
    /// </summary>
    int DopplerTheta;
    int CWDDopplerTheta;
    /// <summary>
    /// 增强血流颜色的log参数
    /// </summary>
    int ColorEdgeEnhance;

    /// <summary>
    /// spatial compounding
    /// add by daixiao, 2010-11-15
    /// </summary>
    bool OmniBeam;
    /// <summary>
    /// Sunshine 新控制表
    /// 彩色模式下高低密度
    /// </summary>
    uchar ColorLineDensity;
    uchar PDLineDensity;
    uchar DPDLineDensity;
    uchar Reserve12[13]; // 9 + 4使用uchar，由于CPU对界，这样可以节省出4个字节，必须 +4
    bool ColorInvertState;
    bool SpectralInvertState; // PW翻转
    bool CWDInvertState;
    uchar PatientSize;
    uchar DModeSweepSpeed;
    uchar CWDModeSweepSpeed;
    uchar MModeSweepSpeed;
    uchar Reserve13[12];
    short InvertLeftRight;
    short InvertUpDown;
    uchar TransferSignal;
    uchar Reserve14[3];

    int RoiMidLine;
    int RoiHalfLines;
    float RoiMidDepthMM;
    float RoiHalfDepthMM;

    uchar BColorMapIndex;
    uchar CfColorMapIndex;
    uchar PdColorMapIndex;
    uchar DpdColorMapIndex;
    uchar PwColorMapIndex;
    uchar MColorMapIndex;
    uchar CwdColorMapIndex;
    uchar GrayCurveIndex;
    uchar VarColorMapIndex;

    uchar CVLT;
    uchar TNR;
    uchar CHET;
    uchar CET;
    uchar CVRT;
    uchar MB; // 多波束模式
    uchar Alpha;
    uchar Beta;

    float m_DopplerStartDepth;
    int AcousticPowerDop;
    int SR;

    unsigned char Reserve[888 - 844];

    unsigned short Check;
    unsigned short Version;

    QList<QPoint> samplePoints2List(uchar bPoints[6]);

    //    void list2SamplePoints(uchar bPoints[6], const QList<QPoint>& points);

    ControlTablePara(int args = 0);
};

struct FreqCommonSetting
{
    uchar UniformityTgc[16];
    uchar AnalogTgc[8];
    uchar LogCompression;
    uchar MFC;
    uchar DYF;
    uchar HF_Alpha;
    uchar IIR_ON;
    uchar HF;
    signed char DynamicOffsetOfCPD; // scpd
    signed char DynamicOffsetOfSRA; // fcpd

    uchar FilterCpd;
    uchar Reserve[64 - 32 - 1];

    FreqCommonSetting();
};

struct ParasOfTransmit
{
    uchar DutyRatio; //发射1占空比
    uchar PulseNum;  //发射1脉冲数 1~4 控制表下发1~3、0
    uchar HighFreq;  //发射1高低频标识
    uchar Frequency; //发射1频率
    uchar FocusNum;  //发射1焦点数
    uchar FilterCoef;
    uchar FocusPos1; //发射1焦点位置1
    uchar FocusPos2; //发射1焦点位置2
    uchar FocusPos3; //发射1焦点位置3
    uchar FocusPos4; //发射1焦点位置4
    uchar Reserve[64 - 10];

    ParasOfTransmit();
};

//动态范围相关参数，每种频率，每个DynamicRange都有一组数据，
//一共16档动态范围，就有16组数据
struct DynamicParas
{
    uchar DynStartDepth; // CM
    uchar DynStartDB;    // Index 0~15
    uchar DynEndDepth;   // CM
    uchar DynEndDB;      // Index 0~15
    uchar Reserve[8 - 4];

    DynamicParas();
};

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT FreqSetting
{
    int Index;
    FreqCommonSetting Common;
    ParasOfTransmit NullCompounding[1];
    ParasOfTransmit FreqCompounding[2];
    ParasOfTransmit SpaceCompounding[3]; // scpd == 2
    DynamicParas Dynamic[16];
    ParasOfTransmit SpaceCompounding1[2]; // scpd == 1
    uchar Reserve[1024 - 708];

    FreqSetting();
};

} // namespace ParasStruct
/*!
 * \brief 检查部位
 */
enum ExamPart
{
    Normal,
    GYN,
    OB,
    Cardiac,
    URO,
    Pediatrics, //儿科
    SmallParts, //小器官
    Canine,
    Feline,
    Equine,
    Bovine,
    Ovine,
    Vessel,
    EXAM_PART_COUNT
};

/*!
 * \brief 管理预设值的结构，用于和preset.ini一起使用
 */
struct Preset
{
    /*!
     * 名称
     */
    QString name;
    /*!
     * 用于哪种检查
     */
    ExamPart examPart;
    /*!
     * 用于哪种检查
     */
    QString examTypeName;
    /*!
     * 器官名称
     */
    QString organ;
    /*!
     * 是否为默认预设值
     */
    bool isDefault;
    /*!
     * 对应ini文件中，某个预设值的标签名
     */
    QString labelName;
    /*!
     * 预设值对应的存放参数的二进制文件名
     */
    QString fileName;
    /*!
     * 存放参数结构，二进制文件，就是将此结构写到文件中
     */
    ParasStruct::ControlTablePara& paraSetting();
    void save(ParasStruct::ControlTablePara* para);
    Preset();
    ~Preset();

private:
    ParasStruct::ControlTablePara* m_ParaSetting;
};

/*!
 * 存储文件的类型
 */
enum StoreType
{
    /*!
     * 单帧文件，后缀.img
     */
    IMAGE,
    /*!
     * 电影文件，后缀.cin
     */
    CINE,
    /*!
     * 截屏文件，后缀.png
     */
    SCREEN,
    /*!
     * 8300电影文件
     */
    CINE_8300,
    /*!
     * 总的文件类型的数量
     */
    STORE_TYPE_COUNT
};

enum CusotmPropertyType
{
    Strtech = 1025,
    Spacing,
    Margin,
    RowStrtech,
    ColStrtech,
    HSpacing,
    VSpacing,
    RowAndColumn,
    Orientation,
    Location,
    DrawBorder,
    ToolWidgetType,
    MenuButtonType,
    Alignment,
    ThumbnailSize,
    ThumbnailPos
};

struct RecallParas
{
    QStringList dirs;
    QString selectFile;
};

struct PropertyInfo
{
    QString m_PropertyName;
    QString m_PropertyType;
    QVariant m_Property;
};

struct FreeHand3DProperty
{
    uint m_Width;
    uint m_Height;
    uint m_Depth;
    float m_XSpacing;
    float m_YSpacing;
    float m_ZSpacing;
};

struct WidgetPropertyInfos
{
    QString m_ClassName;
    QString m_OwnerName;
    QList<PropertyInfo> m_Propretys;
    QList<PropertyInfo> m_CustomPropretys;
    QList<WidgetPropertyInfos> m_ChildInfo;
};

typedef QList<WidgetPropertyInfos> DomainPropertyInfos;
typedef QHash<QString, DomainPropertyInfos> DomainPropertyInfosContainer;

struct OrientationItemInfo
{
    QString m_ClassName;
    QString m_ObjectName;
    int row_h;
    int column_h;
    int rowSpan_h;
    int columnSpan_h;
    int row_v;
    int column_v;
    int rowSpan_v;
    int columnSpan_v;
    int row;
    int column;
    int rowSpan;
    int columnSpan;
};

struct OrientationConfigItem
{
    QString objectName;
    QList<OrientationItemInfo> m_Infos;
};

struct OrientationConfigOnePage
{
    QString m_ClassName;
    OrientationConfigItem m_Config;
};

typedef QList<OrientationConfigOnePage> OrientationPositionModel;

struct FunctionButtonsModel
{
    QString name;
    QString showText;
    QString iconPath;
    QString hoverIconPath;
    int iconWidth;
    int iconHeight;
    QString event;
    bool isFirst;
    bool isLast;
    QString target;
    bool isMode;
    bool isIconButton;
    bool showInRight;
    int pageIndex;
    int align;
    QString objectName;
    QString disabledIconPath;
    bool checkable;
};
Q_DECLARE_METATYPE(FunctionButtonsModel)

namespace MouseMoveEnum
{
enum MouseMove
{
    MouseNone = 0,
    Up,
    Down,
    Left,
    Right,
};

enum MouseState
{
    Move = 0,
    Change,
};
} // namespace MouseMoveEnum

namespace RawDataTypeEnum
{
enum RawDataType
{
    Bits8,
    Bits7
};
}

namespace ImageOptimizationAlgEnum
{
enum ImageOptimizationAlg
{
    Alg_None,
    Alg_iImage,
    Alg_CVIE,
    Alg_Max
};
}

/**
 * @brief AreaResult的类型
 */
typedef enum
{
    AreaResult_TypeGstStreamB = 0,
    AreaResult_TypeGstStreamC,
    AreaResult_TypeOriginal,
    AreaResult_TypeNULL,
    AreaResult_TypeC, // 血流或者类似血流的处理
    AreaResult_TypePanZoom,
    AreaResult_TypePanZoomInner,
    AreaResult_TypeZoomInner,
    AreaResult_TypeZoomOutter,
    AreaResult_TypeZoomSelectWidget,
} AreaResultType;

typedef enum
{
    FPGAPlatformType_Xilinx = 0,
    FPGAPlatformType_Altera
} FPGAPlatformType;

typedef enum
{
    CinePlay_UnKnow = 0,
    CinePlay_X8 = 1,
    CinePlay_X4 = 2,
    CinePlay_X2 = 4,
    CinePlay_X1 = 8,
    CinePlay_X_1_2 = 16,
    CinePlay_X_1_4 = 32,
    CinePlay_X_1_8 = 64
} CinePlaySpeed;

namespace BodyMarkPosEnum
{
/**
 * @brief The BodyMarkId enum
 * save bodymark pos
 * 1.Single B, BBC
 * 2.B+M (up down layout), B+PW
 * 3.2B (B0, B1 pos)
 * 4.4B (B0, B1, B2, B3 pos)
 * 5.B+M (left right layout)
 */
enum BodyMarkId
{
    bodyMarkIdB = 0, // B Mode(C Mode), BCB Mode
    bodyMarkIdBD,    // BD, UDBM
    bodyMarkId2B0,
    bodyMarkId2B1,
    bodyMarkIdLRBM, // LRBM
    bodyMarkId4B0,
    bodyMarkId4B1,
    bodyMarkId4B2,
    bodyMarkId4B3,
    bodyMarkIdCount
};
} // namespace BodyMarkPosEnum

namespace ScpdValueEnum
{
/**
 * 此处定义的是compound功能下，Scpd参数在软件系统里面的值
 */
enum ScpdGearValue
{
    Scpd0 = 0,
    Scpd1 = 1,
    Scpd2 = 2,
    Scpd3 = 3,
    Scpd4 = 4,
    Scpd5 = 5,
    Scpd6 = 6,
    Scpd7 = 7
};

/**
 * 此处定义的是compound功能控制表参数scpd实际下发给FPGA的值，
 * 因为3、4、5、6档位是后来扩充的，所以值的递增并没有规律
 */
enum ScpdControlTableValue
{
    Scpd0_CTValue = 0,
    Scpd1_CTValue = 1,
    Scpd2_CTValue = 2,
    Scpd3_CTValue = 4,
    Scpd4_CTValue = 6,
    Scpd5_CTValue = 5,
    Scpd6_CTValue = 7,
    Scpd7_CTValue = 4,
    ScpdCount = 8
};
} // namespace ScpdValueEnum

// 板卡组合数据结构
struct BoardCombData
{
    unsigned short PCBVersion : 5; // 0-4位：PCB版本号
    unsigned short PCBID : 3;      // 5-7位：PCB ID号
    unsigned short PRB_CH_SET : 2; // 8-9位：PRB_CH_SET
    unsigned short PRB_NUM : 2;    // 10-11位：PRB_NUM
    unsigned short L_H_PRB : 1;    // 12位：L_H_PRB
    unsigned short reserved : 3;   // 13-15位：保留位
};

// FPGA固件信息枚举
enum class FpgaFirmWareType
{
    ECO = 0x80, // 0x80 - ECO固件
    EBIT = 0x81 // 0x81 - EBIT固件
};

// 主板类型枚举
enum class MainBoardType
{
    CH16_BOARD = 0, // "000" - 16CH 主板
    CH32_BOARD = 1, // "001" - 32CH 主板
    CH64_BOARD = 3, // "011" - 64CH 主板
};

// 探头板类型枚举
enum class ProbeBoardType
{
    NO_PROBE_BOARD = 0,    // "00" - 无探头板
    ONE_PROBE_SOCKET = 1,  // "01" - 1插座
    TWO_PROBE_SOCKET = 2,  // "10" - 2插座
    THREE_PROBE_SOCKET = 3 // "11" - 3插座
};

// 板卡组合类型枚举
enum class BoardCombType
{
    CH16_3SOCKET_80Elem = 0,                 // L_H_PRB=0 PRB_CH_SET=00 -> 16CH+3插+80基元
    CH32_3SOCKET_80Elem = 1,                 // L_H_PRB=0 PRB_CH_SET=01 -> 32CH+3插+80基元
    CH32_2SOCKET_80Elem_1SOCKET_128Elem = 2, // L_H_PRB=0 PRB_CH_SET=10 -> 32CH+2插/80基元+1插/128基元
    CH64_3SOCKET_128Elem_156PIN = 3,         // L_H_PRB=0 PRB_CH_SET=11 -> 64CH+3插/128基元/156PIN
    CH64_3SOCKET_128Elem_260PIN = 4,         // L_H_PRB=1 PRB_CH_SET=00 -> 64CH+3插/128基元/260PIN
    CH64_3SOCKET_128Elem_160PIN = 5,         // L_H_PRB=1 PRB_CH_SET=01 -> 64CH+3插/128基元/160PIN
};

#endif // INFOSTRUCT_H
