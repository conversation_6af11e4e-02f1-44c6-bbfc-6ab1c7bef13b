#ifndef TOOLNAMES_H
#define TOOLNAMES_H
#include "usfcommoncoreutilitymodel_global.h"

#include <QString>

class USF_COMMON_CORE_UTILITYMODEL_EXPORT ToolNames
{
public:
    static QString iImageStr;
    static QString MultiMenuStr;
    static QString FlowDetectStr;
    static QString PostProcessStr;
    static QString AdvanceStr;
    static QString TrapezoidalModeGeneralStr;
    static QString MenuStr;

    static QString DirectionMenuStr;

    static QString PRFStr;
    static QString HighDensityStr;
    static QString ExitFromUtilityStr;
    static QString TwoDMapStr;
    static QString ColorMapStr;
    static QString BaseLineStr;
    static QString ColorInvertStr;
    static QString MBStr;
    static QString SlideShowStr;
    static QString BiopsyStr;
    static QString CenterLineStr;
    static QString TDIBMenuShowStr;
    static QString BRulerStr;
    static QString WallFilterStr;
    static QString ExitFromPostProcessStr;
    static QString InvertStr;
    static QString ChromeStr;
    static QString UtilityStr;
    static QString SonoDiaphStr;
    static QString SonoThyroidStr;
    static QString SonoIMTStr;
    static QString EditPostProcessStr;
    static QString ImageRotateStr;
    static QString Up1Str;
    static QString Down1Str;
    static QString Click1Str;
    static QString Up2Str;
    static QString Down2Str;
    static QString Click2Str;
    static QString Up3Str;
    static QString Down3Str;
    static QString Click3Str;
    static QString Up4Str;
    static QString Down4Str;
    static QString Click4Str;
    static QString Up5Str;
    static QString Down5Str;
    static QString Click5Str;
    static QString Up6Str;
    static QString Down6Str;
    static QString Click6Str;
    static QString PersistenceStr;
    static QString GammaStr;
    static QString DensityStr;
    static QString SRAStr;
    static QString SteerStr;
    static QString DynamicRangeStr;
    static QString SmoothStr;
    static QString FrequencyStr;
    static QString FrameAvgStr;
    static QString FocusPosBStr;
    static QString CompoundStr;
    static QString FocusNumBStr;
    static QString AcousticPowerBStr;
    static QString ScanWidthStr;
    static QString SpeedStr;
    static QString DemoImageStr;
    static QString TgcStr;
    static QString SetupStr;
    static QString AIOStr;
    static QString ThiStr;
    static QString LRStr;
    static QString UDStr;
    static QString RotationStr;
    static QString ExitStr;
    static QString ArchiveExitStr;
    static QString ArchiveEnterStr;
    static QString BrowseExitStr;
    static QString SetupExitStr;
    static QString ProbeExitStr;
    static QString PatientExitStr;
    static QString ReportExitStr;
    static QString CQYZStr;
    static QString DepthFocusStr;
    static QString FocusStr;
    static QString FreezeStr;
    static QString ChangeStr;
    static QString PostChangeStr;
    static QString BottomMenuChangeStr;
    static QString StorageTipStr;
    static QString StoreImageStr;
    static QString StoreFourDImageStr;
    static QString StoreFourDCineStr;
    static QString StoreCineStr;
    static QString StoreImageToUDiskStr;
    static QString StoreCineToUDiskStr;
    static QString LoadStr;
    static QString FourDLoadStr;
    static QString RoiStr;
    static QString BMouseStr;
    static QString MLineStr;
    static QString BCDMouseStr;
    static QString BCCWPreMouseStr;
    static QString BCDPreMouseStr;
    static QString BDMouseStr;
    static QString BDPWMouseStr;
    static QString BCDPWMouseStr;
    static QString BDPreMouseStr;
    static QString BCWPreMouseStr;
    static QString BMPreMouseStr;
    static QString DMouseStr;
    static QString ZoomStateStr;
    static QString ZoomAddStr;
    static QString ZoomDecStr;
    static QString ZoomMouseStr;
    static QString ZoomOnMouseStr;
    static QString PanZoomMouseStr;
    static QString ZoomOnCloseStr;
    static QString MeasureMouseStr;
    static QString CommentMouseStr;
    static QString BodyMarkMouseStr;
    static QString ArrowMouseStr;
    static QString EndStr;
    static QString ClearDeleteGlyphsStr;
    static QString ClearMeasurementDeleteStr;
    static QString CinePlayStr;
    static QString CineNavigateStr;
    static QString RotateBodyMarkStr;
    static QString BodyMarkSelectorStr;
    static QString BodyMarkPageChangerStr;
    static QString ExamModeChangeStr;
    static QString ExamModeRenameStr;
    static QString ExamModeLoadStr;
    static QString ExamModeSaveStr;
    static QString ExamModeSaveAsStr;
    static QString ShutDownStr;
    static QString MeasurementChangeStr;
    static QString PresetModeStr;
    static QString ElementTestStr;
    static QString CommentMenuControlStr;
    static QString CommentMenuPageChangeStr;
    static QString CommentChangeStr;
    static QString QuickCommentEditStr;
    static QString QuickCommentIndexChangeStr;
    static QString QuickCommentInputStr;
    static QString BodyMarkChangeStr;
    static QString CommentFontSizeChangeStr;
    static QString CommentFontLoadDefaultStr;
    static QString CommentHomeSaveStr;
    static QString CommentHomeLoadStr;
    static QString ChangeMeasResultViewMovingStateStr;
    static QString ResetMeasResultPosStr;
    static QString VideoPrintStr;
    static QString UsbImageAreaVideoPrintStr;
    static QString UsbOtherAreaVideoPrintStr;
    static QString UsbReportAreaVideoPrintStr;
    static QString PCPrintStr;
    static QString PCPrintFromScreenStr;
    static QString PCPrintFromWindowStr;
    static QString CommentDeleteStr;
    static QString BodyMarkDeleteStr;
    static QString DeleteGlyphsStr;
    static QString ArrowDeleteLastOneStr;
    static QString CommentDeleteLastOneStr;
    static QString BodyMarkDeleteLastOneStr;
    static QString MeasurementDeleteStr;
    static QString MeasurementDeleteLastOneStr;
    static QString RotateArrowStr;
    static QString ArrowDeleteStr;
    static QString ArrowScaleStr;
    static QString ArrowChangeStr;
    static QString CinePlaySpeedAdjustStr;
    static QString FreezeImageRemoveStr;
    static QString FreezeImageSendStr;
    static QString FreezeImagePrevPageStr;
    static QString FreezeImageNextPageStr;
    static QString FreezeImageGeneralPageStr;
    static QString ResetBufferIndexStr;
    static QString StartBufferIndexStr;
    static QString BufferIndexStartPosStr;
    static QString BufferIndexEndPosStr;
    static QString EndBufferIndexStr;
    static QString BodyMarkSelectGeneralStr;
    static QString BodyMarkSelectPrevStr;
    static QString BodyMarkSelectNextStr;
    static QString MoveResetMeasResultPosStr;
    static QString ToggleMoveResetMeasResultPosStr;
    static QString SaveLoadCommentPosStr;
    static QString ToggleSaveLoadCommentPosStr;
    static QString StaticLabelDistanceStr;
    static QString StaticLabelAreaStr;
    static QString StaticLabelVolumeStr;
    static QString DistanceMeasureStr;
    static QString TraceMeasureStr;
    static QString DTraceAreaStr;
    static QString VolumeMeasureStr;
    static QString MeasureResultFontSizeStr;
    static QString MeasureResultFontLoadDefaultSizeStr;
    static QString LCDBrightnessStr;
    static QString RotateMeasureStr;
    static QString AngleZoomEnterMeasureStr;
    static QString GainStr;
    static QString BModeStr;
    static QString TwoBModeStr;
    static QString FourBModeStr;
    static QString ArrowScaleLoadDefaultStr;
    static QString QuickAngleStr;
    static QString LeftMenuStr;
    static QString RightMenuStr;
    static QString LeftAdjustmentStr;
    static QString MenuAngleStr;
    static QString FreqGeneralStr;
    static QString GainGeneralStr;
    static QString BGainGeneralStr;
    static QString CGainGeneralStr;
    static QString DGainGeneralStr;
    static QString MGainGeneralStr;
    static QString FourDGainGeneralStr;
    static QString UDGeneralStr;
    static QString AngleZoomRotateGeneralStr;
    static QString AngleZoomGeneralStr;
    static QString QBeamStr;
    static QString XContrastStr;
    static QString QFlowStr;
    static QString ImageZoomInStr;
    static QString MenuBottomStr;
    static QString DModeStr;
    static QString AngleChangeStr;
    static QString DopplerAngleStr;
    static QString ActiveBChangeStr;
    static QString SteeringAngleStr;
    static QString KeyStr;
    static QString PKeyStr;
    static QString UserKeyStr;
    static QString FKeyStr;
    static QString MeasGSStr;
    static QString MeasCRLStr;
    static QString MeasBPDStr;
    static QString MeasHCStr;
    static QString MeasACStr;
    static QString MeasFLStr;
    static QString MeasNTStr;
    static QString MeasAFIStr;
    static QString MeasHeartRateStr;
    static QString MeasHIPAngleStr;
    static QString RTIMTStr;
    static QString ChromaStr;
    static QString KeyStorageStr;
    static QString KeyMovieStr;
    static QString KeyPrint1Str;
    static QString KeyPrint2Str;
    static QString KeyFootSW1Str;
    static QString KeyFootSW2Str;
    static QString MeasureMenuControlStr;
    static QString ScreenThumbnailExitStr;
    static QString RealTimeStoreCineStr;
    static QString EditExamEndStr;
    static QString EditExamTipStr;
    static QString PresetLoadStr;
    static QString PDModeStr;
    static QString DPDModeStr;
    static QString VolumeControlStr;
    static QString KeyBoardLightEnStr;
    static QString KeyBoardLightLevelStr;
    static QString ArrowStr;
    static QString CWDSampleRateStr;
    static QString ElastoGraphyStr;
    static QString ElastoExitStr;
    static QString ElastoMouseStr;
    static QString FreeMLineStr;
    static QString FreeMModeStr;
    static QString FreeMQuickAngleStr;
    static QString ExitFreeMStr;
    static QString StressEchoEnStr;
    static QString StressEchoTemplateStr;
    static QString StressEchoT1ControlStr;
    static QString StressEchoT2ControlStr;
    static QString StressEchoAnalyzeStr;
    static QString CinePlayMouseStr;
    static QString FourdRoiStr;
    static QString FourDMouseActionChangedStr;
    static QString FourDCurvedLineSlopeChangedStr;
    static QString FourDCurvedLineCtrlPointMoveStr;
    static QString FourDSplitScreenStr;
    static QString FourDReviewLoadStr;
    static QString FourDEventProcessStr;
    static QString FourDiImageParasStr;
    static QString FourDZoomStr;
    static QString FourDTransformModeChangedStr;
    static QString FourDTransformStr;
    static QString FourDCurveClippingStr;
    static QString FourDMouseMoveStr;
    static QString FourDEasyViewStr;
    static QString FreeHand3DMouseStr;
    static QString FreeHand3DStr;
    static QString FreeHand3DZoomStr;
    static QString FreeHand3DResetStr;
    static QString PostGainGeneralStr;
    static QString WorklistStr;
    static QString CurvedPanoramicStr;
    static QString CurvedPanoramicExitStr;
    static QString CurvedPanoramicExitInCallBackStr;
    static QString DepthStr;
    static QString ImageTypeChangeStr;
    static QString FullScreenStr;
    static QString FourDResetStr;
    static QString FourDStateStr;
    static QString FourDLiveStr;
    static QString FourDExitStr;
    static QString GrabScreenStr;
    static QString GrabScreenVideoStr;
    static QString SimulateTouchStr;
    static QString ScreenoOrientationStr;
    static QString DebuggerViewStr;
    static QString EnableBlockDataRecordStr;
    static QString QuickSaveCTableAndBlockDataStr;

    static QString ShowLeftAndBottomMenuWidgetStr;
    static QString HideLeftAndBottomMenuWidgetStr;
    static QString FullScreenEventStr;

    static QString UDLightStr;

    static QString BDynamicRangeGeneralStr;

    static QString NULLStr;

    static QString SamplingValveStr;
    static QString ECGEnStr;
    static QString InternetStr;

    static QString IsEnableAdjustROIStr;
    static QString CWStr;
    static QString SonoHelpStr;

    static QString SonoNeedleExitStr;
    static QString MVIShowStr;
    static QString BaseLineMVIStr;
    static QString BiopsyMenuStr;
    static QString BiopsyChooseStr;
    static QString BiopsyVerifyStr;
    static QString BiopsySaveStr;
    static QString BiopsyRestoreStr;
    static QString BiopsyAngleStr;
    static QString BiopsyXPosMMStr;
    static QString BiopsyExitStr;
    static QString BiopsyCancelStr;
    static QString SonoNerveStr;
    static QString SonoNerveQuitStr;
    static QString SonoNerveScalenusBPStr;
    static QString SonoNerveSupraclavicularBPStr;
    static QString SonoNerveMedianBPStr;
    static QString SonoNerveQuitFromBModeStr;
    static QString StressEchoStr;
    static QString CallbackRTMeasureContollerStr;
    static QString SonoCardiacStr;
    static QString SonoCardiacSectionStr;
    static QString SonoCardiacSecImageStr;
    static QString SonoCardiacSecRecognizeStr;
    static QString SonoCardiacSecRatingStr;
    static QString SonoCardiacExitStr;
    static QString EndCurrentStateStr;
    static QString CallbackRTMeasureCineStr;
    static QString OpenProbeCodeBurnerStr;
    static QString OpenVirtualProbeCodeStr;
    static QString AcousticPowerBShowStr;
    static QString BicepsLHTendonSAStr;
    static QString BicepsLHTendonLAStr;
    static QString SSPTendonLAStr;
    static QString ISPTendonLAStr;
    static QString SSCTendonLAStr;
    static QString SSCTendonSAStr;
    static QString TMTendonLAStr;
    static QString SonoMSKQuitFromBModeStr;
    static QString SonoMSKQuitStr;
    static QString TestBicepsLHTendonSAStr;
    static QString TestBicepsLHTendonLAStr;
    static QString SonoCarotidGuideStr;
    static QString SonoGuideSectionStr;
    static QString SonoCarotidLRStr;
    static QString SonoCarotidExitStr;
    static QString AutoEFStr;
    static QString AutoEFCurLayoutStr;
    static QString ESFrameStr;
    static QString EDFrameStr;
    static QString AutoEFSingleBStr;
    static QString ClickAutoEFMeasureStr;
    static QString TGCAdjustStr;
    static QString GainMouseStr;
    static QString SonoAAAStr;
    static QString FreezeSonoAAAStr;
    static QString SonoAAAProxStr;
    static QString SonoAAAMidStr;
    static QString SonoAAADistalStr;
    static QString AIStr;
    static QString SonoAAAMessageStr;
    static QString PrintfFunctionTimeStr;
    static QString SonoVFStr;
    static QString AutoBLineStr;
    static QString AddBLineStr;
    static QString DeleteBLineStr;
    static QString ChangeBLineStr;
    static QString SonoVTIStr;
    static QString VirtualVertexTrapezoidalModeStr;
    static QString PDStateStr;
    static QString ElastoShowStr;
};

#endif // TOOLNAMES_H
