#include "imageeventargs.h"
#include <QDataStream>
#include <string.h>
ImageEventArgs::ImageEventArgs()
    : m_pImageData(NULL)
    , m_NeedUpdateForzonIndex(true)
    , m_Width(640)
    , m_Height(512)
    , m_BitCount(8)
    , m_ECGEnd(-1)
    , m_ECGTime(60)
    , m_Index(0)
    , m_ImageType(ImageEventArgs::ImageB)
    , m_ElastoData(NULL)
    , m_FrameIndex(0)
    , m_SystemScanMode(0)
    , m_HRValue(0)
    , m_GlTexture(0)
    , m_NeedFreeze(0)
    , m_CacheIndex(0)
    , m_FrontIndex(0)
    , m_PostRawDataType(0)
    , m_ImageRenderPartition(0)
{
}

ImageEventArgs::~ImageEventArgs()
{
}

void ImageEventArgs::setImageData(uchar* value)
{
    m_pImageData = value;
}

void ImageEventArgs::clear()
{
    if (m_pImageData != NULL)
    {
        memset(m_pImageData, 0, imageSize());
    }
}

bool ImageEventArgs::isNull() const
{
    return (m_pImageData == NULL);
}

unsigned int ImageEventArgs::frontIndex() const
{
    return m_FrontIndex;
}

void ImageEventArgs::setFrontIndex(unsigned int value)
{
    m_FrontIndex = value;
}

unsigned int ImageEventArgs::imageRenderPartition() const
{
    return m_ImageRenderPartition;
}

void ImageEventArgs::setImageRenderPartition(unsigned int value)
{
    m_ImageRenderPartition = value;
}

bool ImageEventArgs::isMeasureImageReady() const
{
    return m_IsMeasureImageReady;
}

void ImageEventArgs::setMeasureImageReady(bool isMeasureImageReady)
{
    m_IsMeasureImageReady = isMeasureImageReady;
}

QDataStream& operator>>(QDataStream& in, ImageEventArgs& v)
{
    if (!v.isNull())
    {
        int readData;
        in >> readData;
        v.setWidth(readData);
        in >> readData;
        v.setHeight(readData);
        in >> readData;
        v.setBitCount(readData);
        in >> readData;
        v.setECGEnd(readData);
        in >> readData;
        v.setECGTime(readData);
        in >> readData;
        v.setIndex(readData);
        in >> readData;
        v.setImageType((ImageEventArgs::ImageType)readData);
        in >> readData;
        v.setFrameIndex(readData);
        in >> readData;
        v.setHRValue(readData);
        v.setGlTexture(0);
        in.readRawData((char*)v.imageData(), v.imageSize());
    }

    return in;
}

QDataStream& operator<<(QDataStream& out, const ImageEventArgs& v)
{
    if (!v.isNull())
    {
        out << v.width();
        out << v.height();
        out << v.bitCount();
        out << v.ecgEnd();
        out << (int)v.ecgTime();
        out << v.index();
        out << v.imageType();
        out << v.frameIndex();
        out << v.hrValue();
        out.writeRawData((const char*)v.imageData(), v.imageSize());
    }

    return out;
}
