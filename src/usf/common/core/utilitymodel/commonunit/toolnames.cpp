#include "toolnames.h"

QString ToolNames::iImageStr = "iImage";
QString ToolNames::MultiMenuStr = "MultiMenu";
QString ToolNames::FlowDetectStr = "FlowDetect";
QString ToolNames::PostProcessStr = "PostProcess";
QString ToolNames::AdvanceStr = "Advance";
QString ToolNames::TrapezoidalModeGeneralStr = "TrapezoidalModeGeneral";
QString ToolNames::MenuStr = "Menu";
QString ToolNames::DirectionMenuStr = "DirectionMenu";
QString ToolNames::PRFStr = "PRF";
QString ToolNames::HighDensityStr = "HighDensity";
QString ToolNames::ExitFromUtilityStr = "ExitFromUtility";
QString ToolNames::TwoDMapStr = "TwoDMap";
QString ToolNames::ColorMapStr = "ColorMap";
QString ToolNames::BaseLineStr = "BaseLine";
QString ToolNames::ColorInvertStr = "ColorInvert";
QString ToolNames::MBStr = "MB";
QString ToolNames::SlideShowStr = "SlideShow";
QString ToolNames::BiopsyStr = "Biopsy";
QString ToolNames::CenterLineStr = "CenterLine";
QString ToolNames::TDIBMenuShowStr = "TDIBMenuShow";
QString ToolNames::BRulerStr = "BRuler";
QString ToolNames::WallFilterStr = "WallFilter";
QString ToolNames::ExitFromPostProcessStr = "ExitFromPostProcess";
QString ToolNames::InvertStr = "Invert";
QString ToolNames::ChromeStr = "Chrome";
QString ToolNames::UtilityStr = "Utility";
QString ToolNames::SonoDiaphStr = "SonoDiaph";
QString ToolNames::SonoThyroidStr = "SonoThyroid";
QString ToolNames::SonoIMTStr = "SonoIMT";
QString ToolNames::EditPostProcessStr = "EditPostProcess";
QString ToolNames::ImageRotateStr = "ImageRotate";
QString ToolNames::Up1Str = "Up1";
QString ToolNames::Down1Str = "Down1";
QString ToolNames::Click1Str = "Click1";
QString ToolNames::Up2Str = "Up2";
QString ToolNames::Down2Str = "Down2";
QString ToolNames::Click2Str = "Click2";
QString ToolNames::Up3Str = "Up3";
QString ToolNames::Down3Str = "Down3";
QString ToolNames::Click3Str = "Click3";
QString ToolNames::Up4Str = "Up4";
QString ToolNames::Down4Str = "Down4";
QString ToolNames::Click4Str = "Click4";
QString ToolNames::Up5Str = "Up5";
QString ToolNames::Down5Str = "Down5";
QString ToolNames::Click5Str = "Click5";
QString ToolNames::Up6Str = "Up6";
QString ToolNames::Down6Str = "Down6";
QString ToolNames::Click6Str = "Click6";
QString ToolNames::PersistenceStr = "Persistence";
QString ToolNames::GammaStr = "Gamma";
QString ToolNames::DensityStr = "Density";
QString ToolNames::SRAStr = "SRA";
QString ToolNames::SteerStr = "Steer";
QString ToolNames::DynamicRangeStr = "DynamicRange";
QString ToolNames::SmoothStr = "Smooth";
QString ToolNames::FrequencyStr = "Frequency";
QString ToolNames::FrameAvgStr = "FrameAvg";
QString ToolNames::FocusPosBStr = "FocusPosB";
QString ToolNames::CompoundStr = "Compound";
QString ToolNames::FocusNumBStr = "FocusNumB";
QString ToolNames::AcousticPowerBStr = "AcousticPowerB";
QString ToolNames::ScanWidthStr = "ScanWidth";
QString ToolNames::SpeedStr = "Speed";
QString ToolNames::DemoImageStr = "DemoImage";
QString ToolNames::TgcStr = "Tgc";
QString ToolNames::SetupStr = "Setup";
QString ToolNames::AIOStr = "AIO";
QString ToolNames::ThiStr = "Thi";
QString ToolNames::LRStr = "Left";
QString ToolNames::UDStr = "Up";
QString ToolNames::RotationStr = "Rotation";
QString ToolNames::ExitStr = "Exit";
QString ToolNames::ArchiveExitStr = "ArchiveExit";
QString ToolNames::ArchiveEnterStr = "Archive";
QString ToolNames::BrowseExitStr = "BrowseExit";
QString ToolNames::SetupExitStr = "SetupExit";
QString ToolNames::ProbeExitStr = "ProbeExit";
QString ToolNames::PatientExitStr = "PatientExit";
QString ToolNames::ReportExitStr = "ReportExit";
QString ToolNames::CQYZStr = "CQYZ";
QString ToolNames::DepthFocusStr = "DepthFocus";
QString ToolNames::FocusStr = "Focus";
QString ToolNames::FreezeStr = "Freeze";
QString ToolNames::ChangeStr = "Change";
QString ToolNames::PostChangeStr = "PostChange";
QString ToolNames::BottomMenuChangeStr = "BottomMenuChange";
QString ToolNames::StorageTipStr = "StorageTip";
QString ToolNames::StoreImageStr = "StoreImage";
QString ToolNames::StoreFourDImageStr = "StoreFourDImage";
QString ToolNames::StoreFourDCineStr = "StoreFourDCine";
QString ToolNames::StoreCineStr = "StoreCine";
QString ToolNames::StoreImageToUDiskStr = "StoreImageToUDisk";
QString ToolNames::StoreCineToUDiskStr = "StoreCineToUDisk";
QString ToolNames::LoadStr = "Load";
QString ToolNames::FourDLoadStr = "FourDLoad";
QString ToolNames::RoiStr = "Roi";
QString ToolNames::BMouseStr = "BMouse";
QString ToolNames::MLineStr = "MLine";
QString ToolNames::BCDMouseStr = "BCDMouse";
QString ToolNames::BCCWPreMouseStr = "BCCWPreMouse";
QString ToolNames::BCDPreMouseStr = "BCDPreMouse";
QString ToolNames::BDMouseStr = "BDMouse";
QString ToolNames::BDPWMouseStr = "BDPWMouse";
QString ToolNames::BCDPWMouseStr = "BCDPWMouse";
QString ToolNames::BDPreMouseStr = "BDPreMouse";
QString ToolNames::BCWPreMouseStr = "BCWPreMouse";
QString ToolNames::BMPreMouseStr = "BMPreMouse";
QString ToolNames::DMouseStr = "DMouse";
QString ToolNames::ZoomStateStr = "ZoomState";
QString ToolNames::ZoomAddStr = "ZoomAdd";
QString ToolNames::ZoomDecStr = "ZoomDec";
QString ToolNames::ZoomMouseStr = "ZoomMouse";
QString ToolNames::ZoomOnMouseStr = "ZoomOnMouse";
QString ToolNames::PanZoomMouseStr = "PanZoomMouse";
QString ToolNames::ZoomOnCloseStr = "ZoomOnClose";
QString ToolNames::MeasureMouseStr = "MeasureMouse";
QString ToolNames::CommentMouseStr = "CommentMouse";
QString ToolNames::BodyMarkMouseStr = "BodyMarkMouse";
QString ToolNames::ArrowMouseStr = "ArrowMouse";
QString ToolNames::EndStr = "End";
QString ToolNames::ClearDeleteGlyphsStr = "ClearDeleteGlyphs";
QString ToolNames::ClearMeasurementDeleteStr = "ClearMeasurementDelete";
QString ToolNames::CinePlayStr = "CinePlay";
QString ToolNames::CineNavigateStr = "CineNavigate";
QString ToolNames::RotateBodyMarkStr = "RotateBodyMark";
QString ToolNames::BodyMarkSelectorStr = "BodyMarkSelector";
QString ToolNames::BodyMarkPageChangerStr = "BodyMarkPageChanger";
QString ToolNames::ExamModeChangeStr = "ExamModeChange";
QString ToolNames::ExamModeRenameStr = "ExamModeRename";
QString ToolNames::ExamModeLoadStr = "ExamModeLoad";
QString ToolNames::ExamModeSaveStr = "ExamModeSave";
QString ToolNames::ExamModeSaveAsStr = "ExamModeSaveAs";
QString ToolNames::ShutDownStr = "ShutDown";
QString ToolNames::MeasurementChangeStr = "MeasurementChange";
QString ToolNames::PresetModeStr = "PresetMode";
QString ToolNames::ElementTestStr = "ElementTest";
QString ToolNames::CommentMenuControlStr = "CommentMenuControl";
QString ToolNames::CommentMenuPageChangeStr = "CommentMenuPageChange";
QString ToolNames::CommentChangeStr = "CommentChange";
QString ToolNames::QuickCommentEditStr = "QuickCommentEdit";
QString ToolNames::QuickCommentIndexChangeStr = "QuickCommentIndexChange";
QString ToolNames::QuickCommentInputStr = "QuickCommentInput";
QString ToolNames::BodyMarkChangeStr = "BodyMarkChange";
QString ToolNames::CommentFontSizeChangeStr = "CommentFontSizeChange";
QString ToolNames::CommentFontLoadDefaultStr = "CommentFontLoadDefault";
QString ToolNames::CommentHomeSaveStr = "CommentHomeSave";
QString ToolNames::CommentHomeLoadStr = "CommentHomeLoad";
QString ToolNames::ChangeMeasResultViewMovingStateStr = "ChangeMeasResultViewMovingState";
QString ToolNames::ResetMeasResultPosStr = "ResetMeasResultPos";
QString ToolNames::VideoPrintStr = "VideoPrint";
QString ToolNames::UsbImageAreaVideoPrintStr = "UsbImageAreaVideoPrint";
QString ToolNames::UsbOtherAreaVideoPrintStr = "UsbOtherAreaVideoPrint";
QString ToolNames::UsbReportAreaVideoPrintStr = "UsbReportAreaVideoPrint";
QString ToolNames::PCPrintStr = "PCPrint";
QString ToolNames::PCPrintFromScreenStr = "PCPrintFromScreen";
QString ToolNames::PCPrintFromWindowStr = "PCPrintFromWindow";
QString ToolNames::CommentDeleteStr = "CommentDelete";
QString ToolNames::BodyMarkDeleteStr = "BodyMarkDelete";
QString ToolNames::DeleteGlyphsStr = "DeleteGlyphs";
QString ToolNames::ArrowDeleteLastOneStr = "ArrowDeleteLastOne";
QString ToolNames::CommentDeleteLastOneStr = "CommentDeleteLastOne";
QString ToolNames::BodyMarkDeleteLastOneStr = "BodyMarkDeleteLastOne";
QString ToolNames::MeasurementDeleteStr = "MeasurementDelete";
QString ToolNames::MeasurementDeleteLastOneStr = "MeasurementDeleteLastOne";
QString ToolNames::RotateArrowStr = "RotateArrow";
QString ToolNames::ArrowDeleteStr = "ArrowDelete";
QString ToolNames::ArrowScaleStr = "ArrowScale";
QString ToolNames::ArrowChangeStr = "ArrowChange";
QString ToolNames::CinePlaySpeedAdjustStr = "CinePlaySpeedAdjust";
QString ToolNames::FreezeImageRemoveStr = "FreezeImageRemove";
QString ToolNames::FreezeImageSendStr = "FreezeImageSend";
QString ToolNames::FreezeImagePrevPageStr = "FreezeImagePrevPage";
QString ToolNames::FreezeImageNextPageStr = "FreezeImageNextPage";
QString ToolNames::FreezeImageGeneralPageStr = "FreezeImageGeneralPage";
QString ToolNames::ResetBufferIndexStr = "ResetBufferIndex";
QString ToolNames::StartBufferIndexStr = "StartBufferIndex";
QString ToolNames::EndBufferIndexStr = "EndBufferIndex";
QString ToolNames::BufferIndexStartPosStr = "BufferIndexStartPos";
QString ToolNames::BufferIndexEndPosStr = "BufferIndexEndPos";
QString ToolNames::BodyMarkSelectGeneralStr = "BodyMarkSelectGeneral";
QString ToolNames::BodyMarkSelectPrevStr = "BodyMarkSelectPrev";
QString ToolNames::BodyMarkSelectNextStr = "BodyMarkSelectNext";
QString ToolNames::MoveResetMeasResultPosStr = "MoveResetMeasResultPos";
QString ToolNames::ToggleMoveResetMeasResultPosStr = "ToggleMoveResetMeasResultPos";
QString ToolNames::SaveLoadCommentPosStr = "SaveLoadCommentPos";
QString ToolNames::ToggleSaveLoadCommentPosStr = "ToggleSaveLoadCommentPos";
QString ToolNames::StaticLabelDistanceStr = "StaticLabelDistance";
QString ToolNames::StaticLabelAreaStr = "StaticLabelArea";
QString ToolNames::StaticLabelVolumeStr = "StaticLabelVolume";
QString ToolNames::DistanceMeasureStr = "DistanceMeasure";
QString ToolNames::TraceMeasureStr = "TraceMeasure";
QString ToolNames::DTraceAreaStr = "DTraceArea";
QString ToolNames::VolumeMeasureStr = "VolumeMeasure";
QString ToolNames::MeasureResultFontSizeStr = "MeasureResultFontSize";
QString ToolNames::MeasureResultFontLoadDefaultSizeStr = "MeasureResultFontLoadDefaultSize";
QString ToolNames::LCDBrightnessStr = "LCDBrightness";
QString ToolNames::RotateMeasureStr = "RotateMeasure";
QString ToolNames::AngleZoomEnterMeasureStr = "AngleZoomEnterMeasure";
QString ToolNames::GainStr = "Gain";
QString ToolNames::BModeStr = "BMode";
QString ToolNames::TwoBModeStr = "TwoBMode";
QString ToolNames::FourBModeStr = "FourBMode";
QString ToolNames::ArrowScaleLoadDefaultStr = "ArrowScaleLoadDefault";
QString ToolNames::QuickAngleStr = "QuickAngle";
QString ToolNames::LeftMenuStr = "LeftMenu";
QString ToolNames::RightMenuStr = "RightMenu";
QString ToolNames::LeftAdjustmentStr = "LeftAdjustment";
QString ToolNames::MenuAngleStr = "MenuAngle";
QString ToolNames::FreqGeneralStr = "FreqGeneral";
QString ToolNames::GainGeneralStr = "GainGeneral";
QString ToolNames::BGainGeneralStr = "BGainGeneral";
QString ToolNames::CGainGeneralStr = "CGainGeneral";
QString ToolNames::DGainGeneralStr = "DGainGeneral";
QString ToolNames::MGainGeneralStr = "MGainGeneral";
QString ToolNames::FourDGainGeneralStr = "FourDGainGeneral";
QString ToolNames::UDGeneralStr = "UDGeneral";
QString ToolNames::AngleZoomRotateGeneralStr = "AngleZoomRotateGeneral";
QString ToolNames::AngleZoomGeneralStr = "AngleZoomGeneral";
QString ToolNames::QBeamStr = "QBeam";
QString ToolNames::XContrastStr = "XContrastValue";
QString ToolNames::QFlowStr = "QFlow";
QString ToolNames::ImageZoomInStr = "ImageZoomIn";
QString ToolNames::MenuBottomStr = "MenuBottom";
QString ToolNames::DModeStr = "DMode";
QString ToolNames::AngleChangeStr = "AngleChange";
QString ToolNames::DopplerAngleStr = "DopplerAngle";
QString ToolNames::ActiveBChangeStr = "ActiveBChange";
QString ToolNames::SteeringAngleStr = "SteeringAngleTool";
QString ToolNames::KeyStr = "Key";
QString ToolNames::PKeyStr = "PKey";
QString ToolNames::UserKeyStr = "UserKey";
QString ToolNames::FKeyStr = "FKey";
QString ToolNames::MeasGSStr = "MeasGS";
QString ToolNames::MeasCRLStr = "MeasCRL";
QString ToolNames::MeasBPDStr = "MeasBPD";
QString ToolNames::MeasHCStr = "MeasHC";
QString ToolNames::MeasACStr = "MeasAC";
QString ToolNames::MeasFLStr = "MeasFL";
QString ToolNames::MeasNTStr = "MeasNT";
QString ToolNames::MeasAFIStr = "MeasAFI";
QString ToolNames::MeasHeartRateStr = "MeasHeartRate";
QString ToolNames::MeasHIPAngleStr = "MeasHIPAngle";
QString ToolNames::RTIMTStr = "RTIMT";
QString ToolNames::ChromaStr = "Chroma";
QString ToolNames::KeyStorageStr = "KeyStorage";
QString ToolNames::KeyMovieStr = "KeyMovie";
QString ToolNames::KeyPrint1Str = "KeyPrint1";
QString ToolNames::KeyPrint2Str = "KeyPrint2";
QString ToolNames::KeyFootSW1Str = "KeyFootSW1";
QString ToolNames::KeyFootSW2Str = "KeyFootSW2";
QString ToolNames::MeasureMenuControlStr = "MeasureMenuControl";
QString ToolNames::ScreenThumbnailExitStr = "ScreenThumbnailExit";
QString ToolNames::RealTimeStoreCineStr = "RealTimeStoreCine";
QString ToolNames::EditExamEndStr = "EditExamEnd";
QString ToolNames::EditExamTipStr = "EditExamTip";
QString ToolNames::PresetLoadStr = "PresetLoad";
QString ToolNames::PDModeStr = "PDMode";
QString ToolNames::DPDModeStr = "DPDMode";
QString ToolNames::VolumeControlStr = "VolumeControl";
QString ToolNames::KeyBoardLightEnStr = "KeyBoardLightEn";
QString ToolNames::KeyBoardLightLevelStr = "KeyBoardLightLevel";
QString ToolNames::ArrowStr = "Arrow";
QString ToolNames::CWDSampleRateStr = "CWDSampleRate";
QString ToolNames::ElastoGraphyStr = "ElastoGraphy";
QString ToolNames::ElastoExitStr = "ElastoExit";
QString ToolNames::ElastoMouseStr = "ElastoMouse";
QString ToolNames::StressEchoEnStr = "StressEchoEn";
QString ToolNames::StressEchoTemplateStr = "StressEchoTemplate";
QString ToolNames::StressEchoT1ControlStr = "StressEchoT1Control";
QString ToolNames::StressEchoT2ControlStr = "StressEchoT2Control";
QString ToolNames::StressEchoAnalyzeStr = "StressEchoAnalyze";
QString ToolNames::FreeMLineStr = "FreeMLine";
QString ToolNames::FreeMModeStr = "FreeMMode";
QString ToolNames::FreeMQuickAngleStr = "FreeMQuickAngle";
QString ToolNames::ExitFreeMStr = "ExitFreeM";
QString ToolNames::CinePlayMouseStr = "CinePlayMouse";
QString ToolNames::FourdRoiStr = "FourdRoi";
QString ToolNames::FourDMouseActionChangedStr = "FourDMouseActionChanged";
QString ToolNames::FourDCurvedLineSlopeChangedStr = "FourDCurvedLineSlopeChanged";
QString ToolNames::FourDCurvedLineCtrlPointMoveStr = "FourDCurvedLineCtrlPointMove";
QString ToolNames::FourDSplitScreenStr = "FourDSplitScreen";
QString ToolNames::FourDReviewLoadStr = "FourDReviewLoad";
QString ToolNames::FourDEventProcessStr = "FourDEventProcess";
QString ToolNames::FourDiImageParasStr = "FourDiImageParas";
QString ToolNames::FourDZoomStr = "FourDZoom";
QString ToolNames::FourDTransformModeChangedStr = "FourDTransformModeChanged";
QString ToolNames::FourDTransformStr = "FourDTransform";
QString ToolNames::FourDCurveClippingStr = "FourDCurveClipping";
QString ToolNames::FourDMouseMoveStr = "FourDMouseMove";
QString ToolNames::FourDEasyViewStr = "FourDEasyView";
QString ToolNames::FreeHand3DMouseStr = "FreeHand3DMouse";
QString ToolNames::FreeHand3DStr = "FreeHand3D";
QString ToolNames::FreeHand3DZoomStr = "FreeHand3DZoom";
QString ToolNames::FreeHand3DResetStr = "FreeHand3DReset";
QString ToolNames::PostGainGeneralStr = "PostGainGeneral";
QString ToolNames::WorklistStr = "Worklist";
QString ToolNames::CurvedPanoramicStr = "CurvedPanoramic";
QString ToolNames::CurvedPanoramicExitStr = "CurvedPanoExit";
QString ToolNames::CurvedPanoramicExitInCallBackStr = "CurvedPanoExitInCallback";
QString ToolNames::DepthStr = "Depth";
QString ToolNames::ImageTypeChangeStr = "ImageTypeChange";
QString ToolNames::FullScreenStr = "FullScreen";
QString ToolNames::FourDResetStr = "FourDReset";
QString ToolNames::FourDStateStr = "FourDState";
QString ToolNames::FourDLiveStr = "FourDLive";
QString ToolNames::FourDExitStr = "FourDExit";
QString ToolNames::GrabScreenStr = "GrabScreen";
QString ToolNames::GrabScreenVideoStr = "GrabScreenVideo";
QString ToolNames::SimulateTouchStr = "SimulateTouch";
QString ToolNames::ScreenoOrientationStr = "ScreenoOrientation";
QString ToolNames::DebuggerViewStr = "DebuggerView";
QString ToolNames::EnableBlockDataRecordStr = "EnableBlockDataRecord";
QString ToolNames::QuickSaveCTableAndBlockDataStr = "QuickSaveCTableAndBlockData";
QString ToolNames::ShowLeftAndBottomMenuWidgetStr = "ShowLeftAndBottomMenuWidget";
QString ToolNames::HideLeftAndBottomMenuWidgetStr = "HideLeftAndBottomMenuWidget";
QString ToolNames::FullScreenEventStr = "FullScreenEvent";

QString ToolNames::UDLightStr = "UDLight";

QString ToolNames::BDynamicRangeGeneralStr = "BDynamicRangeGeneral";

QString ToolNames::NULLStr = "NULL";

QString ToolNames::SamplingValveStr = "SamplingValve";
QString ToolNames::ECGEnStr = "ECGEn";
QString ToolNames::InternetStr = "Internet";

QString ToolNames::IsEnableAdjustROIStr = "IsEnableAdjustROI";
QString ToolNames::CWStr = "CW";
QString ToolNames::SonoHelpStr = "SonoHelp";

QString ToolNames::SonoNeedleExitStr = "SonoNeedleExit";
QString ToolNames::MVIShowStr = "MVIShow";
QString ToolNames::BaseLineMVIStr = "BaseLineMVI";
QString ToolNames::BiopsyMenuStr = "BiopsyMenu";
QString ToolNames::BiopsyChooseStr = "BiopsyChoose";
QString ToolNames::BiopsyVerifyStr = "BiopsyVerify";
QString ToolNames::BiopsySaveStr = "BiopsySave";
QString ToolNames::BiopsyRestoreStr = "BiopsyRestore";
QString ToolNames::BiopsyAngleStr = "BiopsyAngle";
QString ToolNames::BiopsyXPosMMStr = "BiopsyXPosMM";
QString ToolNames::BiopsyExitStr = "BiopsyExit";
QString ToolNames::BiopsyCancelStr = "BiopsyCancel";
QString ToolNames::SonoNerveStr = "SonoNerve";
QString ToolNames::SonoNerveQuitStr = "quitSonoNerve";
QString ToolNames::SonoNerveScalenusBPStr = "ScalenusBP";
QString ToolNames::SonoNerveSupraclavicularBPStr = "SupraclavicularBP";
QString ToolNames::SonoNerveMedianBPStr = "SonoNerveMedianBP";
QString ToolNames::StressEchoStr = "StressEcho";
QString ToolNames::SonoNerveQuitFromBModeStr = "SonoNerveQuitFromBMode";
QString ToolNames::CallbackRTMeasureContollerStr = "CallbackRTMeasureContoller";
QString ToolNames::SonoCardiacStr = "SonoCardiac";
QString ToolNames::SonoCardiacSectionStr = "SonoCardiacSection";
QString ToolNames::SonoCardiacSecImageStr = "SonoCardiacSecImage";
QString ToolNames::SonoCardiacSecRecognizeStr = "SonoCardiacSecRecognize";
QString ToolNames::SonoCardiacSecRatingStr = "SonoCardiacSecRating";
QString ToolNames::SonoCardiacExitStr = "SonoCardiacExit";
QString ToolNames::EndCurrentStateStr = "EndCurrentState";
QString ToolNames::CallbackRTMeasureCineStr = "CallbackRTMeasureCine";
QString ToolNames::OpenProbeCodeBurnerStr = "OpenProbeCodeBurner";
QString ToolNames::OpenVirtualProbeCodeStr = "OpenVirtualProbeCode";
QString ToolNames::AcousticPowerBShowStr = "AcousticPowerBShow";
QString ToolNames::BicepsLHTendonSAStr = "BicepsLHTendonSA";
QString ToolNames::BicepsLHTendonLAStr = "BicepsLHTendonLA";
QString ToolNames::SSPTendonLAStr = "SSPTendonLA";
QString ToolNames::ISPTendonLAStr = "ISPTendonLA";
QString ToolNames::SSCTendonLAStr = "SSCTendonLA";
QString ToolNames::SSCTendonSAStr = "SSCTendonSA";
QString ToolNames::TMTendonLAStr = "TMTendonLA";
QString ToolNames::SonoMSKQuitFromBModeStr = "SonoMSKQuitFromBMode";
QString ToolNames::SonoMSKQuitStr = "quitSonoMSK";
QString ToolNames::SonoCarotidGuideStr = "SonoCarotidGuide";
QString ToolNames::SonoGuideSectionStr = "SonoGuideSection";
QString ToolNames::SonoCarotidLRStr = "SonoGuideStep";
QString ToolNames::SonoCarotidExitStr = "SonoGuideExit";
QString ToolNames::AutoEFStr = "AutoEF";
QString ToolNames::AutoEFCurLayoutStr = "AutoEFCurLayout";
QString ToolNames::ESFrameStr = "ESFrame";
QString ToolNames::EDFrameStr = "EDFrame";
QString ToolNames::AutoEFSingleBStr = "AutoEFSingleB";
QString ToolNames::ClickAutoEFMeasureStr = "ClickAutoEFMeasure";
QString ToolNames::TGCAdjustStr = "TGCAdjust";
QString ToolNames::GainMouseStr = "GainMouse";
QString ToolNames::SonoAAAStr = "SonoAAA";
QString ToolNames::FreezeSonoAAAStr = "FreezeSonoAAA";
QString ToolNames::SonoAAAProxStr = "SonoAAAProx";
QString ToolNames::SonoAAAMidStr = "SonoAAAMid";
QString ToolNames::SonoAAADistalStr = "SonoAAADistal";
QString ToolNames::AIStr = "AI";
QString ToolNames::SonoAAAMessageStr = "SonoAAAMessage";
QString ToolNames::PrintfFunctionTimeStr = "PrintfFunctionTime";
QString ToolNames::SonoVFStr = "SonoVF";
QString ToolNames::AutoBLineStr = "AutoBLine";
QString ToolNames::SonoVTIStr = "SonoVTI";
QString ToolNames::AddBLineStr = "AddBLine";
QString ToolNames::DeleteBLineStr = "DeleteBLine";
QString ToolNames::ChangeBLineStr = "ChangeBLine";
QString ToolNames::VirtualVertexTrapezoidalModeStr = "VirtualVertexTrapezoidalMode";
QString ToolNames::PDStateStr = "PDState";
QString ToolNames::ElastoShowStr = "ElastoShow";
