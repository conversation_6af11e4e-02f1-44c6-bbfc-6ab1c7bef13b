#include "simplemultiparamtrigger.h"
#include "assertlog.h"
#include "logger.h"
#include "parameter.h"
#include "isonoparameters.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, SimpleMultiParamTrigger)

SimpleMultiParamTrigger::SimpleMultiParamTrigger(const ISonoParameters* parameters, QStringList names, QObject* parent,
                                                 bool blockSignalsAfterConstruct, int stableDelay, QString namePrefix)
    : QObject(parent)
    , m_timer(NULL)
    , m_Names(names)
    , m_ISonoParameters(parameters)
{
    Q_UNUSED(blockSignalsAfterConstruct);
    QString name = namePrefix + "_of_0x" + QString::number(quintptr(this));
    setObjectName(name);
    log()->debug("create %1 with stable delay %2", objectName(), stableDelay);

    blockSignals(true);

    setSonoParameters(m_ISonoParameters);

    m_timer = new QTimer(this);
    m_timer->setInterval(stableDelay);
    connect(m_timer, SIGNAL(timeout()), this, SLOT(onTimeout()));
    blockSignals(false);
}

SimpleMultiParamTrigger::~SimpleMultiParamTrigger()
{
    if (m_timer->isActive())
    {
        m_timer->stop();
    }
}

void SimpleMultiParamTrigger::beforeSonoParametersChanged()
{
    blockSignals(true);
    if (m_timer->isActive())
    {
        m_timer->stop();
    }
    if (m_ISonoParameters != NULL)
    {
        foreach (const QString& paramName, m_Names)
        {
            Parameter* param = m_ISonoParameters->parameter(paramName);
            disconnect(param, SIGNAL(valueChanged(const QVariant&)), this, SLOT(onValueChanged()));
        }
    }
}

void SimpleMultiParamTrigger::setSonoParameters(const ISonoParameters* sonoParameters)
{
    blockSignals(false);
    m_ISonoParameters = sonoParameters;
    if (m_ISonoParameters != NULL)
    {
        foreach (QString paramName, m_Names)
        {
            Parameter* param = m_ISonoParameters->parameter(paramName);
            connect(param, SIGNAL(valueChanged(const QVariant&)), this, SLOT(onValueChanged()));
        }
    }
}

void SimpleMultiParamTrigger::setParamNames(QStringList name)
{
    beforeSonoParametersChanged();
    m_Names = name;
    if (m_ISonoParameters != NULL)
    {
        foreach (QString paramName, m_Names)
        {
            Parameter* param = m_ISonoParameters->parameter(paramName);
            connect(param, SIGNAL(valueChanged(const QVariant&)), this, SLOT(onValueChanged()));
        }
    }
    blockSignals(false);
}

void SimpleMultiParamTrigger::clearSonoParameters()
{
    beforeSonoParametersChanged();
    m_ISonoParameters = NULL;
}

void SimpleMultiParamTrigger::onValueChanged()
{
    if (m_timer->isActive())
    {
        m_timer->stop();
    }
    else
    {
        emit oneTriggered();
    }

    //    m_timer->start();
    emit triggerCompleted();
}

void SimpleMultiParamTrigger::onTimeout()
{
    m_timer->stop();
    emit triggerCompleted();
}

DelayParamTrigger::DelayParamTrigger(const ISonoParameters* parameters, QStringList names, QObject* parent,
                                     int stableDelay)
    : SimpleMultiParamTrigger(parameters, names, parent)
    , m_StableDelay(stableDelay)
{
    m_timer->setSingleShot(true);
}

DelayParamTrigger::~DelayParamTrigger()
{
}

void DelayParamTrigger::onValueChanged()
{
    if (!m_timer->isActive())
    {
        emit oneTriggered();
        m_timer->start(m_StableDelay);
    }
}

void DelayParamTrigger::onTimeout()
{
    emit triggerCompleted();
}
