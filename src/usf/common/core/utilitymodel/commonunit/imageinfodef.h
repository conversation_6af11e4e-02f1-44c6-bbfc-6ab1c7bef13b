#ifndef IMAGEINFODEF_H
#define IMAGEINFODEF_H

#ifdef WIN32
#include <Windows.h>
#endif
#include <QGL>
#include <QRectF>
#include "imageeventargs.h"
#include <QVariant>
#include <QMatrix4x4>
#include <QMetaType>
#include "sceneinfodef.h"
#include "usfcommoncoreutilitymodel_global.h"
#include "modelconfig.h"

enum ColorMapSection
{
    ColorMap_Fst_Section,
    ColorMap_Sec_Section
};

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT ImageTransformInfo
{
public:
    ImageTransformInfo()
        : m_IsSupportUDInvert(false)
        , m_IsSupportLRInvert(false)
        , m_IsSupportRotation(false)
        , m_IsSupportZoom(false)
        , m_IsOrthTransfrom(false)
        , m_IsClipping(false)
    {
    }

    ImageTransformInfo(bool isUDInvert, bool isLRInvert, bool isRotation, bool isZoom, bool isOrthTransfrom = false,
                       bool isClipping = false)
        : m_IsSupportUDInvert(isUDInvert)
        , m_IsSupportLRInvert(isLRInvert)
        , m_IsSupportRotation(isRotation)
        , m_IsSupportZoom(isZoom)
        , m_IsOrthTransfrom(isOrthTransfrom)
        , m_IsClipping(isClipping)
    {
    }

public:
    bool m_IsSupportUDInvert;
    bool m_IsSupportLRInvert;
    bool m_IsSupportRotation;
    bool m_IsSupportZoom;
    bool m_IsOrthTransfrom;
    bool m_IsClipping;
};

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT MatrixInfo
{
public:
    QMatrix4x4 m_Matrix;     // 变换矩阵
    QMatrix4x4 m_OrthMatrix; // 观察变换矩阵
    QMatrix4x4 m_FilterMatrix;
};

typedef ImageEventArgs::ImageType ModeImageType;
/**
 * @brief The ModeImageInfo struct 模式图像信息
 */
struct USF_COMMON_CORE_UTILITYMODEL_EXPORT ModeImageInfo
{
public:
    ModeImageInfo()
        : m_ModeType(ImageEventArgs::ImageB)
        , m_ImageIndex(0)
    {
    }

    ModeImageInfo(ModeImageType type, const QRectF& area, int imageIndex = 0)
        : m_ModeType(type)
        , m_Area(area)
        , m_ImageIndex(imageIndex)
    {
    }

    ModeImageInfo(ModeImageType type, const QRectF& area, const ImageTransformInfo& transformInfo, int imageIndex = 0)
        : m_ModeType(type)
        , m_Area(area)
        , m_TransformInfo(transformInfo)
        , m_ImageIndex(imageIndex)
    {
    }

public:
    ModeImageType m_ModeType; // 模型类型
    QRectF m_Area;            // 视口大小
    ImageTransformInfo m_TransformInfo;
    int m_ImageIndex; // 图像序号，支持多个相同图像类型
};

typedef uint32_t GLuint;

/**
 * @brief The TextureInfo struct 纹理信息
 */
struct USF_COMMON_CORE_UTILITYMODEL_EXPORT TextureInfo
{
public:
    TextureInfo()
        : m_Type(0)
        , m_Texture(0)
        , m_GenTexture(0)
        , m_TextureId(0)
        , m_TextureValue(0)
    {
    }
    TextureInfo(int type, GLuint texture, int textureId, int textureValue)
        : m_Type(type)
        , m_Texture(texture)
        , m_GenTexture(0)
        , m_TextureId(textureId)
        , m_TextureValue(textureValue)
    {
    }

public:
    int m_Type;       // 纹理类型
    GLuint m_Texture; // 纹理opengl内置编号，用于初始化纹理
    GLuint m_GenTexture;
    int m_TextureId;    // 纹理id，用于激活纹理
    int m_TextureValue; // 纹理数据序号
};

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT PainterImageData
{
    PainterImageData()
        : m_Width(0)
        , m_Height(0)
        , m_BitsCount(4)
        , m_TextureID(0)
    {
        QSize renderImageSize = ModelConfig::instance().value(ModelConfig::RenderImageSize, QSize(640, 512)).toSize();
        // m_Data 直接采用ZeusContext的内存，不用额外分配
        m_Data = nullptr;
    }
    ~PainterImageData()
    {
    }
    void copyfrom(PainterImageData* other)
    {
        m_Width = other->m_Width;
        m_Height = other->m_Height;
        m_BitsCount = other->m_BitsCount;
        m_TextureID = other->m_TextureID;
        m_Data = other->m_Data;
    }

    int size()
    {
        return m_Width * m_Height * m_BitsCount;
    }
    bool isNull()
    {
        return (m_TextureID == 0);
    }
    int m_Width;
    int m_Height;
    uchar* m_Data; // 用于复制数据
    int m_BitsCount;
    unsigned int m_TextureID;
};

#include <QImage>
class USF_COMMON_CORE_UTILITYMODEL_EXPORT PainterImageModel
{
public:
    PainterImageModel()
        : m_ImageData(new PainterImageData())
    {
    }
    ~PainterImageModel()
    {
        if (NULL != m_ImageData)
        {
            delete m_ImageData;
            m_ImageData = NULL;
        }
    }

    void setData(ImageEventArgs* imageArg)
    {
        if (NULL == imageArg || NULL == m_ImageData)
        {
            return;
        }

        if (imageArg->glTexture() != 0)
        {
            m_ImageData->m_TextureID = imageArg->glTexture();
            return;
        }
        m_ImageData->m_TextureID = 0;

        m_ImageData->m_Height = imageArg->height();
        m_ImageData->m_Width = imageArg->width();
        int size = m_ImageData->m_Width * m_ImageData->m_Height * m_ImageData->m_BitsCount;

        //[Apple][BUG:57430]:【3D
        // UI】项目经理反馈：病例从U盘还原到Apple，再回调，播放电影图像区无图像，这时再点击存图区其他图像或电影，机器重启。
        // 1、解决方案
        //老病历渲染区大小是1192*800，新的是1192*770，PainterImageModel::setData中的memcpy方法，回调老病历时src大于dest的size，内存写越界。
        // memcpy的时候，增加判断。同时load参数的时候，size相关参数使用当前的渲染区尺寸。
        // 2、影响范围:新老病历回调
        // 3、Test Case:新老病历回调
        // destSize的计算参照了 PainterImageData() 中size的计算，两边需要保持一致
        QSize destDataSize = ModelConfig::instance().value(ModelConfig::RenderImageSize, QSize(640, 512)).toSize();
        int destSize = destDataSize.height() * destDataSize.width() * m_ImageData->m_BitsCount;
        m_ImageData->m_Data = imageArg->imageData();
    }

    void copyfrom(PainterImageModel* other)
    {
        m_ImageData->copyfrom(other->m_ImageData);
    }

    PainterImageData* data() const
    {
        return m_ImageData;
    }

private:
    PainterImageData* m_ImageData;
};

#include <QTime>

class USF_COMMON_CORE_UTILITYMODEL_EXPORT RenderTimeCalculator
{
public:
    RenderTimeCalculator()
    {
        reset();
    }
    ~RenderTimeCalculator()
    {
    }
    void reset()
    {
        m_FrameIndex = 0;
        m_Elapsed = 0;
        m_StartTime = QTime::currentTime();
    }

    void start()
    {
        m_StartTime = QTime::currentTime();
        m_FrameIndex++;
    }

    qreal calcAvgelapsed()
    {
        if (m_FrameIndex > 0)
        {
            return (m_Elapsed * 1.0f) / (m_FrameIndex * 1.0f);
        }

        return 0;
    }

    int elapsed()
    {
        QTime stopTime = QTime::currentTime();
        int elapsed = m_StartTime.msecsTo(stopTime);
        m_Elapsed += elapsed;
        return elapsed;
    }

private:
    QTime m_StartTime;
    int m_FrameIndex;
    int m_Elapsed;
};

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT ImageTransformPara
{
    bool isUDInvertEnable;
    bool isLRInvertEnable;
    bool isRotationEnable;
    bool isZoomEnable;
    bool isClipEnable;
};

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT ImageModePara
{
    ModeImageType type;
    int index;
    ImageTransformPara transformPara;
};

inline QDataStream& operator<<(QDataStream& stream, const ImageModeViewItem& item)
{
    stream << item.name;
    stream << (int)item.region;
    stream << (int)item.styleOption.direction;
    stream << item.styleOption.displaySize;
    stream << item.styleOption.regionName;
    stream << (int)item.styleOption.displayAlignment;
    return stream;
}

inline QDataStream& operator>>(QDataStream& stream, ImageModeViewItem& item)
{
    stream >> item.name;
    stream >> *((int*)(&(item.region)));
    stream >> *((int*)(&(item.styleOption.direction)));
    stream >> item.styleOption.displaySize;
    stream >> item.styleOption.regionName;
    stream >> *((int*)(&(item.styleOption.displayAlignment)));
    return stream;
}

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT ImageRenderLayoutInfo
{
    ImageRenderLayoutInfo()
    {
        qRegisterMetaTypeStreamOperators<ImageRenderLayoutInfo>();
    }
    QRect widgetArea;
    QRect displayArea;
    ImageModePara imageModePara;
    ImageModeScene scene;
};

Q_DECLARE_METATYPE(ImageRenderLayoutInfo)

inline QDataStream& operator<<(QDataStream& stream, const ImageRenderLayoutInfo& layout)
{
    stream << layout.widgetArea;
    stream << layout.displayArea;
    stream << layout.imageModePara.type;
    stream << layout.imageModePara.index;
    stream << layout.imageModePara.transformPara.isUDInvertEnable;
    stream << layout.imageModePara.transformPara.isLRInvertEnable;
    stream << layout.imageModePara.transformPara.isRotationEnable;
    stream << layout.imageModePara.transformPara.isZoomEnable;
    stream << layout.imageModePara.transformPara.isClipEnable;
    stream << layout.scene.name;
    stream << quint32(layout.scene.views.size());
    foreach (ImageModeViewItem item, layout.scene.views)
    {
        stream << item;
    }
    return stream;
}

inline QDataStream& operator>>(QDataStream& stream, ImageRenderLayoutInfo& layout)
{
    stream >> layout.widgetArea;
    stream >> layout.displayArea;
    int type = 0;
    stream >> type;
    layout.imageModePara.type = (ModeImageType)type;
    stream >> layout.imageModePara.index;
    stream >> layout.imageModePara.transformPara.isUDInvertEnable;
    stream >> layout.imageModePara.transformPara.isLRInvertEnable;
    stream >> layout.imageModePara.transformPara.isRotationEnable;
    stream >> layout.imageModePara.transformPara.isZoomEnable;
    stream >> layout.imageModePara.transformPara.isClipEnable;
    stream >> layout.scene.name;
    layout.scene.views.clear();
    quint32 c;
    stream >> c;
    layout.scene.views.reserve(c);
    for (quint32 i = 0; i < c; ++i)
    {
        ImageModeViewItem item;
        stream >> item;
        layout.scene.views.append(item);
        if (stream.atEnd())
            break;
    }
    return stream;
}

inline QDataStream& operator<<(QDataStream& stream, const ImageRenderLayoutInfo* layout)
{
    stream << *layout;
    return stream;
}

inline QDataStream& operator>>(QDataStream& stream, ImageRenderLayoutInfo* layout)
{

    stream >> *layout;
    return stream;
}

enum LayoutType
{
    GridLayout,
    VerLayout,
    HorLayout,
    FormLayout
};

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT RenderLayoutConfigInfo
{
    LayoutType layoutType;
    int templateLayout;
    QList<ImageModePara> modeParas;
};

struct USF_COMMON_CORE_UTILITYMODEL_EXPORT ImageScale
{
    ImageScale()
        : xScale(1.0f)
        , yScale(1.0f)
    {
        qRegisterMetaTypeStreamOperators<ImageScale>();
    }
    ImageScale(float x, float y)
        : xScale(x)
        , yScale(y)
    {
    }
    float xScale;
    float yScale;
};
Q_DECLARE_METATYPE(ImageScale)
inline QDataStream& operator<<(QDataStream& stream, const ImageScale& scale)
{
    stream << scale.xScale;
    stream << scale.yScale;
    return stream;
}
inline QDataStream& operator>>(QDataStream& stream, ImageScale& scale)
{
    stream >> scale.xScale;
    stream >> scale.yScale;
    return stream;
}

inline QDataStream& operator<<(QDataStream& stream, const ImageScale* scale)
{
    stream << *scale;
    return stream;
}
inline QDataStream& operator>>(QDataStream& stream, ImageScale* scale)
{
    stream >> *scale;
    return stream;
}
typedef QPair<int, int> LayoutCoords;
#endif // IMAGEINFODEF_H
