#ifndef LINEDATAHEAD_H
#define LINEDATAHEAD_H
#include "usfcommoncoreutilitymodel_global.h"

namespace LineData
{
enum DEVICETYPE //掌超信息包usb 和wifi 类型
{
    USB = 2,
    WIFI = 3,
};

enum SyncHead
{
    SyncHead_Flag = 0x55aa
};

enum PackageType
{
    Package_Image,
    Package_Info
};

const int LINE_TYPE_WAVE_BIT = 128;

/**
 * @brief The LineType enum
 *
 * 本枚举是描述FPGA上传/Zeus前处理返回的线数据类型，用于数据接收，分类区分存放在缓存
 */
enum LineType
{
    Line_B = 0,
    Line_C_Vel, // 掌超下代表Color数据
    Line_C_SD,  // 掌超下代表D数据
    Line_Power,
    Line_Direct_Power, // Directional Power
    Line_TDI,
    Line_4D,
    Line_Elasto,
    Line_Needle, // Needle linetype 8，掌超下needle数据类型为Line_B
    //打开MVI后，zeus返回的数据会区分两种数据类型，（zeus）BFDataType枚举中定义,一种是CData=0x01,一种是DynamicFlow=0x81
    //这里定义的Line_Dynamic_Flow只要不和其他线类型冲突就可以，仅仅用来做数据区分用的。
    //以目前的方式，如果不加这个字段，C和MVI上来的数据就没法区分。在LineImageDataReceiver::chooseImageBufferIndex（）中用到
    // LineDataHead中的linetype，高为为0=二维数据，1=波形数据（01111111=127）
    Line_Dynamic_Flow = 127,
    Line_M = LINE_TYPE_WAVE_BIT,
    Line_D,
    Line_CWD,
    Line_CM,                           // Color M
    Line_ECG = LINE_TYPE_WAVE_BIT + 4, // ECG 作为独立线传输，
    Line_Sound,
};

enum FrameFlag
{
    Frame_Invalid = -1,
    Frame_Begin,  // 帧开始标记
    Frame_Middle, // 帧中间的线
    Frame_End,    // 帧结束标记
    FrameFlag_Count
};

enum FanType
{
    Fan_CPU = 0,
    Fan_Sys
};

struct FanInfo
{
    FanType type;
    int speed;
};

struct AllFanSpeed
{
    unsigned short Rear_FAN1Speed; // 机箱后排气风扇1
    unsigned short Rear_FAN2Speed; // 机箱后排气风扇2
    unsigned short Rear_FAN3Speed; // 机箱后排气风扇3
    unsigned short Rear_FAN4Speed; // 机箱后排气风扇4
    unsigned short ADP_FAN1Speed;  // 机箱后适配器风扇1
    unsigned short ADP_FAN2Speed;  // 机箱后适配器风扇2
    unsigned short ADP_FAN3Speed;  // 机箱后适配器风扇3
    unsigned short BOT_FAN1Speed;  // 机箱底部风扇1
    unsigned short BOT_FAN2Speed;  // 机箱底部风扇2
    unsigned short BOT_FAN3Speed;  // 机箱底部风扇3
    unsigned short BOT_FAN4Speed;  // 机箱底部风扇4
    unsigned short BOT_FAN5Speed;  // 机箱底部风扇5
    unsigned short BOT_FAN6Speed;  // 机箱底部风扇6
    unsigned short BOT_FAN7Speed;  // 机箱底部风扇7
    unsigned short BOT_FAN8Speed;  // 机箱底部风扇8
    unsigned short BOT_FAN9Speed;  // 机箱底部风扇9
};

struct AllFanRelatedInfo
{
    unsigned char BFL_VCCINT_0V95;      // 42
    unsigned char BFH_VCCINT_0V95;      // 43
    unsigned char DSP_VCCINT_0V95;      // 44
    unsigned char MGTAVCC_1V;           // 45
    unsigned char MGTAVTT_1V2;          // 46
    unsigned char VCC0_1V2;             // 47
    unsigned char VCCAUX_1V8;           // 48
    unsigned char MGTVCCAUX_1V8;        // 49
    unsigned char VCC0_1V8;             // 50
    unsigned char DVDD_2V5;             // 51
    unsigned char DVDD_3V3;             // 52
    unsigned char MGTVCCAUX_1V8_IOUT;   // 53
    unsigned char VCC0_1V8_IOUT;        // 54
    unsigned char VCC0_3V3_IOUT;        // 55
    qint8 DSP_FPGA_TEMP;                // 60
    qint8 BFL_AFE_TEMP;                 // 61
    qint8 BFL_5589_TEMP;                // 62
    qint8 BFL_VCCINT_TEMP;              // 63
    qint8 BFL_FPGA_TEMP;                // 64
    unsigned char RESV;                 // 65
    qint8 HV1_DCDC_TEMP;                // 66
    qint8 HV2_DCDC_TEMP;                // 67
    qint8 DN12V_PWR_TEMP;               // 68
    qint8 CW_DCDC_TEMP;                 // 69
    qint8 PWR_MIDTOP_TEMP;              // 70
    qint8 AP1V6_DCDC_TEMP;              // 71
    qint8 AP13V5_PWR_TEMP;              // 72
    qint8 CW_LD0_TEMP;                  // 73
    unsigned char BFL_VCCINT_0V95_IOUT; // 124
    unsigned char BFH_VCCINT_0V95_IOUT; // 125
    unsigned char DSP_VCCINT_0V95_IOUT; // 126
    unsigned char MGTAVCC_1V_IOUT;      // 127
    unsigned char MGTAVTT_1V2_IOUT;     // 128
    unsigned char VCC0_1V2_IOUT;        // 129
    unsigned char VCCAUX_1V8_IOUT;      // 130
    unsigned char DVDD_2V5_IOUT;        // 131
    qint8 PWR_MID_TEMP;                 // 132
    qint8 AP5V6_PWR_TEMP;               // 133
    qint8 AP3V6_PWR_TEMP;               // 134
    qint8 AP2V3_PWR_TEMP;               // 135
    qint8 VDD3V3_PWR_TEMP;              // 136
    qint8 VDD5V_PWR_TEMP;               // 137
    qint8 PWR_BOT_TEMP;                 // 138
    unsigned char RESV2;                // 139
    unsigned char VDD_3V3_IOUT;         // 140
    unsigned char AP1V6_IOUT;           // 141
    unsigned char AP2V3_IOUT;           // 142
    unsigned char AP3V6_IOUT;           // 143
    unsigned char AP5V6_IOUT;           // 144
    unsigned char AN5V6_IOUT;           // 145
    unsigned char _5V_PRB_IOUT;         // 146
    unsigned char VDD5V_IOUT;           // 147
    unsigned char _12VIN_ALL_IOUT;      // 148
    unsigned char _12VIN_MAIN_IOUT;     // 149
    unsigned char N12V_IOUT;            // 150
    unsigned char AP13V5_IOUT;          // 151
    unsigned char HVPA_IOUT;            // 152
    unsigned char HVNA_IOUT;            // 153
    unsigned char HVPB_IOUT;            // 154
    unsigned char HVNB_IOUT;            // 155
    unsigned char MCW1_FB;              // 156
    unsigned char AP1V6_FB;             // 157
    unsigned char AP2V3_FB;             // 158
    unsigned char AP3V6_FB;             // 159
    unsigned char AP5V6_FB;             // 160
    unsigned char AN5V6_FB;             // 161
    unsigned char VDD5V_PRB_FB;         // 162
    unsigned char CWLD0_IN_FB;          // 163
    unsigned char _12V_IN_FB;           // 164
    unsigned char N12V_FB;              // 165
    unsigned char AP13V5_FB;            // 166
    unsigned char AN13V5_FB;            // 167
    unsigned char HVPA_FB;              // 168
    unsigned char HVNA_FB;              // 169
    unsigned char HVPB_FB;              // 170
    unsigned char HVNB_FB;              // 171
    unsigned char VDD_3V3_FB;           // 172
    unsigned char AP3V3_FB;             // 173
    unsigned char VDD_5V_FB;            // 174
    unsigned char RESV3;                // 175
};

/**
 * @brief LINE_COUNT_PER_PACK 每个包中的线单位的个数
 *
 * 一个线包=LineDataHead + LINE_UNIT * LINE_UNIT_COUNT_PER_PACK
 * B/4D线由一个LINE_UNIT组成
 * C、Power等线由两个LINE_UNIT组成
 */
static const int LINE_UNIT_COUNT_PER_PACK = 2;

/**
 * @brief validLineNumPerPack 每个包中有效的线数
 *
 * 由于是双波束发射，B模式、4D模式是一个包2根线，由于C、Power等线是16bit的，所以是一根线，
 * M、D等波形类数据，由于FPGA是但线采集的，所以一个包是一根线
 * MVI包含DynamicFlow数据类型，是8bit,所以是两根线
 *
 * @param lineType
 * @return
 */
inline int validLineNumPerPack(int lineType)
{
    if (lineType == Line_B || lineType == Line_4D || lineType == Line_Elasto || lineType == Line_Dynamic_Flow)
    {
        return 2;
    }
    else
    {
        return 1;
    }
}
inline bool isLineTypeWave(unsigned char lineType)
{
    return lineType & LINE_TYPE_WAVE_BIT;
}

inline const char* frameFlagString(unsigned char frameFlag)
{

    static const char* const FRAME_FLAG_STRING[] = {"Frame_Begin", "Frame_Middle", "Frame_End"};

    return frameFlag < FrameFlag_Count ? FRAME_FLAG_STRING[frameFlag] : "INVALID_FRAME_FLAG";
}

inline const char* lineTypeString(unsigned char lineType)
{
    static const char* LINE_TYPE_STRING_1[] = {"Line_B",
                                               "Line_C_Vel", // Color velocity
                                               "Line_C_SD",  // Color Variance
                                               "Line_Power",
                                               "Line_Direct_Power", // Directional Power
                                               "Line_TDI",
                                               "Line_4D",
                                               "Line_Elasto"};

    static const char* LINE_TYPE_STRING_2[] = {"Line_M", "Line_D", "Line_CWD", "Line_CM", "Line_ECG", "Line_Sound"};

    if (lineType & LINE_TYPE_WAVE_BIT)
    {
        return lineType <= Line_Sound ? LINE_TYPE_STRING_2[lineType - LINE_TYPE_WAVE_BIT] : "INVALID_LINE_TYPE";
    }
    return lineType <= Line_Elasto ? LINE_TYPE_STRING_1[lineType] : "INVALID_LINE_TYPE";
}
} // namespace LineData

/**
 * @brief The LineDataHead struct 线数据传输时的包头
 * 总大小: 16字节
 * 描述线数据传输时，每个包的包头信息，每个包中的数据性质必须保持一致
 */
struct LineDataHead
{
    /**
     * @brief SyncHead 同步头
     * 位置: 字节0-1 (Byte[0:1])
     * Byte0:0xaa
     * Byte1:0x55
     */
    unsigned char SyncHead[2];

    /**
     * @brief PackageType 包类型
     * 位置: 字节2 (Byte[2])
     * 图像包:0 信息包:1
     */
    unsigned char PackageType;

    /**
     * @brief LineType 图像线类型
     * 位置: 字节3 (Byte[3])
     * B7: 0=二维图像(B、C等), 1=波形图像(M、D等)
     * B[6:0]: 具体类型编号
     */
    unsigned char LineType;

    /**
     * @brief LineNo 线号
     * 位置: 字节4-5 (Byte[4:5])
     * 范围: [0, 探头基元数*2-1]
     */
    unsigned char LineNo[2];

    /**
     * @brief LineOrderNo 线的累积号
     * 位置: 字节6 (Byte[6])
     * 范围: [0,255], 循环计数
     */
    unsigned char LineOrderNo;

    /**
     * @brief FrameFlag 帧标记
     * 位置: 字节7 (Byte[7])
     * 0: 帧开始
     * 1: 普通线
     * 2: 帧结束
     */
    unsigned char FrameFlag;

    /**
     * @brief FourD 4D线额外信息
     * 位置: 字节8 (Byte[8])
     * B0: Angle
     * B1: Direct
     * B2: Effzone
     * B[7:3]: 保留
     */
    union _FourD
    {
        unsigned char Flag;
        struct _Info
        {
            unsigned char Angle : 1;   // Bit 0
            unsigned char Direct : 1;  // Bit 1
            unsigned char Effzone : 1; // Bit 2
        } Info;
    } FourD;

    /**
     * @brief TableID 控制表ID
     * 位置: 字节9 (Byte[9])
     * 范围: [0,255]
     */
    unsigned char TableID;

    /**
     * @brief ECG 数据采样点个数
     * 位置: 字节10-11 (Byte[10:11])
     * 16位对齐
     */
    unsigned short ECG;

    /**
     * @brief SurfaceNo 平面序号
     * 位置: 字节12 (Byte[12])
     * 0: 平面1
     * 1: 平面2
     */
    unsigned char SurfaceNo;

    /**
     * @brief Steering 偏转方向，使用 bit0-bit3
     *
     * 偏转方向，0:无偏转 1:左偏转 2:右偏转
     * [字节13 低4位]
     */
    unsigned char Steering : 4;
    /**
     * @brief PWGateNo 多采样门编号，使用 bit4-bit5
     * [字节13 位4-5]
     */
    unsigned char PWGateNo : 2;
    /**
     * @brief ColorAccumEnd 血流积累结束标识，使用 bit6
     * [字节13 位6]
     */
    unsigned char ColorAccumEnd : 1;
    /**
     * @brief MultiBeamFlag 四波束标识 ，使用 bit7
     * 0：非四波束帧 1:四波束帧
     * [字节13 位7]
     */
    unsigned char MultiBeamFlag : 1;

    /**
     * @brief Reserve 保留字段
     * 位置: 字节14-15 (Byte[14:15])
     */
    unsigned char Reserve[2];

    /**
     * @brief 获取线号
     * @details 从LineNo字段中提取线号信息:
     *          - Palm设备: 取低10位, 范围[0-1023]
     *          - 其他设备: 取低11位, 范围[0-2047]
     * @return 返回线号值
     */
    int getLineNo() const
    {
#ifndef USE_HEAD_PROTOCOL_LOTUS
        return (LineNo[1] << 8 | LineNo[0]) & 0x3FF;
#else
        return (LineNo[1] << 8 | LineNo[0]) & 0x7FF;
#endif
    }

    /**
     * @brief 设置线号
     * @details 将线号信息设置到LineNo字段中:
     *          - Palm设备: 设置低10位, 保留高6位不变
     *          - 其他设备: 设置低11位, 保留高5位不变
     * @param lineNo 要设置的线号值
     */
    void setLineNo(int lineNo)
    {
        unsigned short currentValue = LineNo[1] << 8 | LineNo[0];
#ifndef USE_HEAD_PROTOCOL_LOTUS
        // 清除低10位，保留高6位
        currentValue &= 0xFC00;
        // 设置新的线号（确保只使用低10位）
        currentValue |= (lineNo & 0x3FF);
#else
        // 清除低11位，保留高5位
        currentValue &= 0xF800;
        // 设置新的线号（确保只使用低11位）
        currentValue |= (lineNo & 0x7FF);
#endif
        // 更新LineNo字段
        LineNo[0] = currentValue & 0xFF;
        LineNo[1] = (currentValue >> 8) & 0xFF;
    }

    /**
     * @brief 获取IQ积累次数
     * @details 从LineNo字段中提取IQ积累次数:
     *          - Palm设备: 取中间4位[10:13], 右移10位
     *          - 其他设备: 取中间4位[11:14], 右移11位
     * @return 返回IQ积累次数, 范围[0-15]
     */
    int getIQAccumulativeTimes() const
    {
#ifndef USE_HEAD_PROTOCOL_LOTUS
        return ((LineNo[1] << 8 | LineNo[0]) & 0x3C00) >> 10;
#else
        return ((LineNo[1] << 8 | LineNo[0]) >> 11) & 0x0F;
#endif
    }

    /**
     * @brief 获取IQ标志位
     * @details 从LineNo字段中提取IQ标志位:
     *          - Palm设备: 取最高位(bit15)
     *          - 其他设备: 取最高位(bit15)
     * @return 返回IQ标志位值(0或1)
     */
    int getIQFlag() const
    {
#ifndef USE_HEAD_PROTOCOL_LOTUS
        return ((LineNo[1] << 8 | LineNo[0]) & 0x8000) >> 15;
#else
        return ((LineNo[1] << 8 | LineNo[0]) >> 15) & 0x01;
#endif
    }

    /**
     * @brief 获取帧号
     * @details 帧号计算方式:
     *          - Palm设备: 直接从Reserve字段获取16位帧号
     *          - 其他设备: 根据IQ积累次数和IQ标志位计算: IQAccumulativeTimes * 2 + IQFlag
     * @return 返回帧号值
     */
    int getFrameNo() const
    {
#ifndef USE_HEAD_PROTOCOL_LOTUS
        return (Reserve[0] << 8) | Reserve[1];
#else
        return getIQAccumulativeTimes() * 2 + getIQFlag();
#endif
    }
};

/*
 * 4D信息包的包头
 * 在线数据前两个字节
 */
struct FourDPara
{
    unsigned char LineNo;

    unsigned char LineNoHigh : 1;

    unsigned char Reserve : 4;

    unsigned char Angle : 1;

    unsigned char Direct : 1;

    unsigned char Effzone : 1;
};
/**
 * @brief The LineDataHWInfo struct 信息线的数据部分
 */
struct LineDataHWInfo
{
#ifdef USE_TARGET_PALM
    //掌超协议和其他有区别，为方便编译
    unsigned char DSCVersion0;
    unsigned char DSCVersion1;
    unsigned char DSCVersion2 : 7;
    unsigned char TestFlag : 1;
    unsigned char ProbeCode : 4;
    unsigned char HardWareVersion : 4;
    unsigned short ECVersion;
    unsigned short FpgaTemperature;
    unsigned char BatteryStatus;
    unsigned char AdapterStatus;
    unsigned short BatteryTemperature;
    unsigned short BatteryVol;
    unsigned short BatteryElectricity;
    unsigned short BoardTemp;
    unsigned char FpgaDNAStatus;
    unsigned char FpgaDNA[12];
    unsigned char PwFlag;
    unsigned char DscStatus[3];
    unsigned char empty;
    unsigned char crc1;
    unsigned char crc2;
    unsigned char DataBlockCheckResult;
    unsigned char LicenseValid;
    unsigned char Info;
    unsigned char ProbeKeyFreeze;
    unsigned char ProbeKeySave;
    unsigned char ProbeKeyDepthUp;
    unsigned char ProbeKeyDepthDown;
    unsigned char FpgaUpgradeStatus;
    unsigned short FpgaRequestIndex : 15;
    unsigned char FpgaRequestEnale : 1;
    unsigned char ErrorFormatNum;
    unsigned char ErrorLenNum;
    unsigned char ChargeStatus;
    unsigned char Others[13];
    unsigned char LicenseStore[256];
#endif
    /**
     * @brief DSC_0_15 DSC版本信息[15:0]
     */
    unsigned int Version0 : 20;
    unsigned int Reserve0 : 12; // 3
    /**
     * @brief B0_4_15 12'b0[15:04]
     */
    /**
     * @brief MP_0_15 MP版本信息[15:0]
     */
    unsigned int Version1 : 20;
    unsigned int Reserve1 : 12; // 7
    unsigned int Version2 : 20;
    unsigned int Reserve2 : 12; // 11
    unsigned int Version3 : 20;
    unsigned int Reserve3 : 12; // 15
    unsigned short Probe0ID;    // 17
    unsigned short Probe1ID;    // 19
    unsigned short Probe2ID;    // 21
    unsigned short Probe3ID;    // 23
    unsigned short MPStatus;    // 25
    unsigned short MBFStatus;   // 27
    unsigned short EBFStatus;   // 29
    unsigned short Probe4ID;    // 31
    unsigned int DSCStatus : 24;
    unsigned int Reserve5 : 8;      // 35
    unsigned int ECGTime;           // 39
    unsigned char ProbeTemperature; // 40
    unsigned char Reserve[19];      //保留字段，暂未使用 59

    unsigned short Reserve6 : 6;
    unsigned short ADTTemperature : 10; // ADT温度 61
    unsigned short Reserve7 : 6;
    unsigned short AFETemperature : 10; // AFE温度 63
    unsigned short Reserve8 : 6;
    unsigned short MOSTemperature : 10; // MOS温度 65

    unsigned char Reserve9[8]; //保留字段，暂未使用 73

    unsigned short Reserve10 : 4;
    unsigned short TeeProbeTemperature : 12; // Tee探头温度 75

    unsigned short Reserve11 : 4;
    unsigned short TeeProbeAnge : 12; // Tee探头角度 77

    unsigned short CpuFanSpeed;    // CPU风扇速度 79
    unsigned char Reserve12[16];   //保留字段，暂未使用 95
    unsigned short SysFanSpeed;    // 系统风扇速度 97
    unsigned char Reserve13[6];    //保留字段，暂未使用 103
    unsigned short Key1 : 1;       // key 键 1:按下 0:未按下
    unsigned short Key2 : 1;       // key 键 1:按下 0:未按下
    unsigned short Key3 : 1;       // key 键 1:按下 0:未按下
    unsigned short Key4 : 1;       // key 键 1:按下 0:未按下
    unsigned short Key5 : 1;       // key 键 1:按下 0:未按下
    unsigned short Key6 : 1;       // key 键 1:按下 0:未按下
    unsigned short Reserve14 : 10; // 105
};

/*namespace LineData
{
    USF_COMMON_CORE_UTILITYMODEL_EXPORT void initWholeDataHWInfo(WholeDataHWInfo *wInfo);
    USF_COMMON_CORE_UTILITYMODEL_EXPORT void lineDataHWInfo2WholeDataHWInfo(const LineDataHWInfo *lInfo, WholeDataHWInfo
*wInfo); USF_COMMON_CORE_UTILITYMODEL_EXPORT int getECGTimeFromLineDataHWInfo(const LineDataHWInfo *lInfo);
}*/

#endif // LINEDATAHEAD_H
