#ifndef IMAGEEVENTARGS_H
#define IMAGEEVENTARGS_H
#include "usfcommoncoreutilitymodel_global.h"

#include <QList>
#include <QMetaType>
#include <QtGlobal>

/*!
 * \brief 封装供图像区绘制的图像数据与图像信息
 *
 * 用于在 \a ImageFormer 中从硬件收到图像原始数据，对图像数据和图像信息封装为 \a ImageEventArgs
 * 的对象，向实时显示部分传递，供实时显示部分绘制显示
 */
class USF_COMMON_CORE_UTILITYMODEL_EXPORT ImageEventArgs
{
public:
    enum ImageType
    {
        ImageB,
        ImageC,
        ImageD,
        ImageM,
        ImageECG,
        ImageElasto,
        ImageCurvedPan,
        ImageFourD,
        WholeImage
    };

    explicit ImageEventArgs();
    ~ImageEventArgs();
    /*!
     * 返回图像缓冲
     * \sa setpImageData()
     */
    uchar* imageData() const
    {
        return m_pImageData;
    }
    /*!
     * 设置图像缓冲
     * \sa pImageData()
     */
    void setImageData(uchar* value);
    void clear();
    bool isNull() const;
    /*!
     * 返回图像的字节数
     */
    int imageSize() const
    {
        return m_Width * m_Height * (m_BitCount / 8);
    }
    /*!
     * 返回图像的宽度
     */
    int width() const
    {
        return m_Width;
    }
    /*!
     * 返回图像的高度
     */
    int height() const
    {
        return m_Height;
    }
    /*!
     * 返回图像每个像素需要的位数
     */
    int bitCount() const
    {
        return m_BitCount;
    }
    /*!
     * 返回图像冻结标志
     */
    int needFreeze() const
    {
        return m_NeedFreeze;
    }
    /*!
     * 设置图像的宽度
     */
    void setWidth(int value)
    {
        m_Width = value;
    }
    /*!
     * 设置图像的高度
     */
    void setHeight(int value)
    {
        m_Height = value;
    }
    /*!
     * 设置图像每个像素需要的位数
     */
    void setBitCount(int value)
    {
        m_BitCount = value;
    }
    /*!
     * 设置图像帧之间是否不变 因此设置了冻结标记
     */
    void setNeedFreeze(int value)
    {
        m_NeedFreeze = value;
    };

    ImageEventArgs::ImageType imageType() const
    {
        return m_ImageType;
    }
    void setImageType(ImageEventArgs::ImageType value)
    {
        m_ImageType = value;
    }

    void setECGEnd(const int ecgEnd)
    {
        m_ECGEnd = ecgEnd;
    }
    int ecgEnd() const
    {
        return m_ECGEnd;
    }

    void setECGTime(float ecgTime)
    {
        m_ECGTime = ecgTime;
    }
    float ecgTime() const
    {
        return m_ECGTime;
    }

    void setIndex(const int index)
    {
        m_Index = index;
    }
    int index() const
    {
        return m_Index;
    }

    void setFrameIndex(const int frameIndex)
    {
        m_FrameIndex = frameIndex;
    }
    int frameIndex() const
    {
        return m_FrameIndex;
    }

    void setImageBufferFrameIndex(const int frameIndex)
    {
        m_ImageBufferFrameIndex = frameIndex;
    }
    int imageBufferFrameIndex() const
    {
        return m_ImageBufferFrameIndex;
    }

    void setSystemScanMode(const int systemScanMode)
    {
        m_SystemScanMode = systemScanMode;
    }
    int systemScanMode() const
    {
        return m_SystemScanMode;
    }

    uchar* elastoData() const
    {
        return m_ElastoData;
    }
    void setElastoData(uchar* value)
    {
        m_ElastoData = value;
    }

    /**
     * @brief 设置心率值
     */
    void setHRValue(int value)
    {
        m_HRValue = value;
    }
    int hrValue() const
    {
        return m_HRValue;
    }

    void setGlTexture(unsigned int texture)
    {
        m_GlTexture = texture;
    }
    unsigned int glTexture() const
    {
        return m_GlTexture;
    }

    void setCacheIndex(const int cacheIndex)
    {
        m_CacheIndex = cacheIndex;
    }
    int cacheIndex() const
    {
        return m_CacheIndex;
    }

    void setSyncId(const int syncId)
    {
        m_SyncId = syncId;
    }
    int syncId() const
    {
        return m_SyncId;
    }

    void setLayout(const int layout)
    {
        m_Layout = layout;
    }
    int layout() const
    {
        return m_Layout;
    }

    void setNeedUpdateForzonIndex(bool value)
    {
        m_NeedUpdateForzonIndex = value;
    }
    bool needUpdateForzonIndex() const
    {
        return m_NeedUpdateForzonIndex;
    }

    void setDataType(const int dataType)
    {
        m_DataType = dataType;
    }
    int dataType() const
    {
        return m_DataType;
    }

    void setPostRawDataType(int type)
    {
        m_PostRawDataType = type;
    }
    int postRawDataType() const
    {
        return m_PostRawDataType;
    }

    unsigned int frontIndex() const;
    void setFrontIndex(unsigned int value);

    qint64 getFrameStartTime() const
    {
        return m_frameStartTime;
    }
    void setFrameStartTime(const qint64 value)
    {
        m_frameStartTime = value;
    }
    unsigned int imageRenderPartition() const;
    void setImageRenderPartition(unsigned int value);

    bool isMeasureImageReady() const;
    void setMeasureImageReady(bool isMeasureImageReady);

private:
    uchar* m_pImageData;
    bool m_NeedUpdateForzonIndex;
    int m_Width;
    int m_Height;
    int m_BitCount;
    int m_ECGEnd;
    float m_ECGTime;
    int m_Index;
    ImageEventArgs::ImageType m_ImageType;
    uchar* m_ElastoData;
    int m_FrameIndex;
    int m_FrontIndex;
    int m_SystemScanMode;
    int m_HRValue;
    unsigned int m_GlTexture;
    int m_NeedFreeze;
    int m_CacheIndex;
    int m_SyncId{0};
    int m_Layout{1};
    int m_DataType{-1};
    int m_PostRawDataType;
    qint64 m_frameStartTime{0};
    int m_ImageRenderPartition;

    int m_ImageBufferFrameIndex{0};

    bool m_IsMeasureImageReady{false};
};

Q_DECLARE_METATYPE(ImageEventArgs);

USF_COMMON_CORE_UTILITYMODEL_EXPORT QDataStream& operator>>(QDataStream& in, ImageEventArgs& v);
USF_COMMON_CORE_UTILITYMODEL_EXPORT QDataStream& operator<<(QDataStream& out, const ImageEventArgs& v);

#endif // IMAGEARGS_H
