/*
 * =====================================================================================
 *
 *       Filename:  tgc.cpp
 *
 *    Description:  :
 *
 *        Version:  1.0
 *        Created:  2012年06月29日 15时57分59秒
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  Wang<PERSON><PERSON>yu (), wang<PERSON><EMAIL>
 *   Organization:
 *
 * =====================================================================================
 */
#include "tgc.h"

#include <algorithm>

#include <boost/foreach.hpp>

#include <QDebug>
#include <QTimeLine>

#include "implmacro.h"
#include "modeluiconfig.h"

TGC::TGC(QGraphicsItem* parent)
    : ProxyGroup(parent)
    , m_width(0)
    , m_xOffset(0)
    , m_height(0)
    , m_timeLine(new QTimeLine(1000, this))
    , m_visibleType(AutoHide)
    , m_IsActiveB(true)
    , m_SupportHoverToReset(true)
{

    m_group.addToGroup(&m_bound);
    m_group.addToGroup(&m_line);
#ifndef SYS_APPLE
    m_group.addToGroup(&m_resetTip);
#endif
    m_fadingOut = false;
    setSoildMs(3000);
    connect(m_timeLine, SIGNAL(frameChanged(int)), this, SLOT(fading(int)));

    construct();

    qRegisterMetaType<QVariant>("QVariant");
}

SETTER_IMPL(TGC, qreal, xOffset, XOffset);

void TGC::setWidth(qreal width)
{

    if (m_width == width)
    {
        return;
    }

    m_width = width;

    construct();

    prepareGeometryChange();

    emit widthChanged(m_width);
}

void TGC::setHeight(qreal height)
{

    if (m_height == height)
    {
        return;
    }

    m_height = height;

    construct();

    prepareGeometryChange();

    emit widthChanged(m_height);
}

void TGC::setNumber(int numberValue)
{

    Q_ASSERT_X(numberValue >= 0, "TGC::setNumber", "numberValue<0");
    if (m_controlPointsMeta.size() != numberValue)
    {

        if (numberValue == 0)
        {
            m_controlPointsMeta.clear();
        }
        else
        {
            if (number() == 0)
            {
                m_controlPointsMeta.push_back(OffsetAndPercent(0, 0));
            }

            PointMetaContainer temp;

            int copyNumber = qMin(numberValue, m_controlPointsMeta.size());

            std::copy(m_controlPointsMeta.begin(), m_controlPointsMeta.begin() + copyNumber, std::back_inserter(temp));

            for (int i = 0, size = numberValue - copyNumber; i < size; i++)
            {
                temp.push_back(m_controlPointsMeta.back());
            }

            m_controlPointsMeta = temp;
        }

        construct();

        emit numberChanged(numberValue);
    }
}

bool TGC::check() const
{

    Q_ASSERT_X(m_width >= m_xOffset, "TGC::check", "xOffset>=width");
    Q_ASSERT_X(!(m_height < 0), "TGC::check", "height<0");
    Q_ASSERT_X(!(m_width < 0), "TGC::check", "width<0");
    Q_ASSERT_X(!(m_xOffset < 0), "TGC::check", "xOffset<0");

    Q_ASSERT((int)m_controlPointsGraphic.size() == m_controlPointsMeta.size());

    return true;
}

void TGC::setControlPointBy(int index, OffsetAndPercent offsetAndPercent)
{
    Q_ASSERT(checkIndex(index));

    if (m_controlPointsMeta[index] != offsetAndPercent)
    {
        m_controlPointsMeta[index] = offsetAndPercent;

        construct();
        emit controlPointChanged(index, offsetAndPercent);
    }
}

bool TGC::checkIndex(int index) const
{

    Q_ASSERT_X(index >= 0, "TGC::checkIndex", "index<0");
    Q_ASSERT_X(index < this->number(), "TGC::getControlPointBy", "index overflow");
    return true;
}

bool TGC::checkAlwaysVisibleType()
{
    if (m_visibleType == AlwaysShow)
    {
        m_timeLine->stop();
        setVisible(m_IsActiveB);
        return true;
    }
    else if (m_visibleType == AlwaysHide)
    {
        m_timeLine->stop();
        setVisible(false);
        return true;
    }

    return false;
}

bool TGC::checkTipFontSize()
{
    QFontMetrics fontMetrics(m_resetTip.font());
    int strWidth = fontMetrics.horizontalAdvance(m_resetTip.text());

    return (strWidth < m_bound.boundingRect().width());
}

void TGC::setSupportHoverToReset(bool supportHoverToReset)
{
    m_SupportHoverToReset = supportHoverToReset;
}

TGC::VisibleType TGC::visibleType() const
{
    return m_visibleType;
}

void TGC::setVisibleType(VisibleType visibleType, bool show)
{
    m_visibleType = visibleType;
    m_show = show;
    checkAlwaysVisibleType();
}

void TGC::setIsActiveB(bool isActiveB)
{
    m_IsActiveB = isActiveB;
}

TGC::OffsetAndPercent TGC::getControlPointBy(int index)
{

    Q_ASSERT(checkIndex(index));

    return m_controlPointsMeta[index];
}

static const int FADING_MS = 3000;
static const int SOILD_FRAMES = 20;

static const int CONTROL_POINT_RADIUS = 2;

void TGC::construct()
{

    //边框构建
#ifdef SYS_APPLE
    //保持和android一致
    QPointF origin(0, 0), upperMinX(m_xOffset, 0), upperRight(m_width, 0),
        bottomRight(m_width, m_height - (512 / 8) * (m_height / 512.0)),
        bottomLeft(0, m_height - (512 / 8) * (m_height / 512.0));
#else
    QPointF origin(0, 0), upperMinX(m_xOffset, 0), upperRight(m_width, 0), bottomRight(m_width, m_height),
        bottomLeft(0, m_height);
#endif

    QPainterPath boundPath(origin);
    boundPath.lineTo(upperRight);
    boundPath.lineTo(bottomRight);
    boundPath.lineTo(bottomLeft);

    m_bound.setPath(boundPath);

    const int pointNumber = m_controlPointsMeta.size();

    m_controlPointsGraphic.resize(pointNumber);

    //点位置计算

    QPainterPath linePath(upperMinX);

    const qreal lineMaxWidth = m_width - m_xOffset;

    QVector<QPointF> pointsVector; // 存储八个点
    QVariant points;

    for (int i = 0; i < pointNumber; ++i)
    {

        OffsetAndPercent meta = m_controlPointsMeta[i];
        QPointF pos(meta.second * lineMaxWidth + m_xOffset, meta.first);

        QGraphicsEllipseItem& graphic = m_controlPointsGraphic[i];
        graphic.setRect(-CONTROL_POINT_RADIUS, -CONTROL_POINT_RADIUS, CONTROL_POINT_RADIUS * 2,
                        CONTROL_POINT_RADIUS * 2);

        graphic.setPos(pos);

        if (!graphic.parentItem())
        {
            m_group.addToGroup(&graphic);
            graphic.setPos(pos);
        }
        graphic.resetTransform();

        linePath.lineTo(pos);

        pointsVector.push_back(pos);
    }

    m_line.setPath(linePath);

    setPen(m_pen);

    if (m_visibleType != AdjustShow)
    {
        resetOptical();
    }

    points.setValue(pointsVector);
    emit pointsChanged(points); // 抛出点位信息

    if (m_SupportHoverToReset)
    { // 设置重置TGC的文字 并根据TGC框的宽度
      // 自动调整合适的字号(默认14号)，最小支持8号(再小没有意义)，没有合适字号直接不显示
        QString str = tr("Hover 1s to reset TGC");
        m_resetTip.setText(str);
        QString fontStr = ModelUiConfig::instance().value(ModelUiConfig::GraphicsFont).toString();
        m_resetTip.setFont(QFont(fontStr, 14));

        for (int fontSize = 14; fontSize >= 8; fontSize--)
        {
            if (!checkTipFontSize())
            {
                m_resetTip.setFont(QFont(fontStr, fontSize));
            }
            else
            {
                QFontMetrics fontMetrics(m_resetTip.font());
                int strWidth = fontMetrics.horizontalAdvance(str);
                int strHeight = fontMetrics.height();
                m_resetTip.setPos(QPoint(m_bound.boundingRect().width() / 2.0 - strWidth / 2.0,
                                         m_bound.boundingRect().bottomLeft().y() - strHeight - 10));
                break;
            }
        }

        if (!checkTipFontSize())
        {
            m_resetTip.setVisible(false);
        }
        else
        {
            m_resetTip.setVisible(true);
        }
    }
    m_resetTip.setVisible(m_SupportHoverToReset);

    Q_ASSERT(check());
}

void TGC::resetOptical(bool delay)
{
    if (checkAlwaysVisibleType())
    {
        return;
    }

    if (!delay)
    {
        m_timeLine->stop();
        setVisible(false);
        return;
    }

    //淡出动画
    setVisible(m_show);
    setOpacity(1);
    m_timeLine->stop();
    m_timeLine->setDuration(m_soildMs + FADING_MS);
    m_timeLine->setFrameRange(0, SOILD_FRAMES + m_fadingFrames);

    m_timeLine->start();
}

void TGC::setPen(const QPen& pen1)
{
    QPen pen = pen1;
#if SYS_APPLE
    pen.setWidth(2.5);
#endif

    m_pen = pen;
    m_bound.setPen(m_pen);
    m_line.setPen(m_pen);
    m_resetTip.setBrush(QBrush(m_pen.color()));
    BOOST_FOREACH (QGraphicsEllipseItem& item, m_controlPointsGraphic)
    {
        item.setPen(m_pen);
    }
}

void TGC::fading(int frames)
{

    int fadingNumber = qMax(0, frames - SOILD_FRAMES);

    if (m_fadingOut)
    {
        setOpacity(1 - fadingNumber / qreal(m_fadingFrames));
    }
    else if (fadingNumber > 0)
    {
        setVisible(false);
        // no need to run timeline anymore
        m_timeLine->stop();
    }
}

void TGC::setFadingOut(bool fadingOut)
{
    m_fadingOut = fadingOut;
    construct();
}

void TGC::setSoildMs(int ms)
{
    m_soildMs = ms;
    m_fadingFrames = (qreal)SOILD_FRAMES / m_soildMs * FADING_MS;
}

QRectF TGC::boundingRect() const
{

    qreal penWidth = 1;
    // bounding rect include control point circle size and half pen width
    return QRectF(-CONTROL_POINT_RADIUS - penWidth / 2, -CONTROL_POINT_RADIUS - penWidth / 2,
                  CONTROL_POINT_RADIUS + m_width + penWidth, CONTROL_POINT_RADIUS + m_height + penWidth);
}
