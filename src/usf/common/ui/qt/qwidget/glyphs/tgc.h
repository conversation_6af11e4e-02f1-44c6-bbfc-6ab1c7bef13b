/*
 * =====================================================================================
 *
 *       Filename:  tgc.h
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  2012年06月29日 15时31分40秒
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  WangXinyu (), <EMAIL>
 *   Organization:
 *
 * =====================================================================================
 */

#ifndef TGC_H

#define TGC_H
#include "usfcommonuiglyphs_global.h"

#include "proxygroup.h"

#include <boost/ptr_container/ptr_vector.hpp>

#include <QPen>

#include "declmacro.h"

Q_DECLARE_METATYPE(QVector<QPointF>);

class QTimeLine;

/**
 * @brief 绘制TGC区域的基类，
 *  仅完成图形绘制功能，不处理复杂的数值映射
 *  当fadingOut=true时，图形变化将渐变淡出
 *  fadingOut=false时，图形将在一段时间后隐藏
 */
class USF_COMMON_UI_GLYPHS_EXPORT TGC : public QObject, public ProxyGroup
{

    Q_OBJECT

public:
    enum VisibleType
    {
        AutoHide,
        AlwaysShow,
        AlwaysHide,
        AdjustShow

    };
    TGC(QGraphicsItem* parent = 0);

    virtual ~TGC()
    {
    }

    GETTER(qreal, xOffset);

    GETTER(qreal, width);

    GETTER(qreal, height);

    BOOL_GETTER(fadingOut, FadingOut);

    int number() const
    {
        return m_controlPointsMeta.size();
    }

    typedef QPair<qreal, qreal> OffsetAndPercent;

    OffsetAndPercent getControlPointBy(int index);

    void setPen(const QPen& pen);

    /**
     * @brief 重置为完全显示并开始延迟隐藏
     *
     * @param delay 若为true则延迟隐藏，否则立刻隐藏
     */
    void resetOptical(bool delay = true);

    /**
     * @brief 指定是否产生淡出效果
     *
     * @param fadingOut
     */
    void setFadingOut(bool fadingOut);

    /**
     * @brief setSoildMs 设置tgc显示实线的时间
     * @param ms
     */
    void setSoildMs(int ms);

    virtual QRectF boundingRect() const;

    TGC::VisibleType visibleType() const;
    void setVisibleType(TGC::VisibleType visibleType, bool show = true);

    void setIsActiveB(bool isActiveB);

    void setSupportHoverToReset(bool supportHoverToReset);

public slots:

    void setControlPointBy(int index, OffsetAndPercent offsetAndPercent);

    void setNumber(int numberValue);

    void setXOffset(qreal xOffsetValue);

    void setHeight(qreal heightValue);

    void setWidth(qreal widthValue);

signals:

    void controlPointChanged(int index, OffsetAndPercent offsetAndPercentValue);

    void numberChanged(int numberValue);

    void xOffsetChanged(qreal xOffsetValue);

    void heightChanged(qreal heightValue);

    void widthChanged(qreal widthValue);

    void pointsChanged(QVariant points);

private:
    /**
     * @brief 总宽度
     */
    qreal m_width;

    /**
     * @brief tgc控制点x偏移量
     * 控制点的值为0时并不是对齐到最左边,而是有一定的偏移量
     */
    qreal m_xOffset;

    /**
     * @brief 总高度
     */
    qreal m_height;

    /**
     * @brief 控制点的y方向offset及x方向的percent
     */
    typedef QList<OffsetAndPercent> PointMetaContainer;

    PointMetaContainer m_controlPointsMeta;

    /**
     * @brief 包围线
     */
    QGraphicsPathItem m_bound;

    /**
     * @brief 控制点连成的折线
     */
    QGraphicsPathItem m_line;

    /**
     * @brief m_resetTip 重置TGC提示
     */
    QGraphicsSimpleTextItem m_resetTip;

    QPen m_pen;

    /**
     * @brief 每个控制点上绘制的小圆圈
     */
    typedef boost::ptr_vector<QGraphicsEllipseItem> PointGraphicContainer;
    PointGraphicContainer m_controlPointsGraphic;

    void construct();
    /**
     * @brief 检查参数有效性
     *
     * @return
     */
    bool check() const;

    /**
     * @brief 检查索引有效性
     *
     * @param index
     *
     * @return
     */
    bool checkIndex(int index) const;
    bool checkAlwaysVisibleType();

    bool checkTipFontSize();

    QTimeLine* m_timeLine;

    bool m_fadingOut;
    int m_soildMs;
    int m_fadingFrames;
    VisibleType m_visibleType;
    int m_show;
    bool m_IsActiveB;
    bool m_SupportHoverToReset;
private slots:

    void fading(int frames);
};

#endif /* end of include guard: TGC_H */
