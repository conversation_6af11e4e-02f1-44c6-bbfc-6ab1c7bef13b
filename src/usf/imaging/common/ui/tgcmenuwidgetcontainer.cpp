#include "tgcmenuwidgetcontainer.h"
#include "ui_tgcmenuwidgetcontainer.h"
#include "tgcitemwidget.h"
#include "tgcadjustmentwidgetcontroller.h"
#include <QMouseEvent>

TGCMenuWidgetContainer::TGCMenuWidgetContainer(int count, QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::TGCMenuWidgetContainer)
    , m_TGCCount(count)
    , m_ItemHeight(50)
    , m_ActiveTGCItemWidget(nullptr)
    , m_WorkState(true)
    , m_MouseIsEnter(false)
{
    ui->setupUi(this);
    setMouseTracking(true);
    initUI();
}

TGCMenuWidgetContainer::~TGCMenuWidgetContainer()
{
    delete ui;
}

void TGCMenuWidgetContainer::setItemHeight(int height)
{
    m_ItemHeight = height;
}

void TGCMenuWidgetContainer::updateWorkState(bool workState)
{
    m_WorkState = workState;
    QApplication::setOverrideCursor(m_WorkState ? QCursor(Qt::ClosedHandCursor) : QCursor(Qt::ArrowCursor));
}

void TGCMenuWidgetContainer::setTGCAdjustmentController(TGCAdjustmentWidgetController* controller)
{
    m_Controller = controller;
    connect(m_Controller, &TGCAdjustmentWidgetController::valueChanged, this, &TGCMenuWidgetContainer::updateItem);
}

void TGCMenuWidgetContainer::mousePressEvent(QMouseEvent* event)
{
    QWidget::mousePressEvent(event);
    if (event->button() == Qt::LeftButton && event->source() == Qt::MouseEventNotSynthesized) // 左键单击的处理逻辑
    {
        emit updateClicked();
    }
}

void TGCMenuWidgetContainer::mouseMoveEvent(QMouseEvent* event)
{
    QWidget::mouseMoveEvent(event);
    if (!m_WorkState)
    {
        return;
    }

    foreach (TGCItemWidget* item, m_TGCItemWidgets)
    {
        if (nullptr != item && item != m_ActiveTGCItemWidget)
        {
            if (item->geometry().top() <= event->pos().y() && item->geometry().bottom() >= event->pos().y())
            {
                if (nullptr != m_ActiveTGCItemWidget)
                {
                    m_ActiveTGCItemWidget->setSelected(false);
                }

                item->setSelected(true);
                m_ActiveTGCItemWidget = item;
                m_ActiveTGCItemWidget->updateSlider(event);
                break;
            }
            else
            {
                item->setSelected(false);
            }
        }
    }

    if (nullptr != m_ActiveTGCItemWidget)
    {
        m_ActiveTGCItemWidget->updateSlider(event);
    }
}

void TGCMenuWidgetContainer::enterEvent(QEvent* event)
{
    QWidget::enterEvent(event);
    QApplication::setOverrideCursor(m_WorkState ? QCursor(Qt::ClosedHandCursor) : QCursor(Qt::ArrowCursor));
    m_MouseIsEnter = true;
}

void TGCMenuWidgetContainer::leaveEvent(QEvent* event)
{
    QWidget::leaveEvent(event);
    QApplication::setOverrideCursor(QCursor(Qt::ArrowCursor));
    m_MouseIsEnter = false;
}

void TGCMenuWidgetContainer::showEvent(QShowEvent* event)
{
    QWidget::showEvent(event);
    m_TGCItemWidgets = findChildren<TGCItemWidget*>();
    for (int index = 0; index < m_TGCItemWidgets.count(); index++)
    {
        TGCItemWidget* item = m_TGCItemWidgets.at(index);
        if (nullptr != item)
        {
            item->setFixedHeight(m_ItemHeight);
            updateItem(index, m_Controller->tgcValueOfIndex(index));
        }
    }
}

void TGCMenuWidgetContainer::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
    TGCItemWidget* firstItem = findChild<TGCItemWidget*>("slidertgc_0");
    if (nullptr != firstItem)
    {
        QRect rect = firstItem->rect();
        QCursor::setPos(firstItem->mapToGlobal(rect.center()));
    }
}

void TGCMenuWidgetContainer::onTGCValueChanged(int value)
{
    TGCItemWidget* item = dynamic_cast<TGCItemWidget*>(sender());
    if (nullptr != item && m_MouseIsEnter)
    {
        int index = item->objectName().split("_").at(1).toInt();
        m_Controller->updateTGCByRatio(index, value); // 调用Controller完成参数控制
    }
}

void TGCMenuWidgetContainer::updateItem(int index, int value)
{
    if (!m_MouseIsEnter) // 当光标不在调节TGC的范围内，如果超声参数变更，需要主动更新滑块的值
    {
        TGCItemWidget* item = findChild<TGCItemWidget*>(QString("slidertgc_%1").arg(index));
        if (nullptr != item)
        {
            item->setSliderValue(value);
        }
    }
}

void TGCMenuWidgetContainer::initUI()
{
    for (int index = 0; index < m_TGCCount; index++)
    {
        TGCItemWidget* item = new TGCItemWidget(this);
        connect(item, &TGCItemWidget::valueChanged, this, &TGCMenuWidgetContainer::onTGCValueChanged);
        item->setObjectName(QString("slidertgc_%1").arg(index));
        ui->verticalLayout->addWidget(item);
    }
}

void TGCMenuWidgetContainer::on_pushButton_reset_clicked()
{
    // 重置时将滑块移动到中间
    for (int index = 0; index < m_TGCCount; index++)
    {
        TGCItemWidget* item = findChild<TGCItemWidget*>(QString("slidertgc_%1").arg(index));
        if (nullptr != item)
        {
            item->reset();
        }
    }
    m_Controller->reset();
}
