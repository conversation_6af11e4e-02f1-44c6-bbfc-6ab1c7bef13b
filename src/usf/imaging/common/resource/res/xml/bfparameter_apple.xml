<?xml version="1.0" encoding="UTF-8"?>
<!--ValueType 如果类型不是int，必须注明类型，目前支持Bool,Int,Double,List,String ,ByteArray,Size,
ControlTable的Bool类型必须注明 TrueValue,
SettingIds 为ByteArray类型， StrIds为String类型("0,1,2,3,4")
TM : TriplexMode
xxxShow: 这种参数一般是控制其他两个或者更多个同类参数，用于显示在一个菜单项上-->
<BFParameter Version="0.1" ParameterVersion="0.1">
        <BFParameterGroup Type="ControlTable" ControlDataLen="1024">
                <Parameter Name="Freeze" BitCount="1" StartBit="0" StartByte="1" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="FrequencyCompounding" ModelType="B" BitCount="1" StartBit="1" StartByte="1" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="FrequencyCompoundingOther" ModelType="B" BitCount="1" StartBit="1" StartByte="1" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsDirectValue="true"/>
                <Parameter Name="Socket" BitCount="3" StartBit="2" StartByte="1" IsPresetParameter="false"/>
                <Parameter Name="ProbeCode" BitCount="3" StartBit="5" StartByte="1" HighBitCount="1" HighStartBit="0" HighStartByte="8" IsPresetParameter="false"/>
                <Parameter Name="FreqIndexColorBin" BitCount="3" StartBit="0" StartByte="2" HighBitCount="1" HighStartBit="7" HighStartByte="166" ThirdBitCount="1" ThirdStartBit="5" ThirdStartByte="711" IsPresetParameter="false"/>
                <Parameter Name="ColorLineDensity" BitCount="1" StartBit="3" StartByte="2" ValueType="Bool" Value="false" TrueValue="0" IsPresetParameter="true"/>
                <Parameter Name="ColorLineDensitySN" ModelType="SonoNeedle" BitCount="1" StartBit="3" StartByte="2" ValueType="Bool" Value="false" TrueValue="0" IsPresetParameter="true"/>
                <Parameter Name="TDILineDensity" ModelType="TDI" BitCount="1" StartBit="3" StartByte="2" ValueType="Bool" Value="false" TrueValue="0" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MVILineDensity" ModelType="MVI" BitCount="1" StartBit="3" StartByte="2" ValueType="Bool" Value="false" TrueValue="0" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="ElastoColorLineDensity" BitCount="1" StartBit="3" StartByte="2" ValueType="Bool" Value="false" TrueValue="0" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SteeringAngle" BitCount="3" StartBit="4" StartByte="2" IsPresetParameter="true"/>
                <Parameter Name="DopSteeringAngle" BitCount="3" StartBit="4" StartByte="2" MissingDefaultValue = "0" IsPresetParameter="true"/>
                <Parameter Name="Linear" BitCount="1" StartBit="7" StartByte="2" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="FocusNumB" ModelType="B" BitCount="2" StartBit="0" StartByte="3" HighBitCount="2" HighStartBit="4" HighStartByte="336" Max="8" IsPresetParameter="true"/>
                <Parameter Name="FocusPosB" ModelType="B" BitCount="4" StartBit="2" StartByte="3" IsPresetParameter="true"/>
                <Parameter Name="FocusNumM" BitCount="2" StartBit="0" StartByte="3" HighBitCount="2" HighStartBit="4" HighStartByte="336" Min="0" Max="0" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="FocusPosM" BitCount="4" StartBit="2" StartByte="3" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="Deep" BitCount="1" StartBit="6" StartByte="3" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="HalfHeight" BitCount="1" StartBit="7" StartByte="3" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="ImageMode" BitCount="4" StartBit="0" StartByte="4" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="SyncMode" BitCount="3" StartBit="4" StartByte="4" IsPresetParameter="false"/>
                <Parameter Name="HighDensity" ModelType="B" BitCount="1" StartBit="7" StartByte="4" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<!--                <Parameter Name="HighDensityInC" BitCount="1" StartBit="7" StartByte="4" Max="2" IsPresetParameter="true" IsDirectValue="true"/>-->
<!--                <Parameter Name="HighDensityInTDI" BitCount="1" StartBit="7" StartByte="4" Max="2" IsPresetParameter="true" IsDirectValue="true"/>-->
                <Parameter Name="CQYZ" ModelType="B" BitCount="2" StartBit="0" StartByte="676" HighBitCount="6" HighStartBit="0" HighStartByte="5" ThirdBitCount="1" ThirdStartBit="7" ThirdStartByte="158" IsPresetParameter="true"/>
                <Parameter Name="MVelocity" BitCount="2" StartBit="6" StartByte="5" IsPresetParameter="true"/>
                <Parameter Name="DVelocity" BitCount="2" StartBit="6" StartByte="5" HighBitCount="1" HighStartBit="1" HighStartByte="749" Max="7" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DVelocityTDI" ModelType="TDI" BitCount="2" StartBit="6" StartByte="5" HighBitCount="1" HighStartBit="1" HighStartByte="749" Max="7" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWDVelocity" ModelType="CW" BitCount="2" StartBit="6" StartByte="5" HighBitCount="1" HighStartBit="1" HighStartByte="749" Max="7" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="FreeMVelocity" BitCount="2" StartBit="6" StartByte="5" DefaultValue="3" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="ColorImageMode" BitCount="3" StartBit="0" StartByte="6" IsPresetParameter="false"/>
                <Parameter Name="FreqSpectrum" BitCount="1" StartBit="3" StartByte="6" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="LowVelocityBlood" BitCount="1" StartBit="4" StartByte="6" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="BaseLine" BitCount="3" StartBit="5" StartByte="6" Max="6" IsPresetParameter="true"/>
                <Parameter Name="BaseLineDTDI" ModelType="TDI" BitCount="3" StartBit="5" StartByte="6" Max="6" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="BaseLineCWD" ModelType="CW" BitCount="3" StartBit="5" StartByte="6" Max="6" IsPresetParameter="true" IsDirectValue="true"/>

                <Parameter Name="GainColor" BitCount="8" StartBit="0" StartByte="7" IsPresetParameter="true"/>
                <Parameter Name="GainPD" ModelType="PD" BitCount="8" StartBit="0" StartByte="7" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="GainSN" ModelType="SonoNeedle" BitCount="8" StartBit="0" StartByte="7" DefaultValue="200" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="GainTDI" ModelType="TDI" BitCount="8" StartBit="0" StartByte="7" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="GainMVI" ModelType="MVI" BitCount="8" StartBit="0" StartByte="7" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CET" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true"/>
                <Parameter Name="PDCET" ModelType="PD" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SNCET" ModelType="SonoNeedle" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DPDCET" ModelType="DPD" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TDICET" ModelType="TDI" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MVICET" ModelType="MVI" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TNR" BitCount="1" StartBit="5" StartByte="8" IsPresetParameter="true"/>
                <Parameter Name="TNFtr" BitCount="1" StartBit="6" StartByte="8" IsPresetParameter="true"/>
                <Parameter Name="ConfigDone" BitCount="1" StartBit="7" StartByte="8" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="DynamicRange" ModelType="B" BitCount="4" StartBit="0" StartByte="9" IsPresetParameter="true"/>
                <Parameter Name="FourDDynamicRange" BitCount="4" StartBit="0" StartByte="9" IsPresetParameter="true"/>
                <Parameter Name="FourD" BitCount="1" StartBit="4" StartByte="9" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="FrameAvg" ModelType="B" BitCount="3" StartBit="5" StartByte="9" IsPresetParameter="true"/>
                <Parameter Name="AccCountColor" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true"/>
                <Parameter Name="AccCountDop" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AccCountPD" ModelType="PD" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AccCountSN" ModelType="SonoNeedle" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AccCountTDI" ModelType="TDI" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AccCountMVI" ModelType="MVI" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SampleRateDop" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true"/>
                <Parameter Name="SampleRateDopSN" ModelType="SonoNeedle" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true"/>
                <Parameter Name="SampleRateDopTDI" ModelType="TDI" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SampleRateDopTM" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SampleRateDopElasto" BitCount="4" StartBit="0" StartByte="11" DefaultValue="3" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SampleRateDopMVI" ModelType="MVI" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DSampleRateDop" BitCount="4" StartBit="4" StartByte="11" IsPresetParameter="true"/>
                <Parameter Name="DSampleRateDopTDI" ModelType="TDI" BitCount="4" StartBit="4" StartByte="11" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DSampleRateDopTM" BitCount="4" StartBit="4" StartByte="11" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WallFilterDop" BitCount="2" StartBit="0" StartByte="12" IsPresetParameter="true"/>
                <Parameter Name="WallFilterDopTDI" ModelType="TDI" BitCount="2" StartBit="0" StartByte="12" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WallFilterCWD" ModelType="CW" BitCount="2" StartBit="0" StartByte="12" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WallFilterColor" BitCount="2" StartBit="2" StartByte="12" IsPresetParameter="true"/>
                <Parameter Name="WallFilterPD" ModelType="PD" BitCount="2" StartBit="2" StartByte="12" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WallFilterSN" ModelType="SonoNeedle" BitCount="2" StartBit="2" StartByte="12" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WallFilterMVI" ModelType="MVI" BitCount="2" StartBit="2" StartByte="12" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WallThreshold" BitCount="4" StartBit="4" StartByte="12" Max="14" IsPresetParameter="true"/>
                <Parameter Name="WallThresholdPD" ModelType="PD" BitCount="4" StartBit="4" StartByte="12" Max="14" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WallThresholdTDI" ModelType="TDI" BitCount="4" StartBit="4" StartByte="12" Max="14" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WallThresholdMVI" ModelType="MVI" BitCount="4" StartBit="4" StartByte="12" Max="15" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SampleVolume" BitCount="3" StartBit="0" StartByte="13" IsPresetParameter="true"/>
                <Parameter Name="SampleVolumeTDI" ModelType="TDI" BitCount="3" StartBit="0" StartByte="13" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TriplexMode" BitCount="1" StartBit="3" StartByte="13" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="TriplexModeTDI" ModelType="TDI" BitCount="1" StartBit="3" StartByte="13" IsPresetParameter="false" ValueType="Bool" Value="false" TrueValue="1" IsDirectValue="true"/>
                <Parameter Name="FrameAvgColor" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true"/>
                <Parameter Name="FrameAvgPD" ModelType="PD" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="FrameAvgSN" ModelType="SonoNeedle" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="FrameAvgTDI" ModelType="TDI" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="FrameAvgMVI" ModelType="MVI" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CVRT" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true"/>
                <Parameter Name="PDCVRT" ModelType="PD" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SNCVRT" ModelType="SonoNeedle" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DPDCVRT" ModelType="DPD" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TDICVRT" ModelType="TDI" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MVICVRT" ModelType="MVI" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="Scpd" ModelType="B" BitCount="2" StartBit="4" StartByte="14" HighBitCount="1" HighStartBit="7" HighStartByte="788" Max="7" IsPresetParameter="true"/>
                <Parameter Name="ScpdTrape" BitCount="2" StartBit="4" StartByte="14" HighBitCount="1" HighStartBit="7" HighStartByte="788" Max="7" IsPresetParameter="true"/>
                <Parameter Name="ScpdOther" BitCount="2" StartBit="4" StartByte="14" HighBitCount="1" HighStartBit="7" HighStartByte="788" Value="0" IsPresetParameter="false" IsDirectValue="true"/>
                <Parameter Name="AcousticPowerTestCode" BitCount="2" StartBit="6" StartByte="14" HighBitCount="1" HighStartBit="7" HighStartByte="862" Max="5" IsPresetParameter="false"/>
                <Parameter Name="MScanLine" BitCount="8" StartBit="0" StartByte="15" HighBitCount="1" HighStartBit="0" HighStartByte="231" IsPresetParameter="true"/>
                <Parameter Name="DScanLine" BitCount="8" StartBit="0" StartByte="15" HighBitCount="1" HighStartBit="0" HighStartByte="231" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DScanLineTDI" ModelType="TDI" BitCount="8" StartBit="0" StartByte="15" HighBitCount="1" HighStartBit="0" HighStartByte="231" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="StartLine" ModelType="B" BitCount="8" StartBit="0" StartByte="16" HighBitCount="1" HighStartBit="0" HighStartByte="230" IsPresetParameter="true"/>
                <Parameter Name="StopLine" ModelType="B" BitCount="8" StartBit="0" StartByte="17" HighBitCount="1" HighStartBit="2" HighStartByte="230" IsPresetParameter="true"/>
                <Parameter Name="FourDStartLine" BitCount="8" StartBit="0" StartByte="16" HighBitCount="1" HighStartBit="0" HighStartByte="230" IsPresetParameter="false"/>
                <Parameter Name="FourDStopLine" BitCount="8" StartBit="0" StartByte="17" HighBitCount="1" HighStartBit="2" HighStartByte="230" IsPresetParameter="false"/>
                <Parameter Name="StartLineColor" BitCount="8" StartBit="0" StartByte="18" HighBitCount="1" HighStartBit="4" HighStartByte="230" IsPresetParameter="false"/>
                <Parameter Name="StopLineColor" BitCount="8" StartBit="0" StartByte="19" HighBitCount="1" HighStartBit="6" HighStartByte="230" IsPresetParameter="false"/>
                <Parameter Name="TopBorderColor" BitCount="8" StartBit="0" StartByte="20" HighBitCount="1" HighStartBit="1" HighStartByte="230" IsPresetParameter="false"/>
                <Parameter Name="BottomBorderColor" BitCount="8" StartBit="0" StartByte="21" HighBitCount="1" HighStartBit="3" HighStartByte="230" IsPresetParameter="false"/>
                <Parameter Name="SampleDepthDop" BitCount="8" StartBit="0" StartByte="22" HighBitCount="1" HighStartBit="5" HighStartByte="230" IsPresetParameter="false"/>
                <Parameter Name="AngleSpacing" BitCount="8" StartBit="0" StartByte="23" IsPresetParameter="false"/>
                <Parameter Name="LineSpacing" ModelType="B" BitCount="8" StartBit="0" StartByte="24" HighBitCount="8" HighStartBit="0" HighStartByte="25" IsPresetParameter="false"/>
                <Parameter Name="Gain" ModelType="B" BitCount="8" StartBit="0" StartByte="26" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="GainThi" ModelType="B" BitCount="8" StartBit="0" StartByte="26" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="FourDGain" BitCount="8" StartBit="0" StartByte="26" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TGC8" ModelType="B" BitCount="8" StartBit="0" StartByte="27" IsPresetParameter="false"/>
                <Parameter Name="TGC1" ModelType="B" BitCount="8" StartBit="0" StartByte="28" IsPresetParameter="false"/>
                <Parameter Name="TGC2" ModelType="B" BitCount="8" StartBit="0" StartByte="29" IsPresetParameter="false"/>
                <Parameter Name="TGC3" ModelType="B" BitCount="8" StartBit="0" StartByte="30" IsPresetParameter="false"/>
                <Parameter Name="TGC4" ModelType="B" BitCount="8" StartBit="0" StartByte="31" IsPresetParameter="false"/>
                <Parameter Name="TGC5" ModelType="B" BitCount="8" StartBit="0" StartByte="32" IsPresetParameter="false"/>
                <Parameter Name="TGC6" ModelType="B" BitCount="8" StartBit="0" StartByte="33" IsPresetParameter="false"/>
                <Parameter Name="TGC7" ModelType="B" BitCount="8" StartBit="0" StartByte="34" IsPresetParameter="false"/>
                <Parameter Name="SlopeDis" BitCount="8" StartBit="0" StartByte="35" HighBitCount="4" HighStartBit="4" HighStartByte="37" IsPresetParameter="false"/>
                <Parameter Name="PerpendicularDis" BitCount="8" StartBit="0" StartByte="36" HighBitCount="4" HighStartBit="0" HighStartByte="37" IsPresetParameter="false"/>
                <Parameter Name="ZoomDepth" BitCount="8" StartBit="0" StartByte="38" HighBitCount="8" HighStartBit="0" HighStartByte="39" IsPresetParameter="false"/>
                <Parameter Name="TransferSignal" BitCount="1" StartBit="3" StartByte="40" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="SpectralInvert" BitCount="1" StartBit="4" StartByte="40" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="Smooth" ModelType="B" BitCount="3" StartBit="5" StartByte="40" IsPresetParameter="true"/>
                <Parameter Name="FocusNumC" BitCount="2" StartBit="0" StartByte="41" Min="0" Max="0" IsPresetParameter="true"/>
                <Parameter Name="FocusPosC" BitCount="4" StartBit="2" StartByte="41" IsPresetParameter="true"/>
                <Parameter Name="Deep2" BitCount="1" StartBit="6" StartByte="41" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="HalfHeight2" BitCount="1" StartBit="7" StartByte="41" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="GainDop" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true"/>
                <Parameter Name="GainDopTM" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="GainDopTDI" ModelType="TDI" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="GainDopCWD" ModelType="CW" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="Volume" BitCount="4" StartBit="0" StartByte="43" HighBitCount="3" HighStartBit="0" HighStartByte="268" Step="1" IsPresetParameter="true"/>
                <Parameter Name="VolumeTDI" ModelType="TDI" BitCount="4" StartBit="0" StartByte="43" HighBitCount="3" HighStartBit="0" HighStartByte="268" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="VolumeCWD" ModelType="CW" BitCount="4" StartBit="0" StartByte="43" HighBitCount="3" HighStartBit="0" HighStartByte="268" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MB" ModelType="B" BitCount="2" StartBit="4" StartByte="43" IsPresetParameter="true"/>
                <Parameter Name="ElastoMB" BitCount="2" StartBit="4" StartByte="43" Value="1" IsPresetParameter="false"/>
                <Parameter Name="BStearingAngle" BitCount="2" StartBit="6" StartByte="43" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexFrequencyCompounding" ModelType="B" BitCount="3" StartBit="0" StartByte="44" IsPresetParameter="true"/>
                <Parameter Name="CRCOpenFlag" BitCount="1" StartBit="3" StartByte="44" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="FocusPosFrequencyCompounding" ModelType="B" BitCount="4" StartBit="4" StartByte="44" IsPresetParameter="true"/>
                <Parameter Name="AcousticPowerB" ModelType="B" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AcousticPowerBThi" ModelType="B" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AcousticPowerColor" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AcousticPowerTDI" ModelType="TDI" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AcousticPowerDop" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AcousticPowerElasto" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AcousticPowerM" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AcousticPowerMThi" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MPEn" BitCount="1" StartBit="0" StartByte="46" IsPresetParameter="false"/>
                <Parameter Name="BFEn" BitCount="1" StartBit="1" StartByte="46" IsPresetParameter="false"/>
                <Parameter Name="DscEn" BitCount="1" StartBit="3" StartByte="46" IsPresetParameter="false"/>
                <Parameter Name="DspEn" BitCount="1" StartBit="0" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="DbfEn" BitCount="1" StartBit="1" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="PwrEn" BitCount="1" StartBit="2" StartByte="46" IsPresetParameter="false"/>
                <Parameter Name="UsbEn" BitCount="1" StartBit="3" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="Prog" BitCount="1" StartBit="4" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="CFGStart" BitCount="1" StartBit="5" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="BEStart" BitCount="1" StartBit="6" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="WRClr" BitCount="1" StartBit="7" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="LCDLight" BitCount="8" StartBit="0" StartByte="47" Min="30" Max="100" Value="61" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="CVLT" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true"/>
                <Parameter Name="PDCVLT" ModelType="PD" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SNCVLT" ModelType="SonoNeedle" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DPDCVLT" ModelType="DPD" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TDICVLT" ModelType="TDI" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MVICVLT" ModelType="MVI" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="ExceedLowBlood" BitCount="1" StartBit="7" StartByte="48" IsPresetParameter="false"/>
                <Parameter Name="DSC" BitCount="1" StartBit="0" StartByte="49" ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="CFVelocityThreshold" BitCount="3" StartBit="1" StartByte="49" Max="5" IsPresetParameter="true"/>
                <Parameter Name="PDVelocityThreshold" ModelType="PD" BitCount="3" StartBit="1" StartByte="49" Max="5" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SNVelocityThreshold" ModelType="SonoNeedle" BitCount="3" StartBit="1" StartByte="49" Max="5" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TDIVelocityThreshold" ModelType="TDI" BitCount="3" StartBit="1" StartByte="49" Max="5" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MVIVelocityThreshold" ModelType="MVI" BitCount="3" StartBit="1" StartByte="49" Max="5" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="StateClearFlag" BitCount="1" StartBit="4" StartByte="49" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="ColorRegionThreshold" BitCount="2" StartBit="5" StartByte="49" IsPresetParameter="true"/>
                <Parameter Name="PDRegionThreshold" ModelType="PD" BitCount="2" StartBit="5" StartByte="49" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SNRegionThreshold" ModelType="SonoNeedle" BitCount="2" StartBit="5" StartByte="49" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TDIRegionThreshold" ModelType="TDI" BitCount="2" StartBit="5" StartByte="49" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MVIRegionThreshold" ModelType="MVI" BitCount="2" StartBit="5" StartByte="49" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WrReset" BitCount="1" StartBit="7" StartByte="49" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="CHET" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true"/>
                <Parameter Name="PDCHET" ModelType="PD" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SNCHET" ModelType="SonoNeedle" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DPDCHET" ModelType="DPD" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TDICHET" ModelType="TDI" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MVICHET" ModelType="MVI" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WT" BitCount="1" StartBit="7" StartByte="50" Value = "1" IsPresetParameter="true"/>
                <Parameter Name="CTGC" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true"/>
                <Parameter Name="PDCTGC" ModelType="PD" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SNCTGC" ModelType="SonoNeedle" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DPDCTGC" ModelType="DPD" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TDICTGC" ModelType="TDI" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MVICTGC" ModelType="MVI" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="BCosAngle" BitCount="8" StartBit="0" StartByte="52" HighBitCount="8" HighStartBit="0" HighStartByte="53" IsPresetParameter="false"/>
                <Parameter Name="BTgAngle" BitCount="8" StartBit="0" StartByte="54" HighBitCount="8" HighStartBit="0" HighStartByte="55" IsPresetParameter="false"/>
                <Parameter Name="DSPReset" BitCount="1" StartBit="0" StartByte="56" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="DBFReset" BitCount="1" StartBit="1" StartByte="56" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="DBFInfo" BitCount="2" StartBit="2" StartByte="56" IsPresetParameter="false"/>
                <Parameter Name="TestSignal" BitCount="1" StartBit="5" StartByte="56" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="TestParameter" BitCount="2" StartBit="6" StartByte="56" IsPresetParameter="false"/>
                <Parameter Name="BPulseNum" ModelType="B" BitCount="2" StartBit="0" StartByte="57" Value="1" IsPresetParameter="false"/>
                <Parameter Name="CPulseNum" BitCount="3" StartBit="2" StartByte="57" Value="1" IsPresetParameter="false"/>
                <Parameter Name="DPulseNum" BitCount="3" StartBit="5" StartByte="57" Value="1" IsPresetParameter="true"/>
                <Parameter Name="DPulseNumTDI" ModelType="TDI" BitCount="3" StartBit="5" StartByte="57" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
                <!--CWDPluseNum Lotus中暂时未下发控制字2015-08-06-->
                <Parameter Name="CWDPluseNum" ModelType="CW" BitCount="3" StartBit="5" StartByte="57" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="BloodEffection" BitCount="2" StartBit="0" StartByte="58" IsPresetParameter="true"/>
                <Parameter Name="BloodEffectionPD" ModelType="PD" BitCount="2" StartBit="0" StartByte="58" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="BloodEffectionSN" ModelType="SonoNeedle" BitCount="2" StartBit="0" StartByte="58" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="BloodEffectionTDI" ModelType="TDI" BitCount="2" StartBit="0" StartByte="58" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="BloodEffectionMVI" ModelType="MVI" BitCount="2" StartBit="0" StartByte="58" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="LogCompression" ModelType="B" BitCount="3" StartBit="2" StartByte="58" IsPresetParameter="false"/>
                <Parameter Name="HighFreqPhasedProbe" BitCount="1" StartBit="5" StartByte="58" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="PhaseProbeId" BitCount="1" StartBit="6" StartByte="58" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="THI" ModelType="B" BitCount="1" StartBit="7" StartByte="58" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="ImageZoomCoefBin" BitCount="8" StartBit="0" StartByte="59" HighBitCount="8" HighStartBit="0" HighStartByte="60" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc00" ModelType="B" BitCount="8" StartBit="0" StartByte="61" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc01" ModelType="B" BitCount="8" StartBit="0" StartByte="62" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc02" ModelType="B" BitCount="8" StartBit="0" StartByte="63" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc03" ModelType="B" BitCount="8" StartBit="0" StartByte="64" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc04" ModelType="B" BitCount="8" StartBit="0" StartByte="65" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc05" ModelType="B" BitCount="8" StartBit="0" StartByte="66" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc06" ModelType="B" BitCount="8" StartBit="0" StartByte="67" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc07" ModelType="B" BitCount="8" StartBit="0" StartByte="68" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc08" ModelType="B" BitCount="8" StartBit="0" StartByte="69" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc09" ModelType="B" BitCount="8" StartBit="0" StartByte="70" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc10" ModelType="B" BitCount="8" StartBit="0" StartByte="71" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc11" ModelType="B" BitCount="8" StartBit="0" StartByte="72" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc12" ModelType="B" BitCount="8" StartBit="0" StartByte="73" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc13" ModelType="B" BitCount="8" StartBit="0" StartByte="74" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc14" ModelType="B" BitCount="8" StartBit="0" StartByte="75" IsPresetParameter="false"/>
                <Parameter Name="UniformityTgc15" ModelType="B" BitCount="8" StartBit="0" StartByte="76" IsPresetParameter="false"/>
                <Parameter Name="DebugPara0" BitCount="8" StartBit="0" StartByte="77" IsPresetParameter="false"/>
                <Parameter Name="DebugPara3" BitCount="4" StartBit="0" StartByte="78" IsPresetParameter="false"/>
                <Parameter Name="DebugPara2" BitCount="3" StartBit="4" StartByte="78" IsPresetParameter="false"/>
                <Parameter Name="DebugPara1" BitCount="1" StartBit="7" StartByte="78" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc0" ModelType="B" BitCount="8" StartBit="0" StartByte="79" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc1" ModelType="B" BitCount="8" StartBit="0" StartByte="80" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc2" ModelType="B" BitCount="8" StartBit="0" StartByte="81" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc3" ModelType="B" BitCount="8" StartBit="0" StartByte="82" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc4" ModelType="B" BitCount="8" StartBit="0" StartByte="83" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc5" ModelType="B" BitCount="8" StartBit="0" StartByte="84" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc6" ModelType="B" BitCount="8" StartBit="0" StartByte="85" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc7" ModelType="B" BitCount="8" StartBit="0" StartByte="86" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc8" ModelType="B" BitCount="8" StartBit="0" StartByte="687" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc9" ModelType="B" BitCount="8" StartBit="0" StartByte="688" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc10" ModelType="B" BitCount="8" StartBit="0" StartByte="689" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc11" ModelType="B" BitCount="8" StartBit="0" StartByte="690" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc12" ModelType="B" BitCount="8" StartBit="0" StartByte="691" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc13" ModelType="B" BitCount="8" StartBit="0" StartByte="692" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc14" ModelType="B" BitCount="8" StartBit="0" StartByte="693" IsPresetParameter="false"/>
                <Parameter Name="AnalogTgc15" ModelType="B" BitCount="8" StartBit="0" StartByte="694" IsPresetParameter="false"/>

                <Parameter Name="FrequencyOfTransmit1" ModelType="B" BitCount="3" StartBit="0" StartByte="87" HighBitCount="1" HighStartBit="7" HighStartByte="87" ThirdBitCount="1" ThirdStartBit="0" ThirdStartByte="711" IsPresetParameter="false"/>
                <Parameter Name="HighFreqOfTransmit1" ModelType="B" BitCount="1" StartBit="3" StartByte="87" IsPresetParameter="false"/>
                <Parameter Name="PulseNumOfTransmit1" ModelType="B" BitCount="3" StartBit="4" StartByte="87" Value="1" IsPresetParameter="false"/>
                <Parameter Name="FocusNumOfTransmit1" ModelType="B" BitCount="2" StartBit="0" StartByte="88" IsPresetParameter="false"/>
                <Parameter Name="FilterCoefOfTransmit1" ModelType="B" BitCount="4" StartBit="4" StartByte="88" IsPresetParameter="false"/>
                <Parameter Name="VHSi" BitCount="3" StartBit="0" StartByte="89" IsPresetParameter="true"/>
                <Parameter Name="Left" BitCount="1" StartBit="0" StartByte="90" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
                <Parameter Name="Up" BitCount="1" StartBit="1" StartByte="90" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
                <Parameter Name="MBColor" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="true"/>
                <Parameter Name="COverlap" BitCount="1" StartBit="7" StartByte="1023" ValueType="Bool" Value="false" IsPresetParameter="true"/>
                <Parameter Name="ElastoMBColor" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="false"/>
                <Parameter Name="MBPD" ModelType="PD" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MBSN" ModelType="SonoNeedle" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MBTDI" ModelType="TDI" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MBMVI" ModelType="MVI" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="ShutDown" BitCount="1" StartBit="5" StartByte="90" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="NotReduceGain" BitCount="1" StartBit="6" StartByte="90" ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="FrequencyOfTransmit2" ModelType="B" BitCount="3" StartBit="0" StartByte="91" HighBitCount="1" HighStartBit="7" HighStartByte="91" ThirdBitCount="1" ThirdStartBit="1" ThirdStartByte="711" IsPresetParameter="false"/>
                <Parameter Name="HighFreqOfTransmit2" ModelType="B" BitCount="1" StartBit="3" StartByte="91" IsPresetParameter="false"/>
                <Parameter Name="PulseNumOfTransmit2" ModelType="B" BitCount="3" StartBit="4" StartByte="91" Value="1" IsPresetParameter="false"/>
                <Parameter Name="FocusNumOfTransmit2" ModelType="B" BitCount="2" StartBit="0" StartByte="92" IsPresetParameter="false"/>
                <Parameter Name="Rotation" Value="0" Max="270" Step="90" BitCount="2" StartBit="2" StartByte="92" IsPresetParameter="true"/>
                <Parameter Name="FilterCoefOfTransmit2" ModelType="B" BitCount="4" StartBit="4" StartByte="92" IsPresetParameter="false"/>
                <Parameter Name="Alpha" BitCount="8" StartBit="0" StartByte="93" IsPresetParameter="false"/>
                <Parameter Name="Beta" BitCount="8" StartBit="0" StartByte="94" IsPresetParameter="false"/>
                <Parameter Name="FrequencyOfTransmit3" ModelType="B" BitCount="3" StartBit="0" StartByte="95" HighBitCount="1" HighStartBit="7" HighStartByte="95" ThirdBitCount="1" ThirdStartBit="2" ThirdStartByte="711" IsPresetParameter="false"/>
                <Parameter Name="HighFreqOfTransmit3" ModelType="B" BitCount="1" StartBit="3" StartByte="95" IsPresetParameter="false"/>
                <Parameter Name="PulseNumOfTransmit3" ModelType="B" BitCount="3" StartBit="4" StartByte="95" Value="1" IsPresetParameter="false"/>
                <Parameter Name="FocusNumOfTransmit3" ModelType="B" BitCount="2" StartBit="0" StartByte="96" IsPresetParameter="false"/>
                <Parameter Name="FilterCoefOfTransmit3" ModelType="B" BitCount="4" StartBit="4" StartByte="96" IsPresetParameter="false"/>
                <Parameter Name="Gamma" BitCount="8" StartBit="0" StartByte="97" IsPresetParameter="false"/>
                <Parameter Name="WtStartPoint" BitCount="8" StartBit="0" StartByte="98" IsPresetParameter="false"/>
                <Parameter Name="FrequencyOfTransmit4" ModelType="B" BitCount="3" StartBit="0" StartByte="99" HighBitCount="1" HighStartBit="7" HighStartByte="99" ThirdBitCount="1" ThirdStartBit="3" ThirdStartByte="711" IsPresetParameter="false"/>
                <Parameter Name="HighFreqOfTransmit4" ModelType="B" BitCount="1" StartBit="3" StartByte="99" IsPresetParameter="false"/>
                <Parameter Name="PulseNumOfTransmit4" ModelType="B" BitCount="3" StartBit="4" StartByte="99" Value="1" IsPresetParameter="false"/>
                <Parameter Name="FocusNumOfTransmit4" ModelType="B" BitCount="2" StartBit="0" StartByte="100" IsPresetParameter="false"/>
                <Parameter Name="FilterCoefOfTransmit4" ModelType="B" BitCount="4" StartBit="4" StartByte="100" IsPresetParameter="false"/>
                <Parameter Name="LeeMCoef" BitCount="8" StartBit="0" StartByte="101" IsPresetParameter="false"/>
                <Parameter Name="LeeSCoef" BitCount="8" StartBit="0" StartByte="102" IsPresetParameter="false"/>
                <Parameter Name="MFC" ModelType="B" BitCount="2" StartBit="0" StartByte="103" IsPresetParameter="false"/>
                <Parameter Name="CWEn" BitCount="1" StartBit="2" StartByte="103" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="TDIEn" ModelType="TDI" BitCount="1" StartBit="4" StartByte="103" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="PWEnhance" BitCount="2" StartBit="5" StartByte="103" IsPresetParameter="true"/>
                <Parameter Name="PWEnhanceTM" BitCount="2" StartBit="5" StartByte="103" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PWEnhanceTDI" ModelType="TDI" BitCount="2" StartBit="5" StartByte="103" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PWEnhanceCWD" ModelType="CW" BitCount="2" StartBit="5" StartByte="103" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="LeeEn" BitCount="1" StartBit="7" StartByte="103" IsPresetParameter="false"/>
                <Parameter Name="HF_Alpha" BitCount="4" StartBit="0" StartByte="104" IsPresetParameter="false"/>
                <Parameter Name="HF_Alpha_Inc" BitCount="4" StartBit="0" StartByte="104" IsPresetParameter="false" IsDirectValue="true"/>
                <Parameter Name="IIR_ON" BitCount="1" StartBit="4" StartByte="104" IsPresetParameter="false"/>
                <Parameter Name="HF" BitCount="3" StartBit="5" StartByte="104" IsPresetParameter="false"/>
                <Parameter Name="HFInc" BitCount="3" StartBit="5" StartByte="104" IsPresetParameter="false" IsDirectValue="true"/>
                <Parameter Name="PixelRatio" BitCount="3" StartBit="2" StartByte="105" IsPresetParameter="true"/>
                <Parameter Name="PixelRatioTM" BitCount="3" StartBit="2" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PixelRatioTDI" ModelType="TDI" BitCount="3" StartBit="2" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PixelRatioCWD" ModelType="CW" BitCount="3" StartBit="2" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="GateSegment" BitCount="2" StartBit="5" StartByte="105" IsPresetParameter="true"/>
                <Parameter Name="GateSegmentTDI" ModelType="TDI" BitCount="2" StartBit="5" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="GateSegmentCWD" ModelType="CW" BitCount="2" StartBit="5" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="Bi_CPA" BitCount="1" StartBit="7" StartByte="105" IsPresetParameter="false"/>
                <Parameter Name="DynStartDepthBin" BitCount="8" StartBit="0" StartByte="106" HighBitCount="8" HighStartBit="0" HighStartByte="107" IsPresetParameter="false"/>
                <Parameter Name="DynDBRate" BitCount="8" StartBit="0" StartByte="108" HighBitCount="8" HighStartBit="0" HighStartByte="109" IsPresetParameter="false"/>
                <Parameter Name="DynDBCount" BitCount="4" StartBit="0" StartByte="110" IsPresetParameter="false"/>
                <Parameter Name="WtCoefficience" BitCount="4" StartBit="4" StartByte="110" IsPresetParameter="false"/>
                <Parameter Name="TransmitPowerPositiveVol" BitCount="8" StartBit="0" StartByte="111" IsPresetParameter="false"/>
                <Parameter Name="TransmitPowerNegativeVol" BitCount="8" StartBit="0" StartByte="112" IsPresetParameter="false"/>
                <!--AcousticPowerCWD  (255 - value);        //电源针对CW的控制反了，0是电压最大，255电压最低，所以这里要特殊处理-->
                <Parameter Name="CWTransmitPowerPositiveVol" BitCount="8" StartBit="0" StartByte="113" IsPresetParameter="true"/>
                <Parameter Name="CWTransmitPowerNegativeVol" BitCount="8" StartBit="0" StartByte="114" IsPresetParameter="false"/>
                <Parameter Name="LeeGain" BitCount="4" StartBit="0" StartByte="115" IsPresetParameter="false"/>
                <Parameter Name="LeeShift" BitCount="4" StartBit="4" StartByte="115" IsPresetParameter="false"/>
                <Parameter Name="PWDynamicRange" BitCount="3" StartBit="0" StartByte="116" IsPresetParameter="true"/>
                <Parameter Name="PWDynamicRangeTM" BitCount="3" StartBit="0" StartByte="116" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PWDynamicRangeTDI" ModelType="TDI" BitCount="3" StartBit="0" StartByte="116" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PWDynamicRangeCWD" ModelType="CW" BitCount="3" StartBit="0" StartByte="116" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DPProbeImageSwitch" BitCount="2" StartBit="3" StartByte="116" IsPresetParameter="true"/>
                <Parameter Name="DPProbeIdentity" BitCount="1" StartBit="5" StartByte="116" IsPresetParameter="true"/>
                <Parameter Name="HV_On" BitCount="1" StartBit="6" StartByte="116" IsPresetParameter="false"/>
                <Parameter Name="MEProb" BitCount="1" StartBit="7" StartByte="116" IsPresetParameter="false"/>
                <Parameter Name="AFCoef0" BitCount="8" StartBit="0" StartByte="117" HighBitCount="1" HighStartBit="0" HighStartByte="130" IsPresetParameter="false"/>
                <Parameter Name="AFCoef1" BitCount="8" StartBit="0" StartByte="118" HighBitCount="1" HighStartBit="1" HighStartByte="130" IsPresetParameter="false"/>
                <Parameter Name="AFCoef2" BitCount="8" StartBit="0" StartByte="119" HighBitCount="1" HighStartBit="2" HighStartByte="130" IsPresetParameter="false"/>
                <Parameter Name="AFCoef3" BitCount="8" StartBit="0" StartByte="120" HighBitCount="1" HighStartBit="3" HighStartByte="130" IsPresetParameter="false"/>
                <Parameter Name="PhasedProbeTgc0" ModelType="B" BitCount="8" StartBit="0" StartByte="121" IsPresetParameter="false"/>
                <Parameter Name="PhasedProbeTgc1" ModelType="B" BitCount="8" StartBit="0" StartByte="122" IsPresetParameter="false"/>
                <Parameter Name="PhasedProbeTgc2" ModelType="B" BitCount="8" StartBit="0" StartByte="123" IsPresetParameter="false"/>
                <Parameter Name="PhasedProbeTgc3" ModelType="B" BitCount="8" StartBit="0" StartByte="124" IsPresetParameter="false"/>
                <Parameter Name="PhasedProbeTgc4" ModelType="B" BitCount="8" StartBit="0" StartByte="125" IsPresetParameter="false"/>
                <Parameter Name="PhasedProbeTgc5" ModelType="B" BitCount="8" StartBit="0" StartByte="126" IsPresetParameter="false"/>
                <Parameter Name="PhasedProbeTgc6" ModelType="B" BitCount="8" StartBit="0" StartByte="127" IsPresetParameter="false"/>
                <Parameter Name="PhasedProbeTgc7" ModelType="B" BitCount="8" StartBit="0" StartByte="128" IsPresetParameter="false"/>
                <Parameter Name="AFCoef4" BitCount="8" StartBit="0" StartByte="129" HighBitCount="1" HighStartBit="4" HighStartByte="130" IsPresetParameter="false"/>
                <Parameter Name="TDIWallfilter" ModelType="TDI" BitCount="2" StartBit="6" StartByte="130" IsPresetParameter="true"/>
                <Parameter Name="AFGain" BitCount="4" StartBit="0" StartByte="131" IsPresetParameter="false"/>
                <Parameter Name="AFShift" BitCount="4" StartBit="4" StartByte="131" IsPresetParameter="false"/>
                <Parameter Name="Image1FGC0" BitCount="8" StartBit="0" StartByte="132" IsPresetParameter="false"/>
                <Parameter Name="Image1FGC1" BitCount="8" StartBit="0" StartByte="133" IsPresetParameter="false"/>
                <Parameter Name="Image1FGC2" BitCount="8" StartBit="0" StartByte="134" IsPresetParameter="false"/>
                <Parameter Name="Image1FGC3" BitCount="8" StartBit="0" StartByte="135" IsPresetParameter="false"/>
                <Parameter Name="Image2FGC0" BitCount="8" StartBit="0" StartByte="136" IsPresetParameter="false"/>
                <Parameter Name="Image2FGC1" BitCount="8" StartBit="0" StartByte="137" IsPresetParameter="false"/>
                <Parameter Name="Image2FGC2" BitCount="8" StartBit="0" StartByte="138" IsPresetParameter="false"/>
                <Parameter Name="Image2FGC3" BitCount="8" StartBit="0" StartByte="139" IsPresetParameter="false"/>
                <Parameter Name="Image3FGC0" BitCount="8" StartBit="0" StartByte="140" IsPresetParameter="false"/>
                <Parameter Name="Image3FGC1" BitCount="8" StartBit="0" StartByte="141" IsPresetParameter="false"/>
                <Parameter Name="Image3FGC2" BitCount="8" StartBit="0" StartByte="142" IsPresetParameter="false"/>
                <Parameter Name="Image3FGC3" BitCount="8" StartBit="0" StartByte="143" IsPresetParameter="false"/>
                <Parameter Name="Image4FGC0" BitCount="8" StartBit="0" StartByte="144" IsPresetParameter="false"/>
                <Parameter Name="Image4FGC1" BitCount="8" StartBit="0" StartByte="145" IsPresetParameter="false"/>
                <Parameter Name="Image4FGC2" BitCount="8" StartBit="0" StartByte="146" IsPresetParameter="false"/>
                <Parameter Name="Image4FGC3" BitCount="8" StartBit="0" StartByte="147" IsPresetParameter="false"/>
                <Parameter Name="HFSZ" Value="4" BitCount="4" StartBit="0" StartByte="148" IsPresetParameter="false"/>
                <Parameter Name="HFSSP" Value="3" BitCount="3" StartBit="4" StartByte="148" IsPresetParameter="false"/>
                <Parameter Name="ZoomOn" BitCount="1" StartBit="7" StartByte="148" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="ColorInvert" BitCount="1" StartBit="2" StartByte="150" ValueType="Bool" Value="false" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="CWDShift" ModelType="CW" BitCount="3" StartBit="0" StartByte="151" IsPresetParameter="true"/>
                <Parameter Name="ColorM" BitCount="1" StartBit="3" StartByte="151" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="FilterCpd" ModelType="B" BitCount="1" StartBit="7" StartByte="152" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="CWDSampleRate" ModelType="CW" BitCount="4" StartBit="0" StartByte="153" IsPresetParameter="true"/>
                <Parameter Name="CWDDynamicRange" ModelType="CW" BitCount="3" StartBit="4" StartByte="153" IsPresetParameter="true"/>
                <Parameter Name="ExtremeLowBlood" BitCount="1" StartBit="7" StartByte="153" IsPresetParameter="false"/>
                <Parameter Name="ZoomInTopOffset" BitCount="8" StartBit="0" StartByte="154" IsPresetParameter="false"/>
                <Parameter Name="CWDFft" ModelType="CW" Value="200" BitCount="8" StartBit="0" StartByte="155" IsPresetParameter="true"/>
                <Parameter Name="CWDIterations" ModelType="CW" Value="60" BitCount="6" StartBit="0" StartByte="156" IsPresetParameter="true"/>
                <Parameter Name="TriplexRefresh" BitCount="1" StartBit="7" StartByte="156" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="FilterCpdCoe" Value="128" BitCount="8" StartBit="0" StartByte="157" IsPresetParameter="true"/>
                <!--系统参数BFPipelineNumber/IODelayTap，不跟预设值以及频率相关-->
                <Parameter Name="BFPipelineNumber" Value="9" BitCount="6" StartBit="0" StartByte="158" IsPresetParameter="false"/>
                <Parameter Name="LRInvert" BitCount="1" StartBit="6" StartByte="158" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="CpdMinValue" Value="30" BitCount="8" StartBit="0" StartByte="159" IsPresetParameter="false"/>
                <Parameter Name="CpdMaxValue" Value="180" BitCount="8" StartBit="0" StartByte="160" IsPresetParameter="false"/>
                <Parameter Name="CCosAngle" BitCount="8" StartBit="0" StartByte="161" HighBitCount="8" HighStartBit="0" HighStartByte="162" IsPresetParameter="false"/>
                <Parameter Name="CTgAngle" BitCount="8" StartBit="0" StartByte="163" HighBitCount="8" HighStartBit="0" HighStartByte="164" IsPresetParameter="false"/>
                <Parameter Name="BSteeringScan" Value="20" BitCount="6" StartBit="0" Max="40" StartByte="165" IsPresetParameter="true"/>
                <Parameter Name="CDucy" BitCount="2" StartBit="6" StartByte="165" IsPresetParameter="false"/>
                <Parameter Name="CFilterCoef" BitCount="4" StartBit="3" StartByte="166" IsPresetParameter="false"/>
                <Parameter Name="CompensationCoef" Value="255" BitCount="8" StartBit="0" StartByte="167" HighBitCount="3" HighStartBit="0" HighStartByte="166" IsPresetParameter="false"/>
                <Parameter Name="APPrf" BitCount="8" StartBit="0" StartByte="168" HighBitCount="8" HighStartBit="0" HighStartByte="169" IsPresetParameter="false"/>
                <Parameter Name="APDutyRatio" BitCount="8" StartBit="0" StartByte="170" IsPresetParameter="false"/>
                <Parameter Name="ConvexCpdSteer" BitCount="4" StartBit="0" StartByte="171" IsPresetParameter="false"/>
                <Parameter Name="Prf_D_Triplex_Work" BitCount="4" StartBit="4" StartByte="176" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc0" Value="192" BitCount="8" StartBit="0" StartByte="177" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc1" Value="255" BitCount="8" StartBit="0" StartByte="178" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc2" Value="255" BitCount="8" StartBit="0" StartByte="179" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc3" Value="255" BitCount="8" StartBit="0" StartByte="180" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc4" Value="255" BitCount="8" StartBit="0" StartByte="181" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc5" Value="255" BitCount="8" StartBit="0" StartByte="182" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc6" Value="255" BitCount="8" StartBit="0" StartByte="183" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc7" Value="255" BitCount="8" StartBit="0" StartByte="184" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc8" Value="255" BitCount="8" StartBit="0" StartByte="695" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc9" Value="255" BitCount="8" StartBit="0" StartByte="696" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc10" Value="255" BitCount="8" StartBit="0" StartByte="697" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc11" Value="255" BitCount="8" StartBit="0" StartByte="698" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc12" Value="255" BitCount="8" StartBit="0" StartByte="699" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc13" Value="255" BitCount="8" StartBit="0" StartByte="700" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc14" Value="255" BitCount="8" StartBit="0" StartByte="701" IsPresetParameter="false"/>
                <Parameter Name="CFMAnologTgc15" Value="255" BitCount="8" StartBit="0" StartByte="702" IsPresetParameter="false"/>

                <Parameter Name="CFMDigitalTgc0" Value="64" BitCount="8" StartBit="0" StartByte="185" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc1" Value="64" BitCount="8" StartBit="0" StartByte="186" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc2" Value="64" BitCount="8" StartBit="0" StartByte="187" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc3" Value="64" BitCount="8" StartBit="0" StartByte="188" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc4" Value="64" BitCount="8" StartBit="0" StartByte="189" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc5" Value="64" BitCount="8" StartBit="0" StartByte="190" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc6" Value="64" BitCount="8" StartBit="0" StartByte="191" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc7" Value="64" BitCount="8" StartBit="0" StartByte="192" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc8" Value="64" BitCount="8" StartBit="0" StartByte="193" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc9" Value="64" BitCount="8" StartBit="0" StartByte="194" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc10" Value="64" BitCount="8" StartBit="0" StartByte="195" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc11" Value="64" BitCount="8" StartBit="0" StartByte="196" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc12" Value="64" BitCount="8" StartBit="0" StartByte="197" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc13" Value="64" BitCount="8" StartBit="0" StartByte="198" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc14" Value="64" BitCount="8" StartBit="0" StartByte="199" IsPresetParameter="false"/>
                <Parameter Name="CFMDigitalTgc15" Value="64" BitCount="8" StartBit="0" StartByte="200" IsPresetParameter="false"/>

                <Parameter Name="PacketSize" Min="1" Max="15" BitCount="4" StartBit="0" StartByte="201" IsPresetParameter="true"/>
                <Parameter Name="PacketSizePD" ModelType="PD" Min="1" Max="15" BitCount="4" StartBit="0" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PacketSizeSN" ModelType="SonoNeedle" Min="1" Max="15" BitCount="4" StartBit="0" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PacketSizeTDI" ModelType="TDI" Min="1" Max="15" BitCount="4" StartBit="0" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PacketSizeMVI" ModelType="MVI" Min="1" Max="15" BitCount="4" StartBit="0" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>

                <Parameter Name="DummyEnSample" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true"/>
                <Parameter Name="DummyEnSampleTM" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DummyEnSamplePD" ModelType="PD" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DummyEnSampleSN" ModelType="SonoNeedle" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DummyEnSampleTDI" ModelType="TDI" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DummyEnSampleMVI" ModelType="MVI" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AudioFilterCoefSel" BitCount="2" StartBit="6" StartByte="201" HighBitCount="1" HighStartBit="6" HighStartByte="156" IsPresetParameter="true"/>
                <Parameter Name="AudioFilterCoefSelTM" BitCount="2" StartBit="6" StartByte="201" HighBitCount="1" HighStartBit="6" HighStartByte="156" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AudioFilterCoefSelTDI" ModelType="TDI" BitCount="2" StartBit="6" StartByte="201" HighBitCount="1" HighStartBit="6" HighStartByte="156" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AudioFilterCoefSelCWD" ModelType="CW" BitCount="2" StartBit="6" StartByte="201" HighBitCount="1" HighStartBit="6" HighStartByte="156" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WFGainControl" BitCount="4" StartBit="0" StartByte="202" IsPresetParameter="true"/>
                <Parameter Name="WFGainControlPD" ModelType="PD" BitCount="4" StartBit="0" StartByte="202" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WFGainControlSN" ModelType="SonoNeedle" BitCount="4" StartBit="0" StartByte="202" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WFGainControlTDI" ModelType="TDI" BitCount="4" StartBit="0" StartByte="202" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WFGainControlMVI" ModelType="MVI" BitCount="4" StartBit="0" StartByte="202" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CLFGain" BitCount="4" StartBit="4" StartByte="202" IsPresetParameter="false"/>
                <Parameter Name="CLFShift" BitCount="4" StartBit="4" StartByte="203" IsPresetParameter="false"/>
                <Parameter Name="CLFCoef0" BitCount="8" StartBit="0" StartByte="204" HighBitCount="1" HighStartBit="0" HighStartByte="203" IsPresetParameter="false"/>
                <Parameter Name="CLFCoef1" BitCount="8" StartBit="0" StartByte="205" HighBitCount="1" HighStartBit="1" HighStartByte="203" IsPresetParameter="false"/>
                <Parameter Name="CLFCoef2" BitCount="8" StartBit="0" StartByte="206" HighBitCount="1" HighStartBit="2" HighStartByte="203" IsPresetParameter="false"/>
                <Parameter Name="CAFGain" BitCount="4" StartBit="0" StartByte="207" IsPresetParameter="false"/>
                <Parameter Name="CAFShift" BitCount="4" StartBit="4" StartByte="207" IsPresetParameter="false"/>
                <Parameter Name="CAFCoef0" BitCount="8" StartBit="0" StartByte="208" HighBitCount="1" HighStartBit="0" HighStartByte="212" IsPresetParameter="false"/>
                <Parameter Name="CAFCoef1" BitCount="8" StartBit="0" StartByte="209" HighBitCount="1" HighStartBit="1" HighStartByte="212" IsPresetParameter="false"/>
                <Parameter Name="CAFCoef2" BitCount="8" StartBit="0" StartByte="210" HighBitCount="1" HighStartBit="2" HighStartByte="212" IsPresetParameter="false"/>
                <Parameter Name="CAFCoef3" BitCount="8" StartBit="0" StartByte="211" HighBitCount="1" HighStartBit="3" HighStartByte="212" IsPresetParameter="false"/>
                <Parameter Name="LComeBack" BitCount="1" StartBit="3" StartByte="203" IsPresetParameter="true"/>
                <Parameter Name="LComeBackPD" ModelType="PD" BitCount="1" StartBit="3" StartByte="203" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="LComeBackSN" ModelType="SonoNeedle" BitCount="1" StartBit="3" StartByte="203" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="LComeBackTDI" ModelType="TDI" BitCount="1" StartBit="3" StartByte="203" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="LComeBackMVI" ModelType="MVI" BitCount="1" StartBit="3" StartByte="203" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AComeBack" BitCount="1" StartBit="4" StartByte="212" IsPresetParameter="true"/>
                <Parameter Name="AComeBackPD" ModelType="PD" BitCount="1" StartBit="4" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AComeBackSN" ModelType="SonoNeedle" BitCount="1" StartBit="4" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AComeBackTDI" ModelType="TDI" BitCount="1" StartBit="4" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="AComeBackMVI" ModelType="MVI" BitCount="1" StartBit="4" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CFMIIRe" BitCount="1" StartBit="5" StartByte="212" IsPresetParameter="true"/>
                <Parameter Name="PDIIRe" ModelType="PD" BitCount="1" StartBit="5" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SNIIRe" ModelType="SonoNeedle" BitCount="1" StartBit="5" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TDIIIRe" ModelType="TDI" BitCount="1" StartBit="5" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MVIIIRe" ModelType="MVI" BitCount="1" StartBit="5" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="HprfEn" BitCount="1" StartBit="6" StartByte="212" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="FCA_Switch" BitCount="1" StartBit="7" StartByte="212" IsPresetParameter="false"/>
                <Parameter Name="AffiliatedChipWrite" BitCount="1" StartBit="0" StartByte="213" IsPresetParameter="false"/>
                <Parameter Name="AffiliatedChipRead" BitCount="1" StartBit="1" StartByte="213" IsPresetParameter="false"/>
                <Parameter Name="AffiliatedChipSelectSignal" BitCount="5" StartBit="2" StartByte="213" IsPresetParameter="false"/>
                <Parameter Name="AffiliatedChipAddr" BitCount="8" StartBit="0" StartByte="214" HighBitCount="8" HighStartBit="0" HighStartByte="215" IsPresetParameter="false"/>
                <Parameter Name="AffiliatedChipWriteData" BitCount="8" StartBit="0" StartByte="216" HighBitCount="8" HighStartBit="0" HighStartByte="217" IsPresetParameter="false"/>
                <Parameter Name="FlashThreshold" Value="96" BitCount="8" StartBit="0" StartByte="220" IsPresetParameter="false"/>
                <Parameter Name="LowThreshold" Value="16" BitCount="8" StartBit="0" StartByte="221" IsPresetParameter="false"/>
                <Parameter Name="MedThreshold" Value="64" BitCount="8" StartBit="0" StartByte="222" IsPresetParameter="false"/>
                <Parameter Name="HoleFillEn" Value="1" BitCount="1" StartBit="0" StartByte="223" IsPresetParameter="false"/>
                <Parameter Name="MedFltByp" Value="1" BitCount="1" StartBit="1" StartByte="223" IsPresetParameter="false"/>
                <Parameter Name="CTxPulseDutyBin" BitCount="5" StartBit="3" StartByte="223" IsPresetParameter="false"/>
                <Parameter Name="FlashSupress" Value="255" BitCount="8" StartBit="0" StartByte="224" IsPresetParameter="false"/>
                <Parameter Name="CPriority" Value="255" BitCount="8" StartBit="0" StartByte="225" IsPresetParameter="false"/>
                <Parameter Name="FCA_Delta" BitCount="8" StartBit="0" StartByte="226" IsPresetParameter="false"/>
                <Parameter Name="FCA_Gamma" BitCount="8" StartBit="0" StartByte="227" IsPresetParameter="false"/>
                <Parameter Name="FCA_ThresholdUpLimit" BitCount="8" StartBit="0" StartByte="228" IsPresetParameter="false"/>
                <Parameter Name="FCA_ThresholdLowLimit" BitCount="8" StartBit="0" StartByte="229" IsPresetParameter="false"/>
                <Parameter Name="AdapPostProcSmooth" BitCount="3" StartBit="1" StartByte="231" IsPresetParameter="true"/>
                <Parameter Name="AdapPostProc" BitCount="4" StartBit="4" StartByte="231" IsPresetParameter="true"/>
                <Parameter Name="FGP0" Value="128" BitCount="8" StartBit="0" StartByte="232" IsPresetParameter="false"/>
                <Parameter Name="FGP1" Value="128" BitCount="8" StartBit="0" StartByte="233" IsPresetParameter="false"/>
                <Parameter Name="FGP2" Value="128" BitCount="8" StartBit="0" StartByte="234" IsPresetParameter="false"/>
                <Parameter Name="FGP3" Value="128" BitCount="8" StartBit="0" StartByte="235" IsPresetParameter="false"/>
                <Parameter Name="CFMQuitSamp_Head" BitCount="4" StartBit="0" StartByte="236" IsPresetParameter="false"/>
                <Parameter Name="CFMQuitSamp_Tail" Value="8" BitCount="4" StartBit="4" StartByte="236" IsPresetParameter="false"/>
                <Parameter Name="ScaningSignalEntireCompensation" BitCount="8" StartBit="0" StartByte="237" IsPresetParameter="false"/>
                <Parameter Name="ScaningSignalIndividualCompensation" BitCount="8" StartBit="0" StartByte="238" IsPresetParameter="false"/>
                <Parameter Name="GaussFilterSwitch" Value="1" BitCount="1" StartBit="0" StartByte="239" IsPresetParameter="false"/>
                <Parameter Name="ElastoGaussFilterSwitch" Value="0" BitCount="1" StartBit="0" StartByte="239" IsPresetParameter="false" IsDirectValue="true"/>
                <!--ThiMod is for PhasedProbe,Keyboard name is FHI-->
                <Parameter Name="ThiMode" ModelType="B" Value="1" BitCount="1" StartBit="1" StartByte="239" IsPresetParameter="false"/>
                <Parameter Name="CfmApertureMode" Value="1" BitCount="1" StartBit="2" StartByte="239" IsPresetParameter="false"/>
                <Parameter Name="CfmDemodulation" BitCount="1" StartBit="3" StartByte="239" IsPresetParameter="false"/>
                <Parameter Name="CFMVelLevel" BitCount="3" StartBit="5" StartByte="239" HighBitCount="1" HighStartBit="6" HighStartByte="292" IsPresetParameter="true"/>
                <Parameter Name="CFMVelLevelTM" BitCount="3" StartBit="5" StartByte="239" HighBitCount="1" HighStartBit="6" HighStartByte="292" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="WaferNumDiff" Value="128" BitCount="8" StartBit="0" StartByte="240" IsPresetParameter="false"/>
                <Parameter Name="MaxTransmitApertureControl" Value="63" BitCount="7" StartBit="0" StartByte="241" IsPresetParameter="false"/>
                <Parameter Name="MinTransmitApertureControl" BitCount="7" StartBit="0" StartByte="242" IsPresetParameter="false"/>
                <Parameter Name="FNumDecimal" BitCount="8" StartBit="0" StartByte="243" HighBitCount="4" HighStartBit="0" HighStartByte="244" IsPresetParameter="false"/>
                <Parameter Name="FNumInteger" BitCount="4" StartBit="4" StartByte="244" IsPresetParameter="false"/>
                <Parameter Name="FrequencyOfTransmit5" ModelType="B" BitCount="3" StartBit="0" StartByte="245" HighBitCount="1" HighStartBit="7" HighStartByte="245" ThirdBitCount="1" ThirdStartBit="4" ThirdStartByte="711" IsPresetParameter="false"/>
                <Parameter Name="HighFreqOfTransmit5" ModelType="B" BitCount="1" StartBit="3" StartByte="245" IsPresetParameter="false"/>
                <Parameter Name="PulseNumOfTransmit5" ModelType="B" Value="1" BitCount="3" StartBit="4" StartByte="245" IsPresetParameter="false"/>
                <Parameter Name="FocusNumOfTransmit5" ModelType="B" BitCount="2" StartBit="0" StartByte="246" IsPresetParameter="false"/>
                <Parameter Name="FilterCoefOfTransmit5" ModelType="B" BitCount="4" StartBit="4" StartByte="246" Max="7" IsPresetParameter="false"/>
                <!--新增发射6和发射7匹配滤波系数选择-->
                <Parameter Name="FrequencyOfTransmit6" ModelType="B" BitCount="5" StartBit="0" StartByte="782" Value="0" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="HighFreqOfTransmit6" ModelType="B" Value="0" BitCount="1" StartBit="7" StartByte="783" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PulseNumOfTransmit6" ModelType="B" BitCount="3" StartBit="5" StartByte="782" Value="0" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="FilterCoefOfTransmit6" ModelType="B" Value="0" BitCount="4" StartBit="0" StartByte="783" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="TransmitPulseEx6" ModelType="B" Value="0" BitCount="8" StartBit="0" StartByte="784" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="ThiMode6" ModelType="B" Value="0" BitCount="1" StartBit="6" StartByte="783" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="FrequencyOfTransmit7" ModelType="B" Value="0" BitCount="5" StartBit="0" StartByte="785" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="HighFreqOfTransmit7" ModelType="B" Value="0" BitCount="1" StartBit="7" StartByte="786" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PulseNumOfTransmit7" ModelType="B" Value="0" BitCount="3" StartBit="5" StartByte="785" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="TransmitPulseEx7" ModelType="B" Value="0" BitCount="8" StartBit="0" StartByte="787" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="FilterCoefOfTransmit7" ModelType="B" Value="0" BitCount="4" StartBit="0" StartByte="786" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="ThiMode7" ModelType="B" Value="0" BitCount="1" StartBit="6" StartByte="786" IsCanFrozenSend="true" IsPresetParameter="false"/>

                <Parameter Name="NeedleMode" ModelType="B" BitCount="1" StartBit="2" StartByte="246" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="NeedleAngleIndex" ModelType="B" Value="30" BitCount="4" StartBit="0" StartByte="247" IsPresetParameter="true"/>
                <Parameter Name="NeedleDynamicRange" ModelType="B" BitCount="4" StartBit="4" StartByte="247" IsPresetParameter="false"/>
                <Parameter Name="NeedleGain" ModelType="B" BitCount="8" StartBit="0" StartByte="248" IsPresetParameter="false"/>
                <Parameter Name="CfmMaxTransmitApertureControlBin" Value="63" BitCount="7" StartBit="0" StartByte="249" IsPresetParameter="false"/>
                <Parameter Name="VideoInvert" BitCount="1" StartBit="7" StartByte="249" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="CfmMinTransmitApertureControlBin" BitCount="7" StartBit="0" StartByte="250" IsPresetParameter="false"/>
                <Parameter Name="VideoInvertOfD" BitCount="1" StartBit="7" StartByte="250" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="CfmFNumDecimal" BitCount="8" StartBit="0" StartByte="251" HighBitCount="4" HighStartBit="0" HighStartByte="252" IsPresetParameter="false"/>
                <Parameter Name="CfmFNumInteger" BitCount="4" StartBit="4" StartByte="252" IsPresetParameter="false"/>
                <Parameter Name="BCRelativePos" BitCount="8" StartBit="0" StartByte="253" IsPresetParameter="false"/>
                <Parameter Name="ParaPresetTime" Value="159" BitCount="8" StartBit="0" StartByte="254" HighBitCount="4" HighStartBit="0" HighStartByte="255" DefaultValue="159" IsPresetParameter="true"/>
                <Parameter Name="CqyzOfD" Min="1" Max="4" Value="3" BitCount="2" StartBit="4" StartByte="255" DefaultValue="3" IsPresetParameter="true"/>
                <Parameter Name="PrtOfB" ModelType="B" BitCount="8" StartBit="0" StartByte="256" HighBitCount="8" HighStartBit="0" HighStartByte="257" IsPresetParameter="false"/>
                <Parameter Name="PrtOfC" BitCount="8" StartBit="0" StartByte="258" HighBitCount="8" HighStartBit="0" HighStartByte="259" IsPresetParameter="false"/>
                <Parameter Name="PrtOfE" BitCount="8" StartBit="0" StartByte="258" HighBitCount="8" HighStartBit="0" HighStartByte="259" IsPresetParameter="false"/>
                <Parameter Name="PrtOfD" BitCount="8" StartBit="0" StartByte="260" HighBitCount="8" HighStartBit="0" HighStartByte="261" IsPresetParameter="false"/>
                <Parameter Name="MSampleRate" BitCount="8" StartBit="0" StartByte="262" Step="1" IsPresetParameter="false"/>
                <Parameter Name="DSampleRate" BitCount="8" StartBit="0" StartByte="263" Step="1" IsPresetParameter="true"/>
                <Parameter Name="BCSampleRate" BitCount="8" StartBit="0" StartByte="264" Step="1" IsPresetParameter="true"/>
                <Parameter Name="BFrameNum" BitCount="4" StartBit="0" StartByte="265" IsPresetParameter="false"/>
                <Parameter Name="MFStitch" BitCount="1" StartBit="5" StartByte="265" IsPresetParameter="false"/>
                <Parameter Name="HBStitch" BitCount="1" StartBit="6" StartByte="265" IsPresetParameter="false"/>
                <Parameter Name="TotalFrameNum" BitCount="5" StartBit="0" StartByte="266" IsPresetParameter="false"/>
                <Parameter Name="ECGSignal" ModelType="ECG" BitCount="1" StartBit="5" StartByte="266" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="PWMDutyRatio" BitCount="7" StartBit="0" StartByte="267" IsPresetParameter="false"/>
                <Parameter Name="FourDBurnSignal" BitCount="1" StartBit="7" StartByte="267" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="CFMEnergeCtl" BitCount="4" StartBit="4" StartByte="268" IsPresetParameter="false"/>
                <Parameter Name="MaxGain" Value="198" BitCount="8" StartBit="0" StartByte="269" Max="198" IsPresetParameter="false"/>
                <Parameter Name="BTgcGain" BitCount="8" StartBit="0" StartByte="270" HighBitCount="2" HighStartBit="0" HighStartByte="271" IsPresetParameter="false"/>
                <Parameter Name="NeedleSSampleAdjust" BitCount="6" StartBit="2" StartByte="271" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl0" Value="255" BitCount="8" StartBit="0" StartByte="272" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl1" Value="255" BitCount="8" StartBit="0" StartByte="273" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl2" Value="255" BitCount="8" StartBit="0" StartByte="274" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl3" Value="255" BitCount="8" StartBit="0" StartByte="275" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl4" Value="255" BitCount="8" StartBit="0" StartByte="276" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl5" Value="255" BitCount="8" StartBit="0" StartByte="277" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl6" Value="255" BitCount="8" StartBit="0" StartByte="278" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl7" Value="255" BitCount="8" StartBit="0" StartByte="279" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl8" Value="255" BitCount="8" StartBit="0" StartByte="280" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl9" Value="255" BitCount="8" StartBit="0" StartByte="281" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl10" Value="255" BitCount="8" StartBit="0" StartByte="282" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl11" Value="255" BitCount="8" StartBit="0" StartByte="283" IsPresetParameter="false"/>
                <Parameter Name="CfmEngCtrl12" Value="255" BitCount="8" StartBit="0" StartByte="284" IsPresetParameter="false"/>
                <Parameter Name="CfmFlashVlt" BitCount="8" StartBit="0" StartByte="285" IsPresetParameter="false"/>
                <Parameter Name="CfmFlashSlope" BitCount="8" StartBit="0" StartByte="286" IsPresetParameter="false"/>
                <Parameter Name="CfmNoiseElt" Value="255" BitCount="8" StartBit="0" StartByte="287" IsPresetParameter="false"/>
                <Parameter Name="CfmNoiseVlt" BitCount="8" StartBit="0" StartByte="288" IsPresetParameter="false"/>
                <Parameter Name="CfmNoiseSlope" BitCount="8" StartBit="0" StartByte="289" IsPresetParameter="false"/>
                <Parameter Name="CStaticOptimization" BitCount="1" StartBit="0" StartByte="290" IsPresetParameter="false"/>
                <Parameter Name="ProbeInvert" BitCount="1" StartBit="6" StartByte="290" ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="FixedLineNum" BitCount="8" StartBit="0" StartByte="291" HighBitCount="1" HighStartBit="0" HighStartByte="292" IsPresetParameter="false"/>
                <Parameter Name="ElemMode" BitCount="1" StartBit="1" StartByte="292" IsPresetParameter="false"/>
                <Parameter Name="FixedLineType" BitCount="2" StartBit="2" StartByte="292" IsPresetParameter="false"/>
                <Parameter Name="FixedLineStartup" BitCount="1" StartBit="4" StartByte="292" IsPresetParameter="false"/>
                <Parameter Name="WTByPass" BitCount="1" StartBit="5" StartByte="292" IsPresetParameter="false"/>
                <Parameter Name="ElemSignal" BitCount="1" StartBit="7" StartByte="292" IsPresetParameter="false"/>
                <Parameter Name="CfmSlope" BitCount="8" StartBit="0" StartByte="296" IsPresetParameter="false"/>
                <Parameter Name="WaferNumDetected" Value="255" BitCount="8" StartBit="0" StartByte="297" IsPresetParameter="false"/>
                <Parameter Name="PFrSd_PwrOldWt" Value="255" BitCount="8" StartBit="0" StartByte="298" IsPresetParameter="false"/>
                <Parameter Name="EtBfWf" Value="255" BitCount="8" StartBit="0" StartByte="299" IsPresetParameter="false"/>
                <Parameter Name="NewWallThreshold" BitCount="7" StartBit="0" StartByte="300" IsPresetParameter="false"/>
                <Parameter Name="ImgFrz" BitCount="1" StartBit="7" StartByte="300" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="CfmPersistentThreshold" Value="24" BitCount="8" StartBit="0" StartByte="301" IsPresetParameter="false"/>
                <Parameter Name="WrapEn" Value="1" BitCount="1" StartBit="0" StartByte="302" IsPresetParameter="false"/>
                <Parameter Name="CLFEn" Value="1" BitCount="1" StartBit="1" StartByte="302" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="CAFEn" Value="1" BitCount="1" StartBit="2" StartByte="302" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="DopOutGain" BitCount="4" StartBit="4" StartByte="302" IsPresetParameter="true"/>
                <Parameter Name="TDOutGain" BitCount="4" StartBit="4" StartByte="302" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWOutGain" BitCount="4" StartBit="4" StartByte="302" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DopFrequency" BitCount="4" StartBit="0" StartByte="303" HighBitCount="1" HighStartBit="6" HighStartByte="711" IsPresetParameter="false"/>
                <Parameter Name="DopFilterCoef" BitCount="4" StartBit="4" StartByte="303" IsPresetParameter="false"/>
                <Parameter Name="DopTxFNum" BitCount="3" StartBit="0" StartByte="304" IsPresetParameter="false"/>
                <Parameter Name="DopRvFNum" BitCount="3" StartBit="3" StartByte="304" IsPresetParameter="false"/>
                <Parameter Name="DopAudioSegment" BitCount="2" StartBit="6" StartByte="304" IsPresetParameter="true"/>
                <Parameter Name="TDAudioSegment" BitCount="2" StartBit="6" StartByte="304" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWAudioSegment" BitCount="2" StartBit="6" StartByte="304" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DopDemodulationFilterCoef" BitCount="4" StartBit="0" StartByte="305" IsPresetParameter="false"/>
                <Parameter Name="DopDemodulationFreq" BitCount="4" StartBit="4" StartByte="305" IsPresetParameter="false"/>
                <Parameter Name="DopPulseDuty" BitCount="5" StartBit="0" StartByte="306" IsPresetParameter="false"/>
                <Parameter Name="DopSampleNum" BitCount="3" StartBit="5" StartByte="306" IsPresetParameter="true"/>
                <Parameter Name="TDSampleNum" BitCount="3" StartBit="5" StartByte="306" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWSampleNum" BitCount="3" StartBit="5" StartByte="306" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DopMaxAperture" BitCount="7" StartBit="0" StartByte="307" IsPresetParameter="false"/>
                <Parameter Name="WrapEnQ" BitCount="1" StartBit="7" StartByte="307" IsPresetParameter="false"/>
                <Parameter Name="DopAudioPostGain" BitCount="2" StartBit="0" StartByte="308" IsPresetParameter="true"/>
                <Parameter Name="TDAudioPostGain" BitCount="2" StartBit="0" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWAudioPostGain" BitCount="2" StartBit="0" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DopAudioPreGain" BitCount="2" StartBit="2" StartByte="308" IsPresetParameter="true"/>
                <Parameter Name="TDAudioPreGain" BitCount="2" StartBit="2" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWAudioPreGain" BitCount="2" StartBit="2" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DopTimeFilter" BitCount="4" StartBit="4" StartByte="308" IsPresetParameter="true"/>
                <Parameter Name="TDTimeFilter" BitCount="4" StartBit="4" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWTimeFilter" BitCount="4" StartBit="4" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DopFilterLength" BitCount="8" StartBit="0" StartByte="309" IsPresetParameter="true"/>
                <Parameter Name="TDFilterLength" BitCount="8" StartBit="0" StartByte="309" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWFilterLength" BitCount="8" StartBit="0" StartByte="309" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DopVelocityFilter" BitCount="4" StartBit="0" StartByte="310" IsPresetParameter="true"/>
                <Parameter Name="TDVelocityFilter" BitCount="4" StartBit="0" StartByte="310" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWVelocityFilter" BitCount="4" StartBit="0" StartByte="310" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DopMidGain" BitCount="4" StartBit="4" StartByte="310" IsPresetParameter="true"/>
                <Parameter Name="TDMidGain" BitCount="4" StartBit="4" StartByte="310" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWMidGain" BitCount="4" StartBit="4" StartByte="310" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="VersionInfor" BitCount="1" StartBit="0" StartByte="311" IsPresetParameter="false"/>
                <Parameter Name="VersionDisplayMode" BitCount="1" StartBit="1" StartByte="311" IsPresetParameter="false"/>
                <Parameter Name="MotherboardShield" BitCount="1" StartBit="2" StartByte="311" IsPresetParameter="false"/>
                <Parameter Name="ExtensionPlateShield" BitCount="1" StartBit="3" StartByte="311" IsPresetParameter="false"/>
                <Parameter Name="InspectionMode" BitCount="2" StartBit="0" StartByte="312" IsPresetParameter="false"/>
                <Parameter Name="UnifTgcEn" BitCount="1" StartBit="7" StartByte="312" ValueType="Bool" Value="true" TrueValue="1" DefaultValue="true" IsPresetParameter="true"/>
                <Parameter Name="InspectionNum" BitCount="8" StartBit="0" StartByte="313" IsPresetParameter="false"/>
                <Parameter Name="DopFNumDecimal" BitCount="8" StartBit="0" StartByte="314" HighBitCount="4" HighStartBit="0" HighStartByte="315" IsPresetParameter="false"/>
                <Parameter Name="DopFNumInteger" BitCount="4" StartBit="4" StartByte="315" IsPresetParameter="false"/>
                <Parameter Name="XCoordinate" BitCount="8" StartBit="0" StartByte="316" HighBitCount="2" HighStartBit="0" HighStartByte="318" IsPresetParameter="false"/>
                <Parameter Name="YCoordinate" BitCount="8" StartBit="0" StartByte="317" HighBitCount="2" HighStartBit="2" HighStartByte="318" IsPresetParameter="false"/>
                <Parameter Name="XCoordinateEn" BitCount="1" StartBit="4" StartByte="318" IsPresetParameter="false"/>
                <Parameter Name="YCoordinateEn" BitCount="1" StartBit="5" StartByte="318" IsPresetParameter="false"/>
                <Parameter Name="LineNoShield" BitCount="8" StartBit="0" StartByte="319" HighBitCount="1" HighStartBit="7" HighStartByte="318" IsPresetParameter="false"/>
                <Parameter Name="DopMinTransmitAperture" BitCount="7" StartBit="0" StartByte="320" IsPresetParameter="false"/>
                <Parameter Name="MinReceiveAperture" BitCount="7" StartBit="0" StartByte="321" IsPresetParameter="false"/>
                <Parameter Name="MaxReceiveAperture" Value="63" BitCount="7" StartBit="0" StartByte="322" IsPresetParameter="false"/>
                <Parameter Name="ReceiveFNumDecimal" BitCount="8" StartBit="0" StartByte="323" HighBitCount="4" HighStartBit="0" HighStartByte="324" IsPresetParameter="false"/>
                <Parameter Name="ReceiveFNumInteger" BitCount="4" StartBit="4" StartByte="324" IsPresetParameter="false"/>
                <Parameter Name="CfmMinReceiveAperture" BitCount="7" StartBit="0" StartByte="325" IsPresetParameter="false"/>
                <Parameter Name="CfmMaxReceiveAperture" Value="63" BitCount="7" StartBit="0" StartByte="326" IsPresetParameter="false"/>
                <Parameter Name="CfmReceiveFNumDecimal" BitCount="8" StartBit="0" StartByte="327" HighBitCount="4" HighStartBit="0" HighStartByte="328" IsPresetParameter="false"/>
                <Parameter Name="CfmReceiveFNumInteger" BitCount="4" StartBit="4" StartByte="328" IsPresetParameter="false"/>
                <Parameter Name="DopMinReceiveAperture" BitCount="7" StartBit="0" StartByte="329" IsPresetParameter="false"/>
                <Parameter Name="DopMaxReceiveAperture" BitCount="7" StartBit="0" StartByte="330" IsPresetParameter="false"/>
                <Parameter Name="DopReceiveFNumDecimal" BitCount="8" StartBit="0" StartByte="331" HighBitCount="4" HighStartBit="0" HighStartByte="332" IsPresetParameter="false"/>
                <Parameter Name="DopReceiveFNumInteger" BitCount="4" StartBit="4" StartByte="332" IsPresetParameter="false"/>
                <Parameter Name="ShieldSPPosition" BitCount="8" StartBit="0" StartByte="333" HighBitCount="2" HighStartBit="0" HighStartByte="334" IsPresetParameter="false"/>
                <Parameter Name="ShieldSPEn" BitCount="1" StartBit="2" StartByte="334" IsPresetParameter="false"/>
                <Parameter Name="TeeFlag" BitCount="1" StartBit="7" StartByte="334" IsPresetParameter="false"/>
                <Parameter Name="PixelsWithinFocus" BitCount="8" StartBit="0" StartByte="335" HighBitCount="2" HighStartBit="0" HighStartByte="336" IsPresetParameter="false"/>
                <Parameter Name="DopAccumulateNum" BitCount="8" StartBit="0" StartByte="337" IsPresetParameter="false"/>

                <Parameter Name="ECGDly" ModelType="ECG" BitCount="8" StartBit="0" StartByte="346" IsPresetParameter="true"/>
                <Parameter Name="MECGDly" ModelType="ECG" BitCount="8" StartBit="0" StartByte="346" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CWECGDly" ModelType="ECG" BitCount="8" StartBit="0" StartByte="346" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="FreeMECGDly" ModelType="ECG" BitCount="8" StartBit="0" StartByte="346" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DECGDly" ModelType="ECG" BitCount="8" StartBit="0" StartByte="346" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="DTDIECGDly" ModelType="ECG" BitCount="8" StartBit="0" StartByte="346" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CEmissionFrequencyCoef" BitCount="8" StartBit="0" StartByte="347" IsPresetParameter="false"/>
                <Parameter Name="CTransmitPulseDutyCoef" BitCount="8" StartBit="0" StartByte="348" IsPresetParameter="false"/>
                <Parameter Name="NeedleEmissionFrequencyCoef" BitCount="8" StartBit="0" StartByte="349" IsPresetParameter="false"/>
                <Parameter Name="NeedleTransmitPulseDutyCoef" BitCount="8" StartBit="0" StartByte="350" IsPresetParameter="false"/>
                <Parameter Name="CSteeringAngleCoding" BitCount="6" StartBit="0" StartByte="351" IsPresetParameter="false"/>
                <Parameter Name="NeedleSteeringAngleCoding" BitCount="6" StartBit="0" StartByte="352" IsPresetParameter="false"/>
                <Parameter Name="BSteeringAngleCoding" BitCount="6" StartBit="0" StartByte="353" IsPresetParameter="false"/>
                <Parameter Name="CWeightedCurveType" BitCount="6" StartBit="0" StartByte="354" IsPresetParameter="false"/>
                <Parameter Name="NeedleWeightedCurveType" BitCount="6" StartBit="0" StartByte="355" IsPresetParameter="false"/>
                <Parameter Name="DWeightedCurveType" BitCount="6" StartBit="0" StartByte="356" IsPresetParameter="false"/>
                <Parameter Name="UnzoomedCQYZ" BitCount="7" StartBit="0" StartByte="363" IsPresetParameter="false"/>
                <Parameter Name="ZoomDepthPixel" BitCount="8" StartBit="0" StartByte="364" HighBitCount="1" HighStartBit="0" HighStartByte="365" IsPresetParameter="false"/>
                <Parameter Name="THISel" BitCount="1" StartBit="2" StartByte="365" IsPresetParameter="true"/>
                <Parameter Name="FScpd" ValueType="Bool" BitCount="1" StartBit="3" StartByte="365" IsPresetParameter="false"/>
                <Parameter Name="ThiMode4" ModelType="B" BitCount="1" StartBit="4" StartByte="365" IsPresetParameter="false"/>
                <Parameter Name="ThiMode3" ModelType="B" BitCount="1" StartBit="5" StartByte="365" IsPresetParameter="false"/>
                <Parameter Name="ThiMode2" ModelType="B" BitCount="1" StartBit="6" StartByte="365" IsPresetParameter="false"/>
                <Parameter Name="ThiMode1" ModelType="B" BitCount="1" StartBit="7" StartByte="365" IsPresetParameter="false"/>
                <Parameter Name="MachineIdentity" BitCount="3" StartBit="2" StartByte="376" IsPresetParameter="false"/>
                <Parameter Name="LinePackageCount" BitCount="8" StartBit="0" StartByte="375" IsPresetParameter="false"/>
                <Parameter Name="LineDataMode" BitCount="1" StartBit="7" StartByte="376" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="InfoLineEnableSignal" BitCount="1" StartBit="6" StartByte="376" IsPresetParameter="false"/>
                <Parameter Name="TableID" BitCount="8" StartBit="0" StartByte="377" IsPresetParameter="false"/>
                <Parameter Name="DebugPara5" BitCount="8" StartBit="0" StartByte="402" IsPresetParameter="false"/>
                <Parameter Name="AudioTest_LR" BitCount="2" StartBit="0" StartByte="404" IsPresetParameter="false"/>
                <Parameter Name="AudioTest_Tone" BitCount="2" StartBit="2" StartByte="404" IsPresetParameter="false"/>
                <Parameter Name="BTriplexPrt_Delta" BitCount="8" StartBit="0" StartByte="407" HighBitCount="8" HighStartBit="0" HighStartByte="408" Step="100" IsPresetParameter="true"/>
                <Parameter Name="ThiTxDummy" BitCount="8" StartBit="0" StartByte="409" IsPresetParameter="true"/>
                <Parameter Name="FFocusPrtDelta" BitCount="8" StartBit="0" StartByte="418" HighBitCount="8" HighStartBit="0" HighStartByte="419" IsPresetParameter="true"/>
                <Parameter Name="VesselThreshold" Value="0" BitCount="4" StartBit="0" StartByte="421" IsPresetParameter="false"/>
                <Parameter Name="DetailWeightBin" Value="0" BitCount="8" StartBit="0" StartByte="422" IsPresetParameter="false"/>
                <Parameter Name="MinWeightBin" Value="128" BitCount="8" StartBit="0" StartByte="423" IsPresetParameter="false"/>
                <Parameter Name="MaxWeightBin" Value="128" BitCount="8" StartBit="0" StartByte="424" IsPresetParameter="false"/>
                <Parameter Name="EdgeWeightBin" Value="0" BitCount="8" StartBit="0" StartByte="425" IsPresetParameter="false"/>
                <Parameter Name="ECGDynamicRange" ModelType="ECG" Value="0" BitCount="3" StartBit="0" StartByte="434" IsPresetParameter="true"/>
                <Parameter Name="SpecFrh" BitCount="1" StartBit="6" StartByte="441" IsPresetParameter="false"/>
                <Parameter Name="CWSwithCode" BitCount="1" StartBit="5" StartByte="441" IsPresetParameter="false"/>
                <Parameter Name="LineCpdPara" BitCount="8" StartBit="0" StartByte="442" IsPresetParameter="false"/>
                <Parameter Name="DLineCorrectPara" BitCount="4" StartBit="0" StartByte="443" IsPresetParameter="true"/>
                <Parameter Name="MLineCorrectPara" BitCount="4" StartBit="0" StartByte="443" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="TDLineCorrectPara" BitCount="4" StartBit="0" StartByte="443" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="MDynamicRange" BitCount="4" StartBit="4" StartByte="443" IsPresetParameter="true"/>
                <Parameter Name="BWeightedCurveType" BitCount="6" StartBit="2" StartByte="444" IsPresetParameter="false"/>
                <Parameter Name="CWGainReduction" BitCount="8" StartBit="0" StartByte="447" IsPresetParameter="true"/>
                <Parameter Name="CWGainReductionSlope" BitCount="8" StartBit="0" StartByte="448" IsPresetParameter="true"/>
                <Parameter Name="VideoSOCutH" BitCount="8" StartBit="0" StartByte="457" HighBitCount="3" HighStartBit="0" HighStartByte="458" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="VideoSOCutV" BitCount="5" StartBit="3" StartByte="458" HighBitCount="6" HighStartBit="0" HighStartByte="459" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="VideoEOCutH" BitCount="2" StartBit="6" StartByte="459" HighBitCount="8" HighStartBit="0" HighStartByte="460" ThirdBitCount="1" ThirdStartBit="0" ThirdStartByte="461" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="VideoEOCutV" BitCount="7" StartBit="1" StartByte="461" HighBitCount="4" HighStartBit="0" HighStartByte="462" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="VideoCprsHSize" BitCount="4" StartBit="4" StartByte="462" HighBitCount="6" HighStartBit="0" HighStartByte="463" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="VideoCprsVSize" BitCount="2" StartBit="6" StartByte="463" HighBitCount="8" HighStartBit="0" HighStartByte="464" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="VideoCprsCoef" BitCount="8" StartBit="0" StartByte="465" HighBitCount="8" HighStartBit="0" HighStartByte="466" ThirdBitCount="2" ThirdStartBit="0" ThirdStartByte="467" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="VideoFecture" BitCount="1" StartBit="2" StartByte="467" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="VideoSwitch" BitCount="1" StartBit="3" StartByte="467" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="VideoCprsEn" BitCount="1" StartBit="4" StartByte="467" ValueType="Bool" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="UpLoadBuffFrz" BitCount="1" StartBit="5" StartByte="467" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="BTxEndClamp" ModelType="B" Value="100" BitCount="8" StartBit="0" StartByte="468" IsPresetParameter="false"/>
                <Parameter Name="CTxEndClamp" Value="100" BitCount="8" StartBit="0" StartByte="469" IsPresetParameter="false"/>
                <Parameter Name="BTxApertureCoef" ModelType="B" BitCount="8" StartBit="0" StartByte="482" HighBitCount="8" HighStartBit="0" HighStartByte="483" ThirdBitCount="4" ThirdStartBit="0" ThirdStartByte="484" IsPresetParameter="false"/>
                <Parameter Name="CTxApertureCoef" BitCount="8" StartBit="0" StartByte="485" HighBitCount="8" HighStartBit="0" HighStartByte="486" ThirdBitCount="4" ThirdStartBit="0" ThirdStartByte="487" IsPresetParameter="false"/>
                <Parameter Name="DTxApertureCoef" BitCount="8" StartBit="0" StartByte="488" HighBitCount="8" HighStartBit="0" HighStartByte="489" ThirdBitCount="4" ThirdStartBit="0" ThirdStartByte="490" IsPresetParameter="false"/>
                <Parameter Name="MGain" BitCount="8" StartBit="0" StartByte="513" IsPresetParameter="true"/>
                <Parameter Name="MGainThi" BitCount="8" StartBit="0" StartByte="513" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="ElastoDR" Value="8" BitCount="5" StartBit="0" StartByte="537" IsPresetParameter="false"/>
                <Parameter Name="ElastoSmoothFilter" DefaultValue="1" BitCount="2" StartBit="5" StartByte="537" IsPresetParameter="true"/>
                <Parameter Name="ElastoEn" BitCount="1" StartBit="7" StartByte="537" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="ElastoStretchGain" DefaultValue="3" BitCount="4" StartBit="0" StartByte="538" IsPresetParameter="true"/>
                <Parameter Name="ElastoEUniformityTgc0" Value="128" BitCount="8" StartBit="0" StartByte="539" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc1" Value="128" BitCount="8" StartBit="0" StartByte="540" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc2" Value="128" BitCount="8" StartBit="0" StartByte="541" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc3" Value="128" BitCount="8" StartBit="0" StartByte="542" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc4" Value="128" BitCount="8" StartBit="0" StartByte="543" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc5" Value="128" BitCount="8" StartBit="0" StartByte="544" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc6" Value="128" BitCount="8" StartBit="0" StartByte="545" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc7" Value="128" BitCount="8" StartBit="0" StartByte="546" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc8" Value="128" BitCount="8" StartBit="0" StartByte="547" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc9" Value="128" BitCount="8" StartBit="0" StartByte="548" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc10" Value="128" BitCount="8" StartBit="0" StartByte="549" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc11" Value="128" BitCount="8" StartBit="0" StartByte="550" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc12" Value="128" BitCount="8" StartBit="0" StartByte="551" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc13" Value="128" BitCount="8" StartBit="0" StartByte="552" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc14" Value="128" BitCount="8" StartBit="0" StartByte="553" IsPresetParameter="false"/>
                <Parameter Name="ElastoEUniformityTgc15" Value="128" BitCount="8" StartBit="0" StartByte="554" IsPresetParameter="false"/>
                <Parameter Name="ElastoFrameGap" BitCount="4" StartBit="4" StartByte="538" IsPresetParameter="false"/>
                <Parameter Name="ElastoProcessDelay" BitCount="8" StartBit="0" StartByte="555" DefaultValue="70" IsPresetParameter="true"/>
                <Parameter Name="ElastoDebug4" DefaultValue="0" BitCount="3" StartBit="0" StartByte="556" IsPresetParameter="true"/>
                <Parameter Name="ElastoMedianPost" DefaultValue="3" BitCount="2" StartBit="3" StartByte="556" IsPresetParameter="true"/>
                <Parameter Name="ElastoStrainFilter" DefaultValue="3" BitCount="3" StartBit="5" StartByte="556" IsPresetParameter="true"/>
                <Parameter Name="ElastoDigitalGainRange" DefaultValue="8" BitCount="4" StartBit="0" StartByte="557" IsPresetParameter="false"/>
                <Parameter Name="ElastoMedianPre" DefaultValue="3" BitCount="2" StartBit="4" StartByte="557" IsPresetParameter="true"/>
                <Parameter Name="ElastoDebug3" DefaultValue="0" BitCount="1" StartBit="6" StartByte="557" IsPresetParameter="true"/>
                <Parameter Name="ElastoDebug1" DefaultValue="0" BitCount="1" StartBit="7" StartByte="557" IsPresetParameter="true"/>
                <Parameter Name="ElastoDebug2" DefaultValue="0" BitCount="8" StartBit="0" StartByte="558" IsPresetParameter="true"/>
                <Parameter Name="ElastoDebug5" DefaultValue="0" BitCount="8" StartBit="0" StartByte="559" IsPresetParameter="true"/>
                <Parameter Name="ElastoThresholdPre" DefaultValue="7" BitCount="8" StartBit="0" StartByte="560" IsPresetParameter="false"/>
                <Parameter Name="ElastoThresholdPost" DefaultValue="255" BitCount="8" StartBit="0" StartByte="561" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainGain" Value="128" BitCount="8" StartBit="0" StartByte="562" IsPresetParameter="false"/>
                <Parameter Name="ElastoPersistentCoef" DefaultValue="32" BitCount="8" StartBit="0" StartByte="563" IsPresetParameter="true"/>
                <Parameter Name="ElastoAxialCoef" DefaultValue="32" BitCount="8" StartBit="0" StartByte="564" IsPresetParameter="true"/>
                <Parameter Name="ElastoLateralCoef" DefaultValue="32" BitCount="8" StartBit="0" StartByte="565" IsPresetParameter="true"/>
                <Parameter Name="ElastoSearchSize" DefaultValue="7" BitCount="3" StartBit="0" StartByte="566" IsPresetParameter="true"/>
                <Parameter Name="ElastoKernelSize" DefaultValue="3" BitCount="2" StartBit="3" StartByte="566" IsPresetParameter="true"/>
                <Parameter Name="NReboot" BitCount="1" StartBit="6" StartByte="566" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="EReboot" BitCount="1" StartBit="7" StartByte="566" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="KeyBoardLightEn" BitCount="1" StartBit="3" StartByte="569" ValueType="Bool" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="KeyBoardLightLevel" BitCount="4" StartBit="4" StartByte="569" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="FreeMMode" BitCount="1" StartBit="7" StartByte="640" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="ConvexRadius" BitCount="8" StartBit="0" StartByte="574" IsPresetParameter="false"/>
                <Parameter Name="CWTxFocusDis" BitCount="8" StartBit="0" StartByte="575" HighBitCount="8" HighStartBit="0" HighStartByte="576" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="577" IsPresetParameter="false"/>
                <Parameter Name="CWRvFocusPos" BitCount="8" StartBit="0" StartByte="578" HighBitCount="8" HighStartBit="0" HighStartByte="579" IsPresetParameter="false"/>
                <Parameter Name="MDFGain" BitCount="4" StartBit="0" StartByte="677" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="MDFShift" BitCount="4" StartBit="4" StartByte="677" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="MDFCGain" BitCount="4" StartBit="0" StartByte="933" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="MDFCShift" BitCount="4" StartBit="4" StartByte="933" IsPresetParameter="false" IsCanFrozenSend="true"/>
                <Parameter Name="AFE_LPF_FCutOff" BitCount="4" StartBit="4" StartByte="637" IsPresetParameter="false"/>
                <Parameter Name="AFE_PDWN_VCA_PGA" BitCount="1" StartBit="3" StartByte="637" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="AFE_PGA_HPF_DIS" BitCount="1" StartBit="7" StartByte="638" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="AFE_PGA_GAIN" BitCount="2" StartBit="4" StartByte="638" IsPresetParameter="true"/>
                <Parameter Name="AFE_LNA_HPF_DIS" BitCount="1" StartBit="3" StartByte="638" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="AFE_Auto_Low_Pass_EN" BitCount="1" StartBit="2" StartByte="638" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="AFE_LNA_GAIN" BitCount="2" StartBit="0" StartByte="638" IsPresetParameter="false"/>
                <Parameter Name="AFE_ACT_TERM_EN" BitCount="1" StartBit="7" StartByte="639" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="AFE_InImpedence" BitCount="2" StartBit="4" StartByte="639" IsPresetParameter="true"/>
                <Parameter Name="AFE_HPF_FCutOff" BitCount="4" StartBit="0" StartByte="639" HighBitCount="1" HighStartBit="6" HighStartByte="639" IsPresetParameter="false"/>
                <Parameter Name="AFE_LNA_Bias" BitCount="2" StartBit="4" StartByte="646" IsPresetParameter="true"/>
                <Parameter Name="AFE_CW_HPF_EN" BitCount="1" StartBit="3" StartByte="646" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="AFE_CW_HPF_FB_RES" BitCount="2" StartBit="0" StartByte="646" IsPresetParameter="true"/>
                <Parameter Name="AFE_DIS_CW_AMP" BitCount="1" StartBit="7" StartByte="647" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="AFE_CW_SUM_AMP_GAIN" BitCount="6" StartBit="0" StartByte="647" IsPresetParameter="true"/>
                <Parameter Name="AFE_CW_ACT_TERM_EN" BitCount="1" StartBit="7" StartByte="657" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="AFE_CW_InImpedence" BitCount="2" StartBit="4" StartByte="657" IsPresetParameter="true"/>
                <Parameter Name="AFE_PGA_HI_FREQ" BitCount="1" StartBit="6" StartByte="657" ValueType="Int" value="0" IsPresetParameter="true"/><!-- 当时atom机型分芯片类型的时候添加了该参数，sonaoair机型上没有添加 -->
                <Parameter Name="AFE_PGA_CLAMP_LVL" BitCount="2" StartBit="2" StartByte="657" Value="1" IsPresetParameter="true"/><!-- 当时atom机型分芯片类型的时候添加了该参数，sonaoair机型上没有添加 -->
                <Parameter Name="AFE_CW_LNA_GAIN" BitCount="2" StartBit="0" StartByte="657" IsPresetParameter="true"/>
                <Parameter Name="DebugPara6" BitCount="1" StartBit="0" StartByte="650" IsPresetParameter="false"/>
                <Parameter Name="DebugPara7" BitCount="2" StartBit="1" StartByte="650" IsPresetParameter="false"/>
                <Parameter Name="DebugPara8" BitCount="2" StartBit="3" StartByte="650" IsPresetParameter="false"/>
                <Parameter Name="DebugPara9" BitCount="3" StartBit="5" StartByte="650" IsPresetParameter="false"/>
                <Parameter Name="BlockDCFilterSwitch1" Min="0" Max="1" BitCount="1" StartBit="4" StartByte="681" Value="0" IsPresetParameter="true"/>
                <Parameter Name="BlockDCFilterSwitch2" Min="0" Max="1" BitCount="1" StartBit="5" StartByte="681" Value="0" IsPresetParameter="true"/>
                <Parameter Name="FrameScapeEnable" Value="0" Min="0" Max="1" BitCount="1" StartBit="7" StartByte="681" IsPresetParameter="true"/>
                <Parameter Name="FrameScapeEnableTM" Value="0" Min="0" Max="1" BitCount="1" StartBit="7" StartByte="681"/>
                <Parameter Name="BFrameScapeTime" Min="0" Max="255" BitCount="8" StartBit="0" StartByte="682" Value="1" />
                <Parameter Name="CFrameScapeTime" Min="0" Max="255" BitCount="8" StartBit="0" StartByte="682" Value="1" IsPresetParameter="true"/>
                <Parameter Name="CWGain" BitCount="8" StartBit="0" StartByte="716" Value="220" IsPresetParameter="false"/>
                <Parameter Name="TransmitPulseEx1" ModelType="B" BitCount="8" StartBit="0" StartByte="717" IsPresetParameter="false"/>
                <Parameter Name="TransmitPulseEx2" ModelType="B" BitCount="8" StartBit="0" StartByte="718" IsPresetParameter="false"/>
                <Parameter Name="TransmitPulseEx3" ModelType="B" BitCount="8" StartBit="0" StartByte="719" IsPresetParameter="false"/>
                <Parameter Name="TransmitPulseEx4" ModelType="B" BitCount="8" StartBit="0" StartByte="720" IsPresetParameter="false"/>
                <Parameter Name="TransmitPulseEx5" ModelType="B" BitCount="8" StartBit="0" StartByte="721" IsPresetParameter="false"/>
                <Parameter Name="Clamp" BitCount="1" StartBit="4" StartByte="772" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <!-- Parameter Name="LPF_PROG" BitCount="2" StartBit="0" StartByte="833" Value="0" IsPresetParameter="true"/ -->
                <!-- Parameter Name="HIGH_POWER" BitCount="1" StartBit="2" StartByte="833" Value="0" IsPresetParameter="true"/ -->
                <Parameter Name="AFE_LOW_POWER" BitCount="1" StartBit="7" StartByte="845" Value="0" IsPresetParameter="true"/>
                <!-- Parameter Name="INP_RES_SEL" BitCount="4" StartBit="3" StartByte="833" Value="0" IsPresetParameter="true"/ -->
                <!--AFE B-->
                <Parameter Name="START_GAIN_0" ModelType="B" Min="0" Max="143" BitCount="8" Step="1" Value="6" StartBit="0" StartByte="839" IsPresetParameter="true"/>
                <Parameter Name="STOP_GAIN_0" ModelType="B" Min="0" Max="144" BitCount="8" Step="1" Value="144" StartBit="0" StartByte="840" IsPresetParameter="true"/>
                <Parameter Name="POS_STEP_0" ModelType="B" Step="1" DefaultValue="0" BitCount="8" Value="0" StartBit="0" StartByte="841" IsPresetParameter="true"/>
                <Parameter Name="NEG_STEP_0" ModelType="B" Step="1" DefaultValue="255" BitCount="8" Value="255" StartBit="0" StartByte="842" IsPresetParameter="true"/>
                <Parameter Name="START_GAIN_POSITION" ModelType="B" BitCount="8" StartBit="0" Value="452" StartByte="843" HighBitCount="8" HighStartBit="0" HighStartByte="844" ThirdBitCount="1" ThirdStartBit="0" ThirdStartByte="845" IsPresetParameter="true"/>
                <Parameter Name="STOP_GAIN_POSITION" ModelType="B" BitCount="8" StartBit="0" Value="4972" StartByte="846" HighBitCount="8" HighStartBit="0" HighStartByte="847" ThirdBitCount="1" ThirdStartBit="1" ThirdStartByte="845" IsPresetParameter="true"/>
                <Parameter Name="POSITION_STEP_NUM" ModelType="B" BitCount="8" StartBit="0" StartByte="848" HighBitCount="1" HighStartBit="2" HighStartByte="845" IsPresetParameter="false"/>
                <Parameter Name="POSITION_STEP_CLK" ModelType="B" BitCount="8" StartBit="0" StartByte="849" HighBitCount="8" HighStartBit="0" HighStartByte="850" ThirdBitCount="1" ThirdStartBit="3" ThirdStartByte="845" IsPresetParameter="false"/>
                <!--AFE Color-->
                <Parameter Name="START_GAIN_1" Min="0" Max="143" BitCount="8" Step="1" Value="6" StartBit="0" StartByte="870" IsPresetParameter="true"/>
                <Parameter Name="STOP_GAIN_1" Min="0" Max="144" BitCount="8" Step="1" Value="144" StartBit="0" StartByte="871" IsPresetParameter="true"/>
                <Parameter Name="POS_STEP_1" Step="1" DefaultValue="0" BitCount="8" Value="0" StartBit="0" StartByte="872" IsPresetParameter="true"/>
                <Parameter Name="NEG_STEP_1" Step="1" DefaultValue="255" BitCount="8" Value="255" StartBit="0" StartByte="873" IsPresetParameter="true"/>
                <Parameter Name="START_GAIN_POSITION_2" BitCount="8" StartBit="0" Value="452" StartByte="874" HighBitCount="8" HighStartBit="0" HighStartByte="875" ThirdBitCount="1" ThirdStartBit="4" ThirdStartByte="869" IsPresetParameter="true"/>
                <Parameter Name="STOP_GAIN_POSITION_2" BitCount="8" StartBit="0" Value="4972" StartByte="876" HighBitCount="8" HighStartBit="0" HighStartByte="877" ThirdBitCount="1" ThirdStartBit="5" ThirdStartByte="869" IsPresetParameter="true"/>
                <Parameter Name="POSITION_STEP_NUM_2" BitCount="8" StartBit="0" StartByte="878" HighBitCount="1" HighStartBit="6" HighStartByte="869" IsPresetParameter="false"/>
                <Parameter Name="POSITION_STEP_CLK_2" BitCount="8" StartBit="0" StartByte="879" HighBitCount="8" HighStartBit="0" HighStartByte="880" ThirdBitCount="1" ThirdStartBit="7" ThirdStartByte="869" IsPresetParameter="false"/>
                <!--AFE PW-->
                <Parameter Name="START_GAIN_D" Min="0" Max="143" BitCount="8" Step="1" Value="6" StartBit="0" StartByte="338" IsPresetParameter="true"/>
                <Parameter Name="STOP_GAIN_D" Min="0" Max="144" BitCount="8" Step="1" Value="144" StartBit="0" StartByte="339" IsPresetParameter="true"/>
                <Parameter Name="POS_STEP_D" Step="1" DefaultValue="0" BitCount="8" Value="0" StartBit="0" StartByte="340" IsPresetParameter="true"/>
                <Parameter Name="NEG_STEP_D" Step="1" DefaultValue="255" BitCount="8" Value="255" StartBit="0" StartByte="341" IsPresetParameter="true"/>
                <Parameter Name="START_GAIN_POSITION_D" BitCount="8" StartBit="0" Value="452" StartByte="342" HighBitCount="8" HighStartBit="0" HighStartByte="343" ThirdBitCount="1" ThirdStartBit="1" ThirdStartByte="706" IsPresetParameter="true"/>
                <Parameter Name="STOP_GAIN_POSITION_D" BitCount="8" StartBit="0" Value="4972" StartByte="344" HighBitCount="8" HighStartBit="0" HighStartByte="345" ThirdBitCount="1" ThirdStartBit="0" ThirdStartByte="706" IsPresetParameter="true"/>
                <Parameter Name="POSITION_STEP_NUM_D" BitCount="8" StartBit="0" StartByte="703" HighBitCount="1" HighStartBit="2" HighStartByte="706" IsPresetParameter="false"/>
                <Parameter Name="POSITION_STEP_CLK_D" BitCount="8" StartBit="0" StartByte="704" HighBitCount="8" HighStartBit="0" HighStartByte="705" ThirdBitCount="1" ThirdStartBit="3" ThirdStartByte="706" IsPresetParameter="false"/>

                <Parameter Name="BCRelativePosInDMode" BitCount="8" StartBit="0" StartByte="828" Value="0" />
                <Parameter Name="BFSRA" BitCount="4" StartBit="4" StartByte="795" Max="8" Value="0" />
                <Parameter Name="CfmAliasThreshold" BitCount="8" StartBit="0" StartByte="450" Value="0" IsPresetParameter="true"/>
                <Parameter Name="TdiAliasThreshold" BitCount="8" StartBit="0" StartByte="450" Value="0" IsPresetParameter="true" IsDirectValue="true" />
                <Parameter Name="CfmTransmitClamp" BitCount="8" StartBit="0" StartByte="366" Value="0" />
                <Parameter Name="ColorPixelFilter" BitCount="1" StartBit="7" StartByte="764" Value="0" IsPresetParameter="true"/>
                <Parameter Name="PdPixelFilter" BitCount="1" StartBit="7" StartByte="764" Value="0" IsPresetParameter="true" IsDirectValue="true" />
                <Parameter Name="MVIPixelFilter" ModelType="MVI" BitCount="1" StartBit="7" StartByte="764" Value="0" IsPresetParameter="true" IsDirectValue="true" />
                <Parameter Name="DSCMidLineDiff" BitCount="4" StartBit="0" StartByte="572" Value="0" />
                <Parameter Name="Edge" BitCount="3" StartBit="0" StartByte="10" Value="0" Max="6" IsPresetParameter="true"/>
                <Parameter Name="EdgeInc" BitCount="3" StartBit="0" StartByte="10" Value="0" Max="6" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexB" ModelType="B" BitCount="3" StartBit="0" StartByte="40" Value="0" IsDirectValue="true" IsPresetParameter="true"/>
                <Parameter Name="Pen_prb" BitCount="1" StartBit="5" StartByte="566" Value="0" />
                <Parameter Name="ProbeDimension1P25" BitCount="1" StartBit="7" StartByte="713" Value="0" />
                <Parameter Name="SRAID" ModelType="B" BitCount="2" StartBit="0" StartByte="892" Value="0" />
                <Parameter Name="SCPDID" ModelType="B" BitCount="3" StartBit="2" StartByte="892" Value="0" />
                <Parameter Name="SuperHFreq" BitCount="1" StartBit="5" StartByte="582" Value="0" />
                <Parameter Name="SpklSmooth_CaseSel" BitCount="3" StartBit="4" StartByte="420" Value="0" />
                <Parameter Name="SpklSooth_alpha" BitCount="4" StartBit="0" StartByte="420" Value="7" />
                <Parameter Name="BModeDuty" BitCount="3" StartBit="5" StartByte="765" HighBitCount="2" HighStartBit="5" HighStartByte="764" Value="0" />
                <Parameter Name="ATGC_GATE" BitCount="8" StartBit="0" StartByte="651" Value="0" />
                <Parameter Name="SoGataCompensation" BitCount="8" StartBit="0" StartByte="571" Value="0" />
                <Parameter Name="TxGataCompensation" BitCount="8" StartBit="0" StartByte="570" Value="570" />
                <Parameter Name="LogCompressionHar" BitCount="4" StartBit="4" StartByte="357" Value="0" />
                <Parameter Name="UDLayoutValue" BitCount="2" StartBit="6" StartByte="404" Value="0" />
                <Parameter Name="UTGC_FOCUSPOS_Related" BitCount="1" StartBit="0" StartByte="372" Value="0" />
                <Parameter Name="AutoPowerDown_OFF" BitCount="1" StartBit="4" StartByte="372" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="false"/>
                <Parameter Name="WaferNumCode" BitCount="1" StartBit="6" StartByte="311" Value="0" />
                <Parameter Name="WeightSlope" BitCount="8" StartBit="0" StartByte="815" Value="0" />
                <Parameter Name="ADC_HPF_EN" BitCount="1" StartBit="7" StartByte="929" Value="1" IsPresetParameter="true"/>
                <Parameter Name="ADC_HPF_ROUND_EN" BitCount="1" StartBit="6" StartByte="929" Value="0" IsPresetParameter="true"/>
                <Parameter Name="ADC_HPF_CORNER_FREQ" BitCount="4" StartBit="0" StartByte="929" Min="2" Max="10" Value="3" IsPresetParameter="true"/>

                <!--数据同步ID-->
                <Parameter Name="DataSyncID" BitCount="8" StartBit="0" StartByte="893" Value="0" />

                <!--凸阵拓展成像-->
                <Parameter Name="CurvedExpandingCode" BitCount="1" StartBit="7" StartByte="678" Value="0" />
                <Parameter Name="CurvedExpandingOn" BitCount="1" StartBit="6" StartByte="678" Value="0" />

                <!--腹腔镜探头标识-->
                <Parameter Name="LaparoscopicProbeIden" BitCount="1" StartBit="5" StartByte="772" Value="0" />
                <!--1.2D探头焦点阈值处理-->
                <Parameter Name="FocusThreshold" BitCount="4" StartBit="4" StartByte="750" Value="0" />

                <!--数据比较-->
                <Parameter Name="DataCompare_Compartor" BitCount="1" StartBit="6" StartByte="372" Value="0" />
                <Parameter Name="DataCompare_Length" BitCount="8" StartBit="0" StartByte="369" HighBitCount="2" HighStartBit="0" HighStartByte="370" Value="0" />
                <Parameter Name="DataCompare_Route" BitCount="4" StartBit="4" StartByte="370" Value="0" />
                <Parameter Name="DataCompare_StartPoint" BitCount="8" StartBit="0" StartByte="367" HighBitCount="8" HighStartBit="0" HighStartByte="368" Value="0" />
                <Parameter Name="DataCompare_Switch" BitCount="1" StartBit="7" StartByte="372" Value="0" />
                <Parameter Name="DataCompare_Threshold" BitCount="8" StartBit="0" StartByte="371" Value="0" />

                <!--//TODO 三插座探头码读取，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="MulitSlotRead" BitCount="1" StartBit="5" StartByte="601" Value="0" />

                <!--//TODO SuperNeedle功能，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="NeedleTHI" BitCount="1" StartBit="6" StartByte="796" IsPresetParameter="false"/>
                <Parameter Name="NeedleLogCompression" BitCount="4" StartBit="4" StartByte="362" IsPresetParameter="false"/>

                <!--//TODO 按键探头相关，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="ProbeButton1ClearFlag" BitCount="1" StartBit="0" StartByte="678" Value="0" />
                <Parameter Name="ProbeButton2ClearFlag" BitCount="1" StartBit="1" StartByte="678" Value="0" />
                <Parameter Name="ProbeButton3ClearFlag" BitCount="1" StartBit="2" StartByte="678" Value="0" />

                <!--//TODO S-Flow相关，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="SF_AFCoef0" BitCount="8" StartBit="0" StartByte="435" HighBitCount="1" HighStartBit="0" HighStartByte="441" Value="0" />
                <Parameter Name="SF_AFCoef1" BitCount="8" StartBit="0" StartByte="436" HighBitCount="1" HighStartBit="1" HighStartByte="441" Value="0" />
                <Parameter Name="SF_AFCoef2" BitCount="8" StartBit="0" StartByte="437" HighBitCount="1" HighStartBit="2" HighStartByte="441" Value="0" />
                <Parameter Name="SF_AFCoef3" BitCount="8" StartBit="0" StartByte="438" HighBitCount="1" HighStartBit="3" HighStartByte="441" Value="0" />
                <Parameter Name="SF_AFCoef4" BitCount="8" StartBit="0" StartByte="439" HighBitCount="1" HighStartBit="4" HighStartByte="441" Value="0" />
                <Parameter Name="SF_AFGain" BitCount="4" StartBit="0" StartByte="440" Value="0" />
                <Parameter Name="SF_AFShift" BitCount="4" StartBit="4" StartByte="440" Value="0" />
                <Parameter Name="SF_FlashMaxThres" BitCount="8" StartBit="0" StartByte="446" Value="0" />
                <Parameter Name="SF_LeeEn" BitCount="1" StartBit="7" StartByte="434" Value="0" />
                <Parameter Name="SF_LeeGain" BitCount="4" StartBit="0" StartByte="433" Value="0" />
                <Parameter Name="SF_LeeMCoef" BitCount="8" StartBit="0" StartByte="431" Value="0" />
                <Parameter Name="SF_LeeSCoef" BitCount="8" StartBit="0" StartByte="432" Value="0" />
                <Parameter Name="SF_LeeShift" BitCount="4" StartBit="4" StartByte="433" Value="0" />
                <Parameter Name="SF_MergeMode" BitCount="1" StartBit="1" StartByte="444" Value="0" />
                <Parameter Name="SF_MinCpdThreshold" BitCount="8" StartBit="0" StartByte="430" Value="0" />
                <Parameter Name="SF_TissueMaxThres" BitCount="8" StartBit="0" StartByte="445" Value="0" />
                <Parameter Name="SF_TissueStraightMode" BitCount="1" StartBit="0" StartByte="444" Value="0" />
                <Parameter Name="SFEn" BitCount="1" StartBit="7" StartByte="415" Value="0" />
                <Parameter Name="SFFrameGain" BitCount="8" StartBit="0" StartByte="416" Value="0" />
                <Parameter Name="SFMinThreshold" BitCount="8" StartBit="0" StartByte="417" Value="0" />
                <Parameter Name="SFStraightFlag" BitCount="1" StartBit="4" StartByte="415" Value="0" />
                <Parameter Name="DRChangeTimesSFlow" BitCount="4" StartBit="0" StartByte="415" Value="0" />
                <Parameter Name="DRRateOfChangeSFlow" BitCount="8" StartBit="0" StartByte="413" HighBitCount="8" HighStartBit="0" HighStartByte="414" Value="0" />
                <Parameter Name="DRStartDbSFlow" BitCount="4" StartBit="0" StartByte="410" Value="0" />
                <Parameter Name="DRStartDepthSFlow" BitCount="8" StartBit="0" StartByte="411" HighBitCount="8" HighStartBit="0" HighStartByte="412" Value="0" />
                <Parameter Name="LogCompressionSFlow" BitCount="4" StartBit="4" StartByte="410" Value="0" />

                <!--//TODO 模拟插槽、探头码相关，工程师调试，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="SimulateProbeHWCode1" BitCount="8" StartBit="0" StartByte="680" Value="0" />
                <Parameter Name="SimulateProbeHWCode2" BitCount="8" StartBit="0" StartByte="679" Value="0" />
                <Parameter Name="SimulateProbeSocket" BitCount="2" StartBit="0" StartByte="681" Value="0" />
                <Parameter Name="IsSimulateProbe" BitCount="1" StartBit="3" StartByte="681" Value="0" />

                <!--//TODO 数据采集相关，工程师调试，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="DataSamplingStart" BitCount="8" StartBit="0" StartByte="293" HighBitCount="8" HighStartBit="0" HighStartByte="294" IsPresetParameter="false"/>
                <Parameter Name="DataSamplingADChannel" BitCount="6" StartBit="0" StartByte="295" IsPresetParameter="false"/>
                <Parameter Name="DataSamplingLength" BitCount="3" StartBit="1" StartByte="372" Value="0" />
                <Parameter Name="DataSamplingType" BitCount="3" StartBit="1" StartByte="290" Value="0" />

                <!--//TODO 梯形成像相关，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="TrapezoidalMode" BitCount="1" StartBit="6" StartByte="582" ValueType="Bool" Value="false" IsPresetParameter="false"/>

                <!--//TODO DTHI相关，逻辑参考Windows，待添加逻辑-->
                <!--//上边界 与fpga沟通确认当前sonoAir机器中用不到这些参数，但ATOM机器中会用到，故调图界面添加了这些参数。为了不影响sonoAir调图，在这里先注掉-->
<!--                <Parameter Name="DTHIMode" BitCount="2" StartBit="6" StartByte="763" Value="0" />
                <Parameter Name="DTHITxFreq1" BitCount="5" StartBit="0" StartByte="763" Value="0" />
                <Parameter Name="DTHITxFreq2" BitCount="5" StartBit="0" StartByte="764" Value="0" />
                <Parameter Name="DTHITxFreq3" BitCount="5" StartBit="0" StartByte="765" Value="0" />
                <Parameter Name="DTHITxFreq4" BitCount="5" StartBit="0" StartByte="766" Value="0" />
                <Parameter Name="DTHITxFreq5" BitCount="5" StartBit="0" StartByte="767" Value="0" />-->
                <!--//下边界 与fpga沟通确认当前sonoAir机器中用不到这些参数，但ATOM机器中会用到，故调图界面添加了这些参数。为了不影响sonoAir调图，在这里先注掉-->

                <!--//TODO HPRF相关，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="HPRFAnalogTgc0" BitCount="8" StartBit="0" StartByte="628" Value="0" />
                <Parameter Name="HPRFAnalogTgc1" BitCount="8" StartBit="0" StartByte="629" Value="0" />
                <Parameter Name="HPRFAnalogTgc2" BitCount="8" StartBit="0" StartByte="630" Value="0" />
                <Parameter Name="HPRFAnalogTgc3" BitCount="8" StartBit="0" StartByte="631" Value="0" />
                <Parameter Name="HPRFAnalogTgc4" BitCount="8" StartBit="0" StartByte="632" Value="0" />
                <Parameter Name="HPRFAnalogTgc5" BitCount="8" StartBit="0" StartByte="633" Value="0" />
                <Parameter Name="HPRFAnalogTgc6" BitCount="8" StartBit="0" StartByte="634" Value="0" />
                <Parameter Name="HPRFAnalogTgc7" BitCount="8" StartBit="0" StartByte="635" Value="0" />
                <Parameter Name="HPRFDynamicFocusStart" BitCount="8" StartBit="0" StartByte="826" HighBitCount="8" HighStartBit="0" HighStartByte="827" Value="0" />
                <Parameter Name="HPRFWeightStart" BitCount="8" StartBit="0" StartByte="825" Value="0" />

                <!--//TODO HoloPW相关，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="DopSampleDepthWithClockSize" BitCount="8" StartBit="0" StartByte="714" HighBitCount="8" HighStartBit="0" HighStartByte="715" Value="0" />
                <Parameter Name="HoloDBaseLine2" BitCount="4" StartBit="4" StartByte="798" Value="0" />
                <Parameter Name="HoloDBaseLine3" BitCount="4" StartBit="4" StartByte="804" Value="0" />
                <Parameter Name="HoloDBaseLine4" BitCount="4" StartBit="4" StartByte="810" Value="0" />
                <Parameter Name="HoloDSampleDepthWithClockSize2" BitCount="8" StartBit="0" StartByte="799" HighBitCount="8" HighStartBit="0" HighStartByte="800" Value="0" />
                <Parameter Name="HoloDSampleDepthWithClockSize3" BitCount="8" StartBit="0" StartByte="805" HighBitCount="8" HighStartBit="0" HighStartByte="806" Value="0" />
                <Parameter Name="HoloDSampleDepthWithClockSize4" BitCount="8" StartBit="0" StartByte="811" HighBitCount="8" HighStartBit="0" HighStartByte="812" Value="0" />
                <Parameter Name="HoloDScanLine2" BitCount="8" StartBit="0" StartByte="797" HighBitCount="3" HighStartBit="0" HighStartByte="798" Value="0" />
                <Parameter Name="HoloDScanLine3" BitCount="8" StartBit="0" StartByte="803" HighBitCount="3" HighStartBit="0" HighStartByte="804" Value="0" />
                <Parameter Name="HoloDScanLine4" BitCount="8" StartBit="0" StartByte="809" HighBitCount="3" HighStartBit="0" HighStartByte="810" Value="0" />
                <Parameter Name="HoloEN" BitCount="1" StartBit="7" StartByte="796" Value="0" />
                <Parameter Name="HoloIndex" BitCount="2" StartBit="4" StartByte="796" Value="0" />
                <Parameter Name="HoloNum" BitCount="2" StartBit="0" StartByte="796" Value="0" />
                <Parameter Name="HoloPRT2" BitCount="8" StartBit="0" StartByte="801" HighBitCount="8" HighStartBit="0" HighStartByte="802" Value="0" />
                <Parameter Name="HoloPRT3" BitCount="8" StartBit="0" StartByte="807" HighBitCount="8" HighStartBit="0" HighStartByte="808" Value="0" />
                <Parameter Name="HoloPRT4" BitCount="8" StartBit="0" StartByte="813" HighBitCount="8" HighStartBit="0" HighStartByte="814" Value="0" />
                <Parameter Name="HoloPW_TX_FOCUS_DIS2" BitCount="8" StartBit="0" StartByte="829" HighBitCount="8" HighStartBit="0" HighStartByte="830" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="831" Value="0" />
                <Parameter Name="HoloPW_TX_FOCUS_DIS3" BitCount="8" StartBit="0" StartByte="832" HighBitCount="8" HighStartBit="0" HighStartByte="833" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="834" Value="0" />
                <Parameter Name="HoloPW_TX_FOCUS_DIS4" BitCount="8" StartBit="0" StartByte="835" HighBitCount="8" HighStartBit="0" HighStartByte="836" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="837" Value="0" />
                <Parameter Name="PWFocusPosCom1" BitCount="4" StartBit="0" StartByte="862" Value="0" />
                <Parameter Name="PWFocusPosCom2" BitCount="4" StartBit="4" StartByte="863" Value="0" />
                <Parameter Name="PWFocusPosCom3" BitCount="4" StartBit="0" StartByte="863" Value="0" />

                <!--//TODO 任意线密度相关，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="TXLensDelayVal" BitCount="8" StartBit="0" StartByte="605" Value="0" />
                <Parameter Name="RXLensDelayVal" BitCount="8" StartBit="0" StartByte="595" Value="0" />
                <Parameter Name="TxSyncSwitch" BitCount="1" StartBit="6" StartByte="676" Value="0" />
                <Parameter Name="ZoomCenterLineChoose" BitCount="1" StartBit="7" StartByte="777" Value="0" />
                <Parameter Name="TX_STEP_DIST" BitCount="8" StartBit="0" StartByte="502" HighBitCount="8" HighStartBit="0" HighStartByte="503" Value="0" />
                <Parameter Name="TX_STEP_DIST_RECIP" BitCount="8" StartBit="0" StartByte="504" HighBitCount="8" HighStartBit="0" HighStartByte="505" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="506" Value="0" />
                <Parameter Name="B_RX_FNUM" BitCount="3" StartBit="0" StartByte="886" Value="0" />
                <Parameter Name="B_RX_LINE_ANGLE_INTERVAL" BitCount="8" StartBit="0" StartByte="507" HighBitCount="8" HighStartBit="0" HighStartByte="508" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="509" Value="0" />
                <Parameter Name="B_RX_LNUM" BitCount="8" StartBit="0" StartByte="494" HighBitCount="2" HighStartBit="0" HighStartByte="496" ThirdBitCount="2" ThirdStartBit="4" ThirdStartByte="496" Value="0" />
                <Parameter Name="B_RX_OFFSET_COEF" BitCount="8" StartBit="0" StartByte="528" HighBitCount="8" HighStartBit="0" HighStartByte="529" Value="0" />
                <Parameter Name="B_TX_LINE_ANGLE_INTERVAL" BitCount="8" StartBit="0" StartByte="510" HighBitCount="8" HighStartBit="0" HighStartByte="511" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="512" Value="0" />
                <Parameter Name="B_TX_LNUM" BitCount="8" StartBit="0" StartByte="495" HighBitCount="2" HighStartBit="2" HighStartByte="496" ThirdBitCount="2" ThirdStartBit="6" ThirdStartByte="496" Value="0" />
                <Parameter Name="B_TX_OFFSET_COEF" BitCount="8" StartBit="0" StartByte="530" HighBitCount="8" HighStartBit="0" HighStartByte="531" Value="0" />
                <Parameter Name="RX_STEP_DIST" BitCount="8" StartBit="0" StartByte="497" HighBitCount="8" HighStartBit="0" HighStartByte="498" Value="0" />
                <Parameter Name="RX_STEP_DIST_RECI" BitCount="8" StartBit="0" StartByte="499" HighBitCount="8" HighStartBit="0" HighStartByte="500" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="501" Value="0" />
                <Parameter Name="C_RX_FNUM" BitCount="3" StartBit="4" StartByte="886" Value="0" />
                <Parameter Name="C_RX_LINE_ANGLE_INTERVAL" BitCount="8" StartBit="0" StartByte="517" HighBitCount="8" HighStartBit="0" HighStartByte="518" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="519" Value="0" />
                <Parameter Name="C_RX_LNUM" BitCount="8" StartBit="0" StartByte="514" HighBitCount="2" HighStartBit="0" HighStartByte="516" ThirdBitCount="2" ThirdStartBit="4" ThirdStartByte="516" Value="0" />
                <Parameter Name="C_RX_OFFSET_COEF" BitCount="8" StartBit="0" StartByte="532" HighBitCount="8" HighStartBit="0" HighStartByte="533" Value="0" />
                <Parameter Name="C_TX_LINE_ANGLE_INTERVAL" BitCount="8" StartBit="0" StartByte="520" HighBitCount="8" HighStartBit="0" HighStartByte="521" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="522" Value="0" />
                <Parameter Name="C_TX_LNUM" BitCount="8" StartBit="0" StartByte="515" HighBitCount="2" HighStartBit="2" HighStartByte="516" ThirdBitCount="2" ThirdStartBit="6" ThirdStartByte="516" Value="0" />
                <Parameter Name="C_TX_OFFSET_COEF" BitCount="8" StartBit="0" StartByte="534" HighBitCount="8" HighStartBit="0" HighStartByte="535" Value="0" />
                <Parameter Name="C_WALL_OPTION" BitCount="2" StartBit="4" StartByte="887" Value="0" />
                <Parameter Name="MAX_RADIUS_ANGLE" BitCount="8" StartBit="0" StartByte="523" HighBitCount="8" HighStartBit="0" HighStartByte="524" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="525" Value="0" />
                <Parameter Name="PROB_ELEM_NUM" BitCount="8" StartBit="0" StartByte="526" HighBitCount="2" HighStartBit="0" HighStartByte="527" Value="0" />
                <Parameter Name="SHVSW_NO" BitCount="8" StartBit="0" StartByte="585" HighBitCount="2" HighStartBit="2" HighStartByte="776" Value="0" />
                <Parameter Name="EHVSW_NO" BitCount="8" StartBit="0" StartByte="586" HighBitCount="2" HighStartBit="4" HighStartByte="776" Value="0" />
                <Parameter Name="HVSW_Config" BitCount="4" StartBit="0" StartByte="777" Value="0" />
                <Parameter Name="HVSW_DN" BitCount="7" StartBit="0" StartByte="589" Value="0" />
                <Parameter Name="HVSW_NUM" BitCount="8" StartBit="0" StartByte="587" HighBitCount="2" HighStartBit="0" HighStartByte="776" Value="0" />
                <Parameter Name="ChannelConnectionMode" BitCount="1" StartBit="4" StartByte="676" Value="0" />
                <Parameter Name="ChannelIdentifier" BitCount="1" StartBit="5" StartByte="676" Value="0" />
                <Parameter Name="AngleSpacingC" BitCount="8" StartBit="0" StartByte="590" HighBitCount="8" HighStartBit="0" HighStartByte="820" Value="0" />
                <Parameter Name="LineSpacingC" BitCount="8" StartBit="0" StartByte="591" HighBitCount="8" HighStartBit="0" HighStartByte="592" Value="0" />
                <Parameter Name="LensDepth" BitCount="8" StartBit="0" StartByte="593" HighBitCount="8" HighStartBit="0" HighStartByte="594" Value="0" />
                <Parameter Name="FocDepth" BitCount="8" StartBit="0" StartByte="596" HighBitCount="8" HighStartBit="0" HighStartByte="597" Value="0" />
                <Parameter Name="SAMP_DIST_RECIP" BitCount="8" StartBit="0" StartByte="598" HighBitCount="8" HighStartBit="0" HighStartByte="599" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="600" Value="0" />
                <Parameter Name="PA_VERT_DIST_Enable" BitCount="1" StartBit="7" StartByte="601" Value="0" />
                <Parameter Name="PA_VERT_DIST" BitCount="8" StartBit="0" StartByte="602" HighBitCount="8" HighStartBit="0" HighStartByte="603" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="604" Value="0" />
                <Parameter Name="DSCCenterLine" BitCount="1" StartBit="6" StartByte="601" Value="0" TrueValue="1" value="false" IsPresetParameter="false"/>
                <Parameter Name="HTGC_LNUM_PER_SEGMENT" BitCount="6" StartBit="0" StartByte="640" Value="0" />
                <Parameter Name="HTGC_LNUM_PER_SEGMENT_RECIP" BitCount="8" StartBit="0" StartByte="644" HighBitCount="2" HighStartBit="0" HighStartByte="645" Value="0" />
                <Parameter Name="ACW_RX_STEP_DIST_RECIP" BitCount="8" StartBit="0" StartByte="641" HighBitCount="8" HighStartBit="0" HighStartByte="642" ThirdBitCount="4" ThirdStartBit="0" ThirdStartByte="643" Value="0" />
                <Parameter Name="ACW_DCW" BitCount="1" StartBit="7" StartByte="643" Value="0" />
                <Parameter Name="CW_HV_Safe" BitCount="1" StartBit="6" StartByte="643" ValueType="Bool" IsPresetParameter="false" />
                <Parameter Name="CW_TX_CHAN_SEL" BitCount="4" StartBit="4" StartByte="645" Value="0" />
                <Parameter Name="CW_TX_RX_SWITCH" BitCount="1" StartBit="6" StartByte="646" Value="0" />
                <Parameter Name="TRANSMIT_APERTURE_ADD_SYMMETRY" BitCount="1" StartBit="4" StartByte="648" Value="0" />
                <Parameter Name="ELEMENT_CHANNEL_MAPPING" BitCount="1" StartBit="5" StartByte="648" Value="0" />
                <Parameter Name="D_RX_FNUM" BitCount="3" StartBit="0" StartByte="887" Value="0" />
                <Parameter Name="Before_Zoom_B_RX_LNUM" BitCount="8" StartBit="0" StartByte="888" HighBitCount="4" HighStartBit="0" HighStartByte="889" Value="0" />
                <Parameter Name="PB_Pitch" BitCount="8" StartBit="0" StartByte="883" HighBitCount="8" HighStartBit="0" HighStartByte="884" ThirdBitCount="8" ThirdStartBit="0" ThirdStartByte="885" Value="0" />
                <Parameter Name="BTXFnumCoef" BitCount="8" StartBit="0" StartByte="654" HighBitCount="8" HighStartBit="0" HighStartByte="655" ThirdBitCount="4" ThirdStartBit="0" ThirdStartByte="656" Value="0" />
                <Parameter Name="AmplifyingFrameBoundaryDepthZoom" BitCount="8" StartBit="0" StartByte="821" HighBitCount="8" HighStartBit="0" HighStartByte="822" ThirdBitCount="4" ThirdStartBit="0" ThirdStartByte="823" Value="0" />

                <!--//TODO FPGA升级CRC校验相关，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="Txbf2CRC" BitCount="8" StartBit="0" StartByte="649" Value="0" />
                <Parameter Name="TxbfCRC" BitCount="8" StartBit="0" StartByte="583" Value="0" />
                <Parameter Name="CPLD2EnFlag_QBit10" BitCount="1" StartBit="0" StartByte="637" Value="0" />
                <Parameter Name="CPLDCRC" BitCount="8" StartBit="0" StartByte="686" Value="0" />
                <Parameter Name="DspCRC" BitCount="8" StartBit="0" StartByte="584" Value="0" />

                <!--//TODO TxCode编码发射相关，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="Tx1Code1" BitCount="8" StartBit="0" StartByte="609" HighBitCount="8" HighStartBit="0" HighStartByte="610" Value="0" />
                <Parameter Name="Tx1Code1Len" BitCount="4" StartBit="0" StartByte="608" Value="0" />
                <Parameter Name="Tx1Code2" BitCount="8" StartBit="0" StartByte="611" HighBitCount="8" HighStartBit="0" HighStartByte="612" Value="0" />
                <Parameter Name="Tx1Code2Len" BitCount="4" StartBit="4" StartByte="608" Value="0" />
                <Parameter Name="Tx1TransverseFilterLen" BitCount="4" StartBit="0" StartByte="652" Value="0" />
                <Parameter Name="Tx2Code1" BitCount="8" StartBit="0" StartByte="614" HighBitCount="8" HighStartBit="0" HighStartByte="615" Value="0" />
                <Parameter Name="Tx2Code1Len" BitCount="4" StartBit="0" StartByte="613" Value="0" />
                <Parameter Name="Tx2Code2" BitCount="8" StartBit="0" StartByte="616" HighBitCount="8" HighStartBit="0" HighStartByte="617" Value="0" />
                <Parameter Name="Tx2Code2Len" BitCount="4" StartBit="4" StartByte="613" Value="0" />
                <Parameter Name="Tx2TransverseFilterLen" BitCount="4" StartBit="4" StartByte="652" Value="0" />
                <Parameter Name="Tx3Code1" BitCount="8" StartBit="0" StartByte="619" HighBitCount="8" HighStartBit="0" HighStartByte="620" Value="0" />
                <Parameter Name="Tx3Code1Len" BitCount="4" StartBit="0" StartByte="618" Value="0" />
                <Parameter Name="Tx3Code2" BitCount="8" StartBit="0" StartByte="621" HighBitCount="8" HighStartBit="0" HighStartByte="622" Value="0" />
                <Parameter Name="Tx3Code2Len" BitCount="4" StartBit="4" StartByte="618" Value="0" />
                <Parameter Name="Tx3TransverseFilterLen" BitCount="4" StartBit="0" StartByte="653" Value="0" />
                <Parameter Name="Tx4Code1" BitCount="8" StartBit="0" StartByte="624" HighBitCount="8" HighStartBit="0" HighStartByte="625" Value="0" />
                <Parameter Name="Tx4Code1Len" BitCount="4" StartBit="0" StartByte="623" Value="0" />
                <Parameter Name="Tx4Code2" BitCount="8" StartBit="0" StartByte="626" HighBitCount="8" HighStartBit="0" HighStartByte="627" Value="0" />
                <Parameter Name="Tx4Code2Len" BitCount="4" StartBit="4" StartByte="623" Value="0" />
                <Parameter Name="Tx4TransverseFilterLen" BitCount="4" StartBit="4" StartByte="653" Value="0" />
                <Parameter Name="TxCodeBCPosPara" BitCount="8" StartBit="0" StartByte="675" Value="0" />
                <Parameter Name="TxCodeCompensation" BitCount="8" StartBit="0" StartByte="658" Value="0" />
                <Parameter Name="TxCodeSwitch" BitCount="1" StartBit="7" StartByte="607" Value="0" />
                <Parameter Name="TxCodeUTGC1" BitCount="8" StartBit="0" StartByte="659" Value="0" />
                <Parameter Name="TxCodeUTGC10" BitCount="8" StartBit="0" StartByte="668" Value="0" />
                <Parameter Name="TxCodeUTGC11" BitCount="8" StartBit="0" StartByte="669" Value="0" />
                <Parameter Name="TxCodeUTGC12" BitCount="8" StartBit="0" StartByte="670" Value="0" />
                <Parameter Name="TxCodeUTGC13" BitCount="8" StartBit="0" StartByte="671" Value="0" />
                <Parameter Name="TxCodeUTGC14" BitCount="8" StartBit="0" StartByte="672" Value="0" />
                <Parameter Name="TxCodeUTGC15" BitCount="8" StartBit="0" StartByte="673" Value="0" />
                <Parameter Name="TxCodeUTGC16" BitCount="8" StartBit="0" StartByte="674" Value="0" />
                <Parameter Name="TxCodeUTGC2" BitCount="8" StartBit="0" StartByte="660" Value="0" />
                <Parameter Name="TxCodeUTGC3" BitCount="8" StartBit="0" StartByte="661" Value="0" />
                <Parameter Name="TxCodeUTGC4" BitCount="8" StartBit="0" StartByte="662" Value="0" />
                <Parameter Name="TxCodeUTGC5" BitCount="8" StartBit="0" StartByte="663" Value="0" />
                <Parameter Name="TxCodeUTGC6" BitCount="8" StartBit="0" StartByte="664" Value="0" />
                <Parameter Name="TxCodeUTGC7" BitCount="8" StartBit="0" StartByte="665" Value="0" />
                <Parameter Name="TxCodeUTGC8" BitCount="8" StartBit="0" StartByte="666" Value="0" />
                <Parameter Name="TxCodeUTGC9" BitCount="8" StartBit="0" StartByte="667" Value="0" />
                <Parameter Name="CodeTransimitDoubleEnable" BitCount="1" StartBit="6" StartByte="607" Value="0" />
                <Parameter Name="CodeTransimitEnable" BitCount="1" StartBit="7" StartByte="607" Value="0" />

                <!--//上边界 与fpga沟通确认当前sonoAir机器中用不到这些参数，但ATOM机器中会用到，故调图界面添加了这些参数。为了不影响sonoAir调图，在这里先注掉-->
                <!--<Parameter Name="CodeTransmitionLength11" BitCount="4" StartBit="0" StartByte="608" Value="0" />
                <Parameter Name="CodeTransmitionLength12" BitCount="4" StartBit="4" StartByte="608" Value="0" />
                <Parameter Name="CodeTransmitionLength21" BitCount="4" StartBit="0" StartByte="613" Value="0" />
                <Parameter Name="CodeTransmitionLength22" BitCount="4" StartBit="4" StartByte="613" Value="0" />
                <Parameter Name="CodeTransmitionLength31" BitCount="4" StartBit="0" StartByte="618" Value="0" />
                <Parameter Name="CodeTransmitionLength32" BitCount="4" StartBit="4" StartByte="618" Value="0" />
                <Parameter Name="CodeTransmitionLength41" BitCount="4" StartBit="0" StartByte="623" Value="0" />
                <Parameter Name="CodeTransmitionLength42" BitCount="4" StartBit="4" StartByte="623" Value="0" />
                <Parameter Name="CodeTransmitionValue11" BitCount="8" StartBit="0" StartByte="609" HighBitCount="8" HighStartBit="0" HighStartByte="610" Value="0" />
                <Parameter Name="CodeTransmitionValue12" BitCount="8" StartBit="0" StartByte="611" HighBitCount="8" HighStartBit="0" HighStartByte="612" Value="0" />
                <Parameter Name="CodeTransmitionValue21" BitCount="8" StartBit="0" StartByte="614" HighBitCount="8" HighStartBit="0" HighStartByte="615" Value="0" />
                <Parameter Name="CodeTransmitionValue22" BitCount="8" StartBit="0" StartByte="616" HighBitCount="8" HighStartBit="0" HighStartByte="617" Value="0" />
                <Parameter Name="CodeTransmitionValue31" BitCount="8" StartBit="0" StartByte="619" HighBitCount="8" HighStartBit="0" HighStartByte="620" Value="0" />
                <Parameter Name="CodeTransmitionValue32" BitCount="8" StartBit="0" StartByte="621" HighBitCount="8" HighStartBit="0" HighStartByte="622" Value="0" />
                <Parameter Name="CodeTransmitionValue41" BitCount="8" StartBit="0" StartByte="624" HighBitCount="8" HighStartBit="0" HighStartByte="625" Value="0" />
                <Parameter Name="CodeTransmitionValue42" BitCount="8" StartBit="0" StartByte="626" HighBitCount="8" HighStartBit="0" HighStartByte="627" Value="0" />-->
                <!--//下边界 与fpga沟通确认当前sonoAir机器中用不到这些参数，但ATOM机器中会用到，故调图界面添加了这些参数。为了不影响sonoAir调图，在这里先注掉-->

                <Parameter Name="EncodeTX_GAIN" BitCount="8" StartBit="0" StartByte="636" Value="0" />
                <Parameter Name="TransverseFilter" BitCount="1" StartBit="3" StartByte="648" Value="0" />

                <!--//TODO 以下造影相关参数，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="Contrast_Accumulation" BitCount="3" StartBit="0" StartByte="751" Value="0" />
                <Parameter Name="Contrast_AFCoef0" BitCount="8" StartBit="0" StartByte="755" HighBitCount="1" HighStartBit="0" HighStartByte="760" Value="0" />
                <Parameter Name="Contrast_AFCoef1" BitCount="8" StartBit="0" StartByte="756" HighBitCount="1" HighStartBit="1" HighStartByte="760" Value="0" />
                <Parameter Name="Contrast_AFCoef2" BitCount="8" StartBit="0" StartByte="757" HighBitCount="1" HighStartBit="2" HighStartByte="760" Value="0" />
                <Parameter Name="Contrast_AFCoef3" BitCount="8" StartBit="0" StartByte="758" HighBitCount="1" HighStartBit="3" HighStartByte="760" Value="0" />
                <Parameter Name="Contrast_AFCoef4" BitCount="8" StartBit="0" StartByte="759" HighBitCount="1" HighStartBit="4" HighStartByte="760" Value="0" />
                <Parameter Name="Contrast_AFGain" BitCount="4" StartBit="0" StartByte="761" Value="0" />
                <Parameter Name="Contrast_AFShift" BitCount="4" StartBit="4" StartByte="761" Value="0" />
                <Parameter Name="Contrast_LeeEn" BitCount="1" StartBit="7" StartByte="751" Value="0" />
                <Parameter Name="Contrast_LeeGain" BitCount="4" StartBit="0" StartByte="754" Value="0" />
                <Parameter Name="Contrast_LeeMCoef" BitCount="8" StartBit="0" StartByte="752" Value="0" />
                <Parameter Name="Contrast_LeeSCoef" BitCount="8" StartBit="0" StartByte="753" Value="0" />
                <Parameter Name="Contrast_LeeShift" BitCount="4" StartBit="4" StartByte="754" Value="0" />
                <Parameter Name="Contrast_Mode" BitCount="2" StartBit="5" StartByte="751" Value="0" />
                <Parameter Name="Contrast_Visual" BitCount="2" StartBit="3" StartByte="751" Value="0" />
                <Parameter Name="ContrastATgc0" BitCount="8" StartBit="0" StartByte="740" Value="0" />
                <Parameter Name="ContrastATgc1" BitCount="8" StartBit="0" StartByte="741" Value="0" />
                <Parameter Name="ContrastATgc2" BitCount="8" StartBit="0" StartByte="742" Value="0" />
                <Parameter Name="ContrastATgc3" BitCount="8" StartBit="0" StartByte="743" Value="0" />
                <Parameter Name="ContrastATgc4" BitCount="8" StartBit="0" StartByte="744" Value="0" />
                <Parameter Name="ContrastATgc5" BitCount="8" StartBit="0" StartByte="745" Value="0" />
                <Parameter Name="ContrastATgc6" BitCount="8" StartBit="0" StartByte="746" Value="0" />
                <Parameter Name="ContrastATgc7" BitCount="8" StartBit="0" StartByte="747" Value="0" />
                <Parameter Name="ContrastLogCopression" BitCount="4" StartBit="4" StartByte="362" Value="0" />
                <Parameter Name="ContrastSW" ModelType="B" BitCount="1" StartBit="0" StartByte="749" Value="0" />
                <Parameter Name="ContrastUTgc00" BitCount="8" StartBit="0" StartByte="724" Value="0" />
                <Parameter Name="ContrastUTgc01" BitCount="8" StartBit="0" StartByte="725" Value="0" />
                <Parameter Name="ContrastUTgc02" BitCount="8" StartBit="0" StartByte="726" Value="0" />
                <Parameter Name="ContrastUTgc03" BitCount="8" StartBit="0" StartByte="727" Value="0" />
                <Parameter Name="ContrastUTgc04" BitCount="8" StartBit="0" StartByte="728" Value="0" />
                <Parameter Name="ContrastUTgc05" BitCount="8" StartBit="0" StartByte="729" Value="0" />
                <Parameter Name="ContrastUTgc06" BitCount="8" StartBit="0" StartByte="730" Value="0" />
                <Parameter Name="ContrastUTgc07" BitCount="8" StartBit="0" StartByte="731" Value="0" />
                <Parameter Name="ContrastUTgc08" BitCount="8" StartBit="0" StartByte="732" Value="0" />
                <Parameter Name="ContrastUTgc09" BitCount="8" StartBit="0" StartByte="733" Value="0" />
                <Parameter Name="ContrastUTgc10" BitCount="8" StartBit="0" StartByte="734" Value="0" />
                <Parameter Name="ContrastUTgc11" BitCount="8" StartBit="0" StartByte="735" Value="0" />
                <Parameter Name="ContrastUTgc12" BitCount="8" StartBit="0" StartByte="736" Value="0" />
                <Parameter Name="ContrastUTgc13" BitCount="8" StartBit="0" StartByte="737" Value="0" />
                <Parameter Name="ContrastUTgc14" BitCount="8" StartBit="0" StartByte="738" Value="0" />
                <Parameter Name="ContrastUTgc15" BitCount="8" StartBit="0" StartByte="739" Value="0" />
                <Parameter Name="ContrastWTStartPoint" BitCount="8" StartBit="0" StartByte="748" Value="0" />
                <Parameter Name="ContrastDynamic" Value="15" Min="0" Max="15" BitCount="4" StartBit="4" StartByte="723" DefaultValue="15" IsPresetParameter="true"/>
                <Parameter Name="ContrastWeight" Value="15" Min="0" Max="15" BitCount="4" StartBit="0" StartByte="723" DefaultValue="15" IsPresetParameter="true"/>
                <Parameter Name="ContrastGain" Value="128" Min="0" Max="255" BitCount="8" StartBit="0" StartByte="722" DefaultValue="128" IsPresetParameter="true"/>
                <Parameter Name="ContrastMode" Value="0" Min="0" Max="1" BitCount="1" StartBit="7" StartByte="749" DefaultValue="0" IsPresetParameter="false"/>
                <Parameter Name="Flash" BitCount="1" StartBit="3" StartByte="750" Value="0" />
                <Parameter Name="DRChangeTimesHar" BitCount="4" StartBit="0" StartByte="362" Value="0" />
                <Parameter Name="DRRateOfChangeHar" BitCount="8" StartBit="0" StartByte="360" HighBitCount="8" HighStartBit="0" HighStartByte="361" Value="0" />
                <Parameter Name="DRStartDbHar" BitCount="4" StartBit="0" StartByte="357" Value="0" />
                <Parameter Name="DRStartDepthHar" BitCount="8" StartBit="0" StartByte="358" HighBitCount="8" HighStartBit="0" HighStartByte="359" Value="0" />

                <!--//TODO 以下1个是频谱和声音同步相关，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="VolumeOffsetOfFreqSpectrum" BitCount="8" StartBit="0" StartByte="890" HighBitCount="8" HighStartBit="0" HighStartByte="891" Value="0" />

                <!--//TODO 以下4个是RegionZoom新增控制字，逻辑参考Windows，待添加逻辑-->
                <Parameter Name="ZoomEndWaferLineFirstPart" BitCount="8" StartBit="0" StartByte="817" Value="0" />
                <Parameter Name="ZoomEndWaferLineSecondPart" BitCount="4" StartBit="0" StartByte="818" Value="0" />
                <Parameter Name="ZoomStartWaferLineFirstPart" BitCount="8" StartBit="0" StartByte="816" Value="0" />
                <Parameter Name="ZoomStartWaferLineSecondPart" BitCount="4" StartBit="4" StartByte="818" Value="0" />

                <!--//TODO 在Phoenix平台可能不需要的参数-->
<!--                <Parameter Name="DopSteerAngle" BitCount="5" StartBit="0" StartByte="768" Value="0" />
                <Parameter Name="DopSteerDirection" BitCount="1" StartBit="6" StartByte="768" Value="0" />-->
                <Parameter Name="ColorEnFlag" BitCount="1" StartBit="7" StartByte="589" Value="0" />
                <Parameter Name="BWAnalogTgcCompensation0" BitCount="8" StartBit="0" StartByte="378" Value="0" />
                <Parameter Name="BWAnalogTgcCompensation1" BitCount="8" StartBit="0" StartByte="379" Value="0" />
                <Parameter Name="BWAnalogTgcCompensation2" BitCount="8" StartBit="0" StartByte="380" Value="0" />
                <Parameter Name="BWAnalogTgcCompensation3" BitCount="8" StartBit="0" StartByte="381" Value="0" />
                <Parameter Name="BWAnalogTgcCompensation4" BitCount="8" StartBit="0" StartByte="382" Value="0" />
                <Parameter Name="BWAnalogTgcCompensation5" BitCount="8" StartBit="0" StartByte="383" Value="0" />
                <Parameter Name="BWAnalogTgcCompensation6" BitCount="8" StartBit="0" StartByte="384" Value="0" />
                <Parameter Name="BWAnalogTgcCompensation7" BitCount="8" StartBit="0" StartByte="385" Value="0" />
                <Parameter Name="BWUniformTgcCompensation0" BitCount="8" StartBit="0" StartByte="386" Value="0" />
                <Parameter Name="BWUniformTgcCompensation1" BitCount="8" StartBit="0" StartByte="387" Value="0" />
                <Parameter Name="BWUniformTgcCompensation10" BitCount="8" StartBit="0" StartByte="396" Value="0" />
                <Parameter Name="BWUniformTgcCompensation11" BitCount="8" StartBit="0" StartByte="397" Value="0" />
                <Parameter Name="BWUniformTgcCompensation12" BitCount="8" StartBit="0" StartByte="398" Value="0" />
                <Parameter Name="BWUniformTgcCompensation13" BitCount="8" StartBit="0" StartByte="399" Value="0" />
                <Parameter Name="BWUniformTgcCompensation14" BitCount="8" StartBit="0" StartByte="400" Value="0" />
                <Parameter Name="BWUniformTgcCompensation15" BitCount="8" StartBit="0" StartByte="401" Value="0" />
                <Parameter Name="BWUniformTgcCompensation2" BitCount="8" StartBit="0" StartByte="388" Value="0" />
                <Parameter Name="BWUniformTgcCompensation3" BitCount="8" StartBit="0" StartByte="389" Value="0" />
                <Parameter Name="BWUniformTgcCompensation4" BitCount="8" StartBit="0" StartByte="390" Value="0" />
                <Parameter Name="BWUniformTgcCompensation5" BitCount="8" StartBit="0" StartByte="391" Value="0" />
                <Parameter Name="BWUniformTgcCompensation6" BitCount="8" StartBit="0" StartByte="392" Value="0" />
                <Parameter Name="BWUniformTgcCompensation7" BitCount="8" StartBit="0" StartByte="393" Value="0" />
                <Parameter Name="BWUniformTgcCompensation8" BitCount="8" StartBit="0" StartByte="394" Value="0" />
                <Parameter Name="BWUniformTgcCompensation9" BitCount="8" StartBit="0" StartByte="395" Value="0" />
                <Parameter Name="ECGDraw" ModelType="ECG" BitCount="1" StartBit="6" StartByte="656" Value="0" />
                <Parameter Name="StartDepthDeltaLine" BitCount="8" StartBit="0" StartByte="712" HighBitCount="2" HighStartBit="0" HighStartByte="713" Min="0" Max="255" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="StaticOptimizationEn" BitCount="1" StartBit="5" StartByte="415" Value="0" />
                <Parameter Name="ResetImageRefreshFlag" BitCount="1" StartBit="4" StartByte="572" Value="0" />
                <Parameter Name="ResetFreqSpectrum" BitCount="1" StartBit="5" StartByte="572" Value="0" />
                <Parameter Name="FreqSpectrumTrig" BitCount="1" StartBit="6" StartByte="881" Value="0" />
                <Parameter Name="BCPosOnTdi" BitCount="8" StartBit="0" StartByte="606" Value="0" />
                <Parameter Name="MediateChoice" BitCount="4" StartBit="0" StartByte="607" Value="0" />
                <Parameter Name="RefreshOnMoveLine" BitCount="1" StartBit="4" StartByte="607" Value="0" />
                <Parameter Name="ECGLRInvert" ModelType="ECG" BitCount="1" StartBit="5" StartByte="152" Value="0" />
                <Parameter Name="ECGStartLine" ModelType="ECG" BitCount="8" StartBit="0" StartByte="580" HighBitCount="2" HighStartBit="2" HighStartByte="582" Value="0" />
                <Parameter Name="ECGStopLine" ModelType="ECG" BitCount="8" StartBit="0" StartByte="581" HighBitCount="2" HighStartBit="0" HighStartByte="582" Value="0" />
                <Parameter Name="NewECGDly" ModelType="ECG" BitCount="8" StartBit="0" StartByte="780" HighBitCount="8" HighStartBit="0" HighStartByte="781" Value="0" />
                <Parameter Name="PrtOfM" BitCount="8" StartBit="0" StartByte="373" HighBitCount="8" HighStartBit="0" HighStartByte="374" Value="0" />
                <Parameter Name="PrtOfMin" BitCount="8" StartBit="0" StartByte="405" HighBitCount="8" HighStartBit="0" HighStartByte="406" Value="0" />
                <Parameter Name="MLineFundamentalHarmonicComplex" BitCount="1" StartBit="7" StartByte="336" Value="0" />
                <Parameter Name="MaxDepthFocusNumber" BitCount="4" StartBit="3" StartByte="334" Value="0" />
                <Parameter Name="DynamicCompensation" BitCount="8" StartBit="0" StartByte="449" Value="0" />
                <Parameter Name="DualApoEn" BitCount="1" StartBit="6" StartByte="415" Value="0" />
                <Parameter Name="DualBMode" BitCount="1" StartBit="6" StartByte="572" Value="0" />
                <Parameter Name="AcousticPowerEn" BitCount="1" StartBit="6" StartByte="713" Value="0" />

                <Parameter Name="PLFIR_Coef0" Value="0" BitCount="8" StartBit="0" StartByte="851" HighBitCount="1" HighStartBit="0" HighStartByte="861" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PLFIR_Coef1" Value="0" BitCount="8" StartBit="0" StartByte="852" HighBitCount="1" HighStartBit="1" HighStartByte="861" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PLFIR_Coef2" Value="0" BitCount="8" StartBit="0" StartByte="853" HighBitCount="1" HighStartBit="2" HighStartByte="861" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PLFIR_Shift" Value="0" BitCount="4" StartBit="0" StartByte="854" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PLFIR_Gain" Value="0" BitCount="4" StartBit="4" StartByte="854" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PAFIR_Coef0" Value="0" BitCount="8" StartBit="0" StartByte="855" HighBitCount="1" HighStartBit="4" HighStartByte="860" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PAFIR_Coef1" Value="0" BitCount="8" StartBit="0" StartByte="856" HighBitCount="1" HighStartBit="5" HighStartByte="860" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PAFIR_Coef2" Value="0" BitCount="8" StartBit="0" StartByte="857" HighBitCount="1" HighStartBit="6" HighStartByte="860" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PAFIR_Coef3" Value="0" BitCount="8" StartBit="0" StartByte="858" HighBitCount="1" HighStartBit="7" HighStartByte="860" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PAFIR_Shift" Value="0" BitCount="4" StartBit="0" StartByte="859" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PAFIR_Gain" Value="0" BitCount="4" StartBit="4" StartByte="859" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="PAComeBack" ModelType="MVI" Value="0" BitCount="1" StartBit="0" StartByte="860" IsCanFrozenSend="true" IsPresetParameter="true"/>
                <Parameter Name="PLComeBack" ModelType="MVI" Value="0" BitCount="1" StartBit="1" StartByte="860" IsCanFrozenSend="true" IsPresetParameter="true"/>
                <Parameter Name="DynFlowMode" Value="0" BitCount="1" StartBit="2" StartByte="860" IsCanFrozenSend="true" IsPresetParameter="false"/>
                <Parameter Name="MVIMode" ModelType="MVI" BitCount="1" StartBit="3" StartByte="860" ValueType="Bool" Value="false" TrueValue="1"  IsPresetParameter="false"/>
                <Parameter Name="DynFlowDynamicRange" Value="0" BitCount="4" StartBit="4" StartByte="861" IsCanFrozenSend="true" IsPresetParameter="true"/>
                <Parameter Name="MVIDynamicRange" ModelType="MVI" Value="0" BitCount="4" StartBit="4" StartByte="861" IsCanFrozenSend="true" IsPresetParameter="true"/>
                <Parameter Name="HDCPAWallFilter" ModelType="MVI" Value="0" BitCount="2" StartBit="6" StartByte="904" IsCanFrozenSend="true" IsPresetParameter="true"/>
                <Parameter Name="HDCPACET" Value="0" BitCount="7" StartBit="0" StartByte="906" IsCanFrozenSend="true" IsPresetParameter="true"/>
                <Parameter Name="HDCPAWFGainCtrl" Value="0" BitCount="4" StartBit="0" StartByte="907" IsCanFrozenSend="true" IsPresetParameter="true"/>
                <Parameter Name="MVIPostGain" ModelType="MVI" Value="0" BitCount="4" StartBit="4" StartByte="907" IsCanFrozenSend="true" IsPresetParameter="true"/>

                <Parameter Name="ReadProbeLicence" Value="0" BitCount="7" StartBit="1" StartByte="456" IsPresetParameter="false"/>

                <Parameter Name="BSteeringAngle2Coding" BitCount="6" StartBit="0" StartByte="788" IsPresetParameter="false"/>
                <Parameter Name="DynDBRateReciprocal" BitCount="8" StartBit="0" StartByte="916" HighBitCount="8" HighStartBit="0" HighStartByte="917" IsPresetParameter="false"/>
                <Parameter Name="TGC9" ModelType="B" BitCount="8" StartBit="0" StartByte="1010" IsPresetParameter="false"/>
                <Parameter Name="TGC10" ModelType="B" BitCount="8" StartBit="0" StartByte="1011" IsPresetParameter="false"/>
                <Parameter Name="TGC11" ModelType="B" BitCount="8" StartBit="0" StartByte="1012" IsPresetParameter="false"/>
                <Parameter Name="TGC12" ModelType="B" BitCount="8" StartBit="0" StartByte="1013" IsPresetParameter="false"/>
                <Parameter Name="PALM_Link_Mode" Value="5" BitCount="5" StartBit="2" StartByte="493" IsPresetParameter="false"/>
                <!--任意波形-->
                <Parameter Name="ArbitraryWave" ModelType="B" Value="false" ValueType="Bool" BitCount="1" StartBit="7" StartByte="776" IsPresetParameter="false"/>
                <!--补偿，探头相关参数-->
                <Parameter Name="RXGate" Value="0" BitCount="8" StartBit="0" StartByte="480" IsPresetParameter="false"/>
        </BFParameterGroup>
        <BFParameterGroup Type="Common">
                <!--增加图像处理链路的版本号，用于区分当前软件采用的图像处理方式-->
                <!--比如：在这之前血流数据推送给Zeus时，采用连续推送多帧，而Zeus只获取第一帧-->
                <!--现在改为：只推送最新的一帧血流数据给Zeus，目的是及时显示，修改来源是SR9的271ms延迟反馈-->
                <Parameter Name="USPipeLineVersion" Value="1" Min="1" Max="1024" Step="1" IsPresetParameter="false"/>
                <!-- TODO: 接收孔径系数，不需要用控制表参数，用数据块代替-->
                <Parameter Name="BRvApertureCoef" ModelType="B" Value="0" Min="0" Max="512" Step="1" IsPresetParameter="false"/>
                <Parameter Name="CRvApertureCoef" Value="0" Min="0" Max="512" Step="1" IsPresetParameter="false"/>
                <Parameter Name="DRvApertureCoef" Value="0" Min="0" Max="512" Step="1" IsPresetParameter="false"/>
                <Parameter Name="IImageType" ModelType="B" Value="0" Min="-1" Max="1" IsPresetParameter="false"/>
                <Parameter Name="PWScanLineDelta" Value="0" Min="-10" Max="10" Step="1" IsPresetParameter="true"/>
                <Parameter Name="DTx1_Triplex" Value="0" Min="0" Max="255" Step="1" IsPresetParameter="true"/>
                <Parameter Name="DTx2_Triplex" Value="0" Min="0" Max="255" Step="1" IsPresetParameter="true"/>
                <Parameter Name="DTx3_Triplex" Value="0" Min="0" Max="255" Step="1" IsPresetParameter="true"/>
                <Parameter Name="GateDepth1" Value="0" Min="0" Max="255" Step="1" IsPresetParameter="true"/>
                <Parameter Name="GateDepth2" Value="0" Min="0" Max="255" Step="1" IsPresetParameter="true"/>
                <Parameter Name="VAFunction" Value="-2" Min="-2" Max="1" IsPresetParameter="false"/>
                <Parameter Name="SonoAV" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="ADFreqMHz" Value="40" IsPresetParameter="false"/>
                <Parameter Name="ADOffsetTimeNs" Value="36000" IsPresetParameter="false"/>
                <Parameter Name="SonicSpeed" Value="1540000" IsPresetParameter="true"/>
                <Parameter Name="TssIncrement" IsPresetParameter="false"/>
                <Parameter Name="MTriggeredAP" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="CTriggeredAP" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="SystemScanMode" IsPresetParameter="false"/>
                <Parameter Name="ProbeId" IsPresetParameter="false"/>
                <Parameter Name="ScanWidth" Min="0" Max="9" Value="3" IsPresetParameter="false"/>
                <Parameter Name="FocusNumShow" IsPresetParameter="true"/>
                <Parameter Name="ImageZoomCoefOther" Min="100" Max="100" Step="1" Value="100" IsPresetParameter="false"/>
                <Parameter Name="ImageZoomCoefB" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="false"/>
                <!--ImageZoomCoefOther ImageZoomCoef ImageZoomCoefB 需要保证此顺序-->
                <Parameter Name="ImageZoomCoef" Min="60" Max="1000" Step="5" Value="100" IsPresetParameter="true"/>
                <Parameter Name="IsFullScreenZoomIn" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="FullScreenZoomInIndex" Min="0" Max="5" Step="1" Value="2" IsPresetParameter="false"/>
                <Parameter Name="IsSecondGearFullScreenZoomIn" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="IsMenuVisible" ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="DRDiffWithCompoundOn" Min="-5" Max="5" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="DRDiffWithSraOn" Min="-5" Max="5" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="DRDiffWithThiOn" Min="-5" Max="5" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="ScpdOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="FcpdOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="CompoundByFPGA" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="DynStartDepth" Min="0" Max="35" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="DynStartDB" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="DynEndDepth" Min="0" Max="35" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="DynEndDB" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="FreqSettingIds" ModelType="B" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="AFSettingIds" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="LeeSettingIds" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="CLFStrIds" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="CAFStrIds" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="TDILFStrIds" ModelType="TDI" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="TDIAFStrIds" ModelType="TDI" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="PDLFStrIds" ModelType="PD" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="SNLFStrIds" ModelType="SonoNeedle" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="MVILFStrIds" ModelType="MVI" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="PDAFStrIds" ModelType="PD" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="SNAFStrIds" ModelType="SonoNeedle" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="MVIAFStrIds" ModelType="MVI" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="iImageLib" ModelType="B" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="iImage" ModelType="B" Min="0" Max="3" Step="1" IsPresetParameter="true"/>
                <Parameter Name="iImageShow" ModelType="B" Min="0" Max="4" Step="1" IsPresetParameter="true"/>
                <Parameter Name="iImageModelNo" ModelType="B" ValueType="Int" Min="0" Max="5" Step="1" Value="0" IsPresetParameter="true"/>
                <Parameter Name="iImageStrength" ModelType="B" ValueType="Float" Value="0" IsPresetParameter="true"/>
                <Parameter Name="iImageSmooth" ModelType="B" ValueType="Float" Value="0.4" IsPresetParameter="true"/>
                <Parameter Name="iImageEdge" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="iImageEdge2" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="iImageEdge3" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="iImageEdge4" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="iImageContrastS1" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="iImageContrastK1" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="iImageContrastS2" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <!-- CVIE's parameters definition begin-->
                <Parameter Name="ParaFileNames" ModelType="B" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="ParaFileName" ModelType="B" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="SettingIndex" ModelType="B" Min="0" Max="7" ValueType="Int" IsPresetParameter="true"/>
                <Parameter Name="NoiseSuppression"  ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="EdgeCV" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>  // parameter name "Edge" is existed, so, renamed "EdgeCV"
                <Parameter Name="Detail" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="LimitEnabled" ModelType="B" ValueType="Bool" Value="false" IsPresetParameter="true"/>
                <Parameter Name="LimitOvershootreduction" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="LimitUndershootreduction" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="StrengthEnabled" ModelType="B" ValueType="Bool" Value="false" IsPresetParameter="true"/>
                <Parameter Name="StrengthFinestructures" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="StrengthLargestructures" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="TemporalEnabled" ModelType="B" ValueType="Bool" Value="false" IsPresetParameter="true"/>
                <Parameter Name="TemporalStrength" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="TemporalHomogeneity" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="LineiImageCVSettingIds" ModelType="B" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="LineiImageType" ModelType="B" ValueType="Bool" Value="false" IsPresetParameter="true"/>
                <!-- CVIE's parameters definition end-->
                <Parameter Name="iImageTemporalThreshold" ModelType="B" ValueType="Float" Value="0" IsPresetParameter="true"/>
                <Parameter Name="iImageTemporalWeight" ModelType="B" ValueType="Float" Value="0.4" IsPresetParameter="true"/>
                <Parameter Name="iImageMap" ModelType="B" ValueType="Float" Value="0.0" IsPresetParameter="true"/>
                <Parameter Name="LineiImageSettingIds" ModelType="B" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="SmoothSettingIds" ModelType="B" ValueType="ByteArray" IsPresetParameter="false"/>
                <Parameter Name="RvFNo" Min="0" Max="7" Step="1" Value="3" IsPresetParameter="false"/>
                <Parameter Name="RvPWFNo" Min="0" Max="7" Step="1" Value="3" IsPresetParameter="false"/>
                <Parameter Name="TxFNo" Min="0" Max="7" Step="1" Value="5" IsPresetParameter="false"/>
                <Parameter Name="CPDSteer" Min="0" Max="30" Step="1" Value="5" IsPresetParameter="false"/>
                <Parameter Name="TrapezoidalCPDSteer" Min="0" Max="20" Step="1" DefaultValue="10" IsPresetParameter="true"/>
                <Parameter Name="TrapezoidalCPDSteer2" Min="0" Max="20" Step="1" DefaultValue="10" IsPresetParameter="false"/>
                <Parameter Name="PersistDiffWithCpdOn" ModelType="B" Min="-7" Max="7" Step="1" Value="0" IsPresetParameter="true"/>
                <Parameter Name="PersistDiffWithSraOn" ModelType="B" Min="-7" Max="7" Step="1" Value="0" IsPresetParameter="true"/>
                <Parameter Name="PersistentDeltaTHI" ModelType="B" Min="-7" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PersistentDeltaCFM" Min="-7" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PersistentDeltaPD" ModelType="PD" Min="-7" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PersistentDeltaSN" ModelType="SonoNeedle" Min="-7" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PersistentDeltaTDI" ModelType="TDI" Min="-7" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PersistentDeltaMVI" ModelType="MVI" Min="-7" Max="7" IsPresetParameter="true"/>
                <Parameter Name="Flow" Value="1" Min="0" Max="3" DefaultValue="1" IsPresetParameter="true"/>
                <Parameter Name="FlowTDI" ModelType="TDI" Value="1" Min="0" Max="3" DefaultValue="1" IsPresetParameter="true"/>
                <Parameter Name="ZoomedCQYZ" Min="1" IsPresetParameter="false"/>
                <Parameter Name="ZoomMultiIndex" Min="0" Max="2" Value="0" IsPresetParameter="false"/>
                <Parameter Name="ZoomMidLine" IsPresetParameter="false"/>
                <Parameter Name="ZoomHalfLines" IsPresetParameter="false"/>
                <Parameter Name="ZoomMidDepthMM" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="ZoomHalfDepthMM" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Scroll" Min="0" Max="255" Value="0" IsPresetParameter="false"/>
                <Parameter Name="IsScroll" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="ActiveB" Min="0" Max="3" Value="0" IsPresetParameter="false"/>
                <Parameter Name="FPS" IsPresetParameter="false"/>
                <Parameter Name="FPSForAP" IsPresetParameter="false"/>
                <Parameter Name="Harmonic" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="PRFColorKHZ" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="PRFDopKHZ" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="BaseLineColor" Min="0" Max="6" Value="3" DefaultValue="3" IsPresetParameter="true"/>
                <Parameter Name="BaseLineDPD" ModelType="DPD" Min="0" Max="6" Value="3" DefaultValue="3" IsPresetParameter="true"/>
                <Parameter Name="BaseLineTDI" ModelType="TDI" Min="0" Max="6" Value="3" DefaultValue="3" IsPresetParameter="true"/>
                <Parameter Name="BaseLineMVI" ModelType="MVI" Min="0" Max="6" Value="3" DefaultValue="3" IsPresetParameter="true"/>
                <Parameter Name="DopplerTheta" Min="-70" Max="70" Step="1" Value="0" IsPresetParameter="true"/>
                <Parameter Name="DopplerThetaTDI" ModelType="TDI" Min="-70" Max="70" Step="1" Value="0" IsPresetParameter="true"/>
                <Parameter Name="DopplerThetaCWD" ModelType="CW" Min="-70" Max="70" Step="1" Value="0" IsPresetParameter="true"/>
                <Parameter Name="RoiMidLine" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfLines" IsPresetParameter="true"/>
                <Parameter Name="RoiMidDepthMM" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfDepthMM" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="RoiMidLineSN" ModelType="SonoNeedle" IsPresetParameter="false"/>
                <Parameter Name="RoiHalfLinesSN" ModelType="SonoNeedle" IsPresetParameter="false"/>
                <Parameter Name="RoiMidDepthMMSN" ModelType="SonoNeedle" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="RoiHalfDepthMMSN" ModelType="SonoNeedle" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="SteeringAngleSN" ModelType="SonoNeedle" IsPresetParameter="false"/>
                <Parameter Name="RoiMidLineTDI" ModelType="TDI" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfLinesTDI" ModelType="TDI" IsPresetParameter="true"/>
                <Parameter Name="RoiMidDepthMMTDI" ModelType="TDI" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfDepthMMTDI" ModelType="TDI" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="RoiMidLineFourD" DefaultValue="128" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfLinesFourD" DefaultValue="52" IsPresetParameter="true"/>
                <Parameter Name="RoiMidDepthMMFourD" DefaultValue="64" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfDepthMMFourD" DefaultValue="20" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="RoiMidLineMVI" ModelType="MVI" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfLinesMVI" ModelType="MVI" IsPresetParameter="true"/>
                <Parameter Name="RoiMidDepthMMMVI" ModelType="MVI" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfDepthMMMVI" ModelType="MVI" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="FourDKnotLine" IsPresetParameter="true"/>
                <Parameter Name="FourDKnotDepthMM" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="RoiMidLineElasto" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfLinesElasto" IsPresetParameter="true"/>
                <Parameter Name="RoiMidDepthMMElasto" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="RoiHalfDepthMMElasto" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="SampleVolumeMM" Min="1" Max="8" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="SampleVolumeTDIMM" ModelType="TDI" Min="1" Max="8" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="DopplerStartDepthMM" ValueType="Double" IsPresetParameter="true"/>
                <Parameter Name="DopplerTDIStartDepthMM" ModelType="TDI" ValueType="Double" IsPresetParameter="true"/>
                <Parameter Name="CMaxVelCMS" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="DMaxVelCMS" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="PRFColor" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="PRFDop" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="PRFCWD" ModelType="CW" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="PRFElasto" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="BGamma" Min="0" Max="8" IsPresetParameter="true"/>
                <Parameter Name="BRejection" Min="0" Max="256" IsPresetParameter="true"/>
                <Parameter Name="BTHIGamma" Min="0" Max="8" Value="4" DefaultValue="4" IsPresetParameter="true"/>
                <Parameter Name="BTHIRejection" Min="0" Max="256" IsPresetParameter="true"/>
                <Parameter Name="MGamma" Min="0" Max="8" IsPresetParameter="true"/>
                <Parameter Name="MRejection" Min="0" Max="256" IsPresetParameter="true"/>
                <Parameter Name="PwGamma" Min="0" Max="8" IsPresetParameter="true"/>
                <Parameter Name="PwRejection" Min="0" Max="256" IsPresetParameter="true"/>
                <Parameter Name="PwTDIGamma" ModelType="TDI" Min="0" Max="8" Value="4" DefaultValue="4" IsPresetParameter="true"/>
                <Parameter Name="PwTDIRejection" ModelType="TDI" Min="0" Max="256" IsPresetParameter="true"/>
                <Parameter Name="CwdGamma" ModelType="CW" Min="0" Max="8" Value="4" DefaultValue="4" IsPresetParameter="true"/>
                <Parameter Name="CwdRejection" ModelType="CW" Min="0" Max="256" IsPresetParameter="true"/>
                <Parameter Name="BGammaPos" Value="16" Min="0" Max="255" DefaultValue="16" IsPresetParameter="true"/>
                <Parameter Name="BGammaStart" Value="60" Min="1" Max="100" DefaultValue="60" IsPresetParameter="true"/>
                <Parameter Name="BGammaStep" Value="10" Min="1" Max="100" DefaultValue="10" IsPresetParameter="true"/>
                <Parameter Name="THIState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="ColorInvertState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="DPDInvertState" ModelType="DPD" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="TDIInvertState" ModelType="TDI" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="ElastoInvertState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="MVIInvertState" ModelType="MVI" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="BColorMapIndex" ModelType="B" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="BTDIColorMapIndex" ModelType="TDI" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="CfColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PdColorMapIndex" ModelType="PD" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="DpdColorMapIndex" ModelType="DPD" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="TDIColorMapIndex" ModelType="TDI" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PwColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PwTDIColorMapIndex" ModelType="TDI" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="MColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="CwdColorMapIndex" ModelType="CW" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="ElastoColorMapIndex" Min="0" Max="9" IsPresetParameter="true"/>
                <Parameter Name="CPColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="MVIColorMapIndex" ModelType="MVI" Min="0" Max="32" IsPresetParameter="true"/>
                <Parameter Name="MVIType1ColorMapIndex" ModelType="MVI" Min="0" Max="1" Value="0" IsPresetParameter="true"/>
                <Parameter Name="MVIType2ColorMapIndex" ModelType="MVI" Min="0" Max="29" IsPresetParameter="true"/>
                <Parameter Name="MVIType3ColorMapIndex" ModelType="MVI" Min="0" Max="32" IsPresetParameter="true"/>
                <Parameter Name="BGrayCurveIndex" ModelType="B" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="BTHIGrayCurveIndex" ModelType="B" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="MGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PwGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PwTDIGrayCurveIndex" ModelType="TDI" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="CwdGrayCurveIndex" ModelType="CW" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="EGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="VarColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="FourDColorMapIndex" Min="0" Max="6" IsPresetParameter="true"/>
                <!--控制 BColorMapIndex BTDIColorMapIndex-->
                <Parameter Name="BColorMapIndexShow" Min="0" Max="7" IsPresetParameter="false"/>
                <!--控制 CfColorMapIndex VarColorMapIndex-->
                <Parameter Name="CfColorMapIndexShow" Min="0" Max="7" IsPresetParameter="false"/>
                <Parameter Name="BGrayCurveIndexShow" Min="0" Max="7" IsPresetParameter="false"/>
                <Parameter Name="BGammaShow" Min="0" Max="8" IsPresetParameter="false"/>
                <Parameter Name="BRejectionShow" Min="0" Max="256" IsPresetParameter="false"/>
                <!--控制Color时 的彩色模式 CF/VAR-->
                <Parameter Name="CFMode" Min="0" Max="1" IsPresetParameter="true"/>
                <Parameter Name="CfContrast" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="PdContrast" ModelType="PD" Min="0" Max="7" IsPresetParameter="true"/>
                <Parameter Name="DpdContrast" ModelType="DPD" Min="0" Max="7" IsPresetParameter="true"/>
                <!--描述从FPGA出来的原始图像大小，如果是线数据版本宽并没有实际意义，高度等于PointNumPerLine（主要用于控制字参数计算）-->
                <Parameter Name="ImageSize" ValueType="Size" IsPresetParameter="false"/>
                <!--描述图像的显示区域大小-->
                <Parameter Name="RenderImageSize" ValueType="Size" IsPresetParameter="false"/>
                <Parameter Name="LayoutBImageSize" ValueType="Size" IsPresetParameter="false"/>
                <Parameter Name="BImageSize" ValueType="Size" IsPresetParameter="false"/>
                <Parameter Name="MImageSize" ValueType="Size" IsPresetParameter="false"/>
                <Parameter Name="DImageSize" ValueType="Size" IsPresetParameter="false"/>
                <Parameter Name="DataBitCount" Value="8" IsPresetParameter="false"/>
                <Parameter Name="PointNumPerLine" Value="512" IsPresetParameter="false"/>
                <Parameter Name="ColorPointNumAfterReduce" Value="256" IsPresetParameter="false"/>
                <Parameter Name="RedundantPointNumPerLine" Value="8" IsPresetParameter="false"/>
                <Parameter Name="StartDepthMM" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="DepthMM" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="PixelSizeMM" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="MPixelSizeSec" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="MPixelSizeMM" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="DPixelSizeCMS" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="DPixelSizeSec" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="DBaseLineYPos" ValueType="Int" IsPresetParameter="false"/>
                <Parameter Name="DBaseLineWidth" ValueType="Int" IsPresetParameter="false"/>
                <Parameter Name="ZoomDepthMM" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="ScrollDepthMM" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="HotLogoDepthMM" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="HotLogoLine" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="AIO" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="IsBiopsyVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="ZoomSelect" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="BiopsyAngle" ValueType="Int" IsPresetParameter="false"/>
                <Parameter Name="BiopsyXPosMM" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="BiopsyYPosMM" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="BiopsyAngleOffset" IsPresetParameter="false"/>
                <Parameter Name="BiopsyXPosMMOffset" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="BiopsyYPosMMOffset" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="IsCenterLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="TDIBMenuShow" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="IsDopplerScanLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="IsCWDScanLineVisible" ModelType="CW" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="IsMLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="IsRoiVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="IsUDBM" ValueType="Bool" Value="false" TrueValue="1" DefaultValue="false" IsPresetParameter="true"/>
                <Parameter Name="GrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
                <Parameter Name="MGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
                <Parameter Name="PwGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
                <Parameter Name="CwdGrayMap" ModelType="CW" ValueType="ByteArray" IsPresetParameter="false"/>
                <Parameter Name="TGC" ValueType="ByteArray" IsPresetParameter="false"/>
                <Parameter Name="MI" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="TIS" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="TIB" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="ExamModeId" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ExamModeType" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="ExamModeCaption" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ProbeConnected" ValueType="Bool" Value="false"/>
                <!--ImageRects is List<QRect>-->
                <Parameter Name="ImageRects" ValueType="List" IsPresetParameter="false"/>
                <!--ImageRegions is List<QRect> for meas region-->
                <Parameter Name="ImageRegions" ValueType="List" IsPresetParameter="false"/>
                <!--SamplePoints is ByteArray-->
                <Parameter Name="BSamplePoints" ModelType="B" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="BTHISamplePoints" ModelType="B" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="MSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="PwSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="PwTDISamplePoints" ModelType="TDI" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="CwdSamplePoints" ModelType="CW" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="ESamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="BReject"/>
                <Parameter Name="SraGainColorDelta" Min="-64" Max="64" IsPresetParameter="false"/>
                <Parameter Name="TxOff" ValueType="Bool" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="Layout" Value="1" Min="1" Max="4" IsPresetParameter="false"/>
                <Parameter Name="Rect" ValueType="Rect" IsPresetParameter="false"/>
                <Parameter Name="WeightedCurve" Min="0" Max="3" IsPresetParameter="false"/>
                <Parameter Name="FCA_Alpha" Min="0" Max="9" IsPresetParameter="false"/>
                <Parameter Name="CVIESettingIndex" Min="0" Max="3" IsPresetParameter="true"/>
                <Parameter Name="CVIESettingName" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="ColorJudgeThreshold" Value="50" IsPresetParameter="false"/>
                <Parameter Name="ImageOptimizationAlg" Value="0" Min="0" Max="2" IsPresetParameter="false"/>
                <Parameter Name="MRD_Alpha" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="MRD_Beta" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="MRD_Gamma" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Threshold1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Threshold2" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Threshold3" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Edge1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Edge2" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Edge3" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Smooth1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Smooth2" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Smooth3" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Iteration1" IsPresetParameter="false"/>
                <Parameter Name="Iteration2" IsPresetParameter="false"/>
                <Parameter Name="Iteration3" IsPresetParameter="false"/>
                <Parameter Name="Contrast" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="OptimizationLevel" IsPresetParameter="false"/>
                <Parameter Name="EdgeThi" ModelType="B" Max="6" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="EdgeThiInc" ModelType="B" Max="6" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="FreqIndexBShow" ModelType="B" Min="0" Max="2" IsDirectValue="true" IsPresetParameter="false"/>
                <Parameter Name="ColorLineChanging" Min="0" Max="1" Value="0" ValueType="Int" IsDirectValue="true" IsPresetParameter="false"/>
                <Parameter Name="FreqIndexColor" Min="0" Max="0" IsDirectValue="true" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexPD" ModelType="PD" Min="0" Max="0" IsDirectValue="true" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexSN" ModelType="SonoNeedle" Min="0" Max="0" IsDirectValue="true" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexTDI" ModelType="TDI" Min="0" Max="0" IsDirectValue="true" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexDop" Min="0" Max="0" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexTD" Min="0" Max="0" IsDirectValue="true" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexCWD" ModelType="CW" Min="0" Max="0" IsDirectValue="true" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexElasto" Min="0" Max="0" IsDirectValue="true" IsPresetParameter="true"/>
                <Parameter Name="FreqIndexMVI" ModelType="MVI" Min="0" Max="0" IsDirectValue="true" IsPresetParameter="true"/>
                <Parameter Name="AcousticPowerBShow" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ECGDlyShow" ModelType="ECG" Min="0" Max="255" IsPresetParameter="false"/>
                <!--用于显示在图像区右侧参数区，以及内部增益值计算-->
                <Parameter Name="GainShow" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="EdgeShow" Min="0" Max="6" IsPresetParameter="false"/>
                <Parameter Name="BWGainDelta" Min="-255" Max="255" IsPresetParameter="true"/>
                <Parameter Name="AdapPostProcDelta" Min="-15" Max="15" IsPresetParameter="true"/>
                <Parameter Name="ECGPosShow" ModelType="ECG" Min="0" Max="7" IsPresetParameter="false"/>
                <Parameter Name="ECGEn" ModelType="ECG" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="ECGVelocity" ModelType="ECG" ValueType="Int" Min="0" Max="4" IsPresetParameter="true"/>
                <Parameter Name="ECGGain" ModelType="ECG" ValueType="Int" Min="1" Max="5" DefaultValue="5" IsPresetParameter="true"/>
                <Parameter Name="ECGPos" ModelType="ECG" ValueType="Int" Min="0" Max="6" DefaultValue="6" IsPresetParameter="true"/>
                <Parameter Name="ECGInvert" ModelType="ECG" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
                <!--SampleRateDopShow 控制 SampleRateDop SampleRateDopTM-->
                <Parameter Name="SampleRateDopShow" Min="0" Max="15" IsPresetParameter="false"/>
                <Parameter Name="DSampleRateDopShow" Min="0" Max="15" IsPresetParameter="false"/>
                <Parameter Name="PixelRatioShow" Min="0" Max="7" IsPresetParameter="false"/>
                <!--PWEnhanceShow 控制 PWEnhance PWEnhanceTM-->
                <Parameter Name="PWEnhanceShow" Min="0" Max="3" IsPresetParameter="false"/>
                <!--PWDynamicRangeShow 控制 PWDynamicRange PWDynamicRangeTM-->
                <Parameter Name="PWDynamicRangeShow" Min="0" Max="7" IsPresetParameter="false"/>
                <!--DummyEnSampleShow(Color的参数) 控制 DummyEnSample DummyEnSampleTM-->
                <Parameter Name="DummyEnSampleShow" Min="0" Max="3" IsPresetParameter="false"/>
                <Parameter Name="CFMVelLevelShow" Min="0" Max="15" IsPresetParameter="false"/>
                <!--只用于显示在图像区右侧参数区，不做控制-->
                <Parameter Name="GainDopShow" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ColorCoef" Min="0" Max="6" IsPresetParameter="true"/>
                <Parameter Name="PDCoef" ModelType="PD" Min="0" Max="6" IsPresetParameter="true"/>
                <Parameter Name="SNCoef" ModelType="SonoNeedle" Min="0" Max="6" IsPresetParameter="true"/>
                <Parameter Name="TDICoef" ModelType="TDI" Min="0" Max="6" IsPresetParameter="true"/>
                <Parameter Name="MVICoef" ModelType="MVI" Min="0" Max="6" IsPresetParameter="true"/>
                <Parameter Name="CVRTDelta" Min="-15" Max="15" IsPresetParameter="true"/>
                <Parameter Name="CVLTDelta" Min="-127" Max="127" IsPresetParameter="true"/>
                <Parameter Name="CETDelta" Min="-15" Max="15" IsPresetParameter="true"/>
                <!--AudioFilterCoefSelShow 控制 AudioFilterCoefSel AudioFilterCoefSelTM-->
                <Parameter Name="AudioFilterCoefSelShow" Min="0" Max="7" IsPresetParameter="false"/>
                <!--IDs 分两种情况，Ids[0]不开THI的 Ids[1]开THI的-->
                <Parameter Name="TransmitStrIds" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="ColorFreqStrIds" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="PDFreqStrIds" ModelType="PD" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="SNFreqStrIds" ModelType="SonoNeedle" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="TDIFreqStrIds" ModelType="TDI" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="DopFreqStrIds" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="TDFreqStrIds" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="CWDFreqStrIds" ModelType="CW" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="ElastoFreqStrIds" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="MVIFreqStrIds" ModelType="MVI" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="ColorTransmitStrIds" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="DynDeltaOnXContrast" Min="-7" Max="7" IsPresetParameter="false"/>
                <Parameter Name="BFreqDelta" Min="-2" Max="2" Step="0.1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="TransmitStepDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="TransmitBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="ReceiveFocusDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="ReceiveBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="ReceiveUnitVariFocusNum" Value="1" Min="1" Max="64" IsPresetParameter="false"/>
                <Parameter Name="WallFilterSetting" Value="0,1,2,3" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="CPAWallFilterSetting" ModelType="PD" Value="0,1,2,3" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="CfmFnum" Min="0" Max="7" IsPresetParameter="false"/>
                <Parameter Name="ElastoFnum" Value="0" Min="0" Max="7" IsPresetParameter="false"/>
                <Parameter Name="CfmRxFnum" Min="0" Max="7" IsPresetParameter="false"/>
                <Parameter Name="ElastoRxFnum" Value="0" Min="0" Max="7" IsPresetParameter="false"/>
                <Parameter Name="MaxSteeringAngle" Value="-1" Min="-1" Max="3" IsPresetParameter="false"/>
                <!--TODO:Float value min max step support-->
                <Parameter Name="ColorFreqDelta" Min="-2" Max="2" Step="0.1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="CFMRoi_Delta" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="CfmTransmitStepDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="CfmTransmitBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="CfmReceiveFocusDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="CfmReceiveBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="CfmReceiveUnitVariFocusNum" Value="1" Min="1" Max="64" IsPresetParameter="false"/>
                <Parameter Name="CWScanLineDelta" Min="-10" Max="10" IsPresetParameter="true"/>
                <Parameter Name="CWMidDepthMMDelta" Min="-100" Max="100" Step="10" IsPresetParameter="true"/>
                <Parameter Name="CTxPulseDuty" Min="0" Max="31" IsPresetParameter="false"/>
                <Parameter Name="CTxPulseDuty_PW" Min="0" Max="31" IsPresetParameter="false"/>
                <Parameter Name="CfmMaxTransmitApertureControl" Value="63" Min="0" Max="63" IsPresetParameter="false"/>
                <Parameter Name="CfmMaxTransmitApertureControl_PW" Value="63" Min="0" Max="63" IsPresetParameter="false"/>
                <Parameter Name="CfmMaxTransmitApertureControlElasto" Value="63" Min="0" Max="63" IsPresetParameter="false"/>
                <Parameter Name="CfmMinTransmitApertureControl" Min="0" Max="63" IsPresetParameter="false"/>
                <Parameter Name="CfmMinTransmitApertureControl_PW" Min="0" Max="63" IsPresetParameter="false"/>
                <Parameter Name="CfmMinTransmitApertureControlElasto" Value="0" Min="0" Max="63" IsPresetParameter="false"/>
                <Parameter Name="TgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta8" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta9" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta10" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta11" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta12" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta13" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta14" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DTgcDelta15" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta8" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta9" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta10" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta11" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta12" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta13" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta14" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="TriplexDTgcDelta15" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta8" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta9" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta10" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta11" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta12" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta13" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta14" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiTriplexDTgcDelta15" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta8" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta9" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta10" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta11" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta12" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta13" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta14" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="ThiDTgcDelta15" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="DopFreqDelta" Min="-2" Max="2" Step="0.1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="DopFNum" Min="0" Max="7" IsPresetParameter="false"/>
                <Parameter Name="DopTransmitStepDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="DopTransmitBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="DopReceiveFocusDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="DopReceiveBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="DopReceiveUnitVariFocusNum" Value="1" Min="1" Max="64" IsPresetParameter="false"/>
                <Parameter Name="QFlowMode" ValueType="Bool" Value="false" IsPresetParameter="true"/>
                <Parameter Name="QFlowOn" ValueType="Bool" Value="true" DefaultValue="true" IsPresetParameter="true"/>
                <Parameter Name="QBeamOn" ValueType="Bool" Value="true" DefaultValue="true" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="XContrastValue" Value="1" Min="0" Max="2" DefaultValue="1" IsPresetParameter="true"/>
                <Parameter Name="BCImagesOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="AdjustmentOfB" Value="479" Min="0" Max="4095" DefaultValue="479" IsPresetParameter="true"/>
                <Parameter Name="AdjustmentOfC" Value="479" Min="0" Max="65535" DefaultValue="479" IsPresetParameter="true"/>
                <Parameter Name="AdjustmentOfD" Value="479" Min="0" Max="4095" DefaultValue="479" IsPresetParameter="true"/>
                <Parameter Name="RvTxB" Min="0" Max="65536" IsPresetParameter="false"/>
                <Parameter Name="RvTxC" Min="0" Max="65536" IsPresetParameter="false"/>
                <Parameter Name="RvTxD" Min="0" Max="65536" IsPresetParameter="false"/>
                <Parameter Name="PrtDeltaOnLowDensity" Min="-32768" Max="32768" IsPresetParameter="true"/>
                <Parameter Name="ImagePixelBits" Value="7" Min="1" Max="8" DefaultValue="7" IsPresetParameter="false"/>
                <Parameter Name="TriplexFrequencyDeltaTr1" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
                <Parameter Name="TriplexFilterCoefDeltaTr1" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
                <Parameter Name="TriplexFrequencyDeltaTr2" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
                <Parameter Name="TriplexFilterCoefDeltaTr2" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
                <Parameter Name="TriplexFrequencyDeltaTr3" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
                <Parameter Name="TriplexFilterCoefDeltaTr3" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
                <Parameter Name="TriplexFrequencyDeltaTr4" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
                <Parameter Name="TriplexFilterCoefDeltaTr4" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
                <Parameter Name="ROISteerAngles" Value="5,10,20" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="TICEn" Value="false" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="TIC" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="Threshold" Value="1" Min="1" Max="25" DefaultValue="1" IsPresetParameter="true"/>
                <Parameter Name="DTraceSmooth" Value="2" Min="0" Max="3" DefaultValue="2" IsPresetParameter="true"/>
                <Parameter Name="ImageNum" Value="0" Min="0" IsPresetParameter="false"/>
                <Parameter Name="SopInstanceId" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="SexInfo" ValueType="String" Value="" IsPresetParameter="false"/>
                <Parameter Name="ImageZoomRatio" ValueType="Float" Value="1" IsPresetParameter="false"/>
                <Parameter Name="ImageLeftTopPointX" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="ImageLeftTopPointY" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainThreshold1" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainThreshold2" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainThreshold3" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainThreshold4" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainThreshold5" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainThreshold6" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoFrameAverageN" Value="1" Min="1" Max="200" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainThresholdDown" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainThresholdUp" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoThresholdX" Value="0" Min="0" Max="2000" IsPresetParameter="true"/>
                <Parameter Name="ElastoThresholdY" Value="0" Min="0" Max="2000" IsPresetParameter="true"/>
                <Parameter Name="ElastoTransparency" Step="5" Value="95" Min="0" Max="255" DefaultValue="95" IsPresetParameter="true"/>
                <Parameter Name="ElastoDepthDelta" Value="0" Min="0" Max="100" IsPresetParameter="true"/>
                <Parameter Name="ElastoStrainMax" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoQualityLevel" Value="0" Min="0" Max="25" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="ElastoRebootTime" Value="2" Min="0" Max="5" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainList" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrain" Min="0" Max="2000" IsPresetParameter="false"/>
                <Parameter Name="ElastoStrainIndex" Min="-1" Max="255" IsPresetParameter="false"/>
                <Parameter Name="IsElastoUnderThreshold1" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="FreeMLineNum" Value="1" DefaultValue="1" Min="1" Max="3" IsPresetParameter="false"/>
                <Parameter Name="FreeMLineNo" Value="1" DefaultValue="1" Min="1" Max="3" IsPresetParameter="false"/>
                <Parameter Name="FreeMStartLine1" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
                <Parameter Name="FreeMStartDepthMM1" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
                <Parameter Name="FreeMAngle1" ValueType="Float" Value="135" DefaultValue="135" Min="0" Max="360" IsPresetParameter="false"/>
                <Parameter Name="FreeMStartLine2" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
                <Parameter Name="FreeMStartDepthMM2" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
                <Parameter Name="FreeMAngle2" ValueType="Float" Value="135" DefaultValue="135" Min="0" Max="360" IsPresetParameter="false"/>
                <Parameter Name="FreeMStartLine3" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
                <Parameter Name="FreeMStartDepthMM3" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
                <Parameter Name="FreeMAngle3" ValueType="Float" Value="135" DefaultValue="135" Min="0" Max="360" IsPresetParameter="false"/>
                <Parameter Name="FreeMBlock" ValueType="ByteArray" IsPresetParameter="false"/>
                <Parameter Name="FocusCombineStart" Min="0" Max="100" IsPresetParameter="false"/>
                <Parameter Name="FocusCombineLen" Min="0" Max="100" IsPresetParameter="false"/>
                <Parameter Name="iImageEffect" Min="0" Max="100" IsPresetParameter="false"/>
                <Parameter Name="GainTDI_Delta" ModelType="TDI" Min="0" Max="255" IsPresetParameter="true"/>
                <Parameter Name="GainColorTM_Delta" Min="0" Max="255" IsPresetParameter="true"/>
                <Parameter Name="TGCMax" Min="0" Max="255" DefaultValue="255" IsPresetParameter="true"/>
                <Parameter Name="TGCMin" Min="0" Max="255" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="Lmp" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="LmpGa" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="LmpEdd" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="CurvedPanoramicEnable" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="CurvedPanoramicLength" Value="0" Min="0" Max="65536" IsPresetParameter="false"/>
                <Parameter Name="CurvedPanoramicImageSize" ValueType="Size" IsPresetParameter="false"/>
                <Parameter Name="CurvedPanoramicPointList" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="CurvedPanoramicROIVisible" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="LinearAreaRect" ValueType="Rect" IsPresetParameter="false"/>
                <Parameter Name="AutoBrightNess" Value="false" ValueType="Bool" IsPresetParameter="true"/>
                <Parameter Name="Up_0" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="Up_1" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="Up_2" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="Up_3" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="Left_0" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="Left_1" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="Left_2" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="Left_3" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
                <Parameter Name="ImageZoomCoef_0" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="false"/>
                <Parameter Name="ImageZoomCoef_1" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="false"/>
                <Parameter Name="ImageZoomCoef_2" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="false"/>
                <Parameter Name="ImageZoomCoef_3" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="false"/>
                <Parameter Name="StartDepthMM_0" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="StartDepthMM_1" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="StartDepthMM_2" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="StartDepthMM_3" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="PixelSizeMM_0" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="PixelSizeMM_1" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="PixelSizeMM_2" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="PixelSizeMM_3" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="Rotation_0" Value="0" Max="270" Step="90" IsPresetParameter="false"/>
                <Parameter Name="Rotation_1" Value="0" Max="270" Step="90" IsPresetParameter="false"/>
                <Parameter Name="Rotation_2" Value="0" Max="270" Step="90" IsPresetParameter="false"/>
                <Parameter Name="Rotation_3" Value="0" Max="270" Step="90" IsPresetParameter="false"/>
                <Parameter Name="BSteeringScan_0" Value="20" Max="40" IsPresetParameter="false"/>
                <Parameter Name="BSteeringScan_1" Value="20" Max="40" IsPresetParameter="false"/>
                <Parameter Name="BSteeringScan_2" Value="20" Max="40" IsPresetParameter="false"/>
                <Parameter Name="BSteeringScan_3" Value="20" Max="40" IsPresetParameter="false"/>
                <Parameter Name="StartLine_0" IsPresetParameter="false"/>
                <Parameter Name="StartLine_1" IsPresetParameter="false"/>
                <Parameter Name="StartLine_2" IsPresetParameter="false"/>
                <Parameter Name="StartLine_3" IsPresetParameter="false"/>
                <Parameter Name="StressEchoEn" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="StressEchoLevel" ValueType="String" Value="" IsPresetParameter="false"/>
                <Parameter Name="StressEchoProjection" ValueType="String" Value="" IsPresetParameter="false"/>
                <Parameter Name="StressEchoTimer1En" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="StressEchoTimer2En" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="StressEchoTimer1" ValueType="String" Value="00:00:00" IsPresetParameter="false"/>
                <Parameter Name="StressEchoTimer2" ValueType="String" Value="00:00:00" IsPresetParameter="false"/>
                <Parameter Name="FourDThreshold" Min="0" Max="150" DefaultValue="33" IsPresetParameter="true"/>
                <Parameter Name="FourDRender" Min="0" Max="4" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="FourDSmooth" Min="0" Max="3" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="FourDFrameRate" Min="0" Max="1" IsPresetParameter="true"/>
                <Parameter Name="FourDPalette" Min="0" Max="7" DefaultValue="1" IsPresetParameter="true"/>
                <Parameter Name="FourDLight" Min="0" Max="11" DefaultValue="1" IsPresetParameter="true"/>
                <Parameter Name="FourDVirtualHDOn" ValueType="Bool" Value="false" DefaultValue="false" IsPresetParameter="true"/>
                <Parameter Name="FourDDirectionSet" Min="0" Max="270" Step="90" IsPresetParameter="true"/>
                <Parameter Name="FourDQualityIndex" Min="0" Max="2" IsPresetParameter="false"/>
                <Parameter Name="FourDQualityValue" ValueType="Float" Min="0" Max="1" IsPresetParameter="false"/>
                <Parameter Name="FourDReadLightAngleIndex" Min="0" Max="11" IsPresetParameter="false"/>
                <Parameter Name="FourDAngleRatioIndex" Min="0" Max="0" IsPresetParameter="false"/>
                <Parameter Name="FourDAngleRatio" Min="0" Max="100" IsPresetParameter="false"/>
                <Parameter Name="FourDDataiImageParasIndex" Min="0" Max="0" IsPresetParameter="false"/>
                <Parameter Name="FourDParasSettingIndex" Min="0" Max="8" IsPresetParameter="false"/>
                <Parameter Name="FourDRenderModeIndex" Min="0" Max="2" IsPresetParameter="false"/>
                <Parameter Name="FourDSliceNum" Min="1" Max="999" DefaultValue="60" IsPresetParameter="false"/>
                <Parameter Name="FourDMotorAngle" Min="20" Max="75" Step="1" DefaultValue="65" IsPresetParameter="true"/>
                <Parameter Name="FourDRoiRatio" ValueType="Float" Min="0" Max="100" DefaultValue="0" IsPresetParameter="false"/>
                <Parameter Name="FourDRoiZoomValue" ValueType="Float" Min="0.1" Max="10" DefaultValue="0.4" IsPresetParameter="false"/>
                <Parameter Name="FourDRectMinRatio" Min="0.3" Max="1" DefaultValue="0.3" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="FourDRectRatio" Min="0" Max="1" DefaultValue="1" ValueType="Float" IsPresetParameter="true"/>
                <Parameter Name="FourDRectMaxHeight" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="FourDMaxVolumeCount" Value="64" Min="1" Max="256" DefaultValue="64" IsPresetParameter="false"/>
                <Parameter Name="FourDCinProcessing" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="TransformationAxis" Value="0" Min="0" Max="2" IsPresetParameter="false"/>
                <Parameter Name="FourDStartHeight" Value="0" Min="0" Max="511" IsPresetParameter="false"/>
                <Parameter Name="FourDKnotPosChanged" ValueType="Bool" Value="false" DefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="ShowFourDWidget" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="FourDGroupParas" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="FourDGrayCurveIndex" Min="0" Max="19" DefaultValue="14" IsPresetParameter="true"/>
                <Parameter Name="FourDGamma" Min="0" Max="8" DefaultValue="2" IsPresetParameter="true"/>
                <Parameter Name="FourDSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
                <Parameter Name="QuadplexMode" Value="false" ValueType="Bool" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="QuadplexRefreshEn" Value="false" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="ContrastLevelStr" ValueType="Double" Min="0" Max="0" Step="1" IsPresetParameter="false"/>
                <Parameter Name="AxialSizeStr" ValueType="Int" Min="0" Max="0" Step="1" IsPresetParameter="false"/>
                <Parameter Name="ContrastLevel" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="AxialSize" IsPresetParameter="false"/>
                <Parameter Name="LateralSize" IsPresetParameter="false"/>
                <Parameter Name="LowFilterAxialSigma" ValueType="Float" Min="0" Max="20" IsPresetParameter="false"/>
                <Parameter Name="LowFilterLateralSigma" ValueType="Float" Min="0" Max="20" IsPresetParameter="false"/>
                <Parameter Name="ECGDisplayFps" ModelType="ECG" ValueType="Float" Min="0" Max="20" IsPresetParameter="false"/>
                <Parameter Name="ECGInvertHorizontal" ModelType="ECG" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="ECGInvertVertical" ModelType="ECG" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="ECGStartPos" ModelType="ECG" Min="0" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="ECGHRValue" ModelType="ECG" Min="0" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="ZoomPercent" ValueType="Double" IsPresetParameter="false"/>
                <Parameter Name="BRXLnum" Min="0" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="CRXLnum" Min="0" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="AnyDensityEn" ValueType="Bool" TrueValue="0" value="false" IsPresetParameter="false"/>
                <Parameter Name="ColorAnyDensityEn" ValueType="Bool" TrueValue="0" value="false" IsPresetParameter="false"/>
                <Parameter Name="CSteeringAngle" ValueType="Float" Min="0" Max="360" value="0" IsPresetParameter="false"/>
                <Parameter Name="LineSpacingMM" ValueType="Float" value="0" IsPresetParameter="false"/>
                <Parameter Name="AngleSpacingRad" ValueType="Float" value="0" IsPresetParameter="false"/>
                <Parameter Name="AngleSpacingRadForC" ValueType="Float" value="0" IsPresetParameter="false"/>
                <Parameter Name="DataSaveEn" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="CFMSlopC" Min="0" Max="255" Value="0" IsPresetParameter="false"/>
                <Parameter Name="CFMSlopPD" ModelType="PD" Min="0" Max="255" Value="0" IsPresetParameter="false"/>
                <Parameter Name="DynamicRangeForPD" ModelType="PD" Min="0" Max="255" Value="60" IsPresetParameter="false"/>
                <Parameter Name="PaVertDistEnable" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="BloodEnableFlag" ValueType="Bool" Value="true" IsPresetParameter="false"/>
                <Parameter Name="TrapezoidalAngle" Min="0" Max="20" Step="1" Value="5" IsPresetParameter="false"/>
                <Parameter Name="dopplerDSCMethod" Min="0" Max="1" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="FPGAPlatformType" Min="0" Max="10" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="BSStartPoint" Min="0" Max="255" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="BSSlope" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="false"/>
                <Parameter Name="SpecFresh" ValueType="Bool" Value="true" IsPresetParameter="false"/>
                <Parameter Name="SupportCQYZExt" ValueType="Bool" TrueValue="1" value="false" IsPresetParameter="false"/>
                <Parameter Name="CQYZLevel" ModelType="B" ValueType="Int" Min="0" Max="255" Step="1" Value="0" IsPresetParameter="true"/>
                <Parameter Name="ImageModes" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="ImageLayoutNum" Min="0" IsPresetParameter="false"/>
                <Parameter Name="ImageModeUpFilter" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ImageModeLeftFilter" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ImageModeRotationFilter" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ImageModeScaleFilter" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ImageRenderRects" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="ImageModeRects" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="FourDWinLayout" Min="0" Max="2" DefaultValue="1" IsPresetParameter="false"/>
                <Parameter Name="FourDVisualDirection" Min="0" Max="5" DefaultValue="0" IsPresetParameter="false"/>
                <Parameter Name="FourDLeftPara2DBck" Min="0" Max="100" Step="1" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="FourDLeftPara2DBias" Min="0" Max="100" Step="1" DefaultValue="50" IsPresetParameter="true"/>
                <Parameter Name="FourDLeftPara2DPos" Min="0" Max="100" Step="1" DefaultValue="50" IsPresetParameter="true"/>
                <Parameter Name="FourDLeftPara3DBck" Min="0" Max="100" Step="1" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="FourDLeftPara3DBias" Min="0" Max="100" Step="1" DefaultValue="50" IsPresetParameter="true"/>
                <Parameter Name="FourDLeftPara3DPos" Min="0" Max="100" Step="1" DefaultValue="50" IsPresetParameter="true"/>
                <Parameter Name="FreeHand3DMode" ValueType="Bool" Value="false" DefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="FreeHand3DRoiOn" ValueType="Bool" Value="false" DefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="FreeHand3DVolmnSpacing" Min="10" Max="100" Step="2" DefaultValue="32" IsPresetParameter="true"/>
                <Parameter Name="FreeHand3DRotateAxis" Value="0" Min="0" Max="2" IsPresetParameter="false"/>
                <Parameter Name="FreeHand3DRoiRect" ValueType="Rect" IsPresetParameter="false"/>
                <Parameter Name="LGCEn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="LGCControlEn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="LGC0" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="true"/>
                <Parameter Name="LGC1" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="true"/>
                <Parameter Name="LGC2" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="true"/>
                <Parameter Name="LGC3" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="true"/>
                <Parameter Name="LGC4" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="true"/>
                <Parameter Name="LGC5" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="true"/>
                <Parameter Name="LGC6" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="true"/>
                <Parameter Name="LGC7" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="true"/>
                <Parameter Name="LGCMAX" Value="80" DefaultValue="80" Min="-150" Max="150" IsPresetParameter="true"/>
                <Parameter Name="LGCMIN" Value="-80" DefaultValue="-80" Min="-150" Max="150" IsPresetParameter="true"/>
                <Parameter Name="FreeMLines" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="PostBGain" Min="0" Max="255" Step="5" IsPresetParameter="false"/>
                <Parameter Name="PostPWGain" Min="0" Max="255" Step="5" IsPresetParameter="false"/>
                <Parameter Name="PostCWGain" Min="0" Max="255" Step="5" IsPresetParameter="false"/>
                <Parameter Name="PostMGain" Min="0" Max="255" Step="5" IsPresetParameter="false"/>
                <Parameter Name="MGainShow" Min="0" Max="255" IsPresetParameter="false"/>
                <Parameter Name="PostBDynamicRange" Min="0" Max="15" Value="0" IsPresetParameter="false"/>
                <Parameter Name="PostPWDynamicRange" Min="0" Max="7" Value="0" IsPresetParameter="false"/>
                <Parameter Name="PostMDynamicRange" Min="0" Max="7" Value="0" IsPresetParameter="false"/>
                <Parameter Name="PostCWDynamicRange" ModelType="CW" Min="0" Max="7" Value="0" IsPresetParameter="false"/>
                <Parameter Name="PostTGC" ValueType="ByteArray" IsPresetParameter="false"/>
                <Parameter Name="PostLGC0" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="false"/>
                <Parameter Name="PostLGC1" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="false"/>
                <Parameter Name="PostLGC2" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="false"/>
                <Parameter Name="PostLGC3" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="false"/>
                <Parameter Name="PostLGC4" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="false"/>
                <Parameter Name="PostLGC5" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="false"/>
                <Parameter Name="PostLGC6" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="false"/>
                <Parameter Name="PostLGC7" ModelType="B" Value="0" DefaultValue="0" Min="-80" Max="80" IsPresetParameter="false"/>
                <Parameter Name="TripleModeResetEn" Value="true" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="RedundantPoints" Value="0" DefaultValue="0" IsPresetParameter="false"/>
                <Parameter Name="CQYZGrowingUp" Value="false" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="RenderWidgetRects" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="RenderWidgetSize" ValueType="Size" IsPresetParameter="false"/>
                <Parameter Name="DSCImageRects" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="DSCImageSize" ValueType="Size" IsPresetParameter="false"/>
                <Parameter Name="DSCImageZoomOn" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="FixedSWImageZoomCof" ValueType="Float" Value="1.0" IsPresetParameter="false"/>
                <Parameter Name="MDisplayFormat" Value="0" Min="0" Max="2" IsPresetParameter="false"/>
                <Parameter Name="DDisplayFormat" Value="0" Min="0" Max="2" IsPresetParameter="false"/>
                <Parameter Name="DTDIDisplayFormat" ModelType="TDI" Value="0" Min="0" Max="2" IsPresetParameter="false"/>
                <!--ImageRenderLayouts is List<ImageRenderLayoutinfos>-->
                <Parameter Name="ImageRenderLayouts" ValueType="List" IsPresetParameter="false"/>
                <!--ImageRenderLayouts is List<ImageRenderLayoutinfos>-->
                <Parameter Name="ImageGLRenderLayouts" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="TwoDImageScaleFactor" ValueType="PointF" IsPresetParameter="false"/>
                <Parameter Name="WaveImageScaleFactor" ValueType="PointF" IsPresetParameter="false"/>
                <Parameter Name="RenderBImageSize" ValueType="Size" IsPresetParameter="false"/>
                <Parameter Name="ProbeDSCImageZoomCof" ValueType="Float" Value="1.0" IsPresetParameter="false"/>
                <Parameter Name="DepthCMList" ValueType="List" IsPresetParameter="false"/>

                <Parameter Name="MECGDlyDeltas" ModelType="ECG" Value="5,10,20" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="FreeMECGDlyDeltas" ModelType="ECG" Value="5,10,20" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="DECGDlyDeltas" ModelType="ECG" Value="5,10,20" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="CWDECGDlyDeltas" ModelType="ECG" Value="5,10,20" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="DTDIECGDlyDeltas" ModelType="ECG" Value="5,10,20" ValueType="String" IsPresetParameter="true"/>
                <Parameter Name="AFE_LPF_FCutOff_Show" Value="0" Min="0" Max="3" IsPresetParameter="true"/>
                <Parameter Name="AFE_LNA_GAIN_Show" Min="0" Max="2" IsPresetParameter="true"/>
                <Parameter Name="AFE_HPF_FCutOff_Show" Min="0" Max="28" IsPresetParameter="true"/>
                <Parameter Name="PrtOfB_Delta" Min="0" Max="65535" IsPresetParameter="true"/>
                <Parameter Name="FrameScapeTimeShow" Min="0" Max="255" IsPresetParameter="false"/>

                <!-- add B mode FrameScapeTime date: 2020-06-05 -->
                <Parameter Name="FrameScapeTime" ModelType="B" Min="0" Max="255" IsPresetParameter="true"/>
                <!-- Parameter Name="FrameScapeTimeFast" Min="0" Max="255" IsPresetParameter="true"/ -->
                <Parameter Name="FrameScapeTimeSlow" ModelType="B" Min="0" Max="255" IsPresetParameter="true"/>

                <!-- add new parameters date: 2020-05-09 -->
                <!-- Blood Flow -->
                <Parameter Name="SampleRateDopSlow" Min="0" Max="15" IsPresetParameter="true" />
                <Parameter Name="SampleRateDopNormal" Min="0" Max="15" IsPresetParameter="true" />
                <Parameter Name="SampleRateDopFast" Min="0" Max="15" IsPresetParameter="true" />
                <!-- Contrast -->
                <Parameter Name="GainDelta" Min="-255" Max="255" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="DynamicRangeDelta" Min="-15" Max="15" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="GainDelta1" Min="-255" Max="255" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="DynamicRangeDelta1" Min="-15" Max="15" DefaultValue="0" IsPresetParameter="true"/>

                <Parameter Name="FlipLR" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>

                <Parameter Name="Q_IMAGE" Min="0" Max="255" IsPresetParameter="true"/>
                <Parameter Name="WallFilter" Min="0" Max="31" IsPresetParameter="true"/>
                <Parameter Name="WallThresholdS" Min="0" Max="255" IsPresetParameter="true"/>
                <Parameter Name="D_TxFNo" Min="0" Max="7" IsPresetParameter="true"/>

                <!--整数CM使用的缩放系数-->
                <Parameter Name="RenderImageZoomCof" ValueType="Float" Value="1.0" IsPresetParameter="false"/>
                <!--整数CM会裁剪图像,原本的DepthMM表示裁剪后图像的DepthMM,而RealDepthMM表示裁剪前的DepthMM-->
                <Parameter Name="RealDepthMM" ValueType="Double" IsPresetParameter="false"/>
                <!--EasyPlay StressEcho 模式下DSC出图缩小的缩放系数-->
                <Parameter Name="EasyPlayImageZoomCof" ValueType="Float" Value="1.0" IsPresetParameter="false"/>
                <Parameter Name="MVIDBIndex" ModelType="MVI" Min="0" Max="7" Value="0" IsPresetParameter="true"/>
                <!--PW声音延时-->
                <Parameter Name="PWSoundDelayTime" Min="100" Max="1000" Value="100" Step="10" IsPresetParameter="true"/>
                <Parameter Name="PWWaveImageDelayTime" Min="0" Max="1000" Value="0" Step="10" IsPresetParameter="true"/>
                <Parameter Name="PW2DImageDelayTime" Min="0" Max="1000" Value="0" Step="10" IsPresetParameter="true"/>
                <Parameter Name="PWECGDelayTime"  ModelType="ECG" Min="0" Max="1000" Value="0" Step="10" IsPresetParameter="true"/>
                <Parameter Name="MWaveImageDelayTime" Min="0" Max="1000" Value="0" Step="10" IsPresetParameter="true"/>
                <Parameter Name="M2DImageDelayTime" Min="0" Max="1000" Value="0" Step="10" IsPresetParameter="true"/>
                <Parameter Name="MECGDelayTime" ModelType="ECG" Min="0" Max="1000" Value="0" Step="10" IsPresetParameter="true"/>
                <Parameter Name="ECGDelayTime" ModelType="ECG" Min="0" Max="1000" Value="0" Step="10" IsPresetParameter="true"/>
                <Parameter Name="ECG2DImageDelayTime" ModelType="ECG" Min="0" Max="1000" Value="0" Step="10" IsPresetParameter="true"/>
                <Parameter Name="BECGDelayTime" ModelType="ECG" Min="0" Max="300" Value="0" Step="1" IsPresetParameter="false"/>
                <Parameter Name="LineImageDebug" Min="0" Max="4" Value="0" IsPresetParameter="false"/>
                <!--掌超探头心跳包-->
                <Parameter Name="HeartBeat" ValueType="Bool" DefaultValue="true" IsPresetParameter="false"/>
                <!--自动冻结参数，是否打开自动冻结-->
                <Parameter Name="AutoFreezeThre" DefaultValue="10" ValueType="Float" IsPresetParameter="false"/>

                <!--区域放大ROI框位置-->
                <Parameter Name="PanZoomSelect" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
>
                <Parameter Name="PanZoomMidPixel" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="PanZoomHalfPixel" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="PanZoomMidDepthPixel" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="PanZoomHalfDepthPixel" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="PanZoomOffsetDepthPixel" ValueType="Float" IsPresetParameter="false"/>
                <Parameter Name="PanZoomOffsetWidthPixel" ValueType="Float" IsPresetParameter="false"/>

                <Parameter Name="FreezePanZoom" ValueType="Bool" IsPresetParameter="false"/>
                <Parameter Name="ChangeStatus" ValueType="Bool" IsPresetParameter="false"/>

                <Parameter Name="PanZoomDrawOver" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="ChisonFourDMouseState" Min="1" Max="2" DefaultValue="1" Step="1" IsPresetParameter="false"/>
                <Parameter Name="PanZoomCineFreezePanZoom" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="DisplayStartXForPanZoom" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="DisplayStartYForPanZoom" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="DisplayWidthForPanZoom" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="DisplayHeightForPanZoom" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="TGCPOINTS" ValueType="List" IsPresetParameter="false"/>
                <Parameter Name="TGCStartY" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="TGCROIWIDTH" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="TGCROIHEIGHT" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="DepthLogoVisible" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="GainLogoVisible" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="TgcLogoVisible" ValueType="Bool" Value = "false" IsPresetParameter="false"/>

                <Parameter Name="BufferIndexStartPos" ValueType="Int" Value = "0" Min="0" Max="1000" IsPresetParameter="false"/>
                <Parameter Name="BufferIndexEndPos" ValueType="Int" Value = "0" Min="0" Max="1000" IsPresetParameter="false"/>
                <Parameter Name="CinePlaySpeedAdjust" ValueType="Int" Value = "2" Min="1" Max="3" IsPresetParameter="false"/>
                <Parameter Name="CineNavigate" ValueType="Int" Value = "1" Min="1" Max="1000" IsPresetParameter="false"/>
                <Parameter Name="CommentFontSizeChange" ValueType="Int" Value = "10" Min="10" Max="20" IsPresetParameter="false"/>
                <Parameter Name="MeasureResultFontSize" ValueType="Int" Value = "14" Min="10" Max="23" IsPresetParameter="false"/>

                <Parameter Name="EnableFreezeAfterProbeFound" ValueType="Bool" Value = "false" IsPresetParameter="false"/>

                <Parameter Name="RenderImageTopMargin" ValueType="Int" IsPresetParameter="false"/>

                <Parameter Name="CompoundDebug" Min="0" Max="7" Value="7" IsPresetParameter="false"/>
                <Parameter Name="CurvedExapanding" ValueType="Bool" Value = "false" IsPresetParameter="false"/>

                <Parameter Name="DSCParameterSaver" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="ColorDataType" ValueType="Int" Value = "1" IsPresetParameter="false"/>
                <Parameter Name="LGCRect" ValueType="Rect" IsPresetParameter="false"/>
                <Parameter Name="LGCPOINTS" ValueType="List" IsPresetParameter="false"/>

                <Parameter Name="SRIEnable" ModelType="B" ValueType="Bool" Value = "false" IsPresetParameter="true"/>
                <Parameter Name="SRINoiseLevel" ModelType="B" ValueType="String" MissingDefaultValue = "30" IsPresetParameter="true"/>
                <Parameter Name="SRIEdgeEnhance" ModelType="B" ValueType="String" MissingDefaultValue = "30" IsPresetParameter="true"/>
                <Parameter Name="SRIFilterStrength" ModelType="B" ValueType="String" MissingDefaultValue = "30" IsPresetParameter="true"/>
                <Parameter Name="SRIDRAdjust" ModelType="B" ValueType="Int" Value = "2" Min="0" Max="100" IsPresetParameter="true"/>
                <Parameter Name="SRIHfnoise" ModelType="B" ValueType="Float" Value = "5" Min="0" Max="100" IsPresetParameter="true"/>
                <Parameter Name="SRIEdgeThreshold" ModelType="B" ValueType="Float" Value = "1"  Min="0" Max="100" IsPresetParameter="true"/>
                <Parameter Name="SRIDetailPreservation" ModelType="B" ValueType="Float" Value = "1" Min="0" Max="100" IsPresetParameter="true"/>
                <Parameter Name="SRIEdgeRampDown" ModelType="B" ValueType="Int" Value = "180" Min="0" Max="255" IsPresetParameter="true"/>
                <Parameter Name="SRIEdgeDirectionThresh" ModelType="B" ValueType="Int" Value = "0" Min="0" Max="255" IsPresetParameter="true"/>
                <Parameter Name="SRIHoleFillerThresh" ModelType="B" ValueType="Int" Value = "10" Min="0" Max="100" IsPresetParameter="true"/>
                <Parameter Name="SRIOverallStrength" ModelType="B" ValueType="Float" Value = "1" IsPresetParameter="true"/>
                <Parameter Name="SRINoiseFilterType" ModelType="B" ValueType="Int" Value = "0" Min="0" Max="0" IsPresetParameter="true"/>
                <Parameter Name="SRIEdgeFilterType" ModelType="B" ValueType="Int" Value = "0" Min="0" Max="0" IsPresetParameter="true"/>

                <Parameter Name="IsNavigationEllipseHidden" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="ShowNavigationEllipse" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="NeedleSize" Min="0" Value = "0" DefaultValue = "0" Max="1" IsPresetParameter="true"/>
                <Parameter Name="PowerThreshold"    Min="0" Max="15" Step="1" Value="0" IsPresetParameter="true"/>
                <Parameter Name="PowerThresholdPD" ModelType="PD" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PowerThresholdSN" ModelType="SonoNeedle" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PowerThresholdTDI" ModelType="TDI" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="PowerThresholdMVI" ModelType="MVI" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="true" IsDirectValue="true"/>
                <Parameter Name="IsDPDColor" ModelType="DPD" ValueType="Bool" Value = "false" IsPresetParameter="true"/>
                <Parameter Name="CWDSampleRateBak" ModelType="CW" IsPresetParameter="false"/>
                <Parameter Name="ChangedByAdjustment" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="HPrfTopGateDelta" Min="0" Max="127" IsPresetParameter="true"/>
                <Parameter Name="SonoRemote" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="SonoHelp" ValueType="Bool" Value="false" IsPresetParameter="false"/>

                <!--DopAnalogTgc在Apple机型下没有用到，控制表中的位置被APF_PW复用-->
                <Parameter Name="DopAnalogTgc0" Value="192" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc1" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc2" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc3" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc4" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc5" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc6" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc7" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc8" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc9" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc10" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc11" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc12" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc13" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc14" Value="255" IsPresetParameter="false"/>
                <Parameter Name="DopAnalogTgc15" Value="255" IsPresetParameter="false"/>

                <Parameter Name="IsEnableAdjustROI" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="IsAutoNeedleAngleEnable" ValueType="Bool" Value = "false" IsPresetParameter="false"/>

                <Parameter Name="OptimizeFPS" ValueType="Bool" Value = "false" IsPresetParameter="true"/>
                <Parameter Name="MBInc" ValueType="Int" Value="1" DefaultValue="1" Min="0" Max="3" IsPresetParameter="true"/>

                <!--pw 前处理-->
                <Parameter Name="IsSonoPWOn" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="IsSmallPW" ValueType="Bool" Value = "true" IsPresetParameter="false"/>
                <!--color 前处理-->
                <Parameter Name="PreColorPersistenceEnable" ValueType="Bool" Value="true" MissingDefaultValue = "false" IsPresetParameter="false"/>
                <Parameter Name="ColorPersistenceAlgorithm" ValueType="Int" Value="0" DefaultValue = "0" MissingDefaultValue = "0" Min="0" Max="10" IsPresetParameter="true"/>
                <Parameter Name="ColorPersistenceHigh" ValueType="Double" Value="0.25" DefaultValue = "0.25" MissingDefaultValue = "0.25" Min="0" Max="1" IsPresetParameter="true"/>
                <Parameter Name="ColorPersistenceLow" ValueType="Double" Value="0.15" DefaultValue = "0.15" MissingDefaultValue = "0.15" Min="0" Max="1" IsPresetParameter="true"/>
                <Parameter Name="ColorPersistenceAN" ValueType="Double" Value="0.8" DefaultValue = "0.8" MissingDefaultValue = "0.8" Min="0" Max="1" IsPresetParameter="true"/>
                <Parameter Name="ColorPersistenceRatio" ValueType="Double" Value="0.3" DefaultValue = "0.3" MissingDefaultValue = "0.3" Min="0" Max="1" IsPresetParameter="true"/>
                <Parameter Name="ColorPersistenceUsingN_1" ValueType="Bool" Value="false" DefaultValue = "false" MissingDefaultValue = "false" IsPresetParameter="true"/>
                <!--color 后处理-->
                <Parameter Name="ISCFMIIREnable" ValueType="Bool" Value ="true" MissingDefaultValue = "true" IsPresetParameter="false"/>
                <Parameter Name="ColorEDAEnable" ValueType="Bool" Value ="false" DefaultValue="false" MissingDefaultValue = "false" IsPresetParameter="true"/>
                <Parameter Name="ColorEDASESize" ValueType="Int" Value ="13" DefaultValue="13" MissingDefaultValue = "13" Min="3" Max="49" Step="2" IsPresetParameter="true"/>
                <Parameter Name="ColorEDASEType" ValueType="Int" Value ="0" DefaultValue="0" MissingDefaultValue = "0" Min="0" Max="4" Step="1" IsPresetParameter="true"/>

                <!--声音播放器属性参数-->
                <Parameter Name="SoundPlayerDeviceName" ValueType="String" Value ="plughw:1,0" MissingDefaultValue = "plughw:1,0" IsPresetParameter="false"/>
                <Parameter Name="SoundPlayerSampleRate" ValueType="Int" Value ="44100" MissingDefaultValue = "16000" IsPresetParameter="false"/>
                <Parameter Name="SoundPlayerChannelCount" ValueType="Int" Value ="2" MissingDefaultValue = "2" IsPresetParameter="false"/>
                <Parameter Name="SoundPlayerSampleSize" ValueType="Int" Value ="8" MissingDefaultValue = "8" IsPresetParameter="false"/>

                <Parameter Name="HideTCGForCine" ValueType="Bool" Value ="false" MissingDefaultValue = "false" IsPresetParameter="false"/>
                <Parameter Name="HideLGCForCine" ValueType="Bool" Value ="false" MissingDefaultValue = "false" IsPresetParameter="false"/>
                <Parameter Name="ArbitraryWaveFormExtend" ModelType="B" ValueType="Bool" Value ="false" MissingDefaultValue = "false" IsPresetParameter="false"/>
                <Parameter Name="ArbitraryWaveForm1" ModelType="B" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ArbitraryWaveForm2" ModelType="B" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ArbitraryWaveForm3" ModelType="B" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ArbitraryWaveForm4" ModelType="B" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ArbitraryWaveForm6" ModelType="B" ValueType="String" IsPresetParameter="false"/>
                <Parameter Name="ArbitraryWaveForm7" ModelType="B" ValueType="String" IsPresetParameter="false"/>

                <Parameter Name="SonoNeedle" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="ColorTransparency" ModelType="SonoNeedle" ValueType="Int" Value="0" Min="0" Max="100" Step="1" IsPresetParameter="true"/>
                <Parameter Name="TDITransparency" ModelType="TDI" ValueType="Int" Value="0" Min="0" Max="100" Step="1" IsPresetParameter="true"/>
                <Parameter Name="SNColorMapIndex" ModelType="SonoNeedle" Min="0" Max="9" IsPresetParameter="true"/>
                <!--用于控制当前预设是否支持TDI功能-->
                <Parameter Name="SupportTDI" ModelType="TDI" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>

                <Parameter Name="MVIShow" ModelType="MVI" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
                <Parameter Name="MVITransparency" ModelType="MVI" ValueType="Int" Value="0" Min="0" Max="100" Step="1" IsPresetParameter="true"/>
                <Parameter Name="MVIType" ModelType="MVI" Step="1" Value="0" Min="0" Max="2" DefaultValue="0" IsPresetParameter="true"/>
                <Parameter Name="ShowBInROI" ModelType="MVI" ValueType="Bool" DefaultValue = "true" TrueValue="1" IsPresetParameter="true"/>
                <Parameter Name="IsSupportIntegerDepth" ValueType="Bool" DefaultValue = "false"  IsPresetParameter="false"/>
                <!--系统采用任意线密度的参数进行描述以及参与计算-->
                <Parameter Name="StartScanLineColor" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="StopScanLineColor" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="StartScanDisColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="StopScanDisColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="LineSpacingColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>

                <!--用于Zeus3，支持PW处理点数的扩展(512, 720等)-->
                <Parameter Name="PWSecondSampDatNum" ValueType="Int" Value = "1024" IsPresetParameter="false"/>

                <Parameter Name="MF_Coef_Fundamental_Steer0" ModelType="B" Min="0" Max="7" ValueType="Int" IsPresetParameter="false"/>
                <Parameter Name="MF_Coef_Fundamental_SteerX" ModelType="B" Min="0" Max="7" ValueType="Int" IsPresetParameter="false"/>
                <Parameter Name="MF_Coef_Harmonic_Steer0" ModelType="B" Min="0" Max="7" ValueType="Int" IsPresetParameter="false"/>
                <Parameter Name="MF_Coef_Harmonic_SteerX" ModelType="B" Min="0" Max="7" ValueType="Int" IsPresetParameter="false"/>
                <Parameter Name="CPDSteer2" ModelType="B" Min="0" Max="20" Step="1" Value="3" IsPresetParameter="false"/>
                <Parameter Name="CPDSteer3" ModelType="B" Min="0" Max="20" Step="1" Value="3" IsPresetParameter="false"/>
                <Parameter Name="CPDSteer4" ModelType="B" Min="0" Max="20" Step="1" Value="3" IsPresetParameter="false"/>
                <Parameter Name="CPDSteer5" ModelType="B" Min="0" Max="20" Step="1" Value="3" IsPresetParameter="false"/>
                <Parameter Name="TCPDSteer" ModelType="B" Min="0" Max="20" Step="1" Value="3" IsPresetParameter="false"/>
                <Parameter Name="CPDSteer_CEUS" ModelType="B" Min="0" Max="20" Step="1" Value="3" IsPresetParameter="false"/>

                <Parameter Name="DScanLineStartX" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="DScanLineStartY" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="DScanLineEndX" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="DScanLineEndY" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="DScanLineFirstDivideY" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="DScanLineSecondDivideY" ValueType="Int" Value = "0" IsPresetParameter="false"/>

                <Parameter Name="PostRawDataEnable" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="BiopsyVerify" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="BiopsyAngleChoose" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="BiopsyAngleIndex" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="BiopsyAngleMax" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="BiopsyAngleMin" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="BiopsyPositionMax" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="BiopsyPositionMin" ValueType="Int" Value = "0" IsPresetParameter="false"/>
                <Parameter Name="BiopsyNeedSave" ValueType="Bool" Value = "false" IsPresetParameter="false"/>
                <Parameter Name="RTIMT" Value="false" ValueType="Bool" MissingDefaultValue="false" IsPresetParameter="false"/>

                <Parameter Name="BMVIBImages" ModelType="MVI" ValueType="Bool" Value="true" IsPresetParameter="false"/>
                <Parameter Name="Save8BitBeforeColorMapEnable" ValueType="Bool" Value="false" IsPresetParameter="false"/>

                <Parameter Name="SonoNerve" Value="false" ValueType="Bool" DefaultValue = "false" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="SonoNerveIsShow" ValueType="Bool" Value="true" DefaultValue = "true" IsPresetParameter="false"/>
                <Parameter Name="SonoNerveTrans" ValueType="Int" Value="70"  DefaultValue = "50" Min="5" Max="100" Step="5" IsPresetParameter="false"/>
                <Parameter Name="SonoNervePart" ValueType="Int" Value = "0"  DefaultValue = "0" IsPresetParameter="false"/>
                <Parameter Name="SupportAdjustTGCByMouse" ValueType="Bool" Value="true" IsPresetParameter="false"/>
                <Parameter Name="SonoThyroid" Value="false" ValueType="Bool" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="SonoCardiac" Value="false" ValueType="Bool" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="SonoCardiacSecRecognize" Value="false" ValueType="Bool" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="SonoCardiacSecRating" Value="false" ValueType="Bool" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="SonoCardiacSection" Value="0" ValueType="Int" MissingDefaultValue="0" IsPresetParameter="false"/>
                <Parameter Name="SonoCarotidGuide" Value="false" ValueType="Bool" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="SonoCarotidDirection" Value="1" ValueType="Int" IsPresetParameter="false"/>
                <Parameter Name="SonoCarotidLR" Value="1" ValueType="Int" IsPresetParameter="false"/>
                <!--WholeImage = 0 整帧数据, LineData = 1 线数据, IQData = 2 Color、PW是IQ数据, BFData = 3 Beamformer之后的数据-->
                <Parameter Name="RawDataFormat" ValueType="Double" Value = "2" IsPresetParameter="false"/>
                <Parameter Name="SonoMSK" Value="false" ValueType="Bool" DefaultValue = "false" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="SonoMSKIsShow" ValueType="Bool" Value="true" DefaultValue = "true" IsPresetParameter="false"/>
                <Parameter Name="SonoMSKTrans" ValueType="Int" Value="70"  DefaultValue = "50" Min="5" Max="100" Step="5" IsPresetParameter="false"/>
                <Parameter Name="SonoMSKPart" ValueType="Int" Value = "0"  DefaultValue = "0" IsPresetParameter="false"/>

                <!--AutoEF 测量-->
                <Parameter Name="AutoEFOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="AutoEFCurLayout" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="ESFrame" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="EDFrame" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="ESFrameStamp" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="EDFrameStamp" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="AutoEFStartFrame" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="AutoEFEndFrame" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="ESCurIndex" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="EDCurIndex" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="HasAutoEFResult" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="ImageRenderPartition" ValueType="Int" Value="0" IsPresetParameter="false"/>
                <Parameter Name="AutoEFActivePartition" ValueType="Int" Value="0" MissingDefaultValue = "0" IsPresetParameter="false"/>

                <Parameter Name="SonoAAA" ValueType="Bool" Value="false" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="SonoAAAMessage" ValueType="Bool" Value="false" MissingDefaultValue="false" IsPresetParameter="false"/>
                <!--Element Check-->
                <Parameter Name="OpenElementCheck" ValueType="Bool" Value="false" IsPresetParameter="false"/>
                <Parameter Name="SonoVF" IsPresetParameter="false"/>
                <Parameter Name="ColorVHSIType" ValueType="Int" Value="0" Min="0" Max="10" Step="1" IsPresetParameter="true"/>
                <Parameter Name="ColorHoleFillingDB" ValueType="Int" DefaultValue="-6" Value="-6" Min="-20" Max="0" Step="1" IsPresetParameter="true"/>
                <!--COverlap Weight-->
                <Parameter Name="ColorOverlapWeight" ValueType="Double" DefaultValue="0.75" Min="0" Max="1" IsPresetParameter="true"/>
                <Parameter Name="ColorMappedBDataCallbackEnable" ValueType="Bool" Value = "false" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="EFastModeON" ValueType="Bool" Value="false" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="FastModeON" ValueType="Bool" Value="false" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="AutoBLine" ValueType="Bool" Value="false" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="SonoVTI" ValueType="Bool" Value="false" MissingDefaultValue="false" IsPresetParameter="false"/>
                <Parameter Name="PictureModeON" ValueType="Bool" Value="false" MissingDefaultValue="false" IsPresetParameter="false"/>
        </BFParameterGroup>
</BFParameter>
<!-- un appended
DefaultScpd = para.DefaultScpd;
SonoParas.VideoInvertOfD = false;
SonoParas.VideoInvertOfTD = false;
SonoParas.VideoInvertOfCW = false;
SonoParas.VideoInvertOfM = false;
FreqIndexIn4D = para.FreqIndexIn4D;
setScaleRatio(para);
InvertUpDown = (para.UdInvert == 1) ? true : false;
-->
