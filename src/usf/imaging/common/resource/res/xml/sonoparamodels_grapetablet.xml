<?xml version="1.0" encoding="UTF-8"?>
<!--Paras 可以用","分隔，增加多个参数-->
<Models>
<Group Key="B">
    <Model Paras="FPS" Label="FPS:"/>
    <Model Paras="GainShow" Label="GN:"/>
    <Model Paras="AcousticPowerBShow" Label="AP:"/>
    <Model Paras="FreqIndexB" Label="Freq.:"/>
    <Model Paras="DynamicRange" Label="DN:"/>
    <Model Paras="CQYZ" Label="D:"/>
    <Model Paras="THI" Label="FHI:"/>
    <Model Paras="XContrastValue" Label="X-con:"/>
    <Model Paras="ImageZoomCoef" Label="Z:"/>
</Group>
<Group Key="OB">
    <Model Paras="Lmp" Label="LMP:"/>
    <Model Paras="LmpGa" Label="GA:"/>
    <Model Paras="LmpEdd" Label="EDD:"/>
</Group>
<Group Key="C">
    <Model Paras="FreqIndexColor" Label="Freq.:"/>
    <Model Paras="SampleRateDopShow" Label="PRF:"/>
    <Model Paras="WallFilterColor" Label="WF:"/>
    <Model Paras="GainColor" Label="GN:"/>
<!--    <Model Paras="QBeamOn" Label="Q-beam:"/>
    <Model Paras="QFlowOn" Label="Q-flow:"/>-->
</Group>
<Group Key="CPA">
    <Model Paras="FreqIndexPD" Label="Freq.:"/>
    <Model Paras="SampleRateDopShow" Label="PRF:"/>
    <Model Paras="WallFilterPD" Label="WF:"/>
    <Model Paras="GainPD" Label="GN:"/>
<!--    <Model Paras="QBeamOn" Label="Q-beam:"/>
    <Model Paras="QFlowOn" Label="Q-flow:"/>-->
</Group>
<Group Key="DPD">
    <Model Paras="FreqIndexPD" Label="Freq.:"/>
    <Model Paras="SampleRateDopShow" Label="PRF:"/>
    <Model Paras="WallFilterPD" Label="WF:"/>
    <Model Paras="GainPD" Label="GN:"/>
<!--    <Model Paras="QBeamOn" Label="Q-beam:"/>
    <Model Paras="QFlowOn" Label="Q-flow:"/>-->
</Group>
<Group Key="TDI">
    <Model Paras="FreqIndexTDI" Label="Freq.:"/>
    <Model Paras="SampleRateDopTDI" Label="PRF:"/>
    <Model Paras="TDIWallfilter" Label="WF:"/>
    <Model Paras="GainTDI" Label="GN:"/>
    <Model Paras="TDIColorMapIndex,FrameAvgTDI" Label="C/P:"/>
</Group>
<Group Key="MVI">
    <Model Paras="SampleRateDopMVI" Label="PRF:"/>
    <Model Paras="WallFilterMVI" Label="WF:"/>
    <Model Paras="GainMVI" Label="GN:"/>
    <Model Paras="MVIColorMapIndex,FrameAvgMVI" Label="C/P:"/>
</Group>
<Group Key="M">
    <Model Paras="MVelocity" Label="MV:"/>
    <Model Paras="MGainShow" Label="GN:"/>
<!--    <Model Paras="MDynamicRange" Label="DN:"/>-->
</Group>
<Group Key="D">
    <Model Paras="FreqIndexDop" Label="Freq.:"/>
    <Model Paras="DSampleRateDopShow" Label="PRF:"/>
    <Model Paras="WallFilterDop" Label="WF:"/>
    <Model Paras="GainDopShow" Label="GN:"/>
    <Model Paras="DopplerTheta" Label="Angle:"/>
    <Model Paras="SampleVolumeMM" Label="SV:"/>
</Group>
<Group Key="PreD">
    <Model Paras="DopplerTheta" Label="Angle:"/>
    <Model Paras="SampleVolumeMM" Label="SV:"/>
</Group>
<Group Key="PreDTDI">
    <Model Paras="DopplerThetaTDI" Label="Angle:"/>
    <Model Paras="SampleVolumeTDIMM" Label="SV:"/>
</Group>
<Group Key="DTDI">
    <Model Paras="FreqIndexTD" Label="Freq.:"/>
    <Model Paras="DSampleRateDopTDI" Label="PRF:"/>
    <Model Paras="WallFilterDopTDI" Label="WF:"/>
    <Model Paras="GainDopShow" Label="GN:"/>
    <Model Paras="DopplerThetaTDI" Label="Angle:"/>
    <Model Paras="SampleVolumeTDIMM" Label="SV:"/>
</Group>
<Group Key="CWD">
    <Model Paras="FreqIndexCWD" Label="Freq.:"/>
    <Model Paras="CWDSampleRate" Label="PRF:"/>
    <Model Paras="WallFilterCWD" Label="WF:"/>
    <Model Paras="GainDopShow" Label="GN:"/>
    <Model Paras="DopplerThetaCWD" Label="Angle:"/>
</Group>
<Group Key="PreCWD">
    <Model Paras="FreqIndexCWD" Label="Freq.:"/>
    <Model Paras="DopplerThetaCWD" Label="Angle:"/>
</Group>
</Models>
