<?xml version="1.0" encoding="UTF-8"?>
<!--Paras 可以用","分隔，增加多个参数-->
<Models>
<Group Key="Temp">
    <Model Paras="TeeProbeTemperature" Label="TEE T:" />
    <Model Paras="PatientTemperature" Label="Pat.T:" />
</Group>
<Group Key="OB">
    <Model Paras="Lmp" Label="LMP:"/>
    <Model Paras="LmpGa" Label="GA:"/>
    <Model Paras="LmpEdd" Label="EDD:"/>
</Group>
<Group Key="B">
    <Model Paras="FPS" Label="FPS:"/>
    <Model Paras="GainShow" Label="GN:"/>
    <Model Paras="AcousticPowerBShow" Label="PWR:"/>
    <Model Paras="FreqIndexB" Label="FRQ:"/>
    <Model Paras="DynamicRange" Label="DN:"/>
    <Model Paras="CQYZ" Label="D:"/>
    <Model Paras="THI" Label="FHI:"/>
    <Model Paras="XContrastValue" Label="X-con:"/>
</Group>
<Group Key="M">
    <Model Paras="MVelocity" Label="MV:"/>
    <Model Paras="MGainShow" Label="GN:"/>
</Group>
<Group Key="C">
    <Model Paras="FreqIndexColor" Label="FRQ:"/>
    <Model Paras="SampleRateDopShow" Label="PRF:"/>
    <Model Paras="WallFilterColor" Label="WF:"/>
    <Model Paras="GainColor" Label="GN:"/>
    <Model Paras="QBeamOn" Label="Q-beam:"/>
    <Model Paras="QFlowOn" Label="Q-flow:"/>
</Group>
<Group Key="CPA">
    <Model Paras="FreqIndexPD" Label="FRQ:"/>
    <Model Paras="SampleRateDopShow" Label="PRF:"/>
    <Model Paras="WallFilterPD" Label="WF:"/>
    <Model Paras="GainPD" Label="GN:"/>
    <Model Paras="QBeamOn" Label="Q-beam:"/>
    <Model Paras="QFlowOn" Label="Q-flow:"/>
</Group>
<Group Key="DPD">
    <Model Paras="FreqIndexPD" Label="FRQ:"/>
    <Model Paras="SampleRateDopShow" Label="PRF:"/>
    <Model Paras="WallFilterPD" Label="WF:"/>
    <Model Paras="GainPD" Label="GN:"/>
    <Model Paras="QBeamOn" Label="Q-beam:"/>
    <Model Paras="QFlowOn" Label="Q-flow:"/>
</Group>
<Group Key="TDI">
    <Model Paras="FreqIndexTDI" Label="FRQ:"/>
    <Model Paras="SampleRateDopTDI" Label="PRF:"/>
    <Model Paras="TDIWallfilter" Label="WF:"/>
    <Model Paras="GainTDI" Label="GN:"/>
</Group>
<Group Key="D">
    <Model Paras="FreqIndexDop" Label="FRQ:"/>
    <Model Paras="DSampleRateDopShow" Label="PRF:"/>
    <Model Paras="WallFilterDop" Label="WF:"/>
    <Model Paras="GainDopShow" Label="GN:"/>
    <Model Paras="DopplerTheta" Label="Angle:"/>
    <Model Paras="SampleVolumeMM" Label="SV:"/>
</Group>
<Group Key="PreD">
    <Model Paras="DopplerTheta" Label="Angle:"/>
    <Model Paras="SampleVolumeMM" Label="SV:"/>
</Group>
<Group Key="PreDTDI">
    <Model Paras="DopplerThetaTDI" Label="Angle:"/>
    <Model Paras="SampleVolumeTDIMM" Label="SV:"/>
</Group>
<Group Key="DTDI">
    <Model Paras="FreqIndexTD" Label="FRQ:"/>
    <Model Paras="DSampleRateDopTDI" Label="PRF:"/>
    <Model Paras="WallFilterDopTDI" Label="WF:"/>
    <Model Paras="GainDopShow" Label="GN:"/>
    <Model Paras="DopplerThetaTDI" Label="Angle:"/>
    <Model Paras="SampleVolumeTDIMM" Label="SV:"/>
</Group>
<Group Key="CWD">
    <Model Paras="FreqIndexCWD" Label="Freq.:"/>
    <Model Paras="CWDSampleRate" Label="PRF:"/>
    <Model Paras="WallFilterCWD" Label="WF:"/>
    <Model Paras="GainDopShow" Label="GN:"/>
    <Model Paras="DopplerThetaCWD" Label="Angle:"/>
</Group>
<Group Key="PreCWD">
    <Model Paras="FreqIndexCWD" Label="Freq.:"/>
    <Model Paras="DopplerThetaCWD" Label="Angle:"/>
</Group>
</Models>
