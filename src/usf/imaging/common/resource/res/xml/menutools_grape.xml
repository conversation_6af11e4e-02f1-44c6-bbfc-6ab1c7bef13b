<MenuTools>
<Menu Type="LeftMenu">
    <!--BMenu-->
    <MenuItem Name="AutoBLine" Text="B-Lines" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
<!--    <MenuItem Name="SonoVTI" Text="SonoVTI" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>-->
<!--    <MenuItem Name="SonoAV" Text="SonoAV" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>-->
    <!-- <MenuItem Name="SonoCardiac" Text="SonoCardiac" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/> -->
    <MenuItem Name="THIState" Text="FHI" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="AcousticPowerBShow" Text="A. Power" Value="60%" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <!--<MenuItem Name="ZoomDec" Text="Enlarge" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true"/>-->
    <!--<MenuItem Name="ZoomAdd" Text="Lessen" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true"/>-->
    <MenuItem Name="ScanWidth" Text="Scan Width" Value="100%" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="FcpdOn" Text="SRA" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="TDIBMenuShow" Text="TDI" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="OrderHighDensity" Text="Density" Value="Low" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="Smooth" Text="Smooth" Value="1" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="XContrastValue" Text="X-contrast" Value="1" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="EdgeShow" Text="Edge Enhance" Value="1" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="Rotation" Text="Rotation" Value="" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>
    <MenuItem Name="BColorMapIndexShow" Text="Chroma" Value="type_1" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="BGrayCurveIndexShow" Text="2D Map" Value="Type1" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="FourBMode" Text="4B Mode" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="BGammaShow" Text="Gamma" Value="4" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="ECGEn" Text="ECG" Value="false" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <!--<MenuItem Name="FocusNumShow" Text="Focus Num" Value="1" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" /> -->
    <!--<MenuItem Name="Focus" Text="Focus Pos." Value="" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" />-->
    <!--<MenuItem Name="QuickAngle" Text="Quick Angle" Value=" " Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <!--<MenuItem Name="Left" Text="L/R Flip" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>-->
    <!--<MenuItem Name="Up" Text="U/D Flip" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>-->
    <MenuItem Name="CurvedPanoramic" Text="Curved Panoramic" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>
    <!--<MenuItem Name="Utility" Text="Utility" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->
    <MenuItem Name="StressEchoEn" Text="Stress Echo" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="fasle" Loop="true" PostProcessTool="false"/>
    <MenuItem Name="Advance" Text="Advance" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>
    <!--<MenuItem Name="ImageZoomCoef" Text="Zoom Coef" Value="90%" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="true" PostProcessTool="true"/>-->
    <!--<MenuItem Name="FullScreenZoomInIndex" Text="Full Screen Zoom" Value="0" Category="BMenu" ClickTool="false" EngineeringTool="true" ParameterTool="true" AdvanceTool="true"/>-->
    <!--<MenuItem Name="TwoBMode" Text="2B" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="true"/>-->
    <!--<MenuItem Name="PA_VERT_DIST_Enable" Text="VirtualApex" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true"/>-->
    <MenuItem Name="CurvedExapanding" Text="Extended" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true" Loop="true" PostProcessTool="false"/>
    <MenuItem Name="VirtualVertexTrapezoidalMode" Text="Trapezoidal" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true"/>
    <MenuItem Name="BSteeringScan" Text="2D Steer" Value="20" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="true"/>
    <MenuItem Name="BiopsyMenu" Text="Biopsy" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true"/>
    <!--<MenuItem Name="NeedleMode" Text="SuperNeedle" Value="false" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true" Loop="true"/>-->
    <!--<MenuItem Name="IsAutoNeedleAngleEnable" Text="AutoAngle" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true" Loop="true"/>-->
    <!--<MenuItem Name="NeedleAngleIndex" Text="Needle Angle" Value="0" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="true"/>-->
    <!--<MenuItem Name="IsCenterLineVisible" Text="Center Line" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true" Loop="true"/>-->
    <MenuItem Name="SonoNeedle" Text="SonoPleura" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true"/>
    <MenuItem Name="SonoNerve" Text="SonoNerve" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
<!--    <MenuItem Name="SonoCarotidGuide" Text="SonoGuide" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="fasle" Loop="true" PostProcessTool="false"/>-->
    <MenuItem Name="MVIShow" Text="MVI" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true" Loop="true"/>
    <MenuItem Name="SonoMSK" Text="SonoMSK" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="LGCControlEn" Text="LGC" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>
    <!--<MenuItem Name="ElastoGraphy" Text="Elastography" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true" Loop="true"/>-->
    <MenuItem Name="ElastoShow" Text="Elastography" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="true" Loop="true"/>
    <MenuItem Name="FreeHand3D" Text="HD 3D" Value="" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>

    <!--Stress EchoMenu-->
    <MenuItem Name="StressEchoEn" Text="Stress Echo" Value="Off" Category="Stress EchoMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="fasle" Loop="true" PostProcessTool="false"/>
    <MenuItem Name="StressEchoTemplate" Text="Template" Value="" Category="Stress EchoMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="StressEchoAnalyze" Text="Analyze" Value="" Category="Stress EchoMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="StressEchoT1Control" Text="T1" Value="" Category="Stress EchoMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="StressEchoT2Control" Text="T2" Value="" Category="Stress EchoMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>

    <!--SonoCardiac Menu-->
    <MenuItem Name="SonoCardiacSection" Text="Section" Value="0" Category="SonoCardiacMenu" ClickTool="false" EngineeringTool="false" PopupTool="true" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="SonoCardiacSecImage" Text="Guide" Value="0" Category="SonoCardiacMenu" ClickTool="false" EngineeringTool="false" PopupTool="true" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="SonoCardiacSecRecognize" Text="Recognize" Value="Off" Category="SonoCardiacMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" Loop="true"/>
    <MenuItem Name="SonoCardiacSecRating" Text="Score" Value="Off" Category="SonoCardiacMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" Loop="true"/>
    <MenuItem Name="SonoCardiacExit" Text="Exit" Value="" Category="SonoCardiacMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>

    <!--CMenu-->
    <MenuItem Name="WallFilterColor" Text="Wall Filter" Value="2" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="PowerThreshold" Text="Wall Thre." Value="2" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="OrderColorLineDensity" Text="Density" Value="Low" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="BloodEffection" Text="Blood Eff." Value="2" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="BCImagesOn" Text="B/BC" Value="Off" Category="CMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="IsEnableAdjustROI" Text="I. Doppler" Value="Off" Category="CMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="PDState" Text="CPA" Value="Off" Category="CMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="CurvedPanoramic" Text="Color Curved Panoramic" Value="" Category="CMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>
    <MenuItem Name="ColorInvertState" Text="Color Invert" Value="on" Category="CMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <!--<MenuItem Name="Utility" Text="Utility" Value="" Category="CMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->
    <!--<MenuItem Name="Advance" Text="Advance" Value="" Category="CMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->
    <!--<MenuItem Name="FourBMode" Text="Quad Mode" Value="" Category="CMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="true"/>-->
    <!--<MenuItem Name="TwoBMode" Text="2B" Value="" Category="CMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="true"/>-->

    <!--CPAMenu-->
    <MenuItem Name="PowerThresholdPD" Text="Wall Thre." Value="2" Category="CPAMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="OrderLineDensityPD" Text="Density" Value="Low" Category="CPAMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="BloodEffectionPD" Text="Blood Eff." Value="2" Category="CPAMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="BCImagesOn" Text="B/BC" Value="Off" Category="CPAMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="IsEnableAdjustROI" Text="I. Doppler" Value="Off" Category="CPAMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>

    <!--SonoNeedleMenu-->
    <MenuItem Name="SonoNeedleExit" Text="Exit" Value="" Category="SonoNeedleMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"/>

    <!--DPDMenu-->
    <MenuItem Name="PowerThresholdPD" Text="Wall Thre." Value="2" Category="DPDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="OrderLineDensityDPD" Text="Density" Value="Low" Category="DPDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="DPDInvertState" Text="Color Invert" Value="on" Category="DPDMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" Loop="true"/>
    <MenuItem Name="BloodEffectionPD" Text="Blood Eff." Value="2" Category="DPDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="BCImagesOn" Text="B/BC" Value="Off" Category="DPDMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="IsEnableAdjustROI" Text="I. Doppler" Value="Off" Category="DPDMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>

    <!--TDIMenu-->
    <MenuItem Name="TDIInvertState"    Text="Color Invert" Value="On"     Category="TDIMenu" ClickTool="true"  EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" Loop="true"/>
    <MenuItem Name="TDIColorMapIndex"  Text="Color Map"    Value="type_1" Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="BloodEffectionTDI" Text="Blood Eff."   Value="2"      Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="OrderLineDensityTDI" Text="Density" Value="Low" Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="FrameAvgTDI"       Text="Persistence"  Value="2"      Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
<!--    <MenuItem Name="QuickAngle" Text="Quick Angle" Value="0" Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <!--<MenuItem Name="Utility" Text="Utility" Value="" Category="TDIMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->

    <!--PW PreMenu-->
    <!--TODO，菜单支持空菜单.PW Pre菜单暂时不支持空菜单，至少有一个菜单项。-->
    <MenuItem Name="DopplerTheta" Text="Angle" Value="0" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="IsEnableAdjustROI" Text="I. Doppler" Value="0" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <!--<MenuItem Name="FreqIndexB" Text="Freq." Value="3.0M" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <!--<MenuItem Name="BaseLine" Text="Baseline" Value="3" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>-->
    <!--<MenuItem Name="WallFilterDop" Text="Wall Filter" Value="2" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <!--<MenuItem Name="PowerThresholdPD" Text="Wall Thre." Value="2" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <!--<MenuItem Name="FrameAvgColor" Text="Persistence" Value="2" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->

    <!--DMenu-->
    <MenuItem Name="TriplexMode" Text="Triplex" Value="Off" Category="DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="QuadplexMode" Text="Quadplex" Value="Off" Category="DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="SonoVF" Text="SonoVF" Value="" Category="DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" PriorityLevel="0"/>
    <MenuItem Name="PwColorMapIndex" Text="Chroma" Value="Type1" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="PWDynamicRangeShow" Text="Dynamic" Value="60" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="PwGrayCurveIndex" Text="2D Map" Value="Type1" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="SteeringAngle" Text="Steer" Value="0" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="QuickAngle" Text="Quick Angle" Value="0" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="DDisplayFormat" Text="DISP Format" Value="" Category="DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="WallFilterDop" Text="Wall Filter" Value="2" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="PWEnhanceShow" Text="Spectrum Enhance" Value="4" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="Volume" Text="Audio" Value="4" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <!--<MenuItem Name="FreqIndexDopStr" Text="Freq." Value="3.0M" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <!--<MenuItem Name="SpectralInvert" Text="Invert" Value="" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <!--<MenuItem Name="Threshold" Text="Threshold" Value="1" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <!--<MenuItem Name="DTraceSmooth" Text="DTrace Smooth" Value="2" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <!--<MenuItem Name="DTraceArea" Text="Trace Area" Value="" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"/>-->
    <!--<MenuItem Name="Utility" Text="Utility" Value="" Category="DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->

    <!--TVD PreMenu-->
    <MenuItem Name="DopplerThetaTDI" Text="Angle" Value="0" Category="TVD PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>

    <!--DTDIMenu-->
<!--    <MenuItem Name="FreqIndexTD"         Text="Freq."            Value=""       Category="DTDIMenu" ClickTool="false" EngineeringTool="true" ParameterTool="true" AdvanceTool="false"/>-->
    <MenuItem Name="PwTDIColorMapIndex"  Text="Chroma"           Value="type_1" Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="PWDynamicRangeTDI"   Text="Dynamic"          Value="60"     Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="PwTDIGrayCurveIndex" Text="2D Map"           Value="Type1"  Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/> 
    <MenuItem Name="VolumeTDI"           Text="Audio"            Value="4"      Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="SamplingValve"   Text="SV"               Value="0"      Category="DTDIMenu" ClickTool="true"  EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="QuickAngle"          Text="Quick Angle"      Value="0"      Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="DopplerThetaTDI"     Text="Angle"            Value="0"      Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="PWEnhanceTDI"        Text="Spectrum Enhance" Value="3"      Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <!--<MenuItem Name="Utility" Text="Utility" Value="" Category="DTDIMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->

    <!--CWDPreMenu-->
    <MenuItem Name="DopplerThetaCWD" Text="Angle" Value="0" Category="CW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>

    <!--CWDMenu-->
<!--    <MenuItem Name="TriplexMode" Text="Triplex" Value="Off" Category="CWMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
<!--    <MenuItem Name="QuadplexMode" Text="Quadplex" Value="Off" Category="CWMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>-->
    <MenuItem Name="CwdColorMapIndex" Text="Chroma" Value="type_1" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="PWDynamicRangeCWD" Text="Dynamic" Value="60" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="CwdGrayCurveIndex" Text="2D Map" Value="Type1" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="QuickAngle" Text="Quick Angle" Value="0" Category="CWMenu" ClickTool="fasle" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="WallFilterCWD" Text="Wall Filter" Value="2" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="PWEnhanceCWD" Text="Spectrum Enhance" Value="4" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <!-- <MenuItem Name="SpectralInvert" Text="Invert" Value="" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"  PostProcessTool="true"/> -->
    <!--<MenuItem Name="Utility" Text="Utility" Value="" Category="CWMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->

    <!--MMenu-->
<!--    <MenuItem Name="" Text="" Value="" Category="MMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->
    <MenuItem Name="FreeMMode" Text="Free M" Value="Off" Category="MMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"/>
    <MenuItem Name="IsUDBM" Text="Layout" Value="Off" Category="MMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>


    <!--ECGMenu-->
    <MenuItem Name="ECGEn" Text="ECG" Value="false" Category="ECGMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>

    <!--EMenu-->
    <MenuItem Name="ElastoExit" Text="Exit" Category="EMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>

    <!--Freeze-->
    <!--<MenuItem Name="StressEchoEn" Text="Protocol" Value="Off" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="fasle" Loop="true" PostProcessTool="true"/>-->
    <!--<MenuItem Name="StressEchoTemplate" Text="Template" Value="" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->
    <!--<MenuItem Name="StressEchoAnalyze" Text="Analyze" Value="" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->
    <!--<MenuItem Name="StressEchoT1Control" Text="T1" Value="" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>-->
    <!--<MenuItem Name="StressEchoT2Control" Text="T2" Value="" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>-->
    <MenuItem Name="SonoVF" Text="SonoVF" Value="" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" PriorityLevel="0"/>
    <MenuItem Name="AutoBLine" Text="B-Lines" Value="Off" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="SonoDiaph" Text="SonoDiaph" Value="" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <!-- <MenuItem Name="SonoCardiac" Text="SonoCardiac" Value="Off" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/> -->
    <MenuItem Name="Utility" Text="Utility" Value="" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>

    <!--SonoGuideMenu-->
    <MenuItem Name="SonoGuideSection" Text="Section" Value="0" Category="SonoGuideMenu" ClickTool="false" EngineeringTool="false" PopupTool="true" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="SonoGuideStep" Text="L/R" Value="0" Category="SonoGuideMenu" ClickTool="false" EngineeringTool="false" PopupTool="true" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="SonoGuideExit" Text="Exit" Value="" Category="SonoGuideMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>

    <!--Freeze3DMenu-->
    <MenuItem Name="Utility" Text="Utility" Value="" Category="Freeze3DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>

    <!--Utility-->
    <MenuItem Name="FullScreenEvent" Text="Full Screen" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="PostProcess" Text="Post Process" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="SlideShow" Text="Slide Show" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="SonoHelp" Text="SonoHelp" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="PresetMode" Text="Preset Mode" Value="" Category="Utility" ClickTool="true" EngineeringTool="true" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="OpenElementCheck" Text="EL Check" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>
    <MenuItem Name="SoundOutputTrigEnable" Text="TrigEnable AP" Value="Off" Category="Utility" ClickTool="true" EngineeringTool="true" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="AcousticPowerTestCode" Text="Triggered AP" Value="0" Category="Utility" ClickTool="false" EngineeringTool="true" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <!--<MenuItem Name="ExitFromUtility" Text="Cancel" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>-->

    <!--PostProcess-->
    <MenuItem Name="BColorMapIndexShow" Text="Chroma" Value="Type1" Category="PostProcess" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="BGrayCurveIndexShow" Text="2D Map" Value="Type1" Category="PostProcess" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="BGammaShow" Text="B Gamma" Value="4" Category="PostProcess" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="BRejectionShow" Text="B Rejection" Value="00" Category="PostProcess" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="ExitFromPostProcess" Text="Cancel" Value="" Category="PostProcess" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"/>

    <!--FreeMMenu-->
    <MenuItem Name="FreeMLineNum" Text="Line Number" Value="" Category="FreeMMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="FreeMAngle1" Text="Angle" Value="" Category="FreeMMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="ExitFreeM" Text="Exit" Value="" Category="FreeMMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="IsUDBM" Text="Layout" Value="Off" Category="FreeMMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>

    <!--MVIMenu-->
    <MenuItem Name="ShowBInROI"    Text="ShowBImage"   Value="On"     Category="MVIMenu" ClickTool="true"  EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="BMVIBImages" Text="B/BMVI" Value="On" Category="MVIMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="MVIType"       Text="Type"  Value=""      Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="MVIColorMapIndex"  Text="Color Map"    Value="1" Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
<!--    <MenuItem Name="OrderMVILineDensity" Text="Density" Value="Low" Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>-->
    <MenuItem Name="FrameAvgMVI"       Text="Persistence"  Value="0"      Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="PowerThresholdMVI" Text="Wall Thre." Value="2" Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="IsEnableAdjustROI" Text="I. Doppler" Value="Off" Category="MVIMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>

   <!--BiopsyMenu-->
   <MenuItem Name="BiopsyChoose" Text="Kit" Value="0" Category="BiopsyMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
   <MenuItem Name="BiopsyVerify" Text="Verify" Value="Off" Category="BiopsyMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
   <MenuItem Name="IsCenterLineVisible" Text="Center Line" Value="Off" Category="BiopsyMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
   <MenuItem Name="SonoNeedle" Text="SonoNeedle" Value="Off" Category="BiopsyMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
   <MenuItem Name="NeedleMode" Text="SuperNeedle" Value="false" Category="BiopsyMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
   <MenuItem Name="IsAutoNeedleAngleEnable" Text="AutoAngle" Value="Off" Category="BiopsyMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
   <MenuItem Name="NeedleAngleIndex" Text="Needle Angle" Value="0" Category="BiopsyMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
   <MenuItem Name="BiopsyCancel" Text="Cancel" Value="" Category="BiopsyMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>

    <!--SonoNerveMenu-->
    <MenuItem Name="ScalenusBP" Text="Scalenus BP" Value="" Category="SonoNerveMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="SupraclavicularBP" Text="Supraclavicular BP" Value="" Category="SonoNerveMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>

    <!--Curved-->
    <MenuItem Name="CurvedPanoExit" Text="Exit"    Value="" Category="Curved Panoramic" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"/>

    <!--SonoMSKMenu-->
    <MenuItem Name="BicepsLHTendonSA" Text="Biceps LH Tendon SA" Value="" Category="SonoMSKMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="BicepsLHTendonLA" Text="Biceps LH Tendon LA" Value="" Category="SonoMSKMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>

</Menu>
<Menu Type="BottomMenu">
    <!--BMenu-->
    <MenuItem Name="FreqIndexB" Text="Freq." Value="3.0M" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="DynamicRange" Text="Dynamic" Value="60" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="Focus" Text="Focus Pos." Value="" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="FrameAvg" Text="Persistence" Value="2" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="iImageShow" Text="Q-image" Value="2" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="4" Row="0" RowSpan="2"/>
     <MenuItem Name="ScpdOn" Text="Compound" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"
    Column="5" Row="0" RowSpan="2"/>

    <!--SonoCardiac Menu (same with B Menu)-->
    <MenuItem Name="FreqIndexB" Text="Freq." Value="3.0M" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="DynamicRange" Text="Dynamic" Value="60" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="Focus" Text="Focus Pos." Value="" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="FrameAvg" Text="Persistence" Value="2" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="iImageShow" Text="Q-image" Value="2" Category="BMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="4" Row="0" RowSpan="2"/>
     <MenuItem Name="ScpdOn" Text="Compound" Value="Off" Category="BMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"
    Column="5" Row="0" RowSpan="2"/>

    <!--Curved-->
    <MenuItem Name="BGainGeneral" Text="Gain" Value="" Category="Curved Panoramic" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="BColorMapIndexShow" Text="Chroma" Value="Type1" Category="Curved Panoramic" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="Curved Panoramic"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Curved Panoramic"
    Column="2" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Curved Panoramic"
    Column="3" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Curved Panoramic"
    Column="3" Row="1" RowSpan="1"/>
    <MenuItem Name="CurvedPanoExit" Text="Exit" Value="" Category="Curved Panoramic" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="Curved Panoramic"
    Column="5" Row="0" RowSpan="2"/>

    <!--CMenu-->
    <MenuItem Name="FreqIndexColor" Text="Freq." Value="3.0M" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="SampleRateDopShow" Text="PRF" Value="1122Hz" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <!--<MenuItem Name="FreqIndexColor" Text="Freq." Value="3.0M" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>-->
    <MenuItem Name="SteeringAngle" Text="Steer" Value="0" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="FrameAvgColor" Text="Persistence" Value="2" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="CfColorMapIndexShow" Text="Color Map" Value="type_1" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="BaseLineColor" Text="Baseline" Value="3" Category="CMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="5" Row="0" RowSpan="2"/>


    <!--CPAMenu-->
    <MenuItem Name="FreqIndexPD" Text="Freq." Value="3.0M" Category="CPAMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="SampleRateDopShow" Text="PRF" Value="1122Hz" Category="CPAMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <!--<MenuItem Name="FreqIndexPD" Text="Freq." Value="3.0M" Category="CPAMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>-->
    <MenuItem Name="SteeringAngle" Text="Steer" Value="0" Category="CPAMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="FrameAvgColor" Text="Persistence" Value="0" Category="CPAMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="DPDMode" Text="DPD" Value="" Category="CPAMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="CPAMenu"
    Column="5" Row="0" RowSpan="2"/>


    <!--SonoNeedleMenu-->
    <MenuItem Name="SNCVLT" Text="Sensitivity" Value="" Category="SonoNeedleMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="GainSN" Text="Brightness"  Category="SonoNeedleMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="ColorTransparency" Text="Transparency" Value="" Category="SonoNeedleMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="SonoNeedleExit" Text="Exit" Value="" Category="SonoNeedleMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Value="" Category="SonoNeedleMenu" Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="SonoNeedleMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--DPDMenu-->
    <MenuItem Name="FreqIndexPD" Text="Freq." Value="3.0M" Category="DPDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="SampleRateDopShow" Text="PRF" Value="1122Hz" Category="DPDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="SteeringAngle" Text="Steer" Value="0" Category="DPDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="FrameAvgColor" Text="Persistence" Value="2" Category="DPDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="PDMode" Text="CPA" Value="" Category="DPDMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="BaseLineDPD" Text="Baseline" Value="3" Category="DPDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="5" Row="0" RowSpan="2"/>


    <!--TDIMenu-->
    <MenuItem Name="CGainGeneral" Text="Gain" Value="0" Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Value="" Category="TDIMenu"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="TDIWallfilter" Text="Wall Filter" Value="2" Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="SampleRateDopTDI" Text="PRF" Value="1122Hz" Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="BaseLineTDI" Text="Baseline" Value="3" Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="FreqIndexTDI" Text="Freq." Value="3.0M" Category="TDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="5" Row="0" RowSpan="2"/>


    <!--TVD PreMenu-->
    <MenuItem Name="NULL" Text="" Category="TVD PreMenu"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="SamplingValve" Text="SV" Value="0" Category="TVD PreMenu" ClickTool="true  " EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="QuickAngle" Text="Quick Angle" Value="0" Category="TVD PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="TVD PreMenu"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="TVD PreMenu"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="TVD PreMenu"
    Column="5" Row="0" RowSpan="2"/>


    <!--DTDIMenu-->
    <MenuItem Name="DGainGeneral" Text="Gain" Value="0" Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="DSampleRateDopTDI" Text="PRF" Value="1122Hz" Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="BaseLineDTDI" Text="Baseline" Value="3" Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="DTDIDisplayFormat" Text="DISP Format" Value="" Category="DTDIMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="DVelocityTDI" Text="Speed" Value="2" Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="FreqIndexTD" Text="Freq." Value="" Category="DTDIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="5" Row="0" RowSpan="2"/>

    <!--PW PreMenu-->
    <MenuItem Name="DopplerTheta" Text="Angle" Value="0" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="SamplingValve" Text="SV" Value="0" Category="PW PreMenu" ClickTool="true  " EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="SteeringAngle" Text="Steer" Value="0" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="QuickAngle" Text="Quick Angle" Value="0" Category="PW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="PW PreMenu"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="PW PreMenu"
    Column="5" Row="0" RowSpan="2"/>


    <!--ECGMenu-->
    <MenuItem Name="ECGGain" Text="ECG Gain." Value="5" Category="ECGMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="ECGPos" Text="ECG Pos." Value="6" Category="ECGMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="ECGVelocity" Text="ECG Vel." Value="1" Category="ECGMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="ECGInvert" Text="ECG Invert" Value="" Category="ECGMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="ECGMenu"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="ECGMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--DMenu-->
    <MenuItem Name="FreqIndexDop" Text="Freq." Value="" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="DSampleRateDopShow" Text="PRF" Value="2" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="BaseLine" Text="Baseline" Value="3" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="SamplingValve" Text="SV" Value="0" Category="DMenu" ClickTool="true  " EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="DVelocity" Text="Speed" Value="2" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="DopplerTheta" Text="Angle" Value="0" Category="DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="5" Row="0" RowSpan="2"/>

    <!--CWDPreMenu-->
    <MenuItem Name="DopplerThetaCWD" Text="Angle" Value="0" Category="CW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="QuickAngle" Text="Quick Angle" Value="0" Category="CW PreMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="CW PreMenu"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="CW PreMenu"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="CW PreMenu"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="CW PreMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--CWDMenu-->
    <MenuItem Name="DopplerThetaCWD" Text="Angle" Value="0" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="CWDSampleRate" Text="PRF" Value="2" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="BaseLineCWD" Text="Baseline" Value="3" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="DDisplayFormat" Text="DISP Format" Value="" Category="CWMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="CWDVelocity" Text="Speed" Value="2" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="Volume" Text="Audio" Value="4" Category="CWMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="5" Row="0" RowSpan="2"/>

    <!-- MMenu-->
    <MenuItem Name="IsUDBM" Text="Layout" Value="Off" Category="MMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="MGrayCurveIndex" Text="2D Map" Value="type_1" Category="MMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="MColorMapIndex" Text="Chroma" Value="type_1" Category="MMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="MDisplayFormat" Text="DISP Format" Value="" Category="MMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"
    Column="3" Row="0" RowSpan="2"/>
    <!--<MenuItem Name="MDynamicRange" Text="Dynamic" Value="60" Category="MMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="4" Row="0" RowSpan="2"/>-->
    <MenuItem Name="MVelocity" Text="Speed" Value="1" Category="MMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="MMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!-- FreeMMenu-->
    <MenuItem Name="NULL" Text="" Category="FreeMMenu"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="MGrayCurveIndex" Text="2D Map" Value="type_1" Category="FreeMMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="MColorMapIndex" Text="Chroma" Value="type_1" Category="FreeMMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="MDisplayFormat" Text="DISP Format" Value="" Category="FreeMMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="FreeMVelocity" Text="Speed" Value="1" Category="FreeMMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="FreeMMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!-- EMenu-->
    <MenuItem Name="FreqIndexElasto" Text="Freq." Value="3.0M" Category="EMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="EGrayCurveIndex" Text="E 2D Map" Value="type_1" Category="EMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="EMenu"
    Column="1" Row="0" RowSpan="1"/>
    <MenuItem Name="ElastoTransparency" Text="Transparency" Value="95" Category="EMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="EMenu"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="ElastoColorMapIndex" Text="Chroma" Value="type_1" Category="EMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="2" Row="1" RowSpan="1"/>
    <MenuItem Name="ElastoInvertState" Text="Color Invert" Value="On" Category="EMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"
    Column="3" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="EMenu"
    Column="4" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="EMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--Freeze-->
    <MenuItem Name="CinePlay" Text="Play/Pause" Value="" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="ResetBufferIndex" Text="Reset Ranges" Value="" Category="Freeze" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="CinePlaySpeedAdjust" Text="Play Speed" Value="3" Category="Freeze" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="CineNavigate" Text="Next/Prev" Value="" Category="Freeze" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" FreezeSendTool="true"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="Freeze"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="Freeze"
    Column="5" Row="0" RowSpan="2"/>

    <!--Freeze3DMenu-->
    <MenuItem Name="CinePlay" Text="Play/Pause" Value="" Category="Freeze3DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="CinePlaySpeedAdjust" Text="Play Speed" Value="3" Category="Freeze3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="StartBufferIndex" Text="Set Start Pos" Value="" Category="Freeze3DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="1" Row="0" RowSpan="1"/>
    <MenuItem Name="BufferIndexStartPos" Text="Start Pos" Value="" Category="Freeze3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"  PostProcessTool="true"
    Column="1" Row="1" RowSpan="1"/>
    <MenuItem Name="EndBufferIndex" Text="Set End Pos" Value="" Category="Freeze3DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="BufferIndexEndPos" Text="End Pos" Value="" Category="Freeze3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="2" Row="1" RowSpan="1"/>
    <MenuItem Name="FreeHand3D" Text="HD 3D" Value="" Category="Freeze3DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"
    Column="3" Row="0" RowSpan="1"/>
    <MenuItem Name="FreeHand3DRoiOn" Text="3D ROI" Value = "off" Category="Freeze3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"
    Column="3" Row="1" RowSpan="1" Loop="true"/>
    <MenuItem Name="FreezeImageSend" Text="Send" Value="" Category="Freeze3DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"
    Column="4" Row="0" RowSpan="1"/>
    <MenuItem Name="FreezeImageGeneralPage" Text="Next/Prev" Value="" Category="Freeze3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="false" AdvanceTool="false" PostProcessTool="true"
    Column="4" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Freeze3DMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--MVIMenu-->
    <MenuItem Name="CGainGeneral" Text="Gain" Value="0" Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="SteeringAngle" Text="Steer" Value="0" Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="WallFilterMVI" Text="Wall Filter" Value="2" Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="SampleRateDopMVI" Text="PRF" Value="1122Hz" Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="BaseLineMVI" Text="Baseline" Value="3" Category="MVIMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="MVIMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--SonoNerveMenu-->
    <MenuItem Name="SonoNerveIsShow" Text="Display" Category="SonoNerveMenu" Value="On" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="SonoNerveTrans" Text="Transparency" Category="SonoNerveMenu" Value="50" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="SonoNerveMenu"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="SonoNerveMenu"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="quitSonoNerve" Text="Exit" Value="" Category="SonoNerveMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="SonoNerveMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--SonoMSKMenu-->
    <MenuItem Name="SonoMSKIsShow" Text="Display" Category="SonoMSKMenu" Value="On" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="SonoMSKTrans" Text="Transparency" Category="SonoMSKMenu" Value="50" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="SonoMSKMenu"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="SonoMSKMenu"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="quitSonoMSK" Text="Exit" Value="" Category="SonoMSKMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="SonoMSKMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--Utility-->
    <!--<MenuItem Name="NULL" Text="" Category="Utility"
    Column="0" Row="0" RowSpan="1"/>-->
    <MenuItem Name="ExamModeChange" Text="Preset" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="ExamModeRename" Text="Rename" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="1" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Utility"
    Column="1" Row="1" RowSpan="1"/>
    <MenuItem Name="ExamModeLoad" Text="Load Preset" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Utility"
    Column="2" Row="1" RowSpan="1"/>
    <MenuItem Name="ExamModeSave" Text="Save" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="3" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Utility"
    Column="3" Row="1" RowSpan="1"/>
    <MenuItem Name="ExamModeSaveAs" Text="Save As" Value="" Category="Utility" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="4" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Utility"
    Column="4" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Utility"
    Column="5" Row="0" RowSpan="2"/>

    <!--Measurement-->
    <MenuItem Name="MeasureResultFontLoadDefaultSize" Text="Reset Font" Value="" Category="Measurement" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="ResetMeasResultPos" Text="Reset Result Pos" Value="" Category="Measurement" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="CineNavigate" Text="Select Frame" Value="" Category="Measurement" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2" PostProcessTool="true"/>
    <MenuItem Name="MeasureResultFontSize" Text="Font Size" Value="14" Category="Measurement" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2" PostProcessTool="true"/>
    <MenuItem Name="MeasurementDeleteLastOne" Text="Delete" Value="" Category="Measurement" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="Measurement"
    Column="5" Row="0" RowSpan="2"/>

    <!--AutoEF-->
    <MenuItem Name="EDFrame" Text="ED Frame" Category="AutoEF" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" FreezeSendTool="true"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="ESFrame" Text="ES Frame" Category="AutoEF" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" FreezeSendTool="true"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="CineNavigate" Text="Next/Pre" Category="AutoEF" Value="50" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true" FreezeSendTool="true"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="AutoEF"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="AutoEF"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="AutoEF"
    Column="5" Row="0" RowSpan="2"/>

    <!--BodyMark-->
    <MenuItem Name="RotateBodyMark" Text="Rotate" Value="" Category="BodyMark" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="RotateBodyMark" Text="Rotate" Value="" Category="BodyMark" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="BodyMark"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="BodyMark"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="BodyMark"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="BodyMark"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="BodyMark"
    Column="5" Row="0" RowSpan="2"/>

    <!--Comment-->
    <MenuItem Name="CommentFontLoadDefault" Text="Reset Font Size" Value="" Category="Comment" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="0" Row="0" RowSpan="2"/>
    <MenuItem Name="SaveLoadCommentPos" Text="Set" Value="" Category="Comment" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>>
    <MenuItem Name="ToggleSaveLoadCommentPos" Text="Home Pos." Value="Set" Category="Comment" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="CommentFontSizeChange" Text="Font Size" Value="14" Category="Comment" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2" PostProcessTool="true"/>
    <MenuItem Name="CommentMenuPageChange" Text="Page" Value="0/0" Category="Comment" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="Comment"
    Column="5" Row="0" RowSpan="2"/>

    <!--Fast Measure-->
    <MenuItem Name="DistanceMeasure" Text="Distance" Value="" Category="Fast Measure" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="DistanceMeasure" Text="Distance" Value="" Category="Fast Measure" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="TraceMeasure" Text="Trace" Value="" Category="Fast Measure" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="1" Row="0" RowSpan="1"/>
    <MenuItem Name="TraceMeasure" Text="Trace" Value="" Category="Fast Measure" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="1" Row="1" RowSpan="1"/>
    <MenuItem Name="VolumeMeasure" Text="Volume" Value="" Category="Fast Measure" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="VolumeMeasure" Text="Volume" Value="" Category="Fast Measure" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="2" Row="1" RowSpan="1"/>
    <MenuItem Name="MeasureResultFontSize" Text="Font Size" Value="14" Category="Fast Measure" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2" PostProcessTool="true"/>
    <MenuItem Name="MeasurementDeleteLastOne" Text="Delete" Value="" Category="Fast Measure" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="Fast Measure"
    Column="5" Row="0" RowSpan="2"/>

    <!--Arrow-->
    <MenuItem Name="RotateArrow" Text="Angle" Category="Arrow" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="RotateArrow" Text="Angle" Category="Arrow" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="ArrowChange" Text="Cursor" Value="Arrow" Category="Arrow" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="ArrowScaleLoadDefault" Text="Reset Size" Category="Arrow" ClickTool="true" EngineeringTool="false" ParameterTool="false" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="ArrowScale" Text="Size" Value="1" Category="Arrow" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="Angle" Category="Arrow" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="Arrow"
    Column="5" Row="0" RowSpan="2"/>

    <!--BiopsyMenu-->
    <MenuItem Name="BiopsyAngle" Text="Angle" value="" Category="BiopsyMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="BiopsyAngle" Text="Angle" value="" Category="BiopsyMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="BiopsyXPosMM" Text="Position" Category="BiopsyMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="2"/>
    <MenuItem Name="BiopsySave" Text="Save" Category="BiopsyMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="2"/>
    <MenuItem Name="BiopsyRestore" Text="Restore" Category="BiopsyMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="2"/>
    <MenuItem Name="BiopsyExit" Text="Exit" Category="BiopsyMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="2"/>
    <MenuItem Name="NULL" Text="" Category="BiopsyMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--4DMenu-->
    <MenuItem Name="FourDSmooth" Text="Smooth" Value="IE1" Category="4DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDThreshold" Text="Threshold" Value="16" Category="4DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDRender" Text="Render" Value="Surface" Category="4DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDZoom" Text="Zoom" Value="" Category="4DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDFrameRate" Text="FrameRate" Value="Normal" Category="4DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDPalette" Text="Palette" Value="1" Category="4DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="4DMenu"
    Column="3" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDDynamicRange" Text="Dynamic" Value="60" Category="4DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="4DMenu"
    Column="4" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDDirectionSet" Text="Direction Set" Value="0" Category="4DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="1" RowSpan="1" Loop="true"/>
    <MenuItem Name="NULL" Text="" Category="4DMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--4DHDMenu-->
    <MenuItem Name="FourDSmooth" Text="Smooth" Value="IE1" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDThreshold" Text="Threshold" Value="16" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDRender" Text="Render" Value="Surface" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDZoom" Text="Zoom" Value="" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDFrameRate" Text="FrameRate" Value="Normal" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDPalette" Text="Palette" Value="1" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDLight" Text="Light" Value="0" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDDynamicRange" Text="Dynamic" Value="60" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDVirtualHDOn" Text="VirtualHD" Value="Off" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDDirectionSet" Text="Direction Set" Value="0" Category="4DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="1" RowSpan="1" Loop="true"/>
    <MenuItem Name="NULL" Text="" Category="4DHDMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--HD 3DMenu-->
    <MenuItem Name="NULL" Text="" Category="HD 3DMenu"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="HD 3DMenu"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="HD 3DMenu"
    Column="1" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="HD 3DMenu"
    Column="1" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="HD 3DMenu"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="HD 3DMenu"
    Column="2" Row="1" RowSpan="1"/>
    <!--<MenuItem Name="FreeHand3DExit" Text="Exit" Category="HD 3DMenu" ClickTool="true" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="1"/>-->
    <MenuItem Name="NULL" Text="" Category="HD 3DMenu"
    Column="3" Row="0" RowSpan="1"/>
    <MenuItem Name="FreeHand3DVolmnSpacing" Text="Vol Spacing" Value="0.32f" Category="HD 3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="1" RowSpan="1"/>
    <MenuItem Name="FreeHand3DReset" Text="Reset" Value="" Category="HD 3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="1"/>
    <MenuItem Name="FreeHand3DZoom" Text="Zoom" Value="" Category="HD 3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="HD 3DMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--Static3DMenu-->
    <MenuItem Name="FourDSmooth" Text="Smooth" Value="IE1" Category="Static3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDThreshold" Text="Threshold" Value="16" Category="Static3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDRender" Text="Render" Value="Surface" Category="Static3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDZoom" Text="Zoom" Value="" Category="Static3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDFrameRate" Text="FrameRate" Value="Normal" Category="Static3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDPalette" Text="Palette" Value="1" Category="Static3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Static3DMenu"
    Column="3" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDDynamicRange" Text="Dynamic" Value="60" Category="Static3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="1" RowSpan="1"/>
    <MenuItem Name="NULL" Text="" Category="Static3DMenu"
    Column="4" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDDirectionSet" Text="Direction Set" Value="0" Category="Static3DMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="1" RowSpan="1" Loop="true"/>
    <MenuItem Name="NULL" Text="" Category="Static3DMenu"
    Column="5" Row="0" RowSpan="2"/>

    <!--Static3DHDMenu-->
    <MenuItem Name="FourDSmooth" Text="Smooth" Value="IE1" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDThreshold" Text="Threshold" Value="16" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="0" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDRender" Text="Render" Value="Surface" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDZoom" Text="Zoom" Value="" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="1" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDFrameRate" Text="FrameRate" Value="Normal" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDPalette" Text="Palette" Value="1" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="2" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDLight" Text="Light" Value="0" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDDynamicRange" Text="Dynamic" Value="60" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="3" Row="1" RowSpan="1"/>
    <MenuItem Name="FourDVirtualHDOn" Text="VirtualHD" Value="Off" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="0" RowSpan="1"/>
    <MenuItem Name="FourDDirectionSet" Text="Direction Set" Value="0" Category="Static3DHDMenu" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"
    Column="4" Row="1" RowSpan="1" Loop="true"/>
    <MenuItem Name="NULL" Text="" Category="Static3DHDMenu"
    Column="5" Row="0" RowSpan="2"/>

</Menu>
<Menu Type="Command">
    <MenuItem Name="Gain" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="GainThi" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="GainColor" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="GainPD" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="GainTDI" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="GainMVI" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="GainDop" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="GainDopTM" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="GainDopTDI" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="GainDopCWD" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="MGain" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="MGainThi" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PostProcessTool="true"/>
    <MenuItem Name="DopplerTheta" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>
    <MenuItem Name="DopplerThetaTDI" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>
    <MenuItem Name="DopplerThetaCWD" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <MenuItem Name="FourDGain" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false"/>
    <MenuItem Name="IsMenuVisible" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PriorityLevel="0"/>
    <MenuItem Name="FullScreen" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true"/>
    <!--<MenuItem Name="DynamicRange" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" PriorityLevel="0"/>-->
    <MenuItem Name="Left" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>
    <MenuItem Name="Up" Text="" Value="" Category="" ClickTool="false" EngineeringTool="false" ParameterTool="true" AdvanceTool="false" Loop="true" PostProcessTool="true"/>
</Menu>
</MenuTools>
