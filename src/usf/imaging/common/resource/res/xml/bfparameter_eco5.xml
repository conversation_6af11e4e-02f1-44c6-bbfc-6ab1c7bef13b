<?xml version="1.0" encoding="UTF-8"?>
<!--ValueType 如果类型不是int，必须注明类型，目前支持Bool,Int,Double,List,String ,ByteArray,Size,
ControlTable的Bool类型必须注明 TrueValue-->
<BFParameter Version="0.1" ParameterVersion="0.1">
<BFParameterGroup Type="ControlTable" ControlDataLen="454">
<Parameter Name="Freeze" BitCount="1" StartBit="0" StartByte="1"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="FrequencyCompounding" BitCount="1" StartBit="1" StartByte="1"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="FrequencyCompoundingOther" BitCount="1" StartBit="1" StartByte="1"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsDirectValue="true"/>
<Parameter Name="Socket" BitCount="3" StartBit="2" StartByte="1" IsPresetParameter="false"/>
<Parameter Name="ProbeCode" BitCount="3" StartBit="5" StartByte="1"
        HighBitCount="1" HighStartBit="0" HighStartByte="8" IsPresetParameter="false"/>
<Parameter Name="FreqIndexColor" BitCount="3" StartBit="0" StartByte="2" Min="0" Max="3" IsPresetParameter="true"/>
<!-- 0:半间距，高密度 1:整间距，低密度。初始化应为1-低密度-->
<Parameter Name="ColorLineDensity" BitCount="1" StartBit="3" StartByte="2"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="SteeringAngle" BitCount="3" StartBit="4" StartByte="2" IsPresetParameter="true"/>
<Parameter Name="Linear" BitCount="1" StartBit="7" StartByte="2"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumB" BitCount="2" StartBit="0" StartByte="3" IsPresetParameter="true"/>
<Parameter Name="FocusPosB" BitCount="4" StartBit="2" StartByte="3" IsPresetParameter="true"/>
<Parameter Name="FocusNumM" BitCount="2" StartBit="0" StartByte="3" Min="0" Max="0" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="FocusPosM" BitCount="4" StartBit="2" StartByte="3" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="Deep" BitCount="1" StartBit="6" StartByte="3"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
<Parameter Name="HalfHeight" BitCount="1" StartBit="7" StartByte="3"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ImageMode" BitCount="4" StartBit="0" StartByte="4" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="SyncMode" BitCount="3" StartBit="4" StartByte="4" IsPresetParameter="false"/>
<Parameter Name="HighDensity" BitCount="1" StartBit="7" StartByte="4"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="CQYZ" BitCount="6" StartBit="0" StartByte="5" IsPresetParameter="true"/>
<Parameter Name="MVelocity" BitCount="2" StartBit="6" StartByte="5" IsPresetParameter="true"/>
<Parameter Name="DVelocity" BitCount="2" StartBit="6" StartByte="5" Max="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="ColorImageMode" BitCount="3" StartBit="0" StartByte="6" IsPresetParameter="false"/>
<Parameter Name="FreqSpectrum" BitCount="1" StartBit="3" StartByte="6"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="LowVelocityBlood" BitCount="1" StartBit="4" StartByte="6"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
<Parameter Name="BaseLine" BitCount="3" StartBit="5" StartByte="6" Max="6" IsPresetParameter="true"/>
<Parameter Name="GainColor" BitCount="8" StartBit="0" StartByte="7" IsPresetParameter="true"/>
<Parameter Name="CET" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true"/>
<Parameter Name="PDCET" BitCount="4" StartBit="1" StartByte="8" DefaultValue="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TNR" BitCount="1" StartBit="5" StartByte="8" IsPresetParameter="true"/>
<Parameter Name="TNFtr" BitCount="1" StartBit="6" StartByte="8" IsPresetParameter="true"/>
<Parameter Name="ConfigDone" BitCount="1" StartBit="7" StartByte="8"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="DynamicRange" BitCount="4" StartBit="0" StartByte="9" IsPresetParameter="true"/>
<Parameter Name="FourD" BitCount="1" StartBit="4" StartByte="9"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FrameAvg" BitCount="3" StartBit="5" StartByte="9" IsPresetParameter="true"/>
<Parameter Name="Edge" BitCount="3" StartBit="0" StartByte="10" IsPresetParameter="true"/>
<Parameter Name="AccCountColor" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="false"/>
<Parameter Name="AccCountDop" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="false" IsDirectValue="true"/>
<Parameter Name="SampleRateDop" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true"/>
<Parameter Name="DSampleRateDop" BitCount="4" StartBit="4" StartByte="11" IsPresetParameter="true"/>
<Parameter Name="WallFilterDop" BitCount="2" StartBit="0" StartByte="12" IsPresetParameter="true"/>
<Parameter Name="WallFilterColor" BitCount="2" StartBit="2" StartByte="12" IsPresetParameter="true"/>
<Parameter Name="WallThreshold" BitCount="4" StartBit="4" StartByte="12" Max="14" IsPresetParameter="true"/>
<Parameter Name="SampleVolume" BitCount="3" StartBit="0" StartByte="13" IsPresetParameter="true"/>
<Parameter Name="TriplexMode" BitCount="1" StartBit="3" StartByte="13"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FrameAvgColor" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true"/>
<Parameter Name="CVRT" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true"/>
<Parameter Name="PDCVRT" BitCount="4" StartBit="0" StartByte="14" DefaultValue="4" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="Scpd" BitCount="2" StartBit="4" StartByte="14" Max="2" IsPresetParameter="true"/>
<Parameter Name="ScpdTrape" BitCount="2" StartBit="4" StartByte="14" Max="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="ScpdOther" BitCount="2" StartBit="4" StartByte="14" Value="0" IsPresetParameter="false" IsDirectValue="true"/>
<Parameter Name="AcousticPowerTestCode" BitCount="2" StartBit="6" StartByte="14" IsPresetParameter="false"/>
<Parameter Name="MScanLine" BitCount="8" StartBit="0" StartByte="15" IsPresetParameter="true"/>
<Parameter Name="DScanLine" BitCount="8" StartBit="0" StartByte="15" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="StartLine" BitCount="8" StartBit="0" StartByte="16" IsPresetParameter="true"/>
<Parameter Name="StopLine" BitCount="8" StartBit="0" StartByte="17" IsPresetParameter="true"/>
<Parameter Name="StartLineColor" BitCount="8" StartBit="0" StartByte="18" IsPresetParameter="false"/>
<Parameter Name="StopLineColor" BitCount="8" StartBit="0" StartByte="19" IsPresetParameter="false"/>
<Parameter Name="TopBorderColor" BitCount="8" StartBit="0" StartByte="20" Max="511" IsPresetParameter="false"/>
<Parameter Name="BottomBorderColor" BitCount="8" StartBit="0" StartByte="21" Max="511" IsPresetParameter="false"/>
<Parameter Name="SampleDepthDop" BitCount="8" StartBit="0" StartByte="22" IsPresetParameter="false"/>
<Parameter Name="AngleSpacing" BitCount="8" StartBit="0" StartByte="23" IsPresetParameter="false"/>
<Parameter Name="LineSpacing" BitCount="8" StartBit="0" StartByte="24"
        HighBitCount="8" HighStartBit="0" HighStartByte="25" IsPresetParameter="false"/>
<Parameter Name="Gain" BitCount="8" StartBit="0" StartByte="26" IsPresetParameter="true"/>
<Parameter Name="TGC1" BitCount="8" StartBit="0" StartByte="27" IsPresetParameter="false"/>
<Parameter Name="TGC2" BitCount="8" StartBit="0" StartByte="28" IsPresetParameter="false"/>
<Parameter Name="TGC3" BitCount="8" StartBit="0" StartByte="29" IsPresetParameter="false"/>
<Parameter Name="TGC4" BitCount="8" StartBit="0" StartByte="30" IsPresetParameter="false"/>
<Parameter Name="TGC5" BitCount="8" StartBit="0" StartByte="31" IsPresetParameter="false"/>
<Parameter Name="TGC6" BitCount="8" StartBit="0" StartByte="32" IsPresetParameter="false"/>
<Parameter Name="TGC7" BitCount="8" StartBit="0" StartByte="33" IsPresetParameter="false"/>
<Parameter Name="TGC8" BitCount="8" StartBit="0" StartByte="34" IsPresetParameter="false"/>
<Parameter Name="SlopeDis" BitCount="8" StartBit="0" StartByte="35"
        HighBitCount="4" HighStartBit="4" HighStartByte="37" IsPresetParameter="false"/>
<Parameter Name="PerpendicularDis" BitCount="8" StartBit="0" StartByte="36"
        HighBitCount="4" HighStartBit="0" HighStartByte="37" IsPresetParameter="false"/>
<Parameter Name="ZoomDepth" BitCount="8" StartBit="0" StartByte="38"
        HighBitCount="8" HighStartBit="0" HighStartByte="39" IsPresetParameter="false"/>
<Parameter Name="FreqIndexB" BitCount="3" StartBit="0" StartByte="40" IsPresetParameter="true"/>
<Parameter Name="TransferSignal" BitCount="1" StartBit="3" StartByte="40"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="SpectralInvert" BitCount="1" StartBit="4" StartByte="40"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="Smooth" BitCount="3" StartBit="5" StartByte="40" IsPresetParameter="true"/>
<Parameter Name="FocusNumC" BitCount="2" StartBit="0" StartByte="41" Min="0" Max="0" IsPresetParameter="true"/>
<Parameter Name="FocusPosC" BitCount="4" StartBit="2" StartByte="41" IsPresetParameter="true"/>
<Parameter Name="Deep2" BitCount="1" StartBit="6" StartByte="41"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
<Parameter Name="HalfHeight2" BitCount="1" StartBit="7" StartByte="41"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="GainDop" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true"/>
<Parameter Name="Volume" BitCount="4" StartBit="0" StartByte="43" IsPresetParameter="true"/>
<Parameter Name="MB" BitCount="1" StartBit="4" StartByte="43"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="BStearingAngle" BitCount="2" StartBit="6" StartByte="43" IsPresetParameter="true"/>
<Parameter Name="FreqIndexFrequencyCompounding" BitCount="3" StartBit="0" StartByte="44" IsPresetParameter="true"/>
<Parameter Name="CRCOpenFlag" BitCount="1" StartBit="3" StartByte="44"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FocusPosFrequencyCompounding" BitCount="4" StartBit="4" StartByte="44" IsPresetParameter="true"/>
<Parameter Name="AcousticPowerB" BitCount="8" StartBit="0" StartByte="45" Max="15" Step="1" IsPresetParameter="true"/>
<Parameter Name="AcousticPowerColor" BitCount="8" StartBit="0" StartByte="45" Max="10" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AcousticPowerDop" BitCount="8" StartBit="0" StartByte="45" Max="10" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
<!--FPGA upgrade-->
<Parameter Name="DspEn" BitCount="1" StartBit="0" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="DbfEn" BitCount="1" StartBit="1" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="PwrEn" BitCount="1" StartBit="2" StartByte="46" IsPresetParameter="false"/>
<Parameter Name="UsbEn" BitCount="1" StartBit="3" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="Prog" BitCount="1" StartBit="4" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="CFGStart" BitCount="1" StartBit="5" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="BEStart" BitCount="1" StartBit="6" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="WRClr" BitCount="1" StartBit="7" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="LCDLight" BitCount="8" StartBit="0" StartByte="47" Min="30" Max="100" Value="61" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="CVLT" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true"/>
<Parameter Name="PDCVLT" BitCount="7" StartBit="0" StartByte="48" DefaultValue="4" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="ExceedLowBlood" BitCount="1" StartBit="7" StartByte="48" IsPresetParameter="false"/>
<!--true:定时DSC，false:整帧DSC-->
<Parameter Name="DSC" BitCount="1" StartBit="0" StartByte="49"
        ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="CFVelocityThreshold" BitCount="3" StartBit="1" StartByte="49" IsPresetParameter="true"/>
<Parameter Name="StateClearFlag" BitCount="1" StartBit="4" StartByte="49"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ColorRegionThreshold" BitCount="2" StartBit="5" StartByte="49" IsPresetParameter="true"/>
<Parameter Name="WrReset" BitCount="1" StartBit="7" StartByte="49"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CHET" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true"/>
<Parameter Name="PDCHET" BitCount="7" StartBit="0" StartByte="50" DefaultValue="60" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WT" BitCount="1" StartBit="7" StartByte="50" Value = "1" IsPresetParameter="true"/>
<Parameter Name="CTGC" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true"/>
<Parameter Name="PDCTGC" BitCount="8" StartBit="0" StartByte="51" DefaultValue="105" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="BCosAngle" BitCount="8" StartBit="0" StartByte="52"
                    HighBitCount="8" HighStartBit="0" HighStartByte="53" IsPresetParameter="false"/>
<Parameter Name="BTgAngle" BitCount="8" StartBit="0" StartByte="54"
                    HighBitCount="8" HighStartBit="0" HighStartByte="55" IsPresetParameter="false"/>
<!--Fpga调试信息用-->
<!--DSP复位-->
<Parameter Name="DSPReset" BitCount="1" StartBit="0" StartByte="56"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="DBFReset" BitCount="1" StartBit="1" StartByte="56"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<!--版本/状态切换 -->
<Parameter Name="DBFInfo" BitCount="2" StartBit="2" StartByte="56" IsPresetParameter="false"/>
<!--测试使能-->
<Parameter Name="TestSignal" BitCount="1" StartBit="5" StartByte="56"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<!--测试参数-->
<Parameter Name="TestParameter" BitCount="2" StartBit="6" StartByte="56" IsPresetParameter="false"/>
<!--发射脉冲数-->
<Parameter Name="BPulseNum" BitCount="2" StartBit="0" StartByte="57" Value="1" IsPresetParameter="false"/>
<Parameter Name="CPulseNum" BitCount="3" StartBit="2" StartByte="57" Value="1" IsPresetParameter="false"/>
<Parameter Name="DPulseNum" BitCount="3" StartBit="5" StartByte="57" Value="1" IsPresetParameter="true"/>
<!--血流效果-->
<Parameter Name="BloodEffection" BitCount="1" StartBit="0" StartByte="58" IsPresetParameter="true"/>
<Parameter Name="LogCompression" BitCount="3" StartBit="2" StartByte="58" IsPresetParameter="false"/>
<!--Parameter Name="PA-S" BitCount="1" StartBit="5" StartByte="58" IsPresetParameter="true"/-->
<Parameter Name="PhaseProbeId" BitCount="1" StartBit="6" StartByte="58"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="THI" BitCount="1" StartBit="7" StartByte="58" ValueType="Bool" Value="false" TrueValue="1"  IsPresetParameter="true"/>
<Parameter Name="ImageZoomCoefBin" BitCount="8" StartBit="0" StartByte="59"
                HighBitCount="8" HighStartBit="0" HighStartByte="60" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc00" BitCount="8" StartBit="0" StartByte="61" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc01" BitCount="8" StartBit="0" StartByte="62" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc02" BitCount="8" StartBit="0" StartByte="63" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc03" BitCount="8" StartBit="0" StartByte="64" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc04" BitCount="8" StartBit="0" StartByte="65" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc05" BitCount="8" StartBit="0" StartByte="66" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc06" BitCount="8" StartBit="0" StartByte="67" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc07" BitCount="8" StartBit="0" StartByte="68" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc08" BitCount="8" StartBit="0" StartByte="69" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc09" BitCount="8" StartBit="0" StartByte="70" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc10" BitCount="8" StartBit="0" StartByte="71" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc11" BitCount="8" StartBit="0" StartByte="72" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc12" BitCount="8" StartBit="0" StartByte="73" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc13" BitCount="8" StartBit="0" StartByte="74" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc14" BitCount="8" StartBit="0" StartByte="75" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc15" BitCount="8" StartBit="0" StartByte="76" IsPresetParameter="false"/>
<Parameter Name="DebugPara0" BitCount="8" StartBit="0" StartByte="77" IsPresetParameter="false"/>
<Parameter Name="DebugPara3" BitCount="4" StartBit="0" StartByte="78" IsPresetParameter="false"/>
<Parameter Name="DebugPara2" BitCount="3" StartBit="4" StartByte="78" IsPresetParameter="false"/>
<Parameter Name="DebugPara1" BitCount="1" StartBit="7" StartByte="78" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc0" BitCount="8" StartBit="0" StartByte="79" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc1" BitCount="8" StartBit="0" StartByte="80" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc2" BitCount="8" StartBit="0" StartByte="81" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc3" BitCount="8" StartBit="0" StartByte="82" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc4" BitCount="8" StartBit="0" StartByte="83" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc5" BitCount="8" StartBit="0" StartByte="84" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc6" BitCount="8" StartBit="0" StartByte="85" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc7" BitCount="8" StartBit="0" StartByte="86" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit1" BitCount="3" StartBit="0" StartByte="87"
                            HighBitCount="1" HighStartBit="7" HighStartByte="87" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit1" BitCount="1" StartBit="3" StartByte="87" IsPresetParameter="false"/>
<!-- //发射脉冲数有2位变成3位-->
<Parameter Name="PulseNumOfTransmit1" BitCount="3" StartBit="4" StartByte="87" Value="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit1" BitCount="2" StartBit="0" StartByte="88" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit1" BitCount="4" StartBit="4" StartByte="88" IsPresetParameter="false"/>
<Parameter Name="VHSi" BitCount="3" StartBit="0" StartByte="89" IsPresetParameter="true"/>
<Parameter Name="Left" BitCount="1" StartBit="0" StartByte="90"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="Up" BitCount="1" StartBit="1" StartByte="90"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="MBColor" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="false"/>
<Parameter Name="ShutDown" BitCount="1" StartBit="5" StartByte="90"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="NotReduceGain" BitCount="1" StartBit="6" StartByte="90"
        ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="ZoomOn" BitCount="1" StartBit="7" StartByte="90"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit2" BitCount="3" StartBit="0" StartByte="91"
                            HighBitCount="1" HighStartBit="7" HighStartByte="91" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit2" BitCount="1" StartBit="3" StartByte="91" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit2" BitCount="3" StartBit="4" StartByte="91" Value="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit2" BitCount="2" StartBit="0" StartByte="92" IsPresetParameter="false"/>
<Parameter Name="Rotation" Value="0" Max="270" Step="90" BitCount="2" StartBit="2" StartByte="92" IsPresetParameter="true"/>
<Parameter Name="FilterCoefOfTransmit2" BitCount="4" StartBit="4" StartByte="92" IsPresetParameter="false"/>
<Parameter Name="Alpha" BitCount="8" StartBit="0" StartByte="93" IsPresetParameter="false"/>
<Parameter Name="Beta" BitCount="8" StartBit="0" StartByte="94" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit3" BitCount="3" StartBit="0" StartByte="95"
                            HighBitCount="1" HighStartBit="7" HighStartByte="95" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit3" BitCount="1" StartBit="3" StartByte="95" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit3" BitCount="3" StartBit="4" StartByte="95" Value="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit3" BitCount="2" StartBit="0" StartByte="96" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit3" BitCount="4" StartBit="4" StartByte="96" IsPresetParameter="false"/>
<Parameter Name="Gamma" BitCount="8" StartBit="0" StartByte="97" IsPresetParameter="false"/>
<Parameter Name="WtStartPoint" BitCount="8" StartBit="0" StartByte="98" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit4" BitCount="3" StartBit="0" StartByte="99"
                            HighBitCount="1" HighStartBit="7" HighStartByte="99" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit4" BitCount="1" StartBit="3" StartByte="99" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit4" BitCount="3" StartBit="4" StartByte="99" Value="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit4" BitCount="2" StartBit="0" StartByte="100" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit4" BitCount="4" StartBit="4" StartByte="100" IsPresetParameter="false"/>
<Parameter Name="LeeMCoef" BitCount="8" StartBit="0" StartByte="101" IsPresetParameter="false"/>
<Parameter Name="LeeSCoef" BitCount="8" StartBit="0" StartByte="102" IsPresetParameter="false"/>
<Parameter Name="MFC" BitCount="2" StartBit="0" StartByte="103" IsPresetParameter="false"/>
<Parameter Name="PWEnhance" BitCount="2" StartBit="5" StartByte="103" IsPresetParameter="true"/>
<Parameter Name="LeeEn" BitCount="1" StartBit="7" StartByte="103" IsPresetParameter="false"/>
<Parameter Name="HF_Alpha" BitCount="4" StartBit="0" StartByte="104" IsPresetParameter="false"/>
<Parameter Name="IIR_ON" BitCount="1" StartBit="4" StartByte="104" IsPresetParameter="false"/>
<Parameter Name="HF" BitCount="3" StartBit="5" StartByte="104" IsPresetParameter="false"/>
<Parameter Name="ECGVelocity" BitCount="2" StartBit="0" StartByte="105" IsPresetParameter="false"/>
<Parameter Name="PixelRatio" BitCount="3" StartBit="2" StartByte="105" IsPresetParameter="true"/>
<Parameter Name="GateSegment" BitCount="2" StartBit="5" StartByte="105" IsPresetParameter="true"/>
<Parameter Name="Bi_CPA" BitCount="1" StartBit="7" StartByte="105"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="DynStartDepthBin" BitCount="8" StartBit="0" StartByte="106" HighBitCount="8" HighStartBit="0" HighStartByte="107" IsPresetParameter="false"/>
<Parameter Name="DynDBRate" BitCount="8" StartBit="0" StartByte="108" HighBitCount="8" HighStartBit="0" HighStartByte="109" IsPresetParameter="false"/>
<Parameter Name="DynDBCount" BitCount="4" StartBit="0" StartByte="110" IsPresetParameter="false"/>
<Parameter Name="WtCoefficience" BitCount="4" StartBit="4" StartByte="110" IsPresetParameter="false"/>
<Parameter Name="LeeGain" BitCount="4" StartBit="0" StartByte="115" IsPresetParameter="false"/>
<Parameter Name="LeeShift" BitCount="4" StartBit="4" StartByte="115" IsPresetParameter="false"/>
<Parameter Name="PWDynamicRange" BitCount="3" StartBit="0" StartByte="116" IsPresetParameter="true"/>
<Parameter Name="AFCoef0" BitCount="8" StartBit="0" StartByte="117" HighBitCount="1" HighStartBit="0" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="AFCoef1" BitCount="8" StartBit="0" StartByte="118" HighBitCount="1" HighStartBit="1" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="AFCoef2" BitCount="8" StartBit="0" StartByte="119" HighBitCount="1" HighStartBit="2" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="AFCoef3" BitCount="8" StartBit="0" StartByte="120" HighBitCount="1" HighStartBit="3" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc0" BitCount="8" StartBit="0" StartByte="121" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc1" BitCount="8" StartBit="0" StartByte="122" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc2" BitCount="8" StartBit="0" StartByte="123" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc3" BitCount="8" StartBit="0" StartByte="124" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc4" BitCount="8" StartBit="0" StartByte="125" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc5" BitCount="8" StartBit="0" StartByte="126" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc6" BitCount="8" StartBit="0" StartByte="127" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc7" BitCount="8" StartBit="0" StartByte="128" IsPresetParameter="false"/>
<Parameter Name="AFCoef4" BitCount="8" StartBit="0" StartByte="129" HighBitCount="1" HighStartBit="4" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="AFGain" BitCount="4" StartBit="0" StartByte="131" IsPresetParameter="false"/>
<Parameter Name="AFShift" BitCount="4" StartBit="4" StartByte="131" IsPresetParameter="false"/>
<Parameter Name="Image1FGC0" BitCount="8" StartBit="0" StartByte="132" IsPresetParameter="false"/>
<Parameter Name="Image1FGC1" BitCount="8" StartBit="0" StartByte="133" IsPresetParameter="false"/>
<Parameter Name="Image1FGC2" BitCount="8" StartBit="0" StartByte="134" IsPresetParameter="false"/>
<Parameter Name="Image1FGC3" BitCount="8" StartBit="0" StartByte="135" IsPresetParameter="false"/>
<Parameter Name="Image2FGC0" BitCount="8" StartBit="0" StartByte="136" IsPresetParameter="false"/>
<Parameter Name="Image2FGC1" BitCount="8" StartBit="0" StartByte="137" IsPresetParameter="false"/>
<Parameter Name="Image2FGC2" BitCount="8" StartBit="0" StartByte="138" IsPresetParameter="false"/>
<Parameter Name="Image2FGC3" BitCount="8" StartBit="0" StartByte="139" IsPresetParameter="false"/>
<Parameter Name="Image3FGC0" BitCount="8" StartBit="0" StartByte="140" IsPresetParameter="false"/>
<Parameter Name="Image3FGC1" BitCount="8" StartBit="0" StartByte="141" IsPresetParameter="false"/>
<Parameter Name="Image3FGC2" BitCount="8" StartBit="0" StartByte="142" IsPresetParameter="false"/>
<Parameter Name="Image3FGC3" BitCount="8" StartBit="0" StartByte="143" IsPresetParameter="false"/>
<Parameter Name="Image4FGC0" BitCount="8" StartBit="0" StartByte="144" IsPresetParameter="false"/>
<Parameter Name="Image4FGC1" BitCount="8" StartBit="0" StartByte="145" IsPresetParameter="false"/>
<Parameter Name="Image4FGC2" BitCount="8" StartBit="0" StartByte="146" IsPresetParameter="false"/>
<Parameter Name="Image4FGC3" BitCount="8" StartBit="0" StartByte="147" IsPresetParameter="false"/>
<Parameter Name="HFSZ" BitCount="4" StartBit="0" StartByte="148" IsPresetParameter="false"/>
<Parameter Name="HFSSP" BitCount="3" StartBit="4" StartByte="148" IsPresetParameter="false"/>
    HighBitCount="1" HighStartBit="3" HighStartByte="150" IsPresetParameter="false"/>
<Parameter Name="ZoomInTopOffset" BitCount="8" StartBit="0" StartByte="154" IsPresetParameter="false"/>
<Parameter Name="CCosAngle" BitCount="8" StartBit="0" StartByte="161"
        HighBitCount="8" HighStartBit="0" HighStartByte="162" IsPresetParameter="false"/>
<Parameter Name="CTgAngle" BitCount="8" StartBit="0" StartByte="163"
        HighBitCount="8" HighStartBit="0" HighStartByte="164" IsPresetParameter="false"/>
<Parameter Name="BSteeringScan" BitCount="6" StartBit="0" Max="40" StartByte="165" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc0" Value="64" BitCount="8" StartBit="0" StartByte="185" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc1" Value="64" BitCount="8" StartBit="0" StartByte="186" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc2" Value="64" BitCount="8" StartBit="0" StartByte="187" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc3" Value="64" BitCount="8" StartBit="0" StartByte="188" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc4" Value="64" BitCount="8" StartBit="0" StartByte="189" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc5" Value="64" BitCount="8" StartBit="0" StartByte="190" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc6" Value="64" BitCount="8" StartBit="0" StartByte="191" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc7" Value="64" BitCount="8" StartBit="0" StartByte="192" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc8" Value="64" BitCount="8" StartBit="0" StartByte="193" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc9" Value="64" BitCount="8" StartBit="0" StartByte="194" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc10" Value="64" BitCount="8" StartBit="0" StartByte="195" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc11" Value="64" BitCount="8" StartBit="0" StartByte="196" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc12" Value="64" BitCount="8" StartBit="0" StartByte="197" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc13" Value="64" BitCount="8" StartBit="0" StartByte="198" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc14" Value="64" BitCount="8" StartBit="0" StartByte="199" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CFMDigitalTgc15" Value="64" BitCount="8" StartBit="0" StartByte="200" DefaultValue="64" IsPresetParameter="true"/>
<Parameter Name="CPriority" BitCount="8" StartBit="0" StartByte="225" IsPresetParameter="true"/>
<Parameter Name="AdapPostProcSmooth" BitCount="3" StartBit="1" StartByte="231" IsPresetParameter="true"/>
<Parameter Name="AdapPostProc" BitCount="4" StartBit="4" StartByte="231" IsPresetParameter="true"/>
<Parameter Name="FrequencyOfTransmit5" Value="5" BitCount="3" StartBit="0" StartByte="245"
    HighBitCount="1" HighStartBit="7" HighStartByte="245" DefaultValue="5" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit5" Value="1" BitCount="1" StartBit="3" StartByte="245" DefaultValue="1" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit5" Value="2" BitCount="3" StartBit="4" StartByte="245" DefaultValue="2" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit5" Value="0" BitCount="2" StartBit="0" StartByte="246" DefaultValue="0" IsPresetParameter="false"/>
<Parameter Name="NeedleMode" BitCount="1" StartBit="2" StartByte="246"
    ValueType="Bool" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit5" Value="0" BitCount="4" StartBit="4" StartByte="246" DefaultValue="0" Max="7" IsPresetParameter="false"/>
<Parameter Name="NeedleAngleIndex" Value="0" BitCount="3" StartBit="0" StartByte="247" IsPresetParameter="false"/>
<Parameter Name="NeedleDynamicRange" Value="1" BitCount="4" StartBit="4" StartByte="247" DefaultValue="1" IsPresetParameter="false"/>
<Parameter Name="NeedleGain" Value="5" BitCount="8" StartBit="0" StartByte="248" DefaultValue="5" IsPresetParameter="false"/>
<Parameter Name="TxOff" BitCount="1" StartBit="7" StartByte="255" ValueType="Bool" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CfmSlope" BitCount="8" StartBit="0" StartByte="296" IsPresetParameter="true"/>
<Parameter Name="VesselThreshold" Value="0" BitCount="4" StartBit="0" StartByte="421" IsPresetParameter="false"/>
<Parameter Name="DetailWeightBin" Value="0" BitCount="8" StartBit="0" StartByte="422" IsPresetParameter="false"/>
<Parameter Name="MinWeightBin" Value="128" BitCount="8" StartBit="0" StartByte="423" IsPresetParameter="false"/>
<Parameter Name="MaxWeightBin" Value="128" BitCount="8" StartBit="0" StartByte="424" IsPresetParameter="false"/>
<Parameter Name="EdgeWeightBin" Value="0" BitCount="8" StartBit="0" StartByte="425" IsPresetParameter="false"/>
</BFParameterGroup>
<BFParameterGroup Type="Common">
<Parameter Name="ADFreqMHz" Value="32" IsPresetParameter="false"/>
<Parameter Name="ADOffsetTimeNs" Value="36000" IsPresetParameter="false"/>
<Parameter Name="MTriggeredAP" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CTriggeredAP" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="SystemScanMode" IsPresetParameter="false"/>
<Parameter Name="ProbeId" IsPresetParameter="false"/>
<Parameter Name="ScanWidth" Min="0" Max="9" Value="3" IsPresetParameter="false"/>
<Parameter Name="FocusNumShow" IsPresetParameter="false"/>
<Parameter Name="ImageZoomCoefOther" Min="100" Max="100" Step="1" Value="100" IsPresetParameter="false"/>
<Parameter Name="ImageZoomCoefB" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="false"/>
<!--ImageZoomCoefOther ImageZoomCoef ImageZoomCoefB 需要保证此顺序-->
<Parameter Name="ImageZoomCoef" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="true"/>
<Parameter Name="IsFullScreenZoomIn" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FullScreenZoomInIndex" Min="0" Max="4" Step="1" Value="2" IsPresetParameter="false"/>
<Parameter Name="DRDiffWithCompoundOn" Min="-5" Max="5" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="DRDiffWithSraOn" Min="-5" Max="5" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="ScpdOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="FcpdOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="TrapezoidalMode" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="DynStartDepth" Min="0" Max="35" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="DynStartDB" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="DynEndDepth" Min="0" Max="35" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="DynEndDB" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="FreqSettingIds" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="AFSettingIds" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="LeeSettingIds" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="iImage" Min="0" Max="3" Step="1" IsPresetParameter="true"/>
<Parameter Name="RvFNo" Min="0" Max="7" Step="1" Value="3" IsPresetParameter="false"/>
<Parameter Name="TxFNo" Min="0" Max="7" Step="1" Value="5" IsPresetParameter="false"/>
<Parameter Name="CPDSteer" Min="0" Max="20" Step="1" Value="5" IsPresetParameter="false"/>
<Parameter Name="TrapezoidalCPDSteer" Min="0" Max="20" Step="1" DefaultValue="10" IsPresetParameter="true"/>
<Parameter Name="PersistDiffWithCpdOn" Min="-7" Max="7" Step="1" Value="0" IsPresetParameter="true"/>
<Parameter Name="PersistDiffWithSraOn" Min="-7" Max="7" Step="1" Value="0" IsPresetParameter="true"/>
<Parameter Name="Flow" Value="1" Min="0" Max="2" DefaultValue="1" IsPresetParameter="true"/>
<Parameter Name="ZoomedCQYZ" Min="1" IsPresetParameter="false"/>
<Parameter Name="ZoomMultiIndex" Min="0" Max="2" Value="0" IsPresetParameter="false"/>
<Parameter Name="ZoomMidLine" IsPresetParameter="false"/>
<Parameter Name="ZoomHalfLines" IsPresetParameter="false"/>
<Parameter Name="ZoomMidDepthMM" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="ZoomHalfDepthMM" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Scroll" Min="0" Max="255" Value="0" IsPresetParameter="false"/>
<Parameter Name="IsScroll" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="ActiveB" Min="0" Max="3" Value="0" IsPresetParameter="false"/>
<Parameter Name="FPS" IsPresetParameter="false"/>
<Parameter Name="Harmonic" ValueType="Bool" Value="false" IsPresetParameter="true"/>
<Parameter Name="PRFColorKHZ" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="PRFDopKHZ" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="BaseLineColor" Min="0" Max="6" Value="3" IsPresetParameter="true"/>
<Parameter Name="DopplerTheta" Min="-70" Max="70" Step="10" Value="0" IsPresetParameter="true"/>
<Parameter Name="RoiMidLine" IsPresetParameter="true"/>
<Parameter Name="RoiHalfLines" IsPresetParameter="true"/>
<Parameter Name="RoiMidDepthMM" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="RoiHalfDepthMM" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="SampleVolumeMM" Min="1" Max="8" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="DopplerStartDepthMM" ValueType="Double" IsPresetParameter="true"/>
<Parameter Name="CMaxVelCMS" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DMaxVelCMS" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PRFColor" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PRFDop" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="BRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="MGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="MRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="PwGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="PwRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="CwdGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="CwdRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="BGammaPos" Value="16" Min="0" Max="255" DefaultValue="16" IsPresetParameter="true"/>
<Parameter Name="BGammaStart" Value="60" Min="1" Max="100" DefaultValue="60" IsPresetParameter="true"/>
<Parameter Name="BGammaStep" Value="10" Min="1" Max="100" DefaultValue="10" IsPresetParameter="true"/>
<Parameter Name="THIState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="ColorInvertState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="BColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CfColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PdColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="DpdColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PwColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="MColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CwdColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CPColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="BGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="MGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PwGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CwdGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="VarColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CfContrast" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PdContrast" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="DpdContrast" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="ImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="BImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="MImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="DImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="StartDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="MPixelSizeSec" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="MPixelSizeMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DPixelSizeCMS" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DPixelSizeSec" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DBaseLineYPos" ValueType="Int" IsPresetParameter="false"/>
<Parameter Name="ZoomDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="ScrollDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="HotLogoDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="HotLogoLine" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="AIO" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsBiopsyVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ZoomSelect" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="BiopsyAngle" IsPresetParameter="false"/>
<Parameter Name="BiopsyXPosMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BiopsyYPosMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BiopsyAngleOffset" IsPresetParameter="false"/>
<Parameter Name="BiopsyXPosMMOffset" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BiopsyYPosMMOffset" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="IsCenterLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsDopplerScanLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsMLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsUDBM" ValueType="Bool" Value="false" TrueValue="1" DefaultValue="false" IsPresetParameter="true"/>
<Parameter Name="GrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="MGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="PwGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="CwdGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="TGC" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="MI" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="TIS" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="TIB" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="ExamModeId" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="ExamModeCaption" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="ProbeConnected" ValueType="Bool" Value="false"/>
<!--ImageRects is List<QRect>-->
<Parameter Name="ImageRects" ValueType="List" IsPresetParameter="false"/>
<!--ImageRegions is List<QRect> for meas region-->
<Parameter Name="ImageRegions" ValueType="List" IsPresetParameter="false"/>
<!--SamplePoints is ByteArray-->
<Parameter Name="BSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="MSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="PwSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="CwdSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="BReject"/>
<Parameter Name="SraGainColorDelta" Min="-64" Max="64" DefaultValue="0" IsPresetParameter="true"/>
<Parameter Name="XContrastValue" Value="1" Min="0" Max="2" DefaultValue="1" IsPresetParameter="true"/>
<Parameter Name="DetailWeightDeltaSraOn" Value="0" Min="-128" Max="128" DefaultValue="0" IsPresetParameter="true"/>
<Parameter Name="DetailWeightDeltaSraCpdOff" Value="0" Min="-128" Max="128" DefaultValue="0" IsPresetParameter="true"/>
<Parameter Name="ImageNum" Value="0" Min="0" IsPresetParameter="false"/>
<Parameter Name="SopInstanceId" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="Threshold" Value="1" Min="1" Max="25" DefaultValue="1" IsPresetParameter="true"/>
<Parameter Name="DTraceSmooth" Value="2" Min="0" Max="3" DefaultValue="2" IsPresetParameter="true"/>
<Parameter Name="Enhance" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FilterCpd" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="SexInfo" ValueType="String" Value="" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicEnable" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicLength" Value="0" Min="0" Max="65536" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicPointList" ValueType="List" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicROIVisible" ValueType="Bool" IsPresetParameter="false"/>
<Parameter Name="LinearAreaRect" ValueType="Rect" IsPresetParameter="false"/>
<Parameter Name="ImageZoomRatio" ValueType="Float" Value="1" IsPresetParameter="false"/>
<Parameter Name="ImageLeftTopPointX" ValueType="Int" Value="0" IsPresetParameter="false"/>
<Parameter Name="ImageLeftTopPointY" ValueType="Int" Value="0" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_0" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_1" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_2" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_3" ValueType="Double" IsPresetParameter="false"/>

<!--系统采用任意线密度的参数进行描述以及参与计算-->
<Parameter Name="StartScanLineColor" ValueType="Int" Value = "0" IsPresetParameter="false"/>
<Parameter Name="StopScanLineColor" ValueType="Int" Value = "0" IsPresetParameter="false"/>
<Parameter Name="StartScanDisColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
<Parameter Name="StopScanDisColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
<Parameter Name="LineSpacingColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
<!--WholeImage = 0 整帧数据, LineData = 1 线数据, IQData = 2 Color、PW是IQ数据, BFData = 3 Beamformer之后的数据-->
<Parameter Name="RawDataFormat" ValueType="Double" Value = "0" IsPresetParameter="false"/>
</BFParameterGroup>
</BFParameter>
