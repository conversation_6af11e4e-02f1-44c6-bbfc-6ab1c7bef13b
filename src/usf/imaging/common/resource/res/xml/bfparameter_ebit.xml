<?xml version="1.0" encoding="UTF-8"?>
<!--ValueType 如果类型不是int，必须注明类型，目前支持Bool,Int,Double,List,String ,ByteArray,Size,
ControlTable的Bool类型必须注明 TrueValue,
SettingIds 为ByteArray类型， StrIds为String类型("0,1,2,3,4")
TM : TriplexMode
xxxShow: 这种参数一般是控制其他两个或者更多个同类参数，用于显示在一个菜单项上-->
<BFParameter Version="0.1" ParameterVersion="0.1">
<BFParameterGroup Type="ControlTable" ControlDataLen="719">
<Parameter Name="Freeze" BitCount="1" StartBit="0" StartByte="1" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="FrequencyCompounding" BitCount="1" StartBit="1" StartByte="1" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="FrequencyCompoundingOther" BitCount="1" StartBit="1" StartByte="1" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsDirectValue="true"/>
<Parameter Name="Socket" BitCount="3" StartBit="2" StartByte="1" IsPresetParameter="false"/>
<Parameter Name="ProbeCode" BitCount="3" StartBit="5" StartByte="1"
        HighBitCount="1" HighStartBit="0" HighStartByte="8" IsPresetParameter="false"/>
<Parameter Name="FreqIndexColorBin" BitCount="3" StartBit="0" StartByte="2"
        HighBitCount="1" HighStartBit="7" HighStartByte="166" IsPresetParameter="false"/>
<Parameter Name="ColorLineDensity" BitCount="1" StartBit="3" StartByte="2" ValueType="Bool" Value="false" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="TDILineDensity" BitCount="1" StartBit="3" StartByte="2" ValueType="Bool" Value="false" TrueValue="0" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="SteeringAngle" BitCount="3" StartBit="4" StartByte="2" IsPresetParameter="true"/>
<Parameter Name="Linear" BitCount="1" StartBit="7" StartByte="2" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumB" BitCount="2" StartBit="0" StartByte="3"
        HighBitCount="2" HighStartBit="4" HighStartByte="336" Max="8" IsPresetParameter="true"/>
<Parameter Name="FocusPosB" BitCount="4" StartBit="2" StartByte="3" IsPresetParameter="true"/>
<Parameter Name="FocusNumM" BitCount="2" StartBit="0" StartByte="3"
        HighBitCount="2" HighStartBit="4" HighStartByte="336" Min="0" Max="0" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="FocusPosM" BitCount="4" StartBit="2" StartByte="3" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="Deep" BitCount="1" StartBit="6" StartByte="3" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
<Parameter Name="HalfHeight" BitCount="1" StartBit="7" StartByte="3" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ImageMode" BitCount="4" StartBit="0" StartByte="4" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="SyncMode" BitCount="3" StartBit="4" StartByte="4" IsPresetParameter="false"/>
<Parameter Name="HighDensity" BitCount="1" StartBit="7" StartByte="4" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="HighDensityInC" BitCount="1" StartBit="7" StartByte="4" Max="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="HighDensityInTDI" BitCount="1" StartBit="7" StartByte="4" Max="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CQYZ" BitCount="6" StartBit="0" StartByte="5"
        HighBitCount="1" HighStartBit="7" HighStartByte="158" IsPresetParameter="true"/>
<Parameter Name="MVelocity" BitCount="2" StartBit="6" StartByte="5" IsPresetParameter="true"/>
<Parameter Name="DVelocity" BitCount="2" StartBit="6" StartByte="5" Max="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DVelocityTDI" BitCount="2" StartBit="6" StartByte="5" Max="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWDVelocity" BitCount="2" StartBit="6" StartByte="5" Max="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="ColorImageMode" BitCount="3" StartBit="0" StartByte="6" IsPresetParameter="false"/>
<Parameter Name="FreqSpectrum" BitCount="1" StartBit="3" StartByte="6" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="LowVelocityBlood" BitCount="1" StartBit="4" StartByte="6" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
<Parameter Name="BaseLine" BitCount="3" StartBit="5" StartByte="6" Max="6" IsPresetParameter="true"/>
<Parameter Name="BaseLineDTDI" BitCount="3" StartBit="5" StartByte="6" Max="6" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="BaseLineCWD" BitCount="3" StartBit="5" StartByte="6" Max="6" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="GainColor" BitCount="8" StartBit="0" StartByte="7" IsPresetParameter="true"/>
<Parameter Name="GainPD" BitCount="8" StartBit="0" StartByte="7" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="GainTDI" BitCount="8" StartBit="0" StartByte="7" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CET" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true"/>
<Parameter Name="PDCET" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DPDCET" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TDICET" BitCount="4" StartBit="1" StartByte="8" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TNR" BitCount="1" StartBit="5" StartByte="8" IsPresetParameter="true"/>
<Parameter Name="TNFtr" BitCount="1" StartBit="6" StartByte="8" IsPresetParameter="true"/>
<Parameter Name="ConfigDone" BitCount="1" StartBit="7" StartByte="8" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="DynamicRange" BitCount="4" StartBit="0" StartByte="9" IsPresetParameter="true"/>
<Parameter Name="FourD" BitCount="1" StartBit="4" StartByte="9" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FrameAvg" BitCount="3" StartBit="5" StartByte="9" IsPresetParameter="true"/>
<Parameter Name="AccCountColor" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true"/>
<Parameter Name="AccCountDop" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AccCountPD" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AccCountTDI" Value="8" BitCount="5" StartBit="3" StartByte="10" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="SampleRateDop" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true"/>
<Parameter Name="SampleRateDopTDI" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="SampleRateDopTM" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DSampleRateDop" BitCount="4" StartBit="4" StartByte="11" IsPresetParameter="true"/>
<Parameter Name="DSampleRateDopTDI" BitCount="4" StartBit="4" StartByte="11" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DSampleRateDopTM" BitCount="4" StartBit="4" StartByte="11" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WallFilterDop" BitCount="2" StartBit="0" StartByte="12" IsPresetParameter="true"/>
<Parameter Name="WallFilterDopTDI" BitCount="2" StartBit="0" StartByte="12" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WallFilterCWD" BitCount="2" StartBit="0" StartByte="12" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WallFilterColor" BitCount="2" StartBit="2" StartByte="12" IsPresetParameter="true"/>
<Parameter Name="WallFilterPD" BitCount="2" StartBit="2" StartByte="12" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WallThreshold" BitCount="4" StartBit="4" StartByte="12" Max="14" IsPresetParameter="true"/>
<Parameter Name="WallThresholdPD" BitCount="4" StartBit="4" StartByte="12" Max="14" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WallThresholdTDI" BitCount="4" StartBit="4" StartByte="12" Max="14" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="SampleVolume" BitCount="3" StartBit="0" StartByte="13" IsPresetParameter="true"/>
<Parameter Name="SampleVolumeTDI" BitCount="3" StartBit="0" StartByte="13" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TriplexMode" BitCount="1" StartBit="3" StartByte="13" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="TriplexModeTDI" BitCount="1" StartBit="3" StartByte="13" IsPresetParameter="false" ValueType="Bool" Value="false" TrueValue="1" IsDirectValue="true"/>
<Parameter Name="FrameAvgColor" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true"/>
<Parameter Name="FrameAvgPD" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="FrameAvgTDI" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CVRT" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true"/>
<Parameter Name="PDCVRT" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DPDCVRT" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TDICVRT" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="Scpd" BitCount="2" StartBit="4" StartByte="14" Max="2" IsPresetParameter="true"/>
<Parameter Name="ScpdTrape" BitCount="2" StartBit="4" StartByte="14" Max="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="ScpdOther" BitCount="2" StartBit="4" StartByte="14" Value="0" IsPresetParameter="false" IsDirectValue="true"/>
<Parameter Name="AcousticPowerTestCode" BitCount="2" StartBit="6" StartByte="14" IsPresetParameter="false"/>
<Parameter Name="MScanLine" BitCount="8" StartBit="0" StartByte="15"
        HighBitCount="1" HighStartBit="0" HighStartByte="231" IsPresetParameter="true"/>
<Parameter Name="DScanLine" BitCount="8" StartBit="0" StartByte="15"
        HighBitCount="1" HighStartBit="0" HighStartByte="231" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DScanLineTDI" BitCount="8" StartBit="0" StartByte="15"
        HighBitCount="1" HighStartBit="0" HighStartByte="231" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="StartLine" BitCount="8" StartBit="0" StartByte="16"
        HighBitCount="1" HighStartBit="0" HighStartByte="230" IsPresetParameter="true"/>
<Parameter Name="StopLine" BitCount="8" StartBit="0" StartByte="17"
        HighBitCount="1" HighStartBit="2" HighStartByte="230" IsPresetParameter="true"/>
<Parameter Name="FourDStartLine" BitCount="8" StartBit="0" StartByte="16"
        HighBitCount="1" HighStartBit="0" HighStartByte="230" IsPresetParameter="false"/>
<Parameter Name="FourDStopLine" BitCount="8" StartBit="0" StartByte="17"
        HighBitCount="1" HighStartBit="2" HighStartByte="230" IsPresetParameter="false"/>
<Parameter Name="StartLineColor" BitCount="8" StartBit="0" StartByte="18"
        HighBitCount="1" HighStartBit="4" HighStartByte="230" IsPresetParameter="false"/>
<Parameter Name="StopLineColor" BitCount="8" StartBit="0" StartByte="19"
        HighBitCount="1" HighStartBit="6" HighStartByte="230" IsPresetParameter="false"/>
<Parameter Name="TopBorderColor" BitCount="8" StartBit="0" StartByte="20"
        HighBitCount="1" HighStartBit="1" HighStartByte="230" IsPresetParameter="false"/>
<Parameter Name="BottomBorderColor" BitCount="8" StartBit="0" StartByte="21"
        HighBitCount="1" HighStartBit="3" HighStartByte="230" IsPresetParameter="false"/>
<Parameter Name="SampleDepthDop" BitCount="8" StartBit="0" StartByte="22"
        HighBitCount="1" HighStartBit="5" HighStartByte="230" IsPresetParameter="false"/>
<Parameter Name="AngleSpacing" BitCount="8" StartBit="0" StartByte="23" IsPresetParameter="false"/>
<Parameter Name="LineSpacing" BitCount="8" StartBit="0" StartByte="24"
        HighBitCount="8" HighStartBit="0" HighStartByte="25" IsPresetParameter="false"/>
<Parameter Name="Gain" BitCount="8" StartBit="0" StartByte="26" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="GainThi" BitCount="8" StartBit="0" StartByte="26" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TGC8" BitCount="8" StartBit="0" StartByte="27" IsPresetParameter="false"/>
<Parameter Name="TGC1" BitCount="8" StartBit="0" StartByte="28" IsPresetParameter="false"/>
<Parameter Name="TGC2" BitCount="8" StartBit="0" StartByte="29" IsPresetParameter="false"/>
<Parameter Name="TGC3" BitCount="8" StartBit="0" StartByte="30" IsPresetParameter="false"/>
<Parameter Name="TGC4" BitCount="8" StartBit="0" StartByte="31" IsPresetParameter="false"/>
<Parameter Name="TGC5" BitCount="8" StartBit="0" StartByte="32" IsPresetParameter="false"/>
<Parameter Name="TGC6" BitCount="8" StartBit="0" StartByte="33" IsPresetParameter="false"/>
<Parameter Name="TGC7" BitCount="8" StartBit="0" StartByte="34" IsPresetParameter="false"/>
<Parameter Name="SlopeDis" BitCount="8" StartBit="0" StartByte="35"
        HighBitCount="4" HighStartBit="4" HighStartByte="37" IsPresetParameter="false"/>
<Parameter Name="PerpendicularDis" BitCount="8" StartBit="0" StartByte="36"
        HighBitCount="4" HighStartBit="0" HighStartByte="37" IsPresetParameter="false"/>
<Parameter Name="ZoomDepth" BitCount="8" StartBit="0" StartByte="38"
        HighBitCount="8" HighStartBit="0" HighStartByte="39" IsPresetParameter="false"/>
<Parameter Name="TransferSignal" BitCount="1" StartBit="3" StartByte="40" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="SpectralInvert" BitCount="1" StartBit="4" StartByte="40" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="Smooth" BitCount="3" StartBit="5" StartByte="40" IsPresetParameter="true"/>
<Parameter Name="FocusNumC" BitCount="2" StartBit="0" StartByte="41" Min="0" Max="0" IsPresetParameter="true"/>
<Parameter Name="FocusPosC" BitCount="4" StartBit="2" StartByte="41" IsPresetParameter="true"/>
<Parameter Name="Deep2" BitCount="1" StartBit="6" StartByte="41" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
<Parameter Name="HalfHeight2" BitCount="1" StartBit="7" StartByte="41" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="GainDop" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true"/>
<Parameter Name="GainDopTM" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="GainDopTDI" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="GainDopCWD" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="Volume" BitCount="4" StartBit="0" StartByte="43"
        HighBitCount="3" HighStartBit="0" HighStartByte="268" Step="1" IsPresetParameter="true"/>
<Parameter Name="VolumeTDI" BitCount="4" StartBit="0" StartByte="43"
        HighBitCount="3" HighStartBit="0" HighStartByte="268" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="VolumeCWD" BitCount="4" StartBit="0" StartByte="43"
        HighBitCount="3" HighStartBit="0" HighStartByte="268" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="MB" BitCount="2" StartBit="4" StartByte="43" IsPresetParameter="true"/>
<Parameter Name="BStearingAngle" BitCount="2" StartBit="6" StartByte="43" IsPresetParameter="true"/>
<Parameter Name="FreqIndexFrequencyCompounding" BitCount="3" StartBit="0" StartByte="44" IsPresetParameter="true"/>
<Parameter Name="CRCOpenFlag" BitCount="1" StartBit="3" StartByte="44" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FocusPosFrequencyCompounding" BitCount="4" StartBit="4" StartByte="44" IsPresetParameter="true"/>
<Parameter Name="AcousticPowerB" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AcousticPowerBThi" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AcousticPowerColor" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AcousticPowerTDI" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AcousticPowerDop" BitCount="8" StartBit="0" StartByte="45" Max="255" Step="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="MPEn" BitCount="1" StartBit="0" StartByte="46" IsPresetParameter="false"/>
<Parameter Name="BFEn" BitCount="1" StartBit="1" StartByte="46" IsPresetParameter="false"/>
<Parameter Name="DscEn" BitCount="1" StartBit="3" StartByte="46" IsPresetParameter="false"/>
<Parameter Name="DspEn" BitCount="1" StartBit="0" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="DbfEn" BitCount="1" StartBit="1" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="PwrEn" BitCount="1" StartBit="2" StartByte="46" IsPresetParameter="false"/>
<Parameter Name="UsbEn" BitCount="1" StartBit="3" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="Prog" BitCount="1" StartBit="4" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="CFGStart" BitCount="1" StartBit="5" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="BEStart" BitCount="1" StartBit="6" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="WRClr" BitCount="1" StartBit="7" StartByte="46" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="LCDLight" BitCount="8" StartBit="0" StartByte="47" Min="30" Max="100" Value="61" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="CVLT" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true"/>
<Parameter Name="PDCVLT" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DPDCVLT" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TDICVLT" BitCount="7" StartBit="0" StartByte="48" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="ExceedLowBlood" BitCount="1" StartBit="7" StartByte="48" IsPresetParameter="false"/>
<Parameter Name="DSC" BitCount="1" StartBit="0" StartByte="49" ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="CFVelocityThreshold" BitCount="3" StartBit="1" StartByte="49" Max="5" IsPresetParameter="true"/>
<Parameter Name="PDVelocityThreshold" BitCount="3" StartBit="1" StartByte="49" Max="5" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TDIVelocityThreshold" BitCount="3" StartBit="1" StartByte="49" Max="5" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="StateClearFlag" BitCount="1" StartBit="4" StartByte="49" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ColorRegionThreshold" BitCount="2" StartBit="5" StartByte="49" IsPresetParameter="true"/>
<Parameter Name="PDRegionThreshold" BitCount="2" StartBit="5" StartByte="49" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TDIRegionThreshold" BitCount="2" StartBit="5" StartByte="49" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WrReset" BitCount="1" StartBit="7" StartByte="49" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CHET" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true"/>
<Parameter Name="PDCHET" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DPDCHET" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TDICHET" BitCount="7" StartBit="0" StartByte="50" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WT" BitCount="1" StartBit="7" StartByte="50" Value = "1" IsPresetParameter="true"/>
<Parameter Name="CTGC" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true"/>
<Parameter Name="PDCTGC" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DPDCTGC" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TDICTGC" BitCount="8" StartBit="0" StartByte="51" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="BCosAngle" BitCount="8" StartBit="0" StartByte="52"
        HighBitCount="8" HighStartBit="0" HighStartByte="53" IsPresetParameter="false"/>
<Parameter Name="BTgAngle" BitCount="8" StartBit="0" StartByte="54"
        HighBitCount="8" HighStartBit="0" HighStartByte="55" IsPresetParameter="false"/>
<Parameter Name="DSPReset" BitCount="1" StartBit="0" StartByte="56" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="DBFReset" BitCount="1" StartBit="1" StartByte="56" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="DBFInfo" BitCount="2" StartBit="2" StartByte="56" IsPresetParameter="false"/>
<Parameter Name="TestSignal" BitCount="1" StartBit="5" StartByte="56" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="TestParameter" BitCount="2" StartBit="6" StartByte="56" IsPresetParameter="false"/>
<Parameter Name="BPulseNum" BitCount="2" StartBit="0" StartByte="57" Value="1" IsPresetParameter="false"/>
<Parameter Name="CPulseNum" BitCount="3" StartBit="2" StartByte="57" Value="1" IsPresetParameter="false"/>
<Parameter Name="DPulseNum" BitCount="3" StartBit="5" StartByte="57" Value="1" IsPresetParameter="true"/>
<Parameter Name="DPulseNumTDI" BitCount="3" StartBit="5" StartByte="57" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
<!--CWDPluseNum Lotus中暂时未下发控制字2015-08-06-->
<Parameter Name="CWDPluseNum" BitCount="3" StartBit="5" StartByte="57" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="BloodEffection" BitCount="2" StartBit="0" StartByte="58" IsPresetParameter="true"/>
<Parameter Name="BloodEffectionPD" BitCount="2" StartBit="0" StartByte="58" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="BloodEffectionTDI" BitCount="2" StartBit="0" StartByte="58" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="LogCompression" BitCount="3" StartBit="2" StartByte="58" IsPresetParameter="false"/>
<Parameter Name="HighFreqPhasedProbe" BitCount="1" StartBit="5" StartByte="58" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="PhaseProbeId" BitCount="1" StartBit="6" StartByte="58" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="THI" BitCount="1" StartBit="7" StartByte="58" ValueType="Bool" Value="false" TrueValue="1"  IsPresetParameter="true"/>
<Parameter Name="ImageZoomCoefBin" BitCount="8" StartBit="0" StartByte="59"  HighBitCount="8" HighStartBit="0" HighStartByte="60" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc00" BitCount="8" StartBit="0" StartByte="61" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc01" BitCount="8" StartBit="0" StartByte="62" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc02" BitCount="8" StartBit="0" StartByte="63" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc03" BitCount="8" StartBit="0" StartByte="64" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc04" BitCount="8" StartBit="0" StartByte="65" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc05" BitCount="8" StartBit="0" StartByte="66" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc06" BitCount="8" StartBit="0" StartByte="67" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc07" BitCount="8" StartBit="0" StartByte="68" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc08" BitCount="8" StartBit="0" StartByte="69" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc09" BitCount="8" StartBit="0" StartByte="70" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc10" BitCount="8" StartBit="0" StartByte="71" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc11" BitCount="8" StartBit="0" StartByte="72" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc12" BitCount="8" StartBit="0" StartByte="73" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc13" BitCount="8" StartBit="0" StartByte="74" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc14" BitCount="8" StartBit="0" StartByte="75" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc15" BitCount="8" StartBit="0" StartByte="76" IsPresetParameter="false"/>
<Parameter Name="DebugPara0" BitCount="8" StartBit="0" StartByte="77" IsPresetParameter="false"/>
<Parameter Name="DebugPara3" BitCount="4" StartBit="0" StartByte="78" IsPresetParameter="false"/>
<Parameter Name="DebugPara2" BitCount="3" StartBit="4" StartByte="78" IsPresetParameter="false"/>
<Parameter Name="DebugPara1" BitCount="1" StartBit="7" StartByte="78" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc0" BitCount="8" StartBit="0" StartByte="79" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc1" BitCount="8" StartBit="0" StartByte="80" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc2" BitCount="8" StartBit="0" StartByte="81" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc3" BitCount="8" StartBit="0" StartByte="82" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc4" BitCount="8" StartBit="0" StartByte="83" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc5" BitCount="8" StartBit="0" StartByte="84" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc6" BitCount="8" StartBit="0" StartByte="85" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc7" BitCount="8" StartBit="0" StartByte="86" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit1" BitCount="3" StartBit="0" StartByte="87"
        HighBitCount="1" HighStartBit="7" HighStartByte="87" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit1" BitCount="1" StartBit="3" StartByte="87" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit1" BitCount="3" StartBit="4" StartByte="87" Value="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit1" BitCount="2" StartBit="0" StartByte="88" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit1" BitCount="4" StartBit="4" StartByte="88" IsPresetParameter="false"/>
<Parameter Name="VHSi" BitCount="3" StartBit="0" StartByte="89" IsPresetParameter="true"/>
<Parameter Name="Left" BitCount="1" StartBit="0" StartByte="90" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="Up" BitCount="1" StartBit="1" StartByte="90" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="MBColor" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="true"/>
<Parameter Name="MBPD" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="MBTDI" BitCount="2" StartBit="2" StartByte="90" Value="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="ShutDown" BitCount="1" StartBit="5" StartByte="90" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="NotReduceGain" BitCount="1" StartBit="6" StartByte="90" ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="FrequencyOfTransmit2" BitCount="3" StartBit="0" StartByte="91"
        HighBitCount="1" HighStartBit="7" HighStartByte="91" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit2" BitCount="1" StartBit="3" StartByte="91" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit2" BitCount="3" StartBit="4" StartByte="91" Value="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit2" BitCount="2" StartBit="0" StartByte="92" IsPresetParameter="false"/>
<Parameter Name="Rotation" Value="0" Max="270" Step="90" BitCount="2" StartBit="2" StartByte="92" IsPresetParameter="true"/>
<Parameter Name="FilterCoefOfTransmit2" BitCount="4" StartBit="4" StartByte="92" IsPresetParameter="false"/>
<Parameter Name="Alpha" BitCount="8" StartBit="0" StartByte="93" IsPresetParameter="false"/>
<Parameter Name="Beta" BitCount="8" StartBit="0" StartByte="94" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit3" BitCount="3" StartBit="0" StartByte="95"
        HighBitCount="1" HighStartBit="7" HighStartByte="95" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit3" BitCount="1" StartBit="3" StartByte="95" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit3" BitCount="3" StartBit="4" StartByte="95" Value="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit3" BitCount="2" StartBit="0" StartByte="96" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit3" BitCount="4" StartBit="4" StartByte="96" IsPresetParameter="false"/>
<Parameter Name="Gamma" BitCount="8" StartBit="0" StartByte="97" IsPresetParameter="false"/>
<Parameter Name="WtStartPoint" BitCount="8" StartBit="0" StartByte="98" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit4" BitCount="3" StartBit="0" StartByte="99"
        HighBitCount="1" HighStartBit="7" HighStartByte="99" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit4" BitCount="1" StartBit="3" StartByte="99" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit4" BitCount="3" StartBit="4" StartByte="99" Value="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit4" BitCount="2" StartBit="0" StartByte="100" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit4" BitCount="4" StartBit="4" StartByte="100" IsPresetParameter="false"/>
<Parameter Name="LeeMCoef" BitCount="8" StartBit="0" StartByte="101" IsPresetParameter="false"/>
<Parameter Name="LeeSCoef" BitCount="8" StartBit="0" StartByte="102" IsPresetParameter="false"/>
<Parameter Name="MFC" BitCount="2" StartBit="0" StartByte="103" IsPresetParameter="false"/>
<Parameter Name="CWEn" BitCount="1" StartBit="2" StartByte="103" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ECGEn" BitCount="1" StartBit="3" StartByte="103" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="TDIEn" BitCount="1" StartBit="4" StartByte="103" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="PWEnhance" BitCount="2" StartBit="5" StartByte="103" IsPresetParameter="true"/>
<Parameter Name="PWEnhanceTM" BitCount="2" StartBit="5" StartByte="103" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="PWEnhanceTDI" BitCount="2" StartBit="5" StartByte="103" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="PWEnhanceCWD" BitCount="2" StartBit="5" StartByte="103" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="LeeEn" BitCount="1" StartBit="7" StartByte="103" IsPresetParameter="false"/>
<Parameter Name="HF_Alpha" BitCount="4" StartBit="0" StartByte="104" IsPresetParameter="false"/>
<Parameter Name="IIR_ON" BitCount="1" StartBit="4" StartByte="104" IsPresetParameter="false"/>
<Parameter Name="HF" BitCount="3" StartBit="5" StartByte="104" IsPresetParameter="false"/>
<Parameter Name="ECGVelocity" BitCount="2" StartBit="0" StartByte="105" IsPresetParameter="true"/>
<Parameter Name="PixelRatio" BitCount="3" StartBit="2" StartByte="105" IsPresetParameter="true"/>
<Parameter Name="PixelRatioTM" BitCount="3" StartBit="2" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="PixelRatioTDI" BitCount="3" StartBit="2" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="PixelRatioCWD" BitCount="3" StartBit="2" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="GateSegment" BitCount="2" StartBit="5" StartByte="105" IsPresetParameter="true"/>
<Parameter Name="GateSegmentTDI" BitCount="2" StartBit="5" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="GateSegmentCWD" BitCount="2" StartBit="5" StartByte="105" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="Bi_CPA" BitCount="1" StartBit="7" StartByte="105" IsPresetParameter="false"/>
<Parameter Name="DynStartDepthBin" BitCount="8" StartBit="0" StartByte="106"
        HighBitCount="8" HighStartBit="0" HighStartByte="107" IsPresetParameter="false"/>
<Parameter Name="DynDBRate" BitCount="8" StartBit="0" StartByte="108"
        HighBitCount="8" HighStartBit="0" HighStartByte="109" IsPresetParameter="false"/>
<Parameter Name="DynDBCount" BitCount="4" StartBit="0" StartByte="110" IsPresetParameter="false"/>
<Parameter Name="WtCoefficience" BitCount="4" StartBit="4" StartByte="110" IsPresetParameter="false"/>
<Parameter Name="TransmitPowerPositiveVol" BitCount="8" StartBit="0" StartByte="111" IsPresetParameter="false"/>
<Parameter Name="TransmitPowerNegativeVol" BitCount="8" StartBit="0" StartByte="112" IsPresetParameter="false"/>
<!--AcousticPowerCWD  (255 - value);        //电源针对CW的控制反了，0是电压最大，255电压最低，所以这里要特殊处理-->
<Parameter Name="CWTransmitPowerPositiveVol" BitCount="8" StartBit="0" StartByte="113" IsPresetParameter="true"/>
<Parameter Name="CWTransmitPowerNegativeVol" BitCount="8" StartBit="0" StartByte="114" IsPresetParameter="false"/>
<Parameter Name="LeeGain" BitCount="4" StartBit="0" StartByte="115" IsPresetParameter="false"/>
<Parameter Name="LeeShift" BitCount="4" StartBit="4" StartByte="115" IsPresetParameter="false"/>
<Parameter Name="PWDynamicRange" BitCount="3" StartBit="0" StartByte="116" IsPresetParameter="true"/>
<Parameter Name="PWDynamicRangeTM" BitCount="3" StartBit="0" StartByte="116" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="PWDynamicRangeTDI" BitCount="3" StartBit="0" StartByte="116" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="PWDynamicRangeCWD" BitCount="3" StartBit="0" StartByte="116" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DPProbeImageSwitch" BitCount="2" StartBit="3" StartByte="116" IsPresetParameter="true"/>
<Parameter Name="DPProbeIdentity" BitCount="1" StartBit="5" StartByte="116" IsPresetParameter="true"/>
<Parameter Name="HV_On" BitCount="1" StartBit="6" StartByte="116" IsPresetParameter="false"/>
<Parameter Name="MEProb" BitCount="1" StartBit="7" StartByte="116" IsPresetParameter="false"/>
<Parameter Name="AFCoef0" BitCount="8" StartBit="0" StartByte="117"
        HighBitCount="1" HighStartBit="0" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="AFCoef1" BitCount="8" StartBit="0" StartByte="118"
        HighBitCount="1" HighStartBit="1" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="AFCoef2" BitCount="8" StartBit="0" StartByte="119"
        HighBitCount="1" HighStartBit="2" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="AFCoef3" BitCount="8" StartBit="0" StartByte="120"
        HighBitCount="1" HighStartBit="3" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc0" BitCount="8" StartBit="0" StartByte="121" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc1" BitCount="8" StartBit="0" StartByte="122" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc2" BitCount="8" StartBit="0" StartByte="123" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc3" BitCount="8" StartBit="0" StartByte="124" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc4" BitCount="8" StartBit="0" StartByte="125" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc5" BitCount="8" StartBit="0" StartByte="126" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc6" BitCount="8" StartBit="0" StartByte="127" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc7" BitCount="8" StartBit="0" StartByte="128" IsPresetParameter="false"/>
<Parameter Name="AFCoef4" BitCount="8" StartBit="0" StartByte="129"
        HighBitCount="1" HighStartBit="4" HighStartByte="130" IsPresetParameter="false"/>
<Parameter Name="TDIWallfilter" BitCount="2" StartBit="6" StartByte="130" IsPresetParameter="true"/>
<Parameter Name="AFGain" BitCount="4" StartBit="0" StartByte="131" IsPresetParameter="false"/>
<Parameter Name="AFShift" BitCount="4" StartBit="4" StartByte="131" IsPresetParameter="false"/>
<Parameter Name="Image1FGC0" BitCount="8" StartBit="0" StartByte="132" IsPresetParameter="false"/>
<Parameter Name="Image1FGC1" BitCount="8" StartBit="0" StartByte="133" IsPresetParameter="false"/>
<Parameter Name="Image1FGC2" BitCount="8" StartBit="0" StartByte="134" IsPresetParameter="false"/>
<Parameter Name="Image1FGC3" BitCount="8" StartBit="0" StartByte="135" IsPresetParameter="false"/>
<Parameter Name="Image2FGC0" BitCount="8" StartBit="0" StartByte="136" IsPresetParameter="false"/>
<Parameter Name="Image2FGC1" BitCount="8" StartBit="0" StartByte="137" IsPresetParameter="false"/>
<Parameter Name="Image2FGC2" BitCount="8" StartBit="0" StartByte="138" IsPresetParameter="false"/>
<Parameter Name="Image2FGC3" BitCount="8" StartBit="0" StartByte="139" IsPresetParameter="false"/>
<Parameter Name="Image3FGC0" BitCount="8" StartBit="0" StartByte="140" IsPresetParameter="false"/>
<Parameter Name="Image3FGC1" BitCount="8" StartBit="0" StartByte="141" IsPresetParameter="false"/>
<Parameter Name="Image3FGC2" BitCount="8" StartBit="0" StartByte="142" IsPresetParameter="false"/>
<Parameter Name="Image3FGC3" BitCount="8" StartBit="0" StartByte="143" IsPresetParameter="false"/>
<Parameter Name="Image4FGC0" BitCount="8" StartBit="0" StartByte="144" IsPresetParameter="false"/>
<Parameter Name="Image4FGC1" BitCount="8" StartBit="0" StartByte="145" IsPresetParameter="false"/>
<Parameter Name="Image4FGC2" BitCount="8" StartBit="0" StartByte="146" IsPresetParameter="false"/>
<Parameter Name="Image4FGC3" BitCount="8" StartBit="0" StartByte="147" IsPresetParameter="false"/>
<Parameter Name="HFSZ" Value="4" BitCount="4" StartBit="0" StartByte="148" IsPresetParameter="false"/>
<Parameter Name="HFSSP" Value="3" BitCount="3" StartBit="4" StartByte="148" IsPresetParameter="false"/>
<Parameter Name="ZoomOn" BitCount="1" StartBit="7" StartByte="148" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
    HighBitCount="1" HighStartBit="3" HighStartByte="150" IsPresetParameter="false"/>
<Parameter Name="ColorInvert" BitCount="1" StartBit="2" StartByte="150" ValueType="Bool" Value="false" TrueValue="0" IsPresetParameter="false"/>
<Parameter Name="CWDShift" BitCount="3" StartBit="0" StartByte="151" IsPresetParameter="true"/>
<Parameter Name="ColorM" BitCount="1" StartBit="3" StartByte="151" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ECGGain" BitCount="3" StartBit="0" StartByte="152" IsPresetParameter="true"/>
<Parameter Name="ECGPos" BitCount="2" StartBit="3" StartByte="152" IsPresetParameter="true"/>
<Parameter Name="ECGInvert" BitCount="1" StartBit="6" StartByte="152" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="FilterCpd" BitCount="1" StartBit="7" StartByte="152" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="CWDSampleRate" BitCount="4" StartBit="0" StartByte="153" IsPresetParameter="true"/>
<Parameter Name="CWDDynamicRange" BitCount="3" StartBit="4" StartByte="153" IsPresetParameter="true"/>
<Parameter Name="ExtremeLowBlood" BitCount="1" StartBit="7" StartByte="153" IsPresetParameter="false"/>
<Parameter Name="ZoomInTopOffset" BitCount="8" StartBit="0" StartByte="154" IsPresetParameter="false"/>
<Parameter Name="CWDFft" Value="200" BitCount="8" StartBit="0" StartByte="155" IsPresetParameter="true"/>
<Parameter Name="CWDIterations" Value="60" BitCount="6" StartBit="0" StartByte="156" IsPresetParameter="true"/>
<Parameter Name="TriplexRefresh" BitCount="1" StartBit="7" StartByte="156" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FilterCpdCoe" Value="128" BitCount="8" StartBit="0" StartByte="157" IsPresetParameter="true"/>
<Parameter Name="BFPipelineNumber" Value="9" BitCount="6" StartBit="0" StartByte="158" IsPresetParameter="false"/>
<Parameter Name="LRInvert" BitCount="1" StartBit="6" StartByte="158" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CpdMinValue" Value="30" BitCount="8" StartBit="0" StartByte="159" IsPresetParameter="false"/>
<Parameter Name="CpdMaxValue" Value="180" BitCount="8" StartBit="0" StartByte="160" IsPresetParameter="false"/>
<Parameter Name="CCosAngle" BitCount="8" StartBit="0" StartByte="161"
        HighBitCount="8" HighStartBit="0" HighStartByte="162" IsPresetParameter="false"/>
<Parameter Name="CTgAngle" BitCount="8" StartBit="0" StartByte="163"
        HighBitCount="8" HighStartBit="0" HighStartByte="164" IsPresetParameter="false"/>
<Parameter Name="BSteeringScan" Value="20" BitCount="6" StartBit="0" Max="40" StartByte="165" IsPresetParameter="true"/>
<Parameter Name="CDucy" BitCount="2" StartBit="6" StartByte="165" IsPresetParameter="false"/>
<Parameter Name="CFilterCoef" BitCount="4" StartBit="3" StartByte="166" IsPresetParameter="false"/>
<Parameter Name="CompensationCoef" Value="255" BitCount="8" StartBit="0" StartByte="167"
        HighBitCount="3" HighStartBit="0" HighStartByte="166" IsPresetParameter="false"/>
<Parameter Name="APPrf" BitCount="8" StartBit="0" StartByte="168"
        HighBitCount="8" HighStartBit="0" HighStartByte="169" IsPresetParameter="false"/>
<Parameter Name="APDutyRatio" BitCount="8" StartBit="0" StartByte="170" IsPresetParameter="false"/>
<Parameter Name="ConvexCpdSteer" BitCount="4" StartBit="0" StartByte="171" IsPresetParameter="false"/>
<Parameter Name="Prf_D_Triplex_Work" BitCount="4" StartBit="4" StartByte="176" IsPresetParameter="false"/>
<Parameter Name="CFMAnologTgc0" Value="192" BitCount="8" StartBit="0" StartByte="177" IsPresetParameter="false"/>
<Parameter Name="CFMAnologTgc1" Value="255" BitCount="8" StartBit="0" StartByte="178" IsPresetParameter="false"/>
<Parameter Name="CFMAnologTgc2" Value="255" BitCount="8" StartBit="0" StartByte="179" IsPresetParameter="false"/>
<Parameter Name="CFMAnologTgc3" Value="255" BitCount="8" StartBit="0" StartByte="180" IsPresetParameter="false"/>
<Parameter Name="CFMAnologTgc4" Value="255" BitCount="8" StartBit="0" StartByte="181" IsPresetParameter="false"/>
<Parameter Name="CFMAnologTgc5" Value="255" BitCount="8" StartBit="0" StartByte="182" IsPresetParameter="false"/>
<Parameter Name="CFMAnologTgc6" Value="255" BitCount="8" StartBit="0" StartByte="183" IsPresetParameter="false"/>
<Parameter Name="CFMAnologTgc7" Value="255" BitCount="8" StartBit="0" StartByte="184" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc0" Value="64" BitCount="8" StartBit="0" StartByte="185" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc1" Value="64" BitCount="8" StartBit="0" StartByte="186" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc2" Value="64" BitCount="8" StartBit="0" StartByte="187" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc3" Value="64" BitCount="8" StartBit="0" StartByte="188" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc4" Value="64" BitCount="8" StartBit="0" StartByte="189" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc5" Value="64" BitCount="8" StartBit="0" StartByte="190" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc6" Value="64" BitCount="8" StartBit="0" StartByte="191" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc7" Value="64" BitCount="8" StartBit="0" StartByte="192" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc8" Value="64" BitCount="8" StartBit="0" StartByte="193" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc9" Value="64" BitCount="8" StartBit="0" StartByte="194" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc10" Value="64" BitCount="8" StartBit="0" StartByte="195" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc11" Value="64" BitCount="8" StartBit="0" StartByte="196" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc12" Value="64" BitCount="8" StartBit="0" StartByte="197" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc13" Value="64" BitCount="8" StartBit="0" StartByte="198" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc14" Value="64" BitCount="8" StartBit="0" StartByte="199" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc15" Value="64" BitCount="8" StartBit="0" StartByte="200" IsPresetParameter="false"/>
<Parameter Name="PacketSize" BitCount="4" StartBit="0" StartByte="201" IsPresetParameter="true"/>
<Parameter Name="PacketSizePD" BitCount="4" StartBit="0" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="PacketSizeTDI" BitCount="4" StartBit="0" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DummyEnSample" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true"/>
<Parameter Name="DummyEnSampleTM" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DummyEnSamplePD" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DummyEnSampleTDI" BitCount="2" StartBit="4" StartByte="201" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AudioFilterCoefSel" BitCount="2" StartBit="6" StartByte="201"
        HighBitCount="1" HighStartBit="6" HighStartByte="156" IsPresetParameter="true"/>
<Parameter Name="AudioFilterCoefSelTM" BitCount="2" StartBit="6" StartByte="201"
        HighBitCount="1" HighStartBit="6" HighStartByte="156" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AudioFilterCoefSelTDI" BitCount="2" StartBit="6" StartByte="201"
        HighBitCount="1" HighStartBit="6" HighStartByte="156" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AudioFilterCoefSelCWD" BitCount="2" StartBit="6" StartByte="201"
        HighBitCount="1" HighStartBit="6" HighStartByte="156" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WFGainControl" BitCount="4" StartBit="0" StartByte="202" IsPresetParameter="true"/>
<Parameter Name="WFGainControlPD" BitCount="4" StartBit="0" StartByte="202" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WFGainControlTDI" BitCount="4" StartBit="0" StartByte="202" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CLFGain" BitCount="4" StartBit="4" StartByte="202" IsPresetParameter="false"/>
<Parameter Name="CLFShift" BitCount="4" StartBit="4" StartByte="203" IsPresetParameter="false"/>
<Parameter Name="CLFCoef0" BitCount="8" StartBit="0" StartByte="204"
        HighBitCount="1" HighStartBit="0" HighStartByte="203" IsPresetParameter="false"/>
<Parameter Name="CLFCoef1" BitCount="8" StartBit="0" StartByte="205"
        HighBitCount="1" HighStartBit="1" HighStartByte="203" IsPresetParameter="false"/>
<Parameter Name="CLFCoef2" BitCount="8" StartBit="0" StartByte="206"
        HighBitCount="1" HighStartBit="2" HighStartByte="203" IsPresetParameter="false"/>
<Parameter Name="CAFGain" BitCount="4" StartBit="0" StartByte="207" IsPresetParameter="false"/>
<Parameter Name="CAFShift" BitCount="4" StartBit="4" StartByte="207" IsPresetParameter="false"/>
<Parameter Name="CAFCoef0" BitCount="8" StartBit="0" StartByte="208"
        HighBitCount="1" HighStartBit="0" HighStartByte="212" IsPresetParameter="false"/>
<Parameter Name="CAFCoef1" BitCount="8" StartBit="0" StartByte="209"
        HighBitCount="1" HighStartBit="1" HighStartByte="212" IsPresetParameter="false"/>
<Parameter Name="CAFCoef2" BitCount="8" StartBit="0" StartByte="210"
        HighBitCount="1" HighStartBit="2" HighStartByte="212" IsPresetParameter="false"/>
<Parameter Name="CAFCoef3" BitCount="8" StartBit="0" StartByte="211"
        HighBitCount="1" HighStartBit="3" HighStartByte="212" IsPresetParameter="false"/>
<Parameter Name="LComeBack" BitCount="1" StartBit="3" StartByte="203" IsPresetParameter="true"/>
<Parameter Name="LComeBackPD" BitCount="1" StartBit="3" StartByte="203" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="LComeBackTDI" BitCount="1" StartBit="3" StartByte="203" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AComeBack" BitCount="1" StartBit="4" StartByte="212" IsPresetParameter="true"/>
<Parameter Name="AComeBackPD" BitCount="1" StartBit="4" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AComeBackTDI" BitCount="1" StartBit="4" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CFMIIRe" BitCount="1" StartBit="5" StartByte="212" IsPresetParameter="true"/>
<Parameter Name="PDIIRe" BitCount="1" StartBit="5" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TDIIIRe" BitCount="1" StartBit="5" StartByte="212" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="HprfEn" BitCount="1" StartBit="6" StartByte="212" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FCA_Switch" BitCount="1" StartBit="7" StartByte="212" IsPresetParameter="false"/>
<Parameter Name="AffiliatedChipWrite" BitCount="1" StartBit="0" StartByte="213" IsPresetParameter="false"/>
<Parameter Name="AffiliatedChipRead" BitCount="1" StartBit="1" StartByte="213" IsPresetParameter="false"/>
<Parameter Name="AffiliatedChipSelectSignal" BitCount="5" StartBit="2" StartByte="213" IsPresetParameter="false"/>
<Parameter Name="AffiliatedChipAddr" BitCount="8" StartBit="0" StartByte="214"
        HighBitCount="8" HighStartBit="0" HighStartByte="215" IsPresetParameter="false"/>
<Parameter Name="AffiliatedChipWriteData" BitCount="8" StartBit="0" StartByte="216"
        HighBitCount="8" HighStartBit="0" HighStartByte="217" IsPresetParameter="false"/>
<Parameter Name="FlashThreshold" Value="96" BitCount="8" StartBit="0" StartByte="220" IsPresetParameter="false"/>
<Parameter Name="LowThreshold" Value="16" BitCount="8" StartBit="0" StartByte="221" IsPresetParameter="false"/>
<Parameter Name="MedThreshold" Value="64" BitCount="8" StartBit="0" StartByte="222" IsPresetParameter="false"/>
<Parameter Name="HoleFillEn" Value="1" BitCount="1" StartBit="0" StartByte="223" IsPresetParameter="false"/>
<Parameter Name="MedFltByp" Value="1" BitCount="1" StartBit="1" StartByte="223" IsPresetParameter="false"/>
<Parameter Name="CTxPulseDutyBin" BitCount="5" StartBit="3" StartByte="223" IsPresetParameter="false"/>
<Parameter Name="FlashSupress" Value="255" BitCount="8" StartBit="0" StartByte="224" IsPresetParameter="false"/>
<Parameter Name="CPriority" Value="255" BitCount="8" StartBit="0" StartByte="225" IsPresetParameter="false"/>
<Parameter Name="FCA_Delta" BitCount="8" StartBit="0" StartByte="226" IsPresetParameter="false"/>
<Parameter Name="FCA_Gamma" BitCount="8" StartBit="0" StartByte="227" IsPresetParameter="false"/>
<Parameter Name="FCA_ThresholdUpLimit" BitCount="8" StartBit="0" StartByte="228" IsPresetParameter="false"/>
<Parameter Name="FCA_ThresholdLowLimit" BitCount="8" StartBit="0" StartByte="229" IsPresetParameter="false"/>
<Parameter Name="AdapPostProcSmooth" BitCount="3" StartBit="1" StartByte="231" IsPresetParameter="true"/>
<Parameter Name="AdapPostProc" BitCount="4" StartBit="4" StartByte="231" IsPresetParameter="true"/>
<Parameter Name="FGP0" Value="128" BitCount="8" StartBit="0" StartByte="232" IsPresetParameter="false"/>
<Parameter Name="FGP1" Value="128" BitCount="8" StartBit="0" StartByte="233" IsPresetParameter="false"/>
<Parameter Name="FGP2" Value="128" BitCount="8" StartBit="0" StartByte="234" IsPresetParameter="false"/>
<Parameter Name="FGP3" Value="128" BitCount="8" StartBit="0" StartByte="235" IsPresetParameter="false"/>
<Parameter Name="CFMQuitSamp_Head" BitCount="4" StartBit="0" StartByte="236" IsPresetParameter="false"/>
<Parameter Name="CFMQuitSamp_Tail" Value="8" BitCount="4" StartBit="4" StartByte="236" IsPresetParameter="false"/>
<Parameter Name="ScaningSignalEntireCompensation" BitCount="8" StartBit="0" StartByte="237" IsPresetParameter="false"/>
<Parameter Name="ScaningSignalIndividualCompensation" BitCount="8" StartBit="0" StartByte="238" IsPresetParameter="false"/>
<Parameter Name="GaussFilterSwitch" Value="1" BitCount="1" StartBit="0" StartByte="239" IsPresetParameter="false"/>
<!--ThiMod is for PhasedProbe,Keyboard name is FHI-->
<Parameter Name="ThiMode" Value="1" BitCount="1" StartBit="1" StartByte="239" IsPresetParameter="false"/>
<Parameter Name="CfmApertureMode" Value="1" BitCount="1" StartBit="2" StartByte="239" IsPresetParameter="false"/>
<Parameter Name="CfmDemodulation" BitCount="1" StartBit="3" StartByte="239" IsPresetParameter="false"/>
<Parameter Name="CFMVelLevel" BitCount="3" StartBit="5" StartByte="239"
        HighBitCount="1" HighStartBit="6" HighStartByte="292" IsPresetParameter="true"/>
<Parameter Name="CFMVelLevelTM" BitCount="3" StartBit="5" StartByte="239"
        HighBitCount="1" HighStartBit="6" HighStartByte="292" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="WaferNumDiff" Value="128" BitCount="8" StartBit="0" StartByte="240" IsPresetParameter="false"/>
<Parameter Name="MaxTransmitApertureControl" Value="63" BitCount="6" StartBit="0" StartByte="241" IsPresetParameter="false"/>
<Parameter Name="MinTransmitApertureControl" BitCount="6" StartBit="0" StartByte="242" IsPresetParameter="false"/>
<Parameter Name="FNumDecimal" BitCount="8" StartBit="0" StartByte="243"
        HighBitCount="4" HighStartBit="0" HighStartByte="244" IsPresetParameter="false"/>
<Parameter Name="FNumInteger" BitCount="4" StartBit="4" StartByte="244" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit5" BitCount="3" StartBit="0" StartByte="245"
        HighBitCount="1" HighStartBit="7" HighStartByte="245" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit5" BitCount="1" StartBit="3" StartByte="245" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit5" Value="1" BitCount="3" StartBit="4" StartByte="245" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit5" BitCount="2" StartBit="0" StartByte="246" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit5" BitCount="4" StartBit="4" StartByte="246" Max="7" IsPresetParameter="false"/>
<Parameter Name="NeedleMode" BitCount="1" StartBit="2" StartByte="246" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="NeedleAngleIndex" Value="12" BitCount="4" StartBit="0" StartByte="247" IsPresetParameter="true"/>
<Parameter Name="NeedleDynamicRange" BitCount="4" StartBit="4" StartByte="247" IsPresetParameter="false"/>
<Parameter Name="NeedleGain" BitCount="8" StartBit="0" StartByte="248" IsPresetParameter="false"/>
<Parameter Name="CfmMaxTransmitApertureControlBin" Value="63" BitCount="6" StartBit="0" StartByte="249" IsPresetParameter="false"/>
<Parameter Name="VideoInvert" BitCount="1" StartBit="7" StartByte="249" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CfmMinTransmitApertureControlBin" BitCount="6" StartBit="0" StartByte="250" IsPresetParameter="false"/>
<Parameter Name="VideoInvertOfD" BitCount="1" StartBit="7" StartByte="250" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CfmFNumDecimal" BitCount="8" StartBit="0" StartByte="251"
        HighBitCount="4" HighStartBit="0" HighStartByte="252" IsPresetParameter="false"/>
<Parameter Name="CfmFNumInteger" BitCount="4" StartBit="4" StartByte="252" IsPresetParameter="false"/>
<Parameter Name="BCRelativePos" BitCount="8" StartBit="0" StartByte="253" IsPresetParameter="false"/>
<Parameter Name="ParaPresetTime" Value="159" BitCount="8" StartBit="0" StartByte="254"
        HighBitCount="4" HighStartBit="0" HighStartByte="255" DefaultValue="159" IsPresetParameter="true"/>
<Parameter Name="CqyzOfD" Min="1" Max="4" Value="3" BitCount="2" StartBit="4" StartByte="255" DefaultValue="3" IsPresetParameter="true"/>
<Parameter Name="PrtOfB" BitCount="8" StartBit="0" StartByte="256"
        HighBitCount="8" HighStartBit="0" HighStartByte="257" IsPresetParameter="false"/>
<Parameter Name="PrtOfC" BitCount="8" StartBit="0" StartByte="258"
        HighBitCount="8" HighStartBit="0" HighStartByte="259" IsPresetParameter="false"/>
<Parameter Name="PrtOfD" BitCount="8" StartBit="0" StartByte="260"
        HighBitCount="8" HighStartBit="0" HighStartByte="261" IsPresetParameter="false"/>
<Parameter Name="MSampleRate" BitCount="8" StartBit="0" StartByte="262" Step="1" IsPresetParameter="false"/>
<Parameter Name="DSampleRate" BitCount="8" StartBit="0" StartByte="263" Step="1" IsPresetParameter="true"/>
<Parameter Name="BCSampleRate" BitCount="8" StartBit="0" StartByte="264" Step="1" IsPresetParameter="true"/>
<Parameter Name="BFrameNum" BitCount="4" StartBit="0" StartByte="265" IsPresetParameter="false"/>
<Parameter Name="MFStitch" BitCount="1" StartBit="5" StartByte="265" IsPresetParameter="false"/>
<Parameter Name="HBStitch" BitCount="1" StartBit="6" StartByte="265" IsPresetParameter="false"/>
<Parameter Name="TotalFrameNum" BitCount="5" StartBit="0" StartByte="266" IsPresetParameter="false"/>
<Parameter Name="ECGSignal" BitCount="1" StartBit="5" StartByte="266" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="PWMDutyRatio" BitCount="7" StartBit="0" StartByte="267" IsPresetParameter="false"/>
<Parameter Name="FourDBurnSignal" BitCount="1" StartBit="7" StartByte="267" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CFMEnergeCtl" BitCount="4" StartBit="4" StartByte="268" IsPresetParameter="false"/>
<Parameter Name="MaxGain" Value="198" BitCount="8" StartBit="0" StartByte="269" Max="198" IsPresetParameter="false"/>
<Parameter Name="BTgcGain" BitCount="8" StartBit="0" StartByte="270"
        HighBitCount="2" HighStartBit="0" HighStartByte="271" IsPresetParameter="false"/>
<Parameter Name="NeedleSSampleAdjust" BitCount="6" StartBit="2" StartByte="271" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl0" Value="255" BitCount="8" StartBit="0" StartByte="272" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl1" Value="255" BitCount="8" StartBit="0" StartByte="273" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl2" Value="255" BitCount="8" StartBit="0" StartByte="274" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl3" Value="255" BitCount="8" StartBit="0" StartByte="275" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl4" Value="255" BitCount="8" StartBit="0" StartByte="276" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl5" Value="255" BitCount="8" StartBit="0" StartByte="277" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl6" Value="255" BitCount="8" StartBit="0" StartByte="278" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl7" Value="255" BitCount="8" StartBit="0" StartByte="279" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl8" Value="255" BitCount="8" StartBit="0" StartByte="280" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl9" Value="255" BitCount="8" StartBit="0" StartByte="281" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl10" Value="255" BitCount="8" StartBit="0" StartByte="282" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl11" Value="255" BitCount="8" StartBit="0" StartByte="283" IsPresetParameter="false"/>
<Parameter Name="CfmEngCtrl12" Value="255" BitCount="8" StartBit="0" StartByte="284" IsPresetParameter="false"/>
<Parameter Name="CfmFlashVlt" BitCount="8" StartBit="0" StartByte="285" IsPresetParameter="false"/>
<Parameter Name="CfmFlashSlope" BitCount="8" StartBit="0" StartByte="286" IsPresetParameter="false"/>
<Parameter Name="CfmNoiseElt" Value="255" BitCount="8" StartBit="0" StartByte="287" IsPresetParameter="false"/>
<Parameter Name="CfmNoiseVlt" BitCount="8" StartBit="0" StartByte="288" IsPresetParameter="false"/>
<Parameter Name="CfmNoiseSlope" BitCount="8" StartBit="0" StartByte="289" IsPresetParameter="false"/>
<Parameter Name="CStaticOptimization" BitCount="1" StartBit="0" StartByte="290" IsPresetParameter="false"/>
<Parameter Name="CalSel" BitCount="4" StartBit="4" StartByte="290" IsPresetParameter="false"/>
<Parameter Name="FixedLineNum" BitCount="8" StartBit="0" StartByte="291"
        HighBitCount="1" HighStartBit="0" HighStartByte="292" IsPresetParameter="false"/>
<Parameter Name="ElemMode" BitCount="1" StartBit="1" StartByte="292" IsPresetParameter="false"/>
<Parameter Name="FixedLineType" BitCount="2" StartBit="2" StartByte="292" IsPresetParameter="false"/>
<Parameter Name="FixedLineStartup" BitCount="1" StartBit="4" StartByte="292" IsPresetParameter="false"/>
<Parameter Name="WTByPass" BitCount="1" StartBit="5" StartByte="292" IsPresetParameter="false"/>
<Parameter Name="ElemSignal" BitCount="1" StartBit="7" StartByte="292" IsPresetParameter="false"/>
<Parameter Name="DataSamplingStart" BitCount="8" StartBit="0" StartByte="293"
        HighBitCount="8" HighStartBit="0" HighStartByte="294" IsPresetParameter="false"/>
<Parameter Name="DataSamplingADChannel" BitCount="6" StartBit="0" StartByte="295" IsPresetParameter="false"/>
<Parameter Name="CfmSlope" BitCount="8" StartBit="0" StartByte="296" IsPresetParameter="false"/>
<Parameter Name="WaferNumDetected" Value="255" BitCount="8" StartBit="0" StartByte="297" IsPresetParameter="false"/>
<Parameter Name="PFrSd_PwrOldWt" Value="255" BitCount="8" StartBit="0" StartByte="298" IsPresetParameter="false"/>
<Parameter Name="EtBfWf" Value="255" BitCount="8" StartBit="0" StartByte="299" IsPresetParameter="false"/>
<Parameter Name="NewWallThreshold" BitCount="7" StartBit="0" StartByte="300" IsPresetParameter="false"/>
<Parameter Name="ImgFrz" BitCount="1" StartBit="7" StartByte="300" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CfmPersistentThreshold" Value="24" BitCount="8" StartBit="0" StartByte="301" IsPresetParameter="false"/>
<Parameter Name="WrapEn" Value="1" BitCount="1" StartBit="0" StartByte="302" IsPresetParameter="false"/>
<Parameter Name="CLFEn" Value="1" BitCount="1" StartBit="1" StartByte="302" IsPresetParameter="false"/>
<Parameter Name="CAFEn" Value="1" BitCount="1" StartBit="2" StartByte="302" IsPresetParameter="false"/>
<Parameter Name="DopOutGain" BitCount="4" StartBit="4" StartByte="302" IsPresetParameter="true"/>
<Parameter Name="TDOutGain" BitCount="4" StartBit="4" StartByte="302" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWOutGain" BitCount="4" StartBit="4" StartByte="302" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DopFrequency" BitCount="4" StartBit="0" StartByte="303" IsPresetParameter="false"/>
<Parameter Name="DopFilterCoef" BitCount="4" StartBit="4" StartByte="303" IsPresetParameter="false"/>
<Parameter Name="DopTxFNum" BitCount="3" StartBit="0" StartByte="304" IsPresetParameter="false"/>
<Parameter Name="DopRvFNum" BitCount="3" StartBit="3" StartByte="304" IsPresetParameter="false"/>
<Parameter Name="DopAudioSegment" BitCount="2" StartBit="6" StartByte="304" IsPresetParameter="true"/>
<Parameter Name="TDAudioSegment" BitCount="2" StartBit="6" StartByte="304" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWAudioSegment" BitCount="2" StartBit="6" StartByte="304" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DopDemodulationFilterCoef" BitCount="4" StartBit="0" StartByte="305" IsPresetParameter="false"/>
<Parameter Name="DopDemodulationFreq" BitCount="4" StartBit="4" StartByte="305" IsPresetParameter="false"/>
<Parameter Name="DopPulseDuty" BitCount="5" StartBit="0" StartByte="306" IsPresetParameter="false"/>
<Parameter Name="DopSampleNum" BitCount="3" StartBit="5" StartByte="306" IsPresetParameter="true"/>
<Parameter Name="TDSampleNum" BitCount="3" StartBit="5" StartByte="306" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWSampleNum" BitCount="3" StartBit="5" StartByte="306" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DopMaxAperture" BitCount="7" StartBit="0" StartByte="307" IsPresetParameter="false"/>
<Parameter Name="WrapEnQ" BitCount="1" StartBit="7" StartByte="307" IsPresetParameter="false"/>
<Parameter Name="DopAudioPostGain" BitCount="2" StartBit="0" StartByte="308" IsPresetParameter="true"/>
<Parameter Name="TDAudioPostGain" BitCount="2" StartBit="0" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWAudioPostGain" BitCount="2" StartBit="0" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DopAudioPreGain" BitCount="2" StartBit="2" StartByte="308" IsPresetParameter="true"/>
<Parameter Name="TDAudioPreGain" BitCount="2" StartBit="2" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWAudioPreGain" BitCount="2" StartBit="2" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DopTimeFilter" BitCount="4" StartBit="4" StartByte="308" IsPresetParameter="true"/>
<Parameter Name="TDTimeFilter" BitCount="4" StartBit="4" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWTimeFilter" BitCount="4" StartBit="4" StartByte="308" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DopFilterLength" BitCount="8" StartBit="0" StartByte="309" IsPresetParameter="true"/>
<Parameter Name="TDFilterLength" BitCount="8" StartBit="0" StartByte="309" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWFilterLength" BitCount="8" StartBit="0" StartByte="309" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DopVelocityFilter" BitCount="4" StartBit="0" StartByte="310" IsPresetParameter="true"/>
<Parameter Name="TDVelocityFilter" BitCount="4" StartBit="0" StartByte="310" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWVelocityFilter" BitCount="4" StartBit="0" StartByte="310" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="DopMidGain" BitCount="4" StartBit="4" StartByte="310" IsPresetParameter="true"/>
<Parameter Name="TDMidGain" BitCount="4" StartBit="4" StartByte="310" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWMidGain" BitCount="4" StartBit="4" StartByte="310" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="VersionInfor" BitCount="1" StartBit="0" StartByte="311" IsPresetParameter="false"/>
<Parameter Name="VersionDisplayMode" BitCount="1" StartBit="1" StartByte="311" IsPresetParameter="false"/>
<Parameter Name="MotherboardShield" BitCount="1" StartBit="2" StartByte="311" IsPresetParameter="false"/>
<Parameter Name="ExtensionPlateShield" BitCount="1" StartBit="3" StartByte="311" IsPresetParameter="false"/>
<Parameter Name="InspectionMode" BitCount="2" StartBit="0" StartByte="312" IsPresetParameter="false"/>
<Parameter Name="LineSpaceCode" BitCount="4" StartBit="4" StartByte="312" IsPresetParameter="false"/>
<Parameter Name="InspectionNum" BitCount="8" StartBit="0" StartByte="313" IsPresetParameter="false"/>
<Parameter Name="DopFNumDecimal" BitCount="8" StartBit="0" StartByte="314"
        HighBitCount="4" HighStartBit="0" HighStartByte="315" IsPresetParameter="false"/>
<Parameter Name="DopFNumInteger" BitCount="4" StartBit="4" StartByte="315" IsPresetParameter="false"/>
<Parameter Name="XCoordinate" BitCount="8" StartBit="0" StartByte="316"
        HighBitCount="2" HighStartBit="0" HighStartByte="318" IsPresetParameter="false"/>
<Parameter Name="YCoordinate" BitCount="8" StartBit="0" StartByte="317"
        HighBitCount="2" HighStartBit="2" HighStartByte="318" IsPresetParameter="false"/>
<Parameter Name="XCoordinateEn" BitCount="1" StartBit="4" StartByte="318" IsPresetParameter="false"/>
<Parameter Name="YCoordinateEn" BitCount="1" StartBit="5" StartByte="318" IsPresetParameter="false"/>
<Parameter Name="LineNoShield" BitCount="8" StartBit="0" StartByte="319"
        HighBitCount="1" HighStartBit="7" HighStartByte="318" IsPresetParameter="false"/>
<Parameter Name="DopMinTransmitAperture" BitCount="7" StartBit="0" StartByte="320" IsPresetParameter="false"/>
<Parameter Name="MinReceiveAperture" BitCount="7" StartBit="0" StartByte="321" IsPresetParameter="false"/>
<Parameter Name="MaxReceiveAperture" Value="63" BitCount="7" StartBit="0" StartByte="322" IsPresetParameter="false"/>
<Parameter Name="ReceiveFNumDecimal" BitCount="8" StartBit="0" StartByte="323"
        HighBitCount="4" HighStartBit="0" HighStartByte="324" IsPresetParameter="false"/>
<Parameter Name="ReceiveFNumInteger" BitCount="4" StartBit="4" StartByte="324" IsPresetParameter="false"/>
<Parameter Name="CfmMinReceiveAperture" BitCount="7" StartBit="0" StartByte="325" IsPresetParameter="false"/>
<Parameter Name="CfmMaxReceiveAperture" Value="63" BitCount="7" StartBit="0" StartByte="326" IsPresetParameter="false"/>
<Parameter Name="CfmReceiveFNumDecimal" BitCount="8" StartBit="0" StartByte="327"
        HighBitCount="4" HighStartBit="0" HighStartByte="328" IsPresetParameter="false"/>
<Parameter Name="CfmReceiveFNumInteger" BitCount="4" StartBit="4" StartByte="328" IsPresetParameter="false"/>
<Parameter Name="DopMinReceiveAperture" BitCount="7" StartBit="0" StartByte="329" IsPresetParameter="false"/>
<Parameter Name="DopMaxReceiveAperture" BitCount="7" StartBit="0" StartByte="330" IsPresetParameter="false"/>
<Parameter Name="DopReceiveFNumDecimal" BitCount="8" StartBit="0" StartByte="331"
        HighBitCount="4" HighStartBit="0" HighStartByte="332" IsPresetParameter="false"/>
<Parameter Name="DopReceiveFNumInteger" BitCount="4" StartBit="4" StartByte="332" IsPresetParameter="false"/>
<Parameter Name="ShieldSPPosition" BitCount="8" StartBit="0" StartByte="333"
        HighBitCount="2" HighStartBit="0" HighStartByte="334" IsPresetParameter="false"/>
<Parameter Name="ShieldSPEn" BitCount="1" StartBit="2" StartByte="334" IsPresetParameter="false"/>
<Parameter Name="TeeFlag" BitCount="1" StartBit="7" StartByte="334" IsPresetParameter="false"/>
<Parameter Name="PixelsWithinFocus" BitCount="8" StartBit="0" StartByte="335"
        HighBitCount="2" HighStartBit="0" HighStartByte="336" IsPresetParameter="false"/>
<Parameter Name="DopAccumulateNum" BitCount="8" StartBit="0" StartByte="337" IsPresetParameter="false"/>
<Parameter Name="DopAnalogTgc0" Value="192" BitCount="8" StartBit="0" StartByte="338" IsPresetParameter="false"/>
<Parameter Name="DopAnalogTgc1" Value="255" BitCount="8" StartBit="0" StartByte="339" IsPresetParameter="false"/>
<Parameter Name="DopAnalogTgc2" Value="255" BitCount="8" StartBit="0" StartByte="340" IsPresetParameter="false"/>
<Parameter Name="DopAnalogTgc3" Value="255" BitCount="8" StartBit="0" StartByte="341" IsPresetParameter="false"/>
<Parameter Name="DopAnalogTgc4" Value="255" BitCount="8" StartBit="0" StartByte="342" IsPresetParameter="false"/>
<Parameter Name="DopAnalogTgc5" Value="255" BitCount="8" StartBit="0" StartByte="343" IsPresetParameter="false"/>
<Parameter Name="DopAnalogTgc6" Value="255" BitCount="8" StartBit="0" StartByte="344" IsPresetParameter="false"/>
<Parameter Name="DopAnalogTgc7" Value="255" BitCount="8" StartBit="0" StartByte="345" IsPresetParameter="false"/>
<Parameter Name="ECGDly" BitCount="8" StartBit="0" StartByte="346" IsPresetParameter="true"/>
<Parameter Name="MECGDly" BitCount="8" StartBit="0" StartByte="346" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWECGDly" BitCount="8" StartBit="0" StartByte="346" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CEmissionFrequencyCoef" BitCount="8" StartBit="0" StartByte="347" IsPresetParameter="false"/>
<Parameter Name="CTransmitPulseDutyCoef" BitCount="8" StartBit="0" StartByte="348" IsPresetParameter="false"/>
<Parameter Name="NeedleEmissionFrequencyCoef" BitCount="8" StartBit="0" StartByte="349" IsPresetParameter="false"/>
<Parameter Name="NeedleTransmitPulseDutyCoef" BitCount="8" StartBit="0" StartByte="350" IsPresetParameter="false"/>
<Parameter Name="CSteeringAngleCoding" BitCount="6" StartBit="0" StartByte="351" IsPresetParameter="false"/>
<Parameter Name="NeedleSteeringAngleCoding" BitCount="6" StartBit="0" StartByte="352" IsPresetParameter="false"/>
<Parameter Name="BSteeringAngleCoding" BitCount="6" StartBit="0" StartByte="353" IsPresetParameter="false"/>
<Parameter Name="CWeightedCurveType" BitCount="6" StartBit="0" StartByte="354" IsPresetParameter="false"/>
<Parameter Name="NeedleWeightedCurveType" BitCount="6" StartBit="0" StartByte="355" IsPresetParameter="false"/>
<Parameter Name="DWeightedCurveType" BitCount="6" StartBit="0" StartByte="356" IsPresetParameter="false"/>
<Parameter Name="FScpd" ValueType="Bool" BitCount="1" StartBit="3" StartByte="365" IsPresetParameter="false"/>
<Parameter Name="ThiMode4" BitCount="1" StartBit="4" StartByte="365" IsPresetParameter="false"/>
<Parameter Name="ThiMode3" BitCount="1" StartBit="5" StartByte="365" IsPresetParameter="false"/>
<Parameter Name="ThiMode2" BitCount="1" StartBit="6" StartByte="365" IsPresetParameter="false"/>
<Parameter Name="ThiMode1" BitCount="1" StartBit="7" StartByte="365" IsPresetParameter="false"/>
<Parameter Name="MachineIdentity" BitCount="3" StartBit="2" StartByte="376" IsPresetParameter="false"/>
<Parameter Name="LinePackageCount" BitCount="8" StartBit="0" StartByte="375" IsPresetParameter="false"/>
<Parameter Name="LineDataMode" BitCount="1" StartBit="7" StartByte="376" IsPresetParameter="false"/>
<Parameter Name="InfoLineEnableSignal" BitCount="1" StartBit="6" StartByte="376" IsPresetParameter="false"/>
<Parameter Name="TableID" BitCount="8" StartBit="0" StartByte="377" IsPresetParameter="false"/>
<Parameter Name="DebugPara5" BitCount="8" StartBit="0" StartByte="402" IsPresetParameter="false"/>
<Parameter Name="AudioTest_LR" BitCount="2" StartBit="0" StartByte="404" IsPresetParameter="false"/>
<Parameter Name="AudioTest_Tone" BitCount="2" StartBit="2" StartByte="404" IsPresetParameter="false"/>
<Parameter Name="BTriplexPrt_Delta" BitCount="8" StartBit="0" StartByte="407"
        HighBitCount="8" HighStartBit="0" HighStartByte="408" Step="100" IsPresetParameter="true"/>
<Parameter Name="ThiTxDummy" BitCount="8" StartBit="0" StartByte="409" IsPresetParameter="true"/>
<Parameter Name="FFocusPrtDelta" BitCount="8" StartBit="0" StartByte="418"
        HighBitCount="8" HighStartBit="0" HighStartByte="419" IsPresetParameter="true"/>
<Parameter Name="VesselThreshold" Value="0" BitCount="4" StartBit="0" StartByte="421" IsPresetParameter="false"/>
<Parameter Name="DetailWeightBin" Value="0" BitCount="8" StartBit="0" StartByte="422" IsPresetParameter="false"/>
<Parameter Name="MinWeightBin" Value="128" BitCount="8" StartBit="0" StartByte="423" IsPresetParameter="false"/>
<Parameter Name="MaxWeightBin" Value="128" BitCount="8" StartBit="0" StartByte="424" IsPresetParameter="false"/>
<Parameter Name="EdgeWeightBin" Value="0" BitCount="8" StartBit="0" StartByte="425" IsPresetParameter="false"/>
<Parameter Name="ECGDynamicRange" Value="0" BitCount="3" StartBit="0" StartByte="434" IsPresetParameter="true"/>
<Parameter Name="SpecFrh" BitCount="1" StartBit="6" StartByte="441" IsPresetParameter="false"/>
<Parameter Name="CWSwithCode" BitCount="1" StartBit="5" StartByte="441" IsPresetParameter="false"/>
<Parameter Name="LineCpdPara" BitCount="8" StartBit="0" StartByte="442" IsPresetParameter="false"/>
<Parameter Name="DLineCorrectPara" BitCount="4" StartBit="0" StartByte="443" IsPresetParameter="true"/>
<Parameter Name="MLineCorrectPara" BitCount="4" StartBit="0" StartByte="443" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TDLineCorrectPara" BitCount="4" StartBit="0" StartByte="443" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CWGainReduction" BitCount="8" StartBit="0" StartByte="447" IsPresetParameter="true"/>
<Parameter Name="CWGainReductionSlope" BitCount="8" StartBit="0" StartByte="448" IsPresetParameter="true"/>
<Parameter Name="VideoSOCutH" BitCount="8" StartBit="0" StartByte="457"
        HighBitCount="3" HighStartBit="0" HighStartByte="458" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="VideoSOCutV" BitCount="5" StartBit="3" StartByte="458"
        HighBitCount="6" HighStartBit="0" HighStartByte="459" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="VideoEOCutH" BitCount="2" StartBit="6" StartByte="459"
        HighBitCount="8" HighStartBit="0" HighStartByte="460"
        ThirdBitCount="1" ThirdStartBit="0" ThirdStartByte="461" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="VideoEOCutV" BitCount="7" StartBit="1" StartByte="461"
        HighBitCount="4" HighStartBit="0" HighStartByte="462" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="VideoCprsHSize" BitCount="4" StartBit="4" StartByte="462"
        HighBitCount="6" HighStartBit="0" HighStartByte="463" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="VideoCprsVSize" BitCount="2" StartBit="6" StartByte="463"
        HighBitCount="8" HighStartBit="0" HighStartByte="464" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="VideoCprsCoef" BitCount="8" StartBit="0" StartByte="465"
        HighBitCount="8" HighStartBit="0" HighStartByte="466"
        ThirdBitCount="2" ThirdStartBit="0" ThirdStartByte="467" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="VideoFecture" BitCount="1" StartBit="2" StartByte="467" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="VideoSwitch" BitCount="1" StartBit="3" StartByte="467" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="VideoCprsEn" BitCount="1" StartBit="4" StartByte="467" ValueType="Bool" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="UpLoadBuffFrz" BitCount="1" StartBit="5" StartByte="467" ValueType="Bool" IsPresetParameter="false"/>
<Parameter Name="BTxEndClamp" Value="100" BitCount="8" StartBit="0" StartByte="468" IsPresetParameter="false"/>
<Parameter Name="CTxEndClamp" Value="100" BitCount="8" StartBit="0" StartByte="469" IsPresetParameter="false"/>
<Parameter Name="KeyBoardLightEn" BitCount="1" StartBit="3" StartByte="569" ValueType="Bool" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="KeyBoardLightLevel" BitCount="4" StartBit="4" StartByte="569"  IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="FreeMMode" BitCount="1" StartBit="7" StartByte="640" ValueType="Bool" IsPresetParameter="false"/>
<Parameter Name="CWGain" BitCount="8" StartBit="0" StartByte="716" Value="220" IsPresetParameter="false"/>
</BFParameterGroup>
<BFParameterGroup Type="Common">
<Parameter Name="ADFreqMHz" Value="32" IsPresetParameter="false"/>
<Parameter Name="ADOffsetTimeNs" Value="36000" IsPresetParameter="false"/>
<Parameter Name="SonicSpeed" Value="1540000" IsPresetParameter="true"/>
<Parameter Name="TssIncrement" IsPresetParameter="false"/>
<Parameter Name="MTriggeredAP" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CTriggeredAP" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="SystemScanMode" IsPresetParameter="false"/>
<Parameter Name="ProbeId" IsPresetParameter="false"/>
<Parameter Name="ScanWidth" Min="0" Max="9" Value="3" IsPresetParameter="false"/>
<Parameter Name="FocusNumShow" IsPresetParameter="false"/>
<Parameter Name="ImageZoomCoefOther" Min="100" Max="100" Step="1" Value="100" IsPresetParameter="false"/>
<Parameter Name="ImageZoomCoefB" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="false"/>
<!--ImageZoomCoefOther ImageZoomCoef ImageZoomCoefB 需要保证此顺序-->
<Parameter Name="ImageZoomCoef" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="true"/>
<Parameter Name="IsFullScreenZoomIn" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FullScreenZoomInIndex" Min="0" Max="4" Step="1" Value="2" IsPresetParameter="false"/>
<Parameter Name="DRDiffWithCompoundOn" Min="-5" Max="5" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="DRDiffWithSraOn" Min="-5" Max="5" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="DRDiffWithThiOn" Min="-5" Max="5" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="ScpdOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="FcpdOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="TrapezoidalMode" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="CompoundByFPGA" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="DynStartDepth" Min="0" Max="35" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="DynStartDB" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="DynEndDepth" Min="0" Max="35" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="DynEndDB" Min="0" Max="15" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="FreqSettingIds" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="AFSettingIds" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="LeeSettingIds" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="CLFStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="CAFStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="TDILFStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="TDIAFStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="PDLFStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="PDAFStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="iImage" Min="0" Max="3" Step="1" IsPresetParameter="true"/>
<Parameter Name="RvFNo" Min="0" Max="7" Step="1" Value="3" IsPresetParameter="false"/>
<Parameter Name="RvPWFNo" Min="0" Max="7" Step="1" Value="3" IsPresetParameter="false"/>
<Parameter Name="TxFNo" Min="0" Max="7" Step="1" Value="5" IsPresetParameter="false"/>
<Parameter Name="CPDSteer" Min="0" Max="30" Step="1" Value="5" IsPresetParameter="false"/>
<Parameter Name="TrapezoidalCPDSteer" Min="0" Max="20" Step="1" DefaultValue="10" IsPresetParameter="true"/>
<Parameter Name="PersistDiffWithCpdOn" Min="-7" Max="7" Step="1" Value="0" IsPresetParameter="true"/>
<Parameter Name="PersistDiffWithSraOn" Min="-7" Max="7" Step="1" Value="0" IsPresetParameter="true"/>
<Parameter Name="PersistentDeltaTHI" Min="-7" Max="7" IsPresetParameter="true"/>
<Parameter Name="PersistentDeltaCFM" Min="-7" Max="7" IsPresetParameter="true"/>
<Parameter Name="PersistentDeltaPD" Min="-7" Max="7" IsPresetParameter="true"/>
<Parameter Name="PersistentDeltaTDI" Min="-7" Max="7" IsPresetParameter="true"/>
<Parameter Name="Flow" Value="1" Min="0" Max="3" DefaultValue="1" IsPresetParameter="true"/>
<Parameter Name="FlowTDI" Value="1" Min="0" Max="3" DefaultValue="1" IsPresetParameter="true"/>
<Parameter Name="ZoomedCQYZ" Min="1" IsPresetParameter="false"/>
<Parameter Name="ZoomMultiIndex" Min="0" Max="2" Value="0" IsPresetParameter="false"/>
<Parameter Name="ZoomMidLine" IsPresetParameter="false"/>
<Parameter Name="ZoomHalfLines" IsPresetParameter="false"/>
<Parameter Name="ZoomMidDepthMM" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="ZoomHalfDepthMM" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Scroll" Min="0" Max="255" Value="0" IsPresetParameter="false"/>
<Parameter Name="IsScroll" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="ActiveB" Min="0" Max="3" Value="0" IsPresetParameter="false"/>
<Parameter Name="FPS" IsPresetParameter="false"/>
<Parameter Name="Harmonic" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="PRFColorKHZ" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="PRFDopKHZ" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="BaseLineColor" Min="0" Max="6" Value="3" IsPresetParameter="true"/>
<Parameter Name="BaseLineDPD" Min="0" Max="6" Value="3" DefaultValue="3" IsPresetParameter="true"/>
<Parameter Name="BaseLineTDI" Min="0" Max="6" Value="3" DefaultValue="3" IsPresetParameter="true"/>
<Parameter Name="DopplerTheta" Min="-70" Max="70" Step="10" Value="0" IsPresetParameter="true"/>
<Parameter Name="DopplerThetaTDI" Min="-70" Max="70" Step="10" Value="0" IsPresetParameter="true"/>
<Parameter Name="DopplerThetaCWD" Min="-70" Max="70" Step="10" Value="0" IsPresetParameter="true"/>
<Parameter Name="RoiMidLine" IsPresetParameter="true"/>
<Parameter Name="RoiHalfLines" IsPresetParameter="true"/>
<Parameter Name="RoiMidDepthMM" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="RoiHalfDepthMM" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="RoiMidLineTDI" IsPresetParameter="true"/>
<Parameter Name="RoiHalfLinesTDI" IsPresetParameter="true"/>
<Parameter Name="RoiMidDepthMMTDI" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="RoiHalfDepthMMTDI" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="RoiMidLineFourD" DefaultValue="128" IsPresetParameter="true"/>
<Parameter Name="RoiHalfLinesFourD" DefaultValue="44" IsPresetParameter="true"/>
<Parameter Name="RoiMidDepthMMFourD" DefaultValue="64" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="RoiHalfDepthMMFourD" DefaultValue="36" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="FourDKnotLine" IsPresetParameter="false"/>
<Parameter Name="FourDKnotDepthMM" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="SampleVolumeMM" Min="1" Max="8" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="SampleVolumeTDIMM" Min="1" Max="8" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="DopplerStartDepthMM" ValueType="Double" IsPresetParameter="true"/>
<Parameter Name="DopplerTDIStartDepthMM" ValueType="Double" IsPresetParameter="true"/>
<Parameter Name="CMaxVelCMS" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DMaxVelCMS" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PRFColor" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PRFDop" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PRFCWD" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="BRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="BTHIGamma" Min="0" Max="8" Value="4" DefaultValue="4" IsPresetParameter="true"/>
<Parameter Name="BTHIRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="MGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="MRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="PwGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="PwRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="PwTDIGamma" Min="0" Max="8" Value="4" DefaultValue="4" IsPresetParameter="true"/>
<Parameter Name="PwTDIRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="CwdGamma" Min="0" Max="8" Value="4" DefaultValue="4" IsPresetParameter="true"/>
<Parameter Name="CwdRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="BGammaPos" Value="16" Min="0" Max="255" DefaultValue="16" IsPresetParameter="true"/>
<Parameter Name="BGammaStart" Value="60" Min="1" Max="100" DefaultValue="60" IsPresetParameter="true"/>
<Parameter Name="BGammaStep" Value="10" Min="1" Max="100" DefaultValue="10" IsPresetParameter="true"/>
<Parameter Name="THIState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="ColorInvertState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="DPDInvertState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="TDIInvertState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="BColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="BTDIColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CfColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PdColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="DpdColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="TDIColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PwColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PwTDIColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="MColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CwdColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CPColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="BGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="BTHIGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="MGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PwGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PwTDIGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CwdGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="VarColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="FourDColorMapIndex" Min="0" Max="6" IsPresetParameter="true"/>
<!--控制 BColorMapIndex BTDIColorMapIndex-->
<Parameter Name="BColorMapIndexShow" Min="0" Max="7" IsPresetParameter="false"/>
<!--控制 CfColorMapIndex VarColorMapIndex-->
<Parameter Name="CfColorMapIndexShow" Min="0" Max="7" IsPresetParameter="false"/>
<Parameter Name="BGrayCurveIndexShow" Min="0" Max="7" IsPresetParameter="false"/>
<Parameter Name="BGammaShow" Min="0" Max="8" IsPresetParameter="false"/>
<Parameter Name="BRejectionShow" Min="0" Max="256" IsPresetParameter="false"/>
<!--控制Color时 的彩色模式 CF/VAR-->
<Parameter Name="CFMode" Min="0" Max="1" IsPresetParameter="true"/>
<Parameter Name="CfContrast" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PdContrast" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="DpdContrast" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="ImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="BImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="MImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="DImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="DataBitCount" Value="8" IsPresetParameter="false"/>
<Parameter Name="PointNumPerLine" Value="600" IsPresetParameter="false"/>
<Parameter Name="StartDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="MPixelSizeSec" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="MPixelSizeMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DPixelSizeCMS" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DPixelSizeSec" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DBaseLineYPos" ValueType="Int" IsPresetParameter="false"/>
<Parameter Name="ZoomDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="ScrollDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="HotLogoDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="HotLogoLine" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="AIO" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsBiopsyVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ZoomSelect" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="BiopsyAngle" IsPresetParameter="false"/>
<Parameter Name="BiopsyXPosMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BiopsyYPosMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BiopsyAngleOffset" IsPresetParameter="false"/>
<Parameter Name="BiopsyXPosMMOffset" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BiopsyYPosMMOffset" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="IsCenterLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsDopplerScanLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsCWDScanLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsMLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsUDBM" ValueType="Bool" Value="false" TrueValue="1" DefaultValue="false" IsPresetParameter="true"/>
<Parameter Name="GrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="MGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="PwGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="CwdGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="TGC" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="MI" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="TIS" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="TIB" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="ExamModeId" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="ExamModeCaption" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="ProbeConnected" ValueType="Bool" Value="false"/>
<!--ImageRects is List<QRect>-->
<Parameter Name="ImageRects" ValueType="List" IsPresetParameter="false"/>
<!--ImageRegions is List<QRect> for meas region-->
<Parameter Name="ImageRegions" ValueType="List" IsPresetParameter="false"/>
<!--SamplePoints is ByteArray-->
<Parameter Name="BSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="BTHISamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="MSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="PwSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="PwTDISamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="CwdSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="BReject"/>
<Parameter Name="SraGainColorDelta" Min="-64" Max="64" IsPresetParameter="false"/>
<Parameter Name="TxOff" ValueType="Bool" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="Layout" Value="1" Min="1" Max="4" IsPresetParameter="false"/>
<Parameter Name="Rect" ValueType="Rect" IsPresetParameter="false"/>
<Parameter Name="WeightedCurve" Min="0" Max="3" IsPresetParameter="false"/>
<Parameter Name="FCA_Alpha" Min="0" Max="9" IsPresetParameter="false"/>
<Parameter Name="CVIESettingIndex" Min="0" Max="3" IsPresetParameter="true"/>
<Parameter Name="CVIESettingName" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="ColorJudgeThreshold" Value="50" IsPresetParameter="false"/>
<Parameter Name="ImageOptimizationAlg" Value="0" Min="0" Max="2" IsPresetParameter="false"/>
<Parameter Name="Threshold1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Threshold2" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Threshold3" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Edge1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Edge2" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Edge3" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Smooth1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Smooth2" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Smooth3" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Iteration1" IsPresetParameter="false"/>
<Parameter Name="Iteration2" IsPresetParameter="false"/>
<Parameter Name="Iteration3" IsPresetParameter="false"/>
<Parameter Name="Contrast" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="OptimizationLevel" IsPresetParameter="false"/>
<Parameter Name="Edge" Max="6" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="EdgeThi" Max="6" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="FreqIndexB" Min="0" Max="4" IsDirectValue="true" IsPresetParameter="true"/>
<Parameter Name="FreqIndexColor" Min="0" Max="4" IsDirectValue="true" IsPresetParameter="true"/>
<Parameter Name="FreqIndexPD" Min="0" Max="4" IsDirectValue="true" IsPresetParameter="true"/>
<Parameter Name="FreqIndexTDI" Min="0" Max="4" IsDirectValue="true" IsPresetParameter="true"/>
<Parameter Name="FreqIndexDop" Min="0" Max="4" IsPresetParameter="true"/>
<Parameter Name="FreqIndexTD" Min="0" Max="4" IsDirectValue="true" IsPresetParameter="true"/>
<Parameter Name="FreqIndexCWD" Min="0" Max="0" IsDirectValue="true" IsPresetParameter="true"/>
<Parameter Name="AcousticPowerBShow" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ECGDlyShow" Min="0" Max="255" IsPresetParameter="false"/>
<!--只用于显示在图像区右侧参数区，不做控制-->
<Parameter Name="GainShow" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="EdgeShow" Min="0" Max="6" IsPresetParameter="false"/>
<Parameter Name="BWGainDelta" Min="-255" Max="255" IsPresetParameter="true"/>
<Parameter Name="AdapPostProcDelta" Min="-15" Max="15" IsPresetParameter="true"/>
<Parameter Name="ECGPosShow" Min="0" Max="7" IsPresetParameter="false"/>
<!--SampleRateDopShow 控制 SampleRateDop SampleRateDopTM-->
<Parameter Name="SampleRateDopShow" Min="0" Max="15" IsPresetParameter="false"/>
<Parameter Name="DSampleRateDopShow" Min="0" Max="15" IsPresetParameter="false"/>
<Parameter Name="PixelRatioShow" Min="0" Max="7" IsPresetParameter="false"/>
<!--PWEnhanceShow 控制 PWEnhance PWEnhanceTM-->
<Parameter Name="PWEnhanceShow" Min="0" Max="3" IsPresetParameter="false"/>
<!--PWDynamicRangeShow 控制 PWDynamicRange PWDynamicRangeTM-->
<Parameter Name="PWDynamicRangeShow" Min="0" Max="7" IsPresetParameter="false"/>
<!--DummyEnSampleShow(Color的参数) 控制 DummyEnSample DummyEnSampleTM-->
<Parameter Name="DummyEnSampleShow" Min="0" Max="3" IsPresetParameter="false"/>
<Parameter Name="CFMVelLevelShow" Min="0" Max="15" IsPresetParameter="false"/>
<!--只用于显示在图像区右侧参数区，不做控制-->
<Parameter Name="GainDopShow" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ColorCoef" Min="0" Max="6" IsPresetParameter="true"/>
<Parameter Name="PDCoef" Min="0" Max="6" IsPresetParameter="true"/>
<Parameter Name="TDICoef" Min="0" Max="6" IsPresetParameter="true"/>
<Parameter Name="CVRTDelta" Min="-15" Max="15" IsPresetParameter="true"/>
<Parameter Name="CVLTDelta" Min="-127" Max="127" IsPresetParameter="true"/>
<Parameter Name="CETDelta" Min="-15" Max="15" IsPresetParameter="true"/>
<!--AudioFilterCoefSelShow 控制 AudioFilterCoefSel AudioFilterCoefSelTM-->
<Parameter Name="AudioFilterCoefSelShow" Min="0" Max="7" IsPresetParameter="false"/>
<!--IDs 分两种情况，Ids[0]不开THI的 Ids[1]开THI的-->
<Parameter Name="NeedleStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="TransmitStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="ColorFreqStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="PDFreqStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="TDIFreqStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="DopFreqStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="TDFreqStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="CWDFreqStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="ColorTransmitStrIds" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="DynDeltaOnXContrast" Min="-7" Max="7" IsPresetParameter="false"/>
<Parameter Name="BFreqDelta" Min="-2" Max="2" Step="0.1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="TransmitStepDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="TransmitBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="ReceiveFocusDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="ReceiveBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="ReceiveUnitVariFocusNum" Value="1" Min="1" Max="64" IsPresetParameter="false"/>
<Parameter Name="WallFilterSetting" Value="0,1,2,3" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="CfmFnum" Min="0" Max="7" IsPresetParameter="false"/>
<Parameter Name="CfmRxFnum" Min="0" Max="7" IsPresetParameter="false"/>
<Parameter Name="MaxSteeringAngle" Value="-1" Min="-1" Max="3" IsPresetParameter="false"/>
<!--TODO:Float value min max step support-->
<Parameter Name="ColorFreqDelta" Min="-2" Max="2" Step="0.1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="CFMRoi_Delta" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="CfmTransmitStepDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="CfmTransmitBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="CfmReceiveFocusDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="CfmReceiveBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="CfmReceiveUnitVariFocusNum" Value="1" Min="1" Max="64" IsPresetParameter="false"/>
<Parameter Name="CWScanLineDelta" Min="-10" Max="10" IsPresetParameter="true"/>
<Parameter Name="CWMidDepthMMDelta" Min="-100" Max="100" Step="10" IsPresetParameter="true"/>
<Parameter Name="CTxPulseDuty" Min="0" Max="31" IsPresetParameter="false"/>
<Parameter Name="CTxPulseDuty_PW" Min="0" Max="31" IsPresetParameter="false"/>
<Parameter Name="CfmMaxTransmitApertureControl" Value="63" Min="0" Max="63" IsPresetParameter="false"/>
<Parameter Name="CfmMaxTransmitApertureControl_PW" Value="63" Min="0" Max="63" IsPresetParameter="false"/>
<Parameter Name="CfmMinTransmitApertureControl" Min="0" Max="63" IsPresetParameter="false"/>
<Parameter Name="CfmMinTransmitApertureControl_PW" Min="0" Max="63" IsPresetParameter="false"/>
<Parameter Name="TgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta8" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta9" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta10" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta11" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta12" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta13" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta14" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DTgcDelta15" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta8" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta9" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta10" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta11" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta12" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta13" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta14" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="TriplexDTgcDelta15" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta8" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta9" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta10" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta11" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta12" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta13" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta14" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiTriplexDTgcDelta15" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta0" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta1" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta2" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta3" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta4" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta5" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta6" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta7" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta8" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta9" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta10" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta11" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta12" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta13" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta14" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="ThiDTgcDelta15" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="DopFreqDelta" Min="-2" Max="2" Step="0.1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="DopFNum" Min="0" Max="7" IsPresetParameter="false"/>
<Parameter Name="DopTransmitStepDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="DopTransmitBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="DopReceiveFocusDis" Value="1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="DopReceiveBeamDevEleNum" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="DopReceiveUnitVariFocusNum" Value="1" Min="1" Max="64" IsPresetParameter="false"/>
<Parameter Name="QFlowMode" ValueType="Bool" Value="false" IsPresetParameter="true"/>
<Parameter Name="QFlowOn" ValueType="Bool" Value="true" DefaultValue="true" IsPresetParameter="true"/>
<Parameter Name="QBeamOn" ValueType="Bool" Value="true" DefaultValue="true" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="XContrastValue" Value="1" Min="0" Max="2" DefaultValue="1" IsPresetParameter="true"/>
<Parameter Name="BCImagesOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="AdjustmentOfB" Value="479" Min="0" Max="4095" DefaultValue="479" IsPresetParameter="true"/>
<Parameter Name="AdjustmentOfC" Value="479" Min="0" Max="4095" DefaultValue="479" IsPresetParameter="true"/>
<Parameter Name="AdjustmentOfD" Value="479" Min="0" Max="4095" DefaultValue="479" IsPresetParameter="true"/>
<Parameter Name="RvTxB" Min="0" Max="65536" IsPresetParameter="false"/>
<Parameter Name="RvTxC" Min="0" Max="65536" IsPresetParameter="false"/>
<Parameter Name="RvTxD" Min="0" Max="65536" IsPresetParameter="false"/>
<Parameter Name="PrtDeltaOnLowDensity" Min="-32768" Max="32768" IsPresetParameter="true"/>
<Parameter Name="ImagePixelBits" Value="7" Min="1" Max="8" DefaultValue="7" IsPresetParameter="false"/>
<Parameter Name="NeedleIndex" Min="0" Max="6" IsPresetParameter="false"/>
<Parameter Name="TriplexFrequencyDeltaTr1" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
<Parameter Name="TriplexFilterCoefDeltaTr1" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
<Parameter Name="TriplexFrequencyDeltaTr2" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
<Parameter Name="TriplexFilterCoefDeltaTr2" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
<Parameter Name="TriplexFrequencyDeltaTr3" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
<Parameter Name="TriplexFilterCoefDeltaTr3" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
<Parameter Name="TriplexFrequencyDeltaTr4" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
<Parameter Name="TriplexFilterCoefDeltaTr4" Value="0" Min="-7" Max="7" IsPresetParameter="false"/>
<Parameter Name="ROISteerAngles" Value="5,10,20" ValueType="String" IsPresetParameter="true"/>
<Parameter Name="TICEn" Value="false" ValueType="Bool" IsPresetParameter="true"/>
<Parameter Name="TIC" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Threshold" Value="1" Min="1" Max="25" DefaultValue="1" IsPresetParameter="true"/>
<Parameter Name="DTraceSmooth" Value="2" Min="0" Max="3" DefaultValue="2" IsPresetParameter="true"/>
<Parameter Name="ImageNum" Value="0" Min="0" IsPresetParameter="false"/>
<Parameter Name="SopInstanceId" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="SexInfo" ValueType="String" Value="" IsPresetParameter="false"/>
<Parameter Name="ImageZoomRatio" ValueType="Float" Value="1" IsPresetParameter="false"/>
<Parameter Name="ImageLeftTopPointX" ValueType="Int" Value="0" IsPresetParameter="false"/>
<Parameter Name="ImageLeftTopPointY" ValueType="Int" Value="0" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_0" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_1" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_2" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_3" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="ElastoStrainThreshold1" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
<Parameter Name="ElastoStrainThreshold2" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
<Parameter Name="ElastoStrainThreshold3" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
<Parameter Name="ElastoStrainThreshold4" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
<Parameter Name="ElastoStrainThreshold5" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
<Parameter Name="ElastoStrainThreshold6" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
<Parameter Name="ElastoFrameAverageN" Value="1" Min="1" Max="200" IsPresetParameter="false"/>
<Parameter Name="ElastoStrainThresholdDown" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
<Parameter Name="ElastoStrainThresholdUp" Value="0" Min="0" Max="2000" IsPresetParameter="false"/>
<Parameter Name="ElastoThresholdX" Value="0" Min="0" Max="2000" IsPresetParameter="true"/>
<Parameter Name="ElastoThresholdY" Value="0" Min="0" Max="2000" IsPresetParameter="true"/>
<Parameter Name="ElastoTransparency" Step="5" Value="95" Min="0" Max="255" DefaultValue="95" IsPresetParameter="true"/>
<Parameter Name="ElastoDepthDelta" Value="0" Min="0" Max="100"  IsPresetParameter="true"/>
<Parameter Name="ElastoStrainMax" Value="0" Min="0" Max="2000"  IsPresetParameter="false"/>
<Parameter Name="ElastoQualityLevel" Value="0" Min="0" Max="25" DefaultValue="0" IsPresetParameter="true"/>
<Parameter Name="ElastoRebootTime" Value="0" Min="0" Max="5" DefaultValue="0" IsPresetParameter="true"/>
<Parameter Name="ElastoStrainList" ValueType="List" IsPresetParameter="false"/>
<Parameter Name="ElastoStrain" Min="0" Max="2000" IsPresetParameter="false"/>
<Parameter Name="IsElastoUnderThreshold1" ValueType="Bool" Value="false"  IsPresetParameter="false"/>
<Parameter Name="FreeMLineNum" Value="1" DefaultValue="1" Min="1" Max="3" IsPresetParameter="false"/>
<Parameter Name="FreeMLineNo" Value="1" DefaultValue="1" Min="1" Max="3" IsPresetParameter="false"/>
<Parameter Name="FreeMStartLine1" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
<Parameter Name="FreeMStartDepthMM1" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
<Parameter Name="FreeMAngle1" ValueType="Float" Value="135" DefaultValue="135" Min="0" Max="360" IsPresetParameter="false"/>
<Parameter Name="FreeMStartLine2" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
<Parameter Name="FreeMStartDepthMM2" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
<Parameter Name="FreeMAngle2" ValueType="Float" Value="135" DefaultValue="135" Min="0" Max="360" IsPresetParameter="false"/>
<Parameter Name="FreeMStartLine3" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
<Parameter Name="FreeMStartDepthMM3" ValueType="Float" Value="128" DefaultValue="128" IsPresetParameter="false"/>
<Parameter Name="FreeMAngle3" ValueType="Float" Value="135" DefaultValue="135" Min="0" Max="360" IsPresetParameter="false"/>
<Parameter Name="FreeMBlock" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="StressEchoEn" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="StressEchoLevel" ValueType="String" Value="" IsPresetParameter="false"/>
<Parameter Name="StressEchoProjection" ValueType="String" Value="" IsPresetParameter="false"/>
<Parameter Name="StressEchoTimer1En" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="StressEchoTimer2En" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="StressEchoTimer1" ValueType="String" Value="00:00:00" IsPresetParameter="false"/>
<Parameter Name="StressEchoTimer2" ValueType="String" Value="00:00:00" IsPresetParameter="false"/>
<Parameter Name="FourDThreshold" Min="0" Max="255" DefaultValue="33" IsPresetParameter="true"/>
<Parameter Name="FourDRender" Min="0" Max="5" DefaultValue="0" IsPresetParameter="true"/>
<Parameter Name="FourDSmooth" Min="0" Max="3" IsPresetParameter="false"/>
<Parameter Name="FourDFrameRate" Min="0" Max="1" IsPresetParameter="false"/>
<Parameter Name="FourDPalette" Min="0" Max="8" IsPresetParameter="false"/>
<Parameter Name="FourDLight" Min="0" Max="11" IsPresetParameter="false"/>
<Parameter Name="FourDVirtualHDOn" ValueType="Bool" Value="false" DefaultValue="false" IsPresetParameter="false"/>
<Parameter Name="FourDDirectionSet" Min="0" Max="270" Step="90" IsPresetParameter="false"/>
<Parameter Name="FourDQualityIndex" Min="0" Max="2" IsPresetParameter="false"/>
<Parameter Name="FourDQualityValue" ValueType="Float" Min="0" Max="1" IsPresetParameter="false"/>
<Parameter Name="FourDAngleRatioIndex" Min="0" Max="0" IsPresetParameter="false"/>
<Parameter Name="FourDAngleRatio" Min="0" Max="100" IsPresetParameter="false"/>
<Parameter Name="FourDParasSettingIndex" Min="0" Max="8" IsPresetParameter="false"/>
<Parameter Name="FourDRenderModeIndex" Min="0" Max="2" IsPresetParameter="false"/>
<Parameter Name="FourDSliceNum" Min="1" Max="999" DefaultValue="60" IsPresetParameter="false"/>
<Parameter Name="FourDMotorAngle" Min="0" Max="80" Step="1" DefaultValue="65" IsPresetParameter="true"/>
<Parameter Name="FourDRoiRatio" ValueType="Float" Min="0" Max="100" DefaultValue="0" IsPresetParameter="false"/>
<Parameter Name="FourDRoiZoomValue" ValueType="Float" Min="0.1" Max="10" DefaultValue="0.4" IsPresetParameter="false"/>
<Parameter Name="FourDRectMinRatio" Min="0.3" Max="1" DefaultValue="0.3" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="FourDRectRatio" Min="0" Max="1" DefaultValue="1" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="FourDRectMaxHeight" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="FourDMaxVolumeCount" Value="64" Min="1" Max="256" DefaultValue="64" IsPresetParameter="false"/>
<Parameter Name="FourDCinProcessing" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="TransformationAxis" Value="0" Min="0" Max="2" IsPresetParameter="false"/>
<Parameter Name="FourDStartHeight" Value="0" Min="0" Max="511" IsPresetParameter="false"/>
<Parameter Name="FourDKnotPosChanged" ValueType="Bool" Value="false" DefaultValue="false" IsPresetParameter="true"/>
<Parameter Name="FourDGain" Min="0" Max="999" DefaultValue="125" IsPresetParameter="false"/>
<Parameter Name="FourDDynamicRange" Min="0" Max="999" DefaultValue="95" IsPresetParameter="false"/>
<Parameter Name="LGCEn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="LGCControlEn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="LGC0" Value="0" DefaultValue="0" Min="-20" Max="20" IsPresetParameter="true"/>
<Parameter Name="LGC1" Value="0" DefaultValue="0" Min="-20" Max="20" IsPresetParameter="true"/>
<Parameter Name="LGC2" Value="0" DefaultValue="0" Min="-20" Max="20" IsPresetParameter="true"/>
<Parameter Name="LGC3" Value="0" DefaultValue="0" Min="-20" Max="20" IsPresetParameter="true"/>
<Parameter Name="LGC4" Value="0" DefaultValue="0" Min="-20" Max="20" IsPresetParameter="true"/>
<Parameter Name="LGC5" Value="0" DefaultValue="0" Min="-20" Max="20" IsPresetParameter="true"/>
<Parameter Name="LGC6" Value="0" DefaultValue="0" Min="-20" Max="20" IsPresetParameter="true"/>
<Parameter Name="LGC7" Value="0" DefaultValue="0" Min="-20" Max="20" IsPresetParameter="true"/>
<Parameter Name="LGCMAX" Value="20" DefaultValue="20" Min="-150" Max="150" IsPresetParameter="true"/>
<Parameter Name="LGCMIN" Value="-20" DefaultValue="-20" Min="-150" Max="150" IsPresetParameter="true"/>
<Parameter Name="CurvedPanoramicEnable" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicLength" Value="0" Min="0" Max="65536" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicPointList" ValueType="List" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicROIVisible" ValueType="Bool" IsPresetParameter="false"/>
<Parameter Name="LinearAreaRect" ValueType="Rect" IsPresetParameter="false"/>

<!--系统采用任意线密度的参数进行描述以及参与计算-->
<Parameter Name="StartScanLineColor" ValueType="Int" Value = "0" IsPresetParameter="false"/>
<Parameter Name="StopScanLineColor" ValueType="Int" Value = "0" IsPresetParameter="false"/>
<Parameter Name="StartScanDisColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
<Parameter Name="StopScanDisColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
<Parameter Name="LineSpacingColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
<!--WholeImage = 0 整帧数据, LineData = 1 线数据, IQData = 2 Color、PW是IQ数据, BFData = 3 Beamformer之后的数据-->
<Parameter Name="RawDataFormat" ValueType="Double" Value = "0" IsPresetParameter="false"/>
</BFParameterGroup>
</BFParameter>
<!-- un appended
DefaultScpd = para.DefaultScpd;
SonoParas.VideoInvertOfD = false;
SonoParas.VideoInvertOfTD = false;
SonoParas.VideoInvertOfCW = false;
SonoParas.VideoInvertOfM = false;
FreqIndexIn4D = para.FreqIndexIn4D;
setScaleRatio(para);
InvertUpDown = (para.UdInvert == 1) ? true : false;
-->
