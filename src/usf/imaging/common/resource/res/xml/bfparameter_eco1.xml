<?xml version="1.0" encoding="UTF-8"?>
<!--ValueType 如果类型不是int，必须注明类型，目前支持Bool,Int,Double,List,String ,ByteArray,Size,
ControlTable的Bool类型必须注明 TrueValue-->
<BFParameter Version="0.1" ParameterVersion="0.1">
<BFParameterGroup Type="ControlTable" ControlDataLen="454">
<Parameter Name="Freeze" BitCount="1" StartBit="0" StartByte="1"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="PhaseProbeId" BitCount="1" StartBit="1" StartByte="1"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="Socket" BitCount="2" StartBit="2" StartByte="1" IsPresetParameter="false"/>
<Parameter Name="ProbeCode" BitCount="4" StartBit="4" StartByte="1" IsPresetParameter="false"/>
<Parameter Name="FreqIndexColor" BitCount="3" StartBit="0" StartByte="2" Min="0" Max="3" IsPresetParameter="true"/>
<Parameter Name="ZoomOn" BitCount="1" StartBit="3" StartByte="2"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="SteeringAngle" BitCount="3" StartBit="4" StartByte="2" IsPresetParameter="true"/>
<Parameter Name="Linear" BitCount="1" StartBit="7" StartByte="2"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FocusNumB" BitCount="2" StartBit="0" StartByte="3" IsPresetParameter="true"/>
<Parameter Name="FocusPosB" BitCount="4" StartBit="2" StartByte="3" IsPresetParameter="true"/>
<Parameter Name="FocusNumM" BitCount="2" StartBit="0" StartByte="3" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="FocusPosM" BitCount="4" StartBit="2" StartByte="3" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="Deep" BitCount="1" StartBit="6" StartByte="3"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
<Parameter Name="HalfHeight" BitCount="1" StartBit="7" StartByte="3"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ImageMode" BitCount="4" StartBit="0" StartByte="4" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="SyncMode" BitCount="3" StartBit="4" StartByte="4" IsPresetParameter="false"/>
<Parameter Name="HighDensity" BitCount="1" StartBit="7" StartByte="4"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="CQYZ" BitCount="6" StartBit="0" StartByte="5" IsPresetParameter="true"/>
<Parameter Name="MVelocity" BitCount="2" StartBit="6" StartByte="5" IsPresetParameter="true"/>
<Parameter Name="DVelocity" BitCount="2" StartBit="6" StartByte="5" Max="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="ColorImageMode" BitCount="3" StartBit="0" StartByte="6" IsPresetParameter="false"/>
<Parameter Name="FreqSpectrum" BitCount="1" StartBit="3" StartByte="6"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="LowVelocityBlood" BitCount="1" StartBit="4" StartByte="6"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="BaseLine" BitCount="3" StartBit="5" StartByte="6" Max="6" IsPresetParameter="true"/>
<Parameter Name="GainColor" BitCount="8" StartBit="0" StartByte="7" IsPresetParameter="true"/>
<Parameter Name="MB" BitCount="1" StartBit="0" StartByte="8"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="DynamicRange" BitCount="4" StartBit="0" StartByte="9" IsPresetParameter="true"/>
<Parameter Name="NotReduceGain" BitCount="1" StartBit="4" StartByte="9"
        ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="FrameAvg" BitCount="3" StartBit="5" StartByte="9" IsPresetParameter="true"/>
<Parameter Name="Edge" BitCount="3" StartBit="0" StartByte="10" IsPresetParameter="true"/>
<Parameter Name="SpectralInvert" BitCount="1" StartBit="4" StartByte="10"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="Smooth" BitCount="3" StartBit="5" StartByte="10" IsPresetParameter="true"/>
<Parameter Name="SampleRateDop" BitCount="4" StartBit="0" StartByte="11" IsPresetParameter="true"/>
<Parameter Name="DSampleRateDop" BitCount="4" StartBit="4" StartByte="11" IsPresetParameter="true"/>
<Parameter Name="WallFilterDop" BitCount="2" StartBit="0" StartByte="12" IsPresetParameter="true"/>
<Parameter Name="WallFilterColor" BitCount="2" StartBit="2" StartByte="12" IsPresetParameter="true"/>
<Parameter Name="WallThreshold" BitCount="4" StartBit="4" StartByte="12" Max="14" IsPresetParameter="true"/>
<Parameter Name="SampleVolume" BitCount="3" StartBit="0" StartByte="13" IsPresetParameter="true"/>
<Parameter Name="Up" BitCount="1" StartBit="3" StartByte="13"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="Left" BitCount="1" StartBit="4" StartByte="13"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="FrameAvgColor" BitCount="3" StartBit="5" StartByte="13" IsPresetParameter="true"/>
<Parameter Name="AcousticPowerB" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true"/>
<Parameter Name="AcousticPowerColor" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="AcousticPowerDop" BitCount="4" StartBit="0" StartByte="14" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="BReject" BitCount="4" StartBit="4" StartByte="14" IsPresetParameter="true"/>
<Parameter Name="MScanLine" BitCount="8" StartBit="0" StartByte="15" IsPresetParameter="true"/>
<Parameter Name="DScanLine" BitCount="8" StartBit="0" StartByte="15" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="StartLine" BitCount="8" StartBit="0" StartByte="16" IsPresetParameter="true"/>
<Parameter Name="StopLine" BitCount="8" StartBit="0" StartByte="17" IsPresetParameter="true"/>
<Parameter Name="StartLineColor" BitCount="8" StartBit="0" StartByte="18" IsPresetParameter="false"/>
<Parameter Name="StopLineColor" BitCount="8" StartBit="0" StartByte="19" IsPresetParameter="false"/>
<Parameter Name="TopBorderColor" BitCount="8" StartBit="0" StartByte="20" Max="511" IsPresetParameter="false"/>
<Parameter Name="BottomBorderColor" BitCount="8" StartBit="0" StartByte="21" Max="511" IsPresetParameter="false"/>
<Parameter Name="SampleDepthDop" BitCount="8" StartBit="0" StartByte="22" IsPresetParameter="false"/>
<Parameter Name="AngleSpacing" BitCount="8" StartBit="0" StartByte="23" IsPresetParameter="false"/>
<Parameter Name="LineSpacing" BitCount="8" StartBit="0" StartByte="24"
                            HighBitCount="8" HighStartBit="0" HighStartByte="25" IsPresetParameter="false"/>
<Parameter Name="Gain" BitCount="8" StartBit="0" StartByte="26" IsPresetParameter="true"/>
<Parameter Name="TGC7" BitCount="8" StartBit="0" StartByte="27" IsPresetParameter="false"/>
<Parameter Name="TGC8" BitCount="8" StartBit="0" StartByte="28" IsPresetParameter="false"/>
<Parameter Name="TGC1" BitCount="8" StartBit="0" StartByte="29" IsPresetParameter="false"/>
<Parameter Name="TGC2" BitCount="8" StartBit="0" StartByte="30" IsPresetParameter="false"/>
<Parameter Name="TGC3" BitCount="8" StartBit="0" StartByte="31" IsPresetParameter="false"/>
<Parameter Name="TGC4" BitCount="8" StartBit="0" StartByte="32" IsPresetParameter="false"/>
<Parameter Name="TGC5" BitCount="8" StartBit="0" StartByte="33" IsPresetParameter="false"/>
<Parameter Name="TGC6" BitCount="8" StartBit="0" StartByte="34" IsPresetParameter="false"/>
<Parameter Name="SlopeDis" BitCount="8" StartBit="0" StartByte="35"
                            HighBitCount="4" HighStartBit="4" HighStartByte="37" IsPresetParameter="false"/>
<Parameter Name="PerpendicularDis" BitCount="8" StartBit="0" StartByte="36"
                            HighBitCount="4" HighStartBit="0" HighStartByte="37" IsPresetParameter="false"/>
<Parameter Name="ZoomDepth" BitCount="8" StartBit="0" StartByte="38"
                            HighBitCount="8" HighStartBit="0" HighStartByte="39" IsPresetParameter="false"/>
<Parameter Name="FreqIndexB" BitCount="3" StartBit="0" StartByte="40" IsPresetParameter="true"/>
<Parameter Name="Scpd" BitCount="2" StartBit="3" StartByte="40" IsPresetParameter="true"/>
<Parameter Name="ScpdTrape" BitCount="2" StartBit="4" StartByte="14" Max="2" IsPresetParameter="false" IsDirectValue="true"/>
<Parameter Name="ScpdOther" BitCount="2" StartBit="3" StartByte="40" IsPresetParameter="false" IsDirectValue="true"/>
<Parameter Name="VHSi" BitCount="3" StartBit="5" StartByte="40" IsPresetParameter="true"/>
<Parameter Name="FocusNumC" BitCount="2" StartBit="0" StartByte="41" IsPresetParameter="true"/>
<Parameter Name="FocusPosC" BitCount="4" StartBit="2" StartByte="41" IsPresetParameter="true"/>
<Parameter Name="Deep2" BitCount="1" StartBit="6" StartByte="41"
        ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="false"/>
<Parameter Name="HalfHeight2" BitCount="1" StartBit="7" StartByte="41"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="GainDop" BitCount="8" StartBit="0" StartByte="42" IsPresetParameter="true"/>
<Parameter Name="CVLT" BitCount="7" StartBit="0" StartByte="43" IsPresetParameter="true"/>
<Parameter Name="PDCVLT" BitCount="7" StartBit="0" StartByte="43" DefaultValue="4" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TNFtr" BitCount="1" StartBit="7" StartByte="43" IsPresetParameter="true"/>
<Parameter Name="CHET" BitCount="7" StartBit="0" StartByte="44" IsPresetParameter="true"/>
<Parameter Name="PDCHET" BitCount="7" StartBit="0" StartByte="44" DefaultValue="60" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="TNR" BitCount="1" StartBit="7" StartByte="44" IsPresetParameter="true"/>
<Parameter Name="CVRT" BitCount="4" StartBit="0" StartByte="45" IsPresetParameter="true"/>
<Parameter Name="PDCVRT" BitCount="4" StartBit="0" StartByte="45" DefaultValue="4" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CET" BitCount="4" StartBit="4" StartByte="45" IsPresetParameter="true"/>
<Parameter Name="PDCET" BitCount="4" StartBit="4" StartByte="45" DefaultValue="2" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="BCosAngle" BitCount="8" StartBit="0" StartByte="46" IsPresetParameter="false"/>
<Parameter Name="BTgAngle" BitCount="8" StartBit="0" StartByte="47" IsPresetParameter="false"/>
<Parameter Name="Alpha" BitCount="8" StartBit="0" StartByte="48" IsPresetParameter="true"/>
<Parameter Name="Beta" BitCount="8" StartBit="0" StartByte="49" IsPresetParameter="true"/>
<Parameter Name="Gamma" BitCount="8" StartBit="0" StartByte="50" IsPresetParameter="true"/>
<Parameter Name="ImageZoomCoefBin" BitCount="8" StartBit="0" StartByte="51"
                            HighBitCount="8" HighStartBit="0" HighStartByte="52" IsPresetParameter="false"/>
<Parameter Name="UniformityTgc00" BitCount="8" StartBit="0" StartByte="53" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc01" BitCount="8" StartBit="0" StartByte="54" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc02" BitCount="8" StartBit="0" StartByte="55" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc03" BitCount="8" StartBit="0" StartByte="56" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc04" BitCount="8" StartBit="0" StartByte="57" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc05" BitCount="8" StartBit="0" StartByte="58" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc06" BitCount="8" StartBit="0" StartByte="59" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc07" BitCount="8" StartBit="0" StartByte="60" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc08" BitCount="8" StartBit="0" StartByte="61" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc09" BitCount="8" StartBit="0" StartByte="62" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc10" BitCount="8" StartBit="0" StartByte="63" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc11" BitCount="8" StartBit="0" StartByte="64" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc12" BitCount="8" StartBit="0" StartByte="65" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc13" BitCount="8" StartBit="0" StartByte="66" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc14" BitCount="8" StartBit="0" StartByte="67" IsPresetParameter="true"/>
<Parameter Name="UniformityTgc15" BitCount="8" StartBit="0" StartByte="68" IsPresetParameter="true"/>
<Parameter Name="ShutDown" BitCount="1" StartBit="5" StartByte="90"
        ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="Rotation" Value="0" Max="270" Step="90" BitCount="2" StartBit="2" StartByte="92" IsPresetParameter="true"/>
<Parameter Name="ZoomInTopOffset" BitCount="8" StartBit="0" StartByte="154" IsPresetParameter="false"/>
<Parameter Name="TxOff" BitCount="1" StartBit="7" StartByte="255" ValueType="Bool" TrueValue="1" IsPresetParameter="false"/>
</BFParameterGroup>
<BFParameterGroup Type="Common">
<Parameter Name="ADFreqMHz" Value="32" IsPresetParameter="false"/>
<Parameter Name="ADOffsetTimeNs" Value="36000" IsPresetParameter="false"/>
<Parameter Name="FrequencyCompounding" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="AcousticPowerTestCode" IsPresetParameter="false"/>
<Parameter Name="MTriggeredAP" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CTriggeredAP" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="FourD" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="Volume" IsPresetParameter="true"/>
<Parameter Name="BStearingAngle" IsPresetParameter="true"/>
<Parameter Name="FreqIndexFrequencyCompounding" IsPresetParameter="true"/>
<Parameter Name="CRCOpenFlag" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FocusPosFrequencyCompounding" IsPresetParameter="true"/>
<Parameter Name="ColorLineDensity" ValueType="Bool" Value="true" TrueValue="0" IsPresetParameter="true"/>
<Parameter Name="ConfigDone" IsPresetParameter="false"/>
<Parameter Name="TransferSignal" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false" IsCanFrozenSend="true"/>
<!--FPGA upgrade-->
<Parameter Name="DspEn" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="DbfEn" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="PwrEn" IsPresetParameter="false"/>
<Parameter Name="UsbEn" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="Prog" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="CFGStart" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="BEStart" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="WRClr" IsPresetParameter="false" IsCanFrozenSend="true"/>
<Parameter Name="LCDLight" IsPresetParameter="false"/>
<Parameter Name="ExceedLowBlood" IsPresetParameter="false"/>
<!--true:定时DSC，false:整帧DSC-->
<Parameter Name="DSC" ValueType="Bool" Value="true" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="CFVelocityThreshold" IsPresetParameter="true"/>
<Parameter Name="StateClearFlag" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ColorRegionThreshold" IsPresetParameter="true"/>
<Parameter Name="WrReset" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CTGC" IsPresetParameter="true"/>
<Parameter Name="PDCTGC" DefaultValue="105" IsPresetParameter="true" IsDirectValue="true"/>
<Parameter Name="CCosAngle" IsPresetParameter="false"/>
<Parameter Name="CTgAngle" IsPresetParameter="false"/>
<!--Fpga调试信息用-->
<!--DSP复位-->
<Parameter Name="DSPReset" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="DBFReset" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<!--版本/状态切换 -->
<Parameter Name="DBFInfo" IsPresetParameter="false"/>
<!--测试使能-->
<Parameter Name="TestSignal" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<!--测试参数-->
<Parameter Name="TestParameter" IsPresetParameter="false"/>
<!--发射脉冲数-->
<Parameter Name="BPulseNum" IsPresetParameter="false"/>
<Parameter Name="CPulseNum" IsPresetParameter="false"/>
<Parameter Name="DPulseNum" IsPresetParameter="true"/>
<Parameter Name="SystemScanMode" IsPresetParameter="false"/>
<Parameter Name="ProbeId" IsPresetParameter="false"/>
<Parameter Name="ScanWidth" Min="0" Max="3" Value="3" IsPresetParameter="false"/>
<Parameter Name="ImageZoomCoefOther" Min="100" Max="100" Step="1" Value="100" IsPresetParameter="false"/>
<Parameter Name="ImageZoomCoefB" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="false"/>
<!--ImageZoomCoefOther ImageZoomCoef ImageZoomCoefB 需要保证此顺序-->
<Parameter Name="ImageZoomCoef" Min="60" Max="100" Step="5" Value="80" IsPresetParameter="true"/>
<Parameter Name="IsFullScreenZoomIn" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FullScreenZoomInIndex" Min="0" Max="4" Step="1" Value="2" IsPresetParameter="false"/>
<Parameter Name="Flow" Value="1" Min="0" Max="1" DefaultValue="0" IsPresetParameter="false"/>
<Parameter Name="ZoomedCQYZ" Min="1" IsPresetParameter="false"/>
<Parameter Name="ZoomMultiIndex" Min="0" Max="2" Value="0" IsPresetParameter="false"/>
<Parameter Name="ZoomMidLine" IsPresetParameter="false"/>
<Parameter Name="ZoomHalfLines" IsPresetParameter="false"/>
<Parameter Name="ZoomMidDepthMM" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="ZoomHalfDepthMM" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="Scroll" Min="0" Max="255" Value="0" IsPresetParameter="false"/>
<Parameter Name="IsScroll" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="ActiveB" Min="0" Max="3" Value="0" IsPresetParameter="false"/>
<Parameter Name="FPS" IsPresetParameter="false"/>
<Parameter Name="AccCountColor" Min="0" Max="15" Value="8" IsPresetParameter="false"/>
<Parameter Name="AccCountDop" Min="0" Max="15" Value="8" IsPresetParameter="false" IsDirectValue="true"/>
<Parameter Name="Harmonic" ValueType="Bool" Value="false" IsPresetParameter="true"/>
<Parameter Name="PRFColorKHZ" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="PRFDopKHZ" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="BaseLineColor" Min="0" Max="6" Value="3" IsPresetParameter="true"/>
<Parameter Name="DopplerTheta" Min="-70" Max="70" Step="10" Value="0" IsPresetParameter="true"/>
<Parameter Name="RoiMidLine" IsPresetParameter="true"/>
<Parameter Name="RoiHalfLines" IsPresetParameter="true"/>
<Parameter Name="RoiMidDepthMM" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="RoiHalfDepthMM" ValueType="Float" IsPresetParameter="true"/>
<Parameter Name="SampleVolumeMM" Min="1" Max="8" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="DopplerStartDepthMM" ValueType="Double" IsPresetParameter="true"/>
<Parameter Name="CMaxVelCMS" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DMaxVelCMS" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PRFColor" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PRFDop" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="BRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="MGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="MRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="PwGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="PwRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="CwdGamma" Min="0" Max="8" IsPresetParameter="true"/>
<Parameter Name="CwdRejection" Min="0" Max="256" IsPresetParameter="true"/>
<Parameter Name="BGammaPos" Value="16" Min="0" Max="255" DefaultValue="16" IsPresetParameter="true"/>
<Parameter Name="BGammaStart" Value="60" Min="1" Max="100" DefaultValue="60" IsPresetParameter="true"/>
<Parameter Name="BGammaStep" Value="10" Min="1" Max="100" DefaultValue="10" IsPresetParameter="true"/>
<Parameter Name="THIState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="ColorInvertState" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="BColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CfColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PdColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="DpdColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PwColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="MColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CwdColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CPColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="BGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="MGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PwGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CwdGrayCurveIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="VarColorMapIndex" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="CfContrast" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="PdContrast" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="DpdContrast" Min="0" Max="7" IsPresetParameter="true"/>
<Parameter Name="ImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="BImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="MImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="DImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="StartDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="MPixelSizeSec" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="MPixelSizeMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DPixelSizeCMS" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DPixelSizeSec" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="DBaseLineYPos" ValueType="Int" IsPresetParameter="false"/>
<Parameter Name="ZoomDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="ScrollDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="HotLogoDepthMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="HotLogoLine" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="AIO" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsBiopsyVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="BiopsyAngle" IsPresetParameter="false"/>
<Parameter Name="BiopsyXPosMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BiopsyYPosMM" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BiopsyAngleOffset" IsPresetParameter="false"/>
<Parameter Name="BiopsyXPosMMOffset" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="BiopsyYPosMMOffset" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="IsCenterLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsDopplerScanLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="ZoomSelect" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsMLineVisible" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="IsUDBM" ValueType="Bool" Value="false" TrueValue="1" DefaultValue="false" IsPresetParameter="true"/>
<Parameter Name="GrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="MGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="PwGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="CwdGrayMap" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="TGC" ValueType="ByteArray" IsPresetParameter="false"/>
<Parameter Name="MI" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="TIS" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="TIB" ValueType="Float" IsPresetParameter="false"/>
<Parameter Name="ExamModeId" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="ExamModeCaption" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="ProbeConnected" ValueType="Bool" Value="false"/>
<!--ImageRects is List<QRect>-->
<Parameter Name="ImageRects" ValueType="List" IsPresetParameter="false"/>
<!--ImageRegions is List<QRect> for meas region-->
<Parameter Name="ImageRegions" ValueType="List" IsPresetParameter="false"/>
<!--SamplePoints is ByteArray-->
<Parameter Name="BSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="MSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="PwSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="CwdSamplePoints" ValueType="ByteArray" IsPresetParameter="true"/>
<Parameter Name="AFCoef0" IsPresetParameter="false"/>
<Parameter Name="AFCoef1" IsPresetParameter="false"/>
<Parameter Name="AFCoef2" IsPresetParameter="false"/>
<Parameter Name="AFCoef3" IsPresetParameter="false"/>
<Parameter Name="AFCoef4" IsPresetParameter="false"/>
<Parameter Name="AFGain" IsPresetParameter="false"/>
<Parameter Name="AFSettingIds" IsPresetParameter="false"/>
<Parameter Name="AFShift" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc0" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc1" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc2" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc3" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc4" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc5" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc6" IsPresetParameter="false"/>
<Parameter Name="AnalogTgc7" IsPresetParameter="false"/>
<Parameter Name="Bi_CPA" IsPresetParameter="false"/>
<Parameter Name="BloodEffection" IsPresetParameter="false"/>
<Parameter Name="DRDiffWithCompoundOn" IsPresetParameter="false"/>
<Parameter Name="DRDiffWithSraOn" IsPresetParameter="false"/>
<Parameter Name="DebugPara0" IsPresetParameter="false"/>
<Parameter Name="DebugPara1" IsPresetParameter="false"/>
<Parameter Name="DebugPara2" IsPresetParameter="false"/>
<Parameter Name="DebugPara3" IsPresetParameter="false"/>
<Parameter Name="DynDBCount" IsPresetParameter="false"/>
<Parameter Name="DynDBRate" IsPresetParameter="false"/>
<Parameter Name="DynEndDB" IsPresetParameter="false"/>
<Parameter Name="DynEndDepth" IsPresetParameter="false"/>
<Parameter Name="DynStartDB" IsPresetParameter="false"/>
<Parameter Name="DynStartDepth" IsPresetParameter="false"/>
<Parameter Name="DynStartDepthBin" IsPresetParameter="false"/>
<Parameter Name="ECGVelocity" IsPresetParameter="false"/>
<Parameter Name="FcpdOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit1" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit2" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit3" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit4" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit1" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit2" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit3" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit4" IsPresetParameter="false"/>
<Parameter Name="FocusNumShow" IsPresetParameter="false"/>
<Parameter Name="FreqSettingIds" IsPresetParameter="false"/>
<Parameter Name="FrequencyCompoundingOther" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit1" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit2" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit3" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit4" IsPresetParameter="false"/>
<Parameter Name="GateSegment" IsPresetParameter="false"/>
<Parameter Name="HF" IsPresetParameter="false"/>
<Parameter Name="HFSSP" IsPresetParameter="false"/>
<Parameter Name="HFSZ" IsPresetParameter="false"/>
<Parameter Name="HF_Alpha" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit1" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit2" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit3" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit4" IsPresetParameter="false"/>
<Parameter Name="IIR_ON" IsPresetParameter="false"/>
<Parameter Name="Image1FGC0" IsPresetParameter="false"/>
<Parameter Name="Image1FGC1" IsPresetParameter="false"/>
<Parameter Name="Image1FGC2" IsPresetParameter="false"/>
<Parameter Name="Image1FGC3" IsPresetParameter="false"/>
<Parameter Name="Image2FGC0" IsPresetParameter="false"/>
<Parameter Name="Image2FGC1" IsPresetParameter="false"/>
<Parameter Name="Image2FGC2" IsPresetParameter="false"/>
<Parameter Name="Image2FGC3" IsPresetParameter="false"/>
<Parameter Name="Image3FGC0" IsPresetParameter="false"/>
<Parameter Name="Image3FGC1" IsPresetParameter="false"/>
<Parameter Name="Image3FGC2" IsPresetParameter="false"/>
<Parameter Name="Image3FGC3" IsPresetParameter="false"/>
<Parameter Name="Image4FGC0" IsPresetParameter="false"/>
<Parameter Name="Image4FGC1" IsPresetParameter="false"/>
<Parameter Name="Image4FGC2" IsPresetParameter="false"/>
<Parameter Name="Image4FGC3" IsPresetParameter="false"/>
<Parameter Name="LeeEn" IsPresetParameter="false"/>
<Parameter Name="LeeGain" IsPresetParameter="false"/>
<Parameter Name="LeeMCoef" IsPresetParameter="false"/>
<Parameter Name="LeeSCoef" IsPresetParameter="false"/>
<Parameter Name="LeeSettingIds" IsPresetParameter="false"/>
<Parameter Name="LeeShift" IsPresetParameter="false"/>
<Parameter Name="LogCompression" IsPresetParameter="false"/>
<Parameter Name="MBColor" IsPresetParameter="false"/>
<Parameter Name="MFC" IsPresetParameter="false"/>
<Parameter Name="PWDynamicRange" IsPresetParameter="false"/>
<Parameter Name="PWEnhance" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc0" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc1" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc2" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc3" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc4" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc5" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc6" IsPresetParameter="false"/>
<Parameter Name="PhasedProbeTgc7" IsPresetParameter="false"/>
<Parameter Name="PixelRatio" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit1" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit2" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit3" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit4" IsPresetParameter="false"/>
<Parameter Name="ScpdOn" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="THI" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="TrapezoidalMode" ValueType="Bool" Value="false" IsPresetParameter="false"/>
<Parameter Name="TriplexMode" IsPresetParameter="false"/>
<Parameter Name="WT" IsPresetParameter="false"/>
<Parameter Name="WtCoefficience" IsPresetParameter="false"/>
<Parameter Name="WtStartPoint" IsPresetParameter="false"/>
<Parameter Name="iImage" IsPresetParameter="false"/>
<Parameter Name="RvFNo" Min="0" Max="7" Step="1" Value="3" IsPresetParameter="false"/>
<Parameter Name="TxFNo" Min="0" Max="7" Step="1" Value="5" IsPresetParameter="false"/>
<Parameter Name="CPDSteer" Min="0" Max="20" Step="1" Value="5" IsPresetParameter="false"/>
<Parameter Name="TrapezoidalCPDSteer" Min="0" Max="20" Step="1" DefaultValue="10" IsPresetParameter="true"/>
<Parameter Name="PersistDiffWithCpdOn" Min="-7" Max="7" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="PersistDiffWithSraOn" Min="-7" Max="7" Step="1" Value="0" IsPresetParameter="false"/>
<Parameter Name="AdapPostProcSmooth" IsPresetParameter="false"/>
<Parameter Name="AdapPostProc" IsPresetParameter="false"/>
<Parameter Name="FrequencyOfTransmit5" Value="0" Min="0" Max="15" IsPresetParameter="false"/>
<Parameter Name="HighFreqOfTransmit5" Value="0" Min="0" Max="1" IsPresetParameter="false"/>
<Parameter Name="PulseNumOfTransmit5" Value="1" Min="1" Max="8" IsPresetParameter="false"/>
<Parameter Name="FocusNumOfTransmit5" Value="0" Min="0" Max="3" IsPresetParameter="false"/>
<Parameter Name="NeedleMode" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FilterCoefOfTransmit5" Value="0" Min="0" Max="7" IsPresetParameter="false"/>
<Parameter Name="NeedleAngleIndex" Value="0" Min="0" Max="7" IsPresetParameter="false"/>
<Parameter Name="NeedleDynamicRange" Value="0" Min="0" Max="15" IsPresetParameter="false"/>
<Parameter Name="NeedleGain" Value="0" Min="0" Max="255" IsPresetParameter="false"/>
<Parameter Name="CPriority" IsPresetParameter="false"/>
<Parameter Name="CfmSlope" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc0" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc1" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc2" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc3" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc4" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc5" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc6" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc7" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc8" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc9" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc10" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc11" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc12" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc13" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc14" IsPresetParameter="false"/>
<Parameter Name="CFMDigitalTgc15" IsPresetParameter="false"/>
<Parameter Name="BSteeringScan" Value="0" Min="0" Max="40" IsPresetParameter="false"/>
<Parameter Name="SraGainColorDelta" Min="-64" Max="64" IsPresetParameter="false"/>
<Parameter Name="VesselThreshold" Value="0" IsPresetParameter="false"/>
<Parameter Name="DetailWeightBin" Value="0" IsPresetParameter="false"/>
<Parameter Name="MinWeightBin" Value="128" IsPresetParameter="false"/>
<Parameter Name="MaxWeightBin" Value="128" IsPresetParameter="false"/>
<Parameter Name="EdgeWeightBin" Value="0" IsPresetParameter="false"/>
<Parameter Name="XContrastValue" Value="1" IsPresetParameter="false"/>
<Parameter Name="DetailWeightDeltaSraOn" Value="0" Min="-128" Max="128" DefaultValue="0" IsPresetParameter="true"/>
<Parameter Name="DetailWeightDeltaSraCpdOff" Value="0" Min="-128" Max="128" DefaultValue="0" IsPresetParameter="true"/>
<Parameter Name="ImageNum" Value="0" Min="0" IsPresetParameter="false"/>
<Parameter Name="SopInstanceId" ValueType="String" IsPresetParameter="false"/>
<Parameter Name="Threshold" Value="1" Min="1" Max="25" DefaultValue="1" IsPresetParameter="true"/>
<Parameter Name="DTraceSmooth" Value="2" Min="0" Max="3" DefaultValue="2" IsPresetParameter="true"/>
<Parameter Name="Enhance" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="FilterCpd" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="true"/>
<Parameter Name="SexInfo" ValueType="String" Value="" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicEnable" ValueType="Bool" Value="false" TrueValue="1" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicLength" Value="0" Min="0" Max="65536" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicImageSize" ValueType="Size" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicPointList" ValueType="List" IsPresetParameter="false"/>
<Parameter Name="CurvedPanoramicROIVisible" ValueType="Bool" IsPresetParameter="false"/>
<Parameter Name="LinearAreaRect" ValueType="Rect" IsPresetParameter="false"/>
<Parameter Name="ImageZoomRatio" ValueType="Float" Value="1" IsPresetParameter="false"/>
<Parameter Name="ImageLeftTopPointX" ValueType="Int" Value="0" IsPresetParameter="false"/>
<Parameter Name="ImageLeftTopPointY" ValueType="Int" Value="0" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_0" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_1" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_2" ValueType="Double" IsPresetParameter="false"/>
<Parameter Name="PixelSizeMM_3" ValueType="Double" IsPresetParameter="false"/>

<!--系统采用任意线密度的参数进行描述以及参与计算-->
<Parameter Name="StartScanLineColor" ValueType="Int" Value = "0" IsPresetParameter="false"/>
<Parameter Name="StopScanLineColor" ValueType="Int" Value = "0" IsPresetParameter="false"/>
<Parameter Name="StartScanDisColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
<Parameter Name="StopScanDisColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
<Parameter Name="LineSpacingColor" ValueType="Double" Value = "0" IsPresetParameter="false"/>
<!--WholeImage = 0 整帧数据, LineData = 1 线数据, IQData = 2 Color、PW是IQ数据, BFData = 3 Beamformer之后的数据-->
<Parameter Name="RawDataFormat" ValueType="Double" Value = "0" IsPresetParameter="false"/>
</BFParameterGroup>
</BFParameter>
