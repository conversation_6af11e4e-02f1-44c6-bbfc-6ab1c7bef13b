#include "imagemanager.h"
#include "iperipheralmanager.h"
#include "infostruct.h"
//#include "icommandargs.h"
#include "appsetting.h"
#include "bfpnames.h"
#include "displaystyleinfoloader.h"
#include "ecoresmanager/ecoresmanager.h"
#include "probedataloader.h"
#include "renderlayoutconfigureloader.h"
#include "resource.h"
#include "scanmodeinfoconverter.h"
#include "util.h"

#include "sonoparafileprovider.h"
#include "sonoparameters.h"
#include "util.h"
#include "virtualdatagenerator.h"
#include "xmlbfparameterloader.h"

#include "aiocontroller.h"
#include "beamformerfactorycreator.h"
#include "fpgaupgradecontrol.h"
#include "fpgaupgrademodel.h"
#include "ibeamformer.h"
#include "ibeamformerfactory.h"

#include "ikeyboard.h"
#include "setting.h"
#include "videocontroleco.h"
#include "videocontrolhandler.h"
#include "videocontrolqbit.h"
#include "videomodelqbit.h"

#include "abstractmachine.h"
#include "probepresetmodel.h"

#include "beamformerbase.h"
#include "bufferstoremanager.h"
#include "chisonultrasoundcontext.h"
#include "framecontrol.h"
#include "ilinebuffermanager.h"
#include "measurecontext.h"
#include "imagedatareceiverswitcher.h"
#include "rdmaimagedatareceiver.h"
#include "generalinfo.h"
#include "parameter.h"
#include "probedatasetfacade.h"

#include "cinelooper.h"

#include "lightnames.h"

#include "basescanmodeupdater.h"
#include "logger.h"
#include "scanmodeupdaterfactory.h"

#include "imgstateswitchprocessorfactory.h"
#include "istateswitchprocessor.h"

#include "fpgaupdatemodedef.h"
#include "updatecontroller.h"
#include "updatesetting.h"
#include "upgradeenum.h"
#include "imagesavehelper.h"
#include "elementdetectalgotithom.h"
#include "imageinterfaceforexam.h"
#include "colormapmanager.h"
#include "imotormanager.h"

#include "tgcadjustmentwidgetcontroller.h"
#include "ihardwaremonitormanager.h"

#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
#include <QOpenGLContext>
#include <QOpenGLWidget>
#endif

#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
#include <fpgaupgrademodel_palm.h>
#endif

LOG4QT_DECLARE_STATIC_LOGGER(log, ImageManager)
ImageManager::ImageManager()
    : m_SonoParameters(NULL)
    , m_ControlTableLen(0)
    , m_XmlBFParameterLoader(NULL)
    , m_BeamFormer(NULL)
    , m_VideoModelQBit(NULL)
    , m_ProbePresetModel(NULL)
    , m_ChisonContext(NULL)
    , m_BufferStoreManager(NULL)
    , m_UpdateController(NULL)
    , m_TGCAdjustmentController(NULL)
    , m_StopUpdateControlTimer(false)
    , m_IsStartAutoUpdateFPGATimerSecond(false)
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
    , m_ShareGLContext(NULL)
    , m_OpenGLWidget(NULL)
#endif
    , m_ScanModeUpdater(NULL)
    , m_PeripheralManager(NULL)
    , m_ExamManager(NULL)
    , m_MarkManager(NULL)
    , m_MainWindow(NULL)
    , m_StateManager(NULL)
    , m_DiskDevice(NULL)
    , m_ColorMapManager(NULL)
    , m_ProbeDataSet(NULL)
    , m_ImageSaveHelper(NULL)
    , m_ElementDetectAlgorithom(NULL)
    , m_ImageInterfaceForExam(NULL)
{
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
    m_OpenGLWidget = new QOpenGLWidget;
    QImage image = m_OpenGLWidget->grabFramebuffer();
    m_OpenGLWidget->makeCurrent();
    m_ShareGLContext = m_OpenGLWidget->context();
#endif
}

ImageManager::~ImageManager()
{
    Util::SafeDeletePtr(m_XmlBFParameterLoader);
    Util::SafeDeletePtr(m_VideoModelQBit);
    Util::SafeDeletePtr(m_ProbePresetModel);
    Util::SafeDeletePtr(m_ChisonContext);
    Util::SafeDeletePtr(m_BeamFormer);
    Util::SafeDeletePtr(m_BufferStoreManager);
    // Util::SafeDeletePtr(m_MotorControl);  //TODO:
    // 该成员显示delete，关机退出时，软件会报错，重构之前系统也未显示删除该对象
    Util::SafeDeletePtr(m_SonoParameters);
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
    Util::SafeDeletePtr(m_OpenGLWidget);
#endif
    Util::SafeDeletePtr(m_ScanModeUpdater);
    Util::SafeDeletePtr(m_UpdateController);
    Util::SafeDeletePtr(m_ProbeDataSet);
    Util::SafeDeletePtr(m_ImageSaveHelper);
    Util::SafeDeletePtr(m_ElementDetectAlgorithom);
    Util::SafeDeletePtr(m_ImageInterfaceForExam);
    Util::SafeDeletePtr(m_ColorMapManager);
    Util::SafeDeletePtr(m_TGCAdjustmentController);
}

void ImageManager::init()
{
    //初始化模块前使用线程加载默认探头信息，可减少createBeamFormer()初始化的时间，用于减少开机时间
    ProbeDataLoader::instance().asyncLoadDefualtProbe();

    EcoResMgr::init();

    ScanModeInfoConverter::instance().read(Resource::scanModeInfoName);
    RenderLayoutConfigureLoader::instance().read(Resource::renderLayoutConfigName);
    DisplayStyleInfoLoader::instance().load(Resource::displayFormatName);

    createProbeDataSet();
    createSonoParameters();
    initializeVirtualMode();
    createColorMapManager();
    createBeamFormer();
    initializeVideoOptions();
    createProbePresetModel();
    createImageSaveHelper();
    createChisonUltrasoundContext();
    createBufferStoreManager();
    initializeFourDProbe();
    createUpdateController();
    enableProbeAutoPowerDownOFF();
    createElementDetectAlgorithom();
    createImageInterfaceForExam();
    initializeEnd();
    createTGCAdjustController();
}

void ImageManager::release()
{
    beamFormer()->close();

    m_StopUpdateControlTimer = true;
    m_UpdateControlTimer.stop();
}

void ImageManager::reset()
{
    chisonUltrasoundContext()->clear();
    startCheckProbeTimer();
    beamFormer()->setStartSupportFreezeOutage(true);
}

void ImageManager::startRealtimeSystem()
{
    beamFormer()->open();
    chisonUltrasoundContext()->start();
}

void ImageManager::unFreeze()
{
    IBeamFormer* beamformer = beamFormer();
    Q_ASSERT(beamformer != NULL);

    ChisonUltrasoundContext* chisonContext = chisonUltrasoundContext();
    Q_ASSERT(chisonContext != NULL);

    Q_ASSERT(m_BufferStoreManager != NULL);

    m_BufferStoreManager->restore();

    // bug pw下激活待机唤醒解冻频谱消失或者显示上一次残留
    AbstractMachine* machine = m_PeripheralManager->machine();
    Q_ASSERT(machine != NULL);
    if (machine->isStandbyFrozen() && ((sonoParameters()->pIV(BFPNames::SyncModeStr) & Sync_D) == Sync_D))
    {
        chisonContext->backUpWave();
    }

    bool isFreeze = beamformer->isFrozen();
    if (isFreeze)
    {
        sendSyncEventTo(UEventType::COMMAND_DELETEGLYPHS, true);
    }

    beamformer->freeze(false);
    chisonContext->deleteLoadSonoparameters();
    m_PeripheralManager->controlKeyLight(LightNames::FreezeStr, false);
}

void ImageManager::unFreezeState()
{
    //退出冻结状态时，需要把当前激活区域的参数变更更新到全局参数中
    m_ChisonContext->syncAvtiveBufferGlobalSonoparamters();
    //退出冻结，每次需要把播放器的速度调整为正常播放速度
    m_SonoParameters->setPV(BFPNames::CinePlaySpeedAdjustStr, m_ChisonContext->cineLooper()->defaultFpsIndex() + 1);

    int systemScanMode = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);
    if (systemScanMode == SystemScanModeSonoNeedle)
    {
        sendSyncEventTo(UEventType::COMMAND_EXITSONONEEDLE);
    }
    else if (systemScanMode == SystemScanModeTissueDoppler || systemScanMode == SystemScanModeTissuePW ||
             systemScanMode == SystemScanModeTDIM || systemScanMode == SystemScanModeTDILRBM ||
             systemScanMode == SystemScanModeTDIUDBM || systemScanMode == SystemScanModeMVI ||
             systemScanMode == SystemScanModeMVIPW)
    {
        sendSyncEventTo(UEventType::EVENT_ENTERBMODE);
    }
}

void ImageManager::freeze()
{
    waitForHasFrameData(2000);

    m_ChisonContext->onBeginPaused();

    m_BeamFormer->freeze(true);

    if (m_ChisonContext->lineBufferManager()->frameCount() <= 0)
    {
        log()->warn("FreezeState::onEntry after freeze no frame");
    }
    m_ChisonContext->clearAvtiveBufferFreezeParasRecord();
}

void ImageManager::switchScanMode(SystemScanMode scan_mode, const QString& state_name)
{
    IBeamFormer* beamformer = beamFormer();
    Q_ASSERT(beamformer != NULL);

    SonoParameters* sonoparameters = sonoParameters();
    Q_ASSERT(sonoparameters != NULL);

    BaseScanModeUpdater* scanModeUpdater =
        ScanModeUpdaterFactory::Instance()->createScanModeUpdater(scan_mode, state_name);
    scanModeUpdater->addObject(ObjectId::MAINFRAME, this->object(ObjectId::MAINFRAME));
    scanModeUpdater->setMachine(m_PeripheralManager->machine());
    BeamFormerBase* beamformerbase = dynamic_cast<BeamFormerBase*>(beamformer);
    Q_ASSERT(beamformerbase != NULL);
    scanModeUpdater->setBeamFormer(beamformerbase);
    scanModeUpdater->setSonoParamters(sonoparameters);
    scanModeUpdater->setBufferStoreManager(m_BufferStoreManager);
    scanModeUpdater->connectSingalAndSlots();
    scanModeUpdater->update(scan_mode);

    if (m_ScanModeUpdater)
        Util::SafeDeletePtr(m_ScanModeUpdater);

    m_ScanModeUpdater = scanModeUpdater;
}

void ImageManager::changeLayout(SystemScanMode scan_mode, int layout, int active_layout)
{
    Q_ASSERT(layout >= Layout_Min && layout <= Layout_Max);
    Q_ASSERT(active_layout >= 0 && active_layout < layout);

    m_BeamFormer->setActiveB(active_layout);
    m_BeamFormer->setSystemScanMode(scan_mode);
    m_SonoParameters->setPV(BFPNames::LayoutStr, layout);
    if (layout != Layout_1x1)
        m_SonoParameters->parameter(BFPNames::BCImagesOnStr)->setEnabled(false);

    if (active_layout == 0)
    {
        m_SonoParameters->setPV(BFPNames::IsDopplerScanLineVisibleStr, false);
        m_SonoParameters->setPV(BFPNames::IsCWDScanLineVisibleStr, false);
        m_SonoParameters->setPV(BFPNames::IsMLineVisibleStr, false);
        m_SonoParameters->setPV(BFPNames::IsRoiVisibleStr, false);
    }
}

bool ImageManager::adjustParam(const QString& name, const QVariant& value, PARAM_ADJUST_ASK_TYPE type)
{
    if (m_SonoParameters == NULL)
        return false;

    Parameter* p = m_SonoParameters->parameter(name);
    if (p != NULL && !p->isNull())
    {
        if (type == PARAM_ADJUST_ASK_TYPE::PARAM_VALUE)
            p->setValue(value);
        else if (type == PARAM_ADJUST_ASK_TYPE::PARAM_MAX)
        {
            Q_ASSERT(value.type() == QVariant::Int);
            p->setMax(value.toInt());
        }
        else if (type == PARAM_ADJUST_ASK_TYPE::PARAM_MIN)
        {
            Q_ASSERT(value.type() == QVariant::Int);
            p->setMin(value.toInt());
        }
        else if (type == PARAM_ADJUST_ASK_TYPE::PARAM_ENABLE)
        {
            Q_ASSERT(value.type() == QVariant::Bool);
            p->setEnabled(value.toBool());
        }
        return true;
    }

    return false;
}

bool ImageManager::askParam(const QString& name, QVariant& result, PARAM_ADJUST_ASK_TYPE ask_type,
                            IMG_PARAM_SOURCE_TYPE source_type)
{
    SonoParameters* sonoparameters = getSonoParameters(source_type);
    if (sonoparameters == NULL)
        return false;

    Parameter* p = sonoparameters->parameter(name);
    if (p !=
        NULL /*&& !p->isNull()*/) //重构前，从sonoParameters获取参数，如果查询不到，会调用nullparameter的value，因此，这里的接口做调整
    {
        if (ask_type == PARAM_ADJUST_ASK_TYPE::PARAM_VALUE)
            result = p->value();
        else if (ask_type == PARAM_ADJUST_ASK_TYPE::PARAM_MAX)
        {
            result = p->max();
        }
        else if (ask_type == PARAM_ADJUST_ASK_TYPE::PARAM_MIN)
        {
            result = p->min();
        }
        else if (ask_type == PARAM_ADJUST_ASK_TYPE::PARAM_ENABLE)
        {
            result = p->isEnabled();
        }
        return true;
    }

    return false;
}

void ImageManager::setPeripheralManager(IPeripheralManager* value)
{
    m_PeripheralManager = value;
}

IPeripheralManager* ImageManager::getPeripheralManager()
{
    return m_PeripheralManager;
}

void ImageManager::setExamManager(IExamManager* value)
{
    m_ExamManager = value;
}

void ImageManager::setMarkManager(IMarkManager* value)
{
    m_MarkManager = value;
}

void ImageManager::setMainWindow(QWidget* value)
{
    m_MainWindow = value;
}

void ImageManager::setStateManagerFacade(IStateManager* value)
{
    m_StateManager = value;
}

void ImageManager::setDiskDevice(IDiskDevice* diskDevice)
{
    m_DiskDevice = diskDevice;
}

SonoParameters* ImageManager::getSonoParameters(IMG_PARAM_SOURCE_TYPE type)
{
    if (type == IMG_PARAM_SOURCE_TYPE::Realtime)
        return m_SonoParameters;
    else if (type == IMG_PARAM_SOURCE_TYPE::BufferStore)
        return m_BufferStoreManager->curSonoParameters();
    else
    {
        return NULL;
    }
}

int ImageManager::getFrameCount() const
{
    return m_ChisonContext->lineBufferManager()->frameCount();
}

bool ImageManager::enterState(FUNC_STATE state, const FuncStateInput& input)
{
    ImgStateSwitchProcessorFactory factory;
    IStateSwitchProcessor* stateSwitchProcessor = factory.createStateSwitchProcess(state);
    stateSwitchProcessor->setImageManager(this);
    bool ret = stateSwitchProcessor->enterState(input);

    Util::SafeDeletePtr(stateSwitchProcessor);

    return ret;
}

bool ImageManager::exitState(FUNC_STATE state, const FuncStateInput& input)
{
    ImgStateSwitchProcessorFactory factory;
    IStateSwitchProcessor* stateSwitchProcessor = factory.createStateSwitchProcess(state);
    stateSwitchProcessor->setImageManager(this);
    bool ret = stateSwitchProcessor->exitState(input);
    Util::SafeDeletePtr(stateSwitchProcessor);

    return ret;
}

void ImageManager::waitForHasFrameData(int delay)
{
    ILineBufferManager* linebuffermanager = lineBufferManager();
    if (!linebuffermanager)
        return;

    QDateTime time = QDateTime::currentDateTime();
    while (!m_ChisonContext->imageIsStable() ||
           linebuffermanager->frameCount() <= 0) // TODO: waitForHasFrameData调用方是否均使用该接口，待确认
    {
        Util::processEvents();
        if (time.msecsTo(QDateTime::currentDateTime()) > delay)
        {
            break;
        }
    }
}

void ImageManager::waitFor2DImagePushed(int delay, int layoutIndex)
{
    ChisonUltrasoundContext* chisoncontext = chisonUltrasoundContext();
    if (!chisoncontext)
        return;

    QDateTime time = QDateTime::currentDateTime();
    while (!chisoncontext->is2DImagePushed(layoutIndex))
    {
        Util::processEvents();
        if (time.msecsTo(QDateTime::currentDateTime()) > delay)
        {
            break;
        }
    }
}

void ImageManager::controlVideoFullScreen(bool value)
{
    VideoControlHandler::instance().videoControl()->setIsFullScreen(value);
}

void ImageManager::resetTGCData()
{
    unsigned char tgcArray[TGC_COUNT];
    memset(tgcArray, 127, TGC_COUNT);
    QByteArray tgcData = QByteArray((const char*)(tgcArray), TGC_COUNT);
    m_SonoParameters->setPV(BFPNames::TGCStr, tgcData);
}

void ImageManager::setColorMap(uchar* image2DMap, uchar* waveMap)
{
    if (m_ChisonContext != NULL)
    {
        m_ChisonContext->setColorMap(image2DMap, waveMap);
    }
}

void ImageManager::onAutoUpdate(int type, const QString& socket)
{
    if (m_UpdateController != NULL)
    {
        m_UpdateController->setHwUpdateMode(FPGAUpdateModeDef::Packets_Mode);
        m_UpdateController->doAutoUpdate(type, socket);
    }
}

void ImageManager::autoFreeze()
{
    if (!sonoParameters()->pBV(BFPNames::FreezeStr) && sonoParameters()->pBV(BFPNames::EnableFreezeAfterProbeFoundStr))
    {
        sendSyncEventTo(UEventType::EVENT_AUTOFREEZE, NULL);
    }
}

void ImageManager::resetFourDProbe(QVariant probeid)
{
#ifdef USE_MOTOR
//    Q_ASSERT(m_SonoParameters != NULL);
//    const ProbeDataInfo& probe = m_ProbeDataSet->getProbe(probeid.toInt());
//    if (probe.IsFourD)
//    {
//        m_PeripheralManager->motorControl()->resetMotor();
//        m_SonoParameters->parameter(BFPNames::FourDMotorAngleStr)->setEnabled(true);
//    }
//    else
//    {
//        m_SonoParameters->parameter(BFPNames::FourDMotorAngleStr)->setEnabled(false);
//    }
#endif
}

void ImageManager::checkProbeState()
{
    if (!beamFormer()->isProbeOnline())
    {
        sonoParameters()->setPV(BFPNames::EnableFreezeAfterProbeFoundStr, true);
        chisonUltrasoundContext()->pauseReceivingImage();
        autoFreeze();
    }
}

void ImageManager::onProbeIdChanged(QVariant probeid)
{
    const ProbeDataInfo& probe = m_ProbeDataSet->getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
    sendSyncEventTo(UEventType::SET_UPDATEPROBEINFO, &probe);
}

void ImageManager::onSetCudaAddr(quint64 addr)
{
    BeamFormerBase* beamformerbase = dynamic_cast<BeamFormerBase*>(beamFormer());

    beamformerbase->bfIODevice()->setCudaAddr(addr);
}

void ImageManager::updateMotorParameter()
{
#ifdef USE_4D
    // example for motorcontrol
    // bool isAbdominal = m_ProbeDataSet->getProbe(sonoParameters()->pIV(BFPNames::ProbeIdStr)).FourD_ProbeType ==
    // ProbeDataInfo::Abdominal; bool isBilateral =
    // m_ProbeDataSet->getProbe(sonoParameters()->pIV(BFPNames::ProbeIdStr)).FourD_FlakyType ==
    // ProbeDataInfo::Bilateral; m_PeripheralManager->motorControl()->setMotorParameter(motorParam, isAbdominal);
    // m_PeripheralManager->motorControl()->setFlaky(isBilateral);
//    chisonFourdProxy()->updateMotorParameter(
//        m_ProbeDataSet->getProbe(sonoParameters()->pIV(BFPNames::ProbeIdStr)).WaferNum, sonoParameters());
#endif
}

void ImageManager::startCheckProbeTimer()
{
    QTimer::singleShot(200, this, SLOT(checkProbeState()));
}

void ImageManager::clearBuffers()
{
    chisonUltrasoundContext()->clear();
}

void ImageManager::sendHardwareKey()
{
    m_BeamFormer->sendHardwareKey();
}

void ImageManager::checkAutoUpdate()
{
    // 10s 为多次观察应用启动加载 之后设置的时间间隔
    connect(&m_UpdateControlTimer, &QTimer::timeout, this, [&]() {
        if (UpdateSetting::instance().isSupportAutoUpdateFPGA())
        {
            if (m_StopUpdateControlTimer)
                return;

            connect(
                m_BeamFormer->probeInfoModel(), &IProbeInfoModel::probeStateChanged, this,
                [this](int socket, bool state) {
                    if (state && (!m_StopUpdateControlTimer)) //基元检测弹窗在的情况下，不进行FPGA自动升级提示
                    {
                        onAutoUpdate(Hardware, QString::number(socket));
                    }
                },
                Qt::QueuedConnection);
            if (!m_IsStartAutoUpdateFPGATimerSecond) //基元检测弹窗退出后，只重新start
                                                     // m_UpdateControlTimer,不弹FPGA自动升级提示框
            {
                metaObject()->invokeMethod(this, "onAutoUpdate", Qt::QueuedConnection);
            }
            else
            {
                m_IsStartAutoUpdateFPGATimerSecond = false;
            }
        }
    });

    m_UpdateControlTimer.setSingleShot(true);
    m_UpdateControlTimer.start(10000);
    m_StopUpdateControlTimer = false;
}

IProbeDataSet* ImageManager::probeDataSet() const
{
    return m_ProbeDataSet;
}

IImageSaveHelper* ImageManager::imageSaveHelper() const
{
    return m_ImageSaveHelper;
}

IElementDetectAlgorithom* ImageManager::elementDetectAlgorithom() const
{
    return m_ElementDetectAlgorithom;
}

IImageInterfaceForExam* ImageManager::imageInterfaceForExam() const
{
    return m_ImageInterfaceForExam;
}

IColorMapManager* ImageManager::colorMapManager() const
{
    return m_ColorMapManager;
}

bool ImageManager::curProbeIsConnected() const
{
    if (m_Codes.count() > m_SonoParameters->pIV(BFPNames::SocketStr))
    {
        return m_Codes[m_SonoParameters->pIV(BFPNames::SocketStr)] != -1;
    }

    return false;
}

void ImageManager::stopUpdateControlTimer()
{
    m_StopUpdateControlTimer = true;
    m_UpdateControlTimer.stop();
}

void ImageManager::startUpdateControlTimer()
{
    if (m_StopUpdateControlTimer)
    {
        m_IsStartAutoUpdateFPGATimerSecond = true;
        m_UpdateControlTimer.setSingleShot(true);
        m_UpdateControlTimer.start(10000);
        m_StopUpdateControlTimer = false;
    }
}

bool ImageManager::isNeedUpdateFPGA(int type, const QString& key)
{
    if (m_UpdateController != nullptr)
    {
        m_UpdateController->setHwUpdateMode(FPGAUpdateModeDef::Packets_Mode);
        return m_UpdateController->isNeedUpdateFPGA(type, key);
    }

    return false;
}

void ImageManager::setMustUpdateFPGA(bool mustUpdateFPGA)
{
    if (m_UpdateController != nullptr)
    {
        m_UpdateController->setIsMustUpdateFPGA(mustUpdateFPGA);
    }
}

void ImageManager::updateFPGA(int type, const QString& key)
{
    onAutoUpdate(type, key);
}

void ImageManager::setSafeVolatile2CTNumber(float value)
{
    float vol = m_PeripheralManager->hardWareMonitorManager()->volatilevlue2CTNumber(value);
    log()->info("...........................set CWTransmitPowerPositiveVolStr:%1\r\n", vol);
    m_SonoParameters->setPV(BFPNames::CWTransmitPowerPositiveVolStr, vol);
}

bool ImageManager::checkCWVolatile(float safeVoltage, int times)
{
    return m_PeripheralManager->hardWareMonitorManager()->checkCWVolatile(safeVoltage, times);
}

void ImageManager::createSonoParameters()
{
    beforeBehavior(PRETTY_FUNCTION);

    SonoParaFileProvider provider;
    QString path = provider.getResourceFile(AppSetting::model().toStdString().c_str());
    m_XmlBFParameterLoader = new XmlBFParameterLoader(path);

    BFParameters bfPara = m_XmlBFParameterLoader->build();
    bfPara.m_SonoParameters->setIsRealTime(true);
    m_SonoParameters = bfPara.m_SonoParameters;
    m_ControlTableLen = bfPara.m_ControlTableLen;

    afterBehavior(PRETTY_FUNCTION);
}

SonoParameters* ImageManager::sonoParameters() const
{
    return m_SonoParameters;
}

CineLooper* ImageManager::cineLooper() const
{
    return chisonUltrasoundContext()->cineLooper();
}

bool ImageManager::isCineLooperLooping()
{
    CineLooper* cineLooper = chisonUltrasoundContext()->cineLooper();
    return (cineLooper != NULL && cineLooper->isLooping());
}

void ImageManager::startCineLooper()
{
    CineLooper* cineLooper = chisonUltrasoundContext()->cineLooper();
    if (cineLooper != NULL)
    {
        cineLooper->startLoop();
    }
}

void ImageManager::stopCineLooper()
{
    CineLooper* cineLooper = chisonUltrasoundContext()->cineLooper();
    if (cineLooper != NULL && cineLooper->isLooping())
    {
        cineLooper->stopLoop();
    }
}

void ImageManager::generateBFNames()
{
    if (m_XmlBFParameterLoader != NULL)
    {
        m_XmlBFParameterLoader->generateBFNames();
        Util::SafeDeletePtr(m_XmlBFParameterLoader);
    }
}

SystemScanMode ImageManager::systemScanMode() const
{
    IBeamFormer* beamformer = beamFormer();
    if (beamformer != NULL)
    {
        return beamformer->systemScanMode();
    }
    else
    {
        return SystemScanMode::SystemScanModeB;
    }
}

IBeamFormer* ImageManager::beamFormer() const
{
    return m_BeamFormer;
}

IProbePresetModel* ImageManager::probePresetModel() const
{
    return m_ProbePresetModel;
}

ChisonUltrasoundContext* ImageManager::chisonUltrasoundContext() const
{
    return m_ChisonContext;
}

ILineBufferManager* ImageManager::lineBufferManager() const
{
    return m_ChisonContext->lineBufferManager();
}

IBufferStoreManager* ImageManager::bufferStoreManager() const
{
    return m_BufferStoreManager;
}

QString ImageManager::currentExamModeID() const
{
    return m_BufferStoreManager->curSonoParameters()->pSV(BFPNames::ExamModeIdStr);
}

UpdateController* ImageManager::updateController() const
{
    return m_UpdateController;
}

TGCAdjustmentWidgetController* ImageManager::controllerOfTGCAdjustment() const
{
    return m_TGCAdjustmentController;
}

void ImageManager::initializeVirtualMode()
{
#ifdef USE_RUN_MODE_VIRTUAL
    beforeBehavior(PRETTY_FUNCTION);
    LineDataSimulator::setLinePointNum(m_SonoParameters->pIV(BFPNames::PointNumPerLineStr));
    afterBehavior(PRETTY_FUNCTION);
#endif
}

void ImageManager::createBeamFormer()
{
    beforeBehavior(PRETTY_FUNCTION);

    IBeamFormerFactory* bfFactory = BeamFormerFactoryCreator::createFactory(AppSetting::model());
    m_BeamFormer = bfFactory->create();
    m_BeamFormer->setStateManager(m_StateManager);
    m_BeamFormer->setColorMapManager(m_ColorMapManager);

    delete bfFactory;

    m_BeamFormer->initialize();
    m_BeamFormer->setControlTableLen(m_ControlTableLen);
    m_BeamFormer->setSonoParameters(sonoParameters());
    m_BeamFormer->setSubSystemSubject(dynamic_cast<USFObject*>(this));
    AIOController::instance().setSonoParameters(sonoParameters());

    Q_ASSERT(m_PeripheralManager);
    beamFormer()->setSoundController(m_PeripheralManager->soundController());

#ifdef USE_TARGET_PALM
    fpgaupgrademodel_palm::instance().setSonoParameters(sonoParameters());
    fpgaupgrademodel_palm::instance().setControlTable(beamFormer()->getControlTable());
#else
    FPGAUpgradeModel::instance().setSonoParameters(sonoParameters());
    FPGAUpgradeModel::instance().setControlTable(beamFormer()->getControlTable());
#endif
    FPGAUpgradecontrol::instance().setBeamFormer(beamFormer());

    QObject::connect(&(FPGAUpgradeModel::instance()), SIGNAL(progressBarValueChanged(int)),
                     &(FPGAUpgradecontrol::instance()), SIGNAL(progressBarValueChanged(int)));
    QObject::connect(&(FPGAUpgradeModel::instance()), SIGNAL(upgradeFinalState(int)), &(FPGAUpgradecontrol::instance()),
                     SIGNAL(upgradeFinalState(int)));

    m_PeripheralManager->machine()->setShutdownController(beamFormer());

    afterBehavior(PRETTY_FUNCTION);

    connect(beamFormer(), &IBeamFormer::beforeExamModeChanged, this, [this](bool& isNeedUpdate, int selectSocket) {
        int socket = m_SonoParameters->pIV(BFPNames::SocketStr);
        //检测当前探头是否需要升级，如果需要升级，则先升级
        isNeedUpdate = isNeedUpdateFPGA(Hardware, QString::number(selectSocket));
        if (isNeedUpdate)
        {
            setMustUpdateFPGA(true);
            updateFPGA(Hardware, QString::number(selectSocket));
        }
    });
}

void ImageManager::initializeVideoOptions()
{
    beforeBehavior(PRETTY_FUNCTION);

    createVideoControl();

    // VideoFecture::instance().setFecture(Setting::instance().defaults().videoFecture());
    VideoControlHandler::instance().videoControl()->setVideoFecture();

    if (Setting::instance().defaults().videoIsOpened())
    {
        // VideoDevice::instance().open();
        VideoControlHandler::instance().videoControl()->setVideoDeviceOpen();
    }
    else
    {
        // VideoDevice::instance().close();
        VideoControlHandler::instance().videoControl()->setVideoDeviceClose();
    }

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createVideoControl()
{
    IVideoControl* videoControl;
    if (ModelConfig::instance().value(ModelConfig::IsFPGAVideo, false).toBool())
    {
        m_VideoModelQBit = new VideoModelQBit;
        m_VideoModelQBit->setControlTable(beamFormer()->getControlTable());
        m_VideoModelQBit->setSonoParameters(sonoParameters());
        Q_ASSERT(m_PeripheralManager);
        QObject::connect(m_VideoModelQBit, SIGNAL(videoPrint()), m_PeripheralManager->keyBoard(),
                         SLOT(sendVideoPrintCmd()));
        VideoControlQBit* control = new VideoControlQBit;
        control->setVideoModel(m_VideoModelQBit);
        videoControl = control;
    }
    else
    {
        videoControl = new VideoControlECO;
    }
    VideoControlHandler::instance().setVideoControl(videoControl);
}

void ImageManager::createProbePresetModel()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_ProbePresetModel = new ProbePresetModel;
    SonoParametersClientBase* clientBase = dynamic_cast<SonoParametersClientBase*>(m_ProbePresetModel);
    Q_ASSERT(clientBase != NULL);
    clientBase->setSonoParameters(sonoParameters());
    BeamFormerContainer* containerBase = dynamic_cast<BeamFormerContainer*>(m_ProbePresetModel);
    Q_ASSERT(containerBase != NULL);
    containerBase->setBeamFormer(beamFormer());

    m_ProbePresetModel->setStateManager(m_StateManager);
    m_ProbePresetModel->setColorMapManager(m_ColorMapManager);
    connect(m_ProbePresetModel, SIGNAL(currentProbeDisconnected()), this, SLOT(autoFreeze()));

    connect(m_ProbePresetModel, &ProbePresetModel::probesChanged, this,
            [=](const QVector<int>& codes, const QVector<bool>& value) {
                m_Codes = codes;
                m_ProbesChanged = value;

                if (m_ChisonContext != nullptr)
                {
                    m_ChisonContext->updateProbesChangedInfo(m_Codes, m_ProbesChanged);
                }
            });

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createChisonUltrasoundContext()
{
    beforeBehavior(PRETTY_FUNCTION);

    Q_ASSERT(m_ImageSaveHelper != NULL); // m_ImageSaveHelper需先于m_ChisonContext构建
    m_ChisonContext = new ChisonUltrasoundContext(m_ImageSaveHelper, ChisonUltrasound::ChisonUltrasound_RealTime
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
                                                  ,
                                                  m_ShareGLContext
#endif
    );

    BeamFormerBase* beamformerbase = dynamic_cast<BeamFormerBase*>(beamFormer());

    Q_ASSERT(beamformerbase != NULL);
    m_ChisonContext->setBFStaticParameters(beamformerbase->staticParameters());
    m_ChisonContext->setStateManager(m_StateManager);

    connect(m_ChisonContext, SIGNAL(setCudaAddr(quint64)), this, SLOT(onSetCudaAddr(quint64)), Qt::DirectConnection);
    connect(m_ChisonContext, SIGNAL(datainfo(void*, int, int, int, int)), &FrameControl::instance(),
            SLOT(onDatainfo(void*, int, int, int, int)));
    connect(beamFormer(), SIGNAL(resetAutoFreezeTime()), &FrameControl::instance(), SLOT(resetStartTime()));
    connect(m_ChisonContext, SIGNAL(wholeImageInfo(void*, int, int)), &FrameControl::instance(),
            SLOT(wholeImageInfo(void*, int, int)));
    connect(m_ChisonContext, SIGNAL(wholeGrayImage(void*, int, int)), &FrameControl::instance(),
            SLOT(onWholeGrayImage(void*, int, int)));
    connect(beamFormer(), SIGNAL(needleAnglesChanged(QList<int>)), &FrameControl::instance(),
            SLOT(onNeedleAnglesChanged(QList<int>)));

    FrameControl::instance().setSonoParameters(sonoParameters());
    FrameControl::instance().setImageSaveHelper(m_ImageSaveHelper);
    FrameControl::instance().setBaseBFStaticParameters(((BeamFormerBase*)beamFormer())->staticParameters());
    connect(&FrameControl::instance(), SIGNAL(imageNoChanged()), m_ChisonContext, SLOT(onImageNoChanged()));

    m_ChisonContext->setSonoParameters(sonoParameters());
    m_ChisonContext->initCurvedPanoramic(sonoParameters());

    connect(beamFormer(), SIGNAL(imageStable()), m_ChisonContext, SLOT(onImageStable()));
    connect(beamFormer(), SIGNAL(imageUnstable()), m_ChisonContext, SLOT(onImageUnstable()));
    connect(beamFormer(), SIGNAL(imageShapeStable()), m_ChisonContext, SLOT(onImageShapeStable()));
    connect(beamFormer(), SIGNAL(imageShapeUnstable()), m_ChisonContext, SLOT(onImageShapeUnstable()));
    connect(beamFormer(), SIGNAL(updateImageData(ByteBuffer)), m_ChisonContext, SIGNAL(updateImageData(ByteBuffer)),
            Qt::DirectConnection);

    connect(m_ChisonContext, SIGNAL(activeLayoutSonoParametersChanged(SonoParameters*)), beamFormer(),
            SLOT(onBufferSonoParametersChanged(SonoParameters*)), Qt::DirectConnection);

    connect(m_ChisonContext, SIGNAL(newHWInfo(unsigned char*, int)), beamFormer(), SLOT(onUpdate(unsigned char*, int)),
            Qt::DirectConnection);

    IHardwareMonitorManager* manager = m_PeripheralManager->hardWareMonitorManager();
    connect(m_ChisonContext, SIGNAL(newHWInfo(unsigned char*, int)), manager, SLOT(onNewHWInfo(unsigned char*, int)),
            Qt::DirectConnection);
    connect(m_ChisonContext, SIGNAL(ECGInfo(unsigned char)), manager, SLOT(onECGInfoChanged(unsigned char)),
            Qt::DirectConnection);

    IProbePresetModel* model = probePresetModel();
    connect(model, SIGNAL(presetBeginChanged()), m_ChisonContext, SLOT(onPresetBeginChanged()));
    connect(model, SIGNAL(presetEndChanged()), m_ChisonContext, SLOT(onPresetEndChanged()));
    connect(model, SIGNAL(presetBeginChanged()), m_ChisonContext, SLOT(onBeginPaused()));
    connect(model, SIGNAL(presetEndChanged()), m_ChisonContext, SLOT(onEndPaused()));

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createBufferStoreManager()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_BufferStoreManager = new BufferStoreManager(
        this, m_MainWindow); // BufferStoreManager构造时，需要用到ImageManager对象，因此，作为形参传入
    m_BufferStoreManager->setExamManager(m_ExamManager);
    m_BufferStoreManager->setMarkManager(m_MarkManager);
    m_BufferStoreManager->setStateManager(m_StateManager);

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::initializeFourDProbe()
{
    beforeBehavior(PRETTY_FUNCTION);

    Q_ASSERT(m_SonoParameters != NULL);
    connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(resetFourDProbe(QVariant)));
    resetFourDProbe(QVariant());

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::enableProbeAutoPowerDownOFF()
{
    beforeBehavior(PRETTY_FUNCTION);

    if (!Setting::instance().defaults().isIODeviceVirtual())
    {
        connect(beamFormer()->probeInfoModel(), &IProbeInfoModel::probeStateChanged, this,
                [this](int socket, bool state) {
                    if (state) // probe identified
                    {
                        Q_UNUSED(socket);
                        m_SonoParameters->setPV(BFPNames::AutoPowerDown_OFFStr, true);
                    }
                });
    }

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createUpdateController()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_UpdateController = new UpdateController(UpdateController::Normal_Hardware);
    // init UpdateController
    m_UpdateController->setCineLooper(chisonUltrasoundContext()->cineLooper());
    m_UpdateController->setStateManager(m_StateManager);
    m_UpdateController->setSonoParameters(sonoParameters());
    m_UpdateController->setDiskDevice(m_DiskDevice);

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createProbeDataSet()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_ProbeDataSet = new ProbeDataSetFacade;

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createImageSaveHelper()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_ImageSaveHelper = new ImageSaveHelper();

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createElementDetectAlgorithom()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_ElementDetectAlgorithom = new ElementDetectAlgotithom();

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createImageInterfaceForExam()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_ImageInterfaceForExam = new ImageInterfaceForExam;

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createColorMapManager()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_ColorMapManager = new ColorMapManager;

    afterBehavior(PRETTY_FUNCTION);
}

void ImageManager::createTGCAdjustController()
{
    if (nullptr == m_TGCAdjustmentController)
    {
        m_TGCAdjustmentController = new TGCAdjustmentWidgetController(m_SonoParameters, TGC_COUNT);
    }
}

void ImageManager::initializeEnd()
{
    Q_ASSERT(m_PeripheralManager != NULL);
    Q_ASSERT(m_SonoParameters != NULL);

    connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onProbeIdChanged(QVariant)));
    //下述代码在蔡剑的协助下查到是14年在MainModle中添加的，根本原因是处理冻结下调节TGC后，解冻打图还能重新给FPGA设置
    //目前基于Zeus实施方案做了调整，首先冻结下调节也能响应，其次解冻后也能正常下发新值给FPGA
    //此次调查的背景是ATOM上切换探头，发现偶发图像异常，有不符合当前场景的图像，调查发现是有TGC消息返回且数值与上次有变化，
    //该情况与李德、硬件沟通后，现做两个方向的事情：
    // 1）切换预设值时，软件不要发送sendTgcCMD（协议中约定，发送后，硬件会回应）
    // 2）开启监听键盘(TGC)的系统软件，检验TGC是否工作正常（虽然软件已去掉发送指令，但也得测试键盘本身的健壮性，测试方式：开启监听软件，运行pangu自动化脚本）
    //    connect(m_SonoParameters, SIGNAL(presetChanged(PresetParameters)), m_PeripheralManager->keyBoard(),
    //    SLOT(sendTgcCMD()));
    connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)),
            this, SLOT(onBeforeSystemScanModeChanged(QVariant, QVariant&)));
    connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant, bool)), this,
            SLOT(onSystemScanModeChanged(QVariant, bool)));
}
