#ifndef IMAGEMANAGER_H
#define IMAGEMANAGER_H

#include "iimagemanager.h"
#include "infostruct.h"
#include "usfimaginginterface_global.h"
#include <QObject>
#include <QTimer>
#include <QVariant>

class XmlBFParameterLoader;
class VideoModelQBit;
class QOpenGLContext;
class QOpenGLWidget;
class BaseScanModeUpdater;

/**
 * @brief ImageManager: 超声图像子系统接口实现类
 *
 * 用途：超声图像子系统接口类的具体实现，外部模块（除构建方模块）禁止直接访问，外部模块通过其基类访问其接口功能
 *
 */
class USF_IMAGING_INTERFACE_EXPORT ImageManager : public QObject, public IImageManager
{
    Q_OBJECT
public:
    ImageManager();
    virtual ~ImageManager();

public:
    virtual void init();
    virtual void release();
    virtual void reset();
    virtual void startRealtimeSystem();
    virtual void freeze();
    virtual void unFreeze();
    virtual void unFreezeState();
    virtual void switchScanMode(SystemScanMode scan_mode, const QString& state_name);
    virtual void changeLayout(SystemScanMode scan_mode, int layout, int active_layout);
    virtual bool adjustParam(const QString& name, const QVariant& value,
                             PARAM_ADJUST_ASK_TYPE type = PARAM_ADJUST_ASK_TYPE::PARAM_VALUE);
    virtual bool askParam(const QString& name, QVariant& result, PARAM_ADJUST_ASK_TYPE ask_type = PARAM_VALUE,
                          IMG_PARAM_SOURCE_TYPE source_type = Realtime);
    virtual void setPeripheralManager(IPeripheralManager* value);
    virtual IPeripheralManager* getPeripheralManager();
    virtual void setExamManager(IExamManager* value);
    virtual void setMarkManager(IMarkManager* value);
    virtual void setMainWindow(QWidget* parent);
    virtual void setStateManagerFacade(IStateManager* value);
    virtual void setDiskDevice(IDiskDevice* diskDevice);
    virtual int getFrameCount() const;
    virtual bool enterState(FUNC_STATE state, const FuncStateInput& input = FuncStateInput());
    virtual bool exitState(FUNC_STATE state, const FuncStateInput& input = FuncStateInput());
    virtual void waitForHasFrameData(int delay);
    virtual void waitFor2DImagePushed(int delay, int layoutIndex);
    virtual void controlVideoFullScreen(bool value);
    virtual void resetTGCData();

    virtual void setColorMap(uchar* image2DMap, uchar* waveMap);

    virtual SonoParameters* sonoParameters() const;
    virtual CineLooper* cineLooper() const;
    virtual bool isCineLooperLooping();
    virtual void startCineLooper();
    virtual void stopCineLooper();
    virtual void generateBFNames();
    virtual SystemScanMode systemScanMode() const;

    virtual IBeamFormer* beamFormer() const;

    virtual IProbePresetModel* probePresetModel() const;
    virtual ChisonUltrasoundContext* chisonUltrasoundContext() const;
    virtual ILineBufferManager* lineBufferManager() const;
    virtual IBufferStoreManager* bufferStoreManager() const;

    virtual QString currentExamModeID() const;
    virtual UpdateController* updateController() const;

    TGCAdjustmentWidgetController* controllerOfTGCAdjustment() const;

    virtual void updateMotorParameter();
    virtual void startCheckProbeTimer();
    virtual void clearBuffers();
    virtual void sendHardwareKey();
    virtual void checkAutoUpdate();
    virtual IProbeDataSet* probeDataSet() const;
    virtual IImageSaveHelper* imageSaveHelper() const;
    virtual IElementDetectAlgorithom* elementDetectAlgorithom() const;
    virtual IImageInterfaceForExam* imageInterfaceForExam() const;
    virtual IColorMapManager* colorMapManager() const;
    virtual bool curProbeIsConnected() const;
    virtual void stopUpdateControlTimer();
    virtual void startUpdateControlTimer();
    virtual bool isNeedUpdateFPGA(int type, const QString& key);
    virtual void setMustUpdateFPGA(bool mustUpdateFPGA);
    virtual void updateFPGA(int type, const QString& key);
    virtual void setSafeVolatile2CTNumber(float value);
    virtual bool checkCWVolatile(float safeVoltage, int times);

private slots:
    Q_INVOKABLE void onAutoUpdate(int type = -1, const QString& socket = "");
    void autoFreeze();
    void resetFourDProbe(QVariant probeid);
    void checkProbeState();
    void onProbeIdChanged(QVariant probeid);
    void onSetCudaAddr(quint64 addr);

private:
    void createSonoParameters();
    void initializeVirtualMode();
    void createBeamFormer();
    void initializeVideoOptions();
    void createVideoControl();
    void createProbePresetModel();
    void createChisonUltrasoundContext();
    void createBufferStoreManager();
    void initializeFourDProbe(); // 原MainModule::createFourDProbeControlModel方法
    void enableProbeAutoPowerDownOFF();
    void createUpdateController();
    void createProbeDataSet();
    void createImageSaveHelper();
    void createElementDetectAlgorithom();
    void createImageInterfaceForExam();
    void createColorMapManager();
    void createTGCAdjustController();
    void initializeEnd();

private:
    SonoParameters* getSonoParameters(IMG_PARAM_SOURCE_TYPE type = Realtime);

private:
    SonoParameters* m_SonoParameters;
    int m_ControlTableLen;
    XmlBFParameterLoader* m_XmlBFParameterLoader;
    IBeamFormer* m_BeamFormer;
    VideoModelQBit* m_VideoModelQBit;
    IProbePresetModel* m_ProbePresetModel;
    ChisonUltrasoundContext* m_ChisonContext;
    IBufferStoreManager* m_BufferStoreManager;
    UpdateController* m_UpdateController;
    TGCAdjustmentWidgetController* m_TGCAdjustmentController;
    QTimer m_UpdateControlTimer;
    bool m_StopUpdateControlTimer;
    bool m_IsStartAutoUpdateFPGATimerSecond;

#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
    QOpenGLContext* m_ShareGLContext;
    QOpenGLWidget* m_OpenGLWidget;
#endif
    BaseScanModeUpdater* m_ScanModeUpdater;
    QVector<bool> m_ProbesChanged;
    QVector<int> m_Codes;

protected:
    IPeripheralManager* m_PeripheralManager;
    IExamManager* m_ExamManager;
    IMarkManager* m_MarkManager;
    QWidget* m_MainWindow;
    IStateManager* m_StateManager;
    IDiskDevice* m_DiskDevice;
    IColorMapManager* m_ColorMapManager;
    IProbeDataSet* m_ProbeDataSet;
    IImageSaveHelper* m_ImageSaveHelper;
    IElementDetectAlgorithom* m_ElementDetectAlgorithom;
    IImageInterfaceForExam* m_ImageInterfaceForExam;
};

#endif
