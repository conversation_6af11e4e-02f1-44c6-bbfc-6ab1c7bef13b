#include "bdmousetool.h"
#include "applicationinfo.h"
#include "bfpnames.h"
#include "parameter.h"
#include "physicalgeometrycontroller.h"
#include "setting.h"
#include "variantutil.h"
#include <QApplication>
#include <QDebug>
#include <QGraphicsPathItem>
#include <QPoint>
#include <QSize>
#include <QtMath>

BDMouseTool::BDMouseTool(PhysicalGeometryController* controller, QObject* parent)
    : PhysicalGeometryControllerTool(controller, parent)
    , m_BDActionType(Move)
    , m_AudioOpened(true)
    , m_multiTouching(false)
    , m_AdjustAngleing(false)
    , m_Type(PW_OP_NORMAL)
    , m_OffsetYCount(0)
    , m_IsTouching(false)
    , m_SonoParameters(nullptr)
    , m_ImageWidget(nullptr)
{
    m_RawHint = "Gate";
    setActionsStep(QPoint(6, 6));
    m_AllowPressCount = 3;
    m_DisableLoopNext = true;
}

BDMouseTool::BDMouseTool(PhysicalGeometryController* controller, SonoParameters* sonoParameters, QWidget* imageWidget,
                         QGraphicsView* pwAngleView, QObject* parent)
    : PhysicalGeometryControllerTool(controller, parent)
    , m_BDActionType(Move)
    , m_AudioOpened(true)
    , m_SonoParameters(sonoParameters)
    , m_multiTouching(false)
    , m_ImageWidget(imageWidget)
    , m_PwAngeView(pwAngleView)
    , m_AdjustAngleing(false)
    , m_Type(PW_OP_NORMAL)
    , m_OffsetYCount(0)
    , m_IsTouching(false)
{
    m_RawHint = "Gate";
    setActionsStep(QPoint(6, 6));
    m_AllowPressCount = 3;
    m_DisableLoopNext = true;
    sonoParameters->pBV(BFPNames::ACW_DCWStr);
}

void BDMouseTool::onExecute()
{
    const QVariant& windowValue = m_SonoParameters->pV(BFPNames::RenderWidgetRectsStr);
    Q_ASSERT(windowValue.type() == QVariant::List);
    IModeGlyphsWidget::LayoutRects windowRects = VariantUtil::variant2Rects(windowValue);

    const QVariant& imageRectsValue = m_SonoParameters->pV(BFPNames::ImageRectsStr);
    Q_ASSERT(imageRectsValue.type() == QVariant::List);
    IModeGlyphsWidget::LayoutRects imageRects = VariantUtil::variant2Rects(imageRectsValue);

    if (!m_SonoParameters->parameterSet().contains(BFPNames::ImageRegionsStr))
    {
        m_Regions = imageRects;
    }
    else
    {
        const QVariant& regValue = m_SonoParameters->pV(BFPNames::ImageRegionsStr);
        Q_ASSERT(imageRectsValue.type() == QVariant::List);
        m_Regions = VariantUtil::variant2Rects(regValue);

        Q_ASSERT(imageRects.count() == m_Regions.count());
    }

    changeActionType(Move);
    PhysicalGeometryControllerTool::onExecute();
}

void BDMouseTool::onLeftButtonPressed(const QPoint& pos)
{
    Q_UNUSED(pos)

    if (m_IsTouching)
    {
        return;
    }

    if (m_BDActionType == Move)
    {
        changeActionType(Change);
        setActionsStep(QPoint(12, 12));
    }
    else if (m_BDActionType == Change)
    {
        changeActionType(Move);
        setActionsStep(QPoint(6, 6));
    }
}

void BDMouseTool::onRightButtonPressed(const QPoint& pos)
{
    Q_UNUSED(pos)
    emit rightButtonPressed();
}

void BDMouseTool::onMovedAction(const QPoint& offset, const QPoint& pos)
{
    Q_UNUSED(pos)

    if (m_IsTouching)
    {
        return;
    }

    if (m_BDActionType == Move)
    {
        if (offset == pos)
        {
            // 自动多普勒调整功能计算的移动
            qDebug() << PRETTY_FUNCTION << "Do doppoler auto adjust move";
            m_Controller->setOperationSource(PhysicalGeometryController::OPER_SRC_DOPPLER_AI);
            m_Controller->move(offset);
            m_Controller->setOperationSource(PhysicalGeometryController::OPER_SRC_GENERAL);
        }
        else
        {
            m_Controller->move(offset);
        }
    }
    else if (m_BDActionType == Change)
    {
        m_Controller->scale(offset);
    }
}

void BDMouseTool::onTouchPressed(const QPoint& pos)
{
    if (adjustAngleInFullScreen(pos, OP_Press) || m_AdjustAngleing)
        return;

    if (m_multiTouching)
        return;

    m_IsTouching = true;

    m_touchPoint = pos;

    if (m_Regions.count() == 1)
    {
        m_Type = PW_OP_NORMAL;
    }
    else
    {
        if (m_Regions.first().contains(pos))
        {
            m_Type = PW_OP_NORMAL;
        }
        else
        {
            m_Type = PW_OP_BASELINE;
        }
    }

    if (m_SonoParameters->parameter(BFPNames::DopSteeringAngleStr)->isEnabled() &&
        isIncludeInLine(m_touchPoint)) // 1、在线上 则进入角度调节
    {
        changeActionType(Angle);
        setActionsStep(QPoint(2, 2));
    }
    else // 2、不在线上 则进入移动状态
    {
        changeActionType(Move);
    }
}

void BDMouseTool::onTouchReleased(const QPoint& pos)
{
    m_IsTouching = false;

    if (adjustAngleInFullScreen(pos, OP_Release))
        return;

    if (m_AdjustAngleing)
    {
        QMouseEvent me =
            QMouseEvent(QEvent::MouseButtonRelease, m_AnglePt, Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
        QApplication::sendEvent(m_PwAngeView->viewport(), &me);
        m_AdjustAngleing = false;
    }

    m_Controller->resetTouchOffsetCount();
    m_multiTouching = false;
    m_OffsetYCount = 0;
}

void BDMouseTool::onTouchMoved(const QPoint& pos)
{
    if (adjustAngleInFullScreen(pos, OP_Move) || m_AdjustAngleing)
        return;

    if (m_multiTouching)
        return;

    QPoint offset = pos - m_touchPoint;

    if (m_Type == PW_OP_NORMAL)
    {
        if (m_BDActionType == Move)
        {
            m_Controller->touchMove(offset);
        }
        else if (m_BDActionType == Angle)
        {
            int dopSteeringAngle = m_SonoParameters->pIV(BFPNames::DopSteeringAngleStr);

            int rotation = m_SonoParameters->pIV(BFPNames::RotationStr);

            if (rotation == 0 || rotation == 180)
            {
                if (qAbs(offset.x()) >= qAbs(offset.y()))
                {
                    if (rotation == 180)
                    {
                        m_angleStepValue -= offset.x();
                    }
                    else
                    {
                        m_angleStepValue += offset.x();
                    }
                }
                else
                {
                    m_angleStepValue = 0;
                }
            }
            else if (rotation == 90 || rotation == 270)
            {
                if (qAbs(offset.y()) >= qAbs(offset.x()))
                {
                    if (rotation == 270)
                    {
                        m_angleStepValue -= offset.y();
                    }
                    else
                    {
                        m_angleStepValue += offset.y();
                    }
                }
                else
                {
                    m_angleStepValue = 0;
                }
            }

            if (qAbs(m_angleStepValue) >= 30)
            {
                if (!m_SonoParameters->pBV(BFPNames::LeftStr))
                {
                    m_angleStepValue = -m_angleStepValue;
                }

                if (m_angleStepValue < 0) // 求出角度增加对应下标
                {
                    if (dopSteeringAngle > m_SonoParameters->pMin(BFPNames::DopSteeringAngleStr))
                    {
                        dopSteeringAngle--;
                    }
                }
                else // 求出角度减小对应下标
                {
                    if (dopSteeringAngle < m_SonoParameters->pMax(BFPNames::DopSteeringAngleStr))
                    {
                        dopSteeringAngle++;
                    }
                }

                // 触发更新角度逻辑
                m_SonoParameters->setPV(BFPNames::DopSteeringAngleStr, dopSteeringAngle);
                m_SonoParameters->parameter(BFPNames::DopSteeringAngleStr)->update();

                m_angleStepValue = 0;
            }
        }
    }
    else
    {
        static bool isAdd = false;
        if (m_OffsetYCount == 0)
        {
            isAdd = offset.y() < 0;
        }

        m_OffsetYCount += offset.y();
        if ((isAdd && offset.y() > 0) || (offset.y() < 0 && !isAdd))
        {
            m_OffsetYCount = 0;
        }

        // TODO Seven 在CW使能的时候不能用这个进行修改，要用BaseLineCWDStr
        QString baseLineParaName =
            m_SonoParameters->pBV(BFPNames::TDIEnStr)
                ? BFPNames::BaseLineDTDIStr
                : (m_SonoParameters->pBV(BFPNames::CWEnStr) ? BFPNames::BaseLineCWDStr : BFPNames::BaseLineStr);

        qreal step = m_Regions.last().height() * 1.0 / m_SonoParameters->parameter(baseLineParaName)->valueCount();
        if (qAbs(m_OffsetYCount) >= step)
        {
            m_SonoParameters->parameter(baseLineParaName)->multi(isAdd);
            m_OffsetYCount = 0;
        }
    }

    m_touchPoint = pos;
}

void BDMouseTool::onTouchPointMultiMoved(const QList<QTouchEvent::TouchPoint> touchPoints)
{
    m_multiTouching = true;

    if (m_Type == PW_OP_BASELINE)
    {
        return;
    }

    const QPointF touchPointOne = touchPoints.at(0).pos();
    const QPointF touchPointTwo = touchPoints.at(1).pos();

    static qreal distance =
        sqrt(pow(touchPointTwo.x() - touchPointOne.x(), 2) + pow(touchPointTwo.y() - touchPointOne.y(), 2));
    qreal curDistance =
        sqrt(pow(touchPointTwo.x() - touchPointOne.x(), 2) + pow(touchPointTwo.y() - touchPointOne.y(), 2));

    if (qAbs(curDistance - distance) >= 10)
    {
        m_Controller->scale(curDistance >= distance);
        distance = curDistance;
    }
}

void BDMouseTool::changeActionType(BDMouseTool::ActionType type)
{
    m_BDActionType = type;
    if (m_BDActionType == Move)
    {
        ApplicationInfo::instance().setLeftButtonStatus("BD_Move");
    }
    else if (m_BDActionType == Change)
    {
        ApplicationInfo::instance().setLeftButtonStatus("BD_Change");
    }
    else
    {
        ApplicationInfo::instance().setLeftButtonStatus("BD_Angle");
    }
}

bool BDMouseTool::isIncludeInLine(QPoint pos)
{
    QPoint startPt(m_SonoParameters->pIV(BFPNames::DScanLineStartXStr),
                   m_SonoParameters->pIV(BFPNames::DScanLineStartYStr));
    QPoint endPt(m_SonoParameters->pIV(BFPNames::DScanLineEndXStr), m_SonoParameters->pIV(BFPNames::DScanLineEndYStr));

    // 允许60PX偏差
    QPainterPath path;
    QPolygonF poly;
    poly.append(QPointF(startPt.x() - 60, startPt.y()));
    poly.append(QPointF(startPt.x() + 60, startPt.y()));
    poly.append(QPointF(endPt.x() - 60, endPt.y()));
    poly.append(QPointF(endPt.x() + 60, endPt.y()));
    path.addPolygon(poly);
    QGraphicsPathItem line(path);

    int DScanLineFirstDivideY = m_SonoParameters->pIV(BFPNames::DScanLineFirstDivideYStr);
    int DScanLineSecondDivideY = m_SonoParameters->pIV(BFPNames::DScanLineSecondDivideYStr);

    bool isContains = line.contains(pos);

    if (isContains)
    {
        // TODO 最终需要调查掌超方案
        // 现在的方案： 当门很小(间隔小于30PX)时，门的上下边线周围15px为调节取样线位置
        //            在正常情况下，门的内部不触发角度调节,只触发位置调节
        if ((DScanLineSecondDivideY - DScanLineFirstDivideY < 30 &&
             (qAbs(pos.y() - DScanLineSecondDivideY) < 15 || qAbs(pos.y() - DScanLineSecondDivideY) < 15)) ||
            (pos.y() > DScanLineFirstDivideY && pos.y() < DScanLineSecondDivideY))
        {
            return false;
        }
    }

    return isContains;
}

bool BDMouseTool::adjustAngleInFullScreen(const QPoint& pos, OperationType op)
{
    QEvent::Type type = QEvent::MouseButtonPress;
    switch (op)
    {
    case OP_Press:
    {
        type = QEvent::MouseButtonPress;
        break;
    }
    case OP_Move:
    {
        type = QEvent::MouseMove;
        break;
    }
    case OP_Release:
    {
        type = QEvent::MouseButtonRelease;
        break;
    }
    default:
        break;
    }

    if (m_PwAngeView == nullptr)
        return false;

    QPoint globalPos = pos * m_ActionsModel->fullScreenAmplificationFactor();
    QPoint topLeft = m_PwAngeView->parentWidget()->geometry().topLeft() - m_ImageWidget->geometry().topLeft();
    QRect angleRect(topLeft.x(), topLeft.y(), m_PwAngeView->geometry().width(), m_PwAngeView->geometry().height());

    m_AnglePt = globalPos - angleRect.topLeft();

    if (angleRect.contains(globalPos))
    {
        if (type == QEvent::MouseButtonRelease)
        {
            m_AdjustAngleing = false;
        }
        else if (type == QEvent::MouseMove)
        {
            m_AdjustAngleing = true;
        }
        QMouseEvent me = QMouseEvent(type, m_AnglePt, Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
        QApplication::sendEvent(m_PwAngeView->viewport(), &me);
        return true;
    }
    return false;
}
