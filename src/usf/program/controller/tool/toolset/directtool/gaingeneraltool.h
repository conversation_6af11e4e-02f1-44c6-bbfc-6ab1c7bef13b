#ifndef GAINGENERALTOOL_H
#define GAINGENERALTOOL_H
#include "usfprogramcontroller_global.h"

#include "menutool.h"

class ICommand;
class SonoParameters;

class USF_PROGRAM_CONTROLLER_EXPORT GainGeneralTool : public MenuTool
{
    Q_OBJECT
public:
    GainGeneralTool(const QString& name);

protected:
    virtual void execute();
    virtual void chooseTool();
    int maxmValue() const;
    int minmValue() const;
    int stepValue() const;
    int value() const;
private slots:
    void onGainValueChanged();
    void connectParaSignals();
    void onValueChanged();
signals:
    void initParaValue() const;

protected:
    ICommand* m_gainTool;
};

#endif // GAINGENERALTOOL_H
