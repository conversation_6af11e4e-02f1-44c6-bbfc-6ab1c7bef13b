#include "gaingeneraltool.h"
#include "qvariantcommandargs.h"
#include "stringcommandargs.h"
#include "infostruct.h"
#include "sonoparameters.h"
#include "toolsfactory.h"
#include "toolnames.h"
#include "bfpnames.h"
#include "menucontroller.h"
#include "menunames.h"
#include "modelconfig.h"
#include <boost/foreach.hpp>
#include "parameter.h"
#include <QDebug>

GainGeneralTool::GainGeneralTool(const QString& name)
    : MenuTool(name)
    , m_gainTool(NULL)
{
    connect(this, SIGNAL(initParaValue()), this, SLOT(onValueChanged()));
}

void GainGeneralTool::execute()
{
    m_gainTool = NULL;
    connectParaSignals();
    if (m_gainTool != NULL)
    {
        return;
    }
    chooseTool();
    if (m_SonoParameters->pBV(BFPNames::FreezeStr) && !isPostProcessTool())
    {
        return;
    }

    // 冻结下宽景模式禁止Gain调节
    if (m_SonoParameters->pBV(BFPNames::FreezeStr) && m_SonoParameters->pBV(BFPNames::CurvedPanoramicEnableStr))
    {
        return;
    }

    QVariantCommandArgs* arg = this->args<QVariantCommandArgs>();
    if (arg != NULL)
    {
        m_gainTool->setArgs(arg);
    }

    m_gainTool->run();
    onGainValueChanged();
}

void GainGeneralTool::chooseTool()
{
    int menuIndex = MenuController::instance().currentParemeterMenuIndex();

    bool isPostTool = false;

    if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::BMenuStr) ||
        menuIndex == MenuNames::MenuList.indexOf(MenuNames::MMenuStr) ||
        menuIndex == MenuNames::MenuList.indexOf(MenuNames::EMenuStr) ||
        menuIndex == MenuNames::MenuList.indexOf(MenuNames::FreeMMenuStr) ||
        menuIndex == MenuNames::MenuList.indexOf(MenuNames::FourDPreMenuStr))
    {
        isPostTool = true;
        if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::MMenuStr) &&
            m_SonoParameters->contains(BFPNames::MGainStr))
        {
            if (m_SonoParameters->pBV(BFPNames::THIStr))
            {
                m_gainTool = ToolsFactory::instance().command("MGainThi");
            }
            else
            {
                m_gainTool = ToolsFactory::instance().command("MGain");
            }
        }
        else
        {
            m_gainTool = m_SonoParameters->pBV(BFPNames::THIStr) ? ToolsFactory::instance().command("GainThi")
                                                                 : ToolsFactory::instance().command("Gain");
        }
    }
    else if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::CMenuStr))
    {
        m_gainTool = ToolsFactory::instance().command(BFPNames::GainColorStr);
    }
    else if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::CPAMenuStr) ||
             menuIndex == MenuNames::MenuList.indexOf(MenuNames::DPDMenuStr))
    {
        m_gainTool = m_SonoParameters->pBV(BFPNames::SonoNeedleStr)
                         ? ToolsFactory::instance().command(BFPNames::GainSNStr)
                         : ToolsFactory::instance().command(BFPNames::GainPDStr);
        if (m_gainTool == NULL)
        {
            // ECO 没有GainPD，使用GainColor控制
            m_gainTool = ToolsFactory::instance().command(BFPNames::GainColorStr);
        }
    }
    else if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::TDIMenuStr))
    {
        m_gainTool = ToolsFactory::instance().command(BFPNames::GainTDIStr);
    }
    else if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::MVIMenuStr))
    {
        m_gainTool = ToolsFactory::instance().command(BFPNames::GainMVIStr);
    }
    else if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::DMenuStr))
    {
        if (m_SonoParameters->pBV(BFPNames::TriplexModeStr))
        {
            m_gainTool = ToolsFactory::instance().command("GainDopTM");
        }
        else
        {
            m_gainTool = ToolsFactory::instance().command("GainDop");
        }
    }
    else if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::DTDIMenuStr))
    {
        m_gainTool = ToolsFactory::instance().command("GainDopTDI");
    }
    else if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::CWDMenuStr))
    {
        m_gainTool = ToolsFactory::instance().command("GainDopCWD");
    }
    else if (menuIndex == MenuNames::MenuList.indexOf(MenuNames::FourDMenuStr) ||
             menuIndex == MenuNames::MenuList.indexOf(MenuNames::FourDHDMenuStr) ||
             menuIndex == MenuNames::MenuList.indexOf(MenuNames::Static3DMenuStr) ||
             menuIndex == MenuNames::MenuList.indexOf(MenuNames::Static3DHDMenuStr) ||
             menuIndex == MenuNames::MenuList.indexOf(MenuNames::ChisonFourDMenuStr))
    {
        m_gainTool = ToolsFactory::instance().command(BFPNames::FourDGainStr);
    }
    else
    {
        isPostTool = true;
        if ((SystemScanMode)(m_SonoParameters->pIV(BFPNames::SystemScanModeStr)) == SystemScanModeM)
        {
            m_gainTool = m_SonoParameters->pBV(BFPNames::THIStr) ? ToolsFactory::instance().command("MGainThi")
                                                                 : ToolsFactory::instance().command("MGain");
        }
        else
        {
            m_gainTool = m_SonoParameters->pBV(BFPNames::THIStr) ? ToolsFactory::instance().command("GainThi")
                                                                 : ToolsFactory::instance().command("Gain");
        }
    }

    setIsPostProcessTool(isPostTool);
}

int GainGeneralTool::maxmValue() const
{
    if (m_gainTool != nullptr)
    {
        ITool* tool = dynamic_cast<ITool*>(m_gainTool);
        if (tool != nullptr)
        {
            return tool->maxmValue();
        }
    }
    return 0;
}

int GainGeneralTool::minmValue() const
{
    if (m_gainTool != nullptr)
    {
        ITool* tool = dynamic_cast<ITool*>(m_gainTool);
        if (tool != nullptr)
        {
            return tool->minmValue();
        }
    }
    return 0;
}

int GainGeneralTool::stepValue() const
{
    if (m_gainTool != nullptr)
    {
        ITool* tool = dynamic_cast<ITool*>(m_gainTool);
        if (tool != nullptr)
        {
            return tool->stepValue();
        }
    }
    return 0;
}

int GainGeneralTool::value() const
{
    if (m_gainTool != nullptr)
    {
        ITool* tool = dynamic_cast<ITool*>(m_gainTool);
        if (tool != nullptr)
        {
            return tool->value();
        }
    }
    emit initParaValue();
    return 0;
}

void GainGeneralTool::onGainValueChanged()
{
    if (m_gainTool != nullptr)
    {
        ITool* tool = dynamic_cast<ITool*>(m_gainTool);
        if (tool != nullptr)
        {
            setToolValue(tool->valueTred());
        }
    }
    //    qDebug()<<m_SonoParameters->pIV(BFPNames::GainShowStr);
}

void GainGeneralTool::connectParaSignals()
{
    QString bfSigParam[] = {BFPNames::THIStr,       BFPNames::TriplexModeStr, BFPNames::HprfEnStr,
                            BFPNames::FourDGainStr, BFPNames::MGainStr,       BFPNames::SystemScanModeStr};

    BOOST_FOREACH (const QString& str, bfSigParam)
    {
        connect(m_SonoParameters->parameter(str), SIGNAL(valueChanged(QVariant)), this, SLOT(onValueChanged()));
    }
}

void GainGeneralTool::onValueChanged()
{
    ICommand* oldCom = m_gainTool;

    chooseTool();

    if (oldCom != m_gainTool)
    {
        onGainValueChanged();
    }
}
