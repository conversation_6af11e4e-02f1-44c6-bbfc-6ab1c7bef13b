#include "curvedpanoramicentool.h"
#include "abstractstate.h"
#include "applicationinfo.h"
#include "bfpnames.h"
#include "infostruct.h"
#include "bfscanwidthparameter.h"
#include "chisonultrasoundcontext.h"
#include "cinelooper.h"
#include "curvedpanoramic.h"
#include "idicomtaskmanager.h"
#include "freezebar.h"
#include "freezebarwidget.h"
#include "generalworkflowfunction.h"
#include "ibufferstoremanager.h"
#include "iimagewidget.h"
#include "ilinebuffer.h"
#include "ilinebuffermanager.h"
#include "imageeventargs.h"
#include "imagelabelinfo.h"
#include "imageskimmanager.h"
#include "imagetile.h"
#include "imagewidget.h"
#include "iprobedataset.h"
#include "linecinelooper.h"
#include "logger.h"
#include "mainwindowmiddledownwidget.h"
#include "menucontroller.h"
#include "menutool.h"
#include "messageboxframe.h"
#include "modelconfig.h"
#include "patientworkflow.h"
#include "resource.h"
#include "screenshots.h"
#include "setting.h"
#include "sonoparameters.h"
#include "stateeventnames.h"
#include "statemanager.h"
#include "toolnames.h"
#include "toolsfactory.h"
#include "util.h"
#include <boost/foreach.hpp>

LOG4QT_DECLARE_STATIC_LOGGER(log, CurvedPanoramicEnTool)

// 在以下情况设置宽景菜单不可用
QString CurvedPanoDisableToolList[] = {BFPNames::IsMLineVisibleStr,  BFPNames::IsBiopsyVisibleStr,
                                       BFPNames::TrapezoidalModeStr, BFPNames::NeedleModeStr,
                                       BFPNames::StressEchoEnStr,    BFPNames::ECGEnStr,
                                       BFPNames::SonoNerveStr,       BFPNames::ZoomOnStr,
                                       BFPNames::ZoomSelectStr,      BFPNames::IsDopplerScanLineVisibleStr,
                                       BFPNames::FreezeStr};

// 在宽景下禁止进入以下状态
QString CurvedPanoPreventState[] = {StateEventNames::Measurement, StateEventNames::Menu,
                                    StateEventNames::MenuAdd,     StateEventNames::MenuDec,
                                    StateEventNames::Zoom,        StateEventNames::Gain,
                                    StateEventNames::Change,      StateEventNames::TrapezoidalModeGeneral};

// 支持在以下状态执行退出宽景
QString CurvedPanoExitPermitStateList[] = {StateEventNames::EditExam, StateEventNames::ExitEditExam,
                                           StateEventNames::BeginLoad, StateEventNames::Freeze, StateEventNames::Exit};

CurvedPanoramicEnTool::CurvedPanoramicEnTool(const QString& name, CurvedPanoramic* curvedPanoramic,
                                             QWidget* freezeBarWidget)
    : MenuTool(name)
    , m_CurvedPanoramic(curvedPanoramic)
    , m_FreezeBarWidget(dynamic_cast<FreezeBarWidget*>(freezeBarWidget))
    , m_ExamModeIdIsChanged(false)
    , m_ProbeDataSet(NULL)
{
}

void CurvedPanoramicEnTool::setCurvedPanoramicEnable(bool value)
{
    m_CurvedPanoramic->clear();
    ToolsFactory::instance().command(ToolNames::DeleteGlyphsStr)->run();
    connectSignals(value);

    if (!m_ExamModeIdIsChanged)
    {
        setDefaultAndRecover(value);
    }

    m_SonoParameters->setPV(BFPNames::CurvedPanoramicEnableStr, value);
    m_SonoParameters->setPV(BFPNames::SupportAdjustTGCByMouseStr, !value);

    setPreventState(value);
}

void CurvedPanoramicEnTool::setPreventState(bool isDisabled)
{
    BOOST_FOREACH (const QString& str, CurvedPanoPreventState)
    {
        if (isDisabled)
        {
            StateManager::getInstance().addPreventor(str);
        }
        else
        {
            StateManager::getInstance().removePreventor(str);
        }
    }
}

void CurvedPanoramicEnTool::setProbeDataSet(IProbeDataSet* probeDataSet)
{
    m_ProbeDataSet = probeDataSet;
}

void CurvedPanoramicEnTool::setCurvedPanoramicDefaultParameter(const bool isForce)
{
    m_SonoParameters->setPV(BFPNames::BSteeringScanStr, 20, isForce);
    m_SonoParameters->setPV(BFPNames::ImageZoomCoefStr, 90, isForce);

    m_SonoParameters->setPV(BFPNames::ActiveBStr, 0, isForce);
    m_SonoParameters->setPV(BFPNames::LayoutStr, 1, isForce);
    m_SonoParameters->setPV(BFPNames::RotationStr, 0, isForce);
    m_SonoParameters->setPV(BFPNames::UpStr, true, isForce);

    int cpColorMap = m_SonoParameters->pV(BFPNames::CPColorMapIndexStr).toInt();

    m_CurvedPanoramic->setOriginalPixelSizeMM(m_BModeData.PixelSizeMM);

    m_SonoParameters->setPV(BFPNames::BColorMapIndexStr, cpColorMap, true);
}

void CurvedPanoramicEnTool::execute()
{
    bool curvedPanoramicIsRuning = StateManager::getInstance().curvedPanoramicIsRuning();
    bool curvedPanoramicIsCallback = StateManager::getInstance().curvedPanoramicIsCallBack();
    if (curvedPanoramicIsRuning || curvedPanoramicIsCallback)
    {
        if (!curvedPanoramicIsCallback) // 仅在宽景模式下才需要更新宽景状态
        {
            setCurvedPanoramicEnable(false);
        }
        //        StateManager::getInstance().postEvent(StateEventNames::CurvedPanomicExit);
    }
    else
    {
        m_ExamModeIdIsChanged = false;
        setCurvedPanoramicEnable(true);
        onLinearAreaRectChanged(m_SonoParameters->pV(BFPNames::LinearAreaRectStr));
        StateManager::getInstance().setCurvedPanoramicLayout(m_BModeData.Layout);
        StateManager::getInstance().postEvent(StateEventNames::CurvedPanoramic);
    }
    StateManager::getInstance().setCurvedPanoramicIsRuning(!curvedPanoramicIsRuning);
}

void CurvedPanoramicEnTool::onBeforeCurSonoParametersChanged()
{
    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::LinearAreaRectStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onLinearAreaRectChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onProbeIdChanged()));
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(setCurvedPanoTool()));
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSystemScanModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFreezeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::BColorMapIndexStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(setCurvedChroma(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::CurvedPanoramicEnableStr), SIGNAL(valueChanged(QVariant)),
                   this, SLOT(updateStateEventOnCPChanged(QVariant)));

        BOOST_FOREACH (const QString& str, CurvedPanoDisableToolList)
        {
            disconnect(m_SonoParameters->parameter(str), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(setCurvedPanoTool()));
        }

        updateState();
    }
}

void CurvedPanoramicEnTool::onSetSonoParameters()
{
    connect(m_SonoParameters->parameter(BFPNames::LinearAreaRectStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onLinearAreaRectChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onProbeIdChanged()));
    connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(setCurvedPanoTool()));
    connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onSystemScanModeChanged(QVariant)));

    connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreezeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::BColorMapIndexStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(setCurvedChroma(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::CurvedPanoramicEnableStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(updateStateEventOnCPChanged(QVariant)));

    BOOST_FOREACH (const QString& str, CurvedPanoDisableToolList)
    {
        connect(m_SonoParameters->parameter(str), SIGNAL(valueChanged(QVariant)), this, SLOT(setCurvedPanoTool()));
    }

    onLinearAreaRectChanged(m_SonoParameters->pV(BFPNames::LinearAreaRectStr));
    setCurvedPanoTool();

    updateState();

    // 如果当前模式为宽景模式，则需要展示虚拟冻结条，因为宽景下始终只显示1帧
    bool isCurvedPanoramic = m_SonoParameters->pV(BFPNames::SystemScanModeStr) == SystemScanModeCP;
    m_FreezeBarWidget->freezeBar()->setSupportShowFakeData(isCurvedPanoramic);
}

void CurvedPanoramicEnTool::setCurvedPanoTool()
{
    bool isEnabled = false;
    if (isSupportCurvedPanoramic())
    {
        SystemScanMode mode = (SystemScanMode)(m_SonoParameters->pIV(BFPNames::SystemScanModeStr));
        bool excludeFun = false;
        BOOST_FOREACH (const QString& str, CurvedPanoDisableToolList)
        {
            // 在彩色多普勒模式下，允许IsDopplerScanLineVisible为true
            if (mode == SystemScanModeColorDoppler && str == BFPNames::IsDopplerScanLineVisibleStr)
            {
                continue; // 跳过这个限制条件
            }
            excludeFun |= m_SonoParameters->pBV(str);
        }

        if ((mode == SystemScanModeB || mode == SystemScanModeColorDoppler) && m_SonoParameters->isRealTime() &&
            !excludeFun)
        {
            isEnabled = true;
        }
    }

    setIsAvailable(isEnabled);
}

void CurvedPanoramicEnTool::onProbeIdChanged()
{
    m_ExamModeIdIsChanged = true;
}

void CurvedPanoramicEnTool::setDefaultAndRecover(bool defaultValue)
{
    if (defaultValue)
    {
        m_BModeData.Rotation = m_SonoParameters->pIV(BFPNames::RotationStr);
        m_BModeData.Up = m_SonoParameters->pBV(BFPNames::UpStr);
        m_BModeData.SteerCode = m_SonoParameters->pIV(BFPNames::BSteeringScanStr);
        m_BModeData.ImageZoomCoef = m_SonoParameters->pIV(BFPNames::ImageZoomCoefStr);
        m_BModeData.Layout = m_SonoParameters->pIV(BFPNames::LayoutStr);
        m_BModeData.PixelSizeMM = m_SonoParameters->pDV(BFPNames::PixelSizeMMStr);

        setCurvedPanoramicDefaultParameter();
    }
    else
    {
        m_SonoParameters->setPV(BFPNames::RotationStr, m_BModeData.Rotation);
        m_SonoParameters->setPV(BFPNames::UpStr, m_BModeData.Up);
        m_SonoParameters->setPV(BFPNames::BSteeringScanStr, m_BModeData.SteerCode);
        m_SonoParameters->setPV(BFPNames::ImageZoomCoefStr, m_BModeData.ImageZoomCoef);
        m_SonoParameters->setPDV(BFPNames::PixelSizeMMStr, m_BModeData.PixelSizeMM);
    }
}

void CurvedPanoramicEnTool::connectSignals(bool value)
{
    if (value)
    {
        connect(m_CurvedPanoramic, &CurvedPanoramic::curvedInfoChanged, this, &CurvedPanoramicEnTool::setCurrentInfo);
    }
    else
    {
        disconnect(m_CurvedPanoramic, &CurvedPanoramic::curvedInfoChanged, this,
                   &CurvedPanoramicEnTool::setCurrentInfo);
    }
}

void CurvedPanoramicEnTool::updateState()
{
    if (m_SonoParameters != nullptr)
    {
        bool isCP = m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanModeCP;
        m_SonoParameters->setPV(BFPNames::CurvedPanoramicEnableStr, isCP);
        m_SonoParameters->setPV(BFPNames::SupportAdjustTGCByMouseStr, !isCP);
        MenuController::instance().setCurvedMenu(isCP && m_SonoParameters->isRealTime());
        StateManager::getInstance().setCurvedPanoramicIsRuning(isCP);
        StateManager::getInstance().setCurvedPanoramicIsCallBack(isCP && !m_SonoParameters->isRealTime());
    }
}

void CurvedPanoramicEnTool::setCurrentInfo(int value, bool roiVisible, QList<QVariant> points)
{
    if (roiVisible || value == 0)
    {
        m_SonoParameters->setPV(BFPNames::CurvedPanoramicLengthStr, value, true);
    }

    if (!points.isEmpty())
    {
        QVariant pointList = points;
        m_SonoParameters->setPV(BFPNames::CurvedPanoramicROIVisibleStr, roiVisible);
        m_SonoParameters->setPV(BFPNames::CurvedPanoramicPointListStr, pointList, true);
    }
}

void CurvedPanoramicEnTool::setCurvedChroma(QVariant value)
{
    m_SonoParameters->setPV(BFPNames::CPColorMapIndexStr, value);
}

void CurvedPanoramicEnTool::onSystemScanModeChanged(QVariant value)
{
    SystemScanMode mode = (SystemScanMode)value.toInt();

    if (mode != SystemScanModeCP)
    {
        updateState();
    }

    // 实时宽景下支持任何情况下直接切回B模式，比如进入宽景前处于多B的情况，此时强制切回B模式前需要刷新深度，避免图元位置不对
    if (mode == SystemScanModeB)
    {
        m_SonoParameters->parameter(BFPNames::CQYZStr)->update();
    }

    setCurvedPanoTool();
}

void CurvedPanoramicEnTool::updateStateEventOnCPChanged(const QVariant value)
{
    if (value.toBool())
    {
        StateManager::getInstance().addPreventor(StateEventNames::LR);
        StateManager::getInstance().addPreventor(StateEventNames::UD);
    }
    else
    {
        StateManager::getInstance().removePreventor(StateEventNames::LR);
        StateManager::getInstance().removePreventor(StateEventNames::UD);
    }
}

void CurvedPanoramicEnTool::onLinearAreaRectChanged(QVariant value)
{
    QRect rect = value.toRect();
    //调节深度,如果使用x坐标小于0的有效区域,进入宽景后会直接冻结
    //此处imagesize是否需要为固定宽度，可以作为一种配置项
    QSize imageSize = m_SonoParameters->pV(BFPNames::CurvedPanoramicImageSizeStr).toSize();
    int x = 0;
    int y = 0;
    int width = 0;
    int height = 0;
    int offset = 10;

    if (imageSize.width() >= rect.width())
    {
        //由于获得的roi框实际上会比有效图像稍微大一些,为了拼接的时候减少图像断层等问题,需要减少宽高目前设置x,y各增加10
        //宽高相应的减少2 * 10,但是在深度最深,扫描宽度最小的情况下,有效宽度会小于20需要分开考虑
        x = imageSize.width() / 2 - rect.width() / 2;
        width = rect.width();
        if (rect.width() > 2 * offset)
        {
            //自行计算是由于需要考虑翻转的情况下,如右翻转,rect.x()会很大
            x += offset;
            width -= 2 * offset;
        }
    }
    else
    {
        x = 0;
        width = imageSize.width();
    }

    if (imageSize.height() >= rect.height())
    {
        y = imageSize.height() / 2 - rect.height() / 2;
        height = rect.height();
        if (rect.height() > 2 * offset)
        {
            y += offset;
            height -= 2 * offset;
        }
    }
    else
    {
        y = 0;
        height = imageSize.height();
    }

    //    0 <= roi.x && 0 <= roi.width && roi.x + roi.width <= m.cols && 0 <= roi.y && 0 <= roi.height && roi.y +
    //    roi.height <= m.rows
    if (0 <= x && 0 < width && x + width <= imageSize.width() && 0 <= y && 0 < height &&
        y + height <= imageSize.height()) //对应roi判断
    {
        setParameter_EffectiveArea(x, y, width, height);
    }
}

void CurvedPanoramicEnTool::onFreezeChanged(QVariant value)
{
    Q_UNUSED(value)

    // 实时宽景下回调图片，解冻后仍然为实时宽景，需要重置回调状态
    if (!value.toBool())
    {
        StateManager::getInstance().setCurvedPanoramicIsCallBack(false);
    }

    if (m_SonoParameters->isRealTime())
    {
        setCurvedPanoTool();
    }
}

bool CurvedPanoramicEnTool::isLinearProbe() const
{
    if (m_SonoParameters != NULL)
    {
        int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
        return m_ProbeDataSet->getProbe(probeId).IsLinear;
    }

    return false;
}

bool CurvedPanoramicEnTool::isSupportCurvedPanoramic() const
{
    if (m_SonoParameters == NULL)
        return false;

    return Resource::supportProbeId(Resource::FunctionName::CurvedPanoramic)
        .contains(QString::number(m_SonoParameters->pIV(BFPNames::ProbeIdStr)));
}

CurvedPanoramicExitTool::CurvedPanoramicExitTool(const QString& name)
    : MenuTool(name)
{
}

void CurvedPanoramicExitTool::execute()
{
    BOOST_FOREACH (const QString& str, CurvedPanoPreventState)
    {
        StateManager::getInstance().removePreventor(str);
    }
    if (StateManager::getInstance().curvedPanoramicIsCallBack())
    {
        StateManager::getInstance().postEvent(
            StateEventNames::CurvedPanomicExitCallBack); // 仅负责非宽景模式下回调宽景的控制
    }
    else if (StateManager::getInstance().curvedPanoramicIsRuning())
    {
        StateManager::getInstance().postEvent(
            StateEventNames::CurvedPanomicExit); // 负责实时宽景和宽景模式下回调宽景的控制
    }
    else
    {
        log()->error("CurvedPanoramic State Exception!!!");
    }
}

CurvedPanoramicExitInCallBackTool::CurvedPanoramicExitInCallBackTool(const QString& name)
    : MenuTool(name)
{
}

void CurvedPanoramicExitInCallBackTool::execute()
{
    BOOST_FOREACH (const QString& str, CurvedPanoPreventState)
    {
        StateManager::getInstance().removePreventor(str);
    }

    StateManager::getInstance().postEvent(StateEventNames::CurvedPanomicExitCallBack);
}
