#ifndef QUICKANGLETOOL_H
#define QUICKANGLETOOL_H
#include "usfprogramcontroller_global.h"

#include "angleadjustbasetool.h"

static const int ANGLE_COUNT = 3;
static const int quickAngles[ANGLE_COUNT] = {-60, 0, 60};

class SonoParameters;
class USF_PROGRAM_CONTROLLER_EXPORT QuickAngleTool : public AngleAdjustBaseTool
{
    Q_OBJECT
public:
    QuickAngleTool(const QString& name, SonoParameters* sonoParameters);
protected slots:
    void onBeforeCurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onToolValueChanged(const QVariant& value);
    void quickAngleValueChanged(const QVariant& value);
    virtual void onStateChanged();

    void onQuickAngleChangeWithScanMode(const QVariant& value);

protected:
    virtual void connectCurSonoParameters();
    void execute();
    void onEnableChanged();
    int maxmValue() const;
    int minmValue() const;
    int value() const;

private:
    int m_index;
};

#endif // QUICKANGLETOOL_H
