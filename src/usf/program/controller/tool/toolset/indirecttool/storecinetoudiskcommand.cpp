/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "storecinetoudiskcommand.h"
#include "resource.h"
#include <QSettings>
#include "setting.h"
#include "gdprutility.h"
#include "util.h"

StoreCineToUDiskCommand::StoreCineToUDiskCommand(
    const QString& name, BaseWidget* slider, IStateManager* stateManager, SonoParameters* sonoParameters,
    IMainWindow* flexBtnVisible, BaseWidget* imageClipWidget, IBufferStoreManager* bufferStoreManager,
    IPatientWorkflow* patientWorkflow, IDicomTaskManager* dicomTaskManager, IStressEchoModel* stressEchoModel,
    BaseWidget* storeProgressMovieWidget, PatientEditModel* patientEditModel, ISystemStatusModel* systemStatusModel,
    QWidget* sonoZoomWidget, IZoomOnProxy* zoomOnProxy, IBeamFormer* beamFormer, CineLooper* cineLooper,
    IColorMapManager* colorMapManager, ISystemInfo* systemInfo, ImageTile* imageTile,
    IDicomUtilityCreator* dicomUtilityCreator, IDicomToolCreator* dicomToolCreator,
    ChisonUltrasoundContext* ultrasoundContext, MeasureContext* measureContext)
    : StoreCineCommand(name, slider, stateManager, sonoParameters, flexBtnVisible, imageClipWidget, bufferStoreManager,
                       patientWorkflow, dicomTaskManager, stressEchoModel, storeProgressMovieWidget, patientEditModel,
                       systemStatusModel, sonoZoomWidget, zoomOnProxy, beamFormer, cineLooper, colorMapManager,
                       systemInfo, imageTile, dicomUtilityCreator, dicomToolCreator, ultrasoundContext, measureContext)
{
    m_IsAddScreenToClipWidget = false;
}

void StoreCineToUDiskCommand::execute()
{
    metaObject()->invokeMethod(this, "doBlockExecute", Qt::QueuedConnection);
}

bool StoreCineToUDiskCommand::checkDiskSpace()
{
    return checkUDiskSpace();
}

QString StoreCineToUDiskCommand::generateFileName() const
{
    return makeUDiskFileName();
}

void StoreCineToUDiskCommand::checkTargetDir() const
{
    makeUDiskDir();
}

QString StoreCineToUDiskCommand::screenSuffix() const
{
    QSettings settings(Resource::optionsforTransmittedImagesIniName(), QSettings::IniFormat);

    settings.beginGroup(Resource::colormapGeneralDevice);
    QString screenSuffix = settings.value("ScreenSuffix").toString();
    settings.endGroup();

    return screenSuffix;
}

void StoreCineToUDiskCommand::exportFile()
{
    if (Setting::instance().defaults().isExportGDPR() &&
        (!Setting::instance().defaults().isRemindExportGDPR() ||
         (Setting::instance().defaults().isRemindExportGDPR() && m_IsGDPR)))
    {
        QString fileName = Util::getSameBaseNamesFileName(m_FileName, Setting::instance().defaults().screenExt());
        QImage image(fileName);
        GDPRUtility::processedImageGDPR(image, fileName, true);
        image.save(fileName);
    }
}
