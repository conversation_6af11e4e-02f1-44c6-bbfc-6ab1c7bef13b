#include "storecinecommand.h"
#include "applicationinfo.h"
#include "bfpnames.h"
#include "cinelooper.h"
#include "dicomgenerateuniqueidentifier.h"
#include "dicomregister.h"
#include "dicomstorage.h"
#include "basedicomtool.h"
#include "idicomtaskmanager.h"
#include "diskselectionwidget.h"
#include "diskvolumevalidator.h"
#include "filesexporter.h"
#include "gdprutility.h"
#include "ibeamformer.h"
#include "ibufferstoremanager.h"
#include "imagenummanagement.h"
#include "imageskimmanager.h"
#include "keyboardcontrol.h"
#include "lightcontroller.h"
#include "logger.h"
#include "messageboxframe.h"
#include "modeldirectorygetter.h"
#include "netinfo.h"
#include "patienteditmodel.h"
#include "ipatientworkflow.h"
#include "progresssliderwidget.h"
#include "remindgdprwidget.h"
#include "resource.h"
#include "screenshots.h"
#include "setting.h"
#include "sonoparameters.h"
#include "sonozoomwidget.h"
#include "stateeventnames.h"
#include "statemanager.h"
#include "storeprogressmoviewidget.h"
#include "istressechomodel.h"
#include "isystemstatusmodel.h"
#include "util.h"
#include "zoomonproxy.h"
#include "imagewidget.h"
#include "imagetile.h"
#include <QDateTime>
#include <QDebug>
#include <QFile>
#include "idicomtoolcreator.h"
#include "chisonultrasoundcontext.h"
#include "measurecontext.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, StoreCineCommand)

StoreCineCommand::StoreCineCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager,
                                   SonoParameters* sonoParameters, IMainWindow* flexBtnVisible,
                                   BaseWidget* imageClipWidget, IBufferStoreManager* bufferStoreManager,
                                   IPatientWorkflow* patientWorkflow, IDicomTaskManager* dicomTaskManager,
                                   IStressEchoModel* stressEchoModel, BaseWidget* storeProgressMovieWidget,
                                   PatientEditModel* patientEditModel, ISystemStatusModel* systemStatusModel,
                                   QWidget* sonoZoomWidget, IZoomOnProxy* zoomOnProxy, IBeamFormer* beamFormer,
                                   CineLooper* cineLooper, IColorMapManager* colorMapManager, ISystemInfo* systemInfo,
                                   ImageTile* imageTile, IDicomUtilityCreator* dicomUtilityCreator,
                                   IDicomToolCreator* dicomToolCreator, ChisonUltrasoundContext* ultrasoundContext,
                                   MeasureContext* measureContext)
    : BaseStoreCommand(name, slider, stateManager, sonoParameters, flexBtnVisible, imageClipWidget, bufferStoreManager,
                       ultrasoundContext, measureContext)
    , m_IsCine(true)
    , m_IsRealTimeCine(false)
    , m_IsAddScreenToClipWidget(true)
    , m_FilesExporter(NULL)
    , m_BeamFormer(beamFormer)
    , m_ModeType(ScreenTypeDef::Normal)
    , m_IsGDPR(false)
    , m_Looper(cineLooper)
    , m_IsRealtimeSaveFinishOnce(false)
    , m_PatientWorkflow(patientWorkflow)
    , m_DicomTaskManager(dicomTaskManager)
    , m_ProgressMovieWidget(dynamic_cast<StoreProgressMovieWidget*>(storeProgressMovieWidget))
    , m_PatientEditModel(patientEditModel)
    , m_SystemStatusModel(systemStatusModel)
    , m_StressEchoModel(stressEchoModel)
    , m_SonoZoomWidget(dynamic_cast<SonoZoomWidget*>(sonoZoomWidget))
    , m_ZoomOnProxy(dynamic_cast<ZoomOnProxy*>(zoomOnProxy))
    , m_SystemInfo(systemInfo)
    , m_ImageTile(imageTile)
    , m_DicomToolCreator(dicomToolCreator)
    , m_DicomUtilityCreator(dicomUtilityCreator)
    , m_RealTimeSaveIndex(-1)
{
    setDefaultLightName(LightNames::MovieStr);
    m_FilesExporter = new FilesExporter(m_PatientWorkflow, m_DicomTaskManager, this);
    m_FilesExporter->setColorMapManager(colorMapManager);
    m_FilesExporter->setDicomUtilityCreator(dicomUtilityCreator);
}

void StoreCineCommand::setIsRealTimeCine(bool value)
{
    m_IsRealTimeCine = value;
}

void StoreCineCommand::realTimeSaveFinished(bool succeed)
{
    m_IsRealtimeSaveFinishOnce = true;
    handleSavedFiles(succeed);
    handleSaveState(succeed, false);
}

const QString& StoreCineCommand::lightToolName() const
{
    return toolName();
}

bool StoreCineCommand::checkDiskSpace()
{
    return innerCheckDiskSpace(Resource::hardDiskDir);
}

void StoreCineCommand::handleScreenshots(bool succeed)
{
    if (succeed)
    {
        if (m_IsAddScreenToClipWidget)
        {
            m_ImageClipWidget->add(Screenshots::instance().lastScreenName());
        }
    }
    else
    {
        removeFailedFiles();
    }
}

bool StoreCineCommand::innerCheckDiskSpace(const QString& name) const
{
    // 当前机器存储空间太少，没有harddisk单独分区，导致判断存储空间不足，这里先把这个判断逻辑注掉，保证能成功存图
    // 李德后续会处理这个问题
    return true;
    uint32_t fileSize = PartitionInfo::DISK_SIZE_SCALE * PartitionInfo::DISK_SIZE_SCALE * 600; // 600M;
    bool isEnough = DiskVolumeValidator::isVolumeEnough(name, fileSize);
    if (!isEnough)
    {
        MessageBoxFrame::warningNonModal(QObject::tr("There is no enough space!"));
    }
    return isEnough;
}

void StoreCineCommand::handleMaxImageNum(bool succeed)
{
    if (succeed)
    {
        ImageNumManagement::wirteMaxImageNum();
    }
}

void StoreCineCommand::saveImageNum(int imageNum)
{
    m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageNumStr, imageNum);
}

void StoreCineCommand::saveSopInstanceUID()
{
    QString sopInstanceUID = DicomGenerateUniqueIdentifier::newSopInstanceID();
    m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::SopInstanceIdStr, sopInstanceUID);
}

void StoreCineCommand::dealCopyThread()
{
    if (!m_FilesExporter->threadFinishedState())
    {
        MessageBoxFrame::warning(tr("Export files failed!"));
        m_FilesExporter->resetThreadFinishedState();
    }
}

void StoreCineCommand::processGDPRRemind()
{
    m_IsGDPR = false;
    if (((m_FileName.startsWith(Resource::hardDiskDir)) &&
         (Setting::instance().defaults().isAutoExportToUDisk() ||
          Setting::instance().defaults().isAutoExportToNetWork()) &&
         Setting::instance().defaults().isRemindExportGDPR()) ||
        Setting::instance().defaults().dicomSendWhileSavingGDPRRemind() ||
        ((!m_FileName.startsWith(Resource::hardDiskDir)) && Setting::instance().defaults().isRemindExportGDPR()))
    {
        RemindGDPRDialog dlg;
        dlg.getWidget()->setRemindGDPR(QObject::tr("The data will be de-identified."),
                                       QObject::tr("De-Identification"));
        dlg.adjustSize();
        dlg.exec();
        m_IsGDPR = dlg.getWidget()->isGDPR();
    }
}

bool StoreCineCommand::isSupportSave() const
{
    return !StateManager::getInstance().curvedPanoramicIsRuning();
}

void StoreCineCommand::invokeSaveScreen(QString fileName)
{
    //先截屏但不保存,显示savingslider后再保存,否则会截到slider
    //截屏前先关闭影响保存测量信息的窗口,再隐藏鼠标,截屏后恢复鼠标原状态
    // todo 保存额外图片修改成png图
    closeSystemStatusTipShown();
    hideImageSkimExpandButton();
    emit hideWindowBeforeScreenshot();
#ifdef USE_VIRTUAL_KEYBOARD
    KeyboardControl::instance().hideKeyboard(); // 关闭键盘
#endif

    bool visible = ApplicationInfo::instance().isCursorVisible();
    if (visible)
    {
        ApplicationInfo::instance().setCursorVisible(false, false);
    }

    QImage screen = Screenshots::instance().grabScreen(m_ModeType);

    QImage thumbnailScreen;
    if (Setting::instance().defaults().screenType() == ScreenTypeDef::Image)
    {
        thumbnailScreen = grabThumbnailScreen();
    }
    if (visible)
    {
        ApplicationInfo::instance().setCursorVisible(visible, false);
    }

    if (Setting::instance().defaults().screenType() == ScreenTypeDef::Image)
    {
        saveThumbnailScreen(thumbnailScreen, Util::getSameBaseNamesFileName(fileName));
    }

    Screenshots::instance().saveScreen(screen, Util::getSameBaseNamesFileName(fileName), screenSuffix());

    //    {
    //        QString filePath = QString("%1imageB_screen/").arg(Resource::zeusContextDataSaverDir());
    //        Util::Mkdir(filePath.toStdString().c_str());
    //        screen.save(QString("%1/screen_%2_%3.jpeg")
    //                    .arg(filePath)
    //                    .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
    //                    .arg(m_RealTimeSaveIndex));
    //    }
}

void StoreCineCommand::doStoreToStressEcho(bool succeed)
{
    if (m_IsCine && succeed && m_StressEchoModel != NULL)
    {
        // stress echo 不支持回调后存储
        if (m_StressEchoModel->isRunning() && m_BufferStoreManager != NULL &&
            m_BufferStoreManager->curSonoParameters() != NULL &&
            m_BufferStoreManager->curSonoParameters()->isRealTime())
        {
            if (!m_BufferStoreManager->curSonoParameters()->pIV(BFPNames::SystemScanModeStr) == SystemScanModeB)
                return;
            m_StressEchoModel->setPath(Screenshots::instance().lastScreenName());
            m_StressEchoModel->onSelectNextCell();
        }
    }
}

void StoreCineCommand::doRealTimeSaveCine(const QString& fileName)
{
    m_BufferStoreManager->setIsRealTimeSaveCine(m_IsRealTimeCine);
    m_BufferStoreManager->asyncSave(fileName);
}

void StoreCineCommand::operate()
{
    m_IsRealtimeSaveFinishOnce = false;
    setFlexButtonVisible(false);
    controlGlyphsControl(false);
    if (m_SonoZoomWidget != NULL)
    {
        m_SonoZoomWidget->hideGlass(true);
    }
    setNavigationEllipseShow(false);
    if ((m_Looper != NULL) && (!m_IsRealTimeCine) && (m_Looper->isLooping()))
    {
        m_Looper->stopLoop();
    }

    turnOnLight();
    m_PatientEditModel->backstageCreatePatient();
    checkTargetDir();

    QString fileName = generateFileName();
    m_FileName = fileName;

    processGDPRRemind();

    connect(m_BufferStoreManager, &IBufferStoreManager::saved, this, &StoreCineCommand::onCommandFinished);

    ImageNumManagement::readMaxImageNumFromFile(
        PatientPath::instance().maxImageIdFilePath(*m_PatientWorkflow->patient()));
    int imageNum = ImageNumManagement::generateImageNum();
    saveImageNum(imageNum);
    saveSopInstanceUID();

    setImageParameters();

    m_RealTimeSaveIndex = m_MeasureContext->getFrameIndex();

    if (!m_IsRealTimeCine)
    {
        doSave(fileName);
    }
    else
    {
        doRealTimeSaveCine(fileName);
    }
}

QString StoreCineCommand::screenSuffix() const
{
    return QString();
}

void StoreCineCommand::doSave(const QString& fileName)
{
    m_BufferStoreManager->asyncSave(fileName);
}

QString StoreCineCommand::generateFileName() const
{
    QString curvedStr = QString();
    if (m_RealSonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanModeCP ||
        StateManager::getInstance().curvedPanoramicIsCallBack())
    {
        curvedStr = Resource::curvedPanomic;
    }

    return QString("%1%2%3")
        .arg(PatientPath::instance().filePath(*m_PatientWorkflow->patient()))
        .arg(curvedStr)
        .arg(fileExt());
}

QString StoreCineCommand::fileExt() const
{
    return Resource::cineExt;
}

void StoreCineCommand::beginOperate()
{
    m_ModeType = ScreenTypeDef::Normal;
}

void StoreCineCommand::doDicomStorage(const QString& str)
{
    BaseDicomTool* tool = m_DicomToolCreator->createDicomTool(DicomChison::storage, m_PatientWorkflow->patient(), NULL,
                                                              m_DicomTaskManager, m_SystemInfo, this);
    tool->setSendStatus(true);
    tool->setIsCine(m_IsCine);
    tool->setDicomUtilityCreator(m_DicomUtilityCreator);

    QStringList keys;
    keys << DicomChison::storageMode();
    QMap<QString, QString> keyValues;
    keyValues = DicomChison::getDefaultServerInfo(DicomChison::storage, keys);
    bool isDicomSendWhileSaving =
        keyValues.value(DicomChison::storageMode()) == DicomChison::allStorageModeName().at(DicomChison::Follow);
    bool isDicomSendWhileSavingGDPR = keyValues.value(DicomChison::storageGDPR()) == "1";
    if (isDicomSendWhileSaving)
    {
        if (isDicomSendWhileSavingGDPR && Setting::instance().defaults().dicomSendWhileSavingGDPRRemind())
        {
            if (m_IsGDPR)
            {
                tool->setIsGDPR(true);
                if (!GDPRUtility::isSupportGDPR(QStringList() << str))
                {
                    MessageBoxFrame::warning(QObject::tr("Some images cannot be de-identified!"));
                    return;
                }
                tool->setImagePathes(QStringList() << str);
            }
            else
            {
                tool->setIsGDPR(false);
                tool->setImagePathes(QStringList() << str);
            }
        }
        else if (isDicomSendWhileSavingGDPR && !(Setting::instance().defaults().dicomSendWhileSavingGDPRRemind()))
        {
            tool->setIsGDPR(true);
            if (!GDPRUtility::isSupportGDPR(QStringList() << str))
            {
                MessageBoxFrame::warning(QObject::tr("Some images cannot be de-identified!"));
                return;
            }
            tool->setImagePathes(QStringList() << str);
        }
        else
        {
            tool->setIsGDPR(false);
            tool->setImagePathes(QStringList() << str);
        }
    }
    else
    {
        tool->setImagePathes(QStringList() << str);
    }
    tool->run();
    delete tool;
    tool = NULL;
}

void StoreCineCommand::makeUDiskDir() const
{
    QString result = PatientPath::instance().exportStudyPath(m_PatientWorkflow->patient(), m_udiskDir);

    Util::Mkdir(result);
}

QString StoreCineCommand::makeUDiskFileName() const
{
    QString fileName =
        QString("%1%2")
            .arg(PatientPath::instance().exportStudyPath(m_PatientWorkflow->patient(), m_udiskDir) +
                 Resource::pathSeparator + PatientPath::instance().fileName(*m_PatientWorkflow->patient()))
            .arg(fileExt());

    return fileName;
}

bool StoreCineCommand::checkUDiskSpace()
{
    m_udiskDir = DiskSelectionDialog::getFileUDiskPath("");

    if (m_udiskDir.isEmpty())
    {
        return false;
    }

    return innerCheckDiskSpace(m_udiskDir);
}

void StoreCineCommand::removeFailedFiles()
{
#if 1
    QFileInfo info(m_FileName);
    QDir dir(info.dir());
    QFileInfoList fileLists = dir.entryInfoList();
    QString infoName = info.baseName();
    foreach (const QFileInfo& temp, fileLists)
    {
        if (temp.baseName().contains(info.baseName()))
        {
            if (temp.isDir())
            {
                Util::Rmdir(temp.filePath(), true, true);
            }
            else
            {
                log()->info() << QString("store cine failed, file path :%1").arg(temp.filePath());
                QFile::remove(temp.filePath());
            }
        }
    }
#else
    QFile::remove(Screenshots::instance().lastScreenName());
    QFile::remove(m_FileName);
#endif
}

void StoreCineCommand::showSavingMovie(bool inFourDWidget)
{
    if (m_ProgressMovieWidget != NULL)
    {
        m_ProgressMovieWidget->showSavingMovie(inFourDWidget);
    }
}

void StoreCineCommand::hideSavingMovie()
{
    if (m_ProgressMovieWidget != NULL && !m_ProgressMovieWidget->isHidden())
    {
        m_ProgressMovieWidget->hideProgessMovie();
    }
}

void StoreCineCommand::closeSystemStatusTipShown()
{
    m_SystemStatusModel->closeInfoWindows();
}

void StoreCineCommand::hideImageSkimExpandButton()
{
    m_ImageClipWidget->hideImageSkimExpandButton();
}

QImage StoreCineCommand::grabThumbnailScreen()
{
    return QImage();
}

void StoreCineCommand::saveThumbnailScreen(const QImage& image, const QString& fileBaseName)
{
}

void StoreCineCommand::realTimeStoreAutoExitFreeze()
{
    if (Setting::instance().defaults().unfreezeAfterRealTimeSaving())
    {
        if (m_IsRealTimeCine && Setting::instance().defaults().pauseWhenStoreCine())
        {
            StateManager::getInstance().postEvent(StateEventNames::Freeze);
        }
    }
    else
    {
        if (m_IsRealTimeCine && !Setting::instance().defaults().pauseWhenStoreCine())
        {
            StateManager::getInstance().postEvent(StateEventNames::Freeze);
        }
    }
    m_IsRealTimeCine = false;
}

void StoreCineCommand::exportFile(bool isGDPR)
{
    m_FilesExporter->doExport(QStringList() << m_FileName, isGDPR);
}

void StoreCineCommand::setImageParameters()
{
    if (m_IsCine)
    {
        m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageLeftTopPointXStr, 0);
        m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageLeftTopPointYStr, 0);
        m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageZoomRatioStr, 1);
    }
    else
    {
        if (!Screenshots::instance().isZoomIn())
        {
            QPoint imageOffset = Screenshots::instance().getImageLeftTopPoint(m_ModeType);
            m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageLeftTopPointXStr, imageOffset.x());
            m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageLeftTopPointYStr, imageOffset.y());
            m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageZoomRatioStr, 1);
        }
        else
        {
            QPoint imageOffset = m_ZoomOnProxy->getImageWidgetLeftTopPoint();
            qreal zoomFactor = m_ZoomOnProxy->zoomFactor();
            m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageLeftTopPointXStr, imageOffset.x());
            m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageLeftTopPointYStr, imageOffset.y());
            m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ImageZoomRatioStr, zoomFactor);
        }
    }
}

void StoreCineCommand::handleSavedFiles(bool succeed)
{
    if (succeed)
    {
        exportFile(m_IsGDPR);
        doDicomStorage(m_FileName);
    }

    handleScreenshots(succeed);
    handleMaxImageNum(succeed);

    // set the cine path to the stressEcho with if's
    doStoreToStressEcho(succeed);

    turnOffLight();
    setNavigationEllipseShow(true);

    dealCopyThread();
}

void StoreCineCommand::handleSaveState(bool succeed, bool showWarn)
{
    if (succeed)
    {
        return;
    }

    removeFailedFiles();

    if (showWarn)
    {
        MessageBoxFrame::showMessage(
            NULL, tr("Warning"),
            tr("Some exceptions occurred and the image/cine file can not be saved.Please try again."));
    }
}

void StoreCineCommand::onCommandFinished(bool succeed)
{
    disconnect(m_BufferStoreManager, &IBufferStoreManager::saved, this, &StoreCineCommand::onCommandFinished);

    BaseStoreCommand::onCommandFinished(succeed);

    invokeSaveScreen(m_FileName);

    if (!m_IsRealTimeCine && !m_IsRealtimeSaveFinishOnce)
    {
        handleSavedFiles(succeed);
    }

    realTimeStoreAutoExitFreeze();

    m_BufferStoreManager->setIsRealTimeSaveCine(false);

    static int retryCount = RealTimeSaveRetryCount;
    bool needTry = ((!succeed) && (!m_IsRealTimeCine) && (0 != retryCount));
    //贾主管要求存图/存电影失败不再需要弹窗
    handleSaveState(succeed, /*!needTry*/ false);

    if (needTry)
    {
        log()->info() << QString("store cine command, retry count :%1").arg(retryCount);
        retryCount--;
        StateManager::getInstance().postEvent(m_IsCine ? StateEventNames::Movie : StateEventNames::Storage);
    }
    else
    {
        retryCount = RealTimeSaveRetryCount;
    }

    setFlexButtonVisible(true);
    controlGlyphsControl(true);
    emit showWindowAforeScreenshot();
    if (m_SonoZoomWidget != NULL)
    {
        m_SonoZoomWidget->showGlass(true);
    }
}

void StoreCineCommand::checkTargetDir() const
{
}
