#ifndef AUTOBLINETOOL_H
#define AUTOBLINETOOL_H

#include <QObject>
#include "conditionaltool.h"

class ChisonUltrasoundContext;
class SonoParameters;
class IMeasurement;
class IRuler;
class ImageEventArgs;
class MeasureContext;
class IBufferStoreManager;
class ILine<PERSON>ufferManager;
class CineLooper;
class ImageTile;
class ToolsOrderManager;
class LungAutoLineBRuler;
class IScreenMeasureController;

class USF_PROGRAM_CONTROLLER_EXPORT AutoBLineTool : public ConditionalTool
{
    Q_OBJECT
public:
    explicit AutoBLineTool(const QString& name, ChisonUltrasoundContext* chisonContext, SonoParameters* sonoParameters,
                           IMeasurement*, MeasureContext* measureContext, IBufferStoreManager* bufferStoreManager,
                           ILineBufferManager* lineBufferManager, CineLooper*, ImageTile* tile, ToolsOrderManager*,
                           IScreenMeasureController*);

    ~AutoBLineTool();

public:
    void startRTMeasure(const QString& filePath);

protected:
    void execute();
    void loadMeasurement();
    void saveData(const QString& filename);

public slots:
    void onSwitchRTImage(bool isConnect);
    void processNewImage(ImageEventArgs*);
    void onConditionalChange(bool enable);
    void onFreezeChanged(QVariant) override;
    void doSavedCine(const QString& filename);
    void doRealTimeSaveCine(int);
    void onCurSonoParametersChanged();
    void saveImage();

private:
    ChisonUltrasoundContext* m_ChisonUltrasoundContext;
    IRuler* m_Ruler;
    IMeasurement* m_PackagesMeasurement;
    IMeasurement* m_Measure;
    MeasureContext* m_MeasureContext;
    SonoParameters* m_RealSonoParameters;
    QString m_FileName;
    IScreenMeasureController* m_ScreenMeasureController;
    bool m_IsFreeze;
    ImageTile* m_imageTile;
};

#endif // AUTOBLINETOOL_H
