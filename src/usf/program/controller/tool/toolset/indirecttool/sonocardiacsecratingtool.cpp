#include "sonocardiacsecratingtool.h"
#include "chisonultrasoundcontext.h"
#include "sonocardiacglyphscontrol.h"
#include "sonocardiacsecrecognizetool.h"
#include "toolnames.h"
#include "util.h"

SonoCardiacSecRatingTool::SonoCardiacSecRatingTool(const QString& name,
                                                   ChisonUltrasoundContext* chisonUltrasoundContext)
    : MenuT<PERSON>(name)
    , m_ChisonUltrasoundContext(chisonUltrasoundContext)
{
    m_Tool =
        dynamic_cast<SonoCardiacSecRecognizeTool*>(ToolsFactory::instance().tool(ToolNames::SonoCardiacSecRatingStr));
}

void SonoCardiacSecRatingTool::controlRating(bool isOpen)
{
    if (m_SonoParameters == NULL)
        return;
    isOpen ? m_Control->addRatingItemToOverLay() : m_Control->removeRatingItemInOverLay();
    m_Control->setRatingSwitch(isOpen ? 1 : 0);
    Util::connectSignal(m_ChisonUltrasoundContext, SIGNAL(newBImage(ImageEventArgs*)), m_Tool,
                        SLOT(onNewImage(ImageEventArgs*)),
                        isOpen || m_SonoParameters->pBV(ToolNames::SonoCardiacSecRecognizeStr),
                        Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
}

void SonoCardiacSecRatingTool::execute()
{
    if (m_Tool == NULL)
        m_Tool = dynamic_cast<SonoCardiacSecRecognizeTool*>(
            ToolsFactory::instance().tool(ToolNames::SonoCardiacSecRecognizeStr));

    MenuTool::execute();
    bool isOpen = m_SonoParameters->pBV(toolName());
    controlRating(isOpen);
}
