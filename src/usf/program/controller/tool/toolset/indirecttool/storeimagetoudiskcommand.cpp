/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "storeimagetoudiskcommand.h"
#include <QSettings>
#include "resource.h"
#include "setting.h"
#include "util.h"
#include "gdprutility.h"
#include <QFileInfo>
#include <QTextCodec>
#include "screenshots.h"

StoreImageToUDiskCommand::StoreImageToUDiskCommand(
    const QString& name, BaseWidget* slider, IStateManager* stateManager, SonoParameters* sonoParameters,
    IMainWindow* flexBtnVisible, BaseWidget* imageClipWidget, IBufferStoreManager* bufferStoreManager,
    IPatientWorkflow* patientWorkflow, IDicomTaskManager* dicomTaskManager, IStressEchoModel* stressEchoModel,
    BaseWidget* storeProgressMovieWidget, PatientEditModel* patientEditModel, ISystemStatusModel* systemStatusModel,
    QWidget* sonoZoomWidget, IZoomOnProxy* zoomOnProxy, IBeamFormer* beamFormer, CineLooper* cineLooper,
    IColorMapManager* colorMapManager, ISystemInfo* systemInfo, ImageTile* imageTile,
    IDicomUtilityCreator* dicomUtilityCreator, IDicomToolCreator* dicomToolCreator,
    ChisonUltrasoundContext* ultrasoundContext, MeasureContext* measureContext)
    : StoreImageCommand(name, slider, stateManager, sonoParameters, flexBtnVisible, imageClipWidget, bufferStoreManager,
                        patientWorkflow, dicomTaskManager, stressEchoModel, storeProgressMovieWidget, patientEditModel,
                        systemStatusModel, sonoZoomWidget, zoomOnProxy, beamFormer, cineLooper, colorMapManager,
                        systemInfo, imageTile, dicomUtilityCreator, dicomToolCreator, ultrasoundContext, measureContext)
{
    m_IsAddScreenToClipWidget = false;
}

bool StoreImageToUDiskCommand::checkDiskSpace()
{
    return checkUDiskSpace();
}

void StoreImageToUDiskCommand::execute()
{
    metaObject()->invokeMethod(this, "doBlockExecute", Qt::QueuedConnection);
}

QString StoreImageToUDiskCommand::generateFileName() const
{
    return makeUDiskFileName();
}

void StoreImageToUDiskCommand::checkTargetDir() const
{
    makeUDiskDir();
}

QString StoreImageToUDiskCommand::screenSuffix() const
{
    QSettings settings(Resource::optionsforTransmittedImagesIniName(), QSettings::IniFormat);

    settings.beginGroup(Resource::colormapGeneralDevice);
    QString screenSuffix = settings.value("ScreenSuffix").toString();
    settings.endGroup();

    return screenSuffix;
}

void StoreImageToUDiskCommand::exportFile()
{
    if (Setting::instance().defaults().isExportGDPR() &&
        (!Setting::instance().defaults().isRemindExportGDPR() ||
         (Setting::instance().defaults().isRemindExportGDPR() && m_IsGDPR)))
    {
        QString fileName = Util::getSameBaseNamesFileName(m_FileName, Setting::instance().defaults().screenExt());
        QImage image(fileName);
        GDPRUtility::processedImageGDPR(image, fileName, true);
        image.save(fileName);
        if (Setting::instance().defaults().screenType() == ScreenTypeDef::Image)
        {
            QString iniFileName = Util::getSameBaseNamesFileName(fileName, "_GDPR.ini");
            if (QFile::exists(iniFileName))
            {
                QSettings settings(iniFileName, QSettings::IniFormat);
                settings.beginGroup("ImageType");
                if ((ScreenTypeDef::ScreenType)settings.value("type").toInt() == ScreenTypeDef::Image)
                {
                    settings.endGroup();
                    settings.beginGroup("GDPROB");

                    QString obRectInfo = settings.value(QString::number(0)).toString();
                    QStringList rectInfoList = obRectInfo.split(",");
                    settings.endGroup();

                    if (rectInfoList.count() != 4)
                    {
                        return;
                    }
                    QRect OBRect = QRect(rectInfoList[0].toInt(), rectInfoList[1].toInt(), rectInfoList[2].toInt(),
                                         rectInfoList[3].toInt());

                    QString calcFileName = Util::getSameBaseNamesFileName(fileName, ".calc");
                    QImage image(calcFileName);
                    if (!image.isNull())
                    {
                        GDPRUtility::processedImageGDPRByOB(image, OBRect);
                        Screenshots::instance().saveScreenEx(image, calcFileName, "PNG");
                    }
                }
            }
        }
    }
}
