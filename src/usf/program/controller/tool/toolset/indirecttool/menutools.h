#ifndef MENUTOOLS_H
#define MENUTOOLS_H
#include "usfprogramcontroller_global.h"

#include "itool.h"
#include "menutool.h"
#include "popupwidget.h"
#include "sonohelpbaseitemwidget.h"
#include <QByteArray>
#include <QHash>
#include <QList>
#include <QString>
#include <QVector>

class IStateManager;
class IProbeDataSet;
class CursorMouseActionsModel;
class LeftMenuWidget;
class CineLooper;
class MeasureContext;
class BaseImageRuler;
class ILineBufferManager;
class IRulerFactory;

class USF_PROGRAM_CONTROLLER_EXPORT TgcTool : public MenuTool
{
    Q_OBJECT
public:
    TgcTool();
    TgcTool(const QString& name);
    void retranslateUi(QWidget* widget = 0);
    virtual void setArgs(ICommandArgs* args);
    void setTgcData(unsigned char* tgcData, int tgcLen);

protected:
    virtual void execute();
private slots:
    void onCurSonoParametersChanged();
    void onFreezeChanged(const QVariant& value);

private:
    uchar limitTGC(const uchar value, const int minValue, const int maxValue);

private:
    QList<unsigned char> m_TgcDatas;
    QByteArray m_TgcData;
    QByteArray m_TgcDiff;
};

class USF_PROGRAM_CONTROLLER_EXPORT LRTool : public MenuTool
{
    Q_OBJECT
public:
    LRTool();
    LRTool(const QString& name);
    int value() const;
    void retranslateUi(QWidget* widget = 0);
protected slots:
    virtual void onToolValueChanged(const QVariant& value);

protected:
    void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT UDTool : public MenuTool
{
    Q_OBJECT
public:
    UDTool();
    UDTool(const QString& name);
    int value() const;
    void retranslateUi(QWidget* widget = 0);
protected slots:
    virtual void onToolValueChanged(const QVariant& value);

protected:
    void execute();
    virtual void onSetSonoParameters();
};

class USF_PROGRAM_CONTROLLER_EXPORT RotationTool : public MenuTool
{
    Q_OBJECT
public:
    RotationTool(const QString& name);
    void onSetSonoParameters();

protected:
    void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT SpectralInvertTool : public MenuTool
{
    Q_OBJECT
public:
    SpectralInvertTool(const QString& name);
    int value() const;
protected slots:
    virtual void onToolValueChanged(const QVariant& value);

protected:
    void execute();
    virtual void onSetSonoParameters();
};

class USF_PROGRAM_CONTROLLER_EXPORT ColorInvertStateTool : public MenuTool
{
    Q_OBJECT
public:
    ColorInvertStateTool(const QString& name);
    int value() const;
protected slots:
    virtual void onToolValueChanged(const QVariant& value);

protected:
    void execute();
    virtual void onSetSonoParameters();
};

class USF_PROGRAM_CONTROLLER_EXPORT DPDInvertStateTool : public MenuTool
{
    Q_OBJECT
public:
    DPDInvertStateTool(const QString& name);
    int value() const;
protected slots:
    virtual void onToolValueChanged(const QVariant& value);

protected:
    void execute();
    virtual void onSetSonoParameters();
};

class USF_PROGRAM_CONTROLLER_EXPORT TDIInvertStateTool : public MenuTool
{
    Q_OBJECT
public:
    TDIInvertStateTool(const QString& name);
    int value() const;
protected slots:
    virtual void onToolValueChanged(const QVariant& value);

protected:
    void execute();
    virtual void onSetSonoParameters();
};

class USF_PROGRAM_CONTROLLER_EXPORT MVIInvertStateTool : public MenuTool
{
    Q_OBJECT
public:
    MVIInvertStateTool(const QString& name);
    int value() const;
protected slots:
    virtual void onToolValueChanged(const QVariant& value);

protected:
    void execute();
    virtual void onSetSonoParameters();
};

/*
 * 深度调节
 */
class USF_PROGRAM_CONTROLLER_EXPORT CQYZTool : public MenuTool
{
public:
    CQYZTool();
    CQYZTool(const QString& name);
    void retranslateUi(QWidget* widget = 0);

protected:
    void execute();
};

/*
 * 深度档位调节
 */
class USF_PROGRAM_CONTROLLER_EXPORT CQYZLevelTool : public CQYZTool
{
public:
    CQYZLevelTool();
    CQYZLevelTool(const QString& name);
    void retranslateUi(QWidget* widget = 0);
};

/*
 * B/M模式下焦点位置调节
 */
class USF_PROGRAM_CONTROLLER_EXPORT FocusTool : public MenuTool
{
    Q_OBJECT
public:
    FocusTool();
    FocusTool(const QString& name);
    void retranslateUi(QWidget* widget = 0);
    void onInitialize();
private slots:
    void onFoucsEnabled();
    void onFsValueChanged();
    void onFreezeChanged(const QVariant& value);
signals:
    void initFocusParaValue() const;

protected:
    void execute();
    int maxmValue() const;
    int minmValue() const;
    int stepValue() const;
    int value() const;
    void onFocusValueChanged();

private:
    void setFocus(QString bfName);

protected:
    QString m_BfName;
};

/*
 * 冻结tool
 */
class USF_PROGRAM_CONTROLLER_EXPORT FreezeTool : public MenuTool
{
public:
    FreezeTool();
    FreezeTool(const QString& name);
    void retranslateUi(QWidget* widget = 0);

protected:
    void execute();
};

/*
 * 切换菜单的tool,包括实时状态的菜单切换
 * 注释下不同检查模式注释菜单的切换
 * 测量下不同检查模式测量项的切换
 */
class USF_PROGRAM_CONTROLLER_EXPORT ChangeTool : public MenuTool
{
    Q_OBJECT
public:
    ChangeTool();
    ChangeTool(const QString& name);
    void retranslateUi(QWidget* widget = 0);
    void onInitialize();
    virtual void setCurrentMenus();
    // IChangeTool interface
    virtual bool isFreezed();
    virtual QStringList getCurrentMenus();
public slots:
    void onParameterValueChanged(const QVariant& value, bool isChanged);
    void setModeChangeLabels(const QString& curModeName);
protected slots:
    void onBeforeCurSonoParametersChanged();
    void onCurSonoParametersChanged();

protected:
    void execute();

private:
    void addIndex();
    // edit xule
    void decIndex();
    QString getNextMenu(bool arg);
    QString nameTranslate(const QString& name);

protected:
    QList<QString> m_MenuNames;
    QString m_CurrentMenu;
    int m_CurrentIndex;
};

struct PostMenu
{
public:
    PostMenu()
        : m_Tool(NULL)
        , m_IsAvailable(false)
    {
    }
    PostMenu(ITool* tool, bool IsAvailable)
        : m_Tool(tool)
        , m_IsAvailable(IsAvailable)
    {
    }

public:
    ITool* m_Tool;
    bool m_IsAvailable;
};

#include <QStringList>

class USF_PROGRAM_CONTROLLER_EXPORT PostChangeTool : public ChangeTool
{
    Q_OBJECT
public:
    PostChangeTool();
    PostChangeTool(const QString& name);
    virtual void setCurrentMenus();
    virtual void onInitialize();

protected:
    void execute();
private slots:
    void onBeforeCurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onFreezeChanging(const QVariant& value);
    void onBeforeProbeIdChanged(const QVariant& oldValue, QVariant& newValue);

private:
    void controlMenuToolAvailable(const QList<ITool*>* tools);
    void onMenuChanged();
    bool isToolRealTimeAvailable(const ITool* tool);

    void SetControlMenuToolAvailable(QString menuName);
    void restoreChangedMenusToolAvailable();

private:
    QVector<QString> m_ChangedMenus;
    QStringList m_ToolFilter; // 不需要处理的menu选项
};

class USF_PROGRAM_CONTROLLER_EXPORT VariableMenuTool : public MenuTool
{
    Q_OBJECT
public:
    VariableMenuTool();
    VariableMenuTool(const QString& realTimeName, const QString& freezeName);

protected:
    virtual void onSetSonoParameters();
protected slots:
    void onBeforeCurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onFreezeChanging(const QVariant& value);

protected:
    QString m_RealTimeTool;
    QString m_FreezeTool;
};

class USF_PROGRAM_CONTROLLER_EXPORT VolumeControlTool : public MenuTool
{
    Q_OBJECT
public:
    VolumeControlTool();
    VolumeControlTool(const QString& name);
    void retranslateUi(QWidget* widget = 0);
    void onInitialize();
private slots:
    void onSystemScanModeChanged();
    void onVolumeStateChanged();
    void onBeforeCurSonoParametersChanged();
    void onCurSonoParametersChanged();

protected:
    void execute();

private:
    bool m_toolEnabled;
    QString m_curVolumeStr;
};

class USF_PROGRAM_CONTROLLER_EXPORT FreeMAngleTool : public MenuTool
{
    Q_OBJECT
public:
    FreeMAngleTool();
    FreeMAngleTool(const QString& name, int step = -1);
    FreeMAngleTool(const QString& toolName, const QString& parameter, int step = -1);
    virtual void onInitialize();

protected:
    void execute();
protected slots:
    virtual void onBeforeCurSonoParametersChanged();
    virtual void onCurSonoParametersChanged();
    virtual void onToolValueChanged(const QVariant& value);
    void onLineIndexChanged(const QVariant& value);

protected:
    QString m_Parameter;
    int m_Step;
};

class USF_PROGRAM_CONTROLLER_EXPORT FourDZoomTool : public MenuTool
{
    Q_OBJECT
public:
    FourDZoomTool();
    FourDZoomTool(const QString& name);
signals:
    void fourdZoomIn(bool);

protected:
    void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT FreeHand3DZoomTool : public MenuTool
{
    Q_OBJECT
public:
    FreeHand3DZoomTool();
    FreeHand3DZoomTool(const QString& name);
signals:
    void freeHand3DZoomIn(bool);

protected:
    void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT FreeHand3DResetTool : public MenuTool
{
    Q_OBJECT
public:
    FreeHand3DResetTool();
    FreeHand3DResetTool(const QString& name);
signals:
    void freeHand3Dreset();

protected:
    void execute();
};

class LGCRegulatorDialog;
class BaseDialog;
class USF_PROGRAM_CONTROLLER_EXPORT LGCTool : public MenuTool
{
    Q_OBJECT
public:
    LGCTool(const QString& name, BaseDialog* dialog);
    void retranslateUi(QWidget* widget = 0);
protected slots:
    void onSetSonoParameters();
    void onCurSonoParametersChanged();
    void onEnableChanged();

protected:
    virtual void execute();

private:
    LGCRegulatorDialog* m_Regulator;
};

class USF_PROGRAM_CONTROLLER_EXPORT CenterLineTool : public MenuTool
{
    Q_OBJECT
public:
    CenterLineTool(const QString& name);

protected:
    virtual void execute();

private:
};

class USF_PROGRAM_CONTROLLER_EXPORT TDIBMenuShowTool : public MenuTool
{
    Q_OBJECT
public:
    TDIBMenuShowTool(const QString& name);
    virtual void onInitialize();

protected:
    virtual void execute();
protected slots:
    void onBeforeCurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onProbeChanged(const QVariant& value);
    void onFunctionStatusChanged();
    void onIsSupportTDIChanged(const QVariant& value, bool changed);
    void onPresetChanged();

private:
    void updateTDI();

private:
    bool m_CanRefeshLeftMenu;
};

class USF_PROGRAM_CONTROLLER_EXPORT BaseLineTool : public MenuTool
{
    Q_OBJECT
public:
    BaseLineTool(const QString& name);

protected:
    virtual void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT BRulerTool : public MenuTool
{
    Q_OBJECT
public:
    BRulerTool(const QString& name);

protected:
    virtual void execute();

private:
};

class PanZoomThumbnail;
class QGraphicsView;
class ToolsOrderManager;
class USF_PROGRAM_CONTROLLER_EXPORT ImageZoomCoefTool : public MenuTool
{
    Q_OBJECT
public:
    ImageZoomCoefTool(QGraphicsView* panZoomThumbnail, ToolsOrderManager* toolsOrderManager, const QString& name);

protected:
    virtual void execute();
    virtual void onSetSonoParameters();
    virtual void onMousePress();
    virtual void onMouseRelease();
    void dealImageZoomMouseTool();
protected slots:
    virtual void onToolValueChanged(const QVariant& value);

private:
    PanZoomThumbnail* m_PanZoomThumbnail;
    bool m_EnableDealZoomMouseTool;
    int m_LastValue;
    ToolsOrderManager* m_ToolsOrderManager;
};

class USF_PROGRAM_CONTROLLER_EXPORT ECGEnTool : public MenuTool
{
    Q_OBJECT
public:
    ECGEnTool(const QString& name, ILineBufferManager* lineBufferManager);
    void onSetSonoParameters();

protected:
    virtual void execute();
    virtual void onInitialize();
    bool isSupportEcg();
protected slots:
    void onRotationChanged(QVariant value);
    void onPhaseProbeIdChanged(QVariant value);
    void onFunctionChanged(int key, bool enable);
    void onSystemScanModeChanged(QVariant value);
    void onFreezeChanged(QVariant value);

private:
    bool m_IsLicenseOpen{false};
    ILineBufferManager* m_LineBufferManager;
};

class USF_PROGRAM_CONTROLLER_EXPORT SonoNeedleTool : public MenuTool
{
    Q_OBJECT
public:
    SonoNeedleTool(const QString& name);
    void setProbeDataSet(IProbeDataSet* probeDataSet);
protected slots:
    void updateToolState();
    virtual void onBeforeCurSonoParametersChanged();
    virtual void onCurSonoParametersChanged();
    void onProbeChanged(const QVariant& value);

protected:
    virtual void execute();
    virtual void onSetSonoParameters();

private:
    IProbeDataSet* m_ProbeDataSet;
};

class USF_PROGRAM_CONTROLLER_EXPORT SuperNeedleTool : public MenuTool
{
    Q_OBJECT
public:
    SuperNeedleTool(const QString& name);
    void setProbeDataSet(IProbeDataSet* probeDataSet);
protected slots:
    void updateToolState();
    virtual void onBeforeCurSonoParametersChanged();
    virtual void onCurSonoParametersChanged();
    void onProbeChanged(const QVariant& value);

protected:
    virtual void execute();
    virtual void onSetSonoParameters();

private:
    IProbeDataSet* m_ProbeDataSet;
};

class USF_PROGRAM_CONTROLLER_EXPORT VirtualVertexTrapezoidalModeTool : public MenuTool
{
    Q_OBJECT
public:
    VirtualVertexTrapezoidalModeTool(const QString& name);
    void setProbeDataSet(IProbeDataSet* probeDataSet);
protected slots:
    void updateToolState();
    virtual void onBeforeCurSonoParametersChanged();
    virtual void onCurSonoParametersChanged();
    void onProbeChanged(const QVariant& value);

protected:
    virtual void execute();
    virtual void onSetSonoParameters();

private:
    IProbeDataSet* m_ProbeDataSet;
};

class USF_PROGRAM_CONTROLLER_EXPORT ExitFromSonoNeedle : public MenuTool
{
public:
    ExitFromSonoNeedle(QString name);

protected:
    virtual void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT MVITool : public MenuTool
{
    Q_OBJECT
public:
    MVITool(const QString& name);
    virtual void onInitialize();

protected:
    virtual void execute();
protected slots:
    void onProbeChanged(const QVariant& value);
    void onSystemScanModeChanged(const QVariant& value);
    void onFreqSpectrumChanged(const QVariant& value);
    void onLayoutChanged(const QVariant& value);
    void onBeforeCurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onFunctionStatusChanged();

private:
    //    bool isAvailable() const;
    void updateMVI();
};

class USF_PROGRAM_CONTROLLER_EXPORT BaselineMVITool : public MenuTool
{
    Q_OBJECT
public:
    BaselineMVITool(const QString& name);
    virtual void onInitialize();

protected slots:
    void onMVITypeChanged(const QVariant& value);
};

class USF_PROGRAM_CONTROLLER_EXPORT BiopsyMenuTool : public MenuTool
{
    Q_OBJECT
public:
    BiopsyMenuTool(const QString& name);
    void updateBiopsy();

protected slots:
    void onFunctionStatusChanged();
    void onProbeIdChanged(const QVariant& value);

protected:
    virtual void execute();
    virtual void onInitialize();

private:
    QString m_PreviousMenuCategory; // 记录进入穿刺菜单之前的菜单类型
};

class PresetParameters;
class USF_PROGRAM_CONTROLLER_EXPORT BiopsyChooseTool : public MenuTool
{
    Q_OBJECT
public:
    BiopsyChooseTool(const QString& name);
    void setStateManager(IStateManager* value);

protected:
    virtual void execute();
    virtual void setToolValue(const QString& toolValue, bool force = false);
    virtual void onInitialize();
protected slots:
    void onProbeIdChanged(const QVariant& value);
    void onBiopsyVerifyChanged(const QVariant& value);
    void onPresetChanged(const PresetParameters& preset);

private:
    QString m_CurGroup;
    IStateManager* m_StateManager;
};

class USF_PROGRAM_CONTROLLER_EXPORT BiopsyVerifyTool : public MenuTool
{
    Q_OBJECT
public:
    BiopsyVerifyTool(const QString& name);
    void setStateManager(IStateManager* value);

protected:
    virtual void execute();
    virtual void onInitialize();
protected slots:
    void onBiopsyVisibleChanged(QVariant value);
    void onBeforeFreezeValueChanged(const QVariant& oldValue, QVariant& newValue);
    void onCurSonoParametersChanged();

private:
    IStateManager* m_StateManager;
};

class USF_PROGRAM_CONTROLLER_EXPORT BiopsyAngleTool : public MenuTool
{
    Q_OBJECT
public:
    BiopsyAngleTool(const QString& name);
protected slots:
    void onBiopsyVerifyChanged(const QVariant& value);

protected:
    virtual void execute();
    virtual void onInitialize();
};

class USF_PROGRAM_CONTROLLER_EXPORT BiopsyXPosMMTool : public MenuTool
{
    Q_OBJECT
public:
    BiopsyXPosMMTool(const QString& name);
protected slots:
    void onBiopsyVerifyChanged(const QVariant& value);

protected:
    virtual void execute();
    virtual void onInitialize();
};

class USF_PROGRAM_CONTROLLER_EXPORT BiopsySaveTool : public MenuTool
{
    Q_OBJECT
public:
    BiopsySaveTool(const QString& name);
    void setStateManager(IStateManager* value);
protected slots:
    void onBiopsyVerifyChanged(const QVariant& value);

protected:
    virtual void execute();
    virtual void onInitialize();

private:
    IStateManager* m_StateManager;
};

class USF_PROGRAM_CONTROLLER_EXPORT BiopsyRestoreTool : public MenuTool
{
    Q_OBJECT
public:
    BiopsyRestoreTool(const QString& name);
    void setStateManager(IStateManager* value);
protected slots:
    void onBiopsyVerifyChanged(const QVariant& value);

protected:
    virtual void execute();
    virtual void onInitialize();

private:
    IStateManager* m_StateManager;
};

class USF_PROGRAM_CONTROLLER_EXPORT BiopsyExitTool : public MenuTool
{
    Q_OBJECT
public:
    BiopsyExitTool(const QString& name);
protected slots:
    void onBiopsyVerifyChanged(const QVariant& value);

protected:
    virtual void execute();
    virtual void onInitialize();
};

class USF_PROGRAM_CONTROLLER_EXPORT BiopsyCancelTool : public MenuTool
{
    Q_OBJECT
public:
    BiopsyCancelTool(const QString& name);

protected:
    virtual void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT SonoNerveQuitTool : public MenuTool
{
    Q_OBJECT
public:
    SonoNerveQuitTool(const QString& name);

protected:
    virtual void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT SonoNerveQuitFromBModeTool : public MenuTool
{
    Q_OBJECT
public:
    SonoNerveQuitFromBModeTool(const QString& name);
    virtual void onInitialize();

protected:
    virtual void execute();
};

class SonoNerveBPTool : public MenuTool
{
    Q_OBJECT
public:
    explicit SonoNerveBPTool(const QString& name, int part);

private slots:
    void onMenuChanged();

private:
    int m_Part;
};

class USF_PROGRAM_CONTROLLER_EXPORT SonoNerveScalenusBPTool : public SonoNerveBPTool
{
    Q_OBJECT
public:
    SonoNerveScalenusBPTool(const QString& name);

protected:
    virtual void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT SonoNerveSupraclavicularBPTool : public SonoNerveBPTool
{
    Q_OBJECT
public:
    SonoNerveSupraclavicularBPTool(const QString& name);

protected:
    virtual void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT SonoNerveMedianBPTool : public SonoNerveBPTool
{
    Q_OBJECT
public:
    SonoNerveMedianBPTool(const QString& name);

protected:
    virtual void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT SonoMSKQuitTool : public MenuTool
{
    Q_OBJECT
public:
    SonoMSKQuitTool(const QString& name);

protected:
    virtual void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT SonoMSKQuitFromBModeTool : public MenuTool
{
    Q_OBJECT
public:
    SonoMSKQuitFromBModeTool(const QString& name);
    virtual void onInitialize();

protected:
    virtual void execute();
};

class USF_PROGRAM_CONTROLLER_EXPORT SonoMSKBicepsLHTendonTool : public MenuTool
{
    Q_OBJECT
public:
    enum
    {
        BBSANUM = 0,
        BBLANUM = 1,
        SSPLANUM = 2,
        ISPLANUM = 3,
        SSCLANUM = 4,
        SSCSANUM = 5,
        TMLANUM = 6
    };
    SonoMSKBicepsLHTendonTool(const QString& name, CursorMouseActionsModel* cursorMouseActionsModel, QWidget*,
                              PopUpWidget*, IStateManager* value);
    void addElementToLeftMenu(int part);
    QString getFilePath(bool& isCurPart, int curPart);

protected:
    virtual void execute();

private slots:
    void onZoomIn(const QFileInfo& info, const SonoHelpBaseItemWidget::CType type);
    void hidePopUpWidget();
    void languageChanged();
    void menuChange(QString menu);

private:
    void loadSetting();
    void clear();
    void prepareForZoomIn(const QFileInfo& info, const SonoHelpBaseItemWidget::CType type);
    void createImageContent(const QFileInfo& info);
    void createTextContent(const QFileInfo& info);
    void setActivePart();
    void ActiveInfo(QString toolName, bool isActive);

private:
    QList<SonoHelpBaseItemWidget*> m_ItemWidgetList;
    CursorMouseActionsModel* m_CursorMouseActionsModel;
    LeftMenuWidget* m_leftMenuWidget;
    PopUpWidget* m_PopUpWidget;
    IStateManager* m_StateManager;
    QString m_BicepsLHTendonSA;
    QString m_BicepsLHTendonLA;
    QString m_SSPTendonLA;
    QString m_ISPTendonLA;
    QString m_SSCTendonLA;
    QString m_SSCTendonSA;
    QString m_TMTendonLA;
    QString m_ShortLanguage;
    QString m_MSKTitle;
    QString m_SAPath;
    QString m_LAPath;
    int m_ImageWidth;
    int m_ImageHeight;
    int m_TextWidth;
    int m_TextHeight;
    int m_FixedHeight;
    int m_FixedWidth;
    QPoint m_Position;
};

class USF_PROGRAM_CONTROLLER_EXPORT EndCurrentStateTool : public MenuTool
{
    Q_OBJECT
public:
    EndCurrentStateTool(const QString& name);
    void setStateManager(IStateManager* value);

protected:
    virtual void execute();

private:
    void endCurState();
    void runEndTool();

private:
    QStringList m_NeedEndStates;
    IStateManager* m_StateManager;
};
/*! 2023-03-06 Write by AlexWang
 * \brief The ZoomStateTool class Region Zoom菜单入口
 */
class USF_PROGRAM_CONTROLLER_EXPORT ZoomStateTool : public MenuTool
{
public:
    ZoomStateTool(const QString& toolName);

protected:
    void execute();
};

/*! 2023-03-20 Write by AlexWang
 * \brief The ZoomAddTool class  Region Zoom+菜单入口
 */
class USF_PROGRAM_CONTROLLER_EXPORT ZoomAddTool : public MenuTool
{
public:
    ZoomAddTool(const QString& toolName);

protected:
    void execute();
};

/*! 2023-03-20 Write by AlexWang
 * \brief The ZoomAddTool class  Region Zoom菜单入口
 */
class USF_PROGRAM_CONTROLLER_EXPORT ZoomDecTool : public MenuTool
{
public:
    ZoomDecTool(const QString& toolName);

protected:
    void execute();
};

class AcousticPowerBShowTool : public MenuTool
{
    Q_OBJECT

public:
    AcousticPowerBShowTool(const QString& toolName);

protected:
    void execute();

private slots:
    void onPresetModeChanged(bool isOpened);

private:
    void dealValueTred();

private:
    bool m_PresetModeChanged;
};

class USF_PROGRAM_CONTROLLER_EXPORT AutoEFCurLayoutTool : public MenuTool
{
    Q_OBJECT
public:
    AutoEFCurLayoutTool(const QString& name, IRulerFactory* rulerFactory);

signals:
    //刷新左右图元
    void change2RightLayout();
    void change2LeftLayout();
    //刷新非激活区图像
    void updateUnActiveLayout();

protected:
    virtual void execute();

protected:
    BaseImageRuler* m_Ruler;
};

/*
 * AutoEFSingleBTool
 */
class USF_PROGRAM_CONTROLLER_EXPORT AutoEFSingleBTool : public MenuTool
{
    Q_OBJECT
public:
    AutoEFSingleBTool(const QString& name, IRulerFactory* rulerFactory);

signals:
    void change2OneLayout(int oldLayout);

protected:
    BaseImageRuler* m_Ruler;

protected:
    virtual void execute();
};

/*
 * AutoEFFrameTool  ESFrame EDFrame
 */
class USF_PROGRAM_CONTROLLER_EXPORT AutoEFFrameTool : public MenuTool
{
    Q_OBJECT
public:
    AutoEFFrameTool(const QString& name, CineLooper* c, MeasureContext* context, ILineBufferManager* lineBufferManager);
    void setCineLooper(CineLooper* value);
private slots:
    void updateFrameInfo();
signals:
    void updateEDVIndex(int index);
    void updateESVIndex(int index);

protected:
    virtual void execute();
protected slots:
    void onBeforeCurSonoParametersChanged();
    void onSetSonoParameters();
    //    void onCurSonoParametersChanged();
    void onGettingEDFrameText(QString& value);
    void onGettingESFrameText(QString& value);

private:
    void setparaValue(QString para, int max, int min);

private:
    CineLooper* m_Looper;
    MeasureContext* m_Context;
    QString m_ToolName{""};
    ILineBufferManager* m_LineBufferManager;
};

class USF_PROGRAM_CONTROLLER_EXPORT PDStateTool : public MenuTool
{
    Q_OBJECT
public:
    PDStateTool(const QString& name);

protected:
    virtual void execute();
};

#endif // XXXMENU_H
