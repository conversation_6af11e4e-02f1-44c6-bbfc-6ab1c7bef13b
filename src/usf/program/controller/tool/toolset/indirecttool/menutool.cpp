#include "menutool.h"
#include "ibufferstoremanager.h"
#include "menutoolcontext.h"
#include "parameter.h"
#include "qvariantcommandargs.h"
#include "sonoparameters.h"
#include "stringcommandargs.h"
#include <QDebug>
#include "appsetting.h"
#include "statemanager.h"
#include "stateeventnames.h"
#include "bfpnames.h"
#include <QDebug>

MenuTool::MenuTool(QObject* parent)
    : ITool(parent)
    , m_BufferStoreManager(NULL)
    , m_SonoParameters(NULL)
    , m_IsAdd(false)
{
}

MenuTool::MenuTool(const QString& name, QObject* parent)
    : ITool(name, parent)
    , m_BufferStoreManager(NULL)
    , m_SonoParameters(NULL)
    , m_IsAdd(false)
{
}

MenuTool::~MenuTool()
{
}

// void MenuTool::initialize()
//{
//}

// void MenuTool::run(void *context)
//{

//}

// void MenuTool::setBufferStoreManager(IBufferStoreManager *value)
//{
//    m_BufferStoreManager = value;
//    m_SonoParameters = m_BufferStoreManager->curSonoParameters();
//    connect(m_BufferStoreManager, SIGNAL(curSonoParametersChanged()), this, SLOT(onCurSonoParametersChanged()));
//}
void MenuTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
        if (args != NULL)
        {
            if (this->args<QVariantCommandArgs>()->getType() == COMMAND_TYPE_BOOL)
            {
                bool arg = isInvertMulti() ? !args->arg() : args->arg();
                m_SonoParameters->parameter(this->toolName())->multi(arg, isLoopMulti());
            }
            else
            {
                int value = this->args<QVariantCommandArgs>()->getValue().toInt();
                if (value != maxmValue())
                {
                    // 考虑到触摸调节相对跟手, 调节产生的值可能是任意值(比如:
                    // 最小值1，最大值49，步进值为2，此时UI上会有产生value为2的可能), 这不符合参数调节的预期
                    // 所以，设计思路是当value大于tool当前值，且二者差值小于stepValue时，保持tool当前值不变，利用公式：value
                    // = value - value % stepValue()，
                    // 但是这种情况只能覆盖最小值为偶数的情况，考虑到奇数需要加上minmValue() % 2
                    Q_ASSERT(stepValue() != 0);
                    if (value % stepValue() != 0)
                    {
                        value = value - value % stepValue() + minmValue() % 2;
                    }
                }
                m_SonoParameters->parameter(this->toolName())->setValue(value);
            }
            this->setArgs(nullptr);
        }
        else
        {
            m_SonoParameters->parameter(this->toolName())->multi(true, true);
        }
    }
}

void MenuTool::connectCurSonoParameters()
{
    if (m_SonoParameters != NULL)
    {
        Parameter* p = m_SonoParameters->parameter(this->toolName());
        // BUG:16844 部分toolName并没有相对应的parameter,导致返回的是静态的nullParameter，之后的操作没有意义。
        //增加对返回的parameter判断,考虑应不应该增加对应的toolName的parameter
        if (p != &SonoParameters::nullParameter)
        {
            connect(p, SIGNAL(valueChanged(QVariant)), this, SLOT(onToolValueChanged(QVariant)));
            connect(p, SIGNAL(enabledChanged(bool)), this, SLOT(setIsAvailable(bool)));
            // 变更 isAvailable 的设置原则，实时打图采用 超声参数的使能控制，冻结与回调时 采用 PostProcess标记控制
            bool isToolAvailable = isPostProcessTool() && p->isEnabled();
            setIsAvailable(m_SonoParameters->isRealTime() ? p->isEnabled() : isToolAvailable);

            //设置参数时，先初始化ToolValue
            onToolValueChanged(p->value());
        }
    }
}

void MenuTool::onBeforeCurSonoParametersChanged()
{
    if (m_SonoParameters != NULL)
    {
        Parameter* p = m_SonoParameters->parameter(this->toolName());
        disconnect(p, SIGNAL(valueChanged(QVariant)), this, SLOT(onToolValueChanged(QVariant)));
        disconnect(p, SIGNAL(enabledChanged(bool)), this, SLOT(setIsAvailable(bool)));
    }
}

void MenuTool::onCurSonoParametersChanged()
{
    //回调时加载的超声参数只针对激活区的超声参数进行了信号槽连接，而cursonoparameters在回调时只是赋值并没有进行信号槽的连接，
    //而声音并不是后处理参数，因此回调时声音的tool显示的值错误。所以要使用激活区的sonoparameters。
    if (isPostProcessTool())
    {
        m_SonoParameters = m_BufferStoreManager->activeSonoParameters();
    }
    else
    {
        m_SonoParameters = m_BufferStoreManager->curSonoParameters();
    }
    onSetSonoParameters();
    connectCurSonoParameters();
}

void MenuTool::onToolValueChanged(const QVariant& value)
{
    if (isParameterTool())
    {
        if (m_SonoParameters != NULL)
        {
            setToolValue(m_SonoParameters->parameter(m_ToolName)->text());
        }
    }
}

void MenuTool::onInitialize()
{
    MenuToolContext* context = dynamic_cast<MenuToolContext*>(m_Context);
    if (context != NULL)
    {
        m_BufferStoreManager = context->bufferStoreManager();
        if (m_BufferStoreManager != NULL)
        {
            // 冻结后可以下发的菜单参数，冻结后加载图像，不绑定 m_BufferStoreManager的 SonoParameters
            // 切换的菜单，否则会无法下发给FPGA
            if (!isFreezeSendTool())
            {
                connect(m_BufferStoreManager, SIGNAL(beforeCurSonoParametersChanged()), this,
                        SLOT(onBeforeCurSonoParametersChanged()));
                connect(m_BufferStoreManager, SIGNAL(curSonoParametersChanged()), this,
                        SLOT(onCurSonoParametersChanged()));
            }
            onCurSonoParametersChanged();
        }
    }
}

void MenuTool::setSonoParameters(SonoParameters* sonoParameters)
{
    if (m_SonoParameters != sonoParameters)
    {
        onBeforeCurSonoParametersChanged();
        m_SonoParameters = sonoParameters;
        connectCurSonoParameters();
        onSetSonoParameters();
    }
}

SonoParameters* MenuTool::sonoParameters()
{
    return m_SonoParameters;
}

int MenuTool::maxmValue() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_ToolName)->max();
    }
    return 0;
}

int MenuTool::minmValue() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_ToolName)->min();
    }
    return 0;
}

int MenuTool::stepValue() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_ToolName)->step();
    }
    return 0;
}

int MenuTool::value() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_ToolName)->value().toInt();
    }
    return 0;
}

bool MenuTool::isBoolValue() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return (m_SonoParameters->parameter(m_ToolName)->valueType() == Parameter::Bool);
    }
    return false;
}

void MenuTool::onSetSonoParameters()
{
}

bool MenuTool::isAdd() const
{
    return m_IsAdd;
}

ElastoTool::ElastoTool(const QString& name)
    : MenuTool(name)
{
    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this,
            &ElastoTool::onFunctionStatusChanged);
}

void ElastoTool::onInitialize()
{
    MenuTool::onInitialize();
}

void ElastoTool::execute()
{
    if (StateManager::getInstance().isSupportElastography())
    {
        MenuTool::execute();
        StateManager::getInstance().postEvent(StateEventNames::E);
    }
}

void ElastoTool::onProbeChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updateElasto();
}

void ElastoTool::onSystemScanModeChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updateElasto();
}

void ElastoTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onProbeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSystemScanModeChanged(QVariant)));
    }
}

void ElastoTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSystemScanModeChanged(QVariant)));
    }
}

void ElastoTool::onFunctionStatusChanged()
{
    updateElasto();
}

void ElastoTool::updateElasto()
{
    bool isSupportElasto = StateManager::getInstance().isSupportElastography();

    setIsVisible(isSupportElasto);
}
