#ifndef MENUTOOL_H
#define MENUTOOL_H
#include "usfprogramcontroller_global.h"

#include <QString>
#include <QObject>
#include "itool.h"
#include "abstractmulticommand.h"

class QVariant;
class SonoParameters;
class IBufferStoreManager;
class USF_PROGRAM_CONTROLLER_EXPORT MenuTool : public ITool
{
    Q_OBJECT
public:
    explicit MenuTool(QObject* parent = NULL);
    MenuTool(const QString& name, QObject* parent = NULL);
    ~MenuTool();
    virtual void onInitialize();
    virtual bool isAdd() const;
    // void setBufferStoreManager(IBufferStoreManager *value);
    void setSonoParameters(SonoParameters* sonoParameters);
    SonoParameters* sonoParameters();
    int maxmValue() const;
    int minmValue() const;
    int stepValue() const;
    int value() const;
    bool isBoolValue() const;

protected:
    void execute();
    virtual void connectCurSonoParameters();
    virtual void onSetSonoParameters();
protected slots:
    virtual void onBeforeCurSonoParametersChanged();
    virtual void onCurSonoParametersChanged();
    virtual void onToolValueChanged(const QVariant& value);

protected:
    IBufferStoreManager* m_BufferStoreManager;
    SonoParameters* m_SonoParameters;
    bool m_IsAdd;
};

class USF_PROGRAM_CONTROLLER_EXPORT ElastoTool : public MenuTool
{
    Q_OBJECT
public:
    ElastoTool(const QString& name);
    virtual void onInitialize();

protected:
    virtual void execute();
protected slots:
    void onProbeChanged(const QVariant& value);
    void onSystemScanModeChanged(const QVariant& value);
    void onBeforeCurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onFunctionStatusChanged();

private:
    void updateElasto();
};

#endif // MENUTOOL_H
