#include "sonocardiactool.h"
#include "appsetting.h"
#include "bfpnames.h"
#include "bufferstoremanager.h"
#include "chisonultrasoundcontext.h"
#include "cinelooper.h"
#include "cursormouseactionsmodel.h"
#include "imageeventargs.h"
#include "imageinfodef.h"
#include "imagetile.h"
#include "imagewidget.h"
#include "iprobedataset.h"
#include "istatemanager.h"
#include "leftmenuwidget.h"
#include "menutools.h"
#include "qvariantcommandargs.h"
#include "resource.h"
#include "setting.h"
#include "sonocardiacexittool.h"
#include "sonocardiacglyphscontrol.h"
#include "sonocardiacsecimagetool.h"
#include "sonocardiacsecratingtool.h"
#include "sonocardiacsecrecognizetool.h"
#include "sonocardiacsectiontool.h"
#include "stateeventnames.h"
#include "statemanager.h"
#include "systemscanmodeclassifier.h"
#include "toolnames.h"
#include "toolsfactory.h"
#include "util.h"
#include <QApplication>
#include <QDir>
#include <QFileInfo>
#include <QSettings>
#include "abstractstate.h"
#include "measurecontext.h"

SonoCardiacTool::SonoCardiacTool(const QString& name, ChisonUltrasoundContext* chisonContext,
                                 CursorMouseActionsModel* cursorMouseActionsModel, QWidget* leftMenuWidget,
                                 SonoParameters* sonoParameters, ImageTile* tile, CineLooper* cineLooper,
                                 MeasureContext* measureContext)
    : MenuTool(name)
    , m_ChisonUltrasoundContext(chisonContext)
    , m_CursorMouseActionsModel(cursorMouseActionsModel)
    , m_leftMenuWidget(dynamic_cast<LeftMenuWidget*>(leftMenuWidget))
    , m_RealSonoParameters(sonoParameters)
    , m_ImageTile(tile)
    , m_CineLooper(cineLooper)
    , m_needInit(true)
    , m_MeasureContext(measureContext)
{
    connect(m_Control, &SonoCardiacGlyphsControl::dispayImageChange, this, &SonoCardiacTool::setDisplayShowImage,
            Qt::UniqueConnection);
    connect(&MenuController::instance(), &MenuController::menuChange, this, &SonoCardiacTool::menuChange,
            Qt::UniqueConnection);
    connect(&m_TextAndPic, &SonoHelpTextItemWidget::mousePress, this, &SonoCardiacTool::mousePressEvent,
            Qt::UniqueConnection);
    connect(qApp, &QApplication::focusChanged, this, &SonoCardiacTool::focuseChanged);
    connect(this, &SonoCardiacTool::preChangeLeftMenu, this, &SonoCardiacTool::changeLeftMenu);
    connect(m_CursorMouseActionsModel, &CursorMouseActionsModel::touchPressed, this, &SonoCardiacTool::hidePopUpWidget);
    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this,
            &SonoCardiacTool::functionChanged);
    connect(m_CursorMouseActionsModel, &CursorMouseActionsModel::touchPressed, this, &SonoCardiacTool::hidePopUpWidget);
    connect(m_leftMenuWidget, &LeftMenuWidget::focusOut, this, &SonoCardiacTool::hidePopUpWidget);
    Util::connectSignal(m_BufferStoreManager, SIGNAL(loadedType(QString)), this, SLOT(startRTMeasure(QString)), true,
                        Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));

    connect(&Setting::instance().defaults(), SIGNAL(languageChanged()), this, SLOT(languageChanged()));

    QSettings setting(Resource::getHelperSettingDir(), QSettings::IniFormat);
    m_ImageWidth = setting.value("Width_Image").toInt();
    m_ImageHeight = setting.value("Height_Image").toInt();
    m_PosX = setting.value("Image_PosX").toInt();
    m_PosY = setting.value("Image_PosY").toInt();
    m_FixedWeight =
        MenuController::instance().leftMenu()->geometry().width() - setting.value("Left_Right_Margin").toInt() * 2;
    m_FixedHeight = setting.value("Fixed_Height").toInt();
    m_TextAndPic.setFixedSize(m_FixedWeight, m_FixedHeight);

    QList<QString> en;
    en << (Resource::getSonoHelperDir() + QDir::separator() + "SonoCardiac" + QDir::separator() + "en_a2c.txt")
       << (Resource::getSonoHelperDir() + QDir::separator() + "SonoCardiac" + QDir::separator() + "en_a4c.txt");

    QList<QString> zh;
    zh << (Resource::getSonoHelperDir() + QDir::separator() + "SonoCardiac" + QDir::separator() + "zh_a2c.txt")
       << (Resource::getSonoHelperDir() + QDir::separator() + "SonoCardiac" + QDir::separator() + "zh_a4c.txt");

    m_AllLanguageMessagePath << en << zh;

    if (Setting::instance().defaults().shortLanguage().compare("zh_CN") == 0)
        m_CurLanguageMessagePath = m_AllLanguageMessagePath.at(1);
    else
        m_CurLanguageMessagePath = m_AllLanguageMessagePath.at(0);

    //超声图、解剖图、模拟扫查、提示信息
    m_LabelText << ("Ultrasound") << ("Anatomy") << ("Simulate") << ("Prompt");

    m_SupportExamMode = Resource::functionalConfigurationSettingValue("SonoCardiac", Resource::ValueType::ExamMode);

    connect(&StateManager::getInstance(), SIGNAL(beforePostEvent(QString)), this, SLOT(hidePopUpWidget()));
}

void SonoCardiacTool::stressechoOpen(QVariant stressecho)
{
    if (stressecho.toBool())
        setIsAvailable(false);
    else
        changeItemStatus();
}

void SonoCardiacTool::onPresetChanged(const PresetParameters&)
{
    closeSonoCardiac();
    m_RealSonoParameters->setPV(BFPNames::SonoCardiacStr, false);
    m_RealSonoParameters->setPV(BFPNames::SonoCardiacSecRatingStr, false);
    m_RealSonoParameters->setPV(BFPNames::SonoCardiacSecRecognizeStr, false);
}

void SonoCardiacTool::onAutoEFCurLayoutChanged(QVariant value)
{
    if (value.toInt() != Layout_1x1_1)
    {
        if (m_SonoParameters->pBV(BFPNames::SonoCardiacStr))
        {
            closeSonoCardiac();
        }
        setIsAvailable(false);
    }
}

void SonoCardiacTool::changeLeftMenu()
{
    setDisplayShowImage();

    changeType();
    changeImage();
}

void SonoCardiacTool::functionChanged()
{
    changeItemStatus();
}

void SonoCardiacTool::languageChanged()
{
    if (Setting::instance().defaults().shortLanguage().compare("zh_CN") == 0)
        m_CurLanguageMessagePath = m_AllLanguageMessagePath.at(1);
    else
        m_CurLanguageMessagePath = m_AllLanguageMessagePath.at(0);

    setDisplayShowImage();
}

void SonoCardiacTool::enterSonoCardiac()
{
    if (!m_SonoParameters->pBV(BFPNames::SonoCardiacStr))
        return;

    StateManager::getInstance().postEvent(StateEventNames::SonoCardiacState);
}

void SonoCardiacTool::onSetSonoParameters()
{
    if (m_SonoParameters == NULL)
        return;
    connect(m_SonoParameters->parameter(BFPNames::AutoEFCurLayoutStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onAutoEFCurLayoutChanged(QVariant)), Qt::UniqueConnection);
}

void SonoCardiacTool::changeType()
{
    if (m_SecChooseTool == NULL)
        m_SecChooseTool =
            dynamic_cast<SonoCardiacSectionTool*>(ToolsFactory::instance().tool(ToolNames::SonoCardiacSectionStr));

    emit m_SecChooseTool->changeComboboxIndex(m_SonoParameters->pIV(BFPNames::SonoCardiacSectionStr));
}

void SonoCardiacTool::changeImage()
{
    if (m_ImageTool == NULL)
        m_ImageTool =
            dynamic_cast<SonoCardiacSecImageTool*>(ToolsFactory::instance().tool(ToolNames::SonoCardiacSecImageStr));

    emit m_ImageTool->changeComboboxIndex(0);
}

void SonoCardiacTool::changeItemStatus()
{
    int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& probeDataInfo = m_ProbeDataSet->getProbe(probeId);
    bool isOpen = probeDataInfo.IsPhasedArray;
    bool examMode = examModeIsSupport(m_SonoParameters->pSV(BFPNames::ExamModeIdStr));
    QVariant isFreeze = m_RealSonoParameters->pV(BFPNames::FreezeStr);
    int mode = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);

    if (isFreeze.toBool())
        isOpen = isOpen && examMode && !badMode(mode) && (m_SonoParameters->pIV(BFPNames::LayoutStr) == Layout_1x1);
    else
        isOpen = isOpen && examMode /*  && !badMode(mode)*/;

    enableChanged(isOpen);
}

void SonoCardiacTool::closeSonoCardiac()
{
    if (m_SonoParameters->pBV(ToolNames::SonoCardiacSecRecognizeStr))
        executeThisTool(ToolNames::SonoCardiacSecRecognizeStr);
    // close rating
    if (m_SonoParameters->pBV(ToolNames::SonoCardiacSecRatingStr))
        executeThisTool(ToolNames::SonoCardiacSecRatingStr);

    m_Control->removeRatingItemInOverLay();
    m_Control->removeTextItemInOverLay();

    StateManager::getInstance().setSonoCardiacEn(false);
    hidePopUpWidget();
    m_SonoParameters->setPV(BFPNames::SonoCardiacStr, false);
    if (m_SonoParameters->isRealTime())
    {
        m_RealSonoParameters->setPV(BFPNames::SonoCardiacStr, false);
        m_RealSonoParameters->setPV(BFPNames::SonoCardiacSecRatingStr, false);
        m_RealSonoParameters->setPV(BFPNames::SonoCardiacSecRecognizeStr, false);
    }
}

void SonoCardiacTool::exitSonoCardiac()
{
    if (m_SonoParameters == NULL)
    {
        return;
    }

    m_needInit = true;
    closeSonoCardiac();
    if (!m_RealSonoParameters->pBV(BFPNames::FreezeStr))
        StateManager::getInstance().postEvent(StateEventNames::B);
    else
        StateManager::getInstance().postEvent(StateEventNames::ExitSonoCardiac);
}

void SonoCardiacTool::setProbeDataSet(IProbeDataSet* probeDataSet)
{
    m_ProbeDataSet = probeDataSet;
}

void SonoCardiacTool::startSonoCardiac()
{
    if (m_needInit)
    {
        m_needInit = false;
        m_Control->initAlgInThread();
        bool isOpen = m_SonoParameters->pBV(toolName());
        if (isOpen && !m_RealSonoParameters->pBV(BFPNames::FreezeStr))
        {
            // set section A4C
            m_Control->setSecType(1);
            m_SonoParameters->setPV(BFPNames::SonoCardiacSectionStr, 1);
            // close recogize
            if (isOpen == m_SonoParameters->pBV(ToolNames::SonoCardiacSecRecognizeStr))
                executeThisTool(ToolNames::SonoCardiacSecRecognizeStr);
            // close rating
            if (isOpen == m_SonoParameters->pBV(ToolNames::SonoCardiacSecRatingStr))
            {
                executeThisTool(ToolNames::SonoCardiacSecRatingStr);
                m_Control->removeRatingItemInOverLay();
            }
        }
        emit preChangeLeftMenu();
    }
}

void SonoCardiacTool::setDisplayShowImage()
{
    int index = m_LabelText.indexOf(("Prompt"));
    if (m_Control->ImageType() == index || m_Control->ImageType() == index + m_LabelText.length())
    {
        m_TextAndPic.setCurrentInfo(m_CurLanguageMessagePath.at(m_Control->SecType()));
        m_TextAndPic.setType(SonoHelpBaseItemWidget::Text);
        m_TextAndPic.setTextContent();
        m_TextAndPic.setShowContent(1);
    }
    else
    {
        m_TextAndPic.setType(SonoHelpBaseItemWidget::Image);
        QPixmap pix(m_ImageFile.at(m_Control->ImageType()));
        m_TextAndPic.setInfo(QFileInfo(m_ImageFile.at(m_Control->ImageType())));
        m_TextAndPic.setImage(pix.scaled(m_FixedWeight, m_FixedHeight, Qt::KeepAspectRatio, Qt::SmoothTransformation),
                              false);
        m_TextAndPic.setShowContent(0);
    }

    m_Text.setText(Util::translate("Tool", m_LabelText.at(m_Control->ImageType() % m_LabelText.length())));

    hidePopUpWidget();
}

void SonoCardiacTool::menuChange(QString menu)
{
    if (menu.compare(MenuNames::SonoCardiacMenuStr) == 0)
        addElementToLeftMenu();
    else
        m_PopUpWidget.setVisible(false);
}

void SonoCardiacTool::mousePressEvent(const QFileInfo& info)
{
    if (m_TextAndPic.getType() == SonoHelpBaseItemWidget::Image)
    {
        QPixmap pix(m_ImageFile.at(m_Control->ImageType()));
        m_PopUpWidget.setCurrentPage(0);
        m_PopUpWidget.label()->setPixmap(pix.scaled(m_ImageWidth, m_ImageHeight));
        m_PopUpWidget.setGeometry(m_PosX, m_PosY, m_ImageWidth, m_ImageHeight);
    }
    else
    {
        QFile file(m_CurLanguageMessagePath.at(m_Control->SecType()));
        if (file.open(QIODevice::ReadOnly))
        {
            QByteArray array = file.readAll();
            m_PopUpWidget.setCurrentPage(1);
            m_PopUpWidget.setText(array);
            file.close();
            m_PopUpWidget.setGeometry(m_PosX, m_PosY, m_ImageWidth / 2, m_ImageHeight);
        }
        else
        {
            qCritical() << PRETTY_FUNCTION << "open file failed!" << file.errorString();
        }
    }
    m_PopUpWidget.show();
}

void SonoCardiacTool::hidePopUpWidget()
{
    m_PopUpWidget.hide();
}

void SonoCardiacTool::onCurSonoParametersChanged()
{
    SonoParameters* sonoParameters;
    if (isPostProcessTool())
    {
        sonoParameters = m_BufferStoreManager->activeSonoParameters();
    }
    else
    {
        sonoParameters = m_BufferStoreManager->curSonoParameters();
    }
    if (sonoParameters != NULL)
    {
        if (m_SonoParameters != NULL)
        {
            disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(onProbeIdChanged(QVariant)));
            disconnect(m_SonoParameters->parameter(BFPNames::ExamModeIdStr), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(onExamModeChanged(QVariant)));
            disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(onScanModeChanged(QVariant)));
            disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(onFreezeChanged(QVariant)));
            disconnect(m_SonoParameters->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(onLayoutChanged(QVariant)));
            disconnect(m_SonoParameters->parameter(BFPNames::StressEchoEnStr), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(stressechoOpen(QVariant)));
            disconnect(m_SonoParameters, SIGNAL(presetChanged(const PresetParameters&)), this,
                       SLOT(onPresetChanged(const PresetParameters&)));
            disconnect(m_SonoParameters->parameter(BFPNames::AutoEFCurLayoutStr), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(onAutoEFCurLayoutChanged(QVariant)));
            disconnect(m_SonoParameters->parameter(BFPNames::IsMLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(onScanLineChanged(QVariant)));
            disconnect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr),
                       SIGNAL(valueChanged(QVariant)), this, SLOT(onScanLineChanged(QVariant)));
        }
        m_SonoParameters = sonoParameters;
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeIdChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::ExamModeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onExamModeChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onScanModeChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreezeChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onLayoutChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::StressEchoEnStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(stressechoOpen(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters, SIGNAL(presetChanged(const PresetParameters&)), this,
                SLOT(onPresetChanged(const PresetParameters&)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::AutoEFCurLayoutStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onAutoEFCurLayoutChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::IsMLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onScanLineChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                this, SLOT(onScanLineChanged(QVariant)));

        changeItemStatus();
        SonoCardiacSecRecognizeTool* rec = dynamic_cast<SonoCardiacSecRecognizeTool*>(
            ToolsFactory::instance().tool(ToolNames::SonoCardiacSecRecognizeStr));
        if (rec != NULL)
            rec->controlRecognize(m_SonoParameters->pBV(BFPNames::SonoCardiacSecRatingStr));

        SonoCardiacSecRatingTool* rating =
            dynamic_cast<SonoCardiacSecRatingTool*>(ToolsFactory::instance().tool(ToolNames::SonoCardiacSecRatingStr));
        if (rating != NULL)
            rating->controlRating(m_SonoParameters->pBV(BFPNames::SonoCardiacSecRatingStr));

        if (m_SonoParameters->pBV(BFPNames::SonoCardiacSecRatingStr) ||
            m_SonoParameters->pBV(BFPNames::SonoCardiacSecRecognizeStr))
        {
            m_Control->initAlg();
        }

        changeType();

        if (!m_SonoParameters->isRealTime())
        {
            m_SonoParameters->setPV(BFPNames::SonoCardiacStr, false);
        }
        else
        {
            StateManager::getInstance().setSonoCardiacEn(
                m_RealSonoParameters->pBV(BFPNames::SonoCardiacSecRatingStr) ||
                m_RealSonoParameters->pBV(BFPNames::SonoCardiacSecRecognizeStr) ||
                m_RealSonoParameters->pBV(BFPNames::SonoCardiacStr));
        }
    }
}

void SonoCardiacTool::onProbeIdChanged(QVariant var)
{
    int probeId = var.toInt();
    const ProbeDataInfo& probeDataInfo = m_ProbeDataSet->getProbe(probeId);
    bool isOpen = probeDataInfo.IsPhasedArray;
    bool examMode = examModeIsSupport(m_SonoParameters->pSV(BFPNames::ExamModeIdStr));
    QVariant isFreeze = m_RealSonoParameters->pV(BFPNames::FreezeStr);
    int mode = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);

    if (isFreeze.toBool())
        isOpen = isOpen && examMode && !badMode(mode) && (m_SonoParameters->pIV(BFPNames::LayoutStr) == Layout_1x1);
    else
        isOpen = isOpen && examMode /* && !badMode(mode)*/;

    enableChanged(isOpen);
}

void SonoCardiacTool::onScanModeChanged(QVariant mode)
{
    int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& probeDataInfo = m_ProbeDataSet->getProbe(probeId);
    bool isOpen = probeDataInfo.IsPhasedArray;
    bool examMode = examModeIsSupport(m_SonoParameters->pSV(BFPNames::ExamModeIdStr));
    QVariant isFreeze = m_RealSonoParameters->pV(BFPNames::FreezeStr);

    if (isFreeze.toBool())
        isOpen =
            isOpen && examMode && !badMode(mode.toInt()) && (m_SonoParameters->pIV(BFPNames::LayoutStr) == Layout_1x1);
    else
        isOpen = isOpen && examMode /*  && !badMode(mode.toInt())*/;

    enableChanged(isOpen);
}

void SonoCardiacTool::onScanLineChanged(QVariant isVisible)
{
    if (isVisible.toBool())
        closeSonoCardiac();
}

void SonoCardiacTool::onExamModeChanged(QVariant examMode)
{
    int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& probeDataInfo = m_ProbeDataSet->getProbe(probeId);
    bool isOpen = probeDataInfo.IsPhasedArray;
    QVariant isFreeze = m_RealSonoParameters->pV(BFPNames::FreezeStr);
    int mode = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);

    bool exam = examModeIsSupport(examMode.toString());
    if (isFreeze.toBool())
        isOpen = isOpen && exam && badMode(mode) && (m_SonoParameters->pIV(BFPNames::LayoutStr) == Layout_1x1);
    else
        isOpen = isOpen && exam /*  && !badMode(mode)*/;

    enableChanged(isOpen);
}

void SonoCardiacTool::onLayoutChanged(QVariant)
{
    int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& probeDataInfo = m_ProbeDataSet->getProbe(probeId);
    bool isOpen = probeDataInfo.IsPhasedArray;
    QString exam = m_SonoParameters->pSV(BFPNames::ExamModeIdStr);
    bool examMode = examModeIsSupport(exam);
    QVariant isFreeze = m_RealSonoParameters->pV(BFPNames::FreezeStr);
    int mode = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);

    if (isFreeze.toBool())
        isOpen = isOpen && examMode && !badMode(mode) && (m_SonoParameters->pIV(BFPNames::LayoutStr) == Layout_1x1);
    else
        isOpen = isOpen && examMode /* && !badMode(mode)*/;

    enableChanged(isOpen);
}

bool SonoCardiacTool::freezeVisibleControl(int mode)
{
    bool scanLineVisible = m_SonoParameters->pBV(BFPNames::AutoEFCurLayoutStr) ||
                           m_SonoParameters->pBV(BFPNames::IsMLineVisibleStr) ||
                           m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr);
    return !scanLineVisible && !badMode(mode) && (m_SonoParameters->pIV(BFPNames::LayoutStr) == Layout_1x1);
}

void SonoCardiacTool::onFreezeChanged(QVariant isFreeze)
{
    int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& probeDataInfo = m_ProbeDataSet->getProbe(probeId);
    bool isOpen = probeDataInfo.IsPhasedArray;
    bool examMode = examModeIsSupport(m_SonoParameters->pSV(BFPNames::ExamModeIdStr));
    int mode = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);

    if (isFreeze.toBool())
        isOpen = isOpen && examMode && freezeVisibleControl(mode);
    else
        isOpen = isOpen && examMode;

    enableChanged(isOpen);

    if (isFreeze.toBool())
    {
        if (!m_SonoParameters->isRealTime())
        {
            m_SonoParameters->setPV(BFPNames::SonoCardiacStr, false);
        }
        connect(m_Control, &SonoCardiacGlyphsControl::secRecSwitchChanged, this, &SonoCardiacTool::algParameterChange,
                Qt::UniqueConnection);
        connect(m_Control, &SonoCardiacGlyphsControl::ratingSwitchChanged, this, &SonoCardiacTool::algParameterChange,
                Qt::UniqueConnection);
        connect(m_Control, &SonoCardiacGlyphsControl::secTypeChanged, this, &SonoCardiacTool::algParameterChange,
                Qt::UniqueConnection);
    }
    else
    {
        disconnect(m_Control, &SonoCardiacGlyphsControl::secRecSwitchChanged, this,
                   &SonoCardiacTool::algParameterChange);
        disconnect(m_Control, &SonoCardiacGlyphsControl::ratingSwitchChanged, this,
                   &SonoCardiacTool::algParameterChange);
        disconnect(m_Control, &SonoCardiacGlyphsControl::secTypeChanged, this, &SonoCardiacTool::algParameterChange);
    }
}

void SonoCardiacTool::execute()
{
    QVariantCommandArgs boolArgs;
    boolArgs.setType(COMMAND_TYPE_BOOL);
    boolArgs.setArg(!m_SonoParameters->pBV(BFPNames::SonoCardiacStr));
    setArgs(&boolArgs);
    MenuTool::execute();

    if (!m_SonoParameters->pBV(BFPNames::SonoCardiacStr))
    {
        exitSonoCardiac();
    }
    else
    {
        m_needInit = true;
        StateManager::getInstance().postEvent(StateEventNames::B);
        if (QString::compare(StateManager::getInstance().currentState()->name(), StateEventNames::SonoCardiacState) !=
            0)
        {
            metaObject()->invokeMethod(this, "enterSonoCardiac", Qt::QueuedConnection);
        }
    }
}

void SonoCardiacTool::addElementToLeftMenu()
{
    MenuController::instance().leftMenu()->addElement(&m_Text);
    MenuController::instance().leftMenu()->addElement(&m_TextAndPic);

    m_Text.setVisible(true);
    m_TextAndPic.setVisible(true);

    MenuController::instance().leftMenu()->refresh();
}

bool SonoCardiacTool::examModeIsSupport(QString exam)
{
    return m_SupportExamMode.contains(exam);
}

void SonoCardiacTool::startRTMeasure(const QString& type)
{
    if (m_Tool == NULL)
        m_Tool = dynamic_cast<SonoCardiacSecRecognizeTool*>(
            ToolsFactory::instance().tool(ToolNames::SonoCardiacSecRecognizeStr));

    if (m_SonoParameters->pBV(BFPNames::SonoCardiacSecRatingStr) ||
        m_SonoParameters->pBV(BFPNames::SonoCardiacSecRecognizeStr))
    {
        Util::connectSignal(m_ChisonUltrasoundContext, SIGNAL(newImage(ImageEventArgs*)), m_Tool,
                            SLOT(onNewImage(ImageEventArgs*)), true,
                            Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
        m_Control->setRatingSwitch(m_SonoParameters->pBV(BFPNames::SonoCardiacSecRatingStr) ? 1 : 0);
        m_Control->setSecRecSwitch(m_SonoParameters->pBV(BFPNames::SonoCardiacSecRecognizeStr) ? 1 : 0);
        changeType();
        m_Control->initAlgInThread();
        m_Control->addTextItemToOverLay();
        m_Control->addRatingItemToOverLay();
    }
    else
    {
        m_Control->releaseCtx();
        m_Control->removeRatingItemInOverLay();
        m_Control->removeTextItemInOverLay();
        Util::connectSignal(m_ChisonUltrasoundContext, SIGNAL(newImage(ImageEventArgs*)), m_Tool,
                            SLOT(onNewImage(ImageEventArgs*)), false,
                            Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
    }
}

void SonoCardiacTool::playRTMeasureCine(ImageEventArgs* arg)
{
    if (m_EasyViewControl == NULL)
        return;

    QImage img(QImage(arg->imageData(), arg->width(), arg->height(), QImage::Format_RGB32));
    m_EasyViewControl->ReconizeSection(img);
}

void SonoCardiacTool::algParameterChange()
{
    if (!m_RealSonoParameters->pBV(BFPNames::FreezeStr))
        return;

    if (m_CineLooper != NULL && m_CineLooper->isLooping())
        return;

    m_Control->ReconizeSection(m_MeasureContext->bImage());
}

void SonoCardiacTool::focuseChanged(QWidget* old, QWidget* now)
{
    if (old == &m_TextAndPic || now != &m_PopUpWidget)
        m_PopUpWidget.setVisible(false);
}

void SonoCardiacTool::enableChanged(bool isOpen)
{
    int mode = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);
    bool isEnable = AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoCardiac);
    if (!m_SonoParameters->isRealTime() && isOpen)
    {
        //没有打开license回调的情况下
    }
    else if (!(isOpen && isEnable) || m_SonoParameters->pIV(BFPNames::LayoutStr) != Layout_1x1 || badMode(mode))
    {
        closeSonoCardiac();
    }
    isOpen = isOpen && isEnable;
    setIsVisible(isOpen);
    if (m_SonoParameters->pBV(BFPNames::StressEchoEnStr))
        setIsAvailable(false);
    else
        setIsAvailable(isOpen);

    if (!isOpen)
        StateManager::getInstance().setSonoCardiacEn(false);

    m_SonoParameters->parameter(BFPNames::SonoCardiacStr)->setEnabled(isOpen);
    m_RealSonoParameters->parameter(BFPNames::SonoCardiacStr)->setEnabled(isOpen);
}
