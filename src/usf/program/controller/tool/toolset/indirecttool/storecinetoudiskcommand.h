/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef STORECINETOUDISKCOMMAND_H
#define STORECINETOUDISKCOMMAND_H
#include "usfprogramcontroller_global.h"

#include "storecinecommand.h"

class USF_PROGRAM_CONTROLLER_EXPORT StoreCineToUDiskCommand : public StoreCineCommand
{
    Q_OBJECT
public:
    StoreCineToUDiskCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager,
                            SonoParameters* sonoParameters, IMainWindow* flexBtnVisible, BaseWidget* imageClipWidget,
                            IBufferStoreManager* bufferStoreManager, IPatientWorkflow* patientWorkflow,
                            IDicomTaskManager* dicomTaskManager, IStressEchoModel* stressEchoModel,
                            BaseWidget* storeProgressMovieWidget, PatientEditModel* patientEditModel,
                            ISystemStatusModel* systemStatusModel, QWidget* sonoZoomWidget, IZoomOnProxy* zoomOnProxy,
                            IBeamFormer* beamFormer, CineLooper* cineLooper, IColorMapManager* colorMapManager,
                            ISystemInfo* systemInfo, ImageTile* imageTile, IDicomUtilityCreator* dicomUtilityCreator,
                            IDicomToolCreator* dicomToolCreator, ChisonUltrasoundContext* ultrasoundContext,
                            MeasureContext* measureContext);
protected slots:
    virtual void execute();
    virtual bool checkDiskSpace();
    virtual QString generateFileName() const;
    virtual void checkTargetDir() const;
    virtual QString screenSuffix() const;
    virtual void exportFile();
};

#endif // STORECINETOUDISKCOMMAND_H
