#ifndef STORECINECOMMAND_H
#define STORECINECOMMAND_H
#include "usfprogramcontroller_global.h"

#include "basestorecommand.h"
#include "dicomutility.h"
#include "abstractlighttool.h"
#include "screentypedef.h"

class CineLooper;
class IBeamFormer;
class QImage;
class FilesExporter;
class SonoZoomWidget;
class ZoomOnProxy;
class IZoomOnProxy;
class IPatientWorkflow;
class IDicomTaskManager;
class ImageSkimManager;
class IBufferStoreManager;
class IStressEchoModel;
class StoreProgressMovieWidget;
class PatientEditModel;
class ISystemStatusModel;
class IColorMapManager;
class ISystemInfo;
class ImageTile;
class IDicomUtilityCreator;
class IDicomToolCreator;
class MeasureContext;

class USF_PROGRAM_CONTROLLER_EXPORT StoreCineCommand : public BaseStoreCommand, public AbstractLightTool
{
    Q_OBJECT
public:
    StoreCineCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager,
                     SonoParameters* sonoParameters, IMainWindow* flexBtnVisible, BaseWidget* imageClipWidget,
                     IBufferStoreManager* bufferStoreManager, IPatientWorkflow* patientWorkflow,
                     IDicomTaskManager* dicomTaskManager, IStressEchoModel* stressEchoModel,
                     BaseWidget* storeProgressMovieWidget, PatientEditModel* patientEditModel,
                     ISystemStatusModel* systemStatusModel, QWidget* sonoZoomWidget, IZoomOnProxy* zoomOnProxy,
                     IBeamFormer* beamFormer, CineLooper* cineLooper, IColorMapManager* colorMapManager,
                     ISystemInfo* systemInfo, ImageTile* imageTile, IDicomUtilityCreator* dicomUtilityCreator,
                     IDicomToolCreator* dicomToolCreator, ChisonUltrasoundContext* ultrasoundContext,
                     MeasureContext* measureContext);

    void setIsRealTimeCine(bool value);
    void realTimeSaveFinished(bool status);
    virtual void showSavingMovie(bool inFourDWidget = false);
    virtual void hideSavingMovie();

protected:
    virtual const QString& lightToolName() const;
    virtual bool checkDiskSpace();
    virtual void operate();
    virtual void doSave(const QString& fileName);
    virtual void doRealTimeSaveCine(const QString& fileName);
    virtual QString generateFileName() const;
    virtual QString fileExt() const;
    virtual void beginOperate();
    virtual void checkTargetDir() const;
    virtual QString screenSuffix() const;
    void doDicomStorage(const QString& str);
    void makeUDiskDir() const;
    QString makeUDiskFileName() const;
    bool checkUDiskSpace();
    /**
     * @brief removeFailedFiles 如果存储失败，删除截屏文件和0字节的img或者cine
     */
    void removeFailedFiles();
    virtual void closeSystemStatusTipShown();
    virtual void hideImageSkimExpandButton();
    virtual QImage grabThumbnailScreen();
    virtual void saveThumbnailScreen(const QImage& image, const QString& fileBaseName);
    virtual void realTimeStoreAutoExitFreeze();
    virtual void exportFile(bool isGDPR = false);
    void setImageParameters();
    void handleScreenshots(bool succeed);
    void handleMaxImageNum(bool succeed);
    void saveImageNum(int imageNum);
    void saveSopInstanceUID();
    void dealCopyThread();
    void processGDPRRemind();
    virtual bool isSupportSave() const;

private:
    Q_INVOKABLE void invokeSaveScreen(QString fileName);
    bool innerCheckDiskSpace(const QString& name) const;
    void doStoreToStressEcho(bool succeed);
    void handleSavedFiles(bool succeed);
    void handleSaveState(bool succeed, bool showWarn = true);
    enum
    {
        RealTimeSaveRetryCount = 3
    };
protected slots:
    virtual void onCommandFinished(bool succeed);

protected:
    bool m_IsCine;
    bool m_IsRealTimeCine;
    QString m_FileName;
    QString m_udiskDir;
    bool m_IsAddScreenToClipWidget;
    FilesExporter* m_FilesExporter;
    IBeamFormer* m_BeamFormer;
    ScreenTypeDef::ModeType m_ModeType;
    bool m_IsGDPR;
    CineLooper* m_Looper;

    //实时存电影完成了一次从开始到结束的周期
    bool m_IsRealtimeSaveFinishOnce;
    IPatientWorkflow* m_PatientWorkflow;
    IDicomTaskManager* m_DicomTaskManager;
    StoreProgressMovieWidget* m_ProgressMovieWidget;
    PatientEditModel* m_PatientEditModel;
    ISystemStatusModel* m_SystemStatusModel;
    IStressEchoModel* m_StressEchoModel;
    SonoZoomWidget* m_SonoZoomWidget;
    ZoomOnProxy* m_ZoomOnProxy;
    ISystemInfo* m_SystemInfo;
    ImageTile* m_ImageTile;
    IDicomToolCreator* m_DicomToolCreator;
    IDicomUtilityCreator* m_DicomUtilityCreator;
    int m_RealTimeSaveIndex; //实时存储电影的当前帧，这个值通常指向绘制界面绘制的索引最新值
};

#endif // STORECINECOMMAND_H
