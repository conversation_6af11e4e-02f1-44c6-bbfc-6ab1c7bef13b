#include "sonomsktool.h"
#include "appsetting.h"
#include "bfpnames.h"
#include "chisonultrasoundcontext.h"
#include "glyphscontrolmanager.h"
#include "ibufferstoremanager.h"
#include "imagetile.h"
#include "imagewidget.h"
#include "irawimagebufferdatasetter.h"
#include "lefttopmenuwidgetcontainer.h"
#include "licenseitemkey.h"
#include "measurecontext.h"
#include "menucontroller.h"
#include "menunames.h"
#include "menutools.h"
#include "probedataset.h"
#include "resource.h"
#include "sonobuffer.h"
#include "sonobuffers.h"
#include "sonomskglyphscontrol.h"
#include "sonoparameters.h"
#include "stateeventnames.h"
#include "statemanager.h"
#include "stringcommandargs.h"
#include "toolnames.h"
#include "toolsfactory.h"
#include "util.h"
#include "painterrenderimagetile.h"

SonoMSKTool::SonoMSKTool(const QString& name, SonoParameters* sonoParameters, ImageTile* tile,
                         MeasureContext* measContext, ChisonUltrasoundContext* ultrasoundContext)
    : MenuTool(name)
    , m_Running(false)
    , m_RunningInEasyView(false)
    , m_SonoPart(0)
    , m_isSupport(false)
    , m_isExamModeNameChanged(false)
    , m_IsClickedToCloseMSK(false)
    , m_easyViewSonoParameters(nullptr)
    , m_Control(nullptr)
    , m_EasyViewControl(nullptr)
    , m_RealSonoParameters(sonoParameters)
    , m_ImageTile(tile)
    , m_MeasContext(measContext)
    , m_ChisonUltrasoundContext(ultrasoundContext)
{
    Util::connectSignal(m_ChisonUltrasoundContext, SIGNAL(newBImage(ImageEventArgs*)), this,
                        SLOT(onNewImage(ImageEventArgs*)), true, Qt::ConnectionType(Qt::DirectConnection));

    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this,
            &SonoMSKTool::onFunctionStatusChanged);

    m_SupportExamMode = Resource::functionalConfigurationSettingValue("SonoMSK", Resource::ValueType::ExamMode);
}

void SonoMSKTool::execute()
{
    if (m_SonoParameters == nullptr)
        return;

    if (!m_RealSonoParameters->pBV(BFPNames::FreezeStr))
        dynamic_cast<SonoBuffers*>(m_ChisonUltrasoundContext->lineBuffer())->activeDataSetter()->clear();

    initGlyphsControl();

    int renderHeight = m_SonoParameters->pV(BFPNames::RenderImageSizeStr).value<QSize>().height();
    m_Control->setRenderWidgetHeight(renderHeight);

    //如果自动肌骨正在运行，则退出自动肌骨功能，否则开启自动肌骨功能
    if (!m_SonoParameters->pBV(BFPNames::SonoMSKStr))
    {
        m_SonoPart = 0;
        m_Running = true;

        if (m_Control != nullptr)
        {
            m_Control->initNeura(m_SonoPart); //默认是第一个，肌肩沟
        }

        m_SonoParameters->setPV(BFPNames::SonoMSKPartStr, 0); //参数值回到初始状态
        if (m_Control != nullptr)
        {
            m_Control->setIsActive(true);
            m_SonoParameters->pBV(BFPNames::SonoMSKIsShowStr) ? m_Control->showActive() : m_Control->hideActive();
        }
    }
    else
    {
        m_IsClickedToCloseMSK = true;
    }

    MenuTool::execute();

    StateManager::getInstance().postEvent(StateEventNames::B);

    if (m_SonoParameters->pBV(BFPNames::SonoMSKStr))
    {
        StateManager::getInstance().postEvent(StateEventNames::SonoMSKState);
    }
}

void SonoMSKTool::onNewImage(ImageEventArgs* args)
{
    SonoMSKGlyphsControl* control =
        GlyphsControlManager::instance().getChildGlyphsControl<SonoMSKGlyphsControl>(GlyphsCtl::SonoMSKGlyphsType);

    if (m_SonoParameters == nullptr || control == nullptr)
    {
        return;
    }

    control->createGlyphs();
    if (!m_SonoParameters->pBV(BFPNames::SonoMSKStr)) //自动肌骨功能未开
    {
        m_Running = false;
        control->hideActive();
        control->neuralsgRelease();
        control->setIsActive(false);
        return;
    }
    else
    {
        m_Running = true;

        if (!control->isCreatectx() || m_SonoPart != m_SonoParameters->pIV(BFPNames::SonoMSKPartStr))
        {
            control->initNeura(m_SonoParameters->pIV(BFPNames::SonoMSKPartStr));
            m_SonoPart = m_SonoParameters->pIV(BFPNames::SonoMSKPartStr);
        }
    }

    control->setIsActive(true);
    control->setTransparent(m_SonoParameters->pIV(BFPNames::SonoMSKTransStr));
    control->setInvert(!m_BufferStoreManager->activeSonoParameters()->pBV(ToolNames::UDStr));
    control->setLRInvert(!m_BufferStoreManager->activeSonoParameters()->pBV(ToolNames::LRStr));
    if (args->imageType() == ImageEventArgs::ImageB && (m_Running || m_SonoParameters->pBV(BFPNames::SonoMSKStr)))
    {
        QImage img(args->imageData(), args->width(), args->height(), QImage::Format_RGB32);
        control->update(img, m_SonoParameters->pBV(BFPNames::SonoMSKIsShowStr));
    }
}

void SonoMSKTool::onExamModeChanged()
{
    m_isExamModeNameChanged = true;
}

void SonoMSKTool::onFunctionStatusChanged()
{
    setMenuState();
}

void SonoMSKTool::playRTMeasureCine(ImageEventArgs* arg)
{
    disconnect(m_MeasContext, &MeasureContext::newImage, this, &SonoMSKTool::playRTMeasureCine);

    if (m_easyViewSonoParameters == NULL || !m_RunningInEasyView)
        return;

    int width = arg->width();
    int height = arg->height();
    m_EasyViewControl->update(QImage(arg->imageData(), width, height, QImage::Format_RGB32),
                              m_easyViewSonoParameters->pBV(BFPNames::SonoMSKIsShowStr));

    if (m_easyViewSonoParameters == NULL || !m_RunningInEasyView)
        return;

    connect(m_MeasContext, &MeasureContext::newImage, this, &SonoMSKTool::playRTMeasureCine,
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
}

void SonoMSKTool::prepareData()
{
    m_EasyViewControl =
        GlyphsControlManager::instance().getChildGlyphsControl<SonoMSKGlyphsControl>(GlyphsCtl::SonoMSKGlyphsType);
    m_EasyViewControl->initNeura(m_easyViewSonoParameters->pIV(BFPNames::SonoMSKPartStr));
    m_SonoPart = m_easyViewSonoParameters->pIV(BFPNames::SonoMSKPartStr);
    m_easyViewSonoParameters->pBV(BFPNames::SonoMSKIsShowStr) ? m_EasyViewControl->showActive()
                                                              : m_EasyViewControl->hideActive();
    m_EasyViewControl->setIsActive(true);
    m_EasyViewControl->setTransparent(m_easyViewSonoParameters->pIV(BFPNames::SonoMSKTransStr));
    m_EasyViewControl->setInvert(!m_easyViewSonoParameters->pBV(ToolNames::UDStr));
    m_EasyViewControl->setLRInvert(!m_easyViewSonoParameters->pBV(ToolNames::LRStr));
    m_RunningInEasyView = true;
}

void SonoMSKTool::Exit()
{
    m_MeasContext->setRealTimeMeasureFrameIndex(-1);
    m_MeasContext->setRealTimeMeasureFrontIndex(-1);

    if (m_SonoParameters->pBV(BFPNames::SonoMSKStr))
    {
        m_SonoParameters->setPV(BFPNames::SonoMSKStr, false);
        SonoMSKGlyphsControl* control =
            GlyphsControlManager::instance().getChildGlyphsControl<SonoMSKGlyphsControl>(GlyphsCtl::SonoMSKGlyphsType);
        if (control->isActive())
        {
            control->hideActive();
            control->neuralsgRelease();
            control->setIsActive(false);
        }
    }

    StateManager::getInstance().postEvent(StateEventNames::B);
}

void SonoMSKTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    forDisConnect();
}

void SonoMSKTool::onCurSonoParametersChanged()
{
    initGlyphsControl();
    MenuTool::onCurSonoParametersChanged();
    forConnect();
    setMenuState();

    initGlyphsIsShow();
}

void SonoMSKTool::onProbeChanged(const QVariant& value)
{
    stopSonoMSK();
    setMenuState();
}

void SonoMSKTool::onInitialize()
{
    MenuTool::onInitialize();
}

void SonoMSKTool::setEasyViewSonoParameters(SonoParameters* sono)
{
    m_easyViewSonoParameters = sono;
    if (m_easyViewSonoParameters != NULL)
        prepareData();
    else
        m_RunningInEasyView = false;
}

void SonoMSKTool::onSystemScanModeChanged(const QVariant& value)
{
    stopSonoMSK();
    setMenuState();
}

void SonoMSKTool::onExamModeIdChanged(const QVariant& value)
{
    stopSonoMSK();
    setMenuState();
}

void SonoMSKTool::onSonoMSKPartChanged(const QVariant& value)
{
    if (m_Control == nullptr)
    {
        m_Control =
            GlyphsControlManager::instance().getChildGlyphsControl<SonoMSKGlyphsControl>(GlyphsCtl::SonoMSKGlyphsType);
    }

    if (m_Control != nullptr)
    {
        m_Control->setPart(value.toInt());
    }

    if (!m_SonoParameters->pBV(BFPNames::SonoMSKStr))
        return;

    if (m_SonoPart != value.toInt())
    {
        if (!m_RealSonoParameters->pBV(BFPNames::FreezeStr))
        {
            dynamic_cast<SonoBuffers*>(m_ChisonUltrasoundContext->lineBuffer())->activeDataSetter()->clear();
        }

        m_SonoPart = value.toInt();
        if (m_Control != nullptr)
        {
            m_Control->createGlyphs();
            m_Control->initNeura(m_SonoPart);
        }
    }
}

void SonoMSKTool::onLayoutChanged(const QVariant& value)
{
    int layout = m_SonoParameters->pIV(BFPNames::LayoutStr);

    if (layout != Layout_1x1)
    {
        stopSonoMSK();
    }

    setMenuState();
}

void SonoMSKTool::onIsShowChanged(const QVariant& value)
{
    if (m_Control != nullptr)
    {
        value.toBool() ? m_Control->showActive() : m_Control->hideActive();
    }
}

void SonoMSKTool::stopSonoMSK()
{
    if (m_SonoParameters != nullptr)
    {
        m_isExamModeNameChanged = false;
        if (m_SonoParameters->pBV(BFPNames::SonoMSKStr))
        {
            m_SonoParameters->setPV(BFPNames::SonoMSKStr, false);
            if (m_Control != nullptr)
            {
                if (m_Control->isActive())
                {
                    m_Control->hideActive();
                    m_Control->neuralsgRelease();
                }
            }
        }
    }
}

void SonoMSKTool::onFreezeChanged(const QVariant& value)
{
    setMenuState();
}

void SonoMSKTool::onScanLineChanged(const QVariant& value)
{
    if (m_SonoParameters != nullptr)
    {
        stopSonoMSK();
        setMenuState();
    }
}

void SonoMSKTool::onPresetChanged(const PresetParameters& value)
{
    stopSonoMSK();
    setMenuState();
}

void SonoMSKTool::onSonoMSKOnChanged(const QVariant& value)
{
    StateManager::getInstance().setIsCloseSonoMSK(!value.toBool());
    if (m_IsClickedToCloseMSK)
    {
        m_IsClickedToCloseMSK = false;
        return;
    }

    StringCommandArgs args;
    if (value.toBool())
    {
        args.setArg(MenuNames::SonoMSKMenuStr);
    }
    else
    {
        args.setArg(MenuNames::BMenuStr);
    }

    ICommand* changeTool = ToolsFactory::instance().command(ToolNames::PostChangeStr);
    changeTool->setArgs(&args);
    changeTool->run();
    dynamic_cast<PostChangeTool*>(changeTool)->setCurrentMenus();
}

void SonoMSKTool::forDisConnect()
{
    if (m_SonoParameters != nullptr)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSystemScanModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ExamModeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onExamModeIdChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onProbeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoMSKPartStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSonoMSKPartChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onLayoutChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoMSKIsShowStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onIsShowChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFreezeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                   this, SLOT(onScanLineChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::IsMLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onScanLineChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoMSKStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSonoMSKOnChanged(QVariant)));
        disconnect(m_SonoParameters, SIGNAL(presetChanged(const PresetParameters&)), this,
                   SLOT(onPresetChanged(const PresetParameters&)));
    }
}

void SonoMSKTool::forConnect()
{
    if (m_SonoParameters != nullptr)
    {
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSystemScanModeChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::ExamModeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onExamModeIdChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::SonoMSKPartStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSonoMSKPartChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onLayoutChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::SonoMSKIsShowStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onIsShowChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreezeChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                this, SLOT(onScanLineChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::IsMLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onScanLineChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::SonoMSKStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSonoMSKOnChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters, SIGNAL(presetChanged(const PresetParameters&)), this,
                SLOT(onPresetChanged(const PresetParameters&)), Qt::UniqueConnection);

        //        //强刷一次
        //        m_SonoParameters->setPV(BFPNames::SonoMSKPartStr, m_SonoParameters->pIV(BFPNames::SonoMSKPartStr),
        //        true);

        if (m_isExamModeNameChanged)
        {
            stopSonoMSK();
            setMenuState();
        }
    }
}

bool SonoMSKTool::availableControl()
{
    bool isEnable = false;
    if (m_SonoParameters != nullptr)
    {
        isEnable = true;
        //单B模式
        SystemScanMode mode = (SystemScanMode)m_SonoParameters->pIV(BFPNames::SystemScanModeStr);
        int layout = m_SonoParameters->pIV(BFPNames::LayoutStr);

        if (layout != Layout_1x1)
        {
            isEnable = false;
        }
        if (mode != SystemScanModeB)
        {
            isEnable = false;
        }

        //肌骨预设
        if (!m_SupportExamMode.contains(m_SonoParameters->pSV(BFPNames::ExamModeIdStr)))
        {
            isEnable = false;
        }

        //线阵
        int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
        const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(probeId);
        if (!probeDataInfo.IsLinear)
        {
            isEnable = false;
        }

        if (m_SonoParameters->pIV(BFPNames::FreezeStr))
        {
            isEnable = false;
        }

        if (!AppSetting::isHuman())
        {
            isEnable = false;
        }

        // PW Pre  or M Pre
        if (m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr) ||
            m_SonoParameters->pBV(BFPNames::IsMLineVisibleStr))
        {
            isEnable = false;
        }

        //回调时，SonoMSK按钮置灰
        if (!m_BufferStoreManager->curSonoParameters()->isRealTime())
        {
            isEnable = false;
        }

        setIsAvailable(isEnable);
    }

    return isEnable;
}

bool SonoMSKTool::visibleControl()
{
    bool isVisible = false;

    if (m_SonoParameters != nullptr)
    {
        isVisible = true;
        //肌骨预设
        if (!m_SupportExamMode.contains(m_SonoParameters->pSV(BFPNames::ExamModeIdStr)))
        {
            isVisible = false;
        }
    }

    if (!AppSetting::isFunctionEnabled(LicenseItemKey::KeySonoMSK))
    {
        isVisible = false;
    }

    setIsVisible(isVisible);

    return isVisible;
}

void SonoMSKTool::setMenuState()
{
    bool isEnable = availableControl();
    bool isVisible = visibleControl();

    StateManager::getInstance().setIsSupportSonoMSK(isEnable && isVisible);
}

void SonoMSKTool::initGlyphsIsShow()
{
    if (m_SonoParameters != nullptr && m_Control != nullptr)
    {
        (m_SonoParameters->pBV(BFPNames::SonoMSKStr) && m_SonoParameters->pBV(BFPNames::SonoMSKIsShowStr))
            ? m_Control->showActive()
            : m_Control->hideActive();
    }
}

void SonoMSKTool::initGlyphsControl()
{
    if (m_Control == NULL)
    {
        m_Control =
            GlyphsControlManager::instance().getChildGlyphsControl<SonoMSKGlyphsControl>(GlyphsCtl::SonoMSKGlyphsType);
    }
}
