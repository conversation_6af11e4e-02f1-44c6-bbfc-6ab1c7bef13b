#include "quickangletool.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "util.h"
#include "infostruct.h"
#include "parameter.h"
#include "menucontroller.h"
#include "toolnames.h"
#include "qvariantcommandargs.h"
QuickAngleTool::QuickAngleTool(const QString& name, SonoParameters* sonoParameters)
    : AngleAdjustBaseTool(name, sonoParameters)
    , m_index(1)
{
    setIsParameterTool(true);
}

void QuickAngleTool::onBeforeCurSonoParametersChanged()
{
    AngleAdjustBaseTool::onBeforeCurSonoParametersChanged();
    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                   this, SLOT(onStateChanged()));
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onStateChanged()));
        disconnect(m_SonoParameters->parameter(BFPNames::SyncModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onStateChanged()));
        disconnect(m_SonoParameters->parameter(BFPNames::DopplerThetaStr), &Parameter::valueChanged, this,
                   &QuickAngleTool::quickAngleValueChanged);
        disconnect(m_SonoParameters->parameter(BFPNames::DopplerThetaTDIStr), &Parameter::valueChanged, this,
                   &QuickAngleTool::quickAngleValueChanged);
        disconnect(m_SonoParameters->parameter(BFPNames::DopplerThetaCWDStr), &Parameter::valueChanged, this,
                   &QuickAngleTool::quickAngleValueChanged);
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), &Parameter::valueChanged, this,
                   &QuickAngleTool::onQuickAngleChangeWithScanMode);
    }
}

void QuickAngleTool::onCurSonoParametersChanged()
{
    AngleAdjustBaseTool::onCurSonoParametersChanged();
    if (m_SonoParameters != NULL)
    {
        connect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                this, SLOT(onStateChanged()));
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onStateChanged()));
        connect(m_SonoParameters->parameter(BFPNames::SyncModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onStateChanged()));
        connect(m_SonoParameters->parameter(BFPNames::DopplerThetaStr), &Parameter::valueChanged, this,
                &QuickAngleTool::quickAngleValueChanged);
        connect(m_SonoParameters->parameter(BFPNames::DopplerThetaTDIStr), &Parameter::valueChanged, this,
                &QuickAngleTool::quickAngleValueChanged);
        connect(m_SonoParameters->parameter(BFPNames::DopplerThetaCWDStr), &Parameter::valueChanged, this,
                &QuickAngleTool::quickAngleValueChanged);
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), &Parameter::valueChanged, this,
                &QuickAngleTool::onQuickAngleChangeWithScanMode);

        onStateChanged();
        quickAngleValueChanged(m_SonoParameters->pIV(dopplerThetaName()));
    }
}

void QuickAngleTool::onToolValueChanged(const QVariant& value)
{
    Q_UNUSED(value)
}

void QuickAngleTool::quickAngleValueChanged(const QVariant& value)
{
    bool isUpdated = false;
    int theta = value.toInt();
    for (int index = 0; index < ANGLE_COUNT; index++)
    {
        if (quickAngles[index] == theta)
        {
            m_index = index;
            isUpdated = true;
            setToolValue(QString::number(quickAngles[m_index]), true);
        }
    }

    if (!isUpdated)
    {
        setToolValue(QString::number(theta), true);

        m_index = 0;
        int minDiff = qAbs(quickAngles[0] - theta);
        for (int i = 1; i < ANGLE_COUNT; ++i)
        {
            int currentDiff = qAbs(quickAngles[i] - theta);
            if (currentDiff < minDiff)
            {
                minDiff = currentDiff;
                m_index = i;
            }
        }
    }
}

void QuickAngleTool::onStateChanged()
{
    onEnableChanged();
    quickAngleValueChanged(m_SonoParameters->pIV(dopplerThetaName()));
}

void QuickAngleTool::onQuickAngleChangeWithScanMode(const QVariant& value)
{
    if (value.toInt() == SystemScanModeBPW || value.toInt() == SystemScanModeColorPW ||
        value.toInt() == SystemScanModePowerPW || value.toInt() == SystemScanModeDPowerPW ||
        value.toInt() == SystemScanModeTissuePW || value.toInt() == SystemScanModeMVIPW ||
        value.toInt() == SystemScanModeCWD || value.toInt() == SystemScanModeCWDColorDoppler ||
        value.toInt() == SystemScanModeCWDDirectionalPowerDoppler || value.toInt() == SystemScanModeCWDPowerDoppler)
    {
        quickAngleValueChanged(m_SonoParameters->pIV(dopplerThetaName()));
    }
}

void QuickAngleTool::connectCurSonoParameters()
{
    AngleAdjustBaseTool::connectCurSonoParameters();
    if (m_SonoParameters != NULL)
    {
        connect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                this, SLOT(onStateChanged()));
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onStateChanged()));
        connect(m_SonoParameters->parameter(BFPNames::SyncModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onStateChanged()));
        connect(m_SonoParameters->parameter(BFPNames::DopplerThetaStr), &Parameter::valueChanged, this,
                &QuickAngleTool::quickAngleValueChanged);
        connect(m_SonoParameters->parameter(BFPNames::DopplerThetaTDIStr), &Parameter::valueChanged, this,
                &QuickAngleTool::quickAngleValueChanged);
        connect(m_SonoParameters->parameter(BFPNames::DopplerThetaCWDStr), &Parameter::valueChanged, this,
                &QuickAngleTool::quickAngleValueChanged);
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), &Parameter::valueChanged, this,
                &QuickAngleTool::onQuickAngleChangeWithScanMode);

        onStateChanged();
        quickAngleValueChanged(m_SonoParameters->pIV(dopplerThetaName()));
    }
}

void QuickAngleTool::execute()
{
    // IsDopplerScanLineVisible 在D模式为false，所以加上SyncMode的判断
    if (m_SonoParameters != NULL)
    {
        bool isDopLineVisible = m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr);
        bool isFrozen = m_SonoParameters->pBV(BFPNames::FreezeStr);
        bool flagD = ((SyncModeType)m_SonoParameters->pIV(BFPNames::SyncModeStr) & Sync_D) == Sync_D;
        //冻结状态限制只有pw模式可以调节
        //来源：TDI的需求文档，TVD Pre模式冻结要支持QuickAngle可调
        if ((isFrozen && flagD) || (!isFrozen && (isDopLineVisible || flagD)) || (isFrozen && isDopLineVisible))
        {
            QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
            if (args != NULL)
            {
                if (args->getType() == COMMAND_TYPE_STRING)
                {
                    int value = this->args<QVariantCommandArgs>()->getValue().toInt();
                    if (value != maxmValue())
                    {
                        value = value - value % stepValue();
                    }
                    m_SonoParameters->parameter(dopplerThetaName())->setValue(quickAngles[value]);
                    m_index = value;
                }
                else
                {
                    int index = m_index;
                    index += args->arg() ? 1 : -1;
                    m_index = qBound(0, index, ANGLE_COUNT - 1);
                }
            }
            else
            {
                int index = -1;
                int angle = m_SonoParameters->pIV(dopplerThetaName());
                for (int i = 0; i < ANGLE_COUNT; i++)
                {
                    if (angle < quickAngles[i])
                    {
                        index = i;
                        break;
                    }
                    else if (angle == quickAngles[i])
                    {
                        index = (i + 1) % ANGLE_COUNT;
                        break;
                    }

                    // angle > 60
                    if (i == (ANGLE_COUNT - 1))
                    {
                        index = 0;
                        break;
                    }
                }

                index = qBound(0, index, ANGLE_COUNT - 1);
                m_index = index;
            }
            m_SonoParameters->setPV(dopplerThetaName(), quickAngles[m_index]);
            setToolValue(QString::number(quickAngles[m_index]), true);
        }
    }
}

void QuickAngleTool::onEnableChanged()
{
    bool isDopLineVisible = m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr);
    bool flagD = ((SyncModeType)m_SonoParameters->pIV(BFPNames::SyncModeStr) & Sync_D) == Sync_D;
    if (!(isDopLineVisible || flagD))
    {
        setIsAvailable(false);
    }
    else
    {
        setIsAvailable(true);
    }
}

int QuickAngleTool::maxmValue() const
{
    return ANGLE_COUNT - 1;
}

int QuickAngleTool::minmValue() const
{
    return 0;
}

int QuickAngleTool::value() const
{
    return m_index;
}
