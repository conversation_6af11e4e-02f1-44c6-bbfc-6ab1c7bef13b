#ifndef BASESTORECOMMAND_H
#define BASESTORECOMMAND_H
#include "usfprogramcontroller_global.h"
#include "commandobject.h"
#include "mainwindowkbunit.h"
#include "ibufferstoremanager.h"

class ProgressSliderWidget;
class IStateManager;
class SonoParameters;
class MainWindowKBUnit;
class ImageSkimManager;
class IBufferStoreManager;
class ChisonUltrasoundContext;
class MeasureContext;

class USF_PROGRAM_CONTROLLER_EXPORT BaseStoreCommand : public CommandObject
{
    Q_OBJECT
public:
    BaseStoreCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager,
                     SonoParameters* sonoParameters, IMainWindow* flexBtnVisible, BaseWidget* imageClipWidget,
                     IBufferStoreManager* bufferStoreManager, ChisonUltrasoundContext* ultrasoundContext,
                     MeasureContext* measureContext);

public:
    void setFlexButtonVisible(bool isVisible);
    void setNavigationEllipseShow(bool isVisible);
    void controlGlyphsControl(bool enabled);
signals:
    void beforeSave();
    void exitSave();
protected slots:
    virtual void onCommandFinished(LoadErrorCode loadErrorCode);
    virtual void onCommandFinished(bool success);

protected:
    virtual bool checkDiskSpace();
    void execute();
    virtual void beginOperate();
    virtual void operate() = 0;
    virtual bool isFreehand3DEditExamMode();
    bool isFreeHand3DMode() const;
    virtual bool isSupportSave() const;

private:
    void doExecute();
    Q_INVOKABLE void doBlockExecute();
    void innerExecute();

protected:
    ProgressSliderWidget* m_Slider;
    IStateManager* m_StateManager;
    SonoParameters* m_RealSonoParameters;
    MainWindowKBUnit* m_FlexBtnVisible;
    ImageSkimManager* m_ImageClipWidget;
    IBufferStoreManager* m_BufferStoreManager;
    ChisonUltrasoundContext* m_ChisonUltrasoundContext;
    MeasureContext* m_MeasureContext;
    bool m_Started;
};

#endif // BASESTORECOMMAND_H
