#ifndef SONOCARDIACSECRECOGNIZETOOL_H
#define SONOCARDIACSECRECOGNIZETOOL_H
#include "sonocardiacbaseclass.h"
#include "menutool.h"

class ImageEventArgs;
class ChisonUltrasoundContext;
class MeasureContext;
//心室识别的开关，这个Tool的变更会影响最后 给到算法的参数
class USF_PROGRAM_CONTROLLER_EXPORT SonoCardiacSecRecognizeTool : public MenuTool, public SonoCardiacBaseClass
{
    Q_OBJECT
public:
    SonoCardiacSecRecognizeTool(const QString& name, ChisonUltrasoundContext* chisonUltrasoundContext,
                                MeasureContext* measureContext);

public slots:
    void onNewImage(ImageEventArgs*);
    void controlRecognize(bool);

protected:
    virtual void execute();

private:
    ChisonUltrasoundContext* m_ChisonUltrasoundContext;
    MeasureContext* m_MeasureContext;
};
#endif // SONOCARDIACSECRECOGNIZETOOL_H
