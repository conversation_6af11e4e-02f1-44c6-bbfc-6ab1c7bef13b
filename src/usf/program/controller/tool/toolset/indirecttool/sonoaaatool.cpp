#include "sonoaaatool.h"
#include "chisonultrasoundcontext.h"
#include "sonoparameters.h"
#include "menucontroller.h"
#include "menunames.h"
#include "iruler.h"
#include "imeasurement.h"
#include "appsetting.h"
#include "bfpnames.h"
#include "measurementmodelutil.h"
#include "util.h"
#include "imageinfodef.h"
#include "measurecontext.h"
#include "measurementdef.h"
#include "ibufferstoremanager.h"
#include "ilinebuffermanager.h"
#include "imageframesavethread.h"
#include "airectlinesmeasureglyphscontrol.h"
#include "toolsfactory.h"
#include "toolnames.h"
#include "storeimagecommand.h"
#include "storeimagetoudiskcommand.h"
#include "sonobuffers.h"
#include "irawimagebufferdatasetter.h"
#include "lightcontroller.h"
#include "menutools.h"
#include "bfcoordtransform.h"
#include "bfscanareawidthparameter.h"
#include "airectlinesmeasureglyphscontrol.h"
#include "sonoaaacontroller.h"
#include "cinelooper.h"
#include "imagetile.h"
#include "statemanager.h"
#include "abstractstate.h"
#include "toolsordermanager.h"
#include "touchmeasuredispather.h"
#include "screenmeasurecontroller.h"
#include "resource.h"

SonoAAATool::SonoAAATool(const QString& name, ChisonUltrasoundContext* chisonContext, SonoParameters* sonoParameters,
                         IMeasurement* packagesMeasurement, MeasureContext* context,
                         IBufferStoreManager* bufferStoreManager, ILineBufferManager* lineBufferManager,
                         CineLooper* cineLooper, ImageTile* tile, ToolsOrderManager* manager,
                         SonoAAAMessageTool* messageTool)
    : ConditionalTool(name)
    , m_RealSonoParameters(sonoParameters)
    , m_ChisonUltrasoundContext(chisonContext)
    , m_PackagesMeasurement(packagesMeasurement)
    , m_Measure(NULL)
    , m_Ruler(NULL)
    , m_MeasureContext(context)
    , m_IsCallBack(false)
    , m_CineLooper(cineLooper)
    , m_ImageTile(tile)
    , m_ToolsManager(manager)
    , m_MessageTool(messageTool)
{
    connect(bufferStoreManager, SIGNAL(savedCine(QString)), this, SLOT(doSavedCine(QString)));
    connect(lineBufferManager, SIGNAL(realTimeSaveCineState(int)), this, SLOT(doRealTimeSaveCine(int)));
    connect(this, SIGNAL(conditionalChange(bool)), this, SLOT(onConditionalChange(bool)),
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
    m_Controller = new SonoAAAController(context);
    m_MessageTool->setController(m_Controller);
    connect(this, &SonoAAATool::drawLines, m_Controller, &SonoAAAController::draw,
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
    loadMeasurement();

    m_IsEnableWhenMLineShow = false;
    m_IsEnableWhenPwLineShow = false;
    m_SupportScanMode.append(SystemScanModeB);
    m_SupportLayout.append(Layout_1x1);
    QStringList list = Resource::functionalConfigurationSettingValue("SonoAAA", Resource::ValueType::ProbeId);
    bool ok = false;
    for (int i = 0; i < list.size(); i++)
    {
        // 3C,4C
        int num = list[i].toInt(&ok);
        if (ok)
        {
            m_SupportProbeId.append(num);
        }
    }

    m_SupportExamMode = Resource::functionalConfigurationSettingValue("SonoAAA", Resource::ValueType::ExamMode);
    m_Item = LicenseItemKey::KeySonoAAA;
    m_License = HAVELICENSE;
}

void SonoAAATool::execute()
{
    MenuTool::execute();
    calValidArea();
    if (m_SonoParameters->pBV(BFPNames::SonoAAAStr))
    {
        if (!m_RealSonoParameters->pBV(BFPNames::FreezeStr))
        {
            controlLight(true);
            m_Controller->setStart(true);
            m_SonoParameters->setPV(BFPNames::SonoAAAMessageStr, true);
            MenuController::instance().createLeftMenu(MenuNames::SonoAAAMenuStr);
            dynamic_cast<SonoBuffers*>(m_ChisonUltrasoundContext->lineBuffer())->activeDataSetter()->clear();
            m_Ruler->setIsFreezeRun(false);
            startRTMeasure();
        }
        else
        {
            ToolsFactory::instance().tool(ToolNames::FreezeSonoAAAStr)->run();
        }
        m_Controller->setNoteVisible(m_MessageTool->value());
    }
    else
    {
        controlLight(false);
        MeasurementModelUtil::stopMeasurement();
        ToolsFactory::instance().command(ToolNames::DeleteGlyphsStr)->run();
        if (m_Ruler == NULL)
        {
            loadMeasurement();
        }
        m_Controller->setStart(false);
        m_Controller->setNoteVisible(false);
        m_Ruler->stop();
        metaObject()->invokeMethod(this, "refreshMenu", Qt::QueuedConnection);
        disconnectSignals();

        m_SonoParameters->setPV(BFPNames::SonoAAAMessageStr, false);
        m_Controller->setNoteVisible(m_MessageTool->value());
    }
}

void SonoAAATool::onSwitchRTImage(bool isConnect)
{
    if (m_Controller->easyViewControl() != NULL)
    {
        return;
    }

    m_Ruler->setIsFreezeRun(false);
    m_Controller->setNoteVisible(m_MessageTool->value());

    if (!isConnect & (!m_RealSonoParameters->pBV(BFPNames::FreezeStr) && m_SonoParameters->pBV(BFPNames::SonoAAAStr)))
    {
        m_Controller->setNoteVisible(false);
    }

    Util::connectSignal(m_MeasureContext, SIGNAL(newImage(ImageEventArgs*)), this,
                        SLOT(processNewImage(ImageEventArgs*)), isConnect,
                        Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
}

void SonoAAATool::processNewImage(ImageEventArgs* args)
{
    if (nullptr == m_SonoParameters || !m_SonoParameters->pBV(BFPNames::SonoAAAStr) || !m_Controller->start() ||
        m_SonoParameters->pIV(BFPNames::LayoutStr) > Layout_1x1)
    {
        return;
    }
    calValidArea();
    m_Ruler->setFrameTimeStamp(args->getFrameStartTime());
    if (m_IsCallBack)
    {
        QVector<int> res = m_Controller->control()->fileResultMap().value(args->getFrameStartTime());
        if (res.length() > 1)
        {
            m_MeasureContext->setFMN(res[1]);
        }
        emit drawLines(res, args->getFrameStartTime());
    }
    else if (args->imageType() == ImageEventArgs::ImageB)
    {
        PainterImageModel imageModel;
        imageModel.setData(args);
        PainterImageData* imageData = imageModel.data();
        QImage img(imageData->m_Data, imageData->m_Width, imageData->m_Height, QImage::Format_RGB32);
        QImage deepCopy = img.copy();
        m_MeasureContext->setImageWithoutValidate(deepCopy);
        QVector<int> res = m_Ruler->runAlg();
        emit drawLines(res, args->getFrameStartTime());
    }
}

void SonoAAATool::processNewImageInEasyview(ImageEventArgs* args)
{
    if (nullptr == m_EasyViewSonoParameters || !m_EasyViewSonoParameters->pBV(BFPNames::SonoAAAStr))
    {
        return;
    }

    QVector<int> res = m_Controller->easyViewControl()->fileResultMap().value(args->getFrameStartTime());
    if (res.length() > 1)
    {
        m_MeasureContext->setFMN(res[1]);
    }
    emit drawLines(res, args->getFrameStartTime());
}

void SonoAAATool::setFMN()
{
    if (m_Measure == NULL)
    {
        loadMeasurement();
    }
    m_Measure->setFarMidNear(m_MeasureContext->fMN());
    m_Controller->control()->setFmn(m_MeasureContext->fMN());
    MeasurementModelUtil::changeMeasureAttribute();
}

void SonoAAATool::onFreezeChanged(QVariant var)
{
    //冻结到实时 关闭
    if (!m_RealSonoParameters->pBV(BFPNames::FreezeStr) && m_SonoParameters->pBV(BFPNames::SonoAAAStr))
    {
        run();
    }
    //冻结
    if (m_RealSonoParameters->pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime())
    {
        m_IsCallBack = true;
        controlLight(false);
    }
    else
    {
        m_SonoParameters->setPV(BFPNames::SonoAAAMessageStr, false);
        m_Controller->setNoteVisible(m_MessageTool->value());
    }
    ConditionalTool::onFreezeChanged(var);
}

void SonoAAATool::doSavedCine(const QString& filename)
{
    //冻结存 或者 实时存
    if (m_RealSonoParameters->pBV(BFPNames::SonoAAAStr))
    {
        if (m_RealSonoParameters->pBV(BFPNames::FreezeStr))
        {
            saveData(filename);
        }
        else
        {
            m_FileName = filename;
        }
    }
}

void SonoAAATool::doRealTimeSaveCine(int state)
{
    if (state == ImageFrameSaveThread::Finished)
    {
        saveData(m_FileName);
    }
}

void SonoAAATool::saveData(const QString& filename)
{
    m_Controller->doSavedCine(filename);
}

void SonoAAATool::saveImage()
{
    if (m_Measure == NULL)
    {
        return;
    }
    m_Measure->onBeforeSave();
    m_Measure->updateAllMeasureResult(m_Measure->leftRight(), m_Measure->farMidNear(), m_Measure->fetusIndex(), true);
}

void SonoAAATool::onConditionalChange(bool enable)
{
    if (!enable && value() != 0)
    {
        run();
    }
}

void SonoAAATool::onCurSonoParametersChanged()
{
    if (m_SonoParameters != NULL && m_SonoParameters->pBV(BFPNames::SonoAAAStr))
    {
        controlLight(false);
        MeasurementModelUtil::stopMeasurement();
        if (m_Ruler == NULL)
        {
            loadMeasurement();
        }
        m_Controller->setStart(false);
        disconnectSignals();
        m_Ruler->stop();
    }
    if (m_Controller->control() != nullptr)
    {
        m_Controller->control()->hideAnchor();
    }

    ConditionalTool::onCurSonoParametersChanged();
}

void SonoAAATool::changeMouseTool()
{
    m_ToolsManager->stopAllTool();

    ICommand* command = ToolsFactory::instance().command(ToolNames::MeasureMouseStr);
    command->run();
}

void SonoAAATool::calValidArea()
{
    BFCoordTransform bf(m_SonoParameters);
    QPoint leftBottom;
    bf.convertPhysicsToLogic(m_SonoParameters->pIV(BFPNames::StartLineStr), m_SonoParameters->pDV(BFPNames::DepthMMStr),
                             leftBottom);
    QSize imageSize = m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    leftBottom.setX(leftBottom.x() + imageSize.width() / 2);

    QPoint leftTop;
    bf.convertPhysicsToLogic(m_SonoParameters->pIV(BFPNames::StartLineStr), 0, leftTop);
    leftTop.setX(leftBottom.x());

    QPoint rightBottom;
    bf.convertPhysicsToLogic(m_SonoParameters->pIV(BFPNames::StopLineStr), m_SonoParameters->pDV(BFPNames::DepthMMStr),
                             rightBottom);
    rightBottom.setX(rightBottom.x() + imageSize.width() / 2);

    if (m_Ruler == NULL)
    {
        loadMeasurement();
    }
    if (leftTop.x() < 0)
    {
        leftTop.setX(0);
    }
    if (leftTop.y() < 0)
    {
        leftTop.setY(0);
    }
    int realwidth = rightBottom.x() - leftTop.x();
    if (realwidth > imageSize.width())
    {
        realwidth = imageSize.width() - leftTop.x();
    }
    int realHeight = rightBottom.y() - leftTop.y();
    if (realHeight > imageSize.height())
    {
        realHeight = imageSize.height() - leftTop.y();
    }

    m_Ruler->setImageValidArea(QRect(leftTop, QSize(realwidth, realHeight)));
}

void SonoAAATool::refreshMenu()
{
    if (!m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        ChangeTool* change = dynamic_cast<ChangeTool*>(ToolsFactory::instance().tool(ToolNames::ChangeStr));
        if (change != NULL && !change->getCurrentMenus().isEmpty())
            MenuController::instance().createLeftMenu(change->getCurrentMenus().at(0));
    }
    else
    {
        PostChangeTool* postChange =
            dynamic_cast<PostChangeTool*>(ToolsFactory::instance().tool(ToolNames::PostChangeStr));
        if (postChange != NULL && !postChange->getCurrentMenus().isEmpty())
            MenuController::instance().createLeftMenu(postChange->getCurrentMenus().at(0));
    }
}

void SonoAAATool::runAfterStopLoop()
{
    calValidArea();
    //冻结下 先暂停电影播放
    if (m_Controller->control()->isEmpty())
    {
        if (!m_MessageTool->value())
        {
            m_MessageTool->run();
        }
        controlLight(true);
        m_Ruler->setFrameTimeStamp(m_MeasureContext->getFramestampByIndex(
            dynamic_cast<SonoBuffers*>(m_ChisonUltrasoundContext->lineBuffer())->currentIndex()));
        m_MeasureContext->setImageWithoutValidate(m_ImageTile->currentImage(ImageEventArgs::ImageB));
        m_Ruler->setIsFreezeRun(true);
        startRTMeasure();
        controlLight(false);
        m_Ruler->setIsFreezeRun(false);
    }
    else if (!m_Controller->control()->isEmpty() && !m_Controller->control()->beginEdit())
    {
        startRTMeasureForEdit();
        m_Ruler->setFrameTimeStamp(m_MeasureContext->getFramestampByIndex(
            dynamic_cast<SonoBuffers*>(m_ChisonUltrasoundContext->lineBuffer())->currentIndex()));
    }

    MenuController::instance().createLeftMenu(MenuNames::SonoAAAMenuStr);
}

void SonoAAATool::loadMeasurement()
{
    IMeasurement* measurement = m_PackagesMeasurement->findChildById(loadRulerId());
    if (measurement == NULL)
    {
        return;
    }
    m_Measure = measurement;
    if (m_Measure == NULL)
    {
        return;
    }
    if (m_Measure != NULL)
    {
        m_Ruler = m_Measure->currentRuler();
        if (m_Ruler != NULL)
        {
            m_Controller->setRuler(m_Ruler);
            m_Ruler->setEnabled(true);
        }
    }
}

void SonoAAATool::startRTMeasure(QString path)
{
    if (m_Measure == nullptr)
    {
        loadMeasurement();
    }
    if (m_Measure == nullptr)
    {
        return;
    }
    ToolsFactory::instance().command(ToolNames::DeleteGlyphsStr)->run();
    m_Measure->setFarMidNear(Measurement::FMN_Near);
    m_MeasureContext->setFMN(Measurement::FMN_Near);
    m_Controller->control()->fileResultMap().clear();
    if (path.isEmpty())
    {
        m_IsCallBack = false;
    }
    else
    {
        m_Controller->startRTMeasure(path);
        m_IsCallBack = true;
    }
    m_Controller->control()->setBeginEdit(false);
    m_Controller->setStart(true);
    m_Ruler->setEnabled(true);
    connectSignals();
    StoreImageCommand* cmd1 =
        dynamic_cast<StoreImageCommand*>(ToolsFactory::instance().command(ToolNames::StoreImageStr));
    StoreImageCommand* cmd2 =
        dynamic_cast<StoreImageToUDiskCommand*>(ToolsFactory::instance().command(ToolNames::StoreImageToUDiskStr));
    connect(cmd1, SIGNAL(beforeSave()), this, SLOT(saveImage()), Qt::UniqueConnection);
    connect(cmd2, SIGNAL(beforeSave()), this, SLOT(saveImage()), Qt::UniqueConnection);
    MeasurementModelUtil::runMeasurement(m_Measure, false);
}

void SonoAAATool::startRTMeasureForEdit()
{
    if (m_Measure == nullptr)
    {
        loadMeasurement();
    }
    if (m_Measure == nullptr)
    {
        return;
    }
    disconnect(m_MeasureContext, &MeasureContext::editFinish, m_Controller, &SonoAAAController::endEdit);
    changeMouseTool();
    m_IsCallBack = true;
    m_Controller->control()->setBeginEdit(true);
    m_Ruler->setEnabled(true);
    connectSignals();
    StoreImageCommand* cmd1 =
        dynamic_cast<StoreImageCommand*>(ToolsFactory::instance().command(ToolNames::StoreImageStr));
    StoreImageCommand* cmd2 =
        dynamic_cast<StoreImageToUDiskCommand*>(ToolsFactory::instance().command(ToolNames::StoreImageToUDiskStr));
    connect(cmd1, SIGNAL(beforeSave()), this, SLOT(saveImage()), Qt::UniqueConnection);
    connect(cmd2, SIGNAL(beforeSave()), this, SLOT(saveImage()), Qt::UniqueConnection);
    m_Measure->setFarMidNear(m_Controller->control()->fmn());
    MeasurementModelUtil::runMeasurement(m_Measure);
    connect(m_MeasureContext, &MeasureContext::editFinish, m_Controller, &SonoAAAController::endEdit,
            Qt::UniqueConnection);
}

void SonoAAATool::controlLight(bool b)
{
    LightController::instance().light(LightNames::AIStr, b);
}

void SonoAAATool::updateResult()
{
    if (m_Ruler == NULL)
        return;

    m_Ruler->updateResult();
}

void SonoAAATool::setEasyViewSonoParameters(SonoParameters* sono)
{
    m_EasyViewSonoParameters = sono;
}

void SonoAAATool::setEasyViewControl(AIRectLinesMeasureGlyphsControl* control)
{
    m_Controller->setEasyViewControl(control);
    if (m_Ruler == NULL)
    {
        loadMeasurement();
    }
    m_Ruler->setControl(control);
}

void SonoAAATool::easyViewLoadData(QString path)
{
    m_Controller->startRTMeasure(path);
}

void SonoAAATool::runMeasureInEasyView()
{
    if (m_Ruler == NULL)
    {
        loadMeasurement();
    }
    m_Ruler->setEnabled(true);
    m_Controller->setStart(true);

    connectSignals();
    MeasurementModelUtil::runMeasurement(m_Measure, false);
}

void SonoAAATool::setControl(AIRectLinesMeasureGlyphsControl* control)
{
    m_Ruler->setControl(control);
}

void SonoAAATool::runAtFreeze()
{
    if (m_CineLooper != NULL && m_CineLooper->isLooping())
    {
        m_CineLooper->stopLoop();
    }
    metaObject()->invokeMethod(this, "runAfterStopLoop", Qt::QueuedConnection);
}

void SonoAAATool::setScreenMeasureController(IScreenMeasureController* screenMeasureController)
{
    m_Controller->setScreenMeasureController(screenMeasureController);
}

void SonoAAATool::connectSignals()
{
    connect(m_Ruler, &IRuler::drawLines, m_Controller, &SonoAAAController::draw,
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
    connect(m_Ruler, SIGNAL(receiveRTImage(bool)), this, SLOT(onSwitchRTImage(bool)),
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
    connect(m_MeasureContext, &MeasureContext::fmnChanged, this, &SonoAAATool::setFMN, Qt::UniqueConnection);
}

void SonoAAATool::disconnectSignals()
{
    disconnect(m_Ruler, &IRuler::drawLines, m_Controller, &SonoAAAController::draw);
    disconnect(m_Ruler, SIGNAL(receiveRTImage(bool)), this, SLOT(onSwitchRTImage(bool)));
    disconnect(m_MeasureContext, &MeasureContext::fmnChanged, this, &SonoAAATool::setFMN);
}

QString SonoAAATool::loadRulerId()
{
    QStringList id = Resource::functionalConfigurationSettingValue("SonoAAA", Resource::ValueType::MeasurementId);
    if (id.isEmpty())
        return "911842b1-d0fbd3-ec1a93e2";
    else
        return id.first();
}

SonoAAAFmnTool::SonoAAAFmnTool(const QString& name, int fmn, ChisonUltrasoundContext* chisonContext,
                               MeasureContext* context, SonoParameters* sonoParameters)
    : MenuTool(name)
    , m_ChisonUltrasoundContext(chisonContext)
    , m_MeasureContext(context)
    , m_RealSonoParameters(sonoParameters)
{
    m_Tool = dynamic_cast<SonoAAATool*>(ToolsFactory::instance().tool(ToolNames::SonoAAAStr));
    m_Fmn = fmn;
}

void SonoAAAFmnTool::execute()
{
    MenuTool::execute();
    m_MeasureContext->setFMN(m_Fmn);

    if (m_RealSonoParameters->pBV(BFPNames::FreezeStr))
    {
        m_Tool->updateResult();
    }
}

FreezeSonoAAA::FreezeSonoAAA(const QString& name, SonoAAATool* tool, QObject* parent)
    : MenuTool(name, parent)
    , m_SonoAAA(tool)
{
}

void FreezeSonoAAA::execute()
{
    m_SonoAAA->runAtFreeze();
}

SonoAAAMessageTool::SonoAAAMessageTool(const QString& name)
    : MenuTool(name)
    , m_Controller(NULL)
{
}

void SonoAAAMessageTool::execute()
{
    MenuTool::execute();
    if (m_Controller == NULL)
    {
        return;
    }

    m_Controller->setNoteVisible(value());
}

void SonoAAAMessageTool::onCurSonoParametersChanged()
{
    if (m_SonoParameters != NULL)
    {
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreezeChanged(QVariant)), Qt::UniqueConnection);
    }

    MenuTool::onCurSonoParametersChanged();

    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFreezeChanged(QVariant)));
    }

    m_Controller->setNoteVisible(m_SonoParameters->pBV(BFPNames::SonoAAAMessageStr));
}

void SonoAAAMessageTool::onFreezeChanged(QVariant)
{
    if (value() && m_SonoParameters->pBV(BFPNames::SonoAAAStr))
    {
        m_Controller->setNoteVisible(true);
    }
    else
    {
        m_Controller->setNoteVisible(false);
    }
}

void SonoAAAMessageTool::setController(SonoAAAController* Controller)
{
    m_Controller = Controller;
}
