#include "loadcommand.h"
#include "ibufferstoremanager.h"
#include "stringcommandargs.h"
#include "progresssliderwidget.h"
#include "istatemanager.h"
#include "mousekeyboardinputeventfilter.h"
#include "stateeventnames.h"
#include "imageskimmanager.h"
#include "imagelabelinfo.h"
#include "resource.h"
#include "util.h"

#include "imagetile.h"
#include "iimagewidget.h"
#include "thumbnaildialog.h"
#include "bfpnames.h"

#include <QFile>
#include <QObject>
#include <QFileDialog>
#ifdef USE_FREEHAND3D
#include "freehand3dproxy.h"
#endif
#include "ilinebuffermanager.h"
#include "menucontroller.h"
#include "descrip/menunames.h"
#include "modelconfig.h"
#include "chisonultrasoundcontext.h"
#include "messageboxframe.h"
#include "mainwindowmiddledownwidget.h"
#include "imageskimmanager.h"
#include "toolsfactory.h"
#include "toolnames.h"
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, LoadCommand)

LoadCommand::LoadCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager,
                         SonoParameters* sonoParameters, IMainWindow* flexBtnVisible, BaseWidget* imageClipWidget,
                         IBufferStoreManager* bufferStoreManager, ChisonUltrasoundContext* chisonContext,
                         MeasureContext* measureContext, ImageTile* imageTile, BaseBkWidget* thumbnailDialog)
    : BaseStoreCommand(name, slider, stateManager, sonoParameters, flexBtnVisible, imageClipWidget, bufferStoreManager,
                       chisonContext, measureContext)
    , m_ChisonUltrasoundContext(chisonContext)
    , m_ImageTile(imageTile)
    , m_ThumbnailDialog(dynamic_cast<ThumbnailDialog*>(thumbnailDialog))
    , m_CurSonoparameters(m_RealSonoParameters)
    , m_FunctionAvailable(true)
    , m_ClickedButton(Qt::NoButton)
#ifdef USE_FREEHAND3D
    , m_FreeHand3DProxy(context->freeHand3DProxy())
#endif
{
    connect(m_ImageClipWidget, SIGNAL(selectedInfoChanged(IimageInfo*)), this,
            SLOT(onSelectedInfoChanged(IimageInfo*)));
    connect(this, SIGNAL(startLoad()), m_BufferStoreManager, SIGNAL(startLoad()));

    qRegisterMetaType<LoadErrorCode>("LoadErrorCode");
}

void LoadCommand::beginOperate()
{
    ApplicationInfo::instance().toggleGrab();

    MenuController::instance().setCurvedMenu(false);

    // load图像时(此时有可能是实时状态)，先暂停刷新图像，避免出现图像的过度状态
    m_ImageTile->beginPause();
    m_StateManager->postEvent(StateEventNames::BeginLoad);
    m_ChisonUltrasoundContext->prepareLoadImage();

    // 此行代码确保状态机执行完毕 BeginLoad 事件，否则如果测量激活状态，进行回调操作，会导致测量状态exit中的stop操作和
    // BufferStoreManager 中的清屏操作冲突而崩溃
    // 增加 ExcludeUserInputEvents 避免此时还可以点击图片回调，导致状态不正常而无法退出状态
    QList<QEvent::Type> fileteredEvent;
    fileteredEvent.append(QEvent::MouseButtonPress);
    fileteredEvent.append(QEvent::MouseButtonDblClick);
    MouseKeyboardInputEventFilter::getInstance().setFilterEvents(fileteredEvent);
    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
    fileteredEvent.clear();
    MouseKeyboardInputEventFilter::getInstance().setFilterEvents(fileteredEvent);
}

void LoadCommand::operate()
{
    //    QString fileName = QFileDialog::getOpenFileName(NULL, QObject::tr("Open File"),
    //                                                            ".", QObject::tr("Frames (*.cin *.img)"));
    //    StringCommandArgs strArgs(fileName);
    //    setArgs(&strArgs);
    connect(m_BufferStoreManager, &IBufferStoreManager::loaded, this, &LoadCommand::onCommandFinished,
            Qt::QueuedConnection);

    StringCommandArgs* s = args<StringCommandArgs>();
    if (s != NULL)
    {
        if (ModelConfig::instance().value(ModelConfig::NeedShowLoadingSlider, false).toBool())
        {
            m_Slider->showLoadingSlider();
        }
        m_BufferStoreManager->asyncLoad(s->arg());
    }
}

void LoadCommand::handleLoadState(LoadErrorCode loadErrorCode)
{
    log()->info(QString("LoadCommand::handleLoadState  LoadErrorCode:%1").arg(getLoadErrorMessage(loadErrorCode)));

    QMessageBox::StandardButton ret;
    switch (loadErrorCode)
    {
    case LoadErrorCode::Success:
        return;
    case LoadErrorCode::FileNotFound:
    case LoadErrorCode::Image_bin_OpenFiled:
    case LoadErrorCode::Image_bin_FileSizeError:
    case LoadErrorCode::Image_bin_MapError:
    case LoadErrorCode::Headinfo_bin_OpenFailed:
    case LoadErrorCode::Wholeindex_bin_OpenFailed:
    case LoadErrorCode::Frameinfo_bin_OpenFailed:
    case LoadErrorCode::Sonoparameters_bin_OpenFailed:
    case LoadErrorCode::Imageframeinfo_bin_OpenFailed:
    case LoadErrorCode::Layoutinfo_bin_LoadFailed:
    case LoadErrorCode::Cin_LoadFailed:
        ret = MessageBoxFrame::information(
            NULL, tr("Warning"),
            tr("Cannot recall the selected file,file may be corrupted! Do you want to delete corrupt file?"),
            QMessageBox::Ok | QMessageBox::Cancel);
        break;
    case LoadErrorCode::Sonoparameters_bin_UnsupportProbeCine:
        ret = MessageBoxFrame::tipInformation(NULL, tr("Warning"),
                                              tr("The probe detected in the callback image is incompatible with the "
                                                 "current software version. Please upgrade to the latest version."));
        break;
    }
    if (ret == QMessageBox::Ok && loadErrorCode != LoadErrorCode::Sonoparameters_bin_UnsupportProbeCine)
    {
        ToolsFactory::instance().command(ToolNames::FreezeImageRemoveStr)->run();
    }
    else
    {
        m_ImageClipWidget->setSelections(QStringList());
    }
}

void LoadCommand::onCommandFinished(LoadErrorCode loadErrorCode)
{
    ApplicationInfo::instance().toggleGrab();

    if (m_RealSonoParameters->pBV(BFPNames::FreeHand3DRoiOnStr))
    {
        m_RealSonoParameters->setPV(BFPNames::FreeHand3DRoiOnStr, false);
    }
    disconnect(m_BufferStoreManager, &IBufferStoreManager::loaded, this, &LoadCommand::onCommandFinished);
#ifdef USE_FREEHAND3D
    if (m_FreeHand3DProxy->isOpenFreeHand3DMenu() && m_LineBufferManager->frameCount() > 1)
    {
        MenuController::instance().createMenu(MenuNames::Freeze3DMenuStr);
    }
    else
    {
        MenuController::instance().createMenu(MenuNames::FreezeMenuStr);
        m_FreeHand3DProxy->hideFreeHand3DRoi();
    }
#endif
    BaseStoreCommand::onCommandFinished(loadErrorCode);

    // load结束时结束暂停
    m_ImageTile->endPause();

    handleLoadState(loadErrorCode);
}

void LoadCommand::onSelectedInfoChanged(IimageInfo* iInfo)
{
    if (!m_FunctionAvailable || isFreeHand3DMode() || iInfo == NULL)
    {
        // BUG15828，11125 beginOperate()函数中运行到processEvents之后会重新进入此函数,最终结果导致operate()函数无法
        //正常运行,无法进行部分按键操作,加入m_FunctionAvailable变量,使只有此函数完全运行完之后才能再次进入
        return;
    }
    m_FunctionAvailable = false;
    QString rawDataFileName = Util::getSameBaseNamesFileName(iInfo->imagePath(), Resource::imgExt);
    QString bmpDataFileName = Util::getSameBaseNamesFileName(iInfo->imagePath(), Resource::bmpExt);
    QString pngDataFileName = Util::getSameBaseNamesFileName(iInfo->imagePath(), Resource::calcExt);

    m_BufferStoreManager->setIsShowThumbnail(false);
    m_BufferStoreManager->setIsShowSavedGlyph(true);
    m_ThumbnailDialog->hide();

    m_ImageClipWidget->hideImageSkimExpandButton();

    // stressecho在分析页面截图不支持回调
    if (bmpDataFileName.contains(Resource::justPicSuffix))
    {
        MessageBoxFrame::tipInformationNonModal(NULL, "", tr("This image does not support recall."));
        m_FunctionAvailable = true;
        return;
    }

    if (!QFile::exists(rawDataFileName)) // cine
    {
        rawDataFileName = Util::getSameBaseNamesFileName(iInfo->imagePath(), Resource::cineExt);
        if (!QFile::exists(rawDataFileName))
        {
            m_FunctionAvailable = true;
            return;
        }
    }
    else // img
    {
        m_ClickedButton = iInfo->mousePressButton();
        if (!m_CurSonoparameters->pBV(BFPNames::IsFullScreenZoomInStr))
        {
            m_BufferStoreManager->setIsShowThumbnail(true);
            if (QFile::exists(pngDataFileName))
            {
                m_ThumbnailDialog->setThumbnailFileName(pngDataFileName);
                m_ThumbnailDialog->setPicResize(false);
            }
            else if (QFile::exists(bmpDataFileName))
            {
                m_ThumbnailDialog->setThumbnailFileName(bmpDataFileName);
                m_ThumbnailDialog->setPicResize(true);
            }
            else
            {
                m_BufferStoreManager->setIsShowThumbnail(false);
            }
        }
    }

    StringCommandArgs strArgs(rawDataFileName);
    setArgs(&strArgs);
    run();
    setArgs(NULL);
    m_FunctionAvailable = true;
}
