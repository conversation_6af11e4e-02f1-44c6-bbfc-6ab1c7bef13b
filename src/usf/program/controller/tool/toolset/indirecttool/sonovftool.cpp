#include "sonovftool.h"
#include "abstractstate.h"
#include "appsetting.h"
#include "autoeftracemeasureruler.h"
#include "bfpnames.h"
#include "bufferstoremanager.h"
#include "chisonultrasoundcontext.h"
#include "lightcontroller.h"
#include "linebuffermanager.h"
#include "measmeasurement.h"
#include "menucontroller.h"
#include "packagesmeasurement.h"
#include "parameter.h"
#include "probedataset.h"
#include "resource.h"
#include "rulerfactory.h"
#include "sonoparameters.h"
#include "statemanager.h"
#include "toolnames.h"
#include "touchmeasuredispather.h"
#include "util.h"
#include "toolsfactory.h"
#include "sonocardiactool.h"
#include "imagetile.h"
#include "iultrasoundview.h"
#include "measurecontext.h"
#include "cinelooper.h"
#include "iruler.h"
#include "messageboxframe.h"
#include "measurestaterecorder.h"
#include "stateeventnames.h"
#include "statefilterlocker.h"
#include "ilinebuffer.h"
#include "quadplexmodetool.h"

#define SONOVFLINEMEASUREID "911842b1-0dc352-9da347e5"  // AutoLine
#define SONOVFTRACEMEASUREID "911842b1-c453c5-061e654c" // AutoTrace

SonoVFTool::SonoVFTool(const QString& name, CineLooper* c, IMeasureDispather* measureDispather, ImageTile* tile,
                       MeasureContext* measContext, IMeasurement* packages, ChisonUltrasoundContext* ultrasoundContext,
                       IStateManager* stateManager, SonoParameters* sonoParameters)
    : BaseRTMeasurementTool(name)
    , m_MeasureTrace(NULL)
    , m_MeasureLine(NULL)
    , m_Looper(c)
    , m_PackagesMeasurement(packages)
    , m_MeasureDispather(dynamic_cast<BaseMeasureDispather*>(measureDispather))
    , m_ImageTile(tile)
    , m_MeasureContext(measContext)
    , m_ChisonUltrasoundContext(ultrasoundContext)
    , m_StateManager(stateManager)
    , m_RealSonoParameters(sonoParameters)
    , m_LineRuler(NULL)
    , m_TraceRuler(NULL)
    , m_NotingState(nullptr)
    , m_IsOnlyBData(true)
    , m_IsQuadplexMode(false)
{
    setIsParameterTool(true);
    setIsPostProcessTool(true);
    m_RefreshTimer.setSingleShot(true);
    m_RefreshTimer.setInterval(500);
    connect(&m_RefreshTimer, SIGNAL(timeout()), this, SLOT(onExecuteMesurement()));
    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this, &SonoVFTool::licenseOpen);
    m_SupportExamMode = Resource::functionalConfigurationSettingValue("SonoVF", Resource::ValueType::ExamMode);
}

void SonoVFTool::loadMeasurement()
{
    if (!AppSetting::isHuman())
    {
        return;
    }

    QStringList id = Resource::functionalConfigurationSettingValue("SonoVFLine", Resource::ValueType::MeasurementId);
    if (id.isEmpty())
    {
        id.append(SONOVFLINEMEASUREID);
    }
    IMeasurement* measurement = m_PackagesMeasurement->findChildById(id.first());

    if (measurement == NULL)
    {
        return;
    }
    m_MeasureLine = measurement;
    m_LineRuler = m_MeasureLine->currentRuler();

    id = Resource::functionalConfigurationSettingValue("SonoVFTrace", Resource::ValueType::MeasurementId);
    if (id.isEmpty())
    {
        id.append(SONOVFTRACEMEASUREID);
    }
    measurement = m_PackagesMeasurement->findChildById(id.first());

    if (measurement == NULL)
    {
        return;
    }
    m_MeasureTrace = measurement;
    m_TraceRuler = m_MeasureTrace->currentRuler();
}

void SonoVFTool::onSetSonoParameters()
{
    if (m_SonoParameters == NULL)
        return;

    connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreezeChanged(QVariant)), Qt::UniqueConnection);
    connect(m_SonoParameters->parameter(BFPNames::ExamModeIdStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onExamModeIdChanged(QVariant)), Qt::UniqueConnection);
    connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onSystemScanModeChanged(QVariant)), Qt::UniqueConnection);

    setIsVisible(isToolVisiable());
}

void SonoVFTool::runMeasure()
{
    ToolsFactory::instance().command(ToolNames::MeasurementDeleteStr)->run();
    if (m_MeasureLine != nullptr)
    {
        m_MeasureDispather->run(m_MeasureLine, false);
    }
    if (m_MeasureTrace != nullptr)
    {
        m_MeasureDispather->run(m_MeasureTrace, false);
    }

    if (!m_LineRuler->executeSuccessful())
    {
        MessageBoxFrame::tipInformationNonModal(tr("Unable to identify vessel diameter."));
    }
    else if (!m_TraceRuler->executeSuccessful())
    {
        MessageBoxFrame::tipInformationNonModal(tr("Unable to identify the trace."));
    }

    if (!m_IsOnlyBData)
    {
        Util::connectSignal(m_ChisonUltrasoundContext, SIGNAL(wholeGrayImage(void*, int, int)), this,
                            SLOT(onNewBImage(void*, int, int)), false, Qt::UniqueConnection);
        m_SonoParameters->setPV(BFPNames::ColorMappedBDataCallbackEnableStr, false);
    }
}

void SonoVFTool::setQuadplexMode(bool value)
{
    QuadplexModeController* control =
        dynamic_cast<QuadplexModeTool*>(ToolsFactory::instance().tool(BFPNames::QuadplexModeStr))->getController();
    control->setModeOn(value);
    m_RealSonoParameters->setPV(BFPNames::QuadplexModeStr, value);
}

void SonoVFTool::onExamModeIdChanged(const QVariant& value)
{
    setIsVisible(isToolVisiable());
}

void SonoVFTool::onSystemScanModeChanged(const QVariant&)
{
    setIsVisible(isToolVisiable());
}

void SonoVFTool::onNewBImage(void* data, int width, int height)
{
    QImage img((uchar*)data, width, height, QImage::Format_RGBA8888);
    m_MeasureContext->setBImage(img);

    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
    m_RefreshTimer.start();
    if (m_NotingState == nullptr)
    {
        m_NotingState = new StateFilterLocker(m_StateManager, StateEventNames::NothingStateFilter());
    }
}

void SonoVFTool::onExecuteMesurement()
{
    if (m_IsQuadplexMode)
    {
        setQuadplexMode(false);
    }

    //    if (m_IsOnlyBData)
    //    {
    //        m_MeasureContext->setBImage(m_ImageTile->currentImage(ImageEventArgs::ImageB),
    //                                    m_ChisonUltrasoundContext->sonoParameters()->pIV(BFPNames::ActiveBStr));
    //    }

    //    m_MeasureContext->setDImage(m_ImageTile->currentImage(ImageEventArgs::ImageD));
    runMeasure();

    if (m_NotingState != nullptr)
    {
        delete m_NotingState;
        m_NotingState = nullptr;
    }
}

void SonoVFTool::onFreezeChanged(QVariant)
{
    setIsVisible(isToolVisiable());
}

bool SonoVFTool::isToolVisiable()
{
    bool isEnable = true;
    //单B模式
    SystemScanMode mode = (SystemScanMode)m_SonoParameters->pIV(BFPNames::SystemScanModeStr);
    int layout = m_SonoParameters->pIV(BFPNames::LayoutStr);

    if (layout != Layout_1x1)
    {
        isEnable = false;
    }
    if (mode != SystemScanModeBPW && mode != SystemScanModeColorPW)
    {
        isEnable = false;
    }

    QString presetName = m_SonoParameters->pSV(BFPNames::ExamModeIdStr);
    if (!m_SupportExamMode.contains(presetName))
    {
        isEnable = false;
    }

    //线阵
    int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(probeId);
    if (!probeDataInfo.IsLinear)
    {
        isEnable = false;
    }

    if (!AppSetting::isHuman())
    {
        isEnable = false;
    }

    isEnable = isEnable && AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoVF);

    return isEnable;
}
void SonoVFTool::licenseOpen()
{
    setIsVisible(isToolVisiable());
}

void SonoVFTool::onInitialize()
{
    MenuTool::onInitialize();
    loadMeasurement();
}

void SonoVFTool::execute()
{
    MeasureStateRecorder::instance().setIsNewGroup(true);

    m_IsOnlyBData =
        ((SystemScanMode)m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanMode::SystemScanModeBPW);

    if (!m_SonoParameters->pBV(BFPNames::FreezeStr) && m_SonoParameters->isRealTime())
    {
        StateManager::getInstance().postEvent(StateEventNames::Freeze);

        m_IsQuadplexMode = m_SonoParameters->pBV(BFPNames::QuadplexModeStr);

        if (!m_IsOnlyBData)
        {
            m_SonoParameters->setPV(BFPNames::ColorMappedBDataCallbackEnableStr, true, true);
            Util::connectSignal(m_ChisonUltrasoundContext, SIGNAL(wholeGrayImage(void*, int, int)), this,
                                SLOT(onNewBImage(void*, int, int)), true, Qt::UniqueConnection);
        }
        else
        {
            Util::processEvents(QEventLoop::ExcludeUserInputEvents);
            m_RefreshTimer.start();
            if (m_NotingState == nullptr)
            {
                m_NotingState = new StateFilterLocker(m_StateManager, StateEventNames::NothingStateFilter());
            }
        }
    }
    else
    {
        m_Looper->stopLoop();

        m_IsQuadplexMode = m_SonoParameters->pBV(BFPNames::QuadplexModeStr);

        if (m_IsQuadplexMode)
        {
            setQuadplexMode(false);
        }

        if (!m_IsOnlyBData)
        {
            m_SonoParameters->setPV(BFPNames::ColorMappedBDataCallbackEnableStr, true, true);
            Util::connectSignal(m_ChisonUltrasoundContext, SIGNAL(wholeGrayImage(void*, int, int)), this,
                                SLOT(onNewBImage(void*, int, int)), true, Qt::UniqueConnection);
            m_ChisonUltrasoundContext->lineBuffer()->requestFlush();
        }
        else
        {
            //            m_MeasureContext->setBImage(m_ImageTile->currentImage(ImageEventArgs::ImageB),
            //                                        m_ChisonUltrasoundContext->sonoParameters()->pIV(BFPNames::ActiveBStr));
            //            m_MeasureContext->setDImage(m_ImageTile->currentImage(ImageEventArgs::ImageD));

            runMeasure();
        }
    }
}
