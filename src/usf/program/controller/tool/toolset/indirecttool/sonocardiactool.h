#ifndef SONOCARDIAC_H
#define SONOCARDIAC_H

#include "menutool.h"
#include "popupwidget.h"
#include "qobject.h"
#include "sonocardiacbaseclass.h"
#include "sonohelpbaseitemwidget.h"
#include <QLabel>
#include <QPushButton>

class SonoCardiacGlyphsControl;
class ImageEventArgs;
class SonoCardiacSecImageTool;
class SonoCardiacSecRecognizeTool;
class SonoCardiacSectionTool;
class ChisonUltrasoundContext;
class CursorMouseActionsModel;
class LeftMenuWidget;
class SonoParameters;
class ImageTile;
class CineLooper;
class IProbeDataSet;
class MeasureContext;

//左侧菜单SonoCardiac开关按钮
class USF_PROGRAM_CONTROLLER_EXPORT SonoCardiacTool : public MenuTool, public SonoCardiacBaseClass
{
    Q_OBJECT
public:
    SonoCardiacTool(const QString& name, ChisonUltrasoundContext* chisonContext,
                    CursorMouseActionsModel* cursorMouseActionsModel, QWidget* leftMenuWidget,
                    SonoParameters* sonoParameters, ImageTile* tile, CineLooper* cineLooper,
                    MeasureContext* measureContext);
    void onSetSonoParameters();
    void setEasyViewSonoParameters(SonoParameters*);
    void changeType();
    void changeImage();
    void changeItemStatus();
    void closeSonoCardiac();
    void exitSonoCardiac();
    void setProbeDataSet(IProbeDataSet* probeDataSet);
    void startSonoCardiac();

public slots:
    void setDisplayShowImage();
    void menuChange(QString);
    void mousePressEvent(const QFileInfo& info);
    void hidePopUpWidget();
    void onCurSonoParametersChanged();
    void onProbeIdChanged(QVariant);
    void onScanModeChanged(QVariant);
    void onScanLineChanged(QVariant);
    void onExamModeChanged(QVariant);
    void onLayoutChanged(QVariant);
    void onFreezeChanged(QVariant);
    void startRTMeasure(const QString&);
    void playRTMeasureCine(ImageEventArgs*);
    void algParameterChange();
    void focuseChanged(QWidget*, QWidget*);
    void changeLeftMenu();
    void functionChanged();
    void languageChanged();
    void enterSonoCardiac();
    void stressechoOpen(QVariant);
    void onPresetChanged(const PresetParameters&);
    void onAutoEFCurLayoutChanged(QVariant);
signals:
    void preChangeLeftMenu();

protected:
    virtual void execute();
    void addElementToLeftMenu();

private:
    bool examModeIsSupport(QString);
    void enableChanged(bool);
    bool freezeVisibleControl(int mode);

private:
    QLabel m_Text;
    //套用SonoHelp的类 显示图像和文字
    SonoHelpTextItemWidget m_TextAndPic;
    //显示点击之后 放大的图像和文字
    PopUpWidget m_PopUpWidget;
    SonoCardiacSecRecognizeTool* m_Tool{NULL};
    SonoCardiacSectionTool* m_SecChooseTool{NULL};
    QStringList m_SupportExamMode;
    ChisonUltrasoundContext* m_ChisonUltrasoundContext;
    CursorMouseActionsModel* m_CursorMouseActionsModel;
    LeftMenuWidget* m_leftMenuWidget;
    SonoParameters* m_RealSonoParameters;
    ImageTile* m_ImageTile;
    CineLooper* m_CineLooper;
    IProbeDataSet* m_ProbeDataSet;
    bool m_needInit;
    MeasureContext* m_MeasureContext;
};
#endif // SONOCARDIAC_H
