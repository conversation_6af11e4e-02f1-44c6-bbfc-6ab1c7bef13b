#ifndef STOREIMAGECOMMAND_H
#define STOREIMAGECOMMAND_H
#include "usfprogramcontroller_global.h"

#include "storecinecommand.h"
#include "screentypedef.h"

class ImageTile;
class USF_PROGRAM_CONTROLLER_EXPORT StoreImageCommand : public StoreCineCommand
{
    Q_OBJECT
public:
    StoreImageCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager,
                      SonoParameters* sonoParameters, IMainWindow* flexBtnVisible, BaseWidget* imageClipWidget,
                      IBufferStoreManager* bufferStoreManager, IPatientWorkflow* patientWorkflow,
                      IDicomTaskManager* dicomTaskManager, IStressEchoModel* stressEchoModel,
                      BaseWidget* storeProgressMovieWidget, PatientEditModel* patientEditModel,
                      ISystemStatusModel* systemStatusModel, QWidget* sonoZoomWidget, IZoomOnProxy* zoomOnProxy,
                      IBeamFormer* beamFormer, CineLooper* cineLooper, IColorMapManager* colorMapManager,
                      ISystemInfo* systemInfo, ImageTile* imageTile, IDicomUtilityCreator* dicomUtilityCreator,
                      IDicomToolCreator* dicomToolCreator, ChisonUltrasoundContext* ultrasoundContext,
                      MeasureContext* measureContext);

protected:
    virtual void doSave(const QString& fileName);
    virtual QString fileExt() const;
    virtual void beginOperate();
    virtual void showSavingMovie(bool inFourDWidget = false);
    virtual QImage grabThumbnailScreen();
    virtual void saveThumbnailScreen(const QImage& image, const QString& fileBaseName);
    virtual void exportFile(bool isGDPR = false);
    bool isFreehand3DEditExamMode();
    virtual bool isSupportSave() const;
protected slots:
    virtual void onCommandFinished(bool succeed);

private:
    bool m_RealTime;
    ScreenTypeDef::ScreenType m_originScreenType;
    bool m_RealTimePaused;
};

#endif // STOREIMAGECOMMAND_H
