#include "menutools.h"
#include "abstractstate.h"
#include "aiocontroller.h"
#include "applicationinfo.h"
#include "autofocusadjuster.h"
#include "bfdepthparameters.h"
#include "bfpnames.h"
#include "buttonmenupanel.h"
#include "demoimporters.h"
#include "diskselectionwidget.h"
#include "ibeamformertool.h"
#include "ibufferstoremanager.h"
#include "infostruct.h"
#include "itool.h"
#include "iupdownkeyoperator.h"
#include "lgcwidget.h"
#include "lightcontroller.h"
#include "mainwindowmiddledownwidget.h"
#include "menucontroller.h"
#include "menunames.h"
#include "messageboxframe.h"
#include "modelconfig.h"
#include "parameter.h"
#include "presetmodewidget.h"
#include "qvariantcommandargs.h"
#include "resource.h"
#include "setting.h"
#include "sonoparameters.h"
#include "stateeventnames.h"
#include "statefilterlocker.h"
#include "statemanager.h"
#include "string.h"
#include "stringcommandargs.h"
#include "toollist.h"
#include "toolnames.h"
#include "toolsfactory.h"
#include "util.h"
#include <QDebug>
#include <QDir>
#include <QSettings>
//#include "ecgreceiver.h"
#include "appsetting.h"
#include "baseruler.h"
#include "cursormouseactionsmodel.h"
#include "ecgsettings.h"
#include "glyphscontrolmanager.h"
#include "ilinebuffermanager.h"
#include "iprobedataset.h"
#include "istatemanager.h"
#include "leftmenuwidget.h"
#include "lgcregulatorwidget.h"
#include "licenseitemkey.h"
#include "linecinelooper.h"
#include "measurecontext.h"
#include "irulerfactory.h"
#include "sonomskglyphscontrol.h"
#include "sononerveglyphscontrol.h"
#include "stdstringcommandargs.h"
#include "toolsordermanager.h"
#include "qdeadlinetimer.h"
#include "sonocardiactool.h"
#include "sonomsktool.h"
#include "sonodiaphtool.h"

TgcTool::TgcTool()
{
}

TgcTool::TgcTool(const QString& name)
    : MenuTool(name)
{
}

void TgcTool::setArgs(ICommandArgs* args)
{
    MenuTool::setArgs(args);
    StdStringCommandArgs* p_args = dynamic_cast<StdStringCommandArgs*>(args);
    Q_ASSERT(p_args != NULL);
    int length = 0;
    unsigned char* p_data = p_args->arg(length);
    setTgcData(p_data, length);
}

void TgcTool::setTgcData(unsigned char* tgcData, int tgcLen)
{
    if (tgcData == NULL)
    {
        return;
    }

    QByteArray newData = QByteArray((const char*)(tgcData), tgcLen);

    //不配置时，默认最小值是0，最大值是255,Atom机型配置了100-255
    int minValue = ModelConfig::instance().value(ModelConfig::TGCLimitMinValue, 0).toInt();
    int maxValue = ModelConfig::instance().value(ModelConfig::TGCLimitMaxValue, 255).toInt();

    // tgc值的限制，在收到tgc数据后就进行
    //代码原先的位置会造成bug：第一次的tgc数据值，无法进行限制
    for (int i = 0; i < newData.size(); i++)
    {
        // TODO：SonoAir机型的Tgc冻结下调节走的不是该Tool。
        //如果SonoAir也需要配置limitTGC，相应的Tool也需要增加该limit代码
        newData[i] = limitTGC(newData[i], minValue, maxValue);
    }

    if (!m_TgcData.isEmpty())
    {
        if (m_TgcDiff.isEmpty())
        {
            m_TgcDiff = QByteArray(tgcLen, 0);
        }

        for (int i = 0; i < newData.size(); i++)
        {
            m_TgcDiff[i] = (int)((uchar)newData[i]) - (int)((uchar)m_TgcData[i]);
        }
    }

    m_TgcData = newData;
}

void TgcTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        if (AIOController::instance().isOpen())
        {
            LightController::instance().light(LightNames::AIOStr, false);
            AIOController::instance().close();
        }

        if (!m_TgcData.isEmpty())
        {
            //这里只发送一次控制表，注释掉的要发送8次
            m_SonoParameters->setPV(BFPNames::TGCStr, m_TgcData);
        }
    }
}

void TgcTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (m_SonoParameters != NULL && m_SonoParameters->isRealTime())
    {
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreezeChanged(QVariant)), Qt::UniqueConnection);
    }
}

void TgcTool::onFreezeChanged(const QVariant& value)
{
    if (m_SonoParameters != NULL && !value.toBool())
    {
        if (!AIOController::instance().isOpen())
        {
            if (!m_TgcData.isEmpty())
            {
                //如果在冻结后调节TGC，解冻后会设置TGC
                m_SonoParameters->setPV(BFPNames::TGCStr, m_TgcData);
            }
        }
    }
}

uchar TgcTool::limitTGC(const uchar value, const int minValue, const int maxValue)
{
    int range = (maxValue - minValue + 1) > 0 ? (maxValue - minValue + 1) : 1;
    return (minValue + (int)(1.0 * range * value / 256));
}

void TgcTool::retranslateUi(QWidget* widget)
{
}

LRTool::LRTool()
{
}

LRTool::LRTool(const QString& name)
    : MenuTool(name)
{
    setIsFreezeSendTool(true);
}

void LRTool::retranslateUi(QWidget* widget)
{
}

void LRTool::onToolValueChanged(const QVariant& value)
{
    setToolValue(value.toBool() ? "false" : "true");
    LightController::instance().light(LightNames::LRStr, !value.toBool());
}

int LRTool::value() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_ToolName)->value().toBool() ? 0 : 1;
    }
    return 0;
}

void LRTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        bool value = m_SonoParameters->pBV(this->toolName());
        LightController::instance().light(LightNames::LRStr, value);
        m_SonoParameters->setPV(this->toolName(), !value);
        if (!m_SonoParameters->pBV(BFPNames::RTIMTStr))
        {
            ToolsFactory::instance().command(ToolNames::MeasurementDeleteStr)->run();
        }
    }
}

UDTool::UDTool()
{
}

UDTool::UDTool(const QString& name)
    : MenuTool(name)
{
    setIsFreezeSendTool(true);
}

void UDTool::retranslateUi(QWidget* widget)
{
}

void UDTool::onToolValueChanged(const QVariant& value)
{
    setToolValue(value.toBool() ? "false" : "true");
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (tool != NULL)
    {
        tool->run();
    }
}

void UDTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        if (m_SonoParameters->pBV(BFPNames::RTIMTStr))
        {
            return;
        }
        bool value = m_SonoParameters->pBV(this->toolName());
        m_SonoParameters->setPV(this->toolName(), !value);
        ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
        if (tool != NULL)
        {
            tool->run();
        }
        ToolsFactory::instance().command(ToolNames::MeasurementDeleteStr)->run();
    }
}

void UDTool::onSetSonoParameters()
{
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (tool != NULL)
    {
        tool->run();
    }
}

int UDTool::value() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_ToolName)->value().toBool() ? 0 : 1;
    }
    return 0;
}

SpectralInvertTool::SpectralInvertTool(const QString& name)
    : MenuTool(name)
{
}

void SpectralInvertTool::onToolValueChanged(const QVariant& value)
{
    setToolValue(value.toBool() ? "false" : "true");
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (tool != NULL)
    {
        tool->run();
    }
}

void SpectralInvertTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        bool value = m_SonoParameters->pBV(this->toolName());
        m_SonoParameters->setPV(this->toolName(), !value);
        ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
        if (tool != NULL)
        {
            tool->run();
        }
    }
}

void SpectralInvertTool::onSetSonoParameters()
{
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (tool != NULL)
    {
        tool->run();
    }
}

int SpectralInvertTool::value() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_ToolName)->value().toBool() ? 1 : 0;
    }
    return 0;
}

ColorInvertStateTool::ColorInvertStateTool(const QString& name)
    : MenuTool(name)
{
}

void ColorInvertStateTool::onToolValueChanged(const QVariant& value)
{
    setToolValue(value.toBool() ? "false" : "true");
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (tool != NULL)
    {
        tool->run();
    }
}

void ColorInvertStateTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        bool value = m_SonoParameters->pBV(this->toolName());
        m_SonoParameters->setPV(this->toolName(), !value);
        ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
        if (tool != NULL)
        {
            tool->run();
        }
    }
}

void ColorInvertStateTool::onSetSonoParameters()
{
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (tool != NULL)
    {
        tool->run();
    }
}

int ColorInvertStateTool::value() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_ToolName)->value().toBool() ? 1 : 0;
    }
    return 0;
}

CQYZTool::CQYZTool()
{
}

CQYZTool::CQYZTool(const QString& name)
    : MenuTool(name)
{
}

void CQYZTool::retranslateUi(QWidget* widget)
{
}

void CQYZTool::execute()
{
    //频谱开启时不可调节B/BC的深度
    if (!m_SonoParameters->pBV(BFPNames::FreqSpectrumStr))
    {
        QVariantCommandArgs* arg = this->args<QVariantCommandArgs>();
        if (arg != NULL)
        {
            m_SonoParameters->setPV(BFPNames::CQYZGrowingUpStr, arg->arg());
        }

        MenuTool::execute();
        m_IsAdd = false;
        m_SonoParameters->setPV(BFPNames::CQYZGrowingUpStr, false);
    }
}

#include "lightcontroller.h"

FocusTool::FocusTool()
{
}

FocusTool::FocusTool(const QString& name)
    : MenuTool(name)
{
    connect(this, SIGNAL(initFocusParaValue()), this, SLOT(onFsValueChanged()));
}

void FocusTool::retranslateUi(QWidget* widget)
{
}

void FocusTool::onInitialize()
{
    MenuTool::onInitialize();

    if (m_SonoParameters != NULL)
    {
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFoucsEnabled()));
        connect(m_SonoParameters->parameter(BFPNames::FocusPosBStr), &Parameter::valueChanged, this,
                &MenuTool::valueChanged);
        connect(m_SonoParameters->parameter(BFPNames::FocusPosMStr), &Parameter::valueChanged, this,
                &MenuTool::valueChanged);
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreezeChanged(QVariant)));
    }

    // 该Tool没有写入bfparameter文件里，因此不属于真正的parameter,不需要在sonoparameter改变时候进行处理
    disconnect(m_BufferStoreManager, SIGNAL(beforeCurSonoParametersChanged()), this,
               SLOT(onBeforeCurSonoParametersChanged()));
    disconnect(m_BufferStoreManager, SIGNAL(curSonoParametersChanged()), this, SLOT(onCurSonoParametersChanged()));
}

void FocusTool::onFoucsEnabled()
{
    if (m_SonoParameters != NULL)
    {
        SystemScanMode mode = (SystemScanMode)(m_SonoParameters->pIV(BFPNames::SystemScanModeStr));
        bool isEnabled = false;
        if (mode == SystemScanModeB || mode == SystemScanMode2B || mode == SystemScanMode4B ||
            mode == SystemScanModeAV || mode == SystemScanModeLRBM || mode == SystemScanModeUDBM ||
            mode == SystemScanModeFourDPre)
        {
            isEnabled = true;
        }
        setIsAvailable(isEnabled);
        onToolValueChanged(QVariant());
    }
}

void FocusTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        SystemScanMode scanMode = (SystemScanMode)m_SonoParameters->pIV(BFPNames::SystemScanModeStr);
        switch (scanMode)
        {
        case SystemScanModeB:
        case SystemScanMode2B:
        case SystemScanMode4B:
        case SystemScanModeFourDPre:
        case SystemScanModeFreeHand3D:
        case SystemScanModeCP:
        case SystemScanModeAV:
            m_BfName = BFPNames::FocusPosBStr;
            setFocus(BFPNames::FocusPosBStr);
            break;
        case SystemScanModeM:
        case SystemScanModeTDIM:
        case SystemScanModeDPDM:
        case SystemScanModePDM:
        case SystemScanModeLRBM:
        case SystemScanModeUDBM:
        case SystemScanModeColorLRBM:
        case SystemScanModeColorUDBM:
        case SystemScanModePDLRBM:
        case SystemScanModePDUDBM:
        case SystemScanModeDPDLRBM:
        case SystemScanModeDPDUDBM:
        case SystemScanModeTDILRBM:
        case SystemScanModeTDIUDBM:
        case SystemScanModeLRFreeM:
        case SystemScanModeUDFreeM:
            m_BfName = BFPNames::FocusPosMStr;
            setFocus(BFPNames::FocusPosMStr);
            break;
        default:
            break;
        }
    }

    if (m_SonoParameters != NULL)
    {
        QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
        if (args != NULL)
        {
            if (args->getType() == COMMAND_TYPE_STRING)
            {
                int value = args->getValue().toInt();
                m_SonoParameters->parameter(m_BfName)->setValue(value);
            }
            else
            {
                setToolValue(m_SonoParameters->parameter(m_BfName)->text());
            }
        }
        else
        {
            setToolValue(m_SonoParameters->parameter(m_BfName)->text());
        }
        AutoFocusAdjuster::instance().updateCoefficient(m_SonoParameters);
    }
}

void FocusTool::setFocus(QString bfName)
{
    if (m_SonoParameters != NULL)
    {
        QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
        if (args != NULL)
        {
            bool arg = isInvertMulti() ? !args->arg() : args->arg();
            m_SonoParameters->parameter(bfName)->multi(arg);
        }
    }
}

int FocusTool::maxmValue() const
{
    if (m_SonoParameters != NULL)
    {
        return m_SonoParameters->pMax(m_BfName);
    }
    return 0;
}

int FocusTool::minmValue() const
{
    if (m_SonoParameters != NULL)
    {
        return m_SonoParameters->pMin(m_BfName);
    }
    return 0;
}

int FocusTool::stepValue() const
{
    if (m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_BfName)->step();
    }
    return 0;
}

int FocusTool::value() const
{
    emit initFocusParaValue();
    if (m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_BfName)->value().toInt();
    }
    return 0;
}

void FocusTool::onFocusValueChanged()
{
    if (m_SonoParameters != NULL)
    {
        setToolValue(m_SonoParameters->parameter(m_BfName)->text());
    }
}

void FocusTool::onFsValueChanged()
{
    if (m_SonoParameters != NULL)
    {
        SystemScanMode scanMode = (SystemScanMode)m_SonoParameters->pIV(BFPNames::SystemScanModeStr);
        switch (scanMode)
        {
        case SystemScanModeB:
        case SystemScanMode2B:
        case SystemScanMode4B:
        case SystemScanModeFourDPre:
        case SystemScanModeFreeHand3D:
        case SystemScanModeCP:
        case SystemScanModeAV:
            m_BfName = BFPNames::FocusPosBStr;
            break;
        case SystemScanModeM:
        case SystemScanModeTDIM:
        case SystemScanModeDPDM:
        case SystemScanModePDM:
        case SystemScanModeLRBM:
        case SystemScanModeUDBM:
        case SystemScanModeColorLRBM:
        case SystemScanModeColorUDBM:
        case SystemScanModePDLRBM:
        case SystemScanModePDUDBM:
        case SystemScanModeDPDLRBM:
        case SystemScanModeDPDUDBM:
        case SystemScanModeTDILRBM:
        case SystemScanModeTDIUDBM:
        case SystemScanModeLRFreeM:
        case SystemScanModeUDFreeM:
            m_BfName = BFPNames::FocusPosMStr;
            break;
        default:
            break;
        }
    }
    onFocusValueChanged();
}

void FocusTool::onFreezeChanged(const QVariant& value)
{
    if (m_SonoParameters != NULL && m_SonoParameters->isRealTime() && !value.toBool())
    {
        onFoucsEnabled();
    }
}

FreezeTool::FreezeTool()
{
}

FreezeTool::FreezeTool(const QString& name)
    : MenuTool(name)
{
}

void FreezeTool::retranslateUi(QWidget* widget)
{
}

void FreezeTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        bool value = m_SonoParameters->pBV(BFPNames::FreezeStr);
        m_SonoParameters->setPV(BFPNames::FreezeStr, !value);
    }
}

ChangeTool::ChangeTool()
    : m_CurrentMenu(MenuNames::BMenuStr)
    , m_CurrentIndex(0)
{
}

ChangeTool::ChangeTool(const QString& name)
    : MenuTool(name)
    , m_CurrentMenu(MenuNames::BMenuStr)
    , m_CurrentIndex(0)
{
}

void ChangeTool::retranslateUi(QWidget* widget)
{
}

void ChangeTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        //如果当前尚未显示菜单，则先显示当前菜单
        if (!MenuController::instance().isShowingLeftMenu())
        {
            MenuController::instance().setShowLeftMenu(true);
        }
        else //否则显示下一个菜单
        {
            QString menuName;
            QVariantCommandArgs* arg = this->args<QVariantCommandArgs>();
            if (arg != NULL)
            {
                ApplicationInfo::instance().flashLeftAndRightArrow(!(arg->arg()));
                menuName = getNextMenu(arg->arg());
            }
            else
            {
                StringCommandArgs* argString = this->args<StringCommandArgs>();
                if (argString != NULL)
                {
                    menuName = argString->arg();
                    for (int i = 0; i < m_MenuNames.count(); ++i)
                    {
                        if (menuName == m_MenuNames[i])
                        {
                            m_CurrentIndex = i;
                            m_CurrentMenu = m_MenuNames[m_CurrentIndex];
                        }
                    }
                }
            }
            if (!menuName.isEmpty())
            {
                MenuController::instance().createMenu(menuName);
            }
        }
    }
}

void ChangeTool::onInitialize()
{
    MenuTool::onInitialize();

    // add xule
    if (MenuController::instance().navigationWidget() != NULL)
    {
        connect((&MenuController::instance()), SIGNAL(freshModeLabels(QString)), this,
                SLOT(setModeChangeLabels(QString)));
    }

    setCurrentMenus();
}

void ChangeTool::setCurrentMenus()
{
    m_MenuNames.clear();
    m_CurrentIndex = 0;
    if (m_SonoParameters != NULL)
    {
        SystemScanMode mode = (SystemScanMode)(m_SonoParameters->pIV(BFPNames::SystemScanModeStr));
        m_MenuNames.append(MenuNames::BMenuStr);
        switch (mode)
        {
        case SystemScanModeSonoNeedle:
            m_MenuNames.append(MenuNames::SonoNeedleMenuStr);
            m_MenuNames.append(MenuNames::BiopsyMenuStr);
            m_CurrentMenu = MenuNames::SonoNeedleMenuStr;
            break;
        case SystemScanModeB:
        case SystemScanMode2B:
        case SystemScanMode4B:
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                    m_CurrentMenu = MenuNames::CWDPreMenuStr;
                }
                else //判断进入BPW-pre的状态
                {
                    m_MenuNames.append(MenuNames::DPREMenuStr);
                    m_CurrentMenu = MenuNames::DPREMenuStr;
                }
            }
            else if (m_SonoParameters->pBV(BFPNames::StressEchoEnStr))
            {
                m_MenuNames.append(MenuNames::StressEchoMenuStr);
                m_CurrentMenu = MenuNames::StressEchoMenuStr;
            }
            else
            {
                if (m_SonoParameters->pBV(BFPNames::SonoNerveStr))
                {
                    m_MenuNames.append(MenuNames::SonoNerveMenuStr);
                }
                if (m_SonoParameters->pBV(BFPNames::SonoMSKStr))
                {
                    m_MenuNames.append(MenuNames::SonoMSKMenuStr);
                }
                m_CurrentMenu = MenuNames::BMenuStr;
            }
            break;
        case SystemScanModeFourDPre:
            m_MenuNames.append(MenuNames::FourDPreMenuStr);
            m_CurrentMenu = MenuNames::FourDPreMenuStr;
            break;
        case SystemScanModeColorDoppler:
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                m_MenuNames.append(MenuNames::CMenuStr);
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                    m_CurrentMenu = MenuNames::CWDPreMenuStr;
                }
                else //判断进入BPW-pre的状态
                {
                    m_MenuNames.append(MenuNames::DPREMenuStr);
                    m_CurrentMenu = MenuNames::DPREMenuStr;
                }
            }
            else
            {
                m_MenuNames.append(MenuNames::CMenuStr);
                m_CurrentMenu = MenuNames::CMenuStr;
            }
            break;
        case SystemScanModePowerDoppler:
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                m_MenuNames.append(MenuNames::CPAMenuStr);
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                    m_CurrentMenu = MenuNames::CWDPreMenuStr;
                }
                else //判断进入BPW-pre的状态
                {
                    m_MenuNames.append(MenuNames::DPREMenuStr);
                    m_CurrentMenu = MenuNames::DPREMenuStr;
                }
            }
            else
            {
                m_MenuNames.append(MenuNames::CPAMenuStr);
                m_CurrentMenu = MenuNames::CPAMenuStr;
            }
            break;
        case SystemScanModeDPowerDoppler:
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                m_MenuNames.append(MenuNames::DPDMenuStr);
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                    m_CurrentMenu = MenuNames::CWDPreMenuStr;
                }
                else //判断进入BPW-pre的状态
                {
                    m_MenuNames.append(MenuNames::DPREMenuStr);
                    m_CurrentMenu = MenuNames::DPREMenuStr;
                }
            }
            else
            {
                m_MenuNames.append(MenuNames::DPDMenuStr);
                m_CurrentMenu = MenuNames::DPDMenuStr;
            }
            break;
        case SystemScanModeTissueDoppler:
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                m_MenuNames.append(MenuNames::TDIMenuStr);
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                    m_CurrentMenu = MenuNames::CWDPreMenuStr;
                }
                else //判断进入BPW-pre的状态
                {
                    m_MenuNames.append(MenuNames::DTDIPreMenuStr);
                    m_CurrentMenu = MenuNames::DTDIPreMenuStr;
                }
            }
            else
            {
                m_MenuNames.append(MenuNames::TDIMenuStr);
                m_CurrentMenu = MenuNames::TDIMenuStr;
            }
            break;
        case SystemScanModeBPW:
            m_MenuNames.append(MenuNames::DMenuStr);
            m_CurrentMenu = MenuNames::DMenuStr;
            break;
        case SystemScanModeColorPW:
            m_MenuNames.append(MenuNames::CMenuStr);
            m_MenuNames.append(MenuNames::DMenuStr);
            m_CurrentMenu = MenuNames::DMenuStr;
            break;
        case SystemScanModePowerPW:
            m_MenuNames.append(MenuNames::CPAMenuStr);
            m_MenuNames.append(MenuNames::DMenuStr);
            m_CurrentMenu = MenuNames::DMenuStr;
            break;
        case SystemScanModeDPowerPW:
            m_MenuNames.append(MenuNames::DPDMenuStr);
            m_MenuNames.append(MenuNames::DMenuStr);
            m_CurrentMenu = MenuNames::DMenuStr;
            break;
        case SystemScanModeTissuePW:
            m_MenuNames.append(MenuNames::TDIMenuStr);
            m_MenuNames.append(MenuNames::DTDIMenuStr);
            m_CurrentMenu = MenuNames::DTDIMenuStr;
            break;
        case SystemScanModeCWD:
            m_MenuNames.append(MenuNames::CWDMenuStr);
            m_CurrentMenu = MenuNames::CWDMenuStr;
            break;
        case SystemScanModeCWDColorDoppler:
            m_MenuNames.append(MenuNames::CMenuStr);
            m_MenuNames.append(MenuNames::CWDMenuStr);
            m_CurrentMenu = MenuNames::CWDMenuStr;
            break;
        case SystemScanModeCWDPowerDoppler:
            m_MenuNames.append(MenuNames::CPAMenuStr);
            m_MenuNames.append(MenuNames::CWDMenuStr);
            m_CurrentMenu = MenuNames::CWDMenuStr;
            break;
        case SystemScanModeCWDDirectionalPowerDoppler:
            m_MenuNames.append(MenuNames::DPDMenuStr);
            m_MenuNames.append(MenuNames::CWDMenuStr);
            m_CurrentMenu = MenuNames::CWDMenuStr;
            break;
        case SystemScanModeM:
        case SystemScanModeLRBM:
        case SystemScanModeUDBM:
            m_MenuNames.append(MenuNames::MMenuStr);
            m_CurrentMenu = MenuNames::MMenuStr;
            break;
        case SystemScanModeLRFreeM:
        case SystemScanModeUDFreeM:
            m_MenuNames.append(MenuNames::FreeMMenuStr);
            m_CurrentMenu = MenuNames::FreeMMenuStr;
            break;
        case SystemScanModeColorM:
        case SystemScanModeColorLRBM:
        case SystemScanModeColorUDBM:
            m_MenuNames.append(MenuNames::CMenuStr);
            m_MenuNames.append(MenuNames::MMenuStr);
            m_CurrentMenu = MenuNames::MMenuStr;
            break;
        case SystemScanModePDM:
        case SystemScanModePDLRBM:
        case SystemScanModePDUDBM:
            m_MenuNames.append(MenuNames::CPAMenuStr);
            m_MenuNames.append(MenuNames::MMenuStr);
            m_CurrentMenu = MenuNames::MMenuStr;
            break;
        case SystemScanModeDPDM:
        case SystemScanModeDPDLRBM:
        case SystemScanModeDPDUDBM:
            m_MenuNames.append(MenuNames::DPDMenuStr);
            m_MenuNames.append(MenuNames::MMenuStr);
            m_CurrentMenu = MenuNames::MMenuStr;
            break;
        case SystemScanModeTDIM:
        case SystemScanModeTDILRBM:
        case SystemScanModeTDIUDBM:
            m_MenuNames.append(MenuNames::TDIMenuStr);
            m_MenuNames.append(MenuNames::MMenuStr);
            m_CurrentMenu = MenuNames::MMenuStr;
            break;
        case SystemScanModeE:
            m_MenuNames.append(MenuNames::EMenuStr);
            m_CurrentMenu = MenuNames::EMenuStr;
            break;
        case SystemScanModeFourDLive:
            m_MenuNames.append(MenuNames::FourDMenuStr);
            m_CurrentMenu = MenuNames::FourDMenuStr;
            break;
        case SystemScanModeFreeHand3D:
            m_MenuNames.append(MenuNames::FreeHand3DMenuStr);
            m_CurrentMenu = MenuNames::FreeHand3DMenuStr;
            break;
        case SystemScanModeCP:
            m_MenuNames.clear();
            m_MenuNames.append(MenuNames::CurvedPanoramicMenuStr);
            m_CurrentMenu = MenuNames::CurvedPanoramicMenuStr;
            break;
        case SystemScanModeMVI:
            m_MenuNames.append(MenuNames::MVIMenuStr);
            m_CurrentMenu = MenuNames::MVIMenuStr;
            break;
        case SystemScanModeMVIPW:
            m_MenuNames.append(MenuNames::MVIMenuStr);
            m_MenuNames.append(MenuNames::DMenuStr);
            m_CurrentMenu = MenuNames::DMenuStr;
            break;
        default:
            break;
        }
        if (m_SonoParameters->pBV(BFPNames::SonoCardiacStr) &&
            AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoCardiac))
        {
            m_MenuNames.append(MenuNames::SonoCardiacMenuStr);
        }
        if (m_SonoParameters->pBV(BFPNames::SonoAAAStr))
        {
            m_MenuNames.append(MenuNames::SonoAAAMenuStr);
            m_CurrentMenu = MenuNames::SonoAAAMenuStr;
        }
        if (m_SonoParameters->pBV(BFPNames::SonoCarotidGuideStr))
        {
            m_MenuNames.append(MenuNames::SonoGuideMenuStr);
            m_CurrentMenu = MenuNames::SonoGuideMenuStr;
        }
        if (m_SonoParameters->pBV(BFPNames::ECGEnStr))
        {
            m_MenuNames.append(MenuNames::ECGMenuStr);
        }
        if (m_SonoParameters->pBV(BFPNames::EFastModeONStr))
        {
            m_MenuNames.append(MenuNames::EFastMenuStr);
        }
        if (m_SonoParameters->pBV(BFPNames::FastModeONStr))
        {
            m_MenuNames.append(MenuNames::FastMenuStr);
        }
        m_MenuNames.append(MenuNames::UtilityMenu);
    }
}

bool ChangeTool::isFreezed()
{
    if (m_SonoParameters != NULL)
    {
        // 由于实时保存的数据中冻结参数为false，需要isRealTime来辅助
        return m_SonoParameters->pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime();
    }
    return false;
}

QStringList ChangeTool::getCurrentMenus()
{
    return m_MenuNames;
}

void ChangeTool::onParameterValueChanged(const QVariant& value, bool isChanged)
{
    Q_UNUSED(value)
    if (isChanged)
    {
        setCurrentMenus();
    }
}

void ChangeTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr),
                   SIGNAL(valueChanged(QVariant, bool)), this, SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::IsCWDScanLineVisibleStr), SIGNAL(valueChanged(QVariant, bool)),
                   this, SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::ECGEnStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoNerveStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::StressEchoEnStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoCardiacStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoCarotidGuideStr), SIGNAL(valueChanged(QVariant, bool)),
                   this, SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoMSKStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoAAAStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::EFastModeONStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
        disconnect(m_SonoParameters->parameter(BFPNames::FastModeONStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onParameterValueChanged(QVariant, bool)));
    }
}

void ChangeTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (m_SonoParameters != NULL)
    {
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        // FreqSpectrum 变化，没有触发模式变更，但需要重新组织menus
        connect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        // DopplerScanLineVisibleStr
        connect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr),
                SIGNAL(valueChanged(QVariant, bool)), this, SLOT(onParameterValueChanged(QVariant, bool)));
        // CWDScanLineVisibleStr
        connect(m_SonoParameters->parameter(BFPNames::IsCWDScanLineVisibleStr), SIGNAL(valueChanged(QVariant, bool)),
                this, SLOT(onParameterValueChanged(QVariant, bool)));
        // bug 57064 PW Pre 解冻后无法切回，原因：冻结解冻，执行了Parameter::cloneFrom，导致IsDopplerScanLineVisible
        // 信号未触发。增加冻结解冻，让menus响应
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        connect(m_SonoParameters->parameter(BFPNames::ECGEnStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        connect(m_SonoParameters->parameter(BFPNames::SonoNerveStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        connect(m_SonoParameters->parameter(BFPNames::StressEchoEnStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        connect(m_SonoParameters->parameter(BFPNames::SonoCardiacStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        connect(m_SonoParameters->parameter(BFPNames::SonoCarotidGuideStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        connect(m_SonoParameters->parameter(BFPNames::SonoMSKStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        connect(m_SonoParameters->parameter(BFPNames::SonoAAAStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        connect(m_SonoParameters->parameter(BFPNames::EFastModeONStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
        connect(m_SonoParameters->parameter(BFPNames::FastModeONStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onParameterValueChanged(QVariant, bool)));
    }
}

void ChangeTool::addIndex()
{
    m_CurrentIndex++;
    if (m_CurrentIndex > m_MenuNames.count() - 1)
    {
        m_CurrentIndex = 0;
    }
}

void ChangeTool::decIndex()
{
    m_CurrentIndex--;
    if (m_CurrentIndex < 0)
    {
        m_CurrentIndex = m_MenuNames.count() - 1;
    }
}

QString ChangeTool::getNextMenu(bool arg)
{
    if (m_MenuNames.count() == 0)
    {
        return MenuNames::BMenuStr;
    }
    else if (m_MenuNames.count() == 1)
    {
        m_CurrentMenu = m_MenuNames.at(0);
    }
    else
    {
        if (QString::compare(m_MenuNames[m_CurrentIndex], m_CurrentMenu, Qt::CaseInsensitive) == 0)
        {
            if (arg == true)
            {
                addIndex();
            }
            else
            {
                decIndex();
            }
        }
        m_CurrentMenu = m_MenuNames[m_CurrentIndex];
    }
    return m_CurrentMenu;
}

// add xule
void ChangeTool::setModeChangeLabels(const QString& curModeName)
{
    QString preMode, nextMode;
    QList<QString> modes;
    if (m_MenuNames.contains(curModeName))
    {
        if (m_MenuNames.count() > 1)
        {
            int index = m_MenuNames.indexOf(curModeName);
            // setCurrentMenus()时下标默认为0,此处同步菜单名与下标
            if (m_CurrentMenu != curModeName || m_CurrentIndex != index)
            {
                m_CurrentMenu = curModeName;
                m_CurrentIndex = index;
            }
            if ((index + 1) >= m_MenuNames.count())
            {
                nextMode = m_MenuNames.at(0);
            }
            else
            {
                nextMode = m_MenuNames.at(index + 1);
            }
            if (index == 0)
            {
                preMode = m_MenuNames.last();
            }
            else
            {
                preMode = m_MenuNames.at(index - 1);
            }
            modes.append(nameTranslate(preMode));
            modes.append(nameTranslate(curModeName));
            modes.append(nameTranslate(nextMode));
        }
        else
        {
            modes.append(nameTranslate(curModeName));
        }
    }
    else if (curModeName == MenuNames::FreezeMenuStr)
    {
        modes.append(nameTranslate(curModeName));
    }
    else
    {
        return;
    }
    ApplicationInfo::instance().setModeShow(modes);
}

QString ChangeTool::nameTranslate(const QString& name)
{
    QString menuStr = MenuNames::menuCaption(name);
    int m = menuStr.indexOf("Menu");
    if (m >= 0)
    {
        menuStr = menuStr.left(m);
    }
    else
    {
        menuStr = Util::translate("MenuController", menuStr);
    }
    return menuStr;
}

VolumeControlTool::VolumeControlTool()
{
}

VolumeControlTool::VolumeControlTool(const QString& name)
    : MenuTool(name)
    , m_toolEnabled(false)
{
}

void VolumeControlTool::retranslateUi(QWidget* widget)
{
}

void VolumeControlTool::onInitialize()
{
    MenuTool::onInitialize();

    m_curVolumeStr.clear();
}

void VolumeControlTool::onSystemScanModeChanged()
{
    if (m_SonoParameters != NULL)
    {
        bool flagD = ((m_SonoParameters->pIV(BFPNames::SyncModeStr) & Sync_D) == Sync_D);
        if (flagD)
        {
            QString volumeStr;
            if (m_SonoParameters->pBV(BFPNames::TDIEnStr)) // VolumeTDI
            {
                volumeStr = BFPNames::VolumeTDIStr;
            }
            else if (m_SonoParameters->pBV(BFPNames::CWEnStr) &&
                     !ModelConfig::instance().value(ModelConfig::HPrfCW, false).toBool()) // VolumeCWD
            {
                volumeStr = BFPNames::VolumeCWDStr;
            }
            else // Volume
            {
                volumeStr = BFPNames::VolumeStr;
            }

            //频谱关闭时切换模式不会改变频谱开关状态
            if (!m_toolEnabled && !m_curVolumeStr.isEmpty())
            {
                m_SonoParameters->parameter(volumeStr)->setEnabled(false);
            }
            if (m_curVolumeStr != volumeStr)
            {
                //恢复前一个模式音量控制的状态
                m_SonoParameters->parameter(m_curVolumeStr)->setEnabled(true);
                m_curVolumeStr = volumeStr;
            }
        }
        else
        {
            if (!m_curVolumeStr.isEmpty())
            {
                m_SonoParameters->parameter(m_curVolumeStr)->setEnabled(true);
                m_curVolumeStr.clear();
            }
            m_toolEnabled = false;
        }
    }
}

void VolumeControlTool::onVolumeStateChanged()
{
    if (m_SonoParameters != NULL)
    {
        bool flagD = ((m_SonoParameters->pIV(BFPNames::SyncModeStr) & Sync_D) == Sync_D);
        if (flagD &&
            (m_SonoParameters->pBV(BFPNames::TriplexModeStr) || m_SonoParameters->pBV(BFPNames::FreqSpectrumStr)))
        {
            m_toolEnabled = true;
            if (m_SonoParameters->pBV(BFPNames::QuadplexModeStr))
                m_SonoParameters->parameter(BFPNames::TriplexModeStr)->setEnabled(false);
            else
                m_SonoParameters->parameter(BFPNames::TriplexModeStr)->setEnabled(true);
        }
        else
        {
            m_toolEnabled = false;
        }

        if (!m_curVolumeStr.isEmpty())
        {
            if (m_toolEnabled)
            {
                m_SonoParameters->parameter(m_curVolumeStr)->setEnabled(true);
            }
            else
            {
                m_SonoParameters->parameter(m_curVolumeStr)->setEnabled(false);
            }
        }
    }
}

void VolumeControlTool::onBeforeCurSonoParametersChanged()
{
    // TODO:现在已经遇到change,VolumeControl,anglechang,leftadjustment四个tool有相关问题,
    //未来需要在基类中统一修改解决回调后恢复update时使用m_sonoparameters指针使用错误的问题
    MenuTool::onBeforeCurSonoParametersChanged();
    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanging(QVariant)), this,
                   SLOT(onSystemScanModeChanged()));
        disconnect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onVolumeStateChanged()));
        disconnect(m_SonoParameters->parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onVolumeStateChanged()));
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanging(QVariant)), this,
                   SLOT(onParameterValueChanged()));
    }
}

void VolumeControlTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (m_SonoParameters != NULL)
    {
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanging(QVariant)), this,
                SLOT(onSystemScanModeChanged()));
        connect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onVolumeStateChanged()));
        connect(m_SonoParameters->parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onVolumeStateChanged()));
    }
}

void VolumeControlTool::execute()
{
    if (m_SonoParameters != NULL && !m_curVolumeStr.isEmpty() && m_toolEnabled)
    {
        if (m_SonoParameters->pBV(BFPNames::FreezeStr))
        {
            return;
        }
        QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
        if (args != NULL)
        {
            bool arg = isInvertMulti() ? !args->arg() : args->arg();
            m_SonoParameters->parameter(m_curVolumeStr)->multi(arg, false, 10);
        }
    }
}

FreeMAngleTool::FreeMAngleTool()
    : m_Step(-1)
{
}

FreeMAngleTool::FreeMAngleTool(const QString& name, int step)
    : MenuTool(name)
    , m_Parameter(name)
    , m_Step(step)
{
}

FreeMAngleTool::FreeMAngleTool(const QString& toolName, const QString& parameter, int step)
    : MenuTool(toolName)
    , m_Parameter(parameter)
    , m_Step(step)
{
    setIsLoopMulti(true);
}

void FreeMAngleTool::onInitialize()
{
    MenuTool::onInitialize();
    if (m_SonoParameters != NULL)
    {
        connect(m_SonoParameters->parameter(BFPNames::FreeMLineNoStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onLineIndexChanged(QVariant)));
    }
}

void FreeMAngleTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
        if (args != NULL)
        {
            bool arg = isInvertMulti() ? args->arg() : !args->arg();
            m_SonoParameters->parameter(m_Parameter)->multi(arg, isLoopMulti(), m_Step);
        }
        else
        {
            m_SonoParameters->parameter(m_Parameter)->multi(true, true);
        }
    }
}

void FreeMAngleTool::onBeforeCurSonoParametersChanged()
{
    if (m_SonoParameters != NULL)
    {
        Parameter* p = m_SonoParameters->parameter(m_Parameter);
        disconnect(p, SIGNAL(valueChanged(QVariant)), this, SLOT(onToolValueChanged(QVariant)));
        disconnect(p, SIGNAL(enabledChanged(bool)), this, SLOT(setIsAvailable(bool)));
    }
}

void FreeMAngleTool::onCurSonoParametersChanged()
{
    if (isPostProcessTool())
    {
        m_SonoParameters = m_BufferStoreManager->activeSonoParameters();
    }
    else
    {
        m_SonoParameters = m_BufferStoreManager->curSonoParameters();
    }
    if (m_SonoParameters != NULL)
    {
        Parameter* p = m_SonoParameters->parameter(m_Parameter);
        connect(p, SIGNAL(valueChanged(QVariant)), this, SLOT(onToolValueChanged(QVariant)));
        connect(p, SIGNAL(enabledChanged(bool)), this, SLOT(setIsAvailable(bool)));
        setIsAvailable(p->isEnabled());

        //设置参数时，先初始化ToolValue
        onToolValueChanged(p->value());
    }
}

void FreeMAngleTool::onToolValueChanged(const QVariant& value)
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        setToolValue(m_SonoParameters->parameter(m_Parameter)->text());
    }
}

void FreeMAngleTool::onLineIndexChanged(const QVariant& value)
{
    m_Parameter.chop(1);
    m_Parameter.append(QString::number(value.toInt()));
    onBeforeCurSonoParametersChanged();
    onCurSonoParametersChanged();
}

FourDZoomTool::FourDZoomTool()
{
}

FourDZoomTool::FourDZoomTool(const QString& name)
    : MenuTool(name)
{
}

void FourDZoomTool::execute()
{
    QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
    if (args != NULL)
    {
        bool arg = isInvertMulti() ? !args->arg() : args->arg();
        emit fourdZoomIn(arg);
    }
}

FreeHand3DZoomTool::FreeHand3DZoomTool()
{
}

FreeHand3DZoomTool::FreeHand3DZoomTool(const QString& name)
    : MenuTool(name)
{
}

void FreeHand3DZoomTool::execute()
{
    QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
    if (args != NULL)
    {
        bool arg = isInvertMulti() ? !args->arg() : args->arg();
        emit freeHand3DZoomIn(arg);
    }
}

FreeHand3DResetTool::FreeHand3DResetTool()
{
}

FreeHand3DResetTool::FreeHand3DResetTool(const QString& name)
    : MenuTool(name)
{
}

void FreeHand3DResetTool::execute()
{
    emit freeHand3Dreset();
}

LGCTool::LGCTool(const QString& name, BaseDialog* dialog)
    : MenuTool(name)
    , m_Regulator(dynamic_cast<LGCRegulatorDialog*>(dialog))
{
}

void LGCTool::retranslateUi(QWidget*)
{
}

void LGCTool::onSetSonoParameters()
{
    if (m_Regulator == nullptr)
        return;
    m_Regulator->setSonoParameters(m_SonoParameters);
    m_SonoParameters->parameter(BFPNames::LGCControlEnStr)
        ->setEnabled(m_SonoParameters->pBV(BFPNames::PhaseProbeIdStr) &&
                     AppSetting::isFunctionEnabled(LicenseItemKey::KeyLGC));
    connect(m_SonoParameters->parameter(BFPNames::LGCControlEnStr), SIGNAL(enabledChanged(bool)), this,
            SLOT(onEnableChanged()), Qt::UniqueConnection);
    onEnableChanged();
}

void LGCTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
}

void LGCTool::onEnableChanged()
{
    setIsVisible(m_SonoParameters->parameter(BFPNames::LGCControlEnStr)->isEnabled());
}

void LGCTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        bool enable = m_SonoParameters->pBV(BFPNames::LGCControlEnStr);
        m_SonoParameters->setPV(BFPNames::LGCControlEnStr, !enable);
    }
}

PostChangeTool::PostChangeTool()
{
}

PostChangeTool::PostChangeTool(const QString& name)
    : ChangeTool(name)
{
}

void PostChangeTool::setCurrentMenus()
{
    m_MenuNames.clear();
    m_CurrentIndex = 0;
    if (m_SonoParameters != NULL)
    {
        SystemScanMode mode = (SystemScanMode)(m_SonoParameters->pIV(BFPNames::SystemScanModeStr));
        m_MenuNames.append(MenuNames::FreezeMenuStr);
        m_MenuNames.append(MenuNames::BMenuStr);
        switch (mode)
        {
        case SystemScanModeSonoNeedle:
            m_MenuNames.append(MenuNames::SonoNeedleMenuStr);
            m_CurrentMenu = MenuNames::SonoNeedleMenuStr;
            break;
        case SystemScanModeB:
        case SystemScanMode2B:
        case SystemScanMode4B:
        case SystemScanModeFourDPre:
            // m_MenuNames.append(MenuNames::Freeze3DMenuStr);
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                }
                else
                {
                    //判断进入BPW-pre的状态
                    m_MenuNames.append(MenuNames::DPREMenuStr);
                }
            }
            if (m_SonoParameters->pBV(BFPNames::SonoNerveStr))
            {
                m_MenuNames.append(MenuNames::SonoNerveMenuStr);
            }
            if (m_SonoParameters->pBV(BFPNames::SonoMSKStr))
            {
                m_MenuNames.append(MenuNames::SonoMSKMenuStr);
            }
            break;
        case SystemScanModeCP:
            m_MenuNames.removeOne(MenuNames::BMenuStr);
            if (m_SonoParameters->isRealTime())
            {
                m_MenuNames.removeOne(MenuNames::FreezeMenuStr);
            }
            m_MenuNames.append(MenuNames::CurvedPanoramicMenuStr);
            m_MenuNames.append(MenuNames::UtilityMenu);
            break;
        case SystemScanModeColorDoppler:
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                m_MenuNames.append(MenuNames::CMenuStr);
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                }
                else
                {
                    //判断进入BPW-pre的状态
                    m_MenuNames.append(MenuNames::DPREMenuStr);
                }
            }
            else
            {
                m_MenuNames.append(MenuNames::CMenuStr);
            }
            break;
        case SystemScanModePowerDoppler:
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                m_MenuNames.append(MenuNames::CPAMenuStr);
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                }
                else
                {
                    //判断进入BPW-pre的状态
                    m_MenuNames.append(MenuNames::DPREMenuStr);
                }
            }
            else
            {
                m_MenuNames.append(MenuNames::CPAMenuStr);
            }
            break;
        case SystemScanModeDPowerDoppler:
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                m_MenuNames.append(MenuNames::DPDMenuStr);
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                }
                else
                {
                    //判断进入BPW-pre的状态
                    m_MenuNames.append(MenuNames::DPREMenuStr);
                }
            }
            else
            {
                m_MenuNames.append(MenuNames::DPDMenuStr);
            }
            break;
        case SystemScanModeTissueDoppler:
            if (m_SonoParameters->pBV(
                    BFPNames::IsDopplerScanLineVisibleStr)) // TODO IsCWDScanLineVisibleStr CWPREMenuStr
            {
                m_MenuNames.append(MenuNames::TDIMenuStr);
                if (m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr)) // 判断进入CW-pre状态
                {
                    m_MenuNames.append(MenuNames::CWDPreMenuStr);
                }
                else
                {
                    //判断进入BPW-pre的状态
                    m_MenuNames.append(MenuNames::DTDIPreMenuStr);
                }
            }
            else
            {
                m_MenuNames.append(MenuNames::TDIMenuStr);
            }
            break;
        case SystemScanModeBPW:
            m_MenuNames.append(MenuNames::DMenuStr);
            break;
        case SystemScanModeColorPW:
            m_MenuNames.append(MenuNames::CMenuStr);
            m_MenuNames.append(MenuNames::DMenuStr);
            break;
        case SystemScanModePowerPW:
            m_MenuNames.append(MenuNames::CPAMenuStr);
            m_MenuNames.append(MenuNames::DMenuStr);
            break;
        case SystemScanModeDPowerPW:
            m_MenuNames.append(MenuNames::DPDMenuStr);
            m_MenuNames.append(MenuNames::DMenuStr);
            break;
        case SystemScanModeTissuePW:
            m_MenuNames.append(MenuNames::TDIMenuStr);
            m_MenuNames.append(MenuNames::DTDIMenuStr);
            break;
        case SystemScanModeCWD:
            m_MenuNames.append(MenuNames::CWDMenuStr);
            break;
        case SystemScanModeCWDColorDoppler:
            m_MenuNames.append(MenuNames::CMenuStr);
            m_MenuNames.append(MenuNames::CWDMenuStr);
            break;
        case SystemScanModeCWDPowerDoppler:
            m_MenuNames.append(MenuNames::CPAMenuStr);
            m_MenuNames.append(MenuNames::CWDMenuStr);
            break;
        case SystemScanModeCWDDirectionalPowerDoppler:
            m_MenuNames.append(MenuNames::DPDMenuStr);
            m_MenuNames.append(MenuNames::CWDMenuStr);
            break;
        case SystemScanModeM:
        case SystemScanModeLRBM:
        case SystemScanModeUDBM:
            m_MenuNames.append(MenuNames::MMenuStr);
            break;
        case SystemScanModeLRFreeM:
        case SystemScanModeUDFreeM:
            m_MenuNames.append(MenuNames::FreeMMenuStr);
            break;
        case SystemScanModeColorM:
        case SystemScanModeColorLRBM:
        case SystemScanModeColorUDBM:
            m_MenuNames.append(MenuNames::CMenuStr);
            m_MenuNames.append(MenuNames::MMenuStr);
            break;
        case SystemScanModePDM:
        case SystemScanModePDLRBM:
        case SystemScanModePDUDBM:
            m_MenuNames.append(MenuNames::CPAMenuStr);
            m_MenuNames.append(MenuNames::MMenuStr);
            break;
        case SystemScanModeDPDM:
        case SystemScanModeDPDLRBM:
        case SystemScanModeDPDUDBM:
            m_MenuNames.append(MenuNames::DPDMenuStr);
            m_MenuNames.append(MenuNames::MMenuStr);
            break;
        case SystemScanModeTDIM:
        case SystemScanModeTDILRBM:
        case SystemScanModeTDIUDBM:
            m_MenuNames.append(MenuNames::TDIMenuStr);
            m_MenuNames.append(MenuNames::MMenuStr);
            break;
        case SystemScanModeE:
            m_MenuNames.append(MenuNames::EMenuStr);
            break;
        case SystemScanModeMVI:
            m_MenuNames.append(MenuNames::MVIMenuStr);
            break;
        case SystemScanModeMVIPW:
            m_MenuNames.append(MenuNames::MVIMenuStr);
            m_MenuNames.append(MenuNames::DMenuStr);
            break;
        default:
            break;
        }

        if (m_SonoParameters->pBV(BFPNames::SonoCardiacStr) && m_SonoParameters->isRealTime() &&
            AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoCardiac))
        {
            m_MenuNames.append(MenuNames::SonoCardiacMenuStr);
        }
        if (m_SonoParameters->pBV(BFPNames::SonoCarotidGuideStr))
        {
            m_MenuNames.append(MenuNames::SonoGuideMenuStr);
        }
        if (m_SonoParameters->pBV(BFPNames::SonoAAAStr) && m_SonoParameters->isRealTime())
        {
            m_MenuNames.append(MenuNames::SonoAAAMenuStr);
        }
        m_CurrentMenu = MenuNames::FreezeMenuStr;

        if (SystemScanModeB == mode && m_SonoParameters->pBV(BFPNames::StressEchoEnStr) &&
            m_SonoParameters->isRealTime())
        {
            m_MenuNames.append(MenuNames::StressEchoMenuStr);
            m_CurrentMenu = MenuNames::StressEchoMenuStr;
        }
        if (SystemScanModeB == mode && m_SonoParameters->pBV(BFPNames::SonoAAAStr) && m_SonoParameters->isRealTime())
        {
            m_CurrentMenu = MenuNames::SonoAAAMenuStr;
        }
        if (m_SonoParameters->pBV(BFPNames::EFastModeONStr))
        {
            m_MenuNames.append(MenuNames::EFastMenuStr);
        }
        if (m_SonoParameters->pBV(BFPNames::FastModeONStr))
        {
            m_MenuNames.append(MenuNames::FastMenuStr);
        }
    }
}

void PostChangeTool::onInitialize()
{
    ChangeTool::onInitialize();
    if (NULL == m_SonoParameters || !m_SonoParameters->isRealTime())
    {
        return;
    }

    connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
            SLOT(onBeforeProbeIdChanged(QVariant, QVariant&)));
    m_ToolFilter = QStringList() << ToolNames::StressEchoEnStr << ToolNames::StressEchoTemplateStr
                                 << ToolNames::StressEchoT1ControlStr << ToolNames::StressEchoT2ControlStr
                                 << ToolNames::StressEchoAnalyzeStr << ToolNames::CurvedPanoramicExitStr;
}

void PostChangeTool::execute()
{
    if (m_SonoParameters != NULL && (m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr)))
    {
        ChangeTool::execute();
    }
    else
    {
        QString menuName = m_CurrentMenu;
        ChangeTool::execute();
        if (QString::compare(menuName, m_CurrentMenu, Qt::CaseInsensitive) != 0)
        {
            onMenuChanged();
        }
    }
}

void PostChangeTool::onBeforeCurSonoParametersChanged()
{
    ChangeTool::onBeforeCurSonoParametersChanged();
    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanging(QVariant)), this,
                   SLOT(onFreezeChanging(QVariant)));
    }
}

void PostChangeTool::onCurSonoParametersChanged()
{
    ChangeTool::onCurSonoParametersChanged();
    connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanging(QVariant)), this,
            SLOT(onFreezeChanging(QVariant)));
    // 由于实时保存的数据中冻结参数为false，需要isRealTime来辅助
    if (m_SonoParameters != NULL && (m_SonoParameters->pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime()))
    {
        setCurrentMenus();
        foreach (const QString& menu, m_MenuNames)
        {
            SetControlMenuToolAvailable(menu);
        }

        MenuController::instance().setCurvedMenu(false);

        // TODO: BufferStoreManager::curSonoParametersChanged()
        //      信号触发了UI刷新和tool状态变更两个动作
        //      但是没有规范的控制他们的执行顺序 导致回调的菜单可能出现异常.
        //      目前先临时在这里刷新一下tool的状态.
        SonoCardiacTool* tool =
            dynamic_cast<SonoCardiacTool*>(ToolsFactory::instance().tool(ToolNames::SonoCardiacStr));
        if (tool != NULL)
        {
            tool->changeItemStatus();
        }

        SonoDiaphTool* diaphTool = dynamic_cast<SonoDiaphTool*>(ToolsFactory::instance().tool(ToolNames::SonoDiaphStr));
        if (diaphTool != NULL)
        {
            diaphTool->onFreezeChanging(true);
        }

        if (MenuController::instance().isShowingLeftMenu() && !StateManager::getInstance().isStressechoEn())
        {
            MenuController::instance().createMenu(MenuNames::FreezeMenuStr);
            m_CurrentIndex = 0;
        }
    }
}

void PostChangeTool::onFreezeChanging(const QVariant& value)
{
    if (!value.toBool())
    {
        restoreChangedMenusToolAvailable();
    }
    else
    {
        foreach (const QString& menu, m_MenuNames)
        {
            SetControlMenuToolAvailable(menu);
        }
        m_CurrentIndex = 0;
        m_CurrentMenu = MenuNames::FreezeMenuStr;
    }
}

void PostChangeTool::onBeforeProbeIdChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
}

void PostChangeTool::controlMenuToolAvailable(const QList<ITool*>* tools)
{
    if (tools == NULL)
    {
        return;
    }

    bool isPostProcess = (!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr));
    foreach (ITool* tool, *tools)
    {
        if (tool != NULL)
        {
            if (!m_ToolFilter.contains(tool->toolName()))
            {
                if (isPostProcess)
                {
                    bool isPostAvailable = tool->isPostProcessTool();
                    bool isRealTimeAvailable = isToolRealTimeAvailable(tool);
                    tool->setIsAvailable((isPostAvailable && isRealTimeAvailable));
                }
                else
                {
                    Parameter* parameter = m_SonoParameters->parameter(tool->toolName());
                    tool->setIsAvailable(parameter->isNull() ? true : parameter->isEnabled());
                }
            }
        }
    }
}

void PostChangeTool::onMenuChanged()
{
    SetControlMenuToolAvailable(m_CurrentMenu);
}

bool PostChangeTool::isToolRealTimeAvailable(const ITool* tool)
{
    if (NULL == tool || NULL == m_SonoParameters)
    {
        return false;
    }

    Parameter* parameter = m_SonoParameters->parameter(tool->toolName());
    if (!parameter->isNull())
    {
        return parameter->isEnabled();
    }

    return tool->isAvailable();
}

void PostChangeTool::SetControlMenuToolAvailable(QString menuName)
{
    if (m_SonoParameters != NULL && !m_ChangedMenus.contains(menuName))
    {
        controlMenuToolAvailable(MenuController::instance().leftMenuTools(menuName));
        controlMenuToolAvailable(MenuController::instance().bottomMenuTools(menuName));
        m_ChangedMenus.append(menuName);
    }
}

void PostChangeTool::restoreChangedMenusToolAvailable()
{
    if (NULL == m_SonoParameters)
    {
        return;
    }

    foreach (QString name, m_ChangedMenus)
    {
        controlMenuToolAvailable(MenuController::instance().leftMenuTools(name));
        controlMenuToolAvailable(MenuController::instance().bottomMenuTools(name));
    }
    m_ChangedMenus.clear();
}

VariableMenuTool::VariableMenuTool()
{
}

VariableMenuTool::VariableMenuTool(const QString& realTimeName, const QString& freezeName)
    : MenuTool(realTimeName)
{
    m_RealTimeTool = realTimeName;
    m_FreezeTool = freezeName;
}

void VariableMenuTool::onSetSonoParameters()
{
    MenuTool::onSetSonoParameters();
    if (m_SonoParameters != NULL)
    {
        onFreezeChanging(m_SonoParameters->pV(BFPNames::FreezeStr));
    }
}

void VariableMenuTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanging(QVariant)), this,
                   SLOT(onFreezeChanging(QVariant)));
    }
}

void VariableMenuTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (m_SonoParameters != NULL)
    {
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanging(QVariant)), this,
                SLOT(onFreezeChanging(QVariant)));
    }
}

void VariableMenuTool::onFreezeChanging(const QVariant& value)
{
    QString toolName = value.toBool() || !m_SonoParameters->isRealTime() ? m_FreezeTool : m_RealTimeTool;
    if (this->toolName() != toolName)
    {
        disconnect(m_SonoParameters->parameter(this->toolName()), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onToolValueChanged(QVariant)));
        this->setToolName(toolName);
        connect(m_SonoParameters->parameter(toolName), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onToolValueChanged(QVariant)));
        onToolValueChanged(m_SonoParameters->pV(toolName));
    }
}

CenterLineTool::CenterLineTool(const QString& name)
    : MenuTool(name)
{
}

void CenterLineTool::execute()
{
    if (m_SonoParameters->parameter(BFPNames::IsCenterLineVisibleStr)->isEnabled())
    {
        m_SonoParameters->setPV(BFPNames::IsCenterLineVisibleStr,
                                !m_SonoParameters->pBV(BFPNames::IsCenterLineVisibleStr));
    }
}

TDIBMenuShowTool::TDIBMenuShowTool(const QString& name)
    : MenuTool(name)
    , m_CanRefeshLeftMenu(false)
{
    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this,
            &TDIBMenuShowTool::onFunctionStatusChanged);
}

void TDIBMenuShowTool::onInitialize()
{
    MenuTool::onInitialize();
    updateTDI();
}

void TDIBMenuShowTool::execute()
{
    if (nullptr != m_SonoParameters)
    {
        //需要判断当前预设是否支持TDI功能
        if (StateManager::getInstance().isSupportTDI() && m_SonoParameters->pBV(BFPNames::SupportTDIStr))
        {
            StateManager::getInstance().postEvent(StateEventNames::TDI);
        }
    }
}

void TDIBMenuShowTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), &Parameter::valueChanged, this,
                   &TDIBMenuShowTool::onProbeChanged);
        disconnect(m_SonoParameters->parameter(BFPNames::SupportTDIStr), &Parameter::valueChanged, this,
                   &TDIBMenuShowTool::onIsSupportTDIChanged);
        disconnect(m_SonoParameters, &SonoParameters::presetChanged, this, &TDIBMenuShowTool::onPresetChanged);
    }
}

void TDIBMenuShowTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), &Parameter::valueChanged, this,
                &TDIBMenuShowTool::onProbeChanged);
        connect(m_SonoParameters->parameter(BFPNames::SupportTDIStr), &Parameter::valueChanged, this,
                &TDIBMenuShowTool::onIsSupportTDIChanged);
        connect(m_SonoParameters, &SonoParameters::presetChanged, this, &TDIBMenuShowTool::onPresetChanged);
    }
    updateTDI();
}

void TDIBMenuShowTool::onProbeChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updateTDI();
}

void TDIBMenuShowTool::onFunctionStatusChanged()
{
    updateTDI();
}

void TDIBMenuShowTool::onIsSupportTDIChanged(const QVariant& value, bool changed)
{
    updateTDI();
    if (changed && value.toBool() == true && m_CanRefeshLeftMenu)
    {
        MenuController::instance().createLeftMenu();
    }
}

void TDIBMenuShowTool::onPresetChanged()
{
    m_CanRefeshLeftMenu = true;

    updateTDI();

    // BUG:73989 若3S或5S探头插D口，关机重启后，进入B模式，左侧B Menu菜单中没有显示TDI按钮
    //调查发现，出现问题时，插入多把探头，creaLeftMenu在onProbeIdChanged到3S或5S探头之前执行，在切到3S或5S之前，TDI功能是不可用的，所以才产生了Bug中的问题
    //暂定解决方案，在切换探头之后，在onPresetChanged时，重新create一下左侧菜单
    // TODO：需要找到creaLeftMenu在onProbeIdChanged到3S或5S探头之前执行的具体原因，根本解决问题。
    MenuController::instance().createLeftMenu(MenuNames::BMenuStr);
}

void TDIBMenuShowTool::updateTDI()
{
    if (nullptr != m_SonoParameters)
    {
        //需要判断当前预设是否支持TDI功能
        if (StateManager::getInstance().isSupportTDI() && m_SonoParameters->pBV(BFPNames::SupportTDIStr))
        {
            setIsVisible(true);

            //[bug:65693]【zeus3.0】回调图像时，B模式参数TDI未置灰
            //
            if (m_SonoParameters->isRealTime())
            {
                setIsAvailable(true);
            }
            else
            {
                setIsAvailable(false);
            }
        }
        else
        {
            setIsVisible(false);
            setIsAvailable(false);
        }
    }
}

CQYZLevelTool::CQYZLevelTool()
{
}

CQYZLevelTool::CQYZLevelTool(const QString& name)
    : CQYZTool(name)
{
}

void CQYZLevelTool::retranslateUi(QWidget* widget)
{
}

BRulerTool::BRulerTool(const QString& name)
    : MenuTool(name)
{
}

void BRulerTool::execute()
{
    if (m_SonoParameters->parameter(BFPNames::IsBHorizontalRulerVisibleStr)->isEnabled())
    {
        m_SonoParameters->setPV(BFPNames::IsBHorizontalRulerVisibleStr,
                                !m_SonoParameters->pBV(BFPNames::IsBHorizontalRulerVisibleStr));
    }
}

#include "panzoomthumbnail.h"
ImageZoomCoefTool::ImageZoomCoefTool(QGraphicsView* panZoomThumbnail, ToolsOrderManager* toolsOrderManager,
                                     const QString& name)
    : MenuTool(name)
    , m_PanZoomThumbnail(dynamic_cast<PanZoomThumbnail*>(panZoomThumbnail))
    , m_EnableDealZoomMouseTool(true)
    , m_LastValue(-1)
    , m_ToolsOrderManager(toolsOrderManager)
{
}

void ImageZoomCoefTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
        if (args->getType() == COMMAND_TYPE_BOOL)
        {
            //解决第一次点击menu下的Advance按键，zoomcoef改变
            if (args != NULL)
            {
                bool arg = isInvertMulti() ? !args->arg() : args->arg();
                m_SonoParameters->parameter(this->toolName())->multi(arg, isLoopMulti());
            }
        }
        else
        {
            int value = args->getValue().toInt();
            value = value - value % stepValue();
            m_SonoParameters->parameter(this->toolName())->setValue(value);
        }

        ToolsFactory::instance().command(ToolNames::MeasurementDeleteStr)->run();
        onToolValueChanged(m_SonoParameters->pV(this->toolName()));
    }
}

void ImageZoomCoefTool::onSetSonoParameters()
{
    m_PanZoomThumbnail->setSonoParameters(m_SonoParameters);
    m_ToolsOrderManager->setSonoParameters(m_SonoParameters);
}

void ImageZoomCoefTool::onMousePress()
{
    m_EnableDealZoomMouseTool = false;
}

void ImageZoomCoefTool::onMouseRelease()
{
    m_EnableDealZoomMouseTool = true;
    dealImageZoomMouseTool();
}

void ImageZoomCoefTool::dealImageZoomMouseTool()
{
    if (!m_EnableDealZoomMouseTool)
    {
        return;
    }

    if (m_LastValue > 100)
    {
        if (m_ToolsOrderManager->isActiveToolDisableLoopNext())
        {
            //添加到toolorder的列表中，但是不run
            m_ToolsOrderManager->addTool(ToolNames::PanZoomMouseStr, false);
        }
        else
        {
            StateManager::getInstance().runTool(ToolNames::PanZoomMouseStr);
        }
    }
    else
    {
        ICommand* command = ToolsFactory::instance().command(ToolNames::PanZoomMouseStr);
        if ((command != nullptr) && (command->isRunning() || m_ToolsOrderManager->containsTool(command->toolName())))
        {
            StateManager::getInstance().stopTool(ToolNames::PanZoomMouseStr);
        }
    }
}

void ImageZoomCoefTool::onToolValueChanged(const QVariant& value)
{
    if (m_SonoParameters == NULL)
    {
        return;
    }

    //如果 panzoom 下 电影回调，进入FreezePanZoom状态 导致崩溃， 所以增加这个判断
    if ((value != 100) && QString::compare(StateManager::getInstance().postEventName().toStdString().c_str(),
                                           StateEventNames::BeginLoad) != 0)
    {
        if (value.toInt() > 100)
        {
            m_SonoParameters->setPV(BFPNames::PanZoomSelectStr, true);
            m_PanZoomThumbnail->init();
            m_PanZoomThumbnail->setVisible(true);
        }
        else if (m_PanZoomThumbnail != NULL)
        {
            m_PanZoomThumbnail->setVisible(false);
        }
    }
    else //回调一个panzoom 的电影
    {
        if (value.toInt() > 100)
        {
            m_PanZoomThumbnail->init();
            m_PanZoomThumbnail->setVisible(true);
            StateManager::getInstance().setIsInPanZoom(true);
        }
        else
        {
            m_PanZoomThumbnail->setVisible(false);
            StateManager::getInstance().setIsInPanZoom(false);
        }
    }

    if (m_LastValue != value.toInt())
    {
        m_LastValue = value.toInt();
        dealImageZoomMouseTool();
    }
    MenuTool::onToolValueChanged(value);
}

BaseLineTool::BaseLineTool(const QString& name)
    : MenuTool(name)
{
}

void BaseLineTool::execute()
{
    MenuTool::execute();
    if (!m_SonoParameters->pBV(BFPNames::QuadplexModeStr))
    {
        ToolsFactory::instance().command(ToolNames::MeasurementDeleteStr)->run();
    }
}

DPDInvertStateTool::DPDInvertStateTool(const QString& name)
    : MenuTool(name)
{
}

int DPDInvertStateTool::value() const
{
    if (isParameterTool() && m_SonoParameters != NULL)
    {
        return m_SonoParameters->parameter(m_ToolName)->value().toBool() ? 1 : 0;
    }
    return 0;
}

void DPDInvertStateTool::onToolValueChanged(const QVariant& value)
{
    setToolValue(value.toBool() ? "false" : "true");
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (tool != NULL)
    {
        tool->run();
    }
}

void DPDInvertStateTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        bool value = m_SonoParameters->pBV(this->toolName());
        m_SonoParameters->setPV(this->toolName(), !value);
        ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
        if (tool != NULL)
        {
            tool->run();
        }
    }
}

void DPDInvertStateTool::onSetSonoParameters()
{
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (tool != NULL)
    {
        tool->run();
    }
}

TDIInvertStateTool::TDIInvertStateTool(const QString& name)
    : MenuTool(name)
{
}

int TDIInvertStateTool::value() const
{
    if (isParameterTool() && nullptr != m_SonoParameters)
    {
        return m_SonoParameters->parameter(m_ToolName)->value().toBool() ? 1 : 0;
    }
    return 0;
}

void TDIInvertStateTool::onToolValueChanged(const QVariant& value)
{
    setToolValue(value.toBool() ? "false" : "true");
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (nullptr != tool)
    {
        tool->run();
    }
}

void TDIInvertStateTool::execute()
{
    if (nullptr != m_SonoParameters)
    {
        bool value = m_SonoParameters->pBV(this->toolName());
        m_SonoParameters->setPV(this->toolName(), !value);
        ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
        if (nullptr != tool)
        {
            tool->run();
        }
    }
}

void TDIInvertStateTool::onSetSonoParameters()
{
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (nullptr != tool)
    {
        tool->run();
    }
}

MVIInvertStateTool::MVIInvertStateTool(const QString& name)
    : MenuTool(name)
{
}

int MVIInvertStateTool::value() const
{
    if (isParameterTool() && nullptr != m_SonoParameters)
    {
        return m_SonoParameters->parameter(m_ToolName)->value().toBool() ? 1 : 0;
    }
    return 0;
}

void MVIInvertStateTool::onToolValueChanged(const QVariant& value)
{
    setToolValue(value.toBool() ? "false" : "true");
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (nullptr != tool)
    {
        tool->run();
    }
}

void MVIInvertStateTool::execute()
{
    if (nullptr != m_SonoParameters)
    {
        if (m_SonoParameters->pIV(BFPNames::MVITypeStr) != 2)
        {
            return; // Only MVIType3 support to invert
        }

        bool value = m_SonoParameters->pBV(this->toolName());
        m_SonoParameters->setPV(this->toolName(), !value);
        ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
        if (nullptr != tool)
        {
            tool->run();
        }
    }
}

void MVIInvertStateTool::onSetSonoParameters()
{
    ITool* tool = ToolsFactory::instance().tool(ToolNames::UDLightStr);
    if (nullptr != tool)
    {
        tool->run();
    }
}

ECGEnTool::ECGEnTool(const QString& name, ILineBufferManager* lineBufferManager)
    : MenuTool(name)
    , m_LineBufferManager(lineBufferManager)
{
}

void ECGEnTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        bool enable = m_SonoParameters->pBV(BFPNames::ECGEnStr);
        m_SonoParameters->setPV(BFPNames::ECGEnStr, !enable);
        if (m_LineBufferManager != nullptr)
        {
            m_LineBufferManager->removeAll();
        }

        StringCommandArgs args;
        args.setArg(!enable ? MenuNames::ECGMenuStr : MenuNames::BMenuStr);
        ICommand* changeTool = ToolsFactory::instance().command(ToolNames::ChangeStr);
        changeTool->setArgs(&args);
        changeTool->run();
        dynamic_cast<ChangeTool*>(changeTool)->setCurrentMenus();
    }
}

void ECGEnTool::onInitialize()
{
    MenuTool::onInitialize();
    if (m_SonoParameters != nullptr)
    {
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSystemScanModeChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::RotationStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onRotationChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::PhaseProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onPhaseProbeIdChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreezeChanged(QVariant)));
        m_SonoParameters->parameter(BFPNames::ECGEnStr)->setEnabled(m_SonoParameters->pBV(BFPNames::PhaseProbeIdStr));
    }
    connect(AppSetting::eventObject(), SIGNAL(functionChanged(int, bool)), this, SLOT(onFunctionChanged(int, bool)));
    m_IsLicenseOpen = AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeyECG);
    setIsVisible(m_IsLicenseOpen && m_SonoParameters->pBV(BFPNames::PhaseProbeIdStr));
    if (m_IsLicenseOpen)
    {
        m_SonoParameters->parameter(BFPNames::ECGEnStr)->setEnabled(isSupportEcg());
    }
}

bool ECGEnTool::isSupportEcg()
{
    bool isSystemScanModeECGEnable =
        (m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanMode::SystemScanModeB ||
         m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanMode::SystemScanMode2B ||
         m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanMode::SystemScanMode4B);
    // 放开 ECG对图像模式的限制，C/M/D模式 支持ECG
    if (ModelConfig::instance().value(ModelConfig::IsAllImageModeSupportECG, false).toBool())
    {
        isSystemScanModeECGEnable = true;
    }
    return m_IsLicenseOpen && m_SonoParameters->pBV(BFPNames::PhaseProbeIdStr) &&
           !m_SonoParameters->pBV(BFPNames::RotationStr) && !m_SonoParameters->pBV(BFPNames::FreezeStr) &&
           isSystemScanModeECGEnable;
}

void ECGEnTool::onRotationChanged(QVariant value)
{
    if (m_SonoParameters->pBV(BFPNames::ECGEnStr))
    {
        m_SonoParameters->setPV(BFPNames::ECGEnStr, false);
    }

    m_SonoParameters->parameter(BFPNames::ECGEnStr)->setEnabled(isSupportEcg());
}

void ECGEnTool::onPhaseProbeIdChanged(QVariant value)
{
    if (m_IsLicenseOpen)
    {
        setIsVisible(value.toBool());
    }
}

void ECGEnTool::onFunctionChanged(int key, bool enable)
{
    if (key == LicenseItemKey::LicenseKeyType::KeyECG)
    {
        m_IsLicenseOpen = enable;
        m_SonoParameters->parameter(BFPNames::ECGEnStr)->setEnabled(isSupportEcg());
    }

    if (key == LicenseItemKey::LicenseKeyType::KeyECG && !enable)
    {
        if (m_SonoParameters->pBV(BFPNames::ECGEnStr))
        {
            m_SonoParameters->setPV(BFPNames::ECGEnStr, false);
        }
    }
}

void ECGEnTool::onSystemScanModeChanged(QVariant value)
{
    if (ModelConfig::instance().value(ModelConfig::IsAllImageModeSupportECG, false).toBool())
    {
        // 放开 ECG对图像模式的限制，C/M/D模式 支持ECG
        return;
    }

    if (value.toInt() != SystemScanModeB)
    {
        if (m_SonoParameters->pBV(BFPNames::ECGEnStr))
        {
            m_SonoParameters->setPV(BFPNames::ECGEnStr, false);
        }
        m_SonoParameters->parameter(BFPNames::ECGEnStr)->setEnabled(false);
    }
    else
    {
        m_SonoParameters->parameter(BFPNames::ECGEnStr)->setEnabled(isSupportEcg());
    }
}

void ECGEnTool::onFreezeChanged(QVariant value)
{
    if (m_IsLicenseOpen)
    {
        m_SonoParameters->parameter(BFPNames::ECGEnStr)->setEnabled(isSupportEcg());
    }
    if (value.toBool() == false)
    {
        EcgSettings::instance().setRLineVisible(false);
        EcgSettings::instance().clearRWave();
    }
    else
    {
        EcgSettings::instance().setRLineVisible(true);
    }
}

void ECGEnTool::onSetSonoParameters()
{
    MenuTool::onSetSonoParameters();
    if (m_IsLicenseOpen)
    {
        setIsVisible(m_SonoParameters->pBV(BFPNames::PhaseProbeIdStr));
    }
}

SonoNeedleTool::SonoNeedleTool(const QString& name)
    : MenuTool(name)
    , m_ProbeDataSet(NULL)
{
    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this, &SonoNeedleTool::updateToolState);
}

void SonoNeedleTool::setProbeDataSet(IProbeDataSet* probeDataSet)
{
    m_ProbeDataSet = probeDataSet;
}

void SonoNeedleTool::onSetSonoParameters()
{
    MenuTool::onSetSonoParameters();
    updateToolState();
}

void SonoNeedleTool::execute()
{
    if (m_SonoParameters != NULL)
    {
        MenuTool::execute();
        if (m_SonoParameters->pBV(BFPNames::SonoNeedleStr))
        {
            StateManager::getInstance().postEvent(StateEventNames::SonoNeedle);
        }
        else
        {
            StateManager::getInstance().postEvent(StateEventNames::SonoNeedleExit);
        }
    }
}

void SonoNeedleTool::updateToolState()
{
    if (nullptr == m_SonoParameters)
    {
        return;
    }

    if (!AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoNeedle))
    {
        setIsVisible(false);
        setIsAvailable(false);
    }
    else
    {
        bool b = (Resource::supportProbeId(Resource::FunctionName::SonoNeedle)
                      .contains(QString::number(m_SonoParameters->pIV(BFPNames::ProbeIdStr))));
        setIsVisible(b);
        setIsAvailable(b);
    }
}

void SonoNeedleTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onProbeChanged(QVariant)));
    }
}

void SonoNeedleTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeChanged(QVariant)));
    }
}

void SonoNeedleTool::onProbeChanged(const QVariant& value)
{
    updateToolState();
}

ExitFromSonoNeedle::ExitFromSonoNeedle(QString name)
    : MenuTool(name)
{
}

void ExitFromSonoNeedle::execute()
{
    StateManager::getInstance().postEvent(StateEventNames::SonoNeedleExit);
}

MVITool::MVITool(const QString& name)
    : MenuTool(name)
{
    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this,
            &MVITool::onFunctionStatusChanged);
    connect(&MenuController::instance(), &MenuController::menuChange, this, &MVITool::updateMVI);
}

void MVITool::onInitialize()
{
    MenuTool::onInitialize();
}

void MVITool::execute()
{
    if (StateManager::getInstance().isSupportMVI())
    {
        MenuTool::execute();
        StateManager::getInstance().postEvent(StateEventNames::MVI);
    }
}

void MVITool::onProbeChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updateMVI();
}

void MVITool::onSystemScanModeChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updateMVI();
}

void MVITool::onFreqSpectrumChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updateMVI();
}

void MVITool::onLayoutChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updateMVI();
}

void MVITool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onProbeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSystemScanModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFreqSpectrumChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onLayoutChanged(QVariant)));
    }
}

void MVITool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSystemScanModeChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreqSpectrumChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onLayoutChanged(QVariant)));
    }
}

void MVITool::onFunctionStatusChanged()
{
    updateMVI();
}

void MVITool::updateMVI()
{
    bool isSupportMVI = StateManager::getInstance().isSupportMVI();
    bool isMVISwitchable = StateManager::getInstance().isMVISwitchable();

    setIsVisible(isSupportMVI);
    setIsAvailable(isMVISwitchable);
}

BaselineMVITool::BaselineMVITool(const QString& name)
    : MenuTool(name)
{
}

void BaselineMVITool::onInitialize()
{
    MenuTool::onInitialize();

    if (m_SonoParameters != nullptr)
    {
        connect(m_SonoParameters->parameter(BFPNames::MVITypeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onMVITypeChanged(QVariant)));
    }
}

void BaselineMVITool::onMVITypeChanged(const QVariant& value)
{
    int mviType = value.toInt();
    int available = (mviType == 2);

    setIsAvailable(available);
}

BiopsyMenuTool::BiopsyMenuTool(const QString& name)
    : MenuTool(name)
    , m_PreviousMenuCategory() // 初始化为空字符串
{
    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this,
            &BiopsyMenuTool::onFunctionStatusChanged);
}

void BiopsyMenuTool::onFunctionStatusChanged()
{
    updateBiopsy();
}

void BiopsyMenuTool::onProbeIdChanged(const QVariant& value)
{
    updateBiopsy();
}

void BiopsyMenuTool::updateBiopsy()
{
    QStringList supportProbes =
        ModelConfig::instance().value(ModelConfig::SupportBiopsyProbes, QStringList()).toStringList();
    bool enable = supportProbes.contains(m_SonoParameters->pSV(BFPNames::ProbeIdStr)) ? true : false;

    setIsVisible(enable);
}

void BiopsyMenuTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        QString currentMenu = MenuController::instance().currentMenuCategory();

        if (currentMenu == MenuNames::BiopsyMenuStr)
        {
            if (!m_PreviousMenuCategory.isEmpty())
            {
                StringCommandArgs args;
                args.setArg(m_PreviousMenuCategory);
                ICommand* changeTool = ToolsFactory::instance().command(ToolNames::ChangeStr);
                changeTool->setArgs(&args);
                changeTool->run();

                m_PreviousMenuCategory.clear();
            }
        }
        else
        {
            m_PreviousMenuCategory = currentMenu;

            StringCommandArgs args;
            args.setArg(MenuNames::BiopsyMenuStr);
            ICommand* changeTool = ToolsFactory::instance().command(ToolNames::ChangeStr);
            changeTool->setArgs(&args);
            changeTool->run();
        }
    }
}

void BiopsyMenuTool::onInitialize()
{
    MenuTool::onInitialize();
    if (m_SonoParameters != nullptr)
    {
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeIdChanged(QVariant)));
        onProbeIdChanged(m_SonoParameters->pV(BFPNames::ProbeIdStr));
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreezeChanged(QVariant)));
    }
}

BiopsyChooseTool::BiopsyChooseTool(const QString& name)
    : MenuTool(name)
    , m_CurGroup(QString())
    , m_StateManager(NULL)
{
}

void BiopsyChooseTool::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void BiopsyChooseTool::execute()
{
    MenuTool::execute();
    if (!isPopupTool())
    {
        QStringList items = getPopupItems();
        QString currentValue = toolValue();
        int index = items.indexOf(currentValue);
        index++;
        int num = items.count();
        if (index >= num)
        {
            index = 0;
        }
        QString nextValue = items.at(index);
        setToolValue(nextValue, true);
    }
}

void BiopsyChooseTool::setToolValue(const QString& toolValue, bool force)
{
    if (!isPopupTool())
    {
        MenuTool::setToolValue(toolValue, force);
    }
    Q_UNUSED(force)
    if (m_SonoParameters != nullptr)
    {
        QStringList items = getPopupItems();

        // 兼容两种格式：
        // 1. TouchableMenuToolItem (PopupTool=true): "索引#文本" 如 "1#BL08-A 30"
        // 2. MenuToolItem (PopupTool=false): "文本" 如 "BL08-A 30"
        int optionIndex = -1;
        QString actualText = toolValue;

        if (isPopupTool())
        {
            QStringList parts = toolValue.split("#");
            if (parts.size() >= 2)
            {
                optionIndex = parts[0].toInt();
                actualText = parts[1];
                if (optionIndex < 0 || optionIndex >= items.count())
                {
                    optionIndex = items.indexOf(actualText);
                }
            }
        }
        else
        {
            optionIndex = items.indexOf(toolValue);
        }

        m_SonoParameters->setPV(BFPNames::BiopsyAngleIndexStr, optionIndex);
        if (actualText.contains(Util::translate("Tool", "Close")))
        {
            m_SonoParameters->setPV(BFPNames::BiopsyAngleChooseStr, 0);
            m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr)->setEnabled(false);
            m_SonoParameters->setPV(BFPNames::BiopsyVerifyStr, false);
            if (m_SonoParameters->pBV(BFPNames::IsBiopsyVisibleStr))
            {
                m_SonoParameters->setPV(BFPNames::IsBiopsyVisibleStr, false);
            }
            return;
        }
        else if (actualText.contains(Util::translate("Tool", "Free")))
        {
            m_SonoParameters->setPV(BFPNames::BiopsyAngleChooseStr, -1);
        }
        else
        {
            int value = actualText.right(2).toInt();
            m_SonoParameters->setPV(BFPNames::BiopsyAngleChooseStr, value);
        }
        QSettings settings(Resource::biopsySettingFileName(), QSettings::IniFormat);
        settings.beginGroup(m_CurGroup);
        bool horizontal;
        int angleMax = 0;
        int angleMin = 0;
        int angle = 0;
        qreal positionMax = 0.0;
        qreal positionMin = 0.0;
        qreal position = 0.0;
        qreal positionY = 0.0;
        int size = settings.beginReadArray("Para");
        for (int i = 0; i < size; i++)
        {
            settings.setArrayIndex(i);
            if (optionIndex == i)
            {
                horizontal = settings.value("horizontal", false).toBool();
                if (horizontal)
                {
                    settings.endArray();
                    settings.endGroup();
                    m_SonoParameters->setPV(BFPNames::BiopsyAngleChooseStr, 0);
                    m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr)->setEnabled(false);
                    if (m_SonoParameters->pBV(BFPNames::IsBiopsyVisibleStr))
                    {
                        m_SonoParameters->setPV(BFPNames::IsBiopsyVisibleStr, false);
                    }
                    return;
                }
                angle = settings.value("AngleSetting").toInt();
                angleMax = settings.value("AngleMax").toInt();
                angleMin = settings.value("AngleMin").toInt();
                position = settings.value("PositionSetting").toReal();
                positionMax = settings.value("PositionMax").toReal();
                positionMin = settings.value("PositionMin").toReal();
                positionY = settings.value("PositionY", 0).toReal();
                break;
            }
        }
        settings.endArray();
        settings.endGroup();
        m_SonoParameters->parameter(BFPNames::BiopsyAngleStr)->setMax(angleMax);
        m_SonoParameters->parameter(BFPNames::BiopsyAngleStr)->setMin(angleMin);
        m_SonoParameters->setPV(BFPNames::BiopsyAngleStr, angle);
        m_SonoParameters->setPV(BFPNames::BiopsyAngleMaxStr, angleMax);
        m_SonoParameters->setPV(BFPNames::BiopsyAngleMinStr, angleMin);
        m_SonoParameters->setPV(BFPNames::BiopsyAngleOffsetStr, 0);
        m_SonoParameters->parameter(BFPNames::BiopsyXPosMMStr)->setMax(positionMax);
        m_SonoParameters->parameter(BFPNames::BiopsyXPosMMStr)->setMin(positionMin);
        m_SonoParameters->setPV(BFPNames::BiopsyXPosMMStr, position);
        m_SonoParameters->setPV(BFPNames::BiopsyPositionMaxStr, positionMax);
        m_SonoParameters->setPV(BFPNames::BiopsyPositionMinStr, positionMin);
        m_SonoParameters->setPV(BFPNames::BiopsyXPosMMOffsetStr, 0);
        m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr)->setEnabled(true);
        m_SonoParameters->setPV(BFPNames::BiopsyYPosMMStr, positionY);
        if (!m_SonoParameters->pBV(BFPNames::IsBiopsyVisibleStr))
        {
            m_SonoParameters->setPV(BFPNames::IsBiopsyVisibleStr, true);
        }
        StateFilterLocker locker(m_StateManager, StateEventNames::NothingStateFilter());
        MessageBoxFrame::information(tr("Please verify guideline before biopsy!"));
    }
}

void BiopsyChooseTool::onInitialize()
{
    MenuTool::onInitialize();
    if (m_SonoParameters != nullptr)
    {
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeIdChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onBiopsyVerifyChanged(QVariant)));
        connect(m_SonoParameters, SIGNAL(presetChanged(PresetParameters)), this,
                SLOT(onPresetChanged(PresetParameters)));
        onProbeIdChanged(m_SonoParameters->pV(BFPNames::ProbeIdStr));
    }
}

void BiopsyChooseTool::onProbeIdChanged(const QVariant& value)
{
    QStringList supportProbes =
        ModelConfig::instance().value(ModelConfig::SupportBiopsyProbes, QStringList()).toStringList();
    bool enable = supportProbes.contains(m_SonoParameters->pSV(BFPNames::ProbeIdStr)) ? true : false;
    setIsAvailable(enable);
    if (!enable)
    {
        return;
    }
    QSettings setting(Resource::biopsySettingFileName(), QSettings::IniFormat);
    QStringList list;
    QStringList groups = setting.childGroups();
    m_CurGroup = groups.contains(value.toString()) ? value.toString() : QString("Default");
    setting.beginGroup(m_CurGroup);
    int size = setting.beginReadArray("Para");
    for (int i = 0; i < size; i++)
    {
        setting.setArrayIndex(i);
        list.append(setting.value("Option").toString());
    }
    setting.endArray();
    setting.endGroup();
    setPopupItems(list);

    if (!isPopupTool())
    {
        setToolValue(list.first(), true);
    }
}

void BiopsyChooseTool::onBiopsyVerifyChanged(const QVariant& value)
{
    m_SonoParameters->parameter(BFPNames::BiopsyAngleChooseStr)->setEnabled(!value.toBool());
}

void BiopsyChooseTool::onPresetChanged(const PresetParameters& preset)
{
    Q_UNUSED(preset)
    if (m_SonoParameters->pBV(BFPNames::BiopsyVerifyStr))
    {
        m_SonoParameters->setPV(BFPNames::BiopsyVerifyStr, false);
    }
    emit changeComboboxIndex(0);
}

BiopsyVerifyTool::BiopsyVerifyTool(const QString& name)
    : MenuTool(name)
    , m_StateManager(NULL)
{
}

void BiopsyVerifyTool::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void BiopsyVerifyTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->parameter(BFPNames::BiopsyAngleChooseStr)
            ->setEnabled(m_SonoParameters->pBV(BFPNames::BiopsyVerifyStr));
        if (!m_SonoParameters->pBV(BFPNames::BiopsyVerifyStr))
        {
            m_SonoParameters->setPV(BFPNames::BiopsyVerifyStr, true);
            StateFilterLocker locker(m_StateManager, StateEventNames::NothingStateFilter());
            MessageBoxFrame::information(
                tr("Help:\n[Touch Pad]: Up and down for start point, left and right for angle."));
        }
        else
        {
            ICommand* changeTool = ToolsFactory::instance().command(ToolNames::BiopsyExitStr);
            changeTool->run();
        }
    }
}

void BiopsyVerifyTool::onInitialize()
{
    MenuTool::onInitialize();

    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr)->setEnabled(false);
        connect(m_SonoParameters->parameter(BFPNames::IsBiopsyVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onBiopsyVisibleChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr),
                SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
                SLOT(onBeforeFreezeValueChanged(const QVariant&, QVariant&)));
    }
}

void BiopsyVerifyTool::onBiopsyVisibleChanged(QVariant value)
{
    m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr)->setEnabled(value.toBool());
}

void BiopsyVerifyTool::onBeforeFreezeValueChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (m_SonoParameters->pBV(BFPNames::BiopsyVerifyStr))
    {
        m_SonoParameters->setPV(BFPNames::BiopsyVerifyStr, false);
    }
}

void BiopsyVerifyTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();

    //  [Apple][BUG:74242]【V1.6Release】【必现】穿刺引导开启校准，回调一个图片再切预设，会导致穿刺引导菜单中穿刺架为关闭时，校准按钮也高亮可点击状态
    //    通过获取切换预设的事件，保证该按钮在每次切完预设一定不可用
    bool isPresetChanged = m_StateManager->postEventName() == StateEventNames::ProbeOk;
    if (isPresetChanged && nullptr != m_SonoParameters)
    {
        m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr)->setEnabled(false);
    }
}

BiopsyAngleTool::BiopsyAngleTool(const QString& name)
    : MenuTool(name)
{
}

void BiopsyAngleTool::onBiopsyVerifyChanged(const QVariant& value)
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->parameter(BFPNames::BiopsyAngleStr)->setEnabled(value.toBool());
    }
    setIsAvailable(value.toBool());
}

void BiopsyAngleTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::BiopsyNeedSaveStr, true);
        MenuTool::execute();
    }
}

void BiopsyAngleTool::onInitialize()
{
    MenuTool::onInitialize();
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->parameter(BFPNames::BiopsyAngleStr)->setEnabled(false);
        connect(m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onBiopsyVerifyChanged(QVariant)));
        //        connect(m_SonoParameters->parameter(BFPNames::BiopsyAngleOffsetStr), SIGNAL(valueChanged(QVariant)),
        //                this, SLOT(onBiopsyAngleOffsetChanged(QVariant)));
    }
}

BiopsyXPosMMTool::BiopsyXPosMMTool(const QString& name)
    : MenuTool(name)
{
}

void BiopsyXPosMMTool::onBiopsyVerifyChanged(const QVariant& value)
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->parameter(BFPNames::BiopsyXPosMMStr)->setEnabled(value.toBool());
    }
    setIsAvailable(value.toBool());
}

void BiopsyXPosMMTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::BiopsyNeedSaveStr, true);
        MenuTool::execute();
    }
}

void BiopsyXPosMMTool::onInitialize()
{
    MenuTool::onInitialize();
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->parameter(BFPNames::BiopsyXPosMMStr)->setEnabled(false);
        connect(m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onBiopsyVerifyChanged(QVariant)));
        //        connect(m_SonoParameters->parameter(BFPNames::BiopsyXPosMMOffsetStr), SIGNAL(valueChanged(QVariant)),
        //                this, SLOT(onBiopsyXPosMMOffsetChanged(QVariant)));
    }
}

BiopsySaveTool::BiopsySaveTool(const QString& name)
    : MenuTool(name)
    , m_StateManager(NULL)
{
}

void BiopsySaveTool::onBiopsyVerifyChanged(const QVariant& value)
{
    setIsAvailable(value.toBool());
}

void BiopsySaveTool::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void BiopsySaveTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        StateFilterLocker locker(m_StateManager, StateEventNames::NothingStateFilter());
        MessageBoxFrame msgBoxFrame;
        QMessageBox* msgBox = msgBoxFrame.messageBox();
        msgBoxFrame.setFrameTitle(tr("System"));
        msgBox->setText(tr("Do you want to save your changes?"));
        msgBox->setStandardButtons(QMessageBox::Ok | QMessageBox::Cancel);
        msgBoxFrame.show();
        QMessageBox::StandardButton ret = (QMessageBox::StandardButton)msgBox->exec();
        if (ret == QMessageBox::Ok)
        {
            QSettings setting(Resource::biopsySettingFileName(), QSettings::IniFormat);
            QStringList groups = setting.childGroups();
            QString group = groups.contains(m_SonoParameters->pSV(BFPNames::ProbeIdStr))
                                ? m_SonoParameters->pSV(BFPNames::ProbeIdStr)
                                : QString("Default");
            setting.beginGroup(group);
            int angleNew =
                m_SonoParameters->pIV(BFPNames::BiopsyAngleStr) + m_SonoParameters->pIV(BFPNames::BiopsyAngleOffsetStr);
            qreal positionNew = m_SonoParameters->pDV(BFPNames::BiopsyXPosMMStr) +
                                m_SonoParameters->pDV(BFPNames::BiopsyXPosMMOffsetStr);
            int size = setting.beginReadArray("Para");
            for (int i = 0; i < size; i++)
            {
                setting.setArrayIndex(i);
                if (m_SonoParameters->pIV(BFPNames::BiopsyAngleIndexStr) == i)
                {
                    setting.setValue("AngleSetting", angleNew);
                    setting.setValue("PositionSetting", positionNew);
                    break;
                }
            }
            setting.endArray();
            setting.endGroup();
            m_SonoParameters->setPV(BFPNames::BiopsyNeedSaveStr, false);
            //            m_SonoParameters->setPV(BFPNames::BiopsyAngleStr, angleNew);
            //            m_SonoParameters->setPV(BFPNames::BiopsyXPosMMStr, positionNew);
            //            m_SonoParameters->setPV(BFPNames::BiopsyAngleOffsetStr, 0);
            //            m_SonoParameters->setPV(BFPNames::BiopsyXPosMMOffsetStr, 0);
        }
    }
}

void BiopsySaveTool::onInitialize()
{
    MenuTool::onInitialize();
    setIsAvailable(false);
    if (m_SonoParameters != nullptr)
    {
        connect(m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onBiopsyVerifyChanged(QVariant)));
    }
}

BiopsyRestoreTool::BiopsyRestoreTool(const QString& name)
    : MenuTool(name)
    , m_StateManager(NULL)
{
}

void BiopsyRestoreTool::onBiopsyVerifyChanged(const QVariant& value)
{
    setIsAvailable(value.toBool());
}

void BiopsyRestoreTool::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void BiopsyRestoreTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        StateFilterLocker locker(m_StateManager, StateEventNames::NothingStateFilter());
        MessageBoxFrame msgBoxFrame;
        QMessageBox* msgBox = msgBoxFrame.messageBox();
        msgBoxFrame.setFrameTitle(tr("System"));
        msgBox->setText(tr("Do you want to restore to default?"));
        msgBox->setStandardButtons(QMessageBox::Ok | QMessageBox::Cancel);
        msgBoxFrame.show();
        QMessageBox::StandardButton ret = (QMessageBox::StandardButton)msgBox->exec();
        if (ret == QMessageBox::Ok)
        {
            QSettings settings(Resource::biopsySettingFileName(), QSettings::IniFormat);
            settings.beginGroup(m_SonoParameters->pV(BFPNames::ProbeIdStr).toString());
            int size = settings.beginReadArray("Para");
            int angleDef = 0;
            qreal positionDef = 0.0;
            for (int i = 0; i < size; i++)
            {
                settings.setArrayIndex(i);
                if (m_SonoParameters->pIV(BFPNames::BiopsyAngleIndexStr) == i)
                {
                    angleDef = settings.value("AngleDef").toInt();
                    positionDef = settings.value("PositionDef").toReal();
                    settings.setValue("AngleSetting", angleDef);
                    settings.setValue("PositionSetting", positionDef);
                    break;
                }
            }
            settings.endArray();
            settings.endGroup();
            m_SonoParameters->setPV(BFPNames::BiopsyAngleStr, angleDef);
            m_SonoParameters->setPV(BFPNames::BiopsyXPosMMStr, positionDef);
            m_SonoParameters->setPV(BFPNames::BiopsyAngleOffsetStr, 0);
            m_SonoParameters->setPV(BFPNames::BiopsyXPosMMOffsetStr, 0);
            m_SonoParameters->setPV(BFPNames::BiopsyNeedSaveStr, false);
        }
    }
}

void BiopsyRestoreTool::onInitialize()
{
    MenuTool::onInitialize();
    setIsAvailable(false);
    if (m_SonoParameters != nullptr)
    {
        connect(m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onBiopsyVerifyChanged(QVariant)));
    }
}

BiopsyExitTool::BiopsyExitTool(const QString& name)
    : MenuTool(name)
{
}

void BiopsyExitTool::onBiopsyVerifyChanged(const QVariant& value)
{
    setIsAvailable(value.toBool());
}

void BiopsyExitTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        if (m_SonoParameters->pBV(BFPNames::BiopsyNeedSaveStr))
        {
            ICommand* changeTool = ToolsFactory::instance().command(ToolNames::BiopsySaveStr);
            changeTool->run();
            if (m_SonoParameters->pBV(BFPNames::BiopsyNeedSaveStr))
            {
                QSettings setting(Resource::biopsySettingFileName(), QSettings::IniFormat);
                QStringList groups = setting.childGroups();
                QString group = groups.contains(m_SonoParameters->pSV(BFPNames::ProbeIdStr))
                                    ? m_SonoParameters->pSV(BFPNames::ProbeIdStr)
                                    : QString("Default");
                setting.beginGroup(group);
                int angleOld = 0;
                qreal positionOld = 0.0;
                int size = setting.beginReadArray("Para");
                for (int i = 0; i < size; i++)
                {
                    setting.setArrayIndex(i);
                    if (m_SonoParameters->pIV(BFPNames::BiopsyAngleIndexStr) == i)
                    {
                        angleOld = setting.value("AngleSetting").toInt();
                        positionOld = setting.value("PositionSetting").toReal();
                        break;
                    }
                }
                setting.endArray();
                setting.endGroup();
                m_SonoParameters->setPV(BFPNames::BiopsyAngleStr, angleOld);
                m_SonoParameters->setPV(BFPNames::BiopsyXPosMMStr, positionOld);
            }
        }
        m_SonoParameters->setPV(BFPNames::BiopsyNeedSaveStr, false);
        m_SonoParameters->setPV(BFPNames::BiopsyVerifyStr, false);
    }
}

void BiopsyExitTool::onInitialize()
{
    MenuTool::onInitialize();
    setIsAvailable(false);
    if (m_SonoParameters != nullptr)
    {
        connect(m_SonoParameters->parameter(BFPNames::BiopsyVerifyStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onBiopsyVerifyChanged(QVariant)));
    }
}

BiopsyCancelTool::BiopsyCancelTool(const QString& name)
    : MenuTool(name)
{
}

void BiopsyCancelTool::execute()
{
    StringCommandArgs args;
    args.setArg(MenuNames::BMenuStr);
    ICommand* changeTool = ToolsFactory::instance().command(ToolNames::ChangeStr);
    changeTool->setArgs(&args);
    changeTool->run();
}

SonoNerveQuitTool::SonoNerveQuitTool(const QString& name)
    : MenuTool(name)
{
}

void SonoNerveQuitTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        if (m_SonoParameters->pBV(BFPNames::SonoNerveStr))
        {
            m_SonoParameters->setPV(BFPNames::SonoNerveStr, false);

            SonoNerveGlyphsControl* control =
                GlyphsControlManager::instance().getChildGlyphsControl<SonoNerveGlyphsControl>(
                    GlyphsCtl::SonoNerveGlyphsType);
            if (control->isActive())
            {
                control->hideActive();
                control->neuralsgRelease();
                control->setIsActive(false);
            }
        }

        StateManager::getInstance().postEvent(StateEventNames::B);
    }
}

SonoNerveBPTool::SonoNerveBPTool(const QString& name, int part)
    : MenuTool(name)
    , m_Part(part)
{
    connect(&MenuController::instance(), &MenuController::menuChange, this, &SonoNerveBPTool::onMenuChanged);
}

void SonoNerveBPTool::onMenuChanged()
{
    SonoNerveGlyphsControl* control =
        GlyphsControlManager::instance().getChildGlyphsControl<SonoNerveGlyphsControl>(GlyphsCtl::SonoNerveGlyphsType);
    if (nullptr != control)
    {
        setIsChecked(control->curPart() == m_Part);
        MenuTool::setIsActive(control->curPart() == m_Part);
    }
}

SonoNerveScalenusBPTool::SonoNerveScalenusBPTool(const QString& name)
    : SonoNerveBPTool(name, 0)
{
}

void SonoNerveScalenusBPTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::SonoNervePartStr, 0);
    }
}

SonoNerveSupraclavicularBPTool::SonoNerveSupraclavicularBPTool(const QString& name)
    : SonoNerveBPTool(name, 1)
{
}

void SonoNerveSupraclavicularBPTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::SonoNervePartStr, 1);
    }
}

SonoNerveQuitFromBModeTool::SonoNerveQuitFromBModeTool(const QString& name)
    : MenuTool(name)
{
}

void SonoNerveQuitFromBModeTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        if (m_SonoParameters->pBV(BFPNames::SonoNerveStr))
        {
            m_SonoParameters->setPV(BFPNames::SonoNerveStr, false);

            SonoNerveGlyphsControl* control =
                GlyphsControlManager::instance().getChildGlyphsControl<SonoNerveGlyphsControl>(
                    GlyphsCtl::SonoNerveGlyphsType);
            if (control->isActive())
            {
                control->hideActive();
                control->neuralsgRelease();
                control->setIsActive(false);
            }
        }
    }
}

void SonoNerveQuitFromBModeTool::onInitialize()
{
    MenuTool::onInitialize();
}

SonoMSKQuitTool::SonoMSKQuitTool(const QString& name)
    : MenuTool(name)
{
}

void SonoMSKQuitTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::SonoMSKStr, false);
        SonoMSKTool* tool = dynamic_cast<SonoMSKTool*>(ToolsFactory::instance().command(BFPNames::SonoMSKStr));
        if (tool != nullptr)
        {
            tool->Exit();
        }
    }
}

static bool CanClicked = false;
SonoMSKBicepsLHTendonTool::SonoMSKBicepsLHTendonTool(const QString& name,
                                                     CursorMouseActionsModel* cursorMouseActionsModel,
                                                     QWidget* leftMenuWidget, PopUpWidget* popUpWidget,
                                                     IStateManager* value)
    : MenuTool(name)
    , m_CursorMouseActionsModel(cursorMouseActionsModel)
    , m_leftMenuWidget(dynamic_cast<LeftMenuWidget*>(leftMenuWidget))
    , m_PopUpWidget(popUpWidget)
    , m_StateManager(value)
{
    loadSetting();
    m_ShortLanguage = Setting::instance().defaults().shortLanguage();
    connect(m_CursorMouseActionsModel, &CursorMouseActionsModel::touchPressed, this,
            &SonoMSKBicepsLHTendonTool::hidePopUpWidget);
    connect(m_leftMenuWidget, &LeftMenuWidget::focusOut, this, &SonoMSKBicepsLHTendonTool::hidePopUpWidget);
    connect(&Setting::instance().defaults(), SIGNAL(languageChanged()), this, SLOT(languageChanged()));
    connect(&MenuController::instance(), &MenuController::menuChange, this, &SonoMSKBicepsLHTendonTool::menuChange,
            Qt::UniqueConnection);
    connect(m_StateManager, &IStateManager::beforePostEvent, this, &SonoMSKBicepsLHTendonTool::hidePopUpWidget);

    CanClicked = true;
}

void SonoMSKBicepsLHTendonTool::loadSetting()
{
    QSettings setting(Resource::getMSKSettingDir(), QSettings::IniFormat);
    m_ImageWidth = setting.value("Width_Image").toInt();
    m_ImageHeight = setting.value("Height_Image").toInt();
    m_TextWidth = setting.value("Width_Text").toInt();
    m_TextHeight = setting.value("Height_Text").toInt();
    m_FixedHeight = setting.value("Fixed_Height").toInt();
    m_FixedWidth =
        MenuController::instance().leftMenu()->geometry().width() - setting.value("Left_Right_Margin").toInt() * 2;
    m_Position = setting.value("Position").toPoint();
    m_MSKTitle = setting.value("Title").toString();
    m_BicepsLHTendonSA = setting.value("BicepsLHTendonSA").toString();
    m_BicepsLHTendonLA = setting.value("BicepsLHTendonLA").toString();
    m_SSPTendonLA = setting.value("SSPTendonLA").toString();
    m_ISPTendonLA = setting.value("ISPTendonLA").toString();
    m_SSCTendonLA = setting.value("SSCTendonLA").toString();
    m_SSCTendonSA = setting.value("SSCTendonSA").toString();
    m_TMTendonLA = setting.value("TMTendonLA").toString();
}

void SonoMSKBicepsLHTendonTool::addElementToLeftMenu(int part)
{
    bool isCurPart = true;
    QString filePath = getFilePath(isCurPart, part);

    if (!isCurPart)
    {
        return;
    }

    QString file = QFile::exists(filePath + Setting::instance().defaults().shortLanguage() + Resource::sonoHelpTextTips)
                       ? Setting::instance().defaults().shortLanguage() + Resource::sonoHelpTextTips
                       : Resource::sonoHelpDefTextTips;
    QStringList contentList = QStringList()
                              << Resource::scanning << Resource::ultrasonic << Resource::anatomicPic << file;

    foreach (QString name, contentList)
    {
        QString path = filePath + name;
        QFileInfo info(path);
        SonoHelpBaseItemWidget::CType type = SonoHelpBaseItemWidget::analyzeFileType(info);
        if (type == SonoHelpBaseItemWidget::Image)
        {
            SonoHelpImageItemWidget* w = new SonoHelpImageItemWidget(info, m_ImageHeight, nullptr);
            w->setFixedSize(m_FixedWidth, m_FixedHeight);
            connect(w, &SonoHelpBaseItemWidget::mousePress, this, &SonoMSKBicepsLHTendonTool::onZoomIn);
            m_ItemWidgetList.append(w);
            // Remove existing SonoHelpImageItemWidget before adding new one
            QList<QWidget*> widgets = MenuController::instance().leftMenu()->findChildren<QWidget*>();
            foreach (QWidget* widget, widgets)
            {
                // Skip if widget is null to avoid crash
                if (widget == nullptr)
                {
                    continue;
                }
                // Ensure widget is still valid and in layout before casting
                if (!widget->parent() || !widget->isWidgetType())
                {
                    continue;
                }
                // Check if widget is still valid and in layout before removing
                if (!widget->isVisible() || !widget->parentWidget())
                {
                    continue;
                }
                SonoHelpImageItemWidget* imageWidget = qobject_cast<SonoHelpImageItemWidget*>(widget);
                if (imageWidget != nullptr && imageWidget != w)
                {
                    disconnect(imageWidget, &SonoHelpBaseItemWidget::mousePress, this,
                               &SonoMSKBicepsLHTendonTool::onZoomIn);
                    MenuController::instance().leftMenu()->removeElement(imageWidget);
                }
            }
            MenuController::instance().leftMenu()->addElement(w);
            MenuController::instance().leftMenu()->setShowMaxHeight(true);
        }
        else if (type == SonoHelpBaseItemWidget::Text)
        {
            SonoHelpTextItemWidget* w = new SonoHelpTextItemWidget(info, m_TextHeight, nullptr);
            connect(w, &SonoHelpBaseItemWidget::mousePress, this, &SonoMSKBicepsLHTendonTool::onZoomIn);
            m_ItemWidgetList.append(w);
            w->setFixedSize(m_FixedWidth, m_FixedHeight);
            // Remove existing SonoHelpImageItemWidget before adding new one
            QList<QWidget*> widgets = MenuController::instance().leftMenu()->findChildren<QWidget*>();
            foreach (QWidget* widget, widgets)
            {
                // Skip if widget is null to avoid crash
                if (widget == nullptr)
                {
                    continue;
                }
                // Ensure widget is still valid and in layout before casting
                if (!widget->parent() || !widget->isWidgetType())
                {
                    continue;
                }
                // Check if widget is still valid and in layout before removing
                if (!widget->isVisible() || !widget->parentWidget())
                {
                    continue;
                }
                SonoHelpTextItemWidget* textWidget = qobject_cast<SonoHelpTextItemWidget*>(widget);
                if (textWidget != nullptr && textWidget != w)
                {
                    disconnect(textWidget, &SonoHelpBaseItemWidget::mousePress, this,
                               &SonoMSKBicepsLHTendonTool::onZoomIn);
                    MenuController::instance().leftMenu()->removeElement(textWidget);
                }
            }
            MenuController::instance().leftMenu()->addElement(w);
            MenuController::instance().leftMenu()->setShowMaxHeight(true);
        }
    }

    MenuController::instance().leftMenu()->update();
}

QString SonoMSKBicepsLHTendonTool::getFilePath(bool& isCurPart, int curPart)
{
    QString filePath;
    QString partName;
    switch (curPart)
    {
    case BBSANUM:
        if (toolName() != ToolNames::BicepsLHTendonSAStr)
        {
            isCurPart = false;
        }

        partName = m_BicepsLHTendonSA;
        break;
    case BBLANUM:
        if (toolName() != ToolNames::BicepsLHTendonLAStr)
        {
            isCurPart = false;
        }

        partName = m_BicepsLHTendonLA;
        break;
    case SSPLANUM:
        if (toolName() != ToolNames::SSPTendonLAStr)
        {
            isCurPart = false;
        }

        partName = m_SSPTendonLA;
        break;
    case ISPLANUM:
        if (toolName() != ToolNames::ISPTendonLAStr)
        {
            isCurPart = false;
        }

        partName = m_ISPTendonLA;
        break;
    case SSCLANUM:
        if (toolName() != ToolNames::SSCTendonLAStr)
        {
            isCurPart = false;
        }

        partName = m_SSCTendonLA;
        break;
    case SSCSANUM:
        if (toolName() != ToolNames::SSCTendonSAStr)
        {
            isCurPart = false;
        }

        partName = m_SSCTendonSA;
        break;
    case TMLANUM:
        if (toolName() != ToolNames::TMTendonLAStr)
        {
            isCurPart = false;
        }

        partName = m_TMTendonLA;
        break;
    }

    filePath = Resource::getSonoHelperDir() + QDir::separator() + m_MSKTitle + QDir::separator() + partName +
               QDir::separator();

    return filePath;
}

void SonoMSKBicepsLHTendonTool::execute()
{
    if (!CanClicked)
        return;

    if (m_SonoParameters != nullptr)
    {
        int CurPart = BBSANUM;
        if (toolName() == ToolNames::BicepsLHTendonLAStr)
        {
            CurPart = BBLANUM;
        }
        else if (toolName() == ToolNames::SSPTendonLAStr)
        {
            CurPart = SSPLANUM;
        }
        else if (toolName() == ToolNames::ISPTendonLAStr)
        {
            CurPart = ISPLANUM;
        }
        else if (toolName() == ToolNames::SSCTendonLAStr)
        {
            CurPart = SSCLANUM;
        }
        else if (toolName() == ToolNames::SSCTendonSAStr)
        {
            CurPart = SSCSANUM;
        }
        else if (toolName() == ToolNames::TMTendonLAStr)
        {
            CurPart = TMLANUM;
        }
        if (m_SonoParameters->pIV(BFPNames::SonoMSKPartStr) != CurPart)
        {
            QDeadlineTimer timer;
            m_SonoParameters->setPV(BFPNames::SonoMSKPartStr, CurPart);
        }
        else
        {
            return;
        }
    }

    CanClicked = false;
    menuChange(MenuNames::SonoMSKMenuStr);
    CanClicked = true;
}

void SonoMSKBicepsLHTendonTool::onZoomIn(const QFileInfo& info, const SonoHelpBaseItemWidget::CType type)
{
    if (m_PopUpWidget != nullptr)
    {
        if (m_PopUpWidget->isHidden())
        {
            m_PopUpWidget->show();
        }
        else
        {
            m_PopUpWidget->hide();
            return;
        }
        prepareForZoomIn(info, type);
        m_PopUpWidget->setCurrentPage(type);
        m_PopUpWidget->move(m_Position);
    }
}

void SonoMSKBicepsLHTendonTool::hidePopUpWidget()
{
    if (m_PopUpWidget != nullptr)
    {
        m_PopUpWidget->hide();
    }
}

void SonoMSKBicepsLHTendonTool::languageChanged()
{
    if (m_SonoParameters != nullptr)
    {
        if (m_SonoParameters->pBV(BFPNames::SonoMSKStr))
        {
            addElementToLeftMenu(m_SonoParameters->pIV(BFPNames::SonoMSKPartStr));
            MenuController::instance().refreshLeftAndBottomMenu();
        }
    }
}

void SonoMSKBicepsLHTendonTool::menuChange(QString menu)
{
    if (m_SonoParameters != nullptr)
    {
        if (m_SonoParameters->pBV(BFPNames::SonoMSKStr))
        {
            if (menu.compare(MenuNames::SonoMSKMenuStr) == 0)
            {
                addElementToLeftMenu(m_SonoParameters->pIV(BFPNames::SonoMSKPartStr));
                setActivePart();
            }
        }
    }

    hidePopUpWidget();
}

void SonoMSKBicepsLHTendonTool::prepareForZoomIn(const QFileInfo& info, const SonoHelpBaseItemWidget::CType type)
{
    switch (type)
    {
    case SonoHelpBaseItemWidget::Image:
        createImageContent(info);
        break;
    case SonoHelpBaseItemWidget::Text:
        createTextContent(info);
        break;
    default:
        break;
    }
}

void SonoMSKBicepsLHTendonTool::createImageContent(const QFileInfo& info)
{
    if (m_PopUpWidget != nullptr)
    {
        QPixmap pixmap;
        pixmap.load(info.filePath());
        m_PopUpWidget->setFixedSize(m_ImageWidth, m_ImageHeight);
        m_PopUpWidget->label()->setScaledContents(true);
        m_PopUpWidget->label()->setPixmap(pixmap);
    }
}

void SonoMSKBicepsLHTendonTool::createTextContent(const QFileInfo& info)
{
    m_PopUpWidget->setFixedSize(m_TextWidth, m_TextHeight);
    QFile file(info.filePath());
    if (file.open(QIODevice::ReadOnly))
    {
        QByteArray array = file.readAll();
        m_PopUpWidget->setText(array);
        file.close();
    }
    else
    {
        qCritical() << PRETTY_FUNCTION << "open file failed!" << file.errorString();
    }
}

void SonoMSKBicepsLHTendonTool::setActivePart()
{
    if (m_SonoParameters != nullptr)
    {
        if (m_SonoParameters->pIV(BFPNames::SonoMSKPartStr) == BBSANUM)
        {
            ActiveInfo(ToolNames::BicepsLHTendonSAStr, true);
        }
        else if (m_SonoParameters->pIV(BFPNames::SonoMSKPartStr) == BBLANUM)
        {
            ActiveInfo(ToolNames::BicepsLHTendonLAStr, true);
        }
        else if (m_SonoParameters->pIV(BFPNames::SonoMSKPartStr) == SSPLANUM)
        {
            ActiveInfo(ToolNames::SSPTendonLAStr, true);
        }
        else if (m_SonoParameters->pIV(BFPNames::SonoMSKPartStr) == ISPLANUM)
        {
            ActiveInfo(ToolNames::ISPTendonLAStr, true);
        }
        else if (m_SonoParameters->pIV(BFPNames::SonoMSKPartStr) == SSCLANUM)
        {
            ActiveInfo(ToolNames::SSCTendonLAStr, true);
        }
        else if (m_SonoParameters->pIV(BFPNames::SonoMSKPartStr) == SSCSANUM)
        {
            ActiveInfo(ToolNames::SSCTendonSAStr, true);
        }
        else if (m_SonoParameters->pIV(BFPNames::SonoMSKPartStr) == TMLANUM)
        {
            ActiveInfo(ToolNames::TMTendonLAStr, true);
        }
    }
}

void SonoMSKBicepsLHTendonTool::ActiveInfo(QString toolName, bool isActive)
{
    ToolsFactory::instance().tool(ToolNames::BicepsLHTendonSAStr)->setIsActive(false);
    ToolsFactory::instance().tool(ToolNames::BicepsLHTendonLAStr)->setIsActive(false);
    ToolsFactory::instance().tool(ToolNames::SSPTendonLAStr)->setIsActive(false);
    ToolsFactory::instance().tool(ToolNames::ISPTendonLAStr)->setIsActive(false);
    ToolsFactory::instance().tool(ToolNames::SSCTendonLAStr)->setIsActive(false);
    ToolsFactory::instance().tool(ToolNames::SSCTendonSAStr)->setIsActive(false);
    ToolsFactory::instance().tool(ToolNames::TMTendonLAStr)->setIsActive(false);

    ToolsFactory::instance().tool(toolName)->setIsActive(isActive);
}

void SonoMSKBicepsLHTendonTool::clear()
{
    if (m_ItemWidgetList.empty())
    {
        return;
    }
    foreach (SonoHelpBaseItemWidget* w, m_ItemWidgetList)
    {
        if (w != nullptr)
        {
            w->disconnect();
            delete w;
            w = nullptr;
        }
    }
    m_ItemWidgetList.clear();
}

SonoMSKQuitFromBModeTool::SonoMSKQuitFromBModeTool(const QString& name)
    : MenuTool(name)
{
}

void SonoMSKQuitFromBModeTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        if (m_SonoParameters->pBV(BFPNames::SonoMSKStr))
        {
            m_SonoParameters->setPV(BFPNames::SonoMSKStr, false);

            SonoMSKGlyphsControl* control =
                GlyphsControlManager::instance().getChildGlyphsControl<SonoMSKGlyphsControl>(
                    GlyphsCtl::SonoMSKGlyphsType);
            if (control->isActive())
            {
                control->createGlyphs();
                control->hideActive();
                control->neuralsgRelease();
                control->setIsActive(false);
            }
        }
    }
}

void SonoMSKQuitFromBModeTool::onInitialize()
{
    MenuTool::onInitialize();
}

RotationTool::RotationTool(const QString& name)
    : MenuTool(name)
{
    setIsFreezeSendTool(true);
}

void RotationTool::onSetSonoParameters()
{
    if (m_SonoParameters == NULL)
        return;
}

void RotationTool::execute()
{
    MenuTool::execute();
    ToolsFactory::instance().command(ToolNames::MeasurementDeleteStr)->run();
}

EndCurrentStateTool::EndCurrentStateTool(const QString& name)
    : MenuTool(name)
    , m_StateManager(NULL)
{
    m_NeedEndStates << StateEventNames::CommentState << StateEventNames::BodyMarkState
                    << StateEventNames::MeasurementState << StateEventNames::QuickDistanceState
                    << StateEventNames::QuickEllipseState << StateEventNames::QuickTraceState
                    << StateEventNames::ArrowState;
}

void EndCurrentStateTool::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void EndCurrentStateTool::endCurState()
{
    StateManager::getInstance().setNeedEnd(true);

    QString currentState = StateManager::getInstance().currentState()->name();

    /*
     * 当切换用户时，会进入此函数
     * 需求：
     * (1)前用户(A)检查过程中，切换用户(B)，结束前用户(A)检查，根据系统设置的End Exam Option，跳转界面。
     * (2)前用户回看病例过程中，切换用户，关闭回看的病例。
     *
     * 如果在测量（快速测量）、注释、体标、箭头状态下，用postEvent(curState)来结束当前状态，在curState的OnExit中执行end,来结束检查；
     * 如果未在测量（快速测量）、注释、体标、箭头状态下，则直接执行end,来结束检查.
     */
    if ((!m_NeedEndStates.isEmpty()) && (!currentState.isEmpty()) && m_NeedEndStates.contains(currentState))
    {
        StateFilterLocker locker(m_StateManager, QStringList());
        QString state = QString().append("FuncKey.").append(currentState);
        StateManager::getInstance().postEvent(state); //退出当前状态
    }
    else
    {
        runEndTool();
    }
}

void EndCurrentStateTool::runEndTool()
{
    if (StateManager::getInstance().needEnd())
    {
        ICommand* endTool = ToolsFactory::instance().command(ToolNames::EndStr);
        if (endTool != nullptr)
        {
            endTool->setArgs(NULL);
            endTool->run();
            StateManager::getInstance().setNeedEnd(false);
        }
    }
}

void EndCurrentStateTool::execute()
{
    endCurState();
}

SuperNeedleTool::SuperNeedleTool(const QString& name)
    : MenuTool(name)
    , m_ProbeDataSet(NULL)
{
    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this,
            &SuperNeedleTool::updateToolState);
}

void SuperNeedleTool::setProbeDataSet(IProbeDataSet* probeDataSet)
{
    m_ProbeDataSet = probeDataSet;
}

void SuperNeedleTool::onSetSonoParameters()
{
    MenuTool::onSetSonoParameters();
    updateToolState();
}

void SuperNeedleTool::execute()
{
    MenuTool::execute();
}

void SuperNeedleTool::updateToolState()
{
    if (nullptr != m_SonoParameters)
    {
        bool isSupportSuperNeedle = false;
        isSupportSuperNeedle = (AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeyNeedle) &&
                                Resource::supportProbeId(Resource::FunctionName::SuperNeedle)
                                    .contains(QString::number(m_SonoParameters->pIV(BFPNames::ProbeIdStr))));

        setIsVisible(isSupportSuperNeedle);
        setIsAvailable(isSupportSuperNeedle);
    }
}

void SuperNeedleTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onProbeChanged(QVariant)));
    }
}

void SuperNeedleTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeChanged(QVariant)));
    }
}

void SuperNeedleTool::onProbeChanged(const QVariant& value)
{
    updateToolState();
}

VirtualVertexTrapezoidalModeTool::VirtualVertexTrapezoidalModeTool(const QString& name)
    : MenuTool(name)
    , m_ProbeDataSet(NULL)
{
    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this,
            &VirtualVertexTrapezoidalModeTool::updateToolState);
}

void VirtualVertexTrapezoidalModeTool::setProbeDataSet(IProbeDataSet* probeDataSet)
{
    m_ProbeDataSet = probeDataSet;
}

void VirtualVertexTrapezoidalModeTool::onSetSonoParameters()
{
    MenuTool::onSetSonoParameters();
    updateToolState();
}

void VirtualVertexTrapezoidalModeTool::execute()
{
    MenuTool::execute();
}

void VirtualVertexTrapezoidalModeTool::updateToolState()
{
    if (nullptr != m_SonoParameters)
    {
        bool isVisible = m_ProbeDataSet->getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr)).IsLinear &&
                         AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeyTrapezoidalMode);
        setIsVisible(isVisible);
    }
}

void VirtualVertexTrapezoidalModeTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onProbeChanged(QVariant)));
    }
}

void VirtualVertexTrapezoidalModeTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    if (nullptr != m_SonoParameters)
    {
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeChanged(QVariant)));
    }
}

void VirtualVertexTrapezoidalModeTool::onProbeChanged(const QVariant& value)
{
    updateToolState();
}

ZoomStateTool::ZoomStateTool(const QString& toolName)
    : MenuTool(toolName)
{
}

void ZoomStateTool::execute()
{
    StateManager::getInstance().postEvent(StateEventNames::Zoom);
}

ZoomAddTool::ZoomAddTool(const QString& toolName)
    : MenuTool(toolName)
{
}

void ZoomAddTool::execute()
{
    if (m_SonoParameters->pIV(BFPNames::SystemScanModeStr) != SystemScanModeB)
        return;

    if (m_SonoParameters->pBV(BFPNames::ZoomSelectStr) || m_SonoParameters->pBV(BFPNames::ZoomOnStr))
    {
        StateManager::getInstance().postEvent(StateEventNames::AngleZoomAdd);
    }
}

ZoomDecTool::ZoomDecTool(const QString& toolName)
    : MenuTool(toolName)
{
}

void ZoomDecTool::execute()
{
    if (m_SonoParameters->pIV(BFPNames::SystemScanModeStr) != SystemScanModeB)
        return;

    if (m_SonoParameters->pBV(BFPNames::ZoomSelectStr) || m_SonoParameters->pBV(BFPNames::ZoomOnStr))
    {
        StateManager::getInstance().postEvent(StateEventNames::AngleZoomDec);
    }
}

AcousticPowerBShowTool::AcousticPowerBShowTool(const QString& name)
    : MenuTool(name)
    , m_PresetModeChanged(false)
{
    connect(AppSetting::eventObject(), &AppSettingEventObject::presetModeChanged, this,
            &AcousticPowerBShowTool::onPresetModeChanged);
}

void AcousticPowerBShowTool::execute()
{
    MenuTool::execute();
    dealValueTred();
}

void AcousticPowerBShowTool::onPresetModeChanged(bool isOpened)
{
    m_PresetModeChanged = isOpened;
    dealValueTred();
}

void AcousticPowerBShowTool::dealValueTred()
{
    if (m_PresetModeChanged)
    {
        m_ValueTred += QString("(%1)").arg(MenuTool::value());
    }
}

AutoEFCurLayoutTool::AutoEFCurLayoutTool(const QString& name, IRulerFactory* rulerFactory)
    : MenuTool(name)
{
    m_Ruler = dynamic_cast<BaseImageRuler*>(rulerFactory->getRuler(Measurement::Ruler_AutoEF));

    if (m_Ruler != nullptr)
    {
        connect(this, SIGNAL(change2RightLayout()), m_Ruler, SLOT(onUpdateRightLayout()));
        connect(this, SIGNAL(change2LeftLayout()), m_Ruler, SLOT(onUpdateLeftLayout()));
        connect(this, SIGNAL(updateUnActiveLayout()), m_Ruler, SLOT(onUpdateUnActiveLayout()));
    }
}

void AutoEFCurLayoutTool::execute()
{
    if (nullptr == m_SonoParameters)
        return;

    if (m_SonoParameters->pBV(BFPNames::HasAutoEFResultStr))
    {
        m_SonoParameters->setPV(BFPNames::BCImagesOnStr, true);
        int layout = 0;
        int curlayout = m_SonoParameters->pIV(BFPNames::AutoEFCurLayoutStr);
        qint64 timeStamp = m_Ruler->getFrameTimeStamp();
        int partition = -1;
        // need first update or activeB will be changed
        if (curlayout == Layout_1x1_1)
        {
            partition = m_SonoParameters->pIV(BFPNames::AutoEFActivePartitionStr);

            emit change2RightLayout();
            emit change2LeftLayout();

            emit updateUnActiveLayout();

            LightController::instance().enterTwoB();
        }
        if (curlayout != Layout_1x1_1)
        {
            layout = curlayout == Layout_1x2_1 ? Layout_1x2_2 : Layout_1x2_1;
        }
        else
        {
            layout =
                timeStamp == m_SonoParameters->pV(BFPNames::EDCurIndexStr).toLongLong() ? Layout_1x2_1 : Layout_1x2_2;
        }
        if (partition == -1)
        {
            partition = layout;
        }
        m_SonoParameters->setPV(BFPNames::ImageRenderPartitionStr, partition);
        m_SonoParameters->setPV(BFPNames::AutoEFCurLayoutStr, partition);
    }
}

AutoEFSingleBTool::AutoEFSingleBTool(const QString& name, IRulerFactory* rulerFactory)
    : MenuTool(name)
{
    m_Ruler = dynamic_cast<BaseImageRuler*>(rulerFactory->getRuler(Measurement::Ruler_AutoEF));

    if (m_Ruler != nullptr)
    {
        connect(this, SIGNAL(change2OneLayout(int)), m_Ruler, SLOT(onChange2OneLayout(int)));
    }
}

void AutoEFSingleBTool::execute()
{
    if (nullptr == m_SonoParameters)
        return;

    if (m_SonoParameters->pBV(BFPNames::HasAutoEFResultStr))
    {
        int layout = m_SonoParameters->pIV(BFPNames::AutoEFCurLayoutStr);
        if (layout != Layout_1x1_1)
        {
            m_SonoParameters->setPV(BFPNames::AutoEFActivePartitionStr, layout);
        }

        if (layout != Layout_1x1_1)
        {
            m_SonoParameters->setPV(BFPNames::BCImagesOnStr, false);
            m_SonoParameters->setPV(BFPNames::AutoEFCurLayoutStr, Layout_1x1_1);
            emit change2OneLayout(layout);
            // need after for clear image for ImagePainterRender
            m_SonoParameters->setPV(BFPNames::ImageRenderPartitionStr, Partition_Default);

            LightController::instance().enterBMode();
        }
    }
}

AutoEFFrameTool::AutoEFFrameTool(const QString& name, CineLooper* c, MeasureContext* context,
                                 ILineBufferManager* lineBufferManager)
    : MenuTool(name)
    , m_Looper(c)
    , m_Context(context)
    , m_LineBufferManager(lineBufferManager)
{
    m_ToolName = name;
    if (m_ToolName == ToolNames::ESFrameStr)
    {
        connect(this, SIGNAL(updateESVIndex(int)), m_Context, SLOT(onUpdateESVIndex(int)));
    }
    else if (m_ToolName == ToolNames::EDFrameStr)
    {
        connect(this, SIGNAL(updateEDVIndex(int)), m_Context, SLOT(onUpdateEDVIndex(int)));
    }
}

void AutoEFFrameTool::execute()
{
    if (m_Looper != nullptr)
    {
        QVariantCommandArgs* args = this->args<QVariantCommandArgs>();
        if ((args != nullptr) && (m_SonoParameters != nullptr))
        {
            int begin = m_LineBufferManager->getFrameIndexByFrameTimestamp(
                m_SonoParameters->pV(BFPNames::AutoEFStartFrameStr).toLongLong());
            int end = m_LineBufferManager->getFrameIndexByFrameTimestamp(
                m_SonoParameters->pV(BFPNames::AutoEFEndFrameStr).toLongLong());

            int esv = m_LineBufferManager->getFrameIndexByFrameTimestamp(
                m_SonoParameters->pV(BFPNames::ESFrameStampStr).toLongLong());
            int edv = m_LineBufferManager->getFrameIndexByFrameTimestamp(
                m_SonoParameters->pV(BFPNames::EDFrameStampStr).toLongLong());

            bool isESMax = (esv > edv) ? true : false;
            int curMax = (QString::compare(m_ToolName, ToolNames::ESFrameStr) == 0) ? (isESMax ? end : edv - 1)
                                                                                    : (isESMax ? esv - 1 : end);
            int curMin = (QString::compare(m_ToolName, ToolNames::ESFrameStr) == 0) ? (isESMax ? edv + 1 : begin)
                                                                                    : (isESMax ? begin : esv + 1);
            int settingValue = (QString::compare(m_ToolName, ToolNames::ESFrameStr) == 0) ? esv : edv;
            int originPartition = m_SonoParameters->pIV(BFPNames::ImageRenderPartitionStr);
            int imageRenderPartition = Partition_Default;
            if (args->getType() == COMMAND_TYPE_BOOL)
            {
                bool isNext = args->arg();
                Util::multiValue(settingValue, curMin, curMax, isNext, isLoopMulti());
            }
            else
            {
                settingValue = args->getValue().toInt();
                if (settingValue > curMax)
                {
                    settingValue = curMax;
                }
                if (settingValue < curMin)
                {
                    settingValue = curMin;
                }
            }

            if (m_ToolName == ToolNames::ESFrameStr)
            {
                qint64 esFrameStamp = m_LineBufferManager->getFramestampByIndex(settingValue);
                m_SonoParameters->setPV(BFPNames::ESFrameStampStr, esFrameStamp);
                m_SonoParameters->setPV(BFPNames::ESCurIndexStr, esFrameStamp);
                setparaValue(BFPNames::EDFrameStr, (isESMax ? settingValue - 1 : end),
                             (isESMax ? begin : settingValue + 1));
                m_Context->onUpdateESVIndex(esFrameStamp);
                imageRenderPartition = Partition_Right;
            }
            else if (m_ToolName == ToolNames::EDFrameStr)
            {
                qint64 edFrameStamp = m_LineBufferManager->getFramestampByIndex(settingValue);
                m_SonoParameters->setPV(BFPNames::EDFrameStampStr, edFrameStamp);
                m_SonoParameters->setPV(BFPNames::EDCurIndexStr, edFrameStamp);
                setparaValue(BFPNames::ESFrameStr, (isESMax ? end : settingValue - 1),
                             (isESMax ? settingValue + 1 : begin));
                m_Context->onUpdateEDVIndex(edFrameStamp);
                imageRenderPartition = Partition_Left;
            }
            else
            {
                qDebug() << PRETTY_FUNCTION << "Invalid AutoEFFrameTool" << m_ToolName;
                return;
            }

            m_SonoParameters->setPV(m_ToolName, settingValue);
            setToolValue(QString("%1").arg(settingValue + 1));
            m_SonoParameters->setPV(BFPNames::ImageRenderPartitionStr, imageRenderPartition);
            m_Looper->loop(settingValue);
            m_SonoParameters->setPV(BFPNames::ImageRenderPartitionStr, originPartition);
        }
    }
}

void AutoEFFrameTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();

    if (m_SonoParameters != nullptr)
    {
        if (m_ToolName == ToolNames::EDFrameStr)
        {
            disconnect(m_SonoParameters->parameter(BFPNames::EDFrameStr), SIGNAL(gettingText(QString&)), this,
                       SLOT(onGettingEDFrameText(QString&)));
        }

        if (m_ToolName == ToolNames::ESFrameStr)
        {
            disconnect(m_SonoParameters->parameter(BFPNames::ESFrameStr), SIGNAL(gettingText(QString&)), this,
                       SLOT(onGettingESFrameText(QString&)));
        }
    }
}

void AutoEFFrameTool::onSetSonoParameters()
{
    if (m_SonoParameters != nullptr)
    {
        if (m_ToolName == ToolNames::EDFrameStr)
        {
            connect(m_SonoParameters->parameter(BFPNames::EDFrameStr), SIGNAL(gettingText(QString&)), this,
                    SLOT(onGettingEDFrameText(QString&)));
        }

        if (m_ToolName == ToolNames::ESFrameStr)
        {
            connect(m_SonoParameters->parameter(BFPNames::ESFrameStr), SIGNAL(gettingText(QString&)), this,
                    SLOT(onGettingESFrameText(QString&)));
        }
    }
}

void AutoEFFrameTool::onGettingEDFrameText(QString& value)
{
    int edv = m_SonoParameters->pIV(BFPNames::EDFrameStr);

    value = QVariant(edv + 1).toString();
}

void AutoEFFrameTool::onGettingESFrameText(QString& value)
{
    int esv = m_SonoParameters->pIV(BFPNames::ESFrameStr);

    value = QVariant(esv + 1).toString();
}

void AutoEFFrameTool::setparaValue(QString para, int max, int min)
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->parameter(para)->setMax(max);
        m_SonoParameters->parameter(para)->setMin(min);
    }
}

void AutoEFFrameTool::setCineLooper(CineLooper* value)
{
    m_Looper = value;
    setIsAvailable(false);
}

void AutoEFFrameTool::updateFrameInfo()
{
    ILineBufferManager* bufferManager = static_cast<ILineBufferManager*>(sender());

    if (m_ToolName == ToolNames::EDFrameStr)
    {
        if (m_SonoParameters != NULL)
        {
            m_SonoParameters->setPV(BFPNames::EDFrameStr, bufferManager->currentIndex());
        }
        setToolValue(QString("%1").arg(bufferManager->currentIndex() + 1));
    }
    else if (m_ToolName == ToolNames::ESFrameStr)
    {
        if (m_SonoParameters != NULL)
        {
            m_SonoParameters->setPV(BFPNames::ESFrameStr, bufferManager->currentIndex());
        }
        setToolValue(QString("%1").arg(bufferManager->currentIndex() + 1));
    }
}

SonoNerveMedianBPTool::SonoNerveMedianBPTool(const QString& name)
    : SonoNerveBPTool(name, 2)
{
}

void SonoNerveMedianBPTool::execute()
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::SonoNervePartStr, 2);
    }
}

PDStateTool::PDStateTool(const QString& name)
    : MenuTool(name)
{
}

void PDStateTool::execute()
{
    if (StateManager::getInstance().isSupportCPADPD())
    {
        StateManager::getInstance().postEvent(StateEventNames::PD);
    }
}
