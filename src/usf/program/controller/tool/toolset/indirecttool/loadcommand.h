#ifndef LOADCOMMAND_H
#define LOADCOMMAND_H
#include "usfprogramcontroller_global.h"

#include "basestorecommand.h"
#include "sonoparameters.h"
class IimageInfo;
class ImageTile;
class ThumbnailDialog;
class ChisonUltrasoundContext;
class BaseBkWidget;

#ifdef USE_FREEHAND3D
class FreeHand3DProxy;
#endif

class USF_PROGRAM_CONTROLLER_EXPORT LoadCommand : public BaseStoreCommand
{
    Q_OBJECT
public:
    LoadCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager, SonoParameters* sonoParameters,
                IMainWindow* flexBtnVisible, BaseWidget* imageClipWidget, IBufferStoreManager* bufferStoreManager,
                ChisonUltrasoundContext* chisonContext, MeasureContext* measureContext, ImageTile* imageTile,
                BaseBkWidget* thumbnailDialog);

protected:
    virtual void beginOperate();
    virtual void operate();
    void handleLoadState(LoadErrorCode loadErrorCode);
signals:
    void startLoad();
protected slots:
    virtual void onCommandFinished(LoadErrorCode loadErrorCode);
private slots:
    void onSelectedInfoChanged(IimageInfo* iInfo);

private:
    ChisonUltrasoundContext* m_ChisonUltrasoundContext;
    ImageTile* m_ImageTile;
    ThumbnailDialog* m_ThumbnailDialog;
    SonoParameters* m_CurSonoparameters;
    bool m_FunctionAvailable;
    Qt::MouseButton m_ClickedButton;
#ifdef USE_FREEHAND3D
    FreeHand3DProxy* m_FreeHand3DProxy;
#endif
};

#endif // LOADCOMMAND_H
