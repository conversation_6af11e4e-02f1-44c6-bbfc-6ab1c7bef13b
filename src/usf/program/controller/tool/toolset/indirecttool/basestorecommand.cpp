#include "basestorecommand.h"
#include "ibufferstoremanager.h"
#include "progresssliderwidget.h"
#include "istatemanager.h"
#include "ipatientworkflowmodel.h"
#include "ipatientworkflow.h"
#include "imageskimmanager.h"
#include "stressechowidgetmanager.h"
#include "statefilterlocker.h"
#include "lightcontroller.h"
#include "infostruct.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "ilinebuffermanager.h"
#include "glyphscontrolmanager.h"
#include "arrowglyphscontrol.h"
#include "stateeventnames.h"

BaseStoreCommand::BaseStoreCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager,
                                   SonoParameters* sonoParameters, IMainWindow* flexBtnVisible,
                                   BaseWidget* imageClipWidget, IBufferStoreManager* bufferStoreManager,
                                   ChisonUltrasoundContext* ultrasoundContext, MeasureContext* measureContext)
    : CommandObject(name)
    , m_Slider(dynamic_cast<ProgressSliderWidget*>(slider))
    , m_StateManager(stateManager)
    , m_RealSonoParameters(sonoParameters)
    , m_FlexBtnVisible(dynamic_cast<MainWindowKBUnit*>(flexBtnVisible))
    , m_ImageClipWidget(dynamic_cast<ImageSkimManager*>(imageClipWidget))
    , m_BufferStoreManager(bufferStoreManager)
    , m_ChisonUltrasoundContext(ultrasoundContext)
    , m_MeasureContext(measureContext)
    , m_Started(false)
{
}

void BaseStoreCommand::setFlexButtonVisible(bool isVisible)
{
    m_FlexBtnVisible->setFullScreenFlexBtnVisible(isVisible);
    m_FlexBtnVisible->setLockScreenFlexBtnVisible(isVisible);
}

void BaseStoreCommand::setNavigationEllipseShow(bool isVisible)
{
    m_RealSonoParameters->setPV(BFPNames::IsNavigationEllipseHiddenStr, !isVisible);
}

void BaseStoreCommand::controlGlyphsControl(bool enabled)
{
    QList<GlyphsControl*> allGlyphsControls = GlyphsControlManager::instance().getAllGlyphsControls();
    foreach (GlyphsControl* control, allGlyphsControls)
    {
        control->controlGlyphs(enabled);
    }
}

void BaseStoreCommand::onCommandFinished(LoadErrorCode loadErrorCode)
{
    m_Started = false;
    if (m_Slider != NULL && !m_Slider->isHidden())
    {
        m_Slider->hideProgessSlider();
    }
    m_StateManager->setPaused(false);
}

void BaseStoreCommand::onCommandFinished(bool success)
{
    m_Started = false;
    if (m_Slider != NULL && !m_Slider->isHidden())
    {
        m_Slider->hideProgessSlider();
    }
    m_StateManager->setPaused(false);
}

bool BaseStoreCommand::checkDiskSpace()
{
    return true;
}

void BaseStoreCommand::execute()
{
    doExecute();
}

void BaseStoreCommand::doExecute()
{
    if (checkDiskSpace())
    {
        innerExecute();
    }
    else
    {
        emit exitSave();
        LightController::instance().light(LightNames::MovieStr, false);
    }
}

void BaseStoreCommand::doBlockExecute()
{
    StateFilterLocker* helper = new StateFilterLocker(m_StateManager, StateEventNames::DialogStateFilter());

    if (checkDiskSpace())
    {
        delete helper;

        innerExecute();
    }
    else
    {
        delete helper;
    }
}

void BaseStoreCommand::innerExecute()
{
    if (!m_Started)
    {
        if (isFreehand3DEditExamMode())
        {
            return;
        }
        if (!isSupportSave())
        {
            return;
        }
        emit beforeSave();
        m_Started = true;
        beginOperate();
        m_StateManager->setPaused(true);
        operate();
    }
}

void BaseStoreCommand::beginOperate()
{
}

bool BaseStoreCommand::isFreehand3DEditExamMode()
{
    return false;
}

bool BaseStoreCommand::isFreeHand3DMode() const
{
    return m_RealSonoParameters->pBV(BFPNames::FreeHand3DModeStr);
}

bool BaseStoreCommand::isSupportSave() const
{
    return true;
}
