#include "autoblinetool.h"
#include "chisonultrasoundcontext.h"
#include "imeasurement.h"
#include "iruler.h"
#include "util.h"
#include "measurecontext.h"
#include "measurementmodelutil.h"
#include "glyphscontrolmanager.h"
#include "imageinfodef.h"
#include "ibufferstoremanager.h"
#include "ilinebuffermanager.h"
#include "imageframesavethread.h"
#include "autoblineglyphscontrol.h"
#include "screenmeasurecontroller.h"
#include "toolsfactory.h"
#include "toolnames.h"
#include "imagetile.h"
#include "storeimagecommand.h"
#include "storeimagetoudiskcommand.h"
#include "resource.h"

AutoBLineTool::AutoBLineTool(const QString& name, ChisonUltrasoundContext* chisonContext, SonoParameters* sonoparameter,
                             IMeasurement* packagesMeasurement, MeasureContext* measureContext,
                             IBufferStoreManager* bufferStoreManager, ILineBufferManager* lineBufferManager,
                             CineLooper*, ImageTile* imagetile, ToolsOrderManager*,
                             IScreenMeasureController* screenMeasureController)
    : ConditionalTool(name)
    , m_ChisonUltrasoundContext(chisonContext)
    , m_Ruler(NULL)
    , m_PackagesMeasurement(packagesMeasurement)
    , m_Measure(NULL)
    , m_MeasureContext(measureContext)
    , m_RealSonoParameters(sonoparameter)
    , m_ScreenMeasureController(screenMeasureController)
    , m_imageTile(imagetile)
{
    connect(bufferStoreManager, SIGNAL(savedCine(QString)), this, SLOT(doSavedCine(QString)));
    connect(lineBufferManager, SIGNAL(realTimeSaveCineState(int)), this, SLOT(doRealTimeSaveCine(int)));

    m_SupportExamMode = Resource::functionalConfigurationSettingValue("AutoBLine", Resource::ValueType::ExamMode);
    m_IsEnableWhenMLineShow = false;
    m_IsEnableWhenPwLineShow = false;
    m_SupportScanMode.append(SystemScanModeB);
    m_SupportLayout.append(Layout_1x1);

    StoreImageCommand* cmd1 =
        dynamic_cast<StoreImageCommand*>(ToolsFactory::instance().command(ToolNames::StoreImageStr));
    StoreImageCommand* cmd2 =
        dynamic_cast<StoreImageToUDiskCommand*>(ToolsFactory::instance().command(ToolNames::StoreImageToUDiskStr));
    connect(cmd1, SIGNAL(beforeSave()), this, SLOT(saveImage()), Qt::UniqueConnection);
    connect(cmd2, SIGNAL(beforeSave()), this, SLOT(saveImage()), Qt::UniqueConnection);

    connect(this, SIGNAL(conditionalChange(bool)), this, SLOT(onConditionalChange(bool)),
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
}

AutoBLineTool::~AutoBLineTool()
{
}

void AutoBLineTool::execute()
{
    MenuTool::execute();
    if (m_SonoParameters->pBV(BFPNames::AutoBLineStr))
    {
        loadMeasurement();
        if (m_Measure == NULL)
        {
            return;
        }
        if (m_IsFreeze)
        {
            m_MeasureContext->setImageWithoutValidate(m_imageTile->currentImage());
        }
        MeasurementModelUtil::runMeasurement(m_Measure, false);
    }
    else
    {
        onSwitchRTImage(false);
        MeasurementModelUtil::stopMeasurement();
        m_ScreenMeasureController->clearResult();
        ToolsFactory::instance().command(ToolNames::DeleteGlyphsStr)->run();
    }
}

void AutoBLineTool::onConditionalChange(bool enable)
{
    if (!enable && value() != 0)
    {
        run();
    }
}

void AutoBLineTool::onFreezeChanged(QVariant var)
{
    //冻结到实时 关闭
    if (!m_RealSonoParameters->pBV(BFPNames::FreezeStr) && m_SonoParameters->pBV(BFPNames::AutoBLineStr))
    {
        run();
    }
    //冻结
    if (m_RealSonoParameters->pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime())
    {
        m_IsFreeze = true;
    }
    else
    {
        AutoBLineGlyphsControl* control =
            GlyphsControlManager::instance().getChildGlyphsControl<AutoBLineGlyphsControl>(
                GlyphsCtl::AutoBLineMeasureGlyphsType);
        m_IsFreeze = false;
    }
    AutoBLineGlyphsControl* control = GlyphsControlManager::instance().getChildGlyphsControl<AutoBLineGlyphsControl>(
        GlyphsCtl::AutoBLineMeasureGlyphsType);
    control->setIsFreeze(m_IsFreeze);

    ConditionalTool::onFreezeChanged(var);
}

void AutoBLineTool::loadMeasurement()
{
    QStringList id = Resource::functionalConfigurationSettingValue("AutoBLine", Resource::ValueType::MeasurementId);
    if (id.isEmpty())
    {
        id.append("911842b1-d18a78-0d48935b");
    }
    IMeasurement* measurement = m_PackagesMeasurement->findChildById(id.first());
    if (measurement == NULL)
    {
        return;
    }
    m_Measure = measurement;
    if (m_Measure == NULL)
    {
        return;
    }
    if (m_Measure != NULL)
    {
        m_Measure->setIsNeedUserConfirmAI();
        m_Ruler = m_Measure->currentRuler();
        if (m_Ruler != NULL)
        {
            connect(m_Ruler, SIGNAL(receiveRTImage(bool)), this, SLOT(onSwitchRTImage(bool)),
                    Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
        }
    }
}

void AutoBLineTool::saveData(const QString& filename)
{
    QFileInfo info(filename);
    QString fileName(info.path() + "/" + info.baseName() + "/" + "autobline.bin");
    bool ret = false;
    QFile file(fileName);
    if (file.open(QFile::WriteOnly | QFile::Truncate))
    {
        QDataStream out(&file);
        AutoBLineGlyphsControl* control =
            GlyphsControlManager::instance().getChildGlyphsControl<AutoBLineGlyphsControl>(
                GlyphsCtl::AutoBLineMeasureGlyphsType);
        out << control->fileResultMap();
        ret = out.status() == QDataStream::Ok;
    }
    Util::fSync(file);
    file.close();
    if (!ret)
    {
        QFile::remove(fileName);
    }
}

void AutoBLineTool::onSwitchRTImage(bool isConnect)
{
    Util::connectSignal(m_MeasureContext, SIGNAL(newImage(ImageEventArgs*)), this,
                        SLOT(processNewImage(ImageEventArgs*)), isConnect,
                        Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
}

void AutoBLineTool::processNewImage(ImageEventArgs* args)
{
    if (args->imageType() != ImageEventArgs::ImageB)
    {
        return;
    }
    m_Ruler->setFrameTimeStamp(args->getFrameStartTime());
    if (m_IsFreeze)
    {
        m_Ruler->drawGlyphsWhenFreeze();
    }
    else
    {
        PainterImageModel imageModel;
        imageModel.setData(args);
        PainterImageData* imageData = imageModel.data();
        QImage img(imageData->m_Data, imageData->m_Width, imageData->m_Height, QImage::Format_RGB32);
        QImage deepCopy = img.copy();
        m_MeasureContext->setImageWithoutValidate(deepCopy);
        m_Ruler->runAlg();
    }
}

void AutoBLineTool::doSavedCine(const QString& filename)
{
    //冻结存 或者 实时存
    if (m_RealSonoParameters->pBV(BFPNames::AutoBLineStr))
    {
        if (m_RealSonoParameters->pBV(BFPNames::FreezeStr))
        {
            saveData(filename);
        }
        else
        {
            m_FileName = filename;
        }
    }
}

void AutoBLineTool::doRealTimeSaveCine(int state)
{
    if (state == ImageFrameSaveThread::Finished)
    {
        saveData(m_FileName);
    }
}

void AutoBLineTool::onCurSonoParametersChanged()
{
    if (m_SonoParameters != NULL && m_SonoParameters->pBV(BFPNames::AutoBLineStr))
    {
        MeasurementModelUtil::stopMeasurement();
        if (m_Ruler == NULL)
        {
            loadMeasurement();
        }
        m_Ruler->stop();
    }
    ConditionalTool::onCurSonoParametersChanged();
}

void AutoBLineTool::saveImage()
{
    if (m_Measure == NULL)
    {
        return;
    }
    m_Measure->onBeforeSave();
    m_Measure->updateAllMeasureResult(m_Measure->leftRight(), m_Measure->farMidNear(), m_Measure->fetusIndex(), true);
}

void AutoBLineTool::startRTMeasure(const QString& filePath)
{
    AutoBLineGlyphsControl* control = GlyphsControlManager::instance().getChildGlyphsControl<AutoBLineGlyphsControl>(
        GlyphsCtl::AutoBLineMeasureGlyphsType);
    QFileInfo info(filePath);
    QString fileName(info.path() + "/" + info.baseName() + "/" + "autobline.bin");

    if (!QFile::exists(fileName))
    {
        return;
    }

    control->fileResultMap().clear();
    QFile file(fileName);
    if (file.open(QIODevice::ReadOnly))
    {
        QDataStream in(&file);
        in >> control->fileResultMap();
        if (in.status() != QDataStream::Ok)
        {
            control->fileResultMap().clear();
        }
    }
    file.close();
    if (m_Measure == NULL)
    {
        loadMeasurement();
    }
    m_Measure->setIsNeedUserConfirmAI();
    MeasurementModelUtil::runMeasurement(m_Measure, false);
    return;
}
