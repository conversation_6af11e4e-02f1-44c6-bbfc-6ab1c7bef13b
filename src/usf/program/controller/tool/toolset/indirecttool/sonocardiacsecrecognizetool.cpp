#include "sonocardiacsecrecognizetool.h"
#include "chisonultrasoundcontext.h"
#include "imageinfodef.h"
#include "sonocardiacglyphscontrol.h"
#include "toolnames.h"
#include "util.h"
#include "bfpnames.h"
#include "measurecontext.h"

SonoCardiacSecRecognizeTool::SonoCardiacSecRecognizeTool(const QString& name,
                                                         ChisonUltrasoundContext* chisonUltrasoundContext,
                                                         MeasureContext* measureContext)
    : MenuTool(name)
    , m_ChisonUltrasoundContext(chisonUltrasoundContext)
    , m_MeasureContext(measureContext)
{
}

void SonoCardiacSecRecognizeTool::execute()
{
    MenuTool::execute();
    bool isOpen = m_SonoParameters->pBV(toolName());
    controlRecognize(isOpen);
}

void SonoCardiacSecRecognizeTool::onNewImage(ImageEventArgs* args)
{
    if (args->imageType() == ImageEventArgs::ImageB && m_SonoParameters->pBV(BFPNames::SonoCardiacSecRecognizeStr))
    {
        QImage img(args->imageData(), args->width(), args->height(), QImage::Format_RGB32);
        m_Control->ReconizeSection(img);
    }
}

void SonoCardiacSecRecognizeTool::controlRecognize(bool isOpen)
{
    if (m_SonoParameters == NULL)
        return;
    isOpen ? m_Control->addTextItemToOverLay() : m_Control->removeTextItemInOverLay();
    m_Control->setSecRecSwitch(isOpen ? 1 : 0);
    Util::connectSignal(m_ChisonUltrasoundContext, SIGNAL(newBImage(ImageEventArgs*)), this,
                        SLOT(onNewImage(ImageEventArgs*)),
                        isOpen || m_SonoParameters->pBV(ToolNames::SonoCardiacSecRatingStr),
                        Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
}
