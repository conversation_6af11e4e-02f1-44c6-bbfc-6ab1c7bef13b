#include "storeimagecommand.h"
#include "ibufferstoremanager.h"
#include "resource.h"
#include <QDateTime>
#include "lightcontroller.h"
#include "imagewidget.h"
#include "imagetile.h"
#include "ibeamformer.h"
#include "bfpnames.h"
#include "setting.h"
#include "screenshots.h"
#include "util.h"
#include "filesexporter.h"
#include "imagenummanagement.h"
#include "sonoparameters.h"
#include "patienteditmodel.h"
#include "generalworkflowfunction.h"
#include "stateeventnames.h"
#include "statemanager.h"
#include "abstractstate.h"
#include "ipatientworkflow.h"
#include "study.h"
#include "chisonultrasoundcontext.h"
#include "measurecontext.h"

StoreImageCommand::StoreImageCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager,
                                     SonoParameters* sonoParameters, IMainWindow* flexBtnVisible,
                                     BaseWidget* imageClipWidget, IBufferStoreManager* bufferStoreManager,
                                     IPatientWorkflow* patientWorkflow, IDicomTaskManager* dicomTaskManager,
                                     IStressEchoModel* stressEchoModel, BaseWidget* storeProgressMovieWidget,
                                     PatientEditModel* patientEditModel, ISystemStatusModel* systemStatusModel,
                                     QWidget* sonoZoomWidget, IZoomOnProxy* zoomOnProxy, IBeamFormer* beamFormer,
                                     CineLooper* cineLooper, IColorMapManager* colorMapManager, ISystemInfo* systemInfo,
                                     ImageTile* imageTile, IDicomUtilityCreator* dicomUtilityCreator,
                                     IDicomToolCreator* dicomToolCreator, ChisonUltrasoundContext* ultrasoundContext,
                                     MeasureContext* measureContext)
    : StoreCineCommand(name, slider, stateManager, sonoParameters, flexBtnVisible, imageClipWidget, bufferStoreManager,
                       patientWorkflow, dicomTaskManager, stressEchoModel, storeProgressMovieWidget, patientEditModel,
                       systemStatusModel, sonoZoomWidget, zoomOnProxy, beamFormer, cineLooper, colorMapManager,
                       systemInfo, imageTile, dicomUtilityCreator, dicomToolCreator, ultrasoundContext, measureContext)
    , m_RealTime(false)
    , m_originScreenType(ScreenTypeDef::Image)
    , m_RealTimePaused(false)
{
    m_IsCine = false;
    m_Slider = NULL;
    setDefaultLightName(LightNames::StorageStr);
}

void StoreImageCommand::doSave(const QString& fileName)
{
    m_BufferStoreManager->asyncSaveCurrentFrame(fileName, m_RealTimeSaveIndex);
}

QString StoreImageCommand::fileExt() const
{
    return Resource::imgExt;
}

void StoreImageCommand::beginOperate()
{
    m_ChisonUltrasoundContext->setIsPause(true);

    Util::processEvents(QEventLoop::ExcludeUserInputEvents);

    StoreCineCommand::beginOperate();
    //实时状态时保存，先暂停刷新图像，可以提高保存的速度
    if (!m_BeamFormer->isFrozen())
    {
        if (Setting::instance().defaults().pauseWhenStoreImage())
        {
            m_RealTime = true;
            if (!m_RealTimePaused)
            {
                m_ImageTile->setIsPauseWhileFreezeAndChanging(true);

                m_RealTimePaused = true;

                Util::processEvents(QEventLoop::ExcludeUserInputEvents); //保证在实时截屏时图像已经全部刷新
            }
            //必须保证保存到文件中的freeze参数为true，否则回调图像时系统会进入实时模式
            //            m_BeamFormer->setPDV(BFPNames::FreezeStr, true);
        }
    }
    else
    {
        // 统一使用ImageZoomInTool的操作设置是否全屏截图 xule
        // 放大模式下默认保存图片为全屏 by Jin Yuqi
        //    if (m_ImageTile->isZoomedIn())
        //    {
        //        m_originScreenType = Setting::instance().defaults().screenType();
        //        Setting::instance().defaults().setScreenType(ScreenTypeDef::FullScreen);
        //    }
        m_RealTimeSaveIndex = -1;
    }
}

void StoreImageCommand::showSavingMovie(bool inFourDWidget)
{
}

QImage StoreImageCommand::grabThumbnailScreen()
{
    return Screenshots::instance().grabThumbnailScreen();
}

void StoreImageCommand::saveThumbnailScreen(const QImage& image, const QString& fileBaseName)
{
    if (!image.isNull() && !Screenshots::instance().isZoomIn())
    {
        QString name(fileBaseName);
        name.append(Resource::calcExt);
        Screenshots::instance().saveScreenEx(image, name, "PNG");
    }
}

void StoreImageCommand::exportFile(bool isGDPR)
{
    QString fileName = Util::getSameBaseNamesFileName(m_FileName, Setting::instance().defaults().screenExt());
    m_FilesExporter->doExport(QStringList() << fileName, isGDPR);
}

bool StoreImageCommand::isFreehand3DEditExamMode()
{
    if (isFreeHand3DMode())
    {
        if (m_PatientWorkflow->patient() != NULL)
        {
            QString studyOid = m_PatientWorkflow->patient()->firstStudy()->StudyOid();
            if (!studyOid.isEmpty())
            {
                bool continues = m_PatientEditModel->generalWorkflowFunction()->isOrNotContinueStudy(studyOid);
                if (!continues)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

bool StoreImageCommand::isSupportSave() const
{
    return true;
}

void StoreImageCommand::onCommandFinished(bool succeed)
{
    StoreCineCommand::onCommandFinished(succeed);

    if (Setting::instance().defaults().pauseWhenStoreImage())
    {
        if (m_RealTime)
        {
            m_RealTime = false;
            //            m_BeamFormer->setPDV(BFPNames::FreezeStr, false);
            m_ImageTile->setIsPauseWhileFreezeAndChanging(false);
            m_RealTimePaused = false;
        }
    }
    m_RealTimeSaveIndex = -1;
    // 恢复之前默认的screenType by Jin Yuqi
    //    if (m_ImageTile->isZoomedIn())
    //    {
    //        Setting::instance().defaults().setScreenType(m_originScreenType);
    //    }

    m_ChisonUltrasoundContext->setIsPause(false);
}
