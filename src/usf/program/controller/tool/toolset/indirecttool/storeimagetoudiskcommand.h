/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef STOREIMAGETOUDISKCOMMAND_H
#define STOREIMAGETOUDISKCOMMAND_H
#include "usfprogramcontroller_global.h"

#include "storeimagecommand.h"

class USF_PROGRAM_CONTROLLER_EXPORT StoreImageToUDiskCommand : public StoreImageCommand
{
    Q_OBJECT
public:
    StoreImageToUDiskCommand(const QString& name, BaseWidget* slider, IStateManager* stateManager,
                             SonoParameters* sonoParameters, IMainWindow* flexBtnVisible, BaseWidget* imageClipWidget,
                             IBufferStoreManager* bufferStoreManager, IPatientWorkflow* patientWorkflow,
                             IDicomTaskManager* dicomTaskManager, IStressEchoModel* stressEchoModel,
                             BaseWidget* storeProgressMovieWidget, PatientEditModel* patientEditModel,
                             ISystemStatusModel* systemStatusModel, QWidget* sonoZoomWidget, IZoomOnProxy* zoomOnProxy,
                             IBeamFormer* beamFormer, CineLooper* cineLooper, IColorMapManager* colorMapManager,
                             ISystemInfo* systemInfo, ImageTile* imageTile, IDicomUtilityCreator* dicomUtilityCreator,
                             IDicomToolCreator* dicomToolCreator, ChisonUltrasoundContext* ultrasoundContext,
                             MeasureContext* measureContext);

protected:
    virtual bool checkDiskSpace();
    virtual void execute();
    virtual QString generateFileName() const;
    virtual void checkTargetDir() const;
    QString screenSuffix() const;
    virtual void exportFile();
};

#endif // STOREIMAGETOUDISKCOMMAND_H
