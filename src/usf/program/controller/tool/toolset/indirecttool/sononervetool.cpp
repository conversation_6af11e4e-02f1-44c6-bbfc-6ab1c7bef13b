#include "sononervetool.h"
#include "appsetting.h"
#include "bfpnames.h"
#include "chisonultrasoundcontext.h"
#include "glyphscontrolmanager.h"
#include "ibufferstoremanager.h"
#include "imagetile.h"
#include "imagewidget.h"
#include "iprobedataset.h"
#include "irawimagebufferdatasetter.h"
#include "licenseitemkey.h"
#include "logger.h"
#include "measurecontext.h"
#include "menunames.h"
#include "menutools.h"
#include "resource.h"
#include "sonobuffer.h"
#include "sonobuffers.h"
#include "sononerveglyphscontrol.h"
#include "sonoparameters.h"
#include "stateeventnames.h"
#include "statemanager.h"
#include "stringcommandargs.h"
#include "toolnames.h"
#include "toolsfactory.h"
#include "util.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, SonoNerveTool)
SonoNerveTool::SonoNerveTool(const QString& name, SonoParameters* sonoParameters, ImageTile* tile,
                             MeasureContext* measContext, ChisonUltrasoundContext* ultrasoundContext)
    : MenuTool(name)
    , m_RealSonoParameters(sonoParameters)
    , m_ImageTile(tile)
    , m_MeasContext(measContext)
    , m_ChisonUltrasoundContext(ultrasoundContext)
    , m_ProbeDataSet(NULL)
    , m_Running(false)
{
    Util::connectSignal(m_ChisonUltrasoundContext, SIGNAL(newBImage(ImageEventArgs*)), this,
                        SLOT(onNewImage(ImageEventArgs*)), true, Qt::ConnectionType(Qt::DirectConnection));

    connect(AppSetting::eventObject(), &AppSettingEventObject::functionChanged, this,
            &SonoNerveTool::onFunctionStatusChanged);

    m_SupportExamMode = Resource::functionalConfigurationSettingValue("SonoNerve", Resource::ValueType::ExamMode);
}

void SonoNerveTool::execute()
{
    if (m_SonoParameters == nullptr)
        return;

    if (!m_RealSonoParameters->pBV(BFPNames::FreezeStr))
        dynamic_cast<SonoBuffers*>(m_ChisonUltrasoundContext->lineBuffer())->activeDataSetter()->clear();

    if (m_Control == NULL)
        m_Control = GlyphsControlManager::instance().getChildGlyphsControl<SonoNerveGlyphsControl>(
            GlyphsCtl::SonoNerveGlyphsType);
    //如果实时神经正在运行，则退出实时神经功能，否则开启实时神经功能
    if (!m_SonoParameters->pBV(BFPNames::SonoNerveStr))
    {
        m_Running = true;
        m_SonoNervePart = 0;

        if (m_Control != nullptr)
        {
            int renderHeight = m_SonoParameters->pV(BFPNames::RenderImageSizeStr).value<QSize>().height();
            m_Control->setRenderWidgetHeight(renderHeight);
            m_Control->initNeura(m_SonoNervePart); //默认是第一个，肌肩沟
        }
        m_SonoParameters->setPV(BFPNames::SonoNervePartStr, 0);
        m_SonoParameters->setPV(BFPNames::SonoNerveIsShowStr, true);
        if (m_Control != nullptr)
        {
            m_Control->setIsActive(true);
            m_Control->showActive();
        }
    }

    MenuTool::execute();

    StateManager::getInstance().postEvent(StateEventNames::B);

    if (m_SonoParameters->pBV(BFPNames::SonoNerveStr))
    {
        StateManager::getInstance().postEvent(StateEventNames::SonoNerveState);
    }

    // StateManager::getInstance().postEvent(StateEventNames::SonoNerveState);
}

void SonoNerveTool::onNewImage(ImageEventArgs* args)
{
    SonoNerveGlyphsControl* control =
        GlyphsControlManager::instance().getChildGlyphsControl<SonoNerveGlyphsControl>(GlyphsCtl::SonoNerveGlyphsType);

    if (m_SonoParameters == nullptr || control == nullptr || m_ChisonUltrasoundContext->getIsPause())
        return;

    if (!m_SonoParameters->pBV(BFPNames::SonoNerveStr)) //自动神经功能未开
    {
        m_Running = false;
        control->hideActive();
        control->neuralsgRelease();
        control->setIsActive(false);

        return;
    }
    else
    {
        m_Running = true;
        if (!control->isCreatectx() || m_SonoNervePart != m_SonoParameters->pIV(BFPNames::SonoNervePartStr))
        {
            control->initNeura(m_SonoParameters->pIV(BFPNames::SonoNervePartStr));
            m_SonoNervePart = m_SonoParameters->pIV(BFPNames::SonoNervePartStr);
        }
    }

    m_SonoParameters->pBV(BFPNames::SonoNerveIsShowStr) ? control->showActive() : control->hideActive();

    control->setIsActive(true);
    control->setTransparent(m_SonoParameters->pIV(BFPNames::SonoNerveTransStr));
    control->setInvert(!m_BufferStoreManager->activeSonoParameters()->pBV(ToolNames::UDStr));
    control->setLRInvert(!m_BufferStoreManager->activeSonoParameters()->pBV(ToolNames::LRStr));
    if (args->imageType() == ImageEventArgs::ImageB && (m_Running || m_SonoParameters->pBV(BFPNames::SonoNerveStr)))
    {
        QImage img(args->imageData(), args->width(), args->height(), QImage::Format_RGB32);
        control->update(img, m_SonoParameters->pBV(BFPNames::SonoNerveIsShowStr));
    }
}

void SonoNerveTool::onExamModeChanged()
{
    m_isExamModeNameChanged = true;
}

void SonoNerveTool::onFunctionStatusChanged()
{
    setMenuState();
}

void SonoNerveTool::playRTMeasureCine(ImageEventArgs* arg)
{
    disconnect(m_MeasContext, &MeasureContext::newImage, this, &SonoNerveTool::playRTMeasureCine);

    if (m_easyViewSonoParameters == NULL || !m_RunningInEasyView)
        return;

    int width = arg->width();
    int height = arg->height();
    m_EasyViewControl->update(QImage(arg->imageData(), width, height, QImage::Format_RGB32),
                              m_easyViewSonoParameters->pBV(BFPNames::SonoNerveIsShowStr));

    if (m_easyViewSonoParameters == NULL || !m_RunningInEasyView)
        return;

    connect(m_MeasContext, &MeasureContext::newImage, this, &SonoNerveTool::playRTMeasureCine,
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
}

void SonoNerveTool::prepareData()
{
    m_EasyViewControl =
        GlyphsControlManager::instance().getChildGlyphsControl<SonoNerveGlyphsControl>(GlyphsCtl::SonoNerveGlyphsType);
    m_EasyViewControl->initNeura(m_easyViewSonoParameters->pIV(BFPNames::SonoNervePartStr));
    m_SonoNervePart = m_easyViewSonoParameters->pIV(BFPNames::SonoNervePartStr);
    m_easyViewSonoParameters->pBV(BFPNames::SonoNerveIsShowStr) ? m_EasyViewControl->showActive()
                                                                : m_EasyViewControl->hideActive();
    m_EasyViewControl->setIsActive(true);
    m_EasyViewControl->setTransparent(m_easyViewSonoParameters->pIV(BFPNames::SonoNerveTransStr));
    m_EasyViewControl->setInvert(!m_easyViewSonoParameters->pBV(ToolNames::UDStr));
    m_EasyViewControl->setLRInvert(!m_easyViewSonoParameters->pBV(ToolNames::LRStr));
    m_RunningInEasyView = true;
}

void SonoNerveTool::setProbeDataSet(IProbeDataSet* probeDataSet)
{
    m_ProbeDataSet = probeDataSet;
}

void SonoNerveTool::onBeforeCurSonoParametersChanged()
{
    MenuTool::onBeforeCurSonoParametersChanged();
    forDisConnect();
}

void SonoNerveTool::onCurSonoParametersChanged()
{
    MenuTool::onCurSonoParametersChanged();
    forConnect();
    setMenuState();
}

void SonoNerveTool::onProbeChanged(const QVariant& value)
{
    stopSonoNerve();
    setMenuState();
}

void SonoNerveTool::onInitialize()
{
    MenuTool::onInitialize();
}

void SonoNerveTool::setEasyViewSonoParameters(SonoParameters* sono)
{
    m_easyViewSonoParameters = sono;
    if (m_easyViewSonoParameters != NULL)
        prepareData();
    else
        m_RunningInEasyView = false;
}

void SonoNerveTool::onSystemScanModeChanged(const QVariant& value)
{
    stopSonoNerve();
    setMenuState();
}

void SonoNerveTool::onExamModeIdChanged(const QVariant& value)
{
    stopSonoNerve();
    setMenuState();
}

void SonoNerveTool::onSonoNervePartChanged(const QVariant& value)
{
    if (!m_SonoParameters->pBV(BFPNames::SonoNerveStr))
        return;

    if (m_SonoNervePart != value.toInt())
    {
        if (!m_RealSonoParameters->pBV(BFPNames::FreezeStr))
            dynamic_cast<SonoBuffers*>(m_ChisonUltrasoundContext->lineBuffer())->activeDataSetter()->clear();
        m_SonoNervePart = value.toInt();
        if (m_Control != nullptr)
        {
            m_Control->initNeura(m_SonoNervePart);
        }
    }
}

void SonoNerveTool::onLayoutChanged(const QVariant& value)
{
    int layout = m_SonoParameters->pIV(BFPNames::LayoutStr);

    if (layout != Layout_1x1)
    {
        stopSonoNerve();
    }

    setMenuState();
}

void SonoNerveTool::onIsShowChanged(const QVariant& value)
{
    if (m_Control != nullptr)
    {
        value.toBool() ? m_Control->showActive() : m_Control->hideActive();
    }
}

void SonoNerveTool::stopSonoNerve()
{
    m_MeasContext->setRealTimeMeasureFrameIndex(-1);
    m_MeasContext->setRealTimeMeasureFrontIndex(-1);

    if (m_SonoParameters != nullptr)
    {
        if (m_SonoParameters->pBV(BFPNames::SonoNerveStr))
        {
            m_SonoParameters->setPV(BFPNames::SonoNerveStr, false);
            if (m_Control != nullptr)
            {
                if (m_Control->isActive())
                {
                    m_Control->hideActive();
                    m_Control->neuralsgRelease();
                }
            }
        }

        m_isExamModeNameChanged = false;
    }
}

void SonoNerveTool::onFreezeChanged(const QVariant& value)
{
    setMenuState();
}

void SonoNerveTool::onScanLineChanged(const QVariant& value)
{
    if (m_SonoParameters != nullptr)
    {
        stopSonoNerve();
        setMenuState();
    }
}

void SonoNerveTool::onPresetChanged(const PresetParameters& value)
{
    stopSonoNerve();
    setMenuState();
}

void SonoNerveTool::onSonoNerveOnChanged(const QVariant& value)
{
    StringCommandArgs args;
    if (value.toBool())
        args.setArg(MenuNames::SonoNerveMenuStr);
    else
        args.setArg(MenuNames::BMenuStr);

    ICommand* changeTool = ToolsFactory::instance().command(ToolNames::ChangeStr);
    changeTool->setArgs(&args);
    changeTool->run();
    dynamic_cast<ChangeTool*>(changeTool)->setCurrentMenus();

    StateManager::getInstance().setIsCloseSonoNerve(!value.toBool());
}

void SonoNerveTool::forDisConnect()
{
    if (m_SonoParameters != nullptr)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSystemScanModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ExamModeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onExamModeIdChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onProbeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoNervePartStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSonoNervePartChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onLayoutChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoNerveIsShowStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onIsShowChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFreezeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                   this, SLOT(onScanLineChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::IsMLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onScanLineChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SonoNerveStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSonoNerveOnChanged(QVariant)));
        disconnect(m_SonoParameters, SIGNAL(presetChanged(const PresetParameters&)), this,
                   SLOT(onPresetChanged(const PresetParameters&)));
    }
}

void SonoNerveTool::forConnect()
{
    if (m_SonoParameters != nullptr)
    {
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSystemScanModeChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::ExamModeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onExamModeIdChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onProbeChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::SonoNervePartStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSonoNervePartChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onLayoutChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::SonoNerveIsShowStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onIsShowChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreezeChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                this, SLOT(onScanLineChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::IsMLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onScanLineChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters->parameter(BFPNames::SonoNerveStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSonoNerveOnChanged(QVariant)), Qt::UniqueConnection);
        connect(m_SonoParameters, SIGNAL(presetChanged(const PresetParameters&)), this,
                SLOT(onPresetChanged(const PresetParameters&)), Qt::UniqueConnection);

        if (m_isExamModeNameChanged)
        {
            stopSonoNerve();
            setMenuState();
        }
    }
}

bool SonoNerveTool::availableControl()
{
    bool isEnable = false;
    if (m_SonoParameters != nullptr)
    {
        isEnable = true;
        //单B模式
        SystemScanMode mode = (SystemScanMode)m_SonoParameters->pIV(BFPNames::SystemScanModeStr);
        int layout = m_SonoParameters->pIV(BFPNames::LayoutStr);

        if (layout != Layout_1x1)
        {
            isEnable = false;
        }
        if (mode != SystemScanModeB)
        {
            isEnable = false;
        }

        //神经预设
        if (!m_SupportExamMode.contains(m_SonoParameters->pSV(BFPNames::ExamModeIdStr)))
        {
            isEnable = false;
        }

        //线阵
        int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
        if (!m_ProbeDataSet->getProbe(probeId).IsLinear)
        {
            isEnable = false;
        }

        if (m_SonoParameters->pIV(BFPNames::FreezeStr))
        {
            isEnable = false;
        }

        if (!AppSetting::isHuman())
        {
            isEnable = false;
        }

        // PW Pre  or M Pre
        if (m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr) ||
            m_SonoParameters->pBV(BFPNames::IsMLineVisibleStr))
        {
            isEnable = false;
        }

        //回调时，SonoNerve按钮置灰
        if (!m_BufferStoreManager->curSonoParameters()->isRealTime())
        {
            isEnable = false;
        }

        setIsAvailable(isEnable);
    }

    return isEnable;
}

bool SonoNerveTool::visibleControl()
{
    bool isVisible = false;

    if (m_SonoParameters != nullptr)
    {
        isVisible = true;
        //神经预设
        if (!m_SupportExamMode.contains(m_SonoParameters->pSV(BFPNames::ExamModeIdStr)))
        {
            isVisible = false;
        }
    }

    if (!AppSetting::isFunctionEnabled(LicenseItemKey::KeySonoNerve))
    {
        isVisible = false;
    }

    setIsVisible(isVisible);

    return isVisible;
}

void SonoNerveTool::setMenuState()
{
    bool isEnable = availableControl();
    bool isVisible = visibleControl();

    StateManager::getInstance().setIsSupportSonoNerve(isEnable && isVisible);
}
