#include "toolscreator.h"
#include "abstractmachine.h"
#include "ipresetmodel.h"
#include "isonoparaswidget.h"
#include "activebchangetool.h"
#include "aiotool.h"
#include "anglechangetool.h"
#include "anglezoomentermeasuretool.h"
#include "anglezoomgeneraltool.h"
#include "anglezoomrotategeneraltool.h"
#include "applicationexitcommand.h"
#include "archiveentertool.h"
#include "arrowchangetool.h"
#include "arrowdeletelastonetool.h"
#include "arrowglyphscontrol.h"
#include "arrowmousetool.h"
#include "arrowposteventtool.h"
#include "arrowscaletool.h"
#include "autoeftool.h"
#include "basemousecommandcontext.h"
#include "bccwpremousetool.h"
#include "bcdmousetool.h"
#include "bcdpremousetool.h"
#include "bcdpwmousetool.h"
#include "bcwpremousetool.h"
#include "bdmousetool.h"
#include "bdpremousetool.h"
#include "bdpwmousetool.h"
#include "bdynamicrangegeneraltool.h"
#include "bfpnames.h"
#include "bgaingeneraltool.h"
#include "biopsytool.h"
#include "bmousetool.h"
#include "bmpremousetool.h"
#include "bodymarkchangetool.h"
#include "bodymarkdeletelastonetool.h"
#include "bodymarkglyphscontrol.h"
#include "bodymarkmousetool.h"
#include "bodymarkpagechangetool.h"
#include "bodymarkselectgeneraltool.h"
#include "bodymarkselecttool.h"
#include "bodymarkviewmodel.h"
#include "bufferindexendpostool.h"
#include "bufferindexstartpostool.h"
//#include "ibufferstoremanager.h"
#include "callbackrtmeasurecinetool.h"
#include "callbackrtmeasurecontollertool.h"
#include "cgaingeneraltool.h"
#include "changemeasresultviewmovingstatetool.h"
#include "chisonultrasoundcontext.h"
#include "chromatool.h"
#include "cinelooper.h"
#include "cinenavigatetool.h"
#include "cineplaycommandcontext.h"
#include "cineplaymousetool.h"
#include "cineplayspeedadjusttool.h"
#include "cineplaytool.h"
#include "cleardeleteglyphstool.h"
#include "clearmeasurementdeletetool.h"
#include "commentchangetool.h"
#include "commentdeletelastonetool.h"
#include "commentfonttool.h"
#include "commentglyphscontrol.h"
#include "commenthometool.h"
#include "commentmenu.h"
#include "commentmenucontroltool.h"
#include "commentmenupagechangetool.h"
#include "commentmousetool.h"
#include "cursorcommand.h"
#include "cwdsampleratetool.h"
#include "debuggerviewtool.h"
#include "deleteglyphstool.h"
#include "dgaingeneraltool.h"
#include "dialogstateexitcommand.h"
#include "dmousetool.h"
#include "dopplerangletool.h"
#include "dopplerviewercontext.h"
#include "dtraceareatool.h"
#include "editexamendtool.h"
#include "editexamtipcommand.h"
#include "elastoexittool.h"
#include "elastographytool.h"
#include "enableblockdatarecordtool.h"
#include "endbufferindextool.h"
#include "endtool.h"
#include "exammodetool.h"
#include "exitcommand.h"
#include "fourdtransformtool.h"
#include "framecontrol.h"
#include "freemlinemousetool.h"
#include "freezeimagegeneralpagetool.h"
#include "freezeimagenextpagetool.h"
#include "freezeimageprevpagetool.h"
#include "freezeimageremovetool.h"
#include "freezeimagesendtool.h"
#include "freqgeneraltool.h"
#include "fullscreentool.h"
#include "gaingeneraltool.h"
#include "gdprinfoprovider.h"
#include "glyphscontroldeletetool.h"
#include "grabscreentool.h"
#include "grabscreenvideotool.h"
#include "hotkeytool.h"
#include "ibeamformer.h"
#include "imageskimmanager.h"
#include "imagetile.h"
#include "imagewidget.h"
#include "imagezoomintool.h"
#include "internettool.h"
#include "ismenuvisibletool.h"
#include "keyboardlighttool.h"
#include "lcdbrightnesstool.h"
#include "leftadjustmenttool.h"
#include "leftandbottommenuwidgetoperatetool.h"
#include "loadcommand.h"
#include "mainwindowmiddledownwidget.h"
#include "measureitemtool.h"
#include "measurementchangetool.h"
#include "measurementdeletelastonetool.h"
#include "measurementdeletetool.h"
#include "measuremenucontroltool.h"
#include "measuremousetool.h"
#include "measureresultfontsizetool.h"
#include "measureresultlistwidget.h"
#include "menuangletool.h"
#include "menubottomtool.h"
#include "menucontroller.h"
#include "menutoolcontext.h"
#include "menutools.h"
#include "mgaingeneraltool.h"
#include "mlinemousetool.h"
#include "moveresetmeasresultpostool.h"
#include "nulltool.h"
#include "openprobecodeburnertool.h"
#include "openvirtualprobecodetool.h"
#include "panzoommousetool.h"
#include "pcprinttool.h"
#include "posteventtool.h"
#include "postgaingeneraltool.h"
#include "postparametereventtool.h"
#include "presetmodel.h"
#include "qbeamtool.h"
#include "qflowtool.h"
#include "quadplexmodetool.h"
#include "quickangletool.h"
#include "quickcommentedittool.h"
#include "quickcommentindexchangetool.h"
#include "quickcommentinputtool.h"
#include "quickmeasuretool.h"
#include "realtimestorecinetool.h"
#include "resetbufferindextool.h"
#include "resetmeasresultpostool.h"
#include "roiglyphscontrol.h"
#include "roimousetool.h"
#include "rotateglyphstool.h"
#include "rotatemeasuretool.h"
#include "rtimttool.h"
#include "samplingvalvetool.h"
#include "savectableandblockdatatool.h"
#include "saveloadcommentpostool.h"
#include "screenorientationchangedtool.h"
#include "screenthumbnailexittool.h"
#include "shutdowntool.h"
#include "simulatetouchtool.h"
#include "sonoavtool.h"
#include "sonodiaphtool.h"
#include "sonoimttool.h"
#include "sononerveglyphscontrol.h"
#include "sononervetool.h"
#include "sonoparaswidget.h"
#include "sonothyroidtool.h"
#include "startbufferindextool.h"
#include "staticlabeltool.h"
#include "steeringangletool.h"
#include "storagetipcommand.h"
#include "storecinecommand.h"
#include "storecinetoudiskcommand.h"
#include "storeimagecommand.h"
#include "storeimagetoudiskcommand.h"
#include "stressechoanalyzetool.h"
#include "stressechoentool.h"
#include "stressechotemplatetool.h"
#include "stressechotimercontroltool.h"
#include "stressechowidgetmanager.h"
#include "stressechoworkwidget.h"
#include "systemscanmodetool.h"
#include "systemstatuswidget.h"
#include "thistatetool.h"
#include "thitool.h"
#include "togglemoveresetmeasresultpostool.h"
#include "togglesaveloadcommentpostool.h"
#include "worklisttool.h"
#include "toolnames.h"
#include "tools.h"
#include "toolsfactory.h"
#include "toolsordermanager.h"
#include "trapezoidalmodegeneraltool.h"
#include "udgeneraltool.h"
#include "videoprinttool.h"
#include "xcontrasttool.h"
#include "zoommousetool.h"
#include "zoomonclosetool.h"
#include "zoomonmousetool.h"
#include "zoomselectglyphscontrol.h"

#ifdef USE_PANORAMIC
#include "curvedpanoramicentool.h"
#endif

#include "sonocardiacexittool.h"
#include "sonocardiacsecimagetool.h"
#include "sonocardiacsecratingtool.h"
#include "sonocardiacsecrecognizetool.h"
#include "sonocardiacsectiontool.h"
#include "sonocardiactool.h"

#include "iperipheralmanager.h"
#include "iultrasoundview.h"
#include "iexammanager.h"
#include "imarkmanager.h"
#include "iimagemanager.h"
#include "measurecontext.h"
#ifdef USE_SONOCAROTIDGUIDE
#include "sonocarotidtool.h"
#endif
#include "sonomskglyphscontrol.h"
#include "sonomsktool.h"
#include "basemeasuredispather.h"
#include "packagesmeasurement.h"
#include "sonoaaatool.h"
#include "aitool.h"
#include "sonovftool.h"
#include "updatecommand.h"
#include "tgcmenuwidgetcontainer.h"
#include "tgcadjustcommand.h"
#include "gainmousetool.h"
#include "gainadjustcommand.h"
#include "autoblinetool.h"
#include "sonovtitool.h"

ToolsCreator::ToolsCreator(QObject* parent)
    : QObject(parent)
{
}

void ToolsCreator::createTools(ToolsOrderManager* toolsOrderManager, IPeripheralManager* peripheralManager,
                               IImageManager* imageManager, IExamManager* examManager, IMarkManager* markManager,
                               IUltrasoundView* ultrasoundView, IToolsFacade* toolsFacade,
                               IStateManager* m_StateManager)
{
    Q_ASSERT(toolsOrderManager != NULL);
    Q_ASSERT(peripheralManager != NULL);
    Q_ASSERT(imageManager != NULL);
    Q_ASSERT(examManager != NULL);
    Q_ASSERT(markManager != NULL);
    Q_ASSERT(ultrasoundView != NULL);
    DopplerViewerContext* viewContext = ultrasoundView->viewContext();
    Q_ASSERT(viewContext != NULL);

    ToolsFactory::instance().addCommand(ToolNames::MenuStr, new MenuExTool(ToolNames::MenuStr));
    ToolsFactory::instance().addCommand(ToolNames::Up1Str, new UpDownTool(ToolNames::Up1Str, 0, true));
    ToolsFactory::instance().addCommand(ToolNames::Down1Str, new UpDownTool(ToolNames::Down1Str, 0, false));
    ToolsFactory::instance().addCommand(ToolNames::Up2Str, new UpDownTool(ToolNames::Up2Str, 1, true));
    ToolsFactory::instance().addCommand(ToolNames::Down2Str, new UpDownTool(ToolNames::Down2Str, 1, false));
    ToolsFactory::instance().addCommand(ToolNames::Up3Str, new UpDownTool(ToolNames::Up3Str, 2, true));
    ToolsFactory::instance().addCommand(ToolNames::Down3Str, new UpDownTool(ToolNames::Down3Str, 2, false));
    ToolsFactory::instance().addCommand(ToolNames::Up4Str, new UpDownTool(ToolNames::Up4Str, 3, true));
    ToolsFactory::instance().addCommand(ToolNames::Down4Str, new UpDownTool(ToolNames::Down4Str, 3, false));
    ToolsFactory::instance().addCommand(ToolNames::Up5Str, new UpDownTool(ToolNames::Up5Str, 4, true));
    ToolsFactory::instance().addCommand(ToolNames::Down5Str, new UpDownTool(ToolNames::Down5Str, 4, false));
    ToolsFactory::instance().addCommand(ToolNames::Up6Str, new UpDownTool(ToolNames::Up6Str, 5, true));
    ToolsFactory::instance().addCommand(ToolNames::Down6Str, new UpDownTool(ToolNames::Down6Str, 5, false));

    ToolsFactory::instance().addCommand(ToolNames::Click1Str, new ClickTool(ToolNames::Click1Str, 0));
    ToolsFactory::instance().addCommand(ToolNames::Click2Str, new ClickTool(ToolNames::Click2Str, 1));
    ToolsFactory::instance().addCommand(ToolNames::Click3Str, new ClickTool(ToolNames::Click3Str, 2));
    ToolsFactory::instance().addCommand(ToolNames::Click4Str, new ClickTool(ToolNames::Click4Str, 3));
    ToolsFactory::instance().addCommand(ToolNames::Click5Str, new ClickTool(ToolNames::Click5Str, 4));
    ToolsFactory::instance().addCommand(ToolNames::Click6Str, new ClickTool(ToolNames::Click6Str, 5));

    ToolsFactory::instance().addCommand(ToolNames::UtilityStr, new UtilityTool(ToolNames::UtilityStr));
    ToolsFactory::instance().addCommand(ToolNames::AdvanceStr, new AdvTool(ToolNames::AdvanceStr));

    ToolsFactory::instance().addCommand(ToolNames::PostProcessStr, new PostProcessTool(ToolNames::PostProcessStr));
    ToolsFactory::instance().addCommand(ToolNames::ExitFromUtilityStr,
                                        new ExitFromUtilityTool(ToolNames::ExitFromUtilityStr));
    ToolsFactory::instance().addCommand(ToolNames::TwoDMapStr, new TwoDMapTool(ToolNames::TwoDMapStr));
    ToolsFactory::instance().addCommand(ToolNames::ChromeStr, new Chrome(ToolNames::ChromeStr));
    ToolsFactory::instance().addCommand(ToolNames::EditPostProcessStr,
                                        new EditPostProcess(ToolNames::EditPostProcessStr));
    ToolsFactory::instance().addCommand(ToolNames::ExitFromPostProcessStr,
                                        new ExitFromPostProcess(ToolNames::ExitFromPostProcessStr));
    ToolsFactory::instance().addCommand(ToolNames::ColorMapStr, new ColorMapTool(ToolNames::ColorMapStr));
    ToolsFactory::instance().addCommand(ToolNames::DemoImageStr, new DemoImageTool(ToolNames::DemoImageStr));
    ToolsFactory::instance().addCommand(ToolNames::SetupStr, new SetupTool(ToolNames::SetupStr));
    ToolsFactory::instance().addCommand(ToolNames::DepthFocusStr, new DepthFocusTool(ToolNames::DepthFocusStr));
    ToolsFactory::instance().addCommand(
        ToolNames::QuickAngleStr,
        new QuickAngleTool(ToolNames::QuickAngleStr, imageManager->beamFormer()->sonoParameters()));

    ToolsFactory::instance().addCommand(
        ToolNames::ShowLeftAndBottomMenuWidgetStr,
        new ShowLeftAndBottomMenuWidgetTool(viewContext->leftMenuWidget(), viewContext->bottomMenuWidget(),
                                            viewContext->stressEchoWorkWidget(), imageManager->sonoParameters(),
                                            viewContext->AuxiliaryFuncBtnWidgetContainer(),
                                            ToolNames::ShowLeftAndBottomMenuWidgetStr));
    ToolsFactory::instance().addCommand(
        ToolNames::HideLeftAndBottomMenuWidgetStr,
        new HideLeftAndBottomMenuWidgetTool(viewContext->leftMenuWidget(), viewContext->bottomMenuWidget(),
                                            viewContext->stressEchoWorkWidget(), imageManager->sonoParameters(),
                                            viewContext->AuxiliaryFuncBtnWidgetContainer(),
                                            ToolNames::HideLeftAndBottomMenuWidgetStr));
    {
        IsMenuVisibleTool* tool = new IsMenuVisibleTool(BFPNames::IsMenuVisibleStr);
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);

        ToolsFactory::instance().addCommand(ToolNames::DirectionMenuStr,
                                            new DirectionMenuTool(tool, ToolNames::DirectionMenuStr));
    }

    {
        // 2023-06-02 Modify by AlexWang 使用参数开关按钮的形式来控制局部放大状态功能的打开和关闭
        ZoomStateTool* zoomTool = new ZoomStateTool(BFPNames::ZoomSelectStr);
        zoomTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(zoomTool->toolName(), zoomTool);

        // 2023-03-20 Write by AlexWang
        ZoomAddTool* zoomAddTool = new ZoomAddTool(ToolNames::ZoomAddStr);
        zoomAddTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(zoomAddTool->toolName(), zoomAddTool);

        ZoomDecTool* zoomDecTool = new ZoomDecTool(ToolNames::ZoomDecStr);
        zoomDecTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(zoomDecTool->toolName(), zoomDecTool);
    }

    // add by Jin Yuqi
#ifdef USE_FREEHAND3D
    ToolsFactory::instance().addCommand(ToolNames::ImageZoomInStr,
                                        new ImageZoomInTool(zoomOnProxy, m_Context->freeHand3DProxy()));
#endif
    {
        SlideShowTool* tool =
            new SlideShowTool(ToolNames::SlideShowStr, imageManager->chisonUltrasoundContext()->cineLooper());
        tool->setRealTimeParameters(imageManager->sonoParameters());
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        PresetModeTool* tool = new PresetModeTool(ToolNames::PresetModeStr);
        tool->setParentWidget(ultrasoundView->mainWindow());
        tool->setRealTimeParameters(imageManager->sonoParameters());
        tool->setBeamFormerTool(imageManager->beamFormer());
        tool->setExamModeMenuModel(ultrasoundView->examModeMenuModel());
        tool->setIZeusTool(dynamic_cast<IZeusTool*>(imageManager->chisonUltrasoundContext()));
        tool->setDiskDevice(peripheralManager->diskDevice());
        tool->setProbeDataSet(imageManager->probeDataSet());
        tool->setSubSystemSubject(dynamic_cast<USFObject*>(examManager));
        tool->setImageInterface(imageManager->imageInterfaceForExam());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ElementTestTool* tool = new ElementTestTool(ToolNames::ElementTestStr, imageManager->chisonUltrasoundContext());
        tool->setParentWidget(ultrasoundView->mainWindow());
        tool->setRealTimeParameters(imageManager->sonoParameters());
        tool->setBeamFormerTool(imageManager->beamFormer());
        tool->setProbeDataSet(imageManager->probeDataSet());
        tool->setImageSaveHelper(imageManager->imageSaveHelper());
        tool->setEleDetectAlgorithom(imageManager->elementDetectAlgorithom());
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        tool->setImageManager(imageManager);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* tgcTool = new TgcTool(ToolNames::TgcStr);
        tgcTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::TgcStr, tgcTool);
    }

    {
        SonoDiaphTool* tool =
            new SonoDiaphTool(ToolNames::SonoDiaphStr, imageManager->sonoParameters(), ultrasoundView->curImageTile(),
                              markManager->measureContext(), markManager->packagesMeasurement());
        tool->setProbeDataSet(imageManager->probeDataSet());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        SonoThyroidTool* tool = new SonoThyroidTool(
            ToolNames::SonoThyroidStr, imageManager->sonoParameters(), ultrasoundView->curImageTile(),
            markManager->measureContext(), markManager->packagesMeasurement(), imageManager->chisonUltrasoundContext(),
            imageManager->cineLooper(), imageManager->bufferStoreManager());
        tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        SonoIMTTool* tool = new SonoIMTTool(ToolNames::SonoIMTStr, imageManager->sonoParameters(),
                                            ultrasoundView->curImageTile(), markManager->measureContext(),
                                            markManager->packagesMeasurement(), imageManager->bufferStoreManager());

        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        SonoNerveTool* tool =
            new SonoNerveTool(ToolNames::SonoNerveStr, imageManager->sonoParameters(), ultrasoundView->curImageTile(),
                              markManager->measureContext(), imageManager->chisonUltrasoundContext());
        tool->setProbeDataSet(imageManager->probeDataSet());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
        connect(&Setting::instance().defaults(), SIGNAL(languageChanged()),
                GlyphsControlManager::instance().getChildGlyphsControl<SonoNerveGlyphsControl>(
                    GlyphsCtl::SonoNerveGlyphsType),
                SLOT(languageChanged()));
        connect(examManager->presetModel(), &IPresetModel::examModeChanged, tool, &SonoNerveTool::onExamModeChanged);
    }

#ifdef USE_SONOAV
    {
        SonoAVTool* tool = new SonoAVTool(BFPNames::SonoAVStr, imageManager->chisonUltrasoundContext(),
                                          imageManager->bufferStoreManager(), imageManager->lineBufferManager(),
                                          markManager->measureContext());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }
#endif

    {
        SonoMSKTool* tool =
            new SonoMSKTool(BFPNames::SonoMSKStr, imageManager->sonoParameters(), ultrasoundView->curImageTile(),
                            markManager->measureContext(), imageManager->chisonUltrasoundContext());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
        connect(examManager->presetModel(), &IPresetModel::examModeChanged, tool, &SonoMSKTool::onExamModeChanged);
        connect(
            &Setting::instance().defaults(), SIGNAL(languageChanged()),
            GlyphsControlManager::instance().getChildGlyphsControl<SonoMSKGlyphsControl>(GlyphsCtl::SonoMSKGlyphsType),
            SLOT(languageChanged()));
    }

    {
        CallbackRTMeasureCineTool* tool = new CallbackRTMeasureCineTool(
            ToolNames::CallbackRTMeasureCineStr, imageManager->bufferStoreManager(), imageManager->lineBufferManager(),
            imageManager->sonoParameters(), markManager->measureContext());
        tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        NullTool* tool = new NullTool(ToolNames::NULLStr);
        ToolsFactory::instance().addCommand(ToolNames::NULLStr, tool);
    }

    {
        OpenElementCheckTool* tool =
            new OpenElementCheckTool(BFPNames::OpenElementCheckStr, imageManager->sonoParameters(), imageManager);
        ToolsFactory::instance().addCommand(BFPNames::OpenElementCheckStr, tool);
    }

    {
        ITool* lrTool = new LRTool(ToolNames::LRStr);
        lrTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::LRStr, lrTool);
    }
    {
        ITool* udTool = new UDTool(ToolNames::UDStr);
        udTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::UDStr, udTool);
    }
    {
        ITool* udTool = new RotationTool(ToolNames::RotationStr);
        udTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::RotationStr, udTool);
    }
    {
        ITool* udTool = new SpectralInvertTool(BFPNames::SpectralInvertStr);
        udTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(BFPNames::SpectralInvertStr, udTool);
    }
    {
        ITool* udTool = new ColorInvertStateTool(BFPNames::ColorInvertStateStr);
        udTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(BFPNames::ColorInvertStateStr, udTool);
    }
    {
        ITool* udTool = new DPDInvertStateTool(BFPNames::DPDInvertStateStr);
        udTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(BFPNames::DPDInvertStateStr, udTool);
    }
    {
        ITool* udTool = new TDIInvertStateTool(BFPNames::TDIInvertStateStr);
        udTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(BFPNames::TDIInvertStateStr, udTool);
    }
    {
        ITool* udTool = new MVIInvertStateTool(BFPNames::MVIInvertStateStr);
        udTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(BFPNames::MVIInvertStateStr, udTool);
    }
    {
        ITool* cqyzTool = new CQYZTool(ToolNames::CQYZStr);
        cqyzTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(BFPNames::CQYZStr, cqyzTool);
    }
    {
        ITool* tool = new CQYZLevelTool(BFPNames::CQYZLevelStr);
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(BFPNames::CQYZLevelStr, tool);
    }
    {
        ITool* tool = new DepthTool(ToolNames::DepthStr);
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::DepthStr, tool);
    }
    {
        ITool* focusTool = new FocusTool(ToolNames::FocusStr);
        focusTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::FocusStr, focusTool);
    }

    {
        AIOTool* tool = new AIOTool(ToolNames::AIOStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        THIStateTool* tool = new THIStateTool(BFPNames::THIStateStr, imageManager->sonoParameters());
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(BFPNames::THIStateStr, tool);
    }

    {
        ThiTool* tool = new ThiTool(ToolNames::ThiStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    //    {
    //        VideoPrintTool* tool = new VideoPrintTool(ToolNames::VideoPrintStr);
    //        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    //    }

    {
        // Xu
        VideoPrintTool* tool = new VideoPrintTool(
            ToolNames::UsbImageAreaVideoPrintStr, ultrasoundView->mainWindow(), viewContext->sonoZoomWidget(),
            viewContext->zoomOnProxy(), imageManager->chisonUltrasoundContext(), viewContext->mainWindowTopWidget(),
            viewContext->sonoParasWidget(), true);

        tool->setStateManager(ultrasoundView->stateManagerFacade());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        // Xu
        VideoPrintTool* tool = new VideoPrintTool(
            ToolNames::UsbOtherAreaVideoPrintStr, ultrasoundView->mainWindow(), viewContext->sonoZoomWidget(),
            viewContext->zoomOnProxy(), imageManager->chisonUltrasoundContext(), viewContext->mainWindowTopWidget(),
            viewContext->sonoParasWidget(), false);

        tool->setStateManager(ultrasoundView->stateManagerFacade());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        // add by zta 2018.03.29 imageAre = false
        // 时，打印的是全屏图像，解决了USB视频打印机打印Archive页面Review页面不全的Bug
        VideoPrintReportTool* tool = new VideoPrintReportTool(
            ToolNames::UsbReportAreaVideoPrintStr, ultrasoundView->mainWindow(), viewContext->sonoZoomWidget(),
            viewContext->zoomOnProxy(), imageManager->chisonUltrasoundContext(), viewContext->mainWindowTopWidget(),
            viewContext->sonoParasWidget(), false);
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    //    {
    //        PCPrintTool* tool = new PCPrintTool(ToolNames::PCPrintStr);
    //        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    //    }

    {
        PCPrintFromScreenTool* tool = new PCPrintFromScreenTool(
            ToolNames::PCPrintFromScreenStr, ultrasoundView->mainWindow(), viewContext->sonoZoomWidget(),
            imageManager->beamFormer(), imageManager->chisonUltrasoundContext(), examManager->patientWorkflow());

        tool->setGDPRInfoProvider(dynamic_cast<GDPRInfoProvider*>(viewContext->mainWindowTopWidget()));
        ISonoParasWidget* sonoparaswidget = dynamic_cast<ISonoParasWidget*>(viewContext->sonoParasWidget());
        Q_ASSERT(sonoparaswidget != NULL);
        tool->setSonoParasWidget(sonoparaswidget);
        tool->setZoomOnProxy(viewContext->zoomOnProxy());
        tool->setColorMapManager(imageManager->colorMapManager());
        tool->setReportSectionManager(examManager->reportSectionManager());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        PCPrintFromWindowTool* tool = new PCPrintFromWindowTool(
            ToolNames::PCPrintFromWindowStr, ultrasoundView->mainWindow(), viewContext->sonoZoomWidget(),
            imageManager->beamFormer(), imageManager->chisonUltrasoundContext());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* tool = new SystemScanModeTool(ToolNames::BModeStr, StateEventNames::SingleB, Layout_1x1);
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* tool = new SystemScanModeTool(ToolNames::TwoBModeStr, StateEventNames::TwoB, Layout_1x2);
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* tool = new SystemScanModeTool(ToolNames::FourBModeStr, StateEventNames::FourB, Layout_2x2);
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* tool = new PostEventTool(ToolNames::DModeStr, StateEventNames::D);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* tool = new PostEventTool(ToolNames::PDModeStr, StateEventNames::PD);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* tool = new PostEventTool(ToolNames::DPDModeStr, StateEventNames::DPD);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }
    // add by liujia
    {
        ITool* tool = new PostEventTool(ToolNames::FreeMModeStr, StateEventNames::FreeM);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
        // 根据自由m的功能的开启来确认自由m菜单是否可用
        Parameter* p = imageManager->sonoParameters()->parameter(BFPNames::FreeMModeStr);
        connect(p, SIGNAL(enabledChanged(bool)), tool, SLOT(setIsAvailable(bool)));
        tool->setIsAvailable(false);
    }
    {
        ITool* tool =
            new PostMultiEventTool(ToolNames::ExitFreeMStr, QStringList() << StateEventNames::B << StateEventNames::M);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }
    {
        ITool* tool = new FreeMAngleTool(BFPNames::FreeMAngle1Str);
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }
    {
        ITool* tool = new FreeMAngleTool(ToolNames::FreeMQuickAngleStr, BFPNames::FreeMAngle1Str, 5);
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }
    {
        ITool* tool =
            new PostParameterEventTool(BFPNames::CWEnStr, StateEventNames::CW, imageManager->sonoParameters());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* tool =
            new PostParameterEventTool(BFPNames::TDIEnStr, StateEventNames::TDI, imageManager->sonoParameters());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* tool = new ArrowPostEventTool(ToolNames::ArrowStr, StateEventNames::Arrow);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ITool* changeTool = new ChangeTool(ToolNames::ChangeStr);
        changeTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::ChangeStr, changeTool);
    }
    {
        ITool* changeTool = new PostChangeTool(ToolNames::PostChangeStr);
        changeTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::PostChangeStr, changeTool);
    }
    {
        ITool* tool = new ECGEnTool(ToolNames::ECGEnStr, imageManager->lineBufferManager());
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::ECGEnStr, tool);
    }

    // add xule
    {
        ITool* bottomMenuChangeTool = new BottomMenuChangeTool(ToolNames::BottomMenuChangeStr);
        ToolsFactory::instance().addCommand(ToolNames::BottomMenuChangeStr, bottomMenuChangeTool);
    }

    {
        ExamModeChangeTool* tool =
            new ExamModeChangeTool(ultrasoundView->examModeMenuModel(), ToolNames::ExamModeChangeStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ExamModeRenameTool* tool =
            new ExamModeRenameTool(ultrasoundView->examModeMenuModel(), ToolNames::ExamModeRenameStr);
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        tool->setIsClickedTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ExamModeLoadTool* tool = new ExamModeLoadTool(ultrasoundView->examModeMenuModel(), ToolNames::ExamModeLoadStr);
        tool->setIsClickedTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ExamModeSaveTool* tool = new ExamModeSaveTool(ultrasoundView->examModeMenuModel(), ToolNames::ExamModeSaveStr);
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        tool->setIsClickedTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ExamModeSaveAsTool* tool =
            new ExamModeSaveAsTool(ultrasoundView->examModeMenuModel(), ToolNames::ExamModeSaveAsStr);
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        tool->setIsClickedTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        PresetLoadTool* tool = new PresetLoadTool(ultrasoundView->examModeMenuModel(), ToolNames::PresetLoadStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        // 8 bodymark images
        for (int i = 0; i < 8; i++)
        {
            BodyMarkSelectTool* tool = new BodyMarkSelectTool(ToolNames::BodyMarkSelectorStr + QString::number(i + 1));
            tool->setIndex(i);
            tool->setIsClickedTool(true);
            tool->setBodyMarkSkim(viewContext->bodyMarkWidget());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
    }

    {
        BodyMarkPageChangeTool* tool = new BodyMarkPageChangeTool(ToolNames::BodyMarkPageChangerStr);
        tool->setBodyMarkSkim(viewContext->bodyMarkWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        BodyMarkChangeTool* tool = new BodyMarkChangeTool(ToolNames::BodyMarkChangeStr);
        tool->setModel(markManager->bodyMarkViewModel());
        tool->setView(viewContext->bodyMarkWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    // initializeGlyphsControlTools  --- start
    {
        GlyphsControlDeleteTool* tool =
            new GlyphsControlDeleteTool(ToolNames::CommentDeleteStr, GlyphsCtl::CommentGlyphsType);

        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        GlyphsControlDeleteTool* tool =
            new GlyphsControlDeleteTool(ToolNames::BodyMarkDeleteStr, GlyphsCtl::BodyMarkGlyphsType);

        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        GlyphsControlDeleteTool* tool =
            new GlyphsControlDeleteTool(ToolNames::ArrowDeleteStr, GlyphsCtl::ArrowGlyphsType);

        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        DeleteGlyphsTool* tool =
            new DeleteGlyphsTool(ToolNames::DeleteGlyphsStr, markManager->screenMeasResults(),
                                 ultrasoundView->imageTileOverlay(), markManager->screenMeasureController());
        tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
        tool->initialize(new MenuToolContext(imageManager->bufferStoreManager()));
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ArrowDeleteLastOneTool* tool =
            new ArrowDeleteLastOneTool(ToolNames::ArrowDeleteLastOneStr, ultrasoundView->imageTileOverlay());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        CommentDeleteLastOneTool* tool =
            new CommentDeleteLastOneTool(ToolNames::CommentDeleteLastOneStr, ultrasoundView->imageTileOverlay());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        BodyMarkDeleteLastOneTool* tool =
            new BodyMarkDeleteLastOneTool(ToolNames::BodyMarkDeleteLastOneStr, ultrasoundView->imageTileOverlay());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    // initializeGlyphsControlTools  --- end

    {
        MeasurementDeleteLastOneTool* tool =
            new MeasurementDeleteLastOneTool(ToolNames::MeasurementDeleteLastOneStr, markManager->screenMeasResults());
        tool->setScreenMeasureController(markManager->screenMeasureController());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ShutDownTool* tool =
            new ShutDownTool(peripheralManager->systemInfo(), ultrasoundView->mainWindow(), ToolNames::ShutDownStr);
        tool->setMachine(peripheralManager->machine());
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        tool->setDicomTaskManager(examManager->dicomTaskManager());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ArchiveEnterTool* tool =
            new ArchiveEnterTool(ToolNames::ArchiveEnterStr, ultrasoundView->patientWorkflowModel(),
                                 examManager->patientWorkflow()->patient());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    CursorCommand* cursorCommand = new CursorCommand();
    cursorCommand->initialize(new CursorCommandContext(ultrasoundView->mainWindow(), QPoint(250, 650)));
    ToolsFactory::instance().addCommand(cursorCommand->toolName(), cursorCommand);

    ExitCommand* exitCommand = new ExitCommand();
    ToolsFactory::instance().addCommand(exitCommand->toolName(), exitCommand);

    {
        DialogStateExitCommand* c = new DialogStateExitCommand(ToolNames::ArchiveExitStr, "ArchiveManagerDialog");
        ToolsFactory::instance().addCommand(c->toolName(), c);
    }

    {
        DialogStateExitCommand* c = new DialogStateExitCommand(ToolNames::BrowseExitStr, "GlanceImagesDialog");
        ToolsFactory::instance().addCommand(c->toolName(), c);
    }

    {
        // DialogStateExitCommand* c = new DialogStateExitCommand(ToolNames::SetupExitStr, "SettingsDialog");
        DialogStateExitCommand* c = new DialogStateExitCommand(ToolNames::SetupExitStr, "ConfigurationDialog");
        ToolsFactory::instance().addCommand(c->toolName(), c);
    }

    {
        DialogStateExitCommand* c = new DialogStateExitCommand(ToolNames::ProbeExitStr, "ProbeOrganismSelectionWidget");
        ToolsFactory::instance().addCommand(c->toolName(), c);
    }

    {
        DialogStateExitCommand* c = new DialogStateExitCommand(ToolNames::PatientExitStr, "PatientEditDialog");
        ToolsFactory::instance().addCommand(c->toolName(), c);
    }

    {
        DialogStateExitCommand* c = new DialogStateExitCommand(ToolNames::ReportExitStr, "ReportDialog");
        ToolsFactory::instance().addCommand(c->toolName(), c);
    }

    {
        EndTool* e = new EndTool(ToolNames::EndStr);
        e->setPatientWorkflow(examManager->patientWorkflow());
        e->setBeamFormer(imageManager->beamFormer());
        ToolsFactory::instance().addCommand(e->toolName(), e);
    }

    {
        ClearMeasurementDeleteTool* cm = new ClearMeasurementDeleteTool(ToolNames::ClearMeasurementDeleteStr);
        cm->setPatientWorkflow(examManager->patientWorkflow());
        cm->initialize(new MenuToolContext(imageManager->bufferStoreManager()));
        ToolsFactory::instance().addCommand(cm->toolName(), cm);
    }

    {
        ClearDeleteGlyphsTool* cd = new ClearDeleteGlyphsTool(ToolNames::ClearDeleteGlyphsStr);
        cd->setPatientWorkflow(examManager->patientWorkflow());
        ToolsFactory::instance().addCommand(cd->toolName(), cd);
    }

    {
        CinePlayTool* tool = new CinePlayTool(
            ToolNames::CinePlayStr, imageManager->chisonUltrasoundContext()->cineLooper(),
            dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()), markManager->measureContext());
        tool->setIsClickedTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        CineNavigateTool* tool =
            new CineNavigateTool(ToolNames::CineNavigateStr, imageManager->cineLooper(), markManager->measureContext());
        tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
        tool->setCineLooper(imageManager->chisonUltrasoundContext()->cineLooper());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        AutoEFFrameTool* tool = new AutoEFFrameTool(ToolNames::ESFrameStr, imageManager->cineLooper(),
                                                    markManager->measureContext(), imageManager->lineBufferManager());
        tool->setCineLooper(imageManager->chisonUltrasoundContext()->cineLooper());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        AutoEFFrameTool* tool = new AutoEFFrameTool(ToolNames::EDFrameStr, imageManager->cineLooper(),
                                                    markManager->measureContext(), imageManager->lineBufferManager());
        tool->setCineLooper(imageManager->chisonUltrasoundContext()->cineLooper());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        CinePlaySpeedAdjustTool* tool =
            new CinePlaySpeedAdjustTool(ToolNames::CinePlaySpeedAdjustStr, imageManager->cineLooper());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        FreezeImageRemoveTool* tool =
            new FreezeImageRemoveTool(ToolNames::FreezeImageRemoveStr, viewContext->imageClipWidget());
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        FreezeImageRemoveTool* tool =
            new FreezeImageRemoveTool(ToolNames::FreezeImageRemoveStr, viewContext->imageClipWidget());
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        FreezeImageSendTool* tool =
            new FreezeImageSendTool(ToolNames::FreezeImageSendStr, viewContext->imageClipWidget());
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        tool->setColorMapManager(imageManager->colorMapManager());
        tool->setReportSectionManager(examManager->reportSectionManager());
        tool->setSystemInfo(peripheralManager->systemInfo());
        tool->setDicomTaskManager(examManager->dicomTaskManager());
        tool->setDicomUtilityCreator(examManager->dicomUtilityCreator());
        tool->setDicomToolCreator(examManager->dicomToolCreator());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        FreezeImagePrevPageTool* tool =
            new FreezeImagePrevPageTool(ToolNames::FreezeImagePrevPageStr, viewContext->imageClipWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        FreezeImageNextPageTool* tool =
            new FreezeImageNextPageTool(ToolNames::FreezeImageNextPageStr, viewContext->imageClipWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        FreezeImageGeneralPageTool* tool =
            new FreezeImageGeneralPageTool(ToolNames::FreezeImageGeneralPageStr, viewContext->imageClipWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ResetBufferIndexTool* tool =
            new ResetBufferIndexTool(ToolNames::ResetBufferIndexStr, imageManager->lineBufferManager());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        StartBufferIndexTool* tool =
            new StartBufferIndexTool(ToolNames::StartBufferIndexStr, imageManager->lineBufferManager());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        BufferIndexStartPosTool* toolStart =
            new BufferIndexStartPosTool(ToolNames::BufferIndexStartPosStr, imageManager->lineBufferManager());
        toolStart->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(toolStart->toolName(), toolStart);

        BufferIndexEndPosTool* toolEnd =
            new BufferIndexEndPosTool(ToolNames::BufferIndexEndPosStr, imageManager->lineBufferManager());
        toolEnd->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(toolEnd->toolName(), toolEnd);
    }

    {
        EndBufferIndexTool* tool =
            new EndBufferIndexTool(ToolNames::EndBufferIndexStr, imageManager->lineBufferManager());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        BodyMarkSelectGeneralTool* tool = new BodyMarkSelectGeneralTool(ToolNames::BodyMarkSelectGeneralStr);
        tool->setImageSkimManager(viewContext->bodyMarkWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        BodyMarkSelectPrevTool* tool = new BodyMarkSelectPrevTool(ToolNames::BodyMarkSelectPrevStr);
        tool->setImageSkimManager(viewContext->bodyMarkWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        BodyMarkSelectNextTool* tool = new BodyMarkSelectNextTool(ToolNames::BodyMarkSelectNextStr);
        tool->setImageSkimManager(viewContext->bodyMarkWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        MoveResetMeasResultPosTool* srcTool = new MoveResetMeasResultPosTool(ToolNames::MoveResetMeasResultPosStr);
        ToolsFactory::instance().addCommand(srcTool->toolName(), srcTool);

        ToggleMoveResetMeasResultPosTool* tool =
            new ToggleMoveResetMeasResultPosTool(ToolNames::ToggleMoveResetMeasResultPosStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);

        connect(tool, SIGNAL(changeTool()), srcTool, SLOT(onChangeTool()));
    }

    {
        SaveLoadCommentPosTool* srcTool = new SaveLoadCommentPosTool(ToolNames::SaveLoadCommentPosStr);
        ToolsFactory::instance().addCommand(srcTool->toolName(), srcTool);

        ToggleSaveLoadCommentPosTool* tool = new ToggleSaveLoadCommentPosTool(ToolNames::ToggleSaveLoadCommentPosStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);

        connect(tool, SIGNAL(changeTool()), srcTool, SLOT(onChangeTool()));
    }

    {
        StaticLabelTool* tool = new StaticLabelTool(ToolNames::StaticLabelDistanceStr, ToolNames::DistanceMeasureStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        StaticLabelTool* tool = new StaticLabelTool(ToolNames::StaticLabelAreaStr, ToolNames::TraceMeasureStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        StaticLabelTool* tool = new StaticLabelTool(ToolNames::StaticLabelVolumeStr, ToolNames::VolumeMeasureStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        MeasurementChangeTool* c = new MeasurementChangeTool(ToolNames::MeasurementChangeStr);
        c->setMenuModel(markManager->packagesMeasurement());
        ToolsFactory::instance().addCommand(c->toolName(), c);
    }

    {
        CommentMenuControlTool* tool = new CommentMenuControlTool(ToolNames::CommentMenuControlStr);
        IMenuKeyOperator* menuKeyOperator = dynamic_cast<IMenuKeyOperator*>(viewContext->commentMenu());
        Q_ASSERT(menuKeyOperator != NULL);
        tool->setController(menuKeyOperator);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        CommentMenuPageChangeTool* tool = new CommentMenuPageChangeTool(ToolNames::CommentMenuPageChangeStr);
        tool->setCommentMenu(viewContext->commentMenu());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        CommentChangeTool* tool = new CommentChangeTool(ToolNames::CommentChangeStr);
        tool->setModel(markManager->commentMenuModel());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        CommentFontLoadDefaultTool* ldTool = new CommentFontLoadDefaultTool(ToolNames::CommentFontLoadDefaultStr);
        ToolsFactory::instance().addCommand(ldTool->toolName(), ldTool);

        CommentFontSizeChangeTool* scTool =
            new CommentFontSizeChangeTool(ToolNames::CommentFontSizeChangeStr, imageManager->lineBufferManager());
        ToolsFactory::instance().addCommand(scTool->toolName(), scTool);

        connect(ldTool, SIGNAL(defaultLoaded()), scTool, SLOT(onDefaultLoaded()));
    }

    {
        // Xu
        MeasureResultFontLoadDefalutSizeTool* dtool = new MeasureResultFontLoadDefalutSizeTool(
            viewContext->measureResultListWidget(), ToolNames::MeasureResultFontLoadDefaultSizeStr);
        ToolsFactory::instance().addCommand(dtool->toolName(), dtool);

        MeasureResultFontSizeTool* tool =
            new MeasureResultFontSizeTool(viewContext->measureResultListWidget(), ToolNames::MeasureResultFontSizeStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);

        connect(dtool, SIGNAL(fontSizeChangedtoDefault()), tool, SLOT(onFontSizeChangedtoDefault()));
    }

    {
        CommentHomeSaveTool* tool = new CommentHomeSaveTool(ToolNames::CommentHomeSaveStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        CommentHomeLoadTool* tool = new CommentHomeLoadTool(ToolNames::CommentHomeLoadStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        QuickCommentEditTool* tool = new QuickCommentEditTool(ToolNames::QuickCommentEditStr);
        tool->setStateManager(ultrasoundView->stateManagerFacade());
        tool->setQuickCommentModel(markManager->quickCommentModel());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        QuickCommentIndexChangeTool* tool = new QuickCommentIndexChangeTool(ToolNames::QuickCommentIndexChangeStr);
        tool->setQuickCommentModel(markManager->quickCommentModel());
        tool->setQuickCommentPopupWidget(viewContext->quickCommentPopupWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        QuickCommentInputTool* tool = new QuickCommentInputTool(ToolNames::QuickCommentInputStr);
        tool->setQuickCommentModel(markManager->quickCommentModel());
        tool->setQuickCommentPopupWidget(viewContext->quickCommentPopupWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ChangeMeasResultViewMovingStateTool* tool = new ChangeMeasResultViewMovingStateTool(
            viewContext->measureResultListWidget(), ToolNames::ChangeMeasResultViewMovingStateStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ResetMeasResultPosTool* tool =
            new ResetMeasResultPosTool(ToolNames::ResetMeasResultPosStr, viewContext->measureResultListWidget());
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }
    {
        LGCTool* tool = new LGCTool(BFPNames::LGCControlEnStr, viewContext->lgcRegulatorDialog());
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    //    {
    //        QVector<QPair<QString, QString> > variableMenuToolList = QVector<QPair<QString, QString> >()
    //                << QPair<QString, QString>(BFPNames::DynamicRangeStr, BFPNames::PostBDynamicRangeStr)
    //                << QPair<QString, QString>(BFPNames::MDynamicRangeStr, BFPNames::PostMDynamicRangeStr)
    //                << QPair<QString, QString>(BFPNames::PWDynamicRangeShowStr, BFPNames::PostPWDynamicRangeStr)
    //                << QPair<QString, QString>(BFPNames::PWDynamicRangeCWDStr, BFPNames::PostCWDynamicRangeStr);

    //        for(int i = 0; i < variableMenuToolList.size(); ++i)
    //        {
    //            VariableMenuTool* tool = new VariableMenuTool(variableMenuToolList[i].first,
    //                                                          variableMenuToolList[i].second);
    //            tool->setIsParameterTool(true);
    //            ToolsFactory::instance().addCommand(tool->toolName(), tool);
    //        }
    //    }

    {
        ImageZoomCoefTool* tool =
            new ImageZoomCoefTool(viewContext->panZoomThumbnail(), toolsOrderManager, BFPNames::ImageZoomCoefStr);
        tool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
    }

    {
        ZoomOnCloseTool* zoomOnCloseTool = new ZoomOnCloseTool(viewContext->zoomThumbnail(), ToolNames::ZoomOnCloseStr);
        zoomOnCloseTool->setIsParameterTool(true);
        ToolsFactory::instance().addCommand(ToolNames::ZoomOnCloseStr, zoomOnCloseTool);
    }

    {
        ISystemInfo* systemInfo = peripheralManager->systemInfo();

        ToolsFactory::instance().addCommand(ToolNames::StorageTipStr,
                                            new StorageTipCommand(ultrasoundView->mainWindow()));
        StoreImageCommand* storeImage = new StoreImageCommand(
            ToolNames::StoreImageStr, viewContext->progressSliderWidget(), ultrasoundView->stateManagerFacade(),
            imageManager->sonoParameters(), ultrasoundView->mainWindow(), viewContext->imageClipWidget(),
            imageManager->bufferStoreManager(), examManager->patientWorkflow(), examManager->dicomTaskManager(),
            ultrasoundView->stressEchoWidgetManager()->stressEchoModel(), viewContext->storeProgressMovieWidget(),
            ultrasoundView->patientWorkflowModel()->patientEditModel(), examManager->systemStatusModel(),
            viewContext->sonoZoomWidget(), viewContext->zoomOnProxy(), imageManager->beamFormer(),
            imageManager->cineLooper(), imageManager->colorMapManager(), systemInfo, ultrasoundView->curImageTile(),
            examManager->dicomUtilityCreator(), examManager->dicomToolCreator(),
            imageManager->chisonUltrasoundContext(), markManager->measureContext());
        ToolsFactory::instance().addCommand(ToolNames::StoreImageStr, storeImage);

        StoreImageToUDiskCommand* storeImageToUDisk = new StoreImageToUDiskCommand(
            ToolNames::StoreImageToUDiskStr, viewContext->progressSliderWidget(), ultrasoundView->stateManagerFacade(),
            imageManager->sonoParameters(), ultrasoundView->mainWindow(), viewContext->imageClipWidget(),
            imageManager->bufferStoreManager(), examManager->patientWorkflow(), examManager->dicomTaskManager(),
            ultrasoundView->stressEchoWidgetManager()->stressEchoModel(), viewContext->storeProgressMovieWidget(),
            ultrasoundView->patientWorkflowModel()->patientEditModel(), examManager->systemStatusModel(),
            viewContext->sonoZoomWidget(), viewContext->zoomOnProxy(), imageManager->beamFormer(),
            imageManager->cineLooper(), imageManager->colorMapManager(), systemInfo, ultrasoundView->curImageTile(),
            examManager->dicomUtilityCreator(), examManager->dicomToolCreator(),
            imageManager->chisonUltrasoundContext(), markManager->measureContext());
        ToolsFactory::instance().addCommand(ToolNames::StoreImageToUDiskStr, storeImageToUDisk);
#ifdef USE_4D
        ToolsFactory::instance().addCommand(ToolNames::StoreFourDImageStr,
                                            new StoreFourDImageCommand(ToolNames::StoreFourDImageStr, m_Context));
        ToolsFactory::instance().addCommand(ToolNames::StoreFourDCineStr,
                                            new StoreFourDCineCommand(ToolNames::StoreFourDCineStr, m_Context));
        ToolsFactory::instance().addCommand(ToolNames::FourDLoadStr,
                                            new FourDLoadCommand(ToolNames::FourDLoadStr, m_Context));
#endif
        StoreCineCommand* storeCine = new StoreCineCommand(
            ToolNames::StoreCineStr, viewContext->progressSliderWidget(), ultrasoundView->stateManagerFacade(),
            imageManager->sonoParameters(), ultrasoundView->mainWindow(), viewContext->imageClipWidget(),
            imageManager->bufferStoreManager(), examManager->patientWorkflow(), examManager->dicomTaskManager(),
            ultrasoundView->stressEchoWidgetManager()->stressEchoModel(), viewContext->storeProgressMovieWidget(),
            ultrasoundView->patientWorkflowModel()->patientEditModel(), examManager->systemStatusModel(),
            viewContext->sonoZoomWidget(), viewContext->zoomOnProxy(), imageManager->beamFormer(),
            imageManager->cineLooper(), imageManager->colorMapManager(), systemInfo, ultrasoundView->curImageTile(),
            examManager->dicomUtilityCreator(), examManager->dicomToolCreator(),
            imageManager->chisonUltrasoundContext(), markManager->measureContext());

        ToolsFactory::instance().addCommand(ToolNames::StoreCineStr, storeCine);
        ToolsFactory::instance().addCommand(
            ToolNames::StoreCineToUDiskStr,
            new StoreCineToUDiskCommand(
                ToolNames::StoreCineToUDiskStr, viewContext->progressSliderWidget(),
                ultrasoundView->stateManagerFacade(), imageManager->sonoParameters(), ultrasoundView->mainWindow(),
                viewContext->imageClipWidget(), imageManager->bufferStoreManager(), examManager->patientWorkflow(),
                examManager->dicomTaskManager(), ultrasoundView->stressEchoWidgetManager()->stressEchoModel(),
                viewContext->storeProgressMovieWidget(), ultrasoundView->patientWorkflowModel()->patientEditModel(),
                examManager->systemStatusModel(), viewContext->sonoZoomWidget(), viewContext->zoomOnProxy(),
                imageManager->beamFormer(), imageManager->cineLooper(), imageManager->colorMapManager(), systemInfo,
                ultrasoundView->curImageTile(), examManager->dicomUtilityCreator(), examManager->dicomToolCreator(),
                imageManager->chisonUltrasoundContext(), markManager->measureContext()));

        ToolsFactory::instance().addCommand(
            ToolNames::LoadStr, new LoadCommand(ToolNames::LoadStr, viewContext->progressSliderWidget(),
                                                ultrasoundView->stateManagerFacade(), imageManager->sonoParameters(),
                                                ultrasoundView->mainWindow(), viewContext->imageClipWidget(),
                                                imageManager->bufferStoreManager(),
                                                imageManager->chisonUltrasoundContext(), markManager->measureContext(),
                                                ultrasoundView->curImageTile(), viewContext->thumbnailDialog()));

        RealTimeStoreCineTool* realTimeStoreCine = new RealTimeStoreCineTool(
            ToolNames::RealTimeStoreCineStr, imageManager->lineBufferManager(), imageManager->cineLooper(),
            imageManager->beamFormer(), ultrasoundView->curImageTile());
        connect(storeImage, SIGNAL(beforeSave()), realTimeStoreCine, SLOT(onBeforeSave()), Qt::DirectConnection);
        connect(storeCine, SIGNAL(exitSave()), realTimeStoreCine, SLOT(onExitSave()), Qt::DirectConnection);
        connect(storeImageToUDisk, SIGNAL(beforeSave()), realTimeStoreCine, SLOT(onBeforeSave()), Qt::DirectConnection);
        ToolsFactory::instance().addCommand(ToolNames::RealTimeStoreCineStr, realTimeStoreCine);

        RTIMTTool* rtimtTool =
            new RTIMTTool(BFPNames::RTIMTStr, imageManager->bufferStoreManager(), imageManager->lineBufferManager(),
                          imageManager->chisonUltrasoundContext(), imageManager->sonoParameters(),
                          markManager->packagesMeasurement(), markManager->measureContext());
        ToolsFactory::instance().addCommand(rtimtTool->toolName(), rtimtTool);
        connect(storeImage, SIGNAL(beforeSave()), rtimtTool, SLOT(onBeforeSave()));
        connect(storeCine, SIGNAL(beforeSave()), rtimtTool, SLOT(onBeforeSave()));
        connect(storeImageToUDisk, SIGNAL(beforeSave()), rtimtTool, SLOT(onBeforeSave()));

        SonoVFTool* tool = new SonoVFTool(
            ToolNames::SonoVFStr, imageManager->cineLooper(), markManager->measureDispather(),
            ultrasoundView->curImageTile(), markManager->measureContext(), markManager->packagesMeasurement(),
            imageManager->chisonUltrasoundContext(), m_StateManager, imageManager->sonoParameters());
        ToolsFactory::instance().addCommand(ToolNames::SonoVFStr, tool);
    }

    ToolsFactory::instance().addCommand(ToolNames::LCDBrightnessStr,
                                        new LCDBrightnessTool(ToolNames::LCDBrightnessStr));
    ToolsFactory::instance().addCommand(ToolNames::DTraceAreaStr, new DTraceAreaTool(ToolNames::DTraceAreaStr));

    {
        BaseMouseCommandContext* c = new BaseMouseCommandContext();
        c->setCursorMouseActionsModel(dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()));
        {
            //            BaseMouseActionsTool* bmouseTool = new BaseMouseActionsTool();
            //            bmouseTool->initialize(c);

            BMouseTool* tool =
                new BMouseTool(imageManager->sonoParameters(), imageManager->controllerOfTGCAdjustment());
            tool->setProbeDataSet(imageManager->probeDataSet());
            tool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::BMouseStr, tool);
            //            ToolsFactory::instance().addCommand(ToolNames::BMouseStr, bmouseTool);
        }

        {
            RoiMouseTool* roiTool =
                new RoiMouseTool(imageManager->beamFormer()->rOIController(), imageManager->sonoParameters());
            roiTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::RoiStr, roiTool);
            connect(roiTool, SIGNAL(signalROIActionType(const QVariant&)), &FrameControl::instance(),
                    SIGNAL(signalROIActionType(const QVariant&)));
        }
#ifdef USE_4D
        {
            FourdPreMouseTool* roiTool = new FourdPreMouseTool(m_Context->beamFormer()->fourdROIController());
            roiTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::FourdRoiStr, roiTool);
        }
#endif

        {
            MLineMouseTool* mLineTool = new MLineMouseTool(imageManager->beamFormer()->mLineController());
            mLineTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::MLineStr, mLineTool);
        }

        {
            BMPreMouseTool* bmPreMouseTool = new BMPreMouseTool(imageManager->beamFormer()->mLineController());
            bmPreMouseTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::BMPreMouseStr, bmPreMouseTool);
        }

        BCDMouseTool* bcdTool = new BCDMouseTool(
            imageManager->beamFormer()->rOIAndDSamplingGateController(), imageManager->beamFormer()->sonoParameters(),
            viewContext->imageWidget(), (QGraphicsView*)viewContext->pWAngleItem());
        bcdTool->initialize(c);
        ToolsFactory::instance().addCommand(ToolNames::BCDMouseStr, bcdTool);

        BCCWPreMouseTool* bccwpreTool = new BCCWPreMouseTool(
            imageManager->beamFormer()->rOIAndDSamplingGateController(), imageManager->beamFormer()->sonoParameters(),
            viewContext->imageWidget(), (QGraphicsView*)viewContext->pWAngleItem());
        bccwpreTool->initialize(c);
        ToolsFactory::instance().addCommand(ToolNames::BCCWPreMouseStr, bccwpreTool);

        // added by Jin Yuqi
        BCDPreMouseTool* bcdPreTool = new BCDPreMouseTool(
            imageManager->beamFormer()->rOIAndDSamplingGateController(), imageManager->beamFormer()->sonoParameters(),
            viewContext->imageWidget(), (QGraphicsView*)viewContext->pWAngleItem());
        bcdPreTool->initialize(c);
        ToolsFactory::instance().addCommand(ToolNames::BCDPreMouseStr, bcdPreTool);

        BCDPWMouseTool* bcdPWTool = new BCDPWMouseTool(
            imageManager->beamFormer()->rOIAndDSamplingGateController(), imageManager->beamFormer()->sonoParameters(),
            viewContext->imageWidget(), (QGraphicsView*)viewContext->pWAngleItem());
        bcdPWTool->initialize(c);
        ToolsFactory::instance().addCommand(ToolNames::BCDPWMouseStr, bcdPWTool);

        BDMouseTool* bdTool = new BDMouseTool(imageManager->beamFormer()->dSamplingGateController(),
                                              imageManager->beamFormer()->sonoParameters(), viewContext->imageWidget(),
                                              (QGraphicsView*)viewContext->pWAngleItem());
        bdTool->initialize(c);
        ToolsFactory::instance().addCommand(ToolNames::BDMouseStr, bdTool);

        BDPreMouseTool* bdpreTool = new BDPreMouseTool(
            imageManager->beamFormer()->dSamplingGateController(), imageManager->beamFormer()->sonoParameters(),
            viewContext->imageWidget(), (QGraphicsView*)viewContext->pWAngleItem());
        bdpreTool->initialize(c);
        ToolsFactory::instance().addCommand(ToolNames::BDPreMouseStr, bdpreTool);

        BCWPreMouseTool* bcwpreTool = new BCWPreMouseTool(
            imageManager->beamFormer()->dSamplingGateController(), imageManager->beamFormer()->sonoParameters(),
            viewContext->imageWidget(), (QGraphicsView*)viewContext->pWAngleItem());
        bcwpreTool->initialize(c);
        ToolsFactory::instance().addCommand(ToolNames::BCWPreMouseStr, bcwpreTool);

        BDPWMouseTool* bdpwTool = new BDPWMouseTool(
            imageManager->beamFormer()->dSamplingGateController(), imageManager->beamFormer()->sonoParameters(),
            viewContext->imageWidget(), (QGraphicsView*)viewContext->pWAngleItem());
        bdpwTool->initialize(c);
        ToolsFactory::instance().addCommand(ToolNames::BDPWMouseStr, bdpwTool);

        {
            ZoomMouseTool* zoomTool = new ZoomMouseTool(imageManager->beamFormer()->zoomBoxController());
            zoomTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::ZoomMouseStr, zoomTool);
        }

        {
            ZoomOnMouseTool* zoomOnTool =
                new ZoomOnMouseTool(imageManager->beamFormer()->smallZoomBoxController(), viewContext->zoomThumbnail());
            zoomOnTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::ZoomOnMouseStr, zoomOnTool);
        }

        {
            PanZoomMouseTool* panZoomMouseTool = new PanZoomMouseTool(
                imageManager->beamFormer()->panZoomBoxController(), viewContext->panZoomThumbnail());
            panZoomMouseTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::PanZoomMouseStr, panZoomMouseTool);
        }

        MeasureMouseTool* measureTool =
            new MeasureMouseTool(markManager->measureDispather(), markManager->measureContext());
        measureTool->initialize(c);
        ToolsFactory::instance().addCommand(ToolNames::MeasureMouseStr, measureTool);

        {
            MeasurementDeleteTool* tool =
                new MeasurementDeleteTool(ToolNames::MeasurementDeleteStr, markManager->screenMeasResults(),
                                          markManager->screenMeasureController());
            tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
            tool->setMeasureMouseTool(measureTool);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            CommentMouseTool* commentTool = new CommentMouseTool();
            commentTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::CommentMouseStr, commentTool);
        }

        {
            // Xu
            QuickMeasureTool* tool =
                new QuickMeasureTool(ToolNames::DistanceMeasureStr, 0, ultrasoundView->quickMeasureCaptionsLoops(),
                                     ultrasoundView->curImageTile(), markManager->packagesMeasurement());
            tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
            ToolsFactory::instance().addCommand(tool->toolName(), tool);

            connect(tool, SIGNAL(signalCurQuickMeasureToolIsActived(int)), &(MenuController::instance()),
                    SLOT(slotCurQuickMeasureToolIsActived(int)));
        }

        {
            // Xu
            QuickMeasureTool* tool =
                new QuickMeasureTool(ToolNames::TraceMeasureStr, 1, ultrasoundView->quickMeasureCaptionsLoops(),
                                     ultrasoundView->curImageTile(), markManager->packagesMeasurement());
            tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
            ToolsFactory::instance().addCommand(tool->toolName(), tool);

            connect(tool, SIGNAL(signalCurQuickMeasureToolIsActived(int)), &(MenuController::instance()),
                    SLOT(slotCurQuickMeasureToolIsActived(int)));
        }

        {
            // Xu
            QuickMeasureTool* tool =
                new QuickMeasureTool(ToolNames::VolumeMeasureStr, 2, ultrasoundView->quickMeasureCaptionsLoops(),
                                     ultrasoundView->curImageTile(), markManager->packagesMeasurement());
            tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
            ToolsFactory::instance().addCommand(tool->toolName(), tool);

            connect(tool, SIGNAL(signalCurQuickMeasureToolIsActived(int)), &(MenuController::instance()),
                    SLOT(slotCurQuickMeasureToolIsActived(int)));
        }

        {
            QStringList measToolNames = QStringList()
                                        << ToolNames::MeasGSStr << ToolNames::MeasCRLStr << ToolNames::MeasBPDStr
                                        << ToolNames::MeasHCStr << ToolNames::MeasACStr << ToolNames::MeasFLStr
                                        << ToolNames::MeasNTStr << ToolNames::MeasAFIStr << ToolNames::MeasHeartRateStr
                                        << ToolNames::MeasHIPAngleStr;

            foreach (const QString& name, measToolNames)
            {
                MeasureItemTool* tool =
                    new MeasureItemTool(name, markManager->packagesMeasurement(), imageManager->bufferStoreManager(),
                                        peripheralManager->hotKeyContainer());
                ToolsFactory::instance().addCommand(tool->toolName(), tool);
            }
        }

        {
            GainMouseTool* tool =
                new GainMouseTool(imageManager->beamFormer()->sonoParameters(),
                                  dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()));
            ToolsFactory::instance().addCommand(ToolNames::GainMouseStr, tool);
        }

        {
            BodyMarkGlyphsControl* bodyMarkGlyphsControl = dynamic_cast<BodyMarkGlyphsControl*>(
                GlyphsControlManager::instance().getGlyphsControl(GlyphsCtl::BodyMarkGlyphsType));

            BodyMarkMouseTool* bodyMarkTool =
                new BodyMarkMouseTool(bodyMarkGlyphsControl, imageManager->beamFormer()->sonoParameters());
            bodyMarkTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::BodyMarkMouseStr, bodyMarkTool);

            RotateGlyphsTool* rotateBodyMarkTool =
                new RotateGlyphsTool(ToolNames::RotateBodyMarkStr, bodyMarkGlyphsControl);
            ToolsFactory::instance().addCommand(ToolNames::RotateBodyMarkStr, rotateBodyMarkTool);
        }

        {
            ArrowGlyphsControl* arrowGlyphsControl = dynamic_cast<ArrowGlyphsControl*>(
                GlyphsControlManager::instance().getGlyphsControl(GlyphsCtl::ArrowGlyphsType));

            ArrowScaleTool* arrowScaleTool = new ArrowScaleTool(arrowGlyphsControl);
            ToolsFactory::instance().addCommand(ToolNames::ArrowScaleStr, arrowScaleTool);

            ArrowScaleLoadDefaultTool* loadDefaultTool = new ArrowScaleLoadDefaultTool(arrowScaleTool);
            ToolsFactory::instance().addCommand(ToolNames::ArrowScaleLoadDefaultStr, loadDefaultTool);

            ArrowMouseTool* arrowTool = new ArrowMouseTool(arrowGlyphsControl);
            arrowTool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::ArrowMouseStr, arrowTool);

            RotateGlyphsTool* rotateArrowTool = new RotateGlyphsTool(ToolNames::RotateArrowStr, arrowGlyphsControl);
            ToolsFactory::instance().addCommand(ToolNames::RotateArrowStr, rotateArrowTool);

            ArrowChangeTool* arrowChangeTool = new ArrowChangeTool(arrowGlyphsControl);
            ToolsFactory::instance().addCommand(ToolNames::ArrowChangeStr, arrowChangeTool);
        }

        // Jin Yuqi
        {
            RotateMeasureTool* tool = new RotateMeasureTool();
            tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            AngleZoomEnterMeasureTool* tool = new AngleZoomEnterMeasureTool();
            tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            FreqGeneralTool* tool = new FreqGeneralTool(ToolNames::FreqGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            BDynamicRangeGeneralTool* tool = new BDynamicRangeGeneralTool(ToolNames::BDynamicRangeGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            GainGeneralTool* tool = new GainGeneralTool(ToolNames::GainGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            BGainGeneralTool* tool = new BGainGeneralTool(ToolNames::BGainGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            CGainGeneralTool* tool = new CGainGeneralTool(ToolNames::CGainGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            DGainGeneralTool* tool = new DGainGeneralTool(ToolNames::DGainGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            MGainGeneralTool* tool = new MGainGeneralTool(ToolNames::MGainGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            ChromaTool* tool = new ChromaTool(ToolNames::ChromaStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            TrapezoidalModeGeneralTool* tool = new TrapezoidalModeGeneralTool(ToolNames::TrapezoidalModeGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            UDGeneralTool* tool = new UDGeneralTool(ToolNames::UDGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            UDLightTool* tool = new UDLightTool(ToolNames::UDLightStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            AngleZoomRotateGeneralTool* tool = new AngleZoomRotateGeneralTool(ToolNames::AngleZoomRotateGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            AngleZoomGeneralTool* tool = new AngleZoomGeneralTool(ToolNames::AngleZoomGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            QBeamTool* tool = new QBeamTool(ToolNames::QBeamStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            XContrastTool* tool = new XContrastTool(ToolNames::XContrastStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            QFlowTool* tool = new QFlowTool(ToolNames::QFlowStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        // add by liujia
        {
            FreeMlineMouseTool* tool = new FreeMlineMouseTool(imageManager->beamFormer()->freeMLineController());
            tool->initialize(c);
            ToolsFactory::instance().addCommand(ToolNames::FreeMLineStr, tool);
        }
        //        {
        //            PostGainGeneralTool *tool = new PostGainGeneralTool(ToolNames::PostGainGeneralStr, m_Context);
        //            tool->setIsParameterTool(true);
        //            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        //        }
        //        这里的LeftMenuTool RightMenuTool是之前eco60键盘使用的,现在不需要了,先注释掉
        //        {
        //            LeftMenuTool *tool = new LeftMenuTool(ToolNames::LeftMenuStr);
        //            tool->setIsParameterTool(true);
        //            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        //        }

        //        {
        //            RightMenuTool *tool = new RightMenuTool(ToolNames::RightMenuStr);
        //            tool->setIsParameterTool(true);
        //            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        //        }

        // EBit新增的左旋钮功能
        {
            LeftAdjustmentTool* tool =
                new LeftAdjustmentTool(ToolNames::LeftAdjustmentStr, imageManager->beamFormer()->sonoParameters());
            tool->setIsParameterTool(true);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            AngleChangeTool* tool =
                new AngleChangeTool(ToolNames::AngleChangeStr, imageManager->beamFormer()->sonoParameters());
            tool->setIsParameterTool(true);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            DopplerAngleTool* tool =
                new DopplerAngleTool(ToolNames::DopplerAngleStr, imageManager->beamFormer()->sonoParameters());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            MenuAngleTool* tool = new MenuAngleTool(ToolNames::MenuAngleStr, markManager->packagesMeasurement());
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            ActiveBChangeTool* tool =
                new ActiveBChangeTool(ToolNames::ActiveBChangeStr, imageManager->lineBufferManager());
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            SteeringAngleTool* tool = new SteeringAngleTool(ToolNames::SteeringAngleStr);
            tool->setIsParameterTool(true);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            MenuBottomTool* tool = new MenuBottomTool(ToolNames::MenuBottomStr);
            tool->setZoomOnProxy(viewContext->zoomOnProxy());
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            BiopsyTool* tool = new BiopsyTool(ToolNames::BiopsyStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            CenterLineTool* tool = new CenterLineTool(ToolNames::CenterLineStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            TDIBMenuShowTool* tool = new TDIBMenuShowTool(ToolNames::TDIBMenuShowStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            BRulerTool* tool = new BRulerTool(ToolNames::BRulerStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            BaseLineTool* tool = new BaseLineTool(ToolNames::BaseLineStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            UpdateCommand* command = new UpdateCommand(viewContext->cursorMouseActionsModel());
            ToolsFactory::instance().addCommand(command->toolName(), command);
        }

        {
            PDStateTool* tool = new PDStateTool(ToolNames::PDStateStr);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        // createHotKeyTools ---start
        QStringList hotkeyToolNameList;

        for (int i = 1; i <= 5; i++)
        {
            hotkeyToolNameList << (ToolNames::PKeyStr + QString::number(i));
        }
        for (int i = 1; i <= 2; i++)
        {
            hotkeyToolNameList << (ToolNames::UserKeyStr + QString::number(i));
        }
        hotkeyToolNameList << (ToolNames::FKeyStr + QString::number(12));
        for (int i = 0; i < 10; i++)
        {
            hotkeyToolNameList << (ToolNames::KeyStr + QString::number(i));
        }
        hotkeyToolNameList << ToolNames::KeyStorageStr;
        hotkeyToolNameList << ToolNames::KeyMovieStr;
        hotkeyToolNameList << ToolNames::KeyPrint1Str;
        hotkeyToolNameList << ToolNames::KeyPrint2Str;

        foreach (QString toolName, hotkeyToolNameList)
        {
            HotKeyTool* tool =
                new HotKeyTool(toolName, peripheralManager->hotKeyConfig(), peripheralManager->hotKeyContainer());

            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            HotKeyTool* tool = new HotKeyTool(ToolNames::KeyFootSW1Str, peripheralManager->hotKeyConfig(),
                                              peripheralManager->hotKeyContainer());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            HotKeyTool* tool = new HotKeyTool(ToolNames::KeyFootSW2Str, peripheralManager->hotKeyConfig(),
                                              peripheralManager->hotKeyContainer());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        // createHotKeyTools ---end

        {
            MeasureMenuControlTool* tool = new MeasureMenuControlTool(ToolNames::MeasureMenuControlStr);
            tool->setMeasurementMenu(viewContext->measurementMenu());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            ScreenThumbnailExitTool* tool = new ScreenThumbnailExitTool(
                ToolNames::ScreenThumbnailExitStr, imageManager->bufferStoreManager(), imageManager->sonoParameters(),
                viewContext->thumbnailDialog(), markManager->screenMeasResults(),
                markManager->screenMeasureController(), viewContext->measureResultListWidget());
            ToolsFactory::instance().addCommand(ToolNames::ScreenThumbnailExitStr, tool);
        }

        {
            VolumeControlTool* tool = new VolumeControlTool(ToolNames::VolumeControlStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            GrabScreenTool* tool = new GrabScreenTool(ToolNames::GrabScreenStr);
            ToolsFactory::instance().addCommand(ToolNames::GrabScreenStr, tool);
        }

        {
            GrabScreenVideoTool* tool = new GrabScreenVideoTool(ToolNames::GrabScreenVideoStr);
            ToolsFactory::instance().addCommand(ToolNames::GrabScreenVideoStr, tool);
        }

        {
            InternetTool* tool = new InternetTool(ToolNames::InternetStr, ultrasoundView->mainWindow());
            ToolsFactory::instance().addCommand(ToolNames::InternetStr, tool);
        }

        {
            SimulateTouchTool* tool = new SimulateTouchTool(ToolNames::SimulateTouchStr);
            ToolsFactory::instance().addCommand(ToolNames::SimulateTouchStr, tool);
        }

        {
            ScreenOrientationChangedTool* tool = new ScreenOrientationChangedTool(ToolNames::ScreenoOrientationStr);
            ToolsFactory::instance().addCommand(ToolNames::ScreenoOrientationStr, tool);
        }

        {
            KeyBoardLightEnTool* tool = new KeyBoardLightEnTool(ToolNames::KeyBoardLightEnStr);
            tool->setIsParameterTool(true);
            tool->setControlTable(imageManager->beamFormer()->getControlTable());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            KeyBoardLightLevelTool* tool = new KeyBoardLightLevelTool(ToolNames::KeyBoardLightLevelStr);
            tool->setIsParameterTool(true);
            tool->setControlTable(imageManager->beamFormer()->getControlTable());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            EditExamEndTool* tool = new EditExamEndTool(ToolNames::EditExamEndStr);
            tool->setPatientWorkflow(examManager->patientWorkflow());
            tool->setBeamFormer(imageManager->beamFormer());
            tool->setPatientWorkflowModel(ultrasoundView->patientWorkflowModel());
            ToolsFactory::instance().addCommand(ToolNames::EditExamEndStr, tool);
        }

        {
            ToolsFactory::instance().addCommand(
                ToolNames::EditExamTipStr,
                new EditExamTipCommand(imageManager->beamFormer(), ultrasoundView->mainWindow()));
        }

        {
            FullScreenTool* tool = new FullScreenTool(viewContext->zoomOnProxy(), ToolNames::FullScreenStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);

            PostEventTool* fullScreenEvent =
                new PostEventTool(ToolNames::FullScreenEventStr, StateEventNames::FullScreen);
            ToolsFactory::instance().addCommand(fullScreenEvent->toolName(), fullScreenEvent);
        }

        {
            CWDSampleRateTool* tool = new CWDSampleRateTool(ToolNames::CWDSampleRateStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            ElastoGraphyTool* tool = new ElastoGraphyTool(ToolNames::ElastoGraphyStr);
            tool->setIsParameterTool(true);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(ToolNames::ElastoGraphyStr, tool);
        }

        {
            ElastoExitTool* tool = new ElastoExitTool(ToolNames::ElastoExitStr, StateEventNames::E);
            // tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(ToolNames::ElastoExitStr, tool);
        }

        {
            StressEchoEnTool* tool = new StressEchoEnTool(ToolNames::StressEchoEnStr, imageManager->sonoParameters());
            tool->setStressEchoWidgetManager(ultrasoundView->stressEchoWidgetManager());
            tool->setIsParameterTool(true);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(ToolNames::StressEchoEnStr, tool);

            //这个信号是基于一个阻塞窗口弹出的 所以需要使用QueuedConnection连接方式
            //否则状态机不会有响应
            connect(ultrasoundView->stressEchoWidgetManager(), SIGNAL(entryStressecho()), tool, SLOT(enterStressecho()),
                    Qt::QueuedConnection);
            connect(&MenuController::instance(), SIGNAL(menuChange(QString)), tool, SLOT(menuChanged(QString)));
        }

        {
            StressEchoTemplateTool* tool = new StressEchoTemplateTool(ToolNames::StressEchoTemplateStr);
            tool->setStressEchoWidgetManager(ultrasoundView->stressEchoWidgetManager());
            ToolsFactory::instance().addCommand(ToolNames::StressEchoTemplateStr, tool);
        }

        {
            StressEchoAnalyzeTool* tool = new StressEchoAnalyzeTool(ToolNames::StressEchoAnalyzeStr);
            tool->setStressEchoWidgetManager(ultrasoundView->stressEchoWidgetManager());
            ToolsFactory::instance().addCommand(ToolNames::StressEchoAnalyzeStr, tool);
        }

        {
            StressEchoTimer1ControlTool* tool = new StressEchoTimer1ControlTool(ToolNames::StressEchoT1ControlStr);
            tool->setStressEchoModel(ultrasoundView->stressEchoWidgetManager()->stressEchoModel());
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(ToolNames::StressEchoT1ControlStr, tool);
            connect(&ApplicationInfo::instance(), SIGNAL(languageChanged()), tool, SLOT(onLanguageChanged()));
        }

        {
            StressEchoTimer2ControlTool* tool = new StressEchoTimer2ControlTool(ToolNames::StressEchoT2ControlStr);
            tool->setStressEchoModel(ultrasoundView->stressEchoWidgetManager()->stressEchoModel());
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(ToolNames::StressEchoT2ControlStr, tool);
            connect(&ApplicationInfo::instance(), SIGNAL(languageChanged()), tool, SLOT(onLanguageChanged()));
        }

        {
            CinePlayCommandContext* ccontext = new CinePlayCommandContext();
            ccontext->setCursorMouseActionsModel(
                dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()));
            ccontext->setBufferManager(imageManager->lineBufferManager());
            ccontext->setCineLooper(imageManager->cineLooper());
            CinePlayMouseTool* tool =
                new CinePlayMouseTool(imageManager->controllerOfTGCAdjustment(),
                                      dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
            tool->initialize(ccontext);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            SonoCardiacTool* tool = new SonoCardiacTool(
                ToolNames::SonoCardiacStr, imageManager->chisonUltrasoundContext(),
                dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()),
                viewContext->leftMenuWidget(), imageManager->sonoParameters(), ultrasoundView->curImageTile(),
                imageManager->cineLooper(), markManager->measureContext());
            tool->onInitialize();
            tool->setIsClickedTool(true);
            tool->setIsLoopMulti(true);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(ToolNames::SonoCardiacStr, tool);
        }

        {
            SonoCardiacSectionTool* tool = new SonoCardiacSectionTool(ToolNames::SonoCardiacSectionStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoCardiacSectionStr, tool);
        }

        {
            SonoCardiacSecImageTool* tool = new SonoCardiacSecImageTool(ToolNames::SonoCardiacSecImageStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoCardiacSecImageStr, tool);
        }

        {
            SonoCardiacSecRecognizeTool* tool =
                new SonoCardiacSecRecognizeTool(ToolNames::SonoCardiacSecRecognizeStr,
                                                imageManager->chisonUltrasoundContext(), markManager->measureContext());
            ToolsFactory::instance().addCommand(ToolNames::SonoCardiacSecRecognizeStr, tool);
        }

        {
            SonoCardiacSecRatingTool* tool = new SonoCardiacSecRatingTool(ToolNames::SonoCardiacSecRatingStr,
                                                                          imageManager->chisonUltrasoundContext());
            ToolsFactory::instance().addCommand(ToolNames::SonoCardiacSecRatingStr, tool);
        }

        {
            SonoCardiacExitTool* tool = new SonoCardiacExitTool(ToolNames::SonoCardiacExitStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoCardiacExitStr, tool);
        }

        {
            SonoAAAMessageTool* messageTool = new SonoAAAMessageTool(ToolNames::SonoAAAMessageStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoAAAMessageStr, messageTool);

            SonoAAATool* tool = new SonoAAATool(ToolNames::SonoAAAStr, imageManager->chisonUltrasoundContext(),
                                                imageManager->sonoParameters(), markManager->packagesMeasurement(),
                                                markManager->measureContext(), imageManager->bufferStoreManager(),
                                                imageManager->lineBufferManager(), imageManager->cineLooper(),
                                                ultrasoundView->curImageTile(), toolsOrderManager, messageTool);
            tool->onInitialize();
            tool->setIsClickedTool(true);
            tool->setIsLoopMulti(true);
            tool->setScreenMeasureController(markManager->screenMeasureController());
            ToolsFactory::instance().addCommand(ToolNames::SonoAAAStr, tool);

            FreezeSonoAAA* freezeTool = new FreezeSonoAAA(ToolNames::FreezeSonoAAAStr, tool);
            ToolsFactory::instance().addCommand(ToolNames::FreezeSonoAAAStr, freezeTool);
        }

        {
            SonoAAAFmnTool* tool = new SonoAAAFmnTool(ToolNames::SonoAAAProxStr, Measurement::FMN_Near,
                                                      imageManager->chisonUltrasoundContext(),
                                                      markManager->measureContext(), imageManager->sonoParameters());
            ToolsFactory::instance().addCommand(ToolNames::SonoAAAProxStr, tool);
        }

        {
            SonoAAAFmnTool* tool = new SonoAAAFmnTool(ToolNames::SonoAAAMidStr, Measurement::FMN_Mid,
                                                      imageManager->chisonUltrasoundContext(),
                                                      markManager->measureContext(), imageManager->sonoParameters());
            ToolsFactory::instance().addCommand(ToolNames::SonoAAAMidStr, tool);
        }

        {
            SonoAAAFmnTool* tool = new SonoAAAFmnTool(ToolNames::SonoAAADistalStr, Measurement::FMN_Far,
                                                      imageManager->chisonUltrasoundContext(),
                                                      markManager->measureContext(), imageManager->sonoParameters());
            ToolsFactory::instance().addCommand(ToolNames::SonoAAADistalStr, tool);
        }

        {
            AITool* tool =
                new AITool(ToolNames::AIStr, imageManager->sonoParameters(), imageManager->chisonUltrasoundContext());
            ToolsFactory::instance().addCommand(ToolNames::AIStr, tool);
        }

        {
            AutoBLineTool* tool = new AutoBLineTool(
                ToolNames::AutoBLineStr, imageManager->chisonUltrasoundContext(), imageManager->sonoParameters(),
                markManager->packagesMeasurement(), markManager->measureContext(), imageManager->bufferStoreManager(),
                imageManager->lineBufferManager(), imageManager->cineLooper(), ultrasoundView->curImageTile(),
                toolsOrderManager, markManager->screenMeasureController());
            tool->onInitialize();
            tool->setIsClickedTool(true);
            tool->setIsLoopMulti(true);
            ToolsFactory::instance().addCommand(ToolNames::AutoBLineStr, tool);
        }

        {
            SonoVTITool* tool = new SonoVTITool(
                ToolNames::SonoVTIStr, imageManager->chisonUltrasoundContext(), imageManager->sonoParameters(),
                markManager->packagesMeasurement(), markManager->measureContext(), imageManager->bufferStoreManager(),
                imageManager->lineBufferManager(), imageManager->cineLooper(), ultrasoundView->curImageTile(),
                toolsOrderManager, markManager->screenMeasureController());
            tool->onInitialize();
            tool->setIsClickedTool(true);
            tool->setIsLoopMulti(false);
            ToolsFactory::instance().addCommand(ToolNames::SonoVTIStr, tool);
        }

        {
            SamplingValveTool* tool = new SamplingValveTool(ToolNames::SamplingValveStr, imageManager->beamFormer(),
                                                            imageManager->beamFormer()->sonoParameters());
            ToolsFactory::instance().addCommand(ToolNames::SamplingValveStr, tool);
        }

#if defined(USE_FREEHAND3D) || defined(USE_4D)
        {
            FourDTransformTool* tool = new FourDTransformTool(m_Context->chisonUltrasoundContext());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            for (int i = AxisTypeEnum::Axis_X; i <= AxisTypeEnum::Axis_Z; i++)
            {
                FourDTransformTool* tool = new FourDTransformTool(m_Context->chisonUltrasoundContext(), i);
                tool->setToolName(ToolNames::FourDTransformStr + QString::number(i));
                ToolsFactory::instance().addCommand(tool->toolName(), tool);
            }
        }
#endif
#ifdef USE_FREEHAND3D
        {
            FourDTransformTool* tool =
                dynamic_cast<FourDTransformTool*>(ToolsFactory::instance().command(ToolNames::FourDTransformStr));
            if (tool != NULL)
            {
                connect(tool, SIGNAL(freeHand3DRotate(bool)), m_Context->freeHand3DProxy()->freeHand3DWidget(),
                        SLOT(onRotate(bool)));
            }
        }
        {
            for (int i = AxisTypeEnum::Axis_X; i <= AxisTypeEnum::Axis_Z; i++)
            {
                FourDTransformTool* tool = dynamic_cast<FourDTransformTool*>(
                    ToolsFactory::instance().command(ToolNames::FourDTransformStr + QString::number(i)));
                if (tool != NULL)
                {
                    connect(tool, SIGNAL(freeHand3DRotate(bool)), m_Context->freeHand3DProxy()->freeHand3DWidget(),
                            SLOT(onRotate(bool)));
                }
            }
        }

        {
            FreeHand3DMouseCommandContext* ccontext = new FreeHand3DMouseCommandContext();
            ccontext->setCursorMouseActionsModel(m_Context->cursorMouseActionsModel());
            ccontext->setFreeHand3DRoiControl(m_Context->freeHand3DProxy()->freeHand3DRoiControl());
            FreeHand3DMouseTool* tool = new FreeHand3DMouseTool();
            tool->initialize(ccontext);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        FreeHand3DTool* pFreeHand3DTool = new FreeHand3DTool(ToolNames::FreeHand3DStr, lineBufferManager);
        if (NULL != pFreeHand3DTool)
        {
            ToolsFactory::instance().addCommand(pFreeHand3DTool->toolName(), pFreeHand3DTool);
        }

        {
            FreeHand3DZoomTool* tool = new FreeHand3DZoomTool(ToolNames::FreeHand3DZoomStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(ToolNames::FreeHand3DZoomStr, tool);
            connect(tool, SIGNAL(freeHand3DZoomIn(bool)), m_Context->freeHand3DProxy()->freeHand3DWidget(),
                    SLOT(onFreeHand3DZoom(bool)));
        }

        {
            FreeHand3DResetTool* tool = new FreeHand3DResetTool(ToolNames::FreeHand3DResetStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(ToolNames::FreeHand3DResetStr, tool);
            connect(tool, SIGNAL(freeHand3Dreset()), m_Context->freeHand3DProxy()->freeHand3DWidget(),
                    SLOT(onFreeHand3DReset()));
        }
#endif
#ifdef USE_4D
        {
            FourDTransformTool* tool =
                dynamic_cast<FourDTransformTool*>(ToolsFactory::instance().command(ToolNames::FourDTransformStr));
            if (tool != NULL)
            {
                connect(tool, SIGNAL(transform(bool, int)), m_Context->chisonFourdProxy()->transformWidget(),
                        SLOT(onTransformation(bool, int)));
            }
        }
        {
            for (int i = AxisTypeEnum::Axis_X; i <= AxisTypeEnum::Axis_Z; i++)
            {
                FourDTransformTool* tool = dynamic_cast<FourDTransformTool*>(
                    ToolsFactory::instance().command(ToolNames::FourDTransformStr + QString::number(i)));
                if (tool != NULL)
                {
                    connect(tool, SIGNAL(transform(bool, int)), m_Context->chisonFourdProxy()->transformWidget(),
                            SLOT(onTransformation(bool, int)));
                }
            }
        }
        {
            FourdMouseCommandContext* ccontext = new FourdMouseCommandContext();
            ccontext->setCursorMouseActionsModel(m_Context->cursorMouseActionsModel());
            ccontext->setChisonFourDProxy(m_Context->chisonFourdProxy());
            FourDMouseMoveTool* tool = new FourDMouseMoveTool();
            tool->initialize(ccontext);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            FourDCurvedLineSlopeChangedTool* tool = new FourDCurvedLineSlopeChangedTool();
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
            connect(tool, SIGNAL(curvedLineSlopeChanged(bool)), m_Context->chisonFourdProxy()->transformWidget(),
                    SLOT(onSplineSlopeChanged(bool)));
        }

        {
            FourDCurvedLineCtrlPointMoveTool* tool = new FourDCurvedLineCtrlPointMoveTool();
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
            connect(tool, SIGNAL(curvedLineCtrlPointMoved(QString)), m_Context->chisonFourdProxy()->fourDLiveWidget(),
                    SLOT(onCurvedLineCtrlPointMoved(QString)));
        }

        {
            FourDSplitScreenTool* tool = new FourDSplitScreenTool();
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
            connect(tool, SIGNAL(splitScrren(QString)), m_Context->chisonFourdProxy()->fourDLeftWidget(),
                    SLOT(onFourDSplitScreen(QString)));
        }

        {
            FourDReviewLoadTool* tool = new FourDReviewLoadTool(m_Context);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            FourDEventProcessTool* tool = new FourDEventProcessTool(m_Context);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            FourDiImageParasTool* tool = new FourDiImageParasTool();
            tool->setParentWidget(m_Context->chisonFourdProxy()->fourDLiveWidget());
            tool->setRealTimeParameters(m_Context->sonoParameters());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            FourDTransformModeChangedTool* tool = new FourDTransformModeChangedTool();
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
            connect(tool, SIGNAL(transformModeChanged(int)), m_Context->chisonFourdProxy()->transformWidget(),
                    SLOT(onTransformModeChanged(int)));
        }

        {
            FourDZoomTool* tool = new FourDZoomTool(ToolNames::FourDZoomStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(ToolNames::FourDZoomStr, tool);
            connect(tool, SIGNAL(fourdZoomIn(bool)), m_Context->chisonFourdProxy()->fourDLiveWidget(),
                    SLOT(onFourDZoom(bool)));
        }

        {
            FourDCurveClippingTool* tool = new FourDCurveClippingTool(m_Context);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            ITool* tool = new PostMultiEventTool(ToolNames::FourDEasyViewStr,
                                                 QStringList() << StateEventNames::FourD << StateEventNames::Browse);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDVirtualHDTool* tool = new FourDVirtualHDTool(BFPNames::FourDVirtualHDOnStr);
            tool->setIsParameterTool(true);
            tool->setSonoParameters(m_Context->sonoParameters());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDRenderTool* tool = new FourDRenderTool(BFPNames::ChisonFourDRenderStr);
            tool->setIsParameterTool(true);
            tool->setSonoParameters(m_Context->sonoParameters());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        for (int i = FourDWinLayoutEnum::WINLAYOUT_2X2; i <= FourDWinLayoutEnum::WINLAYOUT_1X1; i++)
        {
            FourDLayoutTool* tool = new FourDLayoutTool(BFPNames::FourDWinLayoutStr + QString::number(i), i);
            tool->setIsParameterTool(true);
            tool->setSonoParameters(m_Context->sonoParameters());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDChangeTransformAxisTool* tool = new FourDChangeTransformAxisTool();
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
            connect(tool, SIGNAL(transformAxisChanged(QString)), m_Context->chisonFourdProxy()->fourDLiveWidget(),
                    SLOT(onFourDTransformAxisChanged(QString)));
        }
        {
            FourDMouseStateChangedTool* tool = new FourDMouseStateChangedTool();
            tool->setIsParameterTool(true);
            tool->setSonoParameters(m_Context->sonoParameters());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDThresholdTool* tool = new FourDThresholdTool(BFPNames::ChisonFourDThresholdStr);
            tool->setIsParameterTool(true);
            tool->setSonoParameters(m_Context->sonoParameters());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDPaletteTool* tool = new FourDPaletteTool(BFPNames::ChisonFourDPaletteStr);
            tool->setIsParameterTool(true);
            tool->setSonoParameters(m_Context->sonoParameters());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDResetTool* tool = new FourDResetTool(ToolNames::FourDResetStr);
            tool->setIsParameterTool(false);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
            connect(tool, SIGNAL(fourdReset()), m_Context->chisonFourdProxy(), SLOT(onResetFourDParameters()));
        }
        {
            FourDDirectionTool* tool = new FourDDirectionTool(BFPNames::FourDDirectionSetStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
            connect(tool, SIGNAL(fourdDirectionChanged(bool)), m_Context->chisonFourdProxy()->fourDLiveWidget(),
                    SLOT(onFourDDirectionChanged(bool)));
        }
        {
            FourDGainTool* tool = new FourDGainTool(ToolNames::FourDGainGeneralStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDFrameRateTool* tool = new FourDFrameRateTool(BFPNames::FourDFrameRateStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDDynamicRangeTool* tool = new FourDDynamicRangeTool(BFPNames::FourDDynamicRangeStr);
            tool->setIsParameterTool(true);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDStateTool* tool = new FourDStateTool(m_Context);
            tool->setSonoParameters(m_Context->sonoParameters());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDLiveTool* tool = new FourDLiveTool(m_Context);
            tool->setSonoParameters(m_Context->sonoParameters());

            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
        {
            FourDExitTool* tool = new FourDExitTool();
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
#endif
        ToolsFactory::instance().addCommand(
            BFPNames::QuadplexModeStr,
            new QuadplexModeTool(BFPNames::QuadplexModeStr, examManager->patientWorkflow(),
                                 imageManager->bufferStoreManager(), imageManager->chisonUltrasoundContext(),
                                 markManager->measureContext(), imageManager->beamFormer(),
                                 markManager->packagesMeasurement(), ultrasoundView->curImageTile(),
                                 markManager->screenMeasureController(), markManager->rulerFactory(),
                                 markManager->measurementCreator()));

        {
            WorklistTool* tool =
                new WorklistTool(ToolNames::WorklistStr, ultrasoundView->patientWorkflowModel()->patientEditModel());
            ToolsFactory::instance().addCommand(ToolNames::WorklistStr, tool);
        }
#ifdef USE_PANORAMIC
        {
            CurvedPanoramicEnTool* tool = new CurvedPanoramicEnTool(
                ToolNames::CurvedPanoramicStr, imageManager->chisonUltrasoundContext()->curvedPanoramic(),
                viewContext->freezeBarWidget());
            CurvedPanoramicExitTool* exitTool = new CurvedPanoramicExitTool(ToolNames::CurvedPanoramicExitStr);
            CurvedPanoramicExitInCallBackTool* runExitInCallBack =
                new CurvedPanoramicExitInCallBackTool(ToolNames::CurvedPanoramicExitInCallBackStr);
            tool->setIsParameterTool(true);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(ToolNames::CurvedPanoramicStr, tool);
            ToolsFactory::instance().addCommand(ToolNames::CurvedPanoramicExitStr, exitTool);
            ToolsFactory::instance().addCommand(ToolNames::CurvedPanoramicExitInCallBackStr, runExitInCallBack);
        }
#endif
        {
            ImageTypeChangeTool* tool = new ImageTypeChangeTool(
                ToolNames::ImageTypeChangeStr, imageManager->sonoParameters(), markManager->packagesMeasurement());
            ToolsFactory::instance().addCommand(ToolNames::ImageTypeChangeStr, tool);
        }

        {
            IsEnableAdjustROITool* tool =
                new IsEnableAdjustROITool(ToolNames::IsEnableAdjustROIStr, imageManager->sonoParameters());
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(ToolNames::IsEnableAdjustROIStr, tool);
        }
        {
            SuperNeedleTool* tool = new SuperNeedleTool(BFPNames::NeedleModeStr);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(BFPNames::NeedleModeStr, tool);
        }
        {
            VirtualVertexTrapezoidalModeTool* tool =
                new VirtualVertexTrapezoidalModeTool(ToolNames::VirtualVertexTrapezoidalModeStr);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(ToolNames::VirtualVertexTrapezoidalModeStr, tool);
        }
        {
            SonoNeedleTool* tool = new SonoNeedleTool(BFPNames::SonoNeedleStr);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(BFPNames::SonoNeedleStr, tool);
        }
        {
            ExitFromSonoNeedle* tool = new ExitFromSonoNeedle(ToolNames::SonoNeedleExitStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoNeedleExitStr, tool);
        }
        {
            SonoHelpTool* tool =
                new SonoHelpTool(viewContext->leftTopMenuWidgetContainer(), imageManager->sonoParameters());
            ToolsFactory::instance().addCommand(ToolNames::SonoHelpStr, tool);
        }

        {
            MVITool* tool = new MVITool(ToolNames::MVIShowStr);
            ToolsFactory::instance().addCommand(ToolNames::MVIShowStr, tool);
        }

        {
            BaselineMVITool* tool = new BaselineMVITool(ToolNames::BaseLineMVIStr);
            ToolsFactory::instance().addCommand(ToolNames::BaseLineMVIStr, tool);
        }

        {
            BiopsyMenuTool* tool = new BiopsyMenuTool(ToolNames::BiopsyMenuStr);
            ToolsFactory::instance().addCommand(ToolNames::BiopsyMenuStr, tool);
        }

        {
            BiopsyChooseTool* tool = new BiopsyChooseTool(ToolNames::BiopsyChooseStr);
            tool->setStateManager(ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::BiopsyChooseStr, tool);
        }

        {
            BiopsyVerifyTool* tool = new BiopsyVerifyTool(ToolNames::BiopsyVerifyStr);
            tool->setStateManager(ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::BiopsyVerifyStr, tool);
        }

        {
            BiopsySaveTool* tool = new BiopsySaveTool(ToolNames::BiopsySaveStr);
            tool->setStateManager(ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::BiopsySaveStr, tool);
        }

        {
            BiopsyRestoreTool* tool = new BiopsyRestoreTool(ToolNames::BiopsyRestoreStr);
            tool->setStateManager(ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::BiopsyRestoreStr, tool);
        }

        {
            BiopsyAngleTool* tool = new BiopsyAngleTool(ToolNames::BiopsyAngleStr);
            ToolsFactory::instance().addCommand(ToolNames::BiopsyAngleStr, tool);
        }

        {
            BiopsyXPosMMTool* tool = new BiopsyXPosMMTool(ToolNames::BiopsyXPosMMStr);
            ToolsFactory::instance().addCommand(ToolNames::BiopsyXPosMMStr, tool);
        }

        {
            BiopsyExitTool* tool = new BiopsyExitTool(ToolNames::BiopsyExitStr);
            ToolsFactory::instance().addCommand(ToolNames::BiopsyExitStr, tool);
        }

        {
            BiopsyCancelTool* tool = new BiopsyCancelTool(ToolNames::BiopsyCancelStr);
            ToolsFactory::instance().addCommand(ToolNames::BiopsyCancelStr, tool);
        }
        {
            ElastoTool* tool = new ElastoTool(ToolNames::ElastoShowStr);
            ToolsFactory::instance().addCommand(ToolNames::ElastoShowStr, tool);
        }

        {
            SonoNerveQuitTool* tool = new SonoNerveQuitTool(ToolNames::SonoNerveQuitStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoNerveQuitStr, tool);
        }

        {
            SonoNerveQuitFromBModeTool* tool = new SonoNerveQuitFromBModeTool(ToolNames::SonoNerveQuitFromBModeStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoNerveQuitFromBModeStr, tool);
        }

        {
            SonoNerveScalenusBPTool* tool = new SonoNerveScalenusBPTool(ToolNames::SonoNerveScalenusBPStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoNerveScalenusBPStr, tool);
        }

        {
            SonoNerveSupraclavicularBPTool* tool =
                new SonoNerveSupraclavicularBPTool(ToolNames::SonoNerveSupraclavicularBPStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoNerveSupraclavicularBPStr, tool);
        }
        {
            SonoNerveMedianBPTool* tool = new SonoNerveMedianBPTool(ToolNames::SonoNerveMedianBPStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoNerveMedianBPStr, tool);
        }
        {
            CallbackRTMeasureContollerTool* tool = new CallbackRTMeasureContollerTool(
                ToolNames::CallbackRTMeasureContollerStr, markManager->measureContext(),
                ultrasoundView->patientWorkflowModel());
            tool->setScreenMeasureController(markManager->screenMeasureController());
            tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {

            SonoMSKQuitTool* tool = new SonoMSKQuitTool(ToolNames::SonoMSKQuitStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoMSKQuitStr, tool);
        }

        {
            SonoMSKQuitFromBModeTool* tool = new SonoMSKQuitFromBModeTool(ToolNames::SonoMSKQuitFromBModeStr);
            ToolsFactory::instance().addCommand(ToolNames::SonoMSKQuitFromBModeStr, tool);
        }

        {
            SonoMSKBicepsLHTendonTool* tool = new SonoMSKBicepsLHTendonTool(
                ToolNames::BicepsLHTendonSAStr,
                dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()),
                viewContext->leftMenuWidget(), dynamic_cast<PopUpWidget*>(ultrasoundView->popUpWidget()),
                ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::BicepsLHTendonSAStr, tool);
        }

        {
            SonoMSKBicepsLHTendonTool* tool = new SonoMSKBicepsLHTendonTool(
                ToolNames::BicepsLHTendonLAStr,
                dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()),
                viewContext->leftMenuWidget(), dynamic_cast<PopUpWidget*>(ultrasoundView->popUpWidget()),
                ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::BicepsLHTendonLAStr, tool);
        }
        {
            SonoMSKBicepsLHTendonTool* tool = new SonoMSKBicepsLHTendonTool(
                ToolNames::SSPTendonLAStr,
                dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()),
                viewContext->leftMenuWidget(), dynamic_cast<PopUpWidget*>(ultrasoundView->popUpWidget()),
                ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::SSPTendonLAStr, tool);
        }
        {
            SonoMSKBicepsLHTendonTool* tool = new SonoMSKBicepsLHTendonTool(
                ToolNames::ISPTendonLAStr,
                dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()),
                viewContext->leftMenuWidget(), dynamic_cast<PopUpWidget*>(ultrasoundView->popUpWidget()),
                ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::ISPTendonLAStr, tool);
        }
        {
            SonoMSKBicepsLHTendonTool* tool = new SonoMSKBicepsLHTendonTool(
                ToolNames::SSCTendonLAStr,
                dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()),
                viewContext->leftMenuWidget(), dynamic_cast<PopUpWidget*>(ultrasoundView->popUpWidget()),
                ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::SSCTendonLAStr, tool);
        }
        {
            SonoMSKBicepsLHTendonTool* tool = new SonoMSKBicepsLHTendonTool(
                ToolNames::SSCTendonSAStr,
                dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()),
                viewContext->leftMenuWidget(), dynamic_cast<PopUpWidget*>(ultrasoundView->popUpWidget()),
                ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::SSCTendonSAStr, tool);
        }
        {
            SonoMSKBicepsLHTendonTool* tool = new SonoMSKBicepsLHTendonTool(
                ToolNames::TMTendonLAStr,
                dynamic_cast<CursorMouseActionsModel*>(viewContext->cursorMouseActionsModel()),
                viewContext->leftMenuWidget(), dynamic_cast<PopUpWidget*>(ultrasoundView->popUpWidget()),
                ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(ToolNames::TMTendonLAStr, tool);
        }
        {
            AutoEFTool* tool =
                new AutoEFTool(ToolNames::AutoEFStr, imageManager->bufferStoreManager(), markManager->measureContext(),
                               imageManager->chisonUltrasoundContext(), markManager->packagesMeasurement(),
                               markManager->rulerFactory(), ultrasoundView->stressEchoWidgetManager());
            tool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
            ToolsFactory::instance().addCommand(ToolNames::AutoEFStr, tool);
        }

        {
            ClickAutoEFMeasureTool* tool = new ClickAutoEFMeasureTool(
                ToolNames::ClickAutoEFMeasureStr, imageManager->sonoParameters(), markManager->rulerFactory());
            ToolsFactory::instance().addCommand(ToolNames::ClickAutoEFMeasureStr, tool);
        }

        {
            AutoEFCurLayoutTool* tool =
                new AutoEFCurLayoutTool(ToolNames::AutoEFCurLayoutStr, markManager->rulerFactory());
            ToolsFactory::instance().addCommand(ToolNames::AutoEFCurLayoutStr, tool);
        }

        {
            AutoEFSingleBTool* tool = new AutoEFSingleBTool(ToolNames::AutoEFSingleBStr, markManager->rulerFactory());
            ToolsFactory::instance().addCommand(ToolNames::AutoEFSingleBStr, tool);
        }

        {
            EndCurrentStateTool* tool = new EndCurrentStateTool(ToolNames::EndCurrentStateStr);
            tool->setStateManager(ultrasoundView->stateManagerFacade());
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
            connect(StateManager::getInstance().state(StateEventNames::CommentState), SIGNAL(Exited()), tool,
                    SLOT(runEndTool()));
            connect(StateManager::getInstance().state(StateEventNames::ArrowState), SIGNAL(Exited()), tool,
                    SLOT(runEndTool()));
            connect(StateManager::getInstance().state(StateEventNames::BodyMarkState), SIGNAL(Exited()), tool,
                    SLOT(runEndTool()));
            connect(StateManager::getInstance().state(StateEventNames::MeasurementState), SIGNAL(Exited()), tool,
                    SLOT(runEndTool()));
            connect(StateManager::getInstance().state(StateEventNames::QuickDistanceState), SIGNAL(Exited()), tool,
                    SLOT(runEndTool()));
            connect(StateManager::getInstance().state(StateEventNames::QuickEllipseState), SIGNAL(Exited()), tool,
                    SLOT(runEndTool()));
            connect(StateManager::getInstance().state(StateEventNames::QuickTraceState), SIGNAL(Exited()), tool,
                    SLOT(runEndTool()));
        }

        {
            DebuggerViewTool* tool = new DebuggerViewTool(
                ToolNames::DebuggerViewStr, viewContext->configurationDialog(), ultrasoundView->mainWindow(),
                ultrasoundView->curImageTile(), peripheralManager->machine(), ultrasoundView->bFHWInfoModel(),
                ultrasoundView->stateManagerFacade(), peripheralManager->diskDevice(), imageManager->probeDataSet(),
                peripheralManager->systemInfo(), ultrasoundView->licenseInitializer());
            ToolsFactory::instance().addCommand(ToolNames::DebuggerViewStr, tool);
        }

        {
            EnableBlockDataRecordTool* tool = new EnableBlockDataRecordTool(ToolNames::EnableBlockDataRecordStr);
            ToolsFactory::instance().addCommand(ToolNames::EnableBlockDataRecordStr, tool);
        }

        {
            SaveCTableAndBlockDataTool* tool =
                new SaveCTableAndBlockDataTool(ToolNames::QuickSaveCTableAndBlockDataStr);
            tool->setBeamFormerTool(imageManager->beamFormer());
            PresetModeTool* presetModeTool =
                dynamic_cast<PresetModeTool*>(ToolsFactory::instance().command(ToolNames::PresetModeStr));
            presetModeTool->setPresetTools(tool->presetTools());
            ToolsFactory::instance().addCommand(ToolNames::QuickSaveCTableAndBlockDataStr, tool);
        }

        {
            OpenProbeCodeBurnerTool* tool = new OpenProbeCodeBurnerTool(ToolNames::OpenProbeCodeBurnerStr);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(ToolNames::OpenProbeCodeBurnerStr, tool);
        }

        {
            OpenVirtualProbeCodeTool* tool = new OpenVirtualProbeCodeTool(ToolNames::OpenVirtualProbeCodeStr);
            tool->setProbeDataSet(imageManager->probeDataSet());
            ToolsFactory::instance().addCommand(ToolNames::OpenVirtualProbeCodeStr, tool);
        }

        {
            ITool* tool = new PrintfFunctionTimeTool(ToolNames::PrintfFunctionTimeStr);
            ToolsFactory::instance().addCommand(ToolNames::PrintfFunctionTimeStr, tool);
        }

#ifdef USE_SONOCAROTIDGUIDE
        {
            SonoCarotidLRTool* tool = new SonoCarotidLRTool(ToolNames::SonoCarotidLRStr);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            SonoCarotidGuideTool* tool =
                new SonoCarotidGuideTool(ToolNames::SonoCarotidGuideStr, imageManager->chisonUltrasoundContext(),
                                         ultrasoundView, markManager->measureContext());
            ToolsFactory::instance().addCommand(ToolNames::SonoCarotidGuideStr, tool);
        }

        {
            SonoGuideSectionTool* tool = new SonoGuideSectionTool(ToolNames::SonoGuideSectionStr);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }

        {
            SonoCarotidExitTool* tool = new SonoCarotidExitTool(ToolNames::SonoCarotidExitStr);
            ToolsFactory::instance().addCommand(tool->toolName(), tool);
        }
#endif
    }

    {
        TGCAdjustCommand* tool = new TGCAdjustCommand(ToolNames::TGCAdjustStr);
        ToolsFactory::instance().addCommand(tool->toolName(), tool);
        connect(ultrasoundView->widgetOfTGCAdjustment(), &TGCMenuWidgetContainer::updateClicked, tool,
                &TGCAdjustCommand::onUpdateClicked);
        connect(tool, &TGCAdjustCommand::workStateChanged, ultrasoundView->widgetOfTGCAdjustment(),
                &TGCMenuWidgetContainer::updateWorkState);
    }

    {
        GainAdjustCommand* gainAdjustCommand = new GainAdjustCommand();
        ToolsFactory::instance().addCommand(gainAdjustCommand->toolName(), gainAdjustCommand);
    }

    createToolsEnd(peripheralManager, imageManager, examManager, markManager, ultrasoundView);
}

void ToolsCreator::createToolsEnd(IPeripheralManager* peripheralManager, IImageManager* imageManager,
                                  IExamManager* examManager, IMarkManager* markManager, IUltrasoundView* ultrasoundView)
{
    AbstractMachine* machine = peripheralManager->machine();
    Q_ASSERT(machine != NULL);
    // createMachine放到了dopplerplatform::init(),防止dopplerplatform依赖mainmodule，原来需要作的connect放于此
    connect(machine, SIGNAL(beginToShutdown(bool&)), &ApplicationExitCommand::instance(), SLOT(onShutDown(bool&)));

    ApplicationExitCommand::instance().setPatientWorkflow(examManager->patientWorkflow());
#ifdef USE_PANORAMIC
    ApplicationExitCommand::instance().setCurvedPanoramic(imageManager->chisonUltrasoundContext()->curvedPanoramic());
#endif
    ApplicationExitCommand::instance().setBeamformer(imageManager->beamFormer());

    StressEchoAnalyzeTool* stressEchoAnalyzeTool =
        dynamic_cast<StressEchoAnalyzeTool*>(ToolsFactory::instance().command(ToolNames::StressEchoAnalyzeStr));
    SystemStatusWidget* systemStatusTip = (SystemStatusWidget*)ultrasoundView->viewContext()->systemStatusTip();
    connect(systemStatusTip, SIGNAL(shutDownDialogShow()), stressEchoAnalyzeTool, SIGNAL(shutDownDialogShow()));

    {
        RTIMTTool* rtimtTool = dynamic_cast<RTIMTTool*>(ToolsFactory::instance().command(ToolNames::RTIMTStr));
        rtimtTool->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(markManager->measureDispather()));
        rtimtTool->setProbeDataSet(imageManager->probeDataSet());
        rtimtTool->initialize(new MenuToolContext(imageManager->bufferStoreManager()));
        EndTool* endTool = dynamic_cast<EndTool*>(ToolsFactory::instance().command(ToolNames::EndStr));
        EditExamEndTool* editExamEndTool =
            dynamic_cast<EditExamEndTool*>(ToolsFactory::instance().command(ToolNames::EditExamEndStr));
        connect(endTool, SIGNAL(endClose()), rtimtTool, SLOT(onEndClose()));
        connect(editExamEndTool, SIGNAL(editExamEndClose()), rtimtTool, SLOT(onEndClose()));
    }

    PresetModeTool* presetModeTool =
        dynamic_cast<PresetModeTool*>(ToolsFactory::instance().command(ToolNames::PresetModeStr));
    DebuggerViewTool* debuggerViewTool =
        dynamic_cast<DebuggerViewTool*>(ToolsFactory::instance().command(ToolNames::DebuggerViewStr));
    connect(debuggerViewTool, SIGNAL(exitPresetMode()), presetModeTool, SLOT(slotExitPresetMode()));
}
