#include "applicationeventfilter.h"
#include "setting.h"
#include <QEvent>
#include <QKeyEvent>
#include <QMouseEvent>
#include <QDebug>
#include "itoolsfacade.h"
#include "istatemanager.h"
#include "stateeventnames.h"
#include "cursormouseactionsmodel.h"
#include "applicationinfo.h"
#include "lightcontroller.h"
#include "lightnames.h"
#include "ikeyboard.h"
#include "util.h"
#include "batteryinfo.h"
#include "settingswidget.h"
#include "configurationwidget.h"
#include "systemstatuswidget.h"
#include "commentglyphscontrol.h"
#include "commentglyphs.h"
#include "abstractstate.h"
#include "qtkeyprocessor.h"
#include "appsetting.h"
#include "abstractmachine.h"
#include "icontroltable.h"
#include "controltablesender.h"
#include "toolnames.h"
#include "shutdowntool.h"
#include "autoeftool.h"
#include "uiutil.h"

#include "filedialog.h"
#include "messageevent.h" //
#include "diskinfo.h"
#include "udiskpathinfo.h"
#include "messageboxframe.h"
#ifdef USE_ADMINVIEW
#include "iadminconfigmodel.h"
#endif
#include "glyphscontrolmanager.h"
#include "ieventfiltercontroller.h"
#include <QDesktopWidget>
#include <QCursor>
#include <QApplication>
#include <QScreen>
#include "logger.h"
#include "framecontrol.h"
#include "qvariantcommandargs.h"
#include "gaingeneraltool.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "mainwindowkbunit.h"
#include "imagewidget.h"
#include "imagetile.h"
#include "parameter.h"
#include "updatecontroller.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, ApplicationEventFilter)
ApplicationEventFilter::ApplicationEventFilter(IKeyboard* m_Keyboard, AbstractMachine* machine,
                                               BaseDialog* configurationDialog, UpdateController* updateController,
                                               BaseWidget* systemStatusWidget, QtKeyProcessor* QtKeyProcessor,
                                               IToolsFacade* toolsFacade, IStateManager* stateManager,
                                               IAdminConfigModel* adminConfigModel, SonoParameters* sonoParameters,
                                               ImageTile* imageTile, ICursorMouseAction* cursorMouseAction,
                                               IBeamFormer* beamFormer, ISystemInfo* systemInfo, QObject* parent)
    : QObject(parent)
    , m_ScreenState(None)
    , m_IsCursorVisible(false)
    , m_IsPreviewed(false)
    , m_PictureIndex(Setting::instance().defaults().screenSaverPictureIndex())
    , m_mouseOnTouch(false)
    , m_IsMainWindow(true)
    , m_StartAdjustGain(false)
    , m_Keyboard(NULL)
    , m_Machine(machine)
    , m_ConfigurationDialog(dynamic_cast<ConfigurationDialog*>(configurationDialog))
    , m_UpdateController(dynamic_cast<UpdateController*>(updateController))
    , m_SystemStatusWidget(dynamic_cast<SystemStatusWidget*>(systemStatusWidget))
    , m_QtKeyProcessor(QtKeyProcessor)
    , m_ToolsFacade(toolsFacade)
    , m_StateManager(stateManager)
    , m_AdminConfigModel(adminConfigModel)
    , m_SonoParameters(sonoParameters)
    , m_ImageTile(imageTile)
    , m_MainWIndow(dynamic_cast<MainWindowKBUnit*>(parent))
    , m_CursorMouseActionsModel(dynamic_cast<CursorMouseActionsModel*>(cursorMouseAction))
    , m_BeamFormer(beamFormer)
    , m_BlockTouch(false)
    , m_SystemInfo(systemInfo)

{
    // prepare gainArea
    m_AdjustGainArea << "BottomWidgetContainer"
                     << "SonoParasWidget"
                     << "freezeBarWidget"
                     << "FreezeBarWidget"
                     << "freezeBarWidgetContainter"
                     << "FunctionButtonsWidgetContainer";

    connect(&Setting::instance().defaults(), SIGNAL(settingChanged(QString)), this, SLOT(onSettingChanged(QString)));
    connect(&m_ScreenSaverTimer, SIGNAL(timeout()), this, SLOT(callScreenSaverProgram()));
    connect(&m_ScreenOffTimer, SIGNAL(timeout()), this, SLOT(callScreenOffProgram()));

    connect(m_Keyboard, SIGNAL(keyPressed(bool*)), this, SLOT(onKeyPressed(bool*)));
    connect(m_Keyboard, SIGNAL(tgcDataReceived(unsigned char*, int)), this, SLOT(onTgcDataReceived()));

    // connect(m_Context->settingsDialog(), SIGNAL(screenControlTimersStoped()), this,
    // SLOT(onScreenControlTimersStoped())); connect(m_Context->settingsDialog(), SIGNAL(screenControlTimersStarted()),
    // this, SLOT(onScreenControlTimersStarted()));

    connect(m_ConfigurationDialog, SIGNAL(screenControlTimersStoped()), this, SLOT(onScreenControlTimersStoped()));
    connect(m_ConfigurationDialog, SIGNAL(screenControlTimersStarted()), this, SLOT(onScreenControlTimersStarted()));

    connect(m_UpdateController, SIGNAL(screenControlTimersStoped()), this, SLOT(onScreenControlTimersStoped()));
    connect(m_UpdateController, SIGNAL(screenControlTimersStarted()), this, SLOT(onScreenControlTimersStarted()));

    //电池信息,u盘插拔,网络状态,电源键等的屏保控制
    connect(m_SystemStatusWidget, SIGNAL(screenControllerStoped(bool*)), this, SLOT(onKeyPressed(bool*)));

    //预览
    // connect(m_Context->settingsDialog(), SIGNAL(screenSaverPreviewed(bool, int)), this,
    // SLOT(onScreenSaverPreviewed(bool, int)));
    connect(m_ConfigurationDialog, SIGNAL(screenSaverPreviewed(bool, int)), this,
            SLOT(onScreenSaverPreviewed(bool, int)));

    //按下字符键直接进入comment
    connect(m_QtKeyProcessor, SIGNAL(charKeyPressed(int, int, bool*)), this, SLOT(onCharKeyPressed(int, int, bool*)));

    m_StandbyController.setMachine(m_Machine);
    m_StandbyController.setStateManager(m_StateManager);
    m_StandbyController.setSystemInfo(m_SystemInfo);
    connect(&m_StandbyTimer, SIGNAL(timeout()), this, SLOT(callStandbyProgram()));

    ShutDownTool* shutDownTool = static_cast<ShutDownTool*>(m_ToolsFacade->command(ToolNames::ShutDownStr));
    connect(shutDownTool, SIGNAL(screenOpened()), this, SLOT(sendControlTable()));

    AutoEFTool* autoEFTool = static_cast<AutoEFTool*>(m_ToolsFacade->command(ToolNames::AutoEFStr));
    connect(autoEFTool, SIGNAL(blockTouch(bool)), this, SLOT(onBlockTouch(bool)));

    // BUG:61565 待机唤醒后，虚拟键盘无效
    // 自测发现模态框，待机唤醒后都有虚拟键盘无法输入的问题;
    // 原因：模态框在唤醒后阻塞了虚拟键盘无法拿到焦点，解决方案是在待机前给对话框关闭
    connect(shutDownTool, SIGNAL(standby()), this, SLOT(hideVirtualKeyboardStateOnStandBy()));

    connect(shutDownTool, SIGNAL(standby()), this, SLOT(callStandbyProgram()));

    connect(m_Machine, SIGNAL(afterWake(bool)), this, SLOT(onAfterWake(bool)));

    // 自定义屏保是有ffplay播放的，ffplay收到点击事件会主动退出，需要通知程序同步结束屏保
    connect(&m_ScreenController, &ScreenController::screenSaverExited, this,
            &ApplicationEventFilter::stopScreenSaverProgram);
    //关机按键按下时 ffplay如果在前台将无法收到按键信号，需要监听电源按键按下的信号，然后主动结束屏保
    connect(shutDownTool, &ShutDownTool::powerKeyPressed, this, &ApplicationEventFilter::stopScreenSaverProgram);

    m_Recorder.setKeyboard(m_Keyboard);
    m_Recorder.record();
}

ApplicationEventFilter::~ApplicationEventFilter()
{
    qDeleteAll(m_EventFilters);
}

bool ApplicationEventFilter::standby()
{
    if (!isStandby())
    {
#ifdef USE_ADMINVIEW
        m_AdminConfigModel->hideLoginDlg();
#endif
        onScreenControlTimersStoped();
        m_ScreenController.stopScreenSaver();
        setIsStandby(true);
    }
    return true;
}

bool ApplicationEventFilter::wake()
{
    if (isStandby())
    {
        onScreenControlTimersStarted();
        setIsStandby(false);
    }
    return true;
}

void ApplicationEventFilter::appendEventFilterController(IEventFilterController* controller)
{
    m_EventFilters.append(controller);
}

void ApplicationEventFilter::removeEventFilterController(IEventFilterController* controller)
{
    m_EventFilters.removeAll(controller);
}

bool ApplicationEventFilter::eventFilter(QObject* o, QEvent* e)
{
    QMouseEvent* event = dynamic_cast<QMouseEvent*>(e);

    /* 更新imagetile中的操作主图区标志位，解决在鼠标可见的情况下，且在外部存在hover效果的时候(比如处于非锁屏按钮的hover下)
       直接点击主图区带来的BUG(外部hover效果没有消失)*/
    if (event != nullptr && ApplicationInfo::instance().isCursorVisible())
    {
        m_ImageTile->setOutSideOfRenderPos(event->globalPos());
    }

    /*********************************Adjust Gain******************************/
    // 1、区域校验
    if (event != nullptr)
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            QWidget* localWidgetAt = QApplication::widgetAt(event->globalPos());
            if (localWidgetAt != nullptr)
            {
                foreach (QString str, m_AdjustGainArea)
                {
                    if (QString(localWidgetAt->metaObject()->className()) == str || localWidgetAt->objectName() == str)
                    {
                        m_StartAdjustGain = true;
                    }
                }
            }
        }
        else if (event->type() == QEvent::MouseButtonRelease)
        {
            m_StartAdjustGain = false;
            m_SonoParameters->setPV(BFPNames::GainLogoVisibleStr, false);
        }
    }

    // 2、Gain调节
    if (m_StartAdjustGain && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        foreach (QString str, m_AdjustGainArea)
        {
            if (!(str == "FreezeBarWidget"))
            {
                if (QString(o->metaObject()->className()) == str || o->objectName() == str)
                {
                    adjustGain(e);
                }
            }
        }
    }
    /*************************************End********************************/

    QLabel* lockscreenTipLabel = m_MainWIndow->getLockscreenTipLabel();
    FlexButton* lockScreenFlexBtn = m_MainWIndow->getLockScreenFlexBtn();

    if (QString(o->metaObject()->className()) == "MainWindowKBUnit")
    {
        if (e->type() == QEvent::Leave)
        {
            m_IsMainWindow = false;
        }
        else if (e->type() == QEvent::Enter || e->type() == QEvent::TouchBegin)
        {
            m_IsMainWindow = true;
        }
    }

    if (m_IsMainWindow)
    {
        if (e->type() == QEvent::Enter)
        {
            if (!ApplicationInfo::instance().isCursorVisible()) // 主界面光标不可见(grab情况下)禁止触发hover效果
            {
                return true;
            }
        }

        if (e->type() == QEvent::TouchBegin || e->type() == QEvent::TouchUpdate)
        {
            QWidget* pressWidget = QApplication::widgetAt(QCursor::pos());
            if (pressWidget != nullptr)
            {
                if (m_BlockTouch)
                {
                    return true;
                }
                if (Setting::instance().defaults().getLockScreen() && pressWidget->objectName() != "lockScreenBtn")
                {
                    Util::changeQssWidgetProperty(lockscreenTipLabel, "state", "warn");
                    Util::changeQssWidgetProperty(lockScreenFlexBtn, "state", "warn");
                    return true;
                }
            }
        }
        else if (e->type() == QEvent::TouchEnd)
        {
            if (m_BlockTouch)
            {
                return true;
            }
            if (Setting::instance().defaults().getLockScreen())
            {
                if (lockscreenTipLabel->property("state") == "warn")
                {
                    Util::changeQssWidgetProperty(lockscreenTipLabel, "state", "normal");
                }

                if (lockScreenFlexBtn->property("state") == "warn")
                {
                    Util::changeQssWidgetProperty(lockScreenFlexBtn, "state", "on");
                }
            }
        }
    }

    if (!m_IsPreviewed && !isSupportScreenSaver() && !isSupportScreenOff() && !isSupportStandby())
    {
        return QObject::eventFilter(o, e);
    }
    bool runEventFilterFlag = true;
    QWindowList windows = qGuiApp->allWindows();
    foreach (QWindow* const w, windows)
    {
        if ((quintptr)w == (quintptr)o)
        {
            runEventFilterFlag = false;
        }
    }
    if (runEventFilterFlag)
    {
        foreach (IEventFilterController* controller, m_EventFilters)
        {
            if (controller->eventFilter(o, e))
            {
                return true;
            }
        }
    }

    bool isDone = screenStateEventFilter(e); //屏幕控制状态下响应恢复屏幕后返回true,阻止后续功能

    switch (e->type())
    {
    case QEvent::MouseMove:
    {
        QMouseEvent* mouseEvent = static_cast<QMouseEvent*>(e);

        if (!m_mouseOnTouch && mouseEvent->globalPos() != m_MouseOnOldPos)
        {
            QScreen* screen = QGuiApplication::screenAt(mouseEvent->globalPos());
            bool flag = screen == nullptr ? false : QGuiApplication::screens().indexOf(screen) == 0;
            if (flag)
            {
                m_MouseOnOldPos = mouseEvent->globalPos();
            }
            else
            {
                QPoint pos = mouseEvent->globalPos() - m_MouseOnOldPos;
                //                qDebug() << PRETTY_FUNCTION << mouseEvent << mouseEvent->globalPos() <<
                //                m_MouseOnOldPos << pos;
                if (abs(pos.y()) < 200)
                {
                    QCursor::setPos(mouseEvent->globalPos().x(),
                                    QGuiApplication::primaryScreen()->geometry().bottom() - 10);
                }
            }
        }
    }
    break;
    case QEvent::KeyPress:
    {
        QKeyEvent* keyEvent = static_cast<QKeyEvent*>(e);
        if ((keyEvent->modifiers() == Qt::AltModifier) && keyEvent->key() == Qt::Key_R) // replay
        {
            m_Recorder.replay(1.0f);
            return true;
        }
        else if ((keyEvent->modifiers() == Qt::AltModifier) && keyEvent->key() == Qt::Key_P) // stop
        {
            m_Recorder.stop();
            return true;
        }
        else if ((keyEvent->modifiers() == Qt::AltModifier) && keyEvent->key() == Qt::Key_L) // load
        {
            m_Recorder.load("OperateLogger.txt");
            return true;
        }
    }
    break;
    case QEvent::MouseButtonPress:
    {
        QMouseEvent* mouseEvent = static_cast<QMouseEvent*>(e);
        //        qDebug() << PRETTY_FUNCTION << mouseEvent << o << mouseEvent->globalPos() <<
        //        desktop->screenNumber(mouseEvent->globalPos());
        QScreen* screen = QGuiApplication::screenAt(mouseEvent->globalPos());
        bool flag = screen == nullptr ? false : QGuiApplication::screens().indexOf(screen) == 1;

        if (flag && !m_mouseOnTouch)
        {
            m_mouseOnTouch = true;
            m_IsCursorVisible = ApplicationInfo::instance().isCursorVisible();
            ApplicationInfo::instance().setCursorVisible(false, false);
        }
    }
    break;
    case QEvent::MouseButtonRelease:
    {
        if (m_mouseOnTouch)
        {
            m_mouseOnTouch = false;
            QCursor::setPos(m_MouseOnOldPos);
            ApplicationInfo::instance().setCursorVisible(m_IsCursorVisible, false);
        }
    }
    break;
    default:
        break;
    }

    if (isDone)
    {
        return true;
    }
    return QObject::eventFilter(o, e);
}

bool ApplicationEventFilter::screenStateEventFilter(QEvent* e)
{
    if (e == nullptr)
    {
        return false;
    }

    bool isDone = false; //屏幕控制状态下响应恢复屏幕后返回true,阻止后续功能

    switch (e->type())
    {
    case QEvent::MouseMove:
    {
        QMouseEvent* mouseEvent = static_cast<QMouseEvent*>(e);
        if (m_ScreenState != None)
        {
            if (!m_OldPos.isNull())
            {
                QPoint pos = mouseEvent->globalPos() - m_OldPos;
                if (pos.manhattanLength() > 3)
                {
                    respondEvent();
                    isDone = true;
                }
            }
        }

        if (mouseEvent->globalPos() != m_OldPos)
        {
            if (m_ScreenState == None)
            {
                m_OldPos = QPoint();
                restartTimers();
            }
            else
            {
                m_OldPos = mouseEvent->globalPos();
            }
        }
    }
    break;
    case QEvent::KeyPress:
    //[Apple][BUG:57678] 【3D UI】机器第一次无法进入屏保界面
    // 1、解决方案
    // a、bug必现路径：在屏保设置界面，调出软键盘，点击软键盘的按钮，再点击屏保的Preview按钮（触屏点击第一次闪退，鼠标点击一直闪退）；
    // b、bug描述中出现一闪而过的情况，实际上是屏保开始了然后立马又结束了。结束的原因是响应了keyboardbutton发送的QEvent::MouseButtonRelease事件；
    // c、键盘为了处理之前的bug，在QApplication::focusChanged的时候，会触发QEvent::MouseButtonRelease；
    // d、在ApplicationEventFilter::screenStateEventFilter中将QEvent::MouseButtonRelease的事件响应换成QEvent::MouseButtonPress
    // 2、影响范围:屏保的鼠标按键关闭
    // 3、Test Case:屏保的鼠标按键关闭
    case QEvent::MouseButtonPress:
    case QEvent::MouseButtonDblClick:
    case QEvent::Wheel:
    {
        if (m_ScreenState != None)
        {
            respondEvent();
            isDone = true;
        }
        restartTimers();
    }
    break;

    default:
        break;
    }

    return isDone;
}

void ApplicationEventFilter::respondEvent()
{
    if (m_ScreenState == ScreenSaver)
    {
        m_ScreenController.stopScreenSaver();
    }
    else if (m_ScreenState == ScreenOff)
    {
        m_ScreenController.stopScreenSaver();
        if (!isStandby())
        {
            m_ScreenController.openScreen();
            sendControlTable(); //新的关屏方式唤醒后需要重新下发一下控制字,解决vga输出错乱问题
        }
    }

    if (m_ScreenState != None)
    {
        m_ScreenState = None;

        if (m_IsCursorVisible)
        {
            ApplicationInfo::instance().setCursorVisible(true);
        }

        if (m_CursorMouseActionsModel->isAvailable())
        {
            m_CursorMouseActionsModel->startGrab();
        }
#ifdef USE_ADMINVIEW
        m_AdminConfigModel->showLoginDlg();
#endif
    }
    m_IsPreviewed = false;
    m_PictureIndex = Setting::instance().defaults().screenSaverPictureIndex();
}

void ApplicationEventFilter::restartTimers()
{
    m_ScreenSaverTimer.restart(isSupportScreenSaver());
    m_ScreenOffTimer.restart(isSupportScreenOff());
    m_StandbyTimer.restart(isSupportStandby());
    FrameControl::instance().resetStartTime();
}

void ApplicationEventFilter::screenSaverExec()
{
    m_ScreenSaverTimer.stop();
#ifdef USE_ADMINVIEW
    m_AdminConfigModel->hideLoginDlg();
#endif
    if (m_CursorMouseActionsModel->isAvailable())
    {
        m_CursorMouseActionsModel->stopGrab();
    }

    m_IsCursorVisible = ApplicationInfo::instance().isCursorVisible();
    if (m_IsCursorVisible)
    {
        ApplicationInfo::instance().setCursorVisible(false);
    }

    m_ScreenController.startScreenSaver(m_PictureIndex);
}

bool ApplicationEventFilter::isSupportScreenSaver() const
{
    return Setting::instance().defaults().isSupportScreenSaver();
}

bool ApplicationEventFilter::isSupportScreenOff() const
{
    return Setting::instance().defaults().isSupportScreenOff();
}

bool ApplicationEventFilter::isSupportStandby() const
{
    return Setting::instance().defaults().isSupportStandby();
}

int ApplicationEventFilter::screenSaverInterval() const
{
    return Setting::instance().defaults().screenSaverInterval();
}

int ApplicationEventFilter::screenOffInterval() const
{
    return Setting::instance().defaults().screenOffInterval();
}

int ApplicationEventFilter::standbyInterval() const
{
    return Setting::instance().defaults().standbyInterval();
}

bool ApplicationEventFilter::isIntervalEqual() const
{
    if (isSupportScreenSaver() && isSupportScreenOff() && screenSaverInterval() == screenOffInterval())
    {
        return true;
    }
    return false;
}

bool ApplicationEventFilter::isNeedExecScreenSaver() const
{
    if (isSupportScreenSaver())
    {
        if (isNeedExecScreenOff())
        {
            if (screenSaverInterval() < screenOffInterval())
            {
                return true;
            }
        }
        else
        {
            if (!isSupportStandby() || screenSaverInterval() < standbyInterval())
            {
                return true;
            }
        }
    }
    return false;
}

bool ApplicationEventFilter::isNeedExecScreenOff() const
{
    if (isSupportScreenOff())
    {
        if (FrameControl::instance().isImageStop() && (!isSupportStandby() || screenOffInterval() < standbyInterval()))
        {
            return true;
        }
    }
    return false;
}

void ApplicationEventFilter::enterComment(bool* isBlock)
{
    AbstractState* currentState = m_StateManager->currentState();
    if (currentState != NULL && currentState->name() != StateEventNames::CommentState)
    {
        // printf("\n ### ApplicationEventFilter::enterComment...\n");
        m_StateManager->postEvent(StateEventNames::Comment);
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);

        //成功进入comment状态之后再进行下面判断
        if (m_StateManager->currentState()->name() == StateEventNames::CommentState)
        {
            // comment下,Move状态直接输出字符,select状态不输出字符
            CommentGlyphsControl* control =
                GlyphsControlManager::instance().getChildGlyphsControl<CommentGlyphsControl>(
                    GlyphsCtl::CommentGlyphsType);

            CommentGlyphs* comment = dynamic_cast<CommentGlyphs*>(control->activeGlyphs());
            if (comment == NULL)
            {
                if (isBlock != NULL)
                {
                    *isBlock = true;
                }
            }
        }
    }
}

void ApplicationEventFilter::adjustGain(QEvent* e)
{
    QMouseEvent* mouseEvent = dynamic_cast<QMouseEvent*>(e);
    static QPoint pressedPt = QPoint(-1, -1);
    static bool isPressed = false;
    if (e->type() == QEvent::MouseButtonPress)
    {
        pressedPt = mouseEvent->pos();
        isPressed = true;
    }
    else if (e->type() == QEvent::MouseButtonRelease)
    {
        pressedPt = QPoint(-1, -1);
        isPressed = false;
    }
    else if (e->type() == QEvent::MouseMove)
    {
        if (isPressed)
        {
            if (pressedPt != QPoint(-1, -1))
            {
                int step = ModelConfig::instance()
                               .value(ModelConfig::GainAdjustStep, 1)
                               .toInt(); // Gain调节步进值，控制调节灵敏度

                QPoint offset = mouseEvent->pos() - pressedPt;
                static int offsetCount = 0;

                if (qAbs(offset.x()) > qAbs(offset.y())) // 水平方向
                {
                    offsetCount += offset.x();

                    if ((offsetCount > 0 && offset.x() < 0) || (offsetCount < 0 && offset.x() > 0))
                    {
                        offsetCount = 0;
                    }
                }

                if (qAbs(offsetCount) >= step) // 当产生的偏移量大于step时触发一次调节
                {
                    GainGeneralTool* command =
                        dynamic_cast<GainGeneralTool*>(m_ToolsFacade->command(ToolNames::GainGeneralStr));

                    int count = qRound(qreal(qAbs(offsetCount)) / step);
                    for (int index = 0; index < count; index++)
                    {
                        QVariantCommandArgs boolArgs;
                        boolArgs.setType(COMMAND_TYPE_BOOL);
                        boolArgs.setArg(offsetCount > 0);
                        command->setArgs(&boolArgs);
                        command->run();
                    }
                    m_SonoParameters->setPV(BFPNames::GainLogoVisibleStr, true);
                    offsetCount = 0;
                    if (m_SonoParameters->pBV(BFPNames::FreezeStr) && !command->isPostProcessTool())
                    {
                        return;
                    }
                }

                pressedPt = mouseEvent->pos();
            }
        }
    }
}

void ApplicationEventFilter::onSettingChanged(const QString& property)
{
    if (property == "IsSupportScreenSaver" || property == "ScreenSaverInterval")
    {
        m_ScreenSaverTimer.reset(isSupportScreenSaver(), screenSaverInterval());
    }

    if (property == "IsSupportScreenOff" || property == "ScreenOffInterval")
    {
        m_ScreenOffTimer.reset(isSupportScreenOff(), screenOffInterval());
    }

    if (property == "IsSupportStandby" || property == "StandbyInterval")
    {
        m_StandbyTimer.reset(isSupportStandby(), standbyInterval());
    }

    // 当屏保选项修改后，需要更新m_PictureIndex的值
    if (property == "ScreenSaverPictureIndex")
    {
        m_PictureIndex = Setting::instance().defaults().screenSaverPictureIndex();
    }
}

void ApplicationEventFilter::callScreenSaverProgram()
{
    if (!isNeedExecScreenSaver())
    {
        return;
    }

    if (!m_BeamFormer->isFrozen())
    {
        m_StateManager->postEvent(StateEventNames::Freeze);
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        //等待冻结结束,避免冻结过程中设置鼠标位置引起的mousemove事件导致结束屏保
        //同时也需要等待冻结结束后再释放鼠标控制权
        QTimer::singleShot(200, this, SLOT(doScreenSave()));
    }
    else
    {
        //不需要冻结的话直接控制
        doScreenSave();
    }
}

void ApplicationEventFilter::callScreenOffProgram()
{
    if (!isNeedExecScreenOff())
    {
        return;
    }

    if (m_ScreenSaverTimer.isActive())
    {
        m_ScreenSaverTimer.stop();
    }

    if (!m_BeamFormer->isFrozen())
    {
        m_StateManager->postEvent(StateEventNames::Freeze);
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        QTimer::singleShot(200, this, SLOT(doScreenOff()));
    }
    else
    {
        doScreenOff();
    }
}

void ApplicationEventFilter::callStandbyProgram()
{
    log()->info(QString("%1  standyby").arg(PRETTY_FUNCTION));
    if (m_ScreenSaverTimer.isActive())
    {
        m_ScreenSaverTimer.stop();
    }
    if (m_ScreenOffTimer.isActive())
    {
        m_ScreenOffTimer.stop();
    }

    if (!m_BeamFormer->isFrozen())
    {
        m_StateManager->postEvent(StateEventNames::Freeze);
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        QTimer::singleShot(200, this, SLOT(doStandby()));
    }
    else
    {
        doStandby();
    }
}

void ApplicationEventFilter::doScreenSave()
{
    m_ScreenState = ScreenSaver;
    screenSaverExec();
}

void ApplicationEventFilter::doScreenOff()
{
    m_ScreenOffTimer.stop();
    m_ScreenController.closeScreen();

    //关闭屏幕后打开screensaver是为了能响应到全屏的鼠标事件
    if (m_ScreenState != ScreenSaver)
    {
        //这里m_ScreenState要设置一下,否则exec阻塞后无法设置ScreenOff
        m_ScreenState = ScreenOff;
        screenSaverExec();
    }
    else
    {
        m_ScreenState = ScreenOff;
    }
}

void ApplicationEventFilter::doStandby()
{
    m_StandbyTimer.stop();

    //执行休眠
    m_StandbyController.doStandby();
}

void ApplicationEventFilter::onKeyPressed(bool* isBlock)
{
    if (m_ScreenState != None)
    {
        respondEvent();
        if (isBlock != NULL)
        {
            *isBlock = true; //屏幕控制状态下响应按键恢复屏幕后设置true,阻止后续功能
        }
    }
    restartTimers();
}

void ApplicationEventFilter::onTgcDataReceived()
{
    if (m_ScreenState != None)
    {
        respondEvent();
    }
    restartTimers();
}

void ApplicationEventFilter::onScreenControlTimersStoped()
{
    m_ScreenSaverTimer.stop();
    m_ScreenOffTimer.stop();
    m_StandbyTimer.stop();
}

void ApplicationEventFilter::onScreenControlTimersStarted()
{
    m_ScreenSaverTimer.startAfterUpdate(isSupportScreenSaver());
    m_ScreenOffTimer.startAfterUpdate(isSupportScreenOff());
    m_StandbyTimer.startAfterUpdate(isSupportStandby());
}

void ApplicationEventFilter::onScreenSaverPreviewed(bool isPreviewed, int pictureIndex)
{
    m_IsPreviewed = isPreviewed;
    m_PictureIndex = pictureIndex;
    //如果在不支持屏保和关屏的情况下点击预览,之前的m_OldPos是不记录的,
    //导致进入预览后鼠标稍微动一下就退出预览,所以在进入预览前先纪录m_OldPos
    if (!isSupportScreenSaver() && !isSupportScreenOff())
    {
        m_OldPos = QCursor::pos();
    }
    doScreenSave();
}

void ApplicationEventFilter::onCharKeyPressed(int keyCode, int modifiers, bool* isBlock)
{
    if (m_ScreenState != None)
    {
        respondEvent();
        if (isBlock != NULL)
        {
            *isBlock = true;
        }
    }
    else
    {
        //如果此时在可输入状态中,不进入comment状态.
        if (!UiUtil::isEditable())
        {
            // preset mode 屏蔽该功能，方便presetmodetool中输入字符搜索parameter
            if (!AppSetting::isPresetMode())
            {
                if ((keyCode >= Qt::Key_A && keyCode <= Qt::Key_Z) ||
                    (keyCode >= Qt::Key_A + 32 && keyCode <= Qt::Key_Z + 32))
                {
                    //避免与快捷键冲突
                    if ((modifiers & Qt::CTRL) == 0)
                    {
                        // printf("\n###ApplicationEventFilter::onCharKeyPressed modifiers = %d ...\n", modifiers);
                        enterComment(isBlock);
                    }
                }
            }
        }
    }
    restartTimers();
}

void ApplicationEventFilter::sendControlTable()
{
    IControlTable* ct = m_BeamFormer->getControlTable();
    ControlTableSyncSender cs(ct);
    ct->send();
}

void ApplicationEventFilter::onAfterWake(bool successful)
{
    if (successful)
    {
#ifdef USE_ADMINVIEW
        m_AdminConfigModel->showLoginDlg();
#endif
    }
}

void ApplicationEventFilter::onBlockTouch(bool needBlock)
{
    m_BlockTouch = needBlock;
}

void ApplicationEventFilter::hideVirtualKeyboardStateOnStandBy()
{
    const WindowInfo windowInfo = ApplicationInfo::instance().activeCursorWindow();
    QWidget* w = windowInfo.window();
    if (windowInfo.type() != WindowInfo::FullScreen && w != nullptr)
    {
        QDialog* dialog = dynamic_cast<QDialog*>(w);
        if (dialog != nullptr && dialog->isModal())
        {
            dialog->reject();
        }
    }
}

void ApplicationEventFilter::stopScreenSaverProgram()
{
    if (m_ScreenState != None)
    {
        respondEvent();
    }
    restartTimers();
}
