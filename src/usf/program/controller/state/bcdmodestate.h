#ifndef BCDMODESTATE_H
#define BCDMODESTATE_H
#include "usfprogramcontroller_global.h"

#include "basedrelatedmodestate.h"

class USF_PROGRAM_CONTROLLER_EXPORT BCDModeState : public BaseDRelatedModeState
{
    Q_OBJECT
public:
    typedef BaseDRelatedModeState SuperType;
    explicit BCDModeState(MainModuleContext* context, IUltrasoundView* ultrasoundView, IImageManager* imageManager,
                          QObject* parent = 0);
};

class USF_PROGRAM_CONTROLLER_EXPORT BCCWModeState : public BaseDRelatedModeState
{
    Q_OBJECT
public:
    explicit BCCWModeState(MainModuleContext* context, IUltrasoundView* ultrasoundView, IImageManager* imageManager,
                           QObject* parent = 0);

public slots:
    virtual void onEntry();
    virtual void onExit();
};

#endif // BCDMODESTATE_H
