#include "bdmodestate.h"
#include "mainmodulecontext.h"
#include "menunames.h"
#include "toolnames.h"
#ifdef USE_BATTERY_COUNT_2
#include "batterydevice_2battery.h"
#else
#include "batterydevice_1battery.h"
#endif
#include "bfpnames.h"
#include "iimagemanager.h"
#include "stateeventnames.h"
#include "toolnames.h"
#include <QTimer>
#include <QThread>
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, BDModeState)

BDModeState::BDModeState(MainModuleContext* context, IUltrasoundView* ultrasoundView, IImageManager* imageManager,
                         QObject* parent)
    : BaseDRelatedModeState(context, ultrasoundView, imageManager)
{
    m_MouseCommandName = ToolNames::BDMouseStr;
    m_StateEventNames = StateEventNames::BDModeState;
    m_SystemScanMode = SystemScanModeBPW;
}

BCWModeState::BCWModeState(MainModuleContext* context, IUltrasoundView* ultrasoundView, IImageManager* imageManager,
                           QObject* parent)
    : BaseDRelatedModeState(context, ultrasoundView, imageManager, parent)
{
    m_menuName = MenuNames::CWDMenuStr;
    m_MouseCommandName = ToolNames::BCWPreMouseStr;
    m_StateEventNames = StateEventNames::BCWModeState;
    m_SystemScanMode = SystemScanModeCWD;
}

void BCWModeState::onEntry()
{
    m_ImageManager->setSafeVolatile2CTNumber(DEFAULT_SAFE_VOLTAGE);
    BaseDRelatedModeState::onEntry();

    if (m_ImageManager->checkCWVolatile(DEFAULT_SAFE_VOLTAGE, CHECK_VELTAGE_MAX_TIMES))
    {

        m_ImageManager->adjustParam(BFPNames::CW_HV_SafeStr, 1);
    }
    //    else
    //    {
    //              TODO 与产品进行讨论
    //    }
}

void BCWModeState::onExit()
{
    m_ImageManager->adjustParam(BFPNames::CW_HV_SafeStr, 0);
    BaseDRelatedModeState::onExit();
}
