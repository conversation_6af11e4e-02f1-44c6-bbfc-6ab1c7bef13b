#ifndef BDMODESTATE_H
#define BDMODESTATE_H
#include "usfprogramcontroller_global.h"

#include "basedrelatedmodestate.h"

class QTimer;

class USF_PROGRAM_CONTROLLER_EXPORT BDModeState : public BaseDRelatedModeState
{
    Q_OBJECT
public:
    typedef BaseDRelatedModeState SuperType;
    explicit BDModeState(MainModuleContext* context, IUltrasoundView* ultrasoundView, IImageManager* imageManager,
                         QObject* parent = 0);
};

class USF_PROGRAM_CONTROLLER_EXPORT BCWModeState : public BaseDRelatedModeState
{
    Q_OBJECT
public:
    explicit BCWModeState(MainModuleContext* context, IUltrasoundView* ultrasoundView, IImageManager* imageManager,
                          QObject* parent = 0);
public slots:
    virtual void onEntry();
    virtual void onExit();

private:
};

#endif // BDMODESTATE_H
