#ifndef BASEDRELATEDMODESTATE_H
#define BASEDRELATEDMODESTATE_H
#include "usfprogramcontroller_global.h"

#include "basemousestate.h"
#include "infostruct.h"
#include <QVariant>

#define CHECK_VELTAGE_MAX_TIMES 50
#define CHECK_VELTAGE_INTERVAL_MILSEC 200
#define DEFAULT_SAFE_VOLTAGE 5.00

class IUltrasoundView;
class IImageManager;
class IPeripheralManager;
class USF_PROGRAM_CONTROLLER_EXPORT BaseDRelatedModeState : public BaseMouseState
{
    Q_OBJECT
public:
    typedef BaseMouseState SuperType;
    explicit BaseDRelatedModeState(MainModuleContext* context, IUltrasoundView* ultrasoundView,
                                   IImageManager* imageManager, QObject* parent = 0);

protected:
    void connectCommandSignals();
signals:

public slots:
    virtual void onEntry();
    virtual void onExit();
    virtual void onProcessParams(QString& prams);
private slots:
    void onRightButtonPressed();

protected:
    QString m_StateEventNames;
    QString m_menuName;
    SystemScanMode m_SystemScanMode;

protected:
    IUltrasoundView* m_UltrasoundView;
    IImageManager* m_ImageManager;
};

#endif // BASEDRELATEDMODESTATE_H
