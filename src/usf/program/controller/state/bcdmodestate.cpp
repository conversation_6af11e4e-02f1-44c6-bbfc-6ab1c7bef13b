#include "bcdmodestate.h"
#include "mainmodulecontext.h"
#include "toolnames.h"
#include "lightcontroller.h"
#include "menunames.h"
#include "menutools.h"
#include "toolsfactory.h"
#include "toolnames.h"
#include "stateeventnames.h"
#include "functionbuttoncontroller.h"
#include "bfpnames.h"
#include "iimagemanager.h"

BCDModeState::BCDModeState(MainModuleContext* context, IUltrasoundView* ultrasoundView, IImageManager* imageManager,
                           QObject* parent)
    : BaseDRelatedModeState(context, ultrasoundView, imageManager, parent)
{
    m_MouseCommandName = ToolNames::BCDMouseStr;
    m_StateEventNames = StateEventNames::BCDModeState;
    m_SystemScanMode = SystemScanModeColorPW;
}

BCCWModeState::BCCWModeState(MainModuleContext* context, IUltrasoundView* ultrasoundView, IImageManager* imageManager,
                             QObject* parent)
    : BaseDRelatedModeState(context, ultrasoundView, imageManager, parent)
{
    // TODO BCCWPreMouseStr 不对 暂改成  ToolNames::BCDMouseStr 试试
    m_MouseCommandName = ToolNames::BCDMouseStr;
    m_StateEventNames = StateEventNames::BCCWModeState;
    m_menuName = MenuNames::CWDMenuStr;
    m_SystemScanMode = SystemScanModeCWDColorDoppler;
}

void BCCWModeState::onEntry()
{
    m_ImageManager->setSafeVolatile2CTNumber(DEFAULT_SAFE_VOLTAGE);
    BaseDRelatedModeState::onEntry();

    if (m_ImageManager->checkCWVolatile(DEFAULT_SAFE_VOLTAGE, CHECK_VELTAGE_MAX_TIMES))
    {

        m_ImageManager->adjustParam(BFPNames::CW_HV_SafeStr, 1);
    }
    //    else
    //    {
    //              TODO 与产品进行讨论
    //    }
}

void BCCWModeState::onExit()
{
    m_ImageManager->adjustParam(BFPNames::CW_HV_SafeStr, 0);
    BaseDRelatedModeState::onExit();
}
