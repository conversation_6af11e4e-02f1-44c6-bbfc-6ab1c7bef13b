#include "ultrasoundview.h"
#include "appsetting.h"
#include "basebkwidget.h"
#include "basedialog.h"
#include "iperipheralmanager.h"
#include "basewidget.h"
#include "bfhwinfomodel.h"
#include "bfpnames.h"
#include "dopplerviewercontext.h"
#include "iexammanager.h"
#include "imarkmanager.h"
#include "iimagemanager.h"
#include "imainwindow.h"
#include "imainwindowfactory.h"
#include "keyboardcontrol.h"
#include "mainwindowfactorycreator.h"
#include "modelconfig.h"
#include "modeluiconfig.h"
#include "resource.h"
#include "abstractscreendisplaystatusmodel.h"
#include "screenshots.h"
#include "sonoparametersclientbase.h"
#include "tcpserver.h"
#include "uiutil.h"
#include "util.h"
#include "zoomonproxy.h"
#include "lefttopmenuwidgetcontainer.h"
#include "dopplerthetaadjustitem.h"
#include "fetalheaddirectionwidget.h"
#include "fetalheadposwidget.h"
#include "mainwindowmiddledownwidget.h"
#include "mainwindowtopwidget.h"
#include "measureinputwidget.h"
#include "measurementviewdispatcher.h"
#include "measureresultlistwidget.h"
#include "menupanelmodel.h"
#include "sonozoomwidget.h"
#include "titlebarwidget.h"

#include "colorbarwidget.h"
#include "iimagewidget.h"
#include "imagetile.h"
#include "mainwindowtopinfomodel.h"
#include "pimplglyphswidget.h"
#include "iprobepresetmodel.h"
#include "isonoparaswidget.h"

#include "chisonultrasoundcontext.h"
#include "fpsinfowidget.h"
#include "framecontrol.h"
#include "ibeamformer.h"
#include "sonoparameters.h"

#include "imageskimmanager.h"
#include "measurementmenu.h"

#include "cursormouseactionsmodel.h"

#include "ipatientworkflow.h"
#include "setting.h"
#include "sonoparamodelsfileprovider.h"

#include "freezebarwidget.h"
#include "mainwindowtopinfomodel.h"
#include "navigationwidget.h"
#include "progresssliderwidget.h"
#include "storeprogressmoviewidget.h"
#include "systemhintwidget.h"
#include "systemstatuswidget.h"
#include "systemtbstatuswidget.h"
#include "workstatuswidget.h"

#include "commentmenu.h"
#include "icommentmenumodel.h"
#include "exammodemenumodel.h"
#include "ipresetmodel.h"
#include "probeselectiondispatcher.h"
#include "iquickcommentmodel.h"
#include "quickcommentpopupwidget.h"
#include "screenshots.h"
#include "systemtipmodel.h"

#include "commentglyphscontrol.h"
#include "glyphscontrolmanager.h"
#include "reportwidget.h"

#include "configurationwidget.h"
#include "ilicenseinitializer.h"
#include "reservedsettingmanager.h"
#include "isystemstatusmodel.h"

#include "leftmenuwidget.h"
#include "quickmeasurecaptionsloop.h"
#include "sninput.h"
#include "stressechomodel.h"
#include "stressechowidgetmanager.h"
#include "stressechoworkwidget.h"

#include "bodymarkglyphscontrol.h"
#include "bodymarkviewmodel.h"
#include "controlpanel.h"
#include "functionbuttoncontroller.h"
#include "glyphscontrolmanager.h"
#include "iprobeinfomodel.h"
#include "lgcregulatorwidget.h"
#include "lightcontroller.h"
#include "mainwindowkbunit.h"
#include "measurecontext.h"
#include "menucontroller.h"
#include "menunames.h"
#include "rightmenuwidgetcontainer.h"
#include "sonohelpcontroller.h"
#include "sonoparaswidgetcontroller.h"
#include "stateeventnames.h"
#include "thumbnaildialog.h"

#include "arrowglyphscontrol.h"
#include "istateswitchprocessor.h"
#include "measurestaterecorder.h"
#include "ipatientworkflowmodel.h"
#include "sonozoomcontrol.h"
#include "uistateswitchprocessorfactory.h"

#include "autotracemeasureglyphscontrol.h"
#include "realtimeautotracemeasureglyphscontrol.h"

#include "cinelooper.h"
#include "diskselectionwidget.h"
#include "generalworkflowfunction.h"
#include "icinelooper.h"
#include "idiskdevice.h"
#include "reportfileloadhelper.h"
#include "abstractmachine.h"
#include "istatemanager.h"
#include "abstractstate.h"

#include "probeorganismselectionmodel.h"
#include "organismprobewidget.h"

#include "reportprintjob.h"
#include "licenseinitializer.h"
#include "glyphscontrolcreator.h"

#include "iglyphstoremanager.h"
#include "iscreenmeasurecontroller.h"
#include "storeddata.h"
#include "ibufferstoremanager.h"

#include "middledownwidgetcontainer.h"
#include "renderwidgetcontainer.h"
#include "imagewidget.h"
#include "measurecontext.h"
#include "adminconfigmodel.h"
#include "patientworkflowmodel.h"

#include "splashwindow.h"
#include "isplashwindowfactory.h"
#include "splashwindowfactorycreator.h"

#include "exammodepresethandlerfactory.h"
#include "presetutilitytool.h"
#include "iexammodepresetdatahandler.h"
#include "tgcmenuwidgetcontainer.h"
#include "efastmenuwidget.h"
#include "efastbrowserwidget.h"
#include "iefastmanager.h"
#include "imultiscreensynccontroller.h"
#include "qtkeyprocessor.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, UltrasoundView)
UltrasoundView::UltrasoundView(DopplerViewerContext* context, QObject* parent)
    : IUltrasoundView(parent)
    , m_PeripheralManager(NULL)
    , m_ExamManager(NULL)
    , m_MarkManager(NULL)
    , m_ImageManager(NULL)
    , m_ViewerContext(context)
    , m_ToolsFacade(NULL)
    , m_StateManager(NULL)
    , m_MainWindow(NULL)
    , m_ZoomOnProxy(NULL)
    , m_BFHWInfoModel(NULL)
    , m_MainWindowTopInfoModel(NULL)
    , m_CursorMouseActionsModel(NULL)
    , m_SystemStatusTipAction(NULL)
    , m_SystemTipModel(NULL)
    , m_MeasureInputWidget(NULL)
    , m_SonoZoomWidget(NULL)
    , m_SonoZoomControl(NULL)
    , m_FetalHeadPosWidget(NULL)
    , m_FetalHeadDirectionWidget(NULL)
    , m_QuickCommentPopupWidget(NULL)
    , m_StressEchoWidgetManager(NULL)
    , m_SonoParasWidgetController(NULL)
    , m_SonoHelpController(NULL)
    , m_GainLogoVisible(false)
    , m_StateSwitchProcessor(NULL)
    , m_DiskDevice(NULL)
    , m_ColorMapManager(NULL)
    , m_ProbeSelectionModel(NULL)
    , m_ProbeSelectionWidget(NULL)
    , m_SystemInfo(NULL)
    , m_LicenseInitializer(NULL)
    , m_GlyphsControlCreator(NULL)
    , m_StressEchoModel(NULL)
    , m_AdminConfigModel(NULL)
    , m_PatientWorkflowModel(NULL)
    , m_TGCMenuWidgetContainer(NULL)
{
}

UltrasoundView::~UltrasoundView()
{
    Util::SafeDeletePtr(m_ViewerContext);
    Util::SafeDeletePtr(m_MainWindow);
    Util::SafeDeletePtr(m_ZoomOnProxy);
    Util::SafeDeletePtr(m_BFHWInfoModel);
    Util::SafeDeletePtr(m_CursorMouseActionsModel);
    Util::SafeDeletePtr(m_MainWindowTopInfoModel);
    Util::SafeDeletePtr(m_SystemStatusTipAction);
    Util::SafeDeletePtr(m_SystemTipModel);
    Util::SafeDeletePtr(m_MeasureInputWidget);
    // Util::SafeDeletePtr(m_SonoZoomWidget);  // 由QT对象树自动析构
    Util::SafeDeletePtr(m_SonoZoomControl);
    Util::SafeDeletePtr(m_FetalHeadPosWidget);
    Util::SafeDeletePtr(m_FetalHeadDirectionWidget);
    Util::SafeDeletePtr(m_QuickCommentPopupWidget);
    for (int i = 0; i < m_QuickMeasureCaptionsLoops.size(); i++)
    {
        QuickMeasureCaptionsLoop* loop = m_QuickMeasureCaptionsLoops.at(i);
        Util::SafeDeletePtr(loop);
    }
    // Util::SafeDeletePtr(m_StressEchoWidgetManager);
    // Util::SafeDeletePtr(m_SonoParasWidgetController);
    // //m_SonoParasWidgetController由父对象树管理析构，此处无需显示处理
    Util::SafeDeletePtr(m_StateSwitchProcessor);
    Util::SafeDeletePtr(m_ProbeSelectionModel);
    Util::SafeDeletePtr(m_LicenseInitializer);
    Util::SafeDeletePtr(m_GlyphsControlCreator);
    Util::SafeDeletePtr(m_AdminConfigModel);
    Util::SafeDeletePtr(m_TGCMenuWidgetContainer);
}

void UltrasoundView::handleEvent(UEventType event_name, const USFVariant& variant)
{
    if (event_name == UEventType::FUN_REMOVEIMAGEALLITEMS)
    {
        removeImageTileOverlayAllItems();
    }
    else if (event_name == UEventType::FUN_SETMEASUREMENTMENUTFETUS)
    {
        int menutFetus = variant.toInt();
        setMeasurementMenutFetus(menutFetus);
    }
    else if (event_name == UEventType::FUN_UPDATEIMAGECLIPWIDGET)
    {
        QString path = variant.toStdString().c_str();
        updateImageClipWidget(path);
    }
    else if (event_name == UEventType::FUN_CLEARIMAGECLIPWIDGET)
    {
        clearImageClipWidget();
    }
    else if (event_name == UEventType::FUN_UPDATEIMAGECLIPWIDGETIMAGELABLE)
    {
        int index = variant.toInt();
        updateImageClipWidgetImageLable(index);
    }
    else if (event_name == UEventType::FUN_SETLEFTMENUWIDGETVISIBLE)
    {
        bool visible = variant.toBool();
        setLeftMenuWidgetVisible(visible);
    }
    else if (event_name == UEventType::FUN_SETBOTTOMMENUWIDGETVISIBLE)
    {
        bool visible = variant.toBool();
        setBottomMenuWidgetVisible(visible);
    }
    else if (event_name == UEventType::FUN_BUFFERSTOREONLOADED)
    {
        bufferStoreOnLoaded();
    }
    else if (event_name == UEventType::FUN_BUFFERSTORELOADSTOREDDATA)
    {
        StoredData* storedData = static_cast<StoredData*>(variant.toCustomType());
        bufferStoreLoadStoredData(*storedData);
    }
    else if (event_name == UEventType::FUN_BUFFERSTORESAVEPRINFO)
    {
        const QString& fileName = variant.toStdString().c_str();
        bufferStoreSaveGDPRInfo(fileName);
    }
    else
    {
        Q_ASSERT(false);
    }
}

void UltrasoundView::init()
{
    initializeBegin();
    createMainWindow();
    createZoomOnProxy();
    initTcpServer();
    createAdminConfigModel();
    createPatientWorkflowModel(); // 此方法依赖createAdminConfigModel方法的先执行完毕
    createBFHWInfoModel();
    createFpsInfoWidget();
    createCursorMouseActionsModel();
    createGlyphsControlCreator();
    initializeImageWidget();
    initializeSonoParasWidget(); // 此方法依赖initializeImageWidget方法的先执行完毕
    initializeFreezeBarWidget();
    createProgressSliderWidget();
    createStoreProgressMovieWidget();
    initializeTopInfoWidget(); // TODO: 此业务代码，也可以放到initViewBusiness()里
    createSystemStatusAction();
    createSystemTipModel(); // TODO: 此业务代码，放到UltrasoundView是否最为理想，后续再仔细考虑
    initializeScreenshots();
    createMeasurementWidgets(); // 提炼出的新方法，此逻辑原来在MainModule::createMeasurement()内
    createProbeSelectionModel();
    createExamModeMenuModel();
    createQuickCommentPopupWidget(); // 提炼出的新方法，此逻辑原来在MainModule::createQuickCommentModel()内
    createReportDialog();
    createSettingsDialog(); // 此方法依赖createAdminConfigModel方法的先执行完毕
    createQuickMeasureLoop();
    createStressEchoWidgetManager();
    initializeLGC();
    createThumbnailDialog();
    createSonoParasWidgetController();
    createSonoHelpController();
    controlDopplerAdjustItem();
    createFlexButtons();
    createTGCMenuWidgetContainer();
    createMultiScreenSync();
    initializeEnd();
}

void UltrasoundView::release()
{
    cursorMouseActionsModel()->setGrabedWidget(NULL);
}

void UltrasoundView::setPeripheralManager(IPeripheralManager* value)
{
    m_PeripheralManager = value;
}

void UltrasoundView::setImageManager(IImageManager* value)
{
    m_ImageManager = value;
}

void UltrasoundView::setExamManager(IExamManager* value)
{
    m_ExamManager = value;
}

void UltrasoundView::setMarkManager(IMarkManager* value)
{
    m_MarkManager = value;
}

void UltrasoundView::setToolsFacade(IToolsFacade* value)
{
    m_ToolsFacade = value;
}

void UltrasoundView::setStateManagerFacade(IStateManager* value)
{
    m_StateManager = value;
}

IStateManager* UltrasoundView::stateManagerFacade() const
{
    return m_StateManager;
}

void UltrasoundView::setDiskDevice(IDiskDevice* diskDevice)
{
    Q_ASSERT(diskDevice != NULL);
    Q_ASSERT(diskDevice->diskInfo() != NULL);
    DiskSelectionDialog::setDiskInfo(
        diskDevice->diskInfo()); // TODO: [All][BUG:75989]工程师模式下，change license 弹出的界面不识别U盘
    m_DiskDevice = diskDevice;
}

void UltrasoundView::setSystemInfo(ISystemInfo* systemInfo)
{
    m_SystemInfo = systemInfo;
}

void UltrasoundView::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_ColorMapManager = colorMapManager;
}

void UltrasoundView::setLicenseInitializer(ILicenseInitializer* licenseInitializer)
{
    m_LicenseInitializer = licenseInitializer;
}

ILicenseInitializer* UltrasoundView::licenseInitializer() const
{
    return m_LicenseInitializer;
}

DopplerViewerContext* UltrasoundView::viewContext() const
{
    return m_ViewerContext;
}

IMainWindow* UltrasoundView::mainWindow() const
{
    return m_MainWindow;
}

bool UltrasoundView::realTimeZoomIn()
{
    ImageTile* imageTile = curImageTile();
    if (imageTile != NULL)
        return imageTile->realTimeSonoParameters()->pBV(BFPNames::IsFullScreenZoomInStr);
    else
        return false;
}

void UltrasoundView::pauseUIRefresh(bool pause)
{
    if (pause)
    {
        curImageTile()->beginPause();
        ImageSkimManager* imageClipWidget = this->getImageClipWidget();
        Q_ASSERT(imageClipWidget != NULL);
        imageClipWidget->setSelections(QStringList());
    }
    else
    {
        curImageTile()->endPause();
    }
}

void UltrasoundView::switchUI(int scanmode_name, const QString& menu_name, const QString& state_name, bool switch_on,
                              bool needRefreshHighLight)
{
    if (menu_name == MenuNames::FreezeMenuStr)
    {
        if (switch_on)
        {
            QVariant SonoMSKResult, SonoCardiac, EFastOn, FastOn;
            bool isRealTime = m_ImageManager->chisonUltrasoundContext()->sonoParameters()->isRealTime();
            m_ImageManager->askParam(BFPNames::SonoMSKStr, SonoMSKResult, PARAM_VALUE,
                                     isRealTime ? Realtime : BufferStore);
            m_ImageManager->askParam(BFPNames::EFastModeONStr, EFastOn);
            m_ImageManager->askParam(BFPNames::FastModeONStr, FastOn);
            if (state_name == StateEventNames::FreezeState)
            {
                //
                //以下代码，从FreezeState::onEntry()中移植过来
                //
                //解决从StressEcho 状态 冻结左侧菜单闪烁的问题
                QVariant result;
                bool ret = m_ImageManager->askParam(BFPNames::StressEchoEnStr, result);
                Q_ASSERT(ret == true);
                ret = m_ImageManager->askParam(BFPNames::SonoCardiacStr, SonoCardiac);
                Q_ASSERT(ret == true);
                ret = m_ImageManager->askParam(BFPNames::EFastModeONStr, EFastOn);
                Q_ASSERT(ret == true);
                ret = m_ImageManager->askParam(BFPNames::FastModeONStr, FastOn);
                Q_ASSERT(ret == true);
                if (!result.toBool() && !SonoMSKResult.toBool() && !SonoCardiac.toBool() && !EFastOn.toBool() &&
                    !FastOn.toBool())
                {
                    MenuController::instance().createMenu(menu_name);
                }
            }
            else if (state_name == StateEventNames::CinePlayState)
            {
                if (!SonoMSKResult.toBool() && !EFastOn.toBool() && !FastOn.toBool())
                {
                    MenuController::instance().createMenu(menu_name);
                }
                else
                {
                    MenuController::instance().createMenu("SonoMSKMenu");
                }
            }
        }
    }
    else if (menu_name == MenuNames::SonoCardiacMenuStr)
    {
        if (switch_on)
        {
            MenuController::instance().createLeftMenu(menu_name);

            if (state_name == StateEventNames::CinePlayState) // 冻结状态下
            {
                MenuController::instance().createBottomMenu(MenuNames::FreezeMenuStr);
                MenuController::instance().highlightFirst();
            }
        }
    }
    else if (menu_name == MenuNames::StressEchoMenuStr)
    {
        if (switch_on)
        {
            MenuController::instance().createMenu(menu_name);
            if (state_name == StateEventNames::StressEcho)
            {
                MenuController::instance().createButtonMenu(MenuNames::BMenuStr);
            }
            MenuController::instance().highlightFirst();
        }
    }
    else if (menu_name == MenuNames::CommentMenuStr)
    {
        if (switch_on)
        {
            MenuController::instance().setLeftMenuActiveWidget(
                MenuController::instance().controlPanel()->isNTP() ? LeftMenuWidget::Comment : LeftMenuWidget::Blank);

            MenuController::instance().createMenu(menu_name);
        }
        else
        {
            MenuController::instance().setLeftMenuActiveWidget(LeftMenuWidget::Blank);
        }
    }
    else if (menu_name == MenuNames::BodyMarkMenuStr)
    {
        if (switch_on)
        {
            MenuController::instance().createMenu(menu_name);
            MenuController::instance().leftMenu()->hide();
        }
    }
    else if (menu_name == MenuNames::ArrowMenuStr)
    {
        if (switch_on)
        {
            if (!AppSetting::isP9Series())
            {
                MenuController::instance().createMenu(MenuNames::ArrowMenuStr);
                MenuController::instance().leftMenu()->hide();
            }
        }
    }
    else if (menu_name == MenuNames::MeasurementMenuStr)
    {
        if (switch_on)
        {
            MenuController::instance().createMenu(menu_name);
        }
    }
    else if (menu_name == MenuNames::QuickMeasurementMenuStr)
    {
        if (switch_on)
        {
            MenuController::instance().createMenu(menu_name);
            if (MenuController::instance().leftMenuTools(menu_name) == nullptr)
            {
                MenuController::instance().leftMenu()->hide();
            }
            MenuController::instance().setActiveButtonPanelStyleSheet(-1);
        }
        else
        {
            MenuController::instance().slotCurQuickMeasureToolIsActived(-1);
        }
    }
    else if (menu_name == MenuNames::CurvedPanoramicMenuStr)
    {
        if (switch_on)
        {
            MenuController::instance().setCurvedMenu(true);
            MenuController::instance().createMenu(menu_name);
        }
    }
    else if (menu_name == MenuNames::BMenuStr && m_ImageManager->sonoParameters()->pBV(BFPNames::SonoCarotidGuideStr))
    {
        if (switch_on)
        {
            MenuController::instance().createMenu(MenuNames::SonoGuideMenuStr);
            MenuController::instance().createBottomMenu(MenuNames::BMenuStr);
        }
    }
    else if (menu_name == MenuNames::BMenuStr && m_ImageManager->sonoParameters()->pBV(BFPNames::SonoCardiacStr))
    {
        if (switch_on)
        {
            // SonoCardiac 仅仅需要底部更新为B菜单即可，左侧保持SonoCardiac菜单以免闪烁
            MenuController::instance().createMenu(MenuNames::SonoCardiacMenuStr);
            MenuController::instance().createBottomMenu(MenuNames::BMenuStr);
        }
    }
    else if (menu_name == MenuNames::FreeMMenuStr)
    {
        if (switch_on)
        {
            MenuController::instance().createMenu(menu_name);
            if (MenuController::instance().isShowingLeftMenu())
            {
                MenuController::instance().setShowLeftMenu(true);
            }
        }
    }
    else if (menu_name == MenuNames::EMenuStr)
    {
        if (switch_on)
        {
            MenuController::instance().createMenu(menu_name);
            if (MenuController::instance().controlPanel()->isNTP())
            {
                MenuController::instance().setShowLeftMenu(true);
            }
        }
    }
    else if (menu_name == "")
    {
        if (state_name == StateEventNames::Patient)
        {
            MenuController::instance().setMousePressDisabled(switch_on);
        }
    }
    else
    {
        // default
        if (switch_on)
        {
            if (!needRefreshHighLight)
            {
                MenuController::instance().createMenu(menu_name, "", false);
            }
            else
            {
                MenuController::instance().createMenu(menu_name);
            }
        }
    }

    if (state_name == StateEventNames::FreezeState)
    {
        FunctionButtonController::instance().freezeState(StateEventNames::FreezeState, switch_on);
    }
    else if (state_name == StateEventNames::CommentState || state_name == StateEventNames::BodyMarkState ||
             state_name == StateEventNames::MeasurementState)
    {
        FunctionButtonController::instance().enterMode(state_name, switch_on, false);
    }
    else if (state_name == StateEventNames::ArrowState)
    {
        FunctionButtonController::instance().enterMode(StateEventNames::ArrowState, switch_on, false);
        if (switch_on)
        {
            MenuPanelModel::instance().forceToEnterState(StateEventNames::ArrowState);
        }
    }
    else if (state_name == StateEventNames::QuickDistance || state_name == StateEventNames::QuickEllipse ||
             state_name == StateEventNames::QuickTrace)
    {
        FunctionButtonController::instance().enterMode(LightNames::DistStr, switch_on,
                                                       false); // TODO: 原设计里，三种测量都是做的同一处控制，待确认
    }
    else if (state_name == StateEventNames::MModeState || state_name == StateEventNames::ProbeState ||
             state_name == StateEventNames::EasyViewState ||
             (state_name == StateEventNames::BModeState && scanmode_name == SystemScanModeCP))
    {
        //有些模式下无多功能旋钮控制逻辑
    }
    else if (state_name != "")
    {
        if (switch_on)
        {
            FunctionButtonController::instance().enterMode(state_name);
        }
    }

    if (state_name == StateEventNames::BModeState || state_name == StateEventNames::StressEcho)
    {
        LightController::instance().enterBMode();
    }
    if (state_name == StateEventNames::TwoBMode1Active || state_name == StateEventNames::TwoBMode2Active)
    {
        LightController::instance().enterTwoB();
    }
    else if (state_name == StateEventNames::BCModeState)
    {
        LightController::instance().enterCMode();
    }
    else if (state_name == StateEventNames::BMModeState || state_name == StateEventNames::BMPreModeState)
    {
        LightController::instance().enterBMMode();
    }
    else if (state_name == StateEventNames::BCMModeState)
    {
        LightController::instance().enterCMMode();
    }
    else if (state_name == StateEventNames::BPDModeState)
    {
        if (scanmode_name == SystemScanModeSonoNeedle)
        {
            LightController::instance().enterSonoNeedleMode();
        }
        else
        {
            LightController::instance().enterPDMode();
        }
    }
    else if (state_name == StateEventNames::BPDMModeState)
    {
        LightController::instance().enterPDMMode();
    }
    else if (state_name == StateEventNames::BDPDMModeState)
    {
        LightController::instance().enterDPDMMode();
    }
    else if (state_name == StateEventNames::BTDIDModeState)
    {
        if (scanmode_name == SystemScanModeTissuePW)
            LightController::instance().enterPWTissueDoppler();
        else if (scanmode_name == SystemScanModeTissueDoppler)
            LightController::instance().enterTissueDopplerMode();
        else
            LightController::instance().enterTDIMMode();
    }
    else if (state_name == StateEventNames::BTDIMModeState)
    {
        LightController::instance().enterTDIMMode();
    }
    else if (state_name == StateEventNames::BCCWModeState || state_name == StateEventNames::BCCWPWModeState)
    {
        LightController::instance().enterCWColorDoppler();
    }
    else if (state_name == StateEventNames::BCDModeState || state_name == StateEventNames::BCDPreModeState ||
             state_name == StateEventNames::BCDPWModeState || state_name == StateEventNames::BMVIDModeState ||
             state_name == StateEventNames::BTDIDPWModeState || state_name == StateEventNames::BMVIDPWModeState)
    {
        LightController::instance().enterPWColorDoppler();
    }
    else if (state_name == StateEventNames::BDModeState || state_name == StateEventNames::BDPreModeState ||
             state_name == StateEventNames::BDPWModeState)
    {
        LightController::instance().enterDMode();
    }
    else if (state_name == StateEventNames::BCWModeState || state_name == StateEventNames::BCWPreModeState ||
             state_name == StateEventNames::BCWPWModeState)
    {
        LightController::instance().enterCWMode();
    }
    else if (state_name == StateEventNames::BDPDCWModeState || state_name == StateEventNames::BDPDCWPreModeState ||
             state_name == StateEventNames::BDPDCWPWModeState)
    {
        LightController::instance().enterCWDirectionalPowerDoppler();
    }
    else if (state_name == StateEventNames::BPDCWModeState || state_name == StateEventNames::BPDCWPreModeState ||
             state_name == StateEventNames::BPDCWPWModeState)
    {
        LightController::instance().enterCWPowerDoppler();
    }
    else if (state_name == StateEventNames::BPDDModeState || state_name == StateEventNames::BPDDPreModeState ||
             state_name == StateEventNames::BPDDPWModeState)
    {
        if (state_name == StateEventNames::BPDDPreModeState && scanmode_name == SystemScanModeMVI)
            LightController::instance().enterPWColorDoppler();
        else
            LightController::instance().enterPWPowerDoppler();
    }
    else if (state_name == StateEventNames::BDPDDModeState || state_name == StateEventNames::BDPDDPreModeState ||
             state_name == StateEventNames::BDPDDPWModeState)
    {
        LightController::instance().enterPWDPowerDoppler();
    }
    else if (state_name == StateEventNames::BTDIDPreModeState)
    {
        LightController::instance().enterPWTissueDoppler();
    }
    else if (state_name == StateEventNames::ZoomOnState)
    {
        LightController::instance().light(LightNames::ZoomStr, true);
    }
    else if (state_name == StateEventNames::FreezeState || state_name == StateEventNames::CinePlayState)
    {
        LightController::instance().light(LightNames::FreezeStr, switch_on);
    }
    else if (state_name == StateEventNames::CommentState)
    {
        LightController::instance().light(LightNames::CommentStr, switch_on);
    }
    else if (state_name == StateEventNames::BodyMarkState)
    {
        LightController::instance().light(LightNames::BodyMarkStr, switch_on);
        LightController::instance().light(LightNames::AngleStr, switch_on);
    }
    else if (state_name == StateEventNames::ArrowState)
    {
        LightController::instance().light(LightNames::ArrowStr, switch_on);
        // EBit angle灯亮
        LightController::instance().light(LightNames::AngleStr, switch_on);
    }
    else if (state_name == StateEventNames::MeasurementState)
    {
        LightController::instance().light(LightNames::CalcStr, switch_on);
        LightController::instance().light(LightNames::AngleStr, switch_on);
    }
    else if (state_name == StateEventNames::MModeState)
    {
        LightController::instance().enterMMode();
    }
    else if (state_name == StateEventNames::PatientState)
    {
        LightController::instance().light(LightNames::PatientStr, switch_on);
    }
    else if (state_name == StateEventNames::ProbeState)
    {
        LightController::instance().light(LightNames::ProbeStr, switch_on);
    }
    else if (state_name == StateEventNames::ProbeExit)
    {
        LightController::instance().light(LightNames::ProbeStr, switch_on);

        if (!switch_on && !AppSetting::isPresetMode())
        {
            //如果当前是基元检测预设，退出探头选择界面，切到默认预设
            if (m_ImageManager->sonoParameters()->pIV(BFPNames::ExamModeTypeStr) == WAFER_TYPE)
            {
                //获取默认预设，若无默认预设，则获取第一个预设 在探头连接的前提下
                IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();

                QString defExamId =
                    handle->probeDefaultExamModeID(m_ImageManager->sonoParameters()->pIV(BFPNames::ProbeIdStr), true);
                if (!defExamId.isEmpty() && m_ImageManager->curProbeIsConnected())
                {
                    ProbeSelectionDispatcher::instance()->model()->updateCurrentParameters(
                        m_ImageManager->sonoParameters()->pIV(BFPNames::ProbeIdStr),
                        m_ImageManager->sonoParameters()->pIV(BFPNames::SocketStr), defExamId);
                }
            }
        }
    }
    else if (state_name == StateEventNames::QuickDistance)
    {
        LightController::instance().light(LightNames::DistStr, switch_on);
    }
    else if (state_name == StateEventNames::QuickTrace)
    {
        LightController::instance().light(LightNames::DistStr, switch_on);
    }
    else if (state_name == StateEventNames::Report)
    {
        LightController::instance().light(LightNames::ReportStr, switch_on);
    }
    else if (state_name == StateEventNames::SetupState)
    {
        LightController::instance().light(LightNames::SetupStr, switch_on);
    }
    else if (state_name == StateEventNames::EasyViewState)
    {
        LightController::instance().light(LightNames::BrowseStr, switch_on);
    }
}

void UltrasoundView::switchUI(int scanmode_name, const QString& menu_name, bool needRefreshHighLight)
{
    if (!needRefreshHighLight)
        MenuController::instance().createMenu(menu_name, "", false);
    else
        MenuController::instance().createMenu(menu_name);

    QStringList names;
    QVariant result;
    bool ret = m_ImageManager->askParam(BFPNames::LayoutStr, result);
    Q_ASSERT(ret == true);
    int lay_out = result.toInt();
    if (lay_out == Layout_1x1)
    {
        switch (scanmode_name)
        {
        case SystemScanModeB:
        case SystemScanModeSonoNeedle:
        default:
            break;
        case SystemScanModeColorDoppler:
        case SystemScanModeMVI:
            names << LightNames::CStr;
            break;
        case SystemScanModePowerDoppler:
        case SystemScanModeDPowerDoppler:
            names << LightNames::CPAStr;
            break;
        case SystemScanModeTissueDoppler:
            names << LightNames::TDIStr;
            break;
        }
        LightController::instance().enterOneLayout(names);
    }
    else if (lay_out == Layout_1x2)
    {
        switch (scanmode_name)
        {
        case SystemScanModeB:
        case SystemScanModeSonoNeedle:
        default:
            break;
        case SystemScanModeColorDoppler:
        case SystemScanModeMVI:
            names << LightNames::CStr;
            break;
        case SystemScanModePowerDoppler:
        case SystemScanModeDPowerDoppler:
            names << LightNames::CPAStr;
            break;
        case SystemScanModeTissueDoppler:
            names << LightNames::TDIStr;
            break;
        }
        LightController::instance().enterTwoLayout(names);
    }
    else if (lay_out == Layout_2x2)
    {
        switch (scanmode_name)
        {
        case SystemScanModeB:
        case SystemScanModeSonoNeedle:
        default:
            break;
        case SystemScanModeColorDoppler:
        case SystemScanModeMVI:
            names << LightNames::CStr;
            break;
        case SystemScanModePowerDoppler:
        case SystemScanModeDPowerDoppler:
            names << LightNames::CPAStr;
            break;
        case SystemScanModeTissueDoppler:
            names << LightNames::TDIStr;
            break;
        }
        LightController::instance().enterFourLayout(names);
    }
}

bool UltrasoundView::enterState(FUNC_STATE state, const FuncStateInput& input)
{
    if (m_StateSwitchProcessor)
        Util::SafeDeletePtr(
            m_StateSwitchProcessor); //进入某一具体状态时，m_StateSwitchProcessor对象需要保持，用于接收绑定信号的槽函数响应，因此，不能立即删除
                                     //只有再次进入其它状态时，先删除上次的m_StateSwitchProcessor对象
    UiStateSwitchProcessorFactory factory;
    IStateSwitchProcessor* stateSwitchProcessor = factory.createStateSwitchProcess(state);
    stateSwitchProcessor->setImageManager(m_ImageManager);
    stateSwitchProcessor->setExamManager(m_ExamManager);
    stateSwitchProcessor->setMarkManager(m_MarkManager);
    stateSwitchProcessor->setUltrasoundView(this);
    bool ret = stateSwitchProcessor->enterState(input);
    if (ret)
    {
        IImageWidget* imagewidget = dynamic_cast<IImageWidget*>(m_ViewerContext->imageWidget());
        if (state == Ui_CommentState)
        {
            connect(m_MarkManager->commentMenuModel(), SIGNAL(typeChanged(QString)), m_QuickCommentPopupWidget,
                    SLOT(setModelType(QString)));
            connect(imagewidget->sonoParameters()->parameter(BFPNames::IsFullScreenZoomInStr), &Parameter::valueChanged,
                    this, &UltrasoundView::updateCommentControlState, Qt::UniqueConnection);
            connect(imagewidget->sonoParameters()->parameter(BFPNames::IsSecondGearFullScreenZoomInStr),
                    &Parameter::valueChanged, this, &UltrasoundView::updateCommentControlState, Qt::UniqueConnection);
        }
        else if (state == Ui_BodyMarkState)
        {
            connect(imagewidget->sonoParameters()->parameter(BFPNames::IsFullScreenZoomInStr), &Parameter::valueChanged,
                    this, &UltrasoundView::updateBodyMarkControlState, Qt::UniqueConnection);
            connect(imagewidget->sonoParameters()->parameter(BFPNames::IsSecondGearFullScreenZoomInStr),
                    &Parameter::valueChanged, this, &UltrasoundView::updateBodyMarkControlState, Qt::UniqueConnection);
        }
        else if (state == Ui_ArrowState)
        {
            connect(imagewidget->sonoParameters()->parameter(BFPNames::IsFullScreenZoomInStr), &Parameter::valueChanged,
                    this, &UltrasoundView::updateArrowControlState, Qt::UniqueConnection);
            connect(imagewidget->sonoParameters()->parameter(BFPNames::IsSecondGearFullScreenZoomInStr),
                    &Parameter::valueChanged, this, &UltrasoundView::updateArrowControlState, Qt::UniqueConnection);
        }
        else if (state == Ui_ReportState)
        {
            ReportDialog* dlg = dynamic_cast<ReportDialog*>(m_ViewerContext->reportDialog());
            connect(MenuController::instance().controlPanel(), SIGNAL(tpButClicked(QString, QString, QString)), dlg,
                    SIGNAL(tpButClicked(QString, QString, QString)));
        }
    }

    m_StateSwitchProcessor = stateSwitchProcessor;

    return ret;
}

bool UltrasoundView::exitState(FUNC_STATE state, const FuncStateInput& input)
{
    UiStateSwitchProcessorFactory factory;
    IStateSwitchProcessor* stateSwitchProcessor = factory.createStateSwitchProcess(state);
    stateSwitchProcessor->setImageManager(m_ImageManager);
    stateSwitchProcessor->setExamManager(m_ExamManager);
    stateSwitchProcessor->setMarkManager(m_MarkManager);
    stateSwitchProcessor->setUltrasoundView(this);
    bool ret = stateSwitchProcessor->exitState(input);
    if (ret)
    {
        IImageWidget* imagewidget = dynamic_cast<IImageWidget*>(m_ViewerContext->imageWidget());
        if (state == Ui_CommentState)
        {
            disconnect(m_MarkManager->commentMenuModel(), SIGNAL(typeChanged(QString)), m_QuickCommentPopupWidget,
                       SLOT(setModelType(QString)));
            disconnect(imagewidget->sonoParameters()->parameter(BFPNames::IsFullScreenZoomInStr),
                       &Parameter::valueChanged, this, &UltrasoundView::updateCommentControlState);
            disconnect(imagewidget->sonoParameters()->parameter(BFPNames::IsSecondGearFullScreenZoomInStr),
                       &Parameter::valueChanged, this, &UltrasoundView::updateCommentControlState);
        }
        else if (state == Ui_BodyMarkState)
        {
            disconnect(imagewidget->sonoParameters()->parameter(BFPNames::IsFullScreenZoomInStr),
                       &Parameter::valueChanged, this, &UltrasoundView::updateBodyMarkControlState);
            disconnect(imagewidget->sonoParameters()->parameter(BFPNames::IsSecondGearFullScreenZoomInStr),
                       &Parameter::valueChanged, this, &UltrasoundView::updateBodyMarkControlState);
        }
        else if (state == Ui_ArrowState)
        {
            disconnect(imagewidget->sonoParameters()->parameter(BFPNames::IsFullScreenZoomInStr),
                       &Parameter::valueChanged, this, &UltrasoundView::updateArrowControlState);
            disconnect(imagewidget->sonoParameters()->parameter(BFPNames::IsSecondGearFullScreenZoomInStr),
                       &Parameter::valueChanged, this, &UltrasoundView::updateArrowControlState);
        }
        else if (state == Ui_ReportState)
        {
            ReportDialog* dlg = dynamic_cast<ReportDialog*>(m_ViewerContext->reportDialog());
            disconnect(MenuController::instance().controlPanel(), SIGNAL(tpButClicked(QString, QString, QString)), dlg,
                       SIGNAL(tpButClicked(QString, QString, QString)));
        }
    }

    Util::SafeDeletePtr(stateSwitchProcessor);

    return ret;
}

void UltrasoundView::controlLight(const QString& keyName, bool state)
{
    LightController::instance().light(keyName, state);
}

Overlay* UltrasoundView::imageTileOverlay() const
{
    ImageTile* imageTile = curImageTile();
    if (imageTile != NULL)
        return &imageTile->pimplGlyphsWidget().overlay();
    else
        return NULL;
}

void UltrasoundView::removeImageTileOverlayAllItems()
{
    Overlay* overlay = imageTileOverlay();
    if (overlay != NULL)
    {
        overlay->removeAllItems();
    }
}

void UltrasoundView::setLeftMenuWidgetVisible(bool show)
{
    QWidget* widget = m_ViewerContext->leftMenuWidget();
    if (widget != NULL)
    {
        widget->setVisible(show);
    }
}

void UltrasoundView::setMeasurementMenutFetus(int value)
{
    MeasurementMenu* measurementMenu = dynamic_cast<MeasurementMenu*>(m_ViewerContext->measurementMenu());
    if (measurementMenu != NULL)
    {
        measurementMenu->setCurrentFetus(value);
    }
}

void UltrasoundView::setMeasurementMenuIsToolEnter(bool value)
{
    MeasurementMenu* measurementMenu = dynamic_cast<MeasurementMenu*>(m_ViewerContext->measurementMenu());
    if (measurementMenu != NULL)
    {
        measurementMenu->setIsToolEnter(value);
    }
}

void UltrasoundView::setBottomMenuWidgetVisible(bool show)
{
    QWidget* widget = m_ViewerContext->bottomMenuWidget();
    if (widget != NULL)
    {
        widget->setVisible(show);
    }
}

void UltrasoundView::setstressEchoWorkWidgetVisible(bool show)
{
    StressEchoWorkWidget* stressEchoWorkWidget = m_StressEchoWidgetManager->stressEchoWorkWidget();
    if (stressEchoWorkWidget != NULL)
    {
        if (show)
        {
            stressEchoWorkWidget->show();
        }
        else
        {
            stressEchoWorkWidget->hide();
        }
    }
}

QImage UltrasoundView::currentImage(ImageEventArgs::ImageType type) const
{
    ImageTile* imageTile = curImageTile();
    if (imageTile != NULL)
        return imageTile->currentImage(type);
    else
        return QImage();
}

bool UltrasoundView::isThumbnailDialogVisible() const
{
    BaseBkWidget* widget = m_ViewerContext->thumbnailDialog();
    return widget != NULL && widget->isVisible();
}

void UltrasoundView::setMeasureResultListWidgetOnLoadDisplayControl(bool value)
{
    MeasureResultListWidget* measureResultListWidget =
        dynamic_cast<MeasureResultListWidget*>(m_ViewerContext->measureResultListWidget());
    if (measureResultListWidget != NULL)
    {
        measureResultListWidget->setOnLoadDisplayControl(value);
    }
}

void UltrasoundView::setMeasureResultListWidgetVisible(bool show)
{
    MeasureResultListWidget* measureResultListWidget =
        dynamic_cast<MeasureResultListWidget*>(m_ViewerContext->measureResultListWidget());
    if (measureResultListWidget != NULL)
    {
        measureResultListWidget->setVisible(show);
    }
}

void UltrasoundView::adjustMeasureResultListWidgetSize()
{
    MeasureResultListWidget* measureResultListWidget =
        dynamic_cast<MeasureResultListWidget*>(m_ViewerContext->measureResultListWidget());
    if (measureResultListWidget != NULL)
    {
        measureResultListWidget->doAdjustSize();
    }
}

void UltrasoundView::closeSystemStatusInfoWindows()
{
    if (m_ExamManager != NULL)
    {
        m_ExamManager->closeSystemStatusWidget();
    }
}

void UltrasoundView::setSonoParametersForSonoParasWidget(SonoParameters* sonoparameters)
{
    SonoParametersClientBase* sonoParametersClientBase =
        dynamic_cast<SonoParametersClientBase*>(m_ViewerContext->sonoParasWidget());
    //    SonoParameters* globleSp = m_BufferStoreManager->curSonoParameters();
    //    SonoParameters* spLayout =
    //                m_LineBufferManager->getSonoParametersByLayoutIndex(globleSp->pIV(BFPNames::ActiveBStr));
    if (sonoParametersClientBase != NULL)
    {
        sonoParametersClientBase->setSonoParameters(sonoparameters);
    }
}

void UltrasoundView::setSonoParametersForDopplerAdjustItem(SonoParameters* sonoparameters)
{
    DopplerThetaAdjustItem* dopplerThetaAdjustItem = m_ViewerContext->DopplerAdjustItem();
    if (dopplerThetaAdjustItem != NULL)
    {
        dopplerThetaAdjustItem->setSonoParameters(sonoparameters);
    }
}

void UltrasoundView::setSonoParametersForMainWindowTopWidget(SonoParameters* sonoparameters)
{
    if (m_MainWindowTopInfoModel != NULL)
    {
        m_MainWindowTopInfoModel->setSonoParameters(sonoparameters);
    }
}

void UltrasoundView::updateGDPRInfoForMainWindowTopWidget(int offsetX)
{
    MainWindowTopWidget* mainWindowTopWidget =
        dynamic_cast<MainWindowTopWidget*>(m_ViewerContext->mainWindowTopWidget());
    if (mainWindowTopWidget != NULL)
    {
        mainWindowTopWidget->updateCurrentGDPRRect(offsetX);
    }
}

ImageTile* UltrasoundView::curImageTile() const
{
    IImageWidget* imageWidget = dynamic_cast<IImageWidget*>(m_ViewerContext->imageWidget());
    if (imageWidget != NULL)
    {
        return imageWidget->imageTile();
    }
    else
    {
        return NULL;
    }
}

bool UltrasoundView::isZoomIn() const
{
    ZoomOnProxy* zoomOnProxy = dynamic_cast<ZoomOnProxy*>(m_ZoomOnProxy);
    if (zoomOnProxy != NULL)
    {
        return zoomOnProxy->isZoomIn();
    }
    else
    {
        return false;
    }
}

const QList<QRect> UltrasoundView::getGDPRFullScreenCurrentRect() const
{
    MainWindowTopWidget* mainWindowTopWidget =
        dynamic_cast<MainWindowTopWidget*>(m_ViewerContext->mainWindowTopWidget());
    if (mainWindowTopWidget != NULL)
    {
        return mainWindowTopWidget->getGDPRFullScreenCurrentRect();
    }
    else
    {
        return QList<QRect>();
    }
}

const QList<QRect> UltrasoundView::getGDPRImageAndInfoCurrentRect() const
{
    MainWindowTopWidget* mainWindowTopWidget =
        dynamic_cast<MainWindowTopWidget*>(m_ViewerContext->mainWindowTopWidget());
    if (mainWindowTopWidget != NULL)
    {
        return mainWindowTopWidget->getGDPRImageAndInfoCurrentRect();
    }
    else
    {
        return QList<QRect>();
    }
}

const QRect UltrasoundView::getGDPROBRect() const
{
    ISonoParasWidget* sonoParasWidget = dynamic_cast<ISonoParasWidget*>(m_ViewerContext->sonoParasWidget());
    if (sonoParasWidget != NULL)
    {
        return sonoParasWidget->GDPROBRect();
    }
    else
    {
        return QRect();
    }
}

int UltrasoundView::getMainWindowTopWidgetGDPRWidth() const
{
    MainWindowTopWidget* mainWindowTopWidget =
        dynamic_cast<MainWindowTopWidget*>(m_ViewerContext->mainWindowTopWidget());
    if (mainWindowTopWidget != NULL)
    {
        return mainWindowTopWidget->getGDPRWidth();
    }
    else
    {
        return -1;
    }
}

void UltrasoundView::updateImageClipWidget(const QString& patient_path)
{
    //当非4D探头continue exam包含4D图片的病例时，禁止缩略图显示4D图片
    QDir dir(patient_path);
    QString path = dir.path();
    ImageSkimManager* imageClipWidget = this->getImageClipWidget();
    imageClipWidget->setCurrentDir(path);
    if (dir.exists())
    {
        QStringList imgFilters = QStringList() << Resource::imgFilter << Resource::cineFilter << Resource::justPicFilter
#ifdef USE_4D
                                               << Resource::singleFourdImgFilter << Resource::multiFourdImgFilter
                                               << Resource::aviFilter
#endif
            ;

        imageClipWidget->loadFiles(Util::getFilePathList(dir, imgFilters, Resource::bmpExt));

        //#ifdef USE_4D
        //            QStringList fourdImgfilters = QStringList() <<Resource::singleFourdImgFilter
        //                                                        <<Resource::multiFourdImgFilter
        //                                                        <<Resource::aviFilter;
        //            m_Context->chisonFourdProxy()->imageClipWidget()->loadFiles(screenImgFiles(dir, fourdImgfilters));
        //#endif
    }
}

void UltrasoundView::clearImageClipWidget()
{
    ImageSkimManager* imageClipWidget = this->getImageClipWidget();
    imageClipWidget->setCurrentDir(QString());
    imageClipWidget->clear();

    //#ifdef USE_4D
    //        m_Context->chisonFourdProxy()->imageClipWidget()->clear();
    //        m_Context->chisonFourdProxy()->imageClipWidget()->setRowAndColumn(6, 1);
    //#endif
}

void UltrasoundView::updateImageClipWidgetImageLable(int index)
{
    ImageSkimManager* imageClipWidget = this->getImageClipWidget();
    imageClipWidget->updateImageLabel(index);
}

void UltrasoundView::showSonoZoomWidgetGlass(bool show, bool isOtherFun)
{
    if (m_SonoZoomWidget != NULL)
    {
        if (!show)
            m_SonoZoomWidget->hideGlass(isOtherFun);
        else
            m_SonoZoomWidget->showGlass(isOtherFun);
    }
}

QRect UltrasoundView::imageZoomOnWidgetRect()
{
    if (m_ZoomOnProxy != NULL)
    {
        ZoomOnProxy* zoomonproxy = dynamic_cast<ZoomOnProxy*>(m_ZoomOnProxy);
        Q_ASSERT(zoomonproxy != NULL);
        return zoomonproxy->imageZoomOnWidgetRect();
    }
    else
        return QRect();
}

BFHWInfoModel* UltrasoundView::bFHWInfoModel() const
{
    return m_BFHWInfoModel;
}

MainWindowTopInfoModel* UltrasoundView::mainWindowTopInfoModel() const
{
    return m_MainWindowTopInfoModel;
}

CursorMouseActionsModel* UltrasoundView::cursorMouseActionsModel() const
{
    return m_CursorMouseActionsModel;
}

ProbeSelectionDispatcher* UltrasoundView::probeSelectionDispatcher() const
{
    return ProbeSelectionDispatcher::instance();
}

IExamModeMenuModel* UltrasoundView::examModeMenuModel() const
{
    return m_ExamModeMenuModel;
}

QList<QuickMeasureCaptionsLoop*> UltrasoundView::quickMeasureCaptionsLoops() const
{
    return m_QuickMeasureCaptionsLoops;
}

StressEchoWidgetManager* UltrasoundView::stressEchoWidgetManager() const
{
    return m_StressEchoWidgetManager;
}

IAdminConfigModel* UltrasoundView::adminConfigModel() const
{
    return m_AdminConfigModel;
}

IPatientWorkflowModel* UltrasoundView::patientWorkflowModel() const
{
    return m_PatientWorkflowModel;
}

TGCMenuWidgetContainer* UltrasoundView::widgetOfTGCAdjustment() const
{
    return m_TGCMenuWidgetContainer;
}

void UltrasoundView::showLoginDlg()
{
    if (m_AdminConfigModel != NULL)
    {
        m_AdminConfigModel->showLoginDlg();
    }
}

void UltrasoundView::showSNInputWidget()
{
    createLicenseInitializer();
    Q_ASSERT(m_DiskDevice != NULL);
    Q_ASSERT(m_DiskDevice->udiskPathInfo() != NULL);
    Q_ASSERT(m_LicenseInitializer != NULL);
    emit signalSetCurVirKeyboardEnglish(true);
    SNInput snInput(m_LicenseInitializer, m_DiskDevice->udiskPathInfo(), false);
    emit signalSetCurVirKeyboardEnglish(false);
}

void UltrasoundView::checkRemainDays()
{
    m_LicenseInitializer->checkRemainDays();
}

void UltrasoundView::setupSystemStatusAction()
{
    SystemStatusWidget* systemStatusTip = dynamic_cast<SystemStatusWidget*>(m_ViewerContext->systemStatusTip());
    Q_ASSERT(systemStatusTip != NULL);
    Q_ASSERT(m_SystemStatusTipAction != NULL);
    m_SystemStatusTipAction->setTipSize(systemStatusTip->size());

    QPoint globalPos = systemStatusTip->mapToGlobal(QPoint(0, 0));
    QPoint localPos = m_MainWindow->mapFromGlobal(globalPos);
    m_SystemStatusTipAction->setTipPos(localPos);
}

QStringList UltrasoundView::postParameterNames() const
{
    QStringList results;
    if (m_SonoParasWidgetController != NULL)
    {
        results = m_SonoParasWidgetController->postParameterNames();
    }

    return results;
}

void UltrasoundView::updateToolsSonoParameters(SonoParameters* sonoParameters)
{
    sendSyncEventTo(UEventType::TOOL_UPDATE_PARAMS, sonoParameters);
}

void UltrasoundView::checkImageRender()
{
    ImageTile* imageTitle = curImageTile();
    if (imageTitle == NULL || imageTitle->isRenderError())
    {
        MessageBoxFrame::warningNonModal(mainWindow(), QString(),
                                         tr("The render component initalized failed.Please restart manually!"));
    }
}

void UltrasoundView::controlGainLogo()
{
    //    connect(m_Context->sonoParameters()->parameter(BFPNames::GainThiStr), SIGNAL(valueChanged(QVariant)), this,
    //    SLOT(gainLogoChanged()));
    //    connect(m_Context->sonoParameters()->parameter(BFPNames::GainStr),SIGNAL(valueChanged(QVariant)), this,
    //    SLOT(gainLogoChanged()));
    connect(
        m_ImageManager->sonoParameters()->parameter(BFPNames::GainLogoVisibleStr), &Parameter::valueChanged, this,
        [=](QVariant value) {
            m_GainLogoVisible = value.toBool();
            if (!m_GainLogoVisible)
            {
                QTimer::singleShot(
                    ModelConfig::instance().value(ModelConfig::SKTipIconShowDelayTime, 2000).toInt(), this, [=]() {
                        dynamic_cast<MainWindowKBUnit*>(m_MainWindow)->gainLogoLabel()->setVisible(m_GainLogoVisible);
                    });
            }
            else
            {
                dynamic_cast<MainWindowKBUnit*>(m_MainWindow)->gainLogoLabel()->setVisible(m_GainLogoVisible);
            }
        });
}

void UltrasoundView::showMainWindowBegin()
{
    SplashWindow::setPercentProgress(1);
    SplashWindow::record();
}

void UltrasoundView::showMainWindow()
{
    AbstractScreenDisplayStatusModel::enableSettings();
    if (m_MainWindow != NULL)
    {
        UiUtil::moveWidgetToCenterOfPrimaryScreen(m_MainWindow);
        //    UiUtil::moveWidgetToTouchScreen(m_MainWindow);
        m_MainWindow->show();
        m_ViewerContext->leftMenuWidget()
            ->hide(); //对应原MainModule::showMainWindow()中m_Context->leftMenuWidget()->hide();

        m_MainWindow->raise();
        m_MainWindow->activateWindow();
    }

    initializeEFastWidgets();
    MenuController::instance().controlPanel()->showFunctionPanel();
}

void UltrasoundView::showMainWindowEnd()
{
    SplashWindow::finished(m_MainWindow);
    SplashWindow::release();
}

void UltrasoundView::runSystemUserImpl()
{
    ConfigurationDialog* configurationDialog =
        dynamic_cast<ConfigurationDialog*>(m_ViewerContext->configurationDialog());
    if (configurationDialog != NULL)
    {
        //注意：setDiskDevice必须先于runSystemUserImpl被调用
        configurationDialog->setDiskDevice(m_DiskDevice);
        ITableInfoModelFactory* tableInfoFactory = m_MarkManager->tableInfoModelFactory();
        IEquationInfoModelFactory* equatInfoFactory = m_MarkManager->equationInfoModelFactory();
        Q_ASSERT(tableInfoFactory != NULL && equatInfoFactory != NULL);
        configurationDialog->setTableInfoModelFactory(tableInfoFactory, equatInfoFactory);
        IOBCalc* OBCalc = m_MarkManager->OBcalc();
        Q_ASSERT(OBCalc != NULL);
        configurationDialog->setOBCalc(OBCalc);
        configurationDialog->setSystemInfo(m_SystemInfo);
        configurationDialog->setLicenseInitializer(m_LicenseInitializer);
        configurationDialog->runSystemUserImpl();
    }
}

void UltrasoundView::bufferStoreOnLoaded()
{
    setMeasureResultListWidgetOnLoadDisplayControl(false);

    MeasureContext* measContext = m_MarkManager->measureContext();
    measContext->setBImage(currentImage(ImageEventArgs::ImageB));
    measContext->setDImage(currentImage(ImageEventArgs::ImageD));
    measContext->setMImage(currentImage(ImageEventArgs::ImageM));

    measContext->setImage(currentImage());
    setMeasureResultListWidgetOnLoadDisplayControl(true);

    /*如果截屏窗口显示了，此处不在显示测量结果并标记测量结果需要显示，如果截屏窗口已经隐藏了，此处显示测量结果窗口*/
    if (isThumbnailDialogVisible())
    {
        m_MarkManager->screenMeasureController()->setLoadForDisplay(true);
    }
    else
    {
        setMeasureResultListWidgetVisible(true);
        adjustMeasureResultListWidgetSize();
    }
}

void UltrasoundView::bufferStoreLoadStoredData(const StoredData& storedData)
{
    setSonoParametersForSonoParasWidget(storedData.sonoParameters());
    setSonoParametersForDopplerAdjustItem(storedData.sonoParameters());
    if (!storedData.sonoParameters()->pBV(BFPNames::ShowFourDWidgetStr))
    {
        m_ImageManager->chisonUltrasoundContext()->onLoad(storedData.sonoParameters());
        curImageTile()->onLoad(storedData);
        m_ImageManager->chisonUltrasoundContext()->endOnLoad();
        if (m_ImageManager->chisonUltrasoundContext()->cineLooper() != NULL &&
            !storedData.sonoParameters()->isRealTime())
        {
            m_ImageManager->chisonUltrasoundContext()->cineLooper()->startLoop();
        }
    }
    else
    {
        curImageTile()->onLoad(storedData);
    }
    setSonoParametersForMainWindowTopWidget(storedData.sonoParameters());
}

void UltrasoundView::bufferStoreSaveGDPRInfo(const QString& fileName)
{
    QList<QRect> GDPRRectsInfoList;
    QString GDPROBRectStr;
    QString iniFileName = Util::getSameBaseNamesFileName(fileName, "_GDPR.ini");
    QSettings settings(iniFileName, QSettings::IniFormat);
    int offsetX = 0;
    updateGDPRInfoForMainWindowTopWidget(offsetX);

    if (isZoomIn())
    {
        GDPRRectsInfoList = getGDPRFullScreenCurrentRect();
        settings.beginGroup("ImageType");
        settings.setValue("type", ScreenTypeDef::ZoomIn);
        settings.endGroup();
        settings.beginGroup("GDPRInfo");
        if (isInSecondDegree())
        {
            int i = 0;
            foreach (QRect GDPRRect, GDPRRectsInfoList)
            {
                QString GDPRRectStr = QString("%1,%2,%3,%4")
                                          .arg(GDPRRect.x())
                                          .arg(GDPRRect.y())
                                          .arg(GDPRRect.width())
                                          .arg(GDPRRect.height());
                settings.setValue(QString::number(i++), GDPRRectStr);
            }
        }
        settings.endGroup();
    }
    else if (Setting::instance().defaults().screenType() == ScreenTypeDef::Image)
    {
        settings.beginGroup("ImageType");
        settings.setValue("type", ScreenTypeDef::Image);
        settings.endGroup();

        QRect OBRect = getGDPROBRect();
        if (!OBRect.isNull())
        {
            GDPROBRectStr = QString("%1,%2,%3,%4")
                                .arg(OBRect.x() - getMainWindowTopWidgetGDPRWidth())
                                .arg(OBRect.y())
                                .arg(OBRect.width())
                                .arg(OBRect.height());
            settings.beginGroup("GDPROB");
            settings.setValue(QString::number(0), GDPROBRectStr);
            settings.endGroup();
        }
    }
    else if (Setting::instance().defaults().screenType() == ScreenTypeDef::InfoAndImage)
    {
        GDPRRectsInfoList = getGDPRImageAndInfoCurrentRect();

        QRect OBRect = getGDPROBRect();

        settings.beginGroup("ImageType");
        settings.setValue("type", ScreenTypeDef::InfoAndImage);
        settings.endGroup();

        settings.beginGroup("GDPRInfo");
        int i = 0;
        foreach (QRect GDPRRect, GDPRRectsInfoList)
        {
            QString GDPRRectStr =
                QString("%1,%2,%3,%4").arg(GDPRRect.x()).arg(GDPRRect.y()).arg(GDPRRect.width()).arg(GDPRRect.height());
            settings.setValue(QString::number(i++), GDPRRectStr);
        }
        settings.endGroup();

        if (!OBRect.isNull())
        {
            GDPROBRectStr = QString("%1,%2,%3,%4")
                                .arg(OBRect.x() - getMainWindowTopWidgetGDPRWidth())
                                .arg(OBRect.y())
                                .arg(OBRect.width())
                                .arg(OBRect.height());
            settings.beginGroup("GDPROB");
            settings.setValue(QString::number(0), GDPROBRectStr);
            settings.endGroup();
        }
    }
    else if (Setting::instance().defaults().screenType() == ScreenTypeDef::FullScreen)
    {
        GDPRRectsInfoList = getGDPRFullScreenCurrentRect();
        QRect OBRect = getGDPROBRect();

        settings.beginGroup("ImageType");
        settings.setValue("type", ScreenTypeDef::FullScreen);
        settings.endGroup();

        settings.beginGroup("GDPRInfo");
        int i = 0;
        foreach (QRect GDPRRect, GDPRRectsInfoList)
        {
            QString GDPRRectStr =
                QString("%1,%2,%3,%4").arg(GDPRRect.x()).arg(GDPRRect.y()).arg(GDPRRect.width()).arg(GDPRRect.height());
            settings.setValue(QString::number(i++), GDPRRectStr);
        }
        settings.endGroup();

        if (!OBRect.isNull())
        {
            GDPROBRectStr =
                QString("%1,%2,%3,%4").arg(OBRect.x()).arg(OBRect.y()).arg(OBRect.width()).arg(OBRect.height());
            settings.beginGroup("GDPROB");
            settings.setValue(QString::number(0), GDPROBRectStr);
            settings.endGroup();
        }
    }
}

void UltrasoundView::createSplashWindow()
{
    ISplashWindowFactory* factory = SplashWindowFactoryCreator::createFactory(AppSetting::model());
    // use factory to create splashwindow
    // SplashWindow::instance(Resource::splashFileName());
    //判断开机图的链接是否存在(存在要判断是否和机型匹配)
    //链接不存在创建链接
    QString splashFileNameStr = factory->splashFileName();
    if (QFile(Resource::splashLinkFileName()).exists())
    {
        QString targetStr = Resource::getFileFullName(
            QFileInfo(QFile::symLinkTarget(Resource::splashLinkFileName())).fileName(), Resource::appIconDir);
        if (targetStr != splashFileNameStr)
        {
            QFile::remove(Resource::splashLinkFileName());
            QFile::link(factory->splashFileStr(), Resource::splashLinkFileName());
        }
    }
    else
    {
        QFile::link(factory->splashFileStr(), Resource::splashLinkFileName());
    }
    QPixmap pixmap(splashFileNameStr);
    SplashWindow::instance(factory->create(pixmap));

    delete factory;

    updateSplashProgress("createSplashWindow()");
}

void UltrasoundView::updateSplashProgress(const QString& status)
{
    SplashWindow::updateProgress(status);
}

void UltrasoundView::enableUpdateMeasureContextImage(bool isConnect)
{
    Util::connectSignal(m_MarkManager->measureContext(), SIGNAL(requestImage()), this, SLOT(setImage()), isConnect,
                        Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
}

void UltrasoundView::updateMeasureContextImage()
{
    setImage();
}

void UltrasoundView::setImage()
{
    if (m_ImageManager != NULL)
    {
        QVariant result;
        bool ret = m_ImageManager->askParam(BFPNames::FreezeStr, result);
        if (ret && result.toBool())
        {
            auto measContext = m_MarkManager->measureContext();
            auto tile = curImageTile();

            if (tile != nullptr)
            {
                measContext->setBImage(tile->getSetIndexBImage(0), 0);
                if (tile->sonoParameters()->pIV(BFPNames::LayoutStr) >= Layout_1x2)
                    measContext->setBImage(tile->getSetIndexBImage(1), 1);
                if (tile->sonoParameters()->pIV(BFPNames::LayoutStr) == Layout_2x2)
                {
                    measContext->setBImage(tile->getSetIndexBImage(2), 2);
                    measContext->setBImage(tile->getSetIndexBImage(3), 3);
                }

                measContext->setDImage(tile->currentImage(ImageEventArgs::ImageD));
                measContext->setMImage(tile->currentImage(ImageEventArgs::ImageM));

                measContext->setImage(tile->currentImage());
                measContext->setRawDataImage(tile->currentRawDataImage());
            }
        }
    }
}

bool UltrasoundView::isInSecondDegree()
{
    if (nullptr != m_ZoomOnProxy)
    {
        return m_ZoomOnProxy->isInSecondDegree();
    }
    return false;
}

QWidget* UltrasoundView::popUpWidget() const
{
    MainWindowKBUnit* mainWindow = dynamic_cast<MainWindowKBUnit*>(m_MainWindow);
    Q_ASSERT(mainWindow != NULL);
    return mainWindow->popUpWidget();
}

void UltrasoundView::resetImageRects()
{
    // not safe for empty widget. make sure all widget has be set into MainModuleContext
    BaseWidget* imageWidget = m_ViewerContext->imageWidget();
    BaseWidget* sonoparasWidget = m_ViewerContext->sonoParasWidget();
    BaseBkWidget* thumbnailDialog = m_ViewerContext->thumbnailDialog();
    IMainWindow* mainWindow = m_MainWindow;

    if (imageWidget != NULL && sonoparasWidget != NULL && thumbnailDialog != NULL && mainWindow != NULL)
    {
        QRect rect = QRect(imageWidget->mapToGlobal(QPoint(0, 0)),
                           sonoparasWidget->mapToGlobal(
                               QPoint(sonoparasWidget->rect().bottomRight().x(), imageWidget->rect().height())));
        MeasureResultListWidget* measureResultListWidget =
            dynamic_cast<MeasureResultListWidget*>(m_ViewerContext->measureResultListWidget());
        if (measureResultListWidget != NULL)
        {
            measureResultListWidget->setImageRect(rect);
            QRect onlyImage = QRect(imageWidget->mapToGlobal(QPoint(0, 0)),
                                    sonoparasWidget->mapToGlobal(QPoint(sonoparasWidget->rect().bottomLeft().x(),
                                                                        imageWidget->rect().height())));
            measureResultListWidget->setOnlyImageRect(onlyImage);
            if (Setting::instance().defaults().screenType() == ScreenTypeDef::Image)
                measureResultListWidget->setLimitRect(onlyImage);
            else
                measureResultListWidget->setLimitRect(rect);
        }

        thumbnailDialog->setOriginalSize(rect.width(), imageWidget->rect().height());
        thumbnailDialog->move(imageWidget->mapTo(mainWindow, QPoint(0, 0)));
        thumbnailDialog->setFullScreenRect(QRect(mainWindow->mapToGlobal(QPoint(0, 0)), mainWindow->size()));

        QPoint topLeft(imageWidget->mapToGlobal(QPoint(0, 0)).x(), mainWindow->mapToGlobal(QPoint(0, 0)).y());
        QPoint bottomRight(sonoparasWidget->mapToGlobal(
            QPoint(sonoparasWidget->rect().bottomRight().x(), imageWidget->rect().height())));
        QRect imageAndInfoRect(topLeft, bottomRight);
        // adjust rect
        int mod = imageAndInfoRect.width() % 4;
        if (mod != 0)
        {
            int addW = 4 - mod;
            imageAndInfoRect.setLeft(imageAndInfoRect.left() - addW);
        }

        Screenshots::instance().setImageRect(QRect(imageWidget->mapToGlobal(QPoint(0, 0)), imageWidget->size()));
        Screenshots::instance().setImageAndInfoRect(imageAndInfoRect);
        Screenshots::instance().setFullScreenRect(QRect(mainWindow->mapToGlobal(QPoint(0, 0)), mainWindow->size()));
        Screenshots::instance().setThumbnailRect(rect);
    }

    BaseWidget* widget = m_ViewerContext->stressEchoWorkWidget();
    if (widget != NULL)
    {
        int top = ModelUiConfig::instance().value(ModelUiConfig::StressEchoWorkWidget_top).toInt();
        int left = ModelUiConfig::instance().value(ModelUiConfig::StressEchoWorkWidget_left).toInt();
        widget->setStartPos(QPoint(left, top));
        widget->hide();
    }
}

void UltrasoundView::updateMotorParameter()
{
    if (m_ImageManager != NULL)
    {
        m_ImageManager->updateMotorParameter();
    }
}

void UltrasoundView::fullScreenFlexBtnCliecked()
{
    sendSyncEventTo(UEventType::COMMAND_FULLSCREENTOOL, NULL);
}

void UltrasoundView::onScreenAdded(QScreen* screen)
{
    if (screen == nullptr)
    {
        return;
    }

    log()->info(QString("screen %1 added! (x, y) x = %2, y = %3, width = %4, height = %5")
                    .arg(screen->name())
                    .arg(screen->geometry().x())
                    .arg(screen->geometry().y())
                    .arg(screen->geometry().width())
                    .arg(screen->geometry().height()));
    if (Setting::instance().defaults().isEngineeringMode())
    {
        BaseDialogFrame dlg;
        dlg.setIsShowOk(false);
        dlg.setFrameTitle(tr("System"));
        dlg.setGeometry(screen->geometry());
        dlg.exec();
    }
}

void UltrasoundView::onScreenRemoved(QScreen* screen)
{
    if (screen != nullptr)
    {
        log()->info(QString("screen %1 removed!").arg(screen->name()));
    }
}

void UltrasoundView::onLockScreenStateChanged(QString state)
{
    if (state == "LockScreen")
    {
        MainWindowKBUnit* mainWindow = dynamic_cast<MainWindowKBUnit*>(m_MainWindow);
        Q_ASSERT(mainWindow != NULL);
        Util::changeQssWidgetProperty(mainWindow->getLockScreenFlexBtn(), "state",
                                      Setting::instance().defaults().getLockScreen() ? "on" : "off");
        mainWindow->getLockscreenTipLabel()->setVisible(Setting::instance().defaults().getLockScreen());
    }
}

void UltrasoundView::onCurSocketNoProbeWarning()
{
    MessageBoxFrame::tipWarning(mainWindow(), tr("Warning"), tr("There is no probe exist on current socket."));
}

void UltrasoundView::clearAllGlyphsWithOutComment()
{
    Overlay* overlay = imageTileOverlay();
    overlay->removeAllTypedItems<MeasureGlyphs>();
    overlay->removeAllTypedItems<ArrowGlyphs>();
    overlay->removeAllTypedItems<BodyMarkGlyphs>();
}

void UltrasoundView::onAnalyzeStateClosed()
{
    sendSyncEventTo(UEventType::EVENT_EXITANALYZE);
}

void UltrasoundView::onEntryReportState()
{
    sendSyncEventTo(UEventType::EVENT_RUNREPORT);
}

void UltrasoundView::onEndCurState()
{
    sendSyncEventTo(UEventType::COMMAND_ENDCURRENTSTATE);
}

void UltrasoundView::updateCommentControlState()
{
    CommentGlyphsControl* control = dynamic_cast<CommentGlyphsControl*>(
        GlyphsControlManager::instance().getGlyphsControl(GlyphsCtl::CommentGlyphsType));
    qDebug() << PRETTY_FUNCTION << control->overlay().limitRect() << control->overlay().sceneRect();
    control->overlay().setLimitRect(control->overlay().sceneRect());
}

void UltrasoundView::updateBodyMarkControlState()
{
    BodyMarkGlyphsControl* glyphsControl = dynamic_cast<BodyMarkGlyphsControl*>(
        GlyphsControlManager::instance().getGlyphsControl(GlyphsCtl::BodyMarkGlyphsType));
    glyphsControl->onBeginAction();
}

void UltrasoundView::updateArrowControlState()
{
    ArrowGlyphsControl* control = dynamic_cast<ArrowGlyphsControl*>(
        GlyphsControlManager::instance().getGlyphsControl(GlyphsCtl::ArrowGlyphsType));
    if (control->isPreparedComplete())
    {
        control->updateAtFullScreen();
    }
}

void UltrasoundView::onProbeDialogClosed()
{
    FuncStateInput input;
    input.isSuspendRead = true;
    m_ImageManager->exitState(Img_BeamFormerProbeState, input);
    sendSyncEventTo(UEventType::EVENT_EXITPROBE);
}

void UltrasoundView::onProbeDialogAccepted()
{
    //如果ProbeOk，系统将进入实时状态，不需要再suspendRead
    m_ImageManager->exitState(Img_BeamFormerProbeState);

    if (MenuController::instance().isShowingLeftMenu())
    {
        MenuController::instance().setShowLeftMenu(false);
    }

    //在设置preset时，内部会控制freeze false，导致imagingstate中无法触发以下代码，此处加上
    //设置参数 用于区分解冻状态与清空测量项的状态下的删除图像
    sendSyncEventTo(UEventType::COMMAND_DELETEGLYPHS, true);

    // 根据配置决定是否重设TGC
    if (ModelConfig::instance().value(ModelConfig::ProbeSelectedResetTGC, false).toBool())
    {
        m_ImageManager->resetTGCData();
    }
}

void UltrasoundView::onReportDialogClosed()
{
    sendSyncEventTo(UEventType::EVENT_EXITREPORT);
}

void UltrasoundView::onMeasurementMenuReportButtonClicked()
{
    sendSyncEventTo(UEventType::EVENT_RUNREPORT);
}

void UltrasoundView::onSettingsDialogClosed()
{
    sendSyncEventTo(UEventType::EVENT_EXITSETUP);
}

void UltrasoundView::onSettingsDialogHwKeyImported()
{
    m_ImageManager->sendHardwareKey();
}

void UltrasoundView::onArchiveStateClosed()
{
    ImageSkimManager* imageClipWidget = getImageClipWidget();
    imageClipWidget->checkExist();
    sendSyncEventTo(UEventType::COMMAND_EXITARCHIVE, NULL, ObjectId::ARCHIVESTATE);
}

void UltrasoundView::onEntryPatientState()
{
    if (m_PatientWorkflowModel->isInEditExamState())
    {
        sendSyncEventTo(UEventType::EVENT_EDITEXAMPATIENT);
        m_PatientWorkflowModel->setIsInEditExamState(false);
    }
    else
    {
        sendSyncEventTo(UEventType::EVENT_RUNPATIENT);
    }
}

void UltrasoundView::onEntryContinueExamState()
{
    sendSyncEventTo(UEventType::EVENT_CONTINUEEXAM);
}

void UltrasoundView::onEntryEditExamState()
{
    sendSyncEventTo(UEventType::EVENT_EDITEXAM);
}

void UltrasoundView::onArchiveStateOnlyJump()
{
    sendSyncEventTo(UEventType::FUN_ONLYJUMP, NULL, ObjectId::ARCHIVESTATE);
}

void UltrasoundView::onEasyViewDialogClosed()
{
    QString state_name = m_StateManager->currentState()->name();
    if (state_name == "EditExamEasyView")
    {
        sendSyncEventTo(UEventType::EVENT_EXITBROWSE, NULL, ObjectId::EDITEXAMEASYVIEWSTATE);
    }
    else
    {
        sendSyncEventTo(UEventType::EVENT_EXITBROWSE, NULL, ObjectId::EASYVIEWSTATE);
    }
}

void UltrasoundView::onEntryBrowseState()
{
    sendSyncEventTo(UEventType::EVENT_RUNBROWSE);
}

void UltrasoundView::onEasyViewStateOnlyJump()
{
    QString state_name = m_StateManager->currentState()->name();
    if (state_name == "EditExamEasyView")
    {
        sendSyncEventTo(UEventType::FUN_ONLYJUMP, NULL, ObjectId::EDITEXAMEASYVIEWSTATE);
    }
    else
    {
        sendSyncEventTo(UEventType::FUN_ONLYJUMP, NULL, ObjectId::EASYVIEWSTATE);
    }
}

void UltrasoundView::onFourdCallBack(const QString& filepath)
{

    QString state_name = m_StateManager->currentState()->name();
    if (state_name == "EditExamEasyView")
    {
        sendSyncEventTo(UEventType::TOOL_4DCALLBACK, filepath.toStdString(), ObjectId::EDITEXAMEASYVIEWSTATE);
    }
    else
    {
        sendSyncEventTo(UEventType::TOOL_4DCALLBACK, filepath.toStdString(), ObjectId::EASYVIEWSTATE);
    }
}

void UltrasoundView::onSetCurVirKeyboardEnglish(bool isEnglish)
{
    Q_ASSERT(m_ExamManager != NULL);
    ISystemStatusModel* systemStatusModel = m_ExamManager->systemStatusModel();
    Q_ASSERT(systemStatusModel != NULL);
    systemStatusModel->SetCurVirKeyboardEnglish(isEnglish);
}

void UltrasoundView::createMainWindow()
{
    beforeBehavior(PRETTY_FUNCTION);

    IMainWindowFactory* mwFactory = MainWindowFactoryCreator::createFactory(AppSetting::model());
    m_MainWindow = mwFactory->create(m_ViewerContext);
    m_MainWindow->setWindowFlags(Qt::Window | Qt::FramelessWindowHint);
    connect(m_MainWindow, SIGNAL(posChanged()), this, SLOT(resetImageRects()));
#ifdef SYS_WINDOWS
    connect(m_MainWindow, SIGNAL(dealNativeEvent(const QByteArray&, void*, long*)), this,
            SIGNAL(dealNativeEvent(const QByteArray&, void*, long*)));
#endif
    delete mwFactory;

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createLicenseInitializer()
{
    m_LicenseInitializer = new LicenseInitializer;
}

void UltrasoundView::initTcpServer()
{
    beforeBehavior(PRETTY_FUNCTION);

    Q_ASSERT(m_ImageManager);
    Q_ASSERT(m_ImageManager->beamFormer());
    TcpServer::instance().setBeamFormer(m_ImageManager->beamFormer());
    TcpServer::instance().setStateManager(m_StateManager);
    TcpServer::instance().setColorMapManager(m_ColorMapManager);
    TcpServer::instance().setProbeDataSet(m_ImageManager->probeDataSet());

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createBFHWInfoModel()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_BFHWInfoModel = new BFHWInfoModel;
    m_BFHWInfoModel->setViewParent(m_MainWindow);
    Q_ASSERT(m_ImageManager);
    m_BFHWInfoModel->setBeamFormer(m_ImageManager->beamFormer());
    m_BFHWInfoModel->setImageSkimManager(getImageClipWidget());
    m_BFHWInfoModel->setStateManager(m_StateManager);
    m_BFHWInfoModel->setColorMapManager(m_ColorMapManager);
    m_BFHWInfoModel->setProbeDataSet(m_ImageManager->probeDataSet());
    m_BFHWInfoModel->setDicomUtilityCreator(m_ExamManager->dicomUtilityCreator());
    m_BFHWInfoModel->setHardWareMonitorManager(m_PeripheralManager->hardWareMonitorManager());

    MainWindowTopWidget* mainWindowTopWidget =
        dynamic_cast<MainWindowTopWidget*>(m_ViewerContext->mainWindowTopWidget());
    Q_ASSERT(mainWindowTopWidget != NULL);
    connect(mainWindowTopWidget, SIGNAL(logoClicked()), m_BFHWInfoModel, SLOT(showView()));

    TitleBarWidget* titleBarWidget = dynamic_cast<TitleBarWidget*>(m_ViewerContext->titleBarWidget());
    // Q_ASSERT(titleBarWidget);   //TODO:不同机型不一定会创建这个窗体，去掉断言
    connect(titleBarWidget, SIGNAL(logoClicked()), m_BFHWInfoModel, SLOT(showView()));

    Q_ASSERT(m_ImageManager);
    IBeamFormer* beamformer = m_ImageManager->beamFormer();
    connect(beamformer, SIGNAL(highTemperatureDetected()), m_BFHWInfoModel, SLOT(onHighTemperatureDetected()));
    connect(beamformer, SIGNAL(voltageInfoChanged(QVector<VoltageData>, QVector<unsigned short>)), m_BFHWInfoModel,
            SLOT(onVoltageInfoChanged(QVector<VoltageData>, QVector<unsigned short>)));
    connect(beamformer, SIGNAL(headDataUpdated(uchar*, int)), m_BFHWInfoModel, SLOT(onHeadDataUpdated(uchar*, int)));
    connect(beamformer, SIGNAL(probeTemperatureWarning(int)), m_BFHWInfoModel, SLOT(onProbeTemperatureWarning(int)));
    connect(beamformer, SIGNAL(teeProbeTemperatureWarning(int, int)), m_BFHWInfoModel,
            SLOT(onTeeProbeTemperatureWarning(int, int)));

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createFpsInfoWidget()
{
    beforeBehavior(PRETTY_FUNCTION);

    FpsInfoWidget* fpsInfoWidget = new FpsInfoWidget(m_MainWindow);
    m_ViewerContext->setFpsInfoWidget(fpsInfoWidget);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::initializeTopInfoWidget()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_MainWindowTopInfoModel = new MainWindowTopInfoModel;

    Q_ASSERT(m_ImageManager != NULL);
    m_MainWindowTopInfoModel->setSonoParameters(m_ImageManager->sonoParameters());
    m_MainWindowTopInfoModel->setProbeDataSet(m_ImageManager->probeDataSet());

    Q_ASSERT(m_ExamManager != NULL);
    connect(m_ExamManager->patientWorkflow(), SIGNAL(patientChanged(Patient*)), m_MainWindowTopInfoModel,
            SLOT(setPatientInfo(Patient*)));
    connect(m_ExamManager->patientWorkflow(), SIGNAL(patientInfoChanged(Patient*)), m_MainWindowTopInfoModel,
            SLOT(setPatientInfo(Patient*)));

    IImageWidget* imageWidget = dynamic_cast<IImageWidget*>(m_ViewerContext->imageWidget());
    Q_ASSERT(imageWidget != NULL);
    connect(imageWidget->imageTile(), SIGNAL(changeParameters(SonoParameters*)), m_MainWindowTopInfoModel,
            SLOT(changeParameters(SonoParameters*)));

    MainWindowTopWidget* mainWindowTopWidget =
        dynamic_cast<MainWindowTopWidget*>(m_ViewerContext->mainWindowTopWidget());
    Q_ASSERT(mainWindowTopWidget != NULL);
    mainWindowTopWidget->setMainWindowTopInfoModel(m_MainWindowTopInfoModel);
    mainWindowTopWidget->setHaveTitleBar(m_ViewerContext->titleBarWidget() != nullptr);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createCursorMouseActionsModel()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_CursorMouseActionsModel = new CursorMouseActionsModel;
    m_CursorMouseActionsModel->setGrabedWidget(m_ViewerContext->imageWidget());
    curImageTile()->setMouseAction(m_CursorMouseActionsModel);
    curImageTile()->setGrabedWidget(m_ViewerContext->imageWidget());
    m_ViewerContext->setCursorMouseActionsModel(m_CursorMouseActionsModel);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::initializeSonoParasWidget()
{
    beforeBehavior(PRETTY_FUNCTION);

    SonoParaModelsFileProvider provider;
    QString path = provider.getResourceFile(AppSetting::model().toStdString().c_str());
    ISonoParasWidget* sonoParasWidget = dynamic_cast<ISonoParasWidget*>(m_ViewerContext->sonoParasWidget());
    Q_ASSERT(sonoParasWidget != NULL);
    sonoParasWidget->loadSonoParaModels(path);
    sonoParasWidget->setColorMapManager(m_ColorMapManager);

    Q_ASSERT(m_ImageManager != NULL);
    Q_ASSERT(m_ExamManager != NULL);
    SonoParametersClientBase* sonoParamClient = dynamic_cast<SonoParametersClientBase*>(sonoParasWidget);
    sonoParamClient->setSonoParameters(m_ImageManager->sonoParameters());
    sonoParasWidget->setPatientWorkflow(m_ExamManager->patientWorkflow());
    sonoParasWidget->setProbeDataSet(m_ImageManager->probeDataSet());

    ImageTile* imageTile = curImageTile();
    Q_ASSERT(imageTile != NULL);
    ColorBarWidget* colorBarWidget = sonoParasWidget->colorBarWidget();
    Q_ASSERT(colorBarWidget != NULL);
    connect(imageTile, SIGNAL(colorBarChanged(ColorBarModel*)), colorBarWidget,
            SLOT(onColorBarChanged(ColorBarModel*)));
    connect(imageTile, SIGNAL(colorBarGroupChanged(QList<ColorBarModel*>)), colorBarWidget,
            SLOT(onColorBarGroupChanged(QList<ColorBarModel*>)));
    colorBarWidget->onColorBarGroupChanged(imageTile->activeColorBarModelGroup());
    //    connect(imageTile, SIGNAL(changeParameters(SonoParameters*)),
    //            m_Context->sonoParasWidget(), SLOT(changeParameters(SonoParameters*)));
    connect(m_ImageManager->beamFormer(), SIGNAL(probeIdChanged()), sonoParasWidget, SLOT(onIsVisibleTeeTmp()));
    connect(m_ExamManager->patientWorkflow(), SIGNAL(patientInfoChanged(Patient*)), sonoParasWidget,
            SLOT(onIsVisibleOBLmp(Patient*)));
    connect(&Setting::instance().defaults(), SIGNAL(settingChanged(QString)), sonoParasWidget,
            SLOT(settingChanged(QString)));

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createGlyphsControlCreator()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_GlyphsControlCreator = new GlyphsControlCreator;

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::initializeImageWidget()
{
    beforeBehavior(PRETTY_FUNCTION);

    //原MainModule::initializeImageWidget()里的业务
    IImageWidget* imagewidget = dynamic_cast<IImageWidget*>(m_ViewerContext->imageWidget());
    Q_ASSERT(imagewidget != NULL);
    IBeamFormer* beamformer = m_ImageManager->beamFormer();
    Q_ASSERT(beamformer);
    SonoParameters* sonoParameters = m_ImageManager->sonoParameters();
    Q_ASSERT(sonoParameters);
    imagewidget->setBeamFormer(beamformer);
    imagewidget->setColorMapManager(m_ColorMapManager);
    imagewidget->setSonoParameters(sonoParameters);
    Q_ASSERT(m_GlyphsControlCreator != NULL);
    imagewidget->initImageTile(m_GlyphsControlCreator);

    connect(imagewidget, SIGNAL(zoomFactorChanged(qreal)), &GlyphsControlManager::instance(),
            SLOT(onUpdateFullScreenAmplificationFactor(qreal)));

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::initializeFreezeBarWidget()
{
    beforeBehavior(PRETTY_FUNCTION);

    Q_ASSERT(m_ImageManager != NULL);

    FreezeBarWidget* freezeBarWidget = dynamic_cast<FreezeBarWidget*>(m_ViewerContext->freezeBarWidget());
    Q_ASSERT(freezeBarWidget != NULL);
    freezeBarWidget->setBufferManager(m_ImageManager->lineBufferManager());
    ICineLooper* cineloopler = dynamic_cast<ICineLooper*>(m_ImageManager->chisonUltrasoundContext()->cineLooper());
    Q_ASSERT(cineloopler != NULL);
    freezeBarWidget->setCineLopper(cineloopler);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createProgressSliderWidget()
{
    beforeBehavior(PRETTY_FUNCTION);

    ProgressSliderWidget* progressSliderWidget = new ProgressSliderWidget(m_MainWindow); //由Qt对象树释放对象
    progressSliderWidget->setPositionWidget(m_ViewerContext->freezeBarWidget());
    m_ViewerContext->setProgressSliderWidget(progressSliderWidget);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createStoreProgressMovieWidget()
{
    beforeBehavior(PRETTY_FUNCTION);

    StoreProgressMovieWidget* widget = new StoreProgressMovieWidget(m_MainWindow); //由Qt对象树释放对象
    widget->setPositionWidgets(m_ViewerContext->freezeBarWidget(), m_ViewerContext->sonoParasWidget());
    m_ViewerContext->setStoreProgressMovieWidget(widget);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createSystemStatusAction()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_SystemStatusTipAction = new SystemStatusTipAction;

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createSystemTipModel()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_SystemTipModel = new SystemTipModel; // TODO:SystemTipModel命名不合理，应该是View类
    m_SystemTipModel->setMainWindow(m_MainWindow);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::initializeScreenshots()
{
    beforeBehavior(PRETTY_FUNCTION);

    Screenshots::instance().setImageTileScreenshots(curImageTile());

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createMeasurementWidgets()
{
    beforeBehavior(PRETTY_FUNCTION);

    MeasureResultListWidget* w = new MeasureResultListWidget(m_MainWindow);
    w->setScreenMeasureController(m_MarkManager->screenMeasureController());
    m_ViewerContext->setMeasureResultListWidget(w);
    w->installEventFilter(m_MainWindow);

    MeasurementViewDispatcher::instance().initialize(m_MarkManager->rulerFactory());

    w->bindScreenMeasResults();

    MeasureContext* measureContext = m_MarkManager->measureContext();
    Q_ASSERT(measureContext != NULL);

    m_SonoZoomWidget = new SonoZoomWidget(m_ViewerContext->imageWidget());
    m_ViewerContext->setSonoZoomWidget(m_SonoZoomWidget);
    IImageWidget* imagewidget = dynamic_cast<IImageWidget*>(m_ViewerContext->imageWidget());
    Q_ASSERT(imagewidget != NULL);
    m_SonoZoomControl =
        new SonoZoomControl(imagewidget, m_SonoZoomWidget, measureContext, m_ImageManager->bufferStoreManager());

    m_MeasureInputWidget = new MeasureInputWidget(measureContext);
    m_FetalHeadPosWidget = new FetalHeadPosWidget(measureContext);
    m_FetalHeadDirectionWidget = new FetalHeadDirectionWidget(measureContext);

    connect(measureContext, SIGNAL(updateSonoParameter(SonoParameters*)), m_SonoZoomControl,
            SLOT(updateSonoParameter(SonoParameters*)));
    connect(imagewidget->imageTile(), SIGNAL(clearMeasureContextImage()), measureContext,
            SLOT(clearMeasureContextImage()), Qt::QueuedConnection);
    connect(imagewidget->imageTile(), &ImageTile::renderImageChanged, measureContext,
            &MeasureContext::onRenderImageChanged);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createProbeSelectionModel()
{
    beforeBehavior(PRETTY_FUNCTION);

    Q_ASSERT(m_ImageManager != NULL);
    Q_ASSERT(m_ExamManager != NULL);
    Q_ASSERT(m_ViewerContext != NULL);

    IPresetUser* presetUser = dynamic_cast<IPresetUser*>(m_ImageManager->sonoParameters());
    Q_ASSERT(presetUser != NULL);
    IPresetModel* presetModel = m_ExamManager->presetModel();
    Q_ASSERT(presetModel != NULL);
    IProbePresetModel* probepresetmodel = m_ImageManager->probePresetModel();
    Q_ASSERT(probepresetmodel != NULL);
    IExamModeConfigModel* examModeConfigModel = m_ExamManager->examModeConfigModel();
    Q_ASSERT(examModeConfigModel != NULL);
    IProbeDataSet* probeDataSet = m_ImageManager->probeDataSet();
    Q_ASSERT(probeDataSet != NULL);
    QWidget* parent = dynamic_cast<QWidget*>(m_MainWindow);

    // TODO: IProbeSelectionModel业务子对象的构建放在了view类初始化接口中完成，
    // 这个有点鸡肋，原因主要为该业务对象依赖应用（application）部分业务（主要是预设业务）对象的先构建，所以不能直接放在图像业务类初始化接口中完成
    // 后续可以考虑，将预设业务从应用子模块中移出，单独定义
    if (ModelConfig::instance().value(ModelConfig::UseProbeOrganismSelection, true).toBool())
    {
        m_ProbeSelectionModel = new ProbeOrganismSelectionModel(presetUser, presetModel, probepresetmodel,
                                                                examModeConfigModel, probeDataSet);
        m_ProbeSelectionWidget =
            new ProbeOrganismSelectionWidget(probepresetmodel->currentProbes().count(), m_ProbeSelectionModel, parent);
    }
    else
    {
        m_ProbeSelectionModel = new ProbeSelectionModel(presetUser, presetModel, probepresetmodel, examModeConfigModel);
        m_ProbeSelectionWidget =
            new ProbeSelectionWidget(probepresetmodel->currentProbes().count(), m_ProbeSelectionModel, parent);
    }

    ProbeSelectionDispatcher::instance()->init(m_ProbeSelectionModel, m_ProbeSelectionWidget);

    connect(ProbeSelectionDispatcher::instance(), SIGNAL(accepted()), this, SLOT(onProbeDialogAccepted()));
    connect(ProbeSelectionDispatcher::instance(), SIGNAL(rejected()), this, SLOT(onProbeDialogClosed()));

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createExamModeMenuModel()
{
    beforeBehavior(PRETTY_FUNCTION);

    Q_ASSERT(m_ImageManager != NULL);
    Q_ASSERT(m_ExamManager != NULL);
    Q_ASSERT(m_ViewerContext != NULL);

    m_ExamModeMenuModel = new ExamModeMenuModel(m_ImageManager->sonoParameters(), m_ExamManager->presetModel(),
                                                m_ImageManager->probePresetModel(),
                                                m_ExamManager->examModeConfigModel(), m_ColorMapManager);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createQuickCommentPopupWidget()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_QuickCommentPopupWidget = new QuickCommentPopupWidget;
    m_ViewerContext->setQuickCommentPopupWidget(m_QuickCommentPopupWidget);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createReportDialog()
{
    beforeBehavior(PRETTY_FUNCTION);

    ReportDialog* dlg = new ReportDialog(m_MainWindow);
    dlg->setSubSystemSubject(dynamic_cast<USFObject*>(this));
    dlg->setColorMapManager(m_ColorMapManager);
    IReportSectionManager* petSectionManager = m_ExamManager->reportSectionManager();
    Q_ASSERT(petSectionManager != NULL);
    dlg->setReportSectionManager(petSectionManager);
    IOBCalc* OBCalc = m_MarkManager->OBcalc();
    Q_ASSERT(OBCalc != NULL);
    dlg->setOBCalc(OBCalc);

    connect(dlg, SIGNAL(closed()), this, SLOT(onReportDialogClosed()));
    MeasurementMenu* measurementMenu = dynamic_cast<MeasurementMenu*>(m_ViewerContext->measurementMenu());
    Q_ASSERT(measurementMenu != NULL);
    connect(measurementMenu, SIGNAL(reportButtonClicked()), this, SLOT(onMeasurementMenuReportButtonClicked()));

    ReportFileLoadHelper::setColorMapManager(m_ColorMapManager);
    ReportFileLoadHelper::setReportSectionManager(petSectionManager);

    m_ViewerContext->setReportDialog(dlg);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createSettingsDialog()
{
    beforeBehavior(PRETTY_FUNCTION);

    // edit xule
    AppSetting::setUpdateMode(ModelConfig::instance().value(ModelConfig::HwUpateMode, 0).toInt());
    AppSetting::setHwUpdateModeUI(ModelConfig::instance().value(ModelConfig::HwUpateModeUI, 0).toInt());

    ReservedSettingManager().restoreSetting();

    ConfigurationDialog* dlg = new ConfigurationDialog(m_StateManager, m_AdminConfigModel, m_MainWindow);
    m_ViewerContext->setConfigurationDialog(dlg);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createQuickMeasureLoop()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_QuickMeasureCaptionsLoops.append(new QuickMeasureCaptionsLoop); // dloop
    m_QuickMeasureCaptionsLoops.append(new QuickMeasureCaptionsLoop); // tloop
    m_QuickMeasureCaptionsLoops.append(new QuickMeasureCaptionsLoop); // vloop

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createStressEchoWidgetManager()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_StressEchoModel = new StressEchoModel(m_MainWindow);
    m_StressEchoWidgetManager = new StressEchoWidgetManager(m_StressEchoModel, m_MainWindow);
    m_StressEchoModel->setUSFObject(dynamic_cast<USFObject*>(this));
    m_StressEchoModel->setStateManager(m_StateManager);

    Q_ASSERT(m_ExamManager != NULL);

    IPatientWorkflow* patientWorkflow = m_ExamManager->patientWorkflow();
    StressEchoWorkWidget* stressEchoWorkWidget = m_StressEchoWidgetManager->stressEchoWorkWidget();

    ImageSkimManager* imageClipWidget = getImageClipWidget();
    connect(imageClipWidget, SIGNAL(imageRemoved()), stressEchoWorkWidget, SLOT(onImageRemoved()));
    connect(m_StressEchoModel, SIGNAL(addScreenShot(const QString&)), imageClipWidget,
            SLOT(onAddScreenShot(const QString&)));
    connect(patientWorkflow, SIGNAL(patientChanged(Patient*)), stressEchoWorkWidget, SLOT(setPatientInfo(Patient*)));
    connect(patientWorkflow, SIGNAL(patientContinuous(Patient)), stressEchoWorkWidget, SLOT(doContinue(Patient)));
    connect(patientWorkflow, SIGNAL(patientEnded(Patient, bool)), stressEchoWorkWidget, SLOT(doEnd(Patient, bool)));
    connect(&ApplicationInfo::instance(), SIGNAL(languageChanged()), stressEchoWorkWidget, SIGNAL(languageChanged()));

    LeftMenuWidget* leftMenuWidget = dynamic_cast<LeftMenuWidget*>(m_ViewerContext->leftMenuWidget());
    Q_ASSERT(leftMenuWidget != NULL);
    connect(stressEchoWorkWidget, SIGNAL(stressEchoEnChanged(bool)), leftMenuWidget, SLOT(onStressEchoEnChanged(bool)));

    m_ViewerContext->setStressEchoWorkWidget(stressEchoWorkWidget);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::initializeLGC()
{
    beforeBehavior(PRETTY_FUNCTION);

    LGCRegulatorDialog* regulator = new LGCRegulatorDialog();
    m_ViewerContext->setLGCRegulatorDialog(regulator);
    Q_ASSERT(m_ImageManager != NULL);
    Q_ASSERT(m_ExamManager != NULL);
    regulator->setSonoParameters(m_ImageManager->sonoParameters());
    connect(m_ExamManager->presetModel(), &IPresetModel::examModeChanged, regulator,
            &LGCRegulatorDialog::onExamModeChanged);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createThumbnailDialog()
{
    beforeBehavior(PRETTY_FUNCTION);

    ThumbnailDialog* dlg = new ThumbnailDialog(m_MainWindow);
    m_ViewerContext->setThumbnailDialog(dlg);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createSonoParasWidgetController()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_SonoParasWidgetController = new SonoParasWidgetController(this);
    SonoParametersClientBase* sonoparaswidget =
        dynamic_cast<SonoParametersClientBase*>(m_ViewerContext->sonoParasWidget());
    Q_ASSERT(sonoparaswidget != NULL);
    m_SonoParasWidgetController->setSonoParasWidget(sonoparaswidget);
    m_SonoParasWidgetController->setDopplerThetaAdjustItem(m_ViewerContext->DopplerAdjustItem());
    m_SonoParasWidgetController->setBufferManager(m_ImageManager->chisonUltrasoundContext()->lineBuffer());
    m_SonoParasWidgetController->setBufferStoreManager(m_ImageManager->bufferStoreManager());
    MainWindowMiddleDownWidget* widget =
        dynamic_cast<MainWindowMiddleDownWidget*>(m_ViewerContext->mainWindowMiddleDownWidget());
    Q_ASSERT(widget != NULL);
    m_SonoParasWidgetController->setMainWindowMiddleDownWidget(widget);
    m_SonoParasWidgetController->setMeasureContext(m_MarkManager->measureContext());
    m_SonoParasWidgetController->setSonoParameters(m_ImageManager->sonoParameters());
    m_SonoParasWidgetController->setStateManager(m_StateManager);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createSonoHelpController()
{
    beforeBehavior(PRETTY_FUNCTION);

    if (ModelConfig::instance().value(ModelConfig::IsCreateLeftTopWidget, false).toBool())
    {
        LeftTopMenuWidgetContainer* container =
            dynamic_cast<LeftTopMenuWidgetContainer*>(m_ViewerContext->leftTopMenuWidgetContainer());
        Q_ASSERT(container != NULL);
        m_SonoHelpController = new SonoHelpController(m_ImageManager->sonoParameters(), container,
                                                      cursorMouseActionsModel(), m_StateManager, this);
    }

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::controlDopplerAdjustItem()
{
    beforeBehavior(PRETTY_FUNCTION);

    // Doppler角度调节显示、隐藏控制逻辑（暂时写在这里）
    DopplerThetaAdjustItem* dopplerThetaAdjustItem = m_ViewerContext->DopplerAdjustItem();
    if (dopplerThetaAdjustItem != NULL)
    {
        if (!dopplerThetaAdjustItem->scene())
        {
            RightMenuWidgetContainer* rightMenuWidgetContainer =
                dynamic_cast<RightMenuWidgetContainer*>(m_ViewerContext->sonoParasWidget()->parentWidget());
            rightMenuWidgetContainer->painterPwAngleView();
        }
        dopplerThetaAdjustItem->setVisible(false);

        //        /********************************控制Doppler角度调节ITEM显示隐藏的信号********************************/
        //        // 1、当IsDopplerScanLineVisibleStr为true时显示，反之不显示
        //        connect(m_Context->sonoParameters()->parameter(BFPNames::IsDopplerScanLineVisibleStr),
        //        &Parameter::valueChanged, this, &MainModule::onControlDopplerAdjustItem);

        //        // 2、当systemScanMode为SystemScanModBPW时显示，反之不显示
        //        connect(m_Context->sonoParameters()->parameter(BFPNames::SystemScanModeStr), &Parameter::valueChanged,
        //        this, &MainModule::onControlDopplerAdjustItem);

        //        // 3、冻结控制
        //        connect(m_Context->sonoParameters()->parameter(BFPNames::FreezeStr), &Parameter::valueChanged, this,
        //        &MainModule::onControlDopplerAdjustItem);
        //        /*********DESC:
        //        本意是通过IsDopplerScanLineVisibleStr实现显示隐藏，但是实测这个变量在PW下为false*********/
    }

    if (dopplerThetaAdjustItem != NULL)
    {
        dopplerThetaAdjustItem->setStateManager(m_StateManager);
    }

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createFlexButtons()
{
    beforeBehavior(PRETTY_FUNCTION);

    MainWindowKBUnit* mainWindow = dynamic_cast<MainWindowKBUnit*>(m_MainWindow);
    Q_ASSERT(mainWindow != NULL);

    mainWindow->initGlobalFlexButtons();

    connect(mainWindow->getFullScreenFlexBtn(), SIGNAL(btn_clicked()), this, SLOT(fullScreenFlexBtnCliecked()));

    connect(m_ImageManager->sonoParameters()->parameter(BFPNames::IsSecondGearFullScreenZoomInStr),
            &Parameter::valueChanged, this, [=](QVariant state) {
                Util::changeQssWidgetProperty(mainWindow->getFullScreenFlexBtn(), "state",
                                              state.toBool() ? "on" : "off");
            });

    connect(mainWindow->getLockScreenFlexBtn(), &FlexButton::btn_clicked, this,
            [=]() { Setting::instance().defaults().setLockScreen(!Setting::instance().defaults().getLockScreen()); });

    connect(&Setting::instance().defaults(), SIGNAL(settingChanged(QString)), this,
            SLOT(onLockScreenStateChanged(QString)));

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createMultiScreenSync()
{
    beforeBehavior(PRETTY_FUNCTION);

    if (nullptr != m_ExamManager)
    {
        m_ExamManager->iMultiScreenSyncController()->setMainWindow(m_MainWindow);
    }

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createZoomOnProxy()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_ZoomOnProxy = new ZoomOnProxy(this);
    Q_ASSERT(m_ImageManager != NULL);
    Q_ASSERT(m_ImageManager->beamFormer());
    ZoomOnProxy* zoomonproxy = dynamic_cast<ZoomOnProxy*>(m_ZoomOnProxy);
    Q_ASSERT(zoomonproxy != NULL);
    IBFImageState* bfimagestate = dynamic_cast<IBFImageState*>(m_ImageManager->beamFormer());
    Q_ASSERT(bfimagestate != NULL);
    zoomonproxy->setBFImageState(bfimagestate);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::initializeBegin()
{
    beforeBehavior(PRETTY_FUNCTION);

    MenuController::instance().setToolsFacade(
        m_ToolsFacade); // 部分UI的构建，依赖MenuController中对m_ToolsFacade的初始化完毕，因此，提前做初始化
    MenuController::instance().setStateManager(m_StateManager);
    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::registerZoomOnProxyHideWidget()
{
    m_ZoomOnProxy->registerHideWidget(IZoomOnProxy::ClipBoard_Widget, m_ViewerContext->mainWindowMiddleDownWidget());
    m_ZoomOnProxy->registerHideWidget(IZoomOnProxy::TopTitle_Widget, m_ViewerContext->mainWindowTopWidget());
    m_ZoomOnProxy->registerHideWidget(IZoomOnProxy::SonoParas_Widget, m_ViewerContext->sonoParasWidget());
    m_ZoomOnProxy->registerHideWidget(IZoomOnProxy::SystemStatus_Widget, m_ViewerContext->systemStatusTip());

    ImageWidget* renderWidget = qobject_cast<ImageWidget*>(m_ViewerContext->imageWidget());
    if (renderWidget != NULL)
    {
        m_ZoomOnProxy->setRenderWidgetParent(renderWidget->parentWidget());
    }
}

void UltrasoundView::createStressEchoModel()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_StressEchoModel = new StressEchoModel(m_MainWindow);

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createAdminConfigModel()
{
#ifdef USE_ADMINVIEW
    beforeBehavior(PRETTY_FUNCTION);

    m_AdminConfigModel = new AdminConfigModel();
    m_AdminConfigModel->setMachine(m_PeripheralManager->machine());

    afterBehavior(PRETTY_FUNCTION);
#endif
}

void UltrasoundView::createPatientWorkflowModel()
{
    beforeBehavior(PRETTY_FUNCTION);

#ifdef USE_ADMINVIEW
    Q_ASSERT(m_AdminConfigModel != NULL);
#endif
    m_PatientWorkflowModel =
        new PatientWorkflowModel(m_StateManager, m_AdminConfigModel, m_MainWindow); // TODO:此处需要父窗口，需要再推敲
    m_PatientWorkflowModel->setPatientWorkflow(m_ExamManager->patientWorkflow());
    m_PatientWorkflowModel->setDicomTaskManager(m_ExamManager->dicomTaskManager());
    m_PatientWorkflowModel->setSubSystemSubject(dynamic_cast<USFObject*>(this));
    m_PatientWorkflowModel->setDiskDevice(m_DiskDevice);
    m_PatientWorkflowModel->setColorMapManager(m_ColorMapManager);
    m_PatientWorkflowModel->setSystemInfo(m_SystemInfo);
    m_PatientWorkflowModel->setDicomUtilityCreator(m_ExamManager->dicomUtilityCreator());
    m_PatientWorkflowModel->setDicomToolCreator(m_ExamManager->dicomToolCreator());
    Q_ASSERT(m_ExamManager->reportSectionManager() != NULL);
    m_PatientWorkflowModel->setReportSectionManager(m_ExamManager->reportSectionManager());

    Q_ASSERT(m_ImageManager != NULL);
    ChisonUltrasoundContext* chisonUltrasoundContext = m_ImageManager->chisonUltrasoundContext();
    Q_ASSERT(chisonUltrasoundContext != NULL);
    StressEchoBufferManager* stressEchoBufferManager = chisonUltrasoundContext->stressEchoBufferManager();
    IBeamFormer* beamformer = m_ImageManager->beamFormer();

    m_PatientWorkflowModel->setGlanceImagesBufferManager(stressEchoBufferManager);
    m_PatientWorkflowModel->setGlanceImagesBeamFormer(beamformer);
    connect(m_PatientWorkflowModel, SIGNAL(autoCallBack(int)), m_ExamManager,
            SLOT(updateImageClipWidgetImageLable(int)));
    connect(m_PatientWorkflowModel, SIGNAL(patientDialogClosed(int)), m_ExamManager, SLOT(onPatientDialogClosed(int)));
    connect(m_PatientWorkflowModel, SIGNAL(entryArchiveState()), m_ExamManager, SLOT(onEntryArchiveState()));
    connect(m_PatientWorkflowModel, SIGNAL(onlyJump()), m_ExamManager, SLOT(onOnlyJump()));

    afterBehavior(PRETTY_FUNCTION);
}

void UltrasoundView::createTGCMenuWidgetContainer()
{
    if (nullptr == m_TGCMenuWidgetContainer)
    {
        m_TGCMenuWidgetContainer = new TGCMenuWidgetContainer(TGC_COUNT);
        m_TGCMenuWidgetContainer->setTGCAdjustmentController(m_ImageManager->controllerOfTGCAdjustment());

        LeftMenuWidget* leftMenuWidget = dynamic_cast<LeftMenuWidget*>(m_ViewerContext->leftMenuWidget());
        if (nullptr != leftMenuWidget)
        {
            leftMenuWidget->setTGCMenuWidgetContainer(m_TGCMenuWidgetContainer);
        }
    }
}

void UltrasoundView::initializeEFastWidgets()
{
    if (nullptr != m_ExamManager && nullptr != m_ExamManager->efastManager() &&
        nullptr != m_ExamManager->efastManager()->menuWidget() &&
        nullptr != m_ExamManager->efastManager()->menuWidget())
    {
        LeftMenuWidget* leftMenuWidget = dynamic_cast<LeftMenuWidget*>(m_ViewerContext->leftMenuWidget());
        if (nullptr != leftMenuWidget)
        {
            leftMenuWidget->setEFastMenuWidget(m_ExamManager->efastManager()->menuWidget());
        }
        m_ExamManager->efastManager()->browserWidget()->move(
            m_ViewerContext->renderWidgetContainer()->mapToGlobal(QPoint(0, 0)));
        m_ExamManager->efastManager()->browserWidget()->setFixedSize(
            m_ViewerContext->renderWidgetContainer()->geometry().size());
        connect(getImageClipWidget(), &ImageSkimManager::imageRemoved, m_ExamManager->efastManager(),
                &IEFastManager::onImageRemoved);
    }
}

void UltrasoundView::initializeEnd()
{
    beforeBehavior(PRETTY_FUNCTION);

    Q_ASSERT(m_ImageManager != NULL);
    Q_ASSERT(m_ExamManager != NULL);
    Q_ASSERT(m_PeripheralManager != NULL);
    Q_ASSERT(m_ViewerContext != NULL);

    registerZoomOnProxyHideWidget();

    ReportPrintJob::instance().setSystemInfo(m_SystemInfo);

    IProbePresetModel* probePresetModel = m_ImageManager->probePresetModel();
    IImageWidget* imageWidget = dynamic_cast<IImageWidget*>(m_ViewerContext->imageWidget());
    Q_ASSERT(imageWidget != NULL);
    ImageTile* imageTile = imageWidget->imageTile();
    //以下connect原来在createProbePresetModel中定义，考虑到DopplerImage不能依赖UltrasoundView，现移到View类中
    connect(probePresetModel, SIGNAL(presetBeginChanged()), imageTile, SLOT(beginPause()));
    connect(probePresetModel, SIGNAL(presetEndChanged()), imageTile, SLOT(endPause()));

    //原MainModule::createChisonUltrasoundContext()里的涉及UI的业务
    FpsInfoWidget* fpsInfoWidget = dynamic_cast<FpsInfoWidget*>(m_ViewerContext->fpsInfoWidget());
    connect(imageTile, SIGNAL(fpsChanged(const QString&, const float)), fpsInfoWidget,
            SLOT(onFpsChanged(const QString&, const float)));
    ChisonUltrasoundContext* chisonContext = m_ImageManager->chisonUltrasoundContext();
    Q_ASSERT(chisonContext);
    connect(chisonContext, SIGNAL(fpsChanged(const QString&, const float)), fpsInfoWidget,
            SLOT(onFpsChanged(const QString&, const float)));
    connect(&FrameControl::instance(), SIGNAL(intellectMove(const QVariant&)), imageTile,
            SLOT(onIntellectMove(const QVariant&)));

    connect(chisonContext, SIGNAL(newImage(ImageEventArgs*)), imageWidget, SLOT(onNewImage(ImageEventArgs*)),
            Qt::DirectConnection);
    connect(chisonContext, SIGNAL(newPictureImage(QImage)), imageWidget, SLOT(onNewPictureImage(QImage)),
            Qt::DirectConnection);
    connect(chisonContext, SIGNAL(eraseImage()), imageWidget, SLOT(onEraseImage()), Qt::DirectConnection);
    connect(imageTile, SIGNAL(mapsChanged()), chisonContext, SLOT(onMapsChanged()), Qt::DirectConnection);

    uchar* map1;
    uchar* map2;
    imageTile->getColorMaps((void**)&map1, (void**)&map2);
    imageTile->setBufferManager(chisonContext->lineBuffer());
    m_ImageManager->setColorMap(map1, map2);

    m_BFHWInfoModel->setChisonUltrasoundContext(chisonContext);

    Q_ASSERT(m_ExamManager != NULL);
    m_BFHWInfoModel->setPatientWorkFlow(m_ExamManager->patientWorkflow());

    SystemHintModel* systemHintModel = m_ExamManager->systemHintModel();
    Q_ASSERT(systemHintModel != NULL);

    SystemHintWidget* systemHintWidget = dynamic_cast<SystemHintWidget*>(m_ViewerContext->systemHintWidget());
    if (systemHintWidget != NULL)
    {
        systemHintWidget->setModel(systemHintModel);
    }

    WorkStatusWidget* workStatusWidget = dynamic_cast<WorkStatusWidget*>(m_ViewerContext->workStatusWidget());
    if (workStatusWidget != NULL)
    {
        workStatusWidget->setModel(systemHintModel);
    }

    WorkStatusWidget* workStatusWidgetOfBottom =
        dynamic_cast<WorkStatusWidget*>(m_ViewerContext->workStatusWidgetOfBottom());
    if (workStatusWidgetOfBottom != NULL)
    {
        workStatusWidgetOfBottom->setModel(systemHintModel);
    }

    SystemTBStatusWidget* tbStatusWidget = dynamic_cast<SystemTBStatusWidget*>(m_ViewerContext->tBStatusWidget());
    SystemTBStatusWidget* tbStatusWidgetContainer =
        dynamic_cast<SystemTBStatusWidget*>(m_ViewerContext->tBStatusWidgetContainer());
    if (tbStatusWidget != NULL)
    {
        tbStatusWidget->setModel(systemHintModel);
    }

    if (tbStatusWidgetContainer != NULL)
    {
        tbStatusWidgetContainer->setModel(systemHintModel);
    }

    NavigationWidget* navigationWidget = dynamic_cast<NavigationWidget*>(m_ViewerContext->navigationWidget());
    if (navigationWidget != NULL)
    {
        navigationWidget->setModel(systemHintModel);

        HotKeyContainer* hotKeyContainer = m_PeripheralManager->hotKeyContainer();
        HotKeyConfig* hotKeyConfig = m_PeripheralManager->hotKeyConfig();
        Q_ASSERT(hotKeyContainer != NULL);
        Q_ASSERT(hotKeyConfig != NULL);
        navigationWidget->setHotKey(hotKeyContainer, hotKeyConfig);
    }

    ProgressSliderWidget* progressSliderWidget =
        dynamic_cast<ProgressSliderWidget*>(m_ViewerContext->progressSliderWidget());
    IBufferStoreManager* bufferStoreManager = m_ImageManager->bufferStoreManager();
    connect(bufferStoreManager, SIGNAL(notifyLoadingProgess(qreal)), progressSliderWidget,
            SLOT(onStoringProgess(qreal)));

    SystemStatusWidget* systemStatusTip = dynamic_cast<SystemStatusWidget*>(m_ViewerContext->systemStatusTip());
    Q_ASSERT(systemStatusTip != NULL);
    ISystemStatusModel* systemStatusModel = m_ExamManager->systemStatusModel();
    Q_ASSERT(systemStatusModel != NULL);
    systemStatusTip->setModel(systemStatusModel);
    TitleBarWidget* titleBarWidget = dynamic_cast<TitleBarWidget*>(m_ViewerContext->titleBarWidget());
    if (titleBarWidget != NULL)
    {
        titleBarWidget->insertBatteryStatusWidget(systemStatusTip->batteryStatusWidget());
    }

    m_SystemStatusTipAction->setModel(systemStatusModel);

    IBeamFormer* beamformer = m_ImageManager->beamFormer();
    Q_ASSERT(beamformer);
    connect(beamformer, SIGNAL(hardWareKeyValidChanged(bool)), m_SystemTipModel, SLOT(onHardWareKeyValidChanged(bool)));

    IMeasurement* packagesMeasurement = m_MarkManager->packagesMeasurement();
    Q_ASSERT(packagesMeasurement != NULL);
    MeasurementMenu* measurementMenu = dynamic_cast<MeasurementMenu*>(m_ViewerContext->measurementMenu());
    Q_ASSERT(measurementMenu != NULL);
    measurementMenu->setModel(packagesMeasurement);
    MenuPanelModel::instance().setMeasurement(packagesMeasurement);

    connect(m_ExamManager->patientWorkflow(), SIGNAL(patientInfoChanged(Patient*)), measurementMenu,
            SLOT(updatePatient(Patient*)));

    IPresetModel* presetModel = m_ExamManager->presetModel();
    Q_ASSERT(presetModel != NULL);
    connect(presetModel, SIGNAL(examModeChanged(const QString&)), m_MainWindowTopInfoModel,
            SIGNAL(examModeChanged(const QString&)));

    m_MarkManager->setScreenMeasureControllerOverlay(imageTileOverlay());
    m_MarkManager->setScreenMeasureControllerPackages(packagesMeasurement);

    IQuickCommentModel* quickCommentModel = m_MarkManager->quickCommentModel();
    Q_ASSERT(quickCommentModel != NULL);
    m_QuickCommentPopupWidget->setModel(quickCommentModel);

    ICommentMenuModel* commentMenuModel = m_MarkManager->commentMenuModel();
    Q_ASSERT(commentMenuModel != NULL);
    CommentMenu* commentMenu = dynamic_cast<CommentMenu*>(m_ViewerContext->commentMenu());
    Q_ASSERT(commentMenu != NULL);
    commentMenu->setModel(commentMenuModel);
    MenuPanelModel::instance().setCommentMenu(commentMenuModel);

    CommentGlyphsControl* cmtControl =
        GlyphsControlManager::instance().getChildGlyphsControl<CommentGlyphsControl>(GlyphsCtl::CommentGlyphsType);

    connect(cmtControl, SIGNAL(textChanged(QString)), commentMenuModel, SLOT(search(QString)));

#ifdef USE_VIRTUAL_KEYBOARD
    connect(cmtControl, SIGNAL(enterCommentEdit(QGraphicsItem*)), &KeyboardControl::instance(),
            SLOT(onEnterCommentEdit(QGraphicsItem*)));
    connect(cmtControl, SIGNAL(leaveCommentEdit()), &KeyboardControl::instance(), SLOT(hideKeyboard()));
#endif
    connect(cmtControl, &CommentGlyphsControl::leaveCommentEdit, m_PeripheralManager->keyProcessor(),
            &QtKeyProcessor::onCommentStateLeave);
    connect(commentMenu, SIGNAL(itemSelected(QString)), cmtControl, SLOT(setPredefinedComment(QString)));
    connect(commentMenuModel, SIGNAL(selectedComment(QString)), cmtControl, SLOT(setPredefinedComment(QString)));

    cmtControl->setStateManager(m_StateManager);

    BodyMarkViewModel* bodyMarkViewModel = m_MarkManager->bodyMarkViewModel();
    Q_ASSERT(bodyMarkViewModel != NULL);

    BodyMarkGlyphsControl* bodyMarkGlyphsControl = dynamic_cast<BodyMarkGlyphsControl*>(
        GlyphsControlManager::instance().getGlyphsControl(GlyphsCtl::BodyMarkGlyphsType));
    Q_ASSERT(bodyMarkGlyphsControl != NULL);

    ImageSkimManager* bodyMarkWidget = dynamic_cast<ImageSkimManager*>(m_ViewerContext->bodyMarkWidget());
    Q_ASSERT(bodyMarkWidget != NULL);

    connect(bodyMarkWidget, SIGNAL(selectedInfoChanged(IimageInfo*)), bodyMarkGlyphsControl,
            SLOT(imageSelectChanged(IimageInfo*)));
    connect(bodyMarkViewModel, SIGNAL(selectedInfoChanged(IimageInfo*)), bodyMarkGlyphsControl,
            SLOT(imageSelectChanged(IimageInfo*)));

    MenuPanelModel::instance().setBodyMarkView(bodyMarkViewModel);

    // this code comes from void MainModule::createMenuTools()
    MenuPanelModel::instance().setSonoParameters(m_ImageManager->sonoParameters());
    MenuController::instance().setCurButtonMode(
        (MenuOperationModeDef::OperationMode)ModelConfig::instance().value(ModelConfig::BtnOperationMode, 0).toInt());

    // init ConfigurationDialog
    ConfigurationDialog* configurationDialog =
        dynamic_cast<ConfigurationDialog*>(m_ViewerContext->configurationDialog());
    Q_ASSERT(configurationDialog != NULL);
    configurationDialog->setPackagesMeasurement(m_MarkManager->packagesMeasurement());
    configurationDialog->setCommentConfigModel(m_MarkManager->commentConfigModel());
    configurationDialog->setBodyMarkConfigModel(m_MarkManager->bodyMarkConfigModel());
    configurationDialog->setExamModeConfigModel(m_ExamManager->examModeConfigModel());
    configurationDialog->bindingMeasureControllerSlot(m_MarkManager->screenMeasureController());
    configurationDialog->setMeasurementCreator(m_MarkManager->measurementCreator());
    configurationDialog->setMeasurementItemCreator(m_MarkManager->measurementItemCreator());

    configurationDialog->setKeyboard(m_PeripheralManager->keyBoard());
    IMachine* machine = dynamic_cast<IMachine*>(m_PeripheralManager->machine());
    configurationDialog->setMachine(machine);
    configurationDialog->setHotKeyModels(m_PeripheralManager->hotKeyConfig(), m_PeripheralManager->hotKeyContainer());
    configurationDialog->setDicomTaskManager(m_ExamManager->dicomTaskManager());
    configurationDialog->setImageTile(curImageTile());
    configurationDialog->setBeamFormerTool(m_ImageManager->beamFormer());
    configurationDialog->setColorMapManager(m_ColorMapManager);
    configurationDialog->setIntellConnController(m_ExamManager->intellConnRemoteController());
    Q_ASSERT(m_ImageManager->probeDataSet() != NULL);
    configurationDialog->setProbeDataSet(m_ImageManager->probeDataSet());
    Q_ASSERT(m_MarkManager->commentExportAndImportDataModel() != NULL);
    Q_ASSERT(m_ExamManager->presetExportAndImportDataModel() != NULL);
    configurationDialog->setExportAndImportDataModel(m_MarkManager->commentExportAndImportDataModel(),
                                                     m_ExamManager->presetExportAndImportDataModel());

    configurationDialog->setRulerFactory(m_MarkManager->rulerFactory());
    BasePackageHandle* packageHandle = m_ExamManager->packageHandle();
    Q_ASSERT(packageHandle != NULL);
    configurationDialog->setPackageHandle(packageHandle);

    connect(configurationDialog, SIGNAL(closed()), this, SLOT(onSettingsDialogClosed()));
    connect(configurationDialog, SIGNAL(hwKeyImported()), this, SLOT(onSettingsDialogHwKeyImported()));

    connect(configurationDialog, SIGNAL(ntpButClicked(QString, QString, QString)),
            MenuController::instance().controlPanel(), SIGNAL(ntpButClicked(QString, QString, QString)));

    connect(MenuController::instance().controlPanel(), SIGNAL(tpSettingButClicked(QString, QString, QString)),
            configurationDialog, SIGNAL(tpButClicked(QString, QString, QString)));

    connect(configurationDialog, &ConfigurationDialog::systemDateChanged, this, [=]() {
        if (titleBarWidget != NULL)
        {
            titleBarWidget->updateDate();
        }
    });

    connect(configurationDialog, SIGNAL(signalSetCurVirKeyboardEnglish(bool)), this,
            SLOT(onSetCurVirKeyboardEnglish(bool)));
    connect(configurationDialog, &ConfigurationDialog::endCurState, this, &UltrasoundView::onEndCurState);
    connect(m_LicenseInitializer, SIGNAL(signalSetCurVirKeyboardEnglish(bool)), this,
            SLOT(onSetCurVirKeyboardEnglish(bool)));
    connect(this, SIGNAL(signalSetCurVirKeyboardEnglish(bool)), this, SLOT(onSetCurVirKeyboardEnglish(bool)));

    if (!m_ImageManager->sonoParameters()->parameter(BFPNames::LGCEnStr)->isNull())
    {
        connect(m_ImageManager->sonoParameters()->parameter(BFPNames::LGCEnStr), SIGNAL(valueChanged(const QVariant&)),
                configurationDialog, SIGNAL(lGCEnableChanged(const QVariant&)));
    }
    connect(m_ExamManager->patientWorkflow(), SIGNAL(patientChanged(Patient*)), configurationDialog,
            SIGNAL(patientChanged(Patient*)));

    // init createStressEchoWidgetManager
    m_StressEchoWidgetManager->setBeamFormer(beamformer);
    m_StressEchoWidgetManager->setColorMapManager(m_ColorMapManager);
    m_StressEchoWidgetManager->setBufferManager(m_ImageManager->chisonUltrasoundContext()->stressEchoBufferManager());
    connect(m_StressEchoWidgetManager, SIGNAL(analyzeDialogClosed()), this, SLOT(onAnalyzeStateClosed()));
    connect(m_StressEchoWidgetManager, SIGNAL(entryReportState()), this, SLOT(onEntryReportState()));

    // init createMotorControl
    configurationDialog->setMotorManager(m_PeripheralManager->motorManager());
    m_BFHWInfoModel->setMotorManager(m_PeripheralManager->motorManager());

    configurationDialog->setUpdateController(m_ImageManager->updateController());

    // 此逻辑来自于MainModule::MainModule构造函数内
    connect(m_BFHWInfoModel, SIGNAL(sendParameter()), this, SLOT(updateMotorParameter()));

    MeasureContext* measContext = m_MarkManager->measureContext();
    Q_ASSERT(measContext != NULL);
    FreezeBarWidget* freezeBarWidget = dynamic_cast<FreezeBarWidget*>(m_ViewerContext->freezeBarWidget());
    connect(freezeBarWidget, SIGNAL(currentIndexChanged(int)), measContext, SLOT(onCurrentIndexChanged(int)),
            Qt::DirectConnection);

    connect(measContext, SIGNAL(onHasAutoEFResultChanged(QVariant)), freezeBarWidget,
            SLOT(setHasAutoEFResult(QVariant)), Qt::DirectConnection);

    connect(measContext, SIGNAL(onImageRenderPartitionChanged(QVariant)), freezeBarWidget,
            SLOT(setCurRenderPartition(QVariant)), Qt::DirectConnection);

    connect(measContext, SIGNAL(onActivePartitionChanged(QVariant)), freezeBarWidget,
            SLOT(setActivePartition(QVariant)), Qt::DirectConnection);

    connect(measContext, SIGNAL(onEDFrameChanged(QVariant)), freezeBarWidget, SLOT(setEDFrame(QVariant)),
            Qt::DirectConnection);

    connect(measContext, SIGNAL(onESFrameChanged(QVariant)), freezeBarWidget, SLOT(setESFrame(QVariant)),
            Qt::DirectConnection);

    connect(measContext, SIGNAL(onEDCurIndexChanged(QVariant)), freezeBarWidget, SLOT(setEDCurIndex(QVariant)),
            Qt::DirectConnection);

    connect(measContext, SIGNAL(onESCurIndexChanged(QVariant)), freezeBarWidget, SLOT(setESCurIndex(QVariant)),
            Qt::DirectConnection);

    connect(measContext, SIGNAL(onAutoEFOnChanged(QVariant)), freezeBarWidget, SLOT(setESCurIndex(QVariant)),
            Qt::DirectConnection);

    // 此逻辑来自于MainModule::MainModule构造函数内
    connect(QGuiApplication::instance(), SIGNAL(screenAdded(QScreen*)), this, SLOT(onScreenAdded(QScreen*)));
    connect(QGuiApplication::instance(), SIGNAL(screenRemoved(QScreen*)), this, SLOT(onScreenRemoved(QScreen*)));

    // 此逻辑来自于MainModule::showMainWindow函数内
    connect(&MenuController::instance(), &MenuController::bottomMenuTipLogoVisible, this,
            [=](bool visible, int index, bool isBySK) {
                dynamic_cast<MainWindowKBUnit*>(mainWindow())->controlSkLogo(visible, index, isBySK);
            });

    LeftMenuWidget* leftMenuWidget = dynamic_cast<LeftMenuWidget*>(m_ViewerContext->leftMenuWidget());
    leftMenuWidget->setToolsFacade(m_ToolsFacade);

    measurementMenu->setSubSystemSubject(dynamic_cast<USFObject*>(this));
    measurementMenu->setRulerFactory(m_MarkManager->rulerFactory());
    measurementMenu->setTouchMeasureDispather(m_MarkManager->measureDispather());
    MenuPanelModel::instance().setToolsFacade(m_ToolsFacade);

    getImageClipWidget()->setSubSystemSubject(dynamic_cast<USFObject*>(this));

    bodyMarkWidget->setSubSystemSubject(dynamic_cast<USFObject*>(this));

    m_ExamManager->iMultiScreenSyncController()->setSubSystemSubject(dynamic_cast<USFObject*>(this));

    m_ExamManager->iMultiScreenSyncController()->setImageSkimWidget(m_ViewerContext->imageClipWidget());

    ArrowGlyphsControl* control = dynamic_cast<ArrowGlyphsControl*>(
        GlyphsControlManager::instance().getGlyphsControl(GlyphsCtl::ArrowGlyphsType));
    if (control != NULL)
    {
        control->setStateManager(m_StateManager);
    }

    AutoTraceMeasureGlyphsControl* auto_control =
        GlyphsControlManager::instance().getChildGlyphsControl<AutoTraceMeasureGlyphsControl>(
            GlyphsCtl::AutoTraceMeasureGlyphsType);
    if (auto_control != NULL)
    {
        auto_control->setStateManager(m_StateManager);
    }

    RealTimeAutoTraceMeasureGlyphsControl* real_auto_control =
        GlyphsControlManager::instance().getChildGlyphsControl<RealTimeAutoTraceMeasureGlyphsControl>(
            GlyphsCtl::RealTimeAutoTraceMeasureGlyphsType);
    if (real_auto_control != NULL)
    {
        real_auto_control->setStateManager(m_StateManager);
    }

    FunctionButtonController::instance().setStateManager(m_StateManager);

    ModeChangeWidget* modeChangeWidget = dynamic_cast<ModeChangeWidget*>(m_ViewerContext->modeChangeWidget());
    if (modeChangeWidget != NULL)
    {
        modeChangeWidget->setStateManager(m_StateManager);
    }

    bodyMarkGlyphsControl->setStateManager(m_StateManager);

    GeneralWorkflowFunction::setDiskDevice(m_DiskDevice);

    connect(m_PatientWorkflowModel, SIGNAL(archiveDialogClosed()), this, SLOT(onArchiveStateClosed()));
    connect(m_PatientWorkflowModel, SIGNAL(entryPatientState()), this, SLOT(onEntryPatientState()));
    connect(m_PatientWorkflowModel, SIGNAL(entryContinueExamState()), this, SLOT(onEntryContinueExamState()));
    connect(m_PatientWorkflowModel, SIGNAL(entryEditExamState()), this, SLOT(onEntryEditExamState()));
    connect(m_PatientWorkflowModel, SIGNAL(onlyJump()), this, SLOT(onArchiveStateOnlyJump()));

    connect(m_PatientWorkflowModel, SIGNAL(reviewDialogClosed()), this, SLOT(onEasyViewDialogClosed()));
    connect(m_PatientWorkflowModel, SIGNAL(onlyJump()), this, SLOT(onEasyViewStateOnlyJump()));
    if (true)
    {
        connect(m_PatientWorkflowModel, SIGNAL(entryBrowseState()), this, SLOT(onEntryBrowseState()));
#ifdef USE_4D
        connect(m_PatientWorkflowModel, SIGNAL(fourdCallBack(QString)), this, SLOT(onFourdCallBack(QString)));
#endif
    }

    m_LicenseInitializer->setStateManager(m_StateManager);
    m_LicenseInitializer->setUDiskPathInfo(m_DiskDevice->udiskPathInfo());

    IGlyphStoreManager* glyphStoreManager = m_MarkManager->glyphStoreManager();
    glyphStoreManager->setOverlay(imageTileOverlay());

    MenuController::instance().controlPanel()->setMultiScreenSyncController(
        m_ExamManager->iMultiScreenSyncController());

    afterBehavior(PRETTY_FUNCTION);
}

ImageSkimManager* UltrasoundView::getImageClipWidget() const
{
    ImageSkimManager* clipWidget = dynamic_cast<ImageSkimManager*>(m_ViewerContext->imageClipWidget());
    Q_ASSERT(clipWidget != NULL);
    return clipWidget;
}
