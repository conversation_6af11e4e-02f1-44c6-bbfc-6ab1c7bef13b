#ifndef ULTRASOUNDVIEW_H
#define ULTRASOUNDVIEW_H

#include "usfprogramviewer_global.h"
#include "iultrasoundview.h"

class QScreen;
class ImageSkimManager;
class IZoomOnProxy;
class SystemStatusTipAction;
class SystemTipModel;
class MeasureInputWidget;
class SonoZoomWidget;
class SonoZoomControl;
class FetalHeadPosWidget;
class FetalHeadDirectionWidget;
class QuickCommentPopupWidget;
class SonoParasWidgetController;
class SonoHelpController;
class IStateSwitchProcessor;
class IProbeSelectionModel;
class BaseProbeSelectionWidget;
class IGlyphsControlCreator;
class IStressEchoModel;

/**
 * @brief UltrasoundView: 超声主界面UI接口实现类
 *
 * 用途：超声主界面UI接口类的具体实现，外部模块（除构建方模块）禁止直接访问，外部模块通过其基类访问其接口功能
 */
class USF_PROGRAM_VIEWER_EXPORT UltrasoundView : public IUltrasoundView
{
    Q_OBJECT
public:
    UltrasoundView(DopplerViewerContext* context, QObject* parent = 0);
    virtual ~UltrasoundView();
    virtual void handleEvent(UEventType event_name, const USFVariant& variant);

public:
    virtual void init();
    virtual void release();
    virtual void setPeripheralManager(IPeripheralManager* value);
    virtual void setImageManager(IImageManager* value);
    virtual void setExamManager(IExamManager* value);
    virtual void setMarkManager(IMarkManager* value);
    virtual void setToolsFacade(IToolsFacade* value);
    virtual void setStateManagerFacade(IStateManager* value);
    virtual IStateManager* stateManagerFacade() const;
    virtual void setDiskDevice(IDiskDevice* diskDevice);
    virtual void setSystemInfo(ISystemInfo* systemInfo);
    virtual void setColorMapManager(IColorMapManager* colorMapManager);
    virtual void setLicenseInitializer(ILicenseInitializer* licenseInitializer);
    virtual ILicenseInitializer* licenseInitializer() const;
    virtual DopplerViewerContext* viewContext() const;
    virtual IMainWindow* mainWindow() const;
    virtual bool realTimeZoomIn();
    virtual void pauseUIRefresh(bool pause);
    virtual void switchUI(int scanmode_name, const QString& menu_name, const QString& state_name, bool switch_on = true,
                          bool needRefreshHighLight = true);
    virtual void switchUI(int scanmode_name, const QString& menu_name, bool needRefreshHighLight);
    virtual bool enterState(FUNC_STATE state, const FuncStateInput& input = FuncStateInput());
    virtual bool exitState(FUNC_STATE state, const FuncStateInput& input = FuncStateInput());
    virtual void controlLight(const QString& keyName, bool state);
    virtual Overlay* imageTileOverlay() const;
    virtual void removeImageTileOverlayAllItems();
    virtual void setLeftMenuWidgetVisible(bool show);
    virtual void setMeasurementMenutFetus(int value);
    virtual void setMeasurementMenuIsToolEnter(bool value);
    virtual void setBottomMenuWidgetVisible(bool show);
    virtual void setstressEchoWorkWidgetVisible(bool show);
    virtual QImage currentImage(ImageEventArgs::ImageType type = ImageEventArgs::WholeImage) const;
    virtual bool isThumbnailDialogVisible() const;
    virtual void setMeasureResultListWidgetOnLoadDisplayControl(bool value);
    virtual void setMeasureResultListWidgetVisible(bool show);
    virtual void adjustMeasureResultListWidgetSize();
    virtual void closeSystemStatusInfoWindows();
    virtual void setSonoParametersForSonoParasWidget(SonoParameters* sonoparameters);
    virtual void setSonoParametersForDopplerAdjustItem(SonoParameters* sonoparameters);
    virtual void setSonoParametersForMainWindowTopWidget(SonoParameters* sonoparameters);
    virtual void updateGDPRInfoForMainWindowTopWidget(int offsetX = 0);
    virtual ImageTile* curImageTile() const;
    virtual bool isZoomIn() const;
    virtual const QList<QRect> getGDPRFullScreenCurrentRect() const;
    virtual const QList<QRect> getGDPRImageAndInfoCurrentRect() const;
    virtual const QRect getGDPROBRect() const;
    virtual int getMainWindowTopWidgetGDPRWidth() const;
    virtual void updateImageClipWidget(const QString& patient_path);
    virtual void clearImageClipWidget();
    virtual void updateImageClipWidgetImageLable(int index);
    virtual void showSonoZoomWidgetGlass(bool show, bool isOtherFun = false);
    virtual QRect imageZoomOnWidgetRect();
    virtual BFHWInfoModel* bFHWInfoModel() const;
    virtual QList<QuickMeasureCaptionsLoop*> quickMeasureCaptionsLoops() const;
    virtual MainWindowTopInfoModel* mainWindowTopInfoModel() const;
    virtual CursorMouseActionsModel* cursorMouseActionsModel() const;
    virtual ProbeSelectionDispatcher* probeSelectionDispatcher() const;
    virtual IExamModeMenuModel* examModeMenuModel() const;

    virtual StressEchoWidgetManager* stressEchoWidgetManager() const;

    virtual IAdminConfigModel* adminConfigModel() const;
    virtual IPatientWorkflowModel* patientWorkflowModel() const;
    virtual TGCMenuWidgetContainer* widgetOfTGCAdjustment() const;
    virtual void showLoginDlg();
    virtual void showSNInputWidget();
    virtual void checkRemainDays();
    virtual void setupSystemStatusAction();

    virtual QStringList postParameterNames() const;
    virtual void updateToolsSonoParameters(SonoParameters* sonoParameters);

    virtual void checkImageRender();
    virtual void controlGainLogo();
    virtual void showMainWindowBegin();
    virtual void showMainWindow();
    virtual void showMainWindowEnd();

    virtual void runSystemUserImpl();

    virtual void bufferStoreOnLoaded();

    virtual void bufferStoreLoadStoredData(const StoredData& storedData);

    virtual void bufferStoreSaveGDPRInfo(const QString& fileName);

    virtual void createSplashWindow();
    virtual void updateSplashProgress(const QString& status);
    virtual void enableUpdateMeasureContextImage(bool);
    virtual void updateMeasureContextImage();

    virtual bool isInSecondDegree();

    virtual QWidget* popUpWidget() const;

signals:
    void signalSetCurVirKeyboardEnglish(bool isEnglish);
#ifdef SYS_WINDOWS
    void dealNativeEvent(const QByteArray& eventType, void* message, long* result);
#endif

public slots:
    void setImage();
    void resetImageRects();
    void updateMotorParameter();
    void fullScreenFlexBtnCliecked();
    void onScreenAdded(QScreen* screen);
    void onScreenRemoved(QScreen* screen);
    void onLockScreenStateChanged(QString state);
    void onCurSocketNoProbeWarning();
    void clearAllGlyphsWithOutComment();
    void onAnalyzeStateClosed();
    void onEntryReportState();
    void onEndCurState();

private slots:
    void updateCommentControlState();
    void updateBodyMarkControlState();
    void updateArrowControlState();
    void onProbeDialogClosed();
    void onProbeDialogAccepted();
    void onReportDialogClosed();
    void onMeasurementMenuReportButtonClicked();
    void onSettingsDialogClosed();
    void onSettingsDialogHwKeyImported();
    void onArchiveStateClosed();
    void onEntryPatientState();
    void onEntryContinueExamState();
    void onEntryEditExamState();
    void onArchiveStateOnlyJump();
    void onEasyViewDialogClosed();
    void onEntryBrowseState();
    void onEasyViewStateOnlyJump();
    void onFourdCallBack(const QString& filepath);
    void onSetCurVirKeyboardEnglish(bool isEnglish);

private:
    void createMainWindow();
    void createLicenseInitializer();
    void initTcpServer();
    void createBFHWInfoModel();
    void createFpsInfoWidget();
    void initializeTopInfoWidget();
    void createCursorMouseActionsModel();
    void initializeSonoParasWidget();
    void createGlyphsControlCreator();
    void initializeImageWidget();
    void initializeFreezeBarWidget();
    void createProgressSliderWidget();
    void createStoreProgressMovieWidget();
    void createSystemStatusAction();
    void createSystemTipModel();
    void initializeScreenshots();
    void createMeasurementWidgets();
    void createProbeSelectionModel();
    void createExamModeMenuModel();
    void createQuickCommentPopupWidget();
    void createReportDialog();
    void createSettingsDialog();
    void createQuickMeasureLoop();
    void createStressEchoWidgetManager();
    void initializeLGC();
    void createThumbnailDialog();
    void createSonoParasWidgetController();
    void createSonoHelpController();
    void controlDopplerAdjustItem();
    void createFlexButtons();
    void createMultiScreenSync();
    void createZoomOnProxy();
    void initializeBegin();
    void registerZoomOnProxyHideWidget();
    void createStressEchoModel();
    void createAdminConfigModel();
    void createPatientWorkflowModel();
    void createTGCMenuWidgetContainer();
    void initializeEFastWidgets();
    void initializeEnd();

private:
    ImageSkimManager* getImageClipWidget() const;
    void initQuickMeasureCaptionsLoop(int index);

private:
    IPeripheralManager* m_PeripheralManager;
    IExamManager* m_ExamManager;
    IMarkManager* m_MarkManager;
    IImageManager* m_ImageManager;
    DopplerViewerContext* m_ViewerContext;
    IToolsFacade* m_ToolsFacade;
    IStateManager* m_StateManager;
    IMainWindow* m_MainWindow;
    IZoomOnProxy* m_ZoomOnProxy;
    BFHWInfoModel* m_BFHWInfoModel;
    MainWindowTopInfoModel* m_MainWindowTopInfoModel;
    CursorMouseActionsModel* m_CursorMouseActionsModel;
    SystemStatusTipAction* m_SystemStatusTipAction;
    SystemTipModel* m_SystemTipModel;
    MeasureInputWidget* m_MeasureInputWidget;
    SonoZoomWidget* m_SonoZoomWidget;
    SonoZoomControl* m_SonoZoomControl;
    FetalHeadPosWidget* m_FetalHeadPosWidget;
    FetalHeadDirectionWidget* m_FetalHeadDirectionWidget;
    IExamModeMenuModel* m_ExamModeMenuModel;
    QuickCommentPopupWidget* m_QuickCommentPopupWidget;
    QList<QuickMeasureCaptionsLoop*> m_QuickMeasureCaptionsLoops;
    StressEchoWidgetManager* m_StressEchoWidgetManager;
    SonoParasWidgetController* m_SonoParasWidgetController;
    SonoHelpController* m_SonoHelpController;
    bool m_GainLogoVisible;
    IStateSwitchProcessor* m_StateSwitchProcessor;
    IDiskDevice* m_DiskDevice;
    IColorMapManager* m_ColorMapManager;
    IProbeSelectionModel* m_ProbeSelectionModel;
    BaseProbeSelectionWidget* m_ProbeSelectionWidget;
    ISystemInfo* m_SystemInfo;
    ILicenseInitializer* m_LicenseInitializer;
    IGlyphsControlCreator* m_GlyphsControlCreator;
    IStressEchoModel* m_StressEchoModel;
    IAdminConfigModel* m_AdminConfigModel;
    IPatientWorkflowModel* m_PatientWorkflowModel;
    TGCMenuWidgetContainer* m_TGCMenuWidgetContainer;
};

#endif
