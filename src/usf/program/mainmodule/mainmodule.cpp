#include "mainmodule.h"
#include "applicationdevicestatusmanager.h"
#include "applicationeventfilter.h"
#include "applicationinfo.h"
#include "applogger.h"
#include "dataaccesslayerhelper.h"
#include "ibufferstoremanager.h"
#include "ikeyboard.h"
#include "imageskimmanager.h"
#include "imagetile.h"
#include "iupdownkeyoperator.h"
#include "keyeventcommitter.h"
#include "leftmenutool.h"
#include "logger.h"
#include "mainmodulecontext.h"
#include "mainmoduleinstance.h"
#include "mainwindowmiddledownwidget.h"
#include "measurestaterecorder.h"
#include "menucontroller.h"
#include "menutoolsloader.h"
#include "iprobepresetmodel.h"
#include "qtkeyprocessor.h"
#include "resource.h"
#include "rightmenutool.h"
#include "setting.h"
#include "stateeventnames.h"
#include "statefilterlocker.h"
#include "statemanager.h"
#include "systemstatuswidget.h"
#include "util.h"
#include <QApplication>
#include <QGuiApplication>
#include <QScreen>
#include <QTextCodec>
#include <QTimer>
#ifdef USE_KB_SHORTCUT
#include "systemshortcut.h"
#endif
#include "formula.h"
#include "menutoolcontext.h"
#include "toolnames.h"
#include "toolsfactory.h"

#include "a4ctracemeasureglyphscontrol.h"
#include "abstractmachine.h"
#include "appsetting.h"
#include "assistlinefixedglyphscontrol.h"
#include "assistlinemeasureglyphscontrol.h"
#include "autocubicsplineglyphscontrol.h"
#include "autotracemeasureglyphscontrol.h"
#include "bfhwinfomodel.h"
#include "ellipsemeasureglyphscontrol.h"
#include "envelopetracemeasureglyphscontrol.h"
#include "ibuffermanager.h"
#include "istatemanager.h"
#include "leftmenuwidget.h"
#include "linemeasureglyphscontrol.h"
#include "measureresultlistwidget.h"
#include "messageboxframe.h"
#include "modelconfig.h"
#include "modestatepreventor.h"
#include "pointmeasureglyphscontrol.h"
#include "rectglyphscontrol.h"
#include "tracemeasureglyphscontrol.h"
#include "twopointmeasureglyphscontrol.h"

#include "fourdprobecontrolmodel.h"
#include "menutoolsfileprovider.h"
#include "statemanagerparametermodel.h"
#include "stressechowidgetmanager.h"
#include "systemexitobj.h"
#include "isystemstatusmodel.h"
#include "hintcontainerwidget.h"
#include "statemanagerfacade.h"

// TODO:后续需要清理无效include

#include "istatusmodel.h"
#include "toolsfacade.h"
// TODO:后续需要清理无效include

#ifdef USE_4D
#include "chisonfourdproxy.h"
#include "fourdcallbackmodestate.h"
#include "fourdchangetransformaxistool.h"
#include "fourdcineplaystate.h"
#include "fourdcurveclippingtool.h"
#include "fourdcurvedlinectrlpointmovetool.h"
#include "fourdcurvedlineslopechangedtool.h"
#include "fourddatatypedef.h"
#include "fourddirectiontool.h"
#include "fourddynamicrangetool.h"
#include "fourdeventprocesstool.h"
#include "fourdframeratetool.h"
#include "fourdgaintool.h"
#include "fourdiimageparastool.h"
#include "fourdlayouttool.h"
#include "fourdleftwidget.h"
#include "fourdlivefreezestate.h"
#include "fourdlivemodestate.h"
#include "fourdlivepreprocessstate.h"
#include "fourdlivetool.h"
#include "fourdloadcommand.h"
#include "fourdmouseactionchangedtool.h"
#include "fourdmousecommandcontext.h"
#include "fourdmousemovetool.h"
#include "fourdmousestatechangedtool.h"
#include "fourdpalettetool.h"
#include "fourdpremodestate.h"
#include "fourdpremousetool.h"
#include "fourdrendertool.h"
#include "fourdresettool.h"
#include "fourdreviewloadtool.h"
#include "fourdroiglyphscontrol.h"
#include "fourdsplitscreentool.h"
#include "fourdstatetool.h"
#include "fourdthresholdtool.h"
#include "fourdtransformmodechangedtool.h"
#include "fourdtransformwidget.h"
#include "fourdvirtualhdtool.h"
#include "ifourdlivewidget.h"
#include "static3dfreezestate.h"
#include "storefourdimagecommand.h"
#endif

#ifdef USE_FREEHAND3D
#include "freehand3dmousecommandcontext.h"
#include "freehand3dmousetool.h"
#include "freehand3dproxy.h"
#include "freehand3droiglyphscontrol.h"
#include "freehand3dstate.h"
#include "freehand3dtool.h"
#include "freehand3dwidget.h"
#endif

#include "chisonultrasoundcontext.h"
#include "imagewidget.h"
#include "linecinelooper.h"
#include "probeselectiondispatcher.h"

#include "menupanelmodel.h"

#include "framecontrol.h"
#ifdef USE_VIRTUAL_KEYBOARD
#include "keyboardcontrol.h"
#endif
#include "menuvisiblecontroller.h"
#include <QGuiApplication>
#include <QWindow>

#include "titlebarwidget.h"
#include "toolsordermanager.h"

#include "stressechotemplate.h"
#include "stressechotemplateeditwidget.h"
#include "stressechotemplateselectionwidget.h"
#include "stressechoworkwidget.h"

#include "exammanager.h"
#include "markmanager.h"
#include "imagemanager.h"
#include "peripheralmanager.h"
#include "ultrasoundview.h"
#include "dopplerviewercontext.h"

#include "buttonproperty.h"
#include "deviceutil.h"
#include "i18nstrings.h"
#include "qssloader.h"
#include "traceballconfig.h"

#include "abstractstate.h"
#include "bfpnames.h"
#include "imainwindow.h"
#include "ismenuvisibletool.h" //TODO:这两个tools的依赖后续考虑重构掉
#include "modeluiconfig.h"
#include "qvariantcommandargs.h"
#include "shutdowntool.h"
#include "statecreator.h"
#include "testcommandnames.h"
#include "testcontainer.h"
#include "testswitchprobecommand.h"
#include "toolscreator.h"
#ifdef USE_PANORAMIC
#include "curvedpanoramicentool.h"
#endif
#include "commentmousetool.h"
#include "leftandbottommenuwidgetoperatetool.h"
#include "splashupdateprogress.h"
#include "statemanagerfacade.h"
#include "databasemanager.h"
#include "basemeasuredispather.h"
#include "measurecontext.h"

#include "updateprogressaspect.h"
#include "logaspect.h"
#include "recordtimeaspect.h"
#include "fileutil.h"
#include "ihardwaremonitormanager.h"
#include "iintellconnremotecontroller.h"

bool MainModule::m_SoftWareIsInitialized = false;

LOG4QT_DECLARE_STATIC_LOGGER(log, MainModule)
MainModule::MainModule(QObject* parent)
    : QObject(parent)
    , m_Context(new MainModuleContext())
    , m_Instance(new MainModuleInstance(this))
    , m_ApplicationEventFilter(NULL)
    , m_ApplicationDeviceManager(NULL)
    , m_PeripheralManager(NULL)
    , m_UltrasoundView(NULL)
    , m_ImageManager(NULL)
    , m_ExamManager(NULL)
    , m_MarkManager(NULL)
    , m_ViewerContext(NULL)
    , m_ToolsOrderManager(NULL)
    , m_UpdateProgress(NULL)
    , m_ToolsFacade(NULL)
    , m_StateManager(NULL)
    , m_LogAspect(NULL)
    , m_UpdateProgressAspect(NULL)
    , m_RecordTimeAspect(NULL)
{
    removeClcache();
    createSubSytemObjects();
    initSystemEnvironment();
    checkSNInput(); // checkSNInput需在进度条控件创建之前执行
    createSplashWindow();
    checkRemainDays();
    initUltraSoundSystem();
    createToolsOrderManager();
    createStateManager();
    createMenuTools();
    listenerIntellConnControl();
    createKeyEventCommitter();
    createStateManagerParameterModel();
    createTestContainer();
    setupTestContainer();
    createHintContainerWidget();
#ifdef SYS_WINDOWS
    monitoringDeviceStatusChanges();
#endif
}

MainModule::~MainModule()
{
    FinalizeLogger fl("MainModule");

    StateManager::getInstance().stop();

    removeAppEventFilter();

    ToolsFactory::instance().releaseAll();

    if (m_MarkManager != NULL)
    {
        m_MarkManager->release();
    }

    if (m_ExamManager != NULL)
    {
        m_ExamManager->release();
    }

    if (m_ImageManager != NULL)
    {
        m_ImageManager->release();
    }

    if (m_PeripheralManager != NULL)
    {
        m_PeripheralManager->exit();
    }

    if (m_UltrasoundView != NULL)
    {
        m_UltrasoundView->release();
    }

    Util::SafeDeletePtr(m_UltrasoundView);
    Util::SafeDeletePtr(m_MarkManager);
    Util::SafeDeletePtr(m_ExamManager);
    Util::SafeDeletePtr(m_ImageManager);
    Util::SafeDeletePtr(m_PeripheralManager);
    Util::SafeDeletePtr(m_ApplicationDeviceManager);
    Util::SafeDeletePtr(m_ToolsOrderManager);
    Util::SafeDeletePtr(m_UpdateProgress);
    Util::SafeDeletePtr(m_ToolsFacade);
    Util::SafeDeletePtr(m_StateManager);
    Util::SafeDeletePtr(m_LogAspect);
    Util::SafeDeletePtr(m_UpdateProgressAspect);
    Util::SafeDeletePtr(m_RecordTimeAspect);

    delete m_Context;
}

void MainModule::initSystemEnvironment()
{
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    QTextCodec::setCodecForCStrings(QTextCodec::codecForName("UTF-8"));
    QTextCodec::setCodecForTr(QTextCodec::codecForName("UTF-8"));
#endif
    AppSetting::initialize();

    QssLoader::loadDefaultQss();
    // 在setText之前，translator还没有加载所以必须提前加载，不然不能翻译 by jinyuqi
    //必须在用到翻译之前初始化
    I18nStrings::instance();

    // 为了保证弹出license导入界面时，可以通过触控板来点击完成单击操作，故将TouchPad的注册前移
    // set traceball sensitivity
    QString traceballVendor = TraceballConfig::instance().getTraceballVendor();
    TraceballParam param = TraceballConfig::instance().curTraceballParam();
    ApplicationInfo::instance().setTraceballSensitivity(traceballVendor, param.accel, param.accel_limit);
    if (ModelConfig::instance().value(ModelConfig::IsSupportTouchPad, false).toBool())
    {
        DeviceUtil::tryEnableTouchPadTappingAsync(traceballVendor);
    }

    Setting::instance().defaults().readCurrentIni();
    ButtonIniManager::instance().readCurrentIni(); // TODO: 此代码功能逻辑属于通用GUI，依赖basecontrol，后续考虑是否移出
    QssLoader::loadCurrentModelQss();

    m_PeripheralManager->init();
#ifdef USE_POWERBUTTONMonitor
    m_PeripheralManager->createPwrkeyListener(&MainModule::powerKeyCallback);
#else
    m_PeripheralManager->registerPwrkeyCallback(&MainModule::powerKeyCallback, PowerKey::Event);
    m_PeripheralManager->registerPwrkeyCallback(&MainModule::lidCallback, PowerKey::Lib);
#endif

    IDiskDevice* diskDevice = m_PeripheralManager->diskDevice();
    Q_ASSERT(diskDevice != NULL);

    m_ExamManager->setDiskDevice(diskDevice);
    m_ImageManager->setDiskDevice(diskDevice);
    m_UltrasoundView->setDiskDevice(diskDevice);

    ISystemInfo* systemInfo = m_PeripheralManager->systemInfo();
    Q_ASSERT(systemInfo != NULL);
    m_ExamManager->setSystemInfo(systemInfo);
    m_UltrasoundView->setSystemInfo(systemInfo);
}

void MainModule::checkSNInput()
{
    Q_ASSERT(m_UltrasoundView != NULL);
    m_UltrasoundView->showSNInputWidget();
}

void MainModule::checkRemainDays()
{
    Q_ASSERT(m_UltrasoundView != NULL);
    m_UltrasoundView->checkRemainDays();
}

void MainModule::createSubSytemObjects()
{
    m_ViewerContext = new DopplerViewerContext;
    m_PeripheralManager = new PeripheralManager;
    m_ImageManager = new ImageManager;
    m_ExamManager = new ExamManager;
    m_MarkManager = new MarkManager;
    m_UltrasoundView = new UltrasoundView(m_ViewerContext);
    m_UpdateProgress = new SplashUpdateProgress;
    m_ToolsFacade = new ToolsFacade;
    m_StateManager = new StateManagerFacade;

    // 构建切面装饰对象, 其对象的释放由被装饰者类析构时统一释放
    m_LogAspect = new LogAspect;
    m_UpdateProgressAspect = new UpdateProgressAspect;
    (dynamic_cast<UpdateProgressAspect*>(m_UpdateProgressAspect))->setUpdateProgess(m_UpdateProgress);
    m_RecordTimeAspect = new RecordTimeAspect;

    this->addDecorator(m_LogAspect);
    this->addDecorator(m_UpdateProgressAspect);
    this->addDecorator(m_RecordTimeAspect);

    m_ImageManager->addDecorator(m_LogAspect);
    m_ImageManager->addDecorator(m_UpdateProgressAspect);
    m_ImageManager->addDecorator(m_RecordTimeAspect);

    m_ExamManager->addDecorator(m_LogAspect);
    m_ExamManager->addDecorator(m_UpdateProgressAspect);
    m_ExamManager->addDecorator(m_RecordTimeAspect);

    m_MarkManager->addDecorator(m_LogAspect);
    m_MarkManager->addDecorator(m_UpdateProgressAspect);
    m_MarkManager->addDecorator(m_RecordTimeAspect);

    m_UltrasoundView->addDecorator(m_LogAspect);
    m_UltrasoundView->addDecorator(m_UpdateProgressAspect);
    m_UltrasoundView->addDecorator(m_RecordTimeAspect);

    m_ExamManager->setPeripheralManager(m_PeripheralManager);
    m_ExamManager->setImageManager(m_ImageManager);
    m_ExamManager->setStateManagerFacade(m_StateManager);

    m_MarkManager->setImageManager(m_ImageManager);
    m_MarkManager->setStateManagerFacade(m_StateManager);

    m_ImageManager->setPeripheralManager(m_PeripheralManager);
    m_ImageManager->setExamManager(m_ExamManager);
    m_ImageManager->setMarkManager(m_MarkManager);
    m_ImageManager->setStateManagerFacade(m_StateManager);

    m_UltrasoundView->setPeripheralManager(m_PeripheralManager);
    m_UltrasoundView->setImageManager(m_ImageManager);
    m_UltrasoundView->setExamManager(m_ExamManager);
    m_UltrasoundView->setMarkManager(m_MarkManager);
    m_UltrasoundView->setToolsFacade(m_ToolsFacade);
    m_UltrasoundView->setStateManagerFacade(m_StateManager);

    m_Context->setViewerContext(m_ViewerContext);

    m_PeripheralManager->addObject(ObjectId::MAINFRAME, this);
    m_ImageManager->addObject(ObjectId::MAINFRAME, this);
    m_ImageManager->addObject(ObjectId::ULTRASOUNDVIEW, m_UltrasoundView);
    m_ExamManager->addObject(ObjectId::MAINFRAME, this);
    m_ExamManager->addObject(ObjectId::ULTRASOUNDVIEW, m_UltrasoundView);
    m_MarkManager->addObject(ObjectId::MAINFRAME, this);
    m_MarkManager->addObject(ObjectId::ULTRASOUNDVIEW, m_UltrasoundView);
    m_UltrasoundView->addObject(ObjectId::MAINFRAME, this);
}

void MainModule::initUltraSoundSystemBegin()
{
}

void MainModule::initUltraSoundSystem()
{
    initUltraSoundSystemBegin();

    m_ImageManager->init();

    m_ExamManager->setColorMapManager(m_ImageManager->colorMapManager());
    m_UltrasoundView->setColorMapManager(m_ImageManager->colorMapManager());

    m_ExamManager->init();

    m_MarkManager->setBufferStoreManager(m_ImageManager->bufferStoreManager());
    m_MarkManager->setPatientWorkflow(m_ExamManager->patientWorkflow());
    m_MarkManager->setPatientWorkflowWorker(m_ExamManager->patientWorkflowWorker());
    m_MarkManager->init();

    m_ExamManager->initExamModeConfigModel(m_MarkManager->measurementInterface());

    m_PeripheralManager->createHotKeyModels();
    m_PeripheralManager->setSonoParameter(m_ImageManager->beamFormer());

    m_UltrasoundView->init();

    initUltraSoundSystemEnd();
}

void MainModule::initUltraSoundSystemEnd()
{
    m_Instance->setMainWindow(m_UltrasoundView->mainWindow());

    m_ExamManager->setPatientWorkflowModel(m_UltrasoundView->patientWorkflowModel());

    //以下两个set接口从MeasureMouseTool类接口移过来
    BaseMeasureDispather* measDispather = dynamic_cast<BaseMeasureDispather*>(m_MarkManager->measureDispather());
    measDispather->setGraphicsView(m_UltrasoundView->curImageTile());
    measDispather->setScreenResultWidget(m_ViewerContext->measureResultListWidget());
    measDispather->setStateManager(m_StateManager);
    measDispather->setScreenMeasureController(m_MarkManager->screenMeasureController());

    IBufferStoreManager* bufferStoreManager = m_ImageManager->bufferStoreManager();
    bufferStoreManager->setGlyphStoreManager(m_MarkManager->glyphStoreManager());
    bufferStoreManager->setMeasureDispather(measDispather);
    bufferStoreManager->setScreenMeasureController(m_MarkManager->screenMeasureController());

    AbstractMachine* machine = m_PeripheralManager->machine();
    Q_ASSERT(machine);

    m_Context->setMachine(machine); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    HotKeyContainer* hotKeyContainer = m_PeripheralManager->hotKeyContainer();
    HotKeyConfig* hotKeyConfig = m_PeripheralManager->hotKeyConfig();
    Q_ASSERT(hotKeyContainer != NULL);
    Q_ASSERT(hotKeyConfig != NULL);
    m_Context->setHotKeyContainer(hotKeyContainer); // TODO:临时代码，先确保系统启动运行无问题，后续要移除
    m_Context->setHotKeyConfig(hotKeyConfig); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    SonoParameters* sonoParameters = m_ImageManager->sonoParameters();
    Q_ASSERT(sonoParameters);
    m_Context->setSonoParameters(sonoParameters); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    IBeamFormer* beamformer = m_ImageManager->beamFormer();
    Q_ASSERT(beamformer);
    m_Context->setBeamFormer(beamformer); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    ILineBufferManager* lineBufferManager = m_ImageManager->lineBufferManager();
    m_Context->setLineBufferManager(lineBufferManager); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    ChisonUltrasoundContext* chisonContext = m_ImageManager->chisonUltrasoundContext();
    Q_ASSERT(chisonContext);
    m_Context->setChisonUltrasoundContext(chisonContext); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    m_Context->setBufferStoreManager(bufferStoreManager); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    m_Context->setBodyMarkViewModel(
        m_MarkManager->bodyMarkViewModel()); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    BFHWInfoModel* bFHWInfoModel = m_UltrasoundView->bFHWInfoModel();
    Q_ASSERT(bFHWInfoModel);
    m_Context->setBFHWInfoModel(bFHWInfoModel); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    CursorMouseActionsModel* cursorMouseActionsModel = m_UltrasoundView->cursorMouseActionsModel();
    Q_ASSERT(cursorMouseActionsModel != NULL);
    m_Context->setCursorMouseActionsModel(
        cursorMouseActionsModel); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    SystemHintModel* systemHintModel = m_ExamManager->systemHintModel();
    Q_ASSERT(systemHintModel != NULL);
    ApplicationInfo::instance().setSystemHint(systemHintModel);

    m_Context->setSystemHintModel(systemHintModel); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    MainWindowTopInfoModel* mainWindowTopInfoModel = m_UltrasoundView->mainWindowTopInfoModel();
    Q_ASSERT(mainWindowTopInfoModel != NULL);
    m_Context->setMainWindowTopInfoModel(mainWindowTopInfoModel); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    ProbeSelectionDispatcher* probeSelectionDispatcher = m_UltrasoundView->probeSelectionDispatcher();
    Q_ASSERT(probeSelectionDispatcher != NULL);
    m_Context->setProbeSelectionDispatcher(
        probeSelectionDispatcher); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    ReportDialog* dlg = (ReportDialog*)m_ViewerContext->reportDialog();
    Q_ASSERT(dlg != NULL);
    m_Context->setReportDialog(dlg); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    ConfigurationDialog* configurationDialog = (ConfigurationDialog*)m_ViewerContext->configurationDialog();
    Q_ASSERT(configurationDialog != NULL);
    m_Context->setConfigurationDialog(configurationDialog);
    m_Context->setQuickMeasureLoops(
        m_UltrasoundView->quickMeasureCaptionsLoops()); // TODO:临时代码，先确保系统启动运行无问题，后续要移除
    m_Context->setStressEchoWidgetManager(
        m_UltrasoundView->stressEchoWidgetManager()); // TODO:临时代码，先确保系统启动运行无问题，后续要移除
    m_Context->setLGCRegulatorDialog((LGCRegulatorDialog*)m_ViewerContext
                                         ->lgcRegulatorDialog()); // TODO:临时代码，先确保系统启动运行无问题，后续要移除
    m_Context->setUpdateController(
        m_ImageManager->updateController()); // TODO:临时代码，先确保系统启动运行无问题，后续要移除

    m_Context->setScreenMeasureController(m_MarkManager->screenMeasureController());
    m_Context->setMeasureDispather(dynamic_cast<BaseMeasureDispather*>(m_MarkManager->measureDispather()));
}

void MainModule::removeClcache()
{
    FileUtil::removeDir(Resource::clCacheDir, true, true);
}

void MainModule::run()
{
    if (m_UltrasoundView != NULL)
    {
        m_UltrasoundView->updateSplashProgress("MainModule run()");
    }

    //此处等待是为了在插探头启动时，可以直接识别探头，而不至于冻结
    QTimer::singleShot(ModelConfig::instance().value(ModelConfig::ProbeSteadyTimeMs, 1500).toInt(), this,
                       SLOT(showMainWindow()));

    m_ImageManager->startRealtimeSystem();
    log()->info(QString("%1, #####start####").arg(Util::getSystemInfo()));
}

void MainModule::handleEvent(UEventType event_name, const USFVariant& variant)
{
    if (event_name == UEventType::EVENT_AUTOFREEZE)
    {
        autoFreeze();
    }
    else if (event_name == UEventType::TOOL_UPDATE_PARAMS)
    {
        SonoParameters* sonoparams = static_cast<SonoParameters*>(variant.toCustomType());
        updateToolsParameters(sonoparams);
    }
    else if (event_name == UEventType::COMMAND_DELETE_MEASUREMENT)
    {
        ToolsFactory::instance().command(ToolNames::MeasurementDeleteStr)->run();
    }
    else if (event_name == UEventType::COMMAND_FULLSCREENTOOL)
    {
        ToolsFactory::instance().command(ToolNames::FullScreenStr)->run();
    }
    else if (event_name == UEventType::SET_UPDATEPROBEINFO)
    {
        ProbeDataInfo* probedatainfo = static_cast<ProbeDataInfo*>(variant.toCustomType());
        m_Instance->updateStateMachineProbeInfo(probedatainfo);
    }
    else if (event_name == UEventType::COMMAND_DELETEGLYPHS)
    {
        //设置参数 用于区分解冻状态与清空测量项的状态下的删除图像
        if (variant.isType<bool>())
        {
            QVariantCommandArgs boolArgs;
            boolArgs.setArg(variant.toBool());
            ToolsFactory::instance().command(ToolNames::DeleteGlyphsStr)->setArgs(&boolArgs);
            ToolsFactory::instance().command(ToolNames::DeleteGlyphsStr)->run();
            ToolsFactory::instance().command(ToolNames::DeleteGlyphsStr)->setArgs(NULL);
        }
        else
        {
            ToolsFactory::instance().command(ToolNames::DeleteGlyphsStr)->run();
        }
    }
    else if (event_name == UEventType::TOOL_ADJUSTROIISENABLED)
    {
        ToolsFactory::instance().tool(BFPNames::IsEnableAdjustROIStr)->run();
    }
    else if (event_name == UEventType::TOOL_RUNAUTOEF)
    {
        ToolsFactory::instance().tool(ToolNames::AutoEFStr)->run();
    }
    else if (event_name == UEventType::TOOL_STOPAUTOEF)
    {
        ToolsFactory::instance().tool(ToolNames::AutoEFStr)->stop();
    }
    else if (event_name == UEventType::STATE_ZOOMON)
    {
        StateManager::getInstance().state("ZoomOn")->onEntry();
    }
    else if (event_name == UEventType::EVENT_UPDATE)
    {
        StateManager::getInstance().postEvent(StateEventNames::Update);
    }
    else if (event_name == UEventType::EVENT_FREEZE)
    {
        StateManager::getInstance().postEvent(StateEventNames::Freeze);
    }
    else if (event_name == UEventType::TOOL_CLOSEZOOMON)
    {
        ToolsFactory::instance().tool(ToolNames::ZoomOnCloseStr)->run();
    }
    else if (event_name == UEventType::TOOL_CLOSEZOOMONANDBMODE)
    {
        ToolsFactory::instance().tool(ToolNames::ZoomOnCloseStr)->run();
        StateManager::getInstance().postEvent(StateEventNames::B);
    }
    else if (event_name == UEventType::COMMAND_EXITSONONEEDLE)
    {
        if (!StateManager::getInstance().isSupportSonoNeedle())
        {
            ToolsFactory::instance().command(ToolNames::SonoNeedleExitStr)->run();
        }
    }
    else if (event_name == UEventType::EVENT_ENTERBMODE)
    {
        QVariant result;
        bool ret = m_ImageManager->askParam(BFPNames::SystemScanModeStr, result);
        Q_ASSERT(ret);

        int systemScanMode = result.toInt();
        if (systemScanMode == SystemScanModeTissueDoppler || systemScanMode == SystemScanModeTissuePW ||
            systemScanMode == SystemScanModeTDIM || systemScanMode == SystemScanModeTDILRBM ||
            systemScanMode == SystemScanModeTDIUDBM)
        {
            if (!StateManager::getInstance().isSupportTDI())
            {
                StateManager::getInstance().postEvent(StateEventNames::B);
            }
        }
        else if (systemScanMode == SystemScanModeMVI || systemScanMode == SystemScanModeMVIPW)
        {
            if (!StateManager::getInstance().isSupportMVI())
            {
                StateManager::getInstance().postEvent(StateEventNames::B);
            }
        }
        else
        {
            StateManager::getInstance().postEvent(StateEventNames::B);
        }
    }
    else if (event_name == UEventType::EVENT_EXITPROBE)
    {
        StateManager::getInstance().postEvent(StateEventNames::ProbeExit);
    }
    else if (event_name == UEventType::EVENT_EXITANALYZE)
    {
        StateManager::getInstance().postEvent(StateEventNames::AnalyzeExit);
    }
    else if (event_name == UEventType::EVENT_RUNREPORT)
    {
        StateManager::getInstance().postEvent(StateEventNames::Report);
    }
    else if (event_name == UEventType::EVENT_EXITREPORT)
    {
        StateManager::getInstance().postEvent(StateEventNames::ReportExit);
    }
    else if (event_name == UEventType::EVENT_QUICKDISTANCE)
    {
        StateManager::getInstance().postEvent(StateEventNames::QuickDistance);
    }
    else if (event_name == UEventType::EVENT_EXITSETUP)
    {
        StateManager::getInstance().postEvent(StateEventNames::SetupExit);
    }
    else if (event_name == UEventType::EVENT_EDITEXAMPATIENT)
    {
        StateManager::getInstance().postEvent(StateEventNames::EditExamPatient);
    }
    else if (event_name == UEventType::EVENT_RUNPATIENT)
    {
        StateManager::getInstance().postEvent(StateEventNames::Patient);
    }
    else if (event_name == UEventType::EVENT_CONTINUEEXAM)
    {
        StateManager::getInstance().postEvent(StateEventNames::ContinueExam);
    }
    else if (event_name == UEventType::EVENT_EDITEXAM)
    {
        StateManager::getInstance().postEvent(StateEventNames::EditExam);
    }
    else if (event_name == UEventType::EVENT_RUNBROWSE)
    {
        StateManager::getInstance().postEvent(StateEventNames::Browse);
    }
#ifdef USE_PANORAMIC
    else if (event_name == UEventType::TOOL_CURVEDPANORAMIC)
    {
        CurvedPanoramicEnTool* tool =
            dynamic_cast<CurvedPanoramicEnTool*>(ToolsFactory::instance().tool(ToolNames::CurvedPanoramicStr));
        if (tool != nullptr)
        {
            tool->setCurvedPanoramicDefaultParameter(true);
            tool->setPreventState(true);
        }
    }
#endif
    else if (event_name == UEventType::TOOL_UPDATEKEYBOARD)
    {
        CommentMouseTool* commentMouseTool =
            dynamic_cast<CommentMouseTool*>(ToolsFactory::instance().command(ToolNames::CommentMouseStr));
        if (StateManager::getInstance().currentState()->name() == StateEventNames::CommentState &&
            commentMouseTool != nullptr)
        {
            commentMouseTool->updateKeyBoard();
        }
    }
    else if (event_name == UEventType::TOOL_MODIFYMENUVISIBLE)
    {
        bool visible = variant.toBool();
        ITool* tool = ToolsFactory::instance().tool(BFPNames::IsMenuVisibleStr);
        if (tool != NULL)
        {
            QVariantCommandArgs args(visible);
            tool->setArgs(&args);
            tool->run();
            tool->setArgs(NULL);
        }

        if (visible)
        {
            ShowLeftAndBottomMenuWidgetTool* showTool = dynamic_cast<ShowLeftAndBottomMenuWidgetTool*>(tool);
            if (tool != NULL)
                showTool->resetWidgetIsVisible();
        }
    }
    else if (event_name == UEventType::COMMAND_ENDCURRENTSTATE)
    {
        ToolsFactory::instance().command(ToolNames::EndCurrentStateStr)->run();
    }
    else if (event_name == UEventType::COMMAND_STRESSECHOANALYZE)
    {
        ToolsFactory::instance().command(ToolNames::StressEchoAnalyzeStr)->run();
    }
    else if (event_name == UEventType::COMMAND_STRESSECHOT1CONTROL)
    {
        ToolsFactory::instance().command(ToolNames::StressEchoT1ControlStr)->run();
    }
    else if (event_name == UEventType::COMMAND_STRESSECHOT2CONTROL)
    {
        ToolsFactory::instance().command(ToolNames::StressEchoT2ControlStr)->run();
    }
    else if (event_name == UEventType::COMMAND_CLICKAUTOEFMEASURE)
    {
        ToolsFactory::instance().command(ToolNames::ClickAutoEFMeasureStr)->run();
    }
    else if (event_name == UEventType::COMMAND_FREEZEIMAGESEND)
    {
        ToolsFactory::instance().command(ToolNames::FreezeImageSendStr)->run();
    }
    else if (event_name == UEventType::COMMAND_FREEZEIMAGEREMOVE)
    {
        ToolsFactory::instance().command(ToolNames::FreezeImageRemoveStr)->run();
    }
    else if (event_name == UEventType::COMMAND_RUNARCHIVE)
    {
        ToolsFactory::instance().command(ToolNames::ArchiveEnterStr)->run();
    }
    else if (event_name == UEventType::COMMAND_OPENVIRTUALPROBECODE)
    {
        ToolsFactory::instance().command(ToolNames::OpenVirtualProbeCodeStr)->run();
    }
    else if (event_name == UEventType::COMMAND_OPENPROBECODEBURNER)
    {
        ToolsFactory::instance().command(ToolNames::OpenProbeCodeBurnerStr)->run();
    }
    else
    {
        Q_ASSERT(false);
    }
}

#include "isysteminfo.h"
void MainModule::showMainWindow()
{
    if (m_UltrasoundView != NULL)
        m_UltrasoundView->showMainWindowBegin();

    TimeLogger tl;

    if (m_UltrasoundView != NULL)
        m_UltrasoundView->updateSplashProgress("MainModule showMainWindow()");

    installAppEventFilter();

    //将standby对象添加到控制栈中
    initializeStandbyObjs();

    m_PeripheralManager->setDefaultAudio();

    m_UltrasoundView->showMainWindow();

    m_UltrasoundView->setupSystemStatusAction();

    tl.logger("mainWindow show");

    StateManager::getInstance().start();
    tl.logger("stateManager start");

#ifdef USE_KEYBOARD
    if (!m_PeripheralManager->checkKeyboard())
    {
        MessageBoxFrame::warningNonModal(m_UltrasoundView->mainWindow(), QString(),
                                         tr("The keyboard initialized. Please restart manually!"));
    }
#endif
    m_UltrasoundView->checkImageRender();
    m_Instance->checkModelCompatibility();
    m_Instance->tryShowingUpdatedTip();
    if (m_ExamManager->checkHardDiskError())
    {
        MessageBoxFrame::warningNonModal(m_UltrasoundView->mainWindow(), QString(),
                                         tr("Hardware self-test is unsuccessful, please restart after shutdown!"));
    }

    m_UltrasoundView->runSystemUserImpl();
    m_ImageManager->generateBFNames();
    m_ExamManager->restartNetWorkUdhcpc();

    tl.logger("asyncTryRestartUdhcpc");
    if (m_UltrasoundView != NULL)
        m_UltrasoundView->showMainWindowEnd();
    tl.logger("SplashWindow::finished");

    m_SoftWareIsInitialized = true;

    m_ImageManager->reset();

    m_PeripheralManager->synCapStateToUncapitialized();
#ifdef USE_ADMINVIEW
    m_UltrasoundView->showLoginDlg();
#endif

    m_ExamManager->feedBackOfSatisfaction();
    m_UltrasoundView->controlGainLogo();
    m_ImageManager->checkAutoUpdate();
    //如果支持屏幕旋转且支持模拟屏幕旋转，开机默认显示竖屏
    if (ModelUiConfig::instance().value(ModelUiConfig::IsSupportRotation).toBool())
    {
#ifdef USE_SIMULATEORIENTATION
        QTimer::singleShot(1500, this,
                           [=]() { ToolsFactory::instance().command(ToolNames::ScreenoOrientationStr)->run(); });
#endif
    }

    checkLidState();

    if (m_PeripheralManager->systemInfo()->checkBatteryNeedReplacement())
    {
        MessageBoxFrame::warning(tr("The health state of the battery is too low, if there is this prompt after two "
                                    "cycles of charging and discharging, it is recommended to replace the battery"));
    }

    tl.logger("System startup successful.");
}

void MainModule::autoFreeze()
{
    StateFilterLocker freezeState(m_StateManager, StateEventNames::Freeze);
    StateManager::getInstance().postEvent(StateEventNames::Freeze);
}

#ifdef USE_4D
void MainModule::createChisonFourDProxy()
{
    ChisonFourDProxy* proxy = new ChisonFourDProxy(m_Context, this);
    m_Context->setChisonFourdProxy(proxy);
}
#endif

void MainModule::createStateManager()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_Context->setStateManager(&StateManager::getInstance());                // TODO: 临时代码，后续待删除
    Q_ASSERT(m_Context->originalObject() == m_UltrasoundView->mainWindow()); // TODO: 临时代码，后续待删除
    StateCreator creator(m_Context, m_UltrasoundView->mainWindow());
    creator.createStates(m_PeripheralManager, m_ImageManager, m_ExamManager, m_MarkManager, m_UltrasoundView,
                         m_StateManager);

    ModeStatePreventor* preventor = new ModeStatePreventor(m_UltrasoundView->mainWindow());
    preventor->setBufferStoreManager(m_ImageManager->bufferStoreManager());
    connect(preventor, SIGNAL(systemScanModeLayoutChanged()), m_UltrasoundView, SLOT(clearAllGlyphsWithOutComment()));

#ifdef USE_KB_SHORTCUT
    new SystemShortcut(&StateManager::getInstance(), m_UltrasoundView->mainWindow());
#endif

    connect(&StateManager::getInstance(), SIGNAL(showNoProbeWarning()), m_UltrasoundView,
            SLOT(onCurSocketNoProbeWarning()), Qt::QueuedConnection);
    (&StateManager::getInstance())->setModeID(AppSetting::modelID());

    afterBehavior(PRETTY_FUNCTION);
}

void MainModule::createMenuTools()
{
    beforeBehavior(PRETTY_FUNCTION);

    Q_ASSERT(m_ToolsOrderManager != NULL);
    ToolsCreator creator;
    creator.createTools(m_ToolsOrderManager, m_PeripheralManager, m_ImageManager, m_ExamManager, m_MarkManager,
                        m_UltrasoundView, m_ToolsFacade, m_StateManager);

    MenuToolsFileProvider provider;
    QString path = provider.getResourceFile(AppSetting::model().toStdString().c_str());
    MenuToolsLoader::instance(path).build();

    MenuController::instance().setToolSet(MenuToolsLoader::instance().toolSet());
    MenuController::instance().initalize(new MenuToolContext(m_ImageManager->bufferStoreManager()));
    ApplicationInfo::instance().addUpDownKeyOperator(&MenuController::instance());

    m_ToolsOrderManager->initDisengagedTools();
    afterBehavior(PRETTY_FUNCTION);
}

void MainModule::listenerIntellConnControl()
{
    beforeBehavior(PRETTY_FUNCTION);

    IIntellConnRemoteController* intellConnRemoteController = m_ExamManager->intellConnRemoteController();
    if (nullptr != intellConnRemoteController)
    {
        connect(intellConnRemoteController, &IIntellConnRemoteController::postEvent, m_StateManager,
                &IStateManager::postEvent);
        connect(intellConnRemoteController, &IIntellConnRemoteController::parameterToolUpdated, this,
                &MainModule::onIntellConnParameterToolUpdated);
        connect(intellConnRemoteController, &IIntellConnRemoteController::soundShadowChanged,
                m_ImageManager->chisonUltrasoundContext(), &ChisonUltrasoundContext::setPictureImage);
    }

    afterBehavior(PRETTY_FUNCTION);
}

void MainModule::installAppEventFilter()
{
    m_ApplicationEventFilter = new ApplicationEventFilter(
        m_PeripheralManager->keyBoard(), m_PeripheralManager->machine(), m_ViewerContext->configurationDialog(),
        m_ImageManager->updateController(), m_ViewerContext->systemStatusTip(), m_PeripheralManager->keyProcessor(),
        m_ToolsFacade, m_StateManager, m_UltrasoundView->adminConfigModel(), m_ImageManager->sonoParameters(),
        m_UltrasoundView->curImageTile(), m_UltrasoundView->cursorMouseActionsModel(), m_ImageManager->beamFormer(),
        m_PeripheralManager->systemInfo(), m_UltrasoundView->mainWindow());
    qApp->installEventFilter(m_ApplicationEventFilter);
    createEventFilterController();
    Setting::instance().defaults().initialScreenController();
}

void MainModule::removeAppEventFilter()
{
    qApp->removeEventFilter(m_ApplicationEventFilter);
}

void MainModule::createEventFilterController()
{
    IsMenuVisibleTool* tool =
        dynamic_cast<IsMenuVisibleTool*>(ToolsFactory::instance().command(BFPNames::IsMenuVisibleStr));

    QList<QObject*> handleObjects;
    handleObjects << m_ViewerContext->leftMenuWidget() << m_ViewerContext->bottomMenuWidget();

    MenuVisibleController* controller = new MenuVisibleController(handleObjects, tool);
    m_ApplicationEventFilter->appendEventFilterController(controller);
}

void MainModule::createKeyEventCommitter()
{
    beforeBehavior(PRETTY_FUNCTION);

    new KeyEventCommitter(m_PeripheralManager->keyBoard(), m_StateManager, m_ToolsFacade,
                          m_UltrasoundView->mainWindow());

    afterBehavior(PRETTY_FUNCTION);
}

void MainModule::createTestContainer()
{
    beforeBehavior(PRETTY_FUNCTION);

    TestContainer::instance().addTestCommand(
        TestCommandNames::SwitchProbe,
        new TestSwitchProbeCommand(m_UltrasoundView->probeSelectionDispatcher()->frame()->child(), m_StateManager));

    afterBehavior(PRETTY_FUNCTION);
}

void MainModule::setupTestContainer()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_UltrasoundView->bFHWInfoModel()->setTestCommand(
        TestContainer::instance().testCommand(TestCommandNames::SwitchProbe));

    afterBehavior(PRETTY_FUNCTION);
}

#ifdef SYS_WINDOWS
void MainModule::monitoringDeviceStatusChanges()
{
    if (NULL == m_ApplicationDeviceManager)
    {
        m_ApplicationDeviceManager =
            new ApplicationDeviceStatusManager(m_UltrasoundView->mainWindow(), m_PeripheralManager->diskDevice());
    }
}
#endif

void MainModule::createSplashWindow()
{
    if (m_UltrasoundView != NULL)
    {
        m_UltrasoundView->createSplashWindow();
    }
}

void MainModule::initializeStandbyObjs()
{
    //将支持休眠的对象添加到休眠类的堆栈中,添加时注意顺序
    // LCD,VGA,VIDEO在休眠时先于冻结操作,不放在堆栈中操作
    AbstractMachine* machine = m_PeripheralManager->machine();
    Q_ASSERT(machine != NULL);
    machine->addStandbyObj(m_ApplicationEventFilter);
    machine->addStandbyObj(m_UltrasoundView->curImageTile()->bufferManager());
    machine->addStandbyObj(m_ImageManager->chisonUltrasoundContext()->cineLooper());
    machine->addStandbyObj(m_PeripheralManager->keyProcessor());
    machine->addStandbyObj(m_PeripheralManager->keyBoard());
    machine->addStandbyObj(dynamic_cast<IStandbyObj*>(m_MarkManager->measureContext()));
    machine->addStandbyObj(m_ImageManager->beamFormer());
#ifdef USE_TARGET_PALM
    IStandbyObj* standbyObj = dynamic_cast<IStandbyObj*>(m_ImageManager->probePresetModel());
    Q_ASSERT(standbyObj != NULL);
    machine->addStandbyObj(standbyObj);
#endif

    ISystemStatusModel* systemStatusModel = m_ExamManager->systemStatusModel();
    Q_ASSERT(systemStatusModel != NULL);
    const QList<IStatusModel*> models = systemStatusModel->models();
    for (int i = 0; i < models.count(); i++)
    {
        if (models.at(i)->isSupportStandby())
        {
            machine->addStandbyObj(models.at(i));
        }
    }

    connect(machine, SIGNAL(afterWake(bool)), m_ImageManager->beamFormer(), SLOT(onAfterWake(bool)));
}

void MainModule::createToolsOrderManager()
{
    beforeBehavior(PRETTY_FUNCTION);

    m_ToolsOrderManager = new ToolsOrderManager();
    m_Context->setToolsOrderManager(m_ToolsOrderManager);
    m_ToolsOrderManager->setSonoParameters(m_ImageManager->sonoParameters());

    afterBehavior(PRETTY_FUNCTION);
}

void MainModule::createStateManagerParameterModel()
{
    beforeBehavior(PRETTY_FUNCTION);

    StateManagerParameterModel* stateManagerParameterModel =
        new StateManagerParameterModel(m_UltrasoundView->mainWindow()); //由Qt对象树负责删除
    stateManagerParameterModel->setProbeDataSet(m_ImageManager->probeDataSet());
    stateManagerParameterModel->setSonoParameters(m_ImageManager->sonoParameters());

    afterBehavior(PRETTY_FUNCTION);
}

void MainModule::createHintContainerWidget()
{
    HintContainerWidget* inputMethodWidget = new HintContainerWidget(m_Context->mainWindow());
    inputMethodWidget->setKeyInputMethod(m_PeripheralManager->keyProcessor()->keyInputMethodProxy());
    m_PeripheralManager->keyProcessor()->setCommentWidget(m_UltrasoundView->curImageTile());
    m_Context->setHintContainerWidget(inputMethodWidget);
}

void MainModule::checkLidState()
{
#ifdef USE_POWERKEY
    if (DeviceUtil::isPowerLid())
    {
        log()->info(QString("MainModule::checkLidState doLidCallback"));
        MainModule::lidCallback();
    }
#endif
}

#ifdef USE_4D

void MainModule::initFourDWidget()
{
    m_Context->chisonFourdProxy()->init();
    m_Context->sonoParasWidget()->setFourDGrayCurveThumbnail(m_Context->chisonFourdProxy()->fourdGrayCurveWidget());
    m_Context->chisonFourdProxy()->showFourDWidget(false);
    connect(m_Context->chisonUltrasoundContext(), SIGNAL(volumeDataPrepared(const FourDVolumeLineImageArgs&)),
            m_Context->chisonFourdProxy()->fourDLiveWidget(), SLOT(sendVolumeData(const FourDVolumeLineImageArgs&)),
            Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
}
#endif

int MainModule::powerKeyCallback()
{
    log()->info(QString("%1 Power Button pressed.").arg(PRETTY_FUNCTION));
    StateManager::getInstance().postEvent(StateEventNames::ShutDown);
    return 0;
}

int MainModule::lidCallback()
{
    if (!m_SoftWareIsInitialized)
    {
        return 0;
    }
    log()->info(QString("%1 lidding.").arg(PRETTY_FUNCTION));
    ICommand* cmd = ToolsFactory::instance().command(ToolNames::ShutDownStr);
    if (cmd == nullptr)
    {
        /**
         *  [Apple][BUG:51112]高偶发，按键休眠，唤醒后又出现关机弹框
            原因：因为时序问题，会在唤醒时机，驱动向上层触发按键被按下回调，触发流程
            修改：系统休眠之前，关闭该接口，完全唤醒后再打开; 修改日志使用log->info，打印流程
            影响点：按键休眠，按键唤醒，唤醒过程中再次按下休眠键，不会重复作用
         * */
        ShutDownTool::closePowerKey();
        ShutDownTool::standbyDirectly();
        ShutDownTool::createPowerKey();
    }
    else
    {
        ShutDownTool* shutDownTool = static_cast<ShutDownTool*>(cmd);
        if (shutDownTool != nullptr)
        {
            if (shutDownTool->IsBlocking())
            {
                shutDownTool->closeMsgBox();
            }
            shutDownTool->setIsLidding(true);
            StateManager::getInstance().postEvent(StateEventNames::ShutDown);
        }
        else
        {
            shutDownTool->setIsLidding(true);
            ShutDownTool::closePowerKey();
            ShutDownTool::standbyDirectly();
            ShutDownTool::createPowerKey();
        }
    }

    return 0;
}

void MainModule::onIntellConnParameterToolUpdated(const QString& toolName, const ICommandArgs& args)
{
    ITool* tool = ToolsFactory::instance().tool(toolName);
    if (nullptr != tool)
    {
        tool->setArgs(const_cast<ICommandArgs*>(&args));
        tool->run();
    }
}

#ifdef USE_FREEHAND3D
void MainModule::createFreeHand3DProxy()
{
    FreeHand3DProxy* proxy = new FreeHand3DProxy(m_Context, this);
    m_Context->setFreeHand3DProxy(proxy);
}

void MainModule::initFreeHand3DWidget()
{
    m_Context->freeHand3DProxy()->initFreeHand3DWidget();
}
#endif

void MainModule::updateToolsParameters(SonoParameters* sonoParameters)
{
    const QStringList& postParamNames = m_UltrasoundView->postParameterNames();
    foreach (const QString& postPara, postParamNames)
    {
        MenuTool* tool = qobject_cast<MenuTool*>(ToolsFactory::instance().tool(postPara));
        if (tool != NULL)
        {
            tool->setSonoParameters(sonoParameters);
        }
    }
}
