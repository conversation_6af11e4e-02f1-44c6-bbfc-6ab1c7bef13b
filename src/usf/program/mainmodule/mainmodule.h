#ifndef MAINMODULE_H
#define MAINMODULE_H
#include "usfprogrammain_global.h"

#include "usfobject.h"
#include "baseaspect.h"
#include <QObject>
#include <QVector>

class MainModuleContext;
class MainModuleInstance;
class KeyboardDialog;
class ApplicationEventFilter;
class ApplicationDeviceStatusManager;
class IPeripheralManager;
class IUltrasoundView;
class IImageManager;
class IExamManager;
class IMarkManager;
class DopplerViewerContext;
class SonoParameters;
class ToolsOrderManager;
class IToolsFacade;
class IStateManager;
class IDiskDevice;
class IColorMapManager;
class IUpdateProgess;
class ICommandArgs;

class USF_PROGRAM_MAIN_EXPORT MainModule : public QObject, public USFObject, public BaseAspect
{
    Q_OBJECT
public:
    MainModule(QObject* parent = 0);
    ~MainModule();
    void run();
    virtual void handleEvent(UEventType event_name, const USFVariant& variant);

private slots:
    void showMainWindow();
    void autoFreeze();

#ifdef USE_4D
    //    void createFourDProxy();
    void createChisonFourDProxy();
#endif
    void createStateManager();
    void createMenuTools();
    void listenerIntellConnControl();
    void installAppEventFilter();
    void removeAppEventFilter();
    void createEventFilterController();
    void createKeyEventCommitter();
    void createTestContainer();
    void setupTestContainer();
#ifdef SYS_WINDOWS
    void monitoringDeviceStatusChanges();
#endif
    void createSplashWindow();
    void initializeStandbyObjs();
    void createToolsOrderManager();
    void createStateManagerParameterModel();
    void createHintContainerWidget();
#ifdef USE_4D
    void initFourDWidget();
#endif
    void checkLidState();
    static int powerKeyCallback();
    static int lidCallback();

#ifdef USE_FREEHAND3D
    void createFreeHand3DProxy();
    void initFreeHand3DWidget();
#endif

    void onIntellConnParameterToolUpdated(const QString& toolName, const ICommandArgs& arg);

signals:
    void endCurState();

private:
    void updateToolsParameters(SonoParameters* sonoParameters);
    void createSubSytemObjects();
    void initSystemEnvironment();
    void checkSNInput();
    void checkRemainDays();
    void initUltraSoundSystemBegin();
    void initUltraSoundSystem();
    void initUltraSoundSystemEnd();
    void removeClcache();

private:
    MainModuleContext* m_Context;
    MainModuleInstance* m_Instance;
    ApplicationEventFilter* m_ApplicationEventFilter;
    ApplicationDeviceStatusManager* m_ApplicationDeviceManager;
    IPeripheralManager* m_PeripheralManager;
    IUltrasoundView* m_UltrasoundView;
    IImageManager* m_ImageManager;
    IExamManager* m_ExamManager;
    IMarkManager* m_MarkManager;
    DopplerViewerContext* m_ViewerContext;
    ToolsOrderManager* m_ToolsOrderManager;
    IUpdateProgess* m_UpdateProgress;
    IToolsFacade* m_ToolsFacade;
    IStateManager* m_StateManager;
    static bool m_SoftWareIsInitialized;
    IAspect* m_LogAspect;
    IAspect* m_UpdateProgressAspect;
    IAspect* m_RecordTimeAspect;
};

#endif // MAINMODULE_H
