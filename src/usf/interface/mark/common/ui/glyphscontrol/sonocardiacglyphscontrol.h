#ifndef SONOCARDIACGLYPHSCONTROL_H
#define SONOCARDIACGLYPHSCONTROL_H

#include "alginstance.h"
#include "measureglyphscontrol.h"
#include "workflowglyphscontrol.h"
#include <QGraphicsTextItem>
#include <QObject>
#include <QQueue>
#include <QRunnable>

class SonoCardiacSaveResult
{
public:
    SonoCardiacSaveResult()
    {
    }
    SonoCardiacSaveResult(SonoCardiacResult res, int i, int f)
        : result(res)
        , index(i)
        , front(f)
    {
    }

    SonoCardiacResult result;
    int index;
    int front;
};

class USF_INTERFACE_MARK_UI_GLYPHSCONTROL_EXPORT SonoCardiacMeasureData : public MeasureGlyphsData
{
public:
    virtual bool save(QSettings& iniWriter) const;
    virtual bool load(QSettings& iniReader);

private:
    SonoCardiacSaveResult* m_Res;
    SonoCardiacResult m_LoadResult;
};

class InitTask;
class SonoParameters;
class SonoCardiacAlg;
class USF_INTERFACE_MARK_UI_GLYPHSCONTROL_EXPORT SonoCardiacGlyphsControl : public WorkflowGlyphsControl
{
    Q_OBJECT
public:
    SonoCardiacGlyphsControl(IModeGlyphsWidget* modeGlyphsWidget, Overlay& overlaySet);
    virtual ~SonoCardiacGlyphsControl();
    virtual void postControlCommand(const QString&);
    virtual QGraphicsItem* activeGlyphs();
    CARDIAC_VIEW_TYPE SecType() const;
    void setSecType(const CARDIAC_VIEW_TYPE& SecType);

    int SecRecSwitch() const;
    void setSecRecSwitch(int SecRecSwitch);

    int RatingSwitch() const;
    void setRatingSwitch(int RatingSwitch);

    void addRatingItemToOverLay();
    void removeRatingItemInOverLay();

    void addTextItemToOverLay();
    void removeTextItemInOverLay();
    void drawScore(CARDIAC_QUALITY);

    void PrepareDataAndItems(SonoParameters*);
    void initAlgInThread();
    void initAlg();
    void releaseCtx();

signals:
    void algResult(SonoCardiacResult);
    void dispayImageChange();
    void ratingSwitchChanged();
    void secRecSwitchChanged();
    void secTypeChanged();

private slots:
    void drawText(SonoCardiacResult);

public:
    void ReconizeSection(const QImage& img);

    int ImageType() const;
    void setImageType(int ImageType);

private:
    CARDIAC_VIEW_TYPE m_SecType{0};
    int m_ImageType{0};
    int m_SecRecSwitch{0}; //心脏特征(心室心腔)功能开关：1(开启)，0(关闭)
    int m_RatingSwitch{0}; //心脏质量功能开关：1(开启)，0(关闭)
    QGraphicsTextItem m_TextItem[MAX_CARDIAC_FEATURE_NUM + 1];
    const QStringList m_SecName{"LV", "RV", "LA", "RA", "MV", "TV"};
    QGraphicsPixmapItem m_RatingPic;
    int m_RatingPosX;
    int m_RatingPosY;
    int m_TextPosX;
    int m_TextPosY;
    SonoCardiacAlg* m_SonoCardiacAlg;
};

class USF_INTERFACE_MARK_UI_GLYPHSCONTROL_EXPORT InitTask : public QRunnable
{
public:
    InitTask(SonoCardiacGlyphsControl* control)
        : m_Control(control){};
    SonoCardiacGlyphsControl* m_Control;

protected:
    void run() override
    {
        m_Control->initAlg();
    }
};

#endif // SONOCARDIACGLYPHSCONTROL_H
