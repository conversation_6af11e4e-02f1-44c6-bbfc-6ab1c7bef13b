#include "sonocardiacglyphscontrol.h"
#include "alginstance.h"
#include "bfpnames.h"
#include "modeluiconfig.h"
#include "overlay.h"
#include "resource.h"
#include "sonoparameters.h"
#include <QThreadPool>

#define MAXLEN 5

SonoCardiacGlyphsControl::SonoCardiacGlyphsControl(IModeGlyphsWidget* modeGlyphsWidget, Overlay& overlaySet)
    : WorkflowGlyphsControl(modeGlyphsWidget, overlaySet)
{
    qRegisterMetaType<SonoCardiacResult>("SonoCardiacResult");

    connect(this, &SonoCardiacGlyphsControl::algResult, this, &SonoCardiacGlyphsControl::drawText,
            Qt::ConnectionType::QueuedConnection);

    m_SonoCardiacAlg = AlgInstance::algInstance().getSonoCardiacAlg();
    QFont font = m_TextItem[0].font();
    font.setPixelSize(20);
    for (int i = 0; i < MAX_CARDIAC_FEATURE_NUM + 1; i++)
    {
        m_TextItem[i].setDefaultTextColor(
            QColor(ModelUiConfig::instance().value(ModelUiConfig::MeasureColor).toUInt()));
        m_TextItem[i].setFont(font);
    }

    QSettings setting(Resource::getHelperSettingDir(), QSettings::IniFormat);
    m_RatingPosX = setting.value("Rating_PosX").toInt();
    m_RatingPosY = setting.value("Rating_PosY").toInt();
    m_TextPosX = setting.value("Text_PosX").toInt();
    m_TextPosY = setting.value("Text_PosY").toInt();
}

SonoCardiacGlyphsControl::~SonoCardiacGlyphsControl()
{
}

void SonoCardiacGlyphsControl::postControlCommand(const QString&)
{
}

QGraphicsItem* SonoCardiacGlyphsControl::activeGlyphs()
{
    return NULL;
}

void SonoCardiacGlyphsControl::ReconizeSection(const QImage& img)
{
    if (overlay().isPaused() || img.isNull() || m_SonoCardiacAlg == nullptr || !m_SonoCardiacAlg->isInitialized())
    {
        return;
    }

    QImage convertedImage = img.convertToFormat(QImage::Format_Grayscale8);
    SonoCardiacResult stSonoCardiacResult; //识别结果
    stSonoCardiacResult.nCardiacFeatureNum = 0;
    UserInfo stUserInfo; //用户信息

    if (m_SecType == 0) //心脏切面类型：见宏定义
        stUserInfo.nCardiacViewType = CARDIAC_VIEW_TYPE_A2C;
    else if (m_SecType == 1)
        stUserInfo.nCardiacViewType = CARDIAC_VIEW_TYPE_A4C;
    else
        stUserInfo.nCardiacViewType = CARDIAC_VIEW_TYPE_UKNOWN;

    stUserInfo.bCardiacFeatureSwitch = m_SecRecSwitch;
    stUserInfo.bCardiacQualitySwitch = m_RatingSwitch;

    m_SonoCardiacAlg->ProcessSonoCardiac(m_SonoCardiacAlg->m_HSonoCardiacHandle, //智能心脏句柄
                                         &stUserInfo,                            //用户信息
                                         convertedImage.width(),                 //图像宽度
                                         convertedImage.height(),                //图像高度
                                         convertedImage.sizeInBytes(),           //图像数据大小(字节数)
                                         convertedImage.bits(),                  //灰度图像数据
                                         &stSonoCardiacResult);                  //[OUT]结果

    emit algResult(stSonoCardiacResult);
}

int SonoCardiacGlyphsControl::ImageType() const
{
    return m_ImageType;
}

void SonoCardiacGlyphsControl::setImageType(int ImageType)
{
    bool change = false;
    if (m_ImageType != ImageType)
        change = true;

    m_ImageType = ImageType;

    if (change)
        emit dispayImageChange();
}

int SonoCardiacGlyphsControl::RatingSwitch() const
{
    return m_RatingSwitch;
}

void SonoCardiacGlyphsControl::setRatingSwitch(int RatingSwitch)
{
    m_RatingSwitch = RatingSwitch;

    if (!m_RatingSwitch)
        m_RatingPic.setVisible(false);

    emit ratingSwitchChanged();
}

void SonoCardiacGlyphsControl::addRatingItemToOverLay()
{
    overlay().scene()->addItem(&m_RatingPic);
}

void SonoCardiacGlyphsControl::removeRatingItemInOverLay()
{
    overlay().scene()->removeItem(&m_RatingPic);
}

void SonoCardiacGlyphsControl::addTextItemToOverLay()
{
    for (int i = 0; i < MAX_CARDIAC_FEATURE_NUM + 1; i++)
        overlay().scene()->addItem(&m_TextItem[i]);
}

void SonoCardiacGlyphsControl::removeTextItemInOverLay()
{
    for (int i = 0; i < MAX_CARDIAC_FEATURE_NUM + 1; i++)
        overlay().scene()->removeItem(&m_TextItem[i]);
}

void SonoCardiacGlyphsControl::drawScore(CARDIAC_QUALITY quality)
{
    if (quality == CARDIAC_QUALITY_BAD)
    {
        m_RatingPic.setPixmap(QPixmap(":/images/zhong.png"));
    }
    else if (quality == CARDIAC_QUALITY_MIDDLE)
    {
        m_RatingPic.setPixmap(QPixmap(":/images/liang.png"));
    }
    else if (quality == CARDIAC_QUALITY_GOOD)
    {
        m_RatingPic.setPixmap(QPixmap(":/images/you.png"));
    }
    m_RatingPic.setPos(m_RatingPosX, m_RatingPosY);
    m_RatingPic.setVisible(true);
}

void SonoCardiacGlyphsControl::PrepareDataAndItems(SonoParameters* sono)
{
    if (sono == NULL)
        return;

    setRatingSwitch(sono->pBV(BFPNames::SonoCardiacSecRatingStr));
    setSecRecSwitch(sono->pBV(BFPNames::SonoCardiacSecRecognizeStr));
    setSecType(sono->pIV(BFPNames::SonoCardiacSectionStr));

    addTextItemToOverLay();
    addRatingItemToOverLay();

    initAlgInThread();
}

void SonoCardiacGlyphsControl::initAlgInThread()
{
    QThreadPool::globalInstance()->start(new InitTask(this));
}

void SonoCardiacGlyphsControl::initAlg()
{
    QString path = "./lib/SonoCardiacModels";
    QString logPath = "/usr/tigerapp/res/log";
    memset(m_SonoCardiacAlg->m_HInitInfo.chModelsFolderPath, 0, 256);
    memset(m_SonoCardiacAlg->m_HInitInfo.chAlgLogFolderPath, 0, 256);
    memcpy(m_SonoCardiacAlg->m_HInitInfo.chModelsFolderPath, path.toUtf8().data(), path.length());
    memcpy(m_SonoCardiacAlg->m_HInitInfo.chAlgLogFolderPath, logPath.toUtf8().data(), logPath.length());
    //创建智能心脏句柄
    m_SonoCardiacAlg->CreateSonoCardiacHandle(&m_SonoCardiacAlg->m_HSonoCardiacHandle, &m_SonoCardiacAlg->m_HInitInfo);
}

void SonoCardiacGlyphsControl::releaseCtx()
{
    m_SonoCardiacAlg->ReleaseSonoCardiacHandle(&m_SonoCardiacAlg->m_HSonoCardiacHandle);
}

void SonoCardiacGlyphsControl::drawText(SonoCardiacResult res)
{
    if (overlay().isPaused())
    {
        return;
    }

    for (int i = 0; i < res.nCardiacFeatureNum; i++)
    {
        CardiacFeatureInfo info = res.stArrCardiacFeatureInfo[i];
        if (info.nCardiacFeatureType > m_SecName.size() || info.nCardiacFeatureType < 0)
            return;
        m_TextItem[i].setPlainText(m_SecName[info.nCardiacFeatureType]);
        m_TextItem[i].setPos(info.nCenterX, info.nCenterY);
        m_TextItem[i].setVisible(true);
    }

    for (int i = res.nCardiacFeatureNum; i < MAX_CARDIAC_FEATURE_NUM; i++)
    {
        m_TextItem[i].setVisible(false);
    }

    //    if( res.nCardiacFeatureNum <= 0 && m_SecRecSwitch == 1 )
    //    {
    //        m_TextItem[MAX_CARDIAC_FEATURE_NUM].setVisible(true);
    //        m_TextItem[MAX_CARDIAC_FEATURE_NUM].setPlainText(tr("Standard section not recognized, \nplease adjust."));
    //        m_TextItem[MAX_CARDIAC_FEATURE_NUM].setPos(m_TextPosX, m_TextPosY);
    //    }
    //    else
    //    {
    //        m_TextItem[MAX_CARDIAC_FEATURE_NUM].setVisible(false);
    //    }

    if (m_RatingSwitch == 1)
        drawScore(res.nCardiacQuality);
    else
        m_RatingPic.setVisible(false);
}

int SonoCardiacGlyphsControl::SecRecSwitch() const
{
    return m_SecRecSwitch;
}

void SonoCardiacGlyphsControl::setSecRecSwitch(int SecRecSwitch)
{
    m_SecRecSwitch = SecRecSwitch;

    emit secRecSwitchChanged();
}

CARDIAC_VIEW_TYPE SonoCardiacGlyphsControl::SecType() const
{
    return m_SecType;
}

void SonoCardiacGlyphsControl::setSecType(const int& SecType)
{
    m_SecType = CARDIAC_VIEW_TYPE(SecType);

    emit secTypeChanged();
}
/*
智能心脏AI结果
typedef struct _SonoCardiacResult
{
    CARDIAC_VIEW_TYPE nCardiacViewType;//心脏切面类型：见宏定义
    int nCardiacFeatureNum;//心脏特征数量
    CardiacFeatureInfo stArrCardiacFeatureInfo[MAX_CARDIAC_FEATURE_NUM];//心脏特征数组
    CARDIAC_QUALITY nCardiacQuality;//心脏质量：见宏定义
    char chReserve[40];//预留
}SonoCardiacResult;
*/

bool SonoCardiacMeasureData::save(QSettings& iniWriter) const
{
    iniWriter.setValue("CardiacViewType", m_Res->result.nCardiacViewType);
    iniWriter.setValue("CardiacQuality", m_Res->result.nCardiacQuality);
    iniWriter.setValue("CardiacFeatureNum", m_Res->result.nCardiacFeatureNum);
    for (int i = 0; i < m_Res->result.nCardiacFeatureNum; i++)
    {
        iniWriter.setValue("CardiacFeatureType" + QString::number(i),
                           m_Res->result.stArrCardiacFeatureInfo[i].nCardiacFeatureType);
        iniWriter.setValue("Confidence" + QString::number(i), m_Res->result.stArrCardiacFeatureInfo[i].fConfidence);
        iniWriter.setValue("CenterX" + QString::number(i), m_Res->result.stArrCardiacFeatureInfo[i].nCenterX);
        iniWriter.setValue("CenterY" + QString::number(i), m_Res->result.stArrCardiacFeatureInfo[i].nCenterY);
    }
    iniWriter.setValue("Version", 0.1);
    return true;
}

bool SonoCardiacMeasureData::load(QSettings& iniReader)
{
    m_LoadResult.nCardiacViewType = iniReader.value("CardiacViewType").toInt();
    m_LoadResult.nCardiacQuality = iniReader.value("CardiacQuality").toInt();
    m_LoadResult.nCardiacFeatureNum = iniReader.value("CardiacFeatureNum").toInt();

    for (int i = 0; i < m_LoadResult.nCardiacFeatureNum; i++)
    {
        m_LoadResult.stArrCardiacFeatureInfo[i].nCardiacFeatureType =
            iniReader.value("CardiacFeatureType" + QString::number(i)).toInt();
        m_LoadResult.stArrCardiacFeatureInfo[i].fConfidence =
            iniReader.value("Confidence" + QString::number(i)).toInt();
        m_LoadResult.stArrCardiacFeatureInfo[i].nCenterX = iniReader.value("CenterX" + QString::number(i)).toInt();
        m_LoadResult.stArrCardiacFeatureInfo[i].nCenterY = iniReader.value("CenterY" + QString::number(i)).toInt();
    }
    return true;
}
