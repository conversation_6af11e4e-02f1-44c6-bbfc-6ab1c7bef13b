#ifndef REALTIMEAUTOTRACEMEASUREGLYPHSCONTROL_H
#define REALTIMEAUTOTRACEMEASUREGLYPHSCONTROL_H
#include "glyphscontrol_global.h"

#include "autotracemeasureglyphscontrol.h"

class AutoTraceMeasure;
class QGraphicsItem;

class USF_INTERFACE_MARK_UI_GLYPHSCONTROL_EXPORT RealTimeAutoTraceMeasureGlyphsControl
    : public AutoTraceMeasureGlyphsControl
{
    Q_OBJECT
public:
    RealTimeAutoTraceMeasureGlyphsControl(IModeGlyphsWidget* modeGlyphsWidget, Overlay& overlaySet);
    /**
     * @brief restoreCurrentActiveGlyphs 在Freeze恢复状态下恢复actvie图元
     */
    void restoreCurrentActiveGlyphs();
    /**
     * @brief clearActiveGlyphs 彻底清除actvie图元
     */
    void clearActiveGlyphs();
    /**
     * @brief storeActiveGlyphs 暂时记录actvie图元，始终维护当前的图元指针
     */
    void storeActiveGlyphs();

    void updateBeginPoints(const QList<QPointF>& pointList1, const QList<QPointF>& pointList2);

    void removeTraceLines();

    void resetActiveGlyphs();

    void upateAutoTraceGlyphs(int leftPos, int rightPos);

    void hideActiveGlyphs();

    virtual void showCrossAnchor() override;

    virtual bool ableOutOfLimitRect() const override;
public slots:
    void earseGraphics(QGraphicsItem* item);

protected:
    void clearGlyphs(QGraphicsItem* toBeRemoved);
    void updateBeginLines(const QList<QPointF>& points, int index);
    virtual void updateCrossAnchor(bool active) override;

protected:
    AutoTraceMeasure* m_CurrentActive;
    QGraphicsPathItem m_TraceLines[TraceNum];
};

#endif // REALTIMEAUTOTRACEMEASUREGLYPHSCONTROL_H
