/*
 * =====================================================================================
 *
 *       Filename:  SonoNerveGlyphsControl.h
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  2023年10月10日 10时56分58秒
 *       Function:  自动神经图元显示
 *
 *         Author:  pengfangmei, <EMAIL>
 *
 * =====================================================================================
 */

#ifndef SONONERVEGLYPHSCONTROL_H
#define SONONERVEGLYPHSCONTROL_H

#include "glyphscontrol_global.h"
#include "overlay.h"
#include "sononerveglyphs.h"
#include "sononervenote.h"
#include "workflowglyphscontrol.h"
#include <QtMath>
/**
 * @brief 自动神经图元显示
 *
 *
 */
class SonoNerveAlg;
class USF_INTERFACE_MARK_UI_GLYPHSCONTROL_EXPORT SonoNerveGlyphsControl : public WorkflowGlyphsControl
{
    Q_OBJECT
public:
    SonoNerveGlyphsControl(IModeGlyphsWidget* modeGlyphsWidget, Overlay& overlaySet);
    virtual ~SonoNerveGlyphsControl();

    virtual QGraphicsItem* activeGlyphs();

    virtual void postControlCommand(const QString& controlCommand);

    void setBImage(const QImage& img);

    void update(const QImage& img, bool isShow);

    void showActive();

    void hideActive();

    void setTransparent(int trans);

    void initNeura(int part);

    void neuralsgRelease();

    bool isActive();

    bool isInvert();

    bool isLRInvert();

    void setInvert(bool isInvert);

    void setLRInvert(bool isLRInvert);

    bool isCreatectx();

    void setIsActive(bool isActive);

    void addElementToCurrentScene();

    void resetPara();

    void setRenderWidgetHeight(int renderWidgetHeight);

    void updateNote(const int& part);

    int curPart() const;

signals:
    void setShowGlyphs(bool isShow, QHash<int, QList<QList<QPointF>>> data);
    void setShowGlyphsImmed(bool, QHash<int, QList<QList<QPointF>>> data);

public slots:
    void languageChanged();
    void onSetShowGlyphs(bool isShow, QHash<int, QList<QList<QPointF>>> data);

private:
    void initColors();
    void initGlyphs();
    void initNote();
    void initText();
    void showGlyphs(bool isShow);
    void showNote(bool isShow);
    void addNoteItem();
    void clearActiveGlyphs();

private:
    QHash<int, QList<SonoNerveGlyphs*>> m_Active; //透明色块，神经模块
                                                  //    SonoNerveGlyphs* m_Active{nullptr}; //透明色块，神经模块
                                                  //    SonoNerveNote* m_Note{nullptr};     //注释信息
    QList<QGraphicsRectItem*> m_NoteGraphs;        //注释色块
    QList<QGraphicsSimpleTextItem*> m_NoteComment; //注释名称
    QImage m_image;
    QString m_jjg;          //肌肩沟 mnn位置
    QString m_sgs;          //锁骨上 mnn位置
    QString m_median;       //正中神经
    QString m_jjgName;      //肌肩沟全称
    QString m_sgsName;      //锁骨上全称
    QString m_medianName;   //正中神经全称
    QString m_skeletonName; //骨骼
    QString m_vesselName;   //血管
    bool m_isActive{false};
    bool m_isInvert{false};
    bool m_isLRInvert{false};
    SonoNerveAlg* m_SonoNerverAlg;
    int m_transparent;
    QList<int> m_Types; // 0，神经  1，血管  骨骼
    QHash<int, QColor> m_colorHash;
    int m_RenderWidgetHeight;
    int m_curPart;
};

#endif /* end of include guard: BODY_MARK_GLYPHS_CONTROL_H */
