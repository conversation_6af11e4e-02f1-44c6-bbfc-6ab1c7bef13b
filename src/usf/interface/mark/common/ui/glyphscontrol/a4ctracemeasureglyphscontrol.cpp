/*
 * =====================================================================================
 *
 *       Filename:  a4ctracemeasureglyphscontrol.cpp
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  2012年12月05日 13时44分22秒
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  <PERSON><PERSON><PERSON><PERSON> (), <EMAIL>
 *   Organization:
 *
 * =====================================================================================
 */

#include "a4ctracemeasureglyphscontrol.h"

#include <boost/foreach.hpp>

#include "workflowglyphscontrolcommand.h"
#include "tracemeasureglyphscontrolinner.h"
#include "ruleranchor.h"
#include "overlay.h"
#include "a4ctracemeasure.h"
#include "setting.h"
#include "autocubicsplineglyphscontrolinner.h"
#include "settingitem.h"
#include "tracemeasure.h"
#include "autocubicspline.h"
#include "qmath.h"
#include "modeluiconfig.h"
#include "realcompare.h"
#include "formula.h"
#include "kochaneksplineresolver.h"
#include "threepointssplineglyphscontrolinner.h"
#include "threepointsspline.h"

static const char* STATE_MACHINE_DESC[] = {
    "Init+dummy=InnerState/",
    "InnerState+innerComplete[IsNotThreePoints]=PosCross/",
    "PosCross+completeThisStep=InnerState&LeaveFillArea", // change satemachine description, the origin description
                                                          // can't invoke function "LeaveFillArea"
    "PosCross+undoStep=InnerState/",
};

A4CTraceMeasureGlyphsControl::A4CTraceMeasureGlyphsControl(IModeGlyphsWidget* modeGlyphsWidget, Overlay& overlaySet)
    : MeasureGlyphsControl(modeGlyphsWidget, overlaySet)
    , m_outerStateMachine(this)
{
    //        m_outlineType=TracePolygon;
    //        m_outlineType=TraceSpline;

    m_outlineType = A4CTraceMeasure::TraceThreePointsSpline;

    BOOST_FOREACH (const char* const line, STATE_MACHINE_DESC)
    {
        m_outerStateMachine.parseAddition(line);
    }
    m_activeTrace = NULL;

    switchOutlineImpl();
}

void A4CTraceMeasureGlyphsControl::onLeftButtonPressed(const QPoint& pos)
{
    calcAndSetLimitRect();

    if (isInnerState())
    {
        m_innerControl->onLeftButtonPressed(pos);
        syncOverlayCursorToAnchor();
        return;
    }
    postControlCommand(WorkflowGlyphsControlCommand::CompleteThisStep);
}

void A4CTraceMeasureGlyphsControl::onRightButtonPressed(const QPoint& pos)
{
    if (isInnerState())
    {
        m_innerControl->onRightButtonPressed(pos);
        return;
    }

    postControlCommand(WorkflowGlyphsControlCommand::UndoStep);
}

void A4CTraceMeasureGlyphsControl::onLeftButtonDoubleClicked(const QPoint& pos)
{
    if (isInnerState())
    {
        m_innerControl->onLeftButtonDoubleClicked(pos);
        return;
    }
}

template <typename T> bool expectType(TraceGlyphsControlBase* control)
{
    T* pT = qobject_cast<T*>(control);

    Q_ASSERT_X(pT != NULL, "A4CTraceMeasureGlyphsControl::typeCheck", "glyphscontrol type unexpected");

    return true;
}

bool A4CTraceMeasureGlyphsControl::checkControlAndGlyphs()
{

    if (m_activeTrace == NULL)
    {
        return true;
    }

    if (m_innerControl.isNull())
    {
        return true;
    }

    Q_ASSERT_X(m_activeTrace->outlineImpl(), "A4CTraceMeasureGlyphsControl::checkControlAndGlyphs",
               "bad logic, A4CTrace created, but has null outline");

    /////////////////////////////////////////////////////////////////////////////////////
    TraceMeasureGlyphsControlInner* tryTrace = qobject_cast<TraceMeasureGlyphsControlInner*>(m_innerControl.data());
    if (tryTrace != NULL)
    {
        TraceMeasure* p = dynamic_cast<TraceMeasure*>(m_activeTrace->outlineImpl());
        Q_ASSERT_X(p, "A4CTraceMeasureGlyphsControl::checkControlAndGlyphs",
                   "bad logic, control is TraceMeasure-based, but activeTrace is not");
        return true;
    }

    /////////////////////////////////////////////////////////////////////////////////////
    ThreePointsSplineGlyphsControlInner* tryThreePoints =
        qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());
    if (tryThreePoints)
    {
        ThreePointsSpline* p = dynamic_cast<ThreePointsSpline*>(m_activeTrace->outlineImpl());
        Q_ASSERT_X(p, "A4CTraceMeasureGlyphsControl::checkControlAndGlyphs",
                   "bad logic, control is ThreePointsSpline-based, but activeTrace is not");
        return true;
    }

    /////////////////////////////////////////////////////////////////////////////////////
    AutoCubicSplineGlyphsControlInner* trySpline =
        qobject_cast<AutoCubicSplineGlyphsControlInner*>(m_innerControl.data());
    Q_ASSERT_X(trySpline, "A4CTraceMeasureGlyphsControl::checkControlAndGlyphs",
               "bad logic, control is not TraceMeasure-based,not also AutoCubicSpline-based");
    AutoCubicSpline* p = dynamic_cast<AutoCubicSpline*>(m_activeTrace->outlineImpl());
    Q_ASSERT_X(p, "A4CTraceMeasureGlyphsControl::checkControlAndGlyphs",
               "bad logic, control is AutoCubicSpline-based, but activeTrace is not");
    return true;
}

bool A4CTraceMeasureGlyphsControl::checkFlagAndControl()
{
    if (m_innerControl.isNull())
    {
        // not created
        Q_ASSERT_X(m_activeTrace == NULL, "A4CTraceMeasureGlyphsControl::checkType",
                   "bad logic,glyphs control is null, should not have valid activeTrace");
        return true;
    }
    TraceGlyphsControlBase* control = m_innerControl.data();
    if (m_outlineType == A4CTraceMeasure::TracePolygon)
    {
        return expectType<TraceMeasureGlyphsControlInner>(control);
    }
    else if (m_outlineType == A4CTraceMeasure::TraceThreePointsSpline)
    {
        return expectType<ThreePointsSplineGlyphsControlInner>(control);
    }
    else
    {
        return expectType<AutoCubicSplineGlyphsControlInner>(control);
    }
}

bool A4CTraceMeasureGlyphsControl::switchOutlineImpl()
{
    Q_ASSERT_X(m_activeTrace == NULL, "A4CTraceMeasureGlyphsControl::switchOutlineImpl",
               "can not switch outlineimpl when active trace");

    bool needSwitch = true;
    if (m_innerControl)
    {
        TraceMeasureGlyphsControlInner* tryCast = qobject_cast<TraceMeasureGlyphsControlInner*>(m_innerControl.data());
        if (tryCast != NULL)
        {
            needSwitch = (m_outlineType != A4CTraceMeasure::TracePolygon);
        }
        else
        {
            ThreePointsSplineGlyphsControlInner* cast =
                qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());
            if (cast != nullptr)
            {
                needSwitch = (m_outlineType != A4CTraceMeasure::TraceThreePointsSpline);
            }
            else
            {
                needSwitch = (m_outlineType != A4CTraceMeasure::TraceSpline); // add autocubicsplineglyphscontrol check
            }
        }
    }

    if (!needSwitch)
    {
        Q_ASSERT(checkFlagAndControl());
        return false;
    }

    //分开设置，需要设置曲线开口
    if (m_outlineType == A4CTraceMeasure::TracePolygon)
    {
        m_innerControl.reset(
            (TraceGlyphsControlBase*)(new TraceMeasureGlyphsControlInner(rootWidget(), overlay(), this)));
    }
    else if (m_outlineType == A4CTraceMeasure::TraceThreePointsSpline)
    {
        m_innerControl.reset(
            (TraceGlyphsControlBase*)(new ThreePointsSplineGlyphsControlInner(rootWidget(), overlay(), this)));
    }
    else
    {
        AutoCubicSplineGlyphsControlInner* control =
            new AutoCubicSplineGlyphsControlInner(rootWidget(), overlay(), this);
        control->setOpenEnded(true);
        m_innerControl.reset((TraceGlyphsControlBase*)control);
    }

    Q_ASSERT(m_innerControl);

    connect(m_innerControl.data(), SIGNAL(glyphsChanged(QGraphicsItem*, const QPointF&)), this,
            SLOT(innerGlyphsChanged(QGraphicsItem*, const QPointF&)));

    connect(m_innerControl.data(), SIGNAL(workflowCompleted(QGraphicsItem*)), this,
            SLOT(innerWorkflowCompleted(QGraphicsItem*)));

    connect(m_innerControl.data(), SIGNAL(traceUpdated(bool)), this, SLOT(innerTraceUpdated(bool)));

    Q_ASSERT(checkFlagAndControl());

    return true;
}

void A4CTraceMeasureGlyphsControl::onBeginAction()
{

    m_activeTrace = NULL;

    reset();

    overlay().setLimitRect(limitRect());

    resetSignalSendedFlag();

    emit glyphsChanged(m_activeTrace, overlay().currentPos());

    m_outerStateMachine.enterStateMachine();

    showAnchor();

    syncOverlayCursorToAnchor();

    m_IsFinished = false;
}

void A4CTraceMeasureGlyphsControl::EnterInnerState(const QString& fromState, const QString&)
{
    if (fromState == "Init")
    {

        Q_ASSERT(m_activeTrace == NULL);

        switchOutlineImpl();

        // must hide self cursor,show inner controls'
        //        hideAnchor();

        syncLimitRect();
        // inner state init

        //不论切换过outlineType还是完成过完整操作，
        //都已经调用过onEndAction,故必须调用onBeginAction
        m_innerControl->onBeginAction();

        return;
    }

    Q_ASSERT(fromState == "PosCross");

    Q_ASSERT(m_activeTrace);

    m_activeTrace->setCrossLineVisible(false);

    // should show inner anchor
    //    hideAnchor();

    m_innerControl->restoreLastCompleteState(m_activeTrace->outlineImpl());

    // inner control will change cursor to lastKeyPos,
    // so update this cursor
    syncOverlayCursorToAnchor();
}

ITrace* A4CTraceMeasureGlyphsControl::createCallback()
{
    Q_ASSERT(m_activeTrace == NULL);

    m_activeTrace = overlay().createItem<A4CTraceMeasure>(clipNode());

    TraceGlyphsControlBase* control = m_innerControl.data();
    Q_ASSERT_X(control != NULL, "A4CTraceMeasureGlyphsControl::createCallback",
               "bad logic,createCallback called with no inner control");

    ThreePointsSplineGlyphsControlInner* cast = qobject_cast<ThreePointsSplineGlyphsControlInner*>(control);
    if (cast != NULL)
    {
        m_activeTrace->setOutlineImpl<ThreePointsSpline>();
        m_activeTrace->setOutlineType(A4CTraceMeasure::TraceThreePointsSpline);
    }
    else
    {
        TraceMeasureGlyphsControlInner* tryCast = qobject_cast<TraceMeasureGlyphsControlInner*>(control);
        if (tryCast != nullptr)
        {
            m_activeTrace->setOutlineImpl<TraceMeasure>();
            m_activeTrace->setOutlineType(A4CTraceMeasure::TracePolygon);
        }
        else
        {
            expectType<AutoCubicSplineGlyphsControlInner>(control);

            m_activeTrace->setOutlineImpl<AutoCubicSpline>();
            m_activeTrace->setOutlineType(A4CTraceMeasure::TraceSpline);

            AutoCubicSpline* spline = dynamic_cast<AutoCubicSpline*>(m_activeTrace->outlineImpl());

            spline->setOpenEnded(false);
        }
        m_activeTrace->setPos(overlay().currentPos());
    }

    Q_ASSERT(checkControlAndGlyphs());

    highlightActive(true);

    return m_activeTrace->outlineImpl();
}

void A4CTraceMeasureGlyphsControl::onMovedAction(const QPoint& offset, const QPoint& pos)
{
    if (isInnerState())
    {
        m_innerControl->onMovedAction(offset, pos);
        // must update this anchor by inner move
        syncOverlayCursorToAnchor();
        showAnchor();
        return;
    }
    else if (m_outerStateMachine.currentState() == "PosCross")
    {
        Q_ASSERT(m_activeTrace);

        overlay().cursorMove(offset);
        syncOverlayCursorToAnchor();
        showAnchor();

        // must sync overlay pos to inner control
        m_innerControl->onMovedAction(offset, pos);

        m_activeTrace->setCrossLineSceneEndPos(overlay().currentPos());
    }
    emit glyphsChanged(activeGlyphs(), overlay().currentPos());
}

void A4CTraceMeasureGlyphsControl::EnterPosCross(const QString& fromState, const QString&)
{
    // when pos cross, always show cursor
    showAnchor();

    if (fromState == "InnerState")
    {
        Q_ASSERT(m_activeTrace);

        QPolygonF polygon;
        QPointF startPos, endPos;
        QPointF newStartPos, newEndPos, scenePos;
        qreal maxLen = 0;
        //设置起点
        TraceMeasureGlyphsControlInner* tryCast = qobject_cast<TraceMeasureGlyphsControlInner*>(m_innerControl.data());
        if (tryCast != NULL)
        {
            TraceMeasure* spline = dynamic_cast<TraceMeasure*>(m_activeTrace->outlineImpl());
            polygon = spline->polygon();
            startPos = polygon.first();
            endPos = polygon.last();

            newStartPos = (startPos + endPos) / 2;
            newEndPos = endPos;

            foreach (const QPointF& point, polygon)
            {
                qreal len = qPow(point.x() - newStartPos.x(), 2) + qPow(point.y() - newStartPos.y(), 2);
                if (len > maxLen)
                {
                    maxLen = len;
                    newEndPos = point;
                }
            }
            scenePos = spline->mapToScene(newEndPos);
        }
        else
        {
            AutoCubicSplineGlyphsControlInner* ctrlCast =
                qobject_cast<AutoCubicSplineGlyphsControlInner*>(m_innerControl.data());
            if (ctrlCast != nullptr)
            {
                AutoCubicSpline* spline = dynamic_cast<AutoCubicSpline*>(m_activeTrace->outlineImpl());
                Q_ASSERT(spline != nullptr);

                polygon = spline->polygon();
                startPos = polygon.first();
                if (polygon.size() >= 2)
                    endPos = polygon[polygon.size() - 2];
                else
                    endPos = startPos;

                newStartPos = (startPos + endPos) / 2;
                newEndPos = endPos;
                foreach (const QPointF& point, polygon)
                {
                    qreal len = qPow(point.x() - newStartPos.x(), 2) + qPow(point.y() - newStartPos.y(), 2);
                    if (len > maxLen)
                    {
                        maxLen = len;
                        newEndPos = point;
                    }
                }
                scenePos = spline->mapToScene(newEndPos);
            }
            else
            {
                Q_ASSERT_X(false, "A4CTraceMeasureGlyphsControl::EnterPosCross",
                           "ThreePoint does not support PosCross state");
            }
        }

        m_activeTrace->setCrossLineStartPos(newStartPos);
        m_activeTrace->setCrossLineVisible(true);

        //保证完成轨迹操作后，交叉线在光标处。否则下次移动会跳动
        QPointF offset = scenePos - overlay().currentPos();
        overlay().cursorMove(QPoint(offset.x(), offset.y()));
        syncOverlayCursorToAnchor();
        m_activeTrace->setCrossLineSceneEndPos(scenePos);

        emit glyphsChanged(activeGlyphs(), overlay().currentPos());
        return;
    }

    if (fromState == "FillArea")
    {
        m_activeTrace->setFillArea(false);
        emit glyphsChanged(activeGlyphs(), overlay().currentPos());
        return;
    }

    Q_ASSERT_X(false, "A4CTraceMeasureGlyphsControl::EnterPosCross", "unexpected transition happened");
}

void A4CTraceMeasureGlyphsControl::EnterFillArea(const QString& fromState, const QString&)
{

    Q_ASSERT_X(fromState == "PosCross", "A4CTraceMeasureGlyphsControl::EnterFillArea",
               "only PosCross->FillArea transition is allowed");

    Q_ASSERT(m_activeTrace);

    m_activeTrace->setFillArea(true);

    // after filling area , hide cursor, only complete will show
    hideAnchor();
}

bool A4CTraceMeasureGlyphsControl::isInnerState() const
{
    return m_outerStateMachine.currentState() == "InnerState";
}

ProxyGroup* A4CTraceMeasureGlyphsControl::activeGlyphs()
{
    return m_activeTrace;
}

void A4CTraceMeasureGlyphsControl::destoryCallback(ITrace* toDestory)
{
    Q_ASSERT(m_activeTrace);

    Q_ASSERT(m_activeTrace->outlineImpl() == toDestory);

    overlay().removeItem(m_activeTrace);

    m_activeTrace = NULL;
}

void A4CTraceMeasureGlyphsControl::drawMeasureGlyphs(MeasureGlyphsData* data)
{
    A4CTraceMeasureData* a4ctraceData = dynamic_cast<A4CTraceMeasureData*>(data);

    Q_ASSERT(a4ctraceData != nullptr);

    reset();
    overlay().setLimitRect(limitRect());
    resetSignalSendedFlag();

    if (a4ctraceData->outlineType == A4CTraceMeasure::TraceThreePointsSpline)
    {
        m_outlineType = A4CTraceMeasure::TraceThreePointsSpline;

        switchOutlineImpl();

        ThreePointsSplineGlyphsControlInner* tryThreePoints =
            qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());
        Q_ASSERT(tryThreePoints != nullptr);

        tryThreePoints->drawMeasureGlyphs(a4ctraceData->splineData.get());

        m_activeTrace->setGlyphsData(a4ctraceData);

        highlightActive(false);

        m_activeTrace = nullptr;
    }
    else
    {
        Q_ASSERT(false);
    }
    m_IsFinished = true;
}

void A4CTraceMeasureGlyphsControl::resetGlyphs()
{
    ThreePointsSplineGlyphsControlInner* tryThreePoints =
        qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());
    if (tryThreePoints != nullptr)
    {
        tryThreePoints->resetGlyphs();
    }
    hideAnchor();

    m_IsFinished = true;
    m_activeTrace = nullptr;
}

void A4CTraceMeasureGlyphsControl::removeGlyphs()
{
    if (m_activeTrace != nullptr)
    {
        overlay().removeItem(m_activeTrace);
    }
    resetGlyphs();
}

bool A4CTraceMeasureGlyphsControl::isFinished() const
{
    return m_IsFinished;
}

void A4CTraceMeasureGlyphsControl::setFinished()
{
    ThreePointsSplineGlyphsControlInner* tryThreePoints =
        qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());

    if (tryThreePoints != nullptr)
    {
        tryThreePoints->onEndAction();
    }
    onEndAction();
    m_IsFinished = true;
}

void A4CTraceMeasureGlyphsControl::onTouchPressed(const QPoint& pt)
{
    ThreePointsSplineGlyphsControlInner* tryThreePoints =
        qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());

    if (tryThreePoints != nullptr)
    {
        tryThreePoints->onTouchPressed(pt);
    }
}

void A4CTraceMeasureGlyphsControl::onTouchReleased(const QPoint& pt)
{
    ThreePointsSplineGlyphsControlInner* tryThreePoints =
        qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());

    if (tryThreePoints != nullptr)
    {
        tryThreePoints->onTouchReleased(pt);
    }
}

void A4CTraceMeasureGlyphsControl::setTouchPressed(bool value, const QPoint& pt)
{
    ThreePointsSplineGlyphsControlInner* tryThreePoints =
        qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());

    if (tryThreePoints != nullptr)
    {
        tryThreePoints->setTouchPressed(value, pt);
    }
}

bool A4CTraceMeasureGlyphsControl::isBindReleaseType() const
{
    ThreePointsSplineGlyphsControlInner* tryThreePoints =
        qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());

    if (tryThreePoints != nullptr)
    {
        return tryThreePoints->isBindReleaseType();
    }
    return false;
}

bool A4CTraceMeasureGlyphsControl::ableOutOfLimitRect() const
{
    ThreePointsSplineGlyphsControlInner* tryThreePoints =
        qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());

    if (tryThreePoints != nullptr)
    {
        return tryThreePoints->ableOutOfLimitRect();
    }
    return true;
}

void A4CTraceMeasureGlyphsControl::afterMouseMovingIn(const QPoint& offset, const QPoint& pos)
{
    if (isInnerState())
    {
        ThreePointsSplineGlyphsControlInner* tryThreePoints =
            qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());

        if (tryThreePoints != nullptr)
        {
            tryThreePoints->afterMouseMovingIn(offset, pos);
        }
        syncOverlayCursorToAnchor();
    }
    if (activeGlyphs() != nullptr)
    {
        emit glyphsChanged(activeGlyphs(), overlay().currentPos());
    }
}

bool A4CTraceMeasureGlyphsControl::IsNotThreePoints()
{
    return !IsThreePoints();
}

bool A4CTraceMeasureGlyphsControl::IsThreePoints()
{
    return m_outlineType == A4CTraceMeasure::TraceThreePointsSpline;
}

void A4CTraceMeasureGlyphsControl::postControlCommand(const QString& controlCommand)
{

    if (isInnerState())
    {
        // TODO relax control command check

        m_innerControl->postControlCommand(controlCommand);
        return;
    }

    // TODO check command
    m_outerStateMachine.postEvent(controlCommand);
}

void A4CTraceMeasureGlyphsControl::onEndAction()
{
    if (IsNotThreePoints())
    {
        if (isInnerState())
        {
            m_innerControl->onEndAction();

            // remove item only, do not notify system that this measurement is complete,
            // because if you do this, continue measurement cannot work correctly
            if (m_activeTrace != NULL)
            {
                overlay().removeItem(m_activeTrace);
                m_activeTrace = NULL;
            }
            emit currentValueCleared();

            hideAnchor();
            m_IsFinished = true;
            return;
        }

        if (m_activeTrace != nullptr)
        {
            LeaveFillArea();
        }
    }
    else
    {
        m_innerControl->hideAnchor();
        m_activeTrace = nullptr;
        hideAnchor();
    }
    m_IsFinished = true;
}

void A4CTraceMeasureGlyphsControl::LeaveFillArea()
{
    Q_ASSERT_X(m_activeTrace, "A4CTraceMeasureGlyphsControl::LeaveFillArea",
               "bad logic,when LeaveFillArea, there must be valid active trace");

    highlightActive(false);

    m_activeTrace->setFillArea(true);
    showAnchor();

    //需要先隐藏，不然在连续测量模式下发信号后会开始下一次
    //下一次测量如果先执行，打开了锚点，此时再执行此函数的话
    //会隐藏锚点，导致锚点显示不了
    m_innerControl->hideAnchor();

    noneRecursiveSendWorkflowCompleted(m_activeTrace);

    m_activeTrace = NULL;

    //仅支持一次操作流程，操作结束后需要隐藏光标
    anchor()->hide();
}

void A4CTraceMeasureGlyphsControl::setOutlineType(A4CTraceMeasure::OutlineType outlineType)
{
    Q_ASSERT(checkControlAndGlyphs());
    m_outlineType = outlineType;
}

A4CTraceMeasure::OutlineType A4CTraceMeasureGlyphsControl::outlineType() const
{
    return m_outlineType;
}

void A4CTraceMeasureGlyphsControl::innerGlyphsChanged(QGraphicsItem* glyph, const QPointF& currentCursorPos)
{
    Q_UNUSED(glyph);

    emit glyphsChanged(m_activeTrace, currentCursorPos);
}

void A4CTraceMeasureGlyphsControl::innerWorkflowCompleted(QGraphicsItem* glyph)
{
    Q_UNUSED(glyph);
    if (IsNotThreePoints())
    {
        m_innerControl->onEndAction();
        m_outerStateMachine.postEvent("innerComplete");
    }
    else
    {
        if (m_activeTrace != nullptr)
        {
            highlightActive(false);
            noneRecursiveSendWorkflowCompleted(m_activeTrace);
            m_activeTrace = nullptr;
        }
    }
}

void A4CTraceMeasureGlyphsControl::syncLimitRect()
{
    Q_ASSERT(m_innerControl);

    m_innerControl->setLimitStyle(limitStyle());
    m_innerControl->setLimitImageType(limitImageType());
}

void A4CTraceMeasureGlyphsControl::innerTraceUpdated(bool allUpdated)
{
    ThreePointsSplineGlyphsControlInner* tryThreePoints =
        qobject_cast<ThreePointsSplineGlyphsControlInner*>(m_innerControl.data());
    Q_ASSERT(tryThreePoints != nullptr);

    ThreePointsSpline* spline = dynamic_cast<ThreePointsSpline*>(m_activeTrace->outlineImpl());

    m_activeTrace->setCrossLineVisible(true);
    if (allUpdated)
    {
        QPointF startPos, endPos;
        startPos = spline->crossStartScenePoint();
        endPos = spline->crossEndScenePoint();

        m_activeTrace->setCrossLineSceneStartPos(startPos);
        m_activeTrace->setCrossLineSceneEndPos(endPos);
        m_activeTrace->setFillArea(true);
    }

    m_activeTrace->setGlyphsControlPoints(spline->glyphsControlPoints());
    m_activeTrace->udpateFillArea();
    emit glyphsChanged(activeGlyphs(), overlay().currentPos());
}

void A4CTraceMeasureGlyphsControl::highlightActive(bool active)
{
    if (m_activeTrace == nullptr)
    {
        return;
    }

    int type = ModelUiConfig::instance().value(ModelUiConfig::MeasureLineType).toInt();

    QColor colorSet = active ? QColor(ModelUiConfig::instance().value(ModelUiConfig::ActiveMeasureColor).toUInt())
                             : QColor(ModelUiConfig::instance().value(ModelUiConfig::MeasureColor).toUInt());

    QPen crossLinePen = m_activeTrace->crossLinePen();
    MeasureGlyphs::setSolidPenStyle(crossLinePen, type);
    crossLinePen.setStyle(type == 1 ? Qt::SolidLine : active ? Qt::DotLine : Qt::SolidLine);
    crossLinePen.setColor(colorSet);
    m_activeTrace->setCrossLinePen(crossLinePen);

    QPen fillPen = m_activeTrace->fillPen();
    fillPen.setColor(colorSet);
    m_activeTrace->setFillPen(fillPen);
}
