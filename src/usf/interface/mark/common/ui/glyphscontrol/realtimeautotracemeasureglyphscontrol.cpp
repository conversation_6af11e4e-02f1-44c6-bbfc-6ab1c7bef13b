#include "realtimeautotracemeasureglyphscontrol.h"
#include "autotracemeasure.h"
#include "overlay.h"
#include "modeluiconfig.h"

RealTimeAutoTraceMeasureGlyphsControl::RealTimeAutoTraceMeasureGlyphsControl(IModeGlyphsWidget* modeGlyphsWidget,
                                                                             Overlay& overlaySet)
    : AutoTraceMeasureGlyphsControl(modeGlyphsWidget, overlaySet)
    , m_CurrentActive(NULL)
{
    QColor color = QColor(ModelUiConfig::instance().value(ModelUiConfig::ActiveMeasureColor).toUInt());
    QPen pen(color);
    pen.setStyle(Qt::DotLine);

    connect(&overlay(), SIGNAL(itemRemoving(QGraphicsItem*)), this, SLOT(earseGraphics(QGraphicsItem*)));

    for (int i = 0; i < TraceNum; i++)
    {
        pen.setStyle(Qt::SolidLine);
        pen.setColor(Qt::white);
        m_TraceLines[i].setPen(pen);
        m_TraceLines[i].setCacheMode(QGraphicsItem::NoCache);
        m_TraceLines[i].setVisible(false);
    }
}

void RealTimeAutoTraceMeasureGlyphsControl::restoreCurrentActiveGlyphs()
{
    if (NULL == m_CurrentActive || NULL != m_active)
    {
        return;
    }

    m_active = m_CurrentActive;
}

void RealTimeAutoTraceMeasureGlyphsControl::clearActiveGlyphs()
{
    clearGlyphs(m_CurrentActive);
    m_CurrentActive = NULL;
}

void RealTimeAutoTraceMeasureGlyphsControl::storeActiveGlyphs()
{
    m_CurrentActive = m_active;
}

void RealTimeAutoTraceMeasureGlyphsControl::updateBeginPoints(const QList<QPointF>& pointList1,
                                                              const QList<QPointF>& pointList2)
{
    for (int i = 0; i < TraceNum; i++)
    {
        if (m_TraceLines[i].parentItem() == NULL)
        {
            m_TraceLines[i].setParentItem(clipNode());
        }
        QPen pen = m_TraceLines[i].pen();
        m_TraceLines[i].setPen(pen);

        if (!m_TraceLines[i].isVisible())
        {
            m_TraceLines[i].setVisible(true);
        }
    }

    updateBeginLines(pointList1, TraceMaxIdx);
    updateBeginLines(pointList2, TraceMeanIdx);
}

void RealTimeAutoTraceMeasureGlyphsControl::removeTraceLines()
{
    for (int i = 0; i < TraceNum; i++)
    {
        m_TraceLines[i].setPath(QPainterPath());
        m_TraceLines[i].setVisible(false);
    }
}

void RealTimeAutoTraceMeasureGlyphsControl::resetActiveGlyphs()
{
    m_active = NULL;
}

void RealTimeAutoTraceMeasureGlyphsControl::upateAutoTraceGlyphs(int leftPos, int rightPos)
{
    if (m_active == NULL)
    {
        m_active = overlay().createItem<AutoTraceMeasure>(clipNode());
    }
    QPointF startPoint(leftPos, 0), endPoint(rightPos, 0);

    m_active->highlight(true);
    updateCrossAnchor(true);

    m_active->setPos(startPoint);
    m_active->putCrossLine(endPoint, 1);

    if (!m_active->isVisible())
    {
        m_active->setVisible(true);
    }

    highLightActive(true);

    emit glyphsChanged(m_active, overlay().currentPos());
}

void RealTimeAutoTraceMeasureGlyphsControl::hideActiveGlyphs()
{
    if (NULL == m_active)
    {
        return;
    }

    m_active->setVisible(false);
    highLightActive(false);
}

void RealTimeAutoTraceMeasureGlyphsControl::showCrossAnchor()
{
}

void RealTimeAutoTraceMeasureGlyphsControl::clearGlyphs(QGraphicsItem* toBeRemoved)
{
    if (toBeRemoved != NULL)
    {
        if (this->overlay().isContainsItem(toBeRemoved) && toBeRemoved->scene() == this->overlay().scene())
        {
            // 删除测量线
            this->overlay().removeItem(toBeRemoved);
        }
    }
    resetActiveGlyphs();
    updateCrossAnchor(true);
}

void RealTimeAutoTraceMeasureGlyphsControl::updateBeginLines(const QList<QPointF>& points, int index)
{
    if (index >= TraceNum)
    {
        return;
    }
    QPainterPath path;
    int count = points.count();
    if (count > 0)
    {
        path.moveTo(m_TraceLines[index].mapFromItem(activeWidget(), points.at(0)));
        for (int i = 0; i < count - 1; ++i)
        {
            path.lineTo(m_TraceLines[index].mapFromItem(activeWidget(), points.at(i)));
            path.lineTo(m_TraceLines[index].mapFromItem(activeWidget(), points.at(i + 1)));
        }
    }
    m_TraceLines[index].setPath(path);
    updateCrossAnchor(true);
}

void RealTimeAutoTraceMeasureGlyphsControl::updateCrossAnchor(bool active)
{
    if (active)
    {
        m_crossAnchor[0]->hide();
        m_crossAnchor[1]->hide();
    }
}

bool RealTimeAutoTraceMeasureGlyphsControl::ableOutOfLimitRect() const
{
    return false;
}

void RealTimeAutoTraceMeasureGlyphsControl::earseGraphics(QGraphicsItem* item)
{
    if (m_active == item)
    {
        m_active = nullptr;
    }
}
