#include "sononerveglyphscontrol.h"
#include "alginstance.h"
#include "overlay.h"
#include "qelapsedtimer.h"
#include "resource.h"
#include "util.h"
#include "workflowglyphscontrolcommand.h"
#include <stdio.h>
#include <iostream>
#define MAXTRANSPARENT 255
#define MAXTRANSVALUE 100.00

void SonoNerveGlyphsControl::postControlCommand(const QString& controlCommand)
{
}

SonoNerveGlyphsControl::SonoNerveGlyphsControl(IModeGlyphsWidget* modeGlyphsWidget, Overlay& overlaySet)
    : WorkflowGlyphsControl(modeGlyphsWidget, overlaySet)
    , m_transparent(0)
    , m_RenderWidgetHeight(770)
    , m_curPart(0)
{
    m_jjg = Resource::mnnDir() + "/bp.mnn";     //肌肩沟
    m_sgs = Resource::mnnDir() + "/bp.mnn";     //锁骨上
    m_median = Resource::mnnDir() + "/mdn.mnn"; //正中神经
    initText();
    m_SonoNerverAlg = AlgInstance::algInstance().getSonoNerveAlg();

    initColors();
    initGlyphs();
    initNote();

    qRegisterMetaType<QHash<int, QList<QList<QPointF>>>>("QHash<int, QList<QPointF>>");
    connect(this, SIGNAL(setShowGlyphs(bool, QHash<int, QList<QList<QPointF>>>)), this,
            SLOT(onSetShowGlyphs(bool, QHash<int, QList<QList<QPointF>>>)),
            Qt::ConnectionType(Qt::QueuedConnection | Qt::UniqueConnection));
}

SonoNerveGlyphsControl::~SonoNerveGlyphsControl()
{
}

QGraphicsItem* SonoNerveGlyphsControl::activeGlyphs()
{
    return nullptr;
}

void SonoNerveGlyphsControl::setBImage(const QImage& img)
{
    m_image = img;
}

void SonoNerveGlyphsControl::update(const QImage& img, bool isShow)
{
    if (overlay().isPaused() || img.isNull() || m_SonoNerverAlg == nullptr || !m_SonoNerverAlg->isInitialized())
        return;

    QImage im = Util::toGrayImage(img);

    NVAI::MixedObject* mixobj;

    /*
     *imgptr ：图像指针，8bit
     *width:图像宽度
     *height:图像高度
     *points:图像返回的轮廓点
     *fliplr: 0：没有左右翻转 1：左右翻转了
     *flipul: 0：没有上下翻转 1：上下翻转了
     *NEURALDT_API void neuralsg_process2(unsigned char * imgptr, int width, int height,bool fliplr, bool flipul,
     *std::vector<BPoint> &points);
     *
     *
     *typedef struct _MixedObject {
    BPoint *pts;
    int size;// pts的长度，轮廓的长度
    int type;// 0：神经  1：血管  2：骨骼
    _MixedObject() {};
}MixedObject;
     *
     */
    int ret = m_SonoNerverAlg->neuralsgProcess(im.bits(), im.width(), im.height(), m_isLRInvert, m_isInvert, mixobj);

    QHash<int, QList<QList<QPointF>>> data;
    for (int i = 0; i < ret; i++)
    {
        QList<QPointF> points;
        for (int j = 0; j < mixobj[i].size; j++)
        {
            points.push_back(QPointF(mixobj[i].pts[j].x, mixobj[i].pts[j].y));
        }

        if (points.size() > 0)
        {
            data[mixobj[i].type].append(points);
        }
    }

    emit setShowGlyphs(isShow, data);
}

void SonoNerveGlyphsControl::showActive()
{
    showGlyphs(true);
    showNote(true);
}

void SonoNerveGlyphsControl::hideActive()
{
    showGlyphs(false);
    showNote(false);
}

void SonoNerveGlyphsControl::setTransparent(int trans)
{
    // 0表示不透明， 255表示完全透明，与Qt系统相反
    int tr = (int)(MAXTRANSPARENT * (abs(MAXTRANSVALUE - trans) / MAXTRANSVALUE));
    m_transparent = tr;
    if (!m_Active.isEmpty())
    {
        foreach (QList<SonoNerveGlyphs*> items, m_Active.values())
        {
            foreach (SonoNerveGlyphs* item, items)
            {
                item->setTransparent(tr);
            }
        }
    }
    if (!m_NoteGraphs.isEmpty())
    {
        for (int i = 0; i < m_NoteGraphs.size(); i++)
        {
            m_colorHash[i].setAlpha(tr);
            m_NoteGraphs.at(i)->setBrush(m_colorHash[i]);
            m_NoteGraphs.at(i)->setPen(Qt::NoPen);
        }
    }
}

void SonoNerveGlyphsControl::initNeura(int part)
{
    if (m_SonoNerverAlg->isInitialized())
    {
        m_SonoNerverAlg->neuralsgRelease();
    }

    m_curPart = part;

    if (part == 0)
    {
        m_SonoNerverAlg->neuralsgCreatectx(m_jjg.toStdString().c_str(), 0, 0);
    }
    else if (part == 1)
    {
        m_SonoNerverAlg->neuralsgCreatectx(m_sgs.toStdString().c_str(), 1, 0);
    }
    else if (part == 2)
    {
        m_SonoNerverAlg->neuralsgCreatectx(m_median.toStdString().c_str(), 2, 0);
    }
}

void SonoNerveGlyphsControl::neuralsgRelease()
{
    m_SonoNerverAlg->neuralsgRelease();
}

bool SonoNerveGlyphsControl::isActive()
{
    return m_isActive;
}

bool SonoNerveGlyphsControl::isInvert()
{
    return m_isInvert;
}

bool SonoNerveGlyphsControl::isLRInvert()
{
    return m_isLRInvert;
}

void SonoNerveGlyphsControl::setInvert(bool isInvert)
{
    m_isInvert = isInvert;
}

void SonoNerveGlyphsControl::setLRInvert(bool isLRInvert)
{
    m_isLRInvert = isLRInvert;
}

bool SonoNerveGlyphsControl::isCreatectx()
{
    return m_SonoNerverAlg->isInitialized();
}

void SonoNerveGlyphsControl::setIsActive(bool isActive)
{
    m_isActive = isActive;
}

void SonoNerveGlyphsControl::addElementToCurrentScene()
{
    foreach (QGraphicsRectItem* item, m_NoteGraphs)
    {
        if (item != nullptr)
        {
            overlay().scene()->addItem(item);
        }
    }

    foreach (QGraphicsSimpleTextItem* item, m_NoteComment)
    {
        if (item != nullptr)
        {
            overlay().scene()->addItem(item);
        }
    }

    foreach (QList<SonoNerveGlyphs*> items, m_Active)
    {
        foreach (SonoNerveGlyphs* item, items)
        {
            if (item != nullptr)
            {
                overlay().scene()->addItem(item);
            }
        }
    }
}

void SonoNerveGlyphsControl::resetPara()
{
}

void SonoNerveGlyphsControl::setRenderWidgetHeight(int renderWidgetHeight)
{
    m_RenderWidgetHeight = renderWidgetHeight;
}

void SonoNerveGlyphsControl::updateNote(const int& part)
{
    if (!m_NoteComment.isEmpty())
    {
        QString nerveText;
        int bottomMargin = 35;
        int leftMargin = 15;
        switch (part)
        {
        case 0:
            nerveText = m_jjgName;
            break;
        case 1:
            nerveText = m_sgsName;
            break;
        case 2:
            nerveText = m_medianName;
            break;
        default:
            break;
        }

        m_NoteComment.at(0)->setText(Util::translate("Comment", nerveText));
        m_NoteComment.at(1)->setText(Util::translate("Comment", m_vesselName));
        m_NoteComment.at(2)->setText(Util::translate("Comment", m_skeletonName));

        for (int type = 0; type < 3; type++)
        {
            m_NoteGraphs.at(type)->setRect(
                70, m_RenderWidgetHeight - m_NoteGraphs.at(type)->boundingRect().height() - bottomMargin, 30, 30);
            m_NoteComment.at(type)->setPos(70 + m_NoteGraphs.at(type)->boundingRect().width() + leftMargin,
                                           m_RenderWidgetHeight - m_NoteGraphs.at(type)->boundingRect().height() -
                                               bottomMargin);
            bottomMargin += 35;
        }
    }
}

void SonoNerveGlyphsControl::languageChanged()
{
    updateNote(m_curPart);
}

void SonoNerveGlyphsControl::onSetShowGlyphs(bool isShow, QHash<int, QList<QList<QPointF>>> data)
{
    if (overlay().isPaused())
    {
        return;
    }

    m_Types.clear();
    clearActiveGlyphs();
    for (QHash<int, QList<QList<QPointF>>>::iterator iter = data.begin(); iter != data.end(); iter++)
    {
        int type = iter.key();
        QList<QList<QPointF>> points = iter.value();
        if (points.count() > 0)
        {
            for (int i = 0; i < points.size(); i++)
            {
                if (m_Active.value(type).size() > i && m_Active.value(type).at(i) != nullptr)
                {
                    QList<QPointF> curPath = points.at(i);
                    m_Active.value(type).at(i)->setPath(curPath);
                    m_Active.value(type).at(i)->setColor(m_colorHash.value(type));

                    if (isShow)
                    {
                        m_Active.value(type).at(i)->show();
                    }
                    else
                    {
                        m_Active.value(type).at(i)->hide();
                    }
                }
            }

            m_Types.append(type);
        }
    }

    showNote(isShow);
}

void SonoNerveGlyphsControl::initColors()
{
    m_colorHash[0] = QColor(255, 255, 8);
    m_colorHash[1] = QColor(168, 47, 69);
    m_colorHash[2] = QColor(154, 92, 97);

    m_colorHash[0].setAlpha(180);
    m_colorHash[1].setAlpha(180);
    m_colorHash[2].setAlpha(180);
}

void SonoNerveGlyphsControl::initGlyphs()
{
    if (m_Active.isEmpty())
    {
        //假设，同一帧图像最多识别出10个神经、10个骨骼、10个血管
        for (int i = 0; i < 10; i++)
        {
            SonoNerveGlyphs* nerve = overlay().createEternalItem<SonoNerveGlyphs>();
            m_Active[0].append(nerve);
        }

        for (int i = 0; i < 10; i++)
        {
            SonoNerveGlyphs* blood = overlay().createEternalItem<SonoNerveGlyphs>();
            m_Active[1].append(blood);
        }

        for (int i = 0; i < 10; i++)
        {
            SonoNerveGlyphs* bones = overlay().createEternalItem<SonoNerveGlyphs>();
            m_Active[2].append(bones);
        }
    }
}

void SonoNerveGlyphsControl::initNote()
{
    if (m_NoteGraphs.isEmpty() && m_NoteComment.isEmpty())
    {
        for (int i = 0; i < 3; i++)
        {
            addNoteItem();
        }

        if (m_NoteGraphs.size() > 2)
        {
            m_NoteGraphs.at(0)->setBrush(QBrush(m_colorHash[0]));
            m_NoteGraphs.at(0)->setPen(Qt::NoPen);

            m_NoteGraphs.at(1)->setBrush(QBrush(m_colorHash[1]));
            m_NoteGraphs.at(1)->setPen(Qt::NoPen);

            m_NoteGraphs.at(2)->setBrush(QBrush(m_colorHash[2]));
            m_NoteGraphs.at(2)->setPen(Qt::NoPen);
        }
    }

    if (m_NoteComment.size() > 2)
    {
        m_NoteComment.at(0)->setText(Util::translate("Comment", m_jjgName));
        m_NoteComment.at(1)->setText(Util::translate("Comment", m_vesselName));
        m_NoteComment.at(2)->setText(Util::translate("Comment", m_skeletonName));

        m_NoteComment.at(0)->setFont(QFont(m_NoteComment.at(0)->font().family(), 16));
        m_NoteComment.at(1)->setFont(QFont(m_NoteComment.at(1)->font().family(), 16));
        m_NoteComment.at(2)->setFont(QFont(m_NoteComment.at(2)->font().family(), 16));

        m_NoteComment.at(0)->setBrush(Qt::white);
        m_NoteComment.at(1)->setBrush(Qt::white);
        m_NoteComment.at(2)->setBrush(Qt::white);
    }
}

void SonoNerveGlyphsControl::initText()
{
    m_jjgName = QString("Scalenus brachial plexus");
    m_sgsName = QString("Supraclavicular brachial plexus");
    m_medianName = QString("Median nerve");
    m_skeletonName = QString("Skeleton");
    m_vesselName = QString("Vessel");
}

void SonoNerveGlyphsControl::showGlyphs(bool isShow)
{
    foreach (QList<SonoNerveGlyphs*> items, m_Active)
    {
        foreach (SonoNerveGlyphs* item, items)
        {
            if (item != nullptr && item->isVisible())
            {
                item->hide();
            }
        }
    }

    if (isShow)
    {
        foreach (int type, m_Types)
        {
            QList<SonoNerveGlyphs*> items = m_Active[type];
            if (items.size() > 0)
            {
                foreach (SonoNerveGlyphs* item, items)
                {
                    if (item != nullptr)
                    {
                        item->show();
                    }
                }
            }
        }
    }
}

void SonoNerveGlyphsControl::showNote(bool isShow)
{
    if (isShow)
    {
        foreach (QGraphicsRectItem* item, m_NoteGraphs)
        {
            if (item != nullptr)
            {
                item->show();
            }
        }

        foreach (QGraphicsSimpleTextItem* item, m_NoteComment)
        {
            if (item != nullptr)
            {
                item->show();
            }
        }
    }
    else
    {
        foreach (QGraphicsRectItem* item, m_NoteGraphs)
        {
            if (item != nullptr && item->isVisible())
            {
                item->hide();
            }
        }

        foreach (QGraphicsSimpleTextItem* item, m_NoteComment)
        {
            if (item != nullptr && item->isVisible())
            {
                item->hide();
            }
        }
    }

    if (isShow)
    {
        updateNote(m_curPart);
    }
}

void SonoNerveGlyphsControl::addNoteItem()
{
    QGraphicsRectItem* item = overlay().createEternalItem<QGraphicsRectItem>();
    QGraphicsSimpleTextItem* itemText = overlay().createEternalItem<QGraphicsSimpleTextItem>();
    m_NoteGraphs.append(item);
    m_NoteComment.append(itemText);

    item->hide();
    itemText->hide();
}

void SonoNerveGlyphsControl::clearActiveGlyphs()
{
    foreach (QList<SonoNerveGlyphs*> items, m_Active)
    {
        foreach (SonoNerveGlyphs* item, items)
        {
            if (item != nullptr)
            {
                item->clearPath();
            }
        }
    }
}

int SonoNerveGlyphsControl::curPart() const
{
    return m_curPart;
}
