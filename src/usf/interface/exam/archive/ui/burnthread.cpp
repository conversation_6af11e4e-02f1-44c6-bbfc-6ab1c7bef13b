#include "burnthread.h"
#include <QDir>
#include "resource.h"
#include "util.h"
#include "backupmodel.h"
#include "messageboxframe.h"
#include "fileutil.h"
#include "burnutil.h"
#include "diskutil.h"

#ifdef Q_OS_LINUX
#include <linux/cdrom.h>
#endif

const int DISC_WAIT_MOUNT_SEC = 20;

BurnThread::BurnThread(QObject* parent)
    : FileOperationThread(parent)
    , m_BurnBackupModel(NULL)
    , m_DiscStatus(DiscInfo::UnkownState)
    , m_DiscType(DiscInfo::UnkownType)
    , m_IsDiskWriterBusy(false)
    , m_BurnSize(0)
    , m_ProgressBarPercent(0)
{
    m_BurnPath = Resource::discTmpDir + Resource::pathSeparator + Resource::dataStoreDir;

    connect(this, SIGNAL(showMsg(const QString&, int)), this, SLOT(onShowMsg(const QString&, int)),
            Qt::BlockingQueuedConnection);
    // BlockingQueuedConnection: 这种类型类似于QueuedConnection，
    // 但是它只能应用于跨线程操作即发送者和接收者处于不同的线程中的情况，并且信号发送者线程会阻塞等待接收者的槽函数执行结束
    connect(this, SIGNAL(finished()), this, SLOT(onTerminate()), Qt::BlockingQueuedConnection);

    connect(&m_progressBarTimer, SIGNAL(timeout()), this, SLOT(onUpdateProgressBar()));
    connect(this, SIGNAL(startProgressBarTimer(int)), this, SLOT(onStartProgressBarTimer(int)));
}

BurnThread::~BurnThread()
{
}

void BurnThread::onShowMsg(const QString& text, int msgType)
{
    if (msgType == Warning)
    {
        MessageBoxFrame::warning(text);
    }
    else
    {
        MessageBoxFrame::information(text);
    }
}

void BurnThread::onUpdateProgressBar()
{
    if (m_ProgressBarPercent < 100)
    {
        emit startBurning(m_ProgressBarPercent);
        m_ProgressBarPercent++;
    }
}

void BurnThread::onStartProgressBarTimer(int interval)
{
    m_progressBarTimer.start(interval);
}

void BurnThread::setBackupModel(BackupModel* backupModel)
{
    m_BurnBackupModel = backupModel;
}

void BurnThread::setBackupPatients(const QList<Patient*>& patients)
{
    m_Patients = patients;
}

void BurnThread::setBurnArgs(const QStringList& list)
{
    m_BurnList = list;
}

bool BurnThread::isDiskWriterBusy()
{
    return m_IsDiskWriterBusy;
}

void BurnThread::copyDirToDest()
{
    // copy local exam dir to tmp burn dir
    if (!QDir(m_BurnPath).exists())
    {
        // Util::Mkdir(m_BurnPath, true);
        FileUtil::makeDir(m_BurnPath, true);
    }
    else
    {
        // Util::Rmdir(m_BurnPath, true, true);
        FileUtil::removeDir(m_BurnPath, true, true);
    }

    QStringList burnList;
    for (int i = 0; i < m_BurnList.count(); i++)
    {
        QString burnDirPath = m_BurnList.at(i).left(m_BurnList.at(i).lastIndexOf("/"));
        if (!burnList.contains(burnDirPath))
        {
            burnList.append(burnDirPath);
        }
    }

    FileUtil::copy(burnList, m_BurnPath);

    // /disc/datastore/CommonData.s3db
    const QString dbFilePath = Resource::discDir + Resource::pathSeparator + Resource::dataStoreDir +
                               Resource::pathSeparator + Resource::getDbBaseFileName();
    // /disctmp/datastore/CommonData.s3db
    const QString destFilePath = m_BurnPath + Resource::pathSeparator + Resource::getDbBaseFileName();
    FileUtil::copy(dbFilePath, destFilePath);
    m_IsDiskWriterBusy = true;
    Util::sync();
    // update db file in tmp burn dir
    m_BurnBackupModel->saveDbfileToTmp(m_BurnPath, m_Patients);
    m_IsDiskWriterBusy = false;
}

int BurnThread::checkDiskStatus()
{
    DiscInfo::DiscMediaInfo discMediainfo = DiscInfo::getInstance().getDiscMediaInfo();

    m_DiscStatus = discMediainfo.discStatus;
    m_DiscType = discMediainfo.discType;

    qulonglong burnSize = DiskUtil::spaceSize(m_BurnList);
    m_BurnSize = burnSize;
    if (burnSize >= discMediainfo.discUnusedSize)
    {
        emit showMsg(tr("There is no enough disc space available for this operation!"), Warning);
        return -1;
    }

    qulonglong harddiskFreeSize = DiskUtil::diskFreeSize(Resource::hardDiskDir);
    if (m_DiscType == DiscInfo::CD)
    {
        if (harddiskFreeSize < (burnSize * 2))
        {
            emit showMsg(tr("There is no enough harddisk space available for this operation!"), Warning);
            return -1;
        }
    }
    else if (m_DiscType == DiscInfo::DVD || m_DiscType == DiscInfo::DVDDL)
    {
        if (harddiskFreeSize < burnSize)
        {
            emit showMsg(tr("There is no enough harddisk space available for this operation!"), Warning);
            return -1;
        }
    }
    else
    {
        emit showMsg(tr("Unable to identify disc format! Please try to reinsert the CD."), Warning);
        return -1;
    }

    return 0;
}

void BurnThread::burnFinishOk()
{
    m_progressBarTimer.stop();
    emit startBurning(100);
    //    emit finished();
    emit showMsg(tr("Burn successfully!"), Info);
}

void BurnThread::run()
{
    int discWriterStatus = -1;

    //判断是否存在磁盘,如果没有弹出光驱
    discWriterStatus = DiscInfo::getInstance().getDiscStatus();
    if ((discWriterStatus != Disk_CDS_DISC_OK) && (discWriterStatus != Disk_CDS_DRIVE_NOT_READY))
    {
        emit showMsg(tr("The disc is not ready! Please try to reinsert the CD."), Warning);

#ifdef SYS_WINDOWS
        DiskUtil::ejectDevice(Resource::discDevice);
#endif

        return;
    }

    for (int i = 0; i <= DISC_WAIT_MOUNT_SEC; i++)
    {
        if (DiscInfo::getInstance().IsDiscMounted())
        {
            break;
        }
        //        qDebug() << "\033[33m   check IsDiscMounted counter:" << i << "\033[0m";
        if (i == DISC_WAIT_MOUNT_SEC)
        {
            emit showMsg(tr("The disc have not been mounted!"), Warning);
#ifdef SYS_WINDOWS
            DiskUtil::ejectDevice(Resource::discDevice);
#endif
            DiscInfo::getInstance().setBurnStatus(false);

            return;
        }
        sleep(1);
    }

    DiscInfo::getInstance().setBurnStatus(true);
    emit startBurning(0);

    DiscInfo::getInstance().lockDisc(true);
    if (checkDiskStatus() != 0)
    {
#ifdef SYS_WINDOWS
        DiskUtil::ejectDevice(Resource::discDevice);
#endif
        DiscInfo::getInstance().setBurnStatus(false);

        return;
    }

    int updateProgressInterval = 1000;
    if (m_DiscType == DiscInfo::DVD || m_DiscType == DiscInfo::DVDDL)
    {
        updateProgressInterval = ((m_BurnSize / 1024) * 4) + 350; // ms
    }
    else
    {
        updateProgressInterval = ((m_BurnSize / 1024) * 6) + 450; // ms
    }
    //    qDebug() << "\033[33m m_BurnSize:" << m_BurnSize << "KB \033[0m";
    //    qDebug() << "\033[33m updateProgressInterval:" << updateProgressInterval << "ms \033[0m";
    m_ProgressBarPercent = 0;
    emit startProgressBarTimer(updateProgressInterval);

    DiscInfo::getInstance().lockDisc(true);
    //备份到本地目录
    copyDirToDest();
    //当使用cdrskin命令时，不允许设备被挂载，需要先解除挂载
    BurnUtil::umount(Resource::discDevice);
    int ret = -1;
    //判断是否是DVD,如果是dvd,使用growisofs刻录,如果是cd,则使用cdrecord刻录
    if (m_DiscType == DiscInfo::DVD || m_DiscType == DiscInfo::DVDDL)
    {
        //刻录到dvd
        if (m_DiscStatus == DiscInfo::Empty)
        {
            ret = firstBurnDvd();
            if (ret != 0)
            {
                endProgressBar();
                showBurnErrorMsg(ret);
            }
            else
            {
                // endProgressBar();
                burnFinishOk();
            }
        }
        else
        {
            ret = appendBurnDvd();
            if (ret != 0)
            {
                endProgressBar();
                showBurnErrorMsg(ret);
            }
            else
            {
                // endProgressBar();
                burnFinishOk();
            }
        }
    }
    else //刻录到cd
    {
        if (m_DiscStatus == DiscInfo::Empty)
        {
            if (firstMakeCdImage() == 0)
            {
                ret = firstBurnCd();
                if (ret != 0)
                {
                    endProgressBar();
                    showBurnErrorMsg(ret);
                }
                else
                {
                    // endProgressBar();
                    burnFinishOk();
                }
            }
            else
            {
                endProgressBar();
                emit showMsg(tr("Failed to make the image file! Please try to reinsert the CD."), Warning);
            }
        }
        else
        {
            if (appendMakeCdImage() == 0)
            {
                ret = appendBurnCd();
                if (ret != 0)
                {
                    endProgressBar();
                    showBurnErrorMsg(ret);
                }
                else
                {
                    // endProgressBar();
                    burnFinishOk();
                }
            }
            else
            {
                endProgressBar();
                emit showMsg(tr("Failed to make the image file! Please try to reinsert the CD."), Warning);
            }
        }
    }

    //删除备份目录
    Util::Rmdir(Resource::discTmpDir, false);
#ifdef SYS_WINDOWS
    DiskUtil::ejectDevice(Resource::discDevice);
#endif
    DiscInfo::getInstance().setBurnStatus(false);
}

void BurnThread::endProgressBar()
{
    if (m_progressBarTimer.isActive())
    {
        m_progressBarTimer.stop();
        emit startBurning(100);
    }
}

void BurnThread::onTerminate()
{
    endProgressBar();

    DiscInfo::getInstance().lockDisc(false);

    //    Util::System(QString("killall -9 growisofs"));
    //    Util::System(QString("killall -9 cdrecord"));
    //    Util::System(QString("killall -9 cp"));
    //    Util::System(QString("killall -9 mkisofs"));
    //    Util::System(QString("umount -fl %1").arg(Resource::discDir));

    BurnUtil::stopISO();
    DiskUtil::umount(Resource::discDir);
    BurnUtil::ejectDevice();

    if (QDir(Resource::discTmpDir).exists())
    {
        FileUtil::removeDir(Resource::discTmpDir);
        Util::sync();
    }
    DiscInfo::getInstance().setBurnStatus(false);
}

int BurnThread::firstBurnDvd()
{
    int ret;
    m_IsDiskWriterBusy = true;
    Util::sync();
    //   ret = Util::System("growisofs -speed=4 -Z /dev/sr0 -R -J " + Resource::discTmpDir + " && eject -s /dev/sr0");
    ret = BurnUtil::burnDVD(Resource::discTmpDir);
    m_IsDiskWriterBusy = false;
    return ret;
}

int BurnThread::firstMakeCdImage()
{
    int ret;
    //    ret = Util::System(QString("mkisofs -V CD -J -jcharset=utf8 -r -o %1/data.iso ").arg(Resource::discTmpDir) +
    //    Resource::discTmpDir);
    ret = BurnUtil::makeISO(Resource::discTmpDir);
    return ret;
}

int BurnThread::firstBurnCd()
{
    int ret;
    m_IsDiskWriterBusy = true;
    Util::sync();
    //    ret = Util::System(QString("cdrecord speed=16 -v dev=/dev/sr0 -eject -tao -multi %1/data.iso  && eject -s
    //    /dev/sr0").arg(Resource::discTmpDir));
    ret = BurnUtil::burnCD(Resource::discTmpDir);
    m_IsDiskWriterBusy = false;
    return ret;
}

int BurnThread::appendBurnDvd()
{
    int ret;
    m_IsDiskWriterBusy = true;
    Util::sync();
    ret = BurnUtil::burnAddtionalDVD(Resource::discTmpDir);
    m_IsDiskWriterBusy = false;
    return ret;
}

int BurnThread::sealBurnDvd()
{
    int ret;
    m_IsDiskWriterBusy = true;
    Util::sync();
    ret = BurnUtil::sealBurnDvd(Resource::discTmpDir);
    m_IsDiskWriterBusy = false;
    return ret;
}

int BurnThread::appendMakeCdImage()
{
    int ret;
    //    ret = Util::System(QString("mkisofs -v -V CD -J -jcharset=utf8 -r -o %1/data.iso -C `cdrecord dev=/dev/sr0
    //    -msinfo`  -M  /dev/sr0 ").arg(Resource::discTmpDir) + Resource::discTmpDir);
    ret = BurnUtil::makeAddtionalISO(Resource::discTmpDir);
    return ret;
}

int BurnThread::appendBurnCd()
{
    int ret;
    m_IsDiskWriterBusy = true;
    Util::sync();
    //    ret = Util::System(QString("cdrecord speed=16 -v dev=/dev/sr0 -eject -tao -multi
    //    %1/data.iso").arg(Resource::discTmpDir));
    ret = BurnUtil::burnAddtionalCD(Resource::discTmpDir);
    m_IsDiskWriterBusy = false;
    return ret;
}

void BurnThread::showBurnErrorMsg(int ret)
{
    if (m_DiscType == DiscInfo::DVD || m_DiscType == DiscInfo::DVDDL)
    {
        if (ret == 5)
        {
            emit showMsg(
                tr("Write error: Disc may be damaged or drive unstable!\nClean the disc/laser or try a new disc."),
                Warning);
            return;
        }
        else if (ret == 6)
        {
            emit showMsg(tr("Disc incompatible or corrupted!\nCheck disc status or use a new disc."), Warning);
            return;
        }
        else if (ret == 11)
        {
            emit showMsg(tr("Buffer underrun: Data read too slowly!\nRestart or close resource-heavy apps."), Warning);
            return;
        }
    }
    else
    {
        if (ret == 2)
        {
            emit showMsg(tr("Write-protected: Disc is read-only! Use rewritable media"), Warning);
            return;
        }
        else if (ret == 4)
        {
            emit showMsg(tr("Disc full: Data exceeds disc capacity! Use higher-capacity media."), Warning);
            return;
        }
        else if (ret == 5)
        {
            emit showMsg(
                tr("Write error: Disc may be damaged or drive unstable!\nClean the disc/laser or try a new disc."),
                Warning);
            return;
        }
    }

    emit showMsg(tr("Failed to burn the exams! Please try to reinsert the CD."), Warning);
    return;
}
