#ifndef BURNTHREAD_H
#define BURNTHREAD_H

#include "usfinterfaceexamarchiveui_global.h"
#include "fileoperationthread.h"
#include "discinfo.h"

#include <QTimer>

class BackupModel;
class Patient;

class USF_INTERFACE_EXAM_ARCHIVE_UI_EXPORT BurnThread : public FileOperationThread
{
    Q_OBJECT
public:
    enum MsgBoxType
    {
        Info,
        Warning
    };
    explicit BurnThread(QObject* parent = 0);
    ~BurnThread();
    void setBurnArgs(const QStringList& list);
    void setBackupModel(BackupModel* backupModel);
    void setBackupPatients(const QList<Patient*>& patients);
    bool isDiskWriterBusy();

protected:
    void run();

private:
    void endProgressBar();
    void burnFinishOk();
    int checkDiskStatus();
    void copyDirToDest(); //拷贝待备份文件到目标目录下
    int firstBurnDvd();   //首次刻录
    int firstMakeCdImage();
    int firstBurnCd();

    int appendBurnDvd(); //追加刻录
    int sealBurnDvd();   //封盘刻录
    int appendMakeCdImage();
    int appendBurnCd();
    void showBurnErrorMsg(int ret);

signals:
    void startProgressBarTimer(int interval);
    void startBurning(qint64 value);
    void showMsg(const QString& text, int msgType);
public slots:
    void onShowMsg(const QString& text, int msgType);
    void onTerminate();
    void onUpdateProgressBar();
    void onStartProgressBarTimer(int interval);

private:
    BackupModel* m_BurnBackupModel;
    QList<Patient*> m_Patients;
    QStringList m_BurnList;
    QString m_BurnPath;
    DiscInfo::DiscStateEnum m_DiscStatus;
    DiscInfo::DiscTypeEnum m_DiscType;
    bool m_IsDiskWriterBusy;
    QTimer m_progressBarTimer;
    unsigned long m_BurnSize;
    int m_ProgressBarPercent;
};

#endif // BURNTHREAD_H
