#include "imagelablecontainerwidget.h"
#include "applicationinfo.h"
#include "clickablelabel.h"
#include "iimagelabel.h"
#include "imagecinewidget.h"
#include "imagelabel.h"
#include "imagelabelinfo.h"
#include "ui_imagelablecontainerwidget.h"
#include "util.h"
#include <QDebug>
#include <QMetaEnum>
#include <QPaintEvent>
#include <QPainter>
#include <QStyle>
#include <QStyleOption>

ImageLableContainerWidget::ImageLableContainerWidget(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::ImageLableContainerWidget)
    , m_indexLabelLocation(right, bottom)
    , m_moveIconLabelLocation(right, bottom, QPoint(0, 30))
    , m_pbPlayBtnLocation(left, bottom)
    , m_selIconLabelLocation(right, top)
    , m_isDrawIndexLabel(true)
    , m_isDrawMoveIconLabe(false)
    , m_isDrawPBPlayBtn(false)
    , m_isDrawSelectIcon(false)
    , m_info(NULL)
    , m_imagelabel(NULL)
    , m_ThumbnailLabel(nullptr)
{
    ui->setupUi(this);
    m_indexLabel = new QLabel(this);
    m_indexLabel->setAlignment(Qt::AlignCenter);
    m_indexLabel->setObjectName("IndexLabel");
    m_indexLabel->hide();

    m_moveIconLabel = new QLabel(this);
    m_moveIconLabel->setAlignment(Qt::AlignCenter);
    m_moveIconLabel->setObjectName("MoveIconLabel");
    m_moveIconLabel->hide();

    m_pbPlayBtn = new ClickableLabel(this);
    m_pbPlayBtn->setObjectName("PBPlayButton");
    //    m_pbPlayBtn->hide();
    connect(m_pbPlayBtn, SIGNAL(clicked()), this, SLOT(onPlayButtonClicked()));

    m_selectedIconLabel = new QLabel(this);
    m_selectedIconLabel->setAlignment(Qt::AlignCenter);
    m_selectedIconLabel->setObjectName("SelectedIcon");
    m_selectedIconLabel->hide();
}

ImageLableContainerWidget::~ImageLableContainerWidget()
{
    onPlayButtonClicked(false);
    delete m_imagelabel;
    if (m_ThumbnailLabel != nullptr)
    {
        delete m_ThumbnailLabel;
    }
    delete ui;
}

void ImageLableContainerWidget::paintEvent(QPaintEvent* event)
{
    QWidget::paintEvent(event);
    QStyleOption opt;
    opt.init(this);
    QPainter p(this);
    style()->drawPrimitive(QStyle::PE_Widget, &opt, &p, this);
}

void ImageLableContainerWidget::updateStatus()
{
    QRect crect = contentsRect();

    m_indexLabel->setAttribute(Qt::WA_UnderMouse, underMouse());
    m_moveIconLabel->setAttribute(Qt::WA_UnderMouse, underMouse());
    m_pbPlayBtn->setAttribute(Qt::WA_UnderMouse, underMouse());
    m_selectedIconLabel->setAttribute(Qt::WA_UnderMouse, underMouse());

    m_indexLabel->setVisible(m_isDrawIndexLabel);
    if (m_isDrawIndexLabel)
    {
        m_indexLabel->raise();
        m_indexLabel->move(subControlPos(crect, m_indexLabelLocation, m_indexLabel));
    }

    m_moveIconLabel->setVisible(m_isDrawMoveIconLabe);
    if (m_isDrawMoveIconLabe)
    {
        m_moveIconLabel->raise();
        m_moveIconLabel->move(subControlPos(crect, m_moveIconLabelLocation, m_moveIconLabel));
    }

    m_pbPlayBtn->setVisible(m_isDrawPBPlayBtn /* || m_info->isCine()*/);
    if (m_isDrawPBPlayBtn /* || m_info->isCine()*/)
    {
        m_pbPlayBtn->raise();
        m_pbPlayBtn->move(subControlPos(crect, m_pbPlayBtnLocation, m_pbPlayBtn));
    }

    m_selectedIconLabel->setVisible(m_isDrawSelectIcon);
    if (m_isDrawSelectIcon)
    {
        m_selectedIconLabel->raise();
        m_selectedIconLabel->move(subControlPos(crect, m_selIconLabelLocation, m_selectedIconLabel));
    }
}

void ImageLableContainerWidget::onSelectChange()
{
    if (m_info == NULL)
    {
        return;
    }
    bool isSelected = m_info->isSelect();
    if (m_info->isMultiSelect())
    {
        isSelected = true;
    }

    setIsDrawSelectIcon(isSelected);
    Util::changeQssWidgetProperty(this, "isSelected", isSelected);
    Util::changeQssWidgetProperty(m_indexLabel, "isSelected", isSelected);
    Util::changeQssWidgetProperty(m_moveIconLabel, "isSelected", isSelected);
    Util::changeQssWidgetProperty(m_pbPlayBtn, "isSelected", isSelected);
    updateStatus();
}

void ImageLableContainerWidget::onPlaySatusChanged(bool status)
{
    Util::changeQssWidgetProperty(m_pbPlayBtn, "play", !status);
}

void ImageLableContainerWidget::onPlayButtonClicked(bool status)
{
    ImageCineWidget* cine = dynamic_cast<ImageCineWidget*>(m_imagelabel);
    if (cine != NULL)
    {
        //父窗口被关闭的情况下，不能启动播放
        QWidget* pParent = dynamic_cast<QWidget*>(parent());
        if (status && (pParent != nullptr && !pParent->isVisible()))
        {
            status = false;
        }

        if (status)
        {
            emit playIndexChange(m_info->getIndex());
        }
        emit playStatusChanges(!cine->playStatus() & status);
    }
}

void ImageLableContainerWidget::setLabel(QWidget* label)
{
    if (label == NULL)
    {
        return;
    }
    m_imagelabel = label;
    m_imagelabel->setParent(this);
    IImageLabel* img = dynamic_cast<IImageLabel*>(label);
    if (img == NULL)
    {
        return;
    }
    if (m_info != NULL)
    {
        disconnect(m_info, SIGNAL(selectChange()), this, SLOT(onSelectChange()));
        disconnect(m_info, SIGNAL(playStatusChanges(bool)), this, SLOT(onPlaySatusChanged(bool)));
        disconnect(this, SIGNAL(playStatusChanges(bool)), m_info, SIGNAL(playStatusChanges(bool)));
    }

    m_info = img->getImageInfo();
    if (m_info == NULL)
    {
        return;
    }
    connect(m_info, SIGNAL(selectChange()), this, SLOT(onSelectChange()));
    connect(m_info, SIGNAL(playStatusChanges(bool)), this, SLOT(onPlaySatusChanged(bool)));
    connect(this, SIGNAL(playStatusChanges(bool)), m_info, SIGNAL(playStatusChanges(bool)));
    m_indexLabel->setText(QString::number(m_info->getIndex()));
    this->setFixedSize(m_info->getLabelSize());
    if (m_info->isCine())
    {
        setIsDrawMoveIconLabe(true);
        if (m_info->easyViewCallBack())
        {
            setIsDrawPBPlayBtn(true);
        }
    }
    onSelectChange();
    ImageCineWidget* cine = dynamic_cast<ImageCineWidget*>(m_imagelabel);
    if (cine != nullptr)
    {
        connect(cine, SIGNAL(validPlayButtonClick()), this, SLOT(onPlayButtonClicked()));
    }
    m_imagelabel->resize(contentsRect().size() + QSize(0, 0));
    m_imagelabel->move(contentsRect().topLeft());
    m_imagelabel->show();
    m_imagelabel->setObjectName("imageLabel");
    updateStatus();
}

QString ImageLableContainerWidget::getIndexLabelLocation()
{
    return QString();
}

void ImageLableContainerWidget::setIndexLabelLocation(const QString& indexLabelLocation)
{
    m_indexLabelLocation.parsing(indexLabelLocation);
}

QString ImageLableContainerWidget::getMoveIconLabelLocation()
{
    return QString();
}

void ImageLableContainerWidget::setMoveIconLabelLocation(const QString& moveIconLabelLocation)
{
    m_moveIconLabelLocation.parsing(moveIconLabelLocation);
}

QString ImageLableContainerWidget::getPbPlayBtnLocation()
{
    return QString();
}

void ImageLableContainerWidget::setPbPlayBtnLocation(const QString& pbPlayBtnLocation)
{
    m_pbPlayBtnLocation.parsing(pbPlayBtnLocation);
}

QRect ImageLableContainerWidget::contentsRect() const
{
    QStyleOptionFrame opt;
    opt.init(this);

    return style()->subElementRect(QStyle::SE_FrameContents, &opt, this);
}

QPoint ImageLableContainerWidget::subControlPos(const QRect& prect,
                                                const ImageLableContainerWidget::LabelLocationType& sublocation,
                                                const QWidget* subwidget)
{
    QPoint pos = prect.topLeft();
    QRect surect = subwidget->rect();
    if (sublocation.vLocation == left)
    {
        switch (sublocation.hLocation)
        {
        case top:
            break;
        case center:
            pos = QPoint(prect.left(), prect.center().ry()) - QPoint(0, surect.bottom() / 2);
            break;
        case bottom:
            pos = prect.bottomLeft() - QPoint(0, surect.bottom());
        default:
            break;
        }
    }
    else if (sublocation.vLocation == center)
    {
        switch (sublocation.hLocation)
        {
        case top:
            pos = QPoint(prect.center().rx() - surect.bottom() / 2, prect.top());
            break;
        case center:
            pos = prect.center() - QPoint(surect.width() / 2, surect.height() / 2);
            break;
        case bottom:
            pos = QPoint(prect.center().rx() - surect.width() / 2, prect.bottom() - surect.height());
        default:
            break;
        }
    }
    else if (sublocation.vLocation == right)
    {
        switch (sublocation.hLocation)
        {
        case top:
            pos = prect.topRight() - QPoint(surect.right(), 0);
            break;
        case center:
            pos = QPoint(prect.right() - surect.right(), prect.center().ry() - surect.bottom() / 2);
            break;
        case bottom:
            pos = prect.bottomRight() - surect.bottomRight();
        default:
            break;
        }
    }

    pos += sublocation.offset;

    return pos;
}

bool ImageLableContainerWidget::isDrawSelectIcon() const
{
    return m_isDrawSelectIcon;
}

void ImageLableContainerWidget::setIsDrawSelectIcon(bool isDrawSelectIcon)
{
    m_isDrawSelectIcon = isDrawSelectIcon;
}

void ImageLableContainerWidget::updateIndex(int index)
{
    if (m_indexLabel != nullptr)
    {
        m_info->setIndex(index);
        m_indexLabel->setText(QString::number(index));
    }
}

bool ImageLableContainerWidget::event(QEvent* e)
{
    if (!isEnabled())
    {
        return QWidget::event(e);
    }

    // 当存在缩略图悬浮框相关配置时，需要进行缩略图悬浮框显示
    if (property("thubnailSize").isValid() && property("thubnailPos").isValid())
    {
        if (e->type() == QEvent::Enter || e->type() == QEvent::HoverEnter)
        {
            if (m_ThumbnailLabel == nullptr)
            {
                m_ThumbnailLabel = new QLabel(this);
                m_ThumbnailLabel->setObjectName("ThumbnailLabel");
                m_ThumbnailLabel->setWindowFlags(Qt::FramelessWindowHint | Qt::Tool | Qt::WindowStaysOnTopHint);
                QPixmap pixmap =
                    QPixmap(m_info->imagePath())
                        .scaled(property("thubnailSize").toSize(), Qt::KeepAspectRatio, Qt::SmoothTransformation);
                m_ThumbnailLabel->setFixedSize(pixmap.size());
                m_ThumbnailLabel->setPixmap(pixmap);
                m_ThumbnailLabel->setScaledContents(true);
                m_ThumbnailLabel->move(property("thubnailPos").toPoint());
                m_ThumbnailLabel->show();
            }
            else if (m_ThumbnailLabel->isHidden())
            {
                m_ThumbnailLabel->show();
            }
        }
        else if (e->type() == QEvent::Leave || e->type() == QEvent::HoverLeave)
        {
            if (m_ThumbnailLabel != nullptr && !m_ThumbnailLabel->isHidden())
            {
                m_ThumbnailLabel->hide();
            }
        }
    }

    switch (e->type())
    {
    case QEvent::HoverEnter:
    case QEvent::HoverMove:
    {
        if (!ApplicationInfo::instance().isCursorVisible())
        {
            return true;
        }
    }
    break;
    default:
        break;
    }

    return QWidget::event(e);
}

void ImageLableContainerWidget::setSelIconLabelLocation(const QString& selIconLabelLocation)
{
    m_selIconLabelLocation.parsing(selIconLabelLocation);
}

bool ImageLableContainerWidget::isDrawPBPlayBtn() const
{
    return m_isDrawPBPlayBtn;
}

void ImageLableContainerWidget::setIsDrawPBPlayBtn(bool isDrawPBPlayBtn)
{
    m_isDrawPBPlayBtn = isDrawPBPlayBtn;
}

QString ImageLableContainerWidget::getSelIconLabelLocation()
{
    return QString();
}

void ImageLableContainerWidget::enterEvent(QEvent* e)
{
    QWidget::enterEvent(e);
    if (m_info != NULL && ApplicationInfo::instance().isCursorVisible())
    {
        emit updatePrewindow(m_info->imagePath());
    }
}

bool ImageLableContainerWidget::isDrawMoveIconLabe() const
{
    return m_isDrawMoveIconLabe;
}

void ImageLableContainerWidget::setIsDrawMoveIconLabe(bool isDrawMoveIconLabe)
{
    m_isDrawMoveIconLabe = isDrawMoveIconLabe;
}

bool ImageLableContainerWidget::isDrawIndexLabel() const
{
    return m_isDrawIndexLabel;
}

void ImageLableContainerWidget::setIsDrawIndexLabel(bool isDrawIndexLabel)
{
    m_isDrawIndexLabel = isDrawIndexLabel;
}

ImageLableContainerWidget::LabelLocationType::LabelLocationType(const ImageLableContainerWidget::EnLocation vl,
                                                                const ImageLableContainerWidget::EnLocation hl,
                                                                const QPoint offset)
{
    vLocation = vl;
    hLocation = hl;
    this->offset = offset;
}

void ImageLableContainerWidget::LabelLocationType::parsing(const QString& location)
{
    QStringList msgs = location.split(" ", Qt::SkipEmptyParts);
    QMetaEnum enlocate = QMetaEnum::fromType<EnLocation>();

    int count = msgs.size();
    int entimer = 0, offsettimer = 0;
    for (int i = 0; i < count; i++)
    {
        if (i >= 4)
        {
            break;
        }
        bool isEnum = false;
        int el = enlocate.keyToValue(msgs.at(i).toStdString().c_str(), &isEnum);
        if (isEnum)
        {
            if (entimer > 1)
            {
                continue;
            }
            if (entimer == 0)
            {
                vLocation = (EnLocation)el;
                hLocation = (EnLocation)el;
            }
            else
            {
                hLocation = (EnLocation)el;
            }
            entimer++;
        }
        else
        {
            if (offsettimer > 1)
            {
                break;
            }
            int v = msgs.at(i).toInt();
            if (offsettimer == 0)
            {
                offset.setX(v);
                offset.setY(v);
            }
            else
            {
                offset.setY(v);
            }
            offsettimer++;
        }
    }
}
