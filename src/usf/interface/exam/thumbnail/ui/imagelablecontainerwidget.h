#ifndef IMAGELABLECONTAINERWIDGET_H
#define IMAGELABLECONTAINERWIDGET_H

#include "usfinterfaceexamthumbnailui_global.h"
#include <QWidget>
#include <QLabel>
#include <QPushButton>

class ImageLabel;
class IimageInfo;
class ClickableLabel;
namespace Ui
{
class ImageLableContainerWidget;
}

class USF_INTERFACE_EXAM_THUMBNAIL_UI_EXPORT ImageLableContainerWidget : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(QString IndexLocation READ getIndexLabelLocation WRITE setIndexLabelLocation)
    Q_PROPERTY(QString MoveLabelLocation READ getMoveIconLabelLocation WRITE setMoveIconLabelLocation)
    Q_PROPERTY(QString PBPlayBtnLocation READ getPbPlayBtnLocation WRITE setPbPlayBtnLocation)
    Q_PROPERTY(QString SelectLabelLocation READ getSelIconLabelLocation WRITE setSelIconLabelLocation)
public:
    enum EnLocation
    {
        top,
        bottom,
        left,
        right,
        center
    };
    Q_ENUM(EnLocation)

    explicit ImageLableContainerWidget(QWidget* parent = nullptr);
    ~ImageLableContainerWidget();

    void setLabel(QWidget* label);

    QString getIndexLabelLocation();
    void setIndexLabelLocation(const QString& indexLabelLocation);

    QString getMoveIconLabelLocation();
    void setMoveIconLabelLocation(const QString& moveIconLabelLocation);

    QString getPbPlayBtnLocation();
    void setPbPlayBtnLocation(const QString& pbPlayBtnLocation);
    QRect contentsRect() const;

    bool isDrawIndexLabel() const;
    void setIsDrawIndexLabel(bool isDrawIndexLabel);

    bool isDrawMoveIconLabe() const;
    void setIsDrawMoveIconLabe(bool isDrawMoveIconLabe);

    bool isDrawPBPlayBtn() const;
    void setIsDrawPBPlayBtn(bool isDrawPBPlayBtn);

    QString getSelIconLabelLocation();
    void setSelIconLabelLocation(const QString& selIconLabelLocation);

    bool isDrawSelectIcon() const;
    void setIsDrawSelectIcon(bool isDrawSelectIcon);

    IimageInfo* imageInfo()
    {
        return m_info;
    }

    QWidget* label()
    {
        return m_imagelabel;
    }

    void updateIndex(int index);

public slots:
    void onPlayButtonClicked(bool status = true);

protected:
    bool event(QEvent* e);
    void enterEvent(QEvent* e);
    void paintEvent(QPaintEvent* event);

    /** 2025-06-12 Write by AlexWang
     * @brief updateStatus
     */
    void updateStatus();

signals:
    void updatePrewindow(const QString& image);
    void playStatusChanges(bool status);
    void playIndexChange(int playIndex);

private slots:
    void onSelectChange();
    void onPlaySatusChanged(bool status);

private:
    struct LabelLocationType
    {
        EnLocation vLocation;
        EnLocation hLocation;
        QPoint offset;
        LabelLocationType(const EnLocation vl = left, const EnLocation hl = top, const QPoint offset = QPoint(0, 0));
        void parsing(const QString& location);
    };

    QPoint subControlPos(const QRect& prect, const LabelLocationType& sublocation, const QWidget* subwidget);

private:
    Ui::ImageLableContainerWidget* ui;
    QLabel* m_indexLabel;
    QLabel* m_moveIconLabel;
    QLabel* m_selectedIconLabel;
    ClickableLabel* m_pbPlayBtn;
    LabelLocationType m_indexLabelLocation;
    LabelLocationType m_moveIconLabelLocation;
    LabelLocationType m_pbPlayBtnLocation;
    LabelLocationType m_selIconLabelLocation;
    bool m_isDrawIndexLabel;
    bool m_isDrawMoveIconLabe;
    bool m_isDrawPBPlayBtn;
    bool m_isDrawSelectIcon;
    IimageInfo* m_info;
    QWidget* m_imagelabel;
    QLabel* m_ThumbnailLabel;
};

#endif // IMAGELABLECONTAINERWIDGET_H
