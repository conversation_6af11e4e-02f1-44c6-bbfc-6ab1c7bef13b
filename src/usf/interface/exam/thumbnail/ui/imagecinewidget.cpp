#include "imagecinewidget.h"
#include "clickablelabel.h"
#include "glimagewidget.h"
#include "ibeamformer.h"
#include "imagelabelinfo.h"
#include "modeluiconfig.h"
#include "resource.h"
#include "setting.h"
#include "stressechobuffermanager.h"
#include "ui_imagecinewidget.h"
#include "util.h"
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <QLabel>
#include <QMutexLocker>
#include <QPainter>
#include <QPushButton>
#include <QSize>
#include <QStyleOption>
#include <QtEvents>

const int ImageCineWidget::roundedRectX = 22;
const int ImageCineWidget::roundedRectY = 18;
// easy view中电影播放按钮的响应范围
const int PB_WIDTH = 80;
const int PB_HEIGHT = 60;

ImageCineWidget::ImageCineWidget(StressEchoBufferManager* bufferManager, IBeamFormer* beamformer,
                                 IColorMapManager* colorMapManager, IimageInfo* info, QWidget* parent)
    : BaseBkWidget(parent)
    , ui(new Ui::ImageCineWidget)
    , m_BufferManager(bufferManager)
    , m_Beamformer(beamformer)
    , m_ImageWidget(NULL)
    , m_Index(0)
    , m_Info(NULL)
    , m_CallBackLabel(NULL)
    , m_PBPlayStatus(false)
    , m_ColorMapManager(colorMapManager)
{
    ui->setupUi(this);
    m_PicLabel = new QLabel(this);
    m_PicLabel->setObjectName("cineImageLabel");
    m_PicLabel->setAlignment(Qt::AlignCenter);
    m_Info = info;
    connect(m_Info, SIGNAL(selectChange()), this, SLOT(onSelectChanged()));
    connect(m_Info, SIGNAL(playStatusChanges(bool)), this, SLOT(onPlayStatusChanges(bool)));
    this->setContentsMargins(0, 0, 0, 0);
}

ImageCineWidget::~ImageCineWidget()
{
    delete ui;
}

void ImageCineWidget::setIndex(const int index)
{
    m_Index = index;
}

void ImageCineWidget::setImageInfo(IimageInfo* info)
{
    QMutexLocker locker(&m_SyncMutex);
    if (m_ImageWidget == NULL)
    {
        return;
    }
    setImageInfoInLock(info);
}

void ImageCineWidget::setImageInfoInLock(IimageInfo* info)
{
    if (m_Info != NULL)
    {
        disconnect(m_Info, SIGNAL(selectChange()), this, SLOT(onSelectChanged()));
        disconnect(m_Info, SIGNAL(playStatusChanges(bool)), this, SLOT(onPlayStatusChanges(bool)));
    }

    m_Info = info;
    QPixmap pix = m_Info->pixmap();
    m_PicLabel->setPixmap(pix);
    QString imagePath = info->imagePath();
    if (!imagePath.isEmpty())
    {
        QFileInfo fi(imagePath);
        QFileInfo destInfo(fi.dir(), fi.baseName());
        //        TODO:参数，ColorMap全部准备好了，才能绘制，否则出现异常
        m_ImageWidget->loadFile(destInfo.exists() ? destInfo.filePath() : QString());
        m_ImageWidget->preparePlay();
        m_ImageWidget->start();
    }
    else
    {
        m_ImageWidget->loadFile(QString());
    }
    if (m_Info->isFourDFile() && m_Info->easyViewCallBack() && m_CallBackLabel == NULL)
    {
        m_CallBackLabel = new HighLightLabel(this);
        connect(m_CallBackLabel, SIGNAL(clicked()), this, SLOT(onCallBackLabelClicked()));
    }
    connect(m_Info, SIGNAL(selectChange()), this, SLOT(onSelectChanged()));
    connect(m_Info, SIGNAL(playStatusChanges(bool)), this, SLOT(onPlayStatusChanges(bool)));
    onSelectChanged();
}

void ImageCineWidget::updateStatus()
{
    if (m_PBPlayStatus)
    {
        if (m_ImageWidget != NULL)
        {
            m_ImageWidget->move(contentsRect().topLeft());
            m_PicLabel->lower();
            m_PicLabel->hide();
        }
    }
    else
    {
        m_PicLabel->setPixmap(m_Info->pixmap());
        m_PicLabel->setFixedSize(contentsRect().size());
        m_PicLabel->move(contentsRect().topLeft());
        m_PicLabel->raise();
        m_PicLabel->show();
    }
}

IimageInfo* ImageCineWidget::getImageInfo() const
{
    return m_Info;
}

void ImageCineWidget::setIsSelect(bool select)
{
    if (m_Info != NULL)
    {
        m_Info->setIsSelected(select);
    }
}

bool ImageCineWidget::isSelect() const
{
    if (m_Info != NULL)
    {
        return m_Info->isSelect();
    }
    return false;
}

void ImageCineWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
    m_Info->calcImageSize(contentsRect().width(), contentsRect().height());
    QMutexLocker locker(&m_SyncMutex);
    if (m_ImageWidget != NULL)
    {
        m_ImageWidget->setRenderArea(contentsRect());
    }
}

bool ImageCineWidget::playStatus()
{
    return m_PBPlayStatus;
}

void ImageCineWidget::showEvent(QShowEvent* event)
{
    updateStatus();
    BaseBkWidget::showEvent(event);
}

void ImageCineWidget::mousePressEvent(QMouseEvent* e)
{
    if (mapToParent(e->pos()).x() <= PB_WIDTH && (this->height() - mapToParent(e->pos()).y()) <= PB_HEIGHT)
    {
        emit validPlayButtonClick();
    }
    else if (m_Info != NULL)
    {
        m_Info->setMousePressButton(e->button());
        if (m_Info->isSelect())
        {
            emit doubleClicked(m_Info);
        }
        setIsSelect(!m_Info->isSelect());
        emit clicked(m_Info);
    }
}

void ImageCineWidget::calIconRoundSize()
{
    if (m_Info != NULL)
    {
        if (m_Info->getLabelSize().height() >= 130)
        {
            m_RoundX = roundedRectX * 4 / 3;
            m_RoundY = roundedRectY * 7 / 5;
        }
        else
        {
            m_RoundX = roundedRectX;
            m_RoundY = roundedRectY;
        }
    }
}

void ImageCineWidget::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event);

    // Qwidget subClass shoule provide to support background
    QStyleOption opt;
    opt.init(this);
    QPainter p(this);
    style()->drawPrimitive(QStyle::PE_Widget, &opt, &p, this);
    //    if (m_PBPlayStatus)
    //    {
    //        QMutexLocker locker(&m_SyncMutex);
    //        if (m_ImageWidget != NULL)
    //        {
    //            m_ImageWidget->move(contentsRect().topLeft());
    //            m_PicLabel->lower();
    //            m_PicLabel->hide();
    //            return;
    //        }
    //    }
    //    m_PicLabel->setPixmap(m_Info->pixmap());
    //    m_PicLabel->setFixedSize(contentsRect().size());
    //    m_PicLabel->move(contentsRect().topLeft());
    //    m_PicLabel->raise();
    //    m_PicLabel->show();
}

void ImageCineWidget::onSelectChanged()
{
    m_PicLabel->move(contentsRect().topLeft());
}

void ImageCineWidget::onCallBackLabelClicked()
{
    // bmp
    m_Info->setMousePressButton(Qt::LeftButton);
    emit fourdCallBack(m_Info->imagePath());
}

void ImageCineWidget::onPlayStatusChanges(bool status)
{
    QMutexLocker locker(&m_SyncMutex);
    if (status && m_ImageWidget == NULL)
    {
        m_ImageWidget = new GLImageWidget(m_BufferManager, m_Beamformer, m_ColorMapManager, QString(), this);
        ui->gridLayout->addWidget(m_ImageWidget);
        m_ImageWidget->setCssmMode(true);
        m_ImageWidget->setIndex(0);
        setImageInfoInLock(m_Info);
    }
    if (!status && m_ImageWidget != NULL)
    {
        m_ImageWidget->disconnect();
        m_ImageWidget->stop();
        ui->gridLayout->removeWidget(m_ImageWidget);
        m_ImageWidget->hide();
        delete m_ImageWidget;
        m_ImageWidget = NULL;
        if (m_BufferManager != NULL)
        {
            m_BufferManager->endLoad(0);
        }
    }
    m_PBPlayStatus = status;

    updateStatus();
}
