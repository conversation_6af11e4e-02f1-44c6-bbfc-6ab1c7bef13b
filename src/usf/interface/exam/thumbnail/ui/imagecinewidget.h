#ifndef IMAGECINEWIDGET_H
#define IMAGECINEWIDGET_H

#include "iimagelabel.h"
#include <QGraphicsLineItem>
#include <QMutex>
#include <basebkwidget.h>

namespace Ui
{
class ImageCineWidget;
}

class StressEchoBufferManager;
class IBeamFormer;
class IimageInfo;
class HighLightLabel;
class GLImageWidget;
class QPushButton;
class QLabel;
class IColorMapManager;
class ImageCineWidget : public BaseBkWidget, public IImageLabel
{
    Q_OBJECT
public:
    explicit ImageCineWidget(StressEchoBufferManager* bufferManager, IBeamFormer* beamformer,
                             IColorMapManager* colorMapManager, IimageInfo* info = NULL, QWidget* parent = 0);
    ~ImageCineWidget();

    void setIndex(const int index);
    /**
     * @brief setImageInfo 向label中添加图片
     *
     * @param info
     */
    void setImageInfo(IimageInfo* info);

    IimageInfo* getImageInfo() const;
    /**
     * @brief setIsSelect 设置label中的图片是否被选定
     *
     * @param select
     */
    void setIsSelect(bool select);
    /**
     * @brief isSelect 图片的选中状态
     * @return
     */
    bool isSelect() const;

    virtual void resizeEvent(QResizeEvent* event);

    bool playStatus();

    /** 2025-06-19 Write by AlexWang
     * @brief showEvent
     * @param event
     */
    virtual void showEvent(QShowEvent* event);

protected:
    void mousePressEvent(QMouseEvent* e);
    void calIconRoundSize();
    void paintEvent(QPaintEvent* event);

protected slots:
    void onSelectChanged();
    void onCallBackLabelClicked();
    void onPlayStatusChanges(bool status);

private:
    void setImageInfoInLock(IimageInfo* info);

    /** 2025-06-19 Write by AlexWang
     * @brief updateStatus
     */
    void updateStatus();

signals:
    void fourdCallBack(const QString& filepath);
    void doubleClicked(const IimageInfo* info);
    void clicked(IimageInfo* info);
    void rightClicked(IimageInfo*);
    void validPlayButtonClick();

private:
    Ui::ImageCineWidget* ui;
    static const int roundedRectX;
    static const int roundedRectY;
    StressEchoBufferManager* m_BufferManager;
    IBeamFormer* m_Beamformer;
    GLImageWidget* m_ImageWidget;
    int m_Index;
    IimageInfo* m_Info;
    HighLightLabel* m_CallBackLabel;
    int m_RoundX;
    int m_RoundY;
    bool m_PBPlayStatus;
    QLabel* m_PicLabel;
    QMutex m_SyncMutex;
    IColorMapManager* m_ColorMapManager;
};

#endif // IMAGECINEWIDGET_H
