#include "entitykey.h"
#include <QSettings>

EntityKey::EntityKey()
    : m_IsStdKey(true)
    , m_IsEntityFromGroupOther(false)
{
}
const QString& EntityKey::name() const
{
    return m_Name;
}

void EntityKey::setName(const QString& Name)
{
    m_Name = Name;
}
const QString& EntityKey::caption() const
{
    if (m_Caption.isEmpty())
    {
        return m_Name;
    }
    else
    {
        return m_Caption;
    }
}

void EntityKey::setCaption(const QString& Caption)
{
    m_Caption = Caption;
}
bool EntityKey::isStdKey() const
{
    return m_IsStdKey;
}

void EntityKey::setIsStdKey(bool IsStdKey)
{
    m_IsStdKey = IsStdKey;
}
const QString& EntityKey::light() const
{
    return m_Light;
}

void EntityKey::setLight(const QString& Light)
{
    m_Light = Light;
}

void EntityKey::save(QSettings& settings) const
{
    settings.setValue("Name", m_Name);
    settings.setValue("Caption", m_Caption);
    if (!m_IsStdKey)
    {
        settings.setValue("IsStdKey", m_IsStdKey);
    }

    if (!m_Light.isEmpty())
    {
        settings.setValue("Light", m_Light);
    }
}

void EntityKey::load(QSettings& settings)
{
    m_Name = settings.value("Name").toString();
    m_Caption = settings.value("Caption", m_Name).toString();
    m_IsStdKey = settings.value("IsStdKey", true).toBool();
    m_Light = settings.value("Light").toString();
}

bool EntityKey::IsEntityFromGroupOther() const
{
    return m_IsEntityFromGroupOther;
}

void EntityKey::setIsEntityFromGroupOther(bool IsEntityFromGroupOther)
{
    m_IsEntityFromGroupOther = IsEntityFromGroupOther;
}
