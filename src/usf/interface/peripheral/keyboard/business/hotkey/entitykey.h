#ifndef ENTITYKEY_H
#define ENTITYKEY_H
#include "keyboard_global.h"

#include <QString>
class QSettings;

/**
 * @brief The EntityKey class 描述用于hotkey的实体键
 */
class USF_INTERFACE_PER_KEYBOARDSHARED_EXPORT EntityKey
{
public:
    EntityKey();
    const QString& name() const;
    void setName(const QString& Name);

    const QString& caption() const;
    void setCaption(const QString& Caption);

    bool isStdKey() const;
    void setIsStdKey(bool IsStdKey);

    const QString& light() const;
    void setLight(const QString& Light);
    void save(QSettings& settings) const;
    void load(QSettings& settings);

    bool IsEntityFromGroupOther() const;
    void setIsEntityFromGroupOther(bool IsEntityFromGroupOther);

private:
    QString m_Name;
    QString m_Caption;
    /**
     * @brief m_IsStdKey ASCII 键
     */
    bool m_IsStdKey;
    QString m_Light;
    bool m_IsEntityFromGroupOther;
};

#endif // ENTITYKEY_H
