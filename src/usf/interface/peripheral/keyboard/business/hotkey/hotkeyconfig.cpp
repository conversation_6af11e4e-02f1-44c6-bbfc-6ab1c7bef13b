#include "hotkeyconfig.h"
#include "resource.h"
#include <QSettings>
#include <QFileInfo>
#include "hotkeycontainer.h"
#include "appsetting.h"
#include "entitykey.h"

HotKeyConfig::HotKeyConfig(HotKeyContainer* container)
    : m_Contaner(container)
    , m_EntityKeys(container->entityKeys())
    , m_AvaliableEntityKeys(container->avaliableEntityKeys())
{
}

const QStringList& HotKeyConfig::entityKeys() const
{
    return m_EntityKeys;
}

const QStringList& HotKeyConfig::avaliableEntityKeys() const
{
    return m_AvaliableEntityKeys;
}

QString HotKeyConfig::function(const QString& entityKey) const
{
    return m_Config.value(entityKey, QString());
}

void HotKeyConfig::bindFunction(const QString& entityKey, const QString& function)
{
    m_Config[entityKey] = function;
}

void HotKeyConfig::load()
{
    QFileInfo fileinfo(Resource::hotKeyConfigFileName);

    if (!fileinfo.exists() || fileinfo.size() == 0)
    {
        loadDefault();
    }
    else
    {
        QSettings settings(Resource::hotKeyConfigFileName, QSettings::IniFormat);
        load(settings);
    }

    m_BackConfig = m_Config;
}

void HotKeyConfig::save()
{
    if (m_BackConfig != m_Config)
    {
        QSettings settings(Resource::hotKeyConfigFileName, QSettings::IniFormat);
        foreach (const QString& entityKey, m_EntityKeys)
        {
            settings.setValue(entityKey, m_Config.value(entityKey));
        }
        m_BackConfig = m_Config;
    }
}

void HotKeyConfig::loadDefault()
{
    QSettings settings(Resource::hotKeyFileName, QSettings::IniFormat);
    settings.beginGroup("DefaultConfig");

    load(settings);

    settings.endGroup();

    settings.beginGroup("DefaultConfig_Other");
    loadOther(settings);
    settings.endGroup();
}

void HotKeyConfig::saveDefault()
{
    QSettings settings(Resource::hotKeyFileName, QSettings::IniFormat);
    settings.beginGroup("DefaultConfig");

    foreach (const QString& entityKey, m_EntityKeys)
    {
        settings.setValue(entityKey, "None");
    }

    settings.endGroup();
}

int HotKeyConfig::count()
{
    return m_Config.count();
}

void HotKeyConfig::notifyHotKeyChanged()
{
    emit hotkeyChanged();
}

void HotKeyConfig::load(QSettings& settings)
{
    foreach (const QString& entityKey, m_EntityKeys)
    {
        QString func = settings.value(entityKey, m_Config[entityKey]).toString();
        m_Config[entityKey] = (m_Contaner->hotKeyFunction(func) != NULL) ? func : "None";
    }
}

void HotKeyConfig::loadOther(QSettings& settings)
{
    int size = settings.beginReadArray(AppSetting::model());
    for (int i = 0; i < size; ++i)
    {
        settings.setArrayIndex(i);
        QString key = settings.value("Key").toString();
        QString value = settings.value("Value", "None").toString();
        if (m_Contaner->entityKey(key) != nullptr)
        {
            m_Contaner->entityKey(key)->setIsEntityFromGroupOther(true);
        }

        if (m_Config.contains(key))
        {
            m_Config[key] = (m_Contaner->hotKeyFunction(value) != NULL) ? value : "None";
        }
        else
        {
            m_Config.insert(key, (m_Contaner->hotKeyFunction(value) != NULL) ? value : "None");
        }
    }
    settings.endArray();
}
