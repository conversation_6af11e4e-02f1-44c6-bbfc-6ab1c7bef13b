#ifndef HOTKEYCONFIG_H
#define HOTKEYCONFIG_H
#include "keyboard_global.h"

#include <QObject>
#include <QStringList>
#include <QHash>

class QObject;
class QSettings;
class HotKeyContainer;

/**
 * @brief The HotKeyConfig class 实体键和热键对应的功能的配置管理
 */
class USF_INTERFACE_PER_KEYBOARDSHARED_EXPORT HotKeyConfig : public QObject
{
    Q_OBJECT

public:
    HotKeyConfig(HotKeyContainer* container);
    const QStringList& entityKeys() const;
    const QStringList& avaliableEntityKeys() const;
    QString function(const QString& entityKey) const;
    void bindFunction(const QString& entityKey, const QString& function);
    void load();
    void save();
    void loadDefault();
    void saveDefault();
    int count();
    void notifyHotKeyChanged();

signals:
    void hotkeyChanged();

private:
    void load(QSettings& settings);
    void loadOther(QSettings& settings);

private:
    HotKeyContainer* m_Contaner;
    QStringList m_EntityKeys;
    QStringList m_AvaliableEntityKeys;
    QHash<QString, QString> m_Config;
    QHash<QString, QString> m_BackConfig;
};

#endif // HOTKEYCONFIG_H
