#ifndef IHardwareMonitorManager_H
#define IHardwareMonitorManager_H

#include "hardwaremonitor_global.h"
#include <QObject>
#include <QWidget>

class ISonoParameters;
class USF_INTERFACE_PER_HARDWAREMONITORSHARED_EXPORT IHardwareMonitorManager : public QObject
{
    Q_OBJECT
public:
    explicit IHardwareMonitorManager(QObject* parent = nullptr);
    virtual ~IHardwareMonitorManager();

    /**
     * @brief 启动监控
     */
    virtual void start() = 0;

    /**
     * @brief 停止监控
     */
    virtual void stop() = 0;

    virtual void setSonoParamters(ISonoParameters* sonoParamter) = 0;

    /**
     * @brief volatile2CTNumber 将电压值转成下发控制参数值
     * @return
     */

    virtual int volatilevlue2CTNumber(float value) = 0;

    /**
     * @brief getCWVolatile 判断当前CW电压值是否有效
     * @return
     */
    virtual bool checkCWVolatile(float safeVoltage, int times) = 0;

public slots:
    virtual void onNewHWInfo(unsigned char*, int) = 0;

protected slots:
    virtual void onECGInfoChanged(unsigned char data) = 0;
};

#endif // IHardwareMonitorManager_H
