#include "discinfo.h"
#include "util.h"
#include "resource.h"
#include <QRunnable>
#include <QThreadPool>
#include <QDir>
#ifdef SYS_WINDOWS
#include "unistd_windows.h"
#else
#include <unistd.h>
#endif
//#include <QDebug>
#include "burnutil.h"
#include "diskutil.h"

#ifdef Q_OS_LINUX
#include <linux/cdrom.h>
#endif

DiscInfo& DiscInfo::getInstance()
{
    static DiscInfo _instance;
    return _instance;
}

DiscInfo::DiscInfo(QObject* parent)
    : QObject(parent)
    , m_IsDiscBurning(false)
    , m_IsDiscMounted(false)
    , m_enableCheckDiscTask(true)
{
    m_DiscInfo.discStatus = Empty;
    m_DiscInfo.discType = UnkownType;
    m_DiscInfo.discTotalSize = 0;
    m_DiscInfo.discUnusedSize = 0;
}

bool DiscInfo::IsDiscMounted()
{
    return m_IsDiscMounted;
}

void DiscInfo::setBurnStatus(bool isBurning)
{
    m_IsDiscBurning = isBurning;
}

void DiscInfo::lockDisc(bool isLock)
{
    DiskUtil::lockDevice(Resource::discDevice, isLock);
}

int DiscInfo::getDiscStatus()
{
    return DiskUtil::getDeviceStatus(Resource::discDevice);
}

void DiscInfo::stopCheckUsbDisc()
{
    m_enableCheckDiscTask = false;
}

int DiscInfo::updateDiscInfo(int discStatus)
{
    m_DiscInfo.discStatus = UnkownState;
    m_DiscInfo.discType = UnkownType;
    m_DiscInfo.discTotalSize = 0;
    m_DiscInfo.discUnusedSize = 0;
    if ((discStatus == Disk_CDS_NO_DISC) || (discStatus == Disk_CDS_TRAY_OPEN))
    {
        return 0;
    }
    qulonglong discUnusedSize = 0;
    qulonglong discTotalSize = 0;
    BurnUtil::getDiscInfo((int&)m_DiscInfo.discStatus, (int&)m_DiscInfo.discType, discUnusedSize, discTotalSize);

    m_DiscInfo.discUnusedSize = discUnusedSize;
    m_DiscInfo.discTotalSize = discTotalSize;

    //    qDebug() << "\033[33m m_DiscInfo.discUnusedSize:" << m_DiscInfo.discUnusedSize << "\033[0m";
    //    qDebug() << "\033[33m m_DiscInfo.discTotalSize:" << m_DiscInfo.discTotalSize << "\033[0m";

    return 0;
}

void DiscInfo::checkUsbDisc()
{
    bool lastDiscPlugin = false;
    int discStatusPrev = -1;
    int discStatusCur = -1;

    Util::Rmdir(Resource::discDir, false, true);
    Util::Rmdir(Resource::discTmpDir, true, true);
    // Util::System(QString("umount -fl %1").arg(Resource::discDir));
    BurnUtil::umount(Resource::discDir);
    while (m_enableCheckDiscTask)
    {
        struct timeval tv;
        tv.tv_sec = 1; //论询间隔时间内(1S)无法检测刻录机或光盘的状态变化!!!
        tv.tv_usec = 0;
#ifdef WIN32
#else
        select(0, NULL, NULL, NULL, &tv);
#endif

        if (BurnUtil::isDiscWriterExist())
        {
            if (m_IsDiscBurning)
            {
                continue;
            }

            if (!lastDiscPlugin)
            {
                emit synGet(QString());
                lastDiscPlugin = true;

                discStatusPrev = -1;
                discStatusCur = -1;
            }

            discStatusPrev = discStatusCur;
            discStatusCur = getDiscStatus();
            //            qDebug() << "\033[34m discStatus:" << discStatusCur << "\033[0m";
            //            qDebug() << "\033[34m discStatusPrev:" << discStatusPrev << "\033[0m";
            if ((discStatusCur > 0) && (discStatusCur != discStatusPrev))
            {
                switch (discStatusCur)
                {
                case Disk_CDS_NO_DISC: // 1 托盘上没有光盘(inserted but no disc)
                    updateDiscInfo(Disk_CDS_NO_DISC);
                    m_IsDiscMounted = false;
                    break;

                case Disk_CDS_TRAY_OPEN: // 2 托盘处于弹出状态(ejected)
                    updateDiscInfo(Disk_CDS_TRAY_OPEN);
                    m_IsDiscMounted = false;
                    if (discStatusPrev == Disk_CDS_DISC_OK)
                    {
                        BurnUtil::umount(Resource::discDir);
                        emit synGet(QString());
                        if (QDir(Resource::discDir).exists())
                        {
                            Util::Rmdir(Resource::discDir);
                        }
                    }
                    break;

                case Disk_CDS_DRIVE_NOT_READY: // 3  未准备好(inserted just now and loading...)
                    updateDiscInfo(Disk_CDS_DRIVE_NOT_READY);
                    m_IsDiscMounted = false;
                    break;

                case Disk_CDS_DISC_OK: // 4  正常状态(ok)
                    if (QDir(Resource::discDir).exists())
                    {
                        Util::Rmdir(Resource::discDir, false, true);
                    }
                    if (!QDir(Resource::discDir).exists())
                    {
                        Util::Mkdir(Resource::discDir, false);
                    }

                    if (discStatusPrev != Disk_CDS_DRIVE_NOT_READY)
                    {
                        BurnUtil::umount(Resource::discDevice);
                        updateDiscInfo(Disk_CDS_DISC_OK);
                    }

                    if (BurnUtil::mountBurn(Resource::discDevice, Resource::discDir))
                    {
                        // mount ok
                    }
                    else
                    {
                        // mount failed ,TODO:
                    }
                    m_IsDiscMounted = true;
                    emit synGet(QString());
                    break;

                default:
                    break;
                }
            }
        }
        else
        {
            if (lastDiscPlugin)
            {
                m_IsDiscMounted = false;
                lastDiscPlugin = false;

                clearDiscRes();
                emit synGet(QString());
            }
        }
    }

    // exit
    clearDiscRes();
}

void DiscInfo::clearDiscRes()
{
    if (!BurnUtil::isDiscWriterExist())
    {
        Util::System(QString("killall -9 growisofs"));
        Util::System(QString("killall -9 cdrskin"));
    }
    Util::System(QString("killall -9 mkisofs"));
    BurnUtil::umount(Resource::discDir);

    if (QDir(Resource::discDir).exists())
    {
        //        Util::System(QString("rm %1/* -rf").arg(Resource::discTmpDir));   TODO:
        Util::Rmdir(Resource::discTmpDir, false);
    }
}

class USF_INTERFACE_PER_DISK_BUSINESS_EXPORT UsbDiscCheckTask : public QRunnable
{
public:
    UsbDiscCheckTask(DiscInfo* discInfo)
        : m_DiscInfo(discInfo)
    {
    }

    void run()
    {
        m_DiscInfo->checkUsbDisc();
    }

private:
    DiscInfo* m_DiscInfo;
};

void DiscInfo::startCheckUsbDisc()
{
    UsbDiscCheckTask* task = new UsbDiscCheckTask(this);
    Util::runRunnable(task);
}
