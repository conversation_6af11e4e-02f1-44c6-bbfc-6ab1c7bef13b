
/**********************************************************************************************************************************/
/* Copyright @ CHISON MEDICAL IMAGING CO.,LTD */
/*--------------------------------------------------------------------------------------------------------------------------------*/
/*!@file
 * @brief
 * <AUTHOR>
 * @date    -
 * @note    */
/**********************************************************************************************************************************/

/*---------------------------------------------------------> Includes
 * <----------------------------------------------------------*/
#include "iothreadforread.h"
#include "fpscalculator.h"
#include "setting.h"

/*---------------------------------------------------------> For Debug
 * <----------------------------------------------------------*/
/*---------------------------------------------------------> Constants
 * <----------------------------------------------------------*/
/*---------------------------------------------------------> Defines
 * <----------------------------------------------------------*/
/*---------------------------------------------------------> Structure
 * <----------------------------------------------------------*/
/*---------------------------------------------------------> Functions
 * <----------------------------------------------------------*/

/*================================================================================================================================*/
/**@brief   IoThreadForRead::IoThreadForRead
 * @return
 * @note    */
/*--------------------------------------------------------------------------------------------------------------------------------*/
IoThreadForRead::IoThreadForRead(IoDevice* device, qint32 size, QObject* parent)
    : IoThread(device, parent)
    , m_readSize(0)
    , m_readBuff(nullptr)
    , m_sleepTime(1)
    , m_IsIdentify(false)
//    , m_FpsCalculator(new FpsCalculator(this))
{
    setReadingSize(size);
}

/*================================================================================================================================*/
/**@brief
 * @return
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
IoThreadForRead::~IoThreadForRead()
{
    if (nullptr != m_readBuff)
    {
        delete[] m_readBuff;
        m_readBuff = nullptr;
    }

    //    delete m_FpsCalculator;
    //    m_FpsCalculator = nullptr;
}

/*================================================================================================================================*/
/**@brief
 * @return
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
qint32 IoThreadForRead::readingSize() const
{
    return m_readSize;
}

/*================================================================================================================================*/
/**@brief
 * @return
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
void IoThreadForRead::setReadingSize(qint32 size)
{
    QMutexLocker locker(&m_readMutex);

    if ((m_readSize != size) && (size > 0))
    {
        m_readSize = size;

        if (nullptr != m_readBuff)
        {
            delete[] m_readBuff;
            m_readBuff = nullptr;
        }

        m_readBuff = new uchar[size];
        memset(m_readBuff, 0, size);
    }
}

/*================================================================================================================================*/
/**@brief
 * @return
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
void IoThreadForRead::setReadingInterval(qint32 ms)
{
    m_sleepTime = ms;
}

void IoThreadForRead::setIdentify()
{
    m_IsIdentify = true;
}

/*================================================================================================================================*/
/**@brief
 * @return
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
bool IoThreadForRead::readOneFrame(bool& stopRead)
{
    //调试ATOM（XBit）
    // PCIE的时候，发现readSize需要为47104(读32个包)，而phoniex和apple项目是48576（读33个包），进行了特殊处理
    //目前代码是phoniex的，如果需要适配Xbit机型，不能使用phoniex的特殊处理
    //    m_readSize = 47104;
    // sonomax m_readSize = 94208 (64 package)
    int ret = m_device->read(m_readBuff, m_readSize);
    if (ret == UltrasoundDevice::EC_READ_FAILED_PERMANENT)
    {
        stopRead = true;
    }
    else if (ret == UltrasoundDevice::EC_WAIT_TIMEOUT)
    {
        qCritical() << PRETTY_FUNCTION << " timeout";
        emit waitTimeout();
    }
    return (ret == m_readSize);
}

/*================================================================================================================================*/
/**@brief
 * @return
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
// float IoThreadForRead::fps() const
//{
//    if (nullptr != m_FpsCalculator)
//    {
//        return m_FpsCalculator->fps();
//    }
//    else
//    {
//        return 0.0F;
//    }
//}

/*================================================================================================================================*/
/**@brief
 * @return
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
void IoThreadForRead::run()
{
    if (m_device == NULL || !m_device->isOpen())
    {
        return;
    }

    m_active = true;
    bool ok = false;
    int errorCount = 0;
    const int errorCheckCount = 5;

    while (m_active)
    {
        if (m_device != nullptr && m_device->isOpen())
        {
            QMutexLocker locker(&m_readMutex);
            bool stopRead = false;
            ok = readOneFrame(stopRead);
            if (stopRead)
            {
                m_active = false;
                qCritical() << PRETTY_FUNCTION << "read data failed permanent, breakout...";
                break;
            }
            if (ok)
            {
                emit update(m_readBuff, m_readSize);
            }
            else
            {
                if (m_IsIdentify)
                { //用于探头识别时,USB打开正常, 但是后续读取探头识别码等信息时, 通讯异常无法正常退出
                    errorCount++;
                    if (errorCount >= errorCheckCount)
                    {
                        qCritical() << PRETTY_FUNCTION << " read identified data error!";
                        errorCount = 0;
                        emit update(m_readBuff, m_readSize);
                    }
                }
                setLastError(IoDevice::IO_Read_Error);
            }
            //            msleep(1000 * 1000);
        }

        /**
         * 执行成功：让线程睡眠指定时间，给其它线程执行时间
         * 执行失败：有可能是因为超时而读数失败，所以不退出，只等待一段时间
         */
        if (!ok || m_lowPowerMode)
        {
            msleep(200);
        }
        tryWait();
    }
}
