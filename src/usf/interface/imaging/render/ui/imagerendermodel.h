#ifndef IMAGERENDERMODEL_H
#define IMAGERENDERMODEL_H
#include "usfinterfaceimagingrenderui_global.h"

#include "glshaderimage.h"
#include "imagemodelbase.h"
#include <QImage>
#include <QMutex>

class USF_INTERFACE_IMAGING_RENDER_UI_RENDER_EXPORT ImageRenderModel : public ImageModelBase
{
public:
    ImageRenderModel(QImage::Format imageFormat);
    virtual ~ImageRenderModel();
    void copyfrom(ImageRenderModel* other);
    void setImageData(ImageEventArgs* imageArg);
    void setImageData(QImage image);
    const QImage& imageData();
    const QPixmap pixMapData();
    bool isNull();
    int ecgend();
    void setSyncId(const int syncId);
    int syncId() const;
    void setLayout(const int layout);
    int layout() const;
    void setSystemScanMode(const int systemScanMode);
    int systemScanMode() const;
    void setIsPainting(const bool isPainting);
    bool isPainting() const;
    void clearImageData();
    int imageRenderPartition() const;
    void setImageRenderPartition(const int partition);
    void setFrameIndex(const int frameIndex);
    int frameIndex() const;
    void setImageBufferFrameIndex(int frameIndex);
    int imageBufferFrameIndex();
    bool isMeasureImageReady() const;
    void setMeasureImageReady(bool isMeasureImageReady);

private:
    QMutex m_Mutex; // m_Image 存在多线程访问，用于保护m_Image数据多线程(DSCThread和主线程)访问
    QImage m_Image; // 绘制图像
    PainterImageModel* m_ImageModel; // 图像数据
    QImage::Format m_ImageFormat;
    int ecgEnd;
    int m_SyncId;
    int m_Layout;
    int m_SystemScanMode;
    bool m_IsPainting;
    int m_ImageRenderPartition;
    int m_FrameIndex;

    int m_ImageBufferFrameIndex{0};
    bool m_IsMeasureImageReady{false};
};

#endif // IMAGERENDERMODEL_H
