#include "imagerendermodel.h"

ImageRenderModel::ImageRenderModel(QImage::Format imageFormat)
    : m_Image(QImage())
    , m_ImageModel(new PainterImageModel())
    , m_ImageFormat(imageFormat)
    , m_SyncId(-1)
    , m_Layout(1)
    , m_SystemScanMode(0)
    , m_IsPainting(false)
    , ecgEnd(0)
    , m_ImageRenderPartition(0)
    , m_FrameIndex(0)
{
}

ImageRenderModel::~ImageRenderModel()
{
    if (NULL != m_ImageModel)
    {
        delete m_ImageModel;
        m_ImageModel = NULL;
    }
}

void ImageRenderModel::copyfrom(ImageRenderModel* other)
{
    m_ImageFormat = other->m_ImageFormat;
    ecgEnd = other->ecgEnd;
    m_SyncId = other->m_SyncId;
    m_Layout = other->m_Layout;
    m_SystemScanMode = other->m_SystemScanMode;
    m_IsPainting = other->m_IsPainting;
    m_FrameIndex = other->m_FrameIndex;
    m_ImageBufferFrameIndex = other->m_ImageBufferFrameIndex;
    m_ImageModel->copyfrom(other->m_ImageModel);
    //拷贝的时候,m_image创建的时候，传入的是m_ImageModel里面的data指针，因此在深拷贝后，m_image要重建，确保内部指向的指针是最新的data指针
    m_Image =
        QImage(m_ImageModel->data()->m_Data, other->imageData().width(), other->imageData().height(), m_ImageFormat);
}

void ImageRenderModel::setImageData(ImageEventArgs* imageArg)
{
    if (m_ImageModel == NULL)
    {
        return;
    }
    m_ImageModel->setData(imageArg);
    PainterImageData* imageData = m_ImageModel->data();

    //非必要不重新构建QImage，其源码中构建会做new操作，在operator = 操作时会做delete，
    //但在该图像尺寸未发生变更时，重新构建QImage没有必要，另外由于QImage没提供更改 width 与 height 的接口
    //否则m_Image只需要构建一次，其实际图像已经在m_ImageModel->setData(imageArg)中更新
    if (m_Image.width() != imageData->m_Width || m_Image.height() != imageData->m_Height)
    {
        // setImageData 此函数在DSCThread 线程上执行，加锁保护m_Image多线程访问
        QMutexLocker locker(&m_Mutex);
        m_Image = QImage(imageData->m_Data, imageData->m_Width, imageData->m_Height, m_ImageFormat);
    }

    ecgEnd = imageArg->ecgEnd();
}

void ImageRenderModel::setImageData(QImage image)
{
    if (m_Image != image)
    {
        m_Image = image;
    }
}

void ImageRenderModel::clearImageData()
{
    QMutexLocker locker(&m_Mutex);
    memset(m_Image.bits(), 0, m_Image.sizeInBytes());
}

const QImage& ImageRenderModel::imageData()
{
    QMutexLocker locker(&m_Mutex);
    return m_Image;
}

const QPixmap ImageRenderModel::pixMapData()
{
    QMutexLocker locker(&m_Mutex);
    return QPixmap::fromImage(m_Image);
}

bool ImageRenderModel::isNull()
{
    QMutexLocker locker(&m_Mutex);
    return m_Image.isNull();
}

int ImageRenderModel::ecgend()
{
    return ecgEnd;
}

void ImageRenderModel::setSyncId(const int syncId)
{
    m_SyncId = syncId;
}

int ImageRenderModel::syncId() const
{
    return m_SyncId;
}

void ImageRenderModel::setLayout(const int layout)
{
    m_Layout = layout;
}

int ImageRenderModel::layout() const
{
    return m_Layout;
}

void ImageRenderModel::setSystemScanMode(const int systemScanMode)
{
    m_SystemScanMode = systemScanMode;
}

int ImageRenderModel::systemScanMode() const
{
    return m_SystemScanMode;
}

void ImageRenderModel::setIsPainting(const bool isPainting)
{
    m_IsPainting = isPainting;
}

bool ImageRenderModel::isPainting() const
{
    return m_IsPainting;
}

int ImageRenderModel::imageRenderPartition() const
{
    return m_ImageRenderPartition;
}

void ImageRenderModel::setImageRenderPartition(const int partition)
{
    m_ImageRenderPartition = partition;
}

void ImageRenderModel::setFrameIndex(const int frameIndex)
{
    m_FrameIndex = frameIndex;
}

int ImageRenderModel::frameIndex() const
{
    return m_FrameIndex;
}

void ImageRenderModel::setImageBufferFrameIndex(int frameIndex)
{
    m_ImageBufferFrameIndex = frameIndex;
}

int ImageRenderModel::imageBufferFrameIndex()
{
    return m_ImageBufferFrameIndex;
}

bool ImageRenderModel::isMeasureImageReady() const
{
    return m_IsMeasureImageReady;
}

void ImageRenderModel::setMeasureImageReady(bool isMeasureImageReady)
{
    m_IsMeasureImageReady = isMeasureImageReady;
}
