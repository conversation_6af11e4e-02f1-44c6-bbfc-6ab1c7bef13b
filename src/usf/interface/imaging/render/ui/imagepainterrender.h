#ifndef IMAGEPAINTERRENDER_H
#define IMAGEPAINTERRENDER_H

#include "usfinterfaceimagingrenderui_global.h"

#include "imageinfodef.h"
#include "imageinfogenerator.h"
#include "imagepainterinfogenerator.h"
#include "imagerendermodel.h"
#include "irender.h"
#include <QHash>
#include <QImage>

class USF_INTERFACE_IMAGING_RENDER_UI_RENDER_EXPORT ImagePainterRender : public IRender
{
    Q_OBJECT
public:
    ImagePainterRender(QObject* parent = 0);
    virtual ~ImagePainterRender();
    void draw(QPainter* painter, bool isisFreeze = false, double ratio = 1);
    virtual void reset();
    virtual void render();
    virtual void init();
    virtual void resize(const QRectF& rect);
    virtual void setData(ImageEventArgs* data);
    virtual void setBufferManager(IMuiltSonoParametersBuffer* bufferManager);
    virtual void setSonoParameters(SonoParameters* sonoParameters);
    virtual void setupAllTransform();
    virtual void setFullScreenZoomFactor(qreal zoomFactor);
    /**
     * @brief setShaderTransform 设置指定图像类型的shader的变换矩阵
     * @param type　图像类型
     * @param transform　变换矩阵
     */
    void setImageTransform(ModeImageType type, const QTransform& transform);
    void setOneImageTransform(ImageRenderModel* image, const QTransform& transform);
    void setOneShaderOrthTransform(ImageRenderModel* image, const QTransform& transform);
    /**
     * @brief findShaderImage 查找指定的类型和下标的ShaderImage
     * @param type 图像类型
     * @param index 下标
     * @return
     */
    ImageRenderModel* findImageModel(const ModeImageType& type, int index);
    ImageRenderModel* findImageModel(const ModeImageType& type, int index, int syncID, int layout, int dataType,
                                     int systemScanMode, int partition = 0);
    ImageRenderModel* findValidImageModel(const ModeImageType& type, int index, int syncID, int layout, int dataType,
                                          int systemScanMode, int partition = 0);
    void setWholeImageRect(const QRect& rect);
    void reorganizeImages();
    QImage imageData(ImageEventArgs::ImageType type, int index = 0);
    void setIsFlip(bool isFlip);
    void changeActiveModel(int activeIndex);
    void replaceImageModel(ImageEventArgs::ImageType type);
    QImage::Format ImageFormat();
    void clearImageDataCache();
    int paintFrameIndex();
    int imageBufferFrameIndex();
    void setPictureImage(QImage image);
signals:
    void sonoZoomDataUpdate(int layout, int scanMode, const QRect& rect, const QPixmap& pix, const float imageRadio);
    void clearMeasureContextImage();
    void renderImageChanged(const QPixmap& pix, ModeImageType modeType, int frameIndex);
private slots:
    void onLineImageDebugChanged(const QVariant& value);
    void onLineColorMapDebugChanged(const QVariant& value);

private:
    /**
     * @brief isImageRenderModeSame
     * @param image
     * @param syncID
     * @param systemScanMode
     * @param partition
     * @param isReset  默认为false，image的syncId只需与传参syncID做判等比较; 为true时，image的syncId还需与-1做判等比较
     * @return
     */
    bool isImageRenderModeSame(const ImageRenderModel* image, int syncID, int systemScanMode, int partition,
                               bool isReset = false);

    void reorganizeImagesInLock(int layout, const ModeImageType& type, int active, int syncID, int systemScanMode,
                                int partition = 0);
    /**
     * @brief createGLShaderImages 创建每个图像区域shader图像参数
     * 每个图像区域包含图像类别和图像绘制大小
     */
    virtual void createImages(int activeIndex);
    /**
     * @brief paintGL 绘制图像的接口，通过render提供外部调用刷新图像
     */
    virtual void paint(QPainter* painter, bool isFreeze = false, double ratio = 1);
    void paintInLock(QMultiHash<ModeImageType, ImageRenderModel*>& images, QPainter* painter, bool isFreeze = false,
                     double ratio = 1);
    void destroyImages(QMultiHash<ModeImageType, ImageRenderModel*>& images);
    void onSetSonoParameters();
    ImageRenderModel* findImageModel(int index);
    void createImage(const ModeImageInfo& imageInfo);
    void claAreaRect(QRectF area, qreal ratio, const bool doNotChangeCenter = true);

    void setPaintFrameIndex(int index);

private:
    QMultiHash<ModeImageType, ImageRenderModel*> m_Images;       // shader纹理对象与模式类型的映射
    QMultiHash<ModeImageType, ImageRenderModel*> m_OldImages;    // shader纹理对象与模式类型的映射
    QMultiHash<ModeImageType, ImageRenderModel*> m_BackupImages; // shader纹理对象与模式类型的映射的备份
    bool m_Resetting;
    ImagePainterInfoGenerator m_InfoGenerator;
    QMutex m_Mutex;
    static const QImage::Format m_ImageFormat;
    qreal m_ZoomFactor;
    QTransform m_ZoomTransform;
    bool m_IsPrePare;
    int m_LineImageDebug;
    int m_LineColorMapDebug;
    QRect m_DscImageRect;
    QRectF m_AreaRect;
    QRectF m_AreaRectAfterTransform;
    IMuiltSonoParametersBuffer* m_MuiltSonoParametersBuffer;
    bool m_IsDrawing; //用标记代替锁

    QMutex m_PaintFrameIndexMutex;
    int m_PaintFrameIndex;

    int m_ImageBufferFrameIndex{0};
    QImage m_PictureImage;
};
#endif // IMAGEPAINTERRENDER_H
