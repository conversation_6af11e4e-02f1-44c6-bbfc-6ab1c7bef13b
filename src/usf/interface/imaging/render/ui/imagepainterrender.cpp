#include "imagepainterrender.h"
#include "bfpnames.h"
#include "bfscanareawidthparameter.h"
#include "imuiltsonoparametersbuffer.h"
#include "infostruct.h"
#include "parameter.h"
#include "realcompare.h"
#include "resource.h"
#include "setting.h"
#include "sonoparameters.h"
#include "util.h"
#include "syncidmanager.h"
#include <QGraphicsPolygonItem>
#include <QMutexLocker>
#include <QPainter>
#include <QTransform>
#include <QtMath>

const QImage::Format ImagePainterRender::m_ImageFormat = QImage::Format_RGB32;
ImagePainterRender::ImagePainterRender(QObject* parent)
    : IRender(parent)
    , m_Resetting(false)
    , m_ZoomFactor(1.0f)
    , m_IsPrePare(false)
    , m_LineImageDebug(0)
    , m_LineColorMapDebug(0)
    , m_MuiltSonoParametersBuffer(NULL)
    , m_IsDrawing(false)
    , m_PaintFrameIndex(0)
    , m_ImageBufferFrameIndex(0)
{
}

ImagePainterRender::~ImagePainterRender()
{
    disconnect(this);
    destroyImages(m_OldImages);
    destroyImages(m_Images);
    destroyImages(m_BackupImages);
}

void ImagePainterRender::draw(QPainter* painter, bool isFreeze, double ratio)
{
    if (painter != NULL && m_IsPrePare)
    {
        paint(painter, isFreeze, ratio);
    }
}

void ImagePainterRender::reset()
{
    destroyImages(m_OldImages);
    destroyImages(m_Images);
}

void ImagePainterRender::render()
{
}

void ImagePainterRender::init()
{
}

void ImagePainterRender::resize(const QRectF& rect)
{
}

void ImagePainterRender::setData(ImageEventArgs* data)
{
    if (/*!m_IsPrePare || */ NULL == data || data->isNull())
    {
        return;
    }
    QMutexLocker locker(&m_Mutex);
    ImageRenderModel* model = findImageModel(data->imageType(), data->index(), data->syncId(), data->layout(),
                                             data->dataType(), data->systemScanMode(), data->imageRenderPartition());

    if (model != NULL)
    {
        model->setImageData(data);
        model->setMeasureImageReady(data->isMeasureImageReady());

        //目前实时存储图片用到了这个framindex，在多个image的情况下，
        //如果另一个image不上数据，就没办法更新到最新的索引，因此需要遍历更新
        // 2025-06-12 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
        // 绑定到临时对象，减少遍历时临时拷贝kyes
        const auto& keys = m_Images.values();
        for (ImageRenderModel* curModel : keys)
        {
            if (curModel->frameIndex() != data->frameIndex())
            {
                curModel->setFrameIndex(data->frameIndex());
                curModel->setImageBufferFrameIndex(data->imageBufferFrameIndex());
            }
        }
    }
}

void ImagePainterRender::setBufferManager(IMuiltSonoParametersBuffer* bufferManager)
{
    m_MuiltSonoParametersBuffer = bufferManager;
    m_InfoGenerator.setBufferManager(bufferManager);
}

void ImagePainterRender::setSonoParameters(SonoParameters* sonoParameters)
{
    QSize dscImageSize = sonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    QSize widgetSize = sonoParameters->pV(BFPNames::RenderWidgetSizeStr).toSize();
    m_DscImageRect =
        QRect(0, (widgetSize.height() - dscImageSize.height()) / 2, dscImageSize.width(), dscImageSize.height());
    m_LineImageDebug = sonoParameters->pIV(BFPNames::LineImageDebugStr);
    m_LineColorMapDebug = sonoParameters->pIV(BFPNames::LineColorMapDebugStr);
    connect(sonoParameters->parameter(BFPNames::LineImageDebugStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onLineImageDebugChanged(QVariant)));
    connect(sonoParameters->parameter(BFPNames::LineColorMapDebugStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onLineColorMapDebugChanged(QVariant)));
    m_InfoGenerator.setSonoParameters(sonoParameters);
    onSetSonoParameters();
}

void ImagePainterRender::setupAllTransform()
{
    foreach (ImageRenderModel* image, m_Images)
    {
        if (image != NULL)
        {
            setOneImageTransform(image, m_InfoGenerator.imageTransform(image->imageInfo()));
            setOneShaderOrthTransform(image, m_InfoGenerator.imageOrthTransform(image->imageInfo()));
        }
    }
}

void ImagePainterRender::setFullScreenZoomFactor(qreal zoomFactor)
{
    m_ZoomFactor = zoomFactor;

    QTransform trans;
    trans.scale(m_ZoomFactor, m_ZoomFactor);
    m_ZoomTransform = trans;
}

void ImagePainterRender::setImageTransform(ModeImageType type, const QTransform& transform)
{
    QList<ImageRenderModel*> images = m_Images.values(type);
    foreach (ImageRenderModel* image, images)
    {
        setOneImageTransform(image, transform);
    }
}

void ImagePainterRender::setOneImageTransform(ImageRenderModel* image, const QTransform& transform)
{
    if (image != NULL)
    {
        QMatrix4x4 matrix(transform);
        image->setMatrix(matrix);
    }
}

void ImagePainterRender::setOneShaderOrthTransform(ImageRenderModel* image, const QTransform& transform)
{
    if (image != NULL)
    {
        image->setOrthMatrix(QMatrix4x4(transform));
    }
}

ImageRenderModel* ImagePainterRender::findImageModel(const ModeImageType& type, int index)
{
    QList<ImageRenderModel*> images = m_Images.values(type);
    for (int i = 0; i < images.size(); i++)
    {
        ImageRenderModel* image = images[i];
        // index目前仅用于多B图像的下标
        if (image != NULL && index == image->index())
        {
            return image;
        }
    }
    return NULL;
}

void ImagePainterRender::clearImageDataCache()
{
    foreach (ImageRenderModel* model, m_Images)
    {
        model->clearImageData();
    }
    foreach (ImageRenderModel* model, m_OldImages)
    {
        model->clearImageData();
    }
}

int ImagePainterRender::paintFrameIndex()
{
    QMutexLocker locker(&m_PaintFrameIndexMutex);
    return m_PaintFrameIndex;
}

int ImagePainterRender::imageBufferFrameIndex()
{
    return m_ImageBufferFrameIndex;
}

void ImagePainterRender::setPictureImage(QImage image)
{
    if (image != m_PictureImage)
    {
        m_PictureImage = image;
    }
}

ImageRenderModel* ImagePainterRender::findValidImageModel(const ModeImageType& type, int index, int syncID, int layout,
                                                          int dataType, int systemScanMode, int partition)
{
    if (m_InfoGenerator.sonoParameters() == nullptr)
    {
        return nullptr;
    }
    SonoParameters* curLayoutSonoParameters = m_MuiltSonoParametersBuffer->getSonoParametersByLayoutIndex(
        m_InfoGenerator.sonoParameters()->pIV(BFPNames::ActiveBStr));
    if (curLayoutSonoParameters == nullptr)
    {
        return nullptr;
    }
    QList<ImageRenderModel*> images = m_Images.values(type);
    if (!images.isEmpty())
    {
        if (curLayoutSonoParameters->pBV(BFPNames::PictureModeONStr))
        {
            if (!images.isEmpty() && (images.size() == 2))
            {
                images[0]->setSyncId(syncID);
                images[1]->setSyncId(syncID);
                images[1]->setImageData(m_PictureImage);
                return images[0];
            }
        }

        if (curLayoutSonoParameters->pBV(BFPNames::BCImagesOnStr))
        {
            QList<ImageRenderModel*> images = m_Images.values(type);
            if (!images.isEmpty())
            {
                if (images.size() == 2)
                {
                    if ((partition > 0) && (partition <= 2))
                    {
                        int index = 2 - partition;
                        images[index]->setSyncId(syncID);
                        return images[index];
                    }
                    else if (curLayoutSonoParameters->pBV(BFPNames::HasAutoEFResultStr))
                    {
                        int layoutIndex = curLayoutSonoParameters->pIV(BFPNames::AutoEFCurLayoutStr);
                        int index = (layoutIndex == CurLayout::Layout_1x2_2) ? 0 : 1;
                        images[index]->setSyncId(syncID);
                        return images[index];
                    }
                    else if (dataType == 4)
                    {
                        images[0]->setSyncId(syncID);
                        return images[0];
                    }
                    else if (dataType == 0)
                    {
                        images[1]->setSyncId(syncID);
                        return images[1];
                    }
                }
            }
        }
        else
        {
            for (int i = 0; i < images.size(); i++)
            {
                ImageRenderModel* image = images[i];
                if (image != NULL && index == image->index() && image->layout() == layout &&
                    isImageRenderModeSame(image, syncID, systemScanMode, partition, true)) // autoef测量，单双幅切换
                {
                    image->setSyncId(syncID);
                    return image;
                }
            }
        }
    }
    return NULL;
}

ImageRenderModel* ImagePainterRender::findImageModel(const ModeImageType& type, int index, int syncID, int layout,
                                                     int dataType, int systemScanMode, int partition)
{
    //    qDebug() << PRETTY_FUNCTION
    //             << "type:" << type
    //             << "index:" << index
    //             << "syncID:" << syncID
    //             << "layout:" << layout;
    //    foreach(ImageRenderModel* model, m_Images.values())
    //    {
    //        qDebug() << PRETTY_FUNCTION
    //                 << "--1--"
    //                 << "modeType:" << model->imageInfo().m_ModeType
    //                 << "modeIndex:" << model->imageInfo().m_ImageIndex
    //                 << "syncId:" << model->syncId()
    //                 << "layout:" << model->layout()
    //                 << "systemScanMode:" << model->systemScanMode();
    //    }
    ImageRenderModel* model = findValidImageModel(type, index, syncID, layout, dataType, systemScanMode, partition);
    if (model == NULL)
    {
        reorganizeImagesInLock(layout, type, index, syncID, systemScanMode, partition);
        model = findValidImageModel(type, index, syncID, layout, dataType, systemScanMode, partition);
        if (model != NULL)
        {
            return model;
        }
    }
    else
    {
        return model;
    }

    QList<ImageRenderModel*> images = m_Images.values(type);
    foreach (ImageRenderModel* model, images)
    {
        if (model->index() == index && model->layout() == layout)
        {
            model->setSyncId(syncID);
            return model;
        }
    }
    destroyImages(m_Images);
    m_Images.clear();
    m_Resetting = true;
    //    qDebug() << PRETTY_FUNCTION << "last";
    return NULL;
}

void ImagePainterRender::setWholeImageRect(const QRect& rect)
{
    m_InfoGenerator.setWholeImageSize(rect.size());
}

void ImagePainterRender::reorganizeImages()
{
    QMutexLocker locker(&m_Mutex);
    m_IsPrePare = true;
    //    reorganizeImagesInLock();
}

QImage ImagePainterRender::imageData(ImageEventArgs::ImageType type, int index)
{
    QImage image;
    ImageRenderModel* model = findImageModel(type, index);
    if (model != NULL)
    {
        image = model->imageData();
        return image;
    }

    return image;
}

void ImagePainterRender::setIsFlip(bool isFlip)
{
    m_InfoGenerator.setIsFlip(isFlip);
}

void ImagePainterRender::changeActiveModel(int activeIndex)
{
    //    QMutexLocker locker(&m_Mutex);
    //    ImageRenderModel* model = findImageModel(activeIndex);
    //    if(model != NULL)
    //    {
    //        QList<ModeImageInfo> modeInfos;
    //        m_InfoGenerator.geneModeImageInfos(modeInfos);
    //        if(model->imageInfo().m_ModeType != modeInfos[activeIndex].m_ModeType)
    //        {
    //            // erase model
    //            m_Images.remove(model->imageInfo().m_ModeType, model);
    //            delete model;
    //            model = NULL;
    //            createImage(modeInfos[activeIndex]);
    //        }
    //    }
}

QImage::Format ImagePainterRender::ImageFormat()
{
    return ImagePainterRender::m_ImageFormat;
}

void ImagePainterRender::replaceImageModel(ImageEventArgs::ImageType type)
{
    QMutexLocker locker(&m_Mutex);
    ImageRenderModel* model = findImageModel(type, 0);
    if (model != NULL)
    {
        QList<ModeImageInfo> modeInfos;
        m_InfoGenerator.geneModeImageInfos(modeInfos);
        foreach (const ModeImageInfo& info, modeInfos)
        {
            if (info.m_ModeType == type)
            {
                // erase model
                m_Images.remove(model->imageInfo().m_ModeType, model);
                delete model;
                model = NULL;
                createImage(info);
                break;
            }
        }
    }
}

void ImagePainterRender::onLineImageDebugChanged(const QVariant& value)
{
    m_LineImageDebug = value.toInt();
}

void ImagePainterRender::onLineColorMapDebugChanged(const QVariant& value)
{
    m_LineColorMapDebug = value.toInt();
}

bool ImagePainterRender::isImageRenderModeSame(const ImageRenderModel* image, int syncID, int systemScanMode,
                                               int partition, bool isReset)
{
    bool isSame = (isReset ? (image->syncId() == SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId) ||
                              image->syncId() == -1)
                           : image->syncId() == syncID) &&
                  image->systemScanMode() == systemScanMode && image->imageRenderPartition() == partition;

    return isSame;
}

void ImagePainterRender::reorganizeImagesInLock(int layout, const ModeImageType& type, int active, int syncID,
                                                int systemScanMode, int partition)
{
    m_IsPrePare = false;
    SonoParameters* sp = m_MuiltSonoParametersBuffer->getSonoParametersByLayoutIndex(-1);
    if (sp != NULL && (sp->pIV(BFPNames::LayoutStr) > Layout_1x1 || layout > Layout_1x1))
    {
        if (m_Images.values(type).count() == layout)
        {
            foreach (ImageRenderModel* image, m_Images.values())
            {
                if (image->layout() == sp->pIV(BFPNames::LayoutStr))
                {
                    m_IsPrePare = true;
                    return;
                }
            }
        }
    }
    //    QMultiHash<ModeImageType, ImageRenderModel*> oldImages = m_Images;
    {
        destroyImages(m_BackupImages);
        for (QMultiHash<ModeImageType, ImageRenderModel*>::iterator it = m_Images.begin(); it != m_Images.end(); it++)
        {
            if (!it.value()->isNull() && isImageRenderModeSame(it.value(), syncID, systemScanMode, partition))
            {
                ImageRenderModel* image = new ImageRenderModel(m_ImageFormat);
                image->copyfrom(it.value());
                m_BackupImages.insert(it.key(), image);
            }
        }
    }

    bool isPainting = false;
    // 2025-06-12 Modify by AlexWang 使用c++11的for...range遍历，使用const auto& 绑定到临时对象，减少遍历时临时拷贝kyes
    const auto keys = m_Images.values();
    for (ImageRenderModel* image : keys)
    {
        if (image->isPainting())
        {
            isPainting = true;
        }
        //        qDebug() << PRETTY_FUNCTION
        //                 << "isNull():" << image->isNull()
        //                 << "modeType:" << image->imageInfo().m_ModeType
        //                 << "index():" << image->index()
        //                 << "isPainting():" << image->isPainting();
        //        qDebug()<<"-----------------------------------------------";
    }
    if (isPainting && !m_Images.isEmpty())
    {
        destroyImages(m_OldImages);
        m_OldImages = m_Images;
    }
    else
    {
        destroyImages(m_Images);
    }
    m_Images.clear();
    m_Resetting = true;
    createImages(active);
    m_IsPrePare = true;

    for (QMultiHash<ModeImageType, ImageRenderModel*>::iterator it = m_BackupImages.begin(); it != m_BackupImages.end();
         it++)
    {
        if (m_Images.contains(it.key()))
        {
            m_Images.value(it.key())->copyfrom(it.value());
        }
    }
}

void ImagePainterRender::createImages(int activeIndex)
{
    if (!m_Images.isEmpty())
    {
        return;
    }
    emit clearMeasureContextImage();
    QList<ModeImageInfo> modeInfos;
    m_InfoGenerator.geneModeImageInfos(modeInfos, activeIndex);
    for (int i = 0; i < modeInfos.size(); i++)
    {
        //        qDebug() << PRETTY_FUNCTION << modeInfos.count() << modeInfos[i].m_Area;
        createImage(modeInfos[i]);
    }
}

void ImagePainterRender::paint(QPainter* painter, bool isFreeze, double ratio)
{
    if (!m_IsDrawing)
    { //用标记代替锁
        m_IsDrawing = true;
        if (m_Resetting)
        {
            bool useNewImages = m_Images.isEmpty() ? false : true;
            int activeBIndex = m_InfoGenerator.sonoParameters()->pIV(BFPNames::ActiveBStr);
            SonoParameters* curLayoutSp = m_MuiltSonoParametersBuffer->getSonoParametersByLayoutIndex(activeBIndex);
            foreach (ImageRenderModel* image, m_Images)
            {
                //                            qDebug() << PRETTY_FUNCTION
                //                                     << "isNull():" << image->isNull()
                //                                     << "modeType:" << image->imageInfo().m_ModeType
                //                                     << "index():" << image->index()
                //                                     << "systemScanMode:" << image->systemScanMode()
                //                                     << "layout:" << image->layout()
                //                                     << "syncId:" << image->syncId()
                //                                     << "ActiveB-1:" << sp->pIV(BFPNames::ActiveBStr)
                //                                     << "ActiveB-2:" <<
                //                                     m_InfoGenerator.sonoParameters()->pIV(BFPNames::ActiveBStr);
                if (image->isNull() &&
                    (image->imageInfo().m_ModeType == ImageEventArgs::ImageB ||
                     image->imageInfo().m_ModeType == ImageEventArgs::ImageC) &&
                    image->index() <= activeBIndex && !curLayoutSp->pBV(BFPNames::AutoEFOnStr) &&
                    !curLayoutSp->pBV(BFPNames::PictureModeONStr))
                {
                    useNewImages = false;
                    break;
                }
            }
            //        foreach(ImageRenderModel* model, m_OldImages.values())
            //        {
            //            qDebug() << PRETTY_FUNCTION
            //                     << "--old--"
            //                     << "isNull:" << model->isNull()
            //                     << "modeType:" << model->imageInfo().m_ModeType
            //                     << "modeIndex:" << model->imageInfo().m_ImageIndex
            //                     << "syncId:" << model->syncId()
            //                     << "layout:" << model->layout()
            //                     << "systemScanMode:" << model->systemScanMode();
            //        }
            m_Resetting = !useNewImages;
            if (useNewImages)
            {
                foreach (ImageRenderModel* image, m_Images)
                {
                    //                qDebug() << PRETTY_FUNCTION
                    //                         << "isNull():" << image->isNull()
                    //                         << "modeType:" << image->imageInfo().m_ModeType
                    //                         << "index():" << image->index()
                    //                         << "systemScanMode:" << image->systemScanMode()
                    //                         << "layout:" << image->layout()
                    //                         << "syncId:" << image->syncId();
                    image->setIsPainting(true);
                }
            }
        }
        paintInLock(m_Resetting ? m_OldImages : m_Images, painter, isFreeze, ratio);
        m_IsDrawing = false;
    }
}

void ImagePainterRender::paintInLock(QMultiHash<ModeImageType, ImageRenderModel*>& images, QPainter* painter,
                                     bool isFreeze, double ratio)
{
    foreach (ImageRenderModel* image, images)
    {
        if (image->isNull())
        {
            continue;
        }
        ModeImageInfo info = image->imageInfo();
        QPixmap pix = image->pixMapData();
        {
            //                // for test
            //                QPainter p(&pix);
            //                p.setPen(Qt::red);
            //                int x_edge = DopplerAdjustControl::m_x_edge;
            //                int y_edge = DopplerAdjustControl::m_y_edge;
            //                int x = DopplerAdjustControl::m_x;
            //                int y = DopplerAdjustControl::m_y;
            //                p.drawLine(x, y, x+100, y);
            //                p.drawLine(x_edge, y_edge, x_edge+100, y_edge);
        }

        int debugFlag = m_LineImageDebug | m_LineColorMapDebug;
        if (debugFlag == 0)
        {
            if (m_InfoGenerator.sonoParameters()->pBV(BFPNames::IsSecondGearFullScreenZoomInStr) ||
                m_InfoGenerator.sonoParameters()->pBV(BFPNames::IsFullScreenZoomInStr))
            {
                claAreaRect(info.m_Area, ratio, false);
                QRect rec =
                    m_InfoGenerator.imageRenderRect(image->layout(), image->systemScanMode(), m_AreaRect.toRect(),
                                                    qMax(pix.height(), (int)m_AreaRect.height()), ratio);
                painter->drawPixmap(rec, pix);
                emit sonoZoomDataUpdate(image->layout(), image->systemScanMode(), rec, pix, ratio);
                setPaintFrameIndex(image->frameIndex());

                m_ImageBufferFrameIndex = image->imageBufferFrameIndex();
            }
            else
            {
                if (qAbs(info.m_Area.height() - image->imageData().height()) > 1)
                {
                    printf("ImagePainterRender::paintInLock() return\n");
                    return;
                }
                claAreaRect(info.m_Area, ratio);
                const QRect pixRect = m_InfoGenerator.imageRenderRect(image->layout(), image->systemScanMode(),
                                                                      m_AreaRect.toRect(), pix.height());
                painter->drawPixmap(pixRect, pix);
                if (Setting::instance().defaults().afterZeusSaveFlag())
                {
                    if (info.m_ModeType == ImageEventArgs::ImageD)
                    {
                        QString filePath = QString("%1imagePW_screen/").arg(Resource::zeusContextDataSaverDir());
                        Util::Mkdir(filePath.toStdString().c_str());
                        pix.save(QString("%1/screen_%2_%3_%4.jpeg")
                                     .arg(filePath)
                                     .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
                                     .arg(image->frameIndex())
                                     .arg(image->syncId()));
                    }
                }
                emit sonoZoomDataUpdate(image->layout(), image->systemScanMode(), pixRect, pix, ratio);
                setPaintFrameIndex(image->frameIndex());

                m_ImageBufferFrameIndex = image->imageBufferFrameIndex();
            }
        }
        else if ((debugFlag == info.m_ModeType + 1) ||
                 ((debugFlag == info.m_ModeType + 2) && (debugFlag == ImageEventArgs::ImageC + 1)))
        {
            painter->drawPixmap(m_DscImageRect, pix);
        }

        emit renderImageChanged(pix, info.m_ModeType, image->frameIndex());
    }
}

void ImagePainterRender::destroyImages(QMultiHash<ModeImageType, ImageRenderModel*>& images)
{
    if (!images.isEmpty())
    {
        qDeleteAll(images);
        images.clear();
    }
}

void ImagePainterRender::onSetSonoParameters()
{
    reorganizeImages();
}

ImageRenderModel* ImagePainterRender::findImageModel(int index)
{
    foreach (ImageRenderModel* image, m_Images)
    {
        if (image->index() == index)
        {
            return image;
        }
    }
    return NULL;
}

void ImagePainterRender::createImage(const ModeImageInfo& imageInfo)
{
    ImageRenderModel* image = new ImageRenderModel(m_ImageFormat);
    image->setModeImageInfo(imageInfo);
    // 设置图像下标
    image->setIndex(imageInfo.m_ImageIndex);
    SonoParameters* sp = m_MuiltSonoParametersBuffer->getSonoParametersByLayoutIndex(-1);
    if (sp == NULL)
    {
        sp = m_InfoGenerator.sonoParameters();
    }
    image->setLayout(sp->pIV(BFPNames::LayoutStr));
    image->setSystemScanMode(sp->pIV(BFPNames::SystemScanModeStr));
    image->setImageRenderPartition(sp->pIV(BFPNames::ImageRenderPartitionStr));
    m_Images.insert(imageInfo.m_ModeType, image);
    // 设置变换矩阵
    setOneImageTransform(image, m_InfoGenerator.imageTransform(imageInfo));
    setOneShaderOrthTransform(image, m_InfoGenerator.imageOrthTransform(imageInfo));
}

void ImagePainterRender::claAreaRect(QRectF area, qreal ratio, const bool doNotChangeCenter)
{
    m_AreaRectAfterTransform = area;
    QTransform transform;
    transform.scale(ratio, ratio);
    QPointF center = area.center();
    m_AreaRect = transform.mapRect(area.toRect());
    if (doNotChangeCenter)
    {
        m_AreaRect.moveCenter(center);
    }
}

void ImagePainterRender::setPaintFrameIndex(int index)
{
    QMutexLocker locker(&m_PaintFrameIndexMutex);
    m_PaintFrameIndex = index;
}
