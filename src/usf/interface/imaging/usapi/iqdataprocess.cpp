#include "iqdataprocess.h"
#include "pretreatmentpipleline.h"
#include "bytebuffer.h"
#include "iqdata.h"
#include <QThread>
#include <QDebug>

IQDataProcess::IQDataProcess(int queueMaxSize, PretreatmentPipleline* pipleLine, QObject* parent)
    : QThread(parent)
    , m_pushReady(false)
    , m_Queue(queueMaxSize)
    , m_Pipleline(pipleLine)
{
}

void IQDataProcess::enqueue(const IQData& data)
{
    QMutexLocker lock(&m_DataMutex);
    m_Queue.enqueue(data);
    m_pushReady = false;
    m_Cond.notify_all();
}

int IQDataProcess::queueSize()
{
    QMutexLocker lock(&m_DataMutex);
    return m_Queue.queueSize();
}

bool IQDataProcess::fullQueue()
{
    QMutexLocker lock(&m_DataMutex);
    return m_Queue.fullQueue();
}

IQData IQDataProcess::getDataByIndex(int index)
{
    QMutexLocker lock(&m_DataMutex);
    return m_Queue.getDataByIndex(index);
}

void IQDataProcess::run()
{
    // TODO need quit while loop.
    while (true)
    {
        QMutexLocker lock(&m_DataMutex);
        while (m_Queue.empty())
        {
            m_Cond.wait(&m_DataMutex);
        }
        IQData data = m_Queue.deQueue();
        if (!data.m_data.isNull())
        {
            m_Pipleline->pushData(data);
            //        m_pushReady = true;
        }
    }
}

DataQueue::DataQueue(int maxSize)
    : m_MaxSize(maxSize)
    , m_Rear(0)
    , m_Front(0)
    , m_EnqueueFlag(false)

{
    Q_ASSERT_X(maxSize > 0, "DataQueue::DataQueue", "Size must be greater than or equal to 0.");
    m_MaxSize = (m_MaxSize <= 0) ? 1 : m_MaxSize;
    m_Array = QVector<IQData>(m_MaxSize);
}

DataQueue::~DataQueue()
{
    for (int i = 0; i < m_Array.size(); i++)
    {
        m_Array[i].m_data.destroy();
    }
    m_Array.clear();
}

int DataQueue::queueSize() const
{
    return fullQueue() ? m_MaxSize : ((m_Rear - m_Front + m_MaxSize) % m_MaxSize);
}

void DataQueue::enqueue(const IQData& data)
{
    if (fullQueue())
    {
        clearQueue();
    }
    else
    {
        //        m_Array[m_Rear].copyFrom(data);
        m_Array[m_Rear] = data;
        m_Rear = (m_Rear + 1) % m_MaxSize;
        m_EnqueueFlag = true;
    }
}

IQData DataQueue::deQueue()
{
    if (empty())
    {
        return IQData();
    }
    else
    {
        int dequeueIndex = m_Front;
        m_Front = (m_Front + 1) % m_MaxSize;
        m_EnqueueFlag = false;
        return m_Array[dequeueIndex];
    }
}

void DataQueue::clearQueue()
{
    m_Front = 0;
    m_Rear = 0;
    m_EnqueueFlag = false;
}

IQData DataQueue::getDataByIndex(int index)
{
    if (validIndex(index))
    {
        return m_Array[index];
    }
    return IQData();
}
