#ifndef PRETREATMENTBUFMANAGER4PW_H
#define PRETREATMENTBUFMANAGER4PW_H

#include "preprocessbufmanagerbase.h"
#include "linedatahead.h"
#include <QObject>

class USF_INTERFACE_IMAGING_USAPI_EXPORT PreProcessBufManager4pw : public PreProcessBufManagerBase
{
    Q_OBJECT

protected:
    enum
    {
        MAXRCVSIZEPW = LINEDATAPACKAGESIZE + LINEDATAHEADLEN,
        PWCOUNT = 1
    }; //每个档位256个值,8个档位
    enum Type
    {
        PW,
        PWTDI,
        CW,
        TypeCount
    };

public:
    PreProcessBufManager4pw(PreProcessParaController* paraController, int dataQueueSize,
                            PretreatmentPipleline* pipleline);
    ~PreProcessBufManager4pw();

    virtual ByteBuffer processNewData(uchar* data, InfoForPostProcess& info, int dataType);
    virtual void initbuffer();
    virtual ByteBuffer getbufferbytebyframeindex(int fidx);
    virtual void resetallbyframeindex(int fidx);
    /**
     * @brief 将数据拷贝到对应帧的缓冲区
     * @param data 包含LineDataHead的数据
     * @param[out] lostframe 是否丢帧
     * @return 缓存ID,如果数据未满返回-1,错误返回-2
     */
    virtual int copytobufferbyframeindex(uint8_t* data, bool& lostframe) override;
    virtual void clear();
    virtual void adjustNonPipeline(QString bfpName, QVariant value);

protected:
    virtual void setLineInformation(SonoParameters* sonoParameters, int usblineSize);
    Type getType(int systemScanMode);
    virtual Type getWaveType()
    {
        return m_WaveType;
    };
public slots:
    virtual void onGettingControlTableValue(const QVariant& value, int& ct);

public:
    static int m_PWReceiveFPS;
    static int m_PWPreProcessAudioFPS;
    static int m_PWPreProcessWaveFPS;

protected:
    int m_AccumNum;
    int m_gateNo;

private:
    Type m_WaveType;
    uchar* m_PwPool;
    PackageInfoPW m_Controlbuffers;
};

#endif // PRETREATMENTBUFMANAGER4PW_H
