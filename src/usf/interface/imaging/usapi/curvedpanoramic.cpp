#include "curvedpanoramic.h"
#include "applicationinfo.h"
#include "bfpnames.h"
#include "bfscanwidthparameter.h"
#include <QMutexLocker>
#include <QtCore>
#include "istatemanager.h"
#include "stateeventnames.h"
#include "setting.h"
#include "sonoparameters.h"
#include "toolnames.h"
#include "util.h"
#include "iprocessedimagebufferdatasetter.h"
#include "frameinfo.h"
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, CurvedPanoramic);

CurvedPanoramic::CurvedPanoramic(QObject* parent)
    : QObject(parent)
    , m_CurvedWidth(0)
    , m_CurvedHeight(0)
    , m_NewimgWidth(0)
    , m_NewimgHeight(0)
    , m_MaxLength(200)
    , m_SaveData(nullptr)
    , m_IsStop(true)
    , m_IsRubbishData(true)
    , m_IsFinished(false)
    , m_StateManager(NULL)
{
    {
        //一次性传入初始参数	xThreshold_inOriImg --
        // x方向上移动多少位移，才做一次拼接的阈值，值分别为图像有效区域宽的多少分之一，建议取值45、65
        setParameter_xThreshold_inOriImg(45);
        //一次性传入初始参数	yThreshold_inOriImg --
        // y方向上移动多少位移，才做一次拼接的阈值，值分别为图像有效区域高的多少分之一，建议取值40、60
        setParameter_yThreshold_inOriImg(40);
        //一次性传入初始参数	mRANSAC_Threshold --
        // RANSAC步骤的关键参数，对变换矩阵的结果影响很大，建议取值0.05；不小于0.005，不超过0.1
        setParameter_mRANSAC_Threshold(0.05);
        //一次性传入初始参数	xBlurSize -- 全图高斯平滑，x方向平滑窗的大小，建议取3，可以为3、5、7、9
        setParameter_xBlurSize(3);
        //一次性传入初始参数	yBlurSize -- 全图高斯平滑，y方向平滑窗的大小，建议取3，可以为3、5、7、9
        setParameter_yBlurSize(3);
        //一次性传入初始参数	xSigma --
        //全图高斯平滑，x方向的sigma值，建议取值0.5；当xSigma取0时，既不做全图高斯平滑，也不做拼接缝的高斯平滑
        setParameter_xSigma(0);
        //一次性传入初始参数	ySigma -- 全图高斯平滑，y方向的sigma值，建议取值0.75；当ySigma取0时，不做全图高斯平滑
        setParameter_ySigma(0);
        //一次性传入初始参数	setCurveHeight -- 设定上下曲线与图像中点连线的距离与原图像高度一半的百分比，建议设定为90
        setParameter_CurveHeight(90);
        //一次性传入初始参数	PixelSizeMM -- 两个相邻像素之间的实际物理距离（毫米 mm），默认值0.0578
        setParameter_PixelSizeMM(0.0578);

        //一次性传入初始参数	EffectiveArea -- 真实有效区域的大小
        setParameter_EffectiveArea(274, 42, 630, 666);
        // setParameter_EffectiveArea(98, 0, 464, 502);
    }
}

CurvedPanoramic::~CurvedPanoramic()
{
    delete[] m_SaveData;
    m_SaveData = nullptr;
}

void CurvedPanoramic::initPanoParam(const int imgWidth, const int imgHeight)
{
    //一次性传入初始参数	imageWidth -- 输入图像宽度，以像素为单位
    setParameter_imageWidth(imgWidth);
    //一次性传入初始参数	imageHeight -- 输入图像高度，以像素为单位
    setParameter_imageHeight(imgHeight);
}

void CurvedPanoramic::getRealTimePanoramic(ImageEventArgs* src, DataArg& des)
{
    if (m_IsFinished)
    {
        return;
    }

    if (m_IsStop) // enter realtime curvedPanoramic
    {
        m_IsStop = false;
        setParameter_isStop(m_IsStop);
        emit curvedInfoChanged(0, false);
    }

    int length = 0;

    cv::Mat desImg;
    double scale = 0.0;
    if (!getOneFrameCurvedData(src, desImg, scale))
    {
        return;
    }

    des.setType(DataArg::P);
    des.copyFrom(desImg.data);

    double panoLength = GetPanoLength() > 0 ? GetPanoLength() : 0;

    bool showRoi = false;
    if (m_NewimgWidth != src->width()) // 算法开始拼接
    {
        showRoi = true;
        length = panoLength;
    }

    if (!desImg.empty())
    {
        int x0 = 0;
        int y0 = 0;
        int x1 = 0;
        int y1 = 0;
        int x2 = 0;
        int y2 = 0;
        int x3 = 0;
        int y3 = 0;
        GetPanoRect(x0, y0, x1, y1, x2, y2, x3, y3);

        QTransform transform;
        transform.translate(m_NewimgWidth / 2, m_NewimgHeight / 2);
        transform.scale(1 / scale, 1 / scale);
        transform.translate(-m_NewimgWidth / 2, -m_NewimgHeight / 2);
        int xOffset = (m_CurvedWidth / 2 - m_NewimgWidth / 2) * scale;
        int yOffset = (m_CurvedHeight / 2 - m_NewimgHeight / 2) * scale;
        transform.translate(xOffset, yOffset);
        QPoint leftTop = transform.map(QPoint(x0, y0));
        QPoint rightTop = transform.map(QPoint(x1, y1));
        QPoint rightBottom = transform.map(QPoint(x2, y2));
        QPoint leftBottom = transform.map(QPoint(x3, y3));
        QList<QVariant> pointList;
        pointList << leftTop << rightTop << rightBottom << leftBottom;

        if (length <= m_MaxLength)
        {
            emit curvedInfoChanged(length, showRoi, pointList);
        }
        else
        {
            setFinish();
        }
    }
}

void CurvedPanoramic::setCurvedSize(int width, int height)
{
    m_CurvedWidth = width;
    m_CurvedHeight = height;
    m_SaveData = new char[m_CurvedWidth * m_CurvedHeight];
}

bool CurvedPanoramic::saveResult(const QString& filePath)
{
    QFile curvedpanFile(filePath);
    if (curvedpanFile.open(QIODevice::WriteOnly))
    {
        curvedpanFile.write(m_SaveData, m_CurvedWidth * m_CurvedHeight);
        curvedpanFile.close();
        return true;
    }
    return false;
}

void CurvedPanoramic::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void CurvedPanoramic::setFinish(const bool isActive)
{
    if (!m_IsFinished && !isActive)
    {
        m_IsFinished = true;
        m_StateManager->postEvent(StateEventNames::Freeze);
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        return;
    }
    m_IsFinished = false;

    // get final imager
    m_IsStop = true;
    setParameter_isStop(m_IsStop);
    int newimgWidth = 0;
    int newimgHeight = 0;
    uchar* finalImgData = getImage(newimgWidth, newimgHeight); // 得到拼接图片

    int errorCode = GetErrCode();
    // 未上数据导致算法没有拼接返回的错误码
    // 此时的应对策略为，保持原有冻结图像
    if (errorCode == 206)
    {
        if (m_IsRubbishData)
        {
            return;
        }
        log()->error() << PRETTY_FUNCTION << "curvedPanormic algorithm return erro code : " << errorCode;
    }

    qreal scale = std::min((static_cast<double>(m_CurvedWidth) / newimgWidth),
                           static_cast<double>(m_CurvedHeight) / newimgHeight);
    emit updatePixelSizeMM(m_OriginalPixelSizeMM / scale);

    if (finalImgData != NULL)
    {
        cv::Mat result(newimgHeight, newimgWidth, CV_8UC1, finalImgData);
        cv::Mat resultMat = resizeImg2ScreenSize(result, scale);

        setSaveData((char*)resultMat.data);

        emit curvedPanormicFinished((uchar*)m_SaveData);
        m_IsRubbishData = true;
    }
}

bool CurvedPanoramic::getOneFrameCurvedData(const ImageEventArgs* src, cv::Mat& resultMat, double& scale)
{
    // 注册用，临时修改
    cv::Mat RealTimePano(src->width(), src->height(), CV_8UC3, src->imageData());
    resultMat = /*resizeImg2ScreenSize(RealTimePano, scale);*/ RealTimePano;
    return true;

    m_IsRubbishData = false;
    uchar* realImage = RealTimePanoramic(src->imageData(), m_NewimgWidth, m_NewimgHeight);
    int errorCode = GetErrCode();
    // 运行中累积的错误次数过多(暂定20次)，强制退出，外部强制冻结
    if (errorCode == 222)
    {
        log()->error() << PRETTY_FUNCTION << "curvedPanormic algorithm return erro code : " << errorCode;
        setFinish();
        return false;
    }
    if (errorCode == 210)
    {
        log()->error() << PRETTY_FUNCTION << "curvedPanormic algorithm return erro code : " << errorCode;
        setFinish();
        return false;
    }
    if (realImage != NULL)
    {
        cv::Mat RealTimePano(m_NewimgHeight, m_NewimgWidth, CV_8UC1, realImage /*, m_NewimgWidth*/);
        resultMat = resizeImg2ScreenSize(RealTimePano, scale);

        return true;
    }

    return false;
}

cv::Mat CurvedPanoramic::resizeImg2ScreenSize(const cv::Mat& originImage, double& scale)
{
    int originWidth = originImage.cols;
    int originHeight = originImage.rows;
    cv::Mat imgMat;
    if ((originWidth < m_CurvedWidth) && (originHeight < m_CurvedHeight))
    {
        imgMat = originImage;
        scale = 1.0;
    }
    else
    {
        double nScale_x = originWidth / 1.0 / m_CurvedWidth;
        double nScale_y = originHeight / 1.0 / m_CurvedHeight;
        scale = cv::max(nScale_x, nScale_y);
        cv::resize(originImage, imgMat, cv::Size(originWidth / scale, originHeight / scale));
    }
    cv::Mat desImg(m_CurvedHeight, m_CurvedWidth, CV_8UC1, cv::Scalar(0, 0, 0));
    int x = (desImg.cols - imgMat.cols) / 2;
    int y = (desImg.rows - imgMat.rows) / 2;
    cv::Mat imageROI = desImg(cv::Rect(x, y, imgMat.cols, imgMat.rows));
    imgMat.copyTo(imageROI);
    return desImg;
}

void CurvedPanoramic::clear()
{
    //由clear操作引起的退出动态宽景崩溃,是因为动态宽景的处理在子线程中,clear函数在主线程,且宽景
    //不支持多线程同时操作,未使用线程锁是因为clear之后可能仍会有待处理的动态宽景,目前使用
    //标志位控制动态宽景处理,当m_CurvedEnable为false,不进行宽景拼接,动态宽景返回null的Image,
    //然后清理数据
    m_NewimgWidth = 0;
    m_NewimgHeight = 0;
}

void CurvedPanoramic::setSaveData(char* saveData)
{
    memcpy(m_SaveData, saveData, m_CurvedWidth * m_CurvedHeight);
}

bool CurvedPanoramic::isStop() const
{
    return m_IsStop;
}

qreal CurvedPanoramic::originalPixelSizeMM()
{
    return m_OriginalPixelSizeMM;
}

void CurvedPanoramic::setOriginalPixelSizeMM(qreal pixelSizeMM)
{
    m_OriginalPixelSizeMM = pixelSizeMM;
    setParameter_PixelSizeMM(pixelSizeMM);
}
