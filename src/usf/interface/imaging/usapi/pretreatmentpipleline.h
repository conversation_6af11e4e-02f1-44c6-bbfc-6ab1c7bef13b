#ifndef PRETREATMENTPIPLELINE_H
#define PRETREATMENTPIPLELINE_H
#include "zeusparameternames.h"
#include "infostruct.h"
#include "linedatahead.h"
#include "lineimagedatainfostruct.h"
#include "usapi_global.h"
#include "zeusinfostruct.h"
#include <QHash>
#include <QMutex>
#include <QObject>

#define MAX_PREPUSH_DATA_NUMBER 16 //积累次数最大8， iq最大为16

class ZeusAPIInterface;
class IImageSaveHelper;
class ByteBuffer;
class FpsCalculator;
struct IQData;
class PretreatmentPipleline : public QObject
{
    Q_OBJECT
public:
    enum
    {
        SyncID = 1,
        HeadInfoStart = 10, // zeus前处理info从第10个int开始存储数据包头
        LineNum = HeadInfoStart + LINEDATAHEADLEN,
        LineSize = LineNum + 1
    };

    explicit PretreatmentPipleline(int index, ZeusAPIInterface* api, IImageSaveHelper* imageSaveHelper);

    bool pushData(const IQData& data);
    void setPwPrf(int value);

protected:
    bool pushData4Corlor(const IQData& data);
    bool pushData4Pw(const IQData& data);
    //    bool pushData4Cw(const IQData& data);
    bool pushData4Wave(const IQData& data, BFDataType type);
    void updateParas();
    void onPreProcessedData(void* data, int width, int height, int dataType, int bitWidth, InfoForPostProcess info);
    static void onZeusPreProcessedData(void* userData, CallbackInfo callbackInfo, InfoForPostProcess info);

signals:
    void onNewColorData(uchar* data, InfoForPostProcess& info, int dataType);
    void onNewPWData(uchar* data, InfoForPostProcess& info, int dataType);
    void onNewSoundData(uchar* data, InfoForPostProcess& info, int dataType);

private:
    int m_ActiveIndex; //当前激活区域索引

    ZeusAPIInterface* m_ZeusAPIInterface;
    IImageSaveHelper* m_ImageSaveHelper;
    QHash<QString, QVariant> m_Values;
    typedef struct
    {
        BFDataType DataType;
        DataForPreProcess DataPostProcess[MAX_PREPUSH_DATA_NUMBER];
    } DataPreProcessInfo;
};

#endif // PRETREATMENTPIPLELINE_H
