#include "preprocessbufmanager4colorwithoutframeno.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "probeparameters.h"
#include "generalinfo.h"
#include "setting.h"
#include "iqdata.h"

LOG4QT_DECLARE_STATIC_LOGGER(log4color, PreProcessBufManager4colorWithoutFrameNo)

PreProcessBufManager4colorWithoutFrameNo::PreProcessBufManager4colorWithoutFrameNo(
    PreProcessParaController* paraController, PretreatmentPipleline* pipleline)
    : PreProcessBufManager4color(paraController, 20, pipleline)
{
}

int PreProcessBufManager4colorWithoutFrameNo::copytobufferbyframeindex(uint8_t* data, bool& lostframe)
{
    LineDataHead* lineDataHead = (LineDataHead*)data;
    // 固定使用一块buffer
    int cacheid = 0;
    int lineCountPerPackage = LineData::validLineNumPerPack(lineDataHead->LineType);
    int lineNo = lineDataHead->getLineNo();
    int lineAccNo = lineDataHead->getIQAccumulativeTimes();
    int lineIQFlag = lineDataHead->getIQFlag();
    int frameNo = lineDataHead->getFrameNo();

    if (Setting::instance().defaults().isIODeviceVirtual())
    {
        if (((lineNo % m_Linegap) != 0) || (lineNo < m_Startline) || (lineNo > m_Stopline))
        {
            return -2;
        }
    }

    if ((lineNo < m_Startline || lineNo > m_Stopline) || (lineAccNo >= m_PacketSize))
    {
        return -2;
    }

    int colorDataSize = (m_Linesize - LINEDATAHEADLEN);
    // int colorDataSize = lineCountPerPackage * m_Pointsize * 2;

    int lineidx = ((lineNo - m_Startline) / m_Linegap);
    int iqPieceSize = m_Pointsize * 2 * m_Linenum; //所有的线数据的I为一组 Q为一组

    // 该位置是按ACCI1ACCQ1ACCI2ACCQ2 format 进行填充buffer
    int pos = frameNo * iqPieceSize + lineidx * m_Pointsize * 2;

    memcpy(&m_Controlbuffers[cacheid].m_Buffertpool[pos], &data[LINEDATAHEADLEN], colorDataSize);

    //  qCritical() << PRETTY_FUNCTION
    //          << ", cacheid: " << cacheid
    //          << ", frameNo: " << frameNo
    //          << ", lineNo: " << lineNo
    //          << ", lineidx: " << lineidx
    //          << ", lineColorIQFlag" << lineIQFlag
    //          << ", lineColorAccumulative:" << lineAccNo
    //          << ", m_PacketSize:" << m_PacketSize
    //          << ", colordatasize:" << colorDataSize
    //          << ", m_Startline:" << m_Startline
    //          << ", m_Stopline:" << m_Stopline
    //          << ", m_Linegap:" << m_Linegap
    //          << ", colorAccumEnd: " << lineDataHead->ColorAccumEnd
    //          << ", nouse frameFlag: " << lineDataHead->FrameFlag
    //          << ", isLostframe: " << lostframe;

    if ((m_Stopline == lineNo) && lineIQFlag && lineDataHead->ColorAccumEnd) //血流结束标志
    {

        memcpy(m_Controlbuffers[cacheid].m_Buffertpool, data, LINEDATAHEADLEN); // copy head at the last time
        m_Pipleline->pushData(IQData(
            ByteBuffer(m_Controlbuffers[cacheid].m_Buffertpool, m_PacketSize * iqPieceSize * 2 + LINEDATAHEADLEN),
            CData, m_Colorpointsize, m_Linenum, m_PacketSize, 1, IQFormat4Zeus::ACCI1ACCQ1ACCI2ACCQ2));
    }

    return -1;
}

ByteBuffer PreProcessBufManager4colorWithoutFrameNo::processNewData(uchar* data, InfoForPostProcess& info, int dataType)
{
    uchar* head = (uchar*)&info.info[PretreatmentPipleline::HeadInfoStart];
    LineDataHead* lineDataHead = (LineDataHead*)head;

    bool isReceive = (m_Linenum == info.info[PretreatmentPipleline::LineNum]);
    bool isShortData = (dataType == BFDataType::DynamicFlow); // MVI: true,  Other: false
    int linesPerPacket = isShortData ? 2 : 1;
    int colordatasize = m_Pointsize * (isShortData ? sizeof(uchar) : sizeof(short));

    int realstopline = m_Stopline;
    int iqCacheId = 0;

    for (int j = m_Startline, i = 0; j <= realstopline; i++)
    {
        // line number
        lineDataHead->setLineNo(j);

        // frame flag
        if (j == m_Startline)
        {
            head[7] = 0;
        }
        else if (j != realstopline)
        {
            head[7] = 1;
        }
        else
        {
            // qCritical()<<"GXC ending flag";
            // qCritical() << "GXC lineNo: " << lineDataHead->getLineNo();
            head[7] = 2;
        }

        // 对于一个像素8bit的数据(目前仅MVI)，为适配后续线数据的入队操作，参照B数据的处理方式，将每两条线(带前一条线的数据头)放入一个1040Byte的线数据包中
        bool isNewPacket = (!isShortData || (i % 2 == 0)); // 8bit数据时，放入线数据的后一条线为false
        int packetIndex = i / linesPerPacket;

        // copy head
        if (isNewPacket)
        {
            memcpy(&m_Controlbuffers[iqCacheId].m_Buffertpool[m_Linesize * packetIndex], head, LINEDATAHEADLEN);
        }
        else if (
            j ==
            realstopline) // 最后一条线如果不是新包（MVI这样的一个包两条线的场景），包头用的是倒数第二条线的信息头，没有最后一条线的标志，此处补上该标志。
        {
            m_Controlbuffers[iqCacheId].m_Buffertpool[m_Linesize * packetIndex + 7] = 2;
        }

        if (isReceive)
        {
            // copy data
            int offset = m_Linesize * packetIndex + LINEDATAHEADLEN + (isNewPacket ? 0 : 1) * colordatasize;
            memcpy(&m_Controlbuffers[iqCacheId].m_Buffertpool[offset], &data[i * colordatasize], colordatasize);
        }
        else
        {
            qCritical() << "PreProcessBufManager4color::processNewData  LineNum is not same as m_LineNum";
        }

        j++;
    }

    // qCritical() << PRETTY_FUNCTION << "GXC: m_Linesize"<<m_Linesize<<",m_RealPackNum"<<m_RealPackNum;
    return ByteBuffer((uchar*)m_Controlbuffers[iqCacheId].m_Buffertpool,
                      m_Linesize * (isShortData ? (m_RealPackNum + 1) / 2 : m_RealPackNum));
}
