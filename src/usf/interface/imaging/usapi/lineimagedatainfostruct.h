#ifndef LINEIMAGEDATAINFOSTRUCT_H
#define LINEIMAGEDATAINFOSTRUCT_H

#include <QObject>
#include <condition_variable>
#include "logger.h"
typedef unsigned char uint8_t;
#define CACHESIZE 2
#define MAXCOLORFRAMES 16 //彩色最大积累次数
#define LINEDATAHEADLEN 16

#ifdef USE_PW_PROCESS_1024
#define LINEDATAPACKAGESIZE 1024
#else
#define LINEDATAPACKAGESIZE 1440
#endif

#define CWLINEDATAPACKAGESIZE 1024
#define CWSENDCYCLENUM 20

#define PWQUEUESIZE 3000

#define PW_PUSH_SIZE 50

#define CW_POINT_NUM 15120

LOG4QT_DECLARE_STATIC_LOGGER(log4packinfo, PackageInfo)

enum COLORPOINTSIZE
{
    COLOR512 = 512,
    COLOR256 = 256
};

// pw params
static long long WALLFILTER[256] = {0LL,           0LL,           0LL,           0LL,
                                    0LL,           0LL,           0LL,           0LL,
                                    0LL,           0LL,           0LL,           0LL,
                                    0LL,           0LL,           0LL,           0LL,
                                    0LL,           0LL,           0LL,           -1LL,
                                    -1LL,          -2LL,          -3LL,          -4LL,
                                    -5LL,          -7LL,          -8LL,          -8LL,
                                    -6LL,          1LL,           15LL,          40LL,
                                    84LL,          153LL,         257LL,         407LL,
                                    615LL,         890LL,         1241LL,        1666LL,
                                    2154LL,        2670LL,        3155LL,        3507LL,
                                    3575LL,        3146LL,        1933LL,        -431LL,
                                    -4398LL,       -10494LL,      -19301LL,      -31419LL,
                                    -47406LL,      -67693LL,      -92471LL,      -121544LL,
                                    -154160LL,     -188807LL,     -223008LL,     -253106LL,
                                    -274071LL,     -279364LL,     -260872LL,     -208969LL,
                                    -112732LL,     39632LL,       260131LL,      560052LL,
                                    948746LL,      1432121LL,     2010892LL,     2678611LL,
                                    3419616LL,     4206985LL,     5000690LL,     5746150LL,
                                    6373406LL,     6797168LL,     6917993LL,     6624815LL,
                                    5799034LL,     4320293LL,     2073978LL,     -1039605LL,
                                    -5094603LL,    -10127869LL,   -16126616LL,   -23016124LL,
                                    -30648320LL,   -38792133LL,   -47126641LL,   -55238013LL,
                                    -62621242LL,   -68687537LL,   -72778069LL,   -74184492LL,
                                    -72176362LL,   -66035162LL,   -55094245LL,   -38783559LL,
                                    -16677603LL,   11455327LL,    45604824LL,    85473434LL,
                                    130437552LL,   179516549LL,   231352152LL,   284199653LL,
                                    335931732LL,   384054723LL,   425735884LL,   457838621LL,
                                    476960579LL,   479466740LL,   461505705LL,   418991250LL,
                                    347521029LL,   242186039LL,   97189296LL,    -94879850LL,
                                    -344427822LL,  -667585851LL,  -1091975036LL, -1670194125LL,
                                    -2516425099LL, -3930814520LL, -7021136286LL, -21783369257LL,
                                    21783369257LL, 7021136286LL,  3930814520LL,  2516425099LL,
                                    1670194125LL,  1091975036LL,  667585851LL,   344427822LL,
                                    94879850LL,    -97189296LL,   -242186039LL,  -347521029LL,
                                    -418991250LL,  -461505705LL,  -479466740LL,  -476960579LL,
                                    -457838621LL,  -425735884LL,  -384054723LL,  -335931732LL,
                                    -284199653LL,  -231352152LL,  -179516549LL,  -130437552LL,
                                    -85473434LL,   -45604824LL,   -11455327LL,   16677603LL,
                                    38783559LL,    55094245LL,    66035162LL,    72176362LL,
                                    74184492LL,    72778069LL,    68687537LL,    62621242LL,
                                    55238013LL,    47126641LL,    38792133LL,    30648320LL,
                                    23016124LL,    16126616LL,    10127869LL,    5094603LL,
                                    1039605LL,     -2073978LL,    -4320293LL,    -5799034LL,
                                    -6624815LL,    -6917993LL,    -6797168LL,    -6373406LL,
                                    -5746150LL,    -5000690LL,    -4206985LL,    -3419616LL,
                                    -2678611LL,    -2010892LL,    -1432121LL,    -948746LL,
                                    -560052LL,     -260131LL,     -39632LL,      112732LL,
                                    208969LL,      260872LL,      279364LL,      274071LL,
                                    253106LL,      223008LL,      188807LL,      154160LL,
                                    121544LL,      92471LL,       67693LL,       47406LL,
                                    31419LL,       19301LL,       10494LL,       4398LL,
                                    431LL,         -1933LL,       -3146LL,       -3575LL,
                                    -3507LL,       -3155LL,       -2670LL,       -2154LL,
                                    -1666LL,       -1241LL,       -890LL,        -615LL,
                                    -407LL,        -257LL,        -153LL,        -84LL,
                                    -40LL,         -15LL,         -1LL,          6LL,
                                    8LL,           8LL,           7LL,           5LL,
                                    4LL,           3LL,           2LL,           1LL,
                                    1LL,           0LL,           0LL,           0LL,
                                    0LL,           0LL,           0LL,           0LL,
                                    0LL,           0LL,           0LL,           0LL,
                                    0LL,           0LL,           0LL,           0LL,
                                    0LL,           0LL,           0LL,           0};
static unsigned char WINDOWFILTER[256] = {
    0,   2,   6,   14,  24,  37,  53,  70,  88,  108, 127, 147, 167, 185, 202, 218, 231, 241, 249, 253, 255, 253,
    249, 241, 231, 218, 202, 185, 167, 147, 127, 108, 88,  70,  53,  37,  24,  14,  6,   2,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0};
static int SOUNDFILTER[256] = {
    -2, -62,   1, -24,   0, -29,   0, -34,   0, -40,   0, -46,    0,  -53,    0, -61,    0, -70,    0, -79,   0, -90,
    0,  -101,  0, -113,  0, -127,  0, -141,  0, -157,  0, -174,   0,  -192,   0, -212,   0, -233,   0, -255,  0, -280,
    0,  -306,  0, -333,  0, -363,  0, -395,  0, -429,  0, -465,   0,  -504,   0, -546,   0, -590,   0, -637,  0, -687,
    0,  -741,  0, -799,  0, -860,  0, -926,  0, -997,  0, -1073,  0,  -1155,  0, -1243,  0, -1339,  0, -1442, 0, -1555,
    0,  -1678, 0, -1813, 0, -1962, 0, -2127, 0, -2311, 0, -2518,  0,  -2754,  0, -3024,  0, -3337,  0, -3707, 0, -4149,
    0,  -4690, 0, -5369, 0, -6250, 0, -7443, 0, -9154, 0, -11829, 0,  -16623, 0, -27775, 0, -83430, 0, 83430, 0, 27775,
    0,  16623, 0, 11829, 0, 9154,  0, 7443,  0, 6250,  0, 5369,   0,  4690,   0, 4149,   0, 3707,   0, 3337,  0, 3024,
    0,  2754,  0, 2518,  0, 2311,  0, 2127,  0, 1962,  0, 1813,   0,  1678,   0, 1555,   0, 1442,   0, 1339,  0, 1243,
    0,  1155,  0, 1073,  0, 997,   0, 926,   0, 860,   0, 799,    0,  741,    0, 687,    0, 637,    0, 590,   0, 546,
    0,  504,   0, 465,   0, 429,   0, 395,   0, 363,   0, 333,    0,  306,    0, 280,    0, 255,    0, 233,   0, 212,
    0,  192,   0, 174,   0, 157,   0, 141,   0, 127,   0, 113,    0,  101,    0, 90,     0, 79,     0, 70,    0, 61,
    0,  53,    0, 46,    0, 40,    0, 34,    0, 29,    0, 24,     -1, 62};
// static int PWCOUNT = 1;
typedef void (*PostProcessPwlineDataCallBack)(void* userData, void* data, int dataSize);
static bool m_closeaudio = false;
;

//////////////////////
struct PackageInfo
{
    PackageInfo();

    int m_FrameIndex;
    int m_LineCount;
};
//---------------------Bmode------------------------------
struct PackageInfoB : PackageInfo
{
    PackageInfoB();

    uchar* m_Buffertp;
};

using PackageInfoPreOutC = PackageInfoB;

//---------------------color------------------------------
struct PackageInfoC : PackageInfo
{
    PackageInfoC();

    PackageInfo m_ColorUnitAssembled[MAXCOLORFRAMES][2];
    uchar* m_Buffertpool; // iq 数据存放 连续

    bool m_Iqbuffer;      //是否是处理的iqbuffer
    int m_Colorlen;       //血流线数据大小
    int m_Currentfridx;   //当前iqprocess处理的frameindex
    double m_Timeconsume; // iq处理时间s
    int m_LineNumCurt;
};

//---------------------pw------------------------------
struct PackageInfoPW : PackageInfo
{
    PackageInfoPW();

    uchar* m_BufferForWave;
    uchar* m_BufferForSound;
};
using PackageInfoCW = PackageInfoPW;

/////////////////////////

#endif // LINEIMAGEDATAINFOSTRUCT_H
