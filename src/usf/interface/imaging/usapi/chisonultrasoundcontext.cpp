/*
 * =====================================================================================
 *
 *       Filename:  chisonultrasoundcontext.cpp
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  12/10/2014 04:16:35 PM
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:   (), |<EMAIL>|
 *   Organization:
 *
 * =====================================================================================
 */

#include "chisonultrasoundcontext.h"
#include "assertlog.h"
#include "bfpnames.h"
#include "bufferpushcontrolers.h"
#include "bytelineimageargs.h"
#include "exammodepresethandlerfactory.h"
#include "fileframememory.h"
#include "fpscalculator.h"
#include "functionhandlerfactory.h"
#include "ibufferpushcontroler.h"
#include "icineloopbuffer.h"
#include "iexammodepresetdatahandler.h"
#include "ilinebuffermanager.h"
#include "imagebuffergroup.h"
#include "imagecircularqueuebuffer.h"
#include "imagedatareceiverswitcher.h"
#include "imageeventargs.h"
#include "imageframeinfoswitchers.h"
#include "imagememorypool.h"
#include "iimagesavehelper.h"
#include "infostruct.h"
#include "iprocessedimagebufferdatasetter.h"
#include "linebuffermanager.h"
#include "linecinelooper.h"
#include "linedatahead.h"
#include "lineglpreprocessingcreater.h"
#include "linehwinfo2wholehwinfo.h"
#include "lineimageargs.h"
#include "lineimagebuffer.h"
#include "logger.h"
#include "memoryleakcheck.h"
#include "parameter.h"
#include "postgeneralrawdata.h"
#include "probedataset.h"
#include "setting.h"
#include "sonobuffer.h"
#include "sonobuffers.h"
#include "sonoparameters.h"
#include "stressechobuffer.h"
#include "stressechobuffermanager.h"
#include "systemscanmodeclassifier.h"
#include "wholeimagecinelooper.h"
#include <QApplication>
#include <QDir>
#include <QImage>
#include <QMetaType>
#include "syncidmanager.h"
#ifdef USE_VA
#include "vafunctionhandler.h"
#endif
#include "abstractdscpipeline.h"
#include "dscthread.h"
#include "zeusapihelper.h"
#include "zeuspipeline.h"
//#include "imagedebughelper.h"
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
#ifdef USE_PANORAMIC
#include "curvedpanoramic.h"
#endif
#include <QOffscreenSurface>
#include <QOpenGLContext>
#endif
#include "resourcehelper.h"
#ifdef USE_4D
#include "fourdvolumelineimageargs.h"
#endif
#include "istatemanager.h"
#include "presetmodeparametersynconfrozen.h"
#include "stateeventnames.h"

#ifdef USE_AUTOFREEZE
#include "framecontrol.h"
#endif

#include "basebfstaticparameters.h"
LOG4QT_DECLARE_STATIC_LOGGER(log, ChisonUltrasoundContext)

ChisonUltrasoundContext::ChisonUltrasoundContext(IImageSaveHelper* imageSaveHelper,
                                                 ChisonUltrasound::ChisonUltrasoundMode type,
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
                                                 QGLWidget* shareGlWidget,
#else
                                                 QOpenGLContext* shareGlWidget,
#endif
                                                 QObject* parent)
    : QObject(parent)
    , m_BufferPushControlers(NULL)
    , m_ShareGlWidget(shareGlWidget)
    , m_DSCPipeline(NULL)
    , m_DscThread(NULL)
    , m_ZeusAPIInterface(NULL)
    , m_LineGlPreprocessing(NULL)
    , m_ChisonUltrasoundMode(type)
    , m_SonoBuffers(NULL)
    , m_DataReceiverSwitcher(NULL)
    , m_BFStaticParameters(nullptr)
    , m_FrameInfoSwitchers(NULL)
    , m_CineLooper(NULL)
    , m_pipelineFinished(true)
    , m_WholeDataHWInfo(NULL)
    , m_LineBufferManager(NULL)
    , m_StressEchoManager(NULL)
#ifdef USE_PANORAMIC
    , m_CurvedPanoramic(NULL)
    , m_SendData(nullptr)
#endif
    , m_IsInStressEchoAnalyzeState(false)
    , m_SystemScanMode(SystemScanModeB)
    , m_IsPaused(false)
    , m_FpsCalculator(new FpsCalculator)
    , m_CurvedImageData(new ImageCircularQueueBuffer)
    , m_ProbeChanged(false)
    , m_ProbeID(-1)
    , m_LineImageDebug(0)
    , m_LineColorMapDebug(0)
    , m_PausedLayoutIndex(0)
    , m_WaitForFrozen(false)
    , m_WaitingForFrozen(false)
    , m_Prepare2LoadImage(false)
    , m_DSCChanging(false)
    , m_PresetModeParameterSyncOnFrozen(NULL)
    , m_isStaticCuvedPanomicState(false)
    , m_StateManager(NULL)
    , m_ImageSaveHelper(imageSaveHelper)
{
    qRegisterMetaType<ByteBuffer>("ByteBuffer");
    qRegisterMetaType<ByteBuffer*>("ByteBuffer*");
    qRegisterMetaType<ICineLoopBuffer*>("ICineLoopBuffer*");
    qRegisterMetaType<LineImageArgs*>("LineImageArgs*");
    createSonoBuffers();
    createBufferPushControlers();
    createBufferManager();
    createCineLooper();
    createDSCThread();
    createZeusAPIHelper();
    createZeusPipeline();
    createGlPreprocessing();
    createImageDataReceiverSwitcher();
    createImageFrameInfoSwitchers();
    createStressEchoManager();
#ifdef USE_PANORAMIC
    m_CurvedPanoramic = new CurvedPanoramic(this);
    connect(m_CurvedPanoramic, &CurvedPanoramic::curvedPanormicFinished, this,
            &ChisonUltrasoundContext::onCurvedPanormicFinished);
    connect(m_CurvedPanoramic, &CurvedPanoramic::updatePixelSizeMM, this,
            &ChisonUltrasoundContext::onUpdatePixelSizeMM);
#endif
    connect(this, SIGNAL(closeRequested()), this, SLOT(onClose()));

    m_FpsCalculator->setLogOut(true);
    m_FpsCalculator->setAdditionInfo("ChisonUltrasoundContext -- Out");
    FunctionHandlerFactory::instance().initFunctionHandlerFactory();
#ifdef USE_VA
    createVAFunctionHandler();
#endif
}

ChisonUltrasoundContext::~ChisonUltrasoundContext()
{
    disconnect(this);
    onBeginPaused();
    while (m_WaitingForFrozen || m_WaitForFrozen)
    {
        // Wait for freezing finished.
        QCoreApplication::instance()->processEvents();
        Util::usleep(1000);
    }

    closeRequested();

    while (m_DscThread->isRunning())
    {
        QCoreApplication::instance()->processEvents();
        // must run manual loop to wait
        // gst pipeline finished signal
        log()->trace("waiting gst thread finished");

        Util::usleep(1000);
    }

    FunctionHandlerFactory::instance().setSonoParameters(NULL);
    if (m_SonoBuffers != NULL)
    {
        CHECK_DELETE(SonoBuffers, m_SonoBuffers);
        delete m_SonoBuffers;
        m_SonoBuffers = NULL;
    }

    if (m_FrameInfoSwitchers != NULL)
    {
        CHECK_DELETE(ImageFrameInfoSwitchers, m_FrameInfoSwitchers);
        delete m_FrameInfoSwitchers;
        m_FrameInfoSwitchers = NULL;
    }
    if (m_DataReceiverSwitcher != NULL)
    {
        CHECK_DELETE(ImageDataReceiverSwitcher, m_DataReceiverSwitcher);
        delete m_DataReceiverSwitcher;
        m_DataReceiverSwitcher = NULL;
    }

    if (m_BufferPushControlers != NULL)
    {
        CHECK_DELETE(BufferPushControlers, m_BufferPushControlers);
        delete m_BufferPushControlers;
        m_BufferPushControlers = NULL;
    }

    if (m_SonoBuffers != NULL)
    {
        CHECK_DELETE(SonoBuffers, m_SonoBuffers);
        delete m_SonoBuffers;
        m_SonoBuffers = NULL;
    }

    if (m_WholeDataHWInfo != NULL)
    {
        CHECK_DELETE(BaseWholeDataHWInfoManage, m_WholeDataHWInfo);
        delete m_WholeDataHWInfo;
        m_WholeDataHWInfo = NULL;
    }

    if (m_LineGlPreprocessing != NULL)
    {
        delete m_LineGlPreprocessing;
        m_LineGlPreprocessing = NULL;
    }

    if (m_LineBufferManager != NULL)
    {
        CHECK_DELETE(LineBufferManager, m_LineBufferManager);
        delete m_LineBufferManager;
        m_LineBufferManager = NULL;
    }

    if (m_CineLooper != NULL)
    {
        CHECK_DELETE(LineCineLooper, m_CineLooper);
        delete m_CineLooper;
        m_CineLooper = NULL;
    }
    // dicomscu和mov2mpg退出时析构m_GstPipeline会崩溃，这里先注释掉 add by sjh
    if (m_DSCPipeline != NULL)
    {
        CHECK_DELETE(AbstractDSCPipeline, m_DSCPipeline);
        delete m_DSCPipeline;
        m_DSCPipeline = NULL;
    }

    if (m_DscThread != NULL)
    {
        CHECK_DELETE(DscThread, m_DscThread);
        delete m_DscThread;
        m_DscThread = NULL;
    }
    if (m_ZeusAPIInterface != NULL)
    {
        CHECK_DELETE(AbstractDSCPipeline, m_ZeusAPIInterface);
        delete m_ZeusAPIInterface;
        m_ZeusAPIInterface = NULL;
    }
#ifdef USE_PANORAMIC
    if (m_CurvedPanoramic != NULL)
    {
        delete m_CurvedPanoramic;
        m_CurvedPanoramic = NULL;
    }
    if (m_SendData != nullptr)
    {
        delete[] m_SendData;
        m_SendData = nullptr;
    }
#endif
    if (m_FpsCalculator != NULL)
    {
        delete m_FpsCalculator;
        m_FpsCalculator = NULL;
    }

    if (m_CurvedImageData != NULL)
    {
        delete m_CurvedImageData;
        m_CurvedImageData = NULL;
    }
    if (m_PresetModeParameterSyncOnFrozen != NULL)
    {
        delete m_PresetModeParameterSyncOnFrozen;
    }
#ifdef USE_AUTOFREEZE
    if (nullptr != m_DataForAutoFreeze)
    {
        free(m_DataForAutoFreeze);
        m_DataForAutoFreeze = nullptr;
    }
#endif
}

void ChisonUltrasoundContext::createSonoBuffers()
{
    /*根据建立模式的不同，配置不同的内存大小*/
    if (m_ChisonUltrasoundMode == ChisonUltrasound::ChisonUltrasound_AVITransform)
    {
        ImageMemoryPool::instance().createMemory(1024 * 1024 * 128);
        m_SonoBuffers = new SonoBuffers(&ImageMemoryPool::instance(), false, m_ImageSaveHelper, this);
    }
    else
    {
        // mem size could be load from setting file
        size_t memSize = 1024 * 1024 * Setting::instance().defaults().cineCacheMemSize();
        ImageMemoryPool::instance().createMemory(memSize);
        m_SonoBuffers = new SonoBuffers(&ImageMemoryPool::instance(), true, m_ImageSaveHelper, this);
    }
    CHECK_NEW(SonoBuffers, m_SonoBuffers);
    connect(m_SonoBuffers, SIGNAL(sonoParametersChanged()), this, SLOT(onBufferSonoParametersChanged()),
            Qt::DirectConnection);
    connect(m_SonoBuffers, SIGNAL(activeIndexChanged(int)), this, SLOT(onBufferSonoParametersChanged()),
            Qt::DirectConnection);
    connect(m_SonoBuffers, SIGNAL(staticImage(ImageEventArgs*)), this, SLOT(onStaticImage(ImageEventArgs*)),
            Qt::DirectConnection);
    connect(m_SonoBuffers, SIGNAL(beginPaused()), this, SLOT(onBeginPaused()), Qt::DirectConnection);
    connect(m_SonoBuffers, SIGNAL(endPaused()), this, SLOT(onEndPaused()), Qt::DirectConnection);
    connect(m_SonoBuffers, SIGNAL(fpsChanged(const QString&, const float)), this,
            SIGNAL(fpsChanged(const QString&, const float)), Qt::DirectConnection);

    connect(m_SonoBuffers, SIGNAL(beforeLayoutChanged()), this, SLOT(onBeforeLayoutChanged()), Qt::DirectConnection);
    connect(m_SonoBuffers, SIGNAL(layoutChanged(int)), this, SLOT(onLayoutChanged()), Qt::DirectConnection);
    connect(m_SonoBuffers, SIGNAL(pictureImage(QImage)), this, SLOT(onNewPictureImage(QImage)), Qt::DirectConnection);

    //    connect(m_SonoBuffers, SIGNAL(hasBuffer()), this, SLOT(onHasBuffer()), Qt::DirectConnection);
}
#ifdef USE_VA
void ChisonUltrasoundContext::createVAFunctionHandler()
{
    if (m_ChisonUltrasoundMode != ChisonUltrasound::ChisonUltrasound_AVITransform && m_SonoBuffers != NULL)
    {
        m_VAFunctionHandler = FunctionHandlerFactory::instance().vaFunctionHandler();
        connect(m_SonoBuffers, SIGNAL(bufferCleared(int)), m_VAFunctionHandler, SLOT(onBufferCleared(int)),
                Qt::DirectConnection);
        connect(m_SonoBuffers, SIGNAL(beforeBuffersChanged(int)), m_VAFunctionHandler,
                SLOT(onBeforeBuffersChanged(int)), Qt::DirectConnection);
        connect(m_SonoBuffers, SIGNAL(buffersChanged(int)), m_VAFunctionHandler, SLOT(onBuffersChanged(int)),
                Qt::DirectConnection);
        connect(m_VAFunctionHandler, SIGNAL(clearBuffer(int, int)), this, SLOT(onClearBuffer(int, int)),
                Qt::DirectConnection);
        connect(m_VAFunctionHandler, SIGNAL(dopplerGateChanged()), this, SIGNAL(dopplerGateChanged()),
                Qt::DirectConnection);
        connect(m_VAFunctionHandler, SIGNAL(stateChanged(bool)), this, SLOT(onVAStateChanged(bool)),
                Qt::DirectConnection);
        if (m_BufferPushControlers != NULL)
        {
            connect(m_BufferPushControlers, SIGNAL(imageChanged(const QList<BufferUnit>&, const FrameUnitInfo&)), this,
                    SLOT(onImageChanged(const QList<BufferUnit>&, const FrameUnitInfo&)));
        }
    }
}
#endif
void ChisonUltrasoundContext::createBufferPushControlers()
{
    QList<ISonoBuffer*> iSonoBuffers = m_SonoBuffers->getSonoBuffer();

    m_BufferPushControlers = new BufferPushControlers(iSonoBuffers);
    CHECK_NEW(BufferPushControlers, m_BufferPushControlers);

    connect(m_BufferPushControlers, SIGNAL(fpsChanged(const QString&, const float)), this,
            SIGNAL(fpsChanged(const QString&, const float)), Qt::DirectConnection);
    connect(m_BufferPushControlers, SIGNAL(requestFlush()), this, SLOT(onRequestFlush()));
    connect(m_BufferPushControlers, SIGNAL(imageChanged(const QList<BufferUnit>&, const FrameUnitInfo&)), this,
            SIGNAL(imageChanged(const QList<BufferUnit>&, const FrameUnitInfo&)));
}

void ChisonUltrasoundContext::createCineLooper()
{
    m_CineLooper = new LineCineLooper(m_LineBufferManager, this);

    connect(m_SonoBuffers, SIGNAL(createCineLoopPlayer(int, ICineLoopBuffer*)), m_CineLooper,
            SLOT(onCreateCineLoopPlayer(int, ICineLoopBuffer*)), Qt::DirectConnection);

    connect(m_SonoBuffers, SIGNAL(destoryCineLoopPlayer(int)), m_CineLooper, SLOT(onDestoryCineLoopPlayer(int)),
            Qt::DirectConnection);
    connect(this, SIGNAL(destoryCineLoopPlayer(int)), m_CineLooper, SLOT(onDestoryCineLoopPlayer(int)),
            Qt::DirectConnection);

    connect(m_SonoBuffers, SIGNAL(updateCinePlayStartIndex(int)), m_CineLooper, SLOT(onUpdateCinePlayStartIndex(int)),
            Qt::DirectConnection);

    connect(m_SonoBuffers, SIGNAL(updateCinePlayEndIndex(int)), m_CineLooper, SLOT(onUpdateCinePlayEndIndex(int)),
            Qt::DirectConnection);

    connect(m_SonoBuffers, SIGNAL(updateCinePlayCurrentIndex(int)), m_CineLooper,
            SLOT(onUpdateCinePlayCurrentIndex(int)), Qt::DirectConnection);
    connect(this, SIGNAL(updateCinePlayCurrentIndex(int)), m_CineLooper, SLOT(onUpdateCinePlayCurrentIndex(int)),
            Qt::DirectConnection);

    connect(m_SonoBuffers, SIGNAL(cineStopLoop(int)), m_CineLooper, SLOT(onStopLoop(int)), Qt::DirectConnection);

    connect(m_CineLooper, SIGNAL(playCineStatusChanged(int, bool)), m_BufferPushControlers,
            SLOT(onPlayCineStatusChanged(int, bool)), Qt::DirectConnection);

    connect(m_CineLooper, SIGNAL(cineFpsChanged(float)), m_BufferPushControlers, SLOT(onCineFpsChanged(float)));
    CHECK_NEW(CineLooper, m_CineLooper);
}

void ChisonUltrasoundContext::createDSCThread()
{
    m_DscThread = new DscThread();
    CHECK_NEW(DscThread, m_DscThread);

    connect(this, SIGNAL(closeRequested()), m_DscThread, SLOT(stop()));

    connect(m_DscThread, SIGNAL(finished()), this, SLOT(onPipelineFinished()));
}

void ChisonUltrasoundContext::createGstUltrasoundPipeline()
{
#ifdef USE_4D
    connect(m_DSCPipeline, SIGNAL(newFourDImage(FourDVolumeLineImageArgs*)), this,
            SLOT(onNewFourDImage(FourDVolumeLineImageArgs*)), Qt::DirectConnection);
#endif

    connect(m_DSCPipeline, SIGNAL(rawDataInfo(void*, int, int, int, int)), this,
            SIGNAL(rawDataInfo(void*, int, int, int, int)), Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewGrayImage(LineImageArgs*)),
            Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(requestFlush()), this, SLOT(onRequestFlush()), Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(beforePipelineStop(int)), this, SLOT(onBeforePipelineStop(int)),
            Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(pipelineCreated(int)), this, SLOT(onPipelineCreated(int)), Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(fpsChanged(const QString&, const float)), this,
            SIGNAL(fpsChanged(const QString&, const float)), Qt::DirectConnection);
}

void ChisonUltrasoundContext::createInviwoPipeline()
{
}

void ChisonUltrasoundContext::createZeusPipeline()
{
    QList<IBufferPushControler*> iBufferPushControler = m_BufferPushControlers->getBufferPushControler();
    m_DSCPipeline = new ZeusPipeline(iBufferPushControler, m_ZeusAPIInterface, m_ImageSaveHelper);
    CHECK_NEW(AbstractDSCPipeline, m_DSCPipeline);
    connect(m_DSCPipeline, SIGNAL(rawDataInfo(void*, int, int, int, int)), this,
            SIGNAL(rawDataInfo(void*, int, int, int, int)), Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewGrayImage(LineImageArgs*)),
            Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(newMeasureImage(LineImageArgs*)), this, SLOT(onNewMeasureImage(LineImageArgs*)),
            Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(newGrayImage(LineImageArgs*)), this, SLOT(onNewGrayImageForAI(LineImageArgs*)),
            Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(newGrayImage(LineImageArgs*)), this, SLOT(onNewGrayImageForCP(LineImageArgs*)),
            Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(requestFlush()), this, SLOT(onRequestFlush()), Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(beforePipelineStop(int)), this, SLOT(onBeforePipelineStop(int)),
            Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(pipelineCreated(int)), this, SLOT(onPipelineCreated(int)), Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(fpsChanged(const QString&, const float)), this,
            SIGNAL(fpsChanged(const QString&, const float)), Qt::DirectConnection);
    //    connect(m_DSCPipeline, SIGNAL(datainfo(void *, int, int, int, int)),
    //            this, SIGNAL(datainfo(void *, int, int, int, int)),Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(datainfo(void*, int, int, int, int)), this,
            SLOT(onDataInfo(void*, int, int, int, int)), Qt::DirectConnection);
    connect(m_DSCPipeline, SIGNAL(flushedAllImages()), this, SLOT(onFlushedAllImages()), Qt::QueuedConnection);
    connect(m_DSCPipeline, &AbstractDSCPipeline::imageDataReady, this, &ChisonUltrasoundContext::imageDataReady);

    connect(m_CineLooper, SIGNAL(playCineStatusChanged(int, bool)), m_DSCPipeline,
            SLOT(onPlayCineStatusChanged(int, bool)), Qt::DirectConnection);
}

void ChisonUltrasoundContext::createGlPreprocessing()
{
    m_LineGlPreprocessing = new LineGlPreprocessingCreater(m_SonoBuffers);
}

void ChisonUltrasoundContext::createImageDataReceiverSwitcher()
{
    m_DataReceiverSwitcher =
        new ImageDataReceiverSwitcher(m_LineGlPreprocessing, m_ZeusAPIInterface, m_ImageSaveHelper);
    CHECK_NEW(ImageDataReceiverSwitcher, m_DataReceiverSwitcher);

    connect(m_DataReceiverSwitcher, SIGNAL(setCudaAddr(quint64)), this, SIGNAL(setCudaAddr(quint64)),
            Qt::DirectConnection);
    connect(this, SIGNAL(updateImageData(ByteBuffer)), m_DataReceiverSwitcher, SIGNAL(updateImageData(ByteBuffer)),
            Qt::DirectConnection);

    connect(this, SIGNAL(imageUnstable()), m_DataReceiverSwitcher, SIGNAL(imageUnstable()), Qt::DirectConnection);
    connect(this, SIGNAL(imageStable()), m_DataReceiverSwitcher, SIGNAL(imageStable()), Qt::DirectConnection);
    connect(this, SIGNAL(imageShapeUnstable()), m_DataReceiverSwitcher, SIGNAL(imageShapeUnstable()),
            Qt::DirectConnection);
    connect(this, SIGNAL(imageShapeStable()), m_DataReceiverSwitcher, SIGNAL(imageShapeStable()), Qt::DirectConnection);

    connect(m_DataReceiverSwitcher, SIGNAL(hWInfoChanged()), this, SLOT(onHWInfoChanged()), Qt::DirectConnection);
    connect(m_DataReceiverSwitcher, SIGNAL(ECGInfoChanged(unsigned char)), this, SIGNAL(ECGInfo(unsigned char)),
            Qt::DirectConnection);
}

void ChisonUltrasoundContext::createImageFrameInfoSwitchers()
{
    m_FrameInfoSwitchers = new ImageFrameInfoSwitchers(m_SonoBuffers);
    CHECK_NEW(ImageFrameInfoSwitchers, m_FrameInfoSwitchers);
}

void ChisonUltrasoundContext::createBufferManager()
{
    if (m_LineBufferManager == NULL)
    {
        m_LineBufferManager = new LineBufferManager(this);
        m_LineBufferManager->addBuffer(m_SonoBuffers);
        m_LineBufferManager->setCurrentBufferType(BufferTypeEnum::LineImage);
        CHECK_NEW(LineBufferManager, m_LineBufferManager);
    }
}

void ChisonUltrasoundContext::createStressEchoManager()
{
    m_StressEchoManager = new StressEchoBufferManager(m_SonoBuffers->buffers(), m_ShareGlWidget);
    CHECK_NEW(StressEchoBufferManager, m_StressEchoManager);

    LineCineLooper* cineLooper = qobject_cast<LineCineLooper*>(m_CineLooper);
    if (cineLooper != NULL)
    {
        connect(m_StressEchoManager, SIGNAL(createCineLoopPlayer(int, ICineLoopBuffer*)), cineLooper,
                SLOT(onCreateCineLoopPlayer(int, ICineLoopBuffer*)));
        connect(m_StressEchoManager, SIGNAL(destoryCineLoopPlayer(int)), cineLooper, SLOT(onDestoryCineLoopPlayer(int)),
                Qt::DirectConnection);
        connect(m_StressEchoManager, SIGNAL(suspendLoop(int)), cineLooper, SLOT(onSuspendCineLoopPlayer(int)));
        connect(m_StressEchoManager, SIGNAL(resumeLoop(int)), cineLooper, SLOT(onResumeCineLoopPlayer(int)));
    }
    connect(m_StressEchoManager, SIGNAL(playCineStatusChanged(int, bool)), m_BufferPushControlers,
            SLOT(onPlayCineStatusChanged(int, bool)), Qt::DirectConnection);
    connect(m_StressEchoManager, SIGNAL(colorMapChanged(int, uchar*, uchar*, int)), m_DSCPipeline,
            SLOT(onColorMapChanged(int, uchar*, uchar*, int)), Qt::DirectConnection);
    connect(m_StressEchoManager, SIGNAL(startPlay()), this, SLOT(onStartPlay()), Qt::DirectConnection);
}

void ChisonUltrasoundContext::createrGlBridgePipeline()
{
}

void ChisonUltrasoundContext::createZeusAPIHelper()
{
    bool IsSupportPreProcess = true;
    if (m_ChisonUltrasoundMode == ChisonUltrasound::ChisonUltrasound_AVITransform)
    {
        IsSupportPreProcess = false;
    }
    m_ZeusAPIInterface = new ZeusAPIInterface(m_SonoBuffers->getSonoBuffer(), IsSupportPreProcess);
    CHECK_NEW(ZeusAPIInterface, m_ZeusAPIInterface);
}

bool ChisonUltrasoundContext::mapImageEventArgsFromLineImageArgs(ImageEventArgs& imageEventArgs,
                                                                 LineImageArgs* lineImageArgs)
{
    if (lineImageArgs->lineImageArgsType() == ByteLineImageArgs::Type_Byte)
    {
        // 2025-06-10 Modify by AlexWang 父类增加imageEventArgs()接口，减少动态转换
        imageEventArgs = *(lineImageArgs->imageEvnetArgs());
        return true;
    }
    return false;
}

bool ChisonUltrasoundContext::processCurvedPanoramic(ImageEventArgs* imageEventArgs)
{
    if (imageEventArgs->imageData() == NULL)
    {
        return false;
    }
    *(m_DestCurvedImageData.imageEvnetArgs()) = *imageEventArgs;
    switch (imageEventArgs->imageType())
    {
    case ImageEventArgs::ImageB:
    {
        DataArg& curvedImageData = m_CurvedImageData->next();
        curvedImageData.clear();
#ifdef USE_PANORAMIC
        m_CurvedPanoramic->getRealTimePanoramic(imageEventArgs, curvedImageData);
#endif
        if (curvedImageData.type() != DataArg::P)
        {
            return false;
        }
        m_DestCurvedImageData.imageEvnetArgs()->setWidth(curvedImageData.width());
        m_DestCurvedImageData.imageEvnetArgs()->setHeight(curvedImageData.height());
        m_DestCurvedImageData.imageEvnetArgs()->setImageType(ImageEventArgs::ImageCurvedPan);
        m_DestCurvedImageData.imageEvnetArgs()->setImageData(curvedImageData.data());
        m_DestCurvedImageData.imageEvnetArgs()->setBitCount(imageEventArgs->bitCount());
    }
    break;
    default:
        break;
    }
    return true;
}

ILineBuffer* ChisonUltrasoundContext::lineBuffer() const
{
    return m_SonoBuffers;
}

ILineBufferManager* ChisonUltrasoundContext::lineBufferManager() const
{
    return m_LineBufferManager;
}

CineLooper* ChisonUltrasoundContext::cineLooper() const
{
    return m_CineLooper;
}
#ifdef USE_PANORAMIC
CurvedPanoramic* ChisonUltrasoundContext::curvedPanoramic() const
{
    return m_CurvedPanoramic;
}

void ChisonUltrasoundContext::setCurvedPanomicFile(const QString& filePath)
{
    m_CurvedPanomicFileName = filePath;

    m_isStaticCuvedPanomicState = true;

    sendCurvedPanImageArgs();
}
#endif
void ChisonUltrasoundContext::onLoad(SonoParameters* sonoParameters)
{
    qDebug() << PRETTY_FUNCTION << "sonoParameters:" << (quintptr)sonoParameters
             << "sonoParameters->isRealTime():" << sonoParameters->isRealTime() << "m_ProbeChanged:" << m_ProbeChanged
             << "ActiveB:" << sonoParameters->pIV(BFPNames::ActiveBStr)
             << "freeze:" << sonoParameters->pBV(BFPNames::FreezeStr);
    m_IsPaused = true;
    if (sonoParameters->isRealTime())
    {
        m_SonoBuffers->prepareRestore(sonoParameters->pIV(BFPNames::ActiveBStr));
        m_ProbeChanged = false;
    }
    setSonoParameters(sonoParameters);
    if (sonoParameters->isRealTime())
    {
        m_SonoBuffers->onRestore();
    }
    asyncClearFrameIndexSync();
}

void ChisonUltrasoundContext::endOnLoad()
{
    {
        QMutexLocker locker(&m_WaitFrozenMutex);
        m_Prepare2LoadImage = false;
    }
    // 回调中间过程已结束，放开Enforce的限制
    m_DSCPipeline->setEnforceEnable(true);
    m_IsPaused = m_SonoParameters->isRealTime() ? m_ProbeChanged : false;
    m_SonoBuffers->onload();
    asyncClearFrameIndexSync();

    if (!m_SonoParameters->isRealTime())
    {
        emit m_LineBufferManager->notifyFreezed();
    }

    qDebug() << "********************" << PRETTY_FUNCTION << "m_IsPaused:" << m_IsPaused
             << "m_Prepare2LoadImage:" << m_Prepare2LoadImage << "m_ProbeChanged:" << m_ProbeChanged
             << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime();
}

void ChisonUltrasoundContext::deleteLoadSonoparameters()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
    m_SonoBuffers->deleteLoadSonoparameters();
}

void ChisonUltrasoundContext::setParameter(const QString& name, const QVariant& value)
{
    m_SonoParameters->setPV(name, value);
}

const QVariant& ChisonUltrasoundContext::getParameter(const QString& parameterName) const
{
    return m_SonoParameters->pV(parameterName);
}

void ChisonUltrasoundContext::onPresetBeginChanged()
{
    emit eraseImage();
    qDebug() << "*****" << PRETTY_FUNCTION;
    onImageShapeUnstable();
    m_DataReceiverSwitcher->stop();
    m_ProbeChanged = true;
    onFlushedAllImages();
    m_SonoBuffers->onSetPreset();
    for (int i = 0; i < m_SonoParameters->pIV(BFPNames::LayoutStr); ++i)
    {
        destoryCineLoopPlayer(i);
    }
}

void ChisonUltrasoundContext::onPresetEndChanged()
{
    qDebug() << "*****" << PRETTY_FUNCTION << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime();
    m_DSCPipeline->clearThreadData();
    m_BufferPushControlers->onImageShapeStable(m_SonoBuffers->activeLayoutIndex());
    //开始数据接收
    m_SonoBuffers->setWorkState(true);
    emit imageShapeStable();

    if (m_SonoParameters->isRealTime())
    {
        if (m_SonoParameters->pBV(BFPNames::ProbeConnectedStr))
        {
            m_IsPaused = false;
            m_ProbeChanged = false;
            m_DataReceiverSwitcher->start();
        }
        else
        {
            m_IsPaused = true;
            m_DataReceiverSwitcher->stop();
        }
        m_SonoBuffers->removeAll();
        onFlushedAllImages(false);
    }
}

void ChisonUltrasoundContext::onRequestFlush()
{
    qDebug() << PRETTY_FUNCTION << "m_IsPaused:" << m_IsPaused
             << "m_SonoParameters->isRealTime:" << m_SonoParameters->isRealTime()
             << "FreezeStr:" << m_SonoParameters->pBV(BFPNames::FreezeStr)
             << "m_CineLooper->isLooping():" << m_CineLooper->isLooping() << "m_SystemScanMode:" << m_SystemScanMode
             << "m_WaitingForFrozen:" << m_WaitingForFrozen;
    {
        QMutexLocker locker(&m_WaitFrozenMutex);
        if (m_WaitingForFrozen)
        {
            return;
        }
    }
    SonoParameters* stressEchoParameters = nullptr;
    {
        StressEchoBuffer* curBuffer = m_StressEchoManager->stressEchoBuffer(0);
        if (curBuffer != nullptr && curBuffer->isStressEcho())
        {
            //此处传递的0布局，StressEcho退出或者未播放时，默认布局为-1
            stressEchoParameters = curBuffer->getSonoParametersByLayoutIndex(0);
        }
    }
    if (stressEchoParameters != nullptr &&
        SystemScanModeClassifier::isLikeD(stressEchoParameters->pIV(BFPNames::SystemScanModeStr)))
    {
        // 针对EasyView下回调PW电影，由于播放的时候会同时刷新激活区和非激活区，导致播放频谱的时候多一根线。
        // 如果是非三同步，如果下激活，只刷二维图区域，如果是上激活，只刷频谱区域
        if (!stressEchoParameters->pBV(BFPNames::TriplexModeStr))
        {
            if (stressEchoParameters->pBV(BFPNames::FreqSpectrumStr))
            {
                m_SonoBuffers->requestFlush(0, -1, 0);
            }
            else
            {
                m_SonoBuffers->requestFlush(0, -1, 1);
            }
        }
        else
        {
            m_SonoBuffers->requestFlush();
        }
    }
    else if (!m_IsPaused && !m_CineLooper->isLooping() &&
             (m_SonoParameters->pBV(BFPNames::FreezeStr) || (!m_SonoParameters->isRealTime())))
    {
        m_SonoBuffers->requestFlush();
    }
    else if (SystemScanModeClassifier::isLikeD(m_SonoParameters->pIV(BFPNames::SystemScanModeStr)))
    {
        m_SonoBuffers->requestFlush(-1, -1, 0);
    }
}

void ChisonUltrasoundContext::onBeforePipelineStop(int imageTileIndex)
{
    m_IsPaused = true;
    emit beforePipelineStop(imageTileIndex);

    qDebug() << PRETTY_FUNCTION << "imageTileIndex:" << imageTileIndex << "m_IsPaused:" << m_IsPaused;
}

void ChisonUltrasoundContext::onPipelineCreated(int imageTileIndex)
{
    if (m_DSCChanging)
    {
        qDebug() << PRETTY_FUNCTION << "imageTileIndex:" << imageTileIndex << "m_DSCChanging:" << m_DSCChanging;
        return;
    }

    m_IsPaused = m_SonoParameters->isRealTime() ? m_ProbeChanged : false;
    emit pipelineCreated(imageTileIndex);

    qDebug() << PRETTY_FUNCTION << "imageTileIndex:" << imageTileIndex << "m_SystemScanMode:" << m_SystemScanMode
             << "m_IsPaused:" << m_IsPaused << "m_ProbeChanged:" << m_ProbeChanged
             << "ActiveB:" << m_SonoParameters->pIV(BFPNames::ActiveBStr)
             << "m_SonoParameters->isRealTime:" << m_SonoParameters->isRealTime();

    asyncClearFrameIndexSync();
    //该参数用于EasyView下播放下激活PW电影所用
    SonoParameters* stressEchoParameters = nullptr;
    {
        StressEchoBuffer* curBuffer = m_StressEchoManager->stressEchoBuffer(0);
        if (curBuffer != nullptr && curBuffer->isStressEcho())
        {
            //此处传递的0布局，StressEcho退出或者未播放时，默认布局为-1
            stressEchoParameters = curBuffer->getSonoParametersByLayoutIndex(0);
        }
    }

    //在创建Pipeline的时候，在冻结状态下需要刷新当前帧，确保将当前的buffer缓存的数据推送给zeus，
    //确保后处理调节参数的enfoce回调上来的数据是当前帧的数据
    bool requestFlush =
        m_SonoParameters->pBV(BFPNames::FreezeStr) && (m_SonoParameters->pIV(BFPNames::ActiveBStr) == imageTileIndex);
    if (!m_SonoParameters->isRealTime() || requestFlush)
    {
        m_SonoBuffers->requestFlush();
    }
    else if (stressEchoParameters != nullptr &&
             SystemScanModeClassifier::isLikeD(stressEchoParameters->pIV(BFPNames::SystemScanModeStr)))
    {
        // TODO:在EasyView下播放下激活的PW电影时，虽然触发了requestFlush但是必须用对应的超声参数，否则模式
        //判断不成立，无法执行requestFlush
        //同时由于只刷新PW模式的B图像，所以刷新参数指定：layoutindex为0，bufferindex为0
        m_SonoBuffers->requestFlush(0, -1, 0);
    }
    else
    {
    }
}

bool ChisonUltrasoundContext::onNewGrayImageElasto(LineImageArgs* lineImageArgs)
{
    return false;
}
#ifdef USE_VA
void ChisonUltrasoundContext::detectImageVA(ImageEventArgs* imageArgs)
{
    if (m_ImageStable && m_ImageShapeStable && m_VAFunctionHandler != NULL && m_SonoBuffers != NULL)
    {
        if (!m_SonoParameters->pBV(BFPNames::FreezeStr) && m_VAFunctionHandler->functionOpened())
        {
            m_VAFunctionHandler->detectVessel(imageArgs, imageArgs->cacheIndex(),
                                              m_SonoBuffers->frameCountByFrameType(ImageBufferDef::B_Data));
        }
        QList<VADetectResult> result =
            m_VAFunctionHandler->getResult(m_SonoParameters->pIV(BFPNames::ActiveBStr), imageArgs->cacheIndex());
        emit vaResultUpdated(QVariant::fromValue<QList<VADetectResult>>(result));
    }
    return;
}
void ChisonUltrasoundContext::onImageChanged(const QList<BufferUnit>& bufferunits, const FrameUnitInfo& unitInfo)
{
    if (m_ImageStable && m_ImageShapeStable && m_VAFunctionHandler != NULL && m_VAFunctionHandler->functionOpened() &&
        !unitInfo.needPostProcess() && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        foreach (const BufferUnit& unit, bufferunits)
        {
            m_VAFunctionHandler->updateWaveData(unit.at(0).data(), unitInfo.first().pointNum(), unit.EcgEnd());
        }
    }
}

void ChisonUltrasoundContext::onVAStateChanged(bool isOn)
{
    emit functionStateChanged(FunctionTypeDef::VADetect, isOn);
}
void ChisonUltrasoundContext::onClearBuffer(int layoutIndex, int bufferType)
{
    m_SonoBuffers->removeAll(layoutIndex, bufferType);
}

bool ChisonUltrasoundContext::saveVaResult(QString fileDir, int startIndex, int endIndex)
{
    QHash<int, QList<int>> indexlists;
    QHash<int, QList<int>> indexsaveFilelists;
    QList<int> originList;
    QList<int> newList;
    for (int frameIndex = startIndex; frameIndex <= endIndex; frameIndex++)
    {
        originList.append(m_SonoBuffers->startIndex(frameIndex, 0) + m_SonoBuffers->scpdValue());
        newList.append(frameIndex - startIndex + m_SonoBuffers->scpdValue());
    }
    indexlists.insert(m_SonoBuffers->activeLayoutIndex(), originList);
    indexsaveFilelists.insert(m_SonoBuffers->activeLayoutIndex(), newList);

    QHash<int, int> saveOffset;
    saveOffset.insert(m_SonoBuffers->activeLayoutIndex(), startIndex);
    return m_VAFunctionHandler->saveResult(fileDir, indexlists, indexsaveFilelists);
}
void ChisonUltrasoundContext::loadVaResult(QString fileDir)
{
    m_VAFunctionHandler->loadResult(fileDir, m_SonoBuffers->layout());
}
#endif

void ChisonUltrasoundContext::onNewMeasureImage(LineImageArgs* lineImageArgs)
{
    if (lineImageArgs->lineImageArgsType() == ByteLineImageArgs::Type_Byte)
    {
        // 2025-06-10 Modify by AlexWang 父类增加imageEventArgs()接口，减少动态转换
        emit newMeasureImage(lineImageArgs->imageEvnetArgs());
    }
    lineImageArgs->release();
    return;
}

void ChisonUltrasoundContext::onNewGrayImage(LineImageArgs* lineImageArgs)
{
    if (lineImageArgs->lineImageArgsType() == ByteLineImageArgs::Type_Byte)
    {
        // 2025-06-10 Modify by AlexWang 父类增加imageEventArgs()接口，减少动态转换
        onNewImage(lineImageArgs->imageEvnetArgs());
    }
    lineImageArgs->release();
    return;
}

void ChisonUltrasoundContext::onNewGrayImageForAI(LineImageArgs* lineImageArgs)
{
    if (lineImageArgs->lineImageArgsType() == ByteLineImageArgs::Type_Byte && !pBV(BFPNames::CurvedPanoramicEnableStr))
    {
        // 2025-06-10 Modify by AlexWang 父类增加imageEventArgs()接口，减少动态转换
        ImageEventArgs* imageEventArgs = lineImageArgs->imageEvnetArgs();
        //自动多普勒
        emit wholeGrayImage(imageEventArgs->imageData(), imageEventArgs->width(), imageEventArgs->height());
    }
}

void ChisonUltrasoundContext::onNewGrayImageForCP(LineImageArgs* lineImageArgs)
{
    if (pBV(BFPNames::CurvedPanoramicEnableStr) && !pBV(BFPNames::FreezeStr)) // 负责实时宽景模式
    {
        // 2025-06-10 Modify by AlexWang 父类增加imageEventArgs()接口，减少动态转换
        ImageEventArgs* imageEventArgs = lineImageArgs->imageEvnetArgs();
        processCurvedPanoramic(imageEventArgs);
        emit newImage(m_DestCurvedImageData.imageEvnetArgs());
    }
}

#include "fileframeinfomapper.h"
void ChisonUltrasoundContext::onNewImage(ImageEventArgs* imageEventArgs)
{
    if (m_IsPause)
    {
        return;
    }

    if (imageEventArgs->imageData() == NULL)
    {
        return;
    }

    // curvedPanoramic
    if (pBV(BFPNames::CurvedPanoramicEnableStr) && !pBV(BFPNames::FreezeStr))
    {
        return;
    }
    //    qDebug() << "%%%%%%%%%%"
    //             << PRETTY_FUNCTION << "index:" << imageEventArgs->index()
    //             << "FrameType:" << imageEventArgs->imageType()
    //             << "FrameIndex:" << imageEventArgs->frameIndex()
    //             << "sonoBufferFrameCount:" << sonoBuffer->frameCount()
    //             << "Width/Height:" << imageEventArgs->width() << "/" << imageEventArgs->height()
    //             << "FrameEcgEnd:" << imageEventArgs->ecgEnd()
    //             << "SyncId:" << imageEventArgs->syncId()
    //             << "m_IsPaused:" << m_IsPaused
    //             << "freeze:" << m_SonoParameters->pBV(BFPNames::FreezeStr);

    // LasetFrameIndex目前用于常规冻结的情况下冻结时还未走完dsc的帧做屏蔽，不考虑stressecho的场景
    if (!m_IsInStressEchoAnalyzeState && !setActiveLayoutDataBufferLasetFrameIndex(imageEventArgs))
    {
        return;
    }
    // AVI转换
    if (m_ChisonUltrasoundMode == ChisonUltrasound::ChisonUltrasound_AVITransform)
    {
        emit newImage(imageEventArgs);
        return;
    }
    // call back in easyview
    if (m_IsInStressEchoAnalyzeState)
    {
        m_StressEchoManager->onNewImage(imageEventArgs);
        return;
    }
    //    //判断是否更新图像
    //    if(!updateDisplayIndex(imageEventArgs))
    //    {
    //        return;
    //    }
    /*处理完的数据写入SonoBuffers*/

    //此处代码的作用是在多布局下，备份非激活区域的图像。
    //其执行是在Zeus的回调线程中，且实现中有加锁 和 内存的申请与释放，
    //在频繁的操作路径中，不建议出现加锁 和 内存的变动，
    //由于GrapeTablet演示版本目前未开发多布局功能，因此暂时注释掉此代码，避免引起崩溃

    m_SonoBuffers->onNewImage(imageEventArgs);

    // 3D
    if (m_SystemScanMode == SystemScanModeFreeHand3D)
    {
        return;
    }

    if (m_SystemScanMode == SystemScanModeAV)
    {
        if (imageEventArgs->imageType() == ImageEventArgs::ImageD)
        {
            emit newPWImage(imageEventArgs);
            return;
        }
    }

    if ((!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr)) &&
        imageEventArgs->needUpdateForzonIndex())
    {
        m_SonoBuffers->updateCurrentIndexOnFrozen(imageEventArgs->index(), imageEventArgs->frameIndex(),
                                                  imageEventArgs->imageType());
    }
    /*播放电影时，更新当前索引*/
    if (!m_IsPaused && !m_CineLooper->isLooping() &&
        (!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr)))
    {
        emit updateCinePlayCurrentIndex(imageEventArgs->index());
    }
    /*弹性图像数据，添加弹力柱计算值*/
    // if(imageEventArgs->imageType() == ImageEventArgs::ImageElasto)
    // {
    //     imageEventArgs->setElastoData(m_SonoBuffers->elastoCalculator()->averages(imageEventArgs->frameIndex()).data());
    // }
    // else
    {
        imageEventArgs->setElastoData(NULL);
    }
    /*分发数据*/
    if (imageEventArgs->imageType() != ImageEventArgs::ImageFourD)
    {
        if (imageEventArgs->imageType() == ImageEventArgs::ImageECG && !pBV(BFPNames::FreezeStr))
        {
            const uchar* hwInfoData = m_DataReceiverSwitcher->hwInfoData();
            const struct LineDataHWInfo* ldHWInfo = reinterpret_cast<const struct LineDataHWInfo*>(hwInfoData);
            if (ldHWInfo != NULL && ldHWInfo->ECGTime != 0)
            {
                imageEventArgs->setECGTime(ldHWInfo->ECGTime * 1.0 /
                                           1000000 /* / pIV(BFPNames::ADFreqMHzStr) / 1000000.0*/);
            }
        }
        // m_FpsCalculator->cal();

        //供sonomsk使用
        emit newBImage(imageEventArgs);

        //供刷新使用
        emit newImage(imageEventArgs);

#ifdef USE_VA
        detectImageVA(imageEventArgs);
#endif
    }

    //供彩虹测速使用
    handlePostRawData(imageEventArgs);

    //供AutoNeedleAngle使用
    emit wholeImageInfo(imageEventArgs->imageData(), imageEventArgs->width(), imageEventArgs->height());
}

void ChisonUltrasoundContext::onNewPictureImage(QImage image)
{
    // call back in easyview
    if (m_IsInStressEchoAnalyzeState)
    {
        //        m_StressEchoManager->onNewImage(imageEventArgs);
        return;
    }
    emit newPictureImage(image);
}

#ifdef USE_4D
void ChisonUltrasoundContext::onNewFourDImage(FourDVolumeLineImageArgs* volumeImageArgs)
{
    /*播放电影时，更新当前索引*/
    if (m_CineLooper->isLooping())
    {
        m_SonoBuffers->updateCurrentIndexOnFrozen(m_SonoBuffers->activeLayoutIndex(),
                                                  volumeImageArgs->currentVolumeIndex(), ImageBufferDef::FourD_Data);
    }
    emit volumeDataPrepared(*volumeImageArgs);
}
#endif
bool ChisonUltrasoundContext::updateDisplayIndex(const ImageEventArgs* imageEventArgs)
{
    // frameIndex相等，不管是否暂停，都需要刷新数据
    if (m_FrameIndexSync.m_FrameIndex != imageEventArgs->frameIndex())
    {
        //暂停刷新，新的frameIndex大于记录的frameIndex
        if (m_IsPaused && imageEventArgs->frameIndex() > m_FrameIndexSync.m_FrameIndex)
        {
            return false;
        }
        //无法上锁的时候，不再刷新
        if (!m_FrameIndexSync.m_SyncMutex.tryLock())
        {
            qDebug() << PRETTY_FUNCTION << "SyncMutex is locked";
            return false;
        }
        if ((imageEventArgs->imageType() != m_FrameIndexSync.m_FrameType) &&
            (m_FrameIndexSync.m_FrameIndex >= imageEventArgs->frameIndex()))
        {
            m_FrameIndexSync.m_SyncMutex.unlock();
            return !m_IsPaused;
        }
        m_FrameIndexSync.m_FrameIndex = imageEventArgs->frameIndex();
        m_FrameIndexSync.m_FrameType = imageEventArgs->imageType();

        m_FrameIndexSync.m_SyncMutex.unlock();
    }
    return true;
}

void ChisonUltrasoundContext::onStaticImage(ImageEventArgs* imageEventArgs)
{
    qDebug() << PRETTY_FUNCTION << "index:" << imageEventArgs->index() << "imageType:" << imageEventArgs->imageType()
             << "frameIndex:" << imageEventArgs->frameIndex() << "width/height:" << imageEventArgs->width() << "/"
             << imageEventArgs->height();
    emit newImage(imageEventArgs);
}

void ChisonUltrasoundContext::start()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
    if (m_DscThread->isRunning())
    {
        log()->error("already started");
        return;
    }
    m_pipelineFinished = false;
    m_DscThread->setWorkingPipeline(m_DSCPipeline);

    m_DscThread->start();

    m_BufferPushControlers->start();

    if (Setting::instance().defaults().isWaitInit())
    {
        m_DSCPipeline->waitStartedComplete();
    }
}

void ChisonUltrasoundContext::clear()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
    m_SonoBuffers->removeAll();
}

void ChisonUltrasoundContext::pauseReceivingImage()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
    m_DataReceiverSwitcher->stop();
    m_ProbeChanged = true;
}

void ChisonUltrasoundContext::onClose()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
}

void ChisonUltrasoundContext::onPipelineFinished()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
    m_pipelineFinished = true;
}

void ChisonUltrasoundContext::onHWInfoChanged()
{
    const uchar* hwInfoData = m_DataReceiverSwitcher->hwInfoData();
#ifdef USE_LOTUS_API
    if (m_WholeDataHWInfo == NULL)
    {
        m_WholeDataHWInfo = new WholeDataHWInfoCommonManage();
        CHECK_NEW(WholeDataHWInfoCommonManage, m_WholeDataHWInfo);
    }
    m_WholeDataHWInfo->lineDataHWInfo2WholeDataHWInfo((const LineDataHWInfo*)hwInfoData);
    uchar* wholeHWInfo = (uchar*)m_WholeDataHWInfo->getWholeDataHWInfo();
    emit newHWInfo(wholeHWInfo);
#else
    emit newHWInfo(const_cast<unsigned char*>(hwInfoData), sizeof(LineDataHWInfo));
#endif
}

void ChisonUltrasoundContext::onLineImageDebugChanged(const QVariant& value)
{
    m_LineImageDebug = value.toInt();
    if (m_LineImageDebug != 0)
    {
        m_SonoParameters->setPV(BFPNames::LineColorMapDebugStr, 0);
    }
}

void ChisonUltrasoundContext::onLineColorMapDebugChanged(const QVariant& value)
{
    m_LineColorMapDebug = value.toInt();
    if (m_LineColorMapDebug != 0)
    {
        m_SonoParameters->setPV(BFPNames::LineImageDebugStr, 0);
    }
}

void ChisonUltrasoundContext::onBeforeFreezeChanged(const QVariant& oldValue, QVariant& newValue)
{
    qDebug() << PRETTY_FUNCTION << "oldValue:" << oldValue.toBool() << "newValue:" << newValue.toBool();
#if !defined(SYS_APPLE)
    if (newValue.toBool())
    {
        QMutexLocker locker(&m_WaitFrozenMutex);
        if (!m_Prepare2LoadImage)
        {
            m_WaitingForFrozen = true;
            m_WaitForFrozen = true;
        }
    }
#endif
}

void ChisonUltrasoundContext::onFreezeChanging(const QVariant& value)
{
    qDebug() << PRETTY_FUNCTION << "value:" << value;
    //需要比SonoBuffer中的setFreeze早执行
    if (value.toBool())
    {
        // luodebing 严格按照设计要求执行，此处是为了在当前成像刷新方案下冻结下清除未显示的数据，因此需要增加状态判断
        //否则解冻的时候也调用此方法(FileFrameInfoMapper中m_FrameUnits的访问)，
        //存在解冻后的时序问题(包括快速操作)，多线程其他参数信号触发(FileFrameInfoMapper中m_FrameUnits的清除)
        clearActiveLayoutDataBufferNoDrawnFrames();
    }
    //    qDebug() << PRETTY_FUNCTION << "paintFrameIndex: " <<
    //    imageBufferGroup->fileFrameMemory()->lastPaintFrameIndex() << ", frameCount ***(2): " <<
    //    sonoBuffer->frameCount();

    QMutexLocker locker(&m_WaitFrozenMutex);
    bool isUpdateFrameCount = false;

    if (!m_Prepare2LoadImage)
    {
        isUpdateFrameCount = true;
    }

    m_SonoBuffers->syncDisplayIndex(m_FrameIndexSync.m_FrameIndex, isUpdateFrameCount);
    onEndPaused();

    if (m_LineBufferManager != NULL)
    {
        m_LineBufferManager->freeze(value.toBool());
    }
}

void ChisonUltrasoundContext::onFreezeChanged(const QVariant& value)
{
    qDebug() << PRETTY_FUNCTION << "value:" << value.toBool() << "m_SonoParameters:" << (quintptr)m_SonoParameters
             << "ActiveBStr:" << m_SonoParameters->pIV(BFPNames::ActiveBStr)
             << "frameCount():" << m_SonoBuffers->frameCount() << "m_WaitForFrozen:" << m_WaitForFrozen
             << "m_WaitingForFrozen:" << m_WaitingForFrozen;
    if (value.toBool())
    {
        bool freezeRightNow = false;
        {
            QMutexLocker locker(&m_WaitFrozenMutex);
            if (!m_Prepare2LoadImage && m_WaitForFrozen && m_WaitingForFrozen && m_SonoBuffers->frameCount() > 0)
            {
                m_WaitForFrozen = false;
                m_BufferPushControlers->startWaitForFrozen(m_SonoParameters->pIV(BFPNames::ActiveBStr));
            }
            else
            {
                m_WaitForFrozen = false;
                m_WaitingForFrozen = false;
                m_Prepare2LoadImage = false;
                qDebug() << PRETTY_FUNCTION << m_Prepare2LoadImage;
                freezeRightNow = true;
            }
        }
        if (freezeRightNow)
        {
            m_SonoBuffers->setFreeze(true);
            if (m_PresetModeParameterSyncOnFrozen != NULL)
            {
                m_PresetModeParameterSyncOnFrozen->connectSignals();
            }
        }
        emit m_LineBufferManager->notifyFreezed();
    }
    else
    {
        //开始数据接收
        m_SonoBuffers->setWorkState(true);
        m_SonoBuffers->setFreeze(false);

#ifdef USE_PANORAMIC
        m_isStaticCuvedPanomicState = false;
#endif
    }

#ifdef USE_PANORAMIC
    if (pIV(BFPNames::SystemScanModeStr) == SystemScanModeCP)
    {
        if (value.toBool())
        {
            m_CurvedPanoramic->setFinish(true);
        }
        else
        {
            onUpdatePixelSizeMM(m_CurvedPanoramic->originalPixelSizeMM());
            m_CurvedPanoramic->clear();
        }
    }
#endif
}

void ChisonUltrasoundContext::onProbeIdChanged(const QVariant& value)
{
    // m_ProbeChanged = ((m_ProbeID != value.toInt()) && (-1 != m_ProbeID));
    // TODO 考虑探头ID为-1的初始化情况
    m_ProbeChanged = m_ProbeID != value.toInt();
    m_ProbeID = value.toInt();
    onFlushedAllImages(false);
}

void ChisonUltrasoundContext::onBeforeLayoutChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (oldValue.toInt() != newValue.toInt())
    {
        m_DSCChanging = true;
    }
}

void ChisonUltrasoundContext::onLayoutChanged(const QVariant& value)
{
    m_DSCChanging = false;
}

void ChisonUltrasoundContext::onFlushedAllImagesOfDSC()
{
}

void ChisonUltrasoundContext::prepareLoadImage()
{
    QMutexLocker locker(&m_WaitFrozenMutex);
    m_Prepare2LoadImage = true;
    // 回调过程中，不需要参数发送Enforce，直到回调结束后，用户主动调节后处理参数才下发Enforce
    m_DSCPipeline->setEnforceEnable(false);
}

bool ChisonUltrasoundContext::isPrepareLoadImage() const
{
    return m_Prepare2LoadImage;
}

void ChisonUltrasoundContext::onFlushedAllImages(bool requestFlush)
{
    qDebug() << PRETTY_FUNCTION << "m_WaitingForFrozen:" << m_WaitingForFrozen;
    {
        QMutexLocker locker(&m_WaitFrozenMutex);
        if (!m_WaitingForFrozen)
        {
            return;
        }
        m_WaitingForFrozen = false;
        m_Prepare2LoadImage = false;
    }

    // 当前冻结过程中，有刷新所有图像的控制，在执行控制内会强制更新所有地方的超声参数，
    // 但变更超声参数的过程中，不需要发送Enforce，直到变更结束，才有发送Enforce的能力
    m_DSCPipeline->setEnforceEnable(false);
    m_SonoBuffers->setFreeze(true);
    m_DSCPipeline->setEnforceEnable(true);

    if (m_PresetModeParameterSyncOnFrozen != NULL)
    {
        m_PresetModeParameterSyncOnFrozen->connectSignals();
    }
    if (requestFlush)
    {
        m_SonoBuffers->requestFlush(m_SonoParameters->pIV(BFPNames::ActiveBStr), m_SonoBuffers->frameCount() - 1, -1,
                                    true);
    }
}

void ChisonUltrasoundContext::onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    qDebug() << PRETTY_FUNCTION << "oldValue:" << oldValue << "newValue:" << newValue;

    m_SystemScanMode = newValue.toInt();

#ifdef USE_PANORAMIC
    // 宽景模式直接返回其它模式时，不会触发冻结解冻，需要监听模式变更，完成结束宽景和重置PixelSizeMM的操作
    if (oldValue == SystemScanModeCP && m_SystemScanMode == SystemScanModeB && !m_CurvedPanoramic->isStop())
    {
        m_CurvedPanoramic->setFinish(true);
        onUpdatePixelSizeMM(m_CurvedPanoramic->originalPixelSizeMM());
    }
#endif

    asyncClearFrameIndexSync();
}

void ChisonUltrasoundContext::onActiveBChanged(const QVariant& value)
{
    qDebug() << PRETTY_FUNCTION << "value" << value.toInt();
    asyncClearFrameIndexSync();

    if (m_CineLooper->isLooping())
    {
        m_CineLooper->startLoop(value.toInt());
    }
}

void ChisonUltrasoundContext::onFreqSpectrumChanged(const QVariant& value)
{
    qDebug() << PRETTY_FUNCTION << "value" << value.toInt();
    asyncClearFrameIndexSync();
    m_IsWaveActive = value.toBool();
}

void ChisonUltrasoundContext::onTriplexModeChanged(const QVariant& value)
{
    qDebug() << PRETTY_FUNCTION << "value" << value.toInt();
    asyncClearFrameIndexSync();
}

void ChisonUltrasoundContext::onQuadplexModeStrChanged(const QVariant& value)
{
    qDebug() << PRETTY_FUNCTION << "value" << value.toInt();
    asyncClearFrameIndexSync();
}

void ChisonUltrasoundContext::onBeforeDisplayFormatChanged(const QVariant& value)
{
    qDebug() << PRETTY_FUNCTION << "value" << value.toInt();
    m_IsPaused = true;
}

void ChisonUltrasoundContext::onImageShapeStable()
{
    if (m_LayoutStable && m_ImageStable)
    {
        m_DSCPipeline->onImageShapeStable();
        m_BufferPushControlers->onImageShapeStable(m_SonoBuffers->activeLayoutIndex());
        emit imageShapeStable();
    }
    m_IsPaused = m_SonoParameters->isRealTime() ? m_ProbeChanged : false;
    qDebug() << PRETTY_FUNCTION << "m_IsPaused:" << m_IsPaused << "m_ImageShapeStable:" << m_ImageShapeStable
             << "m_ImageStable:" << m_ImageStable << "m_SonoParameters->isRealTime:" << m_SonoParameters->isRealTime()
             << "m_ProbeChanged:" << m_ProbeChanged
             << "isLikeD:" << SystemScanModeClassifier::isLikeD(m_SystemScanMode);
    if (SystemScanModeClassifier::isLikeD(m_SystemScanMode))
    {
        m_SonoBuffers->requestFlush(-1, -1, m_IsWaveActive ? 0 : 1);
    }
    m_ImageShapeStable = true;
}

void ChisonUltrasoundContext::onImageShapeUnstable()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
    emit imageShapeUnstable();
    m_ImageShapeStable = false;
    m_IsPaused = true;
    m_DSCPipeline->onImageShapeUnstable();
    m_BufferPushControlers->onImageShapeUnstable(m_SonoBuffers->activeLayoutIndex());
}

void ChisonUltrasoundContext::onImageNoChanged()
{
    m_StateManager->postEvent(StateEventNames::Freeze);
}

void ChisonUltrasoundContext::onStartPlay()
{
    emit startPlay();
}

void ChisonUltrasoundContext::onImageStable()
{
    qDebug() << PRETTY_FUNCTION << "m_IsPaused:" << m_IsPaused << "m_ImageStable:" << m_ImageStable
             << "m_ImageShapeStable:" << m_ImageShapeStable
             << "m_SonoParameters->isRealTime:" << m_SonoParameters->isRealTime();
    if (m_LayoutStable && m_ImageShapeStable)
    {
        emit imageShapeStable();
    }
    if (SystemScanModeClassifier::isLikeD(m_SystemScanMode))
    {
        m_SonoBuffers->requestStaticImage(m_SonoBuffers->layout(), false);
        m_SonoBuffers->requestFlush(-1, -1, m_IsWaveActive ? 0 : 1);
    }
    m_IsPaused = m_SonoParameters->isRealTime() ? m_ProbeChanged : false;
    m_ImageStable = true;
}

void ChisonUltrasoundContext::onImageUnstable()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
    m_ImageStable = false;
    m_IsPaused = true;
    emit imageShapeUnstable();
}

void ChisonUltrasoundContext::onBeforeLayoutChanged()
{
    m_LayoutStable = false;
    emit imageShapeUnstable();
}

void ChisonUltrasoundContext::onLayoutChanged()
{
    m_LayoutStable = true;
    if (m_ImageStable && m_ImageShapeStable)
    {
        emit imageShapeStable();
    }
}

void ChisonUltrasoundContext::onMapsChanged()
{
    m_DSCPipeline->onColorMapChanged(m_SonoParameters->pIV(BFPNames::SystemScanModeStr));

#ifdef USE_PANORAMIC
    if ((m_SystemScanMode == SystemScanModeCP && pBV(BFPNames::FreezeStr)) // 支持冻结宽景下支持调节伪彩色
        || m_isStaticCuvedPanomicState)                                    // 支持回调宽景下支持调节伪彩色
    {
        sendCurvedPanImageArgs();
    }
#endif
}

void ChisonUltrasoundContext::setColorMap(uchar* image2DMap, uchar* waveMap)
{
    m_DSCPipeline->setColorMap(image2DMap, waveMap);
}

void ChisonUltrasoundContext::onEntryStressEchoAnalyze()
{
    disconnect(m_SonoBuffers, SIGNAL(staticImage(ImageEventArgs*)), this, SLOT(onStaticImage(ImageEventArgs*)));
    //    if(m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        onFlushedAllImages(false);
    }
    m_IsInStressEchoAnalyzeState = true;
    LineCineLooper* cineLooper = qobject_cast<LineCineLooper*>(m_CineLooper);
    if (cineLooper != NULL)
    {
        cineLooper->onEntryStressEchoAnalyze();
    }
    m_DSCPipeline->onEntryStressEchoAnalyze();
    m_StressEchoManager->onEntryStressEchoAnalyze();
}

void ChisonUltrasoundContext::setSystemScanMode(int systemScanMode)
{
    if (!m_SonoParameters->isRealTime())
    {
        m_SonoParameters->setPV(BFPNames::SystemScanModeStr, systemScanMode);
        m_SonoBuffers->getSonoParametersByLayoutIndex(m_SonoBuffers->activeLayoutIndex())
            ->setPV(BFPNames::SystemScanModeStr, systemScanMode);
    }
}

// bool ChisonUltrasoundContext::hasStableImageBuffer()
//{
//    return m_HasStableImageBuffer;
//}

void ChisonUltrasoundContext::onExitStressEchoAnalyze()
{
    LineCineLooper* cineLooper = qobject_cast<LineCineLooper*>(m_CineLooper);
    if (cineLooper != NULL)
    {
        cineLooper->onExitStressEchoAnalyze();
    }
    m_DSCPipeline->onExitStressEchoAnalyze();
    m_StressEchoManager->onExitStressEchoAnalyze();
    //    m_SonoBuffers->removeAll(m_SonoBuffers->activeLayoutIndex());
    connect(m_SonoBuffers, SIGNAL(staticImage(ImageEventArgs*)), this, SLOT(onStaticImage(ImageEventArgs*)),
            Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
    m_IsInStressEchoAnalyzeState = false;

    // stressecho退出时，如果在冻结状态下，需要刷新一帧图像，确保图像正确，并且支持后处理
    if (m_SonoParameters->pBV(BFPNames::FreezeStr) || (!m_SonoParameters->isRealTime()))
    {
        m_SonoBuffers->requestFlush();
    }
}

StressEchoBufferManager* ChisonUltrasoundContext::stressEchoBufferManager() const
{
    return m_StressEchoManager;
}

void ChisonUltrasoundContext::initCurvedPanoramic(SonoParameters* realSonoparameters)
{
#ifdef USE_PANORAMIC
    if (m_CurvedPanoramic != NULL && realSonoparameters != NULL)
    {
        m_CurvedPanoSize = realSonoparameters->pV(BFPNames::CurvedPanoramicImageSizeStr).toSize();
        QSize imageSize = realSonoparameters->pV(BFPNames::DSCImageSizeStr).toSize();
        m_CurvedPanoramic->initPanoParam(imageSize.width(), imageSize.height());
        m_CurvedPanoramic->setCurvedSize(m_CurvedPanoSize.width(), m_CurvedPanoSize.height());
        m_CurvedImageData->createBuffer(m_CurvedPanoSize.width(), m_CurvedPanoSize.height(), 8);
    }
#endif
}

void ChisonUltrasoundContext::onSetSonoParameters()
{
    m_isStaticCuvedPanomicState = false;
    m_SonoBuffers->setSonoParameters(m_SonoParameters);
    SyncIDManager::instance().setSonoParameters(m_SonoParameters);
    if (m_PresetModeParameterSyncOnFrozen == NULL && m_SonoParameters->isRealTime())
    {
        m_PresetModeParameterSyncOnFrozen = new PresetModeParameterSyncOnFrozen(m_SonoBuffers);
    }

    m_DSCPipeline->setSonoParameters(m_SonoParameters);
    m_ZeusAPIInterface->setSonoParameters(m_SonoParameters);
    m_LineBufferManager->setImageSize(m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize());
    if (m_SonoParameters->isRealTime())
    {
        m_DataReceiverSwitcher->setSonoParameters(m_SonoParameters);
        m_FrameInfoSwitchers->setSonoParameters(m_SonoParameters);
    }
    FunctionHandlerFactory::instance().setSonoParameters(m_SonoParameters);
}
void ChisonUltrasoundContext::connectParametersSignals()
{
    connect(parameter(BFPNames::LineImageDebugStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onLineImageDebugChanged(QVariant)));
    connect(parameter(BFPNames::LineColorMapDebugStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onLineColorMapDebugChanged(QVariant)));
    connect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanging(QVariant)), this, SLOT(onFreezeChanging(QVariant)));
    connect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFreezeChanged(QVariant)));
    connect(parameter(BFPNames::FreezeStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
            SLOT(onBeforeFreezeChanged(QVariant, QVariant&)));
    connect(parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onLayoutChanged(QVariant)));
    connect(parameter(BFPNames::LayoutStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
            SLOT(onBeforeLayoutChanged(QVariant, QVariant&)));
    connect(parameter(BFPNames::SystemScanModeStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
            SLOT(onBeforeSystemScanModeChanged(QVariant, QVariant&)));
    connect(parameter(BFPNames::ActiveBStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onActiveBChanged(QVariant)));
    connect(parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreqSpectrumChanged(QVariant)));
    connect(parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onTriplexModeChanged(QVariant)));
    connect(parameter(BFPNames::QuadplexModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onQuadplexModeStrChanged(QVariant)));
    connect(parameter(BFPNames::MDisplayFormatStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
            SLOT(onBeforeDisplayFormatChanged(QVariant)));
    connect(parameter(BFPNames::DDisplayFormatStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
            SLOT(onBeforeDisplayFormatChanged(QVariant)));
    connect(parameter(BFPNames::DTDIDisplayFormatStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
            SLOT(onBeforeDisplayFormatChanged(QVariant)));
    connect(parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onProbeIdChanged(QVariant)));

#ifdef USE_PANORAMIC
    connect(parameter(BFPNames::BColorMapIndexStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onBColorMapIndexChanged(QVariant)));
#endif
}

void ChisonUltrasoundContext::disconnectParametersSignals()
{
    disconnect(parameter(BFPNames::LineImageDebugStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onLineImageDebugChanged(QVariant)));
    disconnect(parameter(BFPNames::LineColorMapDebugStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onLineColorMapDebugChanged(QVariant)));
    disconnect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanging(QVariant)), this, SLOT(onFreezeChanging(QVariant)));
    disconnect(parameter(BFPNames::FreezeStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
               SLOT(onBeforeFreezeChanged(QVariant, QVariant&)));
    disconnect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFreezeChanged(QVariant)));
    disconnect(parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onLayoutChanged(QVariant)));
    disconnect(parameter(BFPNames::LayoutStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
               SLOT(onBeforeLayoutChanged(QVariant, QVariant&)));
    disconnect(parameter(BFPNames::SystemScanModeStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
               SLOT(onBeforeSystemScanModeChanged(QVariant, QVariant&)));
    disconnect(parameter(BFPNames::ActiveBStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onActiveBChanged(QVariant)));
    disconnect(parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onFreqSpectrumChanged(QVariant)));
    disconnect(parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onTriplexModeChanged(QVariant)));
    disconnect(parameter(BFPNames::QuadplexModeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onQuadplexModeStrChanged(QVariant)));
    disconnect(parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onProbeIdChanged(QVariant)));
#ifdef USE_PANORAMIC
    disconnect(parameter(BFPNames::BColorMapIndexStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onBColorMapIndexChanged(QVariant)));
#endif
}

void ChisonUltrasoundContext::onBeginPaused()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
    //停止数据接收
    m_SonoBuffers->setWorkState(false);
    onFlushedAllImages(false);
    m_IsPaused = true;
    m_PausedLayoutIndex = m_SonoBuffers->activeLayoutIndex();
    m_BufferPushControlers->haltProcessImageChanged(m_PausedLayoutIndex);
    m_DSCPipeline->haltProcessImageChanged(m_PausedLayoutIndex, m_FrameIndexSync.m_FrameIndex);
}

void ChisonUltrasoundContext::onEndPaused()
{
    qDebug() << PRETTY_FUNCTION << "m_FrameIndexSync.m_ImageTileIndex:" << m_FrameIndexSync.m_ImageTileIndex
             << "m_FrameIndexSync.m_FrameIndex:" << m_FrameIndexSync.m_FrameIndex
             << "activelayoutIndex:" << m_SonoBuffers->activeLayoutIndex()
             << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime()
             << "m_ProbeChanged:" << m_ProbeChanged << "m_IsPaused:" << m_IsPaused;
    m_IsPaused = m_SonoParameters->isRealTime() ? m_ProbeChanged : false;
    m_DSCPipeline->recoveryProcessImageChanged(m_PausedLayoutIndex);
    m_BufferPushControlers->recoveryProcessImageChanged(m_PausedLayoutIndex);
    m_FrameIndexSync.m_FrameIndex = -1;
    m_FrameIndexSync.m_ImageTileIndex = -1;
    m_DSCPipeline->clearFrozenIndex(m_SonoBuffers->activeLayoutIndex());
    m_isStaticCuvedPanomicState = false;
}

void ChisonUltrasoundContext::onDataInfo(void* data, int width, int height, int imageType, int frameSteerType)
{
#ifdef USE_AUTOFREEZE

    bool freqSpectrum = m_SonoParameters->pBV(BFPNames::FreqSpectrumStr);
    bool triplexMode = m_SonoParameters->pBV(BFPNames::TriplexModeStr);
    if (freqSpectrum || triplexMode) //有频谱判断频谱
    {
        if (imageType != ImageEventArgs::ImageD)
        {
            //由于现在采用了数据在后端过滤的方式，对于 AutoFreezeControl，m_NewJob控制了实际的运行，
            //当二维图帧频高时，会导致线程在大量执行二维图下的doWork，m_NeedFreeze的使用逻辑会导致m_StartTime被重置
            //导致自动冻结时间延长或者无法进入自动冻结
            //并且后续的datainfo信号使用的auto，在跨线程的情况下，使用的是队列，存在占用资源的情况，即二维图数据不应该放入队列
            return;
        }
    }

    if (FrameControl::instance().isAutoFreezeBusy()) //当AutoFreeze算法的线程正在检查图像。
    {
        return;
    }

    //初始化为 8bit的B图
    int inputLength = width * height; // 8bit
    if (inputLength <= 0)
    {
        return;
    }

    if (ImageEventArgs::ImageD == imageType) // 32bit
    {
        inputLength = width * height * 4; // 32bit
    }

    if (m_DataLengthForAutoFreeze < inputLength) // need Resize
    {
        if (nullptr != m_DataForAutoFreeze)
        {
            free(m_DataForAutoFreeze);
            m_DataForAutoFreeze = nullptr;
        }
        m_DataForAutoFreeze = malloc(inputLength);
        m_DataLengthForAutoFreeze = inputLength;
        memset(m_DataForAutoFreeze, 0, m_DataLengthForAutoFreeze);
    }
    memcpy(m_DataForAutoFreeze, data, inputLength); //把zeus内存的图像拷贝到xultrasound内存

    //    if(Setting::instance().defaults().afterZeusSaveFlag())
    //    {
    //        if(ImageEventArgs::ImageD == imageType)//32bit
    //        {
    //            QString filePath = QString("%1imagePW_2/").arg(Resource::zeusContextDataSaverDir());
    //            Util::Mkdir(filePath.toStdString().c_str());
    //            ImageSaveHelper::instance()->saveImage32((char*)m_DataForAutoFreeze, width, height, filePath, false,
    //            ImageEventArgs::ImageD);
    //        }
    //        else if(ImageEventArgs::ImageB == imageType)//8bit
    //        {
    //            QString filePath = QString("%1imageB_2/").arg(Resource::zeusContextDataSaverDir());
    //            Util::Mkdir(filePath.toStdString().c_str());
    //            ImageSaveHelper::instance()->saveImage8((char*)m_DataForAutoFreeze, width, height, filePath);
    //        }
    //    }

    emit datainfo(m_DataForAutoFreeze, width, height, imageType,
                  frameSteerType); //当前线程把新图像发送给AutoFreeze算法的线程。
#else
    emit datainfo(data, width, height, imageType, frameSteerType);
#endif
}

void ChisonUltrasoundContext::onBufferSonoParametersChanged()
{
    // 切换探头/预设值过程中，SonoBuffer的超声参数还未更新为实时超声参数，而触发信号时，要通知PostProcessHandlerBase变更超声参数，
    // 在预设值的赋值过程中，PostProcessHandlerBase需要用实时超声参数来计算，因此不能采用从当前SonoBuffer中获取
    if (m_ProbeChanged)
    {
        emit activeLayoutSonoParametersChanged(m_LineBufferManager->getSonoParametersByLayoutIndex(-1));
    }
    else
    {
        emit activeLayoutSonoParametersChanged(
            m_LineBufferManager->getSonoParametersByLayoutIndex(m_SonoBuffers->activeLayoutIndex()));
    }
}

#ifdef USE_PANORAMIC
void ChisonUltrasoundContext::onCurvedPanormicFinished(uchar* data)
{
    m_DestCurvedImageData.imageEvnetArgs()->setImageData(data);

    sendCurvedPanImageArgs();
}

void ChisonUltrasoundContext::onUpdatePixelSizeMM(qreal pixelSizeMM)
{
    m_SonoParameters->setPV(BFPNames::PixelSizeMMStr, pixelSizeMM);
}
#endif

void ChisonUltrasoundContext::asyncClearFrameIndexSync()
{
    qDebug() << "*****" << PRETTY_FUNCTION;
    //    QTimer::singleShot(300, this, SLOT(onEndPaused()));
    m_DSCPipeline->clearFrozenIndex(m_SonoBuffers->activeLayoutIndex());
}

bool ChisonUltrasoundContext::setActiveLayoutDataBufferLasetFrameIndex(ImageEventArgs* imageEventArgs)
{
    QMutexLocker lock(&m_ActiveLayoutDataBufferLasetFrameIndexMutex);
    SonoBuffer* activeSonoBuffer = m_SonoBuffers->buffers()[imageEventArgs->index()];

    if ((!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr)) &&
        (imageEventArgs->index() == m_SonoBuffers->activeLayoutIndex()) && (activeSonoBuffer->frameCount() > 0) &&
        (imageEventArgs->frameIndex() >= activeSonoBuffer->frameCount()))
    {
        qDebug() << PRETTY_FUNCTION << "To discard!" << imageEventArgs->frameIndex() << activeSonoBuffer->frameCount();
        return false;
    }

    FrameFile::FileFrameMemory* fileFrameMemory = activeSonoBuffer->dataBuffer()->fileFrameMemory();
    if (nullptr != fileFrameMemory)
    {
        fileFrameMemory->setLastPaintFrameIndex(imageEventArgs->frameIndex());
    }
    return true;
}

void ChisonUltrasoundContext::clearActiveLayoutDataBufferNoDrawnFrames()
{
    QMutexLocker lock(&m_ActiveLayoutDataBufferLasetFrameIndexMutex);
    SonoBuffer* activeSonoBuffer = m_SonoBuffers->buffers()[m_SonoBuffers->activeLayoutIndex()];
    activeSonoBuffer->dataBuffer()->clearNoDrawn();
}

#ifdef USE_PANORAMIC
void ChisonUltrasoundContext::sendCurvedPanImageArgs()
{
    ImageEventArgs args;
    QSize curvedSize = m_SonoParameters->pV(BFPNames::CurvedPanoramicImageSizeStr).toSize();
    args.setWidth(curvedSize.width());
    args.setHeight(curvedSize.height());
    args.setImageType(ImageEventArgs::ImageCurvedPan);
    args.setBitCount(8);

    if (m_SendData == nullptr)
    {
        m_SendData = new uchar[args.imageSize()];
    }

    if (m_isStaticCuvedPanomicState)
    {
        QFile file(m_CurvedPanomicFileName);
        if (file.exists() && file.open(QIODevice::ReadOnly))
        {
            QByteArray imageByteArray = file.readAll();
            if (!imageByteArray.isEmpty())
            {
                memcpy(m_SendData, (uchar*)imageByteArray.data(), args.imageSize());
                args.setImageData(m_SendData);
                m_CurvedPanoramic->setSaveData((char*)imageByteArray.data());
                file.close();
            }
        }
    }
    else if (m_DestCurvedImageData.imageEvnetArgs() != nullptr)
    {
        memcpy(m_SendData, m_DestCurvedImageData.imageEvnetArgs()->imageData(), args.imageSize());

        args.setImageData(m_SendData);
    }
    emit newImage(&args);
}
#endif

void ChisonUltrasoundContext::backUpWave()
{
    m_SonoBuffers->backUpWave();
}

void ChisonUltrasoundContext::syncAvtiveBufferGlobalSonoparamters()
{
    m_SonoBuffers->syncAvtiveBGlobalSonoparamters();
}

void ChisonUltrasoundContext::clearAvtiveBufferFreezeParasRecord()
{
    m_SonoBuffers->clearAvtiveBFreezeParasRecord();
}

void ChisonUltrasoundContext::clearImageBufferDataOnOncePushLimit(int imagebufferIndex, int layoutIndex)
{
    m_SonoBuffers->clearImageBufferDataOnOncePushLimit(imagebufferIndex, layoutIndex);
}

void ChisonUltrasoundContext::handlePostRawData(const ImageEventArgs* imageArgs)
{
    int postRawDataType = imageArgs->postRawDataType();

    if (postRawDataType < 0)
    {
        return;
    }

    //通知彩虹测速，已经有原始数据
    if (postRawDataType == 1)
    {
        emit signalHasVelocityRawData(imageArgs->index());
    }

    const QVector<SonoBuffer*>& sonoBuffers = m_SonoBuffers->buffers();
    SonoBuffer* sonoBuffer = sonoBuffers[imageArgs->index()];
    PostRawDataManager* postRawDataManager = sonoBuffer->postRawDataManager();
    uchar* rawData = imageArgs->imageData() + imageArgs->width() * imageArgs->height() * imageArgs->bitCount() /
                                                  PostRawDataManager::BITS_PER_BYTE;

    postRawDataManager->updateData(imageArgs->postRawDataType(), rawData, imageArgs->width(), imageArgs->height(), 8,
                                   imageArgs->frameIndex());
}

bool ChisonUltrasoundContext::getIsPause() const
{
    return m_IsPause;
}

void ChisonUltrasoundContext::setIsPause(bool IsPause)
{
    m_IsPause = IsPause;
}

ImageDataReceiverSwitcher* ChisonUltrasoundContext::dataReceiverSwitcher() const
{
    return m_DataReceiverSwitcher;
}

void ChisonUltrasoundContext::setPictureImage(QImage image)
{
    m_SonoBuffers->setActiveBufferPictureImage(image);
    emit newPictureImage(image);
}

int ChisonUltrasoundContext::PostRawData(int type, int index, int x, int y)
{
    const QVector<SonoBuffer*>& sonoBuffers = m_SonoBuffers->buffers();

    if (sonoBuffers.count() < index)
        return 0;

    SonoBuffer* sonoBuffer = nullptr;
    if (index < 0)
    {
        sonoBuffer = sonoBuffers.at(m_SonoBuffers->activeLayoutIndex());
    }
    else
    {
        sonoBuffer = sonoBuffers.at(index);
    }

    if (sonoBuffer == nullptr)
        return 0;
    PostRawDataManager* postRawDataManager = sonoBuffer->postRawDataManager();
    PostGeneralRawData* data = (PostGeneralRawData*)postRawDataManager->data(type);

    if (data == nullptr)
        return 0;

    return data->data(x, y);
}

void ChisonUltrasoundContext::setColorRawDataCallbackEnabled(bool isEnable, const int& layoutIndex)
{
    const QVector<SonoBuffer*>& sonoBuffers = m_SonoBuffers->buffers();

    if (layoutIndex < 0)
    {
        int curlayoutIndex = m_SonoBuffers->activeLayoutIndex();
        SonoBuffer* sonoBuffer = sonoBuffers[curlayoutIndex];

        if (sonoBuffer != nullptr)
        {
            if (sonoBuffer->sonoParameters() != nullptr)
            {
                sonoBuffer->sonoParameters()->setPV(BFPNames::PostRawDataEnableStr, isEnable);
                qDebug() << "ChisonUltrasoundContext::setColorRawDataCallbackEnabled "
                         << "curlayoutIndex:" << curlayoutIndex << "isEnable:" << isEnable;
            }
        }
    }
    else
    {
        if (sonoBuffers.count() < layoutIndex)
            return;

        SonoBuffer* sonoBuffer = sonoBuffers[layoutIndex];

        if (sonoBuffer != nullptr)
        {
            if (sonoBuffer->sonoParameters() != nullptr)
            {
                sonoBuffer->sonoParameters()->setPV(BFPNames::PostRawDataEnableStr, isEnable);
                qDebug() << "ChisonUltrasoundContext::setColorRawDataCallbackEnabled "
                         << "layoutIndex:" << layoutIndex << "isEnable:" << isEnable;
            }
        }
    }

    qDebug() << "ChisonUltrasoundContext::setColorRawDataCallbackEnabled index:" << layoutIndex
             << " isEnable:" << isEnable;
}

void ChisonUltrasoundContext::setColorRawDataCallbackTigger(bool isEnforce, const int& layoutCount)
{
    if (isEnforce)
    {
    }
}

void ChisonUltrasoundContext::exportProperties()
{
    if ((nullptr == m_ZeusAPIInterface) || (nullptr == m_SonoBuffers))
    {
        return;
    }

    QString basePath = Resource::zeusContextDataSaverDir();
    foreach (const SonoBuffer* buffer, m_SonoBuffers->buffers())
    {
        int currentIndex = buffer->bufferIndex();
        QString dir = QString("%1PropertiesInfo_%2/").arg(basePath).arg(currentIndex);
        Util::Mkdir(dir);
        m_ZeusAPIInterface->exportProperties(currentIndex, dir);
    }
}

bool ChisonUltrasoundContext::is2DImagePushed(const int layoutIndex) const
{
    return (m_DSCPipeline != nullptr) ? m_DSCPipeline->isDSC2DImagePushed(layoutIndex) : true;
}

bool ChisonUltrasoundContext::existUnActiveRender(int layoutIndex) const
{
    return m_SonoBuffers->existUnActiveRender(layoutIndex);
}

void ChisonUltrasoundContext::setBFStaticParameters(BaseBFStaticParameters* bfStaticParameters)
{
    m_BFStaticParameters = bfStaticParameters;

    m_DataReceiverSwitcher->setBFStaticParameters(m_BFStaticParameters);
}

void ChisonUltrasoundContext::setStateManager(IStateManager* value)
{
    m_StateManager = value;

#ifdef USE_PANORAMIC
    m_CurvedPanoramic->setStateManager(value);
#endif
}

void ChisonUltrasoundContext::updateProbesChangedInfo(const QVector<int>& codes, const QVector<bool>& value)
{
    emit updateProbesChanged(codes, value);
}
