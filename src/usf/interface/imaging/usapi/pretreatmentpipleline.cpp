#include "pretreatmentpipleline.h"
#include "bytebuffer.h"
#include "iimagesavehelper.h"
#include "iqdata.h"
#include "lineimagedatainfostruct.h"
#include "resource.h"
#include "resourcehelper.h"
#include "setting.h"
#include "usimageprocess.h"
#include "util.h"
#include "zeusapihelper.h"
#include "syncidmanager.h"
#include <QDir>
#include <QOpenGLContext>
#include <QVariant>
#include <bfpnames.h>
#include <iostream>
#include <mutex>
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, PretreatmentPipleline)

PretreatmentPipleline::PretreatmentPipleline(int index, ZeusAPIInterface* api, IImageSaveHelper* imageSaveHelper)
    : m_ZeusAPIInterface(api)
    , m_ImageSaveHelper(imageSaveHelper)
{
    //     initPretreatmentParaInfo();
    m_ActiveIndex = index;

    m_ZeusAPIInterface->setZeusPreProcessDataCallBack(m_ActiveIndex, onZeusPreProcessedData, this);
    m_ZeusAPIInterface->setPreProcessFlag(m_ActiveIndex, true);
}

bool PretreatmentPipleline::pushData(const IQData& data)
{
    switch (data.m_LineType)
    {
    case BFDataType::CData:
        return pushData4Corlor(data);
    case LineData::Line_C_SD:
        return pushData4Wave(data, BFDataType::DData);
    case BFDataType::CWData:
        return pushData4Wave(data, BFDataType::CWData);
    default:
        break;
    }
    return false;
}

// void PretreatmentPipleline::addParaInfo(const QPair<QString, PretreatmentParaInfo> &info)
//{
//    m_Paramset.insert(info.first, info.second);
//}

// void PretreatmentPipleline::addParaInfo(const QHash<QString, PretreatmentParaInfo> &infoList)
//{
//    m_Paramset.insert(infoList);
//}

void PretreatmentPipleline::setPwPrf(int value)
{
}

bool PretreatmentPipleline::pushData4Corlor(const IQData& data)
{
    int alineIorQpiecesize = data.m_LineSize * data.m_LineNum * sizeof(uint16_t); //一组I或Q的字节数
    uchar* pData = (uchar*)&(data.m_data.data()[LINEDATAHEADLEN]);
    std::unique_ptr<uchar[]> pushdata(new uchar[data.m_PacketSize * 2 * alineIorQpiecesize]);
    uchar* iqptr = pushdata.get();

    if (data.m_Format == IQFormat4Zeus::IACC123QACC123)
    {
        //按照iq，iq，... ，N个积累次数排列
        for (int i = 0; i < data.m_PacketSize; i++)
        {
            memcpy(&iqptr[i * 2 * alineIorQpiecesize], &pData[i * alineIorQpiecesize], alineIorQpiecesize);
            memcpy(&iqptr[(i * 2 + 1) * alineIorQpiecesize], &pData[(i + data.m_PacketSize) * alineIorQpiecesize],
                   alineIorQpiecesize);
        }
    }
    else if (data.m_Format == IQFormat4Zeus::ACCI1ACCQ1ACCI2ACCQ2)
    {
        // 如果是ACCI1ACCQ1ACCI2ACCQ2格式，直接使用原始格式
        memcpy(iqptr, pData, data.m_PacketSize * 2 * alineIorQpiecesize);
    }
    else
    {
        qCritical() << PRETTY_FUNCTION << "InVaild I/Q data format push to pretreatment pipeline";
        return false;
    }

    //    uchar* pp = new uchar[1440 * data.m_LineNum];
    //    memset(pp,0,1440 * data.m_LineNum);

    //    int line_count = 34;
    //    int squence = 39;
    //     char* ptemp = new char[1024*line_count];
    //    for (int i = 0; i < 16; i++)
    //    {
    //        // 一个i/q
    //        QString file_name = QString("/usr/tigerapp/apple_debuginfo/iq_tmp/iq_c_%1_%2.dat").arg(i).arg(squence);
    //        qCritical() << "start read:" << file_name;
    //        QFile qfile(file_name);
    //        if (qfile.open(QIODevice::ReadOnly))
    //        {
    //            qfile.read(ptemp, 1024*line_count);
    //            for(int h = 0; h < line_count;h++)
    //            {
    //                 memcpy(&pp[1440*h], ptemp+h*1024,1024);
    //            }
    //            memcpy(&iqptr[i  * alineIorQpiecesize], pp, alineIorQpiecesize);

    //        }
    //        else
    //        {
    //            qCritical() << file_name << "not exsits!$$$$$$$$.........";
    //        }

    //    }

    DataPreProcessInfo dataProcessInfo;
    InfoForPostProcess processInfo;
    dataProcessInfo.DataType = (BFDataType)data.m_LineType;

    memcpy(&processInfo.info[HeadInfoStart], data.m_data.data(), LINEDATAHEADLEN);
    processInfo.info[LineNum] = data.m_LineNum;
    processInfo.info[LineSize] = data.m_LineSize;
    processInfo.info[SyncID] = SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId);

    dataProcessInfo.DataPostProcess[0].Data = (char*)iqptr;
    dataProcessInfo.DataPostProcess[0].DataPointNum = data.m_LineSize;
    dataProcessInfo.DataPostProcess[0].DataLineNum = data.m_LineNum;
    dataProcessInfo.DataPostProcess[0].PointBytesCount = data.m_PointBytesCount; // wifi size = 1

    if (Setting::instance().defaults().beforeIQSaveFlag())
    {
        static int frameIndex = 0;
        //        int colorAssemblecolorbatchsize = data.m_LineNum * sizeof(uint16_t) * data.m_PacketSize;
        for (int i = 0; i < data.m_PacketSize * 2; i++)
        {
            char name[48];
            QDir qdir;
            if (!qdir.exists(Resource::iqDataSaverDir()))
            {
                qdir.mkpath(Resource::iqDataSaverDir());
            }
            sprintf(name, "%s/iq_c_%d_%d.dat", Resource::iqDataSaverDir().toStdString().c_str(), frameIndex, i);
            PalmUtil::instance()->writeBinaryData(&iqptr[i * alineIorQpiecesize], alineIorQpiecesize,
                                                  name); //一组I/Q所有的大小,name);
        }
        frameIndex++;
        if (frameIndex >= 16)
        {
            frameIndex = 0;
        }
    }
    m_ZeusAPIInterface->pushDataForPreProcess(m_ActiveIndex, dataProcessInfo.DataPostProcess, dataProcessInfo.DataType,
                                              data.m_PacketSize * 2, &processInfo);
    //{
    //    QFile qfile(QString("/home/<USER>/Project_2/build-xunit-Desktop_Qt_5_15_2_GCC_64bit-Debug/bin/res/log/80.dat"));
    //    if (qfile.open(QIODevice::WriteOnly))
    //    {
    //        qfile.write((const char *)&iqptr, alineIorQpiecesize * 16);
    //    }
    //}
    return true;
}

bool PretreatmentPipleline::pushData4Wave(const IQData& data, BFDataType type)
{
    DataPreProcessInfo dataProcessInfo;
    InfoForPostProcess processInfo;

    memcpy(&processInfo.info[HeadInfoStart], data.m_data.data(), LINEDATAHEADLEN);
    processInfo.info[SyncID] = SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId);

    dataProcessInfo.DataType = type;
    dataProcessInfo.DataPostProcess[0].Data = (char*)&(data.m_data.data()[LINEDATAHEADLEN]);
    dataProcessInfo.DataPostProcess[0].DataPointNum = data.m_LineSize; // tianyi lingshi
    dataProcessInfo.DataPostProcess[0].DataLineNum = data.m_LineNum;
    dataProcessInfo.DataPostProcess[0].PointBytesCount = data.m_PointBytesCount; // wifi size = 1

    if (Setting::instance().defaults().afterIQSaveFlag())
    {
        static int saveCount = 0;
        // 保存推送给前处理的数据
        QString filePath = QString("%1beforePreProcessbufferPW").arg(Resource::iqDataSaverDir());
        int pointByteCount = 2;
        if (BFDataType::DData == type)
        {
            pointByteCount = 2;
            filePath = QString("%1beforePreProcessbufferPW").arg(Resource::iqDataSaverDir());
        }
        else if (BFDataType::CWData == type)
        {
            pointByteCount = 4;
            filePath = QString("%1beforePreProcessbufferCW").arg(Resource::iqDataSaverDir());
        }

        Util::Mkdir(filePath.toStdString().c_str());
        QFile qfile(QString("%1/%2.dat").arg(filePath).arg(saveCount));
        if (qfile.open(QIODevice::WriteOnly))
        {
            qfile.write(dataProcessInfo.DataPostProcess[0].Data, data.m_LineSize * data.m_LineNum * pointByteCount);
            qfile.close();
        }
        saveCount++;
    }

    m_ZeusAPIInterface->pushDataForPreProcess(m_ActiveIndex, dataProcessInfo.DataPostProcess, dataProcessInfo.DataType,
                                              1, &processInfo);
    return true;
}

void PretreatmentPipleline::onPreProcessedData(void* data, int width, int height, int dataType, int bitWidth,
                                               InfoForPostProcess info)
{
    Q_UNUSED(bitWidth);
    switch (dataType)
    {
    case BFDataType::CData:
    case BFDataType::DynamicFlow:
    {
        emit onNewColorData((uchar*)data, info, dataType);
        if (Setting::instance().defaults().afterIQSaveFlag())
        {
            QString filePath = QString("%1clinecallback").arg(Resource::iqDataSaverDir());
            Util::Mkdir(filePath.toStdString().c_str());
            m_ImageSaveHelper->saveImage2Hex((char*)data, width, height * bitWidth, QString("./%1/").arg(filePath),
                                             false);
        }
    }
    break;
    case BFDataType::DData:
    {
        emit onNewPWData((uchar*)data, info, dataType);
        if (Setting::instance().defaults().afterIQSaveFlag())
        {
            QString filePath = QString("%1pwlinecallback").arg(Resource::iqDataSaverDir());
            Util::Mkdir(filePath.toStdString().c_str());
            m_ImageSaveHelper->saveImage2Hex((char*)data, width, height, QString("./%1/").arg(filePath), false);
        }
    }
    break;
    case BFDataType::SOUNDData:
    {
        emit onNewSoundData((uchar*)data, info, dataType);
        if (Setting::instance().defaults().afterIQSaveFlag())
        {
            QString filePath = QString("%1pwlinecallback").arg(Resource::iqDataSaverDir());
            Util::Mkdir(filePath.toStdString().c_str());

            QString fileName = QString("%1/SoundData.dat").arg(filePath);
            QFile file(fileName);
            if (file.open(QFile::WriteOnly | QFile::Append))
            {
                file.write((char*)data, 1024);
            }
            file.close();
        }
    }
    // save data
    break;
    default:
        break;
    }
    //    {
    //        QFile qfile(QString("/home/<USER>/iqOut.dat"));
    //        if (qfile.open(QIODevice::WriteOnly))
    //        {
    //            qfile.write((const char *)data, width * height * 2);
    //        }
    //    }
    //    emit newData((uchar*)data, info.info[5]);
}

void PretreatmentPipleline::onZeusPreProcessedData(void* userData, CallbackInfo callbackInfo, InfoForPostProcess info)
{
    PretreatmentPipleline* fakeThis = reinterpret_cast<PretreatmentPipleline*>(userData);
    if (nullptr != fakeThis)
    {
        fakeThis->onPreProcessedData(callbackInfo.Data, callbackInfo.Width, callbackInfo.Height, callbackInfo.DataType,
                                     callbackInfo.BitWidth, info);
    }
}
