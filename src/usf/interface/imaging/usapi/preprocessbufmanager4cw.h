#ifndef PRETREATMENTBUFMANAGER4CW_H
#define PRETREATMENTBUFMANAGER4CW_H

#include "preprocessbufmanager4pw.h"
#include <QObject>

/**
 * @file PreProcessBufManager4cw.h && cpp
 * @brief CW(连续波)模式下的数据预处理缓冲区管理器
 * @details 该类负责管理CW模式下的IQ数据预处理缓冲区，继承自PreProcessBufManager4pw类
 *
 * @class PreProcessBufManager4cw
 * @brief 连续波（CW）模式下的预处理缓冲区管理器
 *
 * 该类专门用于处理超声成像中连续波多普勒模式的数据预处理和缓冲区管理，
 * 负责将原始IQ数据重组为特定的IQIQ交替格式。
 *
 * 主要功能：
 * 1. **IQ数据处理**：处理连续波模式下的IQ（In-phase/Quadrature）数据，
 *    最终组装成包含2组15120个I值和2组15120个Q值的完整数据包
 * 2. **双缓冲区管理**：管理两个主要缓冲区用于数据重组和处理
 *    - m_Buffertpool：中间缓冲区，存储单个数据包的IQ数据（121,040字节）
 *    - m_Buffertpool2：最终输出缓冲区，存储完整的IQIQ格式数据包（241,936字节）
 * 3. **数据重组**：将接收到的原始IQ数据重新组织成I Q I Q的内存布局格式
 * 4. **数据队列管理**：将处理后的数据包装成IQData对象并放入处理队列
 *
 * 缓冲区数据结构：
 *
 * **中间缓冲区 (m_Buffertpool)：**
 * ```
 * +------------------+--------------------------------+--------------------------------+
 * |    头部数据       |              I 数据             |              Q 数据             |
 * |   (16字节)        |   (60480字节 = 15120*4)        |   (60480字节 = 15120*4)         |
 * +------------------+--------------------------------+--------------------------------+
 * ```
 *
 * **最终输出缓冲区 (m_Buffertpool2) - IQIQ格式：**
 * ```
 * +------------------+--------------------------------+--------------------------------+--------------------------------+--------------------------------+
 * |    头部数据       |           第一组I数据            |           第一组Q数据            |           第二组I数据 |
 * 第二组Q数据            | |   (16字节)        |   (60480字节 = 15120*4)        |   (60480字节 = 15120*4)         |
 * (60480字节 = 15120*4)        |   (60480字节 = 15120*4)         |
 * +------------------+--------------------------------+--------------------------------+--------------------------------+--------------------------------+
 * |                  |                                |                                | | | |   LineDataHead   |   I1
 * I2  I3  ...  I15120      |   Q1  Q2  Q3  ...  Q15120      |   I1  I2  I3  ...  I15120      |   Q1  Q2  Q3  ... Q15120
 * | |   (16字节)        |   (每个I占4字节)                |   (每个Q占4字节)                 |   (每个I占4字节) |
 * (每个Q占4字节)                |
 * +------------------+--------------------------------+--------------------------------+--------------------------------+--------------------------------+
 * ```
 *
 * 数据处理流程：
 * 1. 接收来自硬件的原始IQ数据包（1040字节包含16字节头部+1024字节IQ数据）
 * 2. 解析数据包头信息（LineDataHead）获取帧号、线号等信息
 * 3. 将原始IQ数据（每8字节包含1个I和1个Q）分离存储到中间缓冲区
 * 4. 当中间缓冲区满时，将数据复制到最终缓冲区形成IQIQ格式
 * 5. 两个数据包合并后，发送完整的IQIQ数据包到处理队列
 * 6. 支持数据保存功能用于调试和分析
 *
 * 关键参数：
 * - **数据点数**: CW_POINT_NUM = 15120 (每组数据的点数)
 * - **头部长度**: LINEDATAHEADLEN = 16字节
 * - **数据精度**: 每个I或Q值占4字节
 * - **数据格式**: IQFormat4Zeus::IACC123QACC123
 * - **最终数据大小**: 241,936字节 (16 + 15120*4*4)
 *
 * 技术特点：
 * - 内存布局优化，采用I Q I Q交替存储模式
 * - 支持数据累积和分包处理
 * - 集成了数据保存功能用于调试
 * - 实时数据流处理和缓冲区管理
 * - 支持丢帧检测和错误处理
 *
 * 使用场景：
 * - 超声多普勒成像中的连续波模式
 * - 需要高精度IQ数据处理的应用
 * - 实时数据流处理和缓冲区管理
 * - 医疗超声设备的血流检测
 *
 * @note 该类是超声成像系统中数据处理链路的重要组成部分，负责原始数据的预处理和格式化
 * @warning 缓冲区大小固定，需要确保输入数据符合预期格式
 * @see PreProcessBufManager4pw, IQData, LineDataHead, PretreatmentPipleline
 */

class PreProcessParaController;
class USF_INTERFACE_IMAGING_USAPI_EXPORT PreProcessBufManager4cw : public PreProcessBufManager4pw
{
    Q_OBJECT

public:
    PreProcessBufManager4cw(PreProcessParaController* paraController, int dataQueueSize,
                            PretreatmentPipleline* pipleline);
    ~PreProcessBufManager4cw();

    /**
     * @brief 将数据拷贝到对应帧的缓冲区
     * @param data 包含LineDataHead的数据
     * @param[out] lostframe 是否丢帧
     * @return 缓存ID,如果数据未满返回-1,错误返回-2
     */
    virtual int copytobufferbyframeindex(uint8_t* data, bool& lostframe) override;

protected:
    virtual Type getWaveType()
    {
        return m_WaveType;
    };

private:
    Type m_WaveType;
    int m_savedPos; // 当前存储位置
    uchar* m_Buffertpool;
    uchar* m_Buffertpool2;
    //  测试使用
    //     QByteArray m_byteArrayI;
    //     QByteArray m_byteArrayQ;
};

#endif // PRETREATMENTBUFMANAGER4CW_H
