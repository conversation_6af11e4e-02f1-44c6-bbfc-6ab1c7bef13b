#ifndef LINEIMAGEDATAPALMRECEIVER_H
#define LINEIMAGEDATAPALMRECEIVER_H

#include "lineimagedatareceiver.h"
#include "usapi_global.h"
#include <QElapsedTimer>

//掌超的线数据接收类
class USF_INTERFACE_IMAGING_USAPI_EXPORT LineImageDataPALMReceiver : public LineImageDataReceiver
{
    Q_OBJECT
public:
    explicit LineImageDataPALMReceiver(IGlPreprocessingCreater* rawImageBuffers);
    ~LineImageDataPALMReceiver();

protected:
    virtual void onBeforeSonoParametersChange();
    virtual void onSonoParametersChanged();

    virtual bool shouldEnqueue(const LineDataHead* head);
    virtual void updateBeforeSteerList(int steering);
    virtual void handleReceiverOrder(const ByteBuffer& data) = 0;

    void receiveBase(const ByteBuffer& data)
    {
        LineImageDataReceiver::receive(data);
    }

private:
    void clearDopplerStatus();
    void delayPwRecv();
    void initUDPBufferManager();
public slots:
    virtual void receive(const ByteBuffer& data) override; // 标记为override
protected slots:
    virtual void onTDIEnChanged(const QVariant& value) = 0;
    virtual void onColorImageModeChanged(const QVariant& value) = 0;
    virtual void onFreezeChanged(const QVariant& value);
private slots:
    void onSampleVolmeMMChanged(const QVariant& value);
    void onBaseLineChanged(const QVariant& value);
    void showFps(float value);

private:
    FpsCalculator* m_FpsCalculator;
    //
    QElapsedTimer m_ReceiveElapsedTimer;
#define LINEIMAGEDATALOSSIMAGETIME 5
    int m_LossDataCount; //每5秒记录一次
};

#endif // LINEIMAGEDATAPALMRECEIVER_H
