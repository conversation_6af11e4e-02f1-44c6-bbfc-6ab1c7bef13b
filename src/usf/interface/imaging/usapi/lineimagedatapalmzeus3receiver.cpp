#include "lineimagedatapalmzeus3receiver.h"
#include "preprocessbufmanagerbase.h"
#include "sonoparameters.h"
#include "bytebuffer.h"
#include "bfpnames.h"
#include "setting.h"
#include "generalinfo.h"
#include "preprocessbuffercontroller.h"
#include "logger.h"
#include "syncidmanager.h"
LOG4QT_DECLARE_STATIC_LOGGER(logl, LineImageDataReceiver)

LineImageDataPALMZeus3Receiver::LineImageDataPALMZeus3Receiver(IGlPreprocessingCreater* rawImageBuffers,
                                                               ZeusAPIInterface* api, IImageSaveHelper* imageSaveHelper)
    : LineImageDataPALMReceiver(rawImageBuffers)
    , m_PreProcessBufferController(api, imageSaveHelper)
{
    foreach (const PreProcessBufManagerBase* pBuf, m_PreProcessBufferController.getBufferManagerList())
    {
        if (nullptr != pBuf)
        {
            connect(pBuf, SIGNAL(newData(const ByteBuffer&)), this, SLOT(onNewData(const ByteBuffer&)),
                    Qt::DirectConnection);
        }
    }
}

void LineImageDataPALMZeus3Receiver::onNewData(const ByteBuffer& data)
{
    receiveBase(data); // 调用中间类提供的方法来访问基类实现
}

void LineImageDataPALMZeus3Receiver::handleReceiverOrder(const ByteBuffer& data)
{
    int lineNo = -1;
    int linecolorAccumulative = -1;
    int linecolorIQFlag = -1;
    LineDataHead* lineDataHead = (LineDataHead*)(data.data());
    bool lostframe = false;

    if (*((unsigned short*)lineDataHead->SyncHead) != LineData::SyncHead_Flag)
    {
        logl()->error() << PRETTY_FUNCTION << "LineType" << (LineData::LineType)lineDataHead->LineType
                        << "linetype is error, maybe the package data is dirty"
                        << *((unsigned short*)lineDataHead->SyncHead);
        return;
    }
    int pointNum = m_SonoParameters->pIV(BFPNames::PointNumPerLineStr);
    int usbLineDataSize = pointNum * LINE_COUNT_PER_PACKAGE; // + m_RedundantPoints * 2;
    int usbLineSize = sizeof(LineDataHead) + usbLineDataSize;
    if (data.len() != usbLineSize)
    {
        //            ASSERT_X_LOG(data.len() != usbLineSize, PRETTY_FUNCTION,
        //                         "usb data size must equal usb line size");
        qDebug() << "$$$$$$$$$usb data size must equal usb line size, " << data.len() << ", " << usbLineSize;
        return;
    }
    if (lineDataHead->PackageType == LineData::Package_Image)
    {
        if (m_SonoParameters->pBV(BFPNames::FreezeStr))
            return;
        if (m_SonoParameters->pSV(BFPNames::ExamModeCaptionStr) == "")
            return;                                                //没有切换预设值的时候不接受图像数据
        if (lineDataHead->LineType == LineData::Line_Direct_Power) // m模式修改type保持和大机器一致
        {
            lineDataHead->LineType = LineData::Line_M;
            LineImageDataReceiver::receive(data);
            return;
        }

        SystemScanMode mode = (SystemScanMode)(m_SonoParameters->pIV(BFPNames::SystemScanModeStr));
        int frameNo = lineDataHead->getFrameNo();

        //使用LineDataHead提供的方法获取参数
        lineNo = lineDataHead->getLineNo();
        linecolorAccumulative = lineDataHead->getIQAccumulativeTimes();
        linecolorIQFlag = lineDataHead->getIQFlag();

        //        qDebug() << PRETTY_FUNCTION
        //                 << "usbLineSize: " << usbLineSize
        //                 << "usbLineDataSize: " << usbLineDataSize
        //                 << "pointNum: " << pointNum
        //                 << "frameNo: " << frameNo
        //                 << "lineNo: " << lineNo
        //                 << "linecolorAccumulative: " << linecolorAccumulative
        //                 << "StartLine: " << m_SonoParameters->pIV(BFPNames::StartLineStr)
        //                 << "StopLine: " << m_SonoParameters->pIV(BFPNames::StopLineStr)
        //                 << "m_RedundantPoints: " << m_RedundantPoints
        //                 << "Steering: " << lineDataHead->Steering;

        PreProcessBufManagerBase* udpordmg =
            m_PreProcessBufferController.getBufferManager((LineData::LineType)lineDataHead->LineType);

        if (lineDataHead->Steering == 3) //掌超的needle 偏转角度标志是3,需要改成和inviwo一致
        {
            // 4帧复合的梯形需要偏转角 1 2 3 4，在梯形模式下需要保留偏转角为3的帧
            if (!m_SonoParameters->pBV(BFPNames::NeedleModeStr) && !m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr))
                return;
        }

        if (nullptr == udpordmg)
        {
            LineImageDataReceiver::receive(data);
            return;
        }
        if (SyncIDManager::instance().currentSyncId(SyncIDMode::ControlTableSyncID) != lineDataHead->TableID)
        {
            udpordmg->resetallbyframeindex(frameNo);
            return;
        }
        //尝试重置参数
        udpordmg->tryReset(m_SonoParameters, frameNo, usbLineSize);
        // upd接收满足一帧
        bool lossframe = false;

        if (lineDataHead->LineType == LineData::Line_B)
        {
            LineImageDataReceiver::receive(data);
        }
        else
        {
            // logl()->info("......lineDataHead->LineType:%1,data length:%2\n", (int)lineDataHead->LineType,data.len());

            int cacheid = udpordmg->copytobufferbyframeindex((uint8_t*)lineDataHead, lossframe);
        }
    }
    else
    {
        ByteBuffer bb = ByteBuffer(data.data(), usbLineSize);
        LineImageDataReceiver::receive(bb);
    }
}

void LineImageDataPALMZeus3Receiver::onTDIEnChanged(const QVariant& value)
{
    //    PretreatmentBufManagerBase* buf = m_bufferHpler.getBufferManager(LineData::Line_C_Vel);
    //    if(buf != nullptr)
    //    {
    //        buf->setTDIEn(value.toBool());
    //        if(value.toBool())
    //        {
    //           buf->resetPiplineParametConnect(m_SonoParameters, PreInfoMode::ColorforTDI, PreInfoMode::ColorforCF);
    //        }
    //        else
    //        {
    //            buf->resetPiplineParametConnect(m_SonoParameters, PreInfoMode::ColorforCF, PreInfoMode::ColorforTDI);

    //        }
    //    }
}

void LineImageDataPALMZeus3Receiver::onColorImageModeChanged(const QVariant& value)
{
    //    PretreatmentBufManagerBase* buf = m_bufferHpler.getBufferManager(LineData::Line_C_Vel);
    //    if(buf != nullptr)
    //    {
    //        switch ((ColorImageModeType)value.toInt())
    //        {
    //        case Color_PD:
    //            buf->resetPiplineParametConnect(m_SonoParameters, PreInfoMode::ColorforPD, PreInfoMode::ColorforCF);
    //            break;
    //        case Color_CF:
    //            buf->resetPiplineParametConnect(m_SonoParameters, PreInfoMode::ColorforCF, PreInfoMode::ColorforPD);
    //            break;
    //        default:
    //            break;
    //        }
    //    }
}

void LineImageDataPALMZeus3Receiver::onBeforeSonoParametersChange()
{
    LineImageDataPALMReceiver::onBeforeSonoParametersChange();
    m_PreProcessBufferController.onBeforeSonoParametersChange(m_SonoParameters);
}

void LineImageDataPALMZeus3Receiver::onSonoParametersChanged()
{
    LineImageDataPALMReceiver::onSonoParametersChanged();
    m_PreProcessBufferController.onSonoParametersChanged(m_SonoParameters);
}
