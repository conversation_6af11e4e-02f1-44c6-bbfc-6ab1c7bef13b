/**
 * @file preprocessbufmanager4cw.cpp
 * @brief CW模式预处理缓冲区管理器实现
 *
 * @class PreProcessBufManager4cw
 * @brief 连续波（CW）模式下的预处理缓冲区管理器
 *
 * 该类继承自PreProcessBufManager4pw，专门用于处理超声成像中连续波多普勒模式的数据预处理和缓冲区管理。
 *
 * 主要功能：
 * 1.
 * **IQ数据处理**：处理连续波模式下的IQ（In-phase/Quadrature）数据，最终组装成包含2组15120个I值和2组15120个Q值的数据包
 * 2. **数据缓冲区管理**：管理两个主要缓冲区用于数据重组和处理
 *    - m_Buffertpool：用于存储单个数据包的IQ数据（15120*2*4 + 16字节）
 *    - m_Buffertpool2：用于存储完整的数据包，按I Q I Q格式布局（15120*4*4 + 16字节）
 * 3. **数据重组**：将接收到的原始IQ数据重新组织成I Q I Q的内存布局格式
 * 4. **数据队列管理**：将处理后的数据包装成IQData对象并放入处理队列
 *
 * 数据格式说明（以m_Buffertpool2为准）：
 * ```
 * +------------------+--------------------------------+--------------------------------+--------------------------------+--------------------------------+
 * |    头部数据       |           第一组I数据           |           第一组Q数据           |           第二组I数据 |
 * 第二组Q数据           | |   (16字节)        |   (60480字节 = 15120*4)        |   (60480字节 = 15120*4)        |
 * (60480字节 = 15120*4)        |   (60480字节 = 15120*4)        |
 * +------------------+--------------------------------+--------------------------------+--------------------------------+--------------------------------+
 * |                  |                                |                                | | | |   LineDataHead   |   I1
 * I2  I3  ...  I15120      |   Q1  Q2  Q3  ...  Q15120      |   I1  I2  I3  ...  I15120      |   Q1  Q2  Q3  ... Q15120
 * | |   (16字节)        |   (每个I占4字节)                |   (每个Q占4字节)                |   (每个I占4字节) |
 * (每个Q占4字节)                | |                  |                                | | | |
 * +------------------+--------------------------------+--------------------------------+--------------------------------+--------------------------------+
 * ```
 *
 * 数据布局：I Q I Q 模式，总大小为 16 + 15120*4*4 = 241936字节
 *
 * 工作流程：
 * 1. 接收来自硬件的原始IQ数据包
 * 2. 解析数据包头信息（LineDataHead）
 * 3. 将IQ数据按照I Q I Q格式重组到缓冲区
 * 4. 当缓冲区满时，将数据封装成IQData对象并发送到处理队列
 * 5. 支持数据保存功能（用于调试和分析）
 *
 * 特点：
 * - 支持CW_POINT_NUM（15120）个数据点的处理
 * - 内存布局优化，采用I Q I Q交替存储模式
 * - 支持数据累积和分包处理
 * - 集成了数据保存功能用于调试
 * - 使用IQFormat4Zeus::IACC123QACC123格式
 *
 * 使用场景：
 * - 超声多普勒成像中的连续波模式
 * - 需要高精度IQ数据处理的应用
 * - 实时数据流处理和缓冲区管理
 *
 * @note 该类是超声成像系统中数据处理链路的重要组成部分，负责原始数据的预处理和格式化
 * @warning 缓冲区大小固定，需要确保输入数据符合预期格式
 */

#include "preprocessbufmanager4cw.h"
#include "bfpnames.h"
#include "bytebuffer.h"
#include "controltableparameter.h"
#include "iqdata.h"
#include "probeblockdataset.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "qmath.h"
#include "resource.h"
#include "setting.h"
#include "sonoparameters.h"
#include "systemscanmodeclassifier.h"
#include "util.h"
#include <QDebug>
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, PreProcessBufManager4cw);

PreProcessBufManager4cw::PreProcessBufManager4cw(PreProcessParaController* paraController, int dataQueueSize,
                                                 PretreatmentPipleline* pipleline)
    : PreProcessBufManager4pw(paraController, dataQueueSize, pipleline)
    , m_WaveType(CW)
    , m_savedPos(LINEDATAHEADLEN)
{
    // CW连续波IQ数据缓冲区初始化
    // m_Buffertpool：中间缓冲区，用于存储单个数据包的IQ数据
    // 数据格式：头部(16字节) + I数据(15120*4字节) + Q数据(15120*4字节) = 121,040字节
    // +------------------+--------------------------------+--------------------------------+
    // |    头部数据       |              I 数据             |              Q 数据             |
    // |   (16字节)        |   (60480字节 = 15120*4)        |   (60480字节 = 15120*4)         |
    // +------------------+--------------------------------+--------------------------------+
    m_Buffertpool = new uchar[CW_POINT_NUM * 2 * 4 + 16]{0};

    // m_Buffertpool2：最终输出缓冲区，存储完整的IQIQ格式数据包
    // 数据格式：头部(16字节) + I1数据(15120*4字节) + Q1数据(15120*4字节) + I2数据(15120*4字节) + Q2数据(15120*4字节) =
    // 241,936字节
    // +------------------+--------------------------------+--------------------------------+--------------------------------+--------------------------------+
    // |    头部数据       |           第一组I数据            |           第一组Q数据            |           第二组I数据
    // |           第二组Q数据          | |   (16字节)        |   (60480字节 = 15120*4)        |   (60480字节 = 15120*4)
    // |   (60480字节 = 15120*4)         |   (60480字节 = 15120*4)          |
    // +------------------+--------------------------------+--------------------------------+--------------------------------+--------------------------------+
    // |                  |                                |                                | | | |   LineDataHead   |
    // I1  I2  I3  ...  I15120      |   Q1  Q2  Q3  ...  Q15120      |   I1  I2  I3  ...  I15120      |   Q1  Q2  Q3 ...
    // Q15120      | |   (16字节)        |   (每个I占4字节)                |   (每个Q占4字节)                 |
    // (每个I占4字节)                 |   (每个Q占4字节)                | |                  | | | | |
    // +------------------+--------------------------------+--------------------------------+--------------------------------+--------------------------------+
    // 最终数据布局：I Q I Q 交替模式，用于IQFormat4Zeus::IACC123QACC123格式
    m_Buffertpool2 = new uchar[CW_POINT_NUM * 4 * 4 + 16]{0};

    // Amy测试代码
    //    QString pathI =  "cw-data/CWRecvI";
    //    QString pathQ =  "cw-data/CWRecvQ";
    //    QString PathIData = Resource::ZeusNetworksPath() + pathI;
    //    QString PathQData = Resource::ZeusNetworksPath() + pathQ;
    //    //读取15120 个I
    //    QFile file(PathIData);
    //    if(file.open(QIODevice::ReadOnly))
    //    {
    //        m_byteArrayI = file.readAll();
    //    }
    //    //读取15120 个Q
    //    QFile file1(PathQData);
    //    if(file1.open(QIODevice::ReadOnly))
    //    {
    //        m_byteArrayQ = file1.readAll();
    //    }
}

PreProcessBufManager4cw::~PreProcessBufManager4cw()
{
    // clear();
    if (nullptr != m_Buffertpool)
    {
        delete[] m_Buffertpool;
        m_Buffertpool = nullptr;
    }
    if (nullptr != m_Buffertpool2)
    {
        delete[] m_Buffertpool2;
        m_Buffertpool2 = nullptr;
    }
}

int PreProcessBufManager4cw::copytobufferbyframeindex(uint8_t* data, bool& lostframe)
{
    static int sendCount = 0;
    // CW data
    LineDataHead* lineDataHead = (LineDataHead*)data;
    int lineNo = lineDataHead->getLineNo();
    int frameNo = lineDataHead->getFrameNo();
    int linecolorAccumulative = lineDataHead->getIQAccumulativeTimes();
    int linecolorIQFlag = lineDataHead->getIQFlag();

    if (!Setting::instance().defaults().isIODeviceVirtual())
    {
        if (m_AccumNum <= 0)
        {
            return -1;
        }
    }

    if (Setting::instance().defaults().beforeIQSaveFlag())
    {
        QString filePath = QString("%1beforIQ").arg(Resource::iqDataSaverDir());
        Util::Mkdir(filePath.toStdString().c_str());
        QString fileName = QString("%1/%2.dat").arg(filePath).arg(frameNo);
        QFile file(fileName);
        if (file.open(QFile::WriteOnly))
        {
            file.write((char*)data, MAXRCVSIZEPW);
        }
        file.close();
    }

    ////////////////////////////////////Amy模拟数据测试////////////////////////////////////////////////////////
    //     static int number = 0;
    //     static int readPos = 0;
    //     if( number ++  == 236)
    //     {
    //         uchar* IData = new uchar[15120*4 * 2] {0};
    //         uchar* QData = new uchar[15120*4 * 2] {0};

    //         // 暂定搞个20 组 15120 点，每2组 组成一包数据
    //         for (int i = 15120 * 4 * readPos; i < 15120 * 4 * (readPos + 2); i++)
    //         {
    //             IData[ i - (15120 * 4 * readPos)] = (uchar)m_byteArrayI[i];
    //         }

    //         // 暂定搞个20 组 15120 点，每2组 组成一包数据
    //         for (int j = 15120 * 4 * readPos; j < 15120 * 4 * (readPos + 2); j++)
    //         {
    //             QData[j - (15120 * 4 * readPos)] = (uchar)m_byteArrayQ[j];
    //         }

    //         // 拷贝 16 个 头
    //         memcpy(m_Buffertpool2, data, LINEDATAHEADLEN);
    //         memcpy(&m_Buffertpool2[LINEDATAHEADLEN],IData, 15120*4);
    //         memcpy(&m_Buffertpool2[LINEDATAHEADLEN + 15120*4],QData, 15120*4);
    //         memcpy(&m_Buffertpool2[LINEDATAHEADLEN + 15120*4 *2],&IData[15120*4], 15120*4);
    //         memcpy(&m_Buffertpool2[LINEDATAHEADLEN + 15120*4 * 3] ,&QData[15120*4], 15120*4);

    //         m_IqDataProcess->enqueue(IQData(ByteBuffer(m_Buffertpool2, CW_POINT_NUM * 4 * 4 + LINEDATAHEADLEN),
    //                                         BFDataType::CWData, CW_POINT_NUM, 4, 0, 4,
    //                                         IQFormat4Zeus::IACC123QACC123));
    //         delete[] QData;
    //         QData = NULL;
    //         delete[] IData;
    //         IData = NULL;
    //         number = 0;
    //         readPos +=2;

    //         if(readPos == 20 * 6)
    //         {
    //             readPos = 0;
    //         }
    //     }
    //     return -1;
    ////////////////////////////////////////////////////////////////////////////////////////////////////////

    // CW数据处理：将原始IQ数据重组为IQIQ格式
    // 原始数据大小：1040字节总包 - 16字节头部 = 1024字节IQ数据
    int dataSize = MAXRCVSIZEPW - LINEDATAHEADLEN; // 1040 - 16 = 1024
    int curPoi = m_savedPos;

    // 处理所有IQ数据对（每8字节包含一个I和一个Q）
    for (int i = 0; i < dataSize; i += 8)
    {
        // 检查中间缓冲区是否已满（一个数据包的容量）
        if (curPoi >= CW_POINT_NUM * 4 + LINEDATAHEADLEN)
        {
            sendCount++;
            if (sendCount == 1)
            {
                // 第一个数据包：复制头部和第一组IQ数据到最终缓冲区
                // 布局：[头部][第一组I数据][第一组Q数据][预留空间]
                memcpy(m_Buffertpool, data, LINEDATAHEADLEN);
                memcpy(m_Buffertpool2, m_Buffertpool, LINEDATAHEADLEN + CW_POINT_NUM * 2 * 4);
            }
            else if (sendCount == 2)
            {
                // 第二个数据包：复制第二组IQ数据到最终缓冲区
                // 最终布局：[头部][第一组I数据][第一组Q数据][第二组I数据][第二组Q数据]
                memcpy(&m_Buffertpool2[LINEDATAHEADLEN + CW_POINT_NUM * 2 * 4], &m_Buffertpool[LINEDATAHEADLEN],
                       CW_POINT_NUM * 2 * 4);

                // 将完整的IQIQ格式数据包发送到处理队列
                m_IqDataProcess->enqueue(IQData(ByteBuffer(m_Buffertpool2, CW_POINT_NUM * 4 * 4 + LINEDATAHEADLEN),
                                                BFDataType::CWData, CW_POINT_NUM, 4, 0, 4,
                                                IQFormat4Zeus::IACC123QACC123));
                // 清空最终缓冲区
                memset(m_Buffertpool2, 0, CW_POINT_NUM * 4 * 4 + 16);
                sendCount = 0;
            }
            // 清空中间缓冲区
            memset(m_Buffertpool, 0, CW_POINT_NUM * 2 * 4 + 16);
            // 重置位置指针
            curPoi = m_savedPos = LINEDATAHEADLEN;
        }

        // 将原始IQ数据拷贝到中间缓冲区（分离I和Q）
        // I数据存储在前半部分：offset = curPoi
        memcpy(&m_Buffertpool[curPoi], &data[LINEDATAHEADLEN + i], 4);
        // Q数据存储在后半部分：offset = curPoi + CW_POINT_NUM * 4
        memcpy(&m_Buffertpool[curPoi + CW_POINT_NUM * 4], &data[LINEDATAHEADLEN + i + 4], 4);

        curPoi += 4; // 每次增加4字节（一个I或Q值的大小）
    }
    m_savedPos = curPoi;
    return -1;
}
