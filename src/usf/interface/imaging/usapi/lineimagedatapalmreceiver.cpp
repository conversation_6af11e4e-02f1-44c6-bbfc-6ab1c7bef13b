#include "lineimagedatapalmreceiver.h"
#include "bytebuffer.h"
#include "setting.h"
#include "generalinfo.h"
#include <QDebug>
#include <QTimer>
#include "sonoparameters.h"
#include "bfpnames.h"
#include "logger.h"
#include "assertlog.h"
#include "probeparameters.h"
#include "fpscalculator.h"
#include "appsetting.h"
#include "iglpreprocessingcreater.h"
#include "imagebufferdef.h"
#include "imagebuffergroup.h"
#include "lineimagedatainfostruct.h"

LOG4QT_DECLARE_STATIC_LOGGER(logpalm, LineImageDataPALMReceiver)

LineImageDataPALMReceiver::LineImageDataPALMReceiver(IGlPreprocessingCreater* rawImageBuffers)
    : LineImageDataReceiver(rawImageBuffers)
    , m_FpsCalculator(new FpsCalculator())
{
    initUDPBufferManager();
    m_FpsCalculator->setAdditionInfo("Package Infos");
    connect(m_FpsCalculator, SIGNAL(update(float)), this, SLOT(showFps(float)));

    m_LineTypeStatisticCal[LineData::Line_B] = 0;
    m_LineTypeStatisticCal[LineData::Line_C_Vel] = 0; // Color velocity
    m_LineTypeStatisticCal[LineData::Line_C_SD] = 0;  // Color Variance
    m_LineTypeStatisticCal[LineData::Line_Power] = 0;
    m_LineTypeStatisticCal[LineData::Line_Direct_Power] = 0; // Directional Power
    m_LineTypeStatisticCal[LineData::Line_TDI] = 0;
    m_LineTypeStatisticCal[LineData::Line_4D] = 0;
    m_LineTypeStatisticCal[LineData::Line_Elasto] = 0;
    m_LineTypeStatisticCal[LineData::Line_M] = 0;
    m_LineTypeStatisticCal[LineData::Line_D] = 0;
    m_LineTypeStatisticCal[LineData::Line_CWD] = 0;
    m_LineTypeStatisticCal[LineData::Line_CM] = 0;  // Color M
    m_LineTypeStatisticCal[LineData::Line_ECG] = 0; // ECG 作为独立线传输，
    m_LineTypeStatisticCal[LineData::Line_Sound] = 0;

    m_LineTypeStatistic = m_LineTypeStatisticCal;
    m_LineTypeStatistic[-1] = 0;
    m_LossDataCount = 0;
    // 2025-06-12 Modiby by AlexWang 使用性能更佳的QElapsedTimer来计算超时
    m_ReceiveElapsedTimer.start();
}

LineImageDataPALMReceiver::~LineImageDataPALMReceiver()
{
}

void LineImageDataPALMReceiver::showFps(float value)
{
}

void LineImageDataPALMReceiver::onBeforeSonoParametersChange()
{
    if (m_SonoParameters != NULL)
    {
        LineImageDataReceiver::onBeforeSonoParametersChange();
        disconnect(m_SonoParameters->parameter(BFPNames::SampleVolumeMMStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSampleVolmeMMChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::BaseLineStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onBaseLineChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SampleVolumeTDIMMStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSampleVolmeMMChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::BaseLineDTDIStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onBaseLineChanged(QVariant)));
    }
}

void LineImageDataPALMReceiver::onSonoParametersChanged()
{
    LineImageDataReceiver::onSonoParametersChanged();
    connect(m_SonoParameters->parameter(BFPNames::SampleVolumeMMStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onSampleVolmeMMChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::BaseLineStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onBaseLineChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::SampleVolumeTDIMMStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onSampleVolmeMMChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::BaseLineDTDIStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onBaseLineChanged(QVariant)));
}

bool LineImageDataPALMReceiver::shouldEnqueue(const LineDataHead* head)
{
#ifdef USE_TARGET_PALM
    //掌超udp 会丢帧 打开comound的时候 需要处理 steer的连续性
    bool needleModel = false;
    bool shouldEnqueue = true;
    // 2025-06-10 Modify by AlexWang 父类增加imageEventArgs()接口，减少动态转换
    IRawImageBufferDataSetter* bufferGroup = m_RawImageBuffers->activeDataSetter();
    if (!Setting::instance().defaults().isIODeviceVirtual() && bufferGroup != nullptr)
    {
        int scpd = bufferGroup->scpdValue();
        needleModel = m_SonoParameters->pBV(BFPNames::NeedleModeStr);
        if (!m_beforeSteerList.empty() && head->FrameFlag == LineData::Frame_Begin && scpd > 0 &&
            head->LineType == LineData::Line_B)
        {
            int lastSteer = m_beforeSteerList.at(0);
            if (m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr))
            {
                //梯形成像时，偏转帧的数据是左右偏转各两个，对应steer的值是1、2、3、4，scpd的值为3
                shouldEnqueue = lastSteer < scpd + 1 ? (head->Steering == lastSteer + 1) : (head->Steering == 1);
            }
            else if (!needleModel)
            {
                QList<int> storeValues;
                // 2025-06-10 Modify by AlexWang 合并判断条件
                int count = qMin(scpd, m_beforeSteerList.size());
                storeValues = m_beforeSteerList.mid(0, count);
                storeValues.prepend(head->Steering);

                // qCritical() << PRETTY_FUNCTION << "storeValues.size(): "<< storeValues.size();
                // qCritical() << PRETTY_FUNCTION << "scpd: " << scpd;

                if (storeValues.size() != scpd + 1)
                {
                    // qCritical() << PRETTY_FUNCTION << " storeValues.size() != scpd + 1 if entering
                    // here"<<(((storeValues[1] + 1) % (scpd + 1)) != storeValues[0]);

                    if (storeValues.size() >= 2 && ((storeValues[1] + 1) % (scpd + 1)) != storeValues[0])
                        shouldEnqueue = false;
                }
                else
                {
                    for (int i = 0; i < scpd + 1; i++)
                    {
                        if (!storeValues.contains(i))
                        {
                            qCritical() << PRETTY_FUNCTION
                                        << "(!storeValues.contains(i)):" << (!storeValues.contains(i));
                            shouldEnqueue = false;
                            break;
                        }
                    }
                }
            }
            else
            {
            }
        }
        int frameNo = (head->Reserve[0]) << 8 | (head->Reserve[1]);
        int tempthis = (head->LineNo[1]) << 8 | (head->LineNo[0]);

        // //根据线数据协议，线号占两个字节，低10位是线号，高1位是iqflag，中间的位是iq积累次数
        int lineNo = tempthis & 0x3FF;
        // qDebug() << PRETTY_FUNCTION
        //          << "LineType: " << LineData::lineTypeString(head->LineType)
        //          << "lineNo: " << lineNo
        //          << "start line: " << m_SonoParameters->pIV(BFPNames::StartLineStr)
        //          << "stop line: " << m_SonoParameters->pIV(BFPNames::StopLineStr)
        //          << "frameNo: " << frameNo
        //          << "head->FrameFlag: " << head->FrameFlag
        //          << "scpd: " << m_SonoParameters->pIV(BFPNames::ScpdStr)
        //          << "Steering: " << head->Steering;
        if (!shouldEnqueue)
        {
            logpalm()->error() << __FUNCTION__ << "steer is discontinuous!!!"
                               << " lineType:" << LineData::lineTypeString(head->LineType)
                               << " steer:" << head->Steering << " frameNo: " << frameNo << " lineNo: " << lineNo;
            m_continusNotEnqueueCount++;
            if (m_continusNotEnqueueCount >= 5)
            {
                m_beforeSteerList.clear();
                m_continusNotEnqueueCount = 0;
            }
        }

        //全M模式下没有B数据，因此不需要进行下一步的操作
        if ((SystemScanMode)(m_SonoParameters->pIV(BFPNames::SystemScanModeStr)) != SystemScanModeM)
        {
            if (head->LineType ==
                LineData::
                    Line_M) // M数据入队前，保证足够compound数量的B数据先入队。这样存图存电影时可保证有足够的前置B帧
            {
                int bFrameCount = bufferGroup->frameCountByFrameType(ImageBufferDef::B_Data);
                //            int frameCount =
                //            dynamic_cast<ImageBufferGroup*>(m_RawImageBuffers->activeDataSetter())->frameCount();

                if (bFrameCount <= scpd)
                {
                    shouldEnqueue = false;
                    //                qDebug() << "Ignore M data before B" << bFrameCount << frameCount;
                }
            }
        }
    }
    return shouldEnqueue;
#else
    return true;
#endif
}

void LineImageDataPALMReceiver::updateBeforeSteerList(int steering)
{
    if (!Setting::instance().defaults().isIODeviceVirtual())
    {
        if (m_beforeSteerList.size() > 10)
            m_beforeSteerList.pop_back();
        if (!m_SonoParameters->pBV(BFPNames::NeedleModeStr))
            m_beforeSteerList.prepend(steering);
    }
}

void LineImageDataPALMReceiver::clearDopplerStatus()
{
    //    SyncModeType syncMode = (SyncModeType)m_SonoParameters->pIV(BFPNames::SyncModeStr);
    //    if(m_SonoParameters->isRealTime() && (Sync_CD == syncMode || Sync_D == syncMode) )
    //        udpbufmanager::getinstance(LineData::Line_C_SD)->clear();
}

void LineImageDataPALMReceiver::delayPwRecv()
{
    if (!m_IgnorePwLine)
    {
        m_IgnorePwLine = true;
        QTimer::singleShot(500, this, [this] { m_IgnorePwLine = false; });
    }
}
void pwaudiocallback4receive(void* userdata, void* data, int dataSize)
{

    //   static int count=0;
    //   static qint64 timefirst=QDateTime::currentMSecsSinceEpoch();
    //   static qint64 timefirstb=QDateTime::currentMSecsSinceEpoch();
    //   qDebug()<<"put"<<QDateTime::currentMSecsSinceEpoch()-timefirst<<(count++)<<(QDateTime::currentMSecsSinceEpoch()-timefirstb)*1.0/(count-1);
    //   timefirst=QDateTime::currentMSecsSinceEpoch();

#if 0
    //存储1分钟的声音数据，测试使用 TODO:在后台添加开关进行保存
    static uint8_t *audiobuf=new uint8_t[1024*40*60];
    static qint64 timestart=QDateTime::currentSecsSinceEpoch();
    static int qb = 0;
    static bool saved = false;
    if(QDateTime::currentSecsSinceEpoch() - timestart < 60 ){
        memcpy(&audiobuf[qb*1024],data,1024);
        qb++;
    }else{
        if(!saved){
            FILE *file = fopen("./audiotest_.pcm", "wb");
            if(file != NULL)
            {
                fseek(file, 0, SEEK_END);
                fwrite(audiobuf, qb*1024, 1, file);
                fclose(file);
                saved = true;
                qDebug()<<"pwaudiocallback4receive ############# save finished";
            }
        }
    }
#endif

    static uchar* dataaudiojoin = new uchar[dataSize + LINEDATAHEADLEN];
    memcpy(dataaudiojoin, userdata, LINEDATAHEADLEN);
    dataaudiojoin[3] = LineData::Line_Sound;
    uchar* p = (uchar*)userdata;
    qintptr classptraddress = (qintptr)&p[LINEDATAHEADLEN];
    LineImageDataReceiver* pclass = reinterpret_cast<LineImageDataReceiver*>(classptraddress);

    //     static int qq=0;
    //     if(qq++==0){
    //         memcpy(pp,userdata,16);
    //         pp[3]=LineData::Line_Sound;

    //     }
    //     return;
    memcpy(&dataaudiojoin[LINEDATAHEADLEN], data, dataSize);

    // memcpy(&dataaudiojoin[LINEDATAHEADLEN],&audiogenerate[tt],dataSize);
    ByteBuffer bbfa((uchar*)dataaudiojoin, dataSize + LINEDATAHEADLEN);
    pclass->receive(bbfa);
    //      tt+=1024;
    //      if(tt>=16000*10*2)
    //         tt=0;
}

void pwlinecallback4receive(void* userdata, void* data, int dataSize)
{
#if 0 //测试处理后帧率
    static int count=0;
    static qint64 timefirst=QDateTime::currentMSecsSinceEpoch();
    if(count>1500){
        qDebug()<<PRETTY_FUNCTION<<count/((QDateTime::currentMSecsSinceEpoch()-timefirst)/1000.0)<<0;
        count=0;
        timefirst=QDateTime::currentMSecsSinceEpoch();
    }
     count+=8;
#endif

    // tianyi remove
    //    LineImageDataReceiver *pclass=reinterpret_cast<LineImageDataReceiver*>((void*)userdata);
    //    uchar* pret=(uchar*)data;
    //    static int usbLineSize=dataSize/PWCOUNT;
    //    for(int i=0;i<PWCOUNT;i++){//pw 会有8根线 保持和原来一致
    //        pret[i*usbLineSize+3]=LineData::Line_D;
    //    }
    //    ByteBuffer bbf(pret,dataSize);//8根线
    //    if(pclass->m_IgnorePwLine) return;
    //    pclass->receive(bbf);
}

void LineImageDataPALMReceiver::initUDPBufferManager()
{
}

void LineImageDataPALMReceiver::receive(const ByteBuffer& oldData)
{
    static UsbAndWifiLineDataSize usbWifiLineDataSize = lineDataSize();
    ByteBuffer data(oldData.data(), oldData.len());
    // 2025-06-12 Modiby by AlexWang 使用性能更佳的QElapsedTimer来计算超时
    static QElapsedTimer statistisTime;
    if (!statistisTime.isValid())
    {
        statistisTime.start();
    }
    bool isPresetMode = AppSetting::isPresetMode();
    LineDataHead* dataHead = (LineDataHead*)(oldData.data());
    LineData::LineType lineType = (LineData::LineType)dataHead->LineType;

    //数据接收时，仅获取一次当前时间戳，不能每个图像包获取一次时间戳，频繁获取时间戳会影响CPU性能
    if (isPresetMode)
    {
        if (statistisTime.hasExpired(1000))
        {
            // 2025-06-11 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
            // 绑定到临时对象，减少遍历时临时拷贝kyes
            const auto& keys = m_LineTypeStatisticCal.keys();
            for (int type : keys)
            {
                m_LineTypeStatistic[type] = m_LineTypeStatisticCal.value(type);
                m_LineTypeStatistic[-1] += m_LineTypeStatistic.value(type);
                m_LineTypeStatisticCal[type] = 0;
            }
            statistisTime.restart();
        }
    }
    if (m_ReceiveElapsedTimer.hasExpired(LINEIMAGEDATALOSSIMAGETIME * (m_LossDataCount + 1)))
    {
#ifdef LINEIMAGEDATALOSS_DEBUG_ENABLE
        qCritical() << "LineImageDataPALMReceiver::receive  m_LossDataCount:" << ++m_LossDataCount;
#else
        ++m_LossDataCount;
#endif
    }
#ifdef USE_TARGET_PALM
    if (!m_isIODeviceVirtual && lineType != LineData::Line_ECG) // 避免频繁读取数据引起的单例多线程问题
    {
        data.clear();
        if (oldData.len() % usbWifiLineDataSize.first == 0)
        {

            optimizePacket(oldData);

            data.setData(m_newDataBuffer, m_newDataBufferSize);
            GeneralInfo::instance().m_probeTransmitType = 1; // usb传输 方式
        }
        else if (oldData.len() % usbWifiLineDataSize.second == 0)
        {
            data.setData(oldData.data(), oldData.len());
            GeneralInfo::instance().m_probeTransmitType = 0; // udp传输 方式
        }
        else
        {
            qDebug() << PRETTY_FUNCTION << "Error with packet size" << oldData.len();
        }
    }
#endif
    int lines = data.len() / usbWifiLineDataSize.second;
    //    logpalm()->info() << "!!!!!!!" << PRETTY_FUNCTION
    //             << ", data len: " << data.len()
    //             << ", usbWifiLineDataSize.second: " << usbWifiLineDataSize.second
    //             << ", lines: " << lines;
    for (int i = 0; i < lines; ++i)
    {
        ByteBuffer bb = ByteBuffer(data.data() + i * usbWifiLineDataSize.second,
                                   usbWifiLineDataSize.second - m_RedundantPoints * 2);
        LineDataHead* dataHead = (LineDataHead*)(bb.data());
        if (dataHead->PackageType == LineData::Package_Image)
        {
            m_ReceiveElapsedTimer.restart();
        }

        if (isPresetMode)
        {
            LineData::LineType lineType = (LineData::LineType)dataHead->LineType;
            if (dataHead->PackageType == LineData::Package_Image)
            {
                m_LineTypeStatisticCal[lineType] += 1;
            }
            else
            {
                //                m_FpsCalculator->cal();
            }
        }

        handleReceiverOrder(bb);
    }
}

void LineImageDataPALMReceiver::onSampleVolmeMMChanged(const QVariant& value)
{
    clearDopplerStatus();
    delayPwRecv();
}

void LineImageDataPALMReceiver::onBaseLineChanged(const QVariant& value)
{
    clearDopplerStatus();
    delayPwRecv();
}

#if 0
void LineImageDataPALMReceiver::onTDIEnChanged(const QVariant &value)
{
    udpbufmanager *udpordmg = udpbufmanager::getinstance(LineData::Line_C_Vel);
    if (udpordmg)
    {
        udpordmg->setTDIEn(value.toBool());
        onColorImageModeChanged(m_SonoParameters->pIV(BFPNames::ColorImageModeStr));
    }
}
#endif

#if 0
void LineImageDataPALMReceiver::onColorImageModeChanged(const QVariant &value)
{
    udpbufmanager *udpordmg = udpbufmanager::getinstance(LineData::Line_C_Vel);
    if (udpordmg)
    {
        QList<QString> paramsets = udpordmg->m_IQOperationParamset.keys();
        for(int i=0; i< paramsets.size(); i++)
        {
            if("" != paramsets[i])
            {
                disconnect(m_SonoParameters->parameter(paramsets[i]), SIGNAL(valueChanged(const QVariant&)),
                        udpordmg, SLOT(onSonoParametersValueChanged4IqOperation(const QVariant&)));
            }
        }
        udpordmg->onParamChanged(false);
        udpordmg->registersig = false;
        udpordmg->resetIQOperationParamset(value.toInt());
    }
}
#endif

void LineImageDataPALMReceiver::onFreezeChanged(const QVariant& value)
{
    LineImageDataReceiver::onFreezeChanged(value);
    if (value.toBool())
    {
        clearDopplerStatus();
    }
    else
    {
        m_LossDataCount = 0;
        m_ReceiveElapsedTimer.restart();
    }
}
