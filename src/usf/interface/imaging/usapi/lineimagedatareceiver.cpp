#include "lineimagedatareceiver.h"
#include "assertlog.h"
#include "bfpnames.h"
#include "bufferstates.h"
#include "bufferunit.h"
#include "bytebuffer.h"
#include "datasave.h"
#include "formula.h"
#include "fpscalculator.h"
#include "frameinfo.h"
//#include "D:/work_pangu/xunit/src/corelib/chisonultrasound/opengladapter/iglpreprocessingcreater.h"
#include "appsetting.h"
#include "generalinfo.h"
#include "iglpreprocessingcreater.h"
#include "imagesavehelper.h"
#include "irawimagebufferdatasetter.h"
#include "linedatahead.h"
#include "linehwinfo2wholehwinfo.h"
#include "logger.h"
#include "memoryleakcheck.h"
#include "parameter.h"
#include "probeparameters.h"
#include "resource.h"
#include "setting.h"
#include "sonoparameters.h"
#include "systemscanmodeclassifier.h"
#include "util.h"
#include <QDir>
#include <QElapsedTimer>

#define SAVE_RAWDATA (0)

#define VALID_DATA_HEAD (16)      // head valid data
#define VALID_DATA_MODEL_BM (504) // B&M valid data
#define VALID_DATA_MODEL_C (1008) // C valid data

LOG4QT_DECLARE_STATIC_LOGGER(log, LineImageDataReceiver)

int LineImageDataReceiver::m_BReceiveFPS = 0;
int LineImageDataReceiver::m_MReceiveFPS = 0;

LineImageDataReceiver::LineImageDataReceiver(IGlPreprocessingCreater* rawImageBuffers)
    : ImageDataReceiverBase(rawImageBuffers)
    , m_newDataBuffer(NULL)
    , m_IgnoreFistFrame(false)
    , m_RedundantPoints(0)
    , m_continusNotEnqueueCount(0)
    , LINE_COUNT_PER_PACKAGE(2)
    , m_DataSave(new DataSave("receiveData"))
    , m_FpsCalculator(new FpsCalculator())
    , m_LineDataHWInfo(new LineDataHWInfo())
    , m_OldOrderNo(-1)
    , m_IsFrozen(false)
#ifdef USE_TARGET_PALM
    , m_IgnorePwLine(false)
#endif
    , m_FixedLineStartup(false)
    , m_HasCInCM(false)
    , m_CLineDataMid(nullptr)
    , m_IsECG(false)
{
    memset(m_LineDataHWInfo, 0, sizeof(LineDataHWInfo));

    m_FpsCalculator->setAdditionInfo("line buffer enque");
    m_FpsCalculator->setLogOut(true);

    // 避免频繁读取数据引起的单例多线程问题
    m_isIODeviceVirtual = Setting::instance().defaults().isIODeviceVirtual();
    connect(m_FpsCalculator, SIGNAL(update(float)), this, SLOT(showFps(float)));
    CHECK_NEW(DataSave, m_DataSave);
    CHECK_NEW(FpsCalculator, m_FpsCalculator);
    CHECK_NEW(LineDataHWInfo, m_LineDataHWInfo);

    //    m_LineTypePerCount[ LineData::Line_B ] = 2;
    //    m_LineTypePerCount[ LineData::Line_C_Vel ] = 2;  // Color velocity
    //    m_LineTypePerCount[ LineData::Line_C_SD ] = 2;  // Color Variance
    //    m_LineTypePerCount[ LineData::Line_Power ] = 2;
    //    m_LineTypePerCount[ LineData::Line_Direct_Power ] = 2;  // Directional Power
    //    m_LineTypePerCount[ LineData::Line_TDI ] = 2;
    //    m_LineTypePerCount[ LineData::Line_4D ] = 2;
    //    m_LineTypePerCount[ LineData::Line_Elasto ] = 2;
    //    m_LineTypePerCount[ LineData::Line_M ] = 8;
    //    m_LineTypePerCount[ LineData::Line_D ] = 8;
    //    m_LineTypePerCount[ LineData::Line_CWD ] = 8;
    //    m_LineTypePerCount[ LineData::Line_CM ] = 2;  //Color M
    //    m_LineTypePerCount[ LineData::Line_ECG ] = 2;//ECG 作为独立线传输，
    //    m_LineTypePerCount[ LineData::Line_Sound ] = 2;

    //    m_LineTypeStatisticCal[ LineData::Line_B ] = 0;
    //    m_LineTypeStatisticCal[ LineData::Line_C_Vel ] = 0;  // Color velocity
    //    m_LineTypeStatisticCal[ LineData::Line_C_SD ] = 0;  // Color Variance
    //    m_LineTypeStatisticCal[ LineData::Line_Power ] = 0;
    //    m_LineTypeStatisticCal[ LineData::Line_Direct_Power ] = 0;  // Directional Power
    //    m_LineTypeStatisticCal[ LineData::Line_TDI ] = 0;
    //    m_LineTypeStatisticCal[ LineData::Line_4D ] = 0;
    //    m_LineTypeStatisticCal[ LineData::Line_Elasto ] = 0;
    //    m_LineTypeStatisticCal[ LineData::Line_M ] = 0;
    //    m_LineTypeStatisticCal[ LineData::Line_D ] = 0;
    //    m_LineTypeStatisticCal[ LineData::Line_CWD ] = 0;
    //    m_LineTypeStatisticCal[ LineData::Line_CM ] = 0;  //Color M
    //    m_LineTypeStatisticCal[ LineData::Line_ECG ] = 0;//ECG 作为独立线传输，
    //    m_LineTypeStatisticCal[ LineData::Line_Sound ] = 0;

    //    m_LineTypeStatistic = m_LineTypeStatisticCal;
    //    m_LineTypeStatistic[-1] = 0;
}

LineImageDataReceiver::~LineImageDataReceiver()
{
    CHECK_DELETE(DataSave, m_DataSave);
    CHECK_DELETE(FpsCalculator, m_FpsCalculator);
    CHECK_DELETE(LineDataHWInfo, m_LineDataHWInfo);
    QMutexLocker locker(&m_Mutex);
    setPaused(true);

    if (m_CLineDataMid != nullptr)
    {
        delete[] m_CLineDataMid;
    }

    delete m_DataSave;
    delete m_FpsCalculator;
    delete m_LineDataHWInfo;
    m_LineDataHWInfo = NULL;
    delete[] m_newDataBuffer;
    m_newDataBuffer = NULL;
}

const uchar* LineImageDataReceiver::hwInfoData() const
{
    return (uchar*)m_LineDataHWInfo;
}

void LineImageDataReceiver::onStart()
{
    m_OldOrderNo = -1;
}

void LineImageDataReceiver::onBeforeSonoParametersChange()
{
    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), m_DataSave,
                   SLOT(createFolder(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::DataSaveEnStr), SIGNAL(valueChanged(QVariant)), m_DataSave,
                   SLOT(setDataSaveEn(QVariant)));

        disconnect(m_SonoParameters->parameter(BFPNames::RedundantPointsStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onRedundantPointsChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FPSStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFPSChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr),
                   SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
                   SLOT(onBeforeSystemScanModeChanged(const QVariant&, QVariant&)));
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSystemScanModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ColorImageModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onColorImageModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::TDIEnStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onTDIEnChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::TrapezoidalModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onTrapezoidalModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFreqSpectrumChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onTriplexModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::QuadplexModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onQuadplexModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ECGEnStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onECGEnChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ScpdStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onScpdChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr),
                   SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
                   SLOT(onBeforeFreezeChanged(const QVariant&, QVariant&)));
        disconnect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFreezeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::NeedleModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onScpdChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FixedLineStartupStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFixedLineStartupChanged(QVariant)));
    }
}

void LineImageDataReceiver::onSonoParametersChanged()
{
    m_DataSave->createFolder(m_SonoParameters->parameter(BFPNames::SystemScanModeStr)->showValue());

    connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), m_DataSave,
            SLOT(createFolder(QVariant)));

    connect(m_SonoParameters->parameter(BFPNames::DataSaveEnStr), SIGNAL(valueChanged(QVariant)), m_DataSave,
            SLOT(setDataSaveEn(QVariant)));

    connect(m_SonoParameters->parameter(BFPNames::RedundantPointsStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onRedundantPointsChanged(QVariant)));

    connect(m_SonoParameters->parameter(BFPNames::FPSStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFPSChanged(QVariant)));

    connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr),
            SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
            SLOT(onBeforeSystemScanModeChanged(const QVariant&, QVariant&)));
    connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onSystemScanModeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::ColorImageModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onColorImageModeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::TDIEnStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onTDIEnChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::TrapezoidalModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onTrapezoidalModeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreqSpectrumChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onTriplexModeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::QuadplexModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onQuadplexModeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::ECGEnStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onECGEnChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::ScpdStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onScpdChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(beforeValueChanged(const QVariant&, QVariant&)),
            this, SLOT(onBeforeFreezeChanged(const QVariant&, QVariant&)));
    connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreezeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::NeedleModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onScpdChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::FixedLineStartupStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFixedLineStartupChanged(QVariant)));

    onSystemScanModeChanged(m_SonoParameters->pV(BFPNames::SystemScanModeStr));
    onRedundantPointsChanged(m_SonoParameters->pV(BFPNames::RedundantPointsStr));
}

bool LineImageDataReceiver::shouldEnqueue(const LineDataHead* head)
{
    Q_UNUSED(head);
    return true;
}

void LineImageDataReceiver::updateBeforeSteerList(int steering)
{
    Q_UNUSED(steering);
}

int LineImageDataReceiver::checkLineSize(IRawImageBufferDataSetter* dataSetter) const
{
    const QList<FrameUnitInfo>& groupInfo = dataSetter->frameGroupInfo();
    QList<int> lineSizes;
    foreach (const FrameUnitInfo& unitInfo, groupInfo)
    {
        foreach (const FrameInfo& frameInfo, unitInfo)
        {
            lineSizes.append(frameInfo.pointNum());
        }
    }

    int lineSize = 0;

    if (!lineSizes.isEmpty())
    {
        lineSize = lineSizes.first();
        lineSizes.removeAll(lineSize);
    }
    else
    {
        //在切换Layout时，新的 ImageBufferGroup 可能还没有正确设置FrameGroupInfo，此时不接收数据
        return lineSize;
    }

    // ASSERT_X_LOG(lineSizes.isEmpty(), "LineImageDataReceiver::checkLineSize", "Now app only support line's sizes are
    // equal each other.");

    return lineSize;
}

//#define LINE_COUNT_PER_PACKAGE 2
// extern const int LINE_COUNT_PER_PACKAGE = 2;

static const ImageBufferDef::RawDataMode BINARY_MAP_RAW_TYPE[4] = {
    ImageBufferDef::M_Data,   // Line_M
    ImageBufferDef::D_Data,   // Line_D
    ImageBufferDef::CWD_Data, // Line_CWD
    ImageBufferDef::CM_Data   // Line_CM
};

#ifndef NDEBUG

static bool randDraw()
{
    return !qgetenv("RAND_DRAW").isNull();
}

static const bool RAND_DRAW = randDraw();

#endif // NDEBUG

void LineImageDataReceiver::appendData(BufferUnit& frame, const FrameUnitInfo& frameInfos, const int frameIndex,
                                       unsigned char* lineData, const int bufferIndex, const int copyCount,
                                       const int lineNo, const int usbLineSize, const int bufferLineSize,
                                       const int bufferDataSize)
{
    //    IRawImageBufferDataSetter *dataSetter = m_RawImageBuffers->activeDataSetter();
    if (frame.isEmpty())
    {
        log()->error("frame empty ,skip copy data");
        return;
    }

    const FrameInfo& frameInfo = frameInfos.at(frameIndex);
    ByteBuffer& buffer = frame[frameIndex];

    for (int j = 0; j < copyCount; j++)
    {
        int index = (lineNo + j * frameInfo.lineInterval() - frameInfo.startLine()) / frameInfo.lineInterval();
        if (index >= 0 && index < frameInfo.lineNum())
        {
            memcpy(buffer.data() + index * bufferLineSize,
#ifndef NDEBUG
                   RAND_DRAW ? Formula::randData(bufferDataSize) : lineData + j * usbLineSize,
#else
                   lineData + j * usbLineSize,
#endif
                   bufferDataSize);
        }
        else
        {
            //            log()->warn(QString("index %1 out of [0~%2] range, startline: %3, lineNo: %4")
            //                        .arg(index)
            //                        .arg(frameInfo.lineNum())
            //                        .arg(frameInfo.startLine())
            //                        .arg(lineNo));
        }
    }
}

void LineImageDataReceiver::appendNoneECGData(BufferUnit& frame, const FrameUnitInfo& frameInfos, const int frameIndex,
                                              unsigned char* lineData, const int bufferIndex, const int copyCount,
                                              const int lineNo, const int lineSize)
{
    appendData(frame, frameInfos, frameIndex, lineData, bufferIndex, copyCount, lineNo,
               frameInfos.at(frameIndex).lineSize(), frameInfos.at(frameIndex).lineSize(),
               frameInfos.at(frameIndex).lineSize());
}

void LineImageDataReceiver::appendDataForECG(BufferUnit& frame, const FrameUnitInfo& frameInfos, const int frameIndex,
                                             unsigned char* lineData, const int bufferIndex, const int copyCount,
                                             const int lineNo, const int usbLineSize, const int bufferLineSize,
                                             const int bufferDataSize, const int posNo)
{
    //    IRawImageBufferDataSetter *dataSetter = m_RawImageBuffers->activeDataSetter();

    if (frame.isEmpty())
    {
        log()->trace("frame empty ,skip copy data");
        return;
    }

    const FrameInfo& frameInfo = frameInfos.at(frameIndex);
    ByteBuffer& buffer = frame[frameIndex];

    for (int j = 0; j < copyCount; j++)
    {
        int index = (lineNo + j * frameInfo.lineInterval() - frameInfo.startLine()) / frameInfo.lineInterval();
        if (index >= 0 && index < frameInfo.lineNum())
        {
            //            dataSetter->updateNextRearStartTime(bufferIndex);
            memcpy(buffer.data() + index * bufferLineSize,
#ifndef NDEBUG
                   RAND_DRAW ? Formula::randData(bufferDataSize) : lineData + posNo * usbLineSize,
#else
                   lineData + posNo * usbLineSize,
#endif
                   bufferDataSize);
        }
        else
        {
            //            log()->warn("index %1 out of [0~%2] range", index, frameInfo.lineNum());
        }
    }
}

void LineImageDataReceiver::appendECGData(const int ecgNumber, const FrameUnitInfo& frameInfos, const int frameIndex,
                                          unsigned char* lineData, const int bufferIndex, const int no)
{
    // ecgNumber = ((ecgNumber&0x00ff)<<8) + ((ecgNumber&0xff00)>>8);
    // log()->fatal("appendECGData [ecgNumber %1]",ecgNumber);
    IRawImageBufferDataSetter* dataSetter = m_RawImageBuffers->activeDataSetter();
    for (int i = 0; i < ecgNumber; ++i)
    {
        BufferUnit& frame = dataSetter->nextRear(bufferIndex);
        // ECG only store data in lower 16bit
        // higher 16bit store baseline
        appendDataForECG(frame, frameInfos, frameIndex, lineData, bufferIndex, 1, no, frameInfos.at(0).lineSize(),
                         frameInfos.at(0).lineSize(), frameInfos.at(0).lineSize(), i);
        // ECG belongs to wave type
        // refresh controlled by WaveImageBuffer internal thread
        //        dataSetter->enqueue(bufferIndex, false);
        m_RawImageBuffers->enqueue(bufferIndex, false);
    }
}
//#ifdef USE_TARGET_PALM

// apple 的接收数据预处理部分-----------------------------------------------------------
// static uint8_t *audiogenerate=new uint8_t[16000*2*35];
// static bool staticinitblock = []() {
//     int i=0;
//     int j=0;
//     for(;i<16000*10;i++,j+=2){
//         double t=(1.0/16000)*i;
//         int y ;
//         if(i<16000*5)
//              y = (sin(2*3.141926*500*t)+1)*128;
//         else
//              y = (sin(2*3.141926*500*t)+1)*128;
//         audiogenerate[j]=y;
//         audiogenerate[j+1]=y;
//     }
//     return true;
// }();
// extern void writebinarydata(unsigned char *src,const int len,char *pname);
// static uchar pp[1040];

void LineImageDataReceiver::optimizePacket(const ByteBuffer& data)
{
    QMutexLocker locker(&m_Mutex);
    int linePackCnt = m_SonoParameters->pIV(BFPNames::LinePackageCountStr);
    int pointNum = m_SonoParameters->pIV(BFPNames::PointNumPerLineStr);
    int redundantPointNum = m_SonoParameters->pIV(BFPNames::RedundantPointNumPerLineStr);
    int usbLineDataSize = (pointNum - redundantPointNum) * LINE_COUNT_PER_PACKAGE + m_RedundantPoints * 2;
    int usbLineSize = sizeof(LineDataHead) + usbLineDataSize;
    int wifiLineSize = sizeof(LineDataHead) + pointNum * LINE_COUNT_PER_PACKAGE + m_RedundantPoints * 2;
    m_newDataBufferSize =
        linePackCnt * (pointNum * LineData::LINE_UNIT_COUNT_PER_PACK + sizeof(LineDataHead) + m_RedundantPoints * 2);
    if (m_newDataBuffer == NULL)
    {
        m_newDataBuffer = new uchar[m_newDataBufferSize];
    }

    // lines: 一个data的数据包的数目
    const int lines = data.len() / usbLineSize;
    if (data.len() % usbLineSize != 0)
    {
        return;
    }

    memset(m_newDataBuffer, 0x00, m_newDataBufferSize);

    for (int i = 0; i < lines; i++)
    {
        LineDataHead* lineDataHead = (LineDataHead*)(data.data() + i * usbLineSize);
        uchar* oldDataHead = data.data() + i * usbLineSize;
        uchar* newDataHead = m_newDataBuffer + i * wifiLineSize;
        memcpy(newDataHead, oldDataHead, VALID_DATA_HEAD);
        if (lineDataHead->LineType == LineData::Line_B || lineDataHead->LineType == LineData::Line_M ||
            lineDataHead->LineType == LineData::Line_C_SD)
        {
            memcpy(newDataHead + 16, oldDataHead + 16, VALID_DATA_MODEL_BM);
            memcpy(newDataHead + 528, oldDataHead + 520, VALID_DATA_MODEL_BM);
        }
        else
        {
            memcpy(newDataHead + 16, oldDataHead + 16, VALID_DATA_MODEL_C);
        }
    }
}

LineImageDataReceiver::UsbAndWifiLineDataSize LineImageDataReceiver::lineDataSize()
{
    UsbAndWifiLineDataSize usbWifiLineDataSize;
    QMutexLocker locker(&m_Mutex);
    int pointNum = m_SonoParameters->pIV(BFPNames::PointNumPerLineStr);                                    // 512
    int redundantPointNum = m_SonoParameters->pIV(BFPNames::RedundantPointNumPerLineStr);                  // 8
    int usbLineDataSize = (pointNum - redundantPointNum) * LINE_COUNT_PER_PACKAGE + m_RedundantPoints * 2; // 0
    usbWifiLineDataSize.first = sizeof(LineDataHead) + usbLineDataSize;
    usbWifiLineDataSize.second = sizeof(LineDataHead) + pointNum * LINE_COUNT_PER_PACKAGE + m_RedundantPoints * 2;
    return usbWifiLineDataSize;
}

void LineImageDataReceiver::receive(const ByteBuffer& data)
{
#if SAVE_RAWDATA
    static int icount = 0;
    Util::Mkdir("rawdata");
    QFile qfile(QString("rawdata/rawdata_%1.dat").arg(icount++, 4, 10, QLatin1Char('0')));
    if (qfile.open(QIODevice::WriteOnly))
    {
        qfile.write((const char*)data.data(), data.len());
    }
#endif
#ifdef SYS_APPLE
    LineDataHead* lineDataHead = (LineDataHead*)(data.data());
    if (GeneralInfo::instance().m_recvDataWait && lineDataHead->PackageType == LineData::Package_Image)
        return;
#endif
    QMutexLocker locker(&m_Mutex);
    //    m_FpsCalculator->cal();

    //    int tableID = m_SonoBuffers->pIV(BFPNames::TableIDStr);
    int pointNum = m_SonoParameters->pIV(BFPNames::PointNumPerLineStr);
#ifdef USE_PREPROCESS
    int usbLineDataSize = pointNum * LINE_COUNT_PER_PACKAGE;
#else
    int usbLineDataSize = pointNum * LINE_COUNT_PER_PACKAGE + m_RedundantPoints * 2;
#endif
    int usbLineSize = sizeof(LineDataHead) + usbLineDataSize;
    // lines: 一个data的数据包的数目
    const int lines = data.len() / usbLineSize;

    if (data.len() % usbLineSize != 0)
    {
        log()->error() << PRETTY_FUNCTION << " usb data size can not be divided by usb line size"
                       << " data.len():" << data.len() << " usbLineSize:" << usbLineSize;
        return;
    }

    ASSERT_X_LOG(data.len() % usbLineSize == 0, PRETTY_FUNCTION, "usb data size can not be divided by usb line size");

    //    if(m_IsPaused || m_IsFrozen)
    //    {
    //        m_OldOrderNo = -1;
    //        handlePacketOnFrezon(data, lines, usbLineSize);
    //        return;
    //    }

    IBufferWriteLocker rawImageBuffersWriteLocker(m_RawImageBuffers, PRETTY_FUNCTION);
    IRawImageBufferDataSetter* dataSetter = m_RawImageBuffers->activeDataSetter();
    IBufferWriteLocker dataSetterWriteLocker(dataSetter, PRETTY_FUNCTION);
    // log()->fatal("write lock start");

    if (m_PausedChanged)
    {
        // TODO:此处有如下在设计方面需要调整的，1)接收线程中对缓存做clear操作有职责不单一的情况；
        // 2)就算执行clear操作，但变更m_PausedChanged的指令存在不合理情况，在多线程场景下会出现意想不到的结果
        m_PausedChanged = false;
        //        qDebug() << PRETTY_FUNCTION << "m_PausedChanged changed";
        dataSetter->clear(false);
        m_beforeSteerList.clear();
        m_continusNotEnqueueCount = 0;
    }

    if (dataSetter->bufferCount() == 0)
    {
        m_OldOrderNo = -1;
        log()->error() << PRETTY_FUNCTION << " dataSetter->bufferCount() = 0";
        return;
    }

    for (int i = 0; i < lines; i++)
    {
        LineDataHead* lineDataHead = (LineDataHead*)(data.data() + i * usbLineSize);
        if (*((unsigned short*)lineDataHead->SyncHead) != LineData::SyncHead_Flag)
        {
            log()->error("line data sync head error : " +
                         QString("0x%1").arg(*((unsigned short*)lineDataHead->SyncHead), 4, 16, QChar('0')));
            m_OldOrderNo = -1;
            break;
        }

        unsigned char* lineData = reinterpret_cast<unsigned char*>(lineDataHead) + sizeof(LineDataHead);
        //        if (lineDataHead->LineType == LineData::Line_CWD)
        //        {
        //             log()->debug() << PRETTY_FUNCTION
        //                            << ", receive CW data...======="
        //                            << ", data.len():" << data.len()
        //                            << ", usbLineDataSize: " << usbLineDataSize
        //                            << ", usbLineSize:" << usbLineSize
        //                            << ", lines: " << lines;
        //        }
        //        if (lineDataHead->LineType == LineData::Line_Sound)
        //        {
        //             log()->debug() << PRETTY_FUNCTION
        //                            << ", receive PW Sound data...======="
        //                            << ", data.len():" << data.len()
        //                            << ", usbLineDataSize: " << usbLineDataSize
        //                            << ", usbLineSize:" << usbLineSize
        //                            << ", lines: " << lines;
        //        }

        if (lineDataHead->PackageType == LineData::Package_Image)
        {
            if (lineDataHead->LineType == LineData::Line_ECG)
            {
                unsigned char data = lineData[0];
                emit ECGInfoChanged(data);
            }
            // 合成CM线数据，并对linehead的linetype进行修正
            if (m_SonoParameters->pBV(BFPNames::ColorMStr))
            {
                if (lineDataHead->LineType == LineData::Line_C_Vel)
                {
                    // colorM 模式下会receive多根线,这里只取中间那根线
                    if (i < lines / 2)
                    {
                        continue;
                    }

                    if (m_CLineDataMid == nullptr)
                    {
                        m_CLineDataMid = new uint16_t[usbLineDataSize / sizeof(uint16_t)];
                    }

                    memcpy(m_CLineDataMid, lineData, pointNum * LINE_COUNT_PER_PACKAGE);
                    m_HasCInCM = true;
                    break;
                }
                else if (lineDataHead->LineType == LineData::Line_M)
                {
                    // 接收到M数据,且已有C数据时,调用mergeCMLines函数来完成线数据的合并
                    SystemScanMode mode = static_cast<SystemScanMode>(m_SystemScanMode);
                    if (m_HasCInCM && m_CLineDataMid != nullptr)
                    {
                        mergeCMLines(m_CLineDataMid, lineData, mode, pointNum);
                        lineDataHead->LineType = LineData::Line_CM;
                        lineData = (unsigned char*)m_CLineDataMid;
                        m_HasCInCM = false;
                    }
                    else
                    {
                        break;
                    }
                }
            }

            int frameIndex = 0;
            bool isECG = false;
            int lineNo = -1;
            unsigned char steering = 255;

            int bufferIndex = chooseImageBufferIndex(lineDataHead, lineNo, isECG, steering, frameIndex);

            Q_ASSERT(lineNo >= 0);
            if (bufferIndex >= 0)
            {
                if (!Setting::instance().defaults().isIODeviceVirtual())
                {
                    if (lineDataHead->LineType == LineData::Line_4D)
                    {
                        FourDPara* para4D = (FourDPara*)lineData;
                        if (para4D->Effzone != 1)
                        {
                            continue;
                        }
                    }
                }
                bool needstop = false;
                appendImageDataStage(lineDataHead, lineData, bufferIndex, isECG, lineNo, steering, frameIndex,
                                     needstop);
                if (needstop)
                    break;
            }
        }
        else if (lineDataHead->PackageType == LineData::Package_Info)
        {
            handleInfoPackage(lineData);
        }
        else
        {
            log()->error("unknown package type %1", lineDataHead->PackageType);
        }
    }
}

void LineImageDataReceiver::handlePacketOnFrezon(const ByteBuffer& data, const int lines, const int usbLineSize)
{
    for (int i = 0; i < lines; i++)
    {
        LineDataHead* lineDataHead = (LineDataHead*)(data.data() + i * usbLineSize);
        if (*((unsigned short*)lineDataHead->SyncHead) != LineData::SyncHead_Flag)
        {
            m_OldOrderNo = -1;
            break;
        }

        unsigned char* lineData = reinterpret_cast<unsigned char*>(lineDataHead) + sizeof(LineDataHead);
        if (lineDataHead->PackageType == LineData::Package_Info)
        {
            handleInfoPackage(lineData);
        }
    }
}

int LineImageDataReceiver::chooseImageBufferIndex(LineDataHead* lineDataHead, int& lineNo, bool& isECG,
                                                  unsigned char& steering, int& frameIndex)
{
    if (m_OldOrderNo == -1)
    {
        m_OldOrderNo = lineDataHead->LineOrderNo;
    }
    else
    {
        if (lineDataHead->LineOrderNo != (m_OldOrderNo + 1) % 256)
        {
            // group->clear();
            //            log()->warn("line order no. is not continuous. oldno is %1, newno is %2",
            //                        m_OldOrderNo, lineDataHead->LineOrderNo);
        }
        m_OldOrderNo = lineDataHead->LineOrderNo;
    }

    lineNo = lineDataHead->getLineNo();
    IRawImageBufferDataSetter* dataSetter = m_RawImageBuffers->activeDataSetter();
    unsigned char lineType = lineDataHead->LineType;
#ifdef LINEIMAGEDATARECV_DEBUG_ENABLE
    log()->trace("receiving %1 line, line no %2", LineData::lineTypeString(lineType), lineNo);
#endif

    bool isDWave = false;

    int retImageBufferIndex = -1;
    switch (lineType)
    {
    case LineData::Line_Needle:
    case LineData::Line_B:
    case LineData::Line_4D:
    {
        if (lineType == LineData::Line_B && m_SystemScanMode == SystemScanModeFourDLive)
        {
            return -1;
        }
        retImageBufferIndex = 0;
        // from lukuan: 目前的xbit90中，FPGA上传的Needle帧，有其独立的线型8
        //对于Zeus来说，是通过偏转标志来识别Needle帧的。Needle的偏转标志应设置为128
        //所以，在进入Needle模式之后，一次需向Zeus推送4帧B数据，其偏转标志分别为0、1、2、128
        //在NewECO机型下，打开穿刺增强是通过lineDataHead->LineType ==
        // LineData::Line_Needle判断的；在sonoair机型下，打开穿刺增强是通过lineDataHead->Steering == 3判断的
        if (lineDataHead->LineType == LineData::Line_Needle || lineDataHead->Steering == 3)
        {
            if (m_SonoParameters->pBV(BFPNames::NeedleModeStr))
            {
                lineDataHead->LineType = 0; //将needle帧类型当作B类型处理
                steering = 128;
            }
        }
        else
        {
            steering = 0xFF & (lineDataHead->Steering);
        }
        const FrameUnitInfo& frameInfos = dataSetter->frameUnitInfo(retImageBufferIndex);
        const FrameInfo& frameInfo = frameInfos.at(frameIndex);

        if (frameInfo.mode() == ImageBufferDef::B_Data || frameInfo.mode() == ImageBufferDef::FourD_Data)
        {
            return retImageBufferIndex;
        }
        else
        {
            // Full M mode not need B data, so enter full M mode will print log all the time,  causing subsequent image
            // link lag
            // log()->error() << "LineImageDataReceiver::chooseImageBufferIndex B mode:" << frameInfo.mode();
            return -1;
        }
    }
    case LineData::Line_C_Vel:
    case LineData::Line_C_SD:
    case LineData::Line_Power:
    case LineData::Line_Direct_Power:
    case LineData::Line_TDI:
    case LineData::Line_Elasto:
    {
        frameIndex = 1;
        retImageBufferIndex = 0;

        const FrameUnitInfo& frameInfos = dataSetter->frameUnitInfo(0);
        if (frameInfos.count() <= 1)
        {
            return -1;
        }
        const FrameInfo& frameInfo = frameInfos.at(frameIndex);
        if ((frameInfo.mode() == ImageBufferDef::C_Data) || (frameInfo.mode() == ImageBufferDef::Elasto_Data))
        {
            return retImageBufferIndex;
        }
        else
        {
            log()->error() << "LineImageDataReceiver::chooseImageBufferIndex color mode:" << frameInfo.mode();
            return -1;
        }
    }
    case LineData::Line_Dynamic_Flow:
    {
        frameIndex = 2;
        retImageBufferIndex = 0;

        const FrameUnitInfo& frameInfos = dataSetter->frameUnitInfo(0);
        if (frameInfos.count() < 3)
        {
            return -1;
        }
        const FrameInfo& frameInfo = frameInfos.at(frameIndex);
        if (frameInfo.mode() == ImageBufferDef::DynFlow_Data)
        {
            return retImageBufferIndex;
        }
        else
        {
            log()->error() << "LineImageDataReceiver::chooseImageBufferIndex Dynamic Flow mode:" << frameInfo.mode();
            return -1;
        }
    }
    case LineData::Line_D:
    case LineData::Line_CWD:
    case LineData::Line_M:
    case LineData::Line_CM:
    {
        if (lineType == LineData::Line_D || lineType == LineData::Line_CWD)
        {
            isDWave = true;
        }

        lineNo = 0;
        int bufferCount = 1;
        bufferCount += m_IsECG ? 1 : 0;
#ifdef USE_PW_SOUND
        bufferCount += isDWave ? 1 : 0;
#endif
        if (dataSetter->bufferCount() < bufferCount)
        {
            log()->error("has ecg but buffer size not match");
            return -1;
        }
        retImageBufferIndex = dataSetter->bufferCount() - bufferCount;
        const FrameUnitInfo& frameInfos = dataSetter->frameUnitInfo(retImageBufferIndex);
        const FrameInfo& frameInfo = frameInfos.at(frameIndex);
        //            log()->debug() << PRETTY_FUNCTION
        //                           << ", frameInfo.mode(): " << frameInfo.mode()
        //                           << ", imageType: " << frameInfos.imageType()
        //                           << ", lineDataHead->LineType: " << lineDataHead->LineType;
        if (frameInfo.mode() == BINARY_MAP_RAW_TYPE[lineDataHead->LineType - LineData::Line_M])
        {
            //                log()->debug() << PRETTY_FUNCTION
            //                               << ", dataSetter->bufferCount: " << dataSetter->bufferCount()
            //                               << ", bufferCount: " << bufferCount
            //                               << ", retImageBufferIndex: " << retImageBufferIndex;
            return retImageBufferIndex;
        }
        else
        {
            log()->error() << "LineImageDataReceiver::chooseImageBufferIndex wave mode:" << frameInfo.mode();
            return -1;
        }
    }
    case LineData::Line_Sound:
    {
        lineNo = 0;
        if (m_IsECG && dataSetter->bufferCount() < 2)
        {
            log()->trace("has ecg but buffer size not match");
            return -1;
        }
        retImageBufferIndex = m_IsECG ? dataSetter->bufferCount() - 2 : dataSetter->bufferCount() - 1;
        const FrameUnitInfo& frameInfos = dataSetter->frameUnitInfo(retImageBufferIndex);
        const FrameInfo& frameInfo = frameInfos.at(frameIndex);
        if (frameInfo.mode() == ImageBufferDef::Sound_Data)
        {
            return retImageBufferIndex;
        }
        else
        {
            log()->error() << "LineImageDataReceiver::chooseImageBufferInde return ";
            return -1;
        }
    }
    case LineData::Line_ECG:
    {
        lineNo = 0;
        if (!m_IsECG)
        {
            return -1;
        }
        retImageBufferIndex = dataSetter->bufferCount() - 1;
        const FrameUnitInfo& frameInfos = dataSetter->frameUnitInfo(retImageBufferIndex);
        const FrameInfo& frameInfo = frameInfos.at(frameIndex);
        if (frameInfo.mode() == ImageBufferDef::ECG_Data)
        {
            isECG = true;
            return retImageBufferIndex;
        }
        else
        {
            log()->error() << "LineImageDataReceiver::chooseImageBufferInde ECG line type not match, mode:"
                           << frameInfo.mode();
            return -1;
        }
    }
    default:
        log()->error("unprocessed line type %1 ", lineType);
        return -1;
    }
}

void LineImageDataReceiver::appendImageDataStage(LineDataHead* lineDataHead, unsigned char* lineData,
                                                 const int bufferIndex, const bool isECG, const int lineNo,
                                                 const unsigned char steering, int frameIndex, bool& stop)
{
    IRawImageBufferDataSetter* dataSetter = m_RawImageBuffers->activeDataSetter();
    int lineCount = LineData::validLineNumPerPack(lineDataHead->LineType);
    const FrameUnitInfo& frameInfos = dataSetter->frameUnitInfo(bufferIndex);

    if (lineDataHead->LineType == LineData::Line_B && lineDataHead->FrameFlag == LineData::Frame_End)
    {
        ++m_BReceiveFPS;
    }
    if (lineDataHead->LineType == LineData::Line_M)
    {
        ++m_MReceiveFPS;
    }

    if (!dataSetter->isWriteAble(bufferIndex))
    {
        //        log()->error() << PRETTY_FUNCTION << "bufferIndex:" << bufferIndex
        //                << "image buffer is not writable,skip append data stage.";
        return;
    }

    //    int frameNo = (lineDataHead->Reserve[0])<<8|(lineDataHead->Reserve[1]);
    if (!shouldEnqueue(lineDataHead))
    {
        //        int tempthis = (lineDataHead->LineNo[1])<<8|(lineDataHead->LineNo[0]);
        //        //根据线数据协议，线号占两个字节，低10位是线号，高1位是iqflag，中间的位是iq积累次数
        //    //    lineNo=tempthis&0x3FF;
        //        int linecolorAccumulative = (tempthis&0x3C00)>>10;
        //        int linecolorIQFlag = (tempthis&0x8000)>>15;
        //        qDebug() << PRETTY_FUNCTION
        //                 << "drop shouldn't enqueue data,"
        //                 << "line type: " << LineData::lineTypeString(lineDataHead->LineType)
        //                 << "frameNo: " << frameNo
        //                 << "lineNo: " << lineNo
        //                 << "linecolorAccumulative: " << linecolorAccumulative
        //                 << "linecolorIQFlag: " << linecolorIQFlag
        //                 << "StartLine: " << m_SonoParameters->pIV(BFPNames::StartLineStr)
        //                 << "StopLine: " << m_SonoParameters->pIV(BFPNames::StopLineStr)
        //                 << "m_RedundantPoints: " << m_RedundantPoints
        //                 << "Steering: " << lineDataHead->Steering;
        stop = true;
        return;
    }
    /*if(frameInfos[frameIndex].mode() == ImageBufferDef::B_Data){
        log()->fatal("lineDataHead->FrameFlag-1  %1",lineDataHead->FrameFlag);
    }*/

    bool valid = dataSetter->setCurrentBufferState(bufferIndex, frameIndex, lineDataHead->FrameFlag);
    //    log()->debug() << PRETTY_FUNCTION << ", is valid: " << valid
    //                   << ", line type: " << LineData::lineTypeString(lineDataHead->LineType)
    //                   << ", FixedLineStartup: " << m_FixedLineStartup
    //                   << ", FrameFlag: " << lineDataHead->FrameFlag
    //                   << ", line No: " << *((unsigned short *)lineDataHead->LineNo);
    if (valid || m_FixedLineStartup)
    {
        m_DataSave->writeToFile((const char*)lineData, frameInfos.at(frameIndex).mode(),
                                frameInfos.at(frameIndex).lineSize());
        if (isECG)
        {
            dataSetter->updateNextRearStartTime(bufferIndex);
            appendECGData(lineDataHead->ECG, frameInfos, frameIndex, lineData, bufferIndex, lineNo);
            return;
        }

        BufferUnit& frame = dataSetter->nextRear(bufferIndex);
        dataSetter->updateNextRearStartTime(bufferIndex);
        if (dataSetter->currentBufferStates(bufferIndex).isInvalid() || m_FixedLineStartup)
        {
            //如果线数据的条数小于buffer的可容的大小，左右两边可能有上次buffer中的数据
            frame.zeroMemory();
        }

        int lineSize = frameInfos.at(0).lineSize();

        appendNoneECGData(frame, frameInfos, frameIndex, lineData, bufferIndex, lineCount, lineNo, lineSize);

        if (steering != 255)
        {
            frame.setSteering((int)steering);
        }

        if (!dataSetter->canEnqueue(bufferIndex) && !m_FixedLineStartup)
        {
            //            log()->trace("queue canEnqueue false");
            return;
        }
        // log()->debug() << PRETTY_FUNCTION << ", 3333333";
#ifdef IMAGERECEIVERLOG
        int frameNo = (lineDataHead->Reserve[0]) << 8 | (lineDataHead->Reserve[1]);
        int tempthis = (lineDataHead->LineNo[1]) << 8 | (lineDataHead->LineNo[0]);
        int curLineNo = tempthis & 0x3FF;
        log()->info() << __FUNCTION__ << " lineType:" << LineData::lineTypeString(lineDataHead->LineType)
                      << " frameNo: " << frameNo << " lineNo: " << curLineNo << " steer:" << lineDataHead->Steering
                      << " lineFlag:" << lineDataHead->FrameFlag << " enqueue...";
#endif

        bool wave =
            LineData::isLineTypeWave(lineDataHead->LineType) && (lineDataHead->LineType != LineData::Line_Sound);
        // wave type refresh not controlled by enqueue
        // but by WaveImageBuffer internal thread
        //        dataSetter->enqueue(bufferIndex,!wave);
        //        if (wave)
        //        {
        //            log()->debug() << PRETTY_FUNCTION << ", PW wave data received!";
        //        }
        //        if (lineDataHead->LineType == LineData::Line_Sound)
        //        {
        //            log()->debug() << PRETTY_FUNCTION << ", PW Sound data received!";
        //        }
        m_RawImageBuffers->enqueue(bufferIndex, !wave);
        if (lineDataHead->FrameFlag == LineData::Frame_End)
        {
            updateBeforeSteerList(frame.steering());
        }
        return;
    }

    if (frameIndex >= dataSetter->newRearStates(bufferIndex).count())
    {
        //         qDebug() << PRETTY_FUNCTION
        //                  << "line type:" << LineData::lineTypeString(lineDataHead->LineType)
        //                  << "frame index:" << frameIndex
        //                  << "greater than:"
        //                  << "dataSetter->newRearStates(bufferIndex).count()" <<
        //                  dataSetter->newRearStates(bufferIndex).count()
        //                  << "newRearStates";
        return;
    }

    // if(frameInfos[frameIndex].mode() == ImageBufferDef::B_Data){
    //     log()->fatal("lineDataHead->FrameFlag-2  %1",lineDataHead->FrameFlag);
    // }

    valid = dataSetter->setNewRearState(bufferIndex, frameIndex, lineDataHead->FrameFlag);
    if (!valid)
    {
        // log()->error()
        //  << LineData::lineTypeString(lineDataHead->LineType)
        //  << " invalid state :lineNo:" << lineNo
        //  << " frameNo: " << frameNo
        //  << ", frameFlag: " << LineData::frameFlagString(lineDataHead->FrameFlag)
        //  << ", frameIndex: " << frameIndex;
    }

    BufferUnit& frame = dataSetter->newRear(bufferIndex);
    dataSetter->updateNextRearStartTime(bufferIndex);
    if (dataSetter->newRearStates(bufferIndex).isInvalid())
    {
        //如果线数据的条数小于buffer的可容的大小，左右两边可能有上次buffer中的数据
        frame.zeroMemory();
    }
    m_DataSave->writeToFile((const char*)lineData, frameInfos.at(frameIndex).mode(),
                            frameInfos.at(frameIndex).lineSize());
    //    if (lineDataHead->LineType == LineData::Line_CWD)
    //    {
    //     log()->debug() << PRETTY_FUNCTION
    //           << "...........CW data start to mem..."
    //           << ", frameIndex: " << frameIndex
    //           << ", bufferIndex: " << bufferIndex
    //           << ", lineCount: " << lineCount
    //           << ", lineNo: " << lineNo
    //           << ", lineSize: " << frameInfos.at(0).lineSize();
    //    }
    //     if (lineDataHead->LineType == LineData::Line_C_Vel)
    //     {
    //         log()->debug() << PRETTY_FUNCTION
    //                        << "...........C data start to mem...";
    //     }
    appendNoneECGData(frame, frameInfos, frameIndex, lineData, bufferIndex, lineCount, lineNo,
                      frameInfos.at(0).lineSize());
    if (steering != 255)
    {
        frame.setSteering((int)steering);
    }
}

void LineImageDataReceiver::mergeCMLines(uint16_t* cLineData, unsigned char* mLineData, SystemScanMode mode, int points)
{
    // Color M 线合并代码
    uchar low8Bit = 0;
    uchar high8Bit = 0;
    uchar mValue = 0;
    uint16_t cSrcValue = 0;
    uchar cValue = 0;
    bool useMValue = true;
    int cValueForJudge = 0;
    int threshold = 0;
    uint16_t* pCMDstBuffer = (uint16_t*)cLineData;

    //    m_bIsDPD 和 m_bHasDirection 根据扫查模式来确定
    bool m_bHasDirection = true;
    bool m_bIsDPD = false;
    if (mode == SystemScanModePDM || mode == SystemScanModePDLRBM || mode == SystemScanModePDUDBM)
    {
        m_bHasDirection = false;
    }

    if (mode == SystemScanModeDPDM || mode == SystemScanModeDPDLRBM || mode == SystemScanModeDPDUDBM)
    {
        m_bIsDPD = true;
    }

    int m_cfmSlope = m_SonoParameters->pIV(BFPNames::CfmSlopeStr);
    int m_cPriority = m_SonoParameters->pIV(BFPNames::CPriorityStr);
    int m_TopBorderColor = m_SonoParameters->pIV(BFPNames::TopBorderColorStr) * 2;
    int m_BottomBorderColorStr = m_SonoParameters->pIV(BFPNames::BottomBorderColorStr) * 2;
    points = points - m_SonoParameters->pIV(BFPNames::RedundantPointNumPerLineStr);
    for (int i = 0; i < points; ++i)
    {
        mValue = mLineData[i];
        cSrcValue = cLineData[i];

        bool bNegative = (cSrcValue & 0x0100) > 0;

        if (m_bHasDirection)
        {
            if (m_bIsDPD) // DPD
            {
                cValueForJudge = uchar(cSrcValue >> 9); //(cValue & int(0x7F)) * 2;//UNDO CPA到底有没有/2
                cValue = uchar((cValueForJudge) | (bNegative ? 0x80 : 0x00));
            }
            else // velocity or TDI
            {
                //取反
                cValueForJudge = bNegative ? (0 - (int(cSrcValue) | 0xFFFFFF00)) : (cSrcValue & 0xFF);
                cValue = uchar((cValueForJudge >> 1) | (bNegative ? 0x80 : 0x00));
            }
        }
        else // CPA
        {
            cValue = uchar(cSrcValue >> 9);
            cValueForJudge = cValue;
        }

        threshold = m_cfmSlope * mValue / 256;
        useMValue = ((m_cPriority < threshold) && (cValueForJudge < threshold)) ? true : false;

        // 由于zeus不好对彩色上下边界做判断，这里由软件来实现
        if (i < m_TopBorderColor || i > m_BottomBorderColorStr)
        {
            useMValue = true;
        }

        //设置值
        low8Bit = useMValue ? mValue : cValue;
        high8Bit = uchar((useMValue ? 0 : 0x10));
        pCMDstBuffer[i] = high8Bit << 8 | low8Bit;
    }
}

void LineImageDataReceiver::handleInfoPackage(unsigned char* lineData)
{
    memcpy(m_LineDataHWInfo, lineData, sizeof(LineDataHWInfo));

    QString str = "hw info";
    unsigned char* info = (unsigned char*)m_LineDataHWInfo;
    for (int i = 0; i < (int)sizeof(LineDataHWInfo); i++)
    {
        if (i % 6 == 0)
        {
            str += "\n";
        }
        QString msg = QString("[%1]=0x%3 ").arg(i, 2, 10, QChar('0')).arg(info[i], 2, 16, QChar('0'));
        str += msg;
    }

    ///    qDebug() << PRETTY_FUNCTION << str;
    log()->trace(str);
    emit hWInfoChanged();
}

void LineImageDataReceiver::showFps(float value)
{
    log()->debug("line: %1, receive data fps %2", __LINE__, value);
}

void LineImageDataReceiver::onRedundantPointsChanged(QVariant points)
{
    m_RedundantPoints = points.toInt();
}

void LineImageDataReceiver::onFPSChanged(const QVariant& value)
{
    QMutexLocker locker(&m_Mutex);
    if (m_SonoParameters->pIV(BFPNames::SystemScanModeStr) != SystemScanModeAV)
    {
        m_PausedChanged = true;
    }
}

void LineImageDataReceiver::onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    QMutexLocker locker(&m_Mutex);
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
    m_IsFrozen = true;
}

void LineImageDataReceiver::onSystemScanModeChanged(const QVariant& value)
{
    m_SystemScanMode = value.toInt();
    onQuadplexModeChanged(m_SonoParameters->pV(BFPNames::QuadplexModeStr));
    QMutexLocker locker(&m_Mutex);
    if (!m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        m_IsFrozen = false;
    }
    m_PausedChanged = true;
}

void LineImageDataReceiver::onTrapezoidalModeChanged(const QVariant& value)
{
}

void LineImageDataReceiver::onFreqSpectrumChanged(const QVariant& value)
{
    onTrapezoidalModeChanged(m_SonoParameters->pV(BFPNames::TrapezoidalModeStr));
}

void LineImageDataReceiver::onTriplexModeChanged(const QVariant& value)
{
    if (value.toBool())
    {
        onTrapezoidalModeChanged(m_SonoParameters->pV(BFPNames::TrapezoidalModeStr));
    }
    else
    {
        onFreqSpectrumChanged(m_SonoParameters->pV(BFPNames::FreqSpectrumStr));
    }
}

void LineImageDataReceiver::onQuadplexModeChanged(const QVariant& value)
{
    if (value.toBool())
    {
        onTrapezoidalModeChanged(m_SonoParameters->pV(BFPNames::TrapezoidalModeStr));
    }
    else
    {
        onTriplexModeChanged(m_SonoParameters->pV(BFPNames::TriplexModeStr));
    }
}

void LineImageDataReceiver::onScpdChanged(const QVariant& value)
{
    Q_UNUSED(value);
    m_beforeSteerList.clear();
}

void LineImageDataReceiver::onECGEnChanged(const QVariant& value)
{
    Q_UNUSED(value);
    m_IsECG = value.toBool();
    onQuadplexModeChanged(m_SonoParameters->pV(BFPNames::QuadplexModeStr));
}

void LineImageDataReceiver::onBeforeFreezeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    qDebug() << PRETTY_FUNCTION << "newValue:" << newValue.toBool()
             << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    if (newValue.toBool())
    {
        m_IsFrozen = true;
    }
}

void LineImageDataReceiver::onFreezeChanged(const QVariant& value)
{
    if (!value.toBool())
    {
        m_IsFrozen = false;
    }
    if (value.toBool())
    {
        m_IgnoreFistFrame = true;
    }
}

void LineImageDataReceiver::onFixedLineStartupChanged(const QVariant& value)
{
    if (value.toInt() == 0)
    {
        m_FixedLineStartup = false;
    }
    else
    {
        m_FixedLineStartup = true;
    }
}

void LineImageDataReceiver::onTDIEnChanged(const QVariant& value)
{
}

void LineImageDataReceiver::onColorImageModeChanged(const QVariant& value)
{
}
