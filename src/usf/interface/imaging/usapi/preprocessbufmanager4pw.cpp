#include "preprocessbufmanager4pw.h"
#include "bfpnames.h"
#include "bytebuffer.h"
#include "controltableparameter.h"
#include "iqdata.h"
#include "probeblockdataset.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "qmath.h"
#include "resource.h"
#include "setting.h"
#include "sonoparameters.h"
#include "systemscanmodeclassifier.h"
#include "util.h"
#include "syncidmanager.h"
#include <QDebug>
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, PreProcessBufManager4pw);

using namespace ZeusParameterNames;

int PreProcessBufManager4pw::m_PWReceiveFPS = 0;
int PreProcessBufManager4pw::m_PWPreProcessAudioFPS = 0;
int PreProcessBufManager4pw::m_PWPreProcessWaveFPS = 0;

PreProcessBufManager4pw::PreProcessBufManager4pw(PreProcessParaController* paraController, int dataQueueSize,
                                                 PretreatmentPipleline* pipleline)
    : PreProcessBufManagerBase(paraController, dataQueueSize, pipleline)
    , m_WaveType(PW)
    , m_AccumNum(0)
    , m_gateNo(4)
    , m_PwPool(nullptr)
{
}

PreProcessBufManager4pw::~PreProcessBufManager4pw()
{
    //    clear();
    if (nullptr != m_PwPool)
    {
        delete[] m_PwPool;
        m_PwPool = nullptr;
    }
}

ByteBuffer PreProcessBufManager4pw::processNewData(uchar* data, InfoForPostProcess& info, int dataType)
{
    if (info.info[1] != SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId))
    {
        return ByteBuffer();
    }
    uchar* head = (uchar*)&info.info[PretreatmentPipleline::HeadInfoStart];
    LineDataHead* lineHead = (LineDataHead*)head;

    // TODO Seven
    //    int gateNo = lineHead->PWGateNo;
    //    if(m_gateNo != gateNo)
    //    {
    //        m_gateNo = gateNo;
    //        m_Pipleline->setParameterValue(
    //            DscParameterNames::PreProcessSonoParameterName(DscParameterNames::CurrentGate),
    //            m_gateNo);
    //    }

    //    //
    //    lineHead->FrameFlag = LineData::Frame_End;
    //    *((unsigned short*)lineHead->LineNo)= 0;

    ByteBuffer retBuffer;

    if (BFDataType::DData == dataType)
    {

        ++m_PWPreProcessWaveFPS;
        // PW 模式原先的LineType 为Line_C_SD (2)，需要修改成Line_D;
        // 而CW 出来的数据就是  Line_CWD
        // log()->info(" .....................&&&&  before processNewData line type: %1", lineHead->LineType);

        if (lineHead->LineType == BFDataType::CWData || lineHead->LineType == LineData::Line_CWD)
        {
            lineHead->LineType = LineData::Line_CWD;
        }
        else
        {
            lineHead->LineType = LineData::Line_D;
        }

        int onepackagesize =
            LINEDATAHEADLEN + LINEDATAPACKAGESIZE; // pw 每次出来1根线 构造1个包 每个包1040 后面512无效 保持和大机器一样
        int realonepackagesize = 512; // width * height * bitWidth = 512
        // width, height, bitWidth 从PretreatmentPipleline::onPreProcessedData获取,
        // 由于PW前处理返回的此数值固定，暂不增加参数传递
        for (int i = 0; i < PWCOUNT; i++)
        {
            //构造header
            memcpy(&m_Controlbuffers.m_BufferForWave[onepackagesize * i], head, LINEDATAHEADLEN);
            memcpy(&m_Controlbuffers.m_BufferForWave[onepackagesize * i + LINEDATAHEADLEN],
                   &data[i * realonepackagesize], realonepackagesize); //拷贝1根线
        }
        if (Setting::instance().defaults().afterIQSaveFlag())
        {
            QString filePath = QString("%1pwlinecallback").arg(Resource::iqDataSaverDir());
            Util::Mkdir(filePath.toStdString().c_str());
            QString fileName = QString("%1/WaveData.dat").arg(filePath);
            QFile file(fileName);
            if (file.open(QFile::WriteOnly | QFile::Append))
            {
                //                file.write((char*)head, LINEDATAHEADLEN);
                file.write((char*)data, realonepackagesize);
            }
            file.close();
        }

        retBuffer.setData((uchar*)m_Controlbuffers.m_BufferForWave, PWCOUNT * onepackagesize);
    }
    else if (BFDataType::SOUNDData == dataType)
    {
        ++m_PWPreProcessAudioFPS;

        lineHead->LineType = LineData::Line_Sound;
        //构造header
        memcpy(m_Controlbuffers.m_BufferForSound, head, LINEDATAHEADLEN);
        memcpy(&m_Controlbuffers.m_BufferForSound[LINEDATAHEADLEN], data, LINEDATAPACKAGESIZE);
        retBuffer.setData((uchar*)m_Controlbuffers.m_BufferForSound, LINEDATAHEADLEN + LINEDATAPACKAGESIZE);
    }

    return retBuffer;
}

void PreProcessBufManager4pw::initbuffer()
{
    //    clear();
    if (nullptr == m_PwPool)
    {
        m_PwPool = new uchar[LINEDATAHEADLEN + MAXRCVSIZEPW * PWCOUNT + LINEDATAHEADLEN + MAXRCVSIZEPW];
    }

    //    m_Controlbuffers.__bufferForWave = new uint8_t[ LINEDATAHEADLEN + MAXRCVSIZEPW * PWCOUNT ]; //iq 处理完毕后
    //    出1根线的pw数据 m_Controlbuffers.__bufferForSonund = new uint8_t[ LINEDATAHEADLEN + MAXRCVSIZEPW ];
    m_Controlbuffers.m_BufferForWave = m_PwPool;
    m_Controlbuffers.m_BufferForSound = &m_PwPool[LINEDATAHEADLEN + MAXRCVSIZEPW * PWCOUNT];

    if (getWaveType() != CW)
    {
        // CW前处理后的数据跟PW一样，由PW类去处理，这里不需要连接信号
        connect(m_Pipleline, SIGNAL(onNewPWData(uchar*, InfoForPostProcess&, int)), this,
                SLOT(onNewData(uchar*, InfoForPostProcess&, int)), Qt::DirectConnection);
        connect(m_Pipleline, SIGNAL(onNewSoundData(uchar*, InfoForPostProcess&, int)), this,
                SLOT(onNewData(uchar*, InfoForPostProcess&, int)), Qt::DirectConnection);
    }

    m_Modeparamsreset << BFPNames::DScanLineStr << BFPNames::DopAccumulateNumStr << BFPNames::DScanLineTDIStr;
}

ByteBuffer PreProcessBufManager4pw::getbufferbytebyframeindex(int fidx)
{
    Q_UNUSED(fidx);
    //    return ByteBuffer(m_Controlbuffers.__buffertp, m_Linesize * m_Linenum);
    return ByteBuffer();
}

void PreProcessBufManager4pw::resetallbyframeindex(int fidx)
{
    Q_UNUSED(fidx);
}

int PreProcessBufManager4pw::copytobufferbyframeindex(uint8_t* data, bool& lostframe)
{
    static uint16_t recordIndex = 0;
    LineDataHead* lineDataHead = (LineDataHead*)data;
    int lineNo = lineDataHead->getLineNo();
    int frameNo = lineDataHead->getFrameNo();
    int linecolorAccumulative = lineDataHead->getIQAccumulativeTimes();
    int linecolorIQFlag = lineDataHead->getIQFlag();

    if (!Setting::instance().defaults().isIODeviceVirtual())
    {
        if (m_AccumNum <= 0)
        {
            return -1;
        }
    }

    if (Setting::instance().defaults().beforeIQSaveFlag())
    {
        QString filePath = QString("%1beforIQ").arg(Resource::iqDataSaverDir());
        Util::Mkdir(filePath.toStdString().c_str());
        QString fileName = QString("%1/%2.dat").arg(filePath).arg(recordIndex++);
        QFile file(fileName);
        if (file.open(QFile::WriteOnly))
        {
            file.write((char*)data, MAXRCVSIZEPW);
        }
        file.close();
    }
    else
    {
        recordIndex = 0;
    }
    ++m_PWReceiveFPS;
    m_IqDataProcess->enqueue(
        IQData(ByteBuffer(data, MAXRCVSIZEPW), DData, m_Linesize, m_Linenum, 1, 1, IQFormat4Zeus::IACC123QACC123));
    return -1;
}

void PreProcessBufManager4pw::clear()
{
    //    if(nullptr != m_Controlbuffers.__bufferForWave)
    //    {
    //        delete m_Controlbuffers.__bufferForWave;
    //        m_Controlbuffers.__bufferForWave = nullptr;
    //    }
    //    if(nullptr != m_Controlbuffers.__bufferForSonund)
    //    {
    //        delete m_Controlbuffers.__bufferForSonund;
    //        m_Controlbuffers.__bufferForSonund = nullptr;
    //    }
}

void PreProcessBufManager4pw::adjustNonPipeline(QString bfpName, QVariant value)
{
    if (bfpName == BFPNames::SystemScanModeStr)
    {
        if (SystemScanModeClassifier::isLikeD(value.toInt()))
        {
            resetallbyframeindex(0);
            m_closeaudio = false;
        }
        else
        {
            m_closeaudio = true;
            //            closepwthread();
        }
    }
    else if (bfpName == BFPNames::PRFDopStr)
    {
        m_Pipleline->setPwPrf(value.toInt());
    }
}

void PreProcessBufManager4pw::setLineInformation(SonoParameters* sonoParameters, int usblineSize)
{
    if (nullptr == sonoParameters)
    {
        return;
    }

    QMutexLocker lock(&m_LineInfoMutex);

    int startlinevalue = 0;
    if (sonoParameters->pBV(BFPNames::TDIEnStr))
    {
        // TODO TDI的扫查线目前没有加Delta，后面调图的时候有需要再加
        startlinevalue = sonoParameters->pIV(BFPNames::DScanLineTDIStr);
    }
    else
    {
        startlinevalue =
            sonoParameters->pIV(BFPNames::DScanLineStr) + sonoParameters->pIV(BFPNames::PWScanLineDeltaStr);
    }
    m_Startline = startlinevalue & 0xFE; //偶数
    m_Stopline = startlinevalue;
    m_Linesize = usblineSize - sizeof(LineDataHead);
    m_Linenum = 1;
    m_AccumNum = sonoParameters->pIV(BFPNames::DopAccumulateNumStr);
}

void PreProcessBufManager4pw::onGettingControlTableValue(const QVariant& value, int& ct)
{
}
