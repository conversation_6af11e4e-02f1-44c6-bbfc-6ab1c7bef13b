/*
 * =====================================================================================
 *
 *       Filename:  chisonultrasoundcontext.h
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  12/10/2014 04:11:02 PM
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  YOUR NAME (),
 *   Organization:
 *
 * =====================================================================================
 */

#ifndef CHISON_ULTRASOUND_CONTEXT
#define CHISON_ULTRASOUND_CONTEXT

#include "bytelineimageargs.h"
#include "chisonultrasoundtypes.h"
#include "dataarg.h"
#include "imageeventargs.h"
#include "izeustool.h"
#include "sonoparametersclientbase.h"
#include "usapi_global.h"
#include <QGLWidget>
#include <QMutex>
#include <QObject>
#include <QTimer>
class LineImageArgs;
class FpsCalculator;
class SonoBuffers;
class ImageDataReceiverSwitcher;
class BaseBFStaticParameters;
class ImageFrameInfoSwitchers;
class SonoParameters;
class ImageEventArgs;
class ByteBuffer;
class ILineBufferManager;
class CineLooper;
class BaseWholeDataHWInfoManage;
class ILineBuffer;
class StressEchoBufferManager;
class ImageCircularQueueBuffer;
class LineGlPreprocessingCreater;
class BufferPushControlers;
class VAFunctionHandler;
#ifdef USE_4D
class FourDVolumeLineImageArgs;
#endif
class DscThread;
class AbstractDSCPipeline;
class BufferUnit;
class FrameUnitInfo;
class ZeusAPIInterface;
class IStateManager;
class IImageSaveHelper;

#ifdef USE_PANORAMIC
class CurvedPanoramic;
#endif

class PresetModeParameterSyncOnFrozen;

class USF_INTERFACE_IMAGING_USAPI_EXPORT ChisonUltrasoundContext : public QObject,
                                                                   public SonoParametersClientBase,
                                                                   public IZeusTool
{
    Q_OBJECT
    struct FrameIndexSync
    {
        QMutex m_SyncMutex;
        int m_ImageTileIndex;
        int m_FrameIndex;
        int m_FrameType;

        FrameIndexSync()
            : m_ImageTileIndex(-1)
            , m_FrameIndex(-1)
            , m_FrameType(-1)
        {
        }
    };

public:
    ChisonUltrasoundContext(IImageSaveHelper* imageSaveHelper,
                            ChisonUltrasound::ChisonUltrasoundMode type = ChisonUltrasound::ChisonUltrasound_RealTime,
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
                            QGLWidget* shareGlWidget = NULL,
#else
                            QOpenGLContext* shareGlWidget = NULL,
#endif
                            QObject* parent = 0);
    ~ChisonUltrasoundContext();

    ILineBuffer* lineBuffer() const;

    ILineBufferManager* lineBufferManager() const;

    CineLooper* cineLooper() const;
#ifdef USE_PANORAMIC
    CurvedPanoramic* curvedPanoramic() const;
    void setCurvedPanomicFile(const QString& filePath);
#endif

    void setParameter(const QString& parameterName, const QVariant& value);

    const QVariant& getParameter(const QString& parameterName) const;

    void start();
    void clear();
    void pauseReceivingImage();

    void onLoad(SonoParameters* sonoParameters);

    void endOnLoad();

    void deleteLoadSonoparameters();
    /**
     * @brief 设置ColorMap映射数据
     * @param image2DMap
     * @param waveMap
     */
    void setColorMap(uchar* image2DMap, uchar* waveMap);

    /**
     * @brief onEntryStressEchoAnalyze
     *        进入stress echo需要保存现场
     */
    void onEntryStressEchoAnalyze();
    /**
     * @brief onExitStressEchoAnalyze
     *        退出stress echo需要退出现场
     */
    void onExitStressEchoAnalyze();

    StressEchoBufferManager* stressEchoBufferManager() const;

    void initCurvedPanoramic(SonoParameters* realSonoparameters);

    void setSystemScanMode(int systemScanMode);

    //    bool hasStableImageBuffer();

    bool imageIsStable() const
    {
        return m_ImageStable && m_ImageShapeStable && m_LayoutStable;
    }

    void prepareLoadImage();

    bool isPrepareLoadImage() const;

    void backUpWave();
    void syncAvtiveBufferGlobalSonoparamters();
    void clearAvtiveBufferFreezeParasRecord();
    void clearImageBufferDataOnOncePushLimit(int imagebufferIndex, int layoutIndex = -1);
#ifdef USE_VA
    bool saveVaResult(QString fileDir, int startIndex, int endIndex);
    void loadVaResult(QString fileDir);
#endif

    int PostRawData(int type, int index, int x, int y);
    void setColorRawDataCallbackEnabled(bool isEnable, const int& layoutCount);
    void setColorRawDataCallbackTigger(bool isEnforce, const int& layoutCount);

    void exportProperties();

    bool is2DImagePushed(const int layoutIndex) const;

    bool existUnActiveRender(int layoutIndex) const;

    void setBFStaticParameters(BaseBFStaticParameters* bfStaticParameters);

    void setStateManager(IStateManager* value);

    void updateProbesChangedInfo(const QVector<int>& codes, const QVector<bool>& value);

    ImageDataReceiverSwitcher* dataReceiverSwitcher() const;

    void setPictureImage(QImage image);
    void setIsPause(bool IsPause);

    bool getIsPause() const;

public slots:
    void onMapsChanged();
    void onImageShapeUnstable();
    void onImageShapeStable();
    void onImageUnstable();
    void onImageStable();
    void onBeforeLayoutChanged();
    void onLayoutChanged();
    void onImageNoChanged();
    void onBeginPaused();
    void onStartPlay();

protected:
    void onSetSonoParameters();
    void connectParametersSignals();
    void disconnectParametersSignals();

signals:
    void updateCinePlayCurrentIndex(const int layout);
    void destoryCineLoopPlayer(const int layout);
    void fpsChanged(const QString& name, const float fps);
    void imageUnstable();
    void imageShapeUnstable();
    void imageStable();
    void imageShapeStable();
    void startPlay();
    void setCudaAddr(quint64);

    void newBImage(ImageEventArgs* imageEventArgs);

    void updateImageData(const ByteBuffer& data);

    void closeRequested();

    void beforePipelineStop(int imageTileIndex);

    void pipelineCreated(int imageTileIndex);

    void newImage(ImageEventArgs* imageEventArgs);

    void newPictureImage(QImage image);

    void rawDataInfo(void* data, int width, int height, int bitCount, int layoutIndex);
    void newMeasureImage(ImageEventArgs* imageEventArgs);

    void eraseImage();

    void newHWInfo(unsigned char* data, int len);
    void ECGInfo(unsigned char);
    void datainfo(void* data, int width, int height, int imageType, int frameSteerType);
    void wholeImageInfo(void* data, int width, int height);
    void wholeGrayImage(void* data, int width, int height);
    void imageChanged(const QList<BufferUnit>&, const FrameUnitInfo&);
    void signalHasVelocityRawData(int layoutIndex);
    void updateProbesChanged(const QVector<int>& codes, const QVector<bool>& value);
    void imageDataReady();

#ifdef USE_4D
    void volumeDataPrepared(const FourDVolumeLineImageArgs&);
#endif
#ifdef USE_VA
    void vaResultUpdated(const QVariant&);
#endif

    void activeLayoutSonoParametersChanged(SonoParameters* parameters);

    void dopplerGateChanged();

    void functionStateChanged(int type, bool on);

    /**
     * @brief 用于AV功能下发送pw数据，提供给算法使用
     * @param imageEventArgs
     */
    void newPWImage(ImageEventArgs* imageEventArgs);

private slots:
    void onPresetBeginChanged();
    void onPresetEndChanged();

    void onRequestFlush();

    void onFlushedAllImagesOfDSC();

    void onFlushedAllImages(bool requestFlush = true);

    void onBeforePipelineStop(int imageTileIndex);

    void onPipelineCreated(int imageTileIndex);

    bool onNewGrayImageElasto(LineImageArgs* lineImageArgs);

    void onNewGrayImage(LineImageArgs* lineImageArgs);

    void onNewMeasureImage(LineImageArgs* lineImageArgs);

    void onNewGrayImageForAI(LineImageArgs* lineImageArgs);

    void onNewGrayImageForCP(LineImageArgs* lineImageArgs);

    void onNewImage(ImageEventArgs* imageEventArgs);

    void onNewPictureImage(QImage image);
#ifdef USE_VA
    void onClearBuffer(int layoutIndex, int bufferType);
    void onImageChanged(const QList<BufferUnit>&, const FrameUnitInfo&);
    void onVAStateChanged(bool isOn);
#endif
#ifdef USE_4D
    void onNewFourDImage(FourDVolumeLineImageArgs* volumeImageArgs);
#endif

    void onStaticImage(ImageEventArgs* imageEventArgs);

    void onClose();

    void onPipelineFinished();

    void onHWInfoChanged();

    void onLineImageDebugChanged(const QVariant& value);
    void onLineColorMapDebugChanged(const QVariant& value);
    void onBeforeFreezeChanged(const QVariant& oldValue, QVariant& newValue);
    void onFreezeChanging(const QVariant& value);
    void onFreezeChanged(const QVariant& value);
    void onProbeIdChanged(const QVariant& value);
    void onBeforeLayoutChanged(const QVariant& oldValue, QVariant& newValue);
    void onLayoutChanged(const QVariant& value);
    void onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue);
    void onActiveBChanged(const QVariant& value);
    void onFreqSpectrumChanged(const QVariant& value);
    void onTriplexModeChanged(const QVariant& value);
    void onQuadplexModeStrChanged(const QVariant& value);
    void onBeforeDisplayFormatChanged(const QVariant& value);
    void onEndPaused();
    void onDataInfo(void* data, int width, int height, int imageType, int frameSteerType);
#ifdef USE_PANORAMIC
    void onCurvedPanormicFinished(uchar* data);

    void onUpdatePixelSizeMM(qreal scale);
#endif
    void onBufferSonoParametersChanged();

    //    void onHasBuffer();
private:
    void createSonoBuffers();

    void createCineLooper();

    void createBufferPushControlers();

    void createDSCThread();

    void createGstUltrasoundPipeline();

    void createInviwoPipeline();

    void createZeusPipeline();

    void createGlPreprocessing();

    void createImageDataReceiverSwitcher();

    void createImageFrameInfoSwitchers();

    void createBufferManager();

    void createStressEchoManager();

    void createrGlBridgePipeline();

    void createZeusAPIHelper();

    bool mapImageEventArgsFromLineImageArgs(ImageEventArgs& imageEventArgs, LineImageArgs* lineImageArgs);

    bool processCurvedPanoramic(ImageEventArgs* imageEventArgs);

    bool updateDisplayIndex(const ImageEventArgs* imageEventArgs);

    void asyncClearFrameIndexSync();

    bool setActiveLayoutDataBufferLasetFrameIndex(ImageEventArgs* imageEventArgs);

    void clearActiveLayoutDataBufferNoDrawnFrames();
#ifdef USE_PANORAMIC
    void sendCurvedPanImageArgs();
#endif
#ifdef USE_VA
    void detectImageVA(ImageEventArgs* imageArgs);
    void createVAFunctionHandler();
#endif
    void handlePostRawData(const ImageEventArgs* imageArgs);

private:
    FpsCalculator* m_FpsCalculator;
    ChisonUltrasound::ChisonUltrasoundMode m_ChisonUltrasoundMode;
    SonoBuffers* m_SonoBuffers;
    ImageDataReceiverSwitcher* m_DataReceiverSwitcher;
    BaseBFStaticParameters* m_BFStaticParameters;
    ImageFrameInfoSwitchers* m_FrameInfoSwitchers;
    CineLooper* m_CineLooper;
    BaseWholeDataHWInfoManage* m_WholeDataHWInfo;
    ILineBufferManager* m_LineBufferManager;
    StressEchoBufferManager* m_StressEchoManager;
    LineGlPreprocessingCreater* m_LineGlPreprocessing;
    BufferPushControlers* m_BufferPushControlers;

    AbstractDSCPipeline* m_DSCPipeline;
    DscThread* m_DscThread;
    ZeusAPIInterface* m_ZeusAPIInterface;
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    QGLWidget* m_ShareGlWidget;
#else
    QOpenGLContext* m_ShareGlWidget;
#endif

#ifdef USE_PANORAMIC
    CurvedPanoramic* m_CurvedPanoramic;
    uchar* m_SendData;
#endif
    ImageCircularQueueBuffer* m_CurvedImageData;
    ByteLineImageArgs m_DestCurvedImageData;

    bool m_pipelineFinished;
    bool m_IsPaused;
    bool m_ImageStable{false};
    bool m_ImageShapeStable{false};
    bool m_LayoutStable{false};
    bool m_IsWaveActive{false};
    //    bool m_HasStableImageBuffer{false};
    struct FrameIndexSync m_FrameIndexSync;
    bool m_ProbeChanged;
    int m_ProbeID;

    bool m_IsInStressEchoAnalyzeState;
    int m_SystemScanMode;
    QSize m_CurvedPanoSize;
    int m_LineImageDebug;
    int m_LineColorMapDebug;
    int m_PausedLayoutIndex;
    bool m_WaitForFrozen;
    bool m_WaitingForFrozen;
    bool m_Prepare2LoadImage;
    QMutex m_WaitFrozenMutex;
    QMutex m_ActiveLayoutDataBufferLasetFrameIndexMutex;
    bool m_DSCChanging;
    PresetModeParameterSyncOnFrozen* m_PresetModeParameterSyncOnFrozen;
#ifdef USE_VA
    VAFunctionHandler* m_VAFunctionHandler;
#endif
    bool m_isStaticCuvedPanomicState{false};
    QString m_CurvedPanomicFileName{""};
    IStateManager* m_StateManager;
    IImageSaveHelper* m_ImageSaveHelper;
#ifdef USE_AUTOFREEZE
    void* m_DataForAutoFreeze{nullptr};
    int m_DataLengthForAutoFreeze{0};
#endif
    bool m_IsPause{false};
};

#endif /* end of include guard: CHISON_ULTRASOUND_CONTEXT */
