add_definitions(-DUSF_INTERFACE_IMAGING_USAPI_LIBRARY)
#add_definitions(-DMEMORY_LEAK_CHECK)
#add_definitions(-DIMAGERECEIVERLOG)
#add_definitions(-DLINEIMAGEDATALOSS_DEBUG_ENABLE)
#add_definitions(-DLINEIMAGEDATARECV_DEBUG_ENABLE)

include_directories(${COLORPIPELINE_INCLUDE_DIRS})
include_directories(${DOPPLERADJUST_INCLUDE_DIRS})
include_directories(${NEEDLEANGLE_INCLUDE_DIRS})
include_directories(${GENERATEDATA_INCLUDE_DIRS})
include_directories(${CMAKE_SOURCE_DIR}/src/usf/interface/exam/functionmodel)
include_directories(${CMAKE_SOURCE_DIR}/src/usf/interface/mark/common/business/algorithm)
include_depends(usf.i.imaging.imgcomunit usf.com.core.utilitymodel usf.i.imaging.adapter usf.i.imaging.buffer
    usf.i.imaging.datatransfer usf.i.exam.funcmodel usf.i.preset.bs usf.i.exam.dicom.bs)

option(DEBUG_DUMP OFF "dump all C# runtime input call to datastore")

if(${USE_PANORAMIC})
    set(PANORAMIC_RELATED_CPPS curvedpanoramic.cpp)
endif()
set(srcs
    usapi_global.h
    baseframeinfoswitcher.cpp
    chisonultrasoundcontext.cpp
    iframeinfoswitcher.cpp
    iimagedatareceiver.cpp
    imagedatareceiverbase.cpp
    imagedatareceiverswitcher.cpp
    imageframeinfoswitchers.cpp
    linedataframeinfoswitcher.cpp
    lineimagedatareceiver.cpp
    lineimagedatarecorder.cpp
    wholeimagedatareceiver.cpp
    wholeimageframeinfoswitcher.cpp
    cineloopplayergroup.cpp
    linecinelooper.cpp
    linehwinfo2wholehwinfo.cpp
    imagedatatransfer.cpp
    cinelooper.cpp
    wholeimagecinelooper.cpp
    framecontrol.cpp
    autoreduceframe.cpp
    autofreezecontrol.cpp
    doppleradjustcontrol.cpp
    autoneedleanglecontrol.cpp
    ${PANORAMIC_RELATED_CPPS}
)
#if(USE_PREPROCESS)
    list(APPEND srcs
        bfimagedatareceiver.cpp
        lineimagedatapalmreceiver.cpp
        lineimagedatainfostruct.cpp
        bfimagedatainfostruct.cpp
        lineimagedatazeus3receiver.cpp
        lineimagedatapalmzeus3receiver.cpp
        rdmaimagedatareceiver.cpp
        preprocessbufmanagerbase.cpp
        preprocessbufmanagerbasewithoutframeno.cpp
        preprocessbufmanager4pw.cpp
        preprocessbufmanager4cw.cpp
        preprocessbufmanager4pwwithoutframeno.cpp
        preprocessbufmanager4color.cpp
        preprocessbufmanager4colorwithoutframeno.cpp
        preprocessbufmanagerabstract.cpp
        preprocessbufmanagerforb.cpp
        preprocessbufmanagerforc.cpp
        preprocessbufmanagerforpw.cpp
        preprocessbufmanagerforcw.cpp
        preprocessbuffercontroller.cpp
        preprocessbufcontroller3_0.cpp
        pretreatmentpipleline.cpp
#        preprocesspipelineforb.cpp
        preprocesspipeline.cpp
        iqdata.cpp
        predataprocessbase.cpp
        bfdataprocess.cpp
        iqdataprocess.cpp
        predataforzeus.cpp
        preprocessrdmacontroller.cpp
        processzeusadapter.cpp
        zeusparametertool.cpp
        zeusparainfo.cpp)
#endif()


if(DEBUG_DUMP)
    add_definitions(-DDEBUG_DUMP)
    list(APPEND srcs 
        debugdump.cpp debugdumploader.cpp debugdumpreplayer.cpp)
    message("---enable DEBUG_DUMP, allow store and replay C# API trace")
else()
    message("---disable DEBUG_DUMP")

endif()

add_library_qt(usf.i.imaging.usapi ${srcs})

target_link_libraries(usf.i.imaging.usapi
                    usf.i.imaging.imgcomunit
                    usf.i.imaging.buffer
                    usf.com.core.utilitymodel
                    resource_exam
		            resource_imaging
                    usf.i.imaging.datatransfer
                    usf.i.exam.funcmodel
                    usf.i.preset.bs
                    ${COLORPIPELINE_LIBRARIES}
                    ${DOPPLERADJUST_LIBRARIES}
                    ${NEEDLEANGLE_LIBRARIES}
                    ${MNN_LIBRARIES}
                    ${IIMAGE_LIBRARIES}
                    ${GENERATEDATA_LIBRARIES}
                    usf.i.imaging.adapter
                    usf.i.exam.dicom.bs
                    )

set_target_properties(usf.i.imaging.usapi
                    PROPERTIES
                    DEFINE_SYMBOL CHISONULTRASOUND_LIBRARY)

target_link_libraries(usf.i.imaging.usapi ${OPENCV_LIBRARIES} ${CURVEDPANORAMIC_LIBRARIES})
if(${USE_PANORAMIC})
    target_include_directories(usf.i.imaging.usapi PRIVATE ${OPENCV_INCLUDE_DIRS} ${CURVEDPANORAMIC_INCLUDE_DIRS})
    target_link_libraries(usf.i.imaging.usapi ${CURVEDPANORAMIC_LIBRARIES})

    add_custom_command(TARGET usf.i.imaging.usapi
        POST_BUILD
        COMMAND cmake -DsourceDirector=${OPENCV_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )

    add_custom_command(TARGET usf.i.imaging.usapi
        POST_BUILD
        COMMAND cmake -DsourceDirector=${CURVEDPANORAMIC_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )

endif()

if(${USE_DOPPLERADJUST})
    add_custom_command(TARGET usf.i.imaging.usapi
               POST_BUILD
               COMMAND ${CMAKE_COMMAND} -E copy_directory ${DOPPLERADJUST_RUNTIMELIB_PATH}/ ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
               )
endif()

if(${USE_AUTONEEDLEANGLE})
    add_custom_command(TARGET usf.i.imaging.usapi
               POST_BUILD
               COMMAND ${CMAKE_COMMAND} -E copy_directory ${NEEDLEANGLE_RUNTIMELIB_PATH}/ ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
               )
endif()

if(${USE_IIMAGE})
    target_link_libraries(usf.i.imaging.usapi ${IIMAGE_LIBRARIES})
    add_custom_command(TARGET usf.i.imaging.usapi
                        POST_BUILD
                        COMMAND cmake -DsourceDirector=${IIMAGE_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
                        COMMAND cmake -DsourceDirector=${OPENCV_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
    )
endif(${USE_IIMAGE})

if(DEFINED MNN_RUNTIMELIB_PATH)
    add_custom_command(TARGET usf.i.imaging.usapi
                        POST_BUILD
                        COMMAND cmake -DsourceDirector=${MNN_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
    )
endif()
