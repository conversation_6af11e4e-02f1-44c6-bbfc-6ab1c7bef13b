#include "lineimagedatazeus3receiver.h"
#include "preprocessbufmanagerbase.h"
#include "sonoparameters.h"
#include "bytebuffer.h"
#include "bfpnames.h"
#include "setting.h"
#include "generalinfo.h"
#include "preprocessbuffercontroller.h"
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(logl, LineImageDataReceiver)

LineImageDataZeus3Receiver::LineImageDataZeus3Receiver(IGlPreprocessingCreater* rawImageBuffers, ZeusAPIInterface* api,
                                                       IImageSaveHelper* imageSaveHelper)
    : LineImageDataPALMReceiver(rawImageBuffers)
    , m_PreProcessBufferController(api, imageSaveHelper)
{
    foreach (const PreProcessBufManagerBase* pBuf, m_PreProcessBufferController.getBufferManagerList())
    {
        if (nullptr != pBuf)
        {
            connect(pBuf, SIGNAL(newData(const ByteBuffer&)), this, SLOT(onNewData(const ByteBuffer&)),
                    Qt::DirectConnection);
        }
    }
}

void LineImageDataZeus3Receiver::onNewData(const ByteBuffer& data)
{
    receiveBase(data); // 调用中间类提供的方法来访问基类实现
}

bool LineImageDataZeus3Receiver::shouldEnqueue(const LineDataHead* head)
{
    Q_UNUSED(head);
    return true;
}

void LineImageDataZeus3Receiver::onTDIEnChanged(const QVariant& value)
{
}

void LineImageDataZeus3Receiver::onColorImageModeChanged(const QVariant& value)
{
}

void LineImageDataZeus3Receiver::handleReceiverOrder(const ByteBuffer& data)
{
    int lineNo = -1;
    int linecolorAccumulative = -1;
    int linecolorIQFlag = -1;
    LineDataHead* lineDataHead = (LineDataHead*)(data.data());
    bool lostframe = false;

    if (*((unsigned short*)lineDataHead->SyncHead) != LineData::SyncHead_Flag)
    {
        logl()->error() << PRETTY_FUNCTION << "LineType" << (LineData::LineType)lineDataHead->LineType
                        << "linetype is error, maybe the package data is dirty"
                        << *((unsigned short*)lineDataHead->SyncHead);
        return;
    }
    int pointNum = m_SonoParameters->pIV(BFPNames::PointNumPerLineStr);
    int usbLineDataSize = pointNum * LINE_COUNT_PER_PACKAGE; // + m_RedundantPoints * 2;
    int usbLineSize = sizeof(LineDataHead) + usbLineDataSize;
    if (data.len() != usbLineSize)
    {
        //            ASSERT_X_LOG(data.len() != usbLineSize, PRETTY_FUNCTION,
        //                         "usb data size must equal usb line size");
#ifdef LINEIMAGEDATARECV_DEBUG_ENABLE
        qDebug() << "$$$$$$$$$usb data size must equal usb line size, " << data.len() << ", " << usbLineSize;
#endif
        return;
    }
    if (lineDataHead->PackageType == LineData::Package_Image)
    {
        if (m_SonoParameters->pBV(BFPNames::FreezeStr))
            return;
        if (m_SonoParameters->pSV(BFPNames::ExamModeCaptionStr) == "")
            return;                                     //没有切换预设值的时候不接受图像数据
        if (lineDataHead->LineType == LineData::Line_M) // m模式修改type保持和大机器一致
        {
            LineImageDataReceiver::receive(data);
            return;
        }

        SystemScanMode mode = (SystemScanMode)(m_SonoParameters->pIV(BFPNames::SystemScanModeStr));
        int frameNo = lineDataHead->getFrameNo();

        //使用LineDataHead提供的方法获取参数
        lineNo = lineDataHead->getLineNo();
        linecolorAccumulative = lineDataHead->getIQAccumulativeTimes();
        linecolorIQFlag = lineDataHead->getIQFlag();

        //        qDebug() << PRETTY_FUNCTION
        //                 << "usbLineSize: " << usbLineSize
        //                 << "usbLineDataSize: " << usbLineDataSize
        //                 << "pointNum: " << pointNum
        //                 << "frameNo: " << frameNo
        //                 << "lineNo: " << lineNo
        //                 << "linecolorAccumulative: " << linecolorAccumulative
        //                 << "StartLine: " << m_SonoParameters->pIV(BFPNames::StartLineStr)
        //                 << "StopLine: " << m_SonoParameters->pIV(BFPNames::StopLineStr)
        //                 << "m_RedundantPoints: " << m_RedundantPoints
        //                 << "Steering: " << lineDataHead->Steering;

        PreProcessBufManagerBase* udpordmg =
            m_PreProcessBufferController.getBufferManager((LineData::LineType)lineDataHead->LineType);

        if (udpordmg != nullptr)
        {
            //尝试重置参数
            udpordmg->tryReset(m_SonoParameters, frameNo, usbLineSize);
            // 这里只处理B数据，C和PW数据不走这里
            if (lineDataHead->LineType == LineData::Line_B)
            {
                LineImageDataReceiver::receive(data);
            }
            else
            {
                // 直接传入整个数据buffer,让PreProcessBufManager自己解析
                udpordmg->copytobufferbyframeindex(data.data(), lostframe);
            }
        }
        else
        {
            LineImageDataReceiver::receive(data);
        }
    }
    else
    {
        ByteBuffer bb = ByteBuffer(data.data(), usbLineSize);
        LineImageDataReceiver::receive(bb);
    }
}

void LineImageDataZeus3Receiver::onBeforeSonoParametersChange()
{
    LineImageDataPALMReceiver::onBeforeSonoParametersChange();
    m_PreProcessBufferController.onBeforeSonoParametersChange(m_SonoParameters);
}

void LineImageDataZeus3Receiver::onSonoParametersChanged()
{
    LineImageDataPALMReceiver::onSonoParametersChanged();
    m_PreProcessBufferController.onSonoParametersChanged(m_SonoParameters);
}
