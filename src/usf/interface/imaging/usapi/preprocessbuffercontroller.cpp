#include "preprocessbuffercontroller.h"
#include "preprocessbufmanager4color.h"
#include "preprocessbufmanager4colorwithoutframeno.h"
#include "preprocessbufmanager4pw.h"
#include "preprocessbufmanager4cw.h"
#include "preprocessbufmanager4pwwithoutframeno.h"
#include "preprocessbufmanagerbasewithoutframeno.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "controltableparameter.h"
#include "paracontrol/preprocessparacontroller.h"
#include "setting.h"
#include <QSettings>
#include <QDebug>
#include "resource.h"

PreProcessBufferController::PreProcessBufferController(ZeusAPIInterface* api, IImageSaveHelper* imageSaveHelper)
{
    m_Pipleline = new PretreatmentPipleline(0, api, imageSaveHelper);

    m_ParaController = new PreProcessParaController(0, api);
#ifndef USE_RECEIVE_WITHOUT_FRAME_NO
    m_BufferList.insert(LineData::Line_C_Vel, new PreProcessBufManager4color(m_ParaController, 20, m_Pipleline)); // C
    m_BufferList.insert(LineData::Line_C_SD,
                        new PreProcessBufManager4pw(m_ParaController, 3000, m_Pipleline)); // Others' D
    m_BufferList.insert(LineData::Line_CWD, new PreProcessBufManager4cw(m_ParaController, 3000, m_Pipleline)); // CW
#else
    m_BufferList.insert(LineData::Line_B, new PreProcessBufManagerBaseWithoutFrameNo(m_ParaController)); // B
    m_BufferList.insert(LineData::Line_C_Vel,
                        new PreProcessBufManager4colorWithoutFrameNo(m_ParaController, m_Pipleline)); // C
    m_BufferList.insert(LineData::Line_D,
                        new PreProcessBufManager4pwWithoutFrameNo(m_ParaController, m_Pipleline)); // SonoMax's D
#endif
    foreach (LineData::LineType type, m_BufferList.keys())
    {
        m_BufferList[type]->initbuffer();
    }
}

PreProcessBufferController::~PreProcessBufferController()
{
    if (nullptr != m_Pipleline)
    {
        delete m_Pipleline;
        m_Pipleline = nullptr;
    }

    if (nullptr != m_ParaController)
    {
        delete m_ParaController;
        m_ParaController = nullptr;
    }

    qDeleteAll(m_BufferList);
    m_BufferList.clear();
}

QList<PreProcessBufManagerBase*> PreProcessBufferController::getBufferManagerList()
{
    return m_BufferList.values();
}

PreProcessBufManagerBase* PreProcessBufferController::getBufferManager(LineData::LineType type)
{
    if (m_BufferList.contains(type))
    {
        return m_BufferList[type];
    }
    return nullptr;
}

void PreProcessBufferController::onBeforeSonoParametersChange(SonoParameters* sonoParameters)
{
    if (nullptr == sonoParameters)
    {
        return;
    }

    foreach (LineData::LineType type, m_BufferList.keys())
    {
        m_BufferList[type]->onBeforeSonoParametersChange(sonoParameters);
    }
}

void PreProcessBufferController::onSonoParametersChanged(SonoParameters* sonoParameters)
{
    m_ParaController->setSonoParameters(sonoParameters);

    foreach (LineData::LineType type, m_BufferList.keys())
    {
        m_BufferList[type]->onSonoParametersChanged(sonoParameters);
    }
}
