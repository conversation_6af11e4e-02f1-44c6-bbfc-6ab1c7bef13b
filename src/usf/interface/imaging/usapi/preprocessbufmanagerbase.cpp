#include "preprocessbufmanagerbase.h"
#include "preprocessbufmanager4color.h"
#include "preprocessbufmanager4pw.h"
#include "setting.h"
#include <thread>
//#include "pretreatmentpipleline.h"
#include "bfpnames.h"
#include "bytebuffer.h"
#include "controltableparameter.h"
#include "zeusparameternames.h"
#include "generalinfo.h"
#include "parameter.h"
#include "pretreatmentpipleline.h"
#include "probeblockdataset.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "qmath.h"
#include "resource.h"
#include "sonoparameters.h"
#include "systemscanmodeclassifier.h"
#include "variantutil.h"
#include "syncidmanager.h"
#include <QDebug>
#include <QDir>
#include <QSettings>
#include <QThread>
#if defined(SYS_UNIX) || defined(SYS_ANDROID)
#include "resourcehelper.h"
#endif
//#define UDPPACKAGESIZE 1024
//#define UDPMAXRCVSIZEB 256 * (UDPPACKAGESIZE + LINEDATAHEADLEN)     //(2*512+header)*linenum
//#define UDPMAXRCVSIZECOLOR 256 * (UDPPACKAGESIZE + LINEDATAHEADLEN) //高密度256
//#define UDPMAXRCVSIZECOLORMERGE (/*8*/MAXCOLORFRAMES * 2 * UDPMAXRCVSIZECOLOR) //(2*512+header)*width*iqcount*frame
//#define COLORIQPROCESSWAITCOUNT 1                                  //b图像在c模式卡主多少帧的时候开始丢帧 同步需求
//#define UDPMAXRCVSIZEPW (UDPPACKAGESIZE + LINEDATAHEADLEN)          //pw模式下单根线的大小
int PreProcessBufManagerBase::m_Lastimtype = -1;
static uint8_t* __bufferpoolset = NULL;

static int dopaccumulatesum = 0;
using namespace ZeusParameterNames;
#include <iostream>
#define TRACE
#ifndef TRACE
#define tcout 0 && std::cout //或者NULL && cout
#else
#define tcout std::cout
#endif

PreProcessBufManagerBase::PreProcessBufManagerBase(PreProcessParaController* paraController, int dataQueueSize,
                                                   PretreatmentPipleline* pipleline)
    : m_IqDataProcess(nullptr)
    , m_Pipleline(pipleline)
    , m_ParaController(paraController)
    , m_Currentframeindex(-1)
    , m_Startline(-1)
    , m_Linesize(-1)
    , m_Stopline(-1)
    , m_Pointsize(-1)
    , m_TransParaChangeCount(1)
    , m_Highdensity(false)
    , m_ParametersChanged(false)
    , m_IsTDIEn(false)
    , m_Registersig(false)
    , m_BuffPool(nullptr)
{
    m_Cachesize = CACHESIZE;
    InitDataProceeThread(dataQueueSize);
}

PreProcessBufManagerBase::~PreProcessBufManagerBase()
{
    if (m_BuffPool != nullptr)
    {
        delete[] m_BuffPool;
        m_BuffPool = nullptr;
    }

    // TODO: 线程释放前，应该先暂停
    if (m_IqDataProcess != nullptr)
    {
        delete m_IqDataProcess;
        m_IqDataProcess = nullptr;
    }
}

bool PreProcessBufManagerBase::gettransstate()
{
    return 0 == m_TransParaChangeCount;
}

void PreProcessBufManagerBase::finishOncetrans()
{
    m_TransParaChangeCount--;
    m_Registersig = true;
}

void PreProcessBufManagerBase::resetTransstate()
{
    m_TransParaChangeCount = 1;
}

int PreProcessBufManagerBase::getRealLineNum()
{
    return m_RealPackNum;
}

int PreProcessBufManagerBase::getadaptiveframeid(int fidx, bool& lossframe)
{
    int cacheid = geteqframeindexcacheid(fidx);
    if (-1 == cacheid)
    {
        //        tcout << "@@@@@@@@@@@@@@@@@@@-1==fidxt :" << fidx<<":"<<std::string(typeid(*this).name())<< std::endl;
        resetallbyframeindex(fidx);
        cacheid = 0;
        lossframe = true;
    }
    return cacheid;
}

int PreProcessBufManagerBase::geteqframeindexcacheid(int fidx)
{
    int eqcached = -1;
    for (int i = 0; i < m_Cachesize; i++)
    {
        if (NULL != returnselfcontrolbase(i) && returnselfcontrolbase(i)->m_FrameIndex == fidx)
            eqcached = i;
    }
    return eqcached;
}

int PreProcessBufManagerBase::getmaxframeindex()
{
    int maxcached = 0;
    int maxfidx = -1;
    for (int i = 0; i < m_Cachesize; i++)
    {
        if (NULL == returnselfcontrolbase(i))
            continue;
        if (returnselfcontrolbase(i)->m_FrameIndex > maxfidx)
        {
            maxfidx = returnselfcontrolbase(i)->m_FrameIndex;
            maxcached = i;
        }
    }
    Q_ASSERT(maxfidx != -1);
    return maxfidx;
}

void PreProcessBufManagerBase::tryReset(SonoParameters* sonoParameters, int curFrameIndex, int usblineSize)
{
    if (gettransstate())
    {
        return;
    }

    resetallbyframeindex(curFrameIndex);
    setLineInformation(sonoParameters, usblineSize);

    if (!m_Registersig)
    {
        resetTransstate();
    }
    finishOncetrans();
}

ByteBuffer PreProcessBufManagerBase::processNewData(uchar* data, InfoForPostProcess& info, int dataType)
{
    Q_UNUSED(data);
    Q_UNUSED(info);
    Q_UNUSED(dataType)
    return ByteBuffer();
}

uint8_t* PreProcessBufManagerBase::getemptylinedata()
{
    return nullptr;
}

bool PreProcessBufManagerBase::shouldwait()
{
    return false;
}

void PreProcessBufManagerBase::resetallbyframeindex(int fidx)
{
    m_Controlbuffers[0].m_LineCount = 0;
    m_Controlbuffers[1].m_LineCount = 0;
    m_Controlbuffers[0].m_FrameIndex = fidx;
    m_Controlbuffers[1].m_FrameIndex = fidx + 1;
}

void PreProcessBufManagerBase::resetlinecountbycacheid(int cacheid)
{
    m_Controlbuffers[cacheid].m_LineCount = 0;
}

void PreProcessBufManagerBase::resetframeindexbycacheid(int cacheid, int fidx)
{
    m_Controlbuffers[cacheid].m_FrameIndex = fidx;
}

PackageInfo* PreProcessBufManagerBase::returnselfcontrolbase(int ix)
{
    return &this->m_Controlbuffers[ix];
}

void PreProcessBufManagerBase::initbuffer()
{
    //    clear();
    if (nullptr == m_BuffPool)
    {
        m_BuffPool = new uchar[MaxRCVSIZEB * m_Cachesize];
    }

    for (int i = 0; i < m_Cachesize; i++)
    {
        m_Controlbuffers[i].m_LineCount = 0;
        m_Controlbuffers[i].m_FrameIndex = -1;
        m_Controlbuffers[i].m_Buffertp = &m_BuffPool[MaxRCVSIZEB * i];
    }
    m_Modeparamsreset << BFPNames::StartLineStr << BFPNames::StopLineStr << BFPNames::HighDensityStr;
}

ByteBuffer PreProcessBufManagerBase::getbufferbytebyframeindex(int fidx)
{
    return ByteBuffer(m_Controlbuffers[fidx].m_Buffertp, m_Linesize * m_RealPackNum);
}

//#include "logger.h"

// LOG4QT_DECLARE_STATIC_LOGGER(logll, PreProcessBufManagerBase)

bool PreProcessBufManagerBase::isLineNoInvalid(int lineNo) const
{
    return (lineNo < m_Startline || lineNo > m_Stopline + 2);
}

int PreProcessBufManagerBase::copytobufferbyframeindex(uint8_t* data, bool& lostframe)
{
    LineDataHead* lineDataHead = (LineDataHead*)data;
    int lineNo = lineDataHead->getLineNo();
    int frameNo = lineDataHead->getFrameNo();

    if (isLineNoInvalid(lineNo))
    {
        return -2;
    }

    int cacheid = getadaptiveframeid(frameNo, lostframe);

    int copypos = ((lineNo - m_Startline) / 2 / m_Linegap) * m_Linesize;
    memcpy(&m_Controlbuffers[cacheid].m_Buffertp[copypos], data, m_Linesize);
    m_Controlbuffers[cacheid].m_LineCount += 2;

    if (m_Controlbuffers[cacheid].m_LineCount >= m_Linenum)
    {
        resetlinecountbycacheid(cacheid);
        if (m_Controlbuffers[cacheid].m_FrameIndex > m_Controlbuffers[(cacheid + 1) % 2].m_FrameIndex)
        {
            resetallbyframeindex(frameNo + 1);
        }
        else
        {
            resetframeindexbycacheid(cacheid, getmaxframeindex() + 1);
        }
        return cacheid;
    }

    return -1;
}

void PreProcessBufManagerBase::clear()
{
    //    for (int i = 0; i < m_Cachesize; i++)
    //    {
    //        if(nullptr != m_Controlbuffers[i].m_Buffertp)
    //        {
    //            delete m_Controlbuffers[i].m_Buffertp;
    //            m_Controlbuffers[i].__buffertp = nullptr;
    //        }
    //    }
}

void PreProcessBufManagerBase::InitDataProceeThread(int dataQueueSize)
{
    if ((nullptr != m_Pipleline) && (-1 != dataQueueSize))
    {
        m_IqDataProcess = new IQDataProcess(dataQueueSize, m_Pipleline);

        //    //该线程结束时销毁
        //    connect(&m_IqThread, &QThread::finished, m_IqDataProcess, &QObject::deleteLater);
        //    connect(this, SIGNAL(newIqData(const iqDataInfo&)), m_IqDataProcess, SLOT(onNewIqData(const
        //    iqDataInfo&)));

        //        connect(m_IqDataProcess, SIGNAL(newData(uchar*, uchar*)), this, SLOT(onNewData(uchar*, uchar*)),
        //        Qt::DirectConnection);

        m_IqDataProcess->start();
    }
}

void PreProcessBufManagerBase::onParamChanged()
{
    m_TransParaChangeCount++;
    m_Lastimtype = -1;
}

void PreProcessBufManagerBase::onNewData(uchar* data, InfoForPostProcess& info, int dataType)
{
    ByteBuffer byte = processNewData(data, info, dataType);
    if (!byte.isNull())
    {
        m_Lastimtype = ((LineDataHead*)byte.data())->LineType;
        emit newData(byte);
    }
}

void PreProcessBufManagerBase::adjustNonPipeline(QString bfpName, QVariant value)
{
    if (BFPNames::DopAccumulateNumStr == bfpName)
    {
        dopaccumulatesum = value.toInt();
    }
}

void PreProcessBufManagerBase::setLineInformation(SonoParameters* sonoParameters, int usblineSize)
{
    if (nullptr == sonoParameters)
    {
        return;
    }

    QMutexLocker locker(&m_LineInfoMutex);
    int startlinevalue = sonoParameters->pIV(BFPNames::StartLineStr);
    int stoplinevalue = sonoParameters->pIV(BFPNames::StopLineStr);
#ifdef USE_TARGET_PALM
    bool highDensity = sonoParameters->pBV(BFPNames::HighDensityStr);
#else
    int highDensity = sonoParameters->pIV(BFPNames::HighDensityStr);
#endif

    int linenumvalue = ProbeParameters::lines(startlinevalue, stoplinevalue, highDensity);
    //        reallinenumb = ((stoplinevalue/2) - (startlinevalue/2) +1 );
    //    qDebug() << PRETTY_FUNCTION
    //             << "B:"
    //             << "startlinevalue:" << startlinevalue
    //             << "stoplinevalue:"  << stoplinevalue
    //             << "highDensity:"    << highDensity
    //             << "linenumvalue:"   << linenumvalue;

    m_Startline = startlinevalue;
    m_Linenum = linenumvalue;
    m_Stopline = stoplinevalue;
    m_Linesize = usblineSize;
    m_RealPackNum = ((stoplinevalue / 2) - (startlinevalue / 2) + 1); // 实际的包数(B数据一个包含两根线，所以要除以2)
#ifdef USE_TARGET_PALM
    m_Highdensity = highDensity;
    m_Linegap = m_Highdensity ? 1 : 2;
#else
    m_Linegap = 1; // 任意线密度下包数随着高中低密度动态变化，线之间间隙固定为1
#endif
    m_Pointsize = sonoParameters->pIV(BFPNames::PointNumPerLineStr);
}

void PreProcessBufManagerBase::onBeforeSonoParametersChange(SonoParameters* sonoParameters)
{
    if (nullptr == sonoParameters)
    {
        return;
    }

    //常规参数 用于复位
    for (int i = 0; i < m_Modeparamsreset.size(); i++)
    {
        disconnect(sonoParameters->parameter(m_Modeparamsreset[i]), SIGNAL(valueChanged(const QVariant&)), this,
                   SLOT(onParamChanged()));
    }
}

void PreProcessBufManagerBase::onSonoParametersChanged(SonoParameters* sonoParameters)
{
    //常规参数 用于复位
    for (int i = 0; i < m_Modeparamsreset.size(); i++)
    {
        connect(sonoParameters->parameter(m_Modeparamsreset[i]), SIGNAL(valueChanged(const QVariant&)), this,
                SLOT(onParamChanged()), Qt::DirectConnection);
    }
}
