#include "preprocessbufmanager4color.h"
#include "bfpnames.h"
#include "bytebuffer.h"
#include "fpscalculator.h"
#include "generalinfo.h"
#include "iqdata.h"
#include "logger.h"
#include "pretreatmentpipleline.h"
#include "paracontrol/preprocessparacontroller.h"
#include "probeblockdataset.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "setting.h"
#include "sonoparameters.h"
#include "variantutil.h"
#include "util.h"
#include <array>

#include "syncidmanager.h"
LOG4QT_DECLARE_STATIC_LOGGER(log4color, PreProcessBufManager4color)

int PreProcessBufManager4color::m_ColorReceiveFPS = 0;
int PreProcessBufManager4color::m_ColorPreProcessFPS = 0;

using namespace ZeusParameterNames;
PreProcessBufManager4color::PreProcessBufManager4color(PreProcessParaController* paraController, int dataQueueSize,
                                                       PretreatmentPipleline* pipleline)
    : PreProcessBufManagerBase(paraController, dataQueueSize, pipleline)
    , m_PacketSize(-1)
    , m_Assemblecolorbatchsize(-1)
    , m_Colorpointsize(-1)
    , m_Topbordercolor(-1)
    , m_Bottombordercolor(-1)
    , m_IQPool(nullptr)
    , m_FpsCalculator(new FpsCalculator())
    , m_FourBeamLineNum(-1)
    , m_IsCOverLap(false)
    , m_ColorPointNum(-1)
{
    m_Cachesize = CACHESIZE + 1;
    connect(m_FpsCalculator, SIGNAL(update(float)), this, SLOT(showFps(float)));
}

PreProcessBufManager4color::~PreProcessBufManager4color()
{
    //    clear();
    if (nullptr != m_IQPool)
    {
        delete[] m_IQPool;
        m_IQPool = nullptr;
    }
}

ByteBuffer PreProcessBufManager4color::processNewData(uchar* data, InfoForPostProcess& info, int dataType)
{
    ++m_ColorPreProcessFPS;

    if (info.info[1] != SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId))
    {
        return ByteBuffer();
    }
    uchar* head = (uchar*)&info.info[PretreatmentPipleline::HeadInfoStart];
    //不开启overlap时，zeus前处理前的线数和前处理之后的线数是一致的。
    // m_FourBeamLineNum是前处理之前的线数,前处理完的线数是m_LineNum
    bool isReceive = (m_FourBeamLineNum == info.info[PretreatmentPipleline::LineNum]);
    bool isShortData = (dataType == BFDataType::DynamicFlow); // MVI: true,  Other: false
    int linesPerPacket = isShortData ? 2 : 1;
    int colordatasize = m_Pointsize * (isShortData ? sizeof(uchar) : sizeof(short));
#ifdef USE_HIGHDENSITY_INT
    int realstopline = m_Stopline;
#else
    int realstopline = m_Highdensity ? m_Stopline - 1 : m_Stopline - 2;
#endif
    int iqCacheId = findiqbufcached();
    for (int j = m_Startline, i = 0; j <= realstopline; i++)
    {
        if (dataType == BFDataType::DynamicFlow)
        {
            head[3] = LineData::Line_Dynamic_Flow;
        }
        // line number
        head[4] = j & 0xFF;
        head[5] = (j & 0x300) >> 8;
        // frame flag
        if (j == m_Startline)
        {
            head[7] = 0;
        }
        else if (j != realstopline)
        {
            head[7] = 1;
        }
        else
        {
            head[7] = 2;
        }

        // 对于一个像素8bit的数据(目前仅MVI)，为适配后续线数据的入队操作，参照B数据的处理方式，将每两条线(带前一条线的数据头)放入一个1040Byte的线数据包中
        bool isNewPacket = (!isShortData || (i % 2 == 0)); // 8bit数据时，放入线数据的后一条线为false
        int packetIndex = i / linesPerPacket;

        // copy head
        if (isNewPacket)
        {
            memcpy(&m_Controlbuffers[iqCacheId].m_Buffertpool[m_Linesize * packetIndex], head, LINEDATAHEADLEN);
        }
        else if (
            j ==
            realstopline) // 最后一条线如果不是新包（MVI这样的一个包两条线的场景），包头用的是倒数第二条线的信息头，没有最后一条线的标志，此处补上该标志。
        {
            m_Controlbuffers[iqCacheId].m_Buffertpool[m_Linesize * packetIndex + 7] = 2;
        }

        if (isReceive)
        {
            // copy data
            int offset = m_Linesize * packetIndex + LINEDATAHEADLEN + (isNewPacket ? 0 : 1) * colordatasize;
            memcpy(&m_Controlbuffers[iqCacheId].m_Buffertpool[offset], &data[i * colordatasize], colordatasize);
        }
        else
        {
            qCritical() << "PreProcessBufManager4color::processNewData  LineNum is not same as m_LineNum";
        }

#ifdef USE_HIGHDENSITY_INT
        j++;
#else
        j = m_Highdensity ? j + 1 : j + 2;
#endif
    }

    return ByteBuffer((uchar*)m_Controlbuffers[iqCacheId].m_Buffertpool,
                      m_Linesize * (isShortData ? (m_RealPackNum + 1) / 2 : m_RealPackNum));
}

void PreProcessBufManager4color::resetallbyframeindex(int fidx)
{
    QMutexLocker locker(&m_LineInfoMutex);
    for (int i = 0; i < m_Cachesize; i++)
    {
        m_Controlbuffers[i].m_FrameIndex = fidx + i;
        m_Controlbuffers[i].m_Iqbuffer = false;
        for (int j = 0; j < MAXCOLORFRAMES; j++)
        {
            for (int k = 0; k < 2; k++) // I Q data initialize
            {
                m_Controlbuffers[i].m_ColorUnitAssembled[j][k].m_LineCount = 0;
                m_Controlbuffers[i].m_ColorUnitAssembled[j][k].m_FrameIndex = fidx + i;
            }
        }
    }
    m_Controlbuffers[m_Cachesize - 1].m_Iqbuffer = true; //最后一个buffer是iqbuffer
}

void PreProcessBufManager4color::resetlinecountbycacheid(int cacheid)
{
    for (int j = 0; j < m_PacketSize; j++)
    {
        for (int k = 0; k < 2; k++)
        {
            m_Controlbuffers[cacheid].m_ColorUnitAssembled[j][k].m_LineCount = 0;
        }
    }
}

void PreProcessBufManager4color::resetframeindexbycacheid(int cacheid, int fidx)
{
    m_Controlbuffers[cacheid].m_FrameIndex = fidx;
    for (int j = 0; j < m_PacketSize; j++)
    {
        for (size_t k = 0; k < 2; k++)
        {
            m_Controlbuffers[cacheid].m_ColorUnitAssembled[j][k].m_FrameIndex = fidx;
        }
    }
}

PackageInfo* PreProcessBufManager4color::returnselfcontrolbase(int ix)
{
    return this->m_Controlbuffers[ix].m_Iqbuffer ? NULL : &this->m_Controlbuffers[ix];
}

void PreProcessBufManager4color::initbuffer()
{
    //    clear();
    if (nullptr == m_IQPool)
    {
        m_IQPool = new uchar[MAXRCVSIZECOLORMERGE * m_Cachesize];
    }

    for (int i = 0; i < m_Cachesize; i++)
    {
        m_Controlbuffers[i].m_Buffertpool = &m_IQPool[MAXRCVSIZECOLORMERGE * i];
        m_Controlbuffers[i].m_FrameIndex = -1;
        m_Controlbuffers[i].m_Iqbuffer = false;
        m_Controlbuffers[i].m_LineNumCurt = 0;
        for (size_t j = 0; j < MAXCOLORFRAMES; j++)
        {
            for (int k = 0; k < 2; k++) // I Q data initialize
            {
                m_Controlbuffers[i].m_ColorUnitAssembled[j][k].m_LineCount = 0;
                m_Controlbuffers[i].m_ColorUnitAssembled[j][k].m_FrameIndex = 0;
            }
        }
    }
    m_Controlbuffers[m_Cachesize - 1].m_Iqbuffer = true; //最后一个buffer是iqbuffer
    m_Emptydataptr = new uint8_t[MAXRCVSIZECOLOR];
    memset(m_Emptydataptr, 0, MAXRCVSIZECOLOR);

    connect(m_Pipleline, SIGNAL(onNewColorData(uchar*, InfoForPostProcess&, int)), this,
            SLOT(onNewData(uchar*, InfoForPostProcess&, int)), Qt::DirectConnection);
    m_Modeparamsreset << BFPNames::StartLineColorStr << BFPNames::StopLineColorStr << BFPNames::ColorLineDensityStr
                      << BFPNames::TDILineDensityStr << BFPNames::ColorLineDensitySNStr << BFPNames::MVILineDensityStr
                      << BFPNames::TopBorderColorStr << BFPNames::BottomBorderColorStr << BFPNames::PacketSizeStr
                      << BFPNames::PacketSizeTDIStr << BFPNames::PacketSizePDStr << BFPNames::PacketSizeSNStr
                      << BFPNames::PacketSizeMVIStr << BFPNames::MBColorStr << BFPNames::COverlapStr
                      << BFPNames::ColorOverlapWeightStr;
}

void PreProcessBufManager4color::showFps(float value)
{
    log4color()->debug("%1: receive data fps %2", PRETTY_FUNCTION, value);
}

int PreProcessBufManager4color::copytobufferbyframeindex(uint8_t* data, bool& lostframe)
{
    LineDataHead* lineDataHead = (LineDataHead*)data;
    int lineNo = lineDataHead->getLineNo();
    int frameNo = lineDataHead->getFrameNo();
    int lineColorAccumulative = lineDataHead->getIQAccumulativeTimes();
    int lineColorIQFlag = lineDataHead->getIQFlag();
    int frameFlag = lineDataHead->FrameFlag;
    int colorAccumEnd = lineDataHead->ColorAccumEnd;

    if (Setting::instance().defaults().isIODeviceVirtual())
    {
        if (((lineNo % m_Linegap) != 0) || (lineNo < m_Startline) || (lineNo > m_Stopline))
        {
            return -2;
        }
    }

    if (m_IsCOverLap)
    {
        CHECK_COVERLAP_LINE_ERROR();
    }
    else
    {
        CHECK_LINE_ERROR();
    }
    //    {
    //        m_IqDataProcess->backupHead(data);
    //    }
    int cacheid = getadaptiveframeid(frameNo, lostframe);
    int colordatasize = (m_Linesize - LINEDATAHEADLEN);

    //    调试时结束线数不对，手动丢弃处理
    //    bool dropFlag = false;
    //    if (linepos > m_Stopline - 1)
    //    {
    //        linepos = m_Stopline - 1;
    //        dropFlag = true;
    //    }
    int lineidx = ((lineNo - m_Startline) / m_Linegap);

    //    int testCurrentIndex = testLogBufferIndex++ * testLogBufferSize;
    //    testLog[testCurrentIndex + 0] = fidx;
    //    testLog[testCurrentIndex + 1] = lostframe;
    //    testLog[testCurrentIndex + 2] = cacheid;
    //    testLog[testCurrentIndex + 3] = colordatasize;
    //    testLog[testCurrentIndex + 4] = m_PacketSize;
    //    testLog[testCurrentIndex + 5] = linecolorAccumulative;
    //    testLog[testCurrentIndex + 6] = linepos;
    //    testLog[testCurrentIndex + 7] = m_Startline;
    //    testLog[testCurrentIndex + 8] = m_Stopline;
    //    testLog[testCurrentIndex + 9] = m_Linegap;
    //    testLog[testCurrentIndex + 10] = lineDataHead->FrameFlag;
    //    testLog[testCurrentIndex + 11] = lineidx;

    //    qDebug() << "&&&&&&&&&, "
    //             << "frameNo: " << fidx
    //             << "lostframe: " << lostframe
    //             << "cacheid: " << cacheid
    //             << ", colordatasize:" << colordatasize
    //             << ", FrameAssembledsize:" << m_PacketSize
    //             << ", linecolorAccumulative:" << linecolorAccumulative
    //             << ", linepos:" << linepos
    //             << ", m_Startline:" << m_Startline
    //             << ", m_Stopline:" << m_Stopline
    //             << ", m_Linegap:" << m_Linegap
    //             << ", FrameFlag" << lineDataHead->FrameFlag
    //             << ", lineidx:" << lineidx
    //             << ", IQFlag:" << linecolorIQFlag;

    int alineIorQpiecesize = 0;
    Q_ASSERT(lineColorAccumulative <= MAXCOLORFRAMES);
    if (m_Colorpointsize == m_Pointsize / 2) //掌超 wifi
    {
        //每次只发送 roi 框内的血流数据，每次 发送两根线的pq是 p1 q1 p2 q2  和上面类似，只不过是roi区域内隔点采样
        int __halflinesize = m_Pointsize >> 1;        // 256
        int __realcolorlinesize = colordatasize >> 1; // 512
        alineIorQpiecesize = __halflinesize * sizeof(unsigned short) * m_Linenum;
        int realcolorheight = ceil(m_Bottombordercolor / 2.0) - ceil(m_Topbordercolor / 2.0);
        for (int z = 0; z < 2; z++) //每次收两根线
        {
            uint8_t* lineptr[2]; // p q
            for (int t = 0; t < 2; t++)
            {
                uint8_t* linepq = m_Tempiqbuffer[t];
                memset(linepq, 0, m_Pointsize);
                int fromex = m_Topbordercolor & 0xFFFE; //取偶
                if (m_Topbordercolor % 2)
                    fromex += 2;
                uint8_t* temptr = &data[LINEDATAHEADLEN + z * m_Pointsize + t * __halflinesize];
                for (int i = 0; i < realcolorheight; i++)
                {
                    linepq[fromex + i * 2] = temptr[2 * i];
                    linepq[fromex + i * 2 + 1] = temptr[2 * i + 1];
                    ;
                }
                //底部镜像一下 因为插值需要依赖底部没有
                int sx = fromex + realcolorheight * 2;
                for (int q = sx, p = sx - 1; q < sx + 6; q += 2, p -= 2)
                {
                    linepq[q] = linepq[p - 1];
                    linepq[q + 1] = linepq[p];
                    ;
                }
                lineptr[t] = linepq;
            }

            int posI = LINEDATAHEADLEN + m_PacketSize * alineIorQpiecesize * 0 +
                       lineColorAccumulative * alineIorQpiecesize + lineidx * __halflinesize * sizeof(unsigned short);
            int posQ = LINEDATAHEADLEN + m_PacketSize * alineIorQpiecesize * 1 +
                       lineColorAccumulative * alineIorQpiecesize + lineidx * __halflinesize * sizeof(unsigned short);
            memcpy(&m_Controlbuffers[cacheid].m_Buffertpool[posI], lineptr[0], __realcolorlinesize);
            memcpy(&m_Controlbuffers[cacheid].m_Buffertpool[posQ], lineptr[1], __realcolorlinesize);
            m_Controlbuffers[cacheid].m_ColorUnitAssembled[lineColorAccumulative][0].m_LineCount += 1;
            m_Controlbuffers[cacheid].m_ColorUnitAssembled[lineColorAccumulative][1].m_LineCount += 1;

            lineidx += 1;
        }
    }
    else if (m_Colorpointsize == m_Pointsize)
    {
        int sizePerLine = m_ColorPointNum * sizeof(unsigned short);
        alineIorQpiecesize = sizePerLine * m_FourBeamLineNum; //所有的线数据的I为一组 Q为一组
        //        qDebug() << "所有的线数据的I为一组 Q为一组，size: " << alineIorQpiecesize
        //                 << ", m_Pointsize" << m_Pointsize
        //                 << ", m_Linenum" << m_Linenum
        //                 << ", cacheid" << cacheid
        //                 << ", linecolorAccumulative" << linecolorAccumulative
        //                 << ", linecolorIQFlag" << linecolorIQFlag;
        int linePackges = m_Pointsize / m_ColorPointNum;
        int currentIqFlag = lineColorIQFlag;

        if (linePackges > 1)
        {
            // 一次传输IQ数据，FPGA 中弃用了IQFlag，软件自己维护，协议中数据存放的顺序是先I后Q，
            // currentIqFlag初始化为0
            currentIqFlag = 0;
        }

        for (int linePerPackge = 0; linePerPackge < linePackges; linePerPackge++)
        {
            // IQ一次传输的话（如：SonoAir血流256点），约定的是先I后Q，且flag，I=0,Q=1
            int pos = LINEDATAHEADLEN + m_PacketSize * alineIorQpiecesize * currentIqFlag +
                      alineIorQpiecesize * lineColorAccumulative + lineidx * sizePerLine;
            int paddingPos = pos + sizePerLine - 8;
            memcpy(&m_Controlbuffers[cacheid].m_Buffertpool[pos],
                   &data[LINEDATAHEADLEN + linePerPackge * (sizePerLine - 8)], sizePerLine - 8);
            memset(&m_Controlbuffers[cacheid].m_Buffertpool[paddingPos], 0x00, 8);

            m_Controlbuffers[cacheid].m_ColorUnitAssembled[lineColorAccumulative][currentIqFlag].m_LineCount += 1;
            currentIqFlag += 1;
        }
    }

    // int copypos=lineidx * colordatasize;
    // memcpy(&__controlbuffers[cacheid].colorUnitAssembled[linecolorAccumulative].colorUnit[linecolorIQFlag].__buffertp[copypos],
    // &data[LINEDATAHEADLEN], colordatasize); int alineIorQpiecesize=__frameAssembledsize*__pointsize*sizeof(unsigned
    // short); 按照iiiiiiiiqqqqqqqq int
    // pos=2*alineIorQpiecesize*lineidx+linecolorIQFlag*alineIorQpiecesize+linecolorAccumulative*__pointsize*sizeof(unsigned
    // short);

    //按照iq iq iq iq iq iq iq iq 8个 为一组作为一条线存储
    // int pos=2*alineIorQpiecesize*lineidx + linecolorAccumulative*__pointsize*sizeof(unsigned short)*2 +
    // linecolorIQFlag*__pointsize*sizeof(unsigned short);

    //按照块来存储 __pointsize*sizeof(unsigned short)*__linenum 为一组是一个I 接下来是 Q 也是同等大小 一共 8组每个是I和Q
    //  int alineIorQpiecesize=__pointsize*sizeof(unsigned short)*__linenum;//一组I/Q所有的大小
    //  int pos=2*alineIorQpiecesize*linecolorAccumulative + alineIorQpiecesize*linecolorIQFlag +
    //  lineidx*__pointsize*sizeof(unsigned short);
    auto cid = caculatecolorlinecount(cacheid);
    // qCritical() << PRETTY_FUNCTION
    //         << ", cacheid: " << cacheid
    //         << ", frameNo: " << frameNo
    //         << ", lineNo: " << lineNo
    //         << ", lineidx: " << lineidx
    //         << ", lineColorIQFlag" << lineColorIQFlag
    //         << ", lineColorAccumulative:" << lineColorAccumulative
    //         << ", m_PacketSize:" << m_PacketSize
    //         << ", colordatasize:" << colordatasize
    //         << ", m_Startline:" << m_Startline
    //         << ", m_Stopline:" << m_Stopline
    //         << ", m_Linegap:" << m_Linegap
    //         << ", cid:" << cid
    //         << ", m_Assemblecolorbatchsize:" << m_Assemblecolorbatchsize
    //         << ", lostframe: " << lostframe
    //         << ", frameFlag: " << frameFlag
    //         << ", colorAccumEnd: " << colorAccumEnd;

    if (cid >= m_Assemblecolorbatchsize)
    {
        resetlinecountbycacheid(cacheid);
        if (m_Controlbuffers[cacheid].m_FrameIndex > m_Controlbuffers[(cacheid + 1) % 2].m_FrameIndex)
        {
            resetallbyframeindex(frameNo + 1);
        }
        else
        {
            resetframeindexbycacheid(cacheid, getmaxframeindex() + 1);
        }

        ++m_ColorReceiveFPS;

        memcpy(m_Controlbuffers[cacheid].m_Buffertpool, data, LINEDATAHEADLEN); // copy head at the last time
        m_IqDataProcess->enqueue(IQData(ByteBuffer(m_Controlbuffers[cacheid].m_Buffertpool,
                                                   m_PacketSize * alineIorQpiecesize * 2 + LINEDATAHEADLEN),
                                        CData, m_ColorPointNum, m_FourBeamLineNum, m_PacketSize, 2,
                                        IQFormat4Zeus::IACC123QACC123));
    }
    return -1;
}

void PreProcessBufManager4color::clear()
{
    //    for (int i = 0; i < m_Cachesize; i++)
    //    {
    //        resetlinecountbycacheid(i);
    //        if(nullptr != m_Controlbuffers[i].__buffertpool)
    //        {
    //            delete m_Controlbuffers[i].__buffertpool;
    //            m_Controlbuffers[i].__buffertpool = nullptr;
    //        }
    //    }
}

void PreProcessBufManager4color::setLineInformation(SonoParameters* sonoParameters, int usblineSize)
{
    if (nullptr == sonoParameters)
    {
        return;
    }

    QMutexLocker lock(&m_LineInfoMutex);

    int startlinevalue = sonoParameters->pIV(BFPNames::StartLineColorStr);
    int stoplinevalue = sonoParameters->pIV(BFPNames::StopLineColorStr);

    QString colorDensityName = BFPNames::ColorLineDensityStr;
    if (sonoParameters->pBV(BFPNames::TDIEnStr))
    {
        colorDensityName = BFPNames::TDILineDensityStr;
    }
    else if (sonoParameters->pBV(BFPNames::SonoNeedleStr))
    {
        colorDensityName = BFPNames::ColorLineDensitySNStr;
    }
    else if (sonoParameters->pBV(BFPNames::MVIModeStr))
    {
        colorDensityName = BFPNames::MVILineDensityStr;
    }
    else
    {
        // do nothing
    }

#ifdef USE_HIGHDENSITY_INT
    int highDensity = sonoParameters->pIV(colorDensityName);
#else
    bool highDensity = sonoParameters->pBV(colorDensityName);
#endif

    int linenumvalue = ProbeParameters::lines(startlinevalue, stoplinevalue, highDensity);

    //积累次数
    int packetSize = MAXCOLORFRAMES;
    int colorImageMode = sonoParameters->pIV(BFPNames::ColorImageModeStr);
    if (colorImageMode == (int)Color_CF)
    {
        QString packetSizeItem;

        if (sonoParameters->pBV(BFPNames::TDIEnStr))
        {
            packetSizeItem = BFPNames::PacketSizeTDIStr;
        }
        else if (sonoParameters->pBV(BFPNames::MVIModeStr))
        {
            packetSizeItem = BFPNames::PacketSizeMVIStr;
        }
        else
        {
            packetSizeItem = BFPNames::PacketSizeStr;
        }

        packetSize = sonoParameters->pIV(packetSizeItem);
    }
    else if (colorImageMode == (int)Color_PD)
    {
        if (sonoParameters->pBV(BFPNames::SonoNeedleStr))
        {
            packetSize = sonoParameters->pIV(BFPNames::PacketSizeSNStr);
        }
        else
        {
            packetSize = sonoParameters->pIV(BFPNames::PacketSizePDStr);
        }
    }

    m_PacketSize = packetSize;
    m_Startline = startlinevalue;
    m_Linesize = usblineSize;
#ifdef USE_HIGHDENSITY_INT
    m_Stopline = stoplinevalue;
    m_Linegap = 1;
    m_Linenum = m_RealPackNum = linenumvalue;
    m_FourBeamLineNum = m_Linenum;
//    m_RealPackNum = m_Highdensity ? linenumvalue + 1 : linenumvalue;
#else
    m_Highdensity = highDensity;
    m_Stopline = stoplinevalue + 2;
    m_Linegap = m_Highdensity ? 1 : 2;
    m_Linenum = linenumvalue + 1; //高低密度多发一根线
    //开启四波束和Overlap
    m_IsCOverLap = sonoParameters->pIV(BFPNames::MBColorStr) == 2 && sonoParameters->pBV(BFPNames::COverlapStr);
    if (m_IsCOverLap)
    {
        m_FourBeamLineNum = calculateColorModeQuadBeamLines(sonoParameters, startlinevalue, stoplinevalue);
        m_Linegap = 1;
    }
    else
    {
        m_FourBeamLineNum = m_Linenum;
    }
    m_RealPackNum = m_Highdensity ? linenumvalue + 1 : linenumvalue;
#endif
    m_ColorPointNum = sonoParameters->pIV(BFPNames::ColorPointNumAfterReduceStr);
    m_Pointsize = sonoParameters->pIV(BFPNames::PointNumPerLineStr);
    m_Topbordercolor = sonoParameters->pIV(BFPNames::TopBorderColorStr);
    m_Bottombordercolor = sonoParameters->pIV(BFPNames::BottomBorderColorStr);
    m_Colorpointsize = (GeneralInfo::instance().m_hardwareVersion == LineData::WIFI) ? m_Pointsize / 2 : m_Pointsize;
    m_Assemblecolorbatchsize = m_FourBeamLineNum * 2 * m_PacketSize;
    // qDebug() << PRETTY_FUNCTION
    //          << "m_Linenum:" << m_Linenum
    //          << "m_PacketSize:" << m_PacketSize
    //          << "m_Assemblecolorbatchsize:" << m_Assemblecolorbatchsize;

    // pipline
    m_ParaController->sendPara(PREPARANAME(ProcessSonoParameterIndex::BMSecondSampDatNum), m_Colorpointsize, Dsc_Int);
    m_ParaController->sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECOriLineNum), m_Linenum, Dsc_Int);
    m_ParaController->sendPara(PREPARANAME(ProcessSonoParameterIndex::ColorLineNumAfterBeamforming), m_FourBeamLineNum,
                               Dsc_Int);
    m_ParaController->sendPara(PREPARANAME(ProcessSonoParameterIndex::PREC4BeamOverlapEn), m_IsCOverLap, Dsc_Int);
    m_ParaController->sendPara(PREPARANAME(ProcessSonoParameterIndex::ColorPointNumAfterReduce), m_ColorPointNum,
                               Dsc_Int);
    m_ParaController->sendPara(PREPARANAME(ProcessSonoParameterIndex::PREC4BeamWTCoe),
                               sonoParameters->pDV(BFPNames::ColorOverlapWeightStr), Dsc_Int);
}

int PreProcessBufManager4color::caculatecolorlinecount(int cacheid)
{
    int linecolorcount = 0;
    for (int j = 0; j < m_PacketSize; j++)
    {
        for (size_t k = 0; k < 2; k++)
        {
            linecolorcount += m_Controlbuffers[cacheid].m_ColorUnitAssembled[j][k].m_LineCount;
        }
    }
    return linecolorcount;
}

int PreProcessBufManager4color::findiqbufcached()
{
    QMutexLocker locker(&m_LineInfoMutex);
    int iqcached = -1;
    for (int i = 0; i < m_Cachesize; i++)
    {
        if (m_Controlbuffers[i].m_Iqbuffer)
            iqcached = i;
    }
    Q_ASSERT(iqcached != -1);
    return iqcached;
}

void PreProcessBufManager4color::resetexcludecached(int cacheid, int fidx)
{
    int z = 0;
    for (int i = 0; i < m_Cachesize; i++)
    {
        if (i == cacheid)
            continue;

        m_Controlbuffers[i].m_FrameIndex = fidx + z;
        m_Controlbuffers[i].m_Iqbuffer = false;
        for (int j = 0; j < MAXCOLORFRAMES; j++)
        {
            for (int k = 0; k < 2; k++) // I Q data initialize
            {
                m_Controlbuffers[i].m_ColorUnitAssembled[j][k].m_LineCount = 0;
                m_Controlbuffers[i].m_ColorUnitAssembled[j][k].m_FrameIndex = fidx + z;
            }
        }
        z++;
    }
}

void PreProcessBufManager4color::debugprintbufinformaton()
{
    //    for (int i = 0; i < m_Cachesize; i++)
    //    {
    //        tcout << "cacheid:" << i << " frameidx:" << m_Controlbuffers[i].__frameindex << " flag:" <<
    //        m_Controlbuffers[i].__iqbuffer;
    //    }
    //    tcout << "" << std::endl;;
}

int PreProcessBufManager4color::calculateColorModeQuadBeamLines(SonoParameters* sonoParameters, const int startLine,
                                                                const int stopLine)
{
    int totalLines = 0;
    if (sonoParameters->pBV(BFPNames::ColorLineDensityStr))
    {
        totalLines = 2 * (stopLine - startLine);
    }
    else
    {
        totalLines = stopLine - startLine;
    }
    return totalLines;
}
