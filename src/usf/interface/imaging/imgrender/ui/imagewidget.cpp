#include "imagewidget.h"
#include "ibfkitfactory.h"
#include "imageeventargs.h"
#include "imagerender.h"
#include "setting.h"
#include <QGraphicsView>
#include <QLineEdit>
#include <QPainter>
#include <QtGlobal>
#include "applogger.h"
#include "bfpnames.h"
#include "bfrawdata2showdatabase.h"
#include "elastoinfohandler.h"
#include "imagestablecontroller.h"
#include "painterrenderimagetile.h"
#include "parameter.h"
#include "pimplglyphswidget.h"
#include "sonoparameters.h"
#include "util.h"
#include <QGLWidget>
#include <QResizeEvent>
#include <QScrollBar>

LOG4QT_DECLARE_STATIC_LOGGER(log, ImageWidget)
ImageWidget::ImageWidget(QWidget* parent)
    : IImageWidget(parent)
    //    , m_ImageDataTransfer(NULL)
    , m_ImageStableController(NULL)
    , m_zoomFactor(1.0)
    , m_isEraseImage(false)
{
    setMouseTracking(true);
    m_ImageTile = new PainterRenderImageTile(new QGraphicsScene(), this);
    m_GraphicsView = m_ImageTile;
    m_GraphicsView->setRenderHints(QPainter::Antialiasing | QPainter::SmoothPixmapTransform |
                                   QPainter::Qt4CompatiblePainting);

    connect(m_ImageTile, SIGNAL(refresh()), this, SLOT(update()));
    // TODO:目前垂直方向深度标尺在OMAP平台上会出现某几段不刷新的问题，目前只能通过此种方法解决
//    connect(m_GraphicsView->scene(), SIGNAL(changed(QList<QRectF>)), m_GraphicsView ,SLOT(update()));
#ifdef USE_GRAPHICS_DEBUG
    connect(m_GraphicsView->scene(), SIGNAL(changed(QList<QRectF>)), &m_ImageTile->pimplGlyphsWidget().overlay(),
            SLOT(updateAllSceneBoundingRect()));
#endif
    //图元更新时，降低图像的刷新速度
    connect(m_GraphicsView->scene(), SIGNAL(changed(QList<QRectF>)), m_ImageTile, SLOT(reduceRenderRate()));
    // 放大因子由内部提供，必须显示同步，采取信号链接改变overlay的litmitRect
    connect(m_ImageTile, SIGNAL(zoomFactorChanged(qreal)), this, SLOT(onLimitRectZoomChanged(qreal)));
    m_IfConnectSignals = false;

    m_ImageTile->pimplGlyphsWidget().overlay().setSize(size());

    //    m_GraphicsView->setRenderHint(QPainter::Antialiasing, true);
    m_GraphicsView->move(QPoint(0, 0));
    m_GraphicsView->setFrameStyle(QFrame::NoFrame);
    // added by Jin Yuqi
    m_GraphicsView->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_GraphicsView->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    //
    // http://liveblue.wordpress.com/2010/12/13/qt-tip-trick-masking-widgets/
    m_GraphicsView->setBackgroundRole(QPalette::Window);
    QPalette p = m_GraphicsView->palette();
    p.setColor(QPalette::Window, Qt::transparent);
    m_GraphicsView->setPalette(p);

    // 2025-06-13 Write by AlexWang 减少图像区域移动鼠标时对图像帧频的影响
    setAttribute(Qt::WA_OpaquePaintEvent);
    setAttribute(Qt::WA_NoSystemBackground);
    setAttribute(Qt::WA_StaticContents);
}

ImageWidget::~ImageWidget()
{
}

ImageTile* ImageWidget::imageTile() const
{
    return m_ImageTile;
}

ImageStableController* ImageWidget::imageStableController() const
{
    return m_ImageStableController;
}

void ImageWidget::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_ImageTile->setColorMapManager(colorMapManager);
}

void ImageWidget::initImageTile(IGlyphsControlCreator* glyphsControl)
{
    m_ImageTile->initImageTile(glyphsControl);
}

// ImageDataTransfer *ImageWidget::imageDataTransfer() const
//{
//    return m_ImageDataTransfer;
//}

bool ImageWidget::isZoomedIn() const
{
    return m_ImageTile->isZoomedIn();
}

bool ImageWidget::zoomIn()
{
    return m_ImageTile->zoomIn();
}

bool ImageWidget::zoomOut()
{
    return m_ImageTile->zoomOut();
}

bool ImageWidget::isZoomInEnabled() const
{
    return m_ImageTile->isZoomInEnabled();
}

bool ImageWidget::isColorRender() const
{
    return m_ImageTile->isColorRender();
}

void ImageWidget::setColorRender(bool value)
{
    m_ImageTile->setColorRender(value);
}

QGraphicsView& ImageWidget::graphicsView()
{
    return *m_GraphicsView;
}

void ImageWidget::moveToCustomerPos()
{
    emit resetPos();
}

bool ImageWidget::isShowFreeHand3DRoi() const
{
    return m_ShowFreeHand3DRoi;
}

void ImageWidget::setShowFreeHand3DRoi(bool value)
{
    m_ShowFreeHand3DRoi = value;
}

void ImageWidget::onNewImage(ImageEventArgs* data)
{
    m_isEraseImage = false;
    m_ImageTile->onNewImage(data);
}

void ImageWidget::onNewPictureImage(QImage image)
{
    m_isEraseImage = false;
    m_ImageTile->onNewPictureImage(image);
}

void ImageWidget::mouseDoubleClickEvent(QMouseEvent* e)
{
    m_ImageTile->mouseDoubleClickEvent(e);
    IImageWidget::mouseDoubleClickEvent(e);
}

void ImageWidget::mousePressEvent(QMouseEvent* e)
{
    m_ImageTile->mousePressEvent(e);
    IImageWidget::mousePressEvent(e);
}

void ImageWidget::mouseMoveEvent(QMouseEvent* e)
{
    m_ImageTile->mouseMoveEvent(e);
    IImageWidget::mouseMoveEvent(e);
}

void ImageWidget::mouseReleaseEvent(QMouseEvent* e)
{
    m_ImageTile->mouseReleaseEvent(e);
    IImageWidget::mouseReleaseEvent(e);
    if (m_ShowFreeHand3DRoi && e->button() == Qt::RightButton)
    {
        emit rightButtonReleased();
    }
}

void ImageWidget::paintEvent(QPaintEvent* e)
{
    QPainter painter(this);
    if (m_isEraseImage)
    {
        log()->info() << PRETTY_FUNCTION << "fill black rect";
        painter.fillRect(QRectF(0, 0, size().width(), size().height()), Qt::black);
    }
    else
    {
        m_ImageTile->draw(&painter);
    }
    IImageWidget::paintEvent(e);
}

void ImageWidget::onSetBeamFormer()
{
    IBFKitFactory* bfKitFactory = m_BeamFormer->createBFKitFactory();
    IBFRawData2ShowData* bfRawData2ShowData = bfKitFactory->createBFRawData2ShowData();
    m_ImageTile->setBFRawData2ShowData(bfRawData2ShowData);
    delete bfKitFactory;

    m_ImageTile->setRect(QRect(0, 0, m_BeamFormer->imageWidth(), m_BeamFormer->imageHeight()));
    m_ImageTile->setImageSize(m_BeamFormer->imageSize());
    m_ImageTile->setBeamFormerInitialization(m_BeamFormer);
    m_ImageTile->setRealTimeSonoParameters(m_BeamFormer->sonoParameters());
    m_ImageTile->setBeamFormer(m_BeamFormer);

    connect(m_BeamFormer, SIGNAL(startSendingData()), m_ImageTile, SLOT(reduceRenderRate()));

    QObject* object = dynamic_cast<QObject*>(bfRawData2ShowData);
    if (object != NULL)
    {
        connect(object, SIGNAL(imagePixelBitsChanged(int)), m_BeamFormer, SIGNAL(imagePixelBitsChanged(int)));
    }

    //    if(m_ImageDataTransfer == NULL)
    //    {
    //        m_ImageDataTransfer = new ImageDataTransfer(m_BeamFormer, this);
    //        m_ImageDataTransfer->setImageTile(m_ImageTile);
    ////        connect(m_ImageDataTransfer, SIGNAL(newImage(ImageEventArgs*)), this,
    /// SLOT(onNewImage(ImageEventArgs*)));

    //        connect(m_ImageTile, SIGNAL(colorBarChanged(ColorBarModel*)), m_ImageDataTransfer,
    //        SLOT(refreshCurrentFrame())); connect(m_ImageTile, SIGNAL(colorBarGroupChanged(QList<ColorBarModel*>)),
    //        m_ImageDataTransfer, SLOT(refreshCurrentFrame()));
    //    }
    if (m_ImageStableController == NULL)
    {
        m_ImageStableController = new ImageStableController(m_BeamFormer, this);
        m_ImageStableController->setImageTile(m_ImageTile);
    }
}

void ImageWidget::onSetSonoParameters()
{
    // create bufferManager in m_ImageTile->setSonoParameters
    m_ImageTile->setSonoParameters(m_SonoParameters);
    //    if(m_ImageDataTransfer != NULL && m_IsFirstSetSonoParameters)
    //    {
    //        m_ImageDataTransfer->setBufferManager(m_ImageTile->bufferManager());
    //    }

    // BM模式时，调节深度，hotlog和深度值出现残影，目前暂时通过调节深度时，重刷graphicsview解决
    //线阵steer很大，调节angle时，会出现残影
    QStringList refreshedparas = QStringList()
                                 << BFPNames::CQYZStr << BFPNames::DopplerThetaStr << BFPNames::DopplerThetaCWDStr
                                 << BFPNames::DopplerThetaTDIStr << BFPNames::SystemScanModeStr;
    foreach (const QString& name, refreshedparas)
    {
        connect(m_SonoParameters->parameter(name), SIGNAL(valueChanged(QVariant)), m_GraphicsView, SLOT(update()));
    }

    connect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), &Parameter::valueChanged, this,
            &ImageWidget::dopplerScanLineVisibleChanged);

    connect(m_SonoParameters->parameter(BFPNames::DopplerThetaStr), &Parameter::valueChanged, this,
            &ImageWidget::dopplerThetaStrChanged);
}

void ImageWidget::showEvent(QShowEvent*)
{
    m_ImageTile->onParentShow();
    m_ImageTile->pimplGlyphsWidget().overlay().setSize(size() / zoomFactor());
}

void ImageWidget::resizeEvent(QResizeEvent* event)
{
    IImageWidget::resizeEvent(event);
    m_GraphicsView->resize(size());

    /******************note:
     * 此函数触发时放大系数并没有完成更新操作，故此处需要根据界面尺寸算出放大系数******************/
    qreal zoomFactor = 1.0f;
    QSize originSize = ModelConfig::instance().value(ModelConfig::RenderWidgetSize, QSize(640, 512)).toSize();
    if (size() == originSize)
    {
        zoomFactor = 1.0f;
    }
    else
    {
        zoomFactor = size().height() / (qreal)originSize.height();
    }
    /**************************************************END***************************************************/

    m_ImageTile->pimplGlyphsWidget().overlay().setSize(size() / zoomFactor);

    resetGraphicsViewScrollBar();
}

void ImageWidget::retranslateUi()
{
    imageTile()->pimplGlyphsWidget().overlay().retranslateGraphicsItem();
}

void ImageWidget::onLimitRectZoomChanged(qreal value)
{
    setZoomFactor(value);
}

void ImageWidget::onEraseImage()
{
    m_isEraseImage = true;
    m_ImageTile->removeAllData();
    update();
    Util::artisticSleep(5);
}

void ImageWidget::setZoomFactor(qreal value)
{
    m_zoomFactor = value;
    m_ImageTile->pimplGlyphsWidget().overlay().setSize(size() / zoomFactor());
    emit zoomFactorChanged(value);
}

qreal ImageWidget::zoomFactor() const
{
    return m_zoomFactor;
}

SonoParameters* ImageWidget::sonoParameters() const
{
    return m_ImageTile->sonoParameters();
}

void ImageWidget::resetGraphicsViewScrollBar()
{
    m_GraphicsView->horizontalScrollBar()->setValue(0);
    m_GraphicsView->verticalScrollBar()->setValue(0);
}
