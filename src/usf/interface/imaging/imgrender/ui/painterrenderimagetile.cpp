#include "painterrenderimagetile.h"

#include <stdint.h>

#include "bfpnames.h"
#include "bwidget.h"
#include "colormapassembly.h"
#include "ecgglyphrender.h"
#include "ecgrender.h"
#include "ecgwidget.h"
#include "elastoinfohandler.h"
#include "ewidget.h"
#include "fpscalculator.h"
#include "ibeamformer.h"
#include "icursormouseaction.h"
#include "ilinebuffer.h"
#include "imageglrender.h"
#include "imagepainterrender.h"
#include "imagerender.h"
#include "imagewidget.h"
#include "logger.h"
#include "modelconfig.h"
#include "pimplglyphswidget.h"
#include "probedataset.h"
#include "setting.h"
#include "sonoparameters.h"
#include "straincurvewidget.h"
#include "variantutil.h"
#include <QApplication>
#include <QDebug>
#include <QGraphicsView>
#include <QPainter>
#include <QtOpenGL>

LOG4QT_DECLARE_STATIC_LOGGER(log, PainterRenderImageTile)

PainterRenderImageTile::PainterRenderImageTile(QGraphicsScene* scene, QWidget* parent)
    : ImageTile(scene, parent)
    , m_FpsCalculator(new FpsCalculator())
    , m_IsPaused(false)
    , m_PrepareExitFreeHand3D(false)
    , m_ElastoInfoHandler(NULL)
    , m_RawDataModel(NULL)
    , m_SystemScanModeChanged(true)
{
    m_FpsCalculator->setLogOut(true);
    m_FpsCalculator->setAdditionInfo("Render");
    connect(m_FpsCalculator, SIGNAL(update(const QString&, float)), this, SLOT(onFpsUpdate(const QString&, float)));

    //        m_Renders[ImageEventArgs::ImageB] = new ImageGLRender(this);
    //        m_Renders[ImageEventArgs::ImageECG] = new EcgRender(this);
    QGLContext* context = NULL;
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    context = new QGLContext(QGLFormat());
#endif
    EcgGlyphRender* ecgRender = new EcgGlyphRender(this, context);
    ecgRender->setOverLay(&m_ModeGlyphsWidget->overlay());
    ecgRender->setEndLength(10.0f);
    m_Renders[ImageEventArgs::ImageECG] = ecgRender;

    foreach (GLRender* render, m_Renders)
    {
        if (render != NULL)
        {
            render->init();
        }
    }
    m_Render = new ImagePainterRender(this);
    connect(m_Render, &ImagePainterRender::sonoZoomDataUpdate, this, &ImageTile::sonoZoomDataUpdate);
    connect(m_Render, &ImagePainterRender::clearMeasureContextImage, this, &ImageTile::clearMeasureContextImage,
            Qt::QueuedConnection);
    connect(m_Render, &ImagePainterRender::renderImageChanged, this, &ImageTile::renderImageChanged,
            Qt::DirectConnection);
    m_Render->init();

    m_ZoomRenderWidgetSize = m_RawRenderWidgetSize =
        ModelConfig::instance().value(ModelConfig::RenderWidgetSize, QSize(640, 512)).toSize();
}

PainterRenderImageTile::~PainterRenderImageTile()
{
    delete m_FpsCalculator;
    if (m_RawDataModel != NULL)
    {
        m_RawDataModel->data()->m_Data = NULL;
        delete m_RawDataModel;
        m_RawDataModel = NULL;
    }
}

QImage PainterRenderImageTile::currentImage(ImageEventArgs::ImageType type) const
{
    //    m_ImageRender->rawData2ImageData(m_RawData->imageData(), m_RawData->imageSize(), m_ImageData,
    //    m_RawData->imageSize() * 4);
    switch (type)
    {
    case ImageEventArgs::WholeImage:
    {
        if (NULL != m_RawData && !m_RawData->isNull())
        {
            return QImage(m_RawData->imageData(), m_RawData->width(), m_RawData->height(), QImage::Format_RGB32);
            // QImage image(m_Rect.size());
            // geneCurrentImage(image);
            // return image;
        }
    }
    break;
    case ImageEventArgs::ImageB:
    case ImageEventArgs::ImageC:
    case ImageEventArgs::ImageD:
    case ImageEventArgs::ImageM:
    {
        if (m_Render != NULL)
        {
            return m_Render->imageData(
                type, type == ImageEventArgs::ImageB ? m_SonoParameters->pIV(BFPNames::ActiveBStr) : 0);
        }
    }
    break;
    }

    return QImage(m_Rect.size(), QImage::Format_RGB32);
}

bool PainterRenderImageTile::isZoomedIn() const
{
    return m_ZoomRenderWidgetSize != m_RawRenderWidgetSize;
}

bool PainterRenderImageTile::zoomIn()
{
    m_SonoParameters->setPV(BFPNames::IsFullScreenZoomInStr, true);

    qreal height = 0;
    qreal width = 0;
    if (!m_SonoParameters->pBV(BFPNames::IsSecondGearFullScreenZoomInStr))
    {
        height = m_ZoomInHeights.at(m_SonoParameters->pIV(BFPNames::FullScreenZoomInIndexStr));
        width = m_RawRenderWidgetSize.width() * (height / m_RawRenderWidgetSize.height());
    }
    else
    {
        height = m_ZoomInHeights.at(m_SonoParameters->pIV(BFPNames::FullScreenZoomInIndexStr) + 1);
        width = m_RawRenderWidgetSize.width() * (height / m_RawRenderWidgetSize.height());
    }

    m_ratiofirstGear =
        static_cast<double>(m_ZoomInHeights.at(m_SonoParameters->pIV(BFPNames::FullScreenZoomInIndexStr))) /
        ModelConfig::instance().value(ModelConfig::RenderWidgetSize, QSize(640, 512)).toSize().height();
    m_ratioSecondGear =
        static_cast<double>(m_ZoomInHeights.at(m_SonoParameters->pIV(BFPNames::FullScreenZoomInIndexStr) + 1)) /
        ModelConfig::instance().value(ModelConfig::RenderWidgetSize, QSize(640, 512)).toSize().height();

    return changeSize(width, height);
}

bool PainterRenderImageTile::zoomOut()
{
    // 防止放大之后回到未放大模式下，commentGlyphControl无法捕获
    // 当前焦点，不能输入的问题 by Jin Yuqi
    QWidget* w = m_ParentWidget->findChild<QGraphicsView*>();
    w->setFocus();

    bool ret = changeSize(m_RawRenderWidgetSize.width(), m_RawRenderWidgetSize.height());
    m_SonoParameters->setPV(BFPNames::IsFullScreenZoomInStr, false);
    m_SonoParameters->setPV(BFPNames::IsSecondGearFullScreenZoomInStr, false);
    update();
    return ret;
}

bool PainterRenderImageTile::isZoomInEnabled() const
{
    return true;
}

bool PainterRenderImageTile::isColorRender() const
{
    return false;
}

bool PainterRenderImageTile::isNeedPauseSleep() const
{
    return !m_RealTimeSonoParameters->pBV(BFPNames::FreezeStr) && isZoomedIn();
}

void PainterRenderImageTile::drawBackground(QPainter* painter, const QRectF& rect)
{
    if (m_Renders.isEmpty())
    {
        return;
    }

    //    painter->beginNativePainting();
    //    foreach(GLRender* glRender, m_Renders)
    //    {
    //        if(glRender != NULL)
    //        {
    //            glRender->render();
    //        }
    //    }
    //    painter->endNativePainting();
}

void PainterRenderImageTile::setBufferManager(ILineBuffer* bufferManager)
{
    ImageTile::setBufferManager(bufferManager);

    foreach (GLRender* glRender, m_Renders)
    {
        if (glRender != NULL)
        {
            glRender->setBufferManager(bufferManager);
        }
    }
    if (m_Render != NULL)
    {
        m_Render->setBufferManager(bufferManager);
    }
    onActiveBChanged(this->pIV(BFPNames::ActiveBStr));

    if (m_ElastoInfoHandler != NULL)
    {
        m_ElastoInfoHandler->setBufferManager(bufferManager);
        connect(bufferManager, SIGNAL(currentIndexChanged(int, int, int)), this,
                SLOT(onBufferManagerCurrentIndexChanged(int)));
        connect(bufferManager, SIGNAL(currentIndexChanged(int, int, int)), this,
                SLOT(onBufferCurrentStrainIndexChanged(int)));
    }
    connect(m_LineBufferManager, SIGNAL(beforeSonoParametersChanged(SonoParameters*, SonoParameters*)), this,
            SLOT(onBeforeSonoParametersChanged(SonoParameters*, SonoParameters*)), Qt::DirectConnection);
    connect(m_LineBufferManager, SIGNAL(sonoParametersChanged(SonoParameters*)), this,
            SLOT(onSonoParametersChanged(SonoParameters*)), Qt::DirectConnection);
}

void PainterRenderImageTile::setBeamFormer(IBeamFormer* beamformer)
{
    if (NULL == m_ElastoInfoHandler)
    {
        m_ElastoInfoHandler = new ElastoInfoHandler(beamformer, this);
        connect(beamformer, SIGNAL(imageShapeStable()), this, SLOT(onImageShapeStable()));
        connect(beamformer, SIGNAL(imageStable()), this, SLOT(onImageShapeStable()));
    }
}

void PainterRenderImageTile::removeAllData()
{
    if (pBV(BFPNames::ECGEnStr))
    {
        GLRender* render = glRender(ImageEventArgs::ImageECG);
        if (render != NULL)
        {
            render->reset();
        }
    }
    m_Render->clearImageDataCache();
}

QImage PainterRenderImageTile::getSetIndexBImage(int index)
{
    if (m_Render != NULL)
    {
        return m_Render->imageData(ImageEventArgs::ImageB, index);
    }
    return QImage();
}

int PainterRenderImageTile::paintFrameIndex()
{
    return m_Render->paintFrameIndex();
}

int PainterRenderImageTile::imageBufferFrameIndex()
{
    return m_Render->imageBufferFrameIndex();
}

bool PainterRenderImageTile::isPause()
{
    return m_IsPaused;
}

void PainterRenderImageTile::updateImage()
{
    if (m_IsWholeImageMode && NULL != m_RawData && !m_RawData->isNull())
    {
        m_Image = QImage(m_RawData->imageData(), m_RawData->width(), m_RawData->height(), QImage::Format_RGB32);
    }
    //    else
    //    {
    //        scene()->invalidate(QRectF(0,0,width()/2.0f,height()/2.0f), QGraphicsScene::BackgroundLayer);
    //    }
    update();
}

void PainterRenderImageTile::setImageData(ImageEventArgs* data)
{
    GLRender* render = NULL;
    switch (data->imageType())
    {
    case ImageEventArgs::ImageECG:
    {
        render = glRender(ImageEventArgs::ImageECG);
        if (render != NULL)
        {
            render->setData(data);
        }
    }
    break;
    default:
    {
        //        render = glRender(ImageEventArgs::ImageB);
        //        if(render != NULL)
        //        {
        //            render->setData(data);
        //        }
        if (m_Render != NULL)
        {
            m_Render->setData(data);
        }
    }
    break;
    }
}

void PainterRenderImageTile::resizeEvent(QResizeEvent* event)
{
    scene()->invalidate(QRectF(0, 0, width(), height()), QGraphicsScene::BackgroundLayer);
    QGraphicsView::resizeEvent(event);
}

void PainterRenderImageTile::wheelEvent(QWheelEvent* event)
{
}

void PainterRenderImageTile::updateElastoStrains(ImageEventArgs* data)
{
    if (NULL == m_ElastoInfoHandler)
    {
        return;
    }

    if (pBV(BFPNames::ElastoEnStr))
    {
        m_ElastoInfoHandler->handleInfo(data);
    }
}

GLRender* PainterRenderImageTile::glRender(ImageEventArgs::ImageType type)
{
    if (m_Renders.contains(type))
    {
        return m_Renders[type];
    }

    return NULL;
}

void PainterRenderImageTile::onNewImage(ImageEventArgs* data)
{
    if (m_isPauseWhileFreezeAndChanging)
    {
        return;
    }

    setRawData(data);

    //    bool needProcessChorma = data->bitCount() == 8; //
    //    因为伪彩是zeus做的，所以bitcount都为32位；如果图像数据的bitcount为8则说明需要做伪彩映射（比如宽景）
    bool isECGON = (data->imageType() == ImageEventArgs::ImageECG);
    bool needProcessChorma = data->bitCount() == 8;
    if ((!m_IsWholeImageMode && !needProcessChorma) || isECGON) // Code Review 增加条件判断括号
    {
        setImageData(data);
    }

    if (m_ImageData == NULL)
    {
        m_ImageDataSize = m_RawData->imageSize();
        m_ImageData = reinterpret_cast<uchar*>(new uint32_t[m_RawData->imageSize()]);
        memset(m_ImageData, 0, m_RawData->imageSize() * sizeof(uint32_t));
        m_ImageSize = QSize(m_RawData->width(), m_RawData->height());
        //        m_dstImageSize = QSize(m_RawData->width(), m_RawData->height());
    }

    if (!m_IsPaused)
    {
        updateElastoStrains(data);
        updateImage();
    }

    if (needProcessChorma && !isECGON)
    {
        processChorma(data);
    }
}

void PainterRenderImageTile::onNewPictureImage(QImage image)
{
    // TODO!!!!!!! easyview load
    if (m_isPauseWhileFreezeAndChanging)
    {
        return;
    }

    if (m_Render != NULL)
    {
        m_Render->setPictureImage(image);
    }
}

void PainterRenderImageTile::setPause(bool value)
{
    m_IsPaused = value;
    if (m_RealTimeSonoParameters->pBV(BFPNames::FreezeStr) && !value && m_RawData != NULL)
    {
        //结束暂停时，让线程运行，再冻结状态时，结束暂停时可能需要刷新图像
        updateImage();
    }
}

void PainterRenderImageTile::checkResetRender(const bool forceReset)
{
    SystemScanMode newScanmode = (SystemScanMode)pIV(BFPNames::SystemScanModeStr);
    bool needReset = checkNeedResetRender(m_SystemScanMode, newScanmode);
    if (needReset || forceReset)
    {
        resetRender();
    }
    m_SystemScanMode = newScanmode;
}

void PainterRenderImageTile::onBeforeSonoParametersChanged(SonoParameters* oldSonoParameter,
                                                           SonoParameters* newSonoParameter)
{
    Q_UNUSED(oldSonoParameter);
    if (newSonoParameter == NULL)
    {
        return;
    }
    qDebug() << PRETTY_FUNCTION << "oldSonoParameter:" << (quintptr)oldSonoParameter
             << "newSonoParameter:" << (quintptr)newSonoParameter << "m_SonoParameters:" << (quintptr)m_SonoParameters;
    disconnectTransformChanged(oldSonoParameter);
    if (pIV(BFPNames::LayoutStr) == Layout_1x1)
    {
        m_ModeGlyphsWidget->setSonoParameters(newSonoParameter);
    }
    changedImageRenderSonoParameters(newSonoParameter);
    connectTransformChanged(newSonoParameter);
}

void PainterRenderImageTile::onSonoParametersChanged(SonoParameters* newSonoParameter)
{
    if (newSonoParameter == NULL)
    {
        return;
    }
    if (pIV(BFPNames::LayoutStr) == Layout_1x1)
    {
        m_ModeGlyphsWidget->setSonoParameters(newSonoParameter);
    }
    else
    {
        m_ModeGlyphsWidget->setSonoParameters(m_SonoParameters);
    }
}

void PainterRenderImageTile::onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    if ((SystemScanMode)(oldValue.toInt()) == SystemScanModeFreeHand3D &&
        (SystemScanMode)(newValue.toInt()) == SystemScanModeB)
    {
        m_PrepareExitFreeHand3D = true;
    }
}

void PainterRenderImageTile::onSystemModeChanged()
{
    if (m_FpsCalculator != NULL)
    {
        m_FpsCalculator->reset();
        emit fpsChanged(m_FpsCalculator->additionInfo(), 0);
    }
    if (m_Render != NULL && isMultiLayout())
    {
        m_Render->changeActiveModel(pIV(BFPNames::ActiveBStr));
        return;
    }
    if (SystemScanModeFreeHand3D == m_SonoParameters->pIV(BFPNames::SystemScanModeStr) ||
        (m_PrepareExitFreeHand3D && SystemScanModeB == m_SonoParameters->pIV(BFPNames::SystemScanModeStr)))
    {
        m_PrepareExitFreeHand3D = false;
        return;
    }
    checkResetRender(true);
    m_SystemScanModeChanged = true;
}

void PainterRenderImageTile::onBeforeActiveBChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (NULL == m_LineBufferManager)
    {
        return;
    }

    SonoParameters* oldActiveSonoParameters = m_LineBufferManager->getSonoParametersByLayoutIndex(oldValue.toInt());
    disconnectTransformChanged(oldActiveSonoParameters);

    if (pBV(BFPNames::ECGEnStr) && m_SonoParameters->isRealTime())
    {
        EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
        if (ecgRender != NULL)
        {
            ecgRender->setIsRealTime(oldValue.toInt(), false);
            ecgRender->setIsRealTime(newValue.toInt(), true);
        }
    }
}

void PainterRenderImageTile::onActiveBChanged(const QVariant& value)
{
    if (NULL == m_LineBufferManager)
    {
        return;
    }

    SonoParameters* newActiveSonoParameters = m_LineBufferManager->getSonoParametersByLayoutIndex(value.toInt());
    connectTransformChanged(newActiveSonoParameters);
    onTransformChanged();
    onECGPosChanged(pIV(BFPNames::ECGPosStr));

    if (pIV(BFPNames::LayoutStr) == Layout_1x1)
    {
        qDebug() << PRETTY_FUNCTION << "value:" << value.toInt()
                 << "newActiveSonoParameters:" << (quintptr)newActiveSonoParameters
                 << "ActiveB:" << pIV(BFPNames::ActiveBStr);

        m_ModeGlyphsWidget->setSonoParameters(newActiveSonoParameters);
    }
    if (pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime())
    {
        changedImageRenderSonoParameters(newActiveSonoParameters);
    }
    else if (m_Render != NULL && isMultiLayout())
    {
        m_Render->changeActiveModel(pIV(BFPNames::ActiveBStr));
    }
}

void PainterRenderImageTile::onLayoutChanged()
{
    if (m_FpsCalculator != NULL)
    {
        m_FpsCalculator->reset();
        emit fpsChanged(m_FpsCalculator->additionInfo(), 0);
    }
    resetRender();
    if (!isMultiLayout())
    {
        onHalfHeightChanging(pV(BFPNames::HalfHeightStr), true);
    }
}

void PainterRenderImageTile::onTransformChanged()
{
    foreach (GLRender* glRender, m_Renders)
    {
        if (NULL != glRender)
        {
            if (!m_IsPaused)
            {
                glRender->setupAllTransform();
                updateImage();
            }
        }
    }
    if (m_Render != NULL)
    {
        m_Render->setupAllTransform();
    }

    updateImage();
}

void PainterRenderImageTile::onBufferCurrentStrainIndexChanged(int value)
{
    if (NULL == m_LineBufferManager || NULL == m_ElastoInfoHandler)
    {
        return;
    }

    int currentIndex = m_LineBufferManager->frameCount() <= 256 ? value : m_ElastoInfoHandler->currentElastoIndex();
    setPV(BFPNames::ElastoStrainIndexStr, currentIndex, true);
}

void PainterRenderImageTile::onBufferManagerCurrentIndexChanged(int value)
{
    EWidget* widget = m_ModeGlyphsWidget->getAs<EWidget>();
    StrainCurveWidget* curveWidget = NULL;
    if (widget != NULL && m_LineBufferManager != NULL && m_ElastoInfoHandler != NULL)
    {
        curveWidget = &widget->second();

        if (curveWidget != NULL)
        {
            int frameCount = m_LineBufferManager->frameCount();

            int currentIndex = frameCount <= 256 ? value : m_ElastoInfoHandler->calcElastoStrainIndex(value);
            //弹性模式下电影帧数是和ElastoStrainList的count是一致的,但是在回调单帧弹性图像时frameCount只有一帧图像,
            //而ElastoStrainList是一个Parameter,count是一个电影的所有帧数,所以使用m_BufferManager->currentIndex()
            //会导致回调的单帧弹性图像使用的ElastoStrain值和标记位置错误,所以加入判断分开设置
            if (frameCount > 1)
            {
                curveWidget->setImageIndex(currentIndex);
            }
            else
            {
                curveWidget->setImageIndex(pIV(BFPNames::ElastoStrainIndexStr));
            }
        }
    }
}

void PainterRenderImageTile::onECGEnChanged(const QVariant& value)
{
    EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
    bool isECGOn = value.toBool();
    if (ecgRender != NULL)
    {
        ecgRender->setActive(isECGOn);
        ecgRender->setWholeImageRect(QRect(QPoint(0, 0), pV(BFPNames::RenderImageSizeStr).toSize()));
        ecgRender->clearData(pIV(BFPNames::ActiveBStr));
        if (isECGOn)
        {
            ecgRender->reorganizeShaders();
            int layout = pIV(BFPNames::LayoutStr);
            int activeIndex = pIV(BFPNames::ActiveBStr);
            for (int i = 0; i < layout; ++i)
            {
                ecgRender->setIsRealTime(
                    i, i != activeIndex ? false : (m_SonoParameters->isRealTime() ? !pBV(BFPNames::FreezeStr) : false));
                SonoParameters* sp = m_LineBufferManager->getSonoParametersByLayoutIndex(i);
                if (sp != NULL)
                {
                    ecgRender->updateECGPosLevel(i, sp->pMax(BFPNames::ECGPosStr) - sp->pIV(BFPNames::ECGPosStr));
                }
            }
            onECGPosChanged(pIV(BFPNames::ECGPosStr));
            onECGInvertChanged(pIV(BFPNames::ECGInvertStr));
            ecgRender->resetEcgData();
            ecgRender->resetProcessData();
        }
    }
}

void PainterRenderImageTile::onECGPosChanged(const QVariant& value)
{
    if (pBV(BFPNames::ECGEnStr))
    {
        EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
        if (ecgRender != NULL)
        {
            ecgRender->updateECGPosLevel(pIV(BFPNames::ActiveBStr), pMax(BFPNames::ECGPosStr) - value.toInt());
        }
    }
}

void PainterRenderImageTile::onECGInvertChanged(const QVariant& value)
{
    if (pBV(BFPNames::ECGEnStr))
    {
        EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
        if (ecgRender != NULL)
        {
            ecgRender->ecgInvertChanged(pIV(BFPNames::ActiveBStr), value.toBool());
        }
    }
}

void PainterRenderImageTile::onFreezeChanged(const QVariant& value)
{
    ImageTile::onFreezeChanged(value);
    SonoParameters* sonoParameters =
        isMultiLayout() || !m_SonoParameters->isRealTime()
            ? m_LineBufferManager->getSonoParametersByLayoutIndex(pIV(BFPNames::ActiveBStr))
            : m_SonoParameters;

    qDebug() << PRETTY_FUNCTION << "sonoParameters:" << (quintptr)sonoParameters;

    bool isFreeze = m_SonoParameters->isRealTime() ? value.toBool() : true;
    if (pBV(BFPNames::ECGEnStr))
    {
        EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
        if (ecgRender != NULL)
        {
            if (!isFreeze)
            {
                int layout = pIV(BFPNames::LayoutStr);
                int activeIndex = pIV(BFPNames::ActiveBStr);
                for (int i = 0; i < layout; ++i)
                {
                    if (i != activeIndex)
                    {
                        ecgRender->reset(activeIndex);
                        ecgRender->setIsRealTime(i, false);
                    }
                    else
                    {
                        ecgRender->setIsRealTime(i, !isFreeze);
                    }
                    SonoParameters* sp = m_LineBufferManager->getSonoParametersByLayoutIndex(i);
                    if (sp != NULL)
                    {
                        ecgRender->updateECGPosLevel(i, sp->pMax(BFPNames::ECGPosStr) - sp->pIV(BFPNames::ECGPosStr));
                    }
                }
            }
        }
    }

    if (!value.toBool() && m_SonoParameters->isRealTime())
    {
        m_ModeGlyphsWidget->setSonoParameters(m_SonoParameters);
        m_Render->setSonoParameters(m_SonoParameters);
        changedImageRenderSonoParameters(m_SonoParameters);
        connectTransformChanged(m_SonoParameters);
    }
}

void PainterRenderImageTile::onImageShapeStable()
{
    EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
    if (ecgRender != NULL)
    {
        if (ecgRender->isActive())
        {
            ecgRender->resetOneEcg(pIV(BFPNames::ActiveBStr));
        }
    }

    if (m_Render != NULL)
    {
        m_Render->setupAllTransform();
    }
}

void PainterRenderImageTile::onProbeIdChanged(const QVariant& value)
{
    //    if(m_Render != NULL)
    //    {
    //        m_Render->setIsFlip(ProbeDataSet::instance().getProbe(value.toInt()).IsPhasedArray);
    //    }
}

void PainterRenderImageTile::onLayoutFormatChanging(const QVariant& value, bool isChanged)
{
    Q_UNUSED(value)
    if (isChanged || m_SystemScanModeChanged)
    {
        checkResetRender(isChanged || m_SystemScanModeChanged);
        m_SystemScanModeChanged = false;
    }
}

void PainterRenderImageTile::onCQYZChanging(const QVariant& value, bool isChanged)
{
    if (!isChanged || NULL == m_Render)
    {
        return;
    }

    m_Render->replaceImageModel(bImageType());
}

void PainterRenderImageTile::onHalfHeightChanging(const QVariant& value, bool isChanged)
{
    ImageModeType imageMode = (ImageModeType)(pIV(BFPNames::ImageModeStr));
    if (!isChanged || (imageMode == MODE_2B || imageMode == MODE_4B))
    {
        return;
    }

    if (value.toBool())
    {
        connect(this->parameter(BFPNames::CQYZStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onCQYZChanging(QVariant, bool)), Qt::UniqueConnection);
    }
    else
    {
        disconnect(this->parameter(BFPNames::CQYZStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onCQYZChanging(QVariant, bool)));
    }
}

void PainterRenderImageTile::onFpsUpdate(const QString& addinfo, float fpsV)
{
    emit fpsChanged(addinfo, fpsV);
}

void PainterRenderImageTile::geneCurrentImage(QImage& image) const
{
    if (NULL == m_Render || image.size().isNull())
    {
        return;
    }

    QPainter painter(&image);
    m_Render->draw(&painter);
}

void PainterRenderImageTile::resetAllEcgPos()
{
    if (NULL == m_SonoParameters || !m_SonoParameters->pBV(BFPNames::ECGEnStr) || NULL == m_LineBufferManager)
    {
        return;
    }

    EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
    if (ecgRender != NULL)
    {
        int layoutCount = m_SonoParameters->pIV(BFPNames::LayoutStr);
        for (int i = 0; i < layoutCount; ++i)
        {
            SonoParameters* sonoParameters = m_LineBufferManager->getSonoParametersByLayoutIndex(i);
            if (sonoParameters != NULL)
            {
                ecgRender->updateECGPosLevel(i, sonoParameters->pMax(BFPNames::ECGPosStr) -
                                                    sonoParameters->pIV(BFPNames::ECGPosStr));
            }
        }
    }
}

void PainterRenderImageTile::resetRender()
{
    m_Render->reorganizeImages();
    if (m_Renders.isEmpty())
    {
        return;
    }

    foreach (GLRender* glRender, m_Renders)
    {
        if (glRender != NULL)
        {
            glRender->reorganizeShaders();
        }
    }

    onECGEnChanged(pV(BFPNames::ECGEnStr));

    if (glRender(ImageEventArgs::ImageECG)->isActive())
    {
        int layoutNum = pIV(BFPNames::ImageLayoutNumStr);
        EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
        if (ecgRender != NULL)
        {
            for (int i = 0; i < layoutNum; i++)
            {
                SonoParameters* sonoParameters = m_LineBufferManager->getSonoParametersByLayoutIndex(i);
                if (sonoParameters != NULL)
                {
                    ecgRender->updateECGPosLevel(i,
                                                 pMax(BFPNames::ECGPosStr) - sonoParameters->pIV(BFPNames::ECGPosStr));
                }
            }
        }
    }
}

bool PainterRenderImageTile::isMultiLayout()
{
    if (NULL == m_SonoParameters)
    {
        return false;
    }

    return m_SonoParameters->pIV(BFPNames::LayoutStr) > Layout_1x1;
}

void PainterRenderImageTile::updateEcgEnd(ImageEventArgs* data)
{
    if (NULL == data || data->isNull() || !pBV(BFPNames::ECGEnStr))
    {
        return;
    }

    int ecgEnd = data->ecgEnd();
    if (ecgEnd > -1)
    {
        EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
        if (ecgRender != NULL)
        {
            if (ecgEnd != ecgRender->ecgEnd(data->index()))
            {
                ecgRender->updateECGEnd(data->index(), ecgEnd);
                ecgRender->render();
            }
        }
    }
}

void PainterRenderImageTile::connectTransformChanged(SonoParameters* sonoParameters)
{
    if (NULL == sonoParameters)
    {
        return;
    }

    QStringList connectParas = QStringList() << BFPNames::UpStr << BFPNames::LeftStr << BFPNames::RotationStr
                                             << BFPNames::ImageZoomCoefStr << BFPNames::ECGInvertStr;

    foreach (const QString& connectPara, connectParas)
    {
        connect(sonoParameters->parameter(connectPara), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onTransformChanged()), Qt::UniqueConnection);
    }
}

void PainterRenderImageTile::disconnectTransformChanged(SonoParameters* sonoParameters)
{
    if (NULL == sonoParameters)
    {
        return;
    }

    QStringList connectParas = QStringList() << BFPNames::UpStr << BFPNames::LeftStr << BFPNames::RotationStr
                                             << BFPNames::ImageZoomCoefStr << BFPNames::ECGInvertStr;

    foreach (const QString& connectPara, connectParas)
    {
        disconnect(sonoParameters->parameter(connectPara), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onTransformChanged()));
    }
}

void PainterRenderImageTile::setRawData(ImageEventArgs* data)
{
    m_RawData = data;
    if (m_RawDataModel != NULL)
    {
        m_RawDataModel->setData(data);
        if (m_RawDataModel->data()->m_Data != m_RawData->imageData())
        {
            m_RawData->setImageData(m_RawDataModel->data()->m_Data);
        }
    }
}

void PainterRenderImageTile::connectParametersSignals()
{
    connect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFreezeChanged(QVariant)));
    if (!m_IsWholeImageMode)
    {
        connect(this->parameter(BFPNames::SystemScanModeStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
                SLOT(onBeforeSystemScanModeChanged(QVariant, QVariant&)), Qt::UniqueConnection);
        connect(this->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSystemModeChanged()), Qt::UniqueConnection);
        connect(this->parameter(BFPNames::BCImagesOnStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onSystemModeChanged()), Qt::UniqueConnection);
        connect(this->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onLayoutChanged()),
                Qt::UniqueConnection);
        connect(this->parameter(BFPNames::ActiveBStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
                SLOT(onBeforeActiveBChanged(QVariant, QVariant&)), Qt::UniqueConnection);
        connect(this->parameter(BFPNames::ActiveBStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onActiveBChanged(QVariant)), Qt::UniqueConnection);
        connect(this->parameter(BFPNames::ECGPosStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onECGPosChanged(QVariant)), Qt::UniqueConnection);
        connect(this->parameter(BFPNames::ECGInvertStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onECGInvertChanged(QVariant)), Qt::UniqueConnection);
        connect(this->parameter(BFPNames::ECGEnStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onECGEnChanged(QVariant)), Qt::UniqueConnection);
        connect(this->parameter(BFPNames::MDisplayFormatStr), SIGNAL(valueChanging(QVariant, bool)), this,
                SLOT(onLayoutFormatChanging(QVariant, bool)));
        connect(this->parameter(BFPNames::DDisplayFormatStr), SIGNAL(valueChanging(QVariant, bool)), this,
                SLOT(onLayoutFormatChanging(QVariant, bool)));
        connect(this->parameter(BFPNames::DTDIDisplayFormatStr), SIGNAL(valueChanging(QVariant, bool)), this,
                SLOT(onLayoutFormatChanging(QVariant, bool)));
        connect(this->parameter(BFPNames::HalfHeightStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onHalfHeightChanging(QVariant, bool)));
    }
}

void PainterRenderImageTile::disconnectParametersSignals()
{
    disconnect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFreezeChanged(QVariant)));
    if (!m_IsWholeImageMode)
    {
        disconnect(this->parameter(BFPNames::SystemScanModeStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
                   SLOT(onBeforeSystemScanModeChanged(QVariant, QVariant&)));
        disconnect(this->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSystemModeChanged()));
        disconnect(this->parameter(BFPNames::BCImagesOnStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onSystemModeChanged()));
        disconnect(this->parameter(BFPNames::LayoutStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onLayoutChanged()));
        disconnect(this->parameter(BFPNames::ActiveBStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
                   SLOT(onBeforeActiveBChanged(QVariant, QVariant&)));
        disconnect(this->parameter(BFPNames::ActiveBStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onActiveBChanged(QVariant)));
        disconnect(this->parameter(BFPNames::ECGPosStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onECGPosChanged(QVariant)));
        disconnect(this->parameter(BFPNames::ECGEnStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onECGEnChanged(QVariant)));
        disconnect(this->parameter(BFPNames::MDisplayFormatStr), SIGNAL(valueChanging(QVariant, bool)), this,
                   SLOT(onLayoutFormatChanging(QVariant, bool)));
        disconnect(this->parameter(BFPNames::DDisplayFormatStr), SIGNAL(valueChanging(QVariant, bool)), this,
                   SLOT(onLayoutFormatChanging(QVariant, bool)));
        disconnect(this->parameter(BFPNames::DTDIDisplayFormatStr), SIGNAL(valueChanging(QVariant, bool)), this,
                   SLOT(onLayoutFormatChanging(QVariant, bool)));
        disconnect(this->parameter(BFPNames::HalfHeightStr), SIGNAL(valueChanged(QVariant, bool)), this,
                   SLOT(onHalfHeightChanging(QVariant, bool)));
    }
}

void PainterRenderImageTile::onSetSonoParameters()
{
    if (m_Render != NULL)
    {
        if (m_SonoParameters->pIV(BFPNames::LayoutStr) > Layout_1x1)
        {
            m_Render->reset();
        }
    }
    if (m_ImageRender != NULL)
    {
        m_ImageRender->setSonoParameters(m_SonoParameters);
    }
    foreach (GLRender* glRender, m_Renders)
    {
        if (glRender != NULL)
        {
            glRender->setSonoParameters(m_SonoParameters);
        }
        onECGEnChanged(pV(BFPNames::ECGEnStr));
        onTransformChanged();
    }
    if (m_Render != NULL)
    {
        m_Render->setSonoParameters(m_SonoParameters);
    }
    if (m_ModeGlyphsWidget != NULL)
    {
        m_ModeGlyphsWidget->setSonoParameters(m_SonoParameters);
    }
    if (!m_SonoParameters->isRealTime())
    {
        onActiveBChanged(pIV(BFPNames::ActiveBStr));
        resetAllEcgPos();
    }

    if (m_ElastoInfoHandler != NULL)
    {
        //        m_ElastoInfoHandler->resetElastoIndex(false);
        if (pIV(BFPNames::ElastoEnStr) && m_LineBufferManager != NULL)
        {
            onBufferManagerCurrentIndexChanged(m_LineBufferManager->currentIndex());
        }
    }
    //    if(!m_IsFirstSetSonoParameters&& !m_RealTimeSonoParameters->pBV(BFPNames::ShowFourDWidgetStr))
    //    {
    //        onFreezeChanged(m_SonoParameters->pV(BFPNames::FreezeStr));
    //    }
}

void PainterRenderImageTile::render(QPainter* painter)
{
    bool isFreeze = m_SonoParameters->pBV(BFPNames::FreezeStr);

    double ratio = 1;

    if (m_SonoParameters->pBV(BFPNames::IsFullScreenZoomInStr))
    {
        if (m_SonoParameters->pBV(BFPNames::IsSecondGearFullScreenZoomInStr))
        {
            ratio = m_ratioSecondGear;
        }
        else
        {
            ratio = m_ratiofirstGear;
        }
    }

    m_Render->draw(painter, isFreeze, ratio);

    foreach (GLRender* glRender, m_Renders)
    {
        if (glRender != NULL)
        {
            glRender->render();
        }
    }
    m_FpsCalculator->cal();
}

void PainterRenderImageTile::imageRenderCreated()
{
    m_ImageRender->setImageFormat(IBFRawData2ImageData::Format_RGB888);
}

bool PainterRenderImageTile::changeSize(int width, int height)
{
    m_ZoomRenderWidgetSize.setWidth(width);
    m_ZoomRenderWidgetSize.setHeight(height);
    qreal zoomFact = m_ZoomRenderWidgetSize.height() / (qreal)m_RawRenderWidgetSize.height();
    m_ParentWidget->setFixedSize(m_ZoomRenderWidgetSize);

    ImageWidget* w = dynamic_cast<ImageWidget*>(m_ParentWidget);
    if (w)
    {
        w->moveToCustomerPos();
    }

    setZoomFactor(zoomFact);
    update();
    QApplication::processEvents(QEventLoop::ExcludeUserInputEvents);
    w->setProperty("originPos", w->pos());
    return true;
}

void PainterRenderImageTile::setZoomFactor(qreal value)
{
    ImageWidget* widget = static_cast<ImageWidget*>(m_ParentWidget);

    if (m_ZoomRenderWidgetSize == m_RawRenderWidgetSize)
    {
        widget->graphicsView().scale(1 / m_zoomFactor, 1 / m_zoomFactor);
        m_EventHandler.setZoomFactor(1.0);
        m_zoomFactor = 1.0f;
    }
    else
    {
        if (!RealCompare::AreEqual(m_zoomFactor, 1.0f))
        {
            //如果此时已经放大,需要先scale到原始大小
            widget->graphicsView().scale(1 / m_zoomFactor, 1 / m_zoomFactor);
        }

        widget->graphicsView().scale(value, value);
        m_EventHandler.setZoomFactor(value);
        m_zoomFactor = value;
    }
    emit zoomFactorChanged(m_zoomFactor);
    m_Render->setFullScreenZoomFactor(m_zoomFactor);
}

ImageEventArgs::ImageType PainterRenderImageTile::bImageType() const
{
    SyncModeType type = (SyncModeType)pIV(BFPNames::SyncModeStr);
    switch (type)
    {
    case Sync_CD:
    case Sync_C:
    case Sync_CM:
        return ImageEventArgs::ImageC;
    default:
        return ImageEventArgs::ImageB;
    }
}

bool PainterRenderImageTile::checkNeedResetRender(const SystemScanMode& rawScanmode, const SystemScanMode& newScanMode)
{
    if (rawScanmode == newScanMode)
    {
        return false;
    }

    switch (rawScanmode)
    {
    case SystemScanModeB:
    case SystemScanMode2B:
    case SystemScanMode4B:
        if (newScanMode == SystemScanModeColorDoppler || newScanMode == SystemScanModePowerDoppler ||
            newScanMode == SystemScanModeDPowerDoppler || newScanMode == SystemScanModeAV ||
            newScanMode == SystemScanModeSonoNeedle || newScanMode == SystemScanModeMVI)
        {
            return false;
        }
        break;

    case SystemScanModeLRBM:
    case SystemScanModeUDBM:
    case SystemScanModeM:
        break;

    case SystemScanModeColorDoppler:
    case SystemScanModeMVI:
    case SystemScanModeSonoNeedle:
    case SystemScanModeTissueDoppler:
    case SystemScanModePowerDoppler:
    case SystemScanModeDPowerDoppler:
        if (newScanMode == SystemScanModeB || newScanMode == SystemScanMode2B || newScanMode == SystemScanMode4B)
        {
            return false;
        }
        break;

    case SystemScanModeBPW:
    case SystemScanModeColorPW:
    case SystemScanModePowerPW:
    case SystemScanModeDPowerPW:
    case SystemScanModeTissuePW:
    case SystemScanModeMVIPW:
        if (newScanMode == SystemScanModeBPW || newScanMode == SystemScanModeColorPW ||
            newScanMode == SystemScanModePowerPW || newScanMode == SystemScanModeDPowerPW ||
            newScanMode == SystemScanModeTissuePW || newScanMode == SystemScanModeMVIPW)
        {
            return false;
        }
        break;

    case SystemScanModeCWD:
    case SystemScanModeCWDColorDoppler:
    case SystemScanModeCWDDirectionalPowerDoppler:
    case SystemScanModeCWDPowerDoppler:
        if (newScanMode == SystemScanModeCWD || newScanMode == SystemScanModeCWDColorDoppler ||
            newScanMode == SystemScanModeCWDDirectionalPowerDoppler || newScanMode == SystemScanModeCWDPowerDoppler)
        {
            return false;
        }
        break;
    case SystemScanModeColorM:
    case SystemScanModeColorLRBM:
    case SystemScanModeColorUDBM:
    case SystemScanModePDM:
    case SystemScanModePDLRBM:
    case SystemScanModePDUDBM:
    case SystemScanModeDPDM:
    case SystemScanModeDPDLRBM:
    case SystemScanModeDPDUDBM:
    case SystemScanModeTDIM:
    case SystemScanModeTDILRBM:
    case SystemScanModeTDIUDBM:
    case SystemScanModeE:
    case SystemScanModeLRFreeM:
    case SystemScanModeUDFreeM:
    case SystemScanModeFourDPre:
    case SystemScanModeFourDLive:
    case SystemScanModeFourD:
    case SystemScanModeLRBBC:
    case SystemScanModeElasto:
    case SystemScanModeBBE:
    case SystemScanModeUDBBC:
    case SystemScanModeFreeHand3D:
    case SystemScanModeCP:
        break;

    default:
        break;
    };
    return true;
}

void PainterRenderImageTile::changedImageRenderSonoParameters(SonoParameters* newSonoParameter,
                                                              const bool updateColorMap)
{
    if (m_ImageRender->sonoParameters() != newSonoParameter)
    {
        m_isPauseWhileFreezeAndChanging = true;
        m_ImageRender->setIsUpdateParameters(updateColorMap);
        m_ImageRender->setSonoParameters(newSonoParameter);
        m_ImageRender->setIsUpdateParameters(true);
        m_isPauseWhileFreezeAndChanging = false;
        emit changeParameters(newSonoParameter);
        if (updateColorMap)
        {
            emit mapsChanged();
        }
    }
}

void PainterRenderImageTile::processChorma(ImageEventArgs* data)
{
    Q_ASSERT(data->bitCount() == 8);

    if (data != nullptr)
    {
        m_ImageRender->rawData2ImageData(m_RawData->imageData(), m_RawData->imageSize(), m_ImageData,
                                         m_RawData->imageSize() * 4);

        data->setImageData(m_ImageData);
        setImageData(data);
    }
}
