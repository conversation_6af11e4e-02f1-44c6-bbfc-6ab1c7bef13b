#include <qmath.h>
#include <QWidget>
#include <QMouseEvent>
#include <QPainter>
#include <QGraphicsView>
#include <QDebug>
#include "util.h"
#include "imagetile.h"
#include "ibfrawdata2showdata.h"
#include "imagerender.h"
#include "buffermanager.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "parameter.h"
#include "icinelooper.h"
#include "storeddata.h"
#include "realcompare.h"
#include "icursormouseaction.h"
#include "pimplglyphswidget.h"
#include "imagewidget.h"
#include "setting.h"
#include "ibeamformerinitialization.h"
//#include <unistd.h>
#include "modelconfig.h"
#include "ewidget.h"
#include "straincurvewidget.h"
#include "imagebuffer.h"
#ifdef WIN32
#else
#include "fourdimagebuffer.h"
#endif
#include "ilinebuffer.h"
#include "applicationinfo.h"
#include <stdint.h>
#include <QApplication>
#include "colormapassembly.h"
#include "iglyphscontrolcreator.h"

//最后两个数值为两档一键全屏的renderwidget的窗口的高度。计算方法 (960或者1040 -
// 50(冻结条的高度))/RenderImageSizeStr.height*RenderWidgetSizeStr
const QList<int> ImageTile::m_ZoomInHeights = QList<int>() << 560 << 600 << 640 << 672 << 720 << 960 << 1040;

ImageTile::ImageTile(QGraphicsScene* scene, QWidget* parent)
    : QGraphicsView(scene, parent)
    , m_ImageRender(NULL)
    , m_ImageData(NULL)
    , m_ImageDataSize(0)
    , m_RawDataImageData(NULL)
    , m_RawImageDataSize(0)
    , m_RawData(NULL)
    , m_BFIODeviceController(NULL)
    , m_RealTimeSonoParameters(NULL)
    , m_BufferManager(NULL)
    , m_ParentWidget(parent)
    , m_CineLooper(NULL)
    , m_StartGrabScreen(false)
    , m_ModeGlyphsWidget(new PimplGlyphsWidget(scene, this))
    , m_zoomFactor(1.0f)
    , m_BKColor(Setting::instance().defaults().VideoColorKey())
    , m_IsPauseWhileModeChanging(false)
    , m_PauseCount(0)
    , m_LineBufferManager(NULL)
    , m_isPauseWhileFreezeAndChanging(false)
    , m_IsWholeImageMode(Setting::instance().defaults().isWholeImage())
    , m_OutSideOfRenderPos(QPoint(-1, -1))
{
    m_Timer.setSingleShot(true);
    m_Timer.setInterval(500);
    connect(&m_Timer, SIGNAL(timeout()), this, SLOT(updateZoomIn()));

    m_PauseTimer.setSingleShot(true);
    m_PauseTimer.setInterval(1000); // 675ms 必须超过beamformer中imageIntoUnstable的时间
    connect(&m_PauseTimer, SIGNAL(timeout()), this, SLOT(resumeRefresh()));
    connect(&Setting::instance().defaults(), SIGNAL(settingChanged(QString)), this, SLOT(onSettingChanged(QString)));

    connect(&ApplicationInfo::instance(), &ApplicationInfo::syncCursorPos, this,
            [=](const QPoint point) { m_EventHandler.doSyncCursorPos(point); });

    connect(&ApplicationInfo::instance(), &ApplicationInfo::cursorVisibleChanged, this, [=](bool visible) {
        if (visible)
        {
            m_EventHandler.resetTouchState();
        }
    });

    // 2025-06-13 Write by AlexWang 减少图像区域移动鼠标时对图像帧频的影响
    setAttribute(Qt::WA_OpaquePaintEvent);
    setAttribute(Qt::WA_NoSystemBackground);
    setAttribute(Qt::WA_StaticContents);
}

ImageTile::~ImageTile()
{
    Util::SafeDeleteArrayPtr(m_ImageData);
    Util::SafeDeleteArrayPtr(m_RawDataImageData);
}

int ImageTile::paintFrameIndex()
{
    return 0;
}

int ImageTile::imageBufferFrameIndex()
{
    return 0;
}

QSize ImageTile::imageSize() const
{
    return m_ImageSize;
}

void ImageTile::setImageSize(QSize value)
{
    m_ImageSize = value;
    m_dstImageSize = value;

    onSetImageSize(value);
}

void ImageTile::setCineLooper(ICineLooper* cineLooper)
{
    m_CineLooper = cineLooper;
}

void ImageTile::setBeamFormerInitialization(IBeamFormerInitialization* value)
{
    m_BFIODeviceController = value;
}

void ImageTile::setRealTimeSonoParameters(SonoParameters* value)
{
    m_RealTimeSonoParameters = value;
    connect(m_RealTimeSonoParameters->parameter(BFPNames::FullScreenZoomInIndexStr), SIGNAL(valueChanging(QVariant)),
            this, SLOT(onFullScreenZoomInIndexChanging(QVariant)));
    connect(m_RealTimeSonoParameters->parameter(BFPNames::FullScreenZoomInIndexStr), SIGNAL(gettingText(QString&)),
            this, SLOT(onGettingFullScreenZoomInIndexText(QString&)));
    connect(m_RealTimeSonoParameters->parameter(BFPNames::FullScreenZoomInIndexStr), SIGNAL(gettingMax(int&)), this,
            SLOT(onGettingFullScreenZoomInMax(int&)));
    m_RealTimeSonoParameters->parameter(BFPNames::FullScreenZoomInIndexStr)->update();

    connect(m_RealTimeSonoParameters->parameter(BFPNames::SystemScanModeStr),
            SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
            SLOT(onBeforeSystemScanModeChanged(QVariant, QVariant&)));
}

bool ImageTile::isPause()
{
    return false;
}

SonoParameters* ImageTile::realTimeSonoParameters() const
{
    return m_RealTimeSonoParameters;
}

QRect ImageTile::rect() const
{
    return m_Rect;
}

void ImageTile::setRect(QRect value)
{
    m_Rect = value;
}

void ImageTile::setBFRawData2ShowData(IBFRawData2ShowData* value)
{
    if (m_ImageRender == NULL)
    {
        m_ImageRender = new ImageRender(value, this);
        connect(m_ImageRender, SIGNAL(colorBarChanged(ColorBarModel*)), this, SIGNAL(colorBarChanged(ColorBarModel*)));
        connect(m_ImageRender, SIGNAL(colorBarGroupChanged(QList<ColorBarModel*>)), this,
                SIGNAL(colorBarGroupChanged(QList<ColorBarModel*>)));
        connect(m_ImageRender, SIGNAL(colorMapChanged()), this, SLOT(onColorMapChanged()));
        connect(m_ImageRender, SIGNAL(mapsChanged()), this, SIGNAL(mapsChanged()));
        imageRenderCreated();
    }
}

IBufferManager* ImageTile::bufferManager() const
{
    return m_BufferManager;
}

ICineLooper* ImageTile::cineLooper() const
{
    return m_CineLooper;
}

QImage ImageTile::currentRawDataImage()
{
    if (m_RawData != NULL)
    {
        // 8bit转32bit灰度
        int rawDataSize = m_RawData->imageSize();
        if (m_RawDataImageData == NULL || rawDataSize != m_RawImageDataSize)
        {
            m_RawImageDataSize = rawDataSize;
            if (m_RawDataImageData != NULL)
            {
                delete[] m_RawDataImageData;
            }
#ifdef WIN32
            m_RawDataImageData = new quint32[m_RawImageDataSize];
#else
            m_RawDataImageData = new u_int32_t[m_RawImageDataSize];
#endif
        }

        uchar pixel = 0;
        for (int i = 0; i < rawDataSize; i++)
        {
            pixel = m_RawData->imageData()[i];
            m_RawDataImageData[i] = 0xFF000000 | (pixel << 16) | (pixel << 8) | pixel;
        }

        QImage image(reinterpret_cast<uchar*>(m_RawDataImageData), m_RawData->width(), m_RawData->height(),
                     QImage::Format_RGB32);
        return image;
    }
    else
    {
        return QImage();
    }
}

void ImageTile::setMouseAction(ICursorMouseAction* action)
{
    m_EventHandler.setMouseAction(action);
}

void ImageTile::mouseDoubleClickEvent(QMouseEvent* e)
{
    m_EventHandler.mouseDoubleClickEvent(e);
}

void ImageTile::mousePressEvent(QMouseEvent* e)
{
    m_EventHandler.mousePressEvent(e);
}

void ImageTile::mouseMoveEvent(QMouseEvent* e)
{
    m_EventHandler.mouseMoveEvent(e);
}

void ImageTile::mouseReleaseEvent(QMouseEvent* e)
{
    m_EventHandler.mouseReleaseEvent(e);
}

bool ImageTile::event(QEvent* event)
{
    // 在鼠标显示时操作，需要考虑主图区外是否有其它组件处于hover等情况，如果有需要触发leave，让其它组件恢复正常状态
    if (ApplicationInfo::instance().isCursorVisible() && m_OutSideOfRenderPos != QPoint(-1, -1))
    {
        if (event->type() == QEvent::TouchBegin)
        {
            doLeave(m_OutSideOfRenderPos);
            m_OutSideOfRenderPos = QPoint(-1, -1);
        }
    }

    if (!m_EventHandler.event(event))
    {
        return QGraphicsView::event(event);
    }
    return true;
}

void ImageTile::draw(QPainter* painter)
{
    painter->save();

    //    QTransform transform = QTransform(m_zoomFactor, 0, 0,
    //                                      0, m_zoomFactor, 0,
    //                                      0, 0, 1);

    //    painter->setViewport(transform.mapRect(m_Rect));
    //    painter->setViewport(m_Rect);
    painter->setRenderHint(QPainter::SmoothPixmapTransform);
    painter->setWindow(m_ParentWidget->rect());
    //    painter->setWindow(/*size.width()/2*/0, /*size.height()/2*/0, m_Rect.width(), m_Rect.height());
    //    painter->setWindow(QRect(QPoint(-125, 0), m_ParentWidget->rect().size()));

    render(painter);

    painter->restore();
}

void ImageTile::onParentShow()
{
}

void ImageTile::onLoad(const StoredData& data)
{
    if (data.sonoParameters() != NULL)
    {
        setSonoParameters(data.sonoParameters());
        if (m_BufferManager != NULL)
        {
            // m_SonoParameters 变化，让图像区用新的参数重新绘制一次
            m_BufferManager->update();
        }
    }
}

void ImageTile::startGrabScreen()
{
}

void ImageTile::stopGrabScreen()
{
}

const QList<ColorBarModel*>& ImageTile::activeColorBarModelGroup() const
{
    return m_ImageRender->activeColorBarModelGroup();
}

void ImageTile::update()
{
    emit refresh();
}

void ImageTile::onNewImage(ImageEventArgs* data)
{
    Q_UNUSED(data);
}

void ImageTile::onNewPictureImage(QImage image)
{
    Q_UNUSED(image);
}

void ImageTile::setPause(bool value)
{
    Q_UNUSED(value);
}

void ImageTile::reduceRenderRate()
{
}

void ImageTile::beginPause()
{
    m_PauseCount++;
    setPause(true);
}

void ImageTile::endPause()
{
    if (--m_PauseCount == 0)
    {
        setPause(false);
    }
}

void ImageTile::onFreezeChanged(const QVariant& value)
{
    if (m_BufferManager != NULL)
    {
        m_BufferManager->freeze(value.toBool());
    }

    if (m_CineLooper != NULL)
    {
        m_CineLooper->stopLoop();
    }

    if (ModelConfig::instance().value(ModelConfig::IsRealUSBFps, false).toBool() || !value.toBool())
    {
        changeFps();
    }
}

void ImageTile::onFullScreenZoomInIndexChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (isZoomedIn())
    {
        //用timer延时控制放大功能,如果连续控制,会卡住
        m_Timer.start();
    }
}

void ImageTile::onGettingFullScreenZoomInIndexText(QString& value)
{
    qreal zoomFact = m_ZoomInHeights.at(m_RealTimeSonoParameters->pIV(BFPNames::FullScreenZoomInIndexStr)) /
                     (qreal)m_ImageSize.height();
    value = QString("x %1").arg(zoomFact, 0, 'f', 2);
}

void ImageTile::onGettingFullScreenZoomInMax(int& value)
{
    value = m_ZoomInHeights.count() - 1;
}

void ImageTile::pauseProcess()
{
    beginPause();
    Util::usleep(25000); //等待线程暂停
}

void ImageTile::onElastoImageShapeUnstable()
{
    calcElastoRoiRect();
}

void ImageTile::onIntellectMove(const QVariant& value)
{
    QMouseEvent e = QMouseEvent(QEvent::MouseMove, value.toPoint(), value.toPoint(), value.toPoint(), Qt::LeftButton,
                                Qt::NoButton, Qt::NoModifier, Qt::MouseEventSynthesizedByApplication);
    m_EventHandler.intellectMoveEvent(&e);
}

void ImageTile::onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (m_IsWholeImageMode)
    {
        SystemScanMode oldMode = (SystemScanMode)oldValue.toInt();
        SystemScanMode newMode = (SystemScanMode)newValue.toInt();

        QList<SystemScanMode> modes;
        modes << SystemScanModeB << SystemScanModeColorDoppler << SystemScanModePowerDoppler
              << SystemScanModeTissueDoppler << SystemScanModeDPowerDoppler << SystemScanModeMVI
              << SystemScanModeFourDPre << SystemScanModeSonoNeedle;

        if (modes.contains(oldMode) && modes.contains(newMode) && oldMode != newMode)
        {
            m_IsPauseWhileModeChanging = true;
        }
        else
        {
            m_IsPauseWhileModeChanging = false;
        }
    }
}

void ImageTile::onFPSChanged(const QVariant& value)
{
    Q_UNUSED(value);
    changeFps();
}

void ImageTile::onColorMapChanged()
{
}

void ImageTile::updateZoomIn()
{
    if (isZoomedIn())
    {
        zoomIn();
    }
}

void ImageTile::resumeRefresh()
{
    endPause();
}

void ImageTile::onSettingChanged(const QString& property)
{
    if (!pBV(BFPNames::FreezeStr) && property == "StoreCineLastFrames")
    {
        changeFps();
    }
}

void ImageTile::onBufferCurrentStrainIndexChanged(int index)
{
    setPV(BFPNames::ElastoStrainIndexStr, index, true);
}

void ImageTile::onBufferManagerCurrentIndexChanged(int value)
{
    Q_UNUSED(value);
    EWidget* widget = m_ModeGlyphsWidget->getAs<EWidget>();
    StrainCurveWidget* curveWidget = NULL;
    if (widget != NULL)
    {
        curveWidget = &widget->second();

        if (curveWidget != NULL)
        {
            int strainCount = pV(BFPNames::ElastoStrainListStr).toList().count();
            int frameCount = m_BufferManager->frameCount();
            //弹性模式下电影帧数是和ElastoStrainList的count是一致的,但是在回调单帧弹性图像时frameCount只有一帧图像,
            //而ElastoStrainList是一个Parameter,count是一个电影的所有帧数,所以使用m_BufferManager->currentIndex()
            //会导致回调的单帧弹性图像使用的ElastoStrain值和标记位置错误,所以加入判断分开设置
            if (frameCount == strainCount)
            {
                curveWidget->setImageIndex(m_BufferManager->currentIndex());
            }
            else
            {
                curveWidget->setImageIndex(pIV(BFPNames::ElastoStrainIndexStr));
            }
        }
    }
}

void ImageTile::connectParametersSignals()
{
    m_ConnectedParameters.clear();
    connect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFreezeChanged(QVariant)));
    connect(parameter(BFPNames::FPSStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFPSChanged(QVariant)));

    m_ConnectedParameters.append(parameter(BFPNames::FreezeStr));
    m_ConnectedParameters.append(parameter(BFPNames::FPSStr));

    m_ModeGlyphsWidget->setSonoParameters(m_SonoParameters);
}

void ImageTile::disconnectParametersSignals()
{
    disconnect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFreezeChanged(QVariant)));
    disconnect(parameter(BFPNames::FPSStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFPSChanged(QVariant)));
}

void ImageTile::onSetSonoParameters()
{
    if (m_ImageRender != NULL)
    {
        m_ImageRender->setSonoParameters(m_SonoParameters);
    }

    if (m_BufferManager == NULL)
    {
        m_BufferManager = new BufferManager(256, this);
        m_BufferManager->addBuffer(BufferTypeEnum::WholeImage,
                                   QVector<DataSizeInfo>(1, m_ImageRender->rawDataSizeInfo()));
        //#ifdef USE_4D
        //        m_BufferManager->addBuffer(BufferTypeEnum::FourDImage, QVector<DataSizeInfo>(1,
        //        m_ImageRender->rawDataSizeInfo()));
        //#endif
        m_BufferManager->setCurrentBufferType(BufferTypeEnum::WholeImage);
        //        connect(m_BufferManager, SIGNAL(currentStrainIndexChanged(int)), this,
        //        SLOT(onBufferCurrentStrainIndexChanged(int))); connect(m_BufferManager,
        //        SIGNAL(currentIndexChanged(int)), this, SLOT(onBufferManagerCurrentIndexChanged(int)));
        // m_CineLooper->setBufferManager(m_BufferManager);
    }

    changeFps();

    if (!m_IsFirstSetSonoParameters && !m_RealTimeSonoParameters->pBV(BFPNames::ShowFourDWidgetStr))
    {
        //相当于调用一次onFreezeChanged(const QVariant& value)
        autoCallSelfObjectConnetedChangedSlots();
    }
}

void ImageTile::render(QPainter* painter)
{
    Q_UNUSED(painter);
}

bool ImageTile::isZoomedIn() const
{
    return false;
}

bool ImageTile::zoomIn()
{
    return false;
}

bool ImageTile::zoomOut()
{
    return false;
}

bool ImageTile::isZoomInEnabled() const
{
    return false;
}

bool ImageTile::isColorRender() const
{
    return false;
}

void ImageTile::setColorRender(bool value)
{
    Q_UNUSED(value);
}

bool ImageTile::isRenderError() const
{
    return false;
}

bool ImageTile::isNeedPauseSleep() const
{
    return false;
}

bool ImageTile::isRenderPaused() const
{
    return false;
}

void ImageTile::destroyRender()
{
}

void ImageTile::calcElastoRoiRect()
{
    if (m_ImageRender != NULL)
    {
        m_ImageRender->calcElastoRoiRect();
    }
}

void ImageTile::sendElastoRoiRect()
{
    if (m_ImageRender != NULL)
    {
        m_ImageRender->sendElastoRoiRect();
    }
}

QImage ImageTile::getSetIndexBImage(int)
{
    return QImage();
}

const ColorMapAssembly* ImageTile::colorMapAssembly() const
{
    return m_ImageRender->colorMapAssembly();
}

void ImageTile::getColorMaps(void** map1, void** map2)
{
    return m_ImageRender->getColorMaps(map1, map2);
}

void ImageTile::setBufferManager(ILineBuffer* bufferManager)
{
    if (m_ModeGlyphsWidget != NULL)
    {
        m_ModeGlyphsWidget->setBufferManager(bufferManager);
    }
    m_LineBufferManager = bufferManager;
}

const DataSizeInfo& ImageTile::dataSizeInfo() const
{
    return m_ImageRender->rawDataSizeInfo();
}

void ImageTile::setBeamFormer(IBeamFormer* beamformer)
{
    Q_UNUSED(beamformer);
}

void ImageTile::setColorMapManager(IColorMapManager* colorMapManager)
{
    ColorMapAssembly* colorMapAssembly = const_cast<ColorMapAssembly*>(m_ImageRender->colorMapAssembly());
    Q_ASSERT(colorMapManager != NULL);
    colorMapAssembly->setColorMapManager(colorMapManager);
}

void ImageTile::removeAllData()
{
}

void ImageTile::setFreeHand3DROIGeometry(const QRect& rect, const QPoint& offset)
{
    setPV(BFPNames::FreeHand3DRoiRectStr, rect, true);
    //对FreeHand3D ROI 据相框的大小做限定
    if (rect.x() - offset.x() < 0 || rect.y() - offset.y() < 0 || rect.width() < m_FreeHandRoiMinSize.width() ||
        rect.height() < m_FreeHandRoiMinSize.height() || rect.x() - offset.x() + rect.width() > dataSizeInfo().Width ||
        rect.y() - offset.y() + rect.height() > dataSizeInfo().Height)
    {
        return;
    }
    //确保ROI矩形框的宽度被4整除，高度被2整除

    m_FreeHandRoiRect.setX(rect.x() - offset.x());
    m_FreeHandRoiRect.setY(rect.y() - offset.y());
    m_FreeHandRoiRect.setWidth(rect.width() - rect.width() % 4);
    m_FreeHandRoiRect.setHeight(rect.height() - rect.height() % 2);
}

const QRect& ImageTile::freeHand3DRoiGeometry() const
{
    return m_FreeHandRoiRect;
}

void ImageTile::setFreeHand3DRoiMinSize(const QSize& size)
{
    m_FreeHandRoiMinSize = size;
}

void ImageTile::init()
{
}

void ImageTile::imageRenderCreated()
{
}

bool ImageTile::changeSize(int width, int height)
{
    Q_UNUSED(width);
    Q_UNUSED(height);
    return false;
}

qreal ImageTile::zoomFactor() const
{
    return m_zoomFactor;
}

QWidget* ImageTile::parentWidget() const
{
    return m_ParentWidget;
}

void ImageTile::setOutSideOfRenderPos(const QPoint& pos)
{
    m_OutSideOfRenderPos = pos;
}

void ImageTile::setGrabedWidget(QWidget* grabedWidget)
{
    m_EventHandler.setGrabedWidget(grabedWidget);
}

void ImageTile::setIsPauseWhileFreezeAndChanging(bool value)
{
    m_isPauseWhileFreezeAndChanging = value;
    m_ModeGlyphsWidget->setPaused(value);
}

void ImageTile::initImageTile(IGlyphsControlCreator* glyphsControl)
{
    glyphsControl->createGlyphsControl(m_ModeGlyphsWidget);

    setViewportUpdateMode(QGraphicsView::FullViewportUpdate);

    setAttribute(Qt::WA_AcceptTouchEvents, true);
}

void ImageTile::onSetImageSize(const QSize& value)
{
    Q_UNUSED(value);
}

void ImageTile::changeFps()
{
    if (m_CineLooper != NULL && m_BufferManager != NULL)
    {
        if (m_SonoParameters->isRealTime())
        {
            int fps = 0;
            bool isRealUSBFps = ModelConfig::instance().value(ModelConfig::IsRealUSBFps, false).toBool();
            bool isRealFps = Setting::instance().defaults().isRealFps();
            int frames = Setting::instance().defaults().storeCineLastFrames();
            if (isRealUSBFps)
            {
                if (!pBV(BFPNames::FreezeStr))
                {
                    if (!m_BufferManager->isFourDImageBuffer())
                    {
                        m_BufferManager->removeAll();
                    }
                    m_BufferManager->setUsedSize(Setting::instance().defaults().framesInSecond() ? 256 : frames);
                }
                else //冻结时决定fps
                {
                    fps = m_BufferManager->appendFps();
                    m_CineLooper->setMaxFps(fps);
                    m_BufferManager->setFps(fps);
                }
            }
            else
            {
                if (isRealFps)
                {
                    fps = pIV(BFPNames::FPSStr);
                }

                if (fps == 0)
                {
                    fps = Setting::instance().defaults().fps();
                }

                m_CineLooper->setMaxFps(fps);

                if (!pBV(BFPNames::FreezeStr) && !m_BufferManager->isFourDImageBuffer())
                {
                    m_BufferManager->removeAll();
                }
                m_BufferManager->setUsedSize(!Setting::instance().defaults().framesInSecond() ? frames : fps * frames);
                if (isRealFps)
                {
                    m_BufferManager->setFps(fps);
                }
            }
        }
        else
        {
            // loaded cine, m_BufferManager->fps()==0 is old cine, otherwise is new cine
            m_CineLooper->setMaxFps(m_BufferManager->fps() == 0 ? Setting::instance().defaults().fps()
                                                                : m_BufferManager->fps());
            m_BufferManager->setUsedSize();
        }
    }
}

void ImageTile::doLeave(const QPoint& pos)
{
    QWidget* widget = QApplication::widgetAt(pos);
    if (widget != nullptr)
    {
        QMouseEvent leaveEvent(QEvent::Leave, QPoint(0, 0), Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
        QApplication::sendEvent(widget, &leaveEvent);
        QMouseEvent event(QEvent::HoverLeave, QPoint(0, 0), Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
        QApplication::sendEvent(widget, &event);
    }
}

QMouseEvent ImageTile::decorateEventWithTransform(QMouseEvent* event)
{
    QTransform trans = QTransform(1 / zoomFactor(), 0, 0, 0, 1 / zoomFactor(), 0, 0, 0, 1);

    QPointF posf = trans.map(event->pos());
    QPoint posCeil = QPoint(qRound(posf.x()), qRound(posf.y()));

    QPointF posGlobalf = trans.map(QPointF(event->globalPos()));
    QPoint posGlobalCeil = QPoint(qRound(posGlobalf.x()), qRound(posGlobalf.y()));

    return QMouseEvent(event->type(), posCeil, posGlobalCeil, event->button(), event->buttons(), event->modifiers());
}
