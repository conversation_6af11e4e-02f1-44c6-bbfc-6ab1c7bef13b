#ifndef IMAGETILE_H
#define IMAGETILE_H
#include "usfinterfaceimagerenderui_global.h"

#include <QObject>
#include <QRect>
#include <QSize>
#include <QImage>
#include <QList>
#include <QTimer>
#include <QMouseEvent>
#include <QTouchEvent>
#include "sonoparametersclientbase.h"
#include "iimagetilescreenshots.h"
#include <QGraphicsView>
#include "dataarg.h"
#include "imageeventargs.h"
#include "imagetileeventhandler.h"
#include "imageinfodef.h"

class ImageEventArgs;
class QMouseEvent;
class QPainter;
class IBFRawData2ShowData;
class ImageRender;
class IBufferManager;
class QWidget;
class ICineLooper;
class StoredData;
class ColorBarModel;
class ICursorMouseAction;
class PimplGlyphsWidget;
class Overlay;
class QGraphicsScene;
class IBeamFormerInitialization;
class ColorMapAssembly;
class ILineBuffer;
class IBeamFormer;
class SonoParameters;
class IColorMapManager;
class QGLContext;
class IGlyphsControlCreator;
class USF_INTERFACE_IMAGING_IMAGERENDER_UI_EXPORT ImageTile : public QGraphicsView,
                                                              public SonoParametersClientBase,
                                                              public IImageTileScreenshots
{
    Q_OBJECT
public:
    ImageTile(QGraphicsScene* scene, QWidget* parent = 0);
    ~ImageTile();
    QSize imageSize() const;
    void setImageSize(QSize value);
    void setCineLooper(ICineLooper* cineLooper);
    void setBeamFormerInitialization(IBeamFormerInitialization* value);
    void setRealTimeSonoParameters(SonoParameters* value);
    SonoParameters* realTimeSonoParameters() const;
    QRect rect() const;
    void setRect(QRect value);
    void setBFRawData2ShowData(IBFRawData2ShowData* value);
    IBufferManager* bufferManager() const;
    ICineLooper* cineLooper() const;
    virtual QImage currentImage(ImageEventArgs::ImageType type = ImageEventArgs::WholeImage) const = 0;
    virtual QImage currentRawDataImage();
    void setMouseAction(ICursorMouseAction* action);
    virtual void mouseDoubleClickEvent(QMouseEvent* e);
    virtual void mousePressEvent(QMouseEvent* e);
    virtual void mouseMoveEvent(QMouseEvent* e);
    virtual void mouseReleaseEvent(QMouseEvent* e);
    virtual bool event(QEvent* event) override;
    virtual void draw(QPainter* painter);
    virtual void onParentShow();
    virtual void onLoad(const StoredData& data);
    virtual void startGrabScreen();
    virtual void stopGrabScreen();
    const QList<ColorBarModel*>& activeColorBarModelGroup() const;
    PimplGlyphsWidget& pimplGlyphsWidget()
    {
        return *m_ModeGlyphsWidget;
    }
    virtual bool isZoomedIn() const;
    virtual bool zoomIn();
    virtual bool zoomOut();
    virtual bool isZoomInEnabled() const;
    virtual bool isColorRender() const;
    void setColorRender(bool value);
    virtual bool isRenderError() const;
    virtual bool isNeedPauseSleep() const;
    virtual bool isRenderPaused() const;
    virtual void destroyRender();
    virtual void calcElastoRoiRect();
    virtual void sendElastoRoiRect();
    virtual QImage getSetIndexBImage(int);
    virtual int paintFrameIndex();
    virtual int imageBufferFrameIndex();
    const ColorMapAssembly* colorMapAssembly() const;
    /**
     * @brief getColorMaps 获取最终做colormap映射时，使用已经合成好后的
     * 两个colormap，保护B/M模式，PW模式的两中colormap的情况
     *
     * @param map1
     * @param map2
     */
    void getColorMaps(void** map1, void** map2);
    virtual void setBufferManager(ILineBuffer* bufferManager);
    const DataSizeInfo& dataSizeInfo() const;
    virtual void setBeamFormer(IBeamFormer* beamformer);
    void setColorMapManager(IColorMapManager* colorMapManager);
    virtual void removeAllData();
    void setFreeHand3DROIGeometry(const QRect& rect, const QPoint& offset);
    const QRect& freeHand3DRoiGeometry() const;
    void setFreeHand3DRoiMinSize(const QSize& size);
    virtual void init();
    QWidget* parentWidget() const;
    void setOutSideOfRenderWidget(QWidget* widget);
    void setOutSideOfRenderPos(const QPoint& pos);

    void setGrabedWidget(QWidget* grabedWidget);

    void setIsPauseWhileFreezeAndChanging(bool value);
    static const QList<int> m_ZoomInHeights;

    void initImageTile(IGlyphsControlCreator* glyphsControl);
    virtual bool isPause();
signals:
    void refresh();
    void colorBarGroupChanged(const QList<ColorBarModel*>& group);
    void colorBarChanged(ColorBarModel* model);
    void zoomFactorChanged(qreal value);
    void mapsChanged();
    void changeParameters(SonoParameters* parameter);
    void fpsChanged(const QString& name, const float fps);
    void sonoZoomDataUpdate(int layout, int scanMode, const QRect& rect, const QPixmap& pix, const float imageRadio);
    void clearMeasureContextImage();
    void renderImageChanged(const QPixmap& pix, ModeImageType modeType, int frameIndex);

public slots:
    virtual void update();
    virtual void onNewImage(ImageEventArgs* data);
    virtual void onNewPictureImage(QImage image);
    virtual void setPause(bool value);
    virtual void reduceRenderRate();
    void beginPause();
    void endPause();
    void pauseProcess();
    void onElastoImageShapeUnstable();
    void onIntellectMove(const QVariant& value);
protected slots:
    virtual void onFreezeChanged(const QVariant& value);
    void onFullScreenZoomInIndexChanging(const QVariant& value);
    void onGettingFullScreenZoomInIndexText(QString& value);
    void onGettingFullScreenZoomInMax(int& value);
    void onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue);
    void onFPSChanged(const QVariant& value);
    virtual void onColorMapChanged();
    void updateZoomIn();
private slots:
    void resumeRefresh();
    void onSettingChanged(const QString& property);
    void onBufferCurrentStrainIndexChanged(int index);
    void onBufferManagerCurrentIndexChanged(int value);

protected:
    virtual void connectParametersSignals();
    virtual void disconnectParametersSignals();
    void onSetSonoParameters();
    virtual void render(QPainter* painter);

    virtual void imageRenderCreated();
    virtual bool changeSize(int width, int height);
    qreal zoomFactor() const;

    virtual void onSetImageSize(const QSize& value);
    void changeFps();
    void doLeave(const QPoint& pos);

protected:
    QRect m_Rect;
    QSize m_ImageSize;
    QSize m_dstImageSize;
    ImageRender* m_ImageRender;
    uchar* m_ImageData;
    int m_ImageDataSize;
#ifdef WIN32
    quint32* m_RawDataImageData;
#else
    u_int32_t* m_RawDataImageData;
#endif

    int m_RawImageDataSize;
    ImageEventArgs* m_RawData;
    IBeamFormerInitialization* m_BFIODeviceController;
    SonoParameters* m_RealTimeSonoParameters;
    IBufferManager* m_BufferManager;
    QWidget* m_ParentWidget;
    ICineLooper* m_CineLooper;
    bool m_StartGrabScreen;
    ImageTileEventHandler m_EventHandler;
    PimplGlyphsWidget* m_ModeGlyphsWidget;
    qreal m_zoomFactor;
    QColor m_BKColor;
    QTimer m_Timer;
    QTimer m_PauseTimer;
    bool m_IsPauseWhileModeChanging;
    int m_PauseCount;
    ILineBuffer* m_LineBufferManager;
    QRect m_FreeHandRoiRect;
    QSize m_FreeHandRoiMinSize;
    bool m_isPauseWhileFreezeAndChanging;
    bool m_IsWholeImageMode;
    QPoint m_OutSideOfRenderPos;

private:
    QMouseEvent decorateEventWithTransform(QMouseEvent* ev);
};

#endif // IMAGETILE_H
