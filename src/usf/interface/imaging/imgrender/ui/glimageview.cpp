#include "glimageview.h"
#include "bfpnames.h"
#include "ecgrender.h"
#include "glrender.h"
#include "imageglrender.h"
#include "imuiltsonoparametersbuffer.h"
#include "sonoparameters.h"
#include <QDebug>
#include <QGLContext>
#include <QGLWidget>
#include <QSize>
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
#include <QOpenGLContext>
#endif
#include <QMutexLocker>
/**
 * @brief GLImageView
 * 前提:QImage使用栈方式(堆方式未测试),格式:Format_ARGB32
 * 注意:在使用QImage的数据来进入GL渲染时处理QImage采用bits()方法得到的数据data中像素的
 * 组织形式应为ARGB,但实际调试中发现,每个像素中从字节从低到高依次是BGRA,方向刚好反过来.
 * 在处理彩色图像时尤其注意.
 */

GLImageView::GLImageView(QGraphicsScene* scene, const QGLContext* glContext, QWidget* parent)
    : QGraphicsView(scene, parent)
    , m_IsStart(false)
    , m_FrameAllowCount(AllAllow)
    , m_DrawnFrameCountOnce(0)
    , m_LineBufferManager(nullptr)
{
    m_Renders[ImageEventArgs::ImageB] = new ImageGLRender(this, glContext);
    m_Renders[ImageEventArgs::ImageECG] = new EcgRender(this, glContext);

    foreach (GLRender* render, m_Renders)
    {
        if (render != NULL)
        {
            render->init();
        }
    }

    setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOn);
    setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOn);
}

GLImageView::~GLImageView()
{
    QMutexLocker locker(&m_SyncMutex);
    qDeleteAll(m_Renders);
    m_Renders.clear();
}

void GLImageView::setBufferManager(IMuiltSonoParametersBuffer* bufferManager)
{
    QMutexLocker locker(&m_SyncMutex);
    foreach (GLRender* render, m_Renders)
    {
        if (render != NULL)
        {
            render->setBufferManager(bufferManager);
        }
    }
    m_LineBufferManager = bufferManager;
}

void GLImageView::setSonoParameters(SonoParameters* sonoparameters)
{
    QMutexLocker locker(&m_SyncMutex);
    foreach (GLRender* render, m_Renders)
    {
        if (render != NULL)
        {
            render->setSonoParameters(sonoparameters);
        }
    }
    onEcgActived(sonoparameters);
    EcgRender* ecgRender = static_cast<EcgRender*>(glRender(ImageEventArgs::ImageECG));
    if (ecgRender != NULL)
    {
        ecgRender->setWholeImageRect(QRect(QPoint(0, 0), sonoparameters->pV(BFPNames::ImageSizeStr).toSize()));
    }
}

void GLImageView::drawBackground(QPainter* painter, const QRectF& rect)
{
    QMutexLocker locker(&m_SyncMutex);
    if (!m_IsStart)
    {
        return;
    }
    painter->beginNativePainting();
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    GLRender* render = NULL;
    const auto& keys = m_Renders.keys();
    for (ImageEventArgs::ImageType type : keys)
    {
        if (type == ImageEventArgs::ImageECG)
        {
            render = m_Renders[type];
        }
        else
        {
            m_Renders[type]->render();
        }
    }

    if (render != NULL)
    {
        render->render();
    }

    painter->endNativePainting();
}

void GLImageView::start()
{
    QMutexLocker locker(&m_SyncMutex);
    if (!m_IsStart)
    {
        m_IsStart = true;
        foreach (GLRender* render, m_Renders)
        {
            if ((render != NULL) && !render->isActive())
            {
                render->setActive(true);
            }
        }
    }
}

void GLImageView::stop()
{
    QMutexLocker locker(&m_SyncMutex);
    if (m_IsStart)
    {
        m_IsStart = false;
        foreach (GLRender* render, m_Renders)
        {
            if (render != NULL)
            {
                render->setActive(false);
            }
        }
    }
}

void GLImageView::setImageScale(ImageScale& imageScale)
{
    QMutexLocker locker(&m_SyncMutex);
    foreach (GLRender* render, m_Renders)
    {
        if (render != NULL)
        {
            render->setImageScale(imageScale);
        }
    }
}

void GLImageView::setImageTranslate(QPointF& imageTranslate)
{
    QMutexLocker locker(&m_SyncMutex);
    foreach (GLRender* render, m_Renders)
    {
        if (render != NULL)
        {
            render->setImageTranslate(imageTranslate);
        }
    }
}

void GLImageView::updateFrameAllowCount(int layout)
{
    if (layout == 4)
    {
        m_FrameAllowCount = For_Layout4;
    }
    else
    {
        m_FrameAllowCount = AllAllow;
    }
}

void GLImageView::resizeEvent(QResizeEvent* event)
{
    {
        QMutexLocker locker(&m_SyncMutex);
        auto rendersEnd = m_Renders.end();
        for (auto renderIt = m_Renders.begin(); renderIt != rendersEnd; ++renderIt)
        {
            renderIt.value()->reorganizeShaders();
            if (renderIt.key() == ImageEventArgs::ImageECG)
            {
                EcgRender* ecgRender = static_cast<EcgRender*>(glRender(ImageEventArgs::ImageECG));
                if (ecgRender != NULL)
                {
                    auto ecgEnd = m_EcgPosLevel.end();
                    for (auto ecgIt = m_EcgPosLevel.begin(); ecgIt != ecgEnd; ++ecgIt)
                    {
                        ecgRender->updateECGPosLevel(ecgIt.key(), m_EcgPosLevel[ecgIt.key()]);
                        ecgRender->ecgInvertChanged(ecgIt.key(), m_EcgInvert[ecgIt.key()]);
                    }
                }
            }
        }
        scene()->invalidate(QRectF(0, 0, width(), height()), QGraphicsScene::BackgroundLayer);
        QGraphicsView::resizeEvent(event);
    }
    emit widgetSizeChange();
}

void GLImageView::playFrame(ImageEventArgs* data)
{
    if (m_Renders.isEmpty() || !m_IsStart)
    {
        return;
    }

    if ((m_FrameAllowCount != AllAllow) && (m_DrawnFrameCountOnce++ > m_FrameAllowCount))
    {
        m_DrawnFrameCountOnce = 0;
        return;
    }

    setImageData(data);
    scene()->invalidate(m_Image.rect(), QGraphicsScene::BackgroundLayer);
}

void GLImageView::setImageData(ImageEventArgs* data)
{
    QMutexLocker locker(&m_SyncMutex);

    GLRender* render = NULL;
    switch (data->imageType())
    {
    case ImageEventArgs::ImageECG:
    {
        render = glRender(ImageEventArgs::ImageECG);
        if (render != NULL)
        {
            render->setData(data);
        }
    }
    break;
    default:
    {
        render = glRender(ImageEventArgs::ImageB);
        if (render != NULL)
        {
            render->setData(data);
        }
    }
    break;
    }
}

GLRender* GLImageView::glRender(ImageEventArgs::ImageType type)
{
    auto it = m_Renders.find(type);
    if (it != m_Renders.end())
    {
        return it.value();
    }

    return NULL;
}

void GLImageView::onEcgActived(SonoParameters* sonoparameters)
{
    if (NULL == sonoparameters)
    {
        return;
    }

    EcgRender* ecgRender = static_cast<EcgRender*>(glRender(ImageEventArgs::ImageECG));
    bool isECGOn = sonoparameters->pBV(BFPNames::ECGEnStr);
    if (ecgRender != NULL)
    {
        ecgRender->setActive(isECGOn);
        if (isECGOn)
        {
            ecgRender->clearData(sonoparameters->pIV(BFPNames::ActiveBStr));
            ecgRender->reorganizeShaders();
            ecgRender->updateECGPosLevel(sonoparameters->pIV(BFPNames::ActiveBStr),
                                         sonoparameters->pIV(BFPNames::ECGPosStr));
            ecgRender->ecgInvertChanged(sonoparameters->pIV(BFPNames::ActiveBStr),
                                        !sonoparameters->pIV(BFPNames::ECGInvertStr));

            int layout = sonoparameters->pIV(BFPNames::LayoutStr);
            for (int i = 0; i < layout; ++i)
            {
                ecgRender->setIsRealTime(i, false);
                SonoParameters* sp = m_LineBufferManager->getSonoParametersByLayoutIndex(i);
                if (sp != NULL)
                {
                    m_EcgPosLevel.insert(i, sp->pIV(BFPNames::ECGPosStr));
                    m_EcgInvert.insert(i, sp->pBV(BFPNames::ECGInvertStr));
                }
            }
            ecgRender->resetEcgData(sonoparameters->pIV(BFPNames::ActiveBStr));
            ecgRender->resetProcessData();
        }
    }
}
