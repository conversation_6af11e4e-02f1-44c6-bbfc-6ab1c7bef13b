#ifndef IBUFFERPUSHER_H
#define IBUFFERPUSHER_H

#include "datatransfer_global.h"
#include <QObject>
#include <QList>
#include "frameinfo.h"
#include "bufferunit.h"

class SonoParameters;

/**
 * @brief The IBufferPusher class
 *
 * 后台线程从缓存中取数据，推给zeus
 */
class USF_INTERFACE_IMAGING_DATATRANSFERSHARED_EXPORT IBufferPusher : public QObject
{
    Q_OBJECT
public:
    explicit IBufferPusher(QObject* parent = 0);

    virtual ~IBufferPusher();

    virtual bool isRunning() const = 0;

    virtual void invokeReset() = 0;

    virtual void invokeResume() = 0;

    virtual void invokeSetCurrentIndex(bool isMeasureData, const int index, bool pass = false,
                                       bool persistenceReset = true) = 0;

    virtual void setImageBufferIndex(const int imageBufferIndex) = 0;

    virtual void setFrameType(const int frameType) = 0;

    virtual int frameType() const = 0;

    virtual void startWaitForFrozen() = 0;

    virtual void stopWaitForFrozen() = 0;

    virtual void invokeImageShapeUnstable() = 0;

    virtual void invokeImageShapeStable() = 0;

    virtual void invokeCacheQueueCleared() = 0;

    virtual void beforeSonoParameterChanged() = 0;

    virtual void clearSonoParameter() = 0;

    virtual void sonoParameterChanged(SonoParameters* sonoParameters) = 0;

    virtual void invokeRequestFlushCurrentImage() = 0;

    virtual void invokeCineFpsChanged(float multiplicator) = 0;

signals:
    void imageChanged(const QList<BufferUnit>& bufferUnits, const FrameUnitInfo& frameUnitInfo, IBufferPusher*);

    void requestFlush();

    void flushedAllImages(const int flushedType);
};

#endif // IBUFFERPUSHER_H
