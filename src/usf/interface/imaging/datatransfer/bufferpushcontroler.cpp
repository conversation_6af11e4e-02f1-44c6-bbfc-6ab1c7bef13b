#include "bufferpushcontroler.h"
#include "timerthread.h"
#include "isonobuffer.h"
#include "imagebufferdef.h"
#include "bfpnames.h"
#include "sonoparameters.h"
#include "optimizethread.h"
#include "deliverinvoker.h"
#include "ibufferpusher.h"
#include <QDebug>
#include "memoryleakcheck.h"
#include "assertlog.h"
#if defined(USE_ALSA) || defined(USE_QT_AUDIO)
#include "soundplayerinvoker.h"
#endif

BufferPushControler::BufferPushControler(ISonoBuffer* iSonoBuffer)
    : m_ISonoBuffer(iSonoBuffer)
    , m_IsPlayCineIng(false)
    , m_Is<PERSON><PERSON>zen(false)
    , m_IsActive(false)
    , m_BufferPusherIndex(iSonoBuffer->bufferIndex())
    , m_WaitForFrozen(false)
{
    qRegisterMetaType<BufferUnit>("BufferUnit");
    qRegisterMetaType<BufferUnit>("BufferUnit&");
    qRegisterMetaType<QList<BufferUnit>>("QList<BufferUnit>");
    qRegisterMetaType<QList<BufferUnit>>("QList<BufferUnit>&");
    qRegisterMetaType<FrameUnitInfo>("FrameUnitInfo");
    qRegisterMetaType<FrameUnitInfo>("FrameUnitInfo&");

    createBufferPusher();

    connect(m_ISonoBuffer, SIGNAL(beforeBufferCleared()), this, SLOT(onBeforeBuffersChanged()), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(bufferClearing()), this, SLOT(onBufferChanged()), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(changeFrameTypes(QHash<int, int>, bool)), this,
            SLOT(onFrameTypesChanged(QHash<int, int>, bool)), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(clearFrameTypes()), this, SLOT(onClearFrameTypes()), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(freezeChanged(bool)), this, SLOT(onFreezeChanged(bool)), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(playCineStatusChanged(bool)), this, SLOT(onPlayCineStatusChanged(bool)),
            Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(beforeBuffersChanged(int)), this, SLOT(onBeforeBuffersChanged(int)),
            Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(buffersChanged(int)), this, SLOT(onBufferChanged(int)), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(timerStart(int)), this, SLOT(onTimerStart(int)), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(changeFps(int, double)), this, SLOT(onFpsChange(int, double)), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(changeWaveDisplayFps(int, double)), this, SLOT(onWaveDisplayFpsChange(int, double)),
            Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(beforeSonoParametersChanged()), this, SLOT(onBeforeSonoParameterChanged()),
            Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(clearSonoParameters()), this, SLOT(onClearSonoParameters()), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(sonoParametersChanged()), this, SLOT(onSonoParameterChanged()), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(activeChanged(bool, int)), this, SLOT(onActiveChanged(bool, int)),
            Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(currentIndexChanged(int, int, bool)), this,
            SLOT(onCurrentIndexChanged(int, int, bool)), Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(requestFlushNextBuffer(int)), this, SLOT(onRequestFlushCurrentImage(int)),
            Qt::DirectConnection);
    connect(m_ISonoBuffer, SIGNAL(cacheQueueCleard(int)), this, SLOT(onCacheQueueCleard(int)), Qt::DirectConnection);
}

BufferPushControler::~BufferPushControler()
{
    {
        ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::writeLock);
        m_BufferPushers.clear();
    }
    CHECK_DELETE_LIST(IBufferPusher, m_BufferPusherPool.values());
    qDeleteAll(m_BufferPusherPool.values());
    m_BufferPusherPool.clear();
    CHECK_DELETE_LIST(TimerThread, m_TimerThreads);
    foreach (TimerThread* thread, m_TimerThreads)
    {
        thread->quit();
        thread->wait();
    }
    qDeleteAll(m_TimerThreads);
    m_TimerThreads.clear();
}

ISonoBuffer* BufferPushControler::getISonoBuffer()
{
    return m_ISonoBuffer;
}

void BufferPushControler::startWaitForFrozen()
{
    m_FlushedTypesLock.lock();
    m_FlushedTypes.clear();
    m_FlushedTypesLock.unlock();
    m_WaitForFrozen = true;
    //    QMutexLocker bufferPusherslocker(&m_BufferPushersMutex);  //
    //    此处不能加锁，频谱模式下触发的BufferPushControler::onFlushedAllImages已经加了锁

#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION << "m_FlushedTypes:" << m_FlushedTypes
             << "m_BufferPushers.count:" << m_BufferPushers.count();
#endif
    foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
    {
        iBufferPusher->startWaitForFrozen();
        if (!iBufferPusher->isRunning())
        {
            m_FlushedTypesLock.lock();
            m_FlushedTypes.insert(iBufferPusher->frameType());
            m_FlushedTypesLock.unlock();
        }
    }
}

void BufferPushControler::haltProcessImageChanged()
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << " BufferIndex:" << m_BufferPusherIndex;
#endif
    m_IsActive = false;
}

void BufferPushControler::recoveryProcessImageChanged()
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << " BufferIndex:" << m_BufferPusherIndex;
#endif
    m_IsActive = true;
}

void BufferPushControler::onCurrentIndexChanged(const int frameIndex, const int bufferIndex, bool isMeasureData)
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << "BufferIndex:" << m_BufferPusherIndex << "m_IsActive:" << m_IsActive
             << "bufferIndex:" << bufferIndex << "frameIndex:" << frameIndex;
#endif
    int framgeAvgCount = m_ISonoBuffer->frameAvgCount();
    if (m_IsActive && (framgeAvgCount >= 0))
    {
        ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
        if (bufferIndex >= 0 && m_BufferPushers.contains(bufferIndex))
        {
            if (frameIndex == -1 || framgeAvgCount == 0 ||
                (ImageBufferDef::B_Data != m_BufferPushers[bufferIndex]->frameType()))
            {
                m_BufferPushers[bufferIndex]->invokeSetCurrentIndex(isMeasureData, frameIndex);
            }
            else
            {
                int startIndex = frameIndex >= framgeAvgCount - 1 ? frameIndex - framgeAvgCount + 1 : 0;
                for (int i = startIndex; i <= frameIndex; ++i)
                {
                    m_BufferPushers[bufferIndex]->invokeSetCurrentIndex(isMeasureData, i, frameIndex != i,
                                                                        startIndex == i);
                }
            }
        }
        else
        {
            foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
            {
                if (frameIndex == -1 || framgeAvgCount == 0 || (ImageBufferDef::B_Data != iBufferPusher->frameType()))
                {
                    iBufferPusher->invokeSetCurrentIndex(isMeasureData, frameIndex);
                }
                else
                {
                    // int startIndex = frameIndex >= m_FramgeAvgCount - 1 ? frameIndex - m_FramgeAvgCount + 1 : 0;
                    int startIndex = frameIndex >= framgeAvgCount ? frameIndex - framgeAvgCount : 0;
                    for (int i = startIndex; i <= frameIndex; ++i)
                    {
                        iBufferPusher->invokeSetCurrentIndex(isMeasureData, i, frameIndex != i, startIndex == i);
                    }
                }
            }
        }
    }
}

void BufferPushControler::onRequestFlushCurrentImage(const int imageBufferIndex)
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << " BufferIndex:" << m_BufferPusherIndex << " m_IsActive:" << m_IsActive
             << " imageBufferIndex:" << imageBufferIndex;
#endif
    if (m_IsActive)
    {
        ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
        IBufferPusher* iBufferPusher = m_BufferPushers.value(imageBufferIndex, NULL);
        if (iBufferPusher != NULL)
        {
            iBufferPusher->invokeRequestFlushCurrentImage();
        }
    }
}

void BufferPushControler::onBeforeSonoParameterChanged()
{
    foreach (IBufferPusher* iBufferPusher, m_BufferPusherPool)
    {
        iBufferPusher->beforeSonoParameterChanged();
    }
    onClearFrameTypes();
}

void BufferPushControler::onClearSonoParameters()
{
    foreach (IBufferPusher* iBufferPusher, m_BufferPusherPool)
    {
        iBufferPusher->clearSonoParameter();
    }
    onClearFrameTypes();
}

void BufferPushControler::onSonoParameterChanged()
{
    foreach (IBufferPusher* iBufferPusher, m_BufferPusherPool)
    {
        iBufferPusher->sonoParameterChanged(m_ISonoBuffer->sonoParameters());
    }
}

void BufferPushControler::onImageShapeStable()
{
    ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
    foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
    {
        iBufferPusher->invokeImageShapeStable();
    }
}

void BufferPushControler::onImageShapeUnstable()
{
    ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
    foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
    {
        iBufferPusher->invokeImageShapeUnstable();
    }
}

void BufferPushControler::onPlayCineStatusChanged(const bool playCineStatus)
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << " BufferIndex:" << m_BufferPusherIndex << " m_IsActive:" << m_IsActive
             << " m_IsPlayCineIng:" << m_IsPlayCineIng << " playCineStatus:" << playCineStatus;
#endif
    ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
    if (m_IsActive)
    {
        m_IsPlayCineIng = playCineStatus;
        if (m_IsPlayCineIng)
        {
            foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
            {
                if (!iBufferPusher->isRunning())
                {
                    iBufferPusher->invokeResume();
                }
            }
        }
        else
        {
            foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
            {
                iBufferPusher->invokeReset();
            }
        }
    }
    else
    {
        foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
        {
            iBufferPusher->invokeReset();
        }
    }
}

void BufferPushControler::onCineFpsChanged(float multiplicator)
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << " BufferIndex:" << m_BufferPusherIndex
             << " multiplicator:" << multiplicator;
#endif
    ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
    foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
    {
        iBufferPusher->invokeCineFpsChanged(multiplicator);
    }
}

void BufferPushControler::onFreezeChanged(const bool isFrozen)
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << " BufferIndex:" << m_BufferPusherIndex << " m_IsActive:" << m_IsActive
             << " m_IsFrozen:" << m_IsFrozen << " isFrozen:" << isFrozen;
#endif
    m_IsFrozen = isFrozen;
    //冻结
    if (m_IsFrozen)
    {
        ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);

        foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
        {
            iBufferPusher->invokeReset();
        }
    }
    else //解冻
    {
        if (m_IsActive)
        {
            ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
            foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
            {
                iBufferPusher->invokeReset();
                iBufferPusher->invokeResume();
            }
        }
    }
}

void BufferPushControler::onClearFrameTypes()
{
    ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::writeLock);
    foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
    {
        iBufferPusher->invokeReset();
    }
    m_BufferPushers.clear();
}

void BufferPushControler::onFrameTypesChanged(const QHash<int, int> frameTypes, const bool isPlayCine)
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << "m_BufferPusherIndex:" << m_BufferPusherIndex
             << "isPlayCine:" << isPlayCine << "frameTypes:" << frameTypes;
#endif
    m_IsActive = true;
    ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::writeLock);
    foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
    {
        iBufferPusher->invokeReset();
    }
    m_BufferPushers.clear();
    for (QHash<int, int>::const_iterator iter = frameTypes.begin(); iter != frameTypes.end(); iter++)
    {
        if ((iter.value() == ImageBufferDef::Whole_Data) || (iter.value() == ImageBufferDef::CurvedPanoramic_Data) ||
            (iter.value() == ImageBufferDef::B_Data) || (iter.value() == ImageBufferDef::FourD_Data))
        {
            Q_ASSERT(m_BufferPusherPool.contains(OptThread));
            m_BufferPushers.insert(iter.key(), m_BufferPusherPool.value(OptThread));
        }
        else if (iter.value() == ImageBufferDef::ECG_Data)
        {
            Q_ASSERT(m_BufferPusherPool.contains(EcgThread));
            m_BufferPushers.insert(iter.key(), m_BufferPusherPool.value(EcgThread));
        }
        else if (iter.value() == ImageBufferDef::Sound_Data)
        {
#if defined(USE_ALSA) || defined(USE_QT_AUDIO)
            if (m_BufferPusherIndex == 0)
            {
                Q_ASSERT(m_BufferPusherPool.contains(SoundThread));
                m_BufferPushers.insert(iter.key(), m_BufferPusherPool.value(SoundThread));
            }
            else
            {
                continue;
            }
#else
            continue;
#endif
        }
        else if ((iter.value() == ImageBufferDef::M_Data) || (iter.value() == ImageBufferDef::D_Data) ||
                 (iter.value() == ImageBufferDef::CWD_Data) || (iter.value() == ImageBufferDef::CM_Data) ||
                 (iter.value() == ImageBufferDef::Elasto_Data))
        {
            Q_ASSERT(m_BufferPusherPool.contains(WaveThread));
            m_BufferPushers.insert(iter.key(), m_BufferPusherPool.value(WaveThread));
        }
        else
        {
            Q_ASSERT(false);
        }
        m_BufferPushers.value(iter.key())->setImageBufferIndex(iter.key());
        m_BufferPushers.value(iter.key())->setFrameType(iter.value());
        m_BufferPushers.value(iter.key())->invokeReset();
        if (!isPlayCine)
        {
            m_BufferPushers.value(iter.key())->invokeResume();
        }
    }
}

void BufferPushControler::onTimerStart(const int imageBufferIndex)
{
    if (m_IsActive)
    {
        //冻结播放电影 或 实时打图
        if ((m_IsFrozen && m_IsPlayCineIng) || (!m_IsFrozen))
        {
            //            ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
            IBufferPusher* iBufferPusher = m_BufferPushers.value(imageBufferIndex, NULL);
            DeliverInvoker* deliverInvoker = dynamic_cast<DeliverInvoker*>(iBufferPusher);
            if ((deliverInvoker != NULL) && (!deliverInvoker->isTimerActive()))
            {
                deliverInvoker->invokeTimerStart();
            }
        }
    }
}

void BufferPushControler::onCacheQueueCleard(const int imageBufferIndex)
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << "m_BufferPusherIndex:" << m_BufferPusherIndex
             << "imageBufferIndex:" << imageBufferIndex << "m_IsActive:" << m_IsActive;
#endif
    if (m_IsActive)
    {
        //        ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
        IBufferPusher* iBufferPusher = m_BufferPushers.value(imageBufferIndex, NULL);
        if (iBufferPusher != NULL)
        {
            iBufferPusher->invokeCacheQueueCleared();
        }
    }
}

void BufferPushControler::onFpsChange(const int imageBufferIndex, double fps)
{
    if (m_IsActive)
    {
        ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
        IBufferPusher* iBufferPusher = m_BufferPushers.value(imageBufferIndex, NULL);
        DeliverInvoker* deliverInvoker = dynamic_cast<DeliverInvoker*>(iBufferPusher);
        if (deliverInvoker != NULL)
        {
            deliverInvoker->invokeFpsChange(fps);
        }
    }
}

void BufferPushControler::onWaveDisplayFpsChange(const int imageBufferIndex, double fps)
{
    if (m_IsActive)
    {
        ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
        IBufferPusher* iBufferPusher = m_BufferPushers.value(imageBufferIndex, NULL);
        ECGDeliverInvoker* deliverInvoker = dynamic_cast<ECGDeliverInvoker*>(iBufferPusher);
        if (deliverInvoker != NULL)
        {
            deliverInvoker->invokeWaveDisplayFpsChange(fps);
        }
    }
}

void BufferPushControler::onBeforeBuffersChanged()
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << "m_BufferPusherIndex:" << m_BufferPusherIndex << "invokeReset";
#endif
    ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
    foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
    {
        iBufferPusher->invokeReset();
    }
    haltProcessImageChanged();
}

void BufferPushControler::onBufferChanged()
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    qDebug() << "?????" << PRETTY_FUNCTION << "m_BufferPusherIndex:" << m_BufferPusherIndex << "invokeResume";
#endif
    ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
    foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
    {
        iBufferPusher->invokeResume();
    }
    recoveryProcessImageChanged();
}
void BufferPushControler::onBeforeBuffersChanged(int sonobufferIndex)
{
    onBeforeBuffersChanged();
}

void BufferPushControler::onBufferChanged(int sonobufferIndex)
{
    onBufferChanged();
}

void BufferPushControler::onImageChanged(const QList<BufferUnit>& bufferUnits, const FrameUnitInfo& frameUnitInfo,
                                         IBufferPusher* iBufferPusher)
{
    //#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    //    qDebug()<<"?????"<<PRETTY_FUNCTION
    //            <<" m_IsActive:"<<m_IsActive
    //            <<" BufferIndex:"<<m_BufferPusherIndex
    //            <<" FrameIndex:"<<bufferUnits.first().frameIndex()
    //            <<" Format:"<<frameUnitInfo.first().mode()
    //            <<" EcgEnd:"<<bufferUnits.first().EcgEnd()
    //            <<" IBufferPusher Running:"<<iBufferPusher->isRunning()
    //            <<" m_IsFrozen:"<<m_IsFrozen
    //            <<" data:"<<(quintptr)bufferUnits.first().first().data();
    //#endif
    if (m_IsActive && (iBufferPusher->isRunning() || m_IsFrozen))
    {
        emit imageChanged(bufferUnits, frameUnitInfo);
    }
    else
    {
        qCritical() << PRETTY_FUNCTION << " error m_IsActive:" << m_IsActive;
    }
}

void BufferPushControler::onActiveChanged(const bool activeStatus, const int rawDataMode)
{
    if ((m_BufferPusherIndex == 0) &&
        (m_BufferPusherPool.contains(SoundThread) && rawDataMode == ImageBufferDef::Sound_Data))
    {

        IBufferPusher* sound = m_BufferPusherPool.value(SoundThread);
        if (activeStatus)
        {
            sound->invokeReset();
            sound->invokeResume();
        }
        else
        {
            sound->invokeReset();
        }
    }
}

void BufferPushControler::onFlushedAllImages(const int flushedType)
{
    if (!m_WaitForFrozen)
    {
        return;
    }

    int bufferPushCount = 0;

    {
        ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
        bufferPushCount = m_BufferPushers.count();
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
        if (!m_FlushedTypes.contains(flushedType))
        {
            QMutexLocker bufferPusherslocker(&m_BufferPushersMutex);
            qDebug() << PRETTY_FUNCTION << "flushedType:" << flushedType << "m_FlushedTypes:" << m_FlushedTypes
                     << "m_BufferPushers.count:" << m_BufferPushers.count();
        }
#endif
    }

    m_FlushedTypesLock.lock();
    m_FlushedTypes.insert(flushedType);
    int flushedTypesCount = m_FlushedTypes.count();
    m_FlushedTypesLock.unlock();
    if (flushedTypesCount == bufferPushCount)
    {
        m_WaitForFrozen = false;

        {
            ReadWriteLocker locker(&m_BufferPushersReadWriteLock, ReadWriteLocker::readLock);
            foreach (IBufferPusher* iBufferPusher, m_BufferPushers)
            {
                iBufferPusher->stopWaitForFrozen();
            }
        }

        m_FlushedTypesLock.lock();
        m_FlushedTypes.clear();
        m_FlushedTypesLock.unlock();
        emit flushedAllImages();
    }
}

void BufferPushControler::createBufferPusher()
{
    TimerThread* tThread = new TimerThread("bufferPushThread");
    m_TimerThreads.append(tThread);
    tThread->start();
    IBufferPusher* iBufferPusher = new OptimizeThread(m_BufferPusherIndex, m_ISonoBuffer, tThread);
    m_BufferPusherPool.insert(OptThread, iBufferPusher);
    connect(iBufferPusher, SIGNAL(imageChanged(const QList<BufferUnit>&, const FrameUnitInfo&, IBufferPusher*)), this,
            SLOT(onImageChanged(const QList<BufferUnit>&, const FrameUnitInfo&, IBufferPusher*)), Qt::DirectConnection);
    connect(iBufferPusher, SIGNAL(fpsChanged(const QString&, const float)), this,
            SIGNAL(fpsChanged(const QString&, const float)), Qt::DirectConnection);
    connect(iBufferPusher, SIGNAL(requestFlush()), this, SIGNAL(requestFlush()));
    connect(iBufferPusher, SIGNAL(flushedAllImages(const int)), this, SLOT(onFlushedAllImages(const int)),
            Qt::DirectConnection);
    CHECK_NEW(TimerThread, tThread);
    CHECK_NEW(IBufferPusher, iBufferPusher);

    tThread = new TimerThread("ContinuousDeliverInvoker");
    m_TimerThreads.append(tThread);
    tThread->start();
    iBufferPusher = new ContinuousDeliverInvoker(m_ISonoBuffer, tThread);
    m_BufferPusherPool.insert(WaveThread, iBufferPusher);
    connect(iBufferPusher, SIGNAL(imageChanged(const QList<BufferUnit>&, const FrameUnitInfo&, IBufferPusher*)), this,
            SLOT(onImageChanged(const QList<BufferUnit>&, const FrameUnitInfo&, IBufferPusher*)), Qt::DirectConnection);
    connect(iBufferPusher, SIGNAL(flushedAllImages(const int)), this, SLOT(onFlushedAllImages(const int)),
            Qt::DirectConnection);
    CHECK_NEW(TimerThread, tThread);
    CHECK_NEW(IBufferPusher, iBufferPusher);

    tThread = new TimerThread("ECGDeliverInvoker");
    m_TimerThreads.append(tThread);
    tThread->start();
    iBufferPusher = new ECGDeliverInvoker(m_ISonoBuffer, tThread);
    m_BufferPusherPool.insert(EcgThread, iBufferPusher);
    connect(iBufferPusher, SIGNAL(imageChanged(const QList<BufferUnit>&, const FrameUnitInfo&, IBufferPusher*)), this,
            SLOT(onImageChanged(const QList<BufferUnit>&, const FrameUnitInfo&, IBufferPusher*)), Qt::DirectConnection);
    connect(iBufferPusher, SIGNAL(flushedAllImages(const int)), this, SLOT(onFlushedAllImages(const int)),
            Qt::DirectConnection);
    CHECK_NEW(TimerThread, tThread);
    CHECK_NEW(IBufferPusher, iBufferPusher);
#if defined(USE_ALSA) || defined(USE_QT_AUDIO)
    if (m_BufferPusherIndex == 0)
    {
        tThread = new TimerThread("SoundPlayerInvoker");
        m_TimerThreads.append(tThread);
        tThread->start();
        iBufferPusher = new SoundPlayerInvoker(m_ISonoBuffer, tThread);
        m_BufferPusherPool.insert(SoundThread, iBufferPusher);
        connect(iBufferPusher, SIGNAL(flushedAllImages(const int)), this, SLOT(onFlushedAllImages(const int)),
                Qt::DirectConnection);
        CHECK_NEW(TimerThread, tThread);
        CHECK_NEW(IBufferPusher, iBufferPusher);
    }
#endif
}

ReadWriteLocker::ReadWriteLocker(QReadWriteLock* lock, ReadWriteLocker::LockMode mode)
    : m_Lock(lock)
{
    switch (mode)
    {
    case readLock:
        m_Lock->lockForRead();
        break;
    case writeLock:
        m_Lock->lockForWrite();
        break;
    default:
        m_Lock->lockForRead();
        break;
    }
}

ReadWriteLocker::~ReadWriteLocker()
{
    if (nullptr != m_Lock)
    {
        m_Lock->unlock();
    }
}
