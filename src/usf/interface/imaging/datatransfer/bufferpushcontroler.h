#ifndef BUFFERPUSHCONTROLER_H
#define BUFFERPUSHCONTROLER_H

#include "datatransfer_global.h"
#include "ibufferpushcontroler.h"
#include <QList>
#include <QHash>
#include <QSet>
#include <QMutex>
#include <QReadWriteLock>
/**
 * @brief 负责管理OptimizeThread、DeliverInvoker
 * 对应一个ISonoBuffer，对应一个GstImageTile
 *
 */
class TimerThread;
class IBufferPusher;

class ReadWriteLocker
{
public:
    enum LockMode
    {
        readLock,
        writeLock
    };
    ReadWriteLocker(QReadWriteLock* lock, LockMode mode);
    ~ReadWriteLocker();

private:
    QReadWriteLock* m_Lock;
};

class USF_INTERFACE_IMAGING_DATATRANSFERSHARED_EXPORT BufferPushControler : public IBufferPushControler
{
    Q_OBJECT
    enum BufferPushThreadType
    {
        OptThread,
        WaveThread,
        EcgThread,
        SoundThread
    };

public:
    explicit BufferPushControler(ISonoBuffer* iSonoBuffer);
    ~BufferPushControler();

    virtual ISonoBuffer* getISonoBuffer();

    void startWaitForFrozen();

    void haltProcessImageChanged();
    void recoveryProcessImageChanged();

public slots:
    /**
     * @brief 在回放电影的时候，通过此方法切换图像
     * @param index
     */
    void onCurrentIndexChanged(const int frameIndex, const int bufferIndex, bool isMeasureData);

    void onRequestFlushCurrentImage(const int imageBufferIndex);
    /**
     * @brief
     * @param playBack
     */
    void onPlayCineStatusChanged(const bool playCineStatus);
    /**
     * @brief 切换冻结非冻结状态
     * @param isFrozen
     */
    void onFreezeChanged(const bool isFrozen);

    void onCineFpsChanged(float multiplicator);

    void onSonoParameterChanged();

    void onImageShapeUnstable();

    void onImageShapeStable();

private slots:
    void onBeforeSonoParameterChanged();

    void onClearSonoParameters();

    void onClearFrameTypes();
    /**
     * @brief 响应ISonoBuffer中的帧类型变化
     * @param frameTypes
     * @param isPlayback 标记是否是回放数据
     */
    void onFrameTypesChanged(const QHash<int, int> frameTypes, const bool isPlayCine);
    /**
     * @brief onTimerStart 开启线程的运行
     */
    void onTimerStart(const int imageBufferIndex);
    /**
     * @brief onCacheQueueCleard cache size cleared
     */
    void onCacheQueueCleard(const int imageBufferIndex);
    /**
     * @brief onTimerStart 开启线程的运行
     */
    void onFpsChange(const int imageBufferIndex, double fps);
    /**
     * @brief onTimerStart 开启线程的运行
     */
    void onWaveDisplayFpsChange(const int imageBufferIndex, double fps);
    /**
     * @brief 销毁ImageBuffer
     */
    void onBeforeBuffersChanged(int sonobufferIndex);

    void onBufferChanged(int sonobufferIndex);

    /**
     * @brief 销毁ImageBuffer
     */
    void onBeforeBuffersChanged();

    void onBufferChanged();

    void onImageChanged(const QList<BufferUnit>& bufferUnits, const FrameUnitInfo& frameUnitInfo,
                        IBufferPusher* iBufferPusher);

    void onActiveChanged(const bool activeStatus, const int rawDataMode);

    void onFlushedAllImages(const int flushedType);

private:
    /**
     * @brief 建立TimerThreads
     */
    void createBufferPusher();

    ISonoBuffer* m_ISonoBuffer;
    /**
     * @brief QHash<BufferPushThreadType, IBufferPusher*>
     */
    QHash<int, IBufferPusher*> m_BufferPusherPool;
    /**
     * @brief QHash<ImageBufferIndex, IBufferPusher*>
     */
    QHash<int, IBufferPusher*> m_BufferPushers;

    QReadWriteLock m_BufferPushersReadWriteLock;

    QHash<int, bool> m_BufferPusherRunning;

    QList<TimerThread*> m_TimerThreads;

    bool m_IsPlayCineIng;
    bool m_IsFrozen;
    bool m_IsActive;
    int m_BufferPusherIndex;
    QSet<int> m_FlushedTypes;
    bool m_WaitForFrozen;
    QMutex m_FlushedTypesLock;
};

#endif // BUFFERPUSHCONTROLER_H
