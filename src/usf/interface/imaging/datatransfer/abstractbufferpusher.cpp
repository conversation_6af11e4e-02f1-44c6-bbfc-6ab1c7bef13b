#include "abstractbufferpusher.h"
#include "bfpnames.h"
#include "infostruct.h"
#include "irawimagebufferdatagetter.h"
#include "parameter.h"
#include "sonoparameters.h"
#include "util.h"
#include <QMetaMethod>
#include <QMetaObject>
#include <QThread>

AbstractBufferPusher::AbstractBufferPusher(IRawImageBufferDataGetter* bufferDataGetter, QThread* thread,
                                           QObject* parent)
    : IBufferPusher(parent)
    , m_BufferDataGetter(bufferDataGetter)
    , m_SonoParameters(NULL)
    , m_BufferIndex(-1)
    , m_IsRunning(false)
    , m_FrameType(ImageBufferDef::NULL_Data)
    , m_WaitForFrozen(false)
    , m_TimerThread(thread)
{
}

AbstractBufferPusher::~AbstractBufferPusher()
{
}

bool AbstractBufferPusher::isRunning() const
{
    return m_IsRunning;
}

void AbstractBufferPusher::invokeReset()
{
    if (m_WaitForFrozen)
    {
        qDebug() << PRETTY_FUNCTION << "flushedAllImages";
        emit flushedAllImages(m_FrameType);
    }
    m_IsRunning = false;
    if (QThread::currentThread() != m_TimerThread)
    {
        staticMetaObject.invokeMethod(this, "reset", Qt::BlockingQueuedConnection);
    }
    else
    {
        reset();
    }
}

void AbstractBufferPusher::invokeResume()
{
    if (QThread::currentThread() != m_TimerThread)
    {
        staticMetaObject.invokeMethod(this, "resume", Qt::BlockingQueuedConnection);
    }
    else
    {
        resume();
    }
}

void AbstractBufferPusher::invokeSetCurrentIndex(bool isMeasureData, const int index, bool pass, bool persistenceReset)
{
    if (QThread::currentThread() != m_TimerThread)
    {
        staticMetaObject.invokeMethod(this, "setCurrentIndex", Qt::BlockingQueuedConnection, Q_ARG(bool, isMeasureData),
                                      Q_ARG(int, index), Q_ARG(bool, pass), Q_ARG(bool, persistenceReset));
    }
    else
    {
        setCurrentIndex(isMeasureData, index, pass, persistenceReset);
    }
}

void AbstractBufferPusher::setImageBufferIndex(const int imageBufferIndex)
{
    m_BufferIndex = imageBufferIndex;
}

void AbstractBufferPusher::setFrameType(const int frameType)
{
    m_FrameType = frameType;
}

int AbstractBufferPusher::frameType() const
{
    return m_FrameType;
}

void AbstractBufferPusher::startWaitForFrozen()
{
    QMutexLocker locker(&m_SyncMutex);
    m_WaitForFrozen = true;
    onSetStartWaitForFrozen();
}

void AbstractBufferPusher::stopWaitForFrozen()
{
    m_WaitForFrozen = false;
}

void AbstractBufferPusher::invokeImageShapeStable()
{
    if (QThread::currentThread() != m_TimerThread)
    {
        staticMetaObject.invokeMethod(this, "onImageShapeStable", Qt::BlockingQueuedConnection);
    }
    else
    {
        onImageShapeStable();
    }
}

void AbstractBufferPusher::invokeCacheQueueCleared()
{
}

void AbstractBufferPusher::invokeImageShapeUnstable()
{
    if (QThread::currentThread() != m_TimerThread)
    {
        staticMetaObject.invokeMethod(this, "onImageShapeUnstable", Qt::BlockingQueuedConnection);
    }
    else
    {
        onImageShapeUnstable();
    }
}

void AbstractBufferPusher::reset()
{
    onReset();
}

void AbstractBufferPusher::resume()
{
    if (m_SonoParameters != NULL)
    {
        m_IsRunning = true;
        onResume();
    }
    else
    {
        qDebug() << PRETTY_FUNCTION << "m_SonoParameters:" << (quintptr)m_SonoParameters;
    }
}

void AbstractBufferPusher::setCurrentIndex(bool isMeasureData, const int currentIndex, bool pass,
                                           bool passpersistenceReset)
{
    QList<BufferUnit> bufferUnits;
    FrameUnitInfo frameUnitInfo;
    int actualFlush = m_BufferDataGetter->getBufferByIndex(bufferUnits, frameUnitInfo, m_BufferIndex, currentIndex);
    frameUnitInfo.setNeedPushMultipleFrames(true);
    QDeadlineTimer deadline(1000);
    while (actualFlush == IRawImageBufferDataGetter::BufferIsLocked)
    {
        //超时处理
        if (deadline.hasExpired())
        {
            qCritical() << "getBuffer operation timed out" << PRETTY_FUNCTION;
            break;
        }
        actualFlush = m_BufferDataGetter->getBufferByIndex(bufferUnits, frameUnitInfo, m_BufferIndex, currentIndex);
        Util::usleep(1);
    }

    if (actualFlush > 0 && frameUnitInfo.size() > 0)
    {
        frameUnitInfo.m_persistenceReset = passpersistenceReset;
        bufferUnits.last().setByPass(pass);

        int partition = m_SonoParameters->pIV(BFPNames::ImageRenderPartitionStr);

        bufferUnits.last().setImageRenderPartition(partition);
        bufferUnits.last().setMeasureData(isMeasureData);

        //选帧操作由UI更新index，不再由zeus回调来更新
        frameUnitInfo.setNeedUpdateForzonIndex(false);
        disposeImageBufferData(bufferUnits, frameUnitInfo);
    }
}

void AbstractBufferPusher::onImageShapeStable()
{
}

bool AbstractBufferPusher::isFrozen() const
{
    if (m_SonoParameters != NULL)
    {
        return m_SonoParameters->pBV(BFPNames::FreezeStr);
    }
    return false;
}

void AbstractBufferPusher::onImageShapeUnstable()
{
}

void AbstractBufferPusher::beforeSonoParameterChanged()
{
    onBeforeSonoParameterChanged();
    if (m_SonoParameters == NULL)
    {
        return;
    }
    disconnect(m_SonoParameters->parameter(BFPNames::PWSoundDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onPWSoundDelayTimeChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::PWWaveImageDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onPWWaveImageDelayTimeChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::PW2DImageDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onPW2DImageDelayTimeChanged(QVariant)));
    //    disconnect(m_SonoParameters->parameter(BFPNames::PWECGDelayTimeStr), SIGNAL(valueChanged(QVariant)),
    //               this, SLOT(onPWECGDelayTimeChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::MWaveImageDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onMWaveImageDelayTimeChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::M2DImageDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onM2DImageDelayTimeChanged(QVariant)));
    //    disconnect(m_SonoParameters->parameter(BFPNames::MECGDelayTimeStr), SIGNAL(valueChanged(QVariant)),
    //               this, SLOT(onMECGDelayTimeChanged(QVariant)));
    //    disconnect(m_SonoParameters->parameter(BFPNames::ECGDelayTimeStr), SIGNAL(valueChanged(QVariant)),
    //               this, SLOT(onECGDelayTimeChanged(QVariant)));
    //    disconnect(m_SonoParameters->parameter(BFPNames::ECG2DImageDelayTimeStr), SIGNAL(valueChanged(QVariant)),
    //               this, SLOT(onECG2DImageDelayTimeChanged(QVariant)));
}

void AbstractBufferPusher::onBeforeSonoParameterChanged()
{
}

void AbstractBufferPusher::clearSonoParameter()
{
    onClearSonoParameter();
    m_SonoParameters = NULL;
}

void AbstractBufferPusher::onClearSonoParameter()
{
}

void AbstractBufferPusher::sonoParameterChanged(SonoParameters* sonoParameters)
{
    m_SonoParameters = sonoParameters;
    onSonoParameterChanged();
    if (m_SonoParameters == NULL)
    {
        return;
    }
    connect(m_SonoParameters->parameter(BFPNames::PWSoundDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onPWSoundDelayTimeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::PWWaveImageDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onPWWaveImageDelayTimeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::PW2DImageDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onPW2DImageDelayTimeChanged(QVariant)));
    //    connect(m_SonoParameters->parameter(BFPNames::PWECGDelayTimeStr), SIGNAL(valueChanged(QVariant)),
    //            this, SLOT(onPWECGDelayTimeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::MWaveImageDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onMWaveImageDelayTimeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::M2DImageDelayTimeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onM2DImageDelayTimeChanged(QVariant)));
    //    connect(m_SonoParameters->parameter(BFPNames::MECGDelayTimeStr), SIGNAL(valueChanged(QVariant)),
    //            this, SLOT(onMECGDelayTimeChanged(QVariant)));
    //    connect(m_SonoParameters->parameter(BFPNames::ECGDelayTimeStr), SIGNAL(valueChanged(QVariant)),
    //            this, SLOT(onECGDelayTimeChanged(QVariant)));
    //    connect(m_SonoParameters->parameter(BFPNames::ECG2DImageDelayTimeStr), SIGNAL(valueChanged(QVariant)),
    //            this, SLOT(onECG2DImageDelayTimeChanged(QVariant)));
}

void AbstractBufferPusher::onSonoParameterChanged()
{
}

void AbstractBufferPusher::invokeRequestFlushCurrentImage()
{
    if (QThread::currentThread() != m_TimerThread)
    {
        staticMetaObject.invokeMethod(this, "onRequestFlushCurrentImage", Qt::BlockingQueuedConnection);
    }
    else
    {
        onRequestFlushCurrentImage();
    }
}

void AbstractBufferPusher::onRequestFlushCurrentImage()
{
    handleNextImageBufferData();
}

void AbstractBufferPusher::invokeCineFpsChanged(float multiplicator)
{
    if (QThread::currentThread() != m_TimerThread)
    {
        staticMetaObject.invokeMethod(this, "onCineFpsChanged", Qt::BlockingQueuedConnection,
                                      Q_ARG(const float, multiplicator));
    }
    else
    {
        onCineFpsChanged(multiplicator);
    }
}

void AbstractBufferPusher::onCineFpsChanged(float multiplicator)
{
}

void AbstractBufferPusher::onSetStartWaitForFrozen()
{
}

void AbstractBufferPusher::onPWSoundDelayTimeChanged(const QVariant& value)
{
    if (!m_IsRunning)
    {
        return;
    }
    onResume();
}

void AbstractBufferPusher::onPWWaveImageDelayTimeChanged(const QVariant& value)
{
    if (!m_IsRunning)
    {
        return;
    }
    onResume();
}

void AbstractBufferPusher::onPW2DImageDelayTimeChanged(const QVariant& value)
{
    if (!m_IsRunning)
    {
        return;
    }
    onResume();
}

// void AbstractBufferPusher::onPWECGDelayTimeChanged(const QVariant &value)
//{
//    if(!m_IsRunning)
//    {
//        return;
//    }
//    onResume();
//}

void AbstractBufferPusher::onMWaveImageDelayTimeChanged(const QVariant& value)
{
    if (!m_IsRunning)
    {
        return;
    }
    onResume();
}

void AbstractBufferPusher::onM2DImageDelayTimeChanged(const QVariant& value)
{
    if (!m_IsRunning)
    {
        return;
    }
    onResume();
}

// void AbstractBufferPusher::onMECGDelayTimeChanged(const QVariant &value)
//{
//    if(!m_IsRunning)
//    {
//        return;
//    }
//    onResume();
//}

// void AbstractBufferPusher::onECGDelayTimeChanged(const QVariant &value)
//{
//    if(!m_IsRunning)
//    {
//        return;
//    }
//    onResume();
//}

// void AbstractBufferPusher::onECG2DImageDelayTimeChanged(const QVariant &value)
//{
//    if(!m_IsRunning)
//    {
//        return;
//    }
//    onResume();
//}
