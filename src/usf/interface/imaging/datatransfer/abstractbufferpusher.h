#ifndef ABSTRACTBUFFERPUSHER_H
#define ABSTRACTBUFFERPUSHER_H

#include "datatransfer_global.h"
#include "ibufferpusher.h"
#include <QMutex>

class IRawImageBufferDataGetter;
class SonoParameters;

class USF_INTERFACE_IMAGING_DATATRANSFERSHARED_EXPORT AbstractBufferPusher : public IBufferPusher
{
    Q_OBJECT
public:
    AbstractBufferPusher(IRawImageBufferDataGetter* bufferDataGetter, QThread* thread, QObject* parent = 0);

    virtual ~AbstractBufferPusher();

    bool isRunning() const;

    void invokeReset();

    void invokeResume();

    void invokeSetCurrentIndex(bool isMeasureData, const int index, bool pass = false, bool persistenceReset = true);

    void setImageBufferIndex(const int imageBufferIndex);

    void setFrameType(const int frameType);

    int frameType() const;

    void startWaitForFrozen();

    void stopWaitForFrozen();

    void invokeImageShapeUnstable();

    void invokeImageShapeStable();

    void invokeCacheQueueCleared();

    void beforeSonoParameterChanged();

    void clearSonoParameter();

    void sonoParameterChanged(SonoParameters* sonoParameters);

    void invokeRequestFlushCurrentImage();

    void invokeCineFpsChanged(float multiplicator);

protected:
    Q_INVOKABLE virtual void reset();

    Q_INVOKABLE virtual void resume();

    Q_INVOKABLE virtual void setCurrentIndex(bool isMeasureData, const int currentIndex, bool pass = false,
                                             bool persistenceReset = true);

    Q_INVOKABLE virtual void onImageShapeUnstable();

    Q_INVOKABLE virtual void onImageShapeStable();

    bool isFrozen() const;

    virtual void onReset() = 0;

    virtual void onResume() = 0;

    virtual void disposeImageBufferData(QList<BufferUnit>& bufferUnits, FrameUnitInfo& frameUnitInfo) = 0;

    virtual void handleNextImageBufferData() = 0;

    virtual void onBeforeSonoParameterChanged();

    virtual void onClearSonoParameter();

    virtual void onSonoParameterChanged();

    Q_INVOKABLE virtual void onRequestFlushCurrentImage();

    Q_INVOKABLE virtual void onCineFpsChanged(float multiplicator);

    virtual void onSetStartWaitForFrozen();

private slots:
    void onPWSoundDelayTimeChanged(const QVariant& value);
    void onPWWaveImageDelayTimeChanged(const QVariant& value);
    void onPW2DImageDelayTimeChanged(const QVariant& value);
    // void onPWECGDelayTimeChanged(const QVariant& value);
    void onMWaveImageDelayTimeChanged(const QVariant& value);
    void onM2DImageDelayTimeChanged(const QVariant& value);
    //    void onMECGDelayTimeChanged(const QVariant& value);
    //    void onECGDelayTimeChanged(const QVariant& value);
    //    void onECG2DImageDelayTimeChanged(const QVariant& value);

protected:
    IRawImageBufferDataGetter* m_BufferDataGetter;
    SonoParameters* m_SonoParameters;
    QThread* m_TimerThread;
    QMutex m_SyncMutex;
    int m_BufferIndex;
    bool m_IsRunning;
    int m_FrameType;
    bool m_WaitForFrozen;
};

#endif // ABSTRACTBUFFERPUSHER_H
