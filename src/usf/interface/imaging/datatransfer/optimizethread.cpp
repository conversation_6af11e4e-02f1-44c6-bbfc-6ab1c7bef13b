/*
 * =====================================================================================
 *
 *       Filename:  OptimizeThread.cpp
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  2018-10-18
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:   (), |<EMAIL>|
 *   Organization:
 *
 * =====================================================================================
 */
#include "optimizethread.h"
#include "assertlog.h"
#include "bfpnames.h"
#include "infostruct.h"
#include "logger.h"
#include "parameter.h"
#include "sonoparameters.h"
#include <QDir>
#ifdef USE_4D
#include "fourDprocess.h"
#endif
#include "assertlog.h"
#include "fpscalculator.h"
#include "irawimagebufferdatagetter.h"
#include "memoryleakcheck.h"
#include "util.h"
#include "variantutil.h"
#include "modelconfig.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, OptimizeThread)

OptimizeThread::OptimizeThread(const int sonoBufferIndex, IRawImageBufferDataGetter* bufferDataGetter, QThread* thread)
    : AbstractBufferPusher(bufferDataGetter, thread)
    , m_Timer(NULL)
    , m_ImageOptimizationAlg(ImageOptimizationAlgEnum::Alg_iImage)
    , m_SonoBufferIndex(sonoBufferIndex)
    , m_TrapezoidalMode(false)
    , m_Delaying(false)
    , m_2DImageDelayTime(0)
{
#ifdef BUFFER_PUSH_CONTROLER_DEBUG_ENABLE
    log()->debug("create OptimizeThread");
#endif

    this->moveToThread(m_TimerThread);

    staticMetaObject.invokeMethod(this, "createOnSelfThread", Qt::BlockingQueuedConnection);
}

OptimizeThread::~OptimizeThread()
{
    staticMetaObject.invokeMethod(this, "releaseOnSelfThread", Qt::BlockingQueuedConnection);
}

void OptimizeThread::createOnSelfThread()
{
    m_FpsCalculator = new FpsCalculator();
    m_FpsCalculator->setAdditionInfo("BufferPush");
    m_FpsCalculator->setLogOut(true);
    connect(m_FpsCalculator, SIGNAL(update(const QString&, float)), this, SLOT(onFpsUpdate(const QString&, float)));
    m_Timer = new QTimer(this);
    CHECK_NEW(QTimer, m_Timer);
    connect(m_Timer, SIGNAL(timeout()), this, SLOT(handleTimeOut()));
    connect(this, SIGNAL(stopTimer()), m_Timer, SLOT(stop()));
    connect(this, SIGNAL(startTimer()), m_Timer, SLOT(start()));
}

void OptimizeThread::releaseOnSelfThread()
{
    disconnect(this);
    if (m_FpsCalculator != NULL)
    {
        delete m_FpsCalculator;
        m_FpsCalculator = NULL;
    }
    if (m_Timer != NULL)
    {
        m_Timer->stop();
        CHECK_DELETE(QTimer, m_Timer);
        delete m_Timer;
        m_Timer = NULL;
    }
}

void OptimizeThread::onReset()
{
    QMutexLocker locker(&m_SyncMutex);
    m_Timer->stop();
}

void OptimizeThread::onResume()
{
    if (m_2DImageDelayTime > 0)
    {
        qDebug() << "?????" << PRETTY_FUNCTION << "m_2DImageDelayTime:" << m_2DImageDelayTime;
        m_Delaying = true;
        m_Timer->start(m_2DImageDelayTime);
    }
    else
    {
        m_Timer->start(m_Timeout);
    }
}

void OptimizeThread::disposeImageBufferData(QList<BufferUnit>& bufferUnits, FrameUnitInfo& frameUnitInfo)
{
    //    m_FpsCalculator->cal();
    emit imageChanged(bufferUnits, frameUnitInfo, this);
}

void OptimizeThread::handleNextImageBufferData()
{
    FrameUnitInfo frameUnitInfo;
    QList<BufferUnit> bufferUnits;

    QMutexLocker locker(&m_SyncMutex);
    if (m_IsRunning && (m_BufferDataGetter->getNextBuffer(bufferUnits, frameUnitInfo, m_BufferIndex) >= 0))
    {
        if (!bufferUnits.isEmpty())
        {
            disposeImageBufferData(bufferUnits, frameUnitInfo);
        }
        if (m_WaitForFrozen)
        {
            emit flushedAllImages(m_FrameType);
        }
    }
}

void OptimizeThread::onImageShapeStable()
{
}

void OptimizeThread::onImageShapeUnstable()
{
}

void OptimizeThread::onBeforeSonoParameterChanged()
{
    disconnectSonoParameters();
}

void OptimizeThread::onClearSonoParameter()
{
    m_SonoParameters = NULL;
}

void OptimizeThread::onSonoParameterChanged()
{
    connectSonoParameters();
}

void OptimizeThread::handleTimeOut()
{
    emit stopTimer();

    if (m_Delaying)
    {
        m_Timer->start(m_Timeout);
        m_Delaying = false;
        return;
    }

    handleNextImageBufferData();

    if (m_IsRunning)
    {
        emit startTimer();
    }
}

void OptimizeThread::doOptimize(FrameUnitInfo& frameUnitInfo, QList<BufferUnit>& bufferUnits)
{
    if (frameUnitInfo.imageType() != ImageBufferDef::Image2D)
    {
        emit imageChanged(bufferUnits, frameUnitInfo, this);
        return;
    }

    if ((bufferUnits.first().first().len()) != (frameUnitInfo[0].pointNum() * frameUnitInfo[0].lineNum()))
    {
        emit imageChanged(bufferUnits, frameUnitInfo, this);
        return;
    }
#ifdef USE_4D
    if (frameUnitInfo[0].mode() == ImageBufferDef::FourD_Data)
    {
        emit imageChanged(bufferUnits, frameUnitInfo, this);
        return;
    }
#endif
    if (frameUnitInfo[0].mode() != ImageBufferDef::B_Data)
    {
        emit imageChanged(bufferUnits, frameUnitInfo, this);
        return;
    }
}

void OptimizeThread::imageOptimizationAlgChanged(QVariant alg)
{
    m_ImageOptimizationAlg = alg.toInt();
    log()->info("m_ImageOptimizationAlg %1", alg.toInt());
}

void OptimizeThread::onTrapezoidalModeChanged(const QVariant& value)
{
    m_TrapezoidalMode = value.toBool();
}

void OptimizeThread::onFpsUpdate(const QString& addinfo, float fpsV)
{
    emit fpsChanged(addinfo, fpsV);
}

void OptimizeThread::onTriplexModeChanged(const QVariant& value)
{
    if (value.toBool())
    {
        m_2DImageDelayTime = m_SonoParameters->pIV(BFPNames::PW2DImageDelayTimeStr);
    }
    else
    {
        onECGEnChanged(m_SonoParameters->pV(BFPNames::ECGEnStr));
    }
}

void OptimizeThread::onQuadplexModeStrChanged(const QVariant& value)
{
    if (value.toBool())
    {
        m_2DImageDelayTime = m_SonoParameters->pIV(BFPNames::PW2DImageDelayTimeStr);
    }
    else
    {
        onTriplexModeChanged(m_SonoParameters->pV(BFPNames::TriplexModeStr));
    }
}

void OptimizeThread::onSyncModeChanged(const QVariant& value)
{
    if ((value.toInt() == Sync_M) || (value.toInt() == Sync_CM))
    {
        m_2DImageDelayTime = m_SonoParameters->pIV(BFPNames::M2DImageDelayTimeStr);
    }
    else
    {
        onECGEnChanged(m_SonoParameters->pV(BFPNames::ECGEnStr));
    }
}

void OptimizeThread::onECGEnChanged(const QVariant& value)
{
    if (value.toBool())
    {
        int syncMode = m_SonoParameters->pIV(BFPNames::SyncModeStr);
        if ((syncMode != Sync_M) && (syncMode != Sync_CM) && (syncMode != Sync_D) && (syncMode != Sync_CD))
        {
            m_2DImageDelayTime = m_SonoParameters->pIV(BFPNames::ECG2DImageDelayTimeStr);
        }
    }
    else
    {
        m_2DImageDelayTime = 0;
    }
}

void OptimizeThread::connectSonoParameters()
{
    if (m_SonoParameters == NULL)
    {
        return;
    }

    m_ImageOptimizationAlg = m_SonoParameters->pIV(BFPNames::ImageOptimizationAlgStr);
    m_TrapezoidalMode = m_SonoParameters->pIV(BFPNames::TrapezoidalModeStr);
    connect(m_SonoParameters->parameter(BFPNames::ImageOptimizationAlgStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(imageOptimizationAlgChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::TrapezoidalModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onTrapezoidalModeChanged(QVariant)));

    onQuadplexModeStrChanged(m_SonoParameters->pV(BFPNames::QuadplexModeStr));
    onSyncModeChanged(m_SonoParameters->pV(BFPNames::SyncModeStr));
    onECGEnChanged(m_SonoParameters->pV(BFPNames::ECGEnStr));
    connect(m_SonoParameters->parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onTriplexModeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::QuadplexModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onQuadplexModeStrChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::SyncModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onSyncModeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::ECGEnStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onECGEnChanged(QVariant)));
}

void OptimizeThread::disconnectSonoParameters()
{
    if (m_SonoParameters == NULL)
    {
        return;
    }

    disconnect(m_SonoParameters->parameter(BFPNames::ImageOptimizationAlgStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(imageOptimizationAlgChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::TrapezoidalModeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onTrapezoidalModeChanged(QVariant)));

    disconnect(m_SonoParameters->parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onTriplexModeChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::QuadplexModeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onQuadplexModeStrChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::SyncModeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onSyncModeChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::ECGEnStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onECGEnChanged(QVariant)));
}
