#include "purebwidget.h"
#include <boost/foreach.hpp>
#include <qmath.h>
#include "bfpnames.h"
#include "probedataset.h"
#include "bfcoordtransform.h"
#include "beamformerbase.h"
#include "resource.h"
#include "bfdepthparameters.h"
#include "formula.h"
#include "bffocusparameters.h"
#include "glyphsutil.h"
#include "bftgcparameters.h"
#include "setting/setting.h"
#include "iareasource.h"
#include "util.h"
#include "realcompare.h"
#include "modeluiconfig.h"
#include "appsetting.h"
#include "pimplglyphswidget.h"
#include "bfadfreqparameter.h"
#include <QVector2D>
#include <QGraphicsTextItem>
#include <QDebug>
#include <QGraphicsTextItem>
#include "bfscanareawidthparameter.h"
#include "sonoparameters.h"
#include "isonoparameters.h"
#include "arearesult.h"
#include "realcompare.h"
#include "modelconfig.h"
#include "variantutil.h"
#include "systemscanmodeclassifier.h"

#ifdef WIN32
#undef max
#undef min
#endif

#ifdef USE_GRAPHICS_DEBUG
#include "arearesultdrawer.h"
#include "commonareasource.h"
#endif

#define COLOR_END "\033[0m"
#define COLOR_TITLE "\033[41;37m"
#define COLOR_CONTENT "\033[44;37m"

/**
 * @brief TGC参数名格式化字符串
 */
static const char* TGC_STR = "TGC%1";

const QString PureBWidget::FOCUS_NUM_NAMES[] = {BFPNames::FocusNumBStr, BFPNames::FocusNumMStr, BFPNames::FocusNumCStr};

const QString PureBWidget::FOCUS_POS_NAMES[] = {BFPNames::FocusPosBStr, BFPNames::FocusPosMStr, BFPNames::FocusPosCStr};

const int PureBWidget::OVERFLOW_MAJOR = 3;

PureBWidget::PureBWidget(QObject* parent, const ISonoParameters& sonoParameter, const QRect& windowRect,
                         const QRect& rect, const QRect& region, // windowRect,imageRect, region
                         UseMode useMode, int index)
    : RotatableGlyphsWidget(parent, sonoParameter, windowRect, rect, region, index)
    , m_useMode(useMode)
    , m_IsConnected(false)
    , m_panzoomOffsetX(0)
    , m_centerPanzoomOffsetX(0)
    , m_centerPanzoomOffsetY(0)
    , m_DepthLogoVisible(false)
    , m_SupportTouchScreen(ModelUiConfig::instance().value(ModelUiConfig::SupportTouchScreen).toBool())
{
#ifdef DEBUG_ROBERT
    qDebug() << COLOR_TITLE << "[robert] PureBWidget::PureBWidget" << COLOR_CONTENT << index << &sonoParameter
             << COLOR_END;
#endif
    m_RealTimeSonoParameters = NULL;
    PimplGlyphsWidget* pim = qobject_cast<PimplGlyphsWidget*>(parent);
    if (pim != NULL)
    {
        m_RealTimeSonoParameters = pim->realTimeSonoParameter();
    }
    m_rulerGroup.setPen(partitionNode()->pen());
    m_rulerGroup.setRect(partitionNode()->rect());

    //    qDebug() << PRETTY_FUNCTION
    //             << "windowRect:" << windowRect
    //             << "rect:" << rect
    //             << "region:" << region
    //             << "partitionNode()->rect():" << partitionNode()->rect();

    m_BIndex = index;

    ISonoParameters* paramter = const_cast<ISonoParameters*>(&sonoParameters());

    paramter->setPV(BFPNames::RenderImageTopMarginStr, rect.y());

    if (pIV(getActiveBParaName(BFPNames::RotationStr)) >= 180)
    {
        // 经过调查，旋转超过180度后，图像垂直标尺还是显示在左侧或者上侧，所以这里反向旋转180
        m_rulerGroup.setTransformOriginPoint(m_rulerGroup.rect().center());
        m_rulerGroup.setRotation(-180);
    }

    addItem(&m_rulerGroup);
    m_verticalRuler.setParentItem(&m_rulerGroup);
    //    m_horizontalRuler.setParentItem(&m_rulerGroup);
    m_tgc.setParentItem(&m_rulerGroup);

    if (paramter->pIV(BFPNames::SystemScanModeStr) == SystemScanModeCP &&
        paramter->pBV(BFPNames::CurvedPanoramicEnableStr))
    {
        addItem(&m_CurvedPan); // 宽景取样框
        m_CurvedPan.setCurvedCurrentLength(paramter->pV(BFPNames::CurvedPanoramicLengthStr).toReal());
        m_verticalRuler.setVisible(false); // 宽景不显示左侧垂直标尺
    }
    else
    {
        //        addClipItem(&m_biopsy);
        //        addClipItem(&m_centerLine);
        //        addClipItem(&m_needleLine);
        //        addClipItem(&m_horizontalRuler);
        addItem(&m_biopsy);
        addItem(&m_centerLine);
        addItem(&m_needleLine);
        addItem(&m_horizontalRuler);
        //下面这三个具体是要addClipItem 还是 addItem 待定
        addItem(&m_LGC);
        addItem(&m_FreeHand3DRoiItem);
        addItem(&m_teeAngle);

        // hotlogo 始终在最上
        addItem(&m_hotLogo);
        addItem(&m_depthLogo);

        m_FreeHand3DRoiItem.hide();
    }

    connect(sonoParameter.parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreezeChanged()));

    connect(sonoParameter.parameter(BFPNames::DisplayHeightForPanZoomStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(setupVerticalRulerPos()));

    connect(sonoParameter.parameter(BFPNames::DisplayHeightForPanZoomStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(setupHotLogo()));

    connect(&m_tgc, SIGNAL(pointsChanged(QVariant)), this, SLOT(tgcPointsChanged(QVariant)));

    connect(sonoParameter.parameter(BFPNames::CQYZStr), SIGNAL(valueChanged(QVariant)), this, SLOT(depthLogoChanged()));

    connect(sonoParameter.parameter(BFPNames::DepthLogoVisibleStr), &Parameter::valueChanged, this,
            [=](QVariant value) {
                m_DepthLogoVisible = value.toBool();
                if (!m_DepthLogoVisible)
                {
                    QTimer::singleShot(ModelConfig::instance().value(ModelConfig::SKTipIconShowDelayTime, 2000).toInt(),
                                       this, [=]() {
                                           if (!m_DepthLogoVisible)
                                           {
                                               m_depthLogo.setVisible(false);
                                           }
                                       });
                }
            });

    //    connect(sonoParameter.parameter(BFPNames::GainLogoVisibleStr),
    //            &Parameter::valueChanged, this, [=](QVariant value){
    //        m_GainLogoVisible = value.toBool();
    //        if (!m_GainLogoVisible)
    //        {
    //            QTimer::singleShot(ModelConfig::instance().value(ModelConfig::LogoShowDelayTime, 2000).toInt(), this,
    //            [=](){
    //                if (!m_GainLogoVisible)
    //                {
    //                    m_gainLogo.setVisible(false);
    //                }
    //            });
    //        }
    //    });

    m_debugRuler.setLine(QLineF(0, 0, rect.width(), 0));
    m_debugRuler.setFlag(QGraphicsItem::ItemIgnoresTransformations);
    m_debugRuler.setPen(QPen(rand()));

#ifdef USE_GRAPHICS_DEBUG

    BOOST_FOREACH (QGraphicsLineItem& item, m_debugHotLogo)
    {
        item.setPen(QPen(rand()));
    }

    m_debugB.reset(new AreaResultDrawer(&sonoParameter, windowRect, rect, true, partitionNode()));
    m_bAreaSource = new BAreaSource(&sonoParameter, rect.size(), this);
    m_debugB->setAreaSource(m_bAreaSource);
    m_debugB->setPen(QPen(rand()));

#endif
    m_active = true;

    QColor color = QColor(ModelUiConfig::instance().value(ModelUiConfig::TitleRenderColor).toUInt());

#ifdef SYS_APPLE
    m_biopsy.setLinePen(QPen(QColor(255, 255, 255)));
#else
    m_biopsy.setLinePen(QPen(QColor(ModelUiConfig::instance().value(ModelUiConfig::BiopsyColor).toUInt())));
#endif
    onFreezeChanged();
    m_biopsy.setMovingCircleVisible(false);

    m_centerLine.setLinePen(QPen(color));

    m_needleLine.setLinePen(QPen(color));

    m_teeAngle.setLinePen(QPen(color));

    //初始化深度标尺
    //具体数值参考美工图片
    GlyphsUtil::setupUnitRulerBasicProperties(m_verticalRuler);
    m_verticalRuler.setColor(color);
    m_verticalRuler.setTagMajor(true);

    //初始化水平标尺
    GlyphsUtil::setupUnitRulerBasicProperties(m_horizontalRuler);

    //此处需初始化焦点三角形偏移量
    //（仅其abs值有意义，在后续的construct过程中将正确处理方向）
    //设定尖部位置超过刻度
    static const int OVERFLOW_MAJOR = 3;
    m_verticalRuler.setFocalPointAbsYOffset(m_verticalRuler.majorHeight() / 2 + OVERFLOW_MAJOR);

    //水平标尺设置
    m_horizontalRuler.setType(1, 0, false, 0);
    //水平标尺设置中心对齐
    m_horizontalRuler.centerXTag(true);
    m_horizontalRuler.setLength(this->windowRect().width());
    QPen pen;
    pen.setWidth(3);
    m_horizontalRuler.setMajorPen(pen);
    m_horizontalRuler.setMajorHeight(1);
    m_horizontalRuler.setColor(QColor(ModelUiConfig::instance().value(ModelUiConfig::TitleRenderColor).toInt()));

    enableHorizontolRuler(false);
    //初始化方向标记
    initialHotLogo();
    initialDepthLogo();

    QSize size = sonoParameter.pV(BFPNames::ImageSizeStr).toSize();
    //初始化TGC
    // TODO better control?
    m_tgc.setNumber(TGC_COUNT);
    m_tgc.setWidth(qMin(this->rect().width(), size.width()) * 0.2);
    m_tgc.setXOffset(m_tgc.width() * 0.1);
    m_tgc.setPen(QPen(QColor(ModelUiConfig::instance().value(ModelUiConfig::TgcColor).toUInt())));
    m_tgc.setHeight(this->rect().height() * 0.9);

    m_LGC.setPen(QPen(QColor(ModelUiConfig::instance().value(ModelUiConfig::TitleRenderColor).toUInt())));

    if (isPixelSizeValid())
    {
        // pixelsizemm为非0时,才进行图形配置和信号连接,解决线阵探头存在未配置activeB时解冻会有崩溃的问题
        connectParameterSignals();
        setupAll();
    }
}

void PureBWidget::setupAll()
{

    changeSizeAndClipStatus();

    setItemVisible(true);

    setActive(m_active);

    setupFocalPoints();

    setupFlipsAndZoom();

    setupTGC();

    setupLGC();

    setupTeeProbeAngleItem();
}

void PureBWidget::setupOnlyVerticalRuler()
{
    setItemVisible(false);

    setupFocalPoints();
}

void PureBWidget::connectParameterSignals()
{
    QString hotlogo[] = {
        BFPNames::PanZoomDrawOverStr
        //        BFPNames::PanZoomMidPixelStr,
        //        BFPNames::PanZoomMidDepthPixelStr
    };
    BOOST_FOREACH (const QString& str, hotlogo)
    {
        parameterValueChangedConnect(str, SLOT(setupHotLogo()));
    }

    QString biopsySigParam[] = {
        //基本图像参数的影响
        BFPNames::CQYZStr, BFPNames::ScrollDepthMMStr, getActiveBParaName(BFPNames::StartDepthMMStr),
        //在放大模式下，图像边界与显示区域顶端对齐
        //起始线和结束线将影响穿刺线的显示位置
        // BFPNames::StartLineStr,
        // BFPNames::StopLineStr,
        //但此参数与zoomMidLine和zoomHalfLine是相一致的
        // midLine-halfLine=startLine
        // midLine+halfLine=stopLine

        //穿刺线的坐标及其角度
        BFPNames::BiopsyYPosMMStr, BFPNames::BiopsyXPosMMStr, BFPNames::BiopsyAngleStr,
        // added by Jin Yuqi
        BFPNames::BiopsyAngleOffsetStr, BFPNames::BiopsyXPosMMOffsetStr, BFPNames::BiopsyYPosMMOffsetStr,
        //
        BFPNames::IsBiopsyVisibleStr,
        //放大模式下，穿刺线相对于探头位置固定
        //但是图像中的位置受放大区域的影响
        BFPNames::ZoomOnStr, BFPNames::ZoomMidLineStr, BFPNames::ZoomHalfLinesStr, BFPNames::ZoomMidDepthMMStr,
        BFPNames::ZoomHalfDepthMMStr,
        // imagecoef同样影响穿刺线在图像区的位置
        getActiveBParaName(BFPNames::ImageZoomCoefStr)};

    BOOST_FOREACH (const QString& str, biopsySigParam)
    {
        parameterValueChangedConnect(str, SLOT(setupBiopsy()));
    }

    QString focalPointsSigParam[] = {//焦点数目信号处理
                                     FOCUS_NUM_NAMES[m_useMode], FOCUS_POS_NAMES[m_useMode],
                                     // cqyz信号连接，sonoParameters承诺pixelSizeMM的改变先于CQYZ改变
                                     //且即使CQYZ的值不变，pixelSizeMM的改变也会引起CQYZ的valueChanged
                                     //信号发出(?)
                                     BFPNames::CQYZStr, getActiveBParaName(BFPNames::ImageZoomCoefStr)};

    BOOST_FOREACH (const QString& str, focalPointsSigParam)
    {
        parameterValueChangedConnect(str, SLOT(setupFocalPoints()));
    }
    //当处于放大模式时，垂直标尺的起点位置与非放大模式不同
    //非放大模式：起始线结束线变化不影响图像垂直位置，图像顶端与外框边缘可能不对齐
    //放大模式：总是对齐
    QString verticalRulerSigParam[] = {
        BFPNames::ZoomOnStr, BFPNames::StartLineStr, BFPNames::StopLineStr,
        getActiveBParaName(BFPNames::ImageZoomCoefStr),
        //        getActiveBParaName(BFPNames::PanZoomOffsetDepthPixelStr)
    };

    //绘制标尺
    BOOST_FOREACH (const QString& str, verticalRulerSigParam)
    {
        parameterValueChangedConnect(str, SLOT(setupVerticalRulerPos()));
    }

    //图像反转和缩放信号处理
    // hotLogo随着CQYZ/ImageZoomCoef/pixelSizeMM变化
    //为简化连接，一并放在setupFlipsAndZoom中处理
    const QString flipParamSig[] = {getActiveBParaName(BFPNames::UpStr), getActiveBParaName(BFPNames::LeftStr),
                                    getActiveBParaName(BFPNames::RotationStr), BFPNames::ProbeIdStr, BFPNames::CQYZStr,
                                    //        getActiveBParaName(BFPNames::ImageZoomCoefStr),
                                    BFPNames::BSteeringScanStr, BFPNames::ScanWidthStr,
                                    //        BFPNames::PanZoomOffsetDepthPixelStr,
                                    //        BFPNames::PanZoomMidPixelStr
                                    BFPNames::PanZoomDrawOverStr, BFPNames::IsFullScreenZoomInStr,
                                    BFPNames::IsSecondGearFullScreenZoomInStr,
                                    //        getActiveBParaName(BFPNames::DisplayHeightForPanZoomStr)
                                    getActiveBParaName(BFPNames::PanZoomOffsetDepthPixelStr)};

    BOOST_FOREACH (const QString& str, flipParamSig)
    {
        parameterValueChangedConnect(str, SLOT(setupFlipsAndZoom()));
    }

    // TGC信号连接
    for (int i = 0; i < TGC_COUNT; ++i)
    {
        // TGC命名从1开始
        QString paramName = QString(TGC_STR).arg(i + 1);
        parameterValueChangedConnect(paramName, SLOT(setupTGC()));
    }
    parameterValueChangedConnect(BFPNames::TGCStr, SLOT(setupTGC()));

    //#35 AIO开启时、冻结时，立即隐藏TGC线，在冻结状态不需要显示TGC线
    parameterValueChangedConnect(BFPNames::AIOStr, SLOT(setupTGCVisible()));
    parameterValueChangedConnect(BFPNames::FreezeStr, SLOT(setupTGCVisible()));
    parameterValueChangedConnect(BFPNames::FreeHand3DRoiRectStr, SLOT(setupFreeHand3DRectItem()));
    parameterValueChangedConnect(getActiveBParaName(BFPNames::UpStr), SLOT(setupTGC()));
    parameterValueChangedConnect(getActiveBParaName(BFPNames::ImageZoomCoefStr), SLOT(setupTGC()));

    // centerLine参数设置
    QString centerLineSigParam[] = {BFPNames::IsCenterLineVisibleStr, getActiveBParaName(BFPNames::StartDepthMMStr),
                                    BFPNames::ZoomOnStr, getActiveBParaName(BFPNames::UpStr),
                                    getActiveBParaName(BFPNames::ImageZoomCoefStr)};

    BOOST_FOREACH (const QString& str, centerLineSigParam)
    {
        parameterValueChangedConnect(str, SLOT(setupCenterLine()));
    }

    // B Ruler参数设置
    QString bRulerSigParam[] = {BFPNames::IsBHorizontalRulerVisibleStr, getActiveBParaName(BFPNames::StartDepthMMStr),
                                BFPNames::ZoomOnStr, getActiveBParaName(BFPNames::UpStr),
                                getActiveBParaName(BFPNames::ImageZoomCoefStr)};

    BOOST_FOREACH (const QString& str, bRulerSigParam)
    {
        parameterValueChangedConnect(str, SLOT(setupHorizontalRulerVisible()));
    }

    // needleLine参数设置
    QString needleLineSigParam[] = {getActiveBParaName(BFPNames::StartDepthMMStr),
                                    BFPNames::ZoomOnStr,
                                    getActiveBParaName(BFPNames::UpStr),
                                    getActiveBParaName(BFPNames::LeftStr),
                                    getActiveBParaName(BFPNames::ImageZoomCoefStr),
                                    BFPNames::StartLineStr,
                                    BFPNames::StopLineStr,
                                    BFPNames::NeedleAngleIndexStr,
                                    BFPNames::NeedleModeStr};

    BOOST_FOREACH (const QString& str, needleLineSigParam)
    {
        parameterValueChangedConnect(str, SLOT(setupNeedleLine()));
    }

    connect(&Setting::instance().defaults(), SIGNAL(settingChanged(QString)), this,
            SLOT(onTGCHideTimeChanged(QString)));
    parameterValueChangedConnect(BFPNames::ImageZoomCoefStr, SLOT(onImageZoomCoefChanged(QVariant)));

    {
        QString CurvedPanoEnableSigParam[] = {BFPNames::CurvedPanoramicLengthStr, BFPNames::CurvedPanoramicPointListStr,
                                              BFPNames::CurvedPanoramicROIVisibleStr};
        BOOST_FOREACH (const QString& str, CurvedPanoEnableSigParam)
        {
            parameterValueChangedConnect(str, SLOT(onCurvedPanoInfoChanged()));
        }
    }

    {
        QString TeeProbeAnglSigParam[] = {BFPNames::TeeFlagStr, BFPNames::TeeProbeAngleStr};
        BOOST_FOREACH (const QString& str, TeeProbeAnglSigParam)
        {
            parameterValueChangedConnect(str, SLOT(setupTeeProbeAngleItem()));
        }
    }
}

void PureBWidget::changePostParameterSignalsOnFrozen(ISonoParameters* sonoParameters)
{
    m_LGCController.setSonoParameters(sonoParameters);
    // TGC信号连接
    for (int i = 0; i < TGC_COUNT; ++i)
    {
        // TGC命名从1开始
        parameter(QString(TGC_STR).arg(i + 1))->disconnect(this);
        QString paramName = QString(TGC_STR).arg(i + 1);
        connect(sonoParameters->parameter(paramName), SIGNAL(valueChanged(QVariant)), this, SLOT(setupTGC()));
    }
}

void PureBWidget::setupTGCVisible()
{

    if (!showTGC())
    {
        m_tgc.resetOptical(false);
    }
}

bool PureBWidget::showTGC()
{

    return !pBV(BFPNames::AIOStr);
}

void PureBWidget::setupTGC()
{
    if (!m_active || pBV(BFPNames::HideTCGForCineStr))
    {
        m_tgc.setVisible(false);
        return;
    }
    int tgcHideSec = Setting::instance().defaults().tGCHideTime();
    int needShow = Setting::instance().defaults().TGCNeedShow();

    m_tgc.setSupportHoverToReset(m_SupportTouchScreen);

    // 只调节当前激活的widget
    if (m_BIndex != -1 && m_BIndex != sonoParameters().pIV(BFPNames::ActiveBStr))
    {
        m_tgc.setVisible(false);
        return;
    }

    if (tgcHideSec > 1 ||                            // 延迟隐藏
        (!m_SupportTouchScreen && tgcHideSec == -1)) // 配置为调节时显示但又不支持触摸
    {
        m_tgc.setVisibleType(TGC::AutoHide, needShow);
        if (tgcHideSec == -1)
        {
            m_tgc.setSoildMs(100); // 100MS后自动隐藏
        }
        else
        {
            m_tgc.setSoildMs((tgcHideSec - 1) * 1000);
        }

        //首先检查是否显示TGC
        if (!showTGC())
        {
            m_tgc.resetOptical(false);
            return;
        }
    }
    else if (tgcHideSec == 1)
    {
        m_tgc.setVisibleType(TGC::AlwaysHide);
    }
    else if (tgcHideSec == -1) // 调节时显示
    {
        m_tgc.setVisibleType(TGC::AdjustShow);
        m_tgc.resetOptical(Setting::instance().defaults().tGCAdjusting());
    }
    else
    {
        m_tgc.setVisibleType(TGC::AlwaysShow);
    }

    //不配置时，默认最小值是0，最大值是255,Atom机型配置了100-255
    int minValue = ModelConfig::instance().value(ModelConfig::TGCLimitMinValue, 0).toInt();
    int maxValue = ModelConfig::instance().value(ModelConfig::TGCLimitMaxValue, 255).toInt();
    const qreal fullRange = maxValue - minValue;

    int temp[TGC_COUNT];
    TgcArrayPointer offset = &temp;

    m_tgc.setHeight(scaledBDepth(rect().height()));

    BFTGCParameters::scaledTgcY(offset, m_tgc.height());

    QByteArray byteArray = pV(BFPNames::TGCStr).toByteArray();
    for (int i = 0; i < TGC_COUNT; ++i)
    {
        const qreal percent = (unsigned char)(byteArray[i] - minValue) / fullRange;
        m_tgc.setControlPointBy(i, TGC::OffsetAndPercent((*offset)[i], percent));
    }

    if (isLRLayoutHorizontalRotate())
    {
        m_tgc.setY(combinedUp() ? scaledYInWindowRectCoord(imageRectStartYInWindowRect())
                                : imageRectStartYInWindowRect());
    }
    else
    {
        m_tgc.setY(scaledYInWindowRectCoord(imageRectStartYInWindowRect()));
    }
    int rotation = pIV(BFPNames::RotationStr);
    m_tgc.setY(m_tgc.y() +
               pRV(BFPNames::PanZoomOffsetDepthPixelStr) * ((rotation == 0 || rotation == 180) ? -1.0 : 1.0));
#ifdef SYS_APPLE
    //保持和android一致，起始坐标需要和凸阵的上弧下边缘一致
    int probeId = pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(probeId);
    ProbeParameters probeParam(probeDataInfo, pRV(BFPNames::StartDepthMMStr), pRV(BFPNames::PixelSizeMMStr));
    qreal posy = mm2Pixel(probeParam.arcFloorDisMM() + 0);
    m_tgc.setY(posy);
#endif
    /**
     * @brief 保证即使没有控制点变化也会显示TGC
     * @desc: 前置条件，当前TGC显示模式不是调节时显示
     */
    if (tgcHideSec != -1)
    {
        m_tgc.resetOptical();
    }

    // bug 13743 boundingrect()的size不准确导致变化矩阵，所以在B/M模式下进行90度，270度旋转会有显示不完整的问题．
    // 此处的size应该使用tgc的实际宽度和长度
    m_tgc.setTransform(Formula::flipAndRectCenterTransform(combinedUp(), true, QSizeF(m_tgc.width(), m_tgc.height())));

    const_cast<ISonoParameters*>(&sonoParameters())->setPV(BFPNames::TGCStartYStr, m_tgc.pos().y());
    const_cast<ISonoParameters*>(&sonoParameters())->setPV(BFPNames::TGCROIWIDTHStr, m_tgc.boundingRect().width());
    const_cast<ISonoParameters*>(&sonoParameters())->setPV(BFPNames::TGCROIHEIGHTStr, m_tgc.boundingRect().height());
}

void PureBWidget::disconnectParameterSignals()
{

    // TODO 目前参数断开不完整，但由于在多B切换时
    //仅有激活的B区域绘制图形元素，所以并不影响
    const QString paramNames[] = {
        FOCUS_NUM_NAMES[m_useMode], FOCUS_POS_NAMES[m_useMode], BFPNames::ProbeIdStr,
        getActiveBParaName(BFPNames::UpStr), getActiveBParaName(BFPNames::LeftStr), BFPNames::CQYZStr,
        getActiveBParaName(BFPNames::ImageZoomCoefStr),
        //测试发现下面参数在多B模式下切换active重复连接会影响执行效率
        // biopsyline
        BFPNames::BiopsyYPosMMStr, BFPNames::BiopsyXPosMMStr, BFPNames::BiopsyAngleStr, BFPNames::BiopsyAngleOffsetStr,
        BFPNames::BiopsyXPosMMOffsetStr, BFPNames::BiopsyYPosMMOffsetStr, BFPNames::IsBiopsyVisibleStr,
        // scanwidth
        BFPNames::StartLineStr, BFPNames::StopLineStr,
        // needleline
        BFPNames::NeedleAngleIndexStr, BFPNames::NeedleModeStr,
        // centerline
        BFPNames::IsCenterLineVisibleStr, BFPNames::IsBHorizontalRulerVisibleStr, BFPNames::BSteeringScanStr};

    BOOST_FOREACH (const QString& str, paramNames)
    {
        parameter(str)->disconnect(this);
    }

    for (int i = 0; i < TGC_COUNT; ++i)
    {
        parameter(QString(TGC_STR).arg(i))->disconnect(this);
    }

    disconnect(&Setting::instance().defaults(), SIGNAL(settingChanged(QString)), this,
               SLOT(onTGCHideTimeChanged(QString)));

    m_IsConnected = false;
}

bool PureBWidget::isPixelSizeValid() const
{
    return RealCompare::IsGreater(pRV(getActiveBParaName(BFPNames::PixelSizeMMStr)), 0);
}

void PureBWidget::setupFocalPoints()
{

    //焦点深度标尺
    int focusPosB = pIV(FOCUS_POS_NAMES[m_useMode]), focusNumB = pIV(FOCUS_NUM_NAMES[m_useMode]),
        probeId = pIV(BFPNames::ProbeIdStr);

    //获取像素的物理大小
    const qreal pixelSizeMM = pRV(getActiveBParaName(BFPNames::PixelSizeMMStr));
    if (!RealCompare::IsGreater(pixelSizeMM, 0))
    {
        return;
    }

    //模式1厘米一个副刻度
    GlyphsUtil::setRulerMinorWidth1CM(pixelSizeMM, m_verticalRuler);
    GlyphsUtil::setRulerMinorWidth1CM(pixelSizeMM, m_horizontalRuler);

    //无论是否放大，标尺的起始位置总是与探头的0深度对齐（为保证刻度显示正确）
    //因此放大模式下标尺的起点位置可能不在显示区域内
    // m_verticalRuler的单位以CM计算

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(probeId);

    BFFocusParameters temp;

    DepthRuler::DepthList depthList = temp.getFocusDepthMMs(probeDataInfo, focusNumB, focusPosB);

    // DepthList is MM based ,but m_verticalRuler is CM based
    // so need convert
    BOOST_FOREACH (float& value, depthList)
    {
        value /= 10;
    }

    m_verticalRuler.setDepthList(depthList);

    setupVerticalRulerPos();
}

void PureBWidget::setupFlipsAndZoom()
{

    bool up = combinedUp();

    //处理水平标尺的位置，当上下反转时，标尺随之反转
    const qreal yPos = up ? ORIGIN_OFFSET.y() : windowRect().height() - ORIGIN_OFFSET.y();

    // TODO 水平标尺暂时未处理缩放
    m_horizontalRuler.setY(yPos);

    m_verticalRuler.setUnitTagLeft(up);

    int sign = up ? 1 : -1;
    //垂直标尺同样反转。此处仅处理翻转，不处理位置
    //位置在setupVerticalRulerPos中处理
    m_verticalRuler.setRotation(sign * 90);

    //需保留原来的标尺偏移长度，否则在MultiB重用时将错误重置偏移
    m_verticalRuler.setFocalPointYOffset(-sign * m_verticalRuler.focalPointAbsYOffset());

    setupHotLogo();

    setupVerticalRulerPos();

    setupBiopsy();

    setupCenterLine();

    setupHorizontalRulerVisible();

    setupNeedleLine();

    setupTGC();

    setupLGC();
}

void PureBWidget::setActive(bool activeValue)
{
    m_active = activeValue;

    bool biopsyVisible = pBV(BFPNames::IsBiopsyVisibleStr);
    if (biopsyVisible && (!m_active))
    {
        QPen pen;
        pen = m_biopsy.linePen();
        pen.setColor(QColor(ModelUiConfig::instance().value(ModelUiConfig::BiopsyFreezeColor).toUInt()));
        m_biopsy.setLinePen(pen);
    }
}

void PureBWidget::setupVerticalRulerPos()
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));

    ProbeParameters probeParam(
        probeDataInfo, pRV(getActiveBParaName(BFPNames::StartDepthMMStr)),
        pRV(getActiveBParaName(BFPNames::PixelSizeMMStr)),
        pBV(BFPNames::ZoomOnStr) /* || pBV(BFPNames::PanZoomSelectStr)*/, //这个地方会影响凸阵的标尺位置计算结果
                                                                          // ProbeParameters::probeAngleRadEx
        pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr));

    //图像底部相对于绘制区域边线的像素距离
    // mm2Pixel中使用的pixelSizeMM已经叠加了imageZoomCoef，
    //所以当imageZoomCoef不为0时，此值表示的是在缩放后的区域中
    //标尺起点离缩放后区域边界的距离(已经换算到屏幕坐标系)
    //
    //放大模式下，图像底部并非是深度为0的位置，而是位于StartDepthMM
    //之前的实现将标尺的起点与图像底部对齐，在放大模式下会出现起始刻度
    //不为0的问题，此处仍然计算图像底部位置，但通过StartDepthMM计算出0刻度的
    //像素位置
    // 2023-04-18 Modify by AlexWang 计算探头底部位置与图像区域边界的偏移，不需要在计算时考虑翻转
    qreal probeBottomOffsetImageEdge = mm2Pixel(probeParam.arcFloorDisMM());

    // zoom下图像顶点到零深度点的距离
    qreal zoomedImageOriginY = 0;

    BFDSCScanAreaWidthParameter bfDSCSCanAreaWidthParameter(
        dynamic_cast<SonoParameters*>(const_cast<ISonoParameters*>(&sonoParameters())));
    qreal renderValidImageHeight = bfDSCSCanAreaWidthParameter.renderValidImageHeight();
    QSize renderWidgetSize = pV(BFPNames::RenderWidgetSizeStr).toSize();
    QSize dscImageSize = pV(BFPNames::DSCImageSizeStr).toSize();
    int imageZoomCoef = pIV(BFPNames::ImageZoomCoefStr);
    qreal realImageZoomCoef = imageZoomCoef / 100.0;
    int rotation = pIV(BFPNames::RotationStr);
    int layout = pIV(BFPNames::LayoutStr);
    qreal imageScale = pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>().yScale;
    qreal imageScaleX = pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>().xScale;
    //实际的图像高度
    int realImageHeight = (rotation == 0 || rotation == 180) ? (renderWidgetSize.height() * imageScale)
                                                             : (renderValidImageHeight >= renderWidgetSize.height()
                                                                    ? dscImageSize.height() * imageScale
                                                                    : renderWidgetSize.width() * imageScale);

    //垂直标尺的高度
    qreal verticalRulerRectHeight = realImageHeight;
    //实际的视图宽度
    qreal realRenderWidth = region().width();
#ifdef SYS_APPLE
    float layoutDiv = (layout == Layout_2x2) ? 2.0 : 1.0;
    if (pRV(BFPNames::PanZoomOffsetDepthPixelStr) != 0)
    {
        if (rotation == 0 || rotation == 180)
        {
            qreal widget2ImageRatio = 1.0 * renderWidgetSize.height() / dscImageSize.height();

            zoomedImageOriginY += ((rotation == 0) == pBV(BFPNames::UpStr) ? -1 : 1) *
                                  pRV(BFPNames::PanZoomOffsetDepthPixelStr) * realImageZoomCoef * widget2ImageRatio /
                                  layoutDiv;
            zoomedImageOriginY +=
                (renderWidgetSize.height() - renderValidImageHeight) / 2.0 * realImageZoomCoef / layoutDiv;
            realImageHeight =
                realImageHeight * realImageZoomCoef -
                (renderWidgetSize.height() - dscImageSize.height()) * pIV(BFPNames::ImageZoomCoefStr) / 100.0;
        }
        else
        {
            realImageHeight = realImageHeight * realImageZoomCoef;
            zoomedImageOriginY = ((rotation == 90) == pBV(BFPNames::UpStr) ? -1 : 1) *
                                 pRV(BFPNames::PanZoomOffsetDepthPixelStr) * realImageZoomCoef / layoutDiv;

            float sign = ((rotation == 90) == pBV(BFPNames::UpStr) ? 1 : -1);
            float offsetImageZoomCoef = (pIV(BFPNames::LayoutStr) == Layout_1x2) ? 0 : 1.0;

            if (layout == Layout_2x2)
            {
                // 4B 下 renderWidgetSize.width() - renderValidImageHeight  这个值 要 除 4， 2B下只用/ 2
                if (rotation == 90 || rotation == 270)
                {
                    zoomedImageOriginY +=
                        sign * (((renderWidgetSize.width() - renderValidImageHeight) / 4.0) * realImageZoomCoef -
                                ((windowRect().x() > renderWidgetSize.width() / 2.0)
                                     ? (windowRect().x() - renderWidgetSize.width() / 2.0)
                                     : windowRect().x()));
                }
            }
            else
            {
                zoomedImageOriginY += sign * (renderWidgetSize.width() - renderValidImageHeight) / 2.0 *
                                      (realImageZoomCoef - offsetImageZoomCoef);
            }
        }
    }
    else
    {
        if ((layout != Layout_1x2) || ((layout == Layout_1x2) && (rotation != 90) && (rotation != 270)))
        {
            zoomedImageOriginY =
                (windowRect().height() - qMin(qCeil(rect().height() * realImageZoomCoef), renderWidgetSize.height())) /
                2;
        }
        // 2B 旋转90 和 180度 标尺的位置的调整
        if ((layout == Layout_1x2) && (rotation == 90 || rotation == 270))
        {
            qreal height = bfDSCSCanAreaWidthParameter.renderValidImageHeight();
            if (height < dscImageSize.height())
            {
                zoomedImageOriginY += (((renderWidgetSize.width() / 2) - height * realImageZoomCoef) / 2);
            }
        }
    }
#else
    if (pBV(BFPNames::PanZoomSelectStr))
    {
        realImageHeight = realImageHeight * realImageZoomCoef;
        bool isVertical = (0 == rotation || 180 == rotation);
        qreal startY = ((isVertical ? verticalRulerRectHeight : fmin(verticalRulerRectHeight, realRenderWidth)) -
                        verticalRulerRectHeight * realImageZoomCoef) /
                       2.0;
        zoomedImageOriginY =
            startY + pRV(BFPNames::PanZoomOffsetDepthPixelStr) * ((rotation == 0 || rotation == 180) ? -1.0 : 1.0);
        //        qCritical()<<"zoomedImageOriginY"<<zoomedImageOriginY
        //                   <<"startY"<<startY
        //                   <<"offsetY"<<offsetY
        //                   <<"PanZoomOffsetDepthPixelStr"<<pRV(BFPNames::PanZoomOffsetDepthPixelStr) * (rotation == 0
        //                   ? -1.0 : 1.0)
        //                   <<"verticalRulerRectHeight"<<verticalRulerRectHeight
        //                   <<"realRenderWidth"<<realRenderWidth
        //                   <<"region().height()"<<region().height()
        //                   <<"realImageZoomCoef"<<realImageZoomCoef
        //                   <<"probeBottomOffsetImageEdge"<<probeBottomOffsetImageEdge
        //                   <<"renderValidImageHeight"<<renderValidImageHeight
        //                   <<"renderWidgetSize.height()"<<renderWidgetSize.height()
        //                   <<"pRV(BFPNames::PanZoomMidDepthPixelStr)"<<pRV(BFPNames::PanZoomMidDepthPixelStr)
        //                   <<"pRV(BFPNames::PanZoomHalfDepthPixelStr)"<<pRV(BFPNames::PanZoomHalfDepthPixelStr)
        //                   <<"pRV(BFPNames::PanZoomMidPixelStr)"<<pRV(BFPNames::PanZoomMidPixelStr)
        //                   <<"pRV(BFPNames::PanZoomMidPixelStr)"<<pRV(BFPNames::PanZoomHalfPixelStr)
        //                   <<PRETTY_FUNCTION;
    }
    else
    {
        if ((layout != Layout_1x2) || ((layout == Layout_1x2) && (rotation != 90) && (rotation != 270)))
        {
            zoomedImageOriginY =
                (windowRect().height() - qMin(qCeil(rect().height() * realImageZoomCoef), renderWidgetSize.height())) /
                2;
        }
        // 2B 旋转90 和 180度 标尺的位置的调整
        if ((layout == Layout_1x2) && (rotation == 90 || rotation == 270))
        {
            qreal height = bfDSCSCanAreaWidthParameter.renderValidImageHeight();
            if (height < dscImageSize.height())
            {
                zoomedImageOriginY = (((renderWidgetSize.width() / 2) - height * realImageZoomCoef) / 2);
            }
        }
    }

    // 2023-04-18 Write by AlexWang  解决在Zoom状态进入PW模式崩溃
    // setupFlipsAndZoom已对标尺做翻转，此处计算不需要考虑翻转
    // scaledYInRectCoord计算已考虑ImageZoomCoef
    //图像区域已在参数变化时更新了自身的大小尺寸，标尺的高度和实际图像的高度就是图像区域的高度
    //    zoomedImageOriginY = scaledYInRectCoord(0);
    //    verticalRulerRectHeight = rect().height();
    //    realImageHeight = rect().height();

#endif
    // 2023-05-25 Modify by AlexWang
    // pixelSizeMM已经考虑了ImageZoomCoef系数，probeBottomOffsetImageEdge和startDepthOffset不用重复乘以ImageZoomCoef系数
    qreal finalProbeBottomYOffset = zoomedImageOriginY + probeBottomOffsetImageEdge;

    qreal startDepthOffset = mm2Pixel(pRV(getActiveBParaName(BFPNames::StartDepthMMStr)));

    qreal finalRulerYOffset = (finalProbeBottomYOffset - startDepthOffset);

    m_verticalRuler.setType(-1, -1, true, -1);

    // 2023-04-18 Modify by AlexWang
    m_verticalRuler.setLength(
        // imageZoomCoef显示区域内的长度
        (scaledBDepth(verticalRulerRectHeight)
         //标尺起始点离imageZoomCoef框边缘的距离
         - (finalRulerYOffset - zoomedImageOriginY)),
        imageZoomCoef);

    qreal yPos = combinedUp() ? (finalRulerYOffset) : (realImageHeight + finalRulerYOffset);

    m_verticalRuler.setY(yPos);

    //让水平标尺始终位于图像上方  并且要在垂直标尺的刻度0的上方 ,这里要考虑图像旋转这种特殊情况
    if (imageZoomCoef == 100)
    {
        QPointF pos = m_verticalRuler.pos();
        qreal y;
        if (rotation == 180 || rotation == 270)
        {
            y = pos.y() - m_verticalRuler.getFontHeight() / 2 - m_verticalRuler.length();
        }
        else
        {
            y = pos.y() - m_verticalRuler.getFontHeight() / 2;
        }
        y = y < 0 ? 0 : y;
        pos.setY(y);
        pos.setX(m_horizontalRuler.pos().x());
        m_horizontalRuler.setPos(pos);
    }
    //此处不处理翻转，翻转在 setupFlipsAndZoom 中完成
    qreal beginLimit = m_verticalRuler.y();
    //限定刻度不要超过显示范围,需要在深度标尺的方向和位置都确定后才可计算
    // 2023-04-19 Modify by AlexWang 使用图像区域的高度，图像区域包含在渲染区，图像区域不会超出渲染区，但是会小于渲染区
    if (m_verticalRuler.unitTagLeft())
    {
        //从上到下
        m_verticalRuler.setBeginEndLimit(beginLimit,
                                         localRect().height() - m_verticalRuler.y() - m_verticalRuler.length());
    }
    else
    {
        //从下到上
        m_verticalRuler.setBeginEndLimit(localRect().height() - m_verticalRuler.y(),
                                         m_verticalRuler.y() - m_verticalRuler.length());
    }

    qreal displayCM = m_verticalRuler.unlimitLength() * pRV(getActiveBParaName(BFPNames::PixelSizeMMStr)) / 10;
    //副刻度一直不显示
    m_verticalRuler.setTagMinor(false);

    m_verticalRuler.setUnitTextRotation(pIV(getActiveBParaName(BFPNames::RotationStr)) -
                                        (pIV(getActiveBParaName(BFPNames::RotationStr)) >= 180 ? 180 : 0));

    //    qDebug() << PRETTY_FUNCTION
    //             << "m_verticalRuler.boundingRect():"<<m_verticalRuler.boundingRect()
    //             << "renderWidgetSize"<<renderWidgetSize
    //             <<"dscImageSize"<<dscImageSize
    //             <<"windowRect()"<<windowRect()
    //             <<"localRect()"<<localRect()
    //             <<"regionRect"<<region()
    //             <<"rect"<<rect();
}

void PureBWidget::enableHorizontolRuler(bool enabled)
{
    m_horizontalRuler.setVisible(enabled);
}

QPointF PureBWidget::edgeLimitHotLogoPos(bool xCrossEdge, bool yCrossEdge, const QPointF& center,
                                         const QPointF& predefinedPos, const qreal yCrossRad)
{

    Q_ASSERT(xCrossEdge || yCrossEdge);

    const qreal r = hotLogoR();

    int hotLogoLine = pIV(BFPNames::HotLogoLineStr);

    bool zoomOn = pBV(BFPNames::ZoomOnStr);

    //定位线与x轴的夹角
#ifdef WIN32
    const qreal xCrossRad = 45;
#else
    const qreal xCrossRad = M_PI_2 - yCrossRad;
#endif
    //定位线与y轴的夹角

    //指定的scanLine与当前图像区上边线交点相对于垂直中线的偏移
    const qreal crossLineOffsetCenter = -center.y() * std::tan(yCrossRad);

    if (yCrossEdge && !xCrossEdge)
    {

        //恰好与0线/图像区上边界相切的hotlogo中心位置
        return QPointF(-r / std::tan(xCrossRad / 2) - crossLineOffsetCenter, r);
    }

    qreal halfScaledWidth = windowRect().width() / 2 * scaleFactor();

    return QPointF(-halfScaledWidth + r,

                   (halfScaledWidth - crossLineOffsetCenter) * std::tan(xCrossRad) - r / std::tan(yCrossRad / 2));
}

void PureBWidget::initialHotLogo()
{
    QString model = AppSetting::model();
    if (model.contains("eco"))
    {
        m_hotLogo.setPixmap(QPixmap(Resource::hotLogoFileName()));
    }
    else if (model.contains("qbit"))
    {
        m_hotLogo.setPixmap(QPixmap(Resource::hotLogoFileName()));
    }
    else if (model.contains("ebit"))
    {
        m_hotLogo.setPixmap(QPixmap(Resource::getFileFullName("hotlogo_m600.png", Resource::appIconDir)));
    }
    else if (model.contains("sonobook"))
    {
        m_hotLogo.setPixmap(QPixmap(Resource::getFileFullName("hotlogo_sbk.png", Resource::appIconDir)));
    }
    else if (model.contains("m600"))
    {
        m_hotLogo.setPixmap(QPixmap(Resource::getFileFullName("hotlogo_m600.png", Resource::appIconDir)));
    }
    else if (model.contains("sonoeye"))
    {
        m_hotLogo.setPixmap(QPixmap(Resource::getFileFullName("<EMAIL>", Resource::appIconDir)));
    }
    else
    {
        m_hotLogo.setPixmap(QPixmap(Resource::hotLogoFileName()));
    }
}

void PureBWidget::initialDepthLogo()
{
    m_depthLogo.setPixmap(QPixmap(Resource::depthLogoFileName()));
    int rotation = pIV(BFPNames::RotationStr);
    if (rotation == 0)
    {
        m_depthLogo.setPos(
            QPoint(m_depthLogo.pixmap().width() / 2, rect().height() / 2 - m_depthLogo.pixmap().height() / 2));
    }
    else if (rotation == 90)
    {
        m_depthLogo.setPos(
            QPoint(m_depthLogo.pixmap().width() / 2, rect().height() / 2 - m_depthLogo.pixmap().height() / 2));
    }
    else if (rotation == 180)
    {
        m_depthLogo.setPos(
            QPoint(rect().x() + rect().width() - m_verticalRuler.boundingRect().width() - m_depthLogo.pixmap().width(),
                   rect().height() / 2 - m_depthLogo.pixmap().height() / 2));
    }
    else if (rotation == 270)
    {
        m_depthLogo.setPos(
            QPoint(rect().y() + rect().height() - m_verticalRuler.boundingRect().width() - m_depthLogo.pixmap().width(),
                   rect().height() / 2 - m_depthLogo.pixmap().height() / 2));
    }
    m_depthLogo.setVisible(false);
}

static const qreal R_CAP = 2;

qreal PureBWidget::hotLogoR()
{
    const qreal width = m_hotLogo.pixmap().width(), height = m_hotLogo.pixmap().height();

    return qMax(width, height) / 2 + R_CAP;
}

void PureBWidget::calHotoLogoPosWhenPanzoom(const ProbeDataInfo probeDataInfo, QPointF imageTopLeft, int rotation,
                                            qreal x, qreal y, qreal& logoX, qreal& logoY)
{
    int layout = pIV(BFPNames::LayoutStr);
    qreal temp = 1;
    if (layout == Layout_2x2) // 4B  关于C坐标的偏移量要 /2
    {
        temp = 2.0;
    }
    logoX = m_preHotoLogoPos.x();
    logoY = m_preHotoLogoPos.y();
    QSize renderWidgetSize = pV(BFPNames::RenderWidgetSizeStr).toSize();
    QSize dscImageSize = pV(BFPNames::DSCImageSizeStr).toSize();
    int halfWidth = renderWidgetSize.width() / 2;
    int halfHeight = renderWidgetSize.height() / 2;
    int zoomcoef = pIV(BFPNames::ImageZoomCoefStr);
    SonoParameters* param = dynamic_cast<SonoParameters*>(const_cast<ISonoParameters*>(&sonoParameters()));
    BFDSCScanAreaWidthParameter bfDSCSCanAreaWidthParameter(param);
    qreal height = bfDSCSCanAreaWidthParameter.renderValidImageHeight();
    qreal width = (bfDSCSCanAreaWidthParameter.realImageWidth() > renderWidgetSize.width()
                       ? renderWidgetSize.width()
                       : bfDSCSCanAreaWidthParameter.realImageWidth());
    qreal convexLen = renderWidgetSize.width() / 2.0 - abs(imageTopLeft.x()) / (zoomcoef / 100.0) * temp;
    qreal left = pRV(BFPNames::PanZoomMidPixelStr) - pRV(BFPNames::PanZoomHalfPixelStr);
    qreal right = pRV(BFPNames::PanZoomMidPixelStr) + pRV(BFPNames::PanZoomHalfPixelStr);
    qreal top = pRV(BFPNames::PanZoomMidDepthPixelStr) - pRV(BFPNames::PanZoomHalfDepthPixelStr);
    qreal bottom = pRV(BFPNames::PanZoomMidDepthPixelStr) + pRV(BFPNames::PanZoomHalfDepthPixelStr);
    qreal midX = pRV(BFPNames::PanZoomMidPixelStr);
    qreal midY = pRV(BFPNames::PanZoomMidDepthPixelStr);
    //现在的panzoom会对图像进行一个scale操作
    //    qreal ratio = 1.0*renderWidgetSize.height()/dscImageSize.height();
    ProbeParameters probeParam(probeDataInfo, pRV(getActiveBParaName(BFPNames::StartDepthMMStr)),
                               pRV(getActiveBParaName(BFPNames::PixelSizeMMStr)), pBV(BFPNames::ZoomOnStr),
                               pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr));
    qreal ratio = 1.0;
    qreal tanV = 1;
    qreal unLinerOffset = 0;
    ImageScale imageScale = pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>();
    qreal sceneViewSacle_x = imageScale.xScale;
    qreal sceneViewSacle_y = imageScale.yScale;

    int reduseH = 1;
    int reduseW = 1;
    if (layout == Layout_1x2 || layout == Layout_2x2)
    {
        // 2b下，图像和场景没有被缩小，zeus还是当作单b来处理
        // 4b下，图像和场景等比例缩小，所以还是可以当作单b处理
        sceneViewSacle_x = 1.0;
        sceneViewSacle_y = 1.0;
        reduseW = 2;
        if (layout == Layout_2x2)
        {
            reduseH = 2;
        }
    }

    renderWidgetSize.setWidth(renderWidgetSize.width() * sceneViewSacle_x);
    renderWidgetSize.setHeight(renderWidgetSize.height() * sceneViewSacle_y);
    width *= sceneViewSacle_x;
    height *= sceneViewSacle_y;

    qreal leftBorder = (renderWidgetSize.width() - width) / 2.0;
    qreal topBorder = (renderWidgetSize.height() - height) / 2.0;

    //对于 2B 4B 这种情况 rect的值要处理一下
    int rectX = rect().x() >= halfWidth ? rect().x() - halfWidth : rect().x();
    rectX = rectX < 0 ? 0 : rectX;
    int rectY = rect().y() >= halfHeight ? rect().y() - halfHeight : rect().y();
    rectY = rectY < 0 ? 0 : rectY;

    int distance = (renderWidgetSize.width() / reduseW - rect().height() - rectY * 2) / 2.0;

    if (!probeDataInfo.IsLinear)
    {
        ProbeParameters probeParam(probeDataInfo, pRV(getActiveBParaName(BFPNames::StartDepthMMStr)),
                                   pRV(BFPNames::PixelSizeMMStr), false, pIV(BFPNames::StartLineStr),
                                   pIV(BFPNames::StopLineStr), 0, pRV(BFPNames::AngleSpacingRadStr));

        qreal angle = (probeParam.convexProbeLineAngleRad(pIV(BFPNames::StopLineStr)) -
                       probeParam.convexProbeLineAngleRad(pIV(BFPNames::StartLineStr))) /
                      2;
        tanV = tan(angle);

        // convexLen为凸阵探头上部弧线顶部到左边界的距离，因为凸阵探头得到的图像宽度大于1040，因此leftBorder的值用这个值代替
        leftBorder = convexLen;
        topBorder += imageTopLeft.y() * temp / (zoomcoef / 100.0);
        //如果是相控阵
        if (probeDataInfo.IsPhasedArray)
        {
            if (rotation == 90 || rotation == 270)
            {
                //对于相控阵imageTopLeft的值总是为(0,0),因此convexLen计算方法不同，这里也是计算相控阵图像顶部到边界的距离
                convexLen = renderWidgetSize.height() / 2.0 - abs(imageTopLeft.y()) / (zoomcoef / 100.0);
                topBorder = renderWidgetSize.height() / 2;
                leftBorder = (renderWidgetSize.width() - height) / 2.0;
            }
            else
            {
                convexLen = (renderWidgetSize.height() - height) / 2.0 - rectX;
                topBorder = convexLen;
                leftBorder = renderWidgetSize.width() / 2;
            }
        }
        else if (rotation == 90 || rotation == 270)
        {
            convexLen = renderWidgetSize.height() / 2.0 - abs(imageTopLeft.x()) / (zoomcoef / 100.0) * temp;
            topBorder = convexLen;
            //为了让 2B 4B 的旋转后的C标记位置正确  用到了这个值( (renderWidgetSize.width() - dscImageSize.height()) /
            // 2)
            leftBorder = abs(imageTopLeft.y() / (zoomcoef / 100.0)) * temp +
                         ((renderWidgetSize.width() - dscImageSize.height()) / 2);
        }
    }

    //对于C标记的坐标要注意这里的(0,0)坐标是相对于this->rect()的区域,因此如果想要C标记到整个窗口的(0,0)位置就要把this->rect()的x,y考虑带入计算
    if (rotation == 0 || rotation == 180)
    {
        //考虑上下左右翻转的影响   图像的上下左右翻转 并不会改变 right  left top  bottom 的值，所以我们要去计算主动变更
        if (rotation == 180)
        {
            if (pBV(BFPNames::LeftStr)) //旋转了180度且没有进行左右翻转
            {
                qreal temp = left;
                left = dscImageSize.width() - right;
                right = dscImageSize.width() - temp;
                midX = (right + left) / 2.0;
            }
            if (pBV(BFPNames::UpStr)) //旋转了180度且没有进行上下翻转
            {
                qreal temp = top;
                top = dscImageSize.height() - bottom;
                bottom = dscImageSize.height() - temp;
                midY = (bottom + top) / 2.0;
            }
        }
        else if (rotation == 0)
        {
            if (!pBV(BFPNames::UpStr))
            {
                qreal temp = top;
                top = dscImageSize.height() - bottom;
                bottom = dscImageSize.height() - temp;
                midY = (bottom + top) / 2.0;
            }
            if (!pBV(BFPNames::LeftStr))
            {
                qreal temp = left;
                left = dscImageSize.width() - right;
                right = dscImageSize.width() - temp;
                midX = (right + left) / 2.0;
            }
        }

        if (topBorder < top * ratio && !probeDataInfo.IsLinear)
        {
            unLinerOffset = abs(top * ratio - topBorder) / temp * zoomcoef / 100.0 * tanV;
        }

        logoY = (topBorder - top * ratio) / temp * zoomcoef / 100.0 - rectY; // C标记的坐标系会受到rect的影响
        m_centerPanzoomOffsetY = logoY + rectY;
        logoX = (leftBorder - left) / temp * zoomcoef / 100.0 - rectX - m_hotLogo.pixmap().width() * 2 - unLinerOffset;
        m_panzoomOffsetX = (leftBorder - left) / temp * zoomcoef / 100.0 - rectX;

        if (!pBV(BFPNames::UpStr))
        {
            m_centerPanzoomOffsetY = rect().height() - m_centerPanzoomOffsetY + rectY * 2;
            logoY = rect().height() - logoY - m_hotLogo.pixmap().height();
        }

        if (!pBV(BFPNames::LeftStr))
        {
            logoX = renderWidgetSize.width() / reduseW - logoX;
            m_panzoomOffsetX = renderWidgetSize.width() / reduseW - m_panzoomOffsetX;
        }
    }
    else if (rotation == 90 || rotation == 270) //坐标系跟着旋转了90度
    {
        if (rotation == 90)
        {
            if (probeDataInfo.IsLinear)
            {
                leftBorder = (renderWidgetSize.width() - height) / 2.0;
                topBorder = (renderWidgetSize.height() - width) / 2.0;
            }
            if (pBV(BFPNames::UpStr))
            {
                qreal temp = left;
                left = dscImageSize.width() - right;
                right = dscImageSize.width() - temp;
                midX = (right + left) / 2.0;
            }
            if (!pBV(BFPNames::LeftStr))
            {
                qreal temp = top;
                top = dscImageSize.height() - bottom;
                bottom = dscImageSize.height() - temp;
                midY = (bottom + top) / 2.0;
            }
        }
        else if (rotation == 270)
        {
            if (probeDataInfo.IsLinear)
            {
                leftBorder = (renderWidgetSize.width() - height) / 2.0;
                topBorder = (renderWidgetSize.height() - width) / 2.0;
            }
            if (!pBV(BFPNames::UpStr))
            {
                qreal temp = left;
                left = dscImageSize.width() - right;
                right = dscImageSize.width() - temp;
                midX = (right + left) / 2.0;
            }
            if (pBV(BFPNames::LeftStr))
            {
                qreal temp = top;
                top = dscImageSize.height() - bottom;
                bottom = dscImageSize.height() - temp;
                midY = (bottom + top) / 2.0;
            }
        }

        if (leftBorder < left && !probeDataInfo.IsLinear)
        {
            unLinerOffset = abs(left - leftBorder) / temp * zoomcoef / 100.0 * tanV;
        }

        logoX = (topBorder - top * ratio) / temp * (zoomcoef / 100.0) - m_hotLogo.pixmap().width() * 2 - unLinerOffset;
        logoY = (leftBorder - left) / temp * zoomcoef / 100.0 - rectY - distance;

        m_centerPanzoomOffsetY = (leftBorder - left) / temp * zoomcoef / 100.0 - rectY - distance;
        m_panzoomOffsetX = (topBorder - top * ratio) / temp * (zoomcoef / 100.0);

        if (!pBV(BFPNames::UpStr))
        {
            logoY = rect().height() - logoY - m_hotLogo.pixmap().width();
            m_centerPanzoomOffsetY = rect().height() - m_centerPanzoomOffsetY;
        }

        if (!pBV(BFPNames::LeftStr))
        {
            logoX = renderWidgetSize.height() / reduseH - logoX;
            m_panzoomOffsetX = renderWidgetSize.height() / reduseH - m_panzoomOffsetX;
        }
    }

    qreal layoutRatio = 1;
    if (layout != Layout_1x1)
    {
        if (rotation == 0 || rotation == 180)
        {
            layoutRatio = 2;
        }
        else
        {
            if (layout == Layout_2x2)
            {
                layoutRatio = 2;
            }
        }
    }

    if (layout == Layout_2x2 && (rotation == 90 || rotation == 270))
    {
        m_centerPanzoomOffsetY += rectY;
    }
    m_imageWidth = width;

    qreal halfH, halfW;
    halfH = dscImageSize.height() / 2;
    halfW = dscImageSize.width() / 2;

    if (rotation == 0 || rotation == 180)
    {
        m_centerPanzoomOffsetX = (halfW - midX) / temp * zoomcoef / 100.0;
    }
    else
    {
        m_centerPanzoomOffsetX = (halfH - midY) / temp * zoomcoef / 100.0 * ratio;
    }

    restrictRange(rectX, rectY, logoX, logoY, renderWidgetSize, reduseW, reduseH, rotation, distance, x);
    //    qCritical()<<"preX"<<tyX<<"pV(BFPNames::PanZoomOffsetWidthPixelStr)"<<pV(BFPNames::PanZoomOffsetWidthPixelStr)
    //               <<"preY"<<tyY<<"pV(BFPNames::PanZoomOffsetDepthPixelStr)"<<pV(BFPNames::PanZoomOffsetDepthPixelStr)
    //               <<"logoX"<<logoX<<"logoY"<<logoY
    //               <<"topBorder"<<topBorder
    //               <<"top"<<top
    //               <<"rectX"<<rectX
    //               <<"ratio"<<ratio
    //               <<"temp"<<temp
    //               <<"unLinerOffset"<<unLinerOffset
    //               <<"leftBorder"<<leftBorder
    //               <<"left"<<left
    //               <<"rectY"<<rectY
    //               <<"distance"<<distance
    //               <<"width"<<width
    //               <<"bfDSCSCanAreaWidthParameter.realImageWidth()"<<bfDSCSCanAreaWidthParameter.realImageWidth()
    //               <<"height"<<height
    //               <<"rect"<<rect()
    //               <<"renderWidgetSize"<<renderWidgetSize
    //               <<PRETTY_FUNCTION;
    if (m_horizontalRuler.isVisible())
    {
        m_horizontalRuler.setLength((windowRect().width() * layoutRatio * zoomcoef / 100.0), zoomcoef);
        qreal offsetY = m_verticalRuler.getFontHeight() / 2 * (pBV(BFPNames::UpStr) ? 1 : -1);
        if (!pBV(BFPNames::UpStr))
        {
            offsetY -= m_hotLogo.pixmap().height();
        }

        if (rotation == 0 || rotation == 180)
        {
            m_horizontalRuler.setPos((m_horizontalRuler.getCenterByXOffset() -
                                      ((!pBV(BFPNames::LeftStr) ? ((1040 - right)) : left) * zoomcoef / 100.0)) /
                                         temp,
                                     m_hotLogo.pos().y() - offsetY);
        }
        else
        {
            qreal layoutOffset = 0;
            if (layout == Layout_2x2)
            {
                layoutOffset = m_horizontalRuler.minorWidth() / 2;
            }
            m_horizontalRuler.setPos(
                (m_horizontalRuler.getCenterByXOffset() -
                 ((!pBV(BFPNames::LeftStr)) ? (dscImageSize.height() - bottom) : top) * ratio * zoomcoef / 100.0) /
                        temp +
                    layoutOffset,
                m_hotLogo.pos().y() - offsetY);
        }
    }
}

void PureBWidget::restrictRange(int rectX, int rectY, qreal& logoX, qreal& logoY, QSize renderWidgetSize, int reduseW,
                                int reduseH, int rotation, int distance, qreal x)
{
    if (rotation == 0 || rotation == 180)
    {
        //标尺一定会出现在界面的左侧或者上侧
        //考虑到左右翻转的这种特殊情况 他会让 C标记的限制范围有所不同
        if (rotation == 0)
        {
            logoX = qBound<qreal>(-rectX + (x + m_verticalRuler.getFontWith()), logoX,
                                  renderWidgetSize.width() / reduseW - rectX - m_hotLogo.pixmap().width());
            logoY =
                qBound<qreal>(-rectY, logoY, renderWidgetSize.height() / reduseH - rectY - m_hotLogo.pixmap().height());
        }
        else
        {
            logoX = qBound<qreal>(-rectX, logoX,
                                  renderWidgetSize.width() / reduseW - rectX - x - m_hotLogo.pixmap().width() -
                                      m_verticalRuler.getFontWith());
            logoY =
                qBound<qreal>(-rectY, logoY, renderWidgetSize.height() / reduseH - rectY - m_hotLogo.pixmap().height());
        }
    }
    else
    {
        if (rotation == 90)
        {
            //旋转 相当于高度从850 变成了 1040   所以这里要计算一个distance去补偿这个变化量
            //这里的rect.y有可能存在为负数的情况，这里暂时没有做特殊处理
            logoX = qBound<qreal>(x + m_verticalRuler.getFontHeight(), logoX,
                                  renderWidgetSize.height() / reduseH - m_hotLogo.pixmap().height());
            logoY = qBound<qreal>(-rectY - distance, logoY,
                                  renderWidgetSize.width() / reduseW - (rectY + distance) - m_hotLogo.pixmap().width());
        }
        else
        {
            logoX = qBound<qreal>(0, logoX,
                                  renderWidgetSize.height() / reduseH - m_hotLogo.pixmap().height() - x -
                                      m_verticalRuler.getFontHeight());
            logoY = qBound<qreal>(-rectY - distance, logoY,
                                  renderWidgetSize.width() / reduseW - (rectY + distance) - m_hotLogo.pixmap().width());
        }
    }
}

void PureBWidget::setupHotLogo()
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));

    // 利用起始线号和起始深度来获取B图像起始点的逻辑坐标
    QPointF imageTopLeft = scanLineAndDepthScreenPos(pIV(BFPNames::StartLineStr), pRV(BFPNames::StartDepthMMStr));
    ProbeParameters probeParam(probeDataInfo, pRV(getActiveBParaName(BFPNames::StartDepthMMStr)),
                               pRV(BFPNames::PixelSizeMMStr), false, pIV(BFPNames::StartLineStr),
                               pIV(BFPNames::StopLineStr), 0, pRV(BFPNames::AngleSpacingRadStr));
    // 线阵探头考虑steetingRad
    if (probeDataInfo.IsLinear)
    {
        const qreal steeringRad = IAreaSource::paramShowValueAngle2Rad(&sonoParameters(), BFPNames::BSteeringScanStr);

        imageTopLeft.rx() = imageTopLeft.x() - hotLogoR() * std::sin(steeringRad);
        imageTopLeft.ry() = imageTopLeft.y() * std::cos(steeringRad);
    }

    /** hotlogo的X位置，与B图像的起始坐标始终保持相等距离
     * hotlogo的Y位置, 与B图像顶部基本保持平行，这里由于FPGA不能完整上传起始线号到终止线号的图像
     * 所以标记的位置与真实显示图像存在一些误差
     **/
    // 目前设定hotlogo在x方向的与图像距离为4*hotlogo的宽度
    const qreal xDistance = hotLogoR() * 4;
    // 在左右和上下翻转的时候，需要保持标记的方向不变，所以不能对标记本身进行翻转操作
    // 但是翻转之后会有一个标记长度/宽度的偏差，所以需要补上偏差
    qreal xOffset = (pBV(BFPNames::LeftStr) ? 0.0 : m_hotLogo.pixmap().width()) - xDistance,
          yOffset = pBV(BFPNames::UpStr) ? 0.0 : m_hotLogo.pixmap().height();

    // 设定hotLogo的坐标
    QPointF hotLogoPos(imageTopLeft.x() + xOffset, imageTopLeft.y() + yOffset);
    QSize imageSize(windowLocalRect().width(), localRect().height());

    // 此时的坐标是以图像中点为原点，所以需要翻转矩阵进行变换    调整是上下翻转和左右翻转的位置
    hotLogoPos =
        Formula::flipAndRectZoomCoefTransform(pBV(getActiveBParaName(BFPNames::UpStr)),
                                              pBV(getActiveBParaName(BFPNames::LeftStr)), imageSize, scaleFactor())
            .map(hotLogoPos);

    // C标记不能标尺的数字重合
    qreal x = abs(m_verticalRuler.getTagXPos());
    qreal y = m_verticalRuler.getFontHeight();

    //旋转会导致坐标系跟着旋转
    int rotation = pIV(BFPNames::RotationStr);

    qreal logoX = 0, logoY = 0;

    if (m_posInfo.Left != pBV(BFPNames::LeftStr))
    {
        m_preHotoLogoPos.setX(this->rect().width() - m_preHotoLogoPos.x());
        m_posInfo.Left = pBV(BFPNames::LeftStr);
    }
    if (m_posInfo.Up != pBV(BFPNames::UpStr))
    {
        m_preHotoLogoPos.setY(rect().height() - m_preHotoLogoPos.x() - m_hotLogo.pixmap().width());
        m_posInfo.Up = pBV(BFPNames::UpStr);
    }

    //    多b下的偏移  TODO be better
    float sign = pBV(BFPNames::UpStr) ? 1 : -1;
    qreal mulitLayoutOffset =
        pV(BFPNames::LayoutStr) == Layout_1x2
            ? (localRect().height() - pV(BFPNames::DSCImageSizeStr).toSize().height()) * scaleFactor() / 2
            : 0;
    hotLogoPos.setY(hotLogoPos.y() + mulitLayoutOffset * sign);

    // 对最终的logo的位置进行限制，保持显示在图像内
    if (pBV(BFPNames::PanZoomSelectStr) && pIV(BFPNames::ImageZoomCoefStr) > 100)
    {
        calHotoLogoPosWhenPanzoom(probeDataInfo, imageTopLeft, rotation, x, y, logoX, logoY);
    }
    else
    {
        // 对最终的logo的位置进行限制，保持显示在图像内
        if (rotation != 90 && rotation != 270)
        {
            logoX = qBound<qreal>(x + m_verticalRuler.getFontWith() / 2, hotLogoPos.x(),
                                  windowLocalRect().width() - (x - m_verticalRuler.getFontWith() / 2) -
                                      m_hotLogo.pixmap().width());
            logoY = qBound<qreal>(y / 2, hotLogoPos.y(), localRect().height() - m_hotLogo.pixmap().height() - y / 2);
        }
        else
        {
            logoX = qBound<qreal>(x + m_verticalRuler.getFontHeight(), hotLogoPos.x(),
                                  windowLocalRect().width() -
                                      (x - ((m_verticalRuler.getFontHeight() - m_hotLogo.pixmap().height()) / 2.0)) -
                                      m_hotLogo.pixmap().height());
            logoY = qBound<qreal>(y / 2.0 + m_verticalRuler.getFontWith() / 2, hotLogoPos.y(),
                                  localRect().height() - m_hotLogo.pixmap().height() -
                                      (y / 2.0 + m_verticalRuler.getFontWith() / 2));
        }
    }

    m_hotLogo.setPos(logoX, logoY);
    //记录上一次的位置
    m_preHotoLogoPos = m_hotLogo.pos();
    m_posInfo.Up = pBV(BFPNames::UpStr);
    m_posInfo.Left = pBV(BFPNames::LeftStr);
    //    保持C标记显示状态一直为正
    QPixmap pix;
    QTransform rotateMatrix;
    rotateMatrix.rotate(-rotation);
    initialHotLogo();
    pix = m_hotLogo.pixmap().transformed(rotateMatrix, Qt::SmoothTransformation);
    m_hotLogo.setPixmap(pix);

    resetGraphicItemPosY(&m_hotLogo);
}

void PureBWidget::tgcPointsChanged(QVariant points)
{
    QVector<QPointF> pointVector = points.value<QVector<QPointF>>();

    const_cast<ISonoParameters*>(&sonoParameters())->setPV(BFPNames::TGCPOINTSStr, points);
}

void PureBWidget::depthLogoChanged()
{
    qDebug() << "*********************depthLogoChanged*********************";
    if (m_DepthLogoVisible)
    {
        if (!m_depthLogo.isVisible())
        {
            m_depthLogo.setVisible(true);
        }
    }
}

void PureBWidget::setItemVisible(bool isActiveB)
{
    m_tgc.setIsActiveB(isActiveB);
    m_tgc.setVisible(isActiveB);
    m_biopsy.setVisible(isActiveB);
    m_centerLine.setVisible(isActiveB);
    m_needleLine.setVisible(isActiveB);
}

void PureBWidget::setupFreeHand3DRoiPen()
{
    QPen pen = QColor(ModelUiConfig::instance().value(ModelUiConfig::RoiActiveColor).toUInt());
    pen.setStyle(Qt::SolidLine);
    setFreeHand3DRoiPen(pen);
}

void PureBWidget::setupHotLogoLinear(const ProbeDataInfo& probeDataInfo, const int hotLogoLine,
                                     const QPointF& predefinedPos)
{

    bool zoomOn = pBV(BFPNames::ZoomOnStr);

    const qreal r = hotLogoR();

    //以hotLogoScanLine和hotLogoDepth进行定位，得到中心点的对应位置
    const QPointF tryPosWithouSteer = predefinedPos - QPointF(r, 0);
    //此时的坐标系以imageZoomCoef框上边界中点为原点(不考虑flip)

    //当前坐标未叠加steering的计算
    //叠加steeringRad

    const qreal steeringRad = IAreaSource::paramShowValueAngle2Rad(&sonoParameters(), BFPNames::BSteeringScanStr);

    QPointF tryPosWithSteer;

    tryPosWithSteer.rx() = tryPosWithouSteer.x() - tryPosWithouSteer.y() * std::sin(steeringRad);

    tryPosWithSteer.ry() = tryPosWithouSteer.y() * std::cos(steeringRad);

    setHotLogoPosWithClamp(tryPosWithSteer);

#ifdef USE_GRAPHICS_DEBUG

    QPointF predefinedPosWithSteer;

    predefinedPosWithSteer.rx() = predefinedPos.x() - predefinedPos.y() * std::sin(steeringRad);
    predefinedPosWithSteer.ry() = predefinedPos.y() * std::cos(steeringRad);

    static const int EXTEND_SCALER = 5;

    QVector2D rotateOrigin(predefinedPos.x(), 0);

    QVector2D parallelVector = (QVector2D(predefinedPosWithSteer) - rotateOrigin) * EXTEND_SCALER;

    QVector2D hotLogoLineExtendEnd = rotateOrigin + parallelVector;

    QVector2D assistLineEnd = QVector2D(tryPosWithSteer) + parallelVector;

    QTransform transform = flipAndRectZoomCoefTransform();

    m_debugHotLogo[0].setLine(QLineF(rotateOrigin.toPointF() * transform, hotLogoLineExtendEnd.toPointF() * transform));

    m_debugHotLogo[1].setLine(QLineF(predefinedPosWithSteer * transform, tryPosWithSteer * transform));

    m_debugHotLogo[2].setLine(QLineF(tryPosWithSteer * transform, assistLineEnd.toPointF() * transform));

#endif
}

void PureBWidget::setHotLogoPosWithClamp(const QPointF& posInZoomCoef)
{

    const QTransform transform = flipAndRectZoomCoefTransform();

    QPointF posInRect = posInZoomCoef * transform;

    const qreal r = hotLogoR();

    // clamp into image zone

    const qreal imageZoomCoef = scaleFactor();

    const qreal xCap = (1 - imageZoomCoef) / 2 * rect().width(), yCap = (1 - imageZoomCoef) / 2 * rect().height();

    const qreal width = rect().width(), height = rect().height();

    const qreal finalX = Formula::clampWithSize(posInRect.x(), -r, r * 2, xCap, width - xCap),

                finalY = Formula::clampWithSize(posInRect.y(), -r, r * 2, yCap, height - yCap);

    m_hotLogo.setPos(finalX - m_hotLogo.pixmap().width() / 2, finalY - m_hotLogo.pixmap().height() / 2);

    resetGraphicItemPos(&m_hotLogo);
}

bool PureBWidget::combinedUp() const
{
    return !(pBV(getActiveBParaName(BFPNames::UpStr)) ^ (pIV(getActiveBParaName(BFPNames::RotationStr)) <= 90));
}

bool PureBWidget::isLRLayoutHorizontalRotate() const
{
    int rotation = pIV(getActiveBParaName(BFPNames::RotationStr));
    // 2B, B/M, BBC 90度,270度水平旋转,
    // rect.width = 512 = ImageHeight, rect.height = 320 = ImageWidth/2
    return (rotation == 90 || rotation == 270) && (rect().height() == pV(BFPNames::ImageSizeStr).toSize().width() / 2);
}

qreal PureBWidget::scaledBDepth(qreal depth)
{
    if (!isLRLayoutHorizontalRotate() || (pIV(BFPNames::ImageZoomCoefStr) != 100))
    {
        return scaledLength(depth);
    }
    else
    {
        // 水平旋转，并且是2B, B/M, BBC模式，只去处Rect顶部与图像顶部间的距离
        return depth - pIV(BFPNames::ZoomInTopOffsetStr) *
                           (1.0 / (pIV(BFPNames::ImageZoomCoefStr) /
                                   100.0)); //这里后面跟的系数 为了让panzoom < 100  旋转 90 状态下 标尺长度正确
    }
}

void PureBWidget::setupHotLogoConvex(const ProbeDataInfo& probeDataInfo, const int hotLogoLine,
                                     const QPointF& predefinedPos)
{

    ProbeParameters probeParam(probeDataInfo, pRV(getActiveBParaName(BFPNames::StartDepthMMStr)));

    const qreal r = hotLogoR();

    bool zoomOn = pBV(BFPNames::ZoomOnStr);

    // HotLogoLine相对于图像垂直线+y方向顺时针旋转的角度
    qreal radOffsetY = (zoomOn ? probeParam.convexProbeLineAngleRad(pIV(BFPNames::ZoomMidLineStr) - hotLogoLine)
                               : probeParam.probeAngleRad() / 2 - probeParam.convexProbeLineAngleRad(hotLogoLine));

    // radOffsetY 不允许为钝角，如果是钝角，在edgeLimitHotLogoPos中，会改变hotlogo的
    //左右方向会颠倒，出现钝角的探头有EBit的V7-E
    // OMAP平台由于qreal为float，如果直接用M_PI_2,仍然可能不对，所以-0.0001
#ifdef WIN32
    const qreal MAX_RAD = 45;
#else
    const qreal MAX_RAD = (M_PI_2 - 0.0001);
#endif
    if (radOffsetY > MAX_RAD || RealCompare::AreEqual(radOffsetY, MAX_RAD))
    {
        radOffsetY = MAX_RAD;
    }

    //以hotLogoScanLine和hotLogoDepth进行定位，得到中心点的对应位置
    const QPointF tryPos = predefinedPos - QPointF(r * std::cos(radOffsetY), r * std::sin(radOffsetY));

    const qreal width = windowRect().width(), height = windowRect().height();

    const qreal scaledHalfWidth = scaledLength(width / 2);

    bool xCrossEdge = (tryPos.x() < -scaledHalfWidth + r), yCrossEdge = (tryPos.y() < r);

    QPointF considerPos = tryPos;

    QPointF center = scanLineAndDepthScreenPos(probeParam.lines() / 2, -probeDataInfo.WaferRadius, false);

    if ((xCrossEdge || yCrossEdge) && (!probeDataInfo.IsLinear))
    {
        considerPos = edgeLimitHotLogoPos(xCrossEdge, yCrossEdge, center, predefinedPos, radOffsetY);

        if (xCrossEdge)
        {
            Q_ASSERT(considerPos.x() > tryPos.x());
        }
        else
        {
            Q_ASSERT(considerPos.y() > tryPos.y());
        }
    }

    setHotLogoPosWithClamp(considerPos);

    // debug draw

#ifdef USE_GRAPHICS_DEBUG

    QTransform transform = flipAndRectZoomCoefTransform();

    static const int EXTEND_SCALER = 5;

    QVector2D parallelVector = (QVector2D(predefinedPos) - QVector2D(center)) * EXTEND_SCALER;

    QVector2D hotLogoLineExtendEnd = QVector2D(center) + parallelVector;

    QVector2D assistLineEnd = QVector2D(tryPos) + parallelVector;

    m_debugHotLogo[0].setLine(QLineF(center * transform, hotLogoLineExtendEnd.toPointF() * transform));

    //绘制两条debug线，1是从圆心指向hotlogoline的半径
    // 2是从hotlogodepth指向hotlogoline垂直方向的外切圆半径
    m_debugHotLogo[1].setLine(QLineF(predefinedPos * transform, tryPos * transform));

    m_debugHotLogo[2].setLine(QLineF(tryPos * transform, assistLineEnd.toPointF() * transform));
#endif
}

void PureBWidget::setupCenterLine()
{
    bool centerLineVisible = pBV(BFPNames::IsCenterLineVisibleStr);

    // 2023-06-15 Modify by AlexWang [bug:65122] 使用中心线的参数值和PureBWidget的visible属性值来做判定是否显示中心线
    if (!centerLineVisible || !isVisible())
    {
        m_centerLine.setVisible(false);
        return;
    }

    m_centerLine.setVisible(true);

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));

    ProbeParameters probeParam(probeDataInfo, pRV(getActiveBParaName(BFPNames::StartDepthMMStr)),
                               pRV(getActiveBParaName(BFPNames::PixelSizeMMStr)), pBV(BFPNames::ZoomOnStr));
    probeParam.setTotalLines(pIV(BFPNames::B_RX_LNUMStr));

    // 2023-06-15 Modify by AlexWang [bug:65125] 无论是否在局部放大状态，中心线始终保持在原始图像的中心
    int middleLine = probeParam.middleLineNo();

    BFCoordTransform bfTrans = coordTransform();
    QPointF originBegin;
    bfTrans.convertPhysicsToLogic(middleLine, 0, originBegin);

    QPointF originEnd;
    bfTrans.convertPhysicsToLogic(middleLine, probeParam.bottomDepthMM(pRV(BFPNames::DepthMMStr)), originEnd);

    QTransform transform = flipAndRectZoomCoefTransform();

    QPointF offset = QPointF(imageRectStartXInWindowRect(), imageRectStartYInWindowRect());

    qreal panzoomOffsetX = 0;
    if (pBV(BFPNames::PanZoomSelectStr))
    {
        panzoomOffsetX = ((pIV(BFPNames::RotationStr) == 0 || pIV(BFPNames::RotationStr) == 90) ? -1.0 : 1.0) *
                         pRV(BFPNames::PanZoomOffsetWidthPixelStr);
    }
    originBegin.setX(originBegin.x() + panzoomOffsetX);
    originEnd.setX(originEnd.x() + panzoomOffsetX);

    //掌超 放到平移 需要考虑中心线的变化，需要和主分支合并 TODO
    int transX = pV(BFPNames::ImageTranslateXStr).toDouble() * windowRect().width() / 2;
    int transY = -pV(BFPNames::ImageTranslateYStr).toDouble() * windowRect().height() / 2;
    transform = (transform * QTransform::fromTranslate(transX, transY));
    if (pBV(BFPNames::PanZoomSelectStr))
    {
        qreal offsetY = (pIV(BFPNames::RotationStr) == 270 || pIV(BFPNames::RotationStr) == 180)
                            ? (windowRect().height() - m_verticalRuler.pos().y())
                            : m_verticalRuler.pos().y();
        if (pIV(BFPNames::RotationStr) == 90 || pIV(BFPNames::RotationStr) == 270)
        {
            m_centerLine.setPos(QPointF(originBegin * transform).x() + offset.x(), offsetY);
            m_centerLine.setTailPos(
                QPointF(QPointF(originEnd * transform).x() + offset.x(),
                        offsetY + QPointF(originEnd * transform).y() - QPointF(originBegin * transform).y()));
        }
        else
        {
            m_centerLine.setPos(QPointF(originBegin * transform).x() + offset.x(), offsetY);
            m_centerLine.setTailPos(
                QPointF(QPointF(originEnd * transform).x() + offset.x(),
                        offsetY + QPointF(originEnd * transform).y() - QPointF(originBegin * transform).y()));
        }

        //        qCritical()<<"originBegin"<<originBegin
        //                   <<"originEnd"<<originEnd
        //                   <<"panzoomOffsetX"<<panzoomOffsetX
        //                   <<"Bfp_panzoomOffsetX"<<pRV(BFPNames::PanZoomOffsetWidthPixelStr)
        //                   <<"transX"<<transX
        //                   <<"transY"<<transY
        //                   <<"offsetY"<<offsetY
        //                   <<"m_centerLine_pos"<<m_centerLine.pos()
        //                   <<PRETTY_FUNCTION;
    }
    else
    {
        m_centerLine.setPos(originBegin * transform + offset);
        m_centerLine.setTailPos(originEnd * transform + offset);
    }

    m_centerLine.setPixelSizeMM(pRV(getActiveBParaName(BFPNames::PixelSizeMMStr)));
}

void PureBWidget::setupNeedleLine()
{
    bool needleLineVisible = pBV(BFPNames::NeedleModeStr);

    if (!needleLineVisible || !m_active)
    {
        m_needleLine.setVisible(false);
        return;
    }

    m_needleLine.setVisible(true);

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));

    if (!probeDataInfo.IsLinear)
    {
        m_needleLine.setVisible(false);
        return;
    }

    ProbeParameters probeParam(probeDataInfo, pRV(getActiveBParaName(BFPNames::StartDepthMMStr)),
                               pRV(getActiveBParaName(BFPNames::PixelSizeMMStr)), pBV(BFPNames::ZoomOnStr));

    const QVariant& variant = parameter(BFPNames::NeedleAngleIndexStr)->showValue();
    qreal angle = variant.toFloat();
    BFCoordTransform bfTrans = coordTransform();

    QPointF originBegin;
    // 设置起始点
    // 2016-10-28 BUG:6645 FPGA实现的super needle的原理是，对needle偏转的数据，采的图像数据
    // 是完整宽度的图像，进行偏转时，偏转图像始终会按照下面的形式组成，不管是在放大，还是非放大状态
    // 所以needle line是从StartDepthMM的位置开始绘制
    //                 --------
    //                /|     /|
    //               / |    / |
    //              /  |   /  |
    //             /   |  /   |
    //            /------/    |
    //                 |------|

    if (angle >= qreal(0.0))
    {
        bfTrans.convertPhysicsToLogic(pIV(BFPNames::StopLineStr), pRV(BFPNames::StartDepthMMStr), originBegin);
    }
    else
    {
        bfTrans.convertPhysicsToLogic(pIV(BFPNames::StartLineStr), pRV(BFPNames::StartDepthMMStr), originBegin);
    }
    //    qDebug() << PRETTY_FUNCTION << "StartLine is " << pIV(BFPNames::StartLineStr);
    //    qDebug() << PRETTY_FUNCTION << "StopLine is " << pIV(BFPNames::StopLineStr);
    //    qDebug() << PRETTY_FUNCTION << "originBegin is " << originBegin;

    m_needleLine.setLength(probeParam.bottomDepthMM(pRV(BFPNames::DepthMMStr)) /
                           pRV(getActiveBParaName(BFPNames::PixelSizeMMStr)));
    m_needleLine.setInnerRotation(angle);
    m_needleLine.setPos(originBegin * flipAndRectZoomCoefTransform());
    resetGraphicItemPos(&m_needleLine);

    //掌超 放到平移 需要考虑needleLine的变化，需要和主分支合并 TODO
    int transX = pV(BFPNames::ImageTranslateXStr).toDouble() * windowRect().width() / 2;
    int transY = -pV(BFPNames::ImageTranslateYStr).toDouble() * windowRect().height() / 2;
    m_needleLine.setTransform(flipTransform() * QTransform::fromTranslate(transX, transY));
}

void PureBWidget::onTGCHideTimeChanged(const QString& property)
{
    if (property == "TGCHideTime")
    {
        setupTGC();
    }
    else if (property == "HideTGCNow" && Setting::instance().defaults().tGCHideTime() == -1)
    {
        m_tgc.setVisible(false);
    }
}

void PureBWidget::onCurvedPanoInfoChanged()
{
    Parameter* parameter = static_cast<Parameter*>(sender());
    if (m_RealTimeSonoParameters != NULL && m_RealTimeSonoParameters->pBV(BFPNames::CurvedPanoramicEnableStr) &&
        parameter != NULL)
    {
        bool freeze = sonoParameters().pBV(BFPNames::FreezeStr);
        if (parameter->name() == BFPNames::CurvedPanoramicLengthStr)
        {
            m_CurvedPan.setCurvedCurrentLength(pV(BFPNames::CurvedPanoramicLengthStr).toReal());
        }
        else if (!freeze && parameter->name() == BFPNames::CurvedPanoramicROIVisibleStr)
        {
            m_CurvedPan.setCurvedRoiVisible(pV(BFPNames::CurvedPanoramicROIVisibleStr).toBool());
        }
        else if (!freeze && parameter->name() == BFPNames::CurvedPanoramicPointListStr)
        {
            m_CurvedPan.setCurvedRectLine(pV(BFPNames::CurvedPanoramicPointListStr).toList());
        }
    }
}

void PureBWidget::onFreezeChanged()
{
    bool freeze = sonoParameters().pBV(BFPNames::FreezeStr) || !sonoParameters().isRealTime();
    bool biopsyVisible = pBV(BFPNames::IsBiopsyVisibleStr);

    QPen pen;
    if (biopsyVisible)
    {
        pen = m_biopsy.linePen();
        pen.setColor(freeze ? QColor(ModelUiConfig::instance().value(ModelUiConfig::BiopsyFreezeColor).toUInt())
                            : QColor(
#ifndef SYS_APPLE
                                  ModelUiConfig::instance().value(ModelUiConfig::BiopsyColor).toUInt()
#else
                                  255, 255, 255
#endif
                                      ));
        m_biopsy.setLinePen(pen);
    }
}

void PureBWidget::setupLGC()
{
    if (!m_active || pBV(BFPNames::HideLGCForCineStr))
    {
        m_tgc.setVisible(false);
        return;
    }
    m_LGCController.setWidgetArea(windowRect());
    m_LGCController.setLGC(&m_LGC);
    m_LGCController.setArea(calcLGCRect());
    m_LGCController.setSonoParameters(&sonoParameters());
    m_LGCController.setupLGCVisible();
}

QRectF PureBWidget::calcLGCRect()
{
    qreal rectWidth = this->rect().width() * 1.0f;
    int rotation = pIV(BFPNames::RotationStr);

    //    qreal width = qMin<qreal>(scanAreaWidth(), rectWidth),
    qreal width = rectWidth, height = (LGC_HEIGTH * 1.0f) / (pBV(BFPNames::HalfHeight2Str) ? 2.0f : 1.0f),
          x = (rectWidth - width) / 2.0f,
          y = scaledYInRectCoord(0) + 0.8f +
              pRV(BFPNames::PanZoomOffsetDepthPixelStr) *
                  ((rotation == 0 || rotation == 270) ? -1.0 : 1.0); // 0.8 for line width

    QRectF rect(x, y, width, height);

    rect.translate(imageRectStartXInWindowRect(), imageRectStartYInWindowRect());

    const_cast<ISonoParameters*>(&sonoParameters())->setPV(BFPNames::LGCRectStr, rect);

    QList<QVariant> points;
    for (int index = 0; index < LGC_COUNT; index++)
    {
        points.append(m_LGC.getLgcParam().data(index));
    }

    const_cast<ISonoParameters*>(&sonoParameters())->setPV(BFPNames::LGCPOINTSStr, points);

    return rect;
}

void PureBWidget::onImageZoomCoefChanged(const QVariant& value)
{
    Q_UNUSED(value)
    m_LGCController.setArea(calcLGCRect());
}

void PureBWidget::setupFreeHand3DRectItem()
{
    m_FreeHand3DRoiItem.setRect(pV(BFPNames::FreeHand3DRoiRectStr).toRect());
}

void PureBWidget::setupHorizontalRulerVisible()
{
    bool centerLineVisible = pBV(BFPNames::IsBHorizontalRulerVisibleStr);

    enableHorizontolRuler(centerLineVisible);
}

void PureBWidget::setupTeeProbeAngleItem()
{
    m_teeAngle.setVisible(pBV(BFPNames::TeeFlagStr));
    m_teeAngle.setPos(QPoint(50, 50));
    m_teeAngle.setTeeProbeAngle(pRV(BFPNames::TeeProbeAngleStr));
}

void PureBWidget::setDebugDraw(bool enable)
{
    m_debugRuler.setParentItem(enable ? &m_verticalRuler : NULL);

    BOOST_FOREACH (QGraphicsLineItem& item, m_debugHotLogo)
    {
        item.setParentItem(enable ? partitionNode() : NULL);
    }
}

bool PureBWidget::isConnected() const
{
    return m_IsConnected;
}

void PureBWidget::setFreeHand3DRoiPen(const QPen& pen)
{
    m_FreeHand3DRoiItem.setPen(pen);
    m_FreeHand3DRoiItem.update();
}

void PureBWidget::setFreeHand3DRoiVisible(bool visible)
{
    m_FreeHand3DRoiItem.setVisible(visible);
}

void PureBWidget::changeSizeAndClipStatus()
{
    QRectF retRect;
    if (!getRectByZoomCoef(retRect))
    {
        return;
    }

    int layout = pIV(BFPNames::LayoutStr);
    int rotation = pIV(BFPNames::RotationStr);
    if (layout == Layout_1x1)
    {
        if (!SystemScanModeClassifier::isLRLayout(pIV(BFPNames::SystemScanModeStr)) &&
            (!pBV(BFPNames::BCImagesOnStr)) && (rotation == 90 || rotation == 270))
        {
            partitionNode()->setRect(retRect);
            m_rulerGroup.setRect(retRect);
        }
    }

    //让标尺能够在4B区域完整的显示
    if (layout == Layout_2x2)
    {
        m_rulerGroup.setRect(retRect);
        partitionNode()->setRect(retRect);
        partitionClipNode()->setRect(retRect);
    }
}

void PureBWidget::setupBiopsy()
{
    bool biopsyVisible = pBV(BFPNames::IsBiopsyVisibleStr);

    // TODO change circle visible when moving
    m_biopsy.setMovingCircleVisible(false);

    if (!biopsyVisible || !m_active)
    {
        m_biopsy.setVisible(false);

        return;
    }

    m_biopsy.setVisible(true);

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));

    //首先将相对于放大前图像区顶部中点的偏移坐标（像素）
    //转换到物理坐标
    //再使用BFCoordTransform将其转换到当前的图像区坐标（放大，或者缩放后）

    BFDepthParameters depthP(pIV(BFPNames::ADFreqMHzStr), pV(BFPNames::ImageSizeStr).toSize().height(),
                             pIV(BFPNames::CQYZStr), false);
    depthP.setPixelLen(BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) /
                       pV(BFPNames::FixedSWImageZoomCofStr).toFloat());
    qreal pixelSizeMM = depthP.pixelSizeMM();

    ProbeParameters probeParam(probeDataInfo, pRV(getActiveBParaName(BFPNames::StartDepthMMStr)), pixelSizeMM,
                               pBV(BFPNames::ZoomOnStr));

    BFCoordTransform original = BFCoordTransform(probeDataInfo, 0, pixelSizeMM, pIV(BFPNames::B_RX_LNUMStr));

    qreal posX;
    if (sonoParameters().pIV(BFPNames::BiopsyAngleChooseStr) == -1)
    {
        posX = pRV(BFPNames::BiopsyXPosMMStr);
    }
    else
    {
        posX = qBound(pRV(BFPNames::BiopsyPositionMinStr), pRV(BFPNames::BiopsyXPosMMStr),
                      pRV(BFPNames::BiopsyPositionMaxStr));
    }
    QPointF origLogic(
        (-posX /*pRV(BFPNames::BiopsyXPosMMStr) + pRV(BFPNames::BiopsyXPosMMOffsetStr)*/) / pixelSizeMM,
        (pRV(BFPNames::BiopsyYPosMMStr) + probeParam.arcFloorDisMM() + pRV(BFPNames::BiopsyYPosMMOffsetStr)) /
            pixelSizeMM);

    qreal angle;
    if (sonoParameters().pIV(BFPNames::BiopsyAngleChooseStr) == -1) //如果该值为-1，代表当前模式为Free
    {
        angle = pRV(BFPNames::BiopsyAngleStr) /* + pRV(BFPNames::BiopsyAngleOffsetStr)*/;
    }
    else
    {
        angle = qBound(pRV(BFPNames::BiopsyAngleMinStr),
                       pRV(BFPNames::BiopsyAngleStr) /* + pRV(BFPNames::BiopsyAngleOffsetStr)*/,
                       pRV(BFPNames::BiopsyAngleMaxStr));
    }

    //此值必须非常大，过小将导致后续的计算中靠近中线的位置
    //误差很大，角度跳动
    static const qreal RANDOM_LENGTH = 10000;

    //非放大模式下，穿刺线坐标系原点对齐在图像区上边界中点
    //使用BFCoordTransform计算较为方便

    //
    //相对于完整图像区（非放大，startDepth=探头表面＝0处）的上边界中点

    //在线上获取一点进行结尾 点的计算
    QPointF origLogicEnd(origLogic +
                         QPointF(qCos(Formula::degree2Rad(angle)), qSin(Formula::degree2Rad(angle))) * RANDOM_LENGTH);

    ProbePhysicalPointF probePhysicalPoint = original.convertPtToPhysics(origLogic),
                        probePhysicalEndPoint = original.convertPtToPhysics(origLogicEnd);

    BFCoordTransform secondStage = coordTransform();

    //在coef框内上边界中点为原点的坐标系下
    QPointF screenPos = secondStage.convertPhysicsToLogic(probePhysicalPoint),
            screenEndPos = secondStage.convertPhysicsToLogic(probePhysicalEndPoint);

    QTransform flipAndRectZoomCoefTrans = flipAndRectZoomCoefTransform();

    QTransform flipTrans = flipTransform();

    QPointF biopsyPos(screenPos * flipAndRectZoomCoefTrans);

    m_biopsy.setPos(biopsyPos + QPointF(imageRectStartXInWindowRect(), imageRectStartYInWindowRect()));

    //计算交叉线与startLine的交点
    QPointF startLineBeginPos = secondStage.convertPhysicsToLogicF(
        ProbePhysicalPointF(pIV(BFPNames::StartLineStr), pRV(getActiveBParaName(BFPNames::StartDepthMMStr))));

    QPointF startLineEndPos = secondStage.convertPhysicsToLogicF(ProbePhysicalPointF(
        pIV(BFPNames::StartLineStr), pRV(getActiveBParaName(BFPNames::StartDepthMMStr)) + RANDOM_LENGTH));

    QPointF intersection = Formula::lineIntersection(screenPos, screenEndPos, startLineBeginPos, startLineEndPos);

    qreal tagOffset = QVector2D(intersection - screenPos).length();

    m_biopsy.setTagOffset(tagOffset);

    qreal physicalOffset = tagOffset * pRV(getActiveBParaName(BFPNames::PixelSizeMMStr));

    m_biopsy.setTagText(QString("%1 mm").arg(physicalOffset));

#ifdef USE_GRAPHICS_DEBUG
    if (!pBV(BFPNames::ZoomOnStr))
    {
        m_biopsy.setScale(1);
    }
    else
    {
        BFDepthParameters bfDepthParameters(pIV(BFPNames::CQYZStr), pBV(BFPNames::HalfHeightStr), true,
                                            pIV(BFPNames::ZoomedCQYZStr));
        bfDepthParameters.setPixelLen(BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) /
                                      pV(BFPNames::FixedSWImageZoomCofStr).toFloat());
        qreal factor = bfDepthParameters.realZoomMulti();
        m_biopsy.setScale(factor);
    }
#endif

    QPair<QTransform, qreal> rotateTrans = Formula::rotateTransform(screenPos, screenEndPos);

    //掌超 放到平移 需要考虑biopsy的变化，需要和主分支合并 TODO
    int transX = pV(BFPNames::ImageTranslateXStr).toDouble() * windowRect().width() / 2;
    int transY = -pV(BFPNames::ImageTranslateYStr).toDouble() * windowRect().height() / 2;

    m_biopsy.setTransform(rotateTrans.first * flipTrans * QTransform::fromTranslate(transX, transY));

    if (pBV(BFPNames::PanZoomSelectStr) && pIV(BFPNames::ImageZoomCoefStr) > 100)
    {
        if (pIV(BFPNames::RotationStr) == 0 || pIV(BFPNames::RotationStr) == 180)
        {
            m_biopsy.setPos(m_biopsy.x() +
                                ((!pIV(BFPNames::LeftStr)) ? (-m_centerPanzoomOffsetX) : m_centerPanzoomOffsetX),
                            m_centerPanzoomOffsetY);
        }
        else
        {
            m_biopsy.setPos(m_biopsy.x() +
                                ((!pIV(BFPNames::LeftStr)) ? (-m_centerPanzoomOffsetX) : m_centerPanzoomOffsetX),
                            m_centerPanzoomOffsetY);
        }
    }
}
