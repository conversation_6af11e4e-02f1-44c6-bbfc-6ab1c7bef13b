/*
 * =====================================================================================
 *
 *       Filename:  sharedglyphswidget.h
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  2012年07月30日 14时57分51秒
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  WangXinyu (), <EMAIL>
 *   Organization:
 *
 * =====================================================================================
 */

#ifndef SHARED_GLYPHS_WIDGET_H

#define SHARED_GLYPHS_WIDGET_H
#include "usfinterfaceimagingui_global.h"

#include "baseglyphswidget.h"

#include <QGraphicsItemGroup>
#include <QScopedPointer>

#include "declmacro.h"

class QGraphicsScene;
class QTransform;
class BFCoordTransform;

/**
 * @brief 独立绘制的图形元素（但与sonoParameters有关联）
 *   如ROI框和DopplerScanLine，可用于与pairwidget一起使用，实现组合功能
 */
class USF_INTERFACE_IMAGING_COMMON_UI_EXPORT SharedGlyphsWidget : public BaseGlyphsWidget
{
    Q_OBJECT
public:
    SharedGlyphsWidget(QObject* parent, const ISonoParameters& sonoParameters, const QRect& windowRect,
                       const QRect& rect, const QRect& region, int index = -1);

    virtual ~SharedGlyphsWidget()
    {
    }

    /**
     * @brief 获取本控件窗口在父窗口中的rect位置
     *
     * */
    GETTER(const QRect&, rect);
    // modify by wangmingsheng 此处需要和基类BaseGlyphsWidget的接口相同，
    // 以保证在multiwidget调用的时候可以正常解析到这个方法, 返回图像区
    virtual QRect rect()
    {
        return this->m_rect;
    }

    /**
     * @brief 获取本窗口控件的本地坐标rect(与rect相同，但是以0,0为起点)
     */
    GETTER(const QRect&, localRect);

    /**
     * @brief 获取本控件可供编辑的区域，不受rotate影响，编辑包括测量、注释、体标等
     */
    virtual QRectF region() const
    {
        return m_region;
    }

    /**
     * @brief 返回图像所在窗体的区域（渲染区），原因是图像区可能没有显示区大
     */
    virtual QRect windowRect()
    {
        return m_WindowRect;
    }

    /**
     * @brief 返回图像所在窗体的区域（渲染区），原因是图像区可能没有显示区大
     */
    virtual QRect windowLocalRect()
    {
        return m_WindowLocalRect;
    }

    void setRect(QRect rect)
    {
        m_rect = rect;
    }

    void setLocalRect(QRect rect)
    {
        m_localRect = rect;
    }

    void setWindowRect(QRect rect)
    {
        m_WindowRect = rect;
    }

    void setWindowLocalRect(QRect rect)
    {
        m_WindowLocalRect = rect;
    }

    void setRegion(QRect rect)
    {
        m_region = rect;
    }
    /**
     * @brief 标尺和activeTag在左上角绘制时偏移的一个小量
     */
    static const QPointF ORIGIN_OFFSET;

    /**
     * @brief 根据当前的Up/Left参数生成Transform
     *        变换后的坐标以本地坐标系原点(0,0)为旋转点
     *
     * @return
     */
    QTransform flipTransform();

    /**
     * @brief 根据当前的Up/Left和localRect获得变换矩阵(包括图像中心缩放的影响)
     *        变换前的坐标为 以探头方向图像区（经过缩放后）顶边(不考虑Up/Left的影响)中心点为原点的坐标系
     *        变换后的坐标为 以localRect左上角为原点的坐标系
     *
     * @return
     */
    QTransform flipAndRectZoomCoefTransform();

    //    QTransform flipAndZoomTransform();

    /**
     * @brief 缩放区域中Y=y线位置处于rect坐标系中的位置
     * @param originY
     * @return
     */
    qreal scaledYInRectCoord(qreal originY);

    /**
     * @brief 缩放区域中Y=y线位置处于windowRect坐标系中的位置
     * @param originY
     * @return
     */
    qreal scaledYInWindowRectCoord(qreal originY);

    /**
     * @brief 图像区相对渲染区起始位置Y方向偏差(不考虑缩放的情况下）
     * @return
     */
    qreal imageRectStartYInWindowRect();

    /**
     * @brief 图像区相对渲染区起始位置Y方向偏差(不考虑缩放的情况下）
     * @return
     */
    qreal imageRectStartXInWindowRect();

    /**
     * @brief 根据渲染区与图像区的位置关系，调整图元的位置到合适位置
     */
    void resetGraphicItemPos(QGraphicsItem* item);

    /**
     * @brief 根据渲染区与图像区的位置关系，调整图元X方向的位置到合适位置
     */
    void resetGraphicItemPosX(QGraphicsItem* item);

    /**
     * @brief 根据渲染区与图像区的位置关系，调整图元Y方向的位置到合适位置
     */
    void resetGraphicItemPosY(QGraphicsItem* item);

    /**
     * @brief 经过缩放后，rect中原始长度为length的线现在在缩放区域中的长度
     *
     * @param length
     *
     * @return
     */
    qreal scaledLength(qreal length);

    /**
     * @brief imageZoomCoef缩放数值，范围[0~1]
     *
     * @return
     */
    qreal scaleFactor();

    virtual bool isComposite() const
    {
        return false;
    }

    virtual SubPartitionList subPartitions()
    {
        return SubPartitionList();
    }

    /**
     * @brief 返回当前探头指定扫描线和深度点的坐标
     *        此坐标经过pixelMM变换（已经叠加imageZoomCoef）,
     *        以探头x中线为x=0，
     *        以经过imageZoomCoef变换后的图像上边缘线为y=0
     *        结果需要配合flipTransform和
     *        注：此计算不考虑up/left翻转
     *
     * @param scanLine 扫描线号
     * @param depth 深度
     * @param useBCoordinate 是否使用B模式坐标，默认使用B模式坐标，如果为false，则使用C模式坐标
     * @return
     */
    QPointF scanLineAndDepthScreenPos(int scanLine, qreal depth, bool useBCoordinate = true);
    /**
     * @brief scanAreaWidth 扫查区域的宽度
     * @return
     */
    qreal scanAreaWidth();

protected:
    virtual BFCoordTransform coordTransform() const;

private:
    QRect m_rect; // 图像区区域
    QRect m_localRect;
    QRect m_WindowRect; // 渲染区区域
    QRect m_WindowLocalRect;
    QRect m_region;
};

/**
 * @brief The RotatableGlyphsWidget class 支持旋转功能的GlyphsWidget
 */
class USF_INTERFACE_IMAGING_COMMON_UI_EXPORT RotatableGlyphsWidget : public SharedGlyphsWidget
{
    Q_OBJECT
public:
    RotatableGlyphsWidget(QObject* parent, const ISonoParameters& sonoParameters, const QRect& windowRect,
                          const QRect& rect, const QRect& region, int index = -1);

    virtual ~RotatableGlyphsWidget()
    {
    }

protected:
    bool getRectByZoomCoef(QRectF& retRect);
protected slots:
    virtual void changeSizeAndClipStatus();

private:
    void rotate(const QVariant& value);
private slots:
    void onRotationChanged(const QVariant& value);
};

#endif /* end of include guard: SHARED_GLYPHS_WIDGET_H */
