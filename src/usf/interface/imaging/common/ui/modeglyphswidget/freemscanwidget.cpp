#include "freemscanwidget.h"
#include "freemline.h"
#include "freemarrow.h"
#include "bfpnames.h"
#include "parameter.h"
#include "isonoparameters.h"
#include <QtGlobal>
#include "probedataset.h"
#include "probeparameters.h"
#include "formula.h"
#include "commonareasource.h"
#include "arearesult.h"
#include "modeluiconfig.h"
#include "freemdatagenerator.h"
#include <QDebug>
#include "probephysicalgeometry.h"

const int LONGLINE = 500;
const int DELAY_TIME = 50;

FreeMScanWidget::FreeMScanWidget(QObject* parent, ISonoParameters& sonoParameter, const QRect& windowRect,
                                 const QRect& rect, const QRect& region)
    : PureBWidget(parent, sonoParameter, windowRect, rect, region)
    , m_FreeMLine(NULL)
    , m_ImageState(MixImageState)
    , m_Initialized(false)
{
    // 凸阵探头绘制区域
    m_ConvexAreaResult = new ConvexAreaResult(&sonoParameter, false, true, this);
    connect(m_ConvexAreaResult, SIGNAL(resultChanged()), this, SLOT(setupFan()));
    // 深度值等变化，需要重新绘制FreeMLine
    connect(m_ConvexAreaResult, SIGNAL(resultChanged()), this, SLOT(setupAllMLine()));

    m_AreaSource = new BAreaSource(&sonoParameter, rect.size(), this);
    m_ConvexAreaResult->setAreaSource(m_AreaSource);

    addItem(&m_ImageArea);
    onRotationChanged(pIV(BFPNames::RotationStr));
    m_LineManagerMent.setFreeMArea(&m_ImageArea);
    onMLineNumChanged();

    connectParameterSignals();
    onFreezeChanged();

    m_Timer.stop();
    m_Timer.setInterval(DELAY_TIME);
    m_Timer.setSingleShot(true);
    connect(&m_Timer, SIGNAL(timeout()), this, SLOT(sendFreeMLines()));
}

FreeMScanWidget::~FreeMScanWidget()
{
    m_FreeMLine = NULL;
    if (m_Timer.isActive())
    {
        //控制结束时，要关闭timer，否则timer会导致发送控制表
        m_Timer.stop();
    }
}

const FreeMLineManagerMent* FreeMScanWidget::lineManagerMent() const
{
    return &m_LineManagerMent;
}

QTransform FreeMScanWidget::flipAndRectCenterTransform()
{
    return Formula::flipAndRectCenterTransform(pBV(BFPNames::UpStr), !pBV(BFPNames::LeftStr), localRect().size());
}

void FreeMScanWidget::setLineWidth(bool scale)
{
    int lineNum = pIV(BFPNames::FreeMLineNumStr);
    for (int i = 0; i < lineNum; i++)
    {
        FreeMLine* freeMLine = m_LineManagerMent.mLine(i);
        freeMLine->setLineWidth(scale ? 2 : 1);
    }
}

void FreeMScanWidget::setupMLine()
{
    setupOneLine();

    if (m_Timer.isActive())
    {
        m_Timer.stop();
    }
    m_Timer.start();
}

void FreeMScanWidget::setupFan()
{
    QTransform transform = flipAndRectCenterTransform();

    // 设置扇形绘制偏移，默认是在０,0处
    m_ImageArea.setFanOffset(transform.map(m_ConvexAreaResult->fanCenterPos()));
    // 设置扇形的半径和角度
    // 此处减小半径的考虑时，目前线与底边存在误差值，导致下发的坐标值会超过512，会出现不正常图像
    // 偏移1.5是经过对比误差值得出的预估值，目的只是为了减少下发数据的误差，此方法仍有待改进
    qreal radiusOffset = 1.5;
    m_ImageArea.setFanRadius(m_ConvexAreaResult->radiusRange() - radiusOffset);
    // 0.0335的计算是由于FreeM图像宽度是用整数值,所有扇形区域的角度会增加0.0335
    qreal fanAngleOffset = m_ImageState == MixImageState ? 0.0335 : 0.0f;
    m_ImageArea.setFanAngle(Formula::rad2Degree(m_ConvexAreaResult->radianRange()) - fanAngleOffset);
    m_ImageArea.setTransform(transform);

    if (!m_Initialized)
    {
        resetGraphicItemPos(&m_ImageArea);
        m_Initialized = true;
    }

    calcLogicRegoin();
}

void FreeMScanWidget::setupAllMLine()
{
    int lineNum = pIV(BFPNames::FreeMLineNumStr), curIndex = pIV(BFPNames::FreeMLineNoStr);

    for (int i = 1; i <= lineNum; i++)
    {
        sonoParameters().parameter(BFPNames::FreeMLineNoStr)->setValue(i);
        //        setupMLine();
        setupOneLine();
        generateFreeMData();
    }

    // 恢复到当前线
    sonoParameters().parameter(BFPNames::FreeMLineNoStr)->setValue(curIndex);
}

void FreeMScanWidget::onFreezeChanged()
{
    if (m_FreeMLine == NULL)
    {
        return;
    }

    bool freeze = sonoParameters().pBV(BFPNames::FreezeStr) || !sonoParameters().isRealTime();

    // 冻结状态下的颜色变化
    m_FreeMLine->setLineColor(freeze ? QColor(ModelUiConfig::instance().value(ModelUiConfig::MLineFreezeColor).toUInt())
                                     : QColor(ModelUiConfig::instance().value(ModelUiConfig::CurFreeMLine).toUInt()));
}

void FreeMScanWidget::onMLineNumChanged()
{
    resetMLine();
}

void FreeMScanWidget::resetMLine()
{
    // 根据线的数量重新生成所有的FreeMLine
    // 先进行所有相关的清除工作，恢复原始状态
    m_LineManagerMent.removeLine();
    m_FreeMLine = NULL;

    int lineNum = pIV(BFPNames::FreeMLineNumStr);
    for (int i = 0; i < lineNum; i++)
    {
        FreeMLine* freeMLine = m_LineManagerMent.createAndAddLine();
        if (freeMLine != NULL)
        {
            freeMLine->setParentItem(&m_ImageArea);
            createArrow(freeMLine);
            sonoParameters().parameter(BFPNames::FreeMLineNoStr)->setValue(i + 1);
            onSwitchMLine();
            //            setupMLine();
            setupOneLine();
            generateFreeMData();
            freeMLine->setLineColor(m_LineManagerMent.lineColor(i));
        }
    }

    // 每次默认为第一根线为控制线
    sonoParameters().parameter(BFPNames::FreeMLineNoStr)->setValue(1);
    onSwitchMLine();
}

void FreeMScanWidget::switchMLine()
{
    // FreeMLineIndex是从１开始，而LineMangerMent是从０开始，所有通过下标访问扫查线的地方都需要减１处理
    m_FreeMLine = m_LineManagerMent.mLine(pIV(BFPNames::FreeMLineNoStr) - 1);
}

void FreeMScanWidget::onSwitchMLine()
{
    switchMLine();
    // 更新当前线的颜色
    updateCurLineColor();
}

void FreeMScanWidget::refreshColor()
{
    if (m_FreeMLine == NULL)
    {
        return;
    }

    m_FreeMLine->setLineColor(m_LineManagerMent.lineColor(pIV(BFPNames::FreeMLineNoStr) - 1));
}

void FreeMScanWidget::onRotationChanged(const QVariant& value)
{
    Q_UNUSED(value);

    // 旋转之后，重新设置限制矩形区域
    QRectF rect = localRect();
    m_ImageArea.setLimitRect(rect);
}

void FreeMScanWidget::sendFreeMLines()
{
    generateFreeMData();
}

void FreeMScanWidget::setupMLine(FreeMLine* freeMLine)
{
    if (freeMLine == NULL)
    {
        return;
    }

    // 先在起始点上建立一条垂直线，再围绕起始点进行旋转
    QPointF point = mPoint();
    validatedMPoint(point);

    QLineF line(point.x(), -LONGLINE, point.x(), point.y() + LONGLINE);
    freeMLine->resetTransform();
    freeMLine->setLine(line);
    freeMLine->setupPoint(point);

    rotateMLine(freeMLine, point);

    m_LineManagerMent.resetIntersectPt(freeMLine);
}

void FreeMScanWidget::createArrow(FreeMLine* freeMLine)
{
    if (freeMLine == NULL)
    {
        return;
    }

    FreeMArrow* arrow = m_LineManagerMent.createArrow(freeMLine);
    addItem(arrow);
    connect(freeMLine, SIGNAL(lineColorChanged(QColor)), arrow, SLOT(setArrowColor(QColor)));
}

void FreeMScanWidget::connectParameterSignals()
{
    parameterValueChangedConnect(BFPNames::FreezeStr, SLOT(onFreezeChanged()));
    parameterValueChangedConnect(BFPNames::FreeMLineNumStr, SLOT(onMLineNumChanged()));
    parameterValueChangedConnect(BFPNames::FreeMLineNoStr, SLOT(onSwitchMLine()));
    connect(sonoParameters().parameter(BFPNames::FreeMLineNoStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
            SLOT(refreshColor()));
    connect(parameter(BFPNames::RotationStr), SIGNAL(valueChanging(QVariant)), this, SLOT(onRotationChanged(QVariant)));

    QStringList parameters =
        QStringList() << BFPNames::FreeMStartLine1Str << BFPNames::FreeMStartDepthMM1Str << BFPNames::FreeMAngle1Str
                      << BFPNames::FreeMStartLine2Str << BFPNames::FreeMStartDepthMM2Str << BFPNames::FreeMAngle2Str
                      << BFPNames::FreeMStartLine3Str << BFPNames::FreeMStartDepthMM3Str << BFPNames::FreeMAngle3Str;

    foreach (const QString parameter, parameters)
    {
        parameterValueChangedConnect(parameter, SLOT(setupMLine()));
    }
}

bool FreeMScanWidget::isInRegion(const QPointF& point) const
{
    return m_ImageArea.path().contains(point);
}

void FreeMScanWidget::updateCurLineColor()
{
    if (m_FreeMLine == NULL)
    {
        return;
    }

    m_FreeMLine->setLineColor(QColor(ModelUiConfig::instance().value(ModelUiConfig::CurFreeMLine).toUInt()));
}

QString FreeMScanWidget::lineParameter(const QString& parameter)
{
    QString lineParameter = parameter;
    lineParameter.chop(1);
    return lineParameter + QString::number(pIV(BFPNames::FreeMLineNoStr));
}

void FreeMScanWidget::setupArrow(const FreeMLine* freeMLine)
{
    FreeMArrow* arrow = m_LineManagerMent.arrow(freeMLine);

    if (arrow != NULL)
    {
        FreeMLineManagerMent::IntersectPt intersect = m_LineManagerMent.intersectPoints(freeMLine);

        // 根据交点绘制箭头
        arrow->setupArrow(intersect.first, intersect.second);
        arrow->setTransform(flipAndRectCenterTransform());
        arrow->setTransform(QTransform::fromTranslate(arrow->pos().x() + imageRectStartXInWindowRect(),
                                                      arrow->pos().y() + imageRectStartYInWindowRect()),
                            true);
    }
}

void FreeMScanWidget::generateFreeMData()
{
    QList<FreeMLineManagerMent::IntersectPt> intersects;
    m_LineManagerMent.allIntersectPoints(intersects);

    qreal totalLength = 0;
    QList<QLineF> mLines;
    BFCoordTransform bfCoordTrans = coordTransform();
    // 统一坐标系原点在图像局域顶部的中点，并对其进行缩放处理
    QTransform transform;
    transform.scale(1.0, 1.0 / scaleFactor());
    transform.translate(-m_ImageArea.fanOffset().x(), -m_ImageArea.fanOffset().y());

    switch (m_ImageState)
    {
    case FullImageState:
    {
        // 将扫查线段的交点转换为逻辑点，并计算总长度
        foreach (const FreeMLineManagerMent::IntersectPt& intersect, intersects)
        {
            QLineF mLine(transform.map(intersect.first), transform.map(intersect.second));
            totalLength += mLine.length();
            mLines.append(mLine);
        }
        FreeMDataGenerator generator(pBV(BFPNames::HalfHeight2Str), pV(BFPNames::ImageSizeStr).toSize().height());
        QByteArray data = generator.freeMData(bfCoordTrans, mLines, totalLength);

        sonoParameters().parameter(BFPNames::FreeMBlockStr)->setValue(QVariant(data));
    }
    break;
    case MixImageState:
    {
        QList<QVariant> freemLines;
        foreach (const FreeMLineManagerMent::IntersectPt& intersect, intersects)
        {
            freemLines << QLineF(intersect.first, intersect.second);
            qDebug() << "FreeMScanWidget::generateFreeMData()" << freemLines.last();
        }
        sonoParameters().parameter(BFPNames::FreeMLinesStr)->setValue(freemLines);
    }
    break;
    default:
        break;
    }
}

void FreeMScanWidget::calcLogicRegoin()
{
    QRectF rect = localRect();
    int rotation = pIV(BFPNames::RotationStr);
    bool isHorizontalRotate = (rotation == 90 || rotation == 270);

    m_LogicRegoin.setTop(m_ImageArea.fanOffset().y());
    // 旋转90或者270之后，限制区域底部需要进行翻转计算，这里存在比较特殊的计算
    // 比如考虑底边计算，需要减1处理
    m_LogicRegoin.setBottom(isHorizontalRotate ? rect.height() - 1 : m_ImageArea.fanRadius() + m_LogicRegoin.top());
    m_LogicRegoin.setRight(rect.width());
    m_LogicRegoin.setLeft(rect.left());
}

void FreeMScanWidget::validatedMPoint(QPointF& point)
{
    if (!isInRegion(point))
    {
        point.setX(qBound(m_LogicRegoin.left(), point.x(), m_LogicRegoin.right()));
        point.setY(qBound(m_LogicRegoin.top(), point.y(), m_LogicRegoin.bottom()));
    }
}

void FreeMScanWidget::setupOneLine()
{
    FreeMLine* freeMLine = m_LineManagerMent.mLine(pIV(BFPNames::FreeMLineNoStr) - 1);

    setupMLine(freeMLine);
    setupArrow(freeMLine);
}

QPointF FreeMScanWidget::mPoint()
{
    QTransform transform = flipAndRectZoomCoefTransform() * m_ImageArea.transform().inverted();

    QPointF mPoint = scanLineAndDepthScreenPos(sonoParameters().pFV(lineParameter(BFPNames::FreeMStartLine1Str)),
                                               sonoParameters().pFV(lineParameter(BFPNames::FreeMStartDepthMM1Str))) *
                     transform;
    mPoint.setX(mPoint.x());
    mPoint.setY(mPoint.y());
    return mPoint;
}

void FreeMScanWidget::rotateMLine(FreeMLine* freeMLine, QPointF point)
{
    if (freeMLine == NULL)
    {
        return;
    }

    qreal angle = sonoParameters().pFV(lineParameter(BFPNames::FreeMAngle1Str));
    point = m_ImageArea.mapToItem(freeMLine, point);

    freeMLine->setTransform(QTransform::fromTranslate(point.x(), point.y()), true);

    // 因为初始化线与x轴为90度，所以此处要90度减去旋转角
    freeMLine->setRotation(freeMLine->rotation() + 90 - angle);
    freeMLine->setTransform(QTransform::fromTranslate(-point.x(), -point.y()), true);
}
