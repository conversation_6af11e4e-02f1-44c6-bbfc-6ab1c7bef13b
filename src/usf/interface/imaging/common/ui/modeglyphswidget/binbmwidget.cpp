/*
 * =====================================================================================
 *
 *       Filename:  binbmwidget.cpp
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  2012年07月16日 14时12分38秒
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  Wang<PERSON><PERSON>yu (), <EMAIL>
 *   Organization:
 *
 * =====================================================================================
 */

#include "binbmwidget.h"

#include <boost/foreach.hpp>

#include <QVector2D>

#include "bfpnames.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "parameter.h"
#include "modeluiconfig.h"
#include "isonoparameters.h"
#include "formula.h"

BInBMWidget::BInBMWidget(QObject* parent, ISonoParameters& sonoParameter, const QRect& windowRect, const QRect& rect,
                         const QRect& region, UseMode useMode)
    : PureBWidget(parent, sonoParameter, windowRect, rect, region, useMode)
    , m_tigger(NULL)
{
    addItem(&m_mScanLine);

    m_centerDebug.setPen(QPen(rand()));
    m_hiddenDebug.setPen(QPen(rand()));
    m_linearDebug.setPen(QPen(rand()));

    QPen pen = verticalRuler().pen();

    connect(sonoParameter.parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreezeChanged()));

    //        pen.setStyle(Qt::DashLine);
    ProxyGroup::customDashPen(pen, 2.0, 3.0, 0.1);

#if SYS_APPLE
    pen.setWidth(2.5);
#endif
    m_mScanLine.setPen(pen);
    onFreezeChanged();

    connectParameterSignals(&sonoParameter);

    setupMScanLine();
}

BInBMWidget::~BInBMWidget()
{
    if (m_tigger)
    {
        m_tigger->blockSignals(true);
        delete m_tigger;
        m_tigger = NULL;
    }
}

void BInBMWidget::connectParameterSignals(ISonoParameters* sonoParameter)
{

    const QString mScanLineSigParam[] = {BFPNames::CQYZStr,
                                         // CQYZ参数保证在切换探头时将发出改变信号
                                         //因此无需考虑BFPNames::ProbeIdStr改变时扫描线重新初始化的问题
                                         BFPNames::MScanLineStr, BFPNames::StartDepthMMStr, BFPNames::DepthMMStr,
                                         BFPNames::UpStr, BFPNames::LeftStr, BFPNames::ImageZoomCoefStr,
                                         BFPNames::StartLineStr, BFPNames::PanZoomOffsetDepthPixelStr,
                                         BFPNames::PanZoomOffsetWidthPixelStr};

    QStringList relationParas;
    BOOST_FOREACH (const QString& str, mScanLineSigParam)
    {
        relationParas.append(str);
    }
    m_tigger = new DelayParamTrigger(sonoParameter, relationParas, this);
    connect(m_tigger, SIGNAL(triggerCompleted()), this, SLOT(setupMScanLine()));
}

void BInBMWidget::setupMScanLine()
{
    int mScanLine = pIV(BFPNames::MScanLineStr);

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));

    //    ProbeParameters probeParam(probeDataInfo,
    //            pRV(BFPNames::StartDepthMMStr),
    //            pRV(BFPNames::PixelSizeMMStr),
    //            false,
    //            pIV(BFPNames::StartLineStr),
    //            pIV(BFPNames::StopLineStr));
    // 2023-06-07 Write by AlexWang 解决M模式在局部放大状态下扫查线长度不正常，需要考虑局部放大状态因素
    ProbeParameters probeParam(&sonoParameters(), true);

    qreal startDepthMM = pRV(BFPNames::StartDepthMMStr),
          bottomDepthMM = probeParam.bottomDepthMM(pRV(BFPNames::DepthMMStr));

    QTransform transform = flipAndRectZoomCoefTransform();

    QPointF offset = QPointF(imageRectStartXInWindowRect(), imageRectStartYInWindowRect());

    int rotation = pIV(BFPNames::RotationStr);
    QPointF offsetForZoomcoef(
        ((rotation == 0 || rotation == 90) ? -1.0 : 1.0) * pRV(BFPNames::PanZoomOffsetWidthPixelStr),
        ((rotation == 0 || rotation == 270) ? -1.0 : 1.0) * pRV(BFPNames::PanZoomOffsetDepthPixelStr));

    QPointF startPoint = scanLineAndDepthScreenPos(mScanLine, startDepthMM);
    startPoint = startPoint * transform + offset + offsetForZoomcoef;

    // 2023-06-07 Write by AlexWang
    // 解决M模式在局部放大状态下扫查线长度不正常，需要区分线阵和凸阵，线阵计算stopPoint时需要将起始深度考虑进去
    qreal depthMM;
    if (probeDataInfo.IsLinear)
        depthMM = startDepthMM + bottomDepthMM;
    else
        depthMM = bottomDepthMM;

    QPointF stopPoint = scanLineAndDepthScreenPos(mScanLine, depthMM);
    stopPoint = stopPoint * transform + offset + offsetForZoomcoef;

    m_mScanLine.setLine(QLineF(startPoint, stopPoint));
#if SYS_APPLE
    QPen pentemp = m_mScanLine.pen();
    ;
    pentemp.setWidth(2.5);

    // 0:solid,1:dot
    if (ModelUiConfig::instance().value(ModelUiConfig::PenStyle).toInt() == 0)
    {
        pentemp.setStyle(Qt::SolidLine);
    }
    else
    {
        pentemp.setStyle(Qt::CustomDashLine);
        ProxyGroup::customDashPen(pentemp, 2.5, 3.0, 0.1);
    }
    m_mScanLine.setPen(pentemp);
#endif

    // debugdraw
    QPointF center = QPointF(0, mm2Pixel(-probeParam.perpendicularDisMM())) * transform;

    m_linearDebug.setLine(0, stopPoint.y(), windowRect().width(), stopPoint.y());
    m_hiddenDebug.setLine(QLineF(center, startPoint));

    m_centerDebug.setLine(0, center.y(), windowRect().width(), center.y());
}

void BInBMWidget::onFreezeChanged()
{
    bool freeze = sonoParameters().pBV(BFPNames::FreezeStr) || !sonoParameters().isRealTime();

    QPen pen = m_mScanLine.pen();
    pen.setColor(freeze ? QColor(ModelUiConfig::instance().value(ModelUiConfig::MLineFreezeColor).toUInt())
                        : QColor(ModelUiConfig::instance().value(ModelUiConfig::MLineColor).toUInt()));

    pen.setStyle(Qt::SolidLine);
    pen.setWidth(2);
    m_mScanLine.setPen(pen);
}

void BInBMWidget::setDebugDraw(bool enable)
{

    PureBWidget::setDebugDraw(enable);

    QGraphicsItem* parent = (enable ? partitionNode() : NULL);

    int probeId = pIV(BFPNames::ProbeIdStr);

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(probeId);

    if (probeDataInfo.IsLinear)
    {
        m_linearDebug.setParentItem(parent);
    }

    m_centerDebug.setParentItem(parent);
    m_hiddenDebug.setParentItem(parent);
}

BInBMPreWidget::BInBMPreWidget(QObject* parent, ISonoParameters& sonoParameter, const QRect& windowRect,
                               const QRect& rect, const QRect& region, UseMode useMode)
    : BInBMWidget(parent, sonoParameter, windowRect, rect, region, useMode)
{
}
