#ifndef PUREBWIDGET_H
#define PUREBWIDGET_H
#include "usfinterfaceimagingui_global.h"

#include "sharedglyphswidget.h"
#include "biopsy.h"
#include "centerline.h"
#include "needleline.h"
#include "depthruler.h"
#include "tgc.h"
#include "lgc.h"
#include "lgccontroller.h"
#include "curvedpanoglyphs.h"
#include "teeprobeangleglyphs.h"

#ifdef USE_GRAPHICS_DEBUG
#include <boost/shared_ptr.hpp>

class AreaResultDrawer;
class BAreaSource;
#endif

class ProbeDataInfo;

/**
 * @brief B模式的glyphs组
 *        B模式连接焦点参数信号分为了三种，
 *        FocusNum/PosB FocusNum/PosM FocusNum/PosC
 *        此类通过构造函数的第三个参数UseMode来控制
 *        是否是与M/PW模式同时使用的B图像部分。
 *        但M/PW共存的模式下B图像还存在其他绘制内容，
 *        因此需要继续派生新的类。
 *        使用template可能能更好的组织代码，但是moc不支持
 *        template class的slot/signal。
 *
 *        其中垂直标尺起始位置为侦测区域的弧底
 */
class USF_INTERFACE_IMAGING_COMMON_UI_EXPORT PureBWidget : public RotatableGlyphsWidget
{

    Q_OBJECT
    Q_ENUMS(UseMode)

public:
    /**
     * @brief 构造的B模式部件是否与其他模式共存
     */
    enum UseMode
    {
        UseB, /*!<单B/2B/4B，使用FocusNum/PosB获取焦点位置 */
        UseM, /*!<BM模式中的B ,使用FocusNum/PosM获取焦点位置 */
        UseC  /*!<BPW模式中的B ,使用FocusNum/PosC获取焦点位置 */
    };

    /**
     * @brief 构建B模式的一个完整widget
     *
     * @param parent
     * @param sonoParameter
     * @param rect
     * @param withModeM true=在BM模式下的B模式部分，连接FocusNum/PosM参数信号
     *                  false=连接FocusNum/PosB参数信号
     */
    PureBWidget(QObject* parent, const ISonoParameters& sonoParameter, const QRect& windowRect, const QRect& rect,
                const QRect& region, UseMode useMode = UseB, int index = -1);

    /**
     * @brief 当Widget需要重新连接信号反映部件变化时，调用此函数连接信号
     *        注：构造时自动连接信号，不用手动调用
     */
    void connectParameterSignals();

    virtual void changePostParameterSignalsOnFrozen(ISonoParameters* sonoParameters);
    /**
     * @brief 使用参数重新构建widget
     * 当第一次构造时或者重新连接信号后使用
     */
    void setupAll();

    void setupOnlyVerticalRuler();

    virtual ~PureBWidget()
    {
    }

    /**
     * @brief 当需要保留当前绘制效果而不随着参数变化而变化时，调用此函数断开信号
     *        重新连接时使用 connectParameterSignals()和reconstruct()
     */
    void disconnectParameterSignals();

    DepthRuler& verticalRuler()
    {
        return m_verticalRuler;
    }

    bool isActiveB() const
    {
        return m_active;
    }

    bool isPixelSizeValid() const;

    /**
     * @brief 指定当前B区域是否是激活B区域
     *
     * @param activeValue
     */
    void setActive(bool activeValue);

    virtual ImageType imageType() const
    {
        return ImageType_B;
    }

    virtual void setDebugDraw(bool enable = true);

    bool isConnected() const;

    void setFreeHand3DRoiPen(const QPen& pen);

    void setFreeHand3DRoiVisible(bool visible);

protected:
    virtual void changeSizeAndClipStatus();

    //此段slot用于对其中的子节点槽进行类型上的适配
private slots:

    /**
     * @brief 绘制焦点
     */
    void setupFocalPoints();

    /**
     * @brief 处理上下和左右翻转以及缩放参数变化信号
     */
    void setupFlipsAndZoom();

    /**
     * @brief 处理TGC变化的信号
     */
    void setupTGC();

    /**
     * @brief 处理翻转/起始结束线变化后的垂直标尺的位置
     */
    void setupVerticalRulerPos();

    /**
     * @brief 处理穿刺线对于翻转/起始结束线
     */
    void setupBiopsy();

    /**
     * @brief 处理冻结和AIO开启时立即隐藏TGC
     */
    void setupTGCVisible();

    void setupCenterLine();

    void setupNeedleLine();
    void onTGCHideTimeChanged(const QString& property);

    void onCurvedPanoInfoChanged();

    void onFreezeChanged();
    void onImageZoomCoefChanged(const QVariant& value);
    void setupFreeHand3DRectItem();
    void setupHorizontalRulerVisible();
    void setupTeeProbeAngleItem();
    void setupHotLogo();

    /**
     * @brief tgcPointsChanged TGC绘制坐标变化
     */
    void tgcPointsChanged(QVariant points);

    void depthLogoChanged();

protected:
    /**
     * @brief m_rulerGroup 统一管理verticalRuler horizontalRuler
     * tgc的矩形item,便于在rotation>=180时，统一反向旋转180的操作 该item的形状和partitionNode一致
     *
     * @note 该变量要放在verticalRuler horizontalRuler tgc　之前定义,先定义的变量后释放，先释放child再释放parent
     */
    QGraphicsRectItem m_rulerGroup;

    /**
     * @brief 垂直标尺
     */
    DepthRuler m_verticalRuler;

    /**
     * @brief 水平标尺
     */
    UnitRuler m_horizontalRuler;
    /**
     * @brief tgc
     */
    TGC m_tgc;
    /**
     * @brief 指明探头方向的logo标记
     */
    QGraphicsPixmapItem m_hotLogo;

    QGraphicsPixmapItem m_depthLogo;

    Biopsy m_biopsy;

    CenterLine m_centerLine;

    qreal m_centerPanzoomOffsetX;

    qreal m_centerPanzoomOffsetY;

    qreal m_panzoomOffsetX;

    qreal m_imageWidth;

    NeedleLine m_needleLine;

    CurvedPanoGlyphs m_CurvedPan;
    ISonoParameters* m_RealTimeSonoParameters;

    bool m_active;

    bool m_IsConnected;

    LGC m_LGC;
    /**
     * @brief 控制管理LGC的显示
     */
    LGCController m_LGCController;

    QGraphicsRectItem m_FreeHand3DRoiItem;

    TeeProbeAngleGlyphs m_teeAngle;

    bool m_DepthLogoVisible;

    bool m_SupportTouchScreen;

private:
    /**
     * @brief ruler起始y的辅助线
     */
    QGraphicsLineItem m_debugRuler;

    /**
     * @brief hotlogo的定位辅助线
     *        0为凸阵探头圆心与HotLogoScanLine HotLogoDepthMM的连线
     *        1为在辅助点处与hotlogo定位辅助线外切的圆心的连线
     *        2为过外切圆心，平行于HotLogoScanLine的线
     */
    QGraphicsLineItem m_debugHotLogo[3];

#ifdef USE_GRAPHICS_DEBUG
    BAreaSource* m_bAreaSource;
    boost::shared_ptr<AreaResultDrawer> m_debugB;
#endif

    /**
     * @brief 是否是与M模式同时存在
     */
    const UseMode m_useMode;

    /**
     * @brief 是否显示horizontal ruler
     * 当前由于缩放不统一，因此不绘制horizontal ruler
     *
     * @param enable
     */
    void enableHorizontolRuler(bool enabled);
    /**
     * @brief 对hotlog进行处理，用于内部组织，不作为槽公布
     *     将在setupFlipsAndZoom中被调用
     */

    void setItemVisible(bool isActiveB);

    void setupFreeHand3DRoiPen();

private:
    /**
     * @brief 用于计算凸阵探头下合适的hotlogo pos
     *
     * @param xCrossEdge
     * @param yCrossEdge
     *
     * @return
     */
    QPointF edgeLimitHotLogoPos(bool xCrossEdge, bool yCrossEdge, const QPointF& center, const QPointF& predefinedPos,
                                const qreal yCrossRad);

    void initialHotLogo();

    void initialDepthLogo();

    qreal hotLogoR();

    void calHotoLogoPosWhenPanzoom(const ProbeDataInfo probeDataInfo, QPointF imageTopLeft, int rotation, qreal x,
                                   qreal y, qreal& logoX, qreal& logoY);

    void restrictRange(int rectX, int rectY, qreal& logoX, qreal& logoY, QSize renderWidgetSize, int reduseW,
                       int reduseH, int rotation, int distance, qreal x);

    /**
     * @brief 判定是否显示TGC
     * 仅当!Freeze && !AIO 时显示
     *
     * @return
     */
    bool showTGC();

    void setupHotLogoLinear(const ProbeDataInfo& dataInfo, const int hotLogoLine, const QPointF& predefinedPos);

    void setupHotLogoConvex(const ProbeDataInfo& dataInfo, const int hotLogoLine, const QPointF& predefinedPos);

    /**
     * @brief
     *
     * @param posInZoomCoef imageZoomCoef框中的坐标，以未翻转上边界中点为原点
     */
    void setHotLogoPosWithClamp(const QPointF& posInZoomCoef);
    /**
     * @brief combinedUp 结合up 和 rotation 参数，标尺的方向
     * @return
     */
    bool combinedUp() const;
    /**
     * @brief isLRLayoutHorizontalRotate 是否是 2B, B/M, BBC 90度,270度水平旋转
     * @return
     */
    bool isLRLayoutHorizontalRotate() const;
    /**
     * @brief scaledBDepth 返回B图像区缩放过的深度
     * @param depth
     * @return
     */
    qreal scaledBDepth(qreal depth);
    /**
     * @brief 处理TGC变化的信号
     */
    void setupLGC();
    /**
     * @brief calcLGCRect 计算LGC的显示区域
     * @return
     */
    QRectF calcLGCRect();

    QPointF m_preHotoLogoPos;

    struct posInfo
    {
        bool Up;
        bool Left;
    } m_posInfo;

    int m_BIndex;

private:
    static const QString FOCUS_NUM_NAMES[3];

    static const QString FOCUS_POS_NAMES[3];

    static const int OVERFLOW_MAJOR;
};
#endif // PUREBWIDGET_H
