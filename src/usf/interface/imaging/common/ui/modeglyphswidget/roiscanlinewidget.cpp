/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "roiscanlinewidget.h"
#include "bfpnames.h"
#include "commonareasource.h"
#include "isonoparameters.h"
#include "bfcoordtransform.h"

ROIScanLineWidget::ROIScanLineWidget(QObject* parent, ISonoParameters& sonoParameters, const QRect& windowRect,
                                     const QRect& rect, const QRect& region, ROIAreaSource::UseMode mode)
    : SubAreaWidget(parent, sonoParameters, windowRect, rect, region, AreaResult_TypeC)
{
    m_areaSource = new ROIAreaSource(&sonoParameters, rect.size(), this, mode);

    m_mode = mode;

    setup();

    addItem(&m_scanLine);

    setSupportColorM(true);

    connectParameterSignals();

    setupRoiScanLine();

    parameterValueChangedConnect(BFPNames::FreezeStr, SLOT(onFreezeChanged()));

    onFreezeChanged();
}

ROIScanLineWidget::~ROIScanLineWidget()
{
}

Measurement::ImageType ROIScanLineWidget::imageType() const
{
    return ImageType_B;
}

IAreaSource* ROIScanLineWidget::areaSource()
{
    return m_areaSource;
}

BFCoordTransform ROIScanLineWidget::coordTransform() const
{
    int probeId = pIV(BFPNames::ProbeIdStr);

    qreal startDepthMM = pRV(getActiveBParaName(BFPNames::StartDepthMMStr)),
          pixelSizeMM = pRV(getActiveBParaName(BFPNames::PixelSizeMMStr));

    if (RealCompare::AreEqual(pixelSizeMM, 0.0))
    {
        // TODO:正确的时间设置BIndex
        //退出multiB,pixel0123被清零,在m_BIndex还未重新设置时会有信号槽触到到这里,所以此时读到的pixel为0
        //由于是中间一个过度状态,最后还会被正确设置,所以先做一个预防处理
        startDepthMM = pRV(BFPNames::StartDepthMMStr);
        pixelSizeMM = pRV(BFPNames::PixelSizeMMStr);
    }

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(probeId);

    BFCoordTransform bfCoordTrans(probeDataInfo, startDepthMM, pixelSizeMM, pBV(BFPNames::ZoomOnStr),
                                  pIV(BFPNames::StartLineColorStr), pIV(BFPNames::StopLineColorStr),
                                  pIV(BFPNames::C_RX_LNUMStr));
    return bfCoordTrans;
}

void ROIScanLineWidget::connectParameterSignals()
{
    const QString roiScanLineSigParam[] = {BFPNames::CQYZStr,
                                           // CQYZ参数保证在切换探头时将发出改变信号
                                           //因此无需考虑BFPNames::ProbeIdStr改变时扫描线重新初始化的问题
                                           RoiMidLineStr[m_mode], BFPNames::StartDepthMMStr, BFPNames::DepthMMStr,
                                           BFPNames::UpStr, BFPNames::LeftStr, BFPNames::ImageZoomCoefStr,
                                           BFPNames::StartLineStr, BFPNames::StopLineStr};
    BOOST_FOREACH (const QString& str, roiScanLineSigParam)
    {
        parameterValueChangedConnect(str, SLOT(setupRoiScanLine()));
    }
}

void ROIScanLineWidget::setupRoiScanLine()
{
    int mScanLine = pIV(RoiMidLineStr[m_mode]);
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    ProbeParameters probeParam(probeDataInfo, pRV(BFPNames::StartDepthMMStr), false, pIV(BFPNames::StartLineColorStr),
                               pIV(BFPNames::StopLineColorStr));
    qreal startDepthMM = pRV(BFPNames::StartDepthMMStr);
    qreal bottomDepthMM = probeParam.bottomDepthMM(pRV(BFPNames::DepthMMStr));
    QTransform transform = flipAndRectZoomCoefTransform();

    // rigion room下colorm的扫查线线和框出现分离的问题，需要将偏移量offsetForZoomcoef添加上
    int rotation = pIV(BFPNames::RotationStr);
    QPointF offsetForZoomcoef(
        ((rotation == 0 || rotation == 90) ? -1.0 : 1.0) * pRV(BFPNames::PanZoomOffsetWidthPixelStr),
        ((rotation == 0 || rotation == 270) ? -1.0 : 1.0) * pRV(BFPNames::PanZoomOffsetDepthPixelStr));

    QPointF startPoint = scanLineAndDepthScreenPos(mScanLine, startDepthMM, false) * transform + offsetForZoomcoef;
    QPointF stopPoint = scanLineAndDepthScreenPos(mScanLine, bottomDepthMM, false) * transform + offsetForZoomcoef;

    m_scanLine.setLine(QLineF(startPoint, stopPoint));

    bool freeze = sonoParameters().pBV(BFPNames::FreezeStr) || !sonoParameters().isRealTime();

    QPen pen(freeze ? QColor(ModelUiConfig::instance().value(ModelUiConfig::RoiScanLineFreezeColor).toUInt())
                    : QColor(ModelUiConfig::instance().value(ModelUiConfig::RoiScanLineColor).toUInt()));
    pen.setWidth(2);
    m_scanLine.setPen(pen);

    //    m_scanLine.setPen(QPen(QColor(ModelUiConfig::instance().value(ModelUiConfig::RoiScanLineColor).toUInt())));
    m_scanLine.resetTransform();
    m_scanLine.setTransform(QTransform::fromTranslate(imageRectStartXInWindowRect(), imageRectStartYInWindowRect()),
                            true);
}

const QString ROIScanLineWidget::RoiMidLineStr[] = {BFPNames::RoiMidLineStr, BFPNames::RoiMidLineTDIStr};

void ROIScanLineWidget::onFreezeChanged()
{
    bool freeze = sonoParameters().pBV(BFPNames::FreezeStr) || !sonoParameters().isRealTime();
    QPen prevPen = pen();

    prevPen.setColor(freeze ? QColor(ModelUiConfig::instance().value(ModelUiConfig::RoiScanLineFreezeColor).toUInt())
                            : QColor(ModelUiConfig::instance().value(ModelUiConfig::RoiScanLineColor).toUInt()));
    prevPen.setWidth(2);
    m_scanLine.setPen(prevPen);
    setPen(prevPen);
}
