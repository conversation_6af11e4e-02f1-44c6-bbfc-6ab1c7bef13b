#include "dopplerthetaadjustitem.h"
#include "modeluiconfig.h"
#include "bfpnames.h"
#include "istatemanager.h"
#include "stateeventnames.h"
#include <QFontMetrics>
#include <QDebug>
#include <QtMath>
#include "parameter.h"
#include "infostruct.h"
#include "appsetting.h"
#include "modelconfig.h"

DopplerThetaAdjustItem::DopplerThetaAdjustItem(SonoParameters* sonoParameters, QRect rect)
    : m_Pressed(false)
    , m_SonoParameters(sonoParameters)
    , m_DopplerTheta(0)
    , m_rect(rect)
    , m_quick(false)
    , m_isLeft(true)
    , m_isUp(true)
    , m_isFlipping(false)
    , m_StateManager(NULL)
{
    setSonoParameters(sonoParameters);
}

void DopplerThetaAdjustItem::setSonoParameters(SonoParameters* sonoParameters)
{
    if (AppSetting::isP9Series())
        return;

    QStringList paras = QStringList() << BFPNames::DopplerThetaStr << BFPNames::DopplerThetaTDIStr
                                      << BFPNames::DopplerThetaCWDStr << BFPNames::LeftStr << BFPNames::UpStr;
    //                                      << BFPNames::IsDopplerScanLineVisibleStr;
    if (m_SonoParameters != sonoParameters)
    {
        if (m_SonoParameters != NULL)
        {
            foreach (const QString& para, paras)
            {
                disconnect(m_SonoParameters->parameter(para), SIGNAL(valueChanged(const QVariant&)), this,
                           SLOT(onValueChanged(const QVariant&)));
            }
            disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(const QVariant&)),
                       this, SLOT(onSystemScanModeChanged(const QVariant&)));
            disconnect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr),
                       SIGNAL(valueChanged(const QVariant&)), this,
                       SLOT(onDopplerScanLineVisibleChanged(const QVariant&)));
        }
        m_SonoParameters = sonoParameters;
        if (m_SonoParameters != NULL)
        {
            foreach (const QString& para, paras)
            {
                connect(m_SonoParameters->parameter(para), SIGNAL(valueChanged(const QVariant&)), this,
                        SLOT(onValueChanged(const QVariant&)));
            }
            connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr), SIGNAL(valueChanged(const QVariant&)),
                    this, SLOT(onSystemScanModeChanged(const QVariant&)));
            connect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr),
                    SIGNAL(valueChanged(const QVariant&)), this,
                    SLOT(onDopplerScanLineVisibleChanged(const QVariant&)));
            onValueChanged(QVariant());
            onSystemScanModeChanged(m_SonoParameters->pV(BFPNames::SystemScanModeStr));
        }
    }
}

void DopplerThetaAdjustItem::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void DopplerThetaAdjustItem::onValueChanged(const QVariant& value)
{
    Q_UNUSED(value)

    m_DopplerTheta = m_SonoParameters->pIV(getDopplerThetaParaName());
    m_isLeft = m_SonoParameters->pBV(BFPNames::LeftStr);
    m_isUp = m_SonoParameters->pBV(BFPNames::UpStr);
    update();
}

void DopplerThetaAdjustItem::onSystemScanModeChanged(const QVariant& value)
{
    bool isScanLineVisible = m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr) ||
                             m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr);
    int scanMode = value.toInt();
    bool visible = false;

    if (scanMode == SystemScanModeBPW || scanMode == SystemScanModeColorPW || scanMode == SystemScanModePowerPW ||
        scanMode == SystemScanModeDPowerPW || scanMode == SystemScanModeTissuePW || scanMode == SystemScanModeMVIPW ||
        scanMode == SystemScanModeCWD || scanMode == SystemScanModeCWDColorDoppler ||
        scanMode == SystemScanModeCWDDirectionalPowerDoppler || scanMode == SystemScanModeCWDPowerDoppler ||
        isScanLineVisible)
    {
        visible = true;

        m_DopplerTheta = m_SonoParameters->pIV(getDopplerThetaParaName());
        m_isLeft = m_SonoParameters->pBV(BFPNames::LeftStr);
        m_isUp = m_SonoParameters->pBV(BFPNames::UpStr);
    }
    setVisible(visible);
    emit visibleChanged(visible);
    update();
}

void DopplerThetaAdjustItem::onDopplerScanLineVisibleChanged(const QVariant& value)
{
    setVisible(value.toBool());
    emit visibleChanged(value.toBool());
}

void DopplerThetaAdjustItem::paint(QPainter* painter, const QStyleOptionGraphicsItem* option, QWidget* widget)
{
    Q_UNUSED(option)
    Q_UNUSED(widget)

    painter->setRenderHint(QPainter::Antialiasing, true); // 启用抗锯齿

    if (m_quick)
    {
        // TODO 目前，CW模式下调节angle还是调节的PW的DopplerTheta
        m_DopplerTheta = m_SonoParameters->pIV(getDopplerThetaParaName());
    }

    QColor selectColor =
        QColor(ModelUiConfig::instance().value(ModelUiConfig::DopplerDivideLineColor).toUInt()); // 按下时的颜色

    QPointF centerPoint = this->boundingRect().center(); // 中心点

    if (m_Pressed)
    {
        painter->setPen(QPen(selectColor, 1, Qt::DashLine));

        painter->drawEllipse(centerPoint, 50, 50); // 画圆

        // 绘制角度文字
        painter->save();
        painter->setPen(QPen((qAbs(m_DopplerTheta) > 60 ? Qt::red : Qt::white)));
        painter->setFont(QFont(QFont("", 10)));
        int textWidth = painter->fontMetrics().horizontalAdvance(QString::number(m_DopplerTheta));
        painter->drawText(QPointF(centerPoint.x() - textWidth / 2, centerPoint.y() - 35),
                          QString::number(m_DopplerTheta));
        painter->restore();
    }
    else
    {
        painter->setPen(QPen(Qt::white, 1, Qt::SolidLine));
    }
    painter->drawLine(QPointF(centerPoint.x() - 25, centerPoint.y() - 6),
                      QPointF(centerPoint.x() + 25, centerPoint.y() - 6)); // line1
    painter->drawLine(QPointF(centerPoint.x() - 25, centerPoint.y() + 6),
                      QPointF(centerPoint.x() + 25, centerPoint.y() + 6)); // line2

    static QGraphicsLineItem* controlLine = new QGraphicsLineItem(this); // control line
    controlLine->setPen(QPen(selectColor, 1, m_Pressed ? Qt::DashLine : Qt::SolidLine));
    controlLine->setLine(centerPoint.x(), centerPoint.y() - 35, centerPoint.x(), centerPoint.y() + 35);
    controlLine->setTransformOriginPoint(controlLine->boundingRect().center());

    if (m_DopplerTheta != 0 && (!m_isLeft || !m_isUp) && (m_isLeft != m_isUp)) // && !m_isFlipping
                                                                               //    if (!m_isLeft)
    {
        controlLine->setRotation(-m_DopplerTheta);
    }
    else
    {
        controlLine->setRotation(m_DopplerTheta);
    }

    painter->save();
    painter->setPen(QPen(Qt::white, 1, Qt::SolidLine));
    painter->setBrush(QBrush(Qt::white));
    painter->drawEllipse(centerPoint, 2, 2);
    painter->restore();
}

QRectF DopplerThetaAdjustItem::boundingRect() const
{
    return m_rect;
}

void DopplerThetaAdjustItem::mousePressEvent(QGraphicsSceneMouseEvent* event)
{
    m_LeftButtonPressedPoint = event->pos();
    m_Pressed = true;
    update();
}

void DopplerThetaAdjustItem::mouseReleaseEvent(QGraphicsSceneMouseEvent* event)
{
    Q_UNUSED(event);

    if (m_offset.x() >= 0 && m_offset.x() <= 5 && m_offset.y() >= 0 && m_offset.y() <= 5)
    {
        m_StateManager->postEvent(StateEventNames::QuickAngle);
    }
    m_offset.setX(0);
    m_offset.setY(0);
    m_Pressed = false;
    m_quick = true;
    m_isFlipping = false;
    update();
}

void DopplerThetaAdjustItem::mouseMoveEvent(QGraphicsSceneMouseEvent* event)
{
    m_quick = false;

    m_offset += event->pos() - m_LeftButtonPressedPoint;

    if (m_offset.x() >= 0 && m_offset.x() <= 5 && m_offset.y() >= 0 && m_offset.y() <= 5)
        return;

    if (!m_Pressed)
        return;
    QPointF offset(event->pos().x() - m_LeftButtonPressedPoint.x(), event->pos().y() - m_LeftButtonPressedPoint.y());

    m_LeftButtonPressedPoint = event->pos();

    QPointF centerPoint = this->boundingRect().center();
    QPointF mousePoint = event->pos();

    m_DopplerTheta = 90 - getTheta(mousePoint, centerPoint);

    if (mousePoint.y() <= centerPoint.y())
    {
        if (mousePoint.x() <= centerPoint.x())
        {
            m_DopplerTheta = -m_DopplerTheta;
        }
    }
    else if (mousePoint.x() > centerPoint.x())
    {
        m_DopplerTheta = -m_DopplerTheta;
    }

    update();

    if ((!m_isLeft || !m_isUp) && (m_isLeft != m_isUp)) // 翻转
    {
        m_isFlipping = true;
        m_DopplerTheta = -m_DopplerTheta;
        m_SonoParameters->setPV(getDopplerThetaParaName(), m_DopplerTheta);
    }
    else
    {
        m_SonoParameters->setPV(getDopplerThetaParaName(), m_DopplerTheta);
    }
}

qreal DopplerThetaAdjustItem::getTheta(QPointF& mousePoint, QPointF& centerPoint)
{
    qreal radiusDistance = sqrt((mousePoint.x() - centerPoint.x()) * (mousePoint.x() - centerPoint.x()) +
                                (mousePoint.y() - centerPoint.y()) * (mousePoint.y() - centerPoint.y()));
    qreal radiusHeight = centerPoint.y() - mousePoint.y();
    return asin(abs(radiusHeight) / radiusDistance) * 180 / M_PI;
}

QString DopplerThetaAdjustItem::getDopplerThetaParaName()
{
    QString paraName;
    if (m_SonoParameters->pBV(BFPNames::TDIEnStr))
    {
        paraName = BFPNames::DopplerThetaTDIStr;
    }
    else if (!ModelConfig::instance().value(ModelConfig::HPrfCW, false).toBool() &&
             (m_SonoParameters->pBV(BFPNames::CWEnStr) ||
              (m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr) &&
               m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr))))
    {
        paraName = BFPNames::DopplerThetaCWDStr;
    }
    else
    {
        paraName = BFPNames::DopplerThetaStr;
    }
    return paraName;
}
