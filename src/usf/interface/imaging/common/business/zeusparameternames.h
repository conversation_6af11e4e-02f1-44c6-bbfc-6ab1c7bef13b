#ifndef ZEUSPARAMETERNAMES_H
#define ZEUSPARAMETERNAMES_H

#include "usfinterfaceimagingbusiness_global.h"
#include <QString>
#include <QMetaEnum>
#include <QObject>

namespace ZeusParameterNames
{
enum USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT ParameterType
{
    Dsc_Int,
    Dsc_Double,
    Dsc_Bool,
    Dsc_Array,
    Dsc_Pointer
};

class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT ProcessSonoParameterIndex : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief The PreProcessSonoParameterIndex enum
     * 本枚举中枚举名对应的字符串是给Zeus用于二次采样前 / IQ处理链路使用的参数名
     * 后续新增枚举名时，只需按照Zeus约定的参数名增加枚举名即可，无需再更新cpp文件
     */
    enum PreProcessSonoParameterIndex
    {
        // color
        PRECAccumTimes,
        PRECCET,
        PRECCETDyn,
        PRECCVRT,
        PRECCVLT,
        PRECCHET,
        PRECEtBfWf,
        PRECHDCPAWallThr,
        PRECCGainThr,
        PRECCfmNoiseVlt,
        PRECCfmNoiseSlope,
        PRECCfmFlashVlt,
        PRECCfmFlashSlope,
        PRECTDIEn,
        PRECSMIEn,
        PRECCPAEn,
        PRECBiCPAEn,
        PRECFacMode,
        PRECWFGainCtrl,
        PRECWFGainCtrlDyn,
        PRECCfmEnergeCtrl,
        PRECQuitSamp_Tail,
        PRECQuitSamp_Head,
        PRECBaseLine,
        PRECSMIPosGain,
        PRECDynFLogSel,
        PRECCfmEngCtrl0,
        PRECCfmEngCtrl1,
        PRECCfmEngCtrl2,
        PRECCfmEngCtrl3,
        PRECCfmEngCtrl4,
        PRECCfmEngCtrl5,
        PRECCfmEngCtrl6,
        PRECCfmEngCtrl7,
        PRECCfmEngCtrl8,
        PRECCfmEngCtrl9,
        PRECCfmEngCtrl10,
        PRECCfmEngCtrl11,
        PRECCfmEngCtrl12,
        PreColorPersistenceEnable,
        ColorPersistenceAlgorithm,
        ColorPersistenceHigh,
        ColorPersistenceLow,
        ColorPersistenceAN,
        ColorPersistenceRatio,
        ColorPersistenceUsingN_1,

        ////pw
        PWVolum,
        PWSampNumsel,
        PWWallFilterSel,
        PWAccumNum,
        PWVL,
        PWVK,
        PWVN,
        PWDEnhance,
        PWTDTimefilter,
        PWCWScanMode,
        TriplexRefresh,
        AudioSegment,
        PRF,
        ADFreqMHz,
        PWDDFSel,
        SonicSpeed,
        PWVolumValue,
        AudioPreGain,
        AudioPostGain,
        AudioFilter,
        PWWallFilter,
        PWWindowFilter,
        IsSonoPWOn,
        CurrentGate,
        IsSmallPW,
        PWHPRF,

        // other
        BMSecondSampDatNum,
        PRECOriLineNum,
        ColorLineNumAfterBeamforming,
        PREC4BeamOverlapEn,
        ColorPointNumAfterReduce,
        PREC4BeamWTCoe,
        PRECCpa_DR,
        PRECWF80Coe,
        PRECWF100Coe,
        PWSecondSampDatNum,
        // cw
        CWAntiAliasTabNum1,
        CWAntiAliasTabNum2,
        CWDecimatorRate1st,
        CWDecimatorRate2nd,
        CWAntiAliasValues1,
        CWAntiAliasValues2,
        CWPointNum,
        CWLineNum,
        CWInvert,
        CWSecondSampDatNum,
        PWDGain_Value,
        PreProcessSonoParameterCount
    };
    Q_ENUM(PreProcessSonoParameterIndex)

    /**
     * @brief The PostProcessSonoParameterIndex enum
     * 本枚举中枚举名对应的字符串是给Zeus用于线数据处理链路使用的参数名
     * 后续新增枚举名时，只需按照Zeus约定的参数名增加枚举名即可，无需再更新cpp文件
     */
    enum PostProcessSonoParameterIndex
    {
        LineNumAfterBeamforming, //波束合成完成后的总线数
        LineNum,                 // input height
        ColorLineNum,            //彩色
        StartLineColor,
        StopLineColor,
        StartDepth,
        StopDepth,
        PixelSizeMM,
        CompoundNum, // scp + 1
        HorizontalFlip,
        VerticalFlip,
        HorizontalFlipWave,
        VerticalFlipWave,
        SteeringAngle,
        SteeringAngle2,
        SteeringAngle3,
        SteeringAngle4,
        SteeringAngle5,
        CSteeringAngle,
        BaseLine,
        DynamicRange, // for PW
        Depth,
        // Smooth
        AxialSigma,
        LateralSigma,
        SmoothEnable,
        // Xcontrast
        ContrastLevel,
        XcontrastEnable,
        //
        B2DSteeringAngle,
        ExtraScale,
        NeedleAngle,
        CompoundDebug,
        TrapzoidalAngle, //虚拟顶点扩展角
        TopBorderColor,
        BottomBorderColor,
        NeedleLineNum,
        PWInvert,
        ColorTransparencyEnable,
        ColorTransparency,
        NeedToEraseB,
        DynFlowMixType,
        IntegerDepthEnable,
        PointInterval,
        ROITopDepth,
        ROIBottomDepth,
        ColorRawDataCallbackEnable,
        HighDensity,
        OutGain,
        EnableColorDisplay, //彩色模式的血流色彩可见控制参数
        PWDynamicRangeAdjustmentEnable,
        WaferRadius,       // 半径
        ProbeAngle,        //开角
        TotalLines,        //基元数 * 2 * 任意线密度比例
        TotalLinesColor,   //彩色
        AngleSpacing,      //线间角度
        AngleSpacingColor, //彩色线间角度
        LineSpacing,       //线间距
        LineSpacingColor,  //彩色线间距离
        IsLinear,          //线阵标记
        LineSpacingCompound,
        AngleSpacingCompound,
        NeedleLineSpacing,
        PointNum,
        ColorPointNum,
        RealDepth,
        StartLine,
        StopLine,
        CompoundEnable,           // scpon
        CompoundMethod,           //--
        CompoundMethodFor2Frames, //--
        AmplifyDepth,             //--
        CompoundBeta,             //--
        CompoundGamma,            //--
        NeedleEnable,             //--
        PersistenceLevel,         // FrameAvgStr --

        CfmPerThr,
        CpaCoef,
        CDynPsEn,
        CfmSlope,
        CPriority,
        PersistenceLevelColor,
        RotationAngle,
        RotationAngleWave,
        ZoomCoefWave,
        WtStartPoint,
        WtCoefficience,
        IImageIndex,
        MidGain,
        MinThr,
        ColorDataType,
        PowerThreshold, //壁墙门槛
        IsDpd,          // For color, if use dpd.

        ColorAFCoef0,
        ColorAFCoef1,
        ColorAFCoef2,
        ColorAFCoef3,
        ColorAFIRGain,
        ColorAFIRShift,
        AComeBack,
        ColorLFCoef0,
        ColorLFCoef1,
        ColorLFCoef2,
        ColorLFIRGain,
        ColorLFIRShift,
        LComeBack,
        BloodEffect,
        CFMIIREnable,

        ZoomOn,       //局部放大开启/关闭状态 type:bool
        AnyDensityEn, //任意线密度使能       type:bool
        PAFCoef0,
        PAFCoef1,
        PAFCoef2,
        PAFCoef3,
        PAFIRGain,
        PAFIRShift,
        PAComeBack,
        PLFCoef0,
        PLFCoef1,
        PLFCoef2,
        PLFIRGain,
        PLFIRShift,
        PLComeBack,
        PersistenceLevelDyn,
        BMPAFlag,
        ColorEDAEnable,
        ColorEDASESize,
        ColorEDASEType,
        CFVelocityThreshold,
        // 斑点平滑 后处理参数
        BMSMAlpha,   //此参数值跟随控制表参数
        BMSMModeSel, //此参数值跟随控制表参数
        // 黑洞填充 后处理参数
        BMHFAlpha,   //控制字 HF_Alpha     权重
        BMHFModeSel, //控制字 HF           算法
        // Edge Enhance 后处理参数添加 start
        BMSFIRGain,  //控制字 AFGain
        BMSFIRShift, //控制字 AFShift
        BMAFCoef0,   //控制字 AFCoef0
        BMAFCoef1,   //控制字 AFCoef1
        BMAFCoef2,   //控制字 AFCoef2
        BMAFCoef3,   //控制字 AFCoef3
        BMAFCoef4,   //控制字 AFCoef4
        BMLEEGain,   //控制字 LeeGain
        BMLEEShift,  //控制字 LeeShift
        BMLEE_MCoef, //控制字 LeeMCoef
        BMLEE_SCoef, //控制字 LeeSCoef
        // Edge Enhance 后处理参数添加 end

        //新增彩色平滑算法参数
        ColorVHSIType,
        ColorHoleFillingDB,
        // 后处理参数 end

        SystemScanMode,
        DisplayWidth,  // output width
        DisplayHeight, // output height
        ColorMap2D,
        ColorMap2DChangedTrigger,
        ColorMapWave,
        ColorMapWaveChangedTrigger,
        Layout$StartX,
        Layout$StartY,
        Layout$Width,
        Layout$Height,
        Layout1StartX,
        Layout1StartY,
        Layout1Width,
        Layout1Height,
        Layout2StartX,
        Layout2StartY,
        Layout2Width,
        Layout2Height,
        WavePointNum,
        WaveLineNum,
        PersistenceReset,
        TrapezoidalMode,
        PaVertDistEnable,  //虚拟顶点开关
        SteeringAngleForT, //虚拟顶点偏转角
        VerticalDis,       //虚拟顶点垂距
        DSCForcePushTrigger,
        DataSyncID,
        ZeusDebugFlag,
        ImageFilterHoleFillerThresh,
        ImageFilterHFNoise,
        ImageFilterNoiseLevel,
        ImageFilterEdgeEnhance,
        ImageFilterFilterStrength,
        ImageFilterDetailPreservation,
        ImageFilterNoiseFilterType,
        ImageFilterEdgeRampDown,
        ImageFilterEdgeThreshold,
        ImageFilterEdgeDirectionThresh,
        ImageFilterOverallStrength,
        ImageFilterEdgeFilterType,
        ImageFilterEnable,
        ProcessAfterFreezeEnable,
        BMDTGC_Gain$,
        BMDTGCAfterFreeze_Gain$,
        BMGainValue,
        BMGainValueAfterFreeze,
        BMSegmNum,
        BMSegmNumForLGC,
        BMDR,
        BMDRAfterFreeze,
        PreMGainValue,
        PreMGainValueAfterFreeze,
        PreMDynamicRange,
        PreMDynamicRangeAfterFreeze,
        BMHTGC_Value$,
        BMHTGC_AfterFreezeValue$,
        // 后处理优化控制参数
        BPostEnable, //此参数只与机型有关，且默认为true (SonoAir、P9、Phoenix、ATOM)
        CPostEnable, //此参数只与机型有关，且默认为true (SonoAir、P9、Phoenix、ATOM)
        // DSC插值方法控制参数
        DscMethod, //此参数只与机型有关
        DRTypeForB,
        BlockData42,
        BlockData43,
        BlockData80,
        BlockData100,
        BlockData254,
        //    Zoom, // TODO: 确认目前还需要么
        ZoomEnable,
        ZoomCoef,
        OutputDisplayStartXForZoom,
        OutputDisplayStartYForZoom,
        OutputDisplayWidthForZoom,
        OutputDisplayHeightForZoom,
        MultiBeamNumber,   //波束数
        OverlappingMB,     // Overlap 的线数，调图界面可变参数
        KeepEdgeBeams,     //是否保留两侧的 Beam，最新方案固定为true
        StripRemoveEnable, //多波束合成使能开关
        InterpolatorOrder, //插值阶数，调图界面可变参数
        StripRemoveFPGA,   //多波束合成的位置，true:前处理；false:后处理
        PostProcessSonoParameterCount
    };
    Q_ENUM(PostProcessSonoParameterIndex)
};

USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT QString
PreProcessSonoParameterName(ProcessSonoParameterIndex::PreProcessSonoParameterIndex index, bool isArg = false);
// USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT QString PreProcessSonoParameterName(int index);

#define PREPARANAME(value) ZeusParameterNames::PreProcessSonoParameterName(value)

USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT QString
postProcessSonoParameterName(ProcessSonoParameterIndex::PostProcessSonoParameterIndex index, bool isArg = false);
// USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT QString postProcessSonoParameterName(int index);

#define POSTPARANAME(value) ZeusParameterNames::postProcessSonoParameterName(value)

#define POSTPARANAMEARG(value) ZeusParameterNames::postProcessSonoParameterName(value, true)

} // namespace ZeusParameterNames

#endif // ZEUSPARAMETERNAMES_H
