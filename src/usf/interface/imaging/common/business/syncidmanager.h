#ifndef SYNCIDMANAGER_H
#define SYNCIDMANAGER_H

#include <QObject>
#include <QMutex>
#include "sonoparameters.h"
#include "usfinterfaceimagingbusiness_global.h"
enum class SyncIDMode
{
    ControlTableSyncID, //控制表SyncID
    PrePostSyncId       //前处理和后处理SyncID
};
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT SyncIDManager : public QObject
{
    Q_OBJECT
public:
    explicit SyncIDManager(QObject* parent = nullptr);
    static SyncIDManager& instance();
    /**
     * @brief currentSyncId
     * @return 返回SyncID值
     */
    unsigned char currentSyncId(SyncIDMode syncIDMode) const;
    /**
     * @brief updatePrePostSyncId 统一了前处理和后处理SyncID值，此函数更新前处理和后处理的SyncID值
     *        控制表SyncID值的变更在controltable.cpp的sendBlock中
     */
    void updateSyncId(const SyncIDMode syncIDMode);
    void setSonoParameters(SonoParameters* sonoParameters);

private:
    static SyncIDManager* m_Instance;
    SonoParameters* m_SonoParameters;

    mutable QMutex m_Mutex;
    unsigned char m_CurrentPrePostSyncId;

signals:
};

#endif // SYNCIDMANAGER_H
