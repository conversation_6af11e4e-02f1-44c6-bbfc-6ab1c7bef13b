#ifndef IIMAGEMANAGER_H
#define IIMAGEMANAGER_H
#include "usfinterfaceimagingbusiness_global.h"

#include "funcstatedescriptiondef.h"
#include "imagecommondefine.h"
#include "infostruct.h"
#include "baseaspect.h"
#include "usfobject.h"

class IPeripheralManager;
class IExamManager;
class IMarkManager;
class QWidget;
class SonoParameters;
class IBeamFormer;
class IProbePresetModel;
class ChisonUltrasoundContext;
class IBufferStoreManager;
class ILineBufferManager;
class CineLooper;
class IStateManager;
class UpdateController;
class IDiskDevice;
class IColorMapManager;
class IProbeDataSet;
class IImageSaveHelper;
class IElementDetectAlgorithom;
class IImageInterfaceForExam;
class TGCAdjustmentWidgetController;

/**
 * @brief IImageManager: 超声图像子系统接口基类
 *
 * 用途：提供与超声图像相关的对外抽象接口，同时，赋予Qt信号-槽机制，观察者主题，以及日志记录辅助功能等；
 */
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT IImageManager : public USFObject, public BaseAspect
{
public:
    IImageManager()
    {
    }
    virtual ~IImageManager()
    {
    }
    virtual void init() = 0;
    virtual void release() = 0;
    virtual void reset() = 0;
    virtual void startRealtimeSystem() = 0;
    virtual void freeze() = 0;
    /**
     * @brief unFreeze: 图像子系统执行解冻进入实时扫描状态时的操作
     *
     */
    virtual void unFreeze() = 0;
    /**
     * @brief unFreezeState: 图像子系统执行退出解冻状态时的控制
     *
     */
    virtual void unFreezeState() = 0;
    /**
     * @brief switchScanMode: 图像子系统执行扫描模式切换时的业务控制
     * input:scan_mode,系统定义的扫描模式枚举类型
     * input:state_name,状态名称，有些状态下扫描模式系统，需要通过状态名来区分不同的控制业务
     */
    virtual void switchScanMode(SystemScanMode scan_mode, const QString& state_name) = 0;
    /**
     * @brief changeLayout: 图像子系统执行布局layout切换时的业务控制
     * input:scan_mode,系统定义的扫描模式枚举类型
     * input:layout,布局方式，参考系统定义的Layout枚举类型
     * input:active_layout,当前激活的布局方式
     */
    virtual void changeLayout(SystemScanMode scan_mode, int layout, int active_layout) = 0;
    /**
     * @brief adjustParam: 调节系统参数值； Notes: 此接口当前仅支持系统实时参数的调节，后续有需要再做接口的功能扩展
     * input: name,参数名称
     * input: value，待设置的参数值
     * input: type,调节参数类型
     */
    virtual bool adjustParam(const QString& name, const QVariant& value,
                             PARAM_ADJUST_ASK_TYPE type = PARAM_ADJUST_ASK_TYPE::PARAM_VALUE) = 0;
    /**
     * @brief askParam: 查询参数值
     * input: name,参数名称
     * out：result，参数值结果
     * input:ask_type,查询参数类型
     * input:source_type,查询参数对象源类型，有些应用需要从实时参数对象查询，有些需要从回调等参数对象查询
     */
    virtual bool askParam(const QString& name, QVariant& result, PARAM_ADJUST_ASK_TYPE ask_type = PARAM_VALUE,
                          IMG_PARAM_SOURCE_TYPE source_type = Realtime) = 0;
    virtual void setPeripheralManager(IPeripheralManager* value) = 0;
    virtual IPeripheralManager* getPeripheralManager() = 0;
    virtual void setExamManager(IExamManager* value) = 0;
    virtual void setMarkManager(IMarkManager* value) = 0;
    virtual void setMainWindow(QWidget* parent) = 0;
    virtual void setStateManagerFacade(IStateManager* value) = 0;
    virtual void setDiskDevice(IDiskDevice* diskDevice) = 0;
    virtual int getFrameCount() const = 0;
    virtual bool enterState(FUNC_STATE state, const FuncStateInput& input = FuncStateInput()) = 0;
    virtual bool exitState(FUNC_STATE state, const FuncStateInput& input = FuncStateInput()) = 0;
    virtual void waitForHasFrameData(int delay) = 0;
    virtual void waitFor2DImagePushed(int delay, int layoutIndex) = 0;
    virtual void controlVideoFullScreen(bool value) = 0;
    virtual void resetTGCData() = 0;
    virtual void setColorMap(uchar* image2DMap, uchar* waveMap) = 0;
    virtual SonoParameters* sonoParameters() const = 0;
    virtual CineLooper* cineLooper() const = 0;
    virtual bool isCineLooperLooping() = 0;
    virtual void startCineLooper() = 0;
    virtual void stopCineLooper() = 0;
    virtual void generateBFNames() = 0;
    virtual SystemScanMode systemScanMode() const = 0;
    virtual IBeamFormer* beamFormer() const = 0;
    virtual IProbePresetModel* probePresetModel() const = 0;
    virtual ChisonUltrasoundContext* chisonUltrasoundContext() const = 0;
    virtual IBufferStoreManager* bufferStoreManager() const = 0;
    virtual ILineBufferManager* lineBufferManager() const = 0;
    virtual QString currentExamModeID() const = 0;
    virtual UpdateController* updateController() const = 0;
    virtual TGCAdjustmentWidgetController* controllerOfTGCAdjustment() const = 0;
    virtual void updateMotorParameter() = 0;
    virtual void startCheckProbeTimer() = 0;
    virtual void clearBuffers() = 0;
    virtual void sendHardwareKey() = 0;
    virtual void checkAutoUpdate() = 0;
    virtual IProbeDataSet* probeDataSet() const = 0;
    virtual IImageSaveHelper* imageSaveHelper() const = 0;
    virtual IElementDetectAlgorithom* elementDetectAlgorithom() const = 0;
    virtual IImageInterfaceForExam* imageInterfaceForExam() const = 0;
    virtual IColorMapManager* colorMapManager() const = 0;
    virtual bool curProbeIsConnected() const = 0;
    virtual void stopUpdateControlTimer() = 0;
    virtual void startUpdateControlTimer() = 0;
    virtual bool isNeedUpdateFPGA(int type, const QString& key) = 0;
    virtual void setMustUpdateFPGA(bool mustUpdateFPGA) = 0;
    virtual void updateFPGA(int type, const QString& key) = 0;

    virtual void setSafeVolatile2CTNumber(float value) = 0;
    virtual bool checkCWVolatile(float safeVoltage, int times) = 0;
};

#endif
