#include "bufferunit.h"
#include "assertlog.h"
#include "varianthash.h"
#include <QDataStream>
#include <QHash>
#include <QString>
#include <QVariant>

BufferUnit::BufferUnit()
    : QVector<ByteBuffer>()
{
}

BufferUnit::BufferUnit(int size)
    : QVector<ByteBuffer>(size)
{
}

qint64 BufferUnit::startTime() const
{
    return m_StartTime;
}

void BufferUnit::setStartTime(qint64 value)
{
    m_StartTime = value;
    m_Operating = true;
}

int BufferUnit::duration() const
{
    return m_Duration;
}

void BufferUnit::setDuration(int value)
{
    m_Duration = value;
    m_Operating = false;
}

void BufferUnit::zeroMemory()
{
    for (int i = 0; i < count(); i++)
    {
        if (!(*this)[i].isNull())
        {
            (*this)[i].zeroMemory();
        }
    }
}

int BufferUnit::byteSize() const
{
    int size = 0;
    for (int i = 0; i < count(); i++)
    {
        if (!(*this)[i].isNull())
        {
            size += (*this)[i].len();
        }
    }
    return size;
}

bool BufferUnit::isNull() const
{
    if (isEmpty())
    {
        return true;
    }

    for (int i = 0; i < count(); i++)
    {
        if ((*this)[i].isNull())
        {
            return true;
        }
    }

    return false;
}

BufferUnit BufferUnit::clone() const
{
    BufferUnit other;
    other.copyFrom(*this);
    return other;
}

void BufferUnit::copyFrom(const BufferUnit& other)
{
    m_StartTime = other.startTime();
    m_Duration = other.duration();
    m_Operating = other.isOperating();
    m_Steering = other.steering();
    m_FrameIndex = other.frameIndex();
    m_EcgEnd = other.EcgEnd();
    m_IsWaveFullScreen = other.isWaveFullScreen();
    m_IsByPass = other.isByPass();

    if (count() != other.count())
    {
        resize(other.count());
    }

    for (int i = 0; i < count(); i++)
    {
        if (!other[i].isNull())
        {
            (*this)[i].copyFrom(other[i]);
        }
    }
}

void BufferUnit::destroy()
{
    for (int i = 0; i < count(); i++)
    {
        if (!(*this)[i].isNull())
        {
            (*this)[i].destroy();
        }
    }
}

bool BufferUnit::isOperating() const
{
    return m_Operating;
}

void BufferUnit::setOperating(bool value)
{
    m_Operating = value;
}

int BufferUnit::steering() const
{
    return m_Steering;
}

void BufferUnit::setSteering(int value)
{
    m_Steering = value;
}

unsigned int BufferUnit::frameIndex() const
{
    return m_FrameIndex;
}

void BufferUnit::setFrameIndex(unsigned int value)
{
    m_FrameIndex = value;
}

unsigned int BufferUnit::waveFrameIndex() const
{
    return m_WaveFrameIndex;
}

void BufferUnit::setWaveFrameIndex(unsigned int value)
{
    m_WaveFrameIndex = value;
}

unsigned int BufferUnit::frontIndex() const
{
    return m_FrontIndex;
}

void BufferUnit::setFrontIndex(unsigned int value)
{
    m_FrontIndex = value;
}

unsigned int BufferUnit::cacheFrameIndex() const
{
    return m_cacheFrameIndex;
}

void BufferUnit::setCacheFrameIndex(unsigned int value)
{
    m_cacheFrameIndex = value;
}

bool BufferUnit::isWaveFullScreen() const
{
    return m_IsWaveFullScreen;
}

void BufferUnit::setWaveFullScreen(bool isWaveFullScreen)
{
    m_IsWaveFullScreen = isWaveFullScreen;
}

int BufferUnit::EcgEnd() const
{
    return m_EcgEnd;
}

void BufferUnit::setEcgEnd(int end)
{
    m_EcgEnd = end;
}

bool BufferUnit::isByPass() const
{
    return m_IsByPass;
}

void BufferUnit::setByPass(bool pass)
{
    m_IsByPass = pass;
}

int BufferUnit::imageRenderPartition() const
{
    return m_ImageRenderPartition;
}

void BufferUnit::setImageRenderPartition(int partition)
{
    m_ImageRenderPartition = partition;
}

bool BufferUnit::isMeasureData() const
{
    return m_IsMeasureData;
}

void BufferUnit::setMeasureData(bool isMeasureData)
{
    m_IsMeasureData = isMeasureData;
}

QDataStream& operator>>(QDataStream& in, BufferUnit& v)
{
    QHash<QString, QVariant> values;
    in >> values;

    v.setStartTime(values["StartTime"].toInt());
    v.setDuration(values["Duration"].toInt());
    v.setOperating(values["Operating"].toBool());
    v.setSteering(values["Steering"].toInt());
    v.setFrameIndex(values["FrameIndex"].toUInt());
    v.setEcgEnd(values["EcgEnd"].toUInt());
    v.setFrameIndex(values["CacheFrameIndex"].toUInt());
    v.setWaveFullScreen(values["WaveFullScreen"].toBool());
    if (values["Unit"].toBool())
    {
        QVector<ByteBuffer> buffers;
        in >> buffers;
        ASSERT_X_LOG(buffers.count() == v.count(), "read BufferUnit", "Two count must be equal");
        for (int i = 0; i < v.count(); i++)
        {
            in >> v[i];
        }
    }

    return in;
}

QDataStream& operator<<(QDataStream& out, const BufferUnit& v)
{
    QHash<QString, QVariant> values;
    values["StartTime"] = v.startTime();
    values["Duration"] = v.duration();
    values["Operating"] = v.isOperating();
    values["Steering"] = v.steering();
    values["FrameIndex"] = v.frameIndex();
    values["EcgEnd"] = v.EcgEnd();
    values["Unit"] = !v.isNull();
    values["CacheFrameIndex"] = v.cacheFrameIndex();
    values["WaveFullScreen"] = v.isWaveFullScreen();
    out << values;

    out << (const QVector<ByteBuffer>&)v;

    return out;
}
