#ifndef IMAGEBUFFERDEF_H
#define IMAGEBUFFERDEF_H

namespace ImageBufferDef
{
/**
 * @brief 设备上传的原始数据的格式，同一机型只会是一种类型
 *
 * 本枚举用于区分图像处理的链路，与FPGA、Zeus有关
 */
enum RawDataFormat
{
    /**
     * @brief 整帧数据
     */
    WholeImage = 0,
    /**
     * @brief 线数据
     */
    LineData = 1,
    /**
     * @brief Color、PW、CW是IQ数据
     */
    IQData = 2,
    /**
     * @brief Beamformer之后的数据
     */
    BFData = 3,
    Count
};

/**
 * @brief 读数据的方式，普通的数据包方式和RDMA方式
 */
enum ReceiveMethod
{
    Normal,
    RDMA
};

/**
 * @brief 线数据的实际意义
 *
 * 本枚举用于区分超声软件展示数据的方式
 */
enum ImageType
{
    /**
     * @brief 整帧图像
     * 此时的图像可直接渲染/绘制
     */
    ImageCombine = 0,
    /**
     * @brief 2D图像
     */
    Image2D = 1,
    /**
     * @brief M/PW/CWD 波形图像
     */
    ImageWave = 2,
    /**
     * @brief 声音数据
     */
    ImageSound = 3,
};

/**
 * @brief 原始数据的模式
 *
 * 本枚举用于缓存中数据类型的区分
 */
enum RawDataMode
{
    /**
     * @brief 整帧模式
     */
    Whole_Data = 0x00,
    /**
     * @brief B模式
     */
    B_Data = 0x01,
    /**
     * @brief C模式
     */
    C_Data = 0x02,
    /**
     * @brief D模式
     */
    D_Data = 0x04,
    /**
     * @brief M模式
     */
    M_Data = 0x08,
    /**
     * @brief B M模式
     */
    BM_Data = B_Data | M_Data,
    /**
     * @brief CWD模式
     */
    CWD_Data = 0x10,
    /**
     * @brief CM模式
     */
    CM_Data = 0x20,
    /**
     * @brief B CWD模式
     */
    BCWD_Data = B_Data | CWD_Data,
    /**
     * @brief B D模式
     */
    BD_Data = B_Data | D_Data,
    /**
     * @brief B C D模式
     */
    BCD_Data = B_Data | C_Data | D_Data,
    /**
     * @brief B C CWD模式
     */
    BCCWD_Data = B_Data | C_Data | CWD_Data,
    /**
     * @brief ECG
     */
    ECG_Data = 0x40,
    /**
     * @brief 弹性模式
     */
    Elasto_Data = 0x80,
    /**
     * @brief 4D模式
     */
    FourD_Data = 0x100,
    /**
     * @brief 声音数据
     */
    Sound_Data = 0x200,

    CurvedPanoramic_Data = 0x400,
    /**
     * @brief Dynamic Flow / MVI data
     */
    DynFlow_Data = 0x800,

    NULL_Data = 0xFFFFFFFF
};

/**
 * @brief SupperNeedle偏转标志
 */
enum
{
    SupperNeedleSteer = 100
};
} // namespace ImageBufferDef

#endif // IMAGEBUFFERDEF_H
