#ifndef BUFFERUNIT_H
#define BUFFERUNIT_H

#include "usfinterfaceimagingbusiness_global.h"
#include "bytebuffer.h"

/**
 * @class BufferUnit
 * @brief 超声数据缓存单元
 *
 * @details 数据结构示意：
 *
 * B模式：
 * @code
 * +----------------+
 * | ByteBuffer[0]  |  // 单个B模式数据
 * +----------------+
 * @endcode
 *
 * C模式：
 * @code
 * +----------------+
 * | ByteBuffer[0]  |  // B模式数据
 * +----------------+
 * | ByteBuffer[1]  |  // C模式数据
 * +----------------+
 * @endcode
 *
 * 4D模式：
 * @code
 * +----------------+
 * | ByteBuffer[0]  |  // 体数据第1帧
 * +----------------+
 * | ByteBuffer[1]  |  // 体数据第2帧
 * +----------------+
 * |      ...       |  // 更多帧
 * +----------------+
 * | ByteBuffer[n]  |  // 体数据第n帧
 * +----------------+
 * @endcode
 *
 * 每个ByteBuffer包含：
 * @code
 * +----------------+
 * |    数据长度     |
 * +----------------+
 * |    数据内容     |
 * +----------------+
 * @endcode
 */
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BufferUnit : public QVector<ByteBuffer>
{
public:
    /**
     * @brief 默认构造函数
     */
    BufferUnit();

    /**
     * @brief 带大小的构造函数
     * @param size 初始大小
     */
    explicit BufferUnit(int size);

    /**
     * @brief 获取开始时间
     * @return 开始时间
     */
    qint64 startTime() const;

    /**
     * @brief 设置开始时间
     * @param value 开始时间
     */
    void setStartTime(qint64 value);

    /**
     * @brief 获取持续时间
     * @return 持续时间
     */
    int duration() const;

    /**
     * @brief 设置持续时间
     * @param value 持续时间
     */
    void setDuration(int value);

    /**
     * @brief 将内存清0
     * @details 清空所有ByteBuffer的数据内容
     */
    void zeroMemory();

    /**
     * @brief 获取所有ByteBuffer的总字节数
     * @return 所有ByteBuffer的len()之和
     */
    int byteSize() const;

    /**
     * @brief 检查是否为空
     * @return true: 容器为空或任一ByteBuffer为空; false: 所有ByteBuffer都不为空
     */
    bool isNull() const;

    /**
     * @brief 深拷贝
     * @return 新的BufferUnit实例，包含所有数据的完整副本
     */
    BufferUnit clone() const;

    /**
     * @brief 从其他BufferUnit复制数据
     * @param other 源BufferUnit
     * @details 复制所有属性和ByteBuffer数据
     */
    void copyFrom(const BufferUnit& other);

    /**
     * @brief 释放内存
     * @details 释放所有ByteBuffer的内存
     */
    void destroy();

    /**
     * @brief 获取操作状态
     * @return true: 正在操作; false: 未操作
     */
    bool isOperating() const;

    /**
     * @brief 设置操作状态
     * @param value 操作状态
     */
    void setOperating(bool value);

    /**
     * @brief 获取偏转方向
     * @return 0:不偏转, 1:左偏, 2:右偏
     */
    int steering() const;

    /**
     * @brief 设置偏转方向
     * @param value 偏转方向
     */
    void setSteering(int value);

    /**
     * @brief 获取帧索引
     * @return 帧索引
     */
    unsigned int frameIndex() const;

    /**
     * @brief 设置帧索引
     * @param value 帧索引
     */
    void setFrameIndex(unsigned int value);

    unsigned int waveFrameIndex() const;

    void setWaveFrameIndex(unsigned int value);

    /**
     * @brief 获取前向索引
     * @return 前向索引
     */
    unsigned int frontIndex() const;

    /**
     * @brief 设置前向索引
     * @param value 前向索引
     */
    void setFrontIndex(unsigned int value);

    /**
     * @brief 获取缓存帧索引
     * @return 缓存帧索引
     */
    unsigned int cacheFrameIndex() const;

    /**
     * @brief 设置缓存帧索引
     * @param value 缓存帧索引
     */
    void setCacheFrameIndex(unsigned int value);

    /**
     * @brief 获取ECG结束位置
     * @return ECG结束位置
     */
    int EcgEnd() const;

    /**
     * @brief 设置ECG结束位置
     * @param end ECG结束位置
     */
    void setEcgEnd(int end);

    /**
     * @brief 获取是否绕过处理
     * @return true: 绕过处理; false: 不绕过
     */
    bool isByPass() const;

    /**
     * @brief 设置是否绕过处理
     * @param pass 是否绕过
     */
    void setByPass(bool pass);

    /**
     * @brief 获取是否全屏显示波形
     * @return true: 全屏显示; false: 非全屏
     */
    bool isWaveFullScreen() const;

    /**
     * @brief 设置是否全屏显示波形
     * @param isWaveFullScreen 是否全屏
     */
    void setWaveFullScreen(bool isWaveFullScreen);

    /**
     * @brief 获取图像渲染分区
     * @return 渲染分区
     */
    int imageRenderPartition() const;

    /**
     * @brief 设置图像渲染分区
     * @param partition 渲染分区
     */
    void setImageRenderPartition(int partition);

    /**
     * @brief 返回当前数据是否有效的测量数据
     * 用于波形数据卷屏情况
     * @return
     */
    bool isMeasureData() const;

    /**
     * @brief 设置测量数据的有效标记
     * @param isMeasureData
     */
    void setMeasureData(bool isMeasureData);

private:
    qint64 m_StartTime{0};             ///< 开始时间
    int m_Duration{0};                 ///< 持续时间
    bool m_Operating{false};           ///< 操作状态
    int m_Steering{0};                 ///< 偏转方向
    unsigned int m_FrameIndex{0};      ///< 帧索引
    unsigned int m_WaveFrameIndex{0};  ///< 波形帧索引
    unsigned int m_FrontIndex{0};      ///< 前向索引
    int m_EcgEnd{0};                   ///< ECG结束位置
    unsigned int m_cacheFrameIndex{0}; ///< 缓存帧索引
    bool m_IsWaveFullScreen{false};    ///< 是否全屏显示波形
    bool m_IsByPass{false};            ///< 是否绕过处理
    int m_ImageRenderPartition{0};     ///< 图像渲染分区
    bool m_IsMeasureData{false};       ///< 测量数据的有效标记
};

Q_DECLARE_METATYPE(BufferUnit);

/**
 * @brief 从数据流读取BufferUnit
 * @param in 输入数据流
 * @param v 目标BufferUnit
 * @return 输入数据流引用
 */
USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT QDataStream& operator>>(QDataStream& in, BufferUnit& v);

/**
 * @brief 将BufferUnit写入数据流
 * @param out 输出数据流
 * @param v 源BufferUnit
 * @return 输出数据流引用
 */
USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT QDataStream& operator<<(QDataStream& out, const BufferUnit& v);

#endif // BUFFERUNIT_H
