#include "beamformerapple.h"
#include "abstractbfdatahandler.h"
#include "bfapplekitfactory.h"
#include "bfpnames.h"
#include "icontroltable.h"
#include "controltableparameter.h"
#include "controltablesender.h"
#include "ultrasounddevice.h"
#include "util.h"
#ifdef USE_QT_AUDIO
#include "qtaudioplayer.h"
#endif
#include "appsetting.h"
#include "autofocusadjuster.h"
#include "baseprfcalculator.h"
#include "bfadfreqparameter.h"
#include "bfdatahandlerapple.h"
#include "bfdepthparameters.h"
#include "bfstaticparametersapple.h"
#include "calculatorutil.h"
#include "chipfactory.h"
#include "cqyzstepconfig.h"
#include "deviceutil.h"
#include "directprobesswitchcontroller.h"
#include "formula.h"
#include "iblockdatasender.h"
#include "iblockdataparanameconverter.h"
#include "iimagesettings.h"
#include "imagerenderlayoutrects.h"
#include "irelatedparascontroller.h"
#include "istatemanager.h"
#include "linedatahead.h"
#include "logger.h"
#include "modelconfig.h"
#include "newdopprfcalculator.h"
#include "paralistsettings.h"
#include "parameter.h"
#include "probedataset.h"
#include "probeinfomodel.h"
#include "probeparameters.h"
#include "probesswitchcontroller.h"
#include "realcompare.h"
#include "relatedparascontroller.h"
#include "setting.h"
#include "smoothsettings.h"
#include "sonoparameters.h"
#include "postprocess/postprocesshandlerapple.h"
#include <QtMath>
#include <qvector.h>
const double maxTHIDepthMM = 300.0;
LOG4QT_DECLARE_STATIC_LOGGER(logA, BeamFormerApple)
BeamFormerApple::BeamFormerApple(QObject* parent)
    : BeamFormerSonoeye(parent)
    , m_CurMaxCQYZ(64)
    , m_THIReset(false)
    , m_MBBak(-1)
{
#if defined(SYS_UNIX) || defined(SYS_ANDROID)
    m_EcgReceiver = new EcgReceiver;
    connect(m_EcgReceiver, &EcgReceiver::updateData, this, &BeamFormerApple::update, Qt::DirectConnection);
#endif
    m_ProbeInfoModel = new ProbeInfoModel();
}

BeamFormerApple::~BeamFormerApple()
{
#if defined(SYS_UNIX) || defined(SYS_ANDROID)
    if (m_EcgReceiver)
    {
        m_EcgReceiver->deleteLater();
    }
#endif
}

void BeamFormerApple::createStaticParameters()
{
    m_StaticParameters = new BFStaticParametersApple();
}

IBFKitFactory* BeamFormerApple::createBFKitFactory()
{
    return new BFAppleKitFactory();
}

void BeamFormerApple::onExitProbeState(bool isSuspendRead)
{
    ControlTableSyncSender cs(m_ControlTable);

    ControlTableParameter* txOffP = qobject_cast<ControlTableParameter*>(parameter(BFPNames::TxOffStr));
    if (txOffP != NULL)
    {
        //退出探头选择界面，开启发射
        m_ControlTable->send(txOffP, 1 - txOffP->trueValue(), true, false);
    }
}

bool BeamFormerApple::isProbeOnline() const
{
    if (Setting::instance().defaults().isIODeviceVirtual())
    {
        return true;
    }
    else
    {
        if (m_ProbesSwitchController.isNull())
        {
            return false;
        }
        return m_ProbesSwitchController->isProbeOnline();
    }
}

void BeamFormerApple::update(unsigned char* buf, int len)
{
    emit updateImageData(ByteBuffer(buf, len));
}

void BeamFormerApple::resentProbeParamAndBlockData()
{
    ControlTableSyncSender cs(m_ControlTable);
    m_ControlTable->send();
    setGrayMap(pV(BFPNames::GrayMapStr).toByteArray());
    resentCurProbeBlockData();

    updateTrapCureveBSteer();
    QTimer::singleShot(600, this, SLOT(enableConfigDone()));
}

void BeamFormerApple::setSystemScanMode(SystemScanMode mode)
{
    onSetSystemScanMode(mode);
}

void BeamFormerApple::setStartSupportFreezeOutage(bool value)
{
    if (value && m_SupportFreezeOutage && !Setting::instance().defaults().isIODeviceVirtual() &&
        !m_ProbesSwitchController.isNull())
    {
        m_ProbesSwitchController->setStartSupportFreezeOutage(true);
    }
}

void BeamFormerApple::initCWDPRFParameterCalculator()
{
    if (m_CWDopPrfCalculator != nullptr)
    {
        return;
    }

    m_CWDopPrfCalculator = new NewDopPrfCalculator(m_SonoParameters, BasePRFCalculator::CW);
    if (m_IfConnectSignals)
    {
        foreach (const QString& p, m_CWDopPrfCalculator->relatedParameters())
        {
            connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFDop()));
        }
    }
}

void BeamFormerApple::onCWEnChanging(const QVariant& value)
{
    BeamFormerQBit::onCWEnChanging(value);

    if (value.toBool())
    {
        parameter(BFPNames::CWDSampleRateStr)->update();
    }
    else
    {
        setPV(BFPNames::HprfEnStr, false);
        parameter(BFPNames::DSampleRateDopStr)->update();
    }
}

void BeamFormerApple::onCWDSampleRateChanging(const QVariant& value)
{
    if (pBV(BFPNames::ChangedByAdjustmentStr))
    {
        setPV(BFPNames::CWDSampleRateBakStr, value);
    }
}

void BeamFormerApple::onGettingCWDSampleRateMax(int& value)
{
    value = m_CWDopPrfCalculator->hPfrListMaxIndex();
}

void BeamFormerApple::onGettingCWDSampleRateText(QString& value)
{
    if (pBV(BFPNames::CWEnStr))
    {
        double prf = m_CWDopPrfCalculator->realPrf();
        value = Formula::formatFrequency(prf);
    }
}

void BeamFormerApple::onSetSonoParameters()
{
    BeamFormerBase::onSetSonoParameters();
#if defined(SYS_UNIX) || defined(SYS_ANDROID)
    m_EcgReceiver->setSonoParameters(m_SonoParameters);
#endif
}

void BeamFormerApple::shutdown()
{
    sendShutDownProbeBlockData();
    BeamFormerBase::shutdown();
}

QVector<ChipFactory::ChipNameEnum> BeamFormerApple::supportChipList() const
{
    return QVector<ChipFactory::ChipNameEnum>() << ChipFactory::ChipNameEnum::AFE5832;
}

void BeamFormerApple::createPostProcessHandler()
{
    if (m_PostProcessHandler != nullptr)
    {
        return;
    }

    m_PostProcessHandler = new PostProcessHandlerApple();
}

void BeamFormerApple::sendShutDownProbeBlockData()
{
    QList<ProbeInfo*> probeInfoList = m_ProbeInfoModel->getProbeInfos();
    foreach (ProbeInfo* probeInfo, probeInfoList)
    {
        if (probeInfo->isUnPowerOffProbe())
        {
            // send blockdata after open probe
            if (selectSocket(probeInfo->socket()))
            {
                m_BlockDataSender->sendParaBlockDataGroup("PowerOffProbe", "PowerOffProbe", 0);
            }
            else
            {
                logA()->info(QString("%1: open probe error.").arg(PRETTY_FUNCTION));
            }
        }
    }
}

void BeamFormerApple::createBFIODevice()
{
    BeamFormerBase::createBFIODevice(BFIODeviceType::DUMMY);
}

void BeamFormerApple::initializePostParameterCalculators()
{
    BeamFormerEBit::initializePostParameterCalculators();
    if (nullptr == m_CWDopPrfCalculator)
    {
        m_CWDopPrfCalculator = new NewDopPrfCalculator(m_SonoParameters, BasePRFCalculator::CW);
    }
}

bool BeamFormerApple::selectSocket(int socket, bool writeSocket)
{
    Q_UNUSED(writeSocket)
    if (!Setting::instance().defaults().isIODeviceVirtual())
    {
        m_BlockDataSender->clear();
        imageIntoUnstable();

        if (!m_ProbesSwitchController.isNull())
        {
            m_ProbesSwitchController->selectWorkProbe(socket);
        }

        if (m_SupportFreezeOutage && !bfIODevice()->isOpen())
        {
            bfIODevice()->open();
        }
        setPV(BFPNames::SocketStr, socket, true);

        if (m_IsStartUp)
        {
            setPV(BFPNames::EnableFreezeAfterProbeFoundStr, true);
            m_IsStartUp = false;
        }
        return true;
    }
    else
    {
        return BeamFormerBase::selectSocket(socket);
    }
}

bool BeamFormerApple::standby()
{
    qDebug() << PRETTY_FUNCTION << "doStandby............";
    if (!isStandby())
    {
        qDebug() << PRETTY_FUNCTION << "start apple standby";

        bool value = m_SonoParameters->isRealTime();
        if (value)
        {
            m_SonoParameters->setIsRealTime(false);
        }

        freeze(true);

        if (value)
        {
            m_SonoParameters->setIsRealTime(true);
        }

        bool res = false;

        if (!m_ProbesSwitchController.isNull())
        {
            res = m_ProbesSwitchController->standby();
        }

        setIsStandby(res);

        //待机时，重置当前连接探头
        QVector<int> codes(4, 0);
        QVector<bool> changed(4, true);
        emit probeChanged(codes, changed);

        return res;
    }
    return true;
}

bool BeamFormerApple::wake()
{
    logA()->info(QString("%1").arg(PRETTY_FUNCTION));
    if (isStandby())
    {
        if (!Setting::instance().defaults().isIODeviceVirtual() && !m_ProbesSwitchController.isNull())
        {
            m_ProbesSwitchController->wake();
        }

        setIsStandby(false);

        return true;
    }
    return false;
}

void BeamFormerApple::restoreData()
{
    UltrasoundDevice* device = dynamic_cast<UltrasoundDevice*>(m_BFIODevice);

    if (device->isOpen())
    {
        qDebug() << "usbdevice wake success";
        //探头断开不下发数据不更换探头,解冻不会下发块数据
        //强制发送休眠前当前探头位的探头块数据
        bool value = m_SonoParameters->isRealTime();
        if (value)
        {
            m_SonoParameters->setIsRealTime(false);
        }
        //        freeze(false);
        {
            qDebug() << "start send probe data";
            ControlTableSyncSender cs(m_ControlTable);
            m_ControlTable->send();

            setGrayMap(pV(BFPNames::GrayMapStr).toByteArray());

            setHighVoltage();

            resentCurProbeBlockData();

            if (pBV(BFPNames::FreqSpectrumStr))
            {
                ControlTableParameter* freqSpectrumP =
                    qobject_cast<ControlTableParameter*>(parameter(BFPNames::FreqSpectrumStr));
                if (freqSpectrumP != NULL)
                {
                    m_ControlTable->send(freqSpectrumP, 1 - freqSpectrumP->trueValue(), true, false);
                }
            }
            if (pBV(BFPNames::TriplexModeStr))
            {
                ControlTableParameter* triplexModeP =
                    qobject_cast<ControlTableParameter*>(parameter(BFPNames::TriplexModeStr));
                if (triplexModeP != NULL)
                {
                    m_ControlTable->send(triplexModeP, 1 - triplexModeP->trueValue(), true, false);
                }
            }
            if (pBV(BFPNames::FreeMModeStr))
            {
                parameter(BFPNames::FreeMBlockStr)->update();
            }
        }

        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        //        freeze(true);
        if (value)
        {
            m_SonoParameters->setIsRealTime(true);
        }
    }
}

void BeamFormerApple::initializeSpecialParameters()
{
    BeamFormerSonoeye::initializeSpecialParameters();
    initializeIImageParameters();
    setPV(BFPNames::SmoothSettingIdsStr, ParaListSettings::defaultVariantIds(pMax(BFPNames::SmoothStr) + 1));
    setPV(BFPNames::EnableFreezeAfterProbeFoundStr, false);
}

void BeamFormerApple::initializeIImageParameters()
{
    m_LineiImageParameterHandle.initializeIImageParameters(m_SonoParameters);
}

void BeamFormerApple::initializeProbeDection()
{
    if (!Setting::instance().defaults().isIODeviceVirtual())
    {
        m_ProbesSwitchController.reset(new DirectProbesSwitchController(this));

        m_ProbesSwitchController->initializeProbeIdentifyInfo();
        m_ProbesSwitchController->setWorkUltrasoundDevice(bfIODevice());
    }
}

void BeamFormerApple::sendCurvedCpdTxBlockDataGroup()
{
    int txNum = pIV(BFPNames::TxFNoStr);
    int cpdIndex =
        pBV(BFPNames::CurvedExapandingStr) ? pIV(BFPNames::TrapezoidalCPDSteerStr) : pIV(BFPNames::CPDSteerStr);

    QList<int> indexList;
    indexList.append(cpdIndex * 16 + txNum);
    indexList.append(cpdIndex * 16 + 8 + txNum);

    BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name, QStringList() << "CurvedCpdTx");
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_BlockDataParaNameConverter->groupParaName("CurvedCpdTx"), indexList);
}

bool BeamFormerApple::isScpdEnabled() const
{
    return !((syncMode() != Sync_None && systemScanMode() != SystemScanModeAV) || curProbe().IsPhasedArray ||
             pBV(BFPNames::TrapezoidalModeStr) || pBV(BFPNames::ZoomOnStr) || pBV(BFPNames::CurvedExapandingStr));
}

void BeamFormerApple::updatePrfList()
{
    BeamFormerQBit::updatePrfList();
    m_CWDopPrfCalculator->updatePrfList();
}

void BeamFormerApple::updateCPDSteerStr()
{
    if (pBV(BFPNames::CurvedExapandingStr))
    {
        parameter(BFPNames::TrapezoidalCPDSteerStr)->update();
    }
    else
    {
        parameter(BFPNames::CPDSteerStr)->update();
    }
}

void BeamFormerApple::onGettingFreqIndexCWDText(QString& value)
{
    value = curProbe().FreqCWD;
}

void BeamFormerApple::onGettingiImageModelNoShowValue(QVariant& value)
{
    m_LineiImageParameterHandle.onGettingiImageModelNoShowValue(m_SonoParameters, value);
}

void BeamFormerApple::onBeforeLineiImageSettingIdsChanged(const QVariant& oldValue, QVariant& newValue)
{
    m_LineiImageParameterHandle.onBeforeLineiImageSettingIdsChanged(m_SonoParameters, oldValue, newValue);
}

void BeamFormerApple::oniImageChanged(const QVariant& value)
{
    m_LineiImageParameterHandle.oniImageChanged(m_SonoParameters, value);
}

void BeamFormerApple::oniImageShowChanged(const QVariant& value)
{
    m_LineiImageParameterHandle.oniImageShowChanged(m_SonoParameters, value);
}

void BeamFormerApple::onGettingiImageShowText(QString& value)
{
    m_LineiImageParameterHandle.onGettingiImageShowText(m_SonoParameters, value);
}

void BeamFormerApple::onSmoothChanging(const QVariant& value)
{
    QList<int> ids = VariantUtil::variant2Ints(pV(BFPNames::SmoothSettingIdsStr));
    int smooth = value.toInt();
    if (smooth >= 0 && smooth < ids.count())
    {
        VariantHash& newPara = SingleSmoothSettings::instance().settings().para(ids.at(smooth));
        setPV(BFPNames::LowFilterAxialSigmaStr, newPara.value(BFPNames::LowFilterAxialSigmaStr));
        setPV(BFPNames::LowFilterLateralSigmaStr, newPara.value(BFPNames::LowFilterLateralSigmaStr));
    }
}

void BeamFormerApple::onVolumeChanged(const QVariant& value)
{
    m_PostProcessHandler->onVolumeChanged(value);
}

void BeamFormerApple::onVolumeTDIChanged(const QVariant& value)
{
    m_PostProcessHandler->onVolumeTDIChanged(value);
}

void BeamFormerApple::onVolumeCWDChanged(const QVariant& value)
{
    m_PostProcessHandler->onVolumeCWDChanged(value);
}

void BeamFormerApple::onGettingLineImageDebugText(QString& value)
{
    switch (pIV(BFPNames::LineImageDebugStr))
    {
    case 0:
        value = QString("Off");
        break;
    case ImageEventArgs::ImageB + 1:
        value = QString("B");
        break;
    case ImageEventArgs::ImageM + 1:
        value = QString("M");
        break;
    case ImageEventArgs::ImageD + 1:
        value = QString("PW");
        break;
    default:
        value = QString("None");
        break;
    }
}

void BeamFormerApple::onBeforeFreezeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    if (newValue.toBool())
    {
        // usb 读线程停止状态，无法写数据到usb，必须先写数据(freeze)到usb，再停止读线程
        //冻结时先以同步模式下发冻结指令
        this->m_ControlTable->setWriteMode(IControlTable::Syn);
        //同步方式冻结之前，要清空写数据队列，否则可能，同步控制了冻结，但之后异步队队列中
        //会下发解冻参数，导致冻结失效
        m_BFIODevice->clearWriteQueue();
        m_RefreshTimer.stop();
    }
    else
    {
        resumeRead();
        emit resetAutoFreezeTime();

        if (m_SupportFreezeOutage)
        {
            bfIODevice()->open();
            ControlTableSyncSender cs(m_ControlTable);
            setPV(BFPNames::ConfigDoneStr, false);
        }
    }
}

void BeamFormerApple::onFreezeChanged(const QVariant& value)
{
    if (value.toBool())
    {
        // usb 读线程停止状态，无法写数据到usb，必须先写数据(freeze)到usb，再停止读线程
        this->m_ControlTable->setWriteMode(IControlTable::Asyn);
        suspendBFIODeviceRead();
        //读线程停止,恢复2B/4B的最后冻结帧的判定值为false
        ImageModeType mode = imageMode();
        if (mode == MODE_2B || mode == MODE_4B)
        {
            m_BFDataHandler->setNeedCheckFrame(false);
            m_BFDataHandler->setIsLastActiveBFrame(false);
        }
        m_PostParameterHelper.setIsPostAvailable(true);
        m_PostParameterHelper.setRestoreParameterValues(m_SonoParameters);

        if (m_SupportFreezeOutage && !Setting::instance().defaults().isIODeviceVirtual())
        {
            bfIODevice()->close();
        }
    }
    else
    {
        m_BFDataHandler->startScanProbe();
        m_RefreshTimer.start();
        //会导致冻结解冻后zoomcoef变为100
        //        controlImageZoomCoef();
        //        //pw下回调图片然后按freeze，首先触发的是systemscanmodechange，然后才是freezechange，
        //        //这样会有一个问题systemscanmodechange中设置的imgfrz值无法下发下去，所以在这里update一下
        //        parameter(BFPNames::ImgFrzStr)->update();
        imageShapeIntoUnstable();
        m_PostParameterHelper.restoreParameterValue(m_SonoParameters);
        //此处解冻处理中未做冻结断电的判断，在未开启冻结断电情况下有冗余，原因是选择预设值的处理放在此处
    }
    controlXContrastValue();
}

void BeamFormerApple::onScanWidthChanged(const QVariant& value)
{
    BeamFormerSonoeye::onScanWidthChanged(value);
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (probeDataInfo.IsLinear && pBV(BFPNames::TrapezoidalModeStr))
    {
        QVariant v = pV(BFPNames::TrapezoidalModeStr);
        onBeforeTrapezoidalModeChanged(QVariant(), v);
    }
    else if (!probeDataInfo.IsLinear && !probeDataInfo.IsPhasedArray && pBV(BFPNames::CurvedExapandingStr))
    {
        QVariant v = pV(BFPNames::CurvedExapandingStr);
        onBeforeCurvedExapandingChanged(QVariant(), v);
    }
    else if (pIV(BFPNames::ScpdStr) != 0)
    {
        QVariant v = pV(BFPNames::ScpdStr);
        onBeforeScpdChanged(QVariant(), v);
    }
}

void BeamFormerApple::onBeforeCurvedExapandingChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (!probeDataInfo.IsLinear && !probeDataInfo.IsPhasedArray && newValue.toBool())
    {
        //在探头最小CQYZ下求得安全扫描宽度并根据此做Scan Width的限制，所谓安全即保证复合后在图像区无数据空白
        updateScanWidthOfConvexExtend();
        parameter(BFPNames::ScanWidthStr)->setEnabled(false);
    }
    else if (!probeDataInfo.IsLinear && !probeDataInfo.IsPhasedArray && !newValue.toBool())
    {
        parameter(BFPNames::ScanWidthStr)->setEnabled(true);
    }
    updateMaxCQYZ(pBV(BFPNames::TrapezoidalModeStr), pBV(BFPNames::ScpdOnStr), pIV(BFPNames::ScpdStr),
                  newValue.toBool());
}

void BeamFormerApple::onBeforeScpdChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    updateMaxCQYZ(pBV(BFPNames::TrapezoidalModeStr), pBV(BFPNames::ScpdOnStr), newValue.toInt(),
                  pBV(BFPNames::CurvedExapandingStr));
}

void BeamFormerApple::onBeforeScpdOnChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    updateMaxCQYZ(pBV(BFPNames::TrapezoidalModeStr), newValue.toBool(), pIV(BFPNames::ScpdStr),
                  pBV(BFPNames::CurvedExapandingStr));
}

void BeamFormerApple::onBeforeTrapezoidalModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    updateMaxCQYZ(newValue.toBool(), pBV(BFPNames::ScpdOnStr), pIV(BFPNames::ScpdStr),
                  pBV(BFPNames::CurvedExapandingStr));
}

void BeamFormerApple::updateMaxCQYZ(bool trapezodialOn, bool scpdOn, int scpdValue, bool curvedExapanding)
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (probeDataInfo.IsLinear)
    {
        int angle = -1;
        if (trapezodialOn)
        {
            angle = pIV(BFPNames::TrapezoidalCPDSteerStr);
        }
        else if (scpdValue > 0 || scpdOn)
        {
            angle = pIV(BFPNames::CPDSteerStr);
        }
        if (angle != -1)
        {
            updateLinearCQYZ(angle);
            return;
        }
    }
    else if (!probeDataInfo.IsPhasedArray && curvedExapanding)
    {
        updateConvexCQYZ(pIV(BFPNames::TrapezoidalCPDSteerStr));
        return;
    }
    if (m_CurMaxCQYZ != probeDataInfo.MaxCQYZ)
    {
        m_CurMaxCQYZ = probeDataInfo.MaxCQYZ;
        updateCQYZ();
    }
}

void BeamFormerApple::updateTrapCureveBSteer()
{
    //直接进行梯形和凸阵拓展更新，在调整bsteer后冻结解冻后图像出现问题
    //开启梯形成像时，目前冻结解冻会执行此函数，故此 update TrapezoidalModeStr
    if (pBV(BFPNames::TrapezoidalModeStr))
    {
        parameter(BFPNames::TrapezoidalModeStr)->update();
    }
    //凸阵拓展成像做类似处理
    else if (pBV(BFPNames::CurvedExapandingStr))
    {
        parameter(BFPNames::CurvedExapandingStr)->update();
    }
    else
    {
        parameter(BFPNames::BSteeringScanStr)->update();
    }
    // SpuerNeedle模式下冻结解冻需要重新下发块数据
    parameter(BFPNames::NeedleAngleIndexStr)->update();
    // 解冻后重新下发任意波形
    parameter(BFPNames::ArbitraryWaveStr)->update();
}

void BeamFormerApple::onTrapezoidalModeChanging(const QVariant& value)
{
    ControlTableSender ctSender(m_ControlTable);
    FreqRelatedParasSender fpSender(m_RelatedParasController);

    if (TrapezoidalModeChangingCnt++ == 0)
    {
        ControlTableForcibleSender ctForceSender(m_ControlTable);
        m_SonoParameters->setPV(BFPNames::ConfigDoneStr, false);
    }

    if (value.toBool())
    {
        if (isTrapezoidalModeEnabled())
        {
            controlScpdOn();
            controlFcpdOn();

            setPV(BFPNames::ScpdTrapeStr, 7);

            controlFocusNumB();
        }
    }
    else
    {
        setPV(BFPNames::ScpdTrapeStr, 0);

        controlScpdOn();
        controlFcpdOn();
    }

    controlBSteer();

    parameter(BFPNames::TrapezoidalCPDSteerStr)->update();
    int steer = (value.toBool() ? pIV(BFPNames::TrapezoidalCPDSteerStr) : pIV(BFPNames::CPDSteerStr));
    if (value.toBool())
    {
        int steer2 = pIV(BFPNames::TrapezoidalCPDSteer2Str);
        QList<int> indexes;
        indexes.append(steer);
        indexes.append(steer2);
        sendCpdSteerCTValue(steer, steer2);
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr), indexes);
    }
    else
    {
        sendCpdSteerCTValue(steer);
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr), steer);
    }

    m_StateManager->setIsZoomEnabled(isZoomEnabled());
}

void BeamFormerApple::onTrapezoidalModeChanged(const QVariant& value, bool changed)
{
    TrapezoidalModeChangingCnt--;

    if (changed && (TrapezoidalModeChangingCnt == 0))
    {
        m_SonoParameters->setPV(BFPNames::ConfigDoneStr, true);
    }
}

void BeamFormerApple::onBeforeScpdTrapeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    if (newValue.toInt() > 0)
    {
        parameter(BFPNames::ScpdStr)->setEnabled(false);
    }
    else
    {
        parameter(BFPNames::ScpdStr)->setEnabled(true);
    }
}

void BeamFormerApple::onScpdTrapeChanging(const QVariant& value)
{
    if (isTrapezoidalModeEnabled())
    {
        setPV(BFPNames::TrapezoidalModeStr, value.toInt() == 7);
    }

    if (!isScpdEnabled())
    {
        setPV(BFPNames::ScpdOnStr, false);
    }
}

void BeamFormerApple::updateConvexCQYZ(const int angle)
{
    double alpha = angle * (M_PI / 180.0f);
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    int lines =
        ProbeParameters::lines(pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr), pBV(BFPNames::HighDensityStr));
    double beta = lines * pDV(BFPNames::AngleSpacingRadStr) / 2.0f / (pBV(BFPNames::HighDensityStr) ? 2.0 : 1.0);
    double cos_beta = cos(beta);
    double ac = probeDataInfo.WaferRadius;
    double ac_pow2 = ac * ac;
    double ac_cos_beta = ac * cos_beta;
    double ac_cos_beta_2 = 2 * ac * cos_beta;

    BFDepthParameters bfDepthParameters = *m_DepthParameters;
    int maxCQYZ = probeDataInfo.MaxCQYZ;
    int cqyz = probeDataInfo.MinCQYZ;
    int step = 0;
    while (cqyz <= maxCQYZ)
    {
        bfDepthParameters.setCQYZ(cqyz);
        double ab = ac_cos_beta + bfDepthParameters.depthMM();
        double ab_pow2 = ab * ab;
        double bc_pow2 = ac_pow2 + ab_pow2 - ab * ac_cos_beta_2;
        double bc = sqrt(bc_pow2);
        double gamma = acos((ab_pow2 + bc_pow2 - ac_pow2) / (2 * ab * bc));
        qDebug() << PRETTY_FUNCTION << "alpha:" << alpha << "beta:" << beta << "gamma:" << gamma;
        if (alpha > beta + gamma)
        {
            break;
        }
        step = m_CQYZStepConfig->getStep(probeDataInfo.Name, cqyz, true);
        cqyz += step;
    }
    m_CurMaxCQYZ = cqyz - step;
    if (m_CurMaxCQYZ < pIV(BFPNames::CQYZStr))
    {
        updateCQYZ(m_CurMaxCQYZ);
    }
}

void BeamFormerApple::updateLinearCQYZ(const int angle)
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    int lines =
        ProbeParameters::lines(pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr), pBV(BFPNames::HighDensityStr));
    double halfMaxWidth = lines * pDV(BFPNames::LineSpacingMMStr) / 2.0f / (pBV(BFPNames::HighDensityStr) ? 2.0 : 1.0);
    double radians = angle * (M_PI / 180.0f);
    double maxDepth = halfMaxWidth / qTan(radians);
    BFDepthParameters bfDepthParameters = *m_DepthParameters;
    int maxCQYZ = probeDataInfo.MaxCQYZ;
    int cqyz = probeDataInfo.MinCQYZ;
    qDebug() << PRETTY_FUNCTION << "lines:" << lines << "halfMaxWidth:" << halfMaxWidth << "angle:" << angle
             << "radians:" << radians << "bfDepthParameters.depthMM():" << bfDepthParameters.depthMM()
             << "bfDepthParameters.getRealCQYZ:" << bfDepthParameters.getRealCQYZ() << "maxDepth:" << maxDepth;

    bfDepthParameters.setCQYZ(cqyz);
    int step = 0;
    while ((bfDepthParameters.depthMM() < maxDepth) && (cqyz <= maxCQYZ))
    {
        step = m_CQYZStepConfig->getStep(probeDataInfo.Name, cqyz, true);
        cqyz += step;
        bfDepthParameters.setCQYZ(cqyz);
        qDebug() << PRETTY_FUNCTION << "maxCQYZ:" << maxCQYZ << "cqyz:" << cqyz << "step:" << step
                 << "maxDepth:" << maxDepth << "depthMM:" << bfDepthParameters.depthMM();
    }
    m_CurMaxCQYZ = cqyz - step;
    if (m_CurMaxCQYZ < pIV(BFPNames::CQYZStr))
    {
        updateCQYZ(m_CurMaxCQYZ);
    }
}

bool BeamFormerApple::isCurvedExapandingEnable()
{
    bool isEnable = true;

    if (pBV(BFPNames::ZoomOnStr) || pBV(BFPNames::ZoomSelectStr))
    {
        isEnable = false;
    }
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (probeDataInfo.IsLinear || probeDataInfo.IsPhasedArray)
    {
        isEnable = false;
    }
    SyncModeType syncMode = (SyncModeType)pIV(BFPNames::SyncModeStr);
    if (syncMode == SyncModeType::Sync_CD || syncMode == SyncModeType::Sync_D)
    {
        isEnable = false;
    }

    if (isSonoNeedleOn())
    {
        isEnable = false;
    }

    return isEnable;
}

void BeamFormerApple::onLeftChanging(const QVariant& value)
{
    BeamFormerBase::onLeftChanging(value);
}

void BeamFormerApple::onCPDSteerChanging(const QVariant& value)
{
    if (curProbe().IsLinear)
    {
        if (!isBSteerOn() && !isTrapezoidalModeOn())
        {
            sendCpdSteerCTValue(value.toInt());
            m_BlockDataSender->sendParaBlockDataGroup(
                curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr), value.toInt());
        }
    }
    else
    {
        sendCpdSteerCTValue(value.toInt());
        BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name,
                                         QStringList() << BFPNames::CQYZStr << "CurvedCpd");
        sendCQYZBlockDataGroup();
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName("CurvedCpd"), value.toInt());
        sendCurvedCpdTxBlockDataGroup();
    }
    if (isFixedDepth())
    {
        controlCQYZ(pIV(BFPNames::CQYZLevelStr));
    }
}

void BeamFormerApple::onBeforeZoomOnChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    if (newValue.toBool())
    {
        // 2023-06-06 Modify by AlexWang
        // 解决进入局部放大状态后B模式左侧菜单Zoom开关状态不正确，进入局部放大状态后不要把ZoomSelectStr参数设置成false
        // setPV(BFPNames::ZoomSelectStr, false);

        if (m_FocusNumBBak == -1)
        {
            m_FocusNumBBak = pIV(BFPNames::FocusNumBStr);
        }
        setPV(BFPNames::FocusNumBStr, 0);

        if (m_ScpdBak == -1)
        {
            m_ScpdBak = pIV(BFPNames::ScpdStr);
        }
        setPV(BFPNames::ScpdStr, 0);

        if (m_CurvedExapandingBak == -1)
        {
            m_CurvedExapandingBak = pIV(BFPNames::CurvedExapandingStr);
        }
        setPV(BFPNames::CurvedExapandingStr, 0);
    }
    else
    {
        if (m_FocusNumBBak != -1)
        {
            setPV(BFPNames::FocusNumBStr, m_FocusNumBBak);
            m_FocusNumBBak = -1;
        }

        if (m_CurvedExapandingBak != -1)
        {
            setPV(BFPNames::CurvedExapandingStr, m_CurvedExapandingBak);
            m_CurvedExapandingBak = -1;
        }
    }

    parameter(BFPNames::FocusNumShowStr)->setEnabled(!newValue.toBool());
    parameter(BFPNames::CurvedExapandingStr)->setEnabled(isCurvedExapandingEnable());
}

void BeamFormerApple::onZoomOnChanging(const QVariant& value)
{
    BeamFormerBase::onZoomOnChanging(value);
    parameter(BFPNames::FocusNumShowStr)->setEnabled(!value.toBool());
    parameter(BFPNames::CurvedExapandingStr)->setEnabled(isCurvedExapandingEnable());

    // 2023-09-18 Write by AlexWang [bug:66962] 接入线阵时，当进入局部放大状态时，禁用BSteeringScan按钮
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (probeDataInfo.IsLinear)
        parameter(BFPNames::BSteeringScanStr)->setEnabled(!value.toBool());
}

void BeamFormerApple::onZoomOnChanged(const QVariant& value)
{
    // 2024-11-28 Write by AlexWang [BUG:77751] 必须在退出放大状态且参数计算完成下发后再恢复Compound
    if (!value.toBool())
    {
        if (m_ScpdBak != -1)
        {
            setPV(BFPNames::ScpdStr, m_ScpdBak);
            m_ScpdBak = -1;
        }
    }

    BeamFormerBase::onZoomOnChanged(value);
}

void BeamFormerApple::onZoomSelectChanging(const QVariant& value)
{
    BeamFormerBase::onZoomSelectChanging(value);
    parameter(BFPNames::CurvedExapandingStr)->setEnabled(isCurvedExapandingEnable());

    // 2023-09-18 Write by AlexWang [bug:66962] 接入线阵时，当进入局部放大预备状态时，禁用BSteeringScan按钮
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (probeDataInfo.IsLinear)
        parameter(BFPNames::BSteeringScanStr)->setEnabled(!value.toBool());
}

void BeamFormerApple::onIsRoiVisibleChanging(const QVariant& value)
{
    if (value.toBool())
    {
        setPV(BFPNames::BCImagesOnStr, false);
        parameter(BFPNames::BCImagesOnStr)->setEnabled(false);
    }
    else
    {
        parameter(BFPNames::BCImagesOnStr)->setEnabled(true);
    }
}

void BeamFormerApple::onCurvedExapandingChanged(const QVariant& value)
{
    if (value.toBool())
    {
        if (m_CurvedScpdBak == -1)
        {
            m_CurvedScpdBak = pIV(BFPNames::ScpdStr);
        }
        setPV(BFPNames::ScpdStr, 2);
    }
    else
    {
        if (m_CurvedScpdBak != -1)
        {
            setPV(BFPNames::ScpdStr, m_CurvedScpdBak);
            m_CurvedScpdBak = -1;
        }
    }
    controlScpdOn();
    if (value.toBool())
    {
        parameter(BFPNames::TrapezoidalCPDSteerStr)->update();
    }
    else
    {
        parameter(BFPNames::CPDSteerStr)->update();
    }
}

void BeamFormerApple::onTrapezoidalCPDSteerChanging(const QVariant& value)
{
    if (isTrapezoidalModeOn())
    {
        int value1 = value.toInt();
        // 由于第二个角度和第一个角度差值过大会出现图像下方黑边问题，和zeus沟通后决定参考lotos将第二个角度固定为第一个角度+3的档位
        setPV(BFPNames::TrapezoidalCPDSteer2Str, value1 + 3);
        int value2 = pIV(BFPNames::TrapezoidalCPDSteer2Str);
        sendCpdSteerCTValue(value1, value2);
        QList<int> indexes;
        indexes.append(value1);
        indexes.append(value2);
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr), indexes);
    }
    else
    {
        sendCpdSteerCTValue(value.toInt());
        BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name,
                                         QStringList() << BFPNames::CQYZStr << "CurvedCpd");
        sendCQYZBlockDataGroup();
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName("CurvedCpd"), value.toInt());
        sendCurvedCpdTxBlockDataGroup();
    }
}

// void BeamFormerApple::onTrapezoidalCPDSteer2Changing(const QVariant &value)
//{
//    if(isTrapezoidalModeOn())
//    {
//            int value1 = pIV(BFPNames::TrapezoidalCPDSteerStr);
//            int value2 = value.toInt();
//            sendCpdSteerCTValue(value1, value2);
//            QList<int> indexes;
//            indexes.append(value1);
//            indexes.append(value2);
//            m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
//                                                      m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr),
//                                                      indexes);
//    }
//}

void BeamFormerApple::onProbeIdChanged(const QVariant& value)
{
    //解决Bug：用V7L打图，图像显示是反的
    //原因分析：V7L最开始是Android，Android控制是反的，所以这把探头的主机pcb是反着做的，因此，我们apple给FPGA的控制字（ProbeInvert）应该也要是反的
    //解决方案：新增参数IsReverseControl（反向控制），当探头为V7L时，probe.ini中IsReverseControl设置为true。当读取到探头为反向控制时，将控制信号(BFPNames::ProbeInvertStr)取反。
    bool invert = true;
    if (curProbe().IsReverseControl)
    {
        invert = false;
    }
    m_SonoParameters->setPV(BFPNames::ProbeInvertStr, invert);

    parameter(BFPNames::CurvedExapandingStr)->setEnabled(isCurvedExapandingEnable());
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(value.toInt());
    m_CurMaxCQYZ = probeDataInfo.MaxCQYZ;
}

void BeamFormerApple::onGettingTGC9ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(8);
}

void BeamFormerApple::onGettingTGC10ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(9);
}

void BeamFormerApple::onGettingTGC11ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(10);
}

void BeamFormerApple::onGettingTGC12ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(11);
}

void BeamFormerApple::onGettingCQYZMax(int& value)
{
    value = m_CurMaxCQYZ;
}

void BeamFormerApple::onGettingWallFilterDopText(QString& value)
{
    Q_UNUSED(value);
}

void BeamFormerApple::setPreset(const PresetParameters& presets)
{
    m_CurvedExapandingBak = false;
    m_THIReset = false;
    m_MBBak = -1;
    m_CurMaxCQYZ = curProbe().MaxCQYZ;
    BeamFormerBase::setPreset(presets);
}

void BeamFormerApple::setPresetFinished()
{
    updateTrapCureveBSteer();
}

void BeamFormerApple::setCurProbeFinishedOnSystemBooted()
{
    QTimer::singleShot(600, this, SLOT(enableConfigDone()));
}

void BeamFormerApple::sendCpdSteerCTValue(int value)
{
    ControlTableSender sender(m_ControlTable);
    double steerCos = 1 / qCos(Formula::degree2Rad(value));
    double steerTan = qTan(Formula::degree2Rad(value));
    setPV(BFPNames::BCosAngleStr, Util::floatToBinary(steerCos));
    setPV(BFPNames::BTgAngleStr, Util::floatToBinary(steerTan));
    if (!pBV(BFPNames::
                 PA_VERT_DIST_EnableStr)) //如果不加判断，当虚拟顶点打开的时候，再去调节二维独立偏转会覆盖之前设置的值
    {
        setPV(BFPNames::BSteeringAngleCodingStr, qAbs(value));
    }
}

void BeamFormerApple::sendCpdSteerCTValue(int value1, int value2)
{
    ControlTableSender sender(m_ControlTable);
    double steerCos1 = 1 / qCos(Formula::degree2Rad(value1));
    double steerTan1 = qTan(Formula::degree2Rad(value1));
    setPV(BFPNames::BCosAngleStr, Util::floatToBinary(steerCos1));
    setPV(BFPNames::BTgAngleStr, Util::floatToBinary(steerTan1));
    setPV(BFPNames::BSteeringAngleCodingStr, qAbs(value1));
    setPV(BFPNames::BSteeringAngle2CodingStr, qAbs(value2));
}

void BeamFormerApple::onBeforeSyncModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    Q_UNUSED(newValue);
#if 0

    SyncModeType syncMode = (SyncModeType)newValue.toInt();

    if(syncMode == SyncModeType::Sync_CD ||
       syncMode == SyncModeType::Sync_D  ||
       syncMode ==SyncModeType::Sync_CM  ||
       syncMode ==SyncModeType::Sync_M)
    {
        //设置Rotation为0
        if (m_RotationBak == -1)
        {
            m_RotationBak = pIV(BFPNames::RotationStr);
        }
        setPV(BFPNames::RotationStr, 0);
    }
    else
    {
        if(m_RotationBak != -1)
        {
           setPV(BFPNames::RotationStr,m_RotationBak);
           m_RotationBak = -1;
        }
    }
#endif
}

void BeamFormerApple::onSyncModeChanging(const QVariant& value)
{
    BeamFormerBase::onSyncModeChanging(value);
    // 切换模式只设置凸阵拓展成像的使能控制，不改变参数本身的值
    parameter(BFPNames::CurvedExapandingStr)->setEnabled(isCurvedExapandingEnable());

    if (syncMode() == Sync_None)
    {
        parameter(BFPNames::HFStr)->update();
        parameter(BFPNames::HF_AlphaStr)->update();
    }
    else if ((syncMode() & Sync_C) == Sync_C)
    {
        parameter(BFPNames::HFIncStr)->update();
        parameter(BFPNames::HF_Alpha_IncStr)->update();
    }
    //在彩色模式下调节B的多波束
    controlMB();
    controlXContrastValue();
}

void BeamFormerApple::controlRotationEn()
{
    //    SyncModeType mode = syncMode();
    //    bool bEnable = false;
    //    if (Sync_C == mode || Sync_None == mode)
    //    {
    //        bEnable = true;
    //    }
    //    parameter(BFPNames::RotationStr)->setEnabled(bEnable);
}

void BeamFormerApple::controlBiopsyEn()
{
}

void BeamFormerApple::controlMB()
{
    if ((syncMode() & Sync_C) == Sync_C)
    {
        if (m_MBBak == -1)
        {
            m_MBBak = m_SonoParameters->pIV(BFPNames::MBStr);
            m_SonoParameters->parameter(BFPNames::MBIncStr)->update();
        }
    }
    else
    {
        if (m_MBBak != -1)
        {
            m_SonoParameters->setPV(BFPNames::MBStr, m_MBBak);
            m_MBBak = -1;
        }
    }
}

void BeamFormerApple::onGettingAcousticPowerTestCodeText(QString& value)
{
    int aptc = pIV(BFPNames::AcousticPowerTestCodeStr);

    QString showValue = "";
    switch (aptc)
    {
    case 0:
        showValue = "B";
        break;
    case 1:
        showValue = "M";
        break;
    case 2:
        showValue = "C";
        break;
    case 3:
        showValue = "D";
        break;
    case 4:
        showValue = "B THI";
        break;
    case 5:
        showValue = "M THI";
        break;
    default:
        showValue = "B";
        break;
    }
    value = showValue;
}

void BeamFormerApple::onGettingSRINoiseFilterTypeText(QString& value)
{
    switch (pIV(BFPNames::SRINoiseFilterTypeStr))
    {
    case 0:
        value = QString("Isotropic");
        break;
    default:
        value = QString("None");
        break;
    }
}

void BeamFormerApple::onGettingSRIEdgeFilterTypeText(QString& value)
{
    switch (pIV(BFPNames::SRIEdgeFilterTypeStr))
    {
    case 0:
        value = QString("Asymetric");
        break;
    case 1:
        value = QString("Symetric");
        break;
    default:
        value = QString("None");
        break;
    }
}

void BeamFormerApple::onGettingMVelocityControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pMax(BFPNames::MVelocityStr) - value.toInt();
}

void BeamFormerApple::onTHIChanging(const QVariant& value)
{
    imageIntoUnstable();

    THIChangingHandle(value);
}

void BeamFormerApple::onCQYZChanging(const QVariant& value)
{
    //由于代码中BM/PW模式还存在使用 HalfHeight参数的情况，以及退出BM/PW模式存在过渡图像
    //目前未能做到只发一次相关参数变更的处理，因此在CQYZChanging中增加ConfigDone的控制逻辑
    //代替bug76870，77070的修改方案，之前的方案为了保证响应速度，但使用下来不能保证无过渡图像，因此去掉此方案
    bool currentConfigDone = pIV(BFPNames::ConfigDoneStr);
    if (currentConfigDone)
    {
        //当进入CQYZChanging前，configdone已经是false时，不需要做开关控制，比如：冻结/解冻
        //只有在进入前为true时，才需要做开关控制，比如：BM模式切换到B模式
        setPV(BFPNames::ConfigDoneStr, false);
    }

    m_DepthParameters->setCQYZ(value.toInt());
    m_DepthParameters->setZoomedCQYZ(pIV(BFPNames::ZoomedCQYZStr));
    m_ImageRenderLayoutRects->updateRenderImageScale();

    qreal probeDSCImageZoomCof = 1.0;
    double imageHeight = pV(BFPNames::RenderImageSizeStr).toSize().height();
    m_DepthParameters->setPixelLen(BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) /
                                   pFV(BFPNames::FixedSWImageZoomCofStr) * probeDSCImageZoomCof);
    m_DepthParameters->setImageHeight(imageHeight);
    setPV(BFPNames::ProbeDSCImageZoomCofStr, probeDSCImageZoomCof);

    double zoomCoef = pIV(BFPNames::ImageZoomCoefStr) / 100.0;
    m_DepthParameters->setImageZoomCoef(zoomCoef);
    double pixelSizeMM = m_DepthParameters->getPixelizeMMWithFactor() / pDV(BFPNames::RenderImageZoomCofStr);
    // 2025-05-12 Modify by AlexWang [BUG:78687]计算实际的深度
    double depthMM = getRealDepthMM();
    double depthMMShow = depthMM / pDV(BFPNames::RenderImageZoomCofStr);
    setPV(BFPNames::RealDepthMMStr, depthMM);
    setPV(BFPNames::DepthMMStr, depthMMShow, pBV(BFPNames::ZoomOnStr));
    if (!curProbe().IsLinear && isFixedDepth())
    {
        pixelSizeMM = depthMMShow / imageHeight / zoomCoef / m_DepthParameters->heightFactor();
    }
    qDebug() << PRETTY_FUNCTION << "pixelSizeMM:" << pixelSizeMM;
    setPV(BFPNames::PixelSizeMMStr, pixelSizeMM);
    setPV(BFPNames::StartDepthMMStr, startDepthMM());

    // 4B下面下发的PixelsWithinFocus应该和单B一样
    double pixelSizeMMWithOutHeightFactor =
        m_DepthParameters->getPixelizeMMWithFactor(1.0) / pFV(BFPNames::RenderImageZoomCofStr);
    // TODO Lotus使用的pixelSizeMM没有考虑Halfheight,需要向FPGA确认
    setPV(BFPNames::PixelsWithinFocusStr, (curProbe().IsLinear ? 5.0 : 10.0) / pixelSizeMMWithOutHeightFactor);
    setPV(BFPNames::UnzoomedCQYZStr, value);

    // cqyz改变后更新AdjustmentOfB
    if (value.toInt() >= 0 && value.toInt() < curProbe().AdjustmentOfBs.count())
    {
        setPV(BFPNames::AdjustmentOfBStr, curProbe().AdjustmentOfBs[value.toInt()]);
    }

    // apple要求深度大于最大深度时，要关闭THI
    if (RealCompare::IsGreaterOrEqual(imageBottomDepthMM(), maxTHIDepthMM))
    {
        if (!m_THIReset && pBV(BFPNames::THIStr))
        {
            m_THIReset = true;
            setPV(BFPNames::THIStr, false);
        }
        m_SonoParameters->parameter(BFPNames::THIStateStr)->setEnabled(false);
    }
    else
    {
        if (m_THIReset)
        {
            setPV(BFPNames::THIStr, true);
            m_THIReset = false;
        }
        m_SonoParameters->parameter(BFPNames::THIStateStr)->setEnabled(true);
    }
    AutoFocusAdjuster::instance().autoFocus(m_SonoParameters);

    if (!zoomOn())
    {
        if (syncMode() == Sync_None || systemScanMode() == SystemScanModeAV)
        {
            adjustFocusPosByDepth(BFPNames::FocusNumBStr, BFPNames::FocusPosBStr);
        }
        else if (syncMode() == Sync_M)
        {
            adjustFocusPosByDepth(BFPNames::FocusNumMStr, BFPNames::FocusPosMStr);
        }
        //其他模式，都是根据ROI或者采样门自动调节focusPos
    }

    changeProbeGeometry();
    if (m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        sendCQYZBlockDataGroup();
        sendMDFData();
    }

    if (!m_SonoParameters->pBV(BFPNames::FreeMModeStr))
    {
        int format = imageMode() == MODE_UDBM ? pIV(BFPNames::MDisplayFormatStr) : -1;
        double mHeightZoomFacotr = RenderLayoutDesigner::imageRowScaleFacator(format, 1).yScale;
        // MPixelSizeMMStr计算不考虑panzoom放大系数
        m_SonoParameters->setPV(BFPNames::MPixelSizeMMStr,
                                m_DepthParameters->getPixelizeMMWithFactorAndNoZoomCoef(mHeightZoomFacotr) /
                                    pDV(BFPNames::RenderImageZoomCofStr));
    }

    if (currentConfigDone)
    {
        setPV(BFPNames::ConfigDoneStr, true);
    }
}

void BeamFormerApple::onCQYZChanged(const QVariant& value, bool bChange)
{
    if (bChange)
    {
        BeamFormerBase::onCQYZChanged(value, bChange);
    }
}

void BeamFormerApple::onDDisplayFormatChanged(const QVariant& value)
{
    Q_UNUSED(value);
    imageShapeIntoUnstable(30);
}

void BeamFormerApple::onDTDIDisplayFormatChanged(const QVariant& value)
{
    Q_UNUSED(value);
    imageShapeIntoUnstable(30);
}

void BeamFormerApple::handleSendingDataMsg()
{
    BeamFormerSonoeye::handleSendingDataMsg();

    emit resetAutoFreezeTime();
}

void BeamFormerApple::onGettingWallThresholdControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pMax(BFPNames::WallThresholdStr) - value.toInt();
}

void BeamFormerApple::onGettingWallThresholdPDControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pMax(BFPNames::WallThresholdPDStr) - value.toInt();
}

void BeamFormerApple::onGettingWallThresholdTDIControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pMax(BFPNames::WallThresholdTDIStr) - value.toInt();
}

void BeamFormerApple::onGettingDScanLineControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = value.toInt() + pIV(BFPNames::PWScanLineDeltaStr);
}

void BeamFormerApple::onGettingCQYZControlTableValue(const QVariant& value, int& controlTableValue)
{
    //此时sonoparameters中的cqyz还没变，不能使用 m_DepthParameters，只能使用临时变量
    //    BFDepthParameters depthPara(value.toInt(), pBV(BFPNames::HalfHeightStr),
    //                                zoomOn(),
    //                                BFDepthParameters::zoomMulti(
    //                                pIV(BFPNames::ZoomMultiIndexStr),
    //                                parameter(BFPNames::ZoomMultiIndexStr)->max()));

    BFDepthParameters depthPara(pIV(BFPNames::ADFreqMHzStr), imageHeight(), value.toInt(), pBV(BFPNames::HalfHeightStr),
                                zoomOn(), pIV(BFPNames::ZoomedCQYZStr));
    depthPara.setPixelLen(BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) /
                          pFV(BFPNames::FixedSWImageZoomCofStr));

    if (imageMode() == MODE_4B)
    {
        controlTableValue = depthPara.getRealCQYZ(false);
    }
    else
    {
        controlTableValue = depthPara.getRealCQYZ();
    }
}

void BeamFormerApple::onFreqSpectrumChanged(const QVariant& value)
{
    BeamFormerBase::onFreqSpectrumChanged(value);
    parameter(BFPNames::HighDensityStr)->setEnabled(!value.toBool());
    if (!value.toBool())
    {
        m_SonoParameters->setPV(BFPNames::SteeringAngleStr, m_SonoParameters->pV(BFPNames::DopSteeringAngleStr));
        parameter(BFPNames::SteeringAngleStr)->setEnabled(curProbe().IsLinear);
        parameter(BFPNames::DopSteeringAngleStr)->setEnabled(curProbe().IsLinear);
    }
    else
    {
        parameter(BFPNames::SteeringAngleStr)->setEnabled(false);
    }
    controlXContrastValue();
}

void BeamFormerApple::onTDIEnChanging(const QVariant& value)
{
    setPV(BFPNames::TDIBMenuShowStr, value.toBool());
    BeamFormerBase::onTDIEnChanging(value);
}

void BeamFormerApple::onSteeringAngleChanged(const QVariant& value, bool changed)
{
    if (changed)
    {
        m_SonoParameters->setPV(BFPNames::DopSteeringAngleStr, value);
    }
}

void BeamFormerApple::onBeforeDopSteeringAngleChanged(const QVariant& oldValue, QVariant& newValue)
{
    onBeforeSteeringAngleChanged(oldValue, newValue);
}

void BeamFormerApple::onDopSteeringAngleChanged(const QVariant& value, bool changed)
{
    if (!m_SonoParameters->pBV(BFPNames::FreqSpectrumStr) && changed)
    {
        setPV(BFPNames::SteeringAngleStr, value, true);
    }
}

void BeamFormerApple::onGettingDopSteeringAngleShowValue(QVariant& value)
{
    int iv = pIV(BFPNames::DopSteeringAngleStr);
    QList<int> settings = VariantUtil::variant2Ints(pV(BFPNames::ROISteerAnglesStr), VariantUtil::String);
    if (settings.count() != m_StaticParameters->steeringAngles().count() / 2)
    {
        if (iv >= 0 && iv < m_StaticParameters->steeringAngles().count())
        {
            value = m_StaticParameters->steeringAngles().at(iv);
        }
    }
    else
    {
        QList<int> angles = CalculatorUtil::toMirrorList(settings);
        if (iv >= 0 && iv < angles.count())
        {
            value = angles.at(iv);
        }
    }
}

void BeamFormerApple::onGettingDopSteeringAngleMin(int& value)
{
    onGettingSteeringAngleMin(value);
}

void BeamFormerApple::onGettingDopSteeringAngleMax(int& value)
{
    onGettingSteeringAngleMax(value);
}

void BeamFormerApple::onGettingDopSteeringAngleControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingSteeringAngleControlTableValue(value, controlTableValue);
}

void BeamFormerApple::onGettingPhasedProbeTgc0ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    getLGCControlTableValue(controlTableValue, BFPNames::LGC0Str);
}

void BeamFormerApple::onGettingPhasedProbeTgc1ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    getLGCControlTableValue(controlTableValue, BFPNames::LGC1Str);
}

void BeamFormerApple::onGettingPhasedProbeTgc2ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    getLGCControlTableValue(controlTableValue, BFPNames::LGC2Str);
}

void BeamFormerApple::onGettingPhasedProbeTgc3ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    getLGCControlTableValue(controlTableValue, BFPNames::LGC3Str);
}

void BeamFormerApple::onGettingPhasedProbeTgc4ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    getLGCControlTableValue(controlTableValue, BFPNames::LGC4Str);
}

void BeamFormerApple::onGettingPhasedProbeTgc5ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    getLGCControlTableValue(controlTableValue, BFPNames::LGC5Str);
}

void BeamFormerApple::onGettingPhasedProbeTgc6ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    getLGCControlTableValue(controlTableValue, BFPNames::LGC6Str);
}

void BeamFormerApple::onGettingPhasedProbeTgc7ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    getLGCControlTableValue(controlTableValue, BFPNames::LGC7Str);
}

void BeamFormerApple::getLGCControlTableValue(int& ctValue, const QString para)
{
    int index = para.right(1).toInt();
    ctValue += gettingLGCValue(index);
    if (ctValue > m_SonoParameters->pMax(BFPNames::PhasedProbeTgcStrs[index]))
    {
        ctValue = m_SonoParameters->pMax(BFPNames::PhasedProbeTgcStrs[index]);
    }
    else if (ctValue < m_SonoParameters->pMin(BFPNames::PhasedProbeTgcStrs[index]))
    {
        ctValue = m_SonoParameters->pMin(BFPNames::PhasedProbeTgcStrs[index]);
    }
}

void BeamFormerApple::sendFilterCpd()
{
    //由于FilterCpdStr参数变为频率参数来控制
    m_RelatedParasController->sendFilterCpdPara();
}

void BeamFormerApple::sendNormalBlockDataGroup(const QString& probeName, const QStringList& sendDataNames)
{
    //因为双焦点存在焦点拼接，而拼接时不采用保存到文件中，保存文件会覆盖单焦点的内容，
    //因此单焦点时下发焦点对应的数据块文件内容，双焦点时需要过滤掉焦点对应的数据块文件，采用下发计算内容
    QStringList normalExcludeSendDataName;
    if (pIV(BFPNames::FocusNumBStr) > 0)
    {
        normalExcludeSendDataName.append(
            ModelConfig::instance().value(ModelConfig::Paras::FocusesCombineDataBlock, "data120").toString());
    }
    m_BlockDataSender->sendNormalBlockDataGroup(probeName, sendDataNames, normalExcludeSendDataName);
}

void BeamFormerApple::controlTMTDICWDParas()
{
    ControlTableSender cs(m_ControlTable);

    if (pBV(BFPNames::CWEnStr))
    {
        // SonoAir的CW，在FPGA端采用的是PW的方案
        parameter(BFPNames::GainDopCWDStr)->update();
        parameter(BFPNames::PixelRatioCWDStr)->update();
        parameter(BFPNames::PWDynamicRangeStr)->update(true);
        parameter(BFPNames::AudioFilterCoefSelCWDStr)->update();
    }
    else if (pBV(BFPNames::TriplexModeStr))
    {
        parameter(BFPNames::GainDopTMStr)->update();
        parameter(BFPNames::PixelRatioTMStr)->update();
        parameter(BFPNames::PWDynamicRangeTMStr)->update(true);
        parameter(BFPNames::AudioFilterCoefSelTMStr)->update();
    }
    else if (pBV(BFPNames::TDIEnStr))
    {
        parameter(BFPNames::GainDopTDIStr)->update();
        parameter(BFPNames::PixelRatioTDIStr)->update();
        parameter(BFPNames::PWDynamicRangeTDIStr)->update(true);
        parameter(BFPNames::AudioFilterCoefSelTDIStr)->update();
    }
    else
    {
        parameter(BFPNames::GainDopStr)->update();
        parameter(BFPNames::PixelRatioStr)->update();
        parameter(BFPNames::PWDynamicRangeStr)->update(true);
        parameter(BFPNames::AudioFilterCoefSelStr)->update();
    }
}

void BeamFormerApple::onTriplexModeChanged(const QVariant& value)
{
    if (value.toBool())
    {
        int currentDopplerGate = pIV(BFPNames::SampleDepthDopStr); //当前的DopplerGate
        int dopplerGateDepth1 = pIV(BFPNames::GateDepth1Str);
        int dopplerGateDepth2 = pIV(BFPNames::GateDepth2Str);
        int dSampleRate1_Triplex = pIV(BFPNames::DTx1_TriplexStr);
        int dSampleRate2_Triplex = pIV(BFPNames::DTx2_TriplexStr);
        int dSampleRate3_Triplex = pIV(BFPNames::DTx3_TriplexStr);

        if (currentDopplerGate <= dopplerGateDepth1)
        {
            setPV(BFPNames::DSampleRateStr, dSampleRate1_Triplex);
        }
        else if (currentDopplerGate > dopplerGateDepth1 && currentDopplerGate <= dopplerGateDepth2)
        {
            setPV(BFPNames::DSampleRateStr, dSampleRate2_Triplex);
        }
        else
        {
            setPV(BFPNames::DSampleRateStr, dSampleRate3_Triplex);
        }

        // FPGA更新，要求进入三同步时强制关闭ThiMode，AV功能进入二同步时不强制改变
        if (systemScanMode() != SystemScanModeAV)
        {
            if (m_ThiModeBak == -1)
            {
                m_ThiModeBak = pIV(BFPNames::ThiModeStr);
            }
            setPV(BFPNames::ThiModeStr, 0);
        }
    }
    else
    {
        if (systemScanMode() != SystemScanModeAV)
        {
            if (m_ThiModeBak != -1)
            {
                setPV(BFPNames::ThiModeStr, m_ThiModeBak);
                m_ThiModeBak = -1;
            }
        }
    }
    BeamFormerBase::onTriplexModeChanged(value);
}

void BeamFormerApple::onBCDGeometryChanged()
{
    BeamFormerBase::onBCDGeometryChanged();
    onTriplexModeChanged(pBV(BFPNames::TriplexModeStr));
}

void BeamFormerApple::onIsBiopsyVisibleChanged(const QVariant& value)
{
    Q_UNUSED(value);
    controlXContrastValue();
}

void BeamFormerApple::onFcpdOnChanging(const QVariant& value)
{
    BeamFormerBase::onFcpdOnChanging(value);
    imageIntoUnstable();
}

void BeamFormerApple::onHighDensityChanged(const QVariant& value)
{
    Q_UNUSED(value)
    imageIntoUnstable();
}

void BeamFormerApple::onMBIncChanging(const QVariant& value)
{
    if ((syncMode() & Sync_C) == Sync_C)
    {
        if (m_MBBak == -1)
        {
            m_MBBak = m_SonoParameters->pIV(BFPNames::MBStr);
        }
        m_SonoParameters->setPV(BFPNames::MBStr, value);
    }
}

void BeamFormerApple::onMBIncChanged(const QVariant& value)
{
}

void BeamFormerApple::onGettingMBPreset(QVariant& value)
{
    if (m_MBBak != -1)
    {
        value = QVariant::fromValue(m_MBBak);
    }
}

#if defined(SYS_UNIX) || defined(SYS_ANDROID)
void BeamFormerApple::onECGGainChanging(const QVariant& value)
{
    m_EcgReceiver->setGain(ECG_LEAD2, (ECG_GAIN)(value.toInt()));
}

void BeamFormerApple::onECGVelocityChanging(const QVariant& value)
{
    m_EcgReceiver->setSpeed((ECG_SPEED)value.toInt());
}

void BeamFormerApple::onGettingECGVelocityShowValue(QVariant& value)
{
    // value:0-4 showValue:1-5
    value = value.toInt() + 1;
}

void BeamFormerApple::onGettingECGPosShowValue(QVariant& value)
{
    // value:0-6 showValue:1-7
    value = value.toInt() + 1;
}

void BeamFormerApple::onGettingBECGDelayTimeShowValue(QVariant& value)
{
    value = QString("(pixel:%1,speed:%2)").arg(value.toInt()).arg(m_SonoParameters->pIV(BFPNames::ECGVelocityStr) + 1);
}

#endif
