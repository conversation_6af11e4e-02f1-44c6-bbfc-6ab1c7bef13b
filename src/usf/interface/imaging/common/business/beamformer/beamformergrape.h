#ifndef BEAMFORMERGRAPE_H
#define BEAMFORMERGRAPE_H

#include "usfinterfaceimagingbusiness_global.h"
#include "beamformerapple.h"

class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BeamFormerGrape : public BeamFormerApple
{
    Q_OBJECT
public:
    explicit BeamFormerGrape(QObject* parent = 0);
    virtual ~BeamFormerGrape();
    virtual IBFKitFactory* createBFKitFactory();
    virtual bool selectSocket(int socket, bool writeSocket = false);
    virtual void onSetSonoParameters();
    virtual bool standby();
    virtual bool wake();

protected:
    virtual void initializeProbeDection();
    virtual void initializeBFFpsParameter();
    virtual void createStaticParameters();
    virtual void changeProbePara();
    virtual void changeProbeInterval();
    virtual int getIOReadSize() const;
    virtual void controlIsFPGAElastoGraphyOn();
    // AFE芯片型号
    virtual QVector<ChipFactory::ChipNameEnum> supportChipList() const;

    virtual int getCWDLines(int speed, int sampleRate) const;

protected slots:
    virtual void setPreset(const PresetParameters& presets);
    virtual void onB_RX_LNUMChanged(const QVariant& value);
    virtual void onC_RX_LNUMChanged(const QVariant& value);
    virtual void onMBChanging(const QVariant& value);

    //
    virtual void onCQYZChanging(const QVariant& value);
    virtual void onGettingCQYZMax(int& value);
    virtual void onHighDensityChanging(const QVariant& value);
    virtual void onStripRemoveEnableChanged(const QVariant& value);
    virtual void onOverlappingMBChanged(const QVariant& value);

    virtual void onSyncModeChanging(const QVariant& value);
    virtual void onSystemScanModeChanging(const QVariant& value);
    virtual void onCfmRxFnumChanging(const QVariant& value);

    virtual void onDScanLineChanging(const QVariant& value);

    // B-MatchFilter相关的4个频率参数，要下发数据块
    //参考文档《B-MatchFilter系数选择说明V1_1.pdf》
    virtual void onMF_Coef_Fundamental_Steer0Changing(const QVariant& value);
    virtual void onMF_Coef_Fundamental_SteerXChanging(const QVariant& value);
    virtual void onMF_Coef_Harmonic_Steer0Changing(const QVariant& value);
    virtual void onMF_Coef_Harmonic_SteerXChanging(const QVariant& value);
    virtual void onFilterCoefOfTransmit5Changing(const QVariant& value);

    virtual void onTXFNo_Steer1Changing(const QVariant& value);
    virtual void onTxIndex5Changing(const QVariant& value);
    virtual void onTxIndex7Changing(const QVariant& value);

    virtual void onGettingAFE_LPF_FCutOffControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingAFE_HPF_FCutOffControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingADC_HPF_CORNER_FREQControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingAFE_PGA_CLAMP_LVLControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingAFE_PGA_CLAMP_LVLMax(int& value);
    virtual void onGettingAFE_LPF_FCutOffText(QString& value);
    virtual void onGettingAFE_HPF_FCutOffText(QString& value);
    virtual void onGettingADC_HPF_CORNER_FREQText(QString& value);
    virtual void onGettingAFE_PGA_CLAMP_LVLText(QString& value);
    virtual void onGettingAFE_PGA_HI_FREQText(QString& value);

    // PW的SteerAngle档位控制先保持与C一致，但是控制表的下发参数独立(与FPGA版本一致)
    //    void onDopSteerAngleIndexChanging(const QVariant& value);
    virtual void onSteeringAngleChanged(const QVariant& value, bool changed);
    virtual void onSteeringAngleChanging(const QVariant& value);
    virtual void onBeforeDopSteeringAngleChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onDopSteeringAngleChanged(const QVariant& value, bool changed);
    virtual void onGettingDopSteeringAngleMin(int& value);
    virtual void onGettingDopSteeringAngleMax(int& value);
    virtual void onGettingDopSteeringAngleControlTableValue(const QVariant& value, int& controlTableValue);

    virtual void onGettingPrtOfBControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingCQYZControlTableValue(const QVariant& value, int& controlTableValue);
    //    void onGettingMVelocityShowValue(QVariant& value);

    //    void onVolumeChanged(const QVariant& value);

    //    void onBeforeDopOutGainChanged(const QVariant& oldValue, QVariant& newValue);

    //
    virtual void onCWDSampleRateChanging(const QVariant& value);

    virtual void onGettingCWDSampleRateText(QString& value);

    virtual void onGettingCWDSampleRateMax(int& value);

    virtual void initCWDPRFParameterCalculator();

    virtual void onBaseLineCWDChanging(const QVariant& value);

    // 按照sonomax/xbit来 设置 M 速度的控制字
    virtual void onMVelocityChanging(const QVariant& value);

    virtual void onScanWidthChanged(const QVariant& value);
    virtual void onBeforeScpdChanged(const QVariant& oldValue, QVariant& newValue);

    void updateMaxCQYZ(bool trapezodialOn, bool scpdOn, int scpdValue, bool curvedExapanding);
    void updateLinearCQYZ(const int angle);
    void updateConvexCQYZ(const int angle);

    virtual void updatePRFCWD();
    virtual void onGettingFreqIndexDopText(QString& value);
    virtual void onGettingFreqIndexColorText(QString& value);

private:
    void createBFIODevice();

private:
    int m_CurMaxCQYZ;
    bool m_isVirtualVertexTrapezoidalModeEnable = false;
};

#endif // BEAMFORMERGRAPE_H
