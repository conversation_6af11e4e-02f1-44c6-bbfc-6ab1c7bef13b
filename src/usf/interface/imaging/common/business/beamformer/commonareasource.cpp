/*
 * =====================================================================================
 *
 *       Filename:  commonareasource.cpp
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  09/08/2014 10:03:53 AM
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:   (), |<EMAIL>|
 *   Organization:
 *
 * =====================================================================================
 */

#include "commonareasource.h"

#include <QDebug>
#include <QtCore/qmath.h>

#include "sonoparameters/isonoparameters.h"
#include "sonoparameters/parameter.h"
#include "bfpnames.h"
#include "probephysicalgeometry.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "formula.h"
#include "assertlog.h"
#include "bfdepthparameters.h"
#include "bfadfreqparameter.h"
#include "imageinfodef.h"

BaseAreaSource::BaseAreaSource(const ISonoParameters* sonoParameters, QSizeF wholeImageSize, QObject* parent,
                               AreaResultType type)
    : QObject(parent)
    , m_BFDepthParameters(NULL)
    , m_Type(type)
    , m_sonoParameters(sonoParameters)
{
    Q_ASSERT(sonoParameters);

    m_wholeImageSize = wholeImageSize;

    // DSC 使用的参数通过BFDepthParameters重新计算
    if (m_Type != AreaResult_TypeNULL)
    {
        int imageHeight = qCeil(pIV(BFPNames::PointNumPerLineStr) / pRV(BFPNames::RenderImageZoomCofStr));
        m_BFDepthParameters =
            new BFDepthParameters(pIV(BFPNames::ADFreqMHzStr), imageHeight, pIV(BFPNames::CQYZStr), false, false, 1.0f);
        if (m_Type != AreaResult_TypeOriginal)
        {
            bool halfHeight = (pIV(BFPNames::LayoutStr) != Layout_2x2) ? pBV(BFPNames::HalfHeightStr) : false;
            m_BFDepthParameters->setHalfHeight(halfHeight);
            float heightFactor =
                pIV(BFPNames::LayoutStr) != Layout_2x2
                    ? m_sonoParameters->pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>().yScale
                    : 1.0;
            m_BFDepthParameters->setHeightFactor(heightFactor);
        }
        connect(m_sonoParameters->parameter(BFPNames::CQYZStr), SIGNAL(valueChanging(QVariant)), this,
                SLOT(onCQYZChanging(QVariant)));
        connect(m_sonoParameters->parameter(BFPNames::ADFreqMHzStr), SIGNAL(valueChanging(QVariant)), this,
                SLOT(onADFreqMHzChanging(QVariant)));
        connect(m_sonoParameters->parameter(BFPNames::HalfHeightStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onHalfHeightChanging(QVariant)));
        connect(m_sonoParameters->parameter(BFPNames::TwoDImageScaleFactorStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onTwoDImageScaleFactorChanged(QVariant)));
        connect(m_sonoParameters->parameter(BFPNames::RenderImageZoomCofStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onRenderImageZoomCofStr(QVariant)));
    }
}

BaseAreaSource::~BaseAreaSource()
{
    disconnect(this);
    if (m_BFDepthParameters != NULL)
    {
        delete m_BFDepthParameters;
    }
}

int BaseAreaSource::type() const
{
    return m_Type;
}

void BaseAreaSource::onCQYZChanging(const QVariant& value)
{
    m_BFDepthParameters->setCQYZ(value.toInt());
}

void BaseAreaSource::onADFreqMHzChanging(const QVariant& value)
{
    m_BFDepthParameters->setPixelLen(BFADFreqParameter::pixelLenMM(value.toInt()));
}

void BaseAreaSource::onTwoDImageScaleFactorChanged(const QVariant& value)
{
    Q_UNUSED(value);
    if (m_Type != AreaResult_TypeOriginal)
    {
        float heightFactor = pIV(BFPNames::LayoutStr) != Layout_2x2
                                 ? m_sonoParameters->pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>().yScale
                                 : 1.0;
        m_BFDepthParameters->setHeightFactor(heightFactor);
    }
}

void BaseAreaSource::onHalfHeightChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (m_Type != AreaResult_TypeOriginal)
    {
        bool halfHeight = (pIV(BFPNames::LayoutStr) != Layout_2x2) ? pBV(BFPNames::HalfHeightStr) : false;
        m_BFDepthParameters->setHalfHeight(halfHeight);
    }
}

void BaseAreaSource::onRenderImageZoomCofStr(const QVariant& value)
{
    if (m_Type != AreaResult_TypeOriginal)
    {
        double imageHeight = pIV(BFPNames::PointNumPerLineStr) / value.toDouble();
        m_BFDepthParameters->setImageHeight(imageHeight);
    }
}

qreal BaseAreaSource::outerAreaStartDepthMM() const
{
    // 2023-04-23 Modify by AlexWang 部分窗口不需要考虑局部放大后的深度，如RegionZoom和PanZoom的导航区
    if (considerZoomOn())
        return pRV(BFPNames::StartDepthMMStr);
    return 0.0f;
}

qreal BaseAreaSource::pixelSizeMM() const
{
    if (m_Type != AreaResult_TypeNULL)
    {
        if (m_Type == AreaResult_TypeOriginal)
        {
            return m_BFDepthParameters->getPixelizeMMWithFactor();
        }
        else
        {
            float easyPlayImageZoomCoef =
                m_Type == AreaResult_TypeGstStreamC ? 1.0 : m_sonoParameters->pDV(BFPNames::EasyPlayImageZoomCofStr);
            return m_BFDepthParameters->getPixelizeMMWithFactor() / pRV(BFPNames::RenderImageZoomCofStr) /
                   easyPlayImageZoomCoef;
        }
    }
    else
    {
        return pRV(BFPNames::PixelSizeMMStr);
    }
}

qreal BaseAreaSource::depthMM() const
{
    if (m_Type != AreaResult_TypeNULL)
    {
        return m_BFDepthParameters->depthMM();
    }
    else
    {
        return pRV(BFPNames::DepthMMStr);
    }
}

qreal BaseAreaSource::imageZoomCoefNormalized() const
{
    if ((m_Type != AreaResult_TypeNULL))
    {
        return 1.0f;
    }
    else
    {
        return pRV(BFPNames::ImageZoomCoefStr) / qreal(100);
    }
}

QPointF BaseAreaSource::centerPosOffset() const
{
    return QPointF(0.0, 0.0);
}

int BaseAreaSource::pIV(const QString& name) const
{
    return m_sonoParameters->pIV(name);
}

bool BaseAreaSource::pBV(const QString& name) const
{
    return m_sonoParameters->pBV(name);
}

qreal BaseAreaSource::pRV(const QString& name) const
{
    return m_sonoParameters->pV(name).toReal();
}

QStringList BaseAreaSource::changedSources() const
{
    QStringList ret;

    ret << BFPNames::PixelSizeMMStr << BFPNames::StartDepthMMStr << BFPNames::LineSpacingMMStr
        << BFPNames::AngleSpacingRadStr << BFPNames::PA_VERT_DIST_EnableStr << BFPNames::ProbeDSCImageZoomCofStr
        << BFPNames::TwoDImageScaleFactorStr << BFPNames::RenderImageZoomCofStr;

    return ret;
}

int BaseAreaSource::startLine() const
{
    return pIV(BFPNames::StartLineStr);
}

int BaseAreaSource::stopLine() const
{
    return pIV(BFPNames::StopLineStr);
}

qreal BaseAreaSource::paramShowValueAngle2Rad(const QString& name) const
{

    return IAreaSource::paramShowValueAngle2Rad(m_sonoParameters, name);
}

QStringList BAreaSource::changedSources() const
{

    QStringList ret = BaseAreaSource::changedSources();

    ret << BFPNames::CQYZStr << BFPNames::StartLineStr << BFPNames::StopLineStr << BFPNames::ZoomOnStr
        << BFPNames::DepthMMStr

        //当梯形成像时，影响图像区域的偏转角度是CPDSteer
        //<<(m_compoundMode?
        << BFPNames::CPDSteerStr << BFPNames::BSteeringScanStr

        << BFPNames::BRXLnumStr << BFPNames::HighDensityStr << BFPNames::AnyDensityEnStr
        << BFPNames::FPGAPlatformTypeStr

        << BFPNames::PanZoomOffsetDepthPixelStr << BFPNames::PanZoomMidPixelStr << BFPNames::PanZoomHalfPixelStr
        << BFPNames::PanZoomMidDepthPixelStr << BFPNames::PanZoomHalfDepthPixelStr;

    return ret;
}

ProbePhysicalGeometry BAreaSource::area() const
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));

    ProbeParameters probeParam(probeDataInfo, pRV(BFPNames::StartDepthMMStr), pixelSizeMM(), pBV(BFPNames::ZoomOnStr),
                               pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr));

    qreal bottomDepth = probeParam.bottomDepthMM(depthMM());

    ProbePhysicalGeometry ret;

    ret.setAll(pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr), pRV(BFPNames::StartDepthMMStr), bottomDepth);

    return ret;
}

qreal BAreaSource::steeringRad() const
{
    qreal rad;
    if (pIV(BFPNames::FPGAPlatformTypeStr) == FPGAPlatformType_Xilinx)
    {
        rad = paramShowValueAngle2Rad(BFPNames::BSteeringScanStr);
    }
    else if (pIV(BFPNames::FPGAPlatformTypeStr) == FPGAPlatformType_Altera)
    {
        rad = -paramShowValueAngle2Rad(BFPNames::BSteeringScanStr);
    }
    else
    {
        rad = paramShowValueAngle2Rad(BFPNames::BSteeringScanStr);
    }
    return rad;
}

const QString ROIAreaSource::RoiMidLineStr[] = {BFPNames::RoiMidLineStr,       BFPNames::RoiMidLineTDIStr,
                                                BFPNames::RoiMidLineElastoStr, BFPNames::RoiMidLineFourDStr,
                                                BFPNames::RoiMidLineStr,       BFPNames::RoiMidLineSNStr};

qreal BAreaSource::cPDSteerRad() const
{
    return m_compoundMode ? (pBV(BFPNames::TrapezoidalModeStr) ? Formula::degree2Rad(pRV(BFPNames::TrapezoidalAngleStr))
                                                               : Formula::degree2Rad(pRV(BFPNames::CPDSteerStr)))
                          : 0;
}

const QString ROIAreaSource::RoiHalfLineStr[] = {BFPNames::RoiHalfLinesStr,       BFPNames::RoiHalfLinesTDIStr,
                                                 BFPNames::RoiHalfLinesElastoStr, BFPNames::RoiHalfLinesFourDStr,
                                                 BFPNames::RoiHalfLinesStr,       BFPNames::RoiHalfLinesSNStr};

const QString ROIAreaSource::RoiMidDepthMMStr[] = {BFPNames::RoiMidDepthMMStr,       BFPNames::RoiMidDepthMMTDIStr,
                                                   BFPNames::RoiMidDepthMMElastoStr, BFPNames::RoiMidDepthMMFourDStr,
                                                   BFPNames::RoiMidDepthMMStr,       BFPNames::RoiMidDepthMMSNStr};

const QString ROIAreaSource::RoiHalfDepthMMStr[] = {BFPNames::RoiHalfDepthMMStr,       BFPNames::RoiHalfDepthMMTDIStr,
                                                    BFPNames::RoiHalfDepthMMElastoStr, BFPNames::RoiHalfDepthMMFourDStr,
                                                    BFPNames::RoiHalfDepthMMStr,       BFPNames::RoiHalfDepthMMSNStr};

const QString ROIAreaSource::LineDensityStr[] = {
    BFPNames::ColorLineDensityStr,       BFPNames::TDILineDensityStr,
    BFPNames::ElastoColorLineDensityStr, QString(), // 4D暂时没有,暂时设置为空
    BFPNames::MVILineDensityStr,         BFPNames::ColorLineDensitySNStr};

const QString ROIAreaSource::SteeringAngleStr[] = {BFPNames::SteeringAngleStr, BFPNames::SteeringAngleStr,
                                                   BFPNames::SteeringAngleStr, QString(), // 4D没有,设置为空
                                                   BFPNames::SteeringAngleStr, BFPNames::SteeringAngleSNStr};

QStringList ROIAreaSource::changedSources() const
{

    QStringList ret = BaseAreaSource::changedSources();

    ret << RoiMidLineStr[m_mode] << RoiHalfLineStr[m_mode] << RoiMidDepthMMStr[m_mode] << RoiHalfDepthMMStr[m_mode]
        << BFPNames::SteeringAngleStr << BFPNames::CRXLnumStr << BFPNames::ColorLineDensityStr
        << BFPNames::ColorLineDensitySNStr << BFPNames::TDILineDensityStr << BFPNames::MVILineDensityStr
        << BFPNames::ColorAnyDensityEnStr << BFPNames::CSteeringAngleStr << BFPNames::AngleSpacingRadForCStr
        << BFPNames::DSCCenterLineStr << BFPNames::FourDKnotLineStr << BFPNames::FourDKnotDepthMMStr
        << BFPNames::FourDRectRatioStr << BFPNames::ImageZoomCoefStr << BFPNames::PanZoomOffsetDepthPixelStr
        << BFPNames::PanZoomMidPixelStr << BFPNames::PanZoomHalfPixelStr << BFPNames::PanZoomMidDepthPixelStr
        << BFPNames::PanZoomHalfDepthPixelStr;

    return ret;
}

ProbePhysicalGeometry ROIAreaSource::area() const
{

    ProbePhysicalGeometry rect(pIV(RoiMidLineStr[m_mode]), pIV(RoiHalfLineStr[m_mode]), pRV(RoiMidDepthMMStr[m_mode]),
                               pRV(RoiHalfDepthMMStr[m_mode]));

    return rect;
}

qreal ROIAreaSource::steeringRad() const
{

    //    qreal CSteeringAngle = pRV(BFPNames::CSteeringAngleStr);

    //    return Formula::degree2Rad(CSteeringAngle);
    return paramShowValueAngle2Rad(BFPNames::SteeringAngleStr);
}

qreal ROIAreaSource::cPDSteerRad() const
{
    return 0;
}

QString ROIAreaSource::lineDensityName() const
{
    return LineDensityStr[m_mode];
}

QString ROIAreaSource::SteerAngleName() const
{
    return SteeringAngleStr[m_mode];
}

bool ROIAreaSource::considerZoomOn() const
{
    return pBV(BFPNames::ZoomOnStr);
}
