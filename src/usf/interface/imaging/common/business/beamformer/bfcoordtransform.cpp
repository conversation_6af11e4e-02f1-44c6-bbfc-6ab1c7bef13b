#include "bfcoordtransform.h"
#include "util.h"
#include <math.h>
#include <qmath.h>
#include "isonoparameters.h"
#include "bfpnames.h"
#include "probedataset.h"
#include "bfdepthparameters.h"
#include "bfscanwidthparameter.h"
#include "modelconfig.h"
#include <QDebug>
#include "parameter.h"

BFCoordTransform::BFCoordTransform()
    : m_Probe(&ProbeDataSet::instance().firstProbe())
    , m_StartDepthMM(0)
    , m_PixelSizeMM(1.0f)
    , m_MiddleLine(80)
    , m_ZoomOn(false)
    , m_StartLine(0)
    , m_StopLine(0)
    , m_TotalLines(0)
    , m_DopSteerAngle(0)
    , m_DopplerScanLineStartPointX(0)
    , m_DopplerScanLineStartPointY(0)
{
}

BFCoordTransform::BFCoordTransform(const ISonoParameters* sonoParameters, bool considerZoomOn, int startLine,
                                   int stopLine, int totalLines)
    : m_Probe(&ProbeDataSet::instance().getProbe(sonoParameters->pIV(BFPNames::ProbeIdStr)))
    , m_StartDepthMM(sonoParameters->pDV(BFPNames::StartDepthMMStr))
    , m_PixelSizeMM(sonoParameters->pDV(BFPNames::PixelSizeMMStr))
    , m_ZoomOn(sonoParameters->pBV(BFPNames::ZoomOnStr))
    , m_TotalLines(totalLines)
    , m_DopSteerAngle((sonoParameters->parameter(BFPNames::DopSteeringAngleStr))->showValue().toReal())
    , m_DopplerScanLineStartPointX(sonoParameters->pDV(BFPNames::DScanLineStartXStr))
    , m_DopplerScanLineStartPointY(sonoParameters->pDV(BFPNames::DScanLineStartYStr))
{
    if (startLine == 0)
    {
        m_StartLine = sonoParameters->pIV(BFPNames::StartLineStr);
    }
    else
    {
        m_StartLine = startLine;
    }
    if (stopLine == 0)
    {
        m_StopLine = sonoParameters->pIV(BFPNames::StopLineStr);
    }
    else
    {
        m_StopLine = stopLine;
    }
    m_MiddleLine = probeParameters().middleLineNo(m_StartLine, m_StopLine);

    // 2023-03-27 Add by AlexWang
    // m_pixelsizeMM 不应该更改  不然放大状态下计算出的点不对
    //    if(sonoParameters->pIV(BFPNames::ImageZoomCoefStr) > 100)
    //    {
    //       m_PixelSizeMM = BFDepthParameters::
    //       pixelSizeMMWithoutZoomCoef(sonoParameters->pDV(BFPNames::PixelSizeMMStr),
    //                                                                      sonoParameters->pIV(BFPNames::ImageZoomCoefStr),
    //                                                                      sonoParameters->pDV(BFPNames::RenderImageZoomCofStr),
    //                                                                      sonoParameters->pDV(BFPNames::FixedSWImageZoomCofStr));
    //    }
    // 2023-04-27 Write by AlexWang  部分计算不考虑放大因素，如RegionZoom和PanZoom导航区
    if (!considerZoomOn)
    {
        m_ZoomOn = false;
        m_StartDepthMM = 0.0f;
        if (sonoParameters->pBV(BFPNames::IsScrollStr))
            m_StartDepthMM = sonoParameters->pDV(BFPNames::ScrollDepthMMStr);

        // 2023-05-08 Write by AlexWang 当ZoomCoef或CQYZ改变时，需要通过BFDepthParameters对象计算出正确的PixelSizeMM值
        BFDepthParameters depthP(sonoParameters, considerZoomOn);
        double pixelSizeMM = depthP.pixelSizeMM();
        pixelSizeMM /= sonoParameters->pDV(BFPNames::RenderImageZoomCofStr);
        m_PixelSizeMM = pixelSizeMM;

        // 2023-05-23 Write by AlexWang 不受局部放大因素影响的区域，需要通过SacnWidth参数来计算StartLine和StopLine
        // 2025-07-16 Modify by AlexWang
        // [Task:10658] 支持任意线密度下的局部放大时，计算整个探头扫查区域的起始线和终止线
        BFScanWidthParameter scanWidthP(*m_Probe, sonoParameters->pIV(BFPNames::ScanWidthStr),
                                        ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity).toBool(),
                                        sonoParameters->pIV(BFPNames::B_RX_LNUMStr));
        m_StartLine = scanWidthP.startLine();
        m_StopLine = scanWidthP.stopLine();
        m_MiddleLine = probeParameters().middleLineNo(m_StartLine, m_StopLine);
    }
}

BFCoordTransform::BFCoordTransform(const ProbeDataInfo& probe, qreal startDepthMM, qreal pixelSizeMM, int totalLines)
    : m_Probe(&probe)
    , m_StartDepthMM(startDepthMM)
    , m_PixelSizeMM(pixelSizeMM)
    , m_ZoomOn(false)
    , m_StartLine(0)
    , m_StopLine(0)
    , m_TotalLines(totalLines)
{
    m_MiddleLine = probeParameters().middleLineNo();
}

BFCoordTransform::BFCoordTransform(const ProbeDataInfo& probe, qreal startDepthMM, qreal pixelSizeMM, bool zoomOn,
                                   int startLine, int stopLine, int totalLines)
    : m_Probe(&probe)
    , m_StartDepthMM(startDepthMM)
    , m_PixelSizeMM(pixelSizeMM)
    , m_ZoomOn(zoomOn)
    , m_StartLine(startLine)
    , m_StopLine(stopLine)
    , m_TotalLines(totalLines)
{
    m_MiddleLine = probeParameters().middleLineNo(m_StartLine, m_StopLine);
}

ProbePhysicalPointF BFCoordTransform::convertPtToPhysics(const QPoint& pt) const
{
    ProbePhysicalPointF ppt;
    convertPtToPhysics(pt, ppt.rx(), ppt.ry());
    return ppt;
}

ProbePhysicalPointF BFCoordTransform::convertPtToPhysics(const QPointF& pt) const
{
    ProbePhysicalPointF ppt;
    convertPtToPhysics(pt, ppt.rx(), ppt.ry());
    return ppt;
}

void BFCoordTransform::convertPtToPhysics(const QPoint& pt, qreal& line, qreal& depthMM) const
{
    convertPtToPhysics(QPointF(pt), line, depthMM);
}

void BFCoordTransform::convertPtToPhysics(const QPointF& pt, qreal& line, qreal& depthMM) const
{
    //整个计算过程完全按照 convertPhysicsToLogic 的逆变换进行

    ProbeParameters probeParas = probeParameters();

    QPointF phyPt(pt.x() * m_PixelSizeMM, pt.y() * m_PixelSizeMM);
    //以图像正中间为x=0的位置，计算有方向的线数，这样能支持 line 的范围超过 0~maxLineNo()
    qreal lined = 0.0f;
    if (probe().IsLinear)
    {
        qreal probeW = probeParas.probeWidthMM();
        lined = phyPt.x() * probeParas.lines() / probeW;
        // y坐标要考虑m_StartDepthMM的因素
        depthMM = phyPt.y() + m_StartDepthMM;
    }
    else
    {
        // y坐标要考虑m_StartDepthMM的因素，perpendicularDisMM中已经考虑了m_StartDepthMM
        // phyPt传入点的物理意义上的距离
        qreal yDis = phyPt.y() + probeParas.perpendicularDisMM();
        qreal angled = qAtan(phyPt.x() / yDis);
        // qAtan 的返回值在(-M_PI/2, M_PI/2)区间内，这里要分情况考虑实际的angled是否在这个区间外
        //如果逻辑坐标点在圆心以上的位置，要计算的角度是个钝角，在圆心左侧的是负的钝角，右侧是正的钝角
        // convertPhysicsToLogic 中用的是sin cos不会出现此问题
        if (yDis < 0)
        {
            if (phyPt.x() < 0)
            {
                angled = angled - M_PI;
            }
            else
            {
                angled = angled + M_PI;
            }
        }
        lined = probeParas.convexProbeLineNo(angled);

        // qreal dis = phyPt.x() / qSin(angled);
        // angled有可能0度，qSin(angled)=0，导致dis会无效，使用以下方法计算，angled一般不会是90度
        // qCos(angled)!=0;
        qreal dis = yDis / qCos(angled);
        depthMM = dis - probe().WaferRadius;
    }

    line = lined + m_MiddleLine;
}

void BFCoordTransform::convertPhysicsToLogic(const ProbePhysicalPointF& ppt, QPoint& pt) const
{
    convertPhysicsToLogic(ppt.x(), ppt.y(), pt);
}

void BFCoordTransform::convertPhysicsToLogic(const ProbePhysicalPointF& ppt, QPointF& pt) const
{
    convertPhysicsToLogic(ppt.x(), ppt.y(), pt);
}

QPoint BFCoordTransform::convertPhysicsToLogic(const ProbePhysicalPointF& ppt) const
{
    QPoint pt;
    convertPhysicsToLogic(ppt.x(), ppt.y(), pt);
    return pt;
}

QPointF BFCoordTransform::convertPhysicsToLogicF(const ProbePhysicalPointF& ppt) const
{
    QPointF pt;
    convertPhysicsToLogic(ppt.x(), ppt.y(), pt);
    return pt;
}

QPoint BFCoordTransform::convertPhysicsToLogic(const qreal& line, const qreal& depthMM) const
{
    QPoint pt;
    convertPhysicsToLogic(line, depthMM, pt);
    return pt;
}

QPointF BFCoordTransform::convertPhysicsToLogicF(const qreal& line, const qreal& depthMM) const
{
    QPointF pt;
    convertPhysicsToLogic(line, depthMM, pt);
    return pt;
}

void BFCoordTransform::convertPhysicsToLogic(const qreal& line, const qreal& depthMM, QPoint& pt) const
{
    QPointF ptf(pt);
    convertPhysicsToLogic(line, depthMM, ptf);
    pt = ptf.toPoint();
}

/**
 * @brief convertPhysicsToLogic
 *
 * @param line
 * @param depthMM 不管是放大还是非放大状态，depthMM都是严格意义的深度，从探头表面为起点
 * @param pt
 */
void BFCoordTransform::convertPhysicsToLogic(const qreal& line, const qreal& depthMM, QPointF& pt) const
{
    ProbeParameters probeParas = probeParameters();

    QPointF phyPt;
    //以图像正中间为x=0的位置，计算有方向的线数，这样能支持 line 的范围超过 0~maxLineNo()
    qreal lined = -(m_MiddleLine - line);
    if (probe().IsLinear)
    {
        qreal probeW = probeParas.probeWidthMM(0, true);

        phyPt.setX(probeW * lined / probeParas.lines());
        // y坐标要考虑m_StartDepthMM的因素
        phyPt.setY(depthMM - m_StartDepthMM);
    }
    else
    {
        // 2023-05-04 Modify by AlexWang 解决凸阵在放大状态时C图标位置不正确，需要考虑局部放大对参数计算的影响
        qreal angled = probeParas.convexProbeLineAngleRadEx(lined);
        qreal dis = depthMM + probe().WaferRadius;
        phyPt.setX(dis * sin(angled));
        // y坐标要考虑m_StartDepthMM的因素，perpendicularDisMM中已经考虑了m_StartDepthMM
        phyPt.setY(dis * cos(angled) - probeParas.perpendicularDisMM());
    }

    pt.setX(phyPt.x() / m_PixelSizeMM);
    pt.setY(phyPt.y() / m_PixelSizeMM);
}

ProbePhysicalPointF BFCoordTransform::convertOffsetPhysicalPosToPhysics(const QPointF& offset,
                                                                        const ProbePhysicalPointF& pos) const
{
    QPointF posMid = convertPhysicsToLogicF(pos);
    return convertOffsetToPhysics(offset, posMid);
}

ProbePhysicalPointF BFCoordTransform::convertOffsetToPhysics(const QPointF& offset, const QPointF& pos) const
{
    ProbePhysicalPointF pt;
    convertOffsetToPhysics(offset, pos, pt.rx(), pt.ry());
    return pt;
}

void BFCoordTransform::convertOffsetToPhysics(const QPointF& offset, const QPointF& pos, qreal& lineOffset,
                                              qreal& depthMMOffset) const
{
    qreal linePos, lineTemp, lineDest, depthMMPos, depthMMTemp, depthMMDest;
    convertPtToPhysics(pos, linePos, depthMMPos);

    // x、y方向分开进行位移的计算，否则在凸阵状态时，水平移动，会导致ROI的垂直方向会有位移
    convertPtToPhysics(pos + QPointF(offset.x(), 0), lineDest, depthMMTemp);
    convertPtToPhysics(pos + QPointF(0, offset.y()), lineTemp, depthMMDest);

    lineOffset = lineDest - linePos;
    depthMMOffset = depthMMDest - depthMMPos;
}

void BFCoordTransform::movePhysicalPoint(ProbePhysicalPoint& ppt, const QPointF& offset)
{
    movePhysicalPoint(ppt, ppt, offset);
}

void BFCoordTransform::movePhysicalPointF(ProbePhysicalPointF& ppt, const QPointF& offset)
{
    movePhysicalPointF(ppt, ppt, offset);
}

void BFCoordTransform::movePhysicalPoint(ProbePhysicalPoint& ppt, const ProbePhysicalPoint& relatedPPt,
                                         const QPointF& offset)
{
    ProbePhysicalPointF pptf = ppt.toPointF();
    movePhysicalPointF(pptf, relatedPPt, offset);
    ppt = ProbePhysicalPoint(pptf);
}

void BFCoordTransform::movePhysicalPoint(ProbePhysicalPointF& ppt, const ProbePhysicalPoint& relatedPPt,
                                         const QPointF& offset)
{
    movePhysicalPointF(ppt, relatedPPt, offset);
}

void BFCoordTransform::movePhysicalPointF(ProbePhysicalPointF& ppt, const ProbePhysicalPoint& relatedPPt,
                                          const QPointF& offset)
{
    ppt += convertOffsetPhysicalPosToPhysics(offset, relatedPPt.toPointF());
}

void BFCoordTransform::changePhysicalSize(ProbePhysicalSize& size, const ProbePhysicalPoint& centerPPt,
                                          const QPointF& sizeOffset)
{
    ProbePhysicalPoint sizePt = convertOffsetPhysicalPosToPhysics(sizeOffset, centerPPt.toPointF());
    size += ProbePhysicalSize(sizePt.x(), sizePt.y());
}

ProbePhysicalPointF BFCoordTransform::convertPtToPhysicalSamples(const QPointF& pt, bool isHalfHeight) const
{
    ProbePhysicalPointF ppt;
    convertPtToPhysicalSamples(pt, ppt.rx(), ppt.ry(), isHalfHeight);
    return ppt;
}

void BFCoordTransform::convertPtToPhysicalSamples(const QPointF& pt, qreal& line, qreal& pixel, bool isHalfHeight) const
{
    convertPtToPhysics(pt, line, pixel);
    pixel = (pixel * (isHalfHeight ? 2 : 1)) / (m_PixelSizeMM);
}

QPointF BFCoordTransform::getProbeCenterPt() const
{
    return QPointF(0.0f, -probeParameters().perpendicularDisMM());
}

QPointF BFCoordTransform::getImageCenterPt() const
{
    return QPointF(0.0f, probeParameters().arcFloorDisMM());
}

const ProbeDataInfo& BFCoordTransform::probe() const
{
    return *m_Probe;
}

ProbeParameters BFCoordTransform::probeParameters() const
{
    ProbeParameters probeParam =
        ProbeParameters(probe(), m_StartDepthMM, m_PixelSizeMM, m_ZoomOn, m_StartLine, m_StopLine);
    if (m_TotalLines > 0)
    {
        probeParam.setTotalLines(m_TotalLines);
    }
    return probeParam;
}
