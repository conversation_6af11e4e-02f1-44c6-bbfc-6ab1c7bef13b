#include "probeimageregion.h"
#include "probephysicalgeometry.h"
#include "isonoparameters.h"
#include "bfpnames.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "util.h"
#include "probephysicalpoint.h"
#include "probephysicalline.h"
#include "bfscanwidthparameter.h"
#include "bfdepthparameters.h"
#include "bfadfreqparameter.h"

ProbeImageRegion::ProbeImageRegion()
    : m_Left(0)
    , m_Right(0)
    , m_Top(0.0f)
    , m_Bottom(0.0f)
{
}

ProbeImageRegion::ProbeImageRegion(ISonoParameters* sonoParameters, bool considerZoomOn)
    : m_Left(sonoParameters->pIV(BFPNames::StartLineStr))
    , m_Right(sonoParameters->pIV(BFPNames::StopLineStr))
    , m_Top(sonoParameters->pDV(BFPNames::StartDepthMMStr))
{
    const ProbeDataInfo& probe = ProbeDataSet::instance().getProbe(sonoParameters->pIV(BFPNames::ProbeIdStr));
    // 2023-05-06 Modify by AlexWang 替换使用超声参数集合的指针作为传入参数的构造函数
    ProbeParameters pParameters(sonoParameters, considerZoomOn);
    if (!considerZoomOn)
    {
        m_Top = 0;
        if (sonoParameters->pBV(BFPNames::IsScrollStr))
            m_Top = sonoParameters->pDV(BFPNames::ScrollDepthMMStr);

        // 2023-05-08 Write by AlexWang 进入RegionZoom的放大状态后，导航区的探头图像区域仍是全局的探头图像区域
        //当ZoomCoef或CQYZ改变时，需要通过BFDepthParameters对象计算出正确的PixelSizeMM值和探头的实际深度(m_Bottom)
        //通过ScanWidthStr计算出实际的StartLine(m_Left)和StopLine(m_Right)
        BFDepthParameters depthP(sonoParameters, considerZoomOn);
        double pixelSizeMM = depthP.pixelSizeMM();
        double depthMM = depthP.depthMM();
        pixelSizeMM /= sonoParameters->pDV(BFPNames::RenderImageZoomCofStr);
        depthMM /= sonoParameters->pDV(BFPNames::RenderImageZoomCofStr);
        // 2025-07-16 Modify by AlexWang
        // [Task:10658] 支持任意线密度下的局部放大时，计算整个探头扫查区域的起始线和终止线
        BFScanWidthParameter scanWidthP(probe, sonoParameters->pIV(BFPNames::ScanWidthStr),
                                        ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity).toBool(),
                                        sonoParameters->pIV(BFPNames::B_RX_LNUMStr));
        m_Left = scanWidthP.startLine();
        m_Right = scanWidthP.stopLine();
        m_Bottom = pParameters.bottomDepthMM(depthMM) - 1 * pixelSizeMM;
    }
    else
    {
        m_Bottom = calBottomDepthMM(pParameters, sonoParameters);
    }
}

ProbeImageRegion::ProbeImageRegion(const ProbeParameters& probeParameters, ISonoParameters* sonoParameters)
    : m_Left(sonoParameters->pIV(BFPNames::StartLineStr))
    , m_Right(sonoParameters->pIV(BFPNames::StopLineStr))
    , m_Top(sonoParameters->pDV(BFPNames::StartDepthMMStr))
    , m_Bottom(calBottomDepthMM(probeParameters, sonoParameters))
{
}

ProbeImageRegion::ProbeImageRegion(int left, int right, qreal top, const ProbeParameters& probeParameters,
                                   qreal depthMM, qreal pixelsizeMM)
    : m_Left(left)
    , m_Right(right)
    , m_Top(top)
    , m_Bottom(probeParameters.bottomDepthMM(depthMM - 1 * pixelsizeMM))
{
}

ProbeImageRegion::ProbeImageRegion(int left, int right, qreal top, qreal bottom)
    : m_Left(left)
    , m_Right(right)
    , m_Top(top)
    , m_Bottom(bottom)
{
}

void ProbeImageRegion::setAll(int left, int right, qreal top, qreal bottom)
{
    m_Left = left;
    m_Right = right;
    m_Top = top;
    m_Bottom = bottom;
}

int ProbeImageRegion::left() const
{
    return m_Left;
}

void ProbeImageRegion::setLeft(int value)
{
    m_Left = value;
}

int ProbeImageRegion::right() const
{
    return m_Right;
}

void ProbeImageRegion::setRight(int value)
{
    m_Right = value;
}

int ProbeImageRegion::width() const
{
    return m_Right - m_Left;
}

qreal ProbeImageRegion::top() const
{
    return m_Top;
}

void ProbeImageRegion::setTop(qreal value)
{
    m_Top = value;
}

qreal ProbeImageRegion::bottom() const
{
    return m_Bottom;
}

qreal ProbeImageRegion::height() const
{
    return m_Bottom - m_Top;
}

ProbePhysicalPoint ProbeImageRegion::center() const
{
    return ProbePhysicalPoint(m_Left + (width() / 2), m_Top + (height() / 2));
}

void ProbeImageRegion::setBottom(qreal value)
{
    m_Bottom = value;
}

bool ProbeImageRegion::contains(int line) const
{
    return line >= m_Left && line <= m_Right;
}

bool ProbeImageRegion::contains(qreal depthMM) const
{
    return depthMM >= m_Top && depthMM <= m_Bottom;
}

bool ProbeImageRegion::contains(int line, qreal depthMM) const
{
    return contains(line) && contains(depthMM);
}

bool ProbeImageRegion::contains(const ProbePhysicalPoint& pt) const
{
    return contains(pt.x(), pt.y());
}

bool ProbeImageRegion::contains(const ProbePhysicalLine& line) const
{
    return contains(line.line());
}

bool ProbeImageRegion::contains(const ProbePhysicalGeometry& geometry) const
{
    return contains(ProbePhysicalPoint(geometry.left(), geometry.top())) &&
           contains(ProbePhysicalPoint(geometry.right(), geometry.bottom()));
}

void ProbeImageRegion::validate(int& line) const
{
    line = validated((const int&)line);
}

int ProbeImageRegion::validated(const int& line) const
{
    return qBound(left(), line, right());
}

void ProbeImageRegion::validate(qreal& depthMM) const
{
    depthMM = validated((const qreal&)depthMM);
}

qreal ProbeImageRegion::validated(const qreal& depthMM) const
{
    return qBound(top(), depthMM, bottom());
}

void ProbeImageRegion::validate(int& line, qreal& depthMM) const
{
    validate(line);
    validate(depthMM);
}

void ProbeImageRegion::validate(ProbePhysicalPoint& pt) const
{
    pt = validated((const ProbePhysicalPoint&)pt);
}

void ProbeImageRegion::validate(ProbePhysicalLine& line) const
{
    line = validated((const ProbePhysicalLine&)line);
}

ProbePhysicalPoint ProbeImageRegion::validated(const ProbePhysicalPoint& pt) const
{
    return ProbePhysicalPoint(validated((const int&)pt.x()), validated((const qreal&)pt.y()));
}

ProbePhysicalPointF ProbeImageRegion::validated(const ProbePhysicalPointF& pt) const
{
    return ProbePhysicalPointF(validated((const int&)pt.x()), validated((const qreal&)pt.y()));
}

ProbePhysicalLine ProbeImageRegion::validated(const ProbePhysicalLine& line) const
{
    return ProbePhysicalLine(validated((const int&)line.line()));
}

qreal ProbeImageRegion::calBottomDepthMM(const ProbeParameters& probeParameters, ISonoParameters* sonoParameters)
{
    //这里减去1个像素的物理距离，避免ROI框的底边出界
    return probeParameters.bottomDepthMM(sonoParameters->pDV(BFPNames::DepthMMStr)) -
           1 * sonoParameters->pDV(BFPNames::PixelSizeMMStr);
}
