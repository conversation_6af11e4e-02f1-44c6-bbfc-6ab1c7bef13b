﻿#include "bfstaticparametersgrape.h"

BFStaticParametersGrape::BFStaticParametersGrape()
{
    m_AFE_InImpedenceText = QVector<QString>() << "50 Ω"
                                               << "100 Ω"
                                               << "200 Ω"
                                               << "400 Ω";

    m_AFE_LNA_GAINText = QVector<QString>() << "18 dB"
                                            << "21 dB"
                                            << "15 dB"
                                            << "Reserved";

    m_AFE_HPF_FCutOff = QVector<float>() << 100.0f << 50.0f;
    m_AFE_HPF_FCutOffControlTable = QVector<int>() << 0 << 15;

    m_AFE_LPF_FCutOff = QVector<float>() << 10.0f << 15.0f << 20.0f << 30.0f;
    m_AFE_LPF_FCutOffControlTable = QVector<int>() << 0 << 1 << 2 << 3;

    m_AFE_PGA_GAINText = QVector<QString>() << "24 dB"
                                            << "21 dB"
                                            << "27 dB"
                                            << "18 dB";

    m_ADC_HPF_CORNER_FREQ = QVector<float>()
                            << 2.78f << 1.49f << 0.738f << 0.369f << 0.185f << 0.111f << 0.049f << 0.025f << 0.012f;
    m_ADC_HPF_CORNER_FREQControlTable = QVector<int>() << 2 << 3 << 4 << 5 << 6 << 7 << 8 << 9 << 10;

    m_AFE_PGA_CLAMP_LVL = QVector<float>() << 1.6f << 2.24f << 1.26f << 2.24f << 2.0f << 2.24f << 2.24f;
    m_AFE_PGA_CLAMP_LVLControlTable = QVector<int>() << 0 << 1 << 2 << 3 << 4 << 5 << 7;

    m_AFE_PGA_HI_FREQ = QVector<int>() << 0 << 63;

    m_RealFreqs = QVector<float>() << 2.50f << 3.08f << 3.64f << 4.00f << 5.00f << 6.67f << 8.00f << 10.0f << 2.22f
                                   << 2.11f << 2.00f << 1.67f << 1.54f << 5.71f << 13.3f << 16.0f << 1.81f << 1.90f
                                   << 2.86f << 3.33f << 3.80f << 4.44f << 4.70f << 5.33f << 6.15f << 7.27f << 8.88f
                                   << 11.43f << 12.3f << 10.66f << 20.0f << 14.54f;

    QVector<int> linesOfSpeed1 = QVector<int>() << 38 << 50 << 60 << 75 << 86 << 100 << 111 << 120 << 133 << 150 << 167
                                                << 200 << 250 << 300 << 375 << 429; // TODO Seven 放开
    QVector<int> linesOfSpeed2 = QVector<int>() << 31 << 42 << 50 << 63 << 71 << 83 << 93 << 100 << 111 << 125 << 139
                                                << 167 << 208 << 250 << 313 << 357;
    QVector<int> linesOfSpeed3 = QVector<int>() << 25 << 33 << 40 << 50 << 57 << 67 << 74 << 80 << 89 << 100 << 111
                                                << 133 << 167 << 200 << 250 << 286;

    m_CWDLines = QVector<QVector<int>>() << linesOfSpeed1 << linesOfSpeed2 << linesOfSpeed3;

    m_CWDSampleRateValues = QVector<double>()
                            << 6250 << 8330 << 10000 << 12500 << 14280 << 16670 << 18520 << 20000 << 22220 << 25000
                            << 27780 << 33330 << 41670 << 50000 << 62500 << 125000; // 复用 SonoMax

    m_MSpeedParas = QList<int>() << (50 - 1) << (40 - 1) << (30 - 1) << (20 - 1);
}
