#ifndef BASEBFSTATICPARAMETERS_H
#define BASEBFSTATICPARAMETERS_H
#include "usfinterfaceimagingbusiness_global.h"

#include <QVector>
#include <QHash>

/**
 * @brief The BaseBFStaticParameters class BeamFormer 中一些固定的参数集合
 *
 * 超声系统中的一些固有参数，这些参数不会实时变更。
 * 由于一些控制表参数是档位值，而实际每一档会对应具有物理意义的值，这个类就管理这样的档位值
 * 对应物理值的集合
 */
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BaseBFStaticParameters
{
public:
    BaseBFStaticParameters();
    virtual ~BaseBFStaticParameters();
    /**
     * @brief dopplerVolMMs PW 采样门的高度的集合
     * @return
     */
    const QVector<float>& dopplerVolMMs() const;
    /**
     * @brief steeringAngles 线阵探头Color模式，ROI 偏转的角度集合
     * @return
     */
    const QVector<float>& steeringAngles() const;
    /**
     * @brief steeringAngleIndexes 线阵探头Color模式，ROI 偏转的下发索引集合
     * @return
     */
    const QVector<int>& steeringAngleIndexes() const;
    /**
     * @brief needleAngles 线阵探头 Super Needle 中角度的集合
     * @return
     */
    const QVector<float>& needleAngles() const;
    /**
     * @brief needleAngleIndexes 线阵探头 Super Needle 下发索引集合
     * @return
     */
    const QVector<int>& needleAngleIndexes() const;
    /**
     * @brief needleAngle2Indexe EBit 机型对应的线阵探头 Super Needle 下发索引集合
     * @return
     */
    const QHash<int, int>& needleAngle2Indexe() const;
    /**
     * @brief mPixelSizeSeces M模式水平方向，每个像素对应的时间值的集合
     * @return
     */
    const QVector<float>& mPixelSizeSeces() const;

    /**
     * @brief m_MSampleRates MVelocity对应的M采样率
     * @return
     */
    const QVector<int>& mSampleRates() const;
    /**
     * @brief realFreqs 真实的频率值集合
     * @return
     */
    const QVector<float>& realFreqs() const;
    /**
     * @brief txValues 发射参数的值的集合
     * @return
     */
    const QVector<float>& txValues() const;
    /**
     * @brief CWDSampleRateValues CWD 模式采样率值的集合
     * @return
     */
    const QVector<double>& CWDSampleRateValues() const;
    /**
     * @brief CWDSampleRateValues CWD speed档值的集合
     * @return
     */
    const QVector<int>& CWDSpeedRates() const;
    /**
     * @brief CWDLines CWD 线数集合
     * @return
     */
    const QVector<QVector<int>>& CWDLines() const;
    /**
     * @brief accCountCoef Dop Accumulate Num 集合
     * @return
     */
    const QVector<double>& accCountCoef() const;
    /**
     * @brief absSteeringAngles Steer 绝对值集合
     * @return
     */
    const QList<int>& absSteeringAngles() const;
    /**
     * @brief mSpeedParas M模式速度参数集合
     * @return
     */
    const QList<int>& mSpeedParas() const;
    /**
     * @brief mdf滤波系数,从文件读取
     * @return
     */
    const QVector<int>& mdfGains() const;
    const QVector<int>& mdfShifts() const;

    const QVector<QString> AFE_InImpedenceText() const;
    const QVector<QString> AFE_LNA_GAINText() const;
    const QVector<QString> AFE_PGA_GAINText() const;
    const QVector<QString> AFE_LNA_BiasText() const;
    const QVector<float> AFE_LPF_FCutOff() const;
    const QVector<float> AFE_HPF_FCutOff() const;
    const QVector<float>& ADC_HPF_CORNER_FREQ() const;
    const QVector<float>& AFE_PGA_CLAMP_LVL() const;
    const QVector<int> AFE_LPF_FCutOffControlTable() const;
    const QVector<int>& AFE_HPF_FCutOffControlTable() const;
    const QVector<int>& ADC_HPF_CORNER_FREQControlTable() const;
    const QVector<int>& AFE_PGA_CLAMP_LVLControlTable() const;
    const QVector<int>& AFE_PGA_HI_FREQ() const;

    const QVector<QString> AFE_CW_HPF_FB_RESText() const;
    const QVector<QString> AFE_CW_InImpedenceText() const;
    const QVector<QString> AFE_CW_LNA_GAINText() const;

    const QVector<double> CWD1stFilterCoefficient() const;
    const QVector<double> CWD2stFilterCoefficient() const;
    const QVector<QVector<double>> CWDAccumulateNumList() const;
    virtual void initParameters();
    virtual void SetCWDAccumulateNumList();
    virtual void SetCWD1stExtractingFactor();
    virtual void SetCWD2stExtractingFactor();
    const QVector<QVector<int>> CWD1stExtractingFactor() const;
    const QVector<QVector<int>> CWD2stExtractingFactor() const;

    const QVector<float>& dopSteeringAngles() const;

private:
    void readMDFCoef();

public:
    static const double ANGLE_PRECISION;
    static const int ULTRASOUND_VELOCITY_MS;
    static const int LPFINVALIDPOS;

protected:
    QVector<float> m_DopplerVolMMs;
    QVector<float> m_SteeringAngles;
    QVector<int> m_SteeringAngleIndexes;
    QVector<float> m_NeedleAngles;
    QVector<int> m_NeedleAngleIndexes;
    QHash<int, int> m_NeedleAngle2Index;
    QVector<float> m_MPixelSizeSeces;
    QVector<int> m_MSampleRates;
    QVector<float> m_RealFreqs;
    QVector<float> m_TxValues;
    QVector<double> m_CWDSampleRateValues;
    QVector<QVector<int>> m_CWDLines;
    QVector<int> m_CWDRate;
    QVector<double> m_AccCountCoef;
    QList<int> m_AbsSteeringAngles;
    QList<int> m_MSpeedParas;
    QVector<int> m_MDFGains;
    QVector<int> m_MDFShifts;

    QVector<QString> m_AFE_InImpedenceText;
    QVector<QString> m_AFE_LNA_GAINText;
    QVector<QString> m_AFE_PGA_GAINText;
    QVector<float> m_AFE_LPF_FCutOff;
    QVector<int> m_AFE_LPF_FCutOffControlTable; // AFE_LPF_FCutOff 控制表参数值
    QVector<float> m_AFE_HPF_FCutOff;
    QVector<int> m_AFE_HPF_FCutOffControlTable; // AFE_HPF_FCutOff 控制表参数值
    QVector<float> m_ADC_HPF_CORNER_FREQ;
    QVector<int> m_ADC_HPF_CORNER_FREQControlTable; // ADC_HPF_CORNER_FREQ 控制表参数值
    QVector<float> m_AFE_PGA_CLAMP_LVL;
    QVector<int> m_AFE_PGA_CLAMP_LVLControlTable; // AFE_PGA_CLAMP_LVL 控制表参数值
    QVector<int> m_AFE_PGA_HI_FREQ;
    QVector<QString> m_AFE_LNA_BiasText;

    QVector<QString> m_AFE_CW_HPF_FB_RESText;
    QVector<QString> m_AFE_CW_InImpedenceText;
    QVector<QString> m_AFE_CW_LNA_GAINText;

    QVector<double> m_CWD1stFilterCoefficient;
    QVector<double> m_CWD2stFilterCoefficient;
    QVector<QVector<double>> m_CWDAccumulateNumList;
    QVector<QVector<int>> m_CWD1stExtractingFactor;
    QVector<QVector<int>> m_CWD2stExtractingFactor;

    QVector<float> m_DopSteeringAngles;
};

#endif // BASEBFSTATICPARAMETERS_H
