#include "zoomboxcontroller.h"
#include "isonoparameters.h"
#include "parameter.h"
#include "bfpnames.h"
#include "bfcoordtransform.h"
#include "physicalgeometrytransform.h"
#include "probeimageregion.h"
#include "probephysicalgeometry.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "bfdepthparameters.h"
#include "bfadfreqparameter.h"
#include "util.h"
#include "realcompare.h"
#include "bfscanareawidthparameter.h"
#include <QSize>
#include <QDebug>

static const int COLOR_ALIGN_LINE = 4;
static const int LIMITCQYZ = 2; // 2023-07-17 Write by AlexWang [bug:65130] 极限最小CQYZ

ZoomBoxController::ZoomBoxController(ISonoParameters* parameters, QObject* parent, bool considerZoomOn)
    : PhysicalGeometryController(parameters, parent, considerZoomOn)
    , m_Transform(new PhysicalGeometryTransform())
    , m_MouseVMovedDis(0)
{
    m_BaseTransform = m_Transform;
}

ZoomBoxController::~ZoomBoxController()
{
    delete m_Transform;
}

void ZoomBoxController::doMove(const QPoint& offset)
{
    m_MouseVMovedDis = 0;
    PhysicalGeometryController::doMove(offset);
}

/**
 * @brief doChangeSize 实现将鼠标移动的逻辑位移，转换成物理位移，由于放大框的高度不能线性调节
 * 这里要计算出累积位移的高度是否超过了可调一级的放大框和现有放大框的高度差，然后再设置给放大
 * 框的 geometry的height
 *
 * @param size
 */
void ZoomBoxController::doChangeSize(const QPoint& size)
{
    m_MouseVMovedDis += size.y();
    QPoint offset = QPoint(size.x(), m_MouseVMovedDis);

    const BFCoordTransform& ct = m_Transform->coordTransform();
    ProbePhysicalSize psize = m_Transform->geometry().size();

    ProbePhysicalPoint sizePt =
        ct.convertOffsetPhysicalPosToPhysics(offset, m_Transform->geometry().center().toPointF());
    ProbePhysicalSize sizeOffset = ProbePhysicalSize(sizePt.x(), sizePt.y());

    if (sizeOffset.width() == 0)
    {
        if (offset.x() > 0)
        {
            sizeOffset.setWidth(2);
        }
        else if (offset.x() < 0)
        {
            sizeOffset.setWidth(-2);
        }
    }

    psize.setWidth(psize.width() + sizeOffset.width());

    ProbeImageRegion region = calOriginalRegion();

    bool add = m_MouseVMovedDis > 0;

    Parameter* p = m_SonoParameters->parameter(BFPNames::ZoomedCQYZStr);
    int min = p->min();
    int max = p->max();

    int curV = p->intValue();
    int newV = curV + (add ? 1 : -1);

    if (newV >= min && newV <= max)
    {
        // 2023-07-17 Write by AlexWang [bug:65130] 限定CQYZ的最小值
        if (newV < LIMITCQYZ)
            newV = LIMITCQYZ;

        qreal curMulti = realZoomMulti();
        qreal newMulti = m_SonoParameters->pIV(BFPNames::CQYZStr) / (qreal)newV;

        qreal curHeight = region.height() / curMulti;
        qreal newHeight = region.height() / newMulti;

        qreal dif = newHeight - curHeight;

        if (RealCompare::IsGreater(qAbs(sizeOffset.height()), qAbs(dif)))
        {
            m_SonoParameters->setPV(BFPNames::ZoomedCQYZStr, newV);
            psize.setHeight(newHeight);
            m_MouseVMovedDis = 0;
        }
    }
    else
    {
        m_MouseVMovedDis = 0;
    }

    m_Transform->setSize(psize);
}

void ZoomBoxController::doScale(bool add)
{
    //    if(m_SonoParameters->parameter(BFPNames::ZoomMultiIndexStr)->multi(!add))
    //    {
    //        ProbeImageRegion region = calOriginalRegion();
    //        ProbePhysicalGeometry g(region);
    //        g.scale(1.0f / realZoomMulti());

    //        m_Transform->setSize(g.size());
    //    }

    m_MouseVMovedDis = 0;
    if (m_SonoParameters->parameter(BFPNames::ZoomedCQYZStr)->multi(!add))
    {
        ProbeImageRegion region = calOriginalRegion();
        ProbePhysicalGeometry g(region);
        g.scale(1.0f / realZoomMulti());

        m_Transform->setSize(g.size());
    }
}

void ZoomBoxController::onActive()
{
    bool zoom = m_SonoParameters->pBV(BFPNames::ZoomSelectStr);
    if (!zoom)
    {
        m_SonoParameters->setPV(BFPNames::ZoomSelectStr, true);
        // 2023-05-16
        // 解决多B状态RegionZoom预备模式下切换激活其他区域导致ROI框重置，当前已激活初始化时使用之前激活区域计算出的ROI框，否则需要重新计算构建初始ROI框
        if (m_isActiveInit)
        {
            onResetParameters();
        }
        else
        {
            m_SonoParameters->setPV(BFPNames::ZoomedCQYZStr,
                                    qRound(m_SonoParameters->pIV(BFPNames::CQYZStr) / (qreal)2.0f));

            // set zoom Geometry middle by zoommulti
            ProbeImageRegion region = calOriginalRegion();
            ProbePhysicalGeometry g(region);
            g.scale(1.0f / realZoomMulti());

            m_Transform->setGeometry(g);
            geometry2SonoParameters();

            m_isActiveInit = true;
        }
    }
}

void ZoomBoxController::calOriginalRegionAndCoordTransform(ProbeImageRegion& region, BFCoordTransform& ct) const
{
    // 2023-03-27 Modify by AlexWang 替换使用超声参数集合的指针作为传入参数的构造函数
    // 2025-07-16 Modify by AlexWang [Task:10658] 支持任意线密度下的局部放大
    ct = BFCoordTransform(m_SonoParameters, m_considerZoomOn, 0, 0, m_RevLineNum);
    region = ProbeImageRegion(m_SonoParameters, m_considerZoomOn);
    limitRegionToBImageSize(region, ct, region.left(), region.right());
}

void ZoomBoxController::onResetParameters()
{
    // 2025-07-16 Modify by AlexWang [Task:10658] 支持任意线密度下的局部放大
    m_RevLineNum = 0;
    if (isSupportAnyDensity())
    {
        updateLineDensityInfo();
    }
    // checking luodebing
    ProbeImageRegion region;
    BFCoordTransform ct;
    calOriginalRegionAndCoordTransform(region, ct);

    // 2023-05-24 Modify by AlexWang
    // 解决进入局部放大状态选取部分区域后图像卡住，限制图像区域的左右边界整除4，沿用BCDGeometryController的方法
    int left = region.left();
    int right = region.right();
    adjustedLeftAndRight(left, right);
    region.setLeft(left);
    region.setRight(right);

    m_Transform->setCoordTransform(ct);
    m_Transform->setRegion(region);

    Parameter* p = m_SonoParameters->parameter(BFPNames::ZoomedCQYZStr);
    int min = p->min();
    int max = p->max();
    int oldV = p->intValue();
    int newV = qBound(min, p->intValue(), max);
    if (newV != oldV)
    {
        p->setValue(newV);
    }
    qreal maxZoomMulti = m_SonoParameters->pIV(BFPNames::CQYZStr) / (qreal)min;
    qreal minZoomMulti = m_SonoParameters->pIV(BFPNames::CQYZStr) / (qreal)max;
    m_Transform->setMinSize(ProbePhysicalSize(region.width() / 10, region.height() / maxZoomMulti));
    m_Transform->setMaxSize(ProbePhysicalSize(region.width(), region.height() / minZoomMulti));

    ProbePhysicalGeometry g(
        m_SonoParameters->pIV(BFPNames::ZoomMidLineStr), m_SonoParameters->pIV(BFPNames::ZoomHalfLinesStr),
        m_SonoParameters->pDV(BFPNames::ZoomMidDepthMMStr), m_SonoParameters->pDV(BFPNames::ZoomHalfDepthMMStr));

    ProbePhysicalGeometry gRegion(region);
    //    gRegion.scale(1.0f / realZoomMulti());
    //    g.setSize(gRegion.size());
    g.setSize(ProbePhysicalSize(g.width(), gRegion.height() / realZoomMulti()));

    m_Transform->setGeometry(g);

    m_MouseVMovedDis = 0;
}

void ZoomBoxController::geometry2SonoParameters()
{
    // 2023-05-24 Modify by AlexWang
    // 解决进入局部放大状态选取部分区域后图像卡住，fpga使用偶数线号处理计算总线数，当startLine和stopLine为奇数线号时将线号设置成偶数
    const ProbePhysicalGeometry& geometry = m_Transform->geometry();
    int midLine = geometry.midX();
    int halfLines = geometry.halfWidth();
    adjustedMidAndHalfLines(midLine, halfLines);

    m_SonoParameters->setPV(BFPNames::ZoomMidLineStr, midLine);
    m_SonoParameters->setPV(BFPNames::ZoomHalfLinesStr, halfLines);
    m_SonoParameters->setPV(BFPNames::ZoomMidDepthMMStr, geometry.midY());
    m_SonoParameters->setPV(BFPNames::ZoomHalfDepthMMStr, geometry.halfHeight());
}

void ZoomBoxController::adjustedLeftAndRight(int& left, int& right)
{
    // 控制limitregion的left and right 整除4，这样可以统一DScanLine和ROI的范围统一
    int mod = left % COLOR_ALIGN_LINE;
    if (mod != 0)
    {
        left += COLOR_ALIGN_LINE - mod;
    }

    mod = right % COLOR_ALIGN_LINE;
    if (mod != 0)
    {
        right -= mod;
    }
}

void ZoomBoxController::adjustedMidAndHalfLines(int& midLine, int& halfLines)
{
    //控制绘制和下发时都保持整除4,FPGA真机测试，ECO、EBit必须满足此条件，QBit在高密度
    //时不需要控制，低密度时，一定要整除2,现在为了软件方便处理，都统一成必须整除4
    int mod = midLine % COLOR_ALIGN_LINE;
    if (mod != 0)
    {
        midLine -= mod;
    }

    mod = halfLines % COLOR_ALIGN_LINE;
    if (mod != 0)
    {
        halfLines -= mod;
    }
    // 如果left小于region的左侧，得调整到region内
    int left = midLine - halfLines;
    if (left < m_BaseTransform->region().left())
    {
        midLine += COLOR_ALIGN_LINE;
    }
}

qreal ZoomBoxController::realZoomMulti() const
{
    //    BFDepthParameters depthP(m_SonoParameters->pIV(BFPNames::CQYZStr),
    //                                 m_SonoParameters->pBV(BFPNames::HalfHeightStr),
    //                                 true,
    //                                 BFDepthParameters::zoomMulti(
    //                                     m_SonoParameters->pIV(BFPNames::ZoomMultiIndexStr),
    //                                     m_SonoParameters->parameter(BFPNames::ZoomMultiIndexStr)->max()));

    BFDepthParameters depthP(m_SonoParameters->pIV(BFPNames::ADFreqMHzStr),
                             m_SonoParameters->pV(BFPNames::ImageSizeStr).toSize().height(),
                             m_SonoParameters->pIV(BFPNames::CQYZStr), m_SonoParameters->pBV(BFPNames::HalfHeightStr),
                             true, m_SonoParameters->pIV(BFPNames::ZoomedCQYZStr));
    depthP.setPixelLen(BFADFreqParameter::pixelLenMM(m_SonoParameters->pIV(BFPNames::ADFreqMHzStr)) /
                       m_SonoParameters->pFV(BFPNames::FixedSWImageZoomCofStr));
    //    depthP.setImageZoomCoef(m_SonoParameters->pDV(BFPNames::ImageZoomCoefStr) / 100.0f);
    return depthP.realZoomMulti();
}
