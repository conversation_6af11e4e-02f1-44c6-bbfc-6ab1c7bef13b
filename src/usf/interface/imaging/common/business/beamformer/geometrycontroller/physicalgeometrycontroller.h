#ifndef PHYSICALGEOMETRYCONTROLLER_H
#define PHYSICALGEOMETRYCONTROLLER_H
#include "usfinterfaceimagingbusiness_global.h"

#include <QObject>
#include <QTimer>
#include <QVariant>
#include <QPoint>

class ISonoParameters;
class IControlTable;
class BaseProbeGeometriesTransform;
class ProbeImageRegion;
class BFCoordTransform;
class IStateManager;
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT PhysicalGeometryController : public QObject
{
    Q_OBJECT
public:
    explicit PhysicalGeometryController(ISonoParameters* parameters, QObject* parent = 0, bool considerZoomOn = true);
    virtual ~PhysicalGeometryController();
    bool isActive() const;
    void setActive(bool value);
    void setControlTable(IControlTable* value);
    void updateTouchTransForm(const bool flag = true);

    virtual void setSonoParameters(ISonoParameters* sonoParameters);
    void setStateManager(IStateManager* value);
    /*! 2023-05-26 Write by <PERSON><PERSON>ang
     * \brief sonoParameters
     * \return
     */
    ISonoParameters* sonoParameters() const;

    enum OPER_SRC
    {
        OPER_SRC_GENERAL,
        OPER_SRC_DOPPLER_AI
    };

    /**
     * @brief move
     *
     * @param offset 鼠标的位移，没有经过上下左右翻转
     */
    virtual void move(const QPoint& offset);

    /**
     * @brief move
     *
     * @param offset 鼠标的位移，没有经过上下左右翻转
     */
    virtual void touchMove(const QPoint& offset);

    /**
     * @brief changeSize
     *
     * @param size 鼠标的位移，不需要上下左右翻转
     */
    virtual void changeSize(const QPoint& size);
    virtual void changeSizeInTouch(const QPoint& size);
    virtual void scale(const QPoint& offset);
    virtual void scale(bool add);
    /**
     * @brief setOperationSource
     *
     * @param source 操作的来源
     */
    virtual void setOperationSource(PhysicalGeometryController::OPER_SRC source);
    virtual void setActiveInit(bool value);

    virtual void resetTouchOffsetCount();

protected:
    virtual void doMove(const QPoint& offset);
    virtual void doTouchMove(const QPoint& offset);
    virtual void doMove(const QPointF& offset);
    virtual void doChangeSize(const QPoint& size);
    virtual void doChangeSizeInTouch(const QPoint& size);
    virtual void doScale(bool add);
    /**
     * @brief scaleAction
     * @param offset
     * @return 1:Action 0:NonAction -1:FlipedAction
     */
    virtual int scaleAction(const QPoint& offset) const;
    QPoint flipVector(const QPoint& offset, bool left, bool up, int rotation);
    QPointF flipVector(const QPointF& offset, bool left, bool up, int rotation);
    virtual void onActive();
    virtual void onClosed();
    virtual void onResetParameters();
    virtual void geometry2SonoParameters();
    virtual void geometry2SonoParametersByTouch();
    virtual void geometry2SonoParametersWithLineDensity();
    virtual void updateTransform(const bool flag = true);
    virtual void calOriginalRegionAndCoordTransform(ProbeImageRegion& region, BFCoordTransform& ct) const;
    virtual void limitRegionToBImageSize(ProbeImageRegion& region, BFCoordTransform& ct, int startLine,
                                         int stopLine) const;
    virtual void limitRegionToImageSize(ProbeImageRegion& region, BFCoordTransform& ct, int startLine, int stopLine,
                                        const QSize& imageSize) const;
    virtual ProbeImageRegion calOriginalRegion() const;
    virtual void connectSignals();
    virtual void disconnectSignals();
    void validate();
    virtual void updateLineDensityInfo();
    virtual qreal imageZoomCoef() const;
    bool isSupportAnyDensity() const;

private:
    void onChanged();
signals:
    void imageShapeUnstable(int ms = -1);
    void imageUnstable(int ms = -1);
    void elastoImageShapeUnstable();
public slots:
    virtual void resetParameters(const QVariant& value = QVariant(), bool changed = true);
    virtual void resetLineDensityParameters(const QVariant& value = QVariant(), bool changed = true);
    /**
     * @brief ReFlash
     * 将Paraneters中设置的参数重新刷新到图形中
     */
    virtual void refresh(); // add by jsj at 2017-10-17
protected slots:
    virtual void sendControlTable();
    virtual void sendControlTableByTouch();

protected:
    ISonoParameters* m_SonoParameters;
    IControlTable* m_ControlTable;
    BaseProbeGeometriesTransform* m_BaseTransform;
    QTimer m_Timer;
    bool m_DelayWork;
    bool m_IsActive;
    OPER_SRC m_operationSource;
    bool m_considerZoomOn;
    bool m_isActiveInit;
    int m_RevLineNum;
    int m_StartLine;
    int m_StopLine;
    QPoint m_TouchMoveOffsetCount;
    IStateManager* m_StateManager;
};

#endif // PHYSICALGEOMETRYCONTROLLER_H
