#ifndef BCDGEOMETRYCONTROLLER_H
#define BCDGEOMETRYCONTROLLER_H
#include "usfinterfaceimagingbusiness_global.h"

#include "physicalgeometrycontroller.h"
class PhysicalGeometryTransform;
class ProbePhysicalGeometry;
class ProbePhysicalSize;
class MuxPNames;
class BFDepthParameters;

class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BCDGeometryController : public PhysicalGeometryController
{
    Q_OBJECT
public:
    explicit BCDGeometryController(ISonoParameters* parameters, QObject* parent = 0, bool considerZoomOn = true);
    ~BCDGeometryController()
    {
    }
    void setRoiTopBorder(int value);
    void setRoiMinWidthFactor(float value);
    void setRoiMaxWidthFactor(float value);
    void setRoiMinHeightFactor(float value);
    void setRoiMaxHeightFactor(float value);
    void setRoiEdgeLines(int value);
    void setGateMinHeightMM(float value);
    void setGateMaxHeightMM(float value);

protected:
    virtual void calOriginalRegionAndCoordTransform(ProbeImageRegion& region, BFCoordTransform& ct) const;
    virtual void geometry2SonoParametersWithLineDensity();
    virtual void getRoiGeometry(ProbePhysicalGeometry& g) const;
    virtual void setRoiGeometry(const ProbePhysicalGeometry& g) const;
    virtual void setRoiMinSize(const ProbePhysicalSize& value);
    virtual void setRoiMaxSize(const ProbePhysicalSize& value);

    virtual void getGateGeometry(ProbePhysicalGeometry& g) const;
    virtual void setGateGeometry(const ProbePhysicalGeometry& g) const;
    virtual void setGateSize(const ProbePhysicalSize& size) const;
    virtual void setGateMinSize(const ProbePhysicalSize& value);
    virtual void setGateMaxSize(const ProbePhysicalSize& value);
    /**
     * @brief gateRegionHeight gate 的范围的高度,BCD时为roi的高度，D时为probe region's height
     * @return
     */
    virtual qreal gateRegionHeight() const;

    void onResetRegionAndCoordTransform(const ProbeImageRegion& region, const BFCoordTransform& ct);
    void onResetGateParameters(const ProbeImageRegion& region);
    void onResetRoiParameters(const ProbeImageRegion& region);
    void changeGateSize(bool add);
    void roiGeometry2SonoParameters(const ProbePhysicalGeometry& roi);
    void gateGeometry2SonoParameters(const ProbePhysicalGeometry& gate);
    void sendRoiParameters(const ProbePhysicalGeometry& roi);
    void sendGateParameters(const ProbePhysicalGeometry& gate);

    void adjustedLeftAndRight(int& left, int& right);
    void adjustedMidAndHalfLines(int& midLine, int& halfLines);
    virtual void updateLineDensityInfo();

private:
    /**
     * @brief BC坐标系之间的线号互转
     * @param lineNumber 源坐标系下的线号
     * @param depthMM 转换时使用的深度值（毫米）
     * @param fromCToB 转换方向：true表示从C坐标系转到B坐标系，false表示从B坐标系转到C坐标系
     * @return 目标坐标系下的线号
     */
    int convertLineNumberBetweenBCCoordinate(int lineNumber, qreal depthMM, bool fromCToB) const;
    BFDepthParameters& bfDepthParameters() const;
    bool isSupportAnyDensity();
signals:
    void bcdGeometryChanged();
public slots:
protected:
    PhysicalGeometryTransform* m_GeometryTransform;
    int m_RoiTopBorder;
    float m_RoiMinWidthFactor;
    float m_RoiMaxWidthFactor;
    float m_RoiMinHeightFactor;
    float m_RoiMaxHeightFactor;
    int m_RoiEdgeLines;
    float m_GateMinHeightMM;
    float m_GateMaxHeightMM;
    MuxPNames* m_ROIPNames;
    MuxPNames* m_DopGatePNames;
    int m_RoiStableTime;
};

#endif // BCDGEOMETRYCONTROLLER_H
