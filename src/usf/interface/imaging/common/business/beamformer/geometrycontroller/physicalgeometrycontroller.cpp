#include "physicalgeometrycontroller.h"
#include "isonoparameters.h"
#include "bfpnames.h"
#include "parameter.h"
#include "baseprobegeometriestransform.h"
#include "bfcoordtransform.h"
#include "probeimageregion.h"
#include "probedataset.h"
#include "probeparameters.h"
#include <QSize>
#include <QVariant>
#include <QDebug>
#include <QTransform>
#include "formula.h"
#include "sonoparameters/sonoparameters.h"
#include "bfscanareawidthparameter.h"
#include "panzoomboxcontroller.h"
#include "roicontroller.h"

enum
{
    FlipedAction = -1,
    NonAction = 0,
    Action = 1
};

PhysicalGeometryController::PhysicalGeometryController(ISonoParameters* parameters, QObject* parent,
                                                       bool considerZoomOn)
    : QObject(parent)
    , m_SonoParameters(parameters)
    , m_ControlTable(NULL)
    , m_BaseTransform(NULL)
    , m_DelayWork(true)
    , m_IsActive(false)
    , m_considerZoomOn(considerZoomOn)
    , m_isActiveInit(false)
    , m_TouchMoveOffsetCount(0, 0)
    , m_StateManager(NULL)
{
    updateLineDensityInfo();

    m_Timer.setInterval(50);
    m_Timer.setSingleShot(true);
    connect(&m_Timer, SIGNAL(timeout()), this, SLOT(sendControlTable()));
}

PhysicalGeometryController::~PhysicalGeometryController()
{
}

bool PhysicalGeometryController::isActive() const
{
    return m_IsActive;
}

void PhysicalGeometryController::setActive(bool value)
{
    if (m_IsActive != value)
    {
        m_IsActive = value;
        if (m_IsActive)
        {
            connectSignals();

            onActive();
            resetParameters();
        }
        else
        {
            if (m_Timer.isActive())
            {
                //控制结束时，要关闭timer，否则timer会导致发送控制表
                m_Timer.stop();
            }

            onClosed();

            disconnectSignals();
        }
    }
}

void PhysicalGeometryController::setControlTable(IControlTable* value)
{
    m_ControlTable = value;
}

void PhysicalGeometryController::updateTouchTransForm(const bool flag)
{
    updateTransform(flag);
}

void PhysicalGeometryController::setSonoParameters(ISonoParameters* sonoParameters)
{
}

void PhysicalGeometryController::setStateManager(IStateManager* value)
{
    //此成员，在子类中有用到
    m_StateManager = value;
}

ISonoParameters* PhysicalGeometryController::sonoParameters() const
{
    return m_SonoParameters;
}

void PhysicalGeometryController::move(const QPoint& offset)
{
    doMove(flipVector(offset, m_SonoParameters->pBV(BFPNames::LeftStr), m_SonoParameters->pBV(BFPNames::UpStr),
                      m_SonoParameters->pIV(BFPNames::RotationStr)));
    onChanged();
}

void PhysicalGeometryController::touchMove(const QPoint& offset)
{
    doTouchMove(flipVector(offset, m_SonoParameters->pBV(BFPNames::LeftStr), m_SonoParameters->pBV(BFPNames::UpStr),
                           m_SonoParameters->pIV(BFPNames::RotationStr)));
    onChanged();
}

void PhysicalGeometryController::changeSize(const QPoint& size)
{
    doChangeSize(flipVector(size, true, true, m_SonoParameters->pIV(BFPNames::RotationStr)));
    onChanged();
}

void PhysicalGeometryController::changeSizeInTouch(const QPoint& size)
{
    doChangeSizeInTouch(flipVector(size, true, true, m_SonoParameters->pIV(BFPNames::RotationStr)));
    onChanged();
}

void PhysicalGeometryController::scale(const QPoint& offset)
{
    int ret = scaleAction(flipVector(offset, true, true, m_SonoParameters->pIV(BFPNames::RotationStr)));
    if (ret == Action)
    {
        scale(true);
    }
    else if (ret == FlipedAction)
    {
        scale(false);
    }
}

void PhysicalGeometryController::scale(bool add)
{
    doScale(add);
    onChanged();
}

void PhysicalGeometryController::setOperationSource(PhysicalGeometryController::OPER_SRC source)
{
    if (source != m_operationSource)
    {
        m_BaseTransform->setOperationSource(static_cast<int>(source));
        m_operationSource = source;
    }
}

void PhysicalGeometryController::setActiveInit(bool value)
{
    // 2023-05-16 解决多B状态RegionZoom预备模式下切换激活其他区域导致ROI框重置，提供设置激活初始化参数接口
    m_isActiveInit = value;
}

void PhysicalGeometryController::resetTouchOffsetCount()
{
    m_TouchMoveOffsetCount = QPoint(0, 0);
}

void PhysicalGeometryController::doMove(const QPoint& offset)
{
    if (m_BaseTransform != NULL)
    {
        //所有的图形移动都相对于region的最底下的中点位置移动，这样能保证图形在哪里移动，
        //物理坐标位移的线数和深度的位移保持不变，不会出现上边快，下边慢的问题
        const ProbeImageRegion& region = m_BaseTransform->region();

        m_BaseTransform->move(ProbePhysicalPoint(region.center().x(), region.bottom()), offset);
    }
}

void PhysicalGeometryController::doTouchMove(const QPoint& offset)
{
    if (m_BaseTransform != NULL)
    {
        m_TouchMoveOffsetCount += offset;
        //所有的图形移动都相对于region的最底下的中点位置移动，这样能保证图形在哪里移动，
        //物理坐标位移的线数和深度的位移保持不变，不会出现上边快，下边慢的问题
        const ProbeImageRegion& region = m_BaseTransform->region();
        bool isChanged = false;
        m_BaseTransform->touchMove(ProbePhysicalPoint(region.center().x(), region.bottom()),
                                   QPoint(m_TouchMoveOffsetCount.x(), offset.y()), isChanged);
        if (isChanged)
        {
            m_TouchMoveOffsetCount = QPoint(0, 0);
        }
    }
}

void PhysicalGeometryController::doMove(const QPointF& offset)
{
    if (m_BaseTransform != NULL)
    {
        //所有的图形移动都相对于region的最底下的中点位置移动，这样能保证图形在哪里移动，
        //物理坐标位移的线数和深度的位移保持不变，不会出现上边快，下边慢的问题
        const ProbeImageRegion& region = m_BaseTransform->region();

        m_BaseTransform->move(ProbePhysicalPoint(region.center().x(), region.bottom()), offset);
    }
}

void PhysicalGeometryController::doChangeSize(const QPoint& size)
{
}

void PhysicalGeometryController::doChangeSizeInTouch(const QPoint& size)
{
}

void PhysicalGeometryController::doScale(bool add)
{
}

int PhysicalGeometryController::scaleAction(const QPoint& offset) const
{
    if (offset.y() > 0)
    {
        return Action;
    }
    else if (offset.y() < 0)
    {
        return FlipedAction;
    }
    else
    {
        return NonAction;
    }
}

QPoint PhysicalGeometryController::flipVector(const QPoint& offset, bool left, bool up, int rotation)
{
    return Formula::flipAndCounterclockwiseRotateTransform(up, left, rotation).map(offset);
}

QPointF PhysicalGeometryController::flipVector(const QPointF& offset, bool left, bool up, int rotation)
{
    return Formula::flipAndCounterclockwiseRotateTransform(up, left, rotation).map(offset);
}

void PhysicalGeometryController::onActive()
{
}

void PhysicalGeometryController::onClosed()
{
}

void PhysicalGeometryController::onResetParameters()
{
}

void PhysicalGeometryController::geometry2SonoParameters()
{
}

void PhysicalGeometryController::geometry2SonoParametersByTouch()
{
}

void PhysicalGeometryController::geometry2SonoParametersWithLineDensity()
{
}

void PhysicalGeometryController::updateTransform(const bool flag)
{
}

void PhysicalGeometryController::calOriginalRegionAndCoordTransform(ProbeImageRegion& region,
                                                                    BFCoordTransform& ct) const
{
    // 2023-04-26 Modify by AlexWang
    // 解决放大状态下C模式下的ROI框无法移动到图像区域底部，部分依赖放大状态进行参数的计算，如进入放大状态后进入C模式，此时探头图像区域的计算依赖放大状态
    ProbeParameters pParameters(m_SonoParameters, m_considerZoomOn);

    //    ct = BFCoordTransform(probe,
    //                        m_SonoParameters->pDV(BFPNames::StartDepthMMStr),
    //                        m_SonoParameters->pDV(BFPNames::PixelSizeMMStr));
    // 2023-03-27 Modify by AlexWang 替换使用超声参数集合的指针作为传入参数的构造函数
    ct = BFCoordTransform(m_SonoParameters, m_considerZoomOn, m_StartLine, m_StopLine, m_RevLineNum);

    region = ProbeImageRegion(pParameters, m_SonoParameters);

    limitRegionToBImageSize(region, ct, m_StartLine, m_StopLine);
}

void PhysicalGeometryController::limitRegionToBImageSize(ProbeImageRegion& region, BFCoordTransform& ct, int startLine,
                                                         int stopLine) const
{
    BFDSCScanAreaWidthParameter scanAreaWidth(dynamic_cast<SonoParameters*>(m_SonoParameters));

    // 2023-08-15 Modify by AlexWang 使用LayoutBImageSize的高度,
    // 当ImageZoomCoef大于100时对尺寸进行放大，当ImageZoomCoef小于等于100时不放大尺寸
    int imageHeight = m_SonoParameters->pV(BFPNames::LayoutBImageSizeStr).toSize().height();
    QSize size(scanAreaWidth.dscValidImageWidth(), imageHeight);
    if (m_SonoParameters->pIV(BFPNames::ImageZoomCoefStr) > 100)
        limitRegionToImageSize(region, ct, startLine, stopLine, size * imageZoomCoef());
    else
        limitRegionToImageSize(region, ct, startLine, stopLine, size);
}

void PhysicalGeometryController::limitRegionToImageSize(ProbeImageRegion& region, BFCoordTransform& ct, int startLine,
                                                        int stopLine, const QSize& imageSize) const
{
    qreal line, depthMM;
    ct.convertPtToPhysics(QPoint(-(imageSize.width() / 2), 0), line, depthMM);

    if (line > startLine)
    {
        region.setLeft(line + 1); // line 是小数，左边的线需要进1计算
    }

    ct.convertPtToPhysics(QPoint(imageSize.width() / 2 - 1, 0), line, depthMM); //右侧坐标是halfWidth - 1
    if (line < stopLine)
    {
        region.setRight((int)line); // line 是小数，右边的线直接取整计算
    }

    // 90度、270度旋转后，region深度需要限制在 BImageSize 的底部深度范围
    ct.convertPtToPhysics(QPoint(0, imageSize.height()), line, depthMM);
    if (region.bottom() > depthMM)
    {
        region.setBottom(depthMM);
    }
}

ProbeImageRegion PhysicalGeometryController::calOriginalRegion() const
{
    return ProbeImageRegion(m_SonoParameters);
}

void PhysicalGeometryController::connectSignals()
{
    connect(m_SonoParameters->parameter(BFPNames::CQYZStr), SIGNAL(valueChanged(QVariant, bool)), this,
            SLOT(resetParameters(QVariant, bool)));
    connect(m_SonoParameters->parameter(BFPNames::StartDepthMMStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(resetParameters()));
    connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(resetParameters()));
    connect(m_SonoParameters->parameter(BFPNames::StartLineStr), SIGNAL(valueChanged(QVariant, bool)), this,
            SLOT(resetParameters(QVariant, bool)));
    connect(m_SonoParameters->parameter(BFPNames::StopLineStr), SIGNAL(valueChanged(QVariant, bool)), this,
            SLOT(resetParameters(QVariant, bool)));
    connect(m_SonoParameters->parameter(BFPNames::ImageZoomCoefStr), SIGNAL(valueChanged(QVariant, bool)), this,
            SLOT(resetParameters(QVariant, bool)));
    connect(m_SonoParameters->parameter(BFPNames::RotationStr), SIGNAL(valueChanged(QVariant, bool)), this,
            SLOT(resetParameters(QVariant, bool)));
    connect(m_SonoParameters->parameter(BFPNames::StartScanLineColorStr), SIGNAL(valueChanged(QVariant, bool)), this,
            SLOT(resetLineDensityParameters(QVariant, bool)));
    connect(m_SonoParameters->parameter(BFPNames::StopScanLineColorStr), SIGNAL(valueChanged(QVariant, bool)), this,
            SLOT(resetLineDensityParameters(QVariant, bool)));
    connect(m_SonoParameters->parameter(BFPNames::ColorLineDensityStr), SIGNAL(valueChanged(QVariant, bool)), this,
            SLOT(resetLineDensityParameters(QVariant, bool)));
    //    PhysicalGeometryController* base = new PanZoomBoxController(m_SonoParameters);
    //    PanZoomBoxController* son = dynamic_cast<PanZoomBoxController*>(base);
    //    son->test();
    //    PanZoomBoxController* son =
}

void PhysicalGeometryController::disconnectSignals()
{
    disconnect(m_SonoParameters->parameter(BFPNames::CQYZStr), SIGNAL(valueChanged(QVariant, bool)), this,
               SLOT(resetParameters(QVariant, bool)));
    disconnect(m_SonoParameters->parameter(BFPNames::StartDepthMMStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(resetParameters()));
    disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(resetParameters()));
    disconnect(m_SonoParameters->parameter(BFPNames::StartLineStr), SIGNAL(valueChanged(QVariant, bool)), this,
               SLOT(resetParameters(QVariant, bool)));
    disconnect(m_SonoParameters->parameter(BFPNames::StopLineStr), SIGNAL(valueChanged(QVariant, bool)), this,
               SLOT(resetParameters(QVariant, bool)));
    disconnect(m_SonoParameters->parameter(BFPNames::ImageZoomCoefStr), SIGNAL(valueChanged(QVariant, bool)), this,
               SLOT(resetParameters(QVariant, bool)));
    disconnect(m_SonoParameters->parameter(BFPNames::RotationStr), SIGNAL(valueChanged(QVariant, bool)), this,
               SLOT(resetParameters(QVariant, bool)));
    disconnect(m_SonoParameters->parameter(BFPNames::StartScanLineColorStr), SIGNAL(valueChanged(QVariant, bool)), this,
               SLOT(resetLineDensityParameters(QVariant, bool)));
    disconnect(m_SonoParameters->parameter(BFPNames::StopScanLineColorStr), SIGNAL(valueChanged(QVariant, bool)), this,
               SLOT(resetLineDensityParameters(QVariant, bool)));
    disconnect(m_SonoParameters->parameter(BFPNames::ColorLineDensityStr), SIGNAL(valueChanged(QVariant, bool)), this,
               SLOT(resetLineDensityParameters(QVariant, bool)));
}

void PhysicalGeometryController::onChanged()
{
    geometry2SonoParameters();
    geometry2SonoParametersWithLineDensity();
    if (m_DelayWork)
    {
        if (m_Timer.isActive())
        {
            m_Timer.stop();
        }
        m_Timer.start();
    }
    else
    {
        sendControlTable();
    }
}

void PhysicalGeometryController::validate()
{
    if (m_BaseTransform != NULL)
    {
        m_BaseTransform->validate();
        geometry2SonoParameters();
        sendControlTable();
    }
}

void PhysicalGeometryController::updateLineDensityInfo()
{
    m_StartLine = m_SonoParameters->pIV(BFPNames::StartLineStr);
    m_StopLine = m_SonoParameters->pIV(BFPNames::StopLineStr);
    m_RevLineNum = m_SonoParameters->pIV(BFPNames::B_RX_LNUMStr);
}

qreal PhysicalGeometryController::imageZoomCoef() const
{
    if (NULL == m_SonoParameters)
    {
        return 1.0f;
    }

    return (m_SonoParameters->pFV(BFPNames::ImageZoomCoefStr)) / 100.0f;
}

bool PhysicalGeometryController::isSupportAnyDensity() const
{
    return ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity, false).toBool();
}

void PhysicalGeometryController::resetParameters(const QVariant& value, bool changed)
{
    if (m_IsActive && changed)
    {
        Parameter* p = qobject_cast<Parameter*>(sender());
        if (p != NULL)
        {
            qDebug() << this->metaObject()->className() << "resetParameters" << p->name() << p->value();
        }
        onResetParameters();
        validate();
    }
}

void PhysicalGeometryController::resetLineDensityParameters(const QVariant& value, bool changed)
{
    Parameter* p = qobject_cast<Parameter*>(sender());
    if (m_IsActive && changed)
    {
        if (p != NULL)
        {
            qDebug() << this->metaObject()->className() << "resetParameters" << p->name() << p->value();
        }
        onResetParameters();
        if (m_BaseTransform != NULL)
        {
            m_BaseTransform->validate();
            sendControlTable();
        }
    }
}

void PhysicalGeometryController::sendControlTable()
{
}

void PhysicalGeometryController::sendControlTableByTouch()
{
}
/**
 * @brief PhysicalGeometryController::reflash
 * 将Parameters中的值刷新到物理图形界面中
 */
void PhysicalGeometryController::refresh()
{
    sendControlTable();
    onResetParameters();
    validate();
}
