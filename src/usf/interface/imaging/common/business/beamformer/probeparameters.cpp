#include "probeparameters.h"
#include "bfdepthparameters.h"
#include "bfpnames.h"
#include "bfscanwidthparameter.h"
#include "isonoparameters.h"
#include "logger.h"
#include "probedataset.h"
#include "usglobal.h"
#include "util.h"
#include "setting/setting.h"

ProbeParameters::ProbeParameters(const ProbeDataInfo& probe, qreal startDepthMM, qreal pixelSizeMM, bool zoomOn,
                                 int startLine, int stopLine, qreal lineSpacingMM, qreal angleSpacingRad,
                                 bool DSCCenterLine)
    : m_Probe(probe)
    , m_StartDepthMM(startDepthMM)
    , m_PixelSizeMM(pixelSizeMM)
    , m_ZoomOn(zoomOn)
    , m_StartLine(startLine)
    , m_StopLine(stopLine)
    , m_lineSpacingMM(lineSpacingMM)
    , m_angleSpacingRad(angleSpacingRad)
    , m_DSCCenterLine(DSCCenterLine)
{
    m_TotalLines = m_Probe.WaferNum * 2;
}

ProbeParameters::ProbeParameters(const ISonoParameters* sonoParameters, bool considerZoomOn)
    : m_Probe(ProbeDataSet::instance().getProbe(sonoParameters->pIV(BFPNames::ProbeIdStr)))
    , m_StartDepthMM(sonoParameters->pDV(BFPNames::StartDepthMMStr))
    , m_PixelSizeMM(sonoParameters->pDV(BFPNames::PixelSizeMMStr))
    , m_ZoomOn(sonoParameters->pBV(BFPNames::ZoomOnStr))
    , m_StartLine(sonoParameters->pIV(BFPNames::StartLineStr))
    , m_StopLine(sonoParameters->pIV(BFPNames::StopLineStr))
    , m_lineSpacingMM(sonoParameters->pDV(BFPNames::LineSpacingMMStr))
    , m_angleSpacingRad(sonoParameters->pDV(BFPNames::AngleSpacingRadStr))
    , m_DSCCenterLine(sonoParameters->pBV(BFPNames::DSCCenterLineStr))
{
    m_TotalLines = m_Probe.WaferNum * 2;
    // 2023-04-26 Write by AlexWang RegionZoom和PanZoom的导航区不考虑放大状态
    if (!considerZoomOn)
    {
        m_ZoomOn = false;
        m_StartDepthMM = 0.0f;
        if (sonoParameters->pBV(BFPNames::IsScrollStr))
            m_StartDepthMM = sonoParameters->pDV(BFPNames::ScrollDepthMMStr);

        // 2023-05-23 Write by AlexWang
        // 不受局部放大因素影响的区域，需要通过BFDepthParameters对象计算出正确的PixelSizeMM值，通过BFScanWidthParameter对象计算StartLine和StopLine
        BFDepthParameters depthP(sonoParameters, considerZoomOn);
        m_PixelSizeMM = depthP.pixelSizeMM();
        m_PixelSizeMM /= sonoParameters->pDV(BFPNames::RenderImageZoomCofStr);
        // 2025-07-16 Modify by AlexWang
        // [Task:10658] 支持任意线密度下的局部放大时，计算整个探头扫查区域的起始线和终止线
        BFScanWidthParameter scanWidthP(m_Probe, sonoParameters->pIV(BFPNames::ScanWidthStr),
                                        ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity).toBool(),
                                        sonoParameters->pIV(BFPNames::B_RX_LNUMStr));
        m_StartLine = scanWidthP.startLine();
        m_StopLine = scanWidthP.stopLine();
    }
}

int ProbeParameters::lines() const
{
    return m_TotalLines;
}

int ProbeParameters::actualLines() const
{
    if (m_ZoomOn)
        return m_StopLine - m_StartLine + 1;
    return lines();
}

int ProbeParameters::validLines() const
{
    int noUseLine = m_Probe.NoUseLine;
    //基元检测功能开启，配置文件中未设置noUseLine,未设置WaferNumDiff,则 noUseLine赋为0
    if (Setting::instance().defaults().isElementTestOn() &&
        (m_Probe.WaferNumDiff <= 0 || m_Probe.WaferNumDiff >= m_Probe.WaferNum))
    {
        noUseLine = 0;
    }

    return m_TotalLines - noUseLine * 2;
}

void ProbeParameters::setTotalLines(int totalLines)
{
    m_TotalLines = totalLines;
}

int ProbeParameters::maxLineNo() const
{
    return lines() - 1;
}

int ProbeParameters::middleLineNo() const
{
    return m_TotalLines / 2;
}

int ProbeParameters::middleLineNo(int startLine, int stopLine)
{
    return (startLine + stopLine + 1) / 2;
}

int ProbeParameters::lines(int startLine, int stopLine, bool highDensity)
{
    return (stopLine - startLine + lineInterval(highDensity)) / lineInterval(highDensity);
}

int ProbeParameters::lines(int startLine, int stopLine, int highDensity)
{
    int lineNum = 0;
    int lineInt = lineInterval(highDensity);
    if (isSupportAnyDensity())
    {
        lineNum = (stopLine - startLine + lineInt) / lineInt;
    }
    else
    {
        switch (highDensity)
        {
        case Density::HighDensity:
            lineNum = (stopLine - startLine + lineInt) * 2;
            break;
        default:
            lineNum = (stopLine - startLine + lineInt) / lineInt;
            break;
        }
    }
    return lineNum;
}

int ProbeParameters::lineInterval(bool highDensity)
{
    if (isSupportAnyDensity())
    {
        return 1;
    }
    else
    {
        return highDensity ? 1 : 2;
    }
}

int ProbeParameters::lineInterval(int highDensity)
{
    int interval = 1;
    if (isSupportAnyDensity())
    {
        interval = 1;
    }
    else
    {
        switch (highDensity)
        {
        case Density::LowDensity:
            interval = 2;
            break;
        default:
            interval = 1;
        }
    }
    return interval;
}

int ProbeParameters::stopLine(int startLine, int lines, int lineInterval)
{
    return lines * lineInterval + startLine - 1;
}

float ProbeParameters::radiusMM() const
{
    return m_Probe.WaferRadius + m_StartDepthMM;
}

qreal ProbeParameters::perpendicularDisMM() const
{
    if (!m_Probe.IsLinear)
    {
        return (qreal)(radiusMM() * qCos(probeAngleRadEx(m_ZoomOn, m_StartLine, m_StopLine) / 2));
    }
    else
    {
        return 0.0f;
    }
}

qreal ProbeParameters::perpendicularDisMMEx(bool zoomOn, int startLine, int stopLine) const
{
    if (!m_Probe.IsLinear)
    {
        return (qreal)(radiusMM() * qCos(probeAngleRadEx(zoomOn, startLine, stopLine) / 2));
    }
    else
    {
        return 0.0f;
    }
}

qreal ProbeParameters::arcFloorDisMM() const
{
    if (!m_Probe.IsLinear)
    {
        return (qreal)(radiusMM() - perpendicularDisMM());
    }
    else
    {
        return 0.0f;
    }
}

qreal ProbeParameters::bottomDepthMM(qreal imageHeightMM) const
{
    return m_StartDepthMM + imageHeightMM - arcFloorDisMM();
}

qreal ProbeParameters::imageDepthMM(qreal imageHeightMM) const
{
    // 2023-06-16 Write by AlexWang [bug:65077] 线阵在局部放大状态计算图像高度时要减去StartDetphMM
    qreal delta = 0.0f;
    if (m_Probe.IsLinear)
        delta = m_StartDepthMM;
    return bottomDepthMM(imageHeightMM) - delta;
}

template <>
double ProbeParameters::probeLineInterval<double>(bool useZoomCoefFactor, int zoomCoefPercent,
                                                  bool useHighDensityFactor, bool highDensity) const
{
    Q_ASSERT_X(m_PixelSizeMM > 0.0f, "probeLineInterval(int)", "PixelSizeMM must > 0");
    //线间距
    qreal interval =
        highDensityFactor(useHighDensityFactor, highDensity) * 128.0F /
        (m_Probe.WaferLength / pixelSizeMMZoomCoefFactor(m_PixelSizeMM, useZoomCoefFactor, zoomCoefPercent));

    return interval;
}
template <>
int ProbeParameters::probeLineInterval<int>(bool useZoomCoefFactor, int zoomCoefPercent, bool useHighDensityFactor,
                                            bool highDensity) const
{
    Q_ASSERT_X(m_PixelSizeMM > 0.0f, "probeLineInterval(qreal)", "PixelSizeMM must > 0");
    //线间距
    qreal interval =
        highDensityFactor(useHighDensityFactor, highDensity) * 128.0F /
        (m_Probe.WaferLength / pixelSizeMMZoomCoefFactor(m_PixelSizeMM, useZoomCoefFactor, zoomCoefPercent));

    if (!useHighDensityFactor)
    {
        return Formula::real2Even(interval);
    }
    else
    {
        return qRound(interval);
    }
}

template <typename T>
T ProbeParameters::probeLineInterval(bool useZoomCoefFactor, int zoomCoefPercent, bool useHighDensityFactor,
                                     bool highDensity) const
{
    Q_ASSERT_X(m_PixelSizeMM > 0.0f, "probeLineInterval(qreal)", "PixelSizeMM must > 0");
    //线间距
    qreal interval =
        highDensityFactor(useHighDensityFactor, highDensity) * 128.0F /
        (m_Probe.WaferLength / pixelSizeMMZoomCoefFactor(m_PixelSizeMM, useZoomCoefFactor, zoomCoefPercent));

    if (!useHighDensityFactor)
    {
        return Formula::real2Even(interval);
    }
    else
    {
        return qRound(interval);
    }
}

int ProbeParameters::probeLineIntervals(bool useZoomCoefFactor, int zoomCoefPercent, bool useHighDensityFactor,
                                        int highDensity) const
{
    Q_ASSERT_X(m_PixelSizeMM > 0.0f, "probeLineInterval", "PixelSizeMM must > 0");
    //线间距
    qreal interval =
        highDensityFactor(useHighDensityFactor, highDensity) * 128.0F /
        (m_Probe.WaferLength / pixelSizeMMZoomCoefFactor(m_PixelSizeMM, useZoomCoefFactor, zoomCoefPercent));

    if (!useHighDensityFactor)
    {
        return Formula::real2Even(interval);
    }
    else
    {
        return qRound(interval);
    }
}

template <> qreal ProbeParameters::probeLineSpacingMM() const
{
    if (isSupportAnyDensity())
    {
        return ((qreal)m_Probe.WaferLength * m_Probe.WaferNum) / m_TotalLines;
    }
    else
    {
        //此处计算时，默认都考虑受zoomcoef影响，因为m_PixelSizeMM是随zoomcoef变化而变化的
        //严格设计的话：ECO不随zoomcoef影响，EBit、QBit是受zoomcoef影响的，但通过测试，
        //直接使用随zoomcoef影响的PixelSizeMM计算，ECO也没有出现大的误差，所以统一设计
        qreal interval = probeLineInterval<int>(true);
        Q_ASSERT_X(m_PixelSizeMM > 0.0f, "probeLineSpacingMM", "PixelSizeMM must > 0");
        // 此处不考虑老版本的ECO1，直接使用256.0F
        if (!m_Probe.IsMEProbe)
        {
            return 256.0F * m_PixelSizeMM / (qreal)interval;
        }
        else
        {
            return m_Probe.WaferLength;
        }
    }
}

template <typename T> qreal ProbeParameters::probeLineSpacingMM() const
{
    if (isSupportAnyDensity())
    {
        return ((qreal)m_Probe.WaferLength * m_Probe.WaferNum) / m_TotalLines;
    }
    else
    {
        //此处计算时，默认都考虑受zoomcoef影响，因为m_PixelSizeMM是随zoomcoef变化而变化的
        //严格设计的话：ECO不随zoomcoef影响，EBit、QBit是受zoomcoef影响的，但通过测试，
        //直接使用随zoomcoef影响的PixelSizeMM计算，ECO也没有出现大的误差，所以统一设计
        qreal interval = probeLineInterval<T>(true);
        Q_ASSERT_X(m_PixelSizeMM > 0.0f, "probeLineSpacingMM", "PixelSizeMM must > 0");
        // 此处不考虑老版本的ECO1，直接使用256.0F
        if (!m_Probe.IsMEProbe)
        {
            return 256.0F * m_PixelSizeMM / (qreal)interval;
        }
        else
        {
            return m_Probe.WaferLength;
        }
    }
}
template <> qreal ProbeParameters::probeLineSpacingMM<qreal>() const
{
    if (isSupportAnyDensity())
    {
        return ((qreal)m_Probe.WaferLength * m_Probe.WaferNum) / m_TotalLines;
    }
    else
    {
        //此处计算时，默认都考虑受zoomcoef影响，因为m_PixelSizeMM是随zoomcoef变化而变化的
        //严格设计的话：ECO不随zoomcoef影响，EBit、QBit是受zoomcoef影响的，但通过测试，
        //直接使用随zoomcoef影响的PixelSizeMM计算，ECO也没有出现大的误差，所以统一设计
        qreal interval = probeLineInterval<qreal>(true);
        Q_ASSERT_X(m_PixelSizeMM > 0.0f, "probeLineSpacingMM", "PixelSizeMM must > 0");
        // 此处不考虑老版本的ECO1，直接使用256.0F
        if (!m_Probe.IsMEProbe)
        {
            return 256.0F * m_PixelSizeMM / (qreal)interval;
        }
        else
        {
            return m_Probe.WaferLength;
        }
    }
}

qreal ProbeParameters::probeWidthMM() const
{
    if (m_Probe.IsLinear)
    {
        if (isSupportAnyDensity())
        {
            return ((qreal)m_Probe.WaferNum * m_Probe.WaferLength);
        }
        else
        {
            return (m_Probe.WaferNum * probeLineSpacingMM<qreal>());
        }
    }
    else
    {
        return 0.0F;
    }
}

qreal ProbeParameters::probeWidthMM(qreal lineSpacingMM, bool isNeedReal) const
{
    if (m_Probe.IsLinear)
    {
        if (isNeedReal)
        {
            if (isSupportAnyDensity())
            {
                return ((qreal)m_Probe.WaferNum * m_Probe.WaferLength);
            }
            else
            {
                return (m_Probe.WaferNum * probeLineSpacingMM<qreal>());
            }
        }
        else
        {
            return (m_Probe.WaferNum * lineSpacingMM);
        }
    }
    else
    {
        return 0.0F;
    }
}

qreal ProbeParameters::probeAngleRad(bool isNeedReal) const
{
    //如果探头为凸阵，并且m_Probe.WaferRadius=0，则认定为相控阵探头，
    //相控阵探头的角度=m_Probe.WaferNum * m_Probe.WaferLength / 1.0
    if (!m_Probe.IsLinear)
    {
        if (isSupportAnyDensity())
        {
            if (m_Probe.IsPhasedArray)
            {
                return m_Probe.PhasedAngle * M_PI / 180; //  支持任意线密度机型的相控阵探头开角
            }
            else
            {
                return m_TotalLines * probeAngleSpacingRad();
            }
        }
        else
        {
            if (!isNeedReal)
            {
                return (qreal)(m_Probe.WaferNum * probeAngleSpacingRad());
            }
            else
            {
                return (qreal)(m_Probe.WaferNum * probeAngleSpacingRad<qreal>());
            }
        }
    }
    else
    {
        return 0.0F;
    }
}

qreal ProbeParameters::probeAngleRadEx(bool zoomOn, int startLine, int stopLine) const
{
    if (zoomOn)
    {
        return probeAngleRad(true) * (stopLine - startLine + 1) / lines();
    }
    else
    {
        return probeAngleRad(true);
    }
}

qreal ProbeParameters::convexProbeLineAngleRad(qreal lineNo, ZeroAngleLine zeroAngleLine) const
{

    if (!m_Probe.IsLinear)
    {
        qreal line0Angle = 0;

        if (zeroAngleLine == LeftHorizontalLine)
        {
            line0Angle = M_PI_2 - probeAngleRad(true) / 2;
        }
        else if (zeroAngleLine == CenterLine)
        {
            line0Angle = -probeAngleRad(true) / 2;
        }

        return line0Angle + probeAngleRad(true) * lineNo / lines();
    }
    else
    {
        return 0.0F;
    }
}

qreal ProbeParameters::convexProbeLineAngleRadEx(qreal lineNo, bool zoomOn, int startLine, int stopLine,
                                                 ProbeParameters::ZeroAngleLine zeroAngleLine) const
{
    // 2023-04-19 Write by AlexWang
    // probeAngleRadEx中通过起始线与终止线区间内的线数在探头总线数中的占比计算出弧度angleRad
    //非Zoom状态下lineNumber为探头总线数，Zoom状态下lineNumber是起始线与终止线区间内的线数
    qreal angleRad = probeAngleRadEx(zoomOn, startLine, stopLine);
    int lineNumber = zoomOn ? (stopLine - startLine + 1) : lines();

    if (!m_Probe.IsLinear)
    {
        qreal line0Angle = 0;
        if (zeroAngleLine == LeftHorizontalLine)
        {
            line0Angle = M_PI_2 - angleRad / 2;
        }
        else if (zeroAngleLine == CenterLine)
        {
            line0Angle = -angleRad / 2;
        }
        return line0Angle + angleRad * lineNo / lineNumber;
    }
    else
    {
        return 0.0F;
    }
}

qreal ProbeParameters::convexProbeLineAngleRadEx(qreal lineNo)
{
    return convexProbeLineAngleRadEx(lineNo, m_ZoomOn, m_StartLine, m_StartLine);
}

qreal ProbeParameters::convexProbeLineNo(qreal angleRad, ZeroAngleLine zeroAngleLine) const
{
    if (!m_Probe.IsLinear)
    {
        qreal line0Angle = 0;

        if (zeroAngleLine == LeftHorizontalLine)
        {
            line0Angle = M_PI_2 - probeAngleRad(true) / 2;
        }
        else if (zeroAngleLine == CenterLine)
        {
            line0Angle = -probeAngleRad(true) / 2;
        }

        return lines() * (angleRad - line0Angle) / probeAngleRad(true);
    }
    else
    {
        return 0.0F;
    }
}

qreal ProbeParameters::linearProbeLineDisMM(qreal lineNo, ZeroAngleLine zeroDisLine) const
{
    if (m_Probe.IsLinear)
    {
        qreal line0Dis = 0;
        if (zeroDisLine == CenterLine)
        {
            line0Dis = -probeWidthMM() / 2;
        }
        return line0Dis + probeWidthMM() * lineNo / lines();
    }
    else
    {
        return 0.0F;
    }
}

// qreal ProbeParameters::linearProbeLineDisMM(qreal lineNo, int lines, ZeroAngleLine zeroDisLine) const
//{
//    if(m_Probe.IsLinear)
//    {
//        qreal line0Dis = 0;
//        qreal probeWidthMM = lines * probeLineSpacingMM<qreal>();
//        if(zeroDisLine == CenterLine)
//        {
//            line0Dis = -probeWidthMM / 2;
//        }
//        return line0Dis + probeWidthMM * lineNo / lines;

//    }
//    else
//    {
//        return 0.0F;
//    }
//}

qreal ProbeParameters::linearProbeLineNo(qreal disMM, ZeroAngleLine zeroDisLine) const
{
    if (m_Probe.IsLinear)
    {
        qreal line0Dis = 0;
        if (zeroDisLine == CenterLine)
        {
            line0Dis = -probeWidthMM() / 2;
        }
        return lines() * (disMM - line0Dis) / probeWidthMM();
    }
    else
    {
        return 0.0F;
    }
}

const ProbeDataInfo& ProbeParameters::probe() const
{
    return m_Probe;
}

int ProbeParameters::highDensityFactor(bool useHighDensityFactor, bool highDensity) const
{
    return !useHighDensityFactor ? 2 : (highDensity ? 2 : 1);
}

int ProbeParameters::highDensityFactor(bool useHighDensityFactor, int highDensity) const
{
    if (!useHighDensityFactor)
    {
        return 2;
    }
    switch (highDensity)
    {
    case MediumDensity:
        return 2;
    case LowDensity:
        return 1;
    case HighDensity:
        return 4;
    default:
        return 2;
    }
}

double ProbeParameters::pixelSizeMMZoomCoefFactor(double pixelSizeMM, bool useZoomCoefFactor, int zoomCoefPercent) const
{
    return useZoomCoefFactor ? pixelSizeMM
                             : BFDepthParameters::pixelSizeMMWithoutZoomCoef(pixelSizeMM, zoomCoefPercent);
}

bool ProbeParameters::getDSCCenterLine() const
{
    return m_DSCCenterLine;
}

qreal ProbeParameters::getInnerRadius(bool paVertDistEnable) const
{
    return m_Probe.WaferRadius;
}

bool ProbeParameters::isSupportAnyDensity()
{
    static bool isSupportAnyDensity = ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity, false).toBool();
    return isSupportAnyDensity;
}

BProbeParameters::BProbeParameters(const ProbeDataInfo& probe, qreal startDepthMM, qreal pixelSizeMM, bool zoomOn,
                                   int startLine, int stopLine, qreal lineSpacingMM, qreal angleSpacingRad,
                                   bool DSCCenterLine)
    : ProbeParameters(probe, startDepthMM, pixelSizeMM, zoomOn, startLine, stopLine, lineSpacingMM, angleSpacingRad,
                      DSCCenterLine)
{
}

qreal BProbeParameters::getInnerRadius(bool paVertDistEnable) const
{
    if (paVertDistEnable)
    {
        return m_Probe.WaferRadius / qCos(probeAngleRad(true) / 2);
    }
    else
    {
        return m_Probe.WaferRadius;
    }
}

CProbeParameters::CProbeParameters(const ProbeDataInfo& probe, qreal startDepthMM, qreal pixelSizeMM, bool zoomOn,
                                   int startLine, int stopLine, qreal lineSpacingMM, qreal angleSpacingRad,
                                   bool DSCCenterLine)
    : BProbeParameters(probe, startDepthMM, pixelSizeMM, zoomOn, startLine, stopLine, lineSpacingMM, angleSpacingRad,
                       DSCCenterLine)
{
}

qreal CProbeParameters::getInnerRadius(bool paVertDistEnable) const
{
    return m_Probe.WaferRadius;
}
