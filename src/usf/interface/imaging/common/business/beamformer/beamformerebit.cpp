/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "beamformerebit.h"
#include "abstractbfdatahandler.h"
#include "acousticpowerpresscalculator.h"
#include "applogger.h"
#include "bfstaticparametersebit.h"
#include "baseblockdatasender.h"
#include "bfadfreqparameter.h"
#include "bfcmaxvelparametercalculatorqbit.h"
#include "bfdmaxvelparametercalculatorqbit.h"
#include "bfebitkitfactory.h"
#include "bffocusparameters.h"
#include "bffpsparameter.h"
#include "bfmitiparameters.h"
#include "bfmpixelsizesecparametercalculator.h"
#include "bfpnames.h"
#include "bfprtofbparametercaculator.h"
#include "bfprtofcparametercaculator.h"
#include "bfprtofdparametercaculator.h"
#include "bytebuffer.h"
#include "controltableparameter.h"
#include "cqyzstepconfig.h"
#include "cwdprfcalculator.h"
#include "elastoprfcalculator.h"
#include "fgcsettingmodel.h"
#include "focusescombinemodel.h"
#include "focusparasmodel.h"
#include "formula.h"
#include "freqparascontainer.h"
#include "iblockdatasender.h"
#include "iblockdataparanameconverter.h"
#include "irelatedparascontroller.h"
#include "modelconfig.h"
#include "newbfdpixelsizesecparametercalculator.h"
#include "newcfmprfcalculator.h"
#include "newdopprfcalculator.h"
#include "newtriplexmodehelper.h"
#include "probeblockdataset.h"
#include "probedataset.h"
#include "realcompare.h"
#include "resource.h"
#include "setting.h"
#include "sonoparameters.h"
#include "ultrasounddevice.h"
#include "util.h"
#include "variantutil.h"
#include "calculatorutil.h"
#include <QDebug>
#include <QTime>
#include <qmath.h>
#include "linedatahead.h"

// LOG4QT_DECLARE_STATIC_LOGGER(log, BeamFormerEBit)
BeamFormerEBit::BeamFormerEBit(QObject* parent)
    : BeamFormerTiger(parent)
    , m_FocusParasModel(NULL)
    , m_FGCSettingModel(NULL)
    , m_FocusesCombineModel(NULL)
    , m_AFELPF(0)
{
    disconnect(&m_RefreshTimer, SIGNAL(timeout()), this, SLOT(refreshImage()));
    connect(this, SIGNAL(imagePixelBitsChanged(int)), this, SLOT(onImagePixelBitsChanged(int)));
}

void BeamFormerEBit::suspendRead()
{
    suspendBFIODeviceRead();
}

void BeamFormerEBit::resumeRead()
{
    m_BFIODevice->resumeRead();
}

FocusParasModel* BeamFormerEBit::focusParasModel() const
{
    return m_FocusParasModel;
}

FGCSettingModel* BeamFormerEBit::fgcSettingModel() const
{
    return m_FGCSettingModel;
}

FocusesCombineModel* BeamFormerEBit::focusesCombineModel() const
{
    return m_FocusesCombineModel;
}

void BeamFormerEBit::onGettingIsUDBMText(QString& value)
{
    value = pBV(BFPNames::IsUDBMStr) ? QString("UD") : QString("LR");
}

void BeamFormerEBit::onGettingFlowText(QString& value)
{
    int intValue = pIV(BFPNames::FlowStr);

    if (intValue == 0)
    {
        value = "Low";
    }
    else if (intValue == 1)
    {
        value = "Mid";
    }
    else if (intValue == 2)
    {
        value = "High";
    }
}

void BeamFormerEBit::onGettingSteeringAngleMin(int& value)
{
    int maxSteeringAngleIndex = pIV(BFPNames::MaxSteeringAngleStr);
    if (maxSteeringAngleIndex >= 0)
    {
        value = maxSteeringAngleIndex;
    }
    else
    {
        value = 0;
    }
}

void BeamFormerEBit::onGettingSteeringAngleMax(int& value)
{
    int maxSteeringAngleIndex = pIV(BFPNames::MaxSteeringAngleStr);
    if (maxSteeringAngleIndex >= 0)
    {
        value = m_StaticParameters->steeringAngleIndexes().count() - 1 - maxSteeringAngleIndex;
    }
    else
    {
        value = m_StaticParameters->steeringAngleIndexes().count() - 1;
    }
}

void BeamFormerEBit::onMScanLineChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (curProbe().IsPhasedArray && pBV(BFPNames::ColorMStr))
    {
        parameter(BFPNames::StartLineColorStr)->update();
        parameter(BFPNames::StopLineColorStr)->update();
    }
}

void BeamFormerEBit::onMScanLineChanged(const QVariant& value)
{
    Q_UNUSED(value);
    imageShapeIntoUnstable(50);
}

void BeamFormerEBit::onFreqIndexColorChanging(const QVariant& value)
{
    Q_UNUSED(value);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::Color);
}

void BeamFormerEBit::onFreqIndexPDChanging(const QVariant& value)
{
    Q_UNUSED(value);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::PD);
}

void BeamFormerEBit::onFreqIndexSNChanging(const QVariant& value)
{
    Q_UNUSED(value);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::SonoNeedle);
}

void BeamFormerEBit::onFreqIndexTDIChanging(const QVariant& value)
{
    Q_UNUSED(value);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::TDI);
}

void BeamFormerEBit::onFreqIndexDopChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlSoundOpened(ISoundController::Freq);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::Dop);
}

void BeamFormerEBit::onFreqIndexTDChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlSoundOpened(ISoundController::Freq);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::TD);
}

void BeamFormerEBit::onFreqIndexCWDChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlSoundOpened(ISoundController::Freq);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::CWD);
}

void BeamFormerEBit::onFreqIndexElastoChanging(const QVariant& value)
{
    Q_UNUSED(value);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::Elasto);
}

void BeamFormerEBit::onFreqIndexMVIChanging(const QVariant& value)
{
    Q_UNUSED(value);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::MVI);
}

void BeamFormerEBit::onSteeringAngleChanging(const QVariant& value)
{
    int iv = value.toInt();

    int angle;
    bool isGetAngle = false;
    QList<int> settings = VariantUtil::variant2Ints(pV(BFPNames::ROISteerAnglesStr), VariantUtil::String);
    if (settings.count() != m_StaticParameters->steeringAngles().count() / 2)
    {
        if (iv >= 0 && iv < m_StaticParameters->steeringAngles().count())
        {
            angle = m_StaticParameters->steeringAngles().at(iv);
            isGetAngle = true;
        }
    }
    else
    {
        QList<int> angles = CalculatorUtil::toMirrorList(settings);
        if (iv >= 0 && iv < angles.count())
        {
            angle = angles.at(iv);
            isGetAngle = true;
        }
    }

    if (isGetAngle)
    {
        double steerCos = 1 / qCos(Formula::degree2Rad(angle));
        double steerTan = qTan(Formula::degree2Rad(angle));
        setPV(BFPNames::CCosAngleStr, Util::floatToBinary(steerCos));
        setPV(BFPNames::CTgAngleStr, Util::floatToBinary(steerTan));
        setPV(BFPNames::CSteeringAngleCodingStr, qAbs(angle));

        int steeringAngleIndex = 0;
        int offset = 1;
        if (angle < 0)
        {
            steeringAngleIndex = 62 + qAbs(angle) * 2 + offset;
        }
        else
        {
            steeringAngleIndex = angle * 2 + offset;
        }

        int baseIndex = qAbs(angle) * 8;
        if (angle < 0)
        {
            offset = 3;
        }
        QList<int> absSteeringAngleIndexs = QList<int>() << baseIndex + offset << baseIndex + offset + 4;

        //块数据文件使用QBit相同的data41,data57     by20160810
        BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name,
                                         QStringList() << BFPNames::SteeringAngleStr << "AbsSteeringAngle");
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::SteeringAngleStr),
            steeringAngleIndex);
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName("AbsSteeringAngle"), absSteeringAngleIndexs);
    }
}

void BeamFormerEBit::onGettingSteeringAngleSNShowValue(QVariant& value)
{
    int iv = pIV(BFPNames::SteeringAngleSNStr);
    QList<int> settings = VariantUtil::variant2Ints(pV(BFPNames::ROISteerAnglesStr), VariantUtil::String);
    if (settings.count() != m_StaticParameters->steeringAngles().count() / 2)
    {
        if (iv >= 0 && iv < m_StaticParameters->steeringAngles().count())
        {
            value = m_StaticParameters->steeringAngles().at(iv);
        }
    }
    else
    {
        QList<int> angles = CalculatorUtil::toMirrorList(settings);
        if (iv >= 0 && iv < angles.count())
        {
            value = angles.at(iv);
        }
    }
}

void BeamFormerEBit::onGettingSteeringAngleShowValue(QVariant& value)
{
    int iv = pIV(BFPNames::SteeringAngleStr);
    QList<int> settings = VariantUtil::variant2Ints(pV(BFPNames::ROISteerAnglesStr), VariantUtil::String);
    if (settings.count() != m_StaticParameters->steeringAngles().count() / 2)
    {
        if (iv >= 0 && iv < m_StaticParameters->steeringAngles().count())
        {
            value = m_StaticParameters->steeringAngles().at(iv);
        }
    }
    else
    {
        QList<int> angles = CalculatorUtil::toMirrorList(settings);
        if (iv >= 0 && iv < angles.count())
        {
            value = angles.at(iv);
        }
    }
}

void BeamFormerEBit::onColorCoefChanging(const QVariant& value)
{
    m_RelatedParasController->sendColorCoefParas(value.toInt(), BFPNames::CAFStrIdsStr, BFPNames::CLFStrIdsStr);
}

void BeamFormerEBit::onPDCoefChanging(const QVariant& value)
{
    m_RelatedParasController->sendColorCoefParas(value.toInt(), BFPNames::PDAFStrIdsStr, BFPNames::PDLFStrIdsStr);
}

void BeamFormerEBit::onSNCoefChanging(const QVariant& value)
{
    m_RelatedParasController->sendColorCoefParas(value.toInt(), BFPNames::SNAFStrIdsStr, BFPNames::SNLFStrIdsStr);
}

void BeamFormerEBit::onMVICoefChanging(const QVariant& value)
{
    m_RelatedParasController->sendColorCoefParas(value.toInt(), BFPNames::MVIAFStrIdsStr, BFPNames::MVILFStrIdsStr);
}

void BeamFormerEBit::onTDICoefChanging(const QVariant& value)
{
    m_RelatedParasController->sendColorCoefParas(value.toInt(), BFPNames::TDIAFStrIdsStr, BFPNames::TDILFStrIdsStr);
}

void BeamFormerEBit::onGettingSampleRateDopMax(int& value)
{
    value = m_CfmPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingDSampleRateDopShowText(QString& value)
{
    double prf;
    if (pBV(BFPNames::TriplexModeStr))
    {
        prf = m_TriplexModeHelper->prfTriplexWork();
    }
    else
    {
        prf = m_PWDopPrfCalculator->realPrf();
    }
    value = Formula::formatFrequency(prf);
}

void BeamFormerEBit::onGettingDSampleRateDopShowMax(int& value)
{
    value = m_PWDopPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingSampleRateDopShowText(QString& value)
{
    double prf;
    if (pBV(BFPNames::TriplexModeStr))
    {
        prf = m_TriplexCfmPrfCalculator->realPrf();
    }
    else
    {
        prf = m_CfmPrfCalculator->realPrf();
    }
    value = Formula::formatFrequency(prf);
}

void BeamFormerEBit::onGettingSampleRateDopShowMax(int& value)
{
    value = m_CfmPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingSampleRateDopTDIText(QString& value)
{
    if (pBV(BFPNames::TDIEnStr))
    {
        double prf = m_TdiCfmPrfCalculator->realPrf();
        value = Formula::formatFrequency(prf);
    }
}

void BeamFormerEBit::onGettingSampleRateDopTDIMax(int& value)
{
    value = m_TdiCfmPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingSampleRateDopSNText(QString& value)
{
    if (isSonoNeedleOn())
    {
        double prf = m_SonoNeedleCfmPrfCalculator->realPrf();
        value = Formula::formatFrequency(prf);
    }
}

void BeamFormerEBit::onGettingSampleRateDopMVIText(QString& value)
{
    if (pBV(BFPNames::MVIModeStr))
    {
        double prf = m_MVICfmPrfCalculator->realPrf();
        value = Formula::formatFrequency(prf);
    }
}

void BeamFormerEBit::onGettingSampleRateDopSNMax(int& value)
{
    value = m_SonoNeedleCfmPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingSampleRateDopMVIMax(int& value)
{
    value = m_MVICfmPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingSampleRateDopTMMax(int& value)
{
    value = m_TriplexCfmPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingDSampleRateDopMax(int& value)
{
    value = m_PWDopPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingDSampleRateDopTDIText(QString& value)
{
    if (pBV(BFPNames::TDIEnStr))
    {
        double prf = m_TDDopPrfCalculator->realPrf();
        value = Formula::formatFrequency(prf);
    }
}

void BeamFormerEBit::onGettingDSampleRateDopTDIMax(int& value)
{
    value = m_TDDopPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingDSampleRateDopTMMax(int& value)
{
    value = m_TriplexDopPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingCWDSampleRateText(QString& value)
{
    value = m_CWDPRFCalculator->text();
}

void BeamFormerEBit::onGettingCWDSampleRateMax(int& value)
{
    value = m_StaticParameters->CWDSampleRateValues().count() - 1;
}

void BeamFormerEBit::onGettingSampleRateDopElastoText(QString& value)
{
    double prf = m_ElastoPrfCalculator->realPrf();
    value = Formula::formatFrequency(prf);
}

void BeamFormerEBit::onGettingSampleRateDopElastoMax(int& value)
{
    value = m_ElastoPrfCalculator->pfrListMaxIndex();
}

void BeamFormerEBit::onGettingWallFilterDopControlTableValue(const QVariant& value, int& controlTableValue)
{
    // PW的WF0-3档位放反了，应该是越大滤的越多.modified by daixiao, 2013-3-21
    // ECO 没做invert操作
    controlTableValue = pMax(BFPNames::WallFilterDopStr) - value.toInt();
}

void BeamFormerEBit::onGettingWallFilterDopTDIControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingWallFilterDopControlTableValue(value, controlTableValue);
}

void BeamFormerEBit::onGettingWallFilterCWDControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingWallFilterDopControlTableValue(value, controlTableValue);
}

void BeamFormerEBit::onBSteeringScanChanging(const QVariant& value)
{
    ControlTableParameter* ctPara = qobject_cast<ControlTableParameter*>(parameter(BFPNames::BSteeringScanStr));
    if (ctPara != NULL /* && pIV(BFPNames::CPDSteerStr) == 0*/)
    {
        int angle = value.toInt() - pMax(BFPNames::BSteeringScanStr) / 2;
        sendCpdSteerCTValue(angle);

        //下发偏转块数据
        if (isBSteerOn())
        {
            m_BlockDataSender->sendParaBlockDataGroup(
                curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr), qAbs(angle));
        }
        else //关闭bsteer,发射cpd下的偏转角度
        {
            updateCPDSteerStr();
        }
    }

    if (isFixedDepth())
    {
        controlCQYZ(pIV(BFPNames::CQYZLevelStr));
    }
    controlTrapezoidalMode();
}

void BeamFormerEBit::onGettingBSteeringScanShowValue(QVariant& value)
{
    value = value.toInt() - pMax(BFPNames::BSteeringScanStr) / 2;
}

void BeamFormerEBit::onGettingBSteeringScanControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (curProbe().IsLinear)
    {
        int angle = value.toInt() - pMax(BFPNames::BSteeringScanStr) / 2;

        if (angle < 0)
        {
            controlTableValue = 0x20 | qAbs(angle); //负角度的最高位为1
        }
        else
        {
            controlTableValue = angle;
        }
    }
    else
    {
        controlTableValue = 0;
    }
}

void BeamFormerEBit::onIsBiopsyVisibleChanging(const QVariant& value)
{
    if (value.toBool() && isBSteerOn())
    {
        setPV(BFPNames::BSteeringScanStr, 20);
    }
    controlBSteer();
}

void BeamFormerEBit::onECGEnChanging(const QVariant& value)
{
    imageIntoUnstable();
    parameter(BFPNames::ECGInvertStr)->setEnabled(value.toBool());
    parameter(BFPNames::ECGGainStr)->setEnabled(value.toBool());
    controlECGPos();
    parameter(BFPNames::ECGVelocityStr)->setEnabled(value.toBool());
    parameter(BFPNames::ECGDynamicRangeStr)->setEnabled(value.toBool());
    parameter(BFPNames::ECGDlyShowStr)->setEnabled(value.toBool());

    setPV(BFPNames::ECGSignalStr, value.toBool());
}

void BeamFormerEBit::onECGDlyChanging(const QVariant& value)
{
    if (!((syncMode() & Sync_M) == Sync_M) && !pBV(BFPNames::CWEnStr))
    {
        setPV(BFPNames::ECGDlyShowStr, value);
    }
}

void BeamFormerEBit::onMECGDlyChanging(const QVariant& value)
{
    if ((syncMode() & Sync_M) == Sync_M)
    {
        setPV(BFPNames::ECGDlyShowStr, value);
    }
}

void BeamFormerEBit::onCWECGDlyChanging(const QVariant& value)
{
    if (pBV(BFPNames::CWEnStr))
    {
        setPV(BFPNames::ECGDlyShowStr, value);
    }
}

void BeamFormerEBit::onECGDlyShowChanging(const QVariant& value)
{
    SyncModeType sync = syncMode();
    if ((sync & Sync_M) == Sync_M)
    {
        if (pBV(BFPNames::FreeMModeStr))
        {
            changeECGDeltaElement(BFPNames::FreeMECGDlyStr, BFPNames::FreeMECGDlyDeltasStr, BFPNames::FreeMVelocityStr,
                                  value);
        }
        else
        {
            changeECGDeltaElement(BFPNames::MECGDlyStr, BFPNames::MECGDlyDeltasStr, BFPNames::MVelocityStr, value);
        }
    }
    else if ((sync & Sync_D) == Sync_D)
    {
        if (pBV(BFPNames::CWEnStr))
        {
            changeECGDeltaElement(BFPNames::CWECGDlyStr, BFPNames::CWDECGDlyDeltasStr, BFPNames::CWDVelocityStr, value);
        }
        else
        {
            if (pBV(BFPNames::TDIEnStr))
            {
                changeECGDeltaElement(BFPNames::DTDIECGDlyStr, BFPNames::DTDIECGDlyDeltasStr, BFPNames::DVelocityTDIStr,
                                      value);
            }
            else
            {
                changeECGDeltaElement(BFPNames::DECGDlyStr, BFPNames::DECGDlyDeltasStr, BFPNames::DVelocityStr, value);
            }
        }
    }
    else
    {
        setPV(BFPNames::ECGDlyStr, value);
    }
}

void BeamFormerEBit::onGettingECGPosPreset(QVariant& value)
{
    if (m_ECGPosBak != -1)
    {
        value = m_ECGPosBak;
    }
}

void BeamFormerEBit::onLeftChanging(const QVariant& value)
{
    BeamFormerBase::onLeftChanging(value);
    // FPGA 内部用的是这个参数来纠正ECG在图像翻转时的左右方向
    // ECGLRInvertStr已经没有功能
    setPV(BFPNames::LRInvertStr, !value.toBool());
}

void BeamFormerEBit::onBeforePrtOfBChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    newValue = m_PRTOfBParameter->calculate();

    bool flagD = (syncMode() & Sync_D) == Sync_D && (pBV(BFPNames::FreqSpectrumStr) || pBV(BFPNames::TriplexModeStr));
    if (!flagD && !pBV(BFPNames::CWEnStr))
    {
        newValue = m_APPressCalculator->getPrtB(newValue.toInt());
    }
}

void BeamFormerEBit::onGettingPrtOfBIsValidValue(const QVariant& value, bool& valid)
{
    QVariant oldValue = value;
    QVariant newValue = value;
    onBeforePrtOfBChanged(value, newValue);
    valid = (newValue == oldValue);
}

void BeamFormerEBit::onGettingPrtOfBText(QString& value)
{
    value = timeParaText(BFPNames::PrtOfBStr);
}

void BeamFormerEBit::onGettingPrtOfBControlTableValue(const QVariant& value, int& controlTableValue)
{
    //温升控制，所以要加Debug * 512, getPrtDelta是增加fps控制后添加
    controlTableValue = value.toInt() + (pIV(BFPNames::DebugPara0Str) + m_APPressCalculator->prtDelta()) * 512;

    // Triplex模式下，加Delta
    bool flagC = (syncMode() & Sync_C) == Sync_C || pBV(BFPNames::ColorMStr);
    // TODO C模式下对B模式高低中密度的影响
    if (flagC && !pBV(BFPNames::HighDensityStr)) //血流模式，BW低密度
    {
        controlTableValue += pIV(BFPNames::PrtDeltaOnLowDensityStr);
    }
    if (pBV(BFPNames::TriplexModeStr)) // 3同步
    {
        controlTableValue += pIV(BFPNames::BTriplexPrt_DeltaStr);
    }

    controlTableValue += pIV(BFPNames::PrtOfB_DeltaStr);

    static int PRTOFB_MAX = 65535;
    if (controlTableValue < 0)
    {
        controlTableValue = 0;
    }
    else if (controlTableValue > PRTOFB_MAX)
    {
        controlTableValue = PRTOFB_MAX;
    }
}

void BeamFormerEBit::onPrtOfB_DeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    parameter(BFPNames::PrtOfBStr)->update();
}

void BeamFormerEBit::onGettingPrtOfCText(QString& value)
{
    value = timeParaText(BFPNames::PrtOfCStr);
}

void BeamFormerEBit::onGettingPrtOfDText(QString& value)
{
    value = timeParaText(BFPNames::PrtOfDStr);
}

void BeamFormerEBit::onMVelocityChanging(const QVariant& value)
{
    setECGDly(BFPNames::MECGDlyStr, BFPNames::MECGDlyDeltasStr, value);
}

void BeamFormerEBit::onGettingParaPresetTimeText(QString& value)
{
    value = timeParaText(BFPNames::ParaPresetTimeStr);
}

void BeamFormerEBit::onGettingParaPresetTime_XBFText(QString& value)
{
    value = timeParaText(BFPNames::ParaPresetTime_XBFStr);
}

void BeamFormerEBit::onGettingAdjustmentOfBText(QString& value)
{
    value = timeParaText(BFPNames::AdjustmentOfBStr);
}

void BeamFormerEBit::onGettingAdjustmentOfCText(QString& value)
{
    value = timeParaText(BFPNames::AdjustmentOfCStr);
}

void BeamFormerEBit::onGettingAdjustmentOfDText(QString& value)
{
    value = timeParaText(BFPNames::AdjustmentOfDStr);
}

void BeamFormerEBit::onGettingRvTxBText(QString& value)
{
    value = timeParaText(BFPNames::RvTxBStr);
}

void BeamFormerEBit::onGettingRvTxCText(QString& value)
{
    value = timeParaText(BFPNames::RvTxCStr);
}

void BeamFormerEBit::onGettingRvTxDText(QString& value)
{
    value = timeParaText(BFPNames::RvTxDStr);
}

void BeamFormerEBit::onQFlowModeChanging(const QVariant& value)
{
    Q_UNUSED(value);
    parameter(BFPNames::QBeamOnStr)->update();
}

void BeamFormerEBit::onQBeamOnChanging(const QVariant& value)
{
    QString multiBeamStr = (colorImageMode() == Color_PD) ? (isSonoNeedleOn() ? BFPNames::MBSNStr : BFPNames::MBPDStr)
                                                          : BFPNames::MBColorStr;

    int multiBeam = 1;
    if (value.toBool())
    {
        if (pBV(BFPNames::QFlowModeStr))
        {
            multiBeam = 2;
        }
    }

    setPV(multiBeamStr, multiBeam);
}

void BeamFormerEBit::onQFlowOnChanging(const QVariant& value)
{
    bool isColorPD = (colorImageMode() == Color_PD);
    bool isSonoNeedle = isSonoNeedleOn();
    QString CVRTStr = BFPNames::CVRTStr;
    QString CVLTStr = BFPNames::CVLTStr;
    QString CETStr = BFPNames::CETStr;
    if (isColorPD)
    {
        if (isSonoNeedle)
        {
            CVRTStr = BFPNames::SNCVRTStr;
            CVLTStr = BFPNames::SNCVLTStr;
            CETStr = BFPNames::SNCETStr;
        }
        else
        {
            CVRTStr = BFPNames::PDCVRTStr;
            CVLTStr = BFPNames::PDCVLTStr;
            CETStr = BFPNames::PDCETStr;
        }
    }
    else
    {
        if (pBV(BFPNames::TDIEnStr))
        {
            CVRTStr = BFPNames::TDICVRTStr;
            CVLTStr = BFPNames::TDICVLTStr;
            CETStr = BFPNames::TDICETStr;
        }
        else if (pBV(BFPNames::MVIModeStr))
        {
            CVRTStr = BFPNames::MVICVRTStr;
            CVLTStr = BFPNames::MVICVLTStr;
            CETStr = BFPNames::MVICETStr;
        }
        else
        {
            // Do noting
        }
    }

    parameter(CVRTStr)->update();
    parameter(CVLTStr)->update();
    parameter(CETStr)->update();

    parameter(BFPNames::CVRTDeltaStr)->setEnabled(!value.toBool());
    parameter(BFPNames::CVLTDeltaStr)->setEnabled(!value.toBool());
    parameter(BFPNames::CETDeltaStr)->setEnabled(!value.toBool());
}

// CVRTStr,CVLTStr,CETStr用于计算的话需要使用实际控制表的值
void BeamFormerEBit::onGettingCVRTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVRTDeltaStr);
    }
}

void BeamFormerEBit::onGettingCVLTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVLTDeltaStr);
    }
}

void BeamFormerEBit::onGettingCETControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CETDeltaStr);
    }
}

void BeamFormerEBit::onGettingPDCVRTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVRTDeltaStr);
    }
}

void BeamFormerEBit::onGettingSNCVRTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVRTDeltaStr);
    }
}

void BeamFormerEBit::onGettingTDICVRTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVRTDeltaStr);
    }
}

void BeamFormerEBit::onGettingTDICVLTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVLTDeltaStr);
    }
}

void BeamFormerEBit::onGettingTDICETControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CETDeltaStr);
    }
}

void BeamFormerEBit::onGettingPDCVLTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVLTDeltaStr);
    }
}

void BeamFormerEBit::onGettingSNCVLTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVLTDeltaStr);
    }
}

void BeamFormerEBit::onGettingPDCETControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CETDeltaStr);
    }
}

void BeamFormerEBit::onGettingSNCETControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CETDeltaStr);
    }
}

void BeamFormerEBit::onGettingMVICVRTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVRTDeltaStr);
    }
}

void BeamFormerEBit::onGettingMVICVLTControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CVLTDeltaStr);
    }
}

void BeamFormerEBit::onGettingMVICETControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::CETDeltaStr);
    }
}

void BeamFormerEBit::onGettingCVRTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::CVRTStr) + pIV(BFPNames::CVRTDeltaStr));
    }
}

void BeamFormerEBit::onGettingCVLTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::CVLTStr) + pIV(BFPNames::CVLTDeltaStr));
    }
}

void BeamFormerEBit::onGettingCETText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::CETStr) + pIV(BFPNames::CETDeltaStr));
    }
}

void BeamFormerEBit::onGettingPDCVRTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::PDCVRTStr) + pIV(BFPNames::CVRTDeltaStr));
    }
}

void BeamFormerEBit::onGettingSNCVRTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::SNCVRTStr) + pIV(BFPNames::CVRTDeltaStr));
    }
}

void BeamFormerEBit::onGettingTDICVRTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::TDICVRTStr) + pIV(BFPNames::CVRTDeltaStr));
    }
}

void BeamFormerEBit::onGettingTDICVLTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::TDICVLTStr) + pIV(BFPNames::CVLTDeltaStr));
    }
}

void BeamFormerEBit::onGettingTDICETText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::TDICETStr) + pIV(BFPNames::CETDeltaStr));
    }
}

void BeamFormerEBit::onGettingPDCVLTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::PDCVLTStr) + pIV(BFPNames::CVLTDeltaStr));
    }
}

void BeamFormerEBit::onGettingSNCVLTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::SNCVLTStr) + pIV(BFPNames::CVLTDeltaStr));
    }
}

void BeamFormerEBit::onGettingPDCETText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::PDCETStr) + pIV(BFPNames::CETDeltaStr));
    }
}

void BeamFormerEBit::onGettingSNCETText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::SNCETStr) + pIV(BFPNames::CETDeltaStr));
    }
}

void BeamFormerEBit::onGettingMVICVRTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::MVICVRTStr) + pIV(BFPNames::CVRTDeltaStr));
    }
}

void BeamFormerEBit::onGettingMVICVLTText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::MVICVLTStr) + pIV(BFPNames::CVLTDeltaStr));
    }
}

void BeamFormerEBit::onGettingMVICETText(QString& value)
{
    if (!pBV(BFPNames::QFlowOnStr))
    {
        value = QString::number(pIV(BFPNames::MVICETStr) + pIV(BFPNames::CETDeltaStr));
    }
}

void BeamFormerEBit::onCVRTDeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    QString CVRTStr;
    if (pBV(BFPNames::TDIEnStr))
    {
        CVRTStr = BFPNames::TDICVRTStr;
    }
    else if (pBV(BFPNames::MVIModeStr))
    {
        CVRTStr = BFPNames::MVICVRTStr;
    }
    else
    {
        CVRTStr = (colorImageMode() == Color_PD) ? (isSonoNeedleOn() ? BFPNames::SNCVRTStr : BFPNames::PDCVRTStr)
                                                 : BFPNames::CVRTStr;
    }
    parameter(CVRTStr)->update();
}

void BeamFormerEBit::onCVLTDeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    QString CVLTStr;
    if (pBV(BFPNames::TDIEnStr))
    {
        CVLTStr = BFPNames::TDICVLTStr;
    }
    else if (pBV(BFPNames::MVIModeStr))
    {
        CVLTStr = BFPNames::MVICVLTStr;
    }
    else
    {
        CVLTStr = (colorImageMode() == Color_PD) ? (isSonoNeedleOn() ? BFPNames::SNCVLTStr : BFPNames::PDCVLTStr)
                                                 : BFPNames::CVLTStr;
    }
    parameter(CVLTStr)->update();
}

void BeamFormerEBit::onCETDeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    QString CETStr;
    if (pBV(BFPNames::TDIEnStr))
    {
        CETStr = BFPNames::TDICETStr;
    }
    else if (pBV(BFPNames::MVIModeStr))
    {
        CETStr = BFPNames::MVICETStr;
    }
    else
    {
        CETStr = (colorImageMode() == Color_PD) ? (isSonoNeedleOn() ? BFPNames::SNCETStr : BFPNames::PDCETStr)
                                                : BFPNames::CETStr;
    }
    parameter(CETStr)->update();
}

void BeamFormerEBit::onBeforeROISteerAnglesChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (newValue == pMin(BFPNames::ROISteerAnglesStr)) //预设值中没有时newvalue使用的p->min
    {
        QList<int> settings = m_StaticParameters->absSteeringAngles();
        QVariant strs = VariantUtil::ints2Variant(settings, VariantUtil::String);
        newValue = strs;
    }
    else
    {
        const int settingCount = m_StaticParameters->steeringAngles().count() / 2;
        QList<int> settings = VariantUtil::variant2Ints(newValue, VariantUtil::String);
        QVariant strs = VariantUtil::ints2Variant(settings, VariantUtil::String);

        bool isInRange = true;
        // fpga块数据只支持-30~30角度
        for (int i = 0; i < settings.count(); i++)
        {
            if (settings.at(i) > 30 || settings.at(i) <= 0)
            {
                isInRange = false;
                break;
            }
        }

        if (strs != newValue || settings.count() != settingCount || !isInRange)
        {
            settings = VariantUtil::variant2Ints(oldValue, VariantUtil::String);
            if (settings.count() != settingCount)
            {
                settings = m_StaticParameters->absSteeringAngles();
            }
            strs = VariantUtil::ints2Variant(settings, VariantUtil::String);
            newValue = strs;
        }
    }
}

void BeamFormerEBit::onGettingROISteerAnglesIsValidValue(const QVariant& value, bool& valid)
{
    QVariant oldValue = value;
    QVariant newValue = value;
    onBeforeROISteerAnglesChanged(value, newValue);

    valid = (newValue == oldValue);
}

void BeamFormerEBit::onROISteerAnglesChanging(const QVariant& value)
{
    Q_UNUSED(value);
    //更新steeringAngle的显示值
    parameter(BFPNames::SteeringAngleStr)->update();
}

void BeamFormerEBit::oniImageEffectChanging(const QVariant& value)
{
    int rotation = pIV(BFPNames::RotationStr);
    if (rotation == 0 || rotation == 180)
    {
        m_BlockDataSender->sendParaBlockDataGroup(
            ProbeBlockDataSet::paraName(ProbeBlockDataSet::iImage),
            m_BlockDataParaNameConverter->groupParaName(Resource::iImageEffectParaName()), value.toInt());
    }
}

void BeamFormerEBit::oniImageEffectRotationChanging(const QVariant& value)
{
    int rotation = pIV(BFPNames::RotationStr);
    if (rotation == 90 || rotation == 270)
    {
        m_BlockDataSender->sendParaBlockDataGroup(
            ProbeBlockDataSet::paraName(ProbeBlockDataSet::iImage),
            m_BlockDataParaNameConverter->groupParaName(Resource::iImageEffectParaName()), value.toInt());
    }
}

void BeamFormerEBit::onGettingGainTDIControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (pBV(BFPNames::ColorMStr))
    {
        controlTableValue = value.toInt() - pIV(BFPNames::GainTDI_DeltaStr);
    }
}

void BeamFormerEBit::onGainTDI_DeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::ColorMStr) && pBV(BFPNames::TDIEnStr))
    {
        parameter(BFPNames::GainTDIStr)->update();
    }
}

void BeamFormerEBit::onGettingGainColorControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::GainColorTM_DeltaStr);
    }
}

void BeamFormerEBit::onGettingGainPDControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::GainColorTM_DeltaStr);
    }
}

void BeamFormerEBit::onGettingGainSNControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::GainColorTM_DeltaStr);
    }
}

void BeamFormerEBit::onGettingGainMVIControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        controlTableValue = value.toInt() + pIV(BFPNames::GainColorTM_DeltaStr);
    }
}

void BeamFormerEBit::onGainColorTM_DeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::TriplexModeStr) && (syncMode() & Sync_C) == Sync_C)
    {
        QString gainParaStr = (colorImageMode() == Color_PD)
                                  ? (isSonoNeedleOn() ? BFPNames::GainSNStr : BFPNames::GainPDStr)
                                  : BFPNames::GainColorStr;
        parameter(gainParaStr)->update();
    }
}

void BeamFormerEBit::onGettingAFE_InImpedenceText(QString& value)
{
    value = m_StaticParameters->AFE_InImpedenceText()[pIV(BFPNames::AFE_InImpedenceStr)];
}

void BeamFormerEBit::onGettingAFE_InImpedenceMax(int& value)
{
    value = m_StaticParameters->AFE_InImpedenceText().size() - 1;
}

void BeamFormerEBit::onGettingAFE_LNA_GAINText(QString& value)
{
    value = m_StaticParameters->AFE_LNA_GAINText()[pIV(BFPNames::AFE_LNA_GAINStr)];
}

void BeamFormerEBit::onGettingAFE_LNA_GAINMax(int& value)
{
    value = m_StaticParameters->AFE_LNA_GAINText().size() - 1;
}

void BeamFormerEBit::onGettingAFE_PGA_GAINText(QString& value)
{
    value = m_StaticParameters->AFE_PGA_GAINText()[pIV(BFPNames::AFE_PGA_GAINStr)];
}

void BeamFormerEBit::onGettingAFE_PGA_GAINMax(int& value)
{
    value = m_StaticParameters->AFE_PGA_GAINText().size() - 1;
}

void BeamFormerEBit::onAFE_LPF_FCutOffChanged(const QVariant& value)
{
    Q_UNUSED(value);
    //改变了之后要刷新一下HPF的text
    parameter(BFPNames::AFE_HPF_FCutOffStr)->update();
}

void BeamFormerEBit::onGettingAFE_LPF_FCutOffText(QString& value)
{
    int index = pIV(BFPNames::AFE_LPF_FCutOffStr);
    float coef = index < m_StaticParameters->LPFINVALIDPOS ? 3.0 : 4.5;
    m_AFELPF = m_StaticParameters->AFE_LPF_FCutOff()[index] * 1 / coef * pIV(BFPNames::ADFreqMHzStr);
    value = QString("%1 * 1 / %2 * %3 = %4Mhz")
                .arg(m_StaticParameters->AFE_LPF_FCutOff()[index])
                .arg(coef)
                .arg(pIV(BFPNames::ADFreqMHzStr))
                .arg(QString::number(m_AFELPF, 'f', 2));
}

void BeamFormerEBit::onGettingAFE_LPF_FCutOffMax(int& value)
{
    value = m_StaticParameters->AFE_LPF_FCutOff().size() - 1;
}

void BeamFormerEBit::onGettingAFE_HPF_FCutOffText(QString& value)
{
    int index = pIV(BFPNames::AFE_HPF_FCutOffStr);
    float hpf = m_AFELPF / m_StaticParameters->AFE_HPF_FCutOff()[index];
    value = QString("%1 / %2 = %3Mhz")
                .arg(QString::number(m_AFELPF, 'f', 2))
                .arg(m_StaticParameters->AFE_HPF_FCutOff()[index])
                .arg(QString::number(hpf, 'f', 2));
}

void BeamFormerEBit::onGettingAFE_HPF_FCutOffMax(int& value)
{
    value = m_StaticParameters->AFE_HPF_FCutOff().size() - 1;
}

void BeamFormerEBit::onGettingAFE_LNA_BiasText(QString& value)
{
    value = m_StaticParameters->AFE_LNA_BiasText()[pIV(BFPNames::AFE_LNA_BiasStr)];
}

void BeamFormerEBit::onGettingAFE_LNA_BiasMax(int& value)
{
    value = m_StaticParameters->AFE_LNA_BiasText().size() - 1;
}

void BeamFormerEBit::onGettingFocusNumBMax(int& value)
{
    const ProbeDataInfo& probe = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (pBV(BFPNames::ScpdOnStr) || pBV(BFPNames::FcpdOnStr) || pBV(BFPNames::TrapezoidalModeStr))
    {
        value = qMin(probe.MaxFocusCompoundOn - 1, BFFocusParameters().focusNumMax(probe));
    }
    else
    {
        value = qMin(probe.MaxFocusCompoundOff - 1, BFFocusParameters().focusNumMax(probe));
    }
}

void BeamFormerEBit::onBeforeCQYZChanged(const QVariant& oldValue, QVariant& newValue)
{
    int newValueInt = newValue.toInt();
    Parameter* para = parameter(BFPNames::CQYZStr);
    if (para != NULL)
    {
        int max = para->max();
        int min = para->min();
        if (newValueInt > max)
        {
            newValue = max;
        }
        else if (newValueInt < min)
        {
            newValue = min;
        }
    }
    if (ModelConfig::instance().value(ModelConfig::PixelLenStep, 1).toInt() == 1)
    {
        return;
    }

    //为了节约FPGA资源CQYZ大于64以后只支持偶数档（66,68,70...）
    if (newValueInt > 64)
    {
        if (oldValue.toInt() == newValueInt)
        {
            return;
        }

        bool isAdd = newValueInt > oldValue.toInt();
        if (newValueInt % 2 == 1)
        {
            newValue = newValueInt + (isAdd ? 1 : -1);
        }
    }
}

void BeamFormerEBit::onGettingCQYZStep(int& value)
{
    if (ModelConfig::instance().value(ModelConfig::PixelLenStep, 1).toInt() == 1)
    {
        return;
    }
    bool isAdd = pBV(BFPNames::CQYZGrowingUpStr);
    value = m_CQYZStepConfig->getStep(curProbe().Name, pIV(BFPNames::CQYZStr), isAdd);
}

void BeamFormerEBit::onGettingCQYZIsValidValue(const QVariant& value, bool& valid)
{
    valid = true;
    if (value.toInt() > pMax(BFPNames::CQYZStr) || value.toInt() < pMin(BFPNames::CQYZStr))
    {
        valid = false;
        return;
    }
    if (ModelConfig::instance().value(ModelConfig::PixelLenStep, 1).toInt() == 1)
    {
        return;
    }
    int valueInt = value.toInt();
    if (valueInt < 0 || (valueInt > 64 && valueInt % 2 == 1))
    {
        valid = false;
    }
}

void BeamFormerEBit::onZoomSelectChanging(const QVariant& value)
{
    Q_UNUSED(value);
    BeamFormerBase::onZoomSelectChanging(value);
}

void BeamFormerEBit::updateDPixelSizeSec()
{
    updateDopAccumulateNum();
    setPV(BFPNames::DPixelSizeSecStr, m_NewDPixelSizeSecParameter->calculate());
    imageShapeIntoUnstable();
}

void BeamFormerEBit::updateMPixelSizeSec()
{
    if ((SyncModeType)(syncMode() & Sync_M) == Sync_M)
    {
        double v = m_MPixelSizeSecParameter->calculate().toDouble();
        if (RealCompare::IsGreaterOrEqual(v, 0))
        {
            setPV(BFPNames::MPixelSizeSecStr, v);
        }
    }
}

void BeamFormerEBit::updatePRFColor()
{
    if (pBV(BFPNames::ElastoEnStr))
    {
        m_ElastoPrfCalculator->setPrfIndex(pIV(BFPNames::SampleRateDopElastoStr));
        setPV(BFPNames::PRFElastoStr, m_ElastoPrfCalculator->realPrf());
    }
    else
    {
        if (pBV(BFPNames::TDIEnStr))
        {
            m_TdiCfmPrfCalculator->setPrfIndex(pIV(BFPNames::SampleRateDopTDIStr));
            setPV(BFPNames::PRFColorStr, m_TdiCfmPrfCalculator->realPrf());
        }
        else
        {
            if (pBV(BFPNames::MVIModeStr))
            {
                m_MVICfmPrfCalculator->setPrfIndex(pIV(BFPNames::SampleRateDopMVIStr));
                setPV(BFPNames::PRFColorStr, m_MVICfmPrfCalculator->realPrf());
            }
            else if (isSonoNeedleOn())
            {
                m_SonoNeedleCfmPrfCalculator->setPrfIndex(pIV(BFPNames::SampleRateDopSNStr)); //更新prf,下发prtofc
                setPV(BFPNames::PRFColorStr,
                      m_SonoNeedleCfmPrfCalculator->realPrf()); //更新PRFColorStr,用于菜单显示和速度更新
            }
            else
            {
                if (pBV(BFPNames::TriplexModeStr))
                {
                    m_TriplexCfmPrfCalculator->setPrfIndex(pIV(BFPNames::SampleRateDopTMStr));
                    m_TriplexModeHelper->calTriplexPrf();
                    setPV(BFPNames::PRFColorStr, m_TriplexCfmPrfCalculator->realPrf());
                }
                else
                {
                    m_CfmPrfCalculator->setPrfIndex(pIV(BFPNames::SampleRateDopStr)); //更新prf,下发prtofc
                    setPV(BFPNames::PRFColorStr,
                          m_CfmPrfCalculator->realPrf()); //更新PRFColorStr,用于菜单显示和速度更新
                }
            }
        }
    }
}

void BeamFormerEBit::updatePRFDop()
{
    updateDopAccumulateNum();
    if (pBV(BFPNames::TDIEnStr))
    {
        m_TDDopPrfCalculator->setPrfIndex(pIV(BFPNames::DSampleRateDopTDIStr));
        setPV(BFPNames::PRFDopStr, m_TDDopPrfCalculator->realPrf());
    }
    else
    {
        if (pBV(BFPNames::TriplexModeStr))
        {
            m_TriplexDopPrfCalculator->setPrfIndex(pIV(BFPNames::DSampleRateDopTMStr));
            m_TriplexModeHelper->calTriplexPrf();
            setPV(BFPNames::PRFDopStr, m_TriplexModeHelper->prfTriplexWork());
        }
        else
        {
            if (pBV(BFPNames::CWEnStr) && isHPrfCW())
            {
                m_CWDopPrfCalculator->setPrfIndex(pIV(BFPNames::CWDSampleRateStr));
                setPV(BFPNames::PRFDopStr, m_CWDopPrfCalculator->realPrf());
            }
            else
            {
                m_PWDopPrfCalculator->setPrfIndex(pIV(BFPNames::DSampleRateDopStr));
                setPV(BFPNames::PRFDopStr, m_PWDopPrfCalculator->realPrf());
            }
        }
    }
}

void BeamFormerEBit::updatePRTOfB()
{
    //这里不使用setpv是因为在调节PrtDelta的时候不能触发到实际下发的prtofb
    parameter(BFPNames::PrtOfBStr)->update();
}

void BeamFormerEBit::updatePRTOfC()
{
    // prtc和prte是复用关系,触发条件有很多是一样的,避免在弹性模式下更新prtc,这里加上ElastoEn限制
    if (!pBV(BFPNames::ElastoEnStr))
    {
        if (isFrozen())
        {
            //冻结的时候需要设置一下参数，保持与实时一致
            m_PRTOfCParameter->setPresetColorBottomBorder(
                m_SonoParameters->currentPreset().value(BFPNames::RoiMidDepthMMStr).toDouble() +
                m_SonoParameters->currentPreset().value(BFPNames::RoiHalfDepthMMStr).toDouble());
            m_PRTOfCParameter->setPresetTDIBottomBorder(
                m_SonoParameters->currentPreset().value(BFPNames::RoiMidDepthMMTDIStr).toDouble() +
                m_SonoParameters->currentPreset().value(BFPNames::RoiHalfDepthMMTDIStr).toDouble());
        }
        double depthPrt = m_PRTOfCParameter->calculate().toInt() / (double)pIV(BFPNames::ADFreqMHzStr) * qPow(10, 3);
        if (pBV(BFPNames::TDIEnStr))
        {
            m_TdiCfmPrfCalculator->setDepthPrt(depthPrt);
            setPV(BFPNames::PRFColorStr, m_TdiCfmPrfCalculator->realPrf());
            updateRealtimeParameter(BFPNames::SampleRateDopTDIStr);
        }
        else if (pBV(BFPNames::MVIModeStr))
        {
            m_MVICfmPrfCalculator->setDepthPrt(depthPrt);
            setPV(BFPNames::PRFColorStr, m_MVICfmPrfCalculator->realPrf());
            updateRealtimeParameter(BFPNames::SampleRateDopMVIStr);
        }
        else if (isSonoNeedleOn())
        {
            m_SonoNeedleCfmPrfCalculator->setDepthPrt(depthPrt);
            setPV(BFPNames::PRFColorStr, m_SonoNeedleCfmPrfCalculator->realPrf());
            updateRealtimeParameter(BFPNames::SampleRateDopSNStr);
        }
        else
        {
            if (pBV(BFPNames::TriplexModeStr))
            {
                m_TriplexCfmPrfCalculator->setDepthPrt(depthPrt);
                setPV(BFPNames::PRFColorStr, m_TriplexCfmPrfCalculator->realPrf());
            }
            else
            {
                m_CfmPrfCalculator->setDepthPrt(depthPrt);
                setPV(BFPNames::PRFColorStr, m_CfmPrfCalculator->realPrf());
            }
            //这里使用update是因为在不调节prfindex的情况下改变prf值用setPV不会发出valueChanging信号
            updateRealtimeParameter(BFPNames::SampleRateDopShowStr);
        }
    }
}

void BeamFormerEBit::updatePRTOfD()
{
    //    if((SyncModeType)(syncMode() & Sync_D) == Sync_D && !ModeCW)
    updateDopAccumulateNum();
    double depthPrt = m_PRTOfDParameter->calculate().toInt() / (double)pIV(BFPNames::ADFreqMHzStr) * qPow(10, 3);
    if (pBV(BFPNames::TDIEnStr))
    {
        m_TDDopPrfCalculator->setDepthPrt(depthPrt);
        setPV(BFPNames::PRFDopStr, m_TDDopPrfCalculator->realPrf());
        updateRealtimeParameter(BFPNames::DSampleRateDopTDIStr);
    }
    else
    {
        if (pBV(BFPNames::TriplexModeStr))
        {
            m_TriplexDopPrfCalculator->setDepthPrt(depthPrt);
            m_TriplexModeHelper->calTriplexPrf();
            setPV(BFPNames::PRFDopStr, m_TriplexModeHelper->prfTriplexWork());
            updateRealtimeParameter(BFPNames::DSampleRateDopShowStr);
        }
        else
        {
            if (pBV(BFPNames::CWEnStr) && isHPrfCW())
            {
                m_CWDopPrfCalculator->setDepthPrt(depthPrt);
                setPV(BFPNames::PRFDopStr, m_CWDopPrfCalculator->realPrf());
                //这里的update是为了在depthprt变化后刷新prf的显示值,
                // eco6有hprf,不需要显示depthprt,所以这里屏蔽
                // parameter(BFPNames::CWDSampleRateStr)->update();
            }
            else
            {
                m_PWDopPrfCalculator->setDepthPrt(depthPrt);
                setPV(BFPNames::PRFDopStr, m_PWDopPrfCalculator->realPrf());
                //                parameter(BFPNames::DSampleRateDopShowStr)->update();
            }
        }
    }
}

void BeamFormerEBit::updatePRFCWD()
{
    double prf = m_CWDPRFCalculator->calculate().toDouble();
    setPV(BFPNames::PRFCWDStr, prf);
}

void BeamFormerEBit::updateDopAccumulateNum()
{
    int accumulateNum = 0;
    double prf = 0;
    int velocity = 0;

    if (pBV(BFPNames::CWEnStr))
    {
        if (isHPrfCW())
        {
            prf = m_CWDopPrfCalculator->realPrf();
            velocity = pIV(BFPNames::DVelocityStr);
            accumulateNum = (int)qRound(m_StaticParameters->accCountCoef()[velocity] * prf);
        }
        else
        {
            // grapeTabelt.xml 里没有这个参数，直接用 D
            accumulateNum = getCWDLines(pIV(BFPNames::CWDVelocityStr), pIV(BFPNames::CWDSampleRateStr));
        }
    }
    else
    {
        if (pBV(BFPNames::TriplexModeStr))
        {
            prf = m_TriplexModeHelper->prfTriplexWork();
            velocity = pIV(BFPNames::DVelocityStr);
        }
        else
        {
            if (pBV(BFPNames::TDIEnStr))
            {
                prf = m_TDDopPrfCalculator->realPrf();
                velocity = pIV(BFPNames::DVelocityTDIStr);
            }
            else
            {
                prf = m_PWDopPrfCalculator->realPrf();
                velocity = pIV(BFPNames::DVelocityStr);
            }
        }
        accumulateNum = (int)qRound(m_StaticParameters->accCountCoef()[velocity] * prf);
    }

    setPV(BFPNames::DopAccumulateNumStr, accumulateNum);
}

void BeamFormerEBit::onImagePixelBitsChanged(int value)
{
    //冻结及加载图像数据不修改参数值
    if (!isFrozen())
    {
        setPV(BFPNames::ImagePixelBitsStr, value);
    }
}

IBFKitFactory* BeamFormerEBit::createBFKitFactory()
{
    return new BFEBitKitFactory();
}

void BeamFormerEBit::createParameterModels()
{
    m_FocusParasModel = new FocusParasModel(this);
    m_FocusParasModel->setBeamFormerTool(this);
    m_FocusParasModel->setSonoParameters(m_SonoParameters);

    m_FGCSettingModel = new FGCSettingModel(m_FocusParasModel, this);
    m_FGCSettingModel->setSonoParameters(m_SonoParameters);

    createFocusesCombineModel();
}

void BeamFormerEBit::onUpdate(unsigned char* buf, int len)
{
    static DataArg arg(m_RawData->width(), m_RawData->height(), m_RawData->bitCount());
    memcpy(arg.data(), buf, len);
    m_BFDataHandler->handleRawData(arg.data(), arg.len());
    //    log()->debug("arg.len %1, buf len %2", arg.len(), len);
    emit dataUpdated();

    if (!isFrozen() && isUpdateImage())
    {
        emit updateData(&arg);
    }
}

void BeamFormerEBit::update(unsigned char* buf, int len)
{
    if (m_BFIODevice->isOpen() /* && !isFrozen()*/ && !m_BFDataHandler->isLastActiveBFrame())
    {
        emit updateImageData(ByteBuffer(buf, len));
    }
}

void BeamFormerEBit::imageIntoUnstable(int ms)
{
    if (!isFrozen())
    {
        m_UnstableParaTimer.imageIntoUnstable(ms);
    }
}

void BeamFormerEBit::initializeParameterCalculators()
{
    BeamFormerBase::initializeParameterCalculators();

    initializeBFFpsParameter();

    m_PRTOfBParameter = new BFPRTOfBParameterCaculator(m_SonoParameters);
    foreach (const QString& p, m_PRTOfBParameter->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRTOfB()));
    }

    initBFPRTOfCParameterCaculator();
    initBFPRTOfDParameterCaculator();

    initializePostParameterCalculators();

    foreach (const QString& p, m_CfmPrfCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFColor()));
    }

    foreach (const QString& p, m_SonoNeedleCfmPrfCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFColor()));
    }

    m_TdiCfmPrfCalculator = new NewCfmPrfCalculator(m_SonoParameters, BasePRFCalculator::Tdi);
    foreach (const QString& p, m_TdiCfmPrfCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFColor()));
    }

    m_MVICfmPrfCalculator = new NewCfmPrfCalculator(m_SonoParameters, BasePRFCalculator::Cfm);
    foreach (const QString& p, m_MVICfmPrfCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFColor()));
    }

    foreach (const QString& p, m_TriplexCfmPrfCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFColor()));
    }

    foreach (const QString& p, m_PWDopPrfCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFDop()));
    }

    m_TDDopPrfCalculator = new NewDopPrfCalculator(m_SonoParameters, BasePRFCalculator::TD);
    foreach (const QString& p, m_TDDopPrfCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFDop()));
    }

    foreach (const QString& p, m_TriplexDopPrfCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFDop()));
    }

    initializeElastoPrfCalculator();

    m_CMaxVelParameter =
        new BFCMaxVelParameterCalculatorQBit(m_SonoParameters, m_StaticParameters, m_TriplexModeHelper);
    foreach (const QString& p, m_CMaxVelParameter->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updateCMaxVel()));
    }

    foreach (const QString& p, m_DMaxVelParameter->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updateDMaxVel()));
    }

    m_NewDPixelSizeSecParameter = new NewBFDPixelSizeSecParameterCalculator(m_SonoParameters);
    foreach (const QString& p, m_NewDPixelSizeSecParameter->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updateDPixelSizeSec()));
    }

    initBFMpixelSizeSecParameterCalculator();
    foreach (const QString& p, m_MPixelSizeSecParameter->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updateMPixelSizeSec()));
    }

    m_BFMitiParameters = new BFMitiParameters(m_SonoParameters);
    foreach (const QString& p, m_BFMitiParameters->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updateMiti()));
    }
}

void BeamFormerEBit::initializePostParameterCalculators()
{
    if (m_CfmPrfCalculator == NULL)
    {
        m_CfmPrfCalculator = new NewCfmPrfCalculator(m_SonoParameters, BasePRFCalculator::Cfm);
    }
    if (m_SonoNeedleCfmPrfCalculator == NULL)
    {
        m_SonoNeedleCfmPrfCalculator = new NewCfmPrfCalculator(m_SonoParameters, BasePRFCalculator::Cfm);
    }
    if (m_TdiCfmPrfCalculator == NULL)
    {
        m_TdiCfmPrfCalculator = new NewCfmPrfCalculator(m_SonoParameters, BasePRFCalculator::Tdi);
    }
    if (m_MVICfmPrfCalculator == NULL)
    {
        m_MVICfmPrfCalculator = new NewCfmPrfCalculator(m_SonoParameters, BasePRFCalculator::Cfm);
    }
    if (m_TriplexCfmPrfCalculator == NULL)
    {
        m_TriplexCfmPrfCalculator = new NewCfmPrfCalculator(m_SonoParameters, BasePRFCalculator::Cfm);
    }
    if (m_PWDopPrfCalculator == NULL)
    {
        m_PWDopPrfCalculator = new NewDopPrfCalculator(m_SonoParameters, BasePRFCalculator::PW);
    }
    if (m_TDDopPrfCalculator == NULL)
    {
        m_TDDopPrfCalculator = new NewDopPrfCalculator(m_SonoParameters, BasePRFCalculator::TD);
    }
    if (m_TriplexDopPrfCalculator == NULL)
    {
        m_TriplexDopPrfCalculator = new NewDopPrfCalculator(m_SonoParameters, BasePRFCalculator::PW);
    }
    if (m_TriplexModeHelper == NULL)
    {
        m_TriplexModeHelper = new NewTriplexModeHelper(m_SonoParameters, m_CfmPrfCalculator, m_TriplexCfmPrfCalculator,
                                                       m_PWDopPrfCalculator, m_TriplexDopPrfCalculator);
        m_TriplexModeHelper->setStateManager(m_StateManager);
    }
    if (m_DMaxVelParameter == NULL)
    {
        m_DMaxVelParameter =
            new BFDMaxVelParameterCalculatorQBit(m_SonoParameters, m_StaticParameters, m_TriplexModeHelper);
    }

    initCWDPRFParameterCalculator();
    initBFPRTOfCParameterCaculator();
    initBFPRTOfDParameterCaculator();
}

void BeamFormerEBit::initializeBFFpsParameter()
{
    m_FpsParameter = new BFFpsParameter(m_SonoParameters);
    foreach (const QString& p, m_FpsParameter->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updateFPS()));
    }
}

void BeamFormerEBit::initializeElastoPrfCalculator()
{
    m_ElastoPrfCalculator = new ElastoPrfCalculator(m_SonoParameters, BasePRFCalculator::Elasto);
    foreach (const QString& p, m_ElastoPrfCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFColor()));
    }
}

void BeamFormerEBit::initBFMpixelSizeSecParameterCalculator()
{
    m_MPixelSizeSecParameter = new BFMPixelSizeSecParameterCalculator(m_SonoParameters, m_StaticParameters);
}

void BeamFormerEBit::initCWDPRFParameterCalculator()
{
    if (m_CWDPRFCalculator == nullptr && m_IfConnectSignals)
    {
        m_CWDPRFCalculator = new CWDPRFCalculator(m_SonoParameters, m_StaticParameters);
        foreach (const QString& p, m_CWDPRFCalculator->relatedParameters())
        {
            connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFCWD()));
        }
    }
}

void BeamFormerEBit::initBFPRTOfCParameterCaculator()
{
    if (m_PRTOfCParameter == NULL)
    {
        m_PRTOfCParameter = new BFPRTOfCParameterCaculator(m_SonoParameters);
        foreach (const QString& p, m_PRTOfCParameter->relatedParameters())
        {
            connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRTOfC()));
        }
    }
}

void BeamFormerEBit::initBFPRTOfDParameterCaculator()
{
    if (m_PRTOfDParameter == NULL)
    {
        m_PRTOfDParameter = new BFPRTOfDParameterCaculator(m_SonoParameters, m_StaticParameters);
        m_PRTOfDParameter->setDepthParameters(m_DepthParameters);
        foreach (const QString& p, m_PRTOfDParameter->relatedParameters())
        {
            connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRTOfD()));
        }
    }
}

void BeamFormerEBit::resentCurProbeBlockData()
{
    BeamFormerBase::resentCurProbeBlockData();

    parameter(BFPNames::SteeringAngleStr)->update();

    //    if (ModelConfig::instance().value(ModelConfig::IsNewiImage, false).toBool())
    //    {
    //        // newiimage bloctdata
    //        parameter(BFPNames::iImageEffectStr)->update();
    //        parameter(BFPNames::iImageEffectRotationStr)->update();
    //    }

    // cqyz step blockdata
    sendMDFData();
}

void BeamFormerEBit::updatePrfList()
{
    m_SonoNeedleCfmPrfCalculator->updatePrfList();
    m_CfmPrfCalculator->updatePrfList();
    m_TdiCfmPrfCalculator->updatePrfList();
    m_MVICfmPrfCalculator->updatePrfList();
    m_TriplexCfmPrfCalculator->updatePrfList();
    m_PWDopPrfCalculator->updatePrfList();
    m_TriplexDopPrfCalculator->updatePrfList();
    m_TDDopPrfCalculator->updatePrfList();
}

void BeamFormerEBit::sendCQYZBlockDataGroup()
{
    // ebit以及其子类都添加了新优化功能，此函数在ebit和其子类上都不再需要
}

void BeamFormerEBit::sendMDFData()
{
    if (ModelConfig::instance().value(ModelConfig::PixelLenStep, 1).toInt() == 1)
    {
        return;
    }

    //修改后的CQYZ最大支持到192,但是块数据和MDFCoef的数据都只有128档,
    //所以CQYZ在1~64每档顺序获取，大于64档以后顺序代表的是66,68,70,72，...
    int cqyz = pBV(BFPNames::ZoomOnStr) ? pIV(BFPNames::ZoomedCQYZStr) : pIV(BFPNames::CQYZStr);

    if (pBV(BFPNames::CWEnStr) && pBV(BFPNames::FreqSpectrumStr))
    {
        cqyz = 160;
    }

    int cqyzIndex = cqyz - 1;
    if (cqyz > 64)
    {
        cqyzIndex = (cqyz - 64) / 2 + 64 - 1;
    }

    if (cqyzIndex >= 0 && cqyzIndex < m_StaticParameters->mdfGains().count())
    {
        setPV(BFPNames::MDFGainStr, m_StaticParameters->mdfGains()[cqyzIndex]);
        setPV(BFPNames::MDFShiftStr, m_StaticParameters->mdfShifts()[cqyzIndex]);
    }

    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name, m_BlockDataParaNameConverter->groupParaName("MDFFilter"),
                                              cqyzIndex);
}

void BeamFormerEBit::createFocusesCombineModel()
{
    if (ModelConfig::instance().value(ModelConfig::FocusesCombine, false).toBool())
    {
        m_FocusesCombineModel = new FocusesCombineModel(this);
        m_FocusesCombineModel->setBeamFormerTool(this);
        m_FocusesCombineModel->setSonoParameters(m_SonoParameters);
    }
}

void BeamFormerEBit::createStaticParameters()
{
    if (m_StaticParameters == NULL)
    {
        m_StaticParameters = new BFStaticParametersEBit();
    }
}

void BeamFormerEBit::updateCPDSteerStr()
{
    parameter(BFPNames::CPDSteerStr)->update();
}

void BeamFormerEBit::updateRealtimeParameter(const QString& param)
{
    if (!isFrozen())
    {
        parameter(param)->update();
    }
}

QString BeamFormerEBit::timeParaText(const QString& paraName) const
{
    double aDCycleTimeNs = BFADFreqParameter::cycleTimeNs(pIV(BFPNames::ADFreqMHzStr));
    int time = qRound(pIV(paraName) * aDCycleTimeNs / 1000);
    return QString().number(pIV(paraName)) + "(" + QString().number(time) + "us" + ")";
}

int BeamFormerEBit::getCWDLines(int speed, int sampleRate) const
{
    int lines = 9;
    if (speed >= 0 && speed < m_StaticParameters->CWDLines().count())
    {
        QVector<int> currentLines = m_StaticParameters->CWDLines()[speed];
        if (sampleRate >= 0 && sampleRate < currentLines.count())
        {
            // TODO currentLines 与 sampleRate的 级数不相符
            lines = currentLines[sampleRate];
        }
    }
    return lines;
}
