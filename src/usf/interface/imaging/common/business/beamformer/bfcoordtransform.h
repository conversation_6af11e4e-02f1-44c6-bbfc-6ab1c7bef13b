#ifndef BFCOORDTRANSFORM_H
#define BFCOORDTRANSFORM_H
#include "usfinterfaceimagingbusiness_global.h"

#include <QPointF>
#include "infostruct.h"
#include "probephysicalpoint.h"
#include "probephysicalsize.h"
#include "probeparameters.h"

class ProbeDataInfo;
class ISonoParameters;

/**
 * @brief 探头物理坐标和图像上的逻辑坐标的转换
 *
 *  (probe center pt)
 *          o
 *         /|\
 *        / | \
 * ------/--o--\------->   x
 * |        |(原点）  |
 * |        |        |
 * |        |        |
 * |       \|/ y     |
 * |                 |
 * |                 |
 * --------------------
 * 设定图像原点坐标为(X:图像正中间,Y:图像顶部),坐标方向与逻辑坐标方向相同
 * 获取探头的原点坐标，目前没有考虑任何上下翻转和左右翻转的问题
 * 以下所使的逻辑坐标都以上面这个坐标系为参考的点
 *
 */
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BFCoordTransform
{
public:
    BFCoordTransform();
    /**
     * @brief BFCoordTransform 构造一个当前状态的坐标变化的类
     * @note
     * startLine和stopLine为0时，使用sonoParameters中的B模式StartLineStr和StopLineStr，此时totalLines必须为B模式总线数
     *       如果要使用C的起始线号，那么totalLines必须为C模式总线数
     * @param sonoParameters
     */
    BFCoordTransform(const ISonoParameters* sonoParameters, bool considerZoomOn = true, int startLine = 0,
                     int stopLine = 0, int totalLines = 0);
    /**
     * @brief BFCoordTransform 构造一个用于计算非放大状态时坐标变化的对象
     *
     * @param probe
     * @param startDepthMM 由于放大或者scroll，图像的起始深度不一定为0
     * @param pixelSizeMM 给定每个像素对应的物理距离
     * @param totalLines 任意线密度时总线数
     */
    BFCoordTransform(const ProbeDataInfo& probe, qreal startDepthMM, qreal pixelSizeMM, int totalLines = 0);
    /**
     * @brief BFCoordTransform 构造一个用于放大或者不放大时计算坐标变化的对象
     *
     * @param probe
     * @param startDepthMM 由于放大或者scroll，图像的起始深度不一定为0
     * @param pixelSizeMM 给定每个像素对应的物理距离
     * @param zoomOn 是否放大
     * @param startLine 起始线号
     * @param stopLine 终止线号
     * @param totalLines 任意线密度时总线数
     */
    BFCoordTransform(const ProbeDataInfo& probe, qreal startDepthMM, qreal pixelSizeMM, bool zoomOn, int startLine,
                     int stopLine, int totalLines = 0);

    /**
     * @brief convertPtToPhysics 将图像区的坐标 \a pt，计算出该坐标对应的扫描线线号 \a line ，物理深度 \a depthMM
     *
     * @param pt
     *
     * @return
     */
    ProbePhysicalPointF convertPtToPhysics(const QPoint& pt) const;
    /**
     * @brief convertPtToPhysics 将图像区的坐标 \a pt，计算出该坐标对应的扫描线线号 \a line ，物理深度 \a depthMM
     *
     * @param pt
     *
     * @return
     */
    ProbePhysicalPointF convertPtToPhysics(const QPointF& pt) const;
    /**
     * @brief convertPtToPhysics  将图像区的坐标 \a pt，计算出该坐标对应的扫描线线号 \a line ，物理深度 \a depthMM
     *
     * @param pt
     * @param line
     * @param depthMM
     */
    void convertPtToPhysics(const QPoint& pt, qreal& line, qreal& depthMM) const;
    /**
     * @brief convertPtToPhysics  将图像区的坐标 \a pt，计算出该坐标对应的扫描线线号 \a line ，物理深度 \a depthMM
     *
     * @param pt
     * @param line
     * @param depthMM
     */
    void convertPtToPhysics(const QPointF& pt, qreal& line, qreal& depthMM) const;
    /**
     * @brief convertPhysicsToLogic 根据扫描线线号 \a line ，物理深度 \a depthMM ， 计算出图像区的坐标 \a pt
     *
     * @param ppt
     * @param pt
     */
    void convertPhysicsToLogic(const ProbePhysicalPointF& ppt, QPoint& pt) const;
    /**
     * @brief convertPhysicsToLogic 根据扫描线线号 \a line ，物理深度 \a depthMM ， 计算出图像区的坐标 \a pt
     *
     * @param ppt
     * @param pt
     */
    void convertPhysicsToLogic(const ProbePhysicalPointF& ppt, QPointF& pt) const;
    /**
     * @brief convertPhysicsToLogic 根据扫描线线号 \a line ，物理深度 \a depthMM ， 计算出图像区的坐标 \a pt
     *
     * @param ppt
     *
     * @return
     */
    QPoint convertPhysicsToLogic(const ProbePhysicalPointF& ppt) const;
    /**
     * @brief convertPhysicsToLogicF 根据扫描线线号 \a line ，物理深度 \a depthMM ， 计算出图像区的坐标 \a pt
     *
     * @param ppt
     *
     * @return
     */
    QPointF convertPhysicsToLogicF(const ProbePhysicalPointF& ppt) const;
    /**
     * @brief convertPhysicsToLogic 根据扫描线线号 \a line ，物理深度 \a depthMM ， 计算出图像区的坐标 \a pt
     *
     * @param line
     * @param depthMM 不管是放大还是非放大状态，depthMM都是严格意义的深度，从探头表面为起点
     *
     * @return
     */
    QPoint convertPhysicsToLogic(const qreal& line, const qreal& depthMM) const;
    /**
     * @brief convertPhysicsToLogic 根据扫描线线号 \a line ，物理深度 \a depthMM ， 计算出图像区的坐标 \a pt
     *
     * @param line
     * @param depthMM 不管是放大还是非放大状态，depthMM都是严格意义的深度，从探头表面为起点
     *
     * @return
     */
    QPointF convertPhysicsToLogicF(const qreal& line, const qreal& depthMM) const;
    /**
     * @brief convertPhysicsToLogic 根据扫描线线号 \a line ，物理深度 \a depthMM ， 计算出图像区的坐标 \a pt
     *
     * @param line
     * @param depthMM 不管是放大还是非放大状态，depthMM都是严格意义的深度，从探头表面为起点
     * @param pt
     */
    void convertPhysicsToLogic(const qreal& line, const qreal& depthMM, QPoint& pt) const;
    /**
     * @brief convertPhysicsToLogic 根据扫描线线号 \a line ，物理深度 \a depthMM ， 计算出图像区的坐标 \a pt
     *
     * @param line
     * @param depthMM 不管是放大还是非放大状态，depthMM都是严格意义的深度，从探头表面为起点
     * @param pt
     */
    void convertPhysicsToLogic(const qreal& line, const qreal& depthMM, QPointF& pt) const;
    /**
     * @brief convertOffsetToPhysics
     *
     * @param offset
     * @param pos PhysicalPos
     *
     * @return
     */
    ProbePhysicalPointF convertOffsetPhysicalPosToPhysics(const QPointF& offset, const ProbePhysicalPointF& pos) const;
    /**
     * @brief convertOffsetToPhysics
     *
     * @param offset
     * @param pos logical Pos
     *
     * @return
     */
    ProbePhysicalPointF convertOffsetToPhysics(const QPointF& offset, const QPointF& pos) const;
    /**
     * @brief convertOffsetToPhysics 逻辑坐标中的位移转换为物理值
     *
     * @param offset
     * @param pos
     * @param lineOffset
     * @param depthMMOffset
     */
    void convertOffsetToPhysics(const QPointF& offset, const QPointF& pos, qreal& lineOffset,
                                qreal& depthMMOffset) const;
    /**
     * @brief movePhysicalPoint 物理坐标移动逻辑坐标的位移
     *
     * @param ppt 物理坐标
     * @param offset 逻辑坐标的位移
     */
    void movePhysicalPoint(ProbePhysicalPoint& ppt, const QPointF& offset);
    /**
     * @brief movePhysicalPointF 物理坐标移动逻辑坐标的位移
     *
     * @param ppt 物理坐标
     * @param offset 逻辑坐标的位移
     */
    void movePhysicalPointF(ProbePhysicalPointF& ppt, const QPointF& offset);
    /**
     * @brief movePhysicalPoint 物理坐标相对于 \a relatedPPt 移动逻辑坐标的位移
     *
     * @param ppt 物理坐标
     * @param relatedPPt 相对的物理坐标
     * @param offset 逻辑坐标的位移
     */
    void movePhysicalPoint(ProbePhysicalPoint& ppt, const ProbePhysicalPoint& relatedPPt, const QPointF& offset);
    void movePhysicalPoint(ProbePhysicalPointF& ppt, const ProbePhysicalPoint& relatedPPt, const QPointF& offset);
    /**
     * @brief movePhysicalPointF 物理坐标相对于 \a relatedPPt 移动逻辑坐标的位移
     *
     * @param ppt 物理坐标
     * @param relatedPPt 相对的物理坐标
     * @param offset 逻辑坐标的位移
     */
    void movePhysicalPointF(ProbePhysicalPointF& ppt, const ProbePhysicalPoint& relatedPPt, const QPointF& offset);
    void changePhysicalSize(ProbePhysicalSize& size, const ProbePhysicalPoint& centerPPt, const QPointF& sizeOffset);
    /**
     * @brief convertPtToPhysicalSamples 将图像区的坐标 \a pt，计算出该坐标对应的扫描线线号 \a line 像素点 \a pixel
     * @param pt 逻辑坐标
     * @param isHalfHeight　图像是否处于一半的状态，用于判断是否需要扩大PixelSizeMM至２倍
     */
    ProbePhysicalPointF convertPtToPhysicalSamples(const QPointF& pt, bool isHalfHeight) const;
    /**
     * @brief convertPtToPhysicalSamples 将图像区的坐标 \a pt，计算出该坐标对应的扫描线线号 \a line 像素点 \a pixel
     * @param pt
     * @param line
     * @param pixel
     */
    void convertPtToPhysicalSamples(const QPointF& pt, qreal& line, qreal& pixel, bool isHalfHeight) const;

protected:
    // 设定图像原点坐标为(X:图像正中间,Y:图像顶部),坐标方向与逻辑坐标方向相同
    // 获取探头的原点坐标
    virtual QPointF getProbeCenterPt() const;
    // 获取图像弧底的坐标
    virtual QPointF getImageCenterPt() const;

private:
    const ProbeDataInfo& probe() const;
    ProbeParameters probeParameters() const;

protected:
    const ProbeDataInfo* m_Probe;
    qreal m_StartDepthMM;
    qreal m_PixelSizeMM;
    int m_MiddleLine;
    bool m_ZoomOn;
    int m_StartLine;
    int m_StopLine;
    int m_TotalLines;
    qreal m_DopSteerAngle;
    qreal m_DopplerScanLineStartPointX;
    qreal m_DopplerScanLineStartPointY;
};

#endif // BFCOORDTRANSFORM_H
