/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef BEAMFORMEREBIT_H
#define BEAMFORMEREBIT_H
#include "usfinterfaceimagingbusiness_global.h"

#include "beamformertiger.h"

/**
 * @brief The BeamFormerEBit class EBit机型使用
 */
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BeamFormerEBit : public BeamFormerTiger
{
    Q_OBJECT
public:
    explicit BeamFormerEBit(QObject* parent = 0);
    /**
     * @brief suspendRead
     * suspend read thread
     */
    virtual void suspendRead();
    /**
     * @brief resumeRead
     * resume read thread
     */
    virtual void resumeRead();
    virtual FocusParasModel* focusParasModel() const;
    virtual FGCSettingModel* fgcSettingModel() const;
    virtual FocusesCombineModel* focusesCombineModel() const;
protected slots:
    virtual void onGettingIsUDBMText(QString& value);
    virtual void onGettingFlowText(QString& value);
    virtual void onGettingSteeringAngleMin(int& value);
    virtual void onGettingSteeringAngleMax(int& value);
    virtual void onMScanLineChanging(const QVariant& value);
    virtual void onMScanLineChanged(const QVariant& value);
    virtual void onFreqIndexColorChanging(const QVariant& value);
    virtual void onFreqIndexPDChanging(const QVariant& value);
    virtual void onFreqIndexSNChanging(const QVariant& value);
    virtual void onFreqIndexTDIChanging(const QVariant& value);
    virtual void onFreqIndexDopChanging(const QVariant& value);
    virtual void onFreqIndexTDChanging(const QVariant& value);
    virtual void onFreqIndexCWDChanging(const QVariant& value);
    virtual void onFreqIndexElastoChanging(const QVariant& value);
    virtual void onFreqIndexMVIChanging(const QVariant& value);
    virtual void onSteeringAngleChanging(const QVariant& value);
    virtual void onGettingSteeringAngleShowValue(QVariant& value);
    virtual void onGettingSteeringAngleSNShowValue(QVariant& value);
    virtual void onColorCoefChanging(const QVariant& value);
    virtual void onPDCoefChanging(const QVariant& value);
    virtual void onSNCoefChanging(const QVariant& value);
    virtual void onMVICoefChanging(const QVariant& value);
    virtual void onTDICoefChanging(const QVariant& value);
    virtual void onGettingSampleRateDopMax(int& value);
    virtual void onGettingDSampleRateDopShowText(QString& value);
    virtual void onGettingDSampleRateDopShowMax(int& value);
    virtual void onGettingSampleRateDopShowText(QString& value);
    virtual void onGettingSampleRateDopShowMax(int& value);
    virtual void onGettingSampleRateDopTDIText(QString& value);
    virtual void onGettingSampleRateDopTDIMax(int& value);
    virtual void onGettingSampleRateDopSNText(QString& value);
    virtual void onGettingSampleRateDopSNMax(int& value);
    virtual void onGettingSampleRateDopMVIText(QString& value);
    virtual void onGettingSampleRateDopMVIMax(int& value);
    virtual void onGettingSampleRateDopTMMax(int& value);
    virtual void onGettingDSampleRateDopMax(int& value);
    virtual void onGettingDSampleRateDopTDIText(QString& value);
    virtual void onGettingDSampleRateDopTDIMax(int& value);
    virtual void onGettingDSampleRateDopTMMax(int& value);
    virtual void onGettingCWDSampleRateText(QString& value);
    virtual void onGettingCWDSampleRateMax(int& value);
    virtual void onGettingSampleRateDopElastoText(QString& value);
    virtual void onGettingSampleRateDopElastoMax(int& value);

    //    virtual void onBeforeWallFilterDopChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onWallFilterDopChanging(const QVariant& value);
    //    virtual void onWallFilterDopChanged(const QVariant& value);
    //    virtual void onGettingWallFilterDopShowValue(QVariant& value);
    //    virtual void onGettingWallFilterDopText(QString& value);
    //    virtual void onGettingWallFilterDopMin(int& value);
    //    virtual void onGettingWallFilterDopMax(int& value);
    //    virtual void onGettingWallFilterDopStep(int& value);
    virtual void onGettingWallFilterDopControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingWallFilterDopPreset(QVariant& value);

    //    virtual void onBeforeWallFilterDopTDIChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onWallFilterDopTDIChanging(const QVariant& value);
    //    virtual void onWallFilterDopTDIChanged(const QVariant& value);
    //    virtual void onGettingWallFilterDopTDIShowValue(QVariant& value);
    //    virtual void onGettingWallFilterDopTDIText(QString& value);
    //    virtual void onGettingWallFilterDopTDIMin(int& value);
    //    virtual void onGettingWallFilterDopTDIMax(int& value);
    //    virtual void onGettingWallFilterDopTDIStep(int& value);
    virtual void onGettingWallFilterDopTDIControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingWallFilterDopTDIPreset(QVariant& value);

    //    virtual void onBeforeWallFilterCWDChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onWallFilterCWDChanging(const QVariant& value);
    //    virtual void onWallFilterCWDChanged(const QVariant& value);
    //    virtual void onGettingWallFilterCWDShowValue(QVariant& value);
    //    virtual void onGettingWallFilterCWDText(QString& value);
    //    virtual void onGettingWallFilterCWDMin(int& value);
    //    virtual void onGettingWallFilterCWDMax(int& value);
    //    virtual void onGettingWallFilterCWDStep(int& value);
    virtual void onGettingWallFilterCWDControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingWallFilterCWDPreset(QVariant& value);

    //    virtual void onBeforeBSteeringScanChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onBSteeringScanChanging(const QVariant& value);
    //    virtual void onBSteeringScanChanged(const QVariant& value);
    virtual void onGettingBSteeringScanShowValue(QVariant& value);
    //    virtual void onGettingBSteeringScanText(QString& value);
    //    virtual void onGettingBSteeringScanMin(int& value);
    //    virtual void onGettingBSteeringScanMax(int& value);
    //    virtual void onGettingBSteeringScanStep(int& value);
    virtual void onGettingBSteeringScanControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingBSteeringScanPreset(QVariant& value);

    //    virtual void onBeforeIsBiopsyVisibleChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onIsBiopsyVisibleChanging(const QVariant& value);
    //    virtual void onIsBiopsyVisibleChanged(const QVariant& value);
    //    virtual void onGettingIsBiopsyVisibleShowValue(QVariant& value);
    //    virtual void onGettingIsBiopsyVisibleText(QString& value);
    //    virtual void onGettingIsBiopsyVisibleMin(int& value);
    //    virtual void onGettingIsBiopsyVisibleMax(int& value);
    //    virtual void onGettingIsBiopsyVisibleStep(int& value);
    //    virtual void onGettingIsBiopsyVisibleControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingIsBiopsyVisiblePreset(QVariant& value);

    //    virtual void onBeforeECGEnChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onECGEnChanging(const QVariant& value);
    //    virtual void onECGEnChanged(const QVariant& value);
    //    virtual void onGettingECGEnShowValue(QVariant& value);
    //    virtual void onGettingECGEnText(QString& value);
    //    virtual void onGettingECGEnMin(int& value);
    //    virtual void onGettingECGEnMax(int& value);
    //    virtual void onGettingECGEnStep(int& value);
    //    virtual void onGettingECGEnControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingECGEnPreset(QVariant& value);

    //    virtual void onBeforeECGDlyChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onECGDlyChanging(const QVariant& value);
    //    virtual void onECGDlyChanged(const QVariant& value);
    //    virtual void onGettingECGDlyShowValue(QVariant& value);
    //    virtual void onGettingECGDlyText(QString& value);
    //    virtual void onGettingECGDlyMin(int& value);
    //    virtual void onGettingECGDlyMax(int& value);
    //    virtual void onGettingECGDlyStep(int& value);
    //    virtual void onGettingECGDlyControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingECGDlyPreset(QVariant& value);

    //    virtual void onBeforeMECGDlyChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onMECGDlyChanging(const QVariant& value);
    //    virtual void onMECGDlyChanged(const QVariant& value);
    //    virtual void onGettingMECGDlyShowValue(QVariant& value);
    //    virtual void onGettingMECGDlyText(QString& value);
    //    virtual void onGettingMECGDlyMin(int& value);
    //    virtual void onGettingMECGDlyMax(int& value);
    //    virtual void onGettingMECGDlyStep(int& value);
    //    virtual void onGettingMECGDlyControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingMECGDlyPreset(QVariant& value);

    //    virtual void onBeforeCWECGDlyChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onCWECGDlyChanging(const QVariant& value);
    //    virtual void onCWECGDlyChanged(const QVariant& value);
    //    virtual void onGettingCWECGDlyShowValue(QVariant& value);
    //    virtual void onGettingCWECGDlyText(QString& value);
    //    virtual void onGettingCWECGDlyMin(int& value);
    //    virtual void onGettingCWECGDlyMax(int& value);
    //    virtual void onGettingCWECGDlyStep(int& value);
    //    virtual void onGettingCWECGDlyControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingCWECGDlyPreset(QVariant& value);

    //    virtual void onBeforeECGDlyShowChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onECGDlyShowChanging(const QVariant& value);
    //    virtual void onECGDlyShowChanged(const QVariant& value);
    //    virtual void onGettingECGDlyShowShowValue(QVariant& value);
    //    virtual void onGettingECGDlyShowText(QString& value);
    //    virtual void onGettingECGDlyShowMin(int& value);
    //    virtual void onGettingECGDlyShowMax(int& value);
    //    virtual void onGettingECGDlyShowStep(int& value);
    //    virtual void onGettingECGDlyShowControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingECGDlyShowPreset(QVariant& value);

    //    virtual void onBeforeECGPosChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onECGPosChanging(const QVariant& value);
    //    virtual void onECGPosChanged(const QVariant& value);
    //    virtual void onGettingECGPosShowValue(QVariant& value);
    //    virtual void onGettingECGPosText(QString& value);
    //    virtual void onGettingECGPosMin(int& value);
    //    virtual void onGettingECGPosMax(int& value);
    //    virtual void onGettingECGPosStep(int& value);
    //    virtual void onGettingECGPosControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingECGPosPreset(QVariant& value);

    //    virtual void onBeforeLeftChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onLeftChanging(const QVariant& value);
    //    virtual void onLeftChanged(const QVariant& value);
    //    virtual void onGettingLeftShowValue(QVariant& value);
    //    virtual void onGettingLeftText(QString& value);
    //    virtual void onGettingLeftMin(int& value);
    //    virtual void onGettingLeftMax(int& value);
    //    virtual void onGettingLeftStep(int& value);
    //    virtual void onGettingLeftControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingLeftPreset(QVariant& value);

    virtual void onBeforePrtOfBChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onGettingPrtOfBIsValidValue(const QVariant& value, bool& valid);
    //    virtual void onPrtOfBChanging(const QVariant& value);
    //    virtual void onPrtOfBChanged(const QVariant& value);
    //    virtual void onGettingPrtOfBShowValue(QVariant& value);
    virtual void onGettingPrtOfBText(QString& value);
    //    virtual void onGettingPrtOfBMin(int& value);
    //    virtual void onGettingPrtOfBMax(int& value);
    //    virtual void onGettingPrtOfBStep(int& value);
    virtual void onGettingPrtOfBControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingPrtOfBPreset(QVariant& value);

    virtual void onPrtOfB_DeltaChanging(const QVariant& value);

    //    virtual void onBeforePrtOfCChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onPrtOfCChanging(const QVariant& value);
    //    virtual void onPrtOfCChanged(const QVariant& value);
    //    virtual void onGettingPrtOfCShowValue(QVariant& value);
    virtual void onGettingPrtOfCText(QString& value);
    //    virtual void onGettingPrtOfCMin(int& value);
    //    virtual void onGettingPrtOfCMax(int& value);
    //    virtual void onGettingPrtOfCStep(int& value);
    //    virtual void onGettingPrtOfCControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingPrtOfCPreset(QVariant& value);

    //    virtual void onBeforePrtOfDChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onPrtOfDChanging(const QVariant& value);
    //    virtual void onPrtOfDChanged(const QVariant& value);
    //    virtual void onGettingPrtOfDShowValue(QVariant& value);
    virtual void onGettingPrtOfDText(QString& value);
    //    virtual void onGettingPrtOfDMin(int& value);
    //    virtual void onGettingPrtOfDMax(int& value);
    //    virtual void onGettingPrtOfDStep(int& value);
    //    virtual void onGettingPrtOfDControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingPrtOfDPreset(QVariant& value);

    //    virtual void onBeforeMVelocityChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onMVelocityChanging(const QVariant& value);
    //    virtual void onMVelocityChanged(const QVariant& value);
    //    virtual void onGettingMVelocityShowValue(QVariant& value);
    //    virtual void onGettingMVelocityText(QString& value);
    //    virtual void onGettingMVelocityMin(int& value);
    //    virtual void onGettingMVelocityMax(int& value);
    //    virtual void onGettingMVelocityStep(int& value);
    //    virtual void onGettingMVelocityControlTableValue(const QVariant& value, int& controlTableValue);

    //    virtual void onBeforeParaPresetTimeChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onParaPresetTimeChanging(const QVariant& value);
    //    virtual void onParaPresetTimeChanged(const QVariant& value);
    //    virtual void onGettingParaPresetTimeShowValue(QVariant& value);
    virtual void onGettingParaPresetTimeText(QString& value);
    //    virtual void onGettingParaPresetTimeMin(int& value);
    //    virtual void onGettingParaPresetTimeMax(int& value);
    //    virtual void onGettingParaPresetTimeStep(int& value);
    //    virtual void onGettingParaPresetTimeControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingParaPresetTimePreset(QVariant& value);

    virtual void onGettingParaPresetTime_XBFText(QString& value);

    //    virtual void onBeforeAdjustmentOfBChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onAdjustmentOfBChanging(const QVariant& value);
    //    virtual void onAdjustmentOfBChanged(const QVariant& value);
    //    virtual void onGettingAdjustmentOfBShowValue(QVariant& value);
    virtual void onGettingAdjustmentOfBText(QString& value);
    //    virtual void onGettingAdjustmentOfBMin(int& value);
    //    virtual void onGettingAdjustmentOfBMax(int& value);
    //    virtual void onGettingAdjustmentOfBStep(int& value);
    //    virtual void onGettingAdjustmentOfBControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingAdjustmentOfBPreset(QVariant& value);

    //    virtual void onBeforeAdjustmentOfCChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onAdjustmentOfCChanging(const QVariant& value);
    //    virtual void onAdjustmentOfCChanged(const QVariant& value);
    //    virtual void onGettingAdjustmentOfCShowValue(QVariant& value);
    virtual void onGettingAdjustmentOfCText(QString& value);
    //    virtual void onGettingAdjustmentOfCMin(int& value);
    //    virtual void onGettingAdjustmentOfCMax(int& value);
    //    virtual void onGettingAdjustmentOfCStep(int& value);
    //    virtual void onGettingAdjustmentOfCControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingAdjustmentOfCPreset(QVariant& value);

    //    virtual void onBeforeAdjustmentOfDChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onAdjustmentOfDChanging(const QVariant& value);
    //    virtual void onAdjustmentOfDChanged(const QVariant& value);
    //    virtual void onGettingAdjustmentOfDShowValue(QVariant& value);
    virtual void onGettingAdjustmentOfDText(QString& value);
    //    virtual void onGettingAdjustmentOfDMin(int& value);
    //    virtual void onGettingAdjustmentOfDMax(int& value);
    //    virtual void onGettingAdjustmentOfDStep(int& value);
    //    virtual void onGettingAdjustmentOfDControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingAdjustmentOfDPreset(QVariant& value);

    //    virtual void onBeforeRvTxBChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onRvTxBChanging(const QVariant& value);
    //    virtual void onRvTxBChanged(const QVariant& value);
    //    virtual void onGettingRvTxBShowValue(QVariant& value);
    virtual void onGettingRvTxBText(QString& value);
    //    virtual void onGettingRvTxBMin(int& value);
    //    virtual void onGettingRvTxBMax(int& value);
    //    virtual void onGettingRvTxBStep(int& value);
    //    virtual void onGettingRvTxBControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingRvTxBPreset(QVariant& value);

    //    virtual void onBeforeRvTxCChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onRvTxCChanging(const QVariant& value);
    //    virtual void onRvTxCChanged(const QVariant& value);
    //    virtual void onGettingRvTxCShowValue(QVariant& value);
    virtual void onGettingRvTxCText(QString& value);
    //    virtual void onGettingRvTxCMin(int& value);
    //    virtual void onGettingRvTxCMax(int& value);
    //    virtual void onGettingRvTxCStep(int& value);
    //    virtual void onGettingRvTxCControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingRvTxCPreset(QVariant& value);

    //    virtual void onBeforeRvTxDChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onRvTxDChanging(const QVariant& value);
    //    virtual void onRvTxDChanged(const QVariant& value);
    //    virtual void onGettingRvTxDShowValue(QVariant& value);
    virtual void onGettingRvTxDText(QString& value);
    //    virtual void onGettingRvTxDMin(int& value);
    //    virtual void onGettingRvTxDMax(int& value);
    //    virtual void onGettingRvTxDStep(int& value);
    //    virtual void onGettingRvTxDControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingRvTxDPreset(QVariant& value);

    //    virtual void onBeforeContrastSelChanged(const QVariant& oldValue, QVariant& newValue);
    //    virtual void onContrastSelChanging(const QVariant& value);
    //    virtual void onContrastSelChanged(const QVariant& value);
    //    virtual void onGettingContrastSelShowValue(QVariant& value);
    //    virtual void onGettingContrastSelText(QString& value);
    //    virtual void onGettingContrastSelMin(int& value);
    //    virtual void onGettingContrastSelMax(int& value);
    //    virtual void onGettingContrastSelStep(int& value);
    //    virtual void onGettingContrastSelControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingContrastSelPreset(QVariant& value);

    //    virtual void onBeforeQFlowModeChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onQFlowModeChanging(const QVariant& value);
    //    virtual void onQFlowModeChanged(const QVariant& value);
    //    virtual void onGettingQFlowModeShowValue(QVariant& value);
    //    virtual void onGettingQFlowModeText(QString& value);
    //    virtual void onGettingQFlowModeMin(int& value);
    //    virtual void onGettingQFlowModeMax(int& value);
    //    virtual void onGettingQFlowModeStep(int& value);
    //    virtual void onGettingQFlowModeControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingQFlowModePreset(QVariant& value);

    //    virtual void onBeforeQBeamOnChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onQBeamOnChanging(const QVariant& value);
    //    virtual void onQBeamOnChanged(const QVariant& value);
    //    virtual void onGettingQBeamOnShowValue(QVariant& value);
    //    virtual void onGettingQBeamOnText(QString& value);
    //    virtual void onGettingQBeamOnMin(int& value);
    //    virtual void onGettingQBeamOnMax(int& value);
    //    virtual void onGettingQBeamOnStep(int& value);
    //    virtual void onGettingQBeamOnControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingQBeamOnPreset(QVariant& value);

    //    virtual void onBeforeQFlowOnChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onQFlowOnChanging(const QVariant& value);
    //    virtual void onQFlowOnChanged(const QVariant& value);
    //    virtual void onGettingQFlowOnShowValue(QVariant& value);
    //    virtual void onGettingQFlowOnText(QString& value);
    //    virtual void onGettingQFlowOnMin(int& value);
    //    virtual void onGettingQFlowOnMax(int& value);
    //    virtual void onGettingQFlowOnStep(int& value);
    //    virtual void onGettingQFlowOnControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingQFlowOnPreset(QVariant& value);

    virtual void onGettingCVRTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingCVLTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingCETControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPDCVRTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingSNCVRTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPDCVLTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingSNCVLTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPDCETControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingSNCETControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingTDICVRTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingTDICVLTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingTDICETControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingMVICVRTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingMVICVLTControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingMVICETControlTableValue(const QVariant& value, int& controlTableValue);

    virtual void onGettingCVRTText(QString& value);
    virtual void onGettingCVLTText(QString& value);
    virtual void onGettingCETText(QString& value);
    virtual void onGettingPDCVRTText(QString& value);
    virtual void onGettingPDCVLTText(QString& value);
    virtual void onGettingSNCVLTText(QString& value);
    virtual void onGettingPDCETText(QString& value);
    virtual void onGettingSNCETText(QString& value);
    virtual void onGettingSNCVRTText(QString& value);
    virtual void onGettingTDICVRTText(QString& value);
    virtual void onGettingTDICVLTText(QString& value);
    virtual void onGettingTDICETText(QString& value);
    virtual void onGettingMVICVRTText(QString& value);
    virtual void onGettingMVICVLTText(QString& value);
    virtual void onGettingMVICETText(QString& value);

    virtual void onCVRTDeltaChanging(const QVariant& value);
    virtual void onCVLTDeltaChanging(const QVariant& value);
    virtual void onCETDeltaChanging(const QVariant& value);

    virtual void onBeforeROISteerAnglesChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onGettingROISteerAnglesIsValidValue(const QVariant& value, bool& valid);
    virtual void onROISteerAnglesChanging(const QVariant& value);
    //    virtual void onROISteerAnglesChanged(const QVariant& value);
    //    virtual void onGettingROISteerAnglesShowValue(QVariant& value);
    //    virtual void onGettingROISteerAnglesText(QString& value);
    //    virtual void onGettingROISteerAnglesMin(int& value);
    //    virtual void onGettingROISteerAnglesMax(int& value);
    //    virtual void onGettingROISteerAnglesStep(int& value);
    //    virtual void onGettingROISteerAnglesControlTableValue(const QVariant& value, int& controlTableValue);
    //    virtual void onGettingROISteerAnglesPreset(QVariant& value);

    virtual void oniImageEffectChanging(const QVariant& value);
    virtual void oniImageEffectRotationChanging(const QVariant& value);

    virtual void onGettingGainTDIControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGainTDI_DeltaChanging(const QVariant& value);

    virtual void onGettingGainColorControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingGainPDControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingGainSNControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingGainMVIControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGainColorTM_DeltaChanging(const QVariant& value);

    virtual void onGettingAFE_InImpedenceText(QString& value);
    virtual void onGettingAFE_InImpedenceMax(int& value);

    virtual void onGettingAFE_LNA_GAINText(QString& value);
    virtual void onGettingAFE_LNA_GAINMax(int& value);

    virtual void onGettingAFE_PGA_GAINText(QString& value);
    virtual void onGettingAFE_PGA_GAINMax(int& value);

    virtual void onAFE_LPF_FCutOffChanged(const QVariant& value);
    virtual void onGettingAFE_LPF_FCutOffText(QString& value);
    virtual void onGettingAFE_LPF_FCutOffMax(int& value);

    virtual void onGettingAFE_HPF_FCutOffText(QString& value);
    virtual void onGettingAFE_HPF_FCutOffMax(int& value);

    virtual void onGettingAFE_LNA_BiasText(QString& value);
    virtual void onGettingAFE_LNA_BiasMax(int& value);

    virtual void onGettingFocusNumBMax(int& value);
    virtual void onBeforeCQYZChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onGettingCQYZStep(int& value);
    virtual void onGettingCQYZIsValidValue(const QVariant& value, bool& valid);
    virtual void onZoomSelectChanging(const QVariant& value);

    virtual void updatePRFCWD();
    virtual void updatePRFDop();

private slots:
    void updateDPixelSizeSec();
    void updateMPixelSizeSec();
    void updatePRFColor();

    void updatePRTOfB();
    void updatePRTOfC();
    void updatePRTOfD();
    void updateDopAccumulateNum(); //根据DVelocity和Prf计算AccumulateNum
    void onImagePixelBitsChanged(int value);
public slots:
    void onUpdate(unsigned char* buf, int len);

protected:
    virtual IBFKitFactory* createBFKitFactory();
    virtual void createParameterModels();
    virtual void update(unsigned char* buf, int len);
    //    virtual void imageProcessing(quint8 *data, quint32 size, qint16 error, quint64 flag);
    virtual void imageIntoUnstable(int ms = -1);
    virtual void initializeParameterCalculators();
    virtual void initializePostParameterCalculators();
    virtual void initializeBFFpsParameter();
    virtual void initializeElastoPrfCalculator();
    virtual void initBFMpixelSizeSecParameterCalculator(); // ebit和qbit计算方式变了,需要不同的实现
    virtual void initCWDPRFParameterCalculator();          // eco6cw的prf实现与ebit不一样
    virtual void initBFPRTOfCParameterCaculator();
    virtual void initBFPRTOfDParameterCaculator();
    virtual void resentCurProbeBlockData();
    virtual void updatePrfList();
    virtual void sendCQYZBlockDataGroup();
    virtual void sendMDFData();
    virtual void createFocusesCombineModel();
    virtual void createStaticParameters();
    virtual void updateCPDSteerStr();
    virtual void updateRealtimeParameter(const QString& param);
    virtual int getCWDLines(int speed, int sampleRate) const;

private:
    QString timeParaText(const QString& paraName) const;

protected:
    FocusParasModel* m_FocusParasModel;
    FGCSettingModel* m_FGCSettingModel;
    FocusesCombineModel* m_FocusesCombineModel;
    float m_AFELPF;
};

#endif // BEAMFORMEREBIT_H
