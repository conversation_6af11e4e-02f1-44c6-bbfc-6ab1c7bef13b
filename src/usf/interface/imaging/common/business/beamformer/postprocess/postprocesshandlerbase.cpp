#include "postprocesshandlerbase.h"
#include "bfpnames.h"
#include "parameter.h"
#include "modelconfig.h"
#include "setting.h"
#include "relatedparascontroller.h"
#include "imagerenderlayoutrects.h"
#include "panzoomboxcontroller.h"
#include "sonoparameters.h"
#include "istatemanager.h"
#include "bfdepthparameters.h"
#include "bfadfreqparameter.h"
#include "probedataset.h"
#include "autofocusadjuster.h"
#include "bffocusparameters.h"
#include "qtaudioplayer.h"
#include "systemscanmodeclassifier.h"
#include <qmath.h>

PostProcessHandlerBase::PostProcessHandlerBase(QObject* parent)
    : QObject(parent)
    , m_GainValue(0)
    , m_RelatedParasController(nullptr)
    , m_ImageRenderLayoutRects(nullptr)
    , m_StateManager(nullptr)
    , m_DepthParameters(nullptr)
    , m_StaticParameters(nullptr)
    , m_PanZoomBoxController(nullptr)
    , m_IsUpateParameters(false)
{
    //第一次不需要连接信号
    m_IfConnectSignals = false;
}

PostProcessHandlerBase::~PostProcessHandlerBase()
{
    m_RelatedParasController = nullptr;
    m_ImageRenderLayoutRects = nullptr;
}

void PostProcessHandlerBase::setSonoParameters(SonoParameters* sonoParameters)
{
    //    if (m_SonoParameters != sonoParameters)
    {
        SonoParametersClientBase::setSonoParameters(sonoParameters);
    }
    //    else
    //    {
    //        // sonobufer的参数变更存在浅拷贝变更，因此指针相同时，需要刷新一下连接参数的信号槽
    //        m_IfConnectSignals = false;
    //        updateConnectedSignals();
    //    }
}

void PostProcessHandlerBase::updatePhysicalGeometryControllerSonoParameters()
{
    if (m_PanZoomBoxController == nullptr)
    {
        m_PanZoomBoxController = new PanZoomBoxController(m_SonoParameters);
        m_PanZoomBoxController->setStateManager(m_StateManager);
    }
    else
    {
        m_PanZoomBoxController->setSonoParameters(m_SonoParameters);
    }
}

void PostProcessHandlerBase::updateConnectedSignals()
{
    if (m_IsUpateParameters && !m_SonoParameters->isRealTime())
    {
        autoCallSelfObjectConnetedChangedSlots();
    }
}

PhysicalGeometryController* PostProcessHandlerBase::panZoomBoxController() const
{
    return m_PanZoomBoxController;
}

void PostProcessHandlerBase::setRelatedParasController(IRelatedParasController* relatedParasController)
{
    m_RelatedParasController = relatedParasController;
}

void PostProcessHandlerBase::setImageRenderLayoutRects(ImageRenderLayoutRects* imageRenderLayoutRects)
{
    m_ImageRenderLayoutRects = imageRenderLayoutRects;
}

void PostProcessHandlerBase::setStateManager(IStateManager* stateManager)
{
    m_StateManager = stateManager;
}

void PostProcessHandlerBase::setDepthParameters(BFDepthParameters* depthParameters)
{
    m_DepthParameters = depthParameters;
}

void PostProcessHandlerBase::setStaticParameters(BaseBFStaticParameters* staticParameters)
{
    m_StaticParameters = staticParameters;
}

void PostProcessHandlerBase::setIsUpateParameters(bool update)
{
    m_IsUpateParameters = update;
}

void PostProcessHandlerBase::onSetSonoParameters()
{
    m_ImageRenderLayoutRects->setSonoParameters(m_SonoParameters);
    updatePhysicalGeometryControllerSonoParameters();
    updateConnectedSignals();
}

void PostProcessHandlerBase::onGettingGainControlTableValue(const QVariant& value, int& controlTableValue)
{
    m_GainValue = value.toInt();

    if (pBV(BFPNames::EnhanceStr))
    {
        controlTableValue = qBound(pMin(BFPNames::GainStr), qRound(controlTableValue * 1.2), pMax(BFPNames::GainStr));
    }

    if (pIV(BFPNames::SraGainColorDeltaStr) != 0 && (syncMode() == Sync_C || pBV(BFPNames::ColorMStr)) &&
        pBV(BFPNames::FcpdOnStr))
    {
        controlTableValue = qBound(pMin(BFPNames::GainStr), controlTableValue + pIV(BFPNames::SraGainColorDeltaStr),
                                   pMax(BFPNames::GainStr));
    }

    if (pIV(BFPNames::BWGainDeltaStr) != 0 && pBV(BFPNames::TDIEnStr))
    {
        controlTableValue =
            qBound(pMin(BFPNames::GainStr), controlTableValue + pIV(BFPNames::BWGainDeltaStr), pMax(BFPNames::GainStr));
    }

    setPV(BFPNames::GainShowStr, controlTableValue);
}

void PostProcessHandlerBase::onGettingGainThiControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingGainControlTableValue(value, controlTableValue);
}

void PostProcessHandlerBase::onMaxGainChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlBTgcGain();
}

void PostProcessHandlerBase::onGainShowChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlBTgcGain();
}

void PostProcessHandlerBase::onGettingGainShowText(QString& value)
{
    value = QVariant(m_GainValue).toString();
}

void PostProcessHandlerBase::onGainDopChanging(const QVariant& value)
{
    setPV(BFPNames::GainDopShowStr, value);
}

void PostProcessHandlerBase::onGainDopTMChanging(const QVariant& value)
{
    onGainDopChanging(value);
}

void PostProcessHandlerBase::onGainDopTDIChanging(const QVariant& value)
{
    onGainDopChanging(value);
}

void PostProcessHandlerBase::onGainDopCWDChanging(const QVariant& value)
{
    onGainDopChanging(value);
}

void PostProcessHandlerBase::onMGainChanging(const QVariant& value)
{
    if (!pBV(BFPNames::THIStr))
    {
        setPV(BFPNames::MGainShowStr, value);
    }
}

void PostProcessHandlerBase::onMGainThiChanging(const QVariant& value)
{
    onMGainChanging(value);
}

void PostProcessHandlerBase::onGettingDynamicRangeShowValue(QVariant& value)
{
    value = value.toInt() * ModelConfig::instance()
                                .value(ModelConfig::DynamicRangeStep, Setting::instance().defaults().dynamicRangeStep())
                                .toInt() +
            ModelConfig::instance()
                .value(ModelConfig::DynamicRangeBase, Setting::instance().defaults().dynamicRangeBase())
                .toInt();
}

void PostProcessHandlerBase::onGettingDynamicRangeControlTableValue(const QVariant& value, int& controlTableValue)
{
    //这里必须和2个Offset经过计算，否则 在DynamicParasSender中修改的控制表中的值，又会恢复成原始值
    controlTableValue = m_RelatedParasController->dynamicRangeWithOffset(value.toInt());
}

void PostProcessHandlerBase::onGettingMDynamicRangeShowValue(QVariant& value)
{
    value = value.toInt() * ModelConfig::instance()
                                .value(ModelConfig::DynamicRangeStep, Setting::instance().defaults().dynamicRangeStep())
                                .toInt() +
            ModelConfig::instance()
                .value(ModelConfig::DynamicRangeBase, Setting::instance().defaults().dynamicRangeBase())
                .toInt();
}

void PostProcessHandlerBase::onPWDynamicRangeChanging(const QVariant& value)
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::PWDynamicRangeShowStr, value);
    }
}

void PostProcessHandlerBase::onGettingPWDynamicRangeShowValue(QVariant& value)
{
    value = pwDynamicRangeShowValue(pIV(BFPNames::PWDynamicRangeStr));
}

void PostProcessHandlerBase::onPWDynamicRangeTMChanging(const QVariant& value)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::PWDynamicRangeShowStr, value);
    }
}

void PostProcessHandlerBase::onGettingPWDynamicRangeTMShowValue(QVariant& value)
{
    value = pwDynamicRangeShowValue(pIV(BFPNames::PWDynamicRangeTMStr));
}

void PostProcessHandlerBase::onPWDynamicRangeShowChanging(const QVariant& value)
{
    setPV(pBV(BFPNames::TriplexModeStr) ? BFPNames::PWDynamicRangeTMStr : BFPNames::PWDynamicRangeStr, value);
}

void PostProcessHandlerBase::onGettingPWDynamicRangeShowShowValue(QVariant& value)
{
    value = pwDynamicRangeShowValue(pIV(BFPNames::PWDynamicRangeShowStr));
}

void PostProcessHandlerBase::onGettingPWDynamicRangeTDIShowValue(QVariant& value)
{
    value = pwDynamicRangeShowValue(pIV(BFPNames::PWDynamicRangeTDIStr));
}

void PostProcessHandlerBase::onGettingPWDynamicRangeCWDShowValue(QVariant& value)
{
    value = pwDynamicRangeShowValue(pIV(BFPNames::PWDynamicRangeCWDStr));
}

void PostProcessHandlerBase::onBaseLineChanging(const QVariant& value)
{
    int intervalCount = pMax(BFPNames::BaseLineStr) - pMin(BFPNames::BaseLineStr) + 2;
    setPV(BFPNames::DBaseLineYPosStr,
          (intervalCount - 1 - value.toInt()) * (pV(BFPNames::DImageSizeStr).toSize().height() / intervalCount));
}

void PostProcessHandlerBase::onGettingBaseLineShowValue(QVariant& value)
{
    value = value.toInt() - parameter(BFPNames::BaseLineStr)->valueCount() / 2;
}

void PostProcessHandlerBase::onGettingBaseLineControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pMax(BFPNames::BaseLineStr) - value.toInt();
}

void PostProcessHandlerBase::onBaseLineDTDIChanging(const QVariant& value)
{
    onBaseLineChanging(value);
}

void PostProcessHandlerBase::onGettingBaseLineDTDIShowValue(QVariant& value)
{
    onGettingBaseLineShowValue(value);
}

void PostProcessHandlerBase::onGettingBaseLineDTDIControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingBaseLineControlTableValue(value, controlTableValue);
}

void PostProcessHandlerBase::onBaseLineCWDChanging(const QVariant& value)
{
    onBaseLineChanging(value);
}

void PostProcessHandlerBase::onGettingBaseLineCWDShowValue(QVariant& value)
{
    onGettingBaseLineShowValue(value);
}

void PostProcessHandlerBase::onGettingBaseLineCWDControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingBaseLineControlTableValue(value, controlTableValue);
}

void PostProcessHandlerBase::onTGCChanging(const QVariant& value)
{
    QByteArray tgc = value.toByteArray();
    QStringList tgcStrs;
    for (int i = 0; i < TGC_COUNT; ++i)
    {
        tgcStrs << QString("%1%2").arg(BFPNames::TGCStr).arg(i + 1);
    }

    for (int i = 0; i < tgc.count() && i < tgcStrs.count(); i++)
    {
        setPV(tgcStrs[i], (uchar)tgc[i]);
    }
}

void PostProcessHandlerBase::onImageZoomCoefChanging(const QVariant& value)
{
    if (isZoomCoefEnabled())
    {
        setPDV(BFPNames::ImageZoomCoefBStr, value);
        parameter(BFPNames::ImageZoomCoefBStr)->update();
    }
    else
    {
        parameter(BFPNames::ImageZoomCoefOtherStr)->update();
    }
}

void PostProcessHandlerBase::onGettingImageZoomCoefText(QString& value)
{
    value = QString("%1 %").arg(pIV(BFPNames::ImageZoomCoefStr));
}

void PostProcessHandlerBase::onImageZoomCoefBChanging(const QVariant& value)
{
    int iCoef = value.toInt();
    double fCoef = iCoef / 100.0f;
    setPV(BFPNames::ImageZoomCoefBinStr, Util::floatToBinary(1.0 / fCoef));
    setPV(BFPNames::ZoomInTopOffsetStr, zoomInTopOffset(fCoef, pBV(BFPNames::HalfHeightStr)));
    m_DepthParameters->setImageZoomCoef(fCoef);
    if (!pBV(BFPNames::IsSupportIntegerDepthStr))
    {
        parameter(BFPNames::CQYZStr)->update();
    }
    else
    {
        parameter(BFPNames::CQYZLevelStr)->update();
    }
}

void PostProcessHandlerBase::onImageZoomCoefOtherChanging(const QVariant& value)
{
    onImageZoomCoefBChanging(value);
}

void PostProcessHandlerBase::onHalfHeightChanging(const QVariant& value)
{
    setPV(BFPNames::HalfHeight2Str, value);
    m_DepthParameters->setHalfHeight(value.toBool());
    parameter(BFPNames::CQYZStr)->update();
}

void PostProcessHandlerBase::onCQYZChanging(const QVariant& value)
{
    m_DepthParameters->setCQYZ(value.toInt());
    m_DepthParameters->setZoomedCQYZ(pIV(BFPNames::ZoomedCQYZStr));
    m_ImageRenderLayoutRects->updateRenderImageScale();

    qreal probeDSCImageZoomCof = 1.0;
    double imageHeight = pV(BFPNames::RenderImageSizeStr).toSize().height();

    m_DepthParameters->setPixelLen(BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) /
                                   pFV(BFPNames::FixedSWImageZoomCofStr) * probeDSCImageZoomCof);
    m_DepthParameters->setImageHeight(imageHeight);
    setPV(BFPNames::ProbeDSCImageZoomCofStr, probeDSCImageZoomCof);
    double zoomCoef = pIV(BFPNames::ImageZoomCoefStr) / 100.0;
    m_DepthParameters->setImageZoomCoef(zoomCoef);
    //    m_DepthParameters->setHeightFactor(pIV(BFPNames::ImageZoomCoefStr) / 100.0);

    double depthMM = m_DepthParameters->depthMM();
    double depthMMShow = m_DepthParameters->depthMM() / pDV(BFPNames::RenderImageZoomCofStr);
    setPV(BFPNames::RealDepthMMStr, depthMM);
    setPV(BFPNames::DepthMMStr, depthMMShow, pBV(BFPNames::ZoomOnStr));
    double pixelSizeMM = m_DepthParameters->getPixelizeMMWithFactor() / pDV(BFPNames::RenderImageZoomCofStr);
    qDebug() << PRETTY_FUNCTION << " origin pixelSizeMM:" << pixelSizeMM;

    if (!curProbe().IsLinear && pBV(BFPNames::IsSupportIntegerDepthStr))
    {
        pixelSizeMM = depthMMShow / imageHeight / zoomCoef / m_DepthParameters->heightFactor();
    }
    setPV(BFPNames::PixelSizeMMStr, pixelSizeMM);
    setPV(BFPNames::StartDepthMMStr, startDepthMM());
    // TODO Lotus使用的pixelSizeMM没有考虑Halfheight,需要向FPGA确认
    setPV(BFPNames::PixelsWithinFocusStr, (curProbe().IsLinear ? 5.0 : 10.0) / pixelSizeMM);
    setPV(BFPNames::UnzoomedCQYZStr, value);

    qDebug() << PRETTY_FUNCTION << "ImageZoomCoef:" << pIV(BFPNames::ImageZoomCoefStr)
             << "PixelSizeMMStr:" << pFV(BFPNames::PixelSizeMMStr)
             << "unZoomPixelSizeMM:" << pFV(BFPNames::PixelSizeMMStr) * pIV(BFPNames::ImageZoomCoefStr) / 100.0
             << "DepthMMStr:" << pFV(BFPNames::DepthMMStr) << "imageHeight" << imageHeight;

    // cqyz改变后更新AdjustmentOfB
    if (value.toInt() >= 0 && value.toInt() < curProbe().AdjustmentOfBs.count())
    {
        setPV(BFPNames::AdjustmentOfBStr, curProbe().AdjustmentOfBs[value.toInt()]);
    }

    AutoFocusAdjuster::instance().autoFocus(m_SonoParameters);

    if (!pBV(BFPNames::ZoomOnStr))
    {
        if (syncMode() == Sync_None || systemScanMode() == SystemScanModeAV)
        {
            adjustFocusPosByDepth(BFPNames::FocusNumBStr, BFPNames::FocusPosBStr);
        }
        else if (syncMode() == Sync_M)
        {
            adjustFocusPosByDepth(BFPNames::FocusNumMStr, BFPNames::FocusPosMStr);
        }
        //其他模式，都是根据ROI或者采样门自动调节focusPos
    }

    if (!m_SonoParameters->pBV(BFPNames::FreeMModeStr))
    {
        ImageModeType imageMode = (ImageModeType)(pIV(BFPNames::ImageModeStr));
        int format = imageMode == MODE_UDBM ? pIV(BFPNames::MDisplayFormatStr) : -1;
        double mHeightZoomFacotr = RenderLayoutDesigner::imageRowScaleFacator(format, 1).yScale;
        m_SonoParameters->setPV(BFPNames::MPixelSizeMMStr,
                                m_DepthParameters->getPixelizeMMWithFactor(mHeightZoomFacotr) /
                                    pDV(BFPNames::RenderImageZoomCofStr));
    }
}

void PostProcessHandlerBase::onBCImagesOnChanging(const QVariant& value)
{
    m_ImageRenderLayoutRects->updateRenderImageScale();
    setPV(BFPNames::LayoutBImageSizeStr, m_ImageRenderLayoutRects->getLayoutBImageSize());
    setPV(BFPNames::BImageSizeStr, m_ImageRenderLayoutRects->getBImageSize());
    setPV(BFPNames::RenderBImageSizeStr, m_ImageRenderLayoutRects->getRenderBImageSize());
    setPV(BFPNames::MImageSizeStr, m_ImageRenderLayoutRects->getMImageSize());
    setPV(BFPNames::DImageSizeStr, m_ImageRenderLayoutRects->getDImageSize());
    setPV(BFPNames::ImageModeRectsStr, m_ImageRenderLayoutRects->getModeRects(QSize(
                                           m_SonoParameters->pV(BFPNames::RenderWidgetSizeStr).toSize().width(),
                                           m_SonoParameters->pV(BFPNames::RenderImageSizeStr).toSize().height())));
    setPV(BFPNames::ImageRegionsStr, m_ImageRenderLayoutRects->getImageRegions());
    //    setPV(BFPNames::ImageRenderRectsStr, m_ImageRenderLayoutRects->getImageRenderRects());
    setPV(BFPNames::RenderWidgetRectsStr, m_ImageRenderLayoutRects->getRenderWidgetRects());
    setPV(BFPNames::DSCImageRectsStr, m_ImageRenderLayoutRects->getDSCImageRects());
    setPV(BFPNames::ImageRectsStr, m_ImageRenderLayoutRects->getImageRects());

    if (value.toBool())
    {
        setPV(BFPNames::ImageModeStr, MODE_LCRB);
    }
    else
    {
        if (pIV(BFPNames::ImageModeStr) == MODE_LCRB || pIV(BFPNames::ImageModeStr) == MODE_LBRC)
        {
            setPV(BFPNames::ImageModeStr, MODE_B);
            setPV(BFPNames::SystemScanModeStr, systemScanMode(), true);
        }
    }

    //    controlImageZoomCoef();
    m_StateManager->changeBCImages(value.toBool());
}

void PostProcessHandlerBase::onVolumeChanged(const QVariant& value)
{
    Q_UNUSED(value)
}

void PostProcessHandlerBase::onVolumeTDIChanged(const QVariant& value)
{
}

void PostProcessHandlerBase::onVolumeCWDChanged(const QVariant& value)
{
}

void PostProcessHandlerBase::onGettingVolumeText(QString& value)
{
    value = calVolumeShowText();
}

void PostProcessHandlerBase::onGettingVolumeTDIText(QString& value)
{
    value = calVolumeShowText();
}

void PostProcessHandlerBase::onGettingVolumeCWDText(QString& value)
{
    value = calVolumeShowText();
}

void PostProcessHandlerBase::onGettingCinePlaySpeedAdjustText(QString& value)
{
    int effectionValue = pIV(BFPNames::CinePlaySpeedAdjustStr);
    if (effectionValue == 1)
    {
        value = "X0.5";
    }
    else if (effectionValue == 2)
    {
        value = "X1";
    }
    else if (effectionValue == 3)
    {
        value = "X2";
    }
    else
    {
        value = "X1";
    }
}

void PostProcessHandlerBase::onRotationChanging(const QVariant& value)
{
    if (pBV(BFPNames::ECGEnStr))
    {
        setPV(BFPNames::ECGEnStr, false);
    }
    setPV(BFPNames::LayoutBImageSizeStr, m_ImageRenderLayoutRects->getLayoutBImageSize());
    setPV(BFPNames::BImageSizeStr, m_ImageRenderLayoutRects->getBImageSize());
    setPV(BFPNames::RenderBImageSizeStr, m_ImageRenderLayoutRects->getRenderBImageSize());
    setPV(BFPNames::MImageSizeStr, m_ImageRenderLayoutRects->getMImageSize());
    setPV(BFPNames::DImageSizeStr, m_ImageRenderLayoutRects->getDImageSize());
    setPV(BFPNames::ImageRegionsStr, m_ImageRenderLayoutRects->getImageRegions());
    setPV(BFPNames::RenderWidgetRectsStr, m_ImageRenderLayoutRects->getRenderWidgetRects());
    setPV(BFPNames::ImageRectsStr, m_ImageRenderLayoutRects->getImageRects());

    //下面这个调用会在旋转后把Imagezoomcoef 重置成100，先注释掉
    //    controlImageZoomCoef();
}

void PostProcessHandlerBase::onGettingRotationText(QString& value)
{
    int rotation = pIV(BFPNames::RotationStr);
    value = QString("%1°").arg(rotation);
}

SyncModeType PostProcessHandlerBase::syncMode() const
{
    return (SyncModeType)(pIV(BFPNames::SyncModeStr));
}

SystemScanMode PostProcessHandlerBase::systemScanMode() const
{
    return (SystemScanMode)pIV(BFPNames::SystemScanModeStr);
}

int PostProcessHandlerBase::pwDynamicRangeShowValue(int index) const
{
    return 46 + index * 3;
}

void PostProcessHandlerBase::adjustFocusPosByDepth(const QString& focusNumName, const QString& focusPosName)
{
    //根据深度调整焦点位置
    int oldFocusPos = pIV(focusPosName);
    int advisedPos =
        BFFocusParameters().adviseFocusPos(curProbe(), pIV(focusNumName), pIV(focusPosName), imageBottomDepthMM());

    if ((oldFocusPos != advisedPos))
    {
        setPV(focusPosName, advisedPos);
    }
}

bool PostProcessHandlerBase::isZoomCoefEnabled() const
{
    return !pBV(BFPNames::HalfHeightStr);
}

void PostProcessHandlerBase::controlBTgcGain()
{
    int gain = pIV(BFPNames::GainShowStr);
    if (gain == 0)
    {
        gain = 5;
    }

    int gainMap = gain;
    if (gain > 128)
    {
        gainMap = (gain - 128) * 4 + 128;
    }

    int maxGainMap = (int)(pIV(BFPNames::MaxGainStr) * qPow(2, 11));

    setPV(BFPNames::BTgcGainStr, maxGainMap / gainMap);
}

const ProbeDataInfo& PostProcessHandlerBase::curProbe() const
{
    return ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
}

double PostProcessHandlerBase::imageBottomDepthMM() const
{
    ProbeParameters p(curProbe(), startDepthMM(), pixelSizeMM(), pBV(BFPNames::ZoomOnStr), pIV(BFPNames::StartLineStr),
                      pIV(BFPNames::StopLineStr));
    return p.bottomDepthMM(depthMM());
}

double PostProcessHandlerBase::pixelSizeMM() const
{
    return pDV(BFPNames::PixelSizeMMStr);
}

double PostProcessHandlerBase::startDepthMM() const
{
    double start = 0.0;
    if (pBV(BFPNames::ZoomOnStr))
    {
        start += pDV(BFPNames::ZoomDepthMMStr);
    }
    // Scroll状态
    if (pBV(BFPNames::IsScrollStr))
    {
        start += pDV(BFPNames::ScrollDepthMMStr);
    }

    return start;
}

double PostProcessHandlerBase::depthMM() const
{
    return pDV(BFPNames::DepthMMStr);
}

int PostProcessHandlerBase::zoomInTopOffset(double zoomInPercent, bool halfHeight)
{
    int coef = halfHeight ? 2 : 1;

    ImageModeType mode = (ImageModeType)(pIV(BFPNames::ImageModeStr));

    int rotation = pIV(BFPNames::RotationStr);
    bool isHorizontalRotate = (rotation == 90 || rotation == 270);

    QSize imageSize = pV(BFPNames::ImageSizeStr).toSize();
    // 90度/270度 的2B,左右BM模式中的B图像进行zoomcoef的缩放时，图像始波区顶部与图像region(0, 0, 320, 512)顶部
    // 的高度，要用 640/2 = 320 来进行缩放
    int height = (isHorizontalRotate && (mode == MODE_BM || mode == MODE_2B || pBV(BFPNames::BCImagesOnStr)))
                     ? (imageSize.width() / 2)
                     : imageSize.height();

    return qRound(height * (1.0F - zoomInPercent) / 2 / coef);
}

int PostProcessHandlerBase::calVolumeShowValue()
{
    QString volumePara = currentVolumeParaName();

    int max = pMax(volumePara);
    int min = pMin(volumePara);
    int value = pIV(volumePara);
    int percent = (int)(100.0f * (value - min) / (max - min) + 0.5f);
    return percent;
}

QString PostProcessHandlerBase::calVolumeShowText()
{
    return QString("%1 %").arg(calVolumeShowValue());
}

QString PostProcessHandlerBase::currentVolumeParaName()
{
    QString ret = BFPNames::VolumeStr;
    if (m_SonoParameters == nullptr)
    {
        return ret;
    }
    if (pBV(BFPNames::TDIEnStr))
    {
        ret = BFPNames::VolumeTDIStr;
    }
    else if (SystemScanModeClassifier::isLikeCW(pIV(BFPNames::SystemScanModeStr)))
    {
        ret = BFPNames::VolumeCWDStr;
    }

    return ret;
}
