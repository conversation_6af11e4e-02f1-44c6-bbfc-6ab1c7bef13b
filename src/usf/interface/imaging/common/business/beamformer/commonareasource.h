/*
 * =====================================================================================
 *
 *       Filename:  commonareasource.h
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  09/08/2014 09:59:03 AM
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  YOUR NAME (),
 *   Organization:
 *
 * =====================================================================================
 */

#ifndef COMMON_AREA_SOURCE
#define COMMON_AREA_SOURCE

#include "iareasource.h"
#include "infostruct.h"
#include <QObject>

class ISonoParameters;
class BFDepthParameters;

class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BaseAreaSource : public QObject, public AbstractAreaSource
{
    Q_OBJECT
public:
    BaseAreaSource(const ISonoParameters* sonoParameters, QSizeF wholeImageSize, QObject* parent = NULL,
                   AreaResultType type = AreaResult_TypeNULL);

    virtual ~BaseAreaSource();

    virtual int type() const;

    virtual QSizeF wholeImageSize() const
    {
        return m_wholeImageSize;
    }

    virtual qreal outerAreaStartDepthMM() const;

    virtual qreal pixelSizeMM() const;

    virtual qreal depthMM() const;

    virtual qreal imageZoomCoefNormalized() const;

    virtual QPointF centerPosOffset() const;

    virtual QStringList changedSources() const;

    void setwholeImageSize(const QSize& size)
    {
        m_wholeImageSize = size;
    }

    int startLine() const override;

    int stopLine() const override;

private slots:
    void onCQYZChanging(const QVariant& value);

    void onADFreqMHzChanging(const QVariant& value);

    void onTwoDImageScaleFactorChanged(const QVariant& value);

    void onHalfHeightChanging(const QVariant& value);

    void onRenderImageZoomCofStr(const QVariant& value);

protected:
    qreal paramShowValueAngle2Rad(const QString& name) const;

    int pIV(const QString& name) const;

    bool pBV(const QString& name) const;

    qreal pRV(const QString& name) const;

    const ISonoParameters* sonoParameters() const
    {
        return m_sonoParameters;
    }

protected:
    BFDepthParameters* m_BFDepthParameters;
    AreaResultType m_Type;

private:
    const ISonoParameters* m_sonoParameters;
    QSizeF m_wholeImageSize;
};

class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BAreaSource : public BaseAreaSource
{
    Q_OBJECT
public:
    BAreaSource(const ISonoParameters* sonoParameters, QSizeF wholeImageSize, QObject* parent = NULL,
                bool compoundMode = false, AreaResultType type = AreaResult_TypeNULL)
        : BaseAreaSource(sonoParameters, wholeImageSize, parent, type)
    {
        m_compoundMode = compoundMode;
    }

    virtual ~BAreaSource()
    {
    }

    virtual QStringList changedSources() const;

    virtual ProbePhysicalGeometry area() const;

    virtual qreal steeringRad() const;

    virtual qreal cPDSteerRad() const;

private:
    bool m_compoundMode;
};

class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT ROIAreaSource : public BaseAreaSource
{
    Q_OBJECT
public:
    enum UseMode
    {
        Color,
        TDI,
        Elasto,
        FourDPre,
        MVI,
        SonoNeedle,

        UseModeCount
    };

    ROIAreaSource(const ISonoParameters* sonoParameters, QSizeF wholeImageSize, QObject* parent = NULL,
                  UseMode mode = Color, AreaResultType type = AreaResult_TypeNULL)
        : BaseAreaSource(sonoParameters, wholeImageSize, parent, type)
    {
        m_mode = mode;
    }

    virtual ~ROIAreaSource()
    {
    }

    virtual QStringList changedSources() const;

    virtual ProbePhysicalGeometry area() const;

    virtual qreal steeringRad() const;

    virtual qreal cPDSteerRad() const;
    virtual QString lineDensityName() const;
    virtual QString SteerAngleName() const;

    bool considerZoomOn() const override;

private:
    UseMode m_mode;

    static const QString RoiMidLineStr[UseModeCount];
    static const QString RoiHalfLineStr[UseModeCount];
    static const QString RoiMidDepthMMStr[UseModeCount];
    static const QString RoiHalfDepthMMStr[UseModeCount];
    static const QString LineDensityStr[UseModeCount];
    static const QString SteeringAngleStr[UseModeCount];
};

#endif /* end of include guard: COMMON_AREA_SOURCE */
