#include "freqsetting.h"
#include "bfpnames.h"
#include "infostruct.h"
#include "isonoparameters.h"
#include "probedataset.h"
#include "util.h"
#include <QDebug>
#include <QDir>
#include <QScopedPointer>
#include <QSettings>
#include <QtGlobal>

FreqSetting::FreqSetting()
    : BaseFreqSetting()
    , m_hasSpaceCompounding1(true)

{
    initialize(*this);
}

VariantHashList& FreqSetting::nullCompounding()
{
    return m_NullCompounding;
}

const VariantHashList& FreqSetting::nullCompounding() const
{
    return m_NullCompounding;
}

VariantHashListHash& FreqSetting::freqCompounding()
{
    return m_FreqCompounding;
}

const VariantHashListHash& FreqSetting::freqCompounding() const
{
    return m_FreqCompounding;
}

VariantHashListHash& FreqSetting::spaceCompounding()
{
    return m_SpaceCompounding;
}

const VariantHashListHash& FreqSetting::spaceCompounding() const
{
    return m_SpaceCompounding;
}

VariantHashList& FreqSetting::dynamicParas()
{
    return m_DynamicParas;
}

const VariantHashList& FreqSetting::dynamicParas() const
{
    return m_DynamicParas;
}

VariantHash& FreqSetting::multiBeamParas()
{
    return m_MultiBeamParas;
}

const VariantHash& FreqSetting::multiBeamParas() const
{
    return m_MultiBeamParas;
}

VariantHash& FreqSetting::vsParas()
{
    return m_VSParas;
}

VariantHash& FreqSetting::arbitraryWaveParas()
{
    return m_ArbitraryWaveParas;
}

const VariantHash& FreqSetting::arbitraryWaveParas() const
{
    return m_ArbitraryWaveParas;
}

const VariantHash& FreqSetting::vsParas() const
{
    return m_VSParas;
}

void FreqSetting::save(QSettings& settings) const
{
    BaseFreqSetting::save(settings);

    m_NullCompounding.save(settings);
    m_FreqCompounding.save(settings);
    m_SpaceCompounding.save(settings);
    m_DynamicParas.save(settings);
    m_MultiBeamParas.save(settings);
    m_VSParas.save(settings);
    m_ArbitraryWaveParas.save(settings);
}

void FreqSetting::save(QDataStream& stream) const
{
    BaseFreqSetting::save(stream);

    stream << m_NullCompounding;
    stream << m_FreqCompounding;
    stream << m_SpaceCompounding;
    stream << m_DynamicParas;
    stream << m_MultiBeamParas;
    stream << m_VSParas;
    stream << m_ArbitraryWaveParas;
}

bool FreqSetting::load(QSettings& settings)
{
    if (!BaseFreqSetting::load(settings))
    {
        return false;
    }

    if (!m_NullCompounding.load("N", "P", settings))
    {
        if (!m_NullCompounding.load("NullCompounding", "ParasOfTransmit", settings))
        {
            return false;
        }
    }

    if (!m_FreqCompounding.load("F", "G", "P", settings))
    {
        if (!m_FreqCompounding.load("FreqCompounding", "G", "ParasOfTransmit", settings))
        {
            return false;
        }
    }

    //兼容老的数据结构，新的数据结构不带S1
    m_hasSpaceCompounding1 = true;
    if (!m_SpaceCompounding.load("S1", "G", "P", settings))
    {
        if (!m_SpaceCompounding.load("SpaceCompounding1", "G", "ParasOfTransmit", settings))
        {
            m_hasSpaceCompounding1 = false;
        }
    }

    if (!m_SpaceCompounding.load("S", "G", "P", settings, !m_hasSpaceCompounding1, 1))
    {
        if (!m_SpaceCompounding.load("SpaceCompounding", "G", "ParasOfTransmit", settings, !m_hasSpaceCompounding1, 1))
        {
            return false;
        }
    }

    if (!m_DynamicParas.load("D", "P", settings))
    {
        if (!m_DynamicParas.load("DynamicParas", "DynamicPara", settings))
        {
            return false;
        }
    }

    if (!m_MultiBeamParas.load("M", settings))
    {
        cleanArbitraryWaveParasFromCommon();
        return false;
    }

    if (!m_VSParas.load("VS", settings))
    {
        cleanArbitraryWaveParasFromCommon();
        return false;
    }

    if (!m_ArbitraryWaveParas.load("A", settings))
    {
        // 不再从Common中加载，而是从已经加载的C中取出任意波形的参数存进m_ArbitraryWaveParas，同时删除掉C中的任意波形参数
        m_ArbitraryWaveParas.clear();
        m_ArbitraryWaveParas.setName("A");
        m_ArbitraryWaveParas[BFPNames::ArbitraryWaveStr] = 0;
        m_ArbitraryWaveParas[BFPNames::ArbitraryWaveFormExtendStr] = 0;
        m_ArbitraryWaveParas[BFPNames::ArbitraryWaveForm1Str] = "";
        m_ArbitraryWaveParas[BFPNames::ArbitraryWaveForm2Str] = "";
        m_ArbitraryWaveParas[BFPNames::ArbitraryWaveForm3Str] = "";
        m_ArbitraryWaveParas[BFPNames::ArbitraryWaveForm4Str] = "";
        m_ArbitraryWaveParas[BFPNames::ArbitraryWaveForm6Str] = "";
        m_ArbitraryWaveParas[BFPNames::ArbitraryWaveForm7Str] = "";
        cleanArbitraryWaveParasFromCommon(true);
    }
    else
    {
        // 清理这组里面Common中可能包含的任意波形参数
        cleanArbitraryWaveParasFromCommon();
    }

    return true;
}

bool FreqSetting::load(QDataStream& stream)
{
    BaseFreqSetting::load(stream);
    stream >> m_NullCompounding;
    stream >> m_FreqCompounding;
    stream >> m_SpaceCompounding;
    if ((m_SpaceCompounding.name().compare(QString("S1")) == 0) ||
        (m_SpaceCompounding.name().compare("SpaceCompounding1")) == 0)
    {
        m_hasSpaceCompounding1 = true;
    }
    else
    {
        m_hasSpaceCompounding1 = false;
    }
    stream >> m_DynamicParas;
    stream >> m_MultiBeamParas;
    stream >> m_VSParas;
    stream >> m_ArbitraryWaveParas;
    return true;
}

QStringList FreqSetting::commonPNames() const
{
    return QStringList() << BFPNames::UniformityTgcStrs << BFPNames::UTGC_SegmentStr << BFPNames::AnalogTgcStrs
                         << BFPNames::PhasedProbeTgcStrs << commonParaStrs();
}

QStringList FreqSetting::groupNames() const
{
    return QStringList() << "AllParas"
                         << "CommonParas"
                         << "UniformityTgcParas"
                         << "AnalogTgcParas"
                         << "PhasedProbeTgcParas"
                         << "NullCompoundingParas"
                         << "FreqCompoundingParas"
                         << "SpaceCompoundingParas"
                         << "SpaceCompounding1Paras"
                         << "DynamicParas";
}

void FreqSetting::fromControlTable(ISonoParameters* sonoParas, const QString& freqIndexStr)
{
    BaseFreqSetting::fromControlTable(sonoParas, freqIndexStr);

    controlTable2TransmitParas(sonoParas, this);
}

void FreqSetting::copyGroupParaFrom(const FreqSetting& other, int group)
{
    switch (group)
    {
    case FreqSetting::All:
        copyFrom<FreqSetting>(other);
        break;
    case FreqSetting::Common:
        copyVariantHash(other.common(), m_Common, commonParaStrs() << arbitraryWaveParaStrs());
        break;
    case FreqSetting::UniformityTgc:
        copyVariantHash(other.common(), m_Common, BFPNames::UniformityTgcStrs);
        m_Common[BFPNames::UTGC_SegmentStr] = other.common()[BFPNames::UTGC_SegmentStr];
        break;
    case FreqSetting::AnalogTgc:
        copyVariantHash(other.common(), m_Common, BFPNames::AnalogTgcStrs);
        break;
    case FreqSetting::PhasedProbeTgc:
        copyVariantHash(other.common(), m_Common, BFPNames::PhasedProbeTgcStrs);
        break;
    case FreqSetting::NullCompounding:
        m_NullCompounding = other.nullCompounding();
        break;
    case FreqSetting::FreqCompounding:
        m_FreqCompounding = other.freqCompounding();
        break;
    case FreqSetting::SpaceCompounding:
        m_SpaceCompounding = other.spaceCompounding();
        break;
    case FreqSetting::Dynamic:
        m_DynamicParas = other.dynamicParas();
        break;
    case FreqSetting::MultiBeam:
        m_MultiBeamParas = other.multiBeamParas();
        break;
    case FreqSetting::VS:
        m_VSParas = other.vsParas();
        break;
    case FreqSetting::ArbitraryWave:
        m_ArbitraryWaveParas = other.arbitraryWaveParas();
        break;
    }
}

void FreqSetting::copyOneDynamicPallToAll(int dynamicRange)
{
    copyOneVariantHashToAll(dynamicRange, m_DynamicParas);
}

// added this by jinyuqi to add save fs
static void VariantHashList2tigerParasOfTransmit(const VariantHashList& hs, ParasStruct::ParasOfTransmit* paras)
{
    for (int i = 0; i < hs.count(); i++)
    {
        paras[i].PulseNum = hs[i][FreqSetting::PulseNumStr].value<uchar>();
        paras[i].HighFreq = hs[i][FreqSetting::HighFreqStr].value<uchar>();
        paras[i].Frequency = hs[i][FreqSetting::FrequencyStr].value<uchar>();
        paras[i].FilterCoef = hs[i][FreqSetting::FilterCoefStr].value<uchar>();
        paras[i].FocusNum = hs[i][FreqSetting::FocusNumStr].value<uchar>();
    }
}

static void tigerParasOfTransmit2VariantHashList(ParasStruct::ParasOfTransmit* paras, VariantHashListHash& hs)
{
    //    for(int i=0; i<hs.count(); i++)
    //    {
    //        hs[i][FreqSetting::PulseNumStr] = paras[i].PulseNum;
    //        hs[i][FreqSetting::HighFreqStr] = paras[i].HighFreq;
    //        hs[i][FreqSetting::FrequencyStr] = paras[i].Frequency;
    //        hs[i][FreqSetting::FilterCoefStr] = paras[i].FilterCoef;
    //        hs[i][FreqSetting::FocusNumStr] = paras[i].FocusNum;
    //    }
}

bool FreqSetting::loadFromTigerFs(const QString& fileName)
{
    QScopedPointer<ParasStruct::FreqSetting> tigerFs(new ParasStruct::FreqSetting());
    QFile file(fileName);
    if (file.open(QIODevice::ReadOnly) && (file.size() == sizeof(ParasStruct::FreqSetting)))
    {
        file.read((char*)tigerFs.data(), sizeof(ParasStruct::FreqSetting));
        file.close();
    }
    else
    {
        return false;
    }

    int index = 0;
    foreach (const QString& str, BFPNames::UniformityTgcStrs)
    {
        this->common()[str] = tigerFs->Common.UniformityTgc[index++];
    }

    index = 0;
    foreach (const QString& str, BFPNames::AnalogTgcStrs)
    {
        this->common()[str] = tigerFs->Common.AnalogTgc[index++];
    }

    //    foreach(const QString& str, BFPNames::PhasedProbeTgcStrs)
    //    {
    //        this->common()[str] = 128;
    //    }

    this->common()[BFPNames::LogCompressionStr] = tigerFs->Common.LogCompression;
    this->common()[BFPNames::MFCStr] = tigerFs->Common.MFC;
    this->common()[BFPNames::HF_AlphaStr] = tigerFs->Common.HF_Alpha;
    this->common()[BFPNames::FilterCpdStr] = tigerFs->Common.FilterCpd;
    this->common()[BFPNames::IIR_ONStr] = tigerFs->Common.IIR_ON;
    this->common()[BFPNames::HFStr] = tigerFs->Common.HF;
    this->common()[BFPNames::DRDiffWithCompoundOnStr] = tigerFs->Common.DynamicOffsetOfCPD;
    this->common()[BFPNames::DRDiffWithSraOnStr] = tigerFs->Common.DynamicOffsetOfSRA;
    //    this->common()[BFPNames::WtStartPointStr] = 0;
    //    this->common()[BFPNames::WtCoefficienceStr] = 0;

    Q_ASSERT("need to edit tigerParasOfTransmit2VariantHashList.");
    //    tigerParasOfTransmit2VariantHashList(tigerFs->NullCompounding, this->nullCompounding());
    //    tigerParasOfTransmit2VariantHashList(tigerFs->FreqCompounding, this->freqCompounding());
    //    tigerParasOfTransmit2VariantHashList(tigerFs->SpaceCompounding, this->spaceCompounding());

    for (int i = 0; i < (int)(sizeof(tigerFs->Dynamic) / sizeof(ParasStruct::DynamicParas)); i++)
    {
        this->dynamicParas()[i][BFPNames::DynStartDepthStr] = tigerFs->Dynamic[i].DynStartDepth; // CM
        this->dynamicParas()[i][BFPNames::DynStartDBStr] = tigerFs->Dynamic[i].DynStartDB;       // Index 0~15
        this->dynamicParas()[i][BFPNames::DynEndDepthStr] = tigerFs->Dynamic[i].DynEndDepth;     // CM
        this->dynamicParas()[i][BFPNames::DynEndDBStr] = tigerFs->Dynamic[i].DynEndDB;           // Index 0~15
    }
    return true;
}

bool FreqSetting::saveToTigerFs(const QString destPath) const
{
    // init struct
    QScopedPointer<ParasStruct::FreqSetting> tigerFs(new ParasStruct::FreqSetting());

    QString fsFileName; // the fs file 's name
    QString fsFileID;
    // id create
    if (m_Head.m_Id == -1)
    {
        fsFileID = "default";
    }
    else
    {
        fsFileID = "user_" + QString::number(m_Head.m_Id);
    }
    fsFileName += fsFileID;
    // freq name create
    QString fsFileFreqName = m_Head.m_FreqName;
    fsFileFreqName = "_" + fsFileFreqName;

    fsFileName += fsFileFreqName;
    // thi create
    QString fsFileThi;
    if (m_Head.m_THI == true)
    {
        fsFileThi = "_thi_on";
    }
    else
        fsFileThi = "_thi_off";
    fsFileName += fsFileThi;
    QString fsFreqIndex = "FI_" + QString::number(this->m_Head.m_FreqIndex);
    fsFileName += "_" + fsFreqIndex + ".fs";

    // to make sure weather create it on current path or set the disk path by jinyuqi
    QDir dir;
    if (destPath == QString())
        ;
    else
        dir.cd(destPath);
    qDebug() << "Dir's paht is " << dir.absolutePath();
    dir.mkdir("sonotouch_fs_files");
    QString fsDirName = ProbeDataSet::instance().getProbe(m_Head.m_ProbeId).Name;
    dir.cd("sonotouch_fs_files");
    dir.mkdir(fsDirName);

    // create the file's name
    fsFileName = destPath + "/sonotouch_fs_files/" + fsDirName + "/" + fsFileName;
    qDebug() << fsFileName;

    int index = 0;
    foreach (const QString& str, BFPNames::UniformityTgcStrs)
    {
        tigerFs->Common.UniformityTgc[index++] = this->common()[str].value<uchar>();
    }

    index = 0;
    foreach (const QString& str, BFPNames::AnalogTgcStrs)
    {
        tigerFs->Common.AnalogTgc[index++] = this->common()[str].value<uchar>();
    }

    tigerFs->Common.LogCompression = this->common()[BFPNames::LogCompressionStr].value<uchar>();
    tigerFs->Common.MFC = this->common()[BFPNames::MFCStr].value<uchar>();
    tigerFs->Common.HF_Alpha = this->common()[BFPNames::HF_AlphaStr].value<uchar>();
    tigerFs->Common.FilterCpd = this->common()[BFPNames::FilterCpdStr].value<uchar>();
    tigerFs->Common.IIR_ON = this->common()[BFPNames::IIR_ONStr].value<uchar>();
    tigerFs->Common.HF = this->common()[BFPNames::HFStr].value<uchar>();
    tigerFs->Common.DynamicOffsetOfCPD = this->common()[BFPNames::DRDiffWithCompoundOnStr].value<signed char>();
    tigerFs->Common.DynamicOffsetOfSRA = this->common()[BFPNames::DRDiffWithSraOnStr].value<signed char>();

    //    VariantHashList2tigerParasOfTransmit(this->nullCompounding(),tigerFs->NullCompounding);
    //    VariantHashList2tigerParasOfTransmit(this->freqCompounding(),tigerFs->FreqCompounding);
    //    VariantHashList2tigerParasOfTransmit(this->spaceCompounding(),tigerFs->SpaceCompounding);

    for (int i = 0; i < (int)(sizeof(tigerFs->Dynamic) / sizeof(ParasStruct::DynamicParas)); i++)
    {
        tigerFs->Dynamic[i].DynStartDepth = this->dynamicParas()[i][BFPNames::DynStartDepthStr].value<uchar>(); // CM
        tigerFs->Dynamic[i].DynStartDB = this->dynamicParas()[i][BFPNames::DynStartDBStr].value<uchar>(); // Index 0~15
        tigerFs->Dynamic[i].DynEndDepth = this->dynamicParas()[i][BFPNames::DynEndDepthStr].value<uchar>(); // CM
        tigerFs->Dynamic[i].DynEndDB = this->dynamicParas()[i][BFPNames::DynEndDBStr].value<uchar>(); // Index 0~15
    }

    //    UTIL_DATA_SYNCER
    QFile file(fsFileName);
    qDebug() << "fsfilename" << file.fileName();
    if (file.open(QIODevice::WriteOnly))
    {
        qDebug() << file.write((char*)tigerFs.data(), sizeof(ParasStruct::FreqSetting));
    }
    file.close();

    // Util::sync();
    return true;
}

/**
 * @brief dynamicRange2DB
 *
 * @param value 0~15
 *
 * @return 54~84 dB
 */
int FreqSetting::dynamicRange2DB(int value)
{
    return value * 2 + 54;
}

/**
 * @brief dB2DynamicRange
 *
 * @param dBvalue 54~84 dB
 *
 * @return 0~15
 */
int FreqSetting::dB2DynamicRange(int dBvalue)
{
    return (dBvalue - 54) / 2;
}

bool FreqSetting::getHasSpaceCompounding1()
{
    return m_hasSpaceCompounding1;
}

bool FreqSetting::loadCommon(QSettings& settings)
{
    if (!m_Common.load("C", settings, false))
    {
        if (!m_Common.load("Common", settings, false))
        {
            return false;
        }
    }
    return true;
}

void FreqSetting::initialize(FreqSetting& fs)
{
    fs.common().setName("C");

    foreach (const QString& str, BFPNames::UniformityTgcStrs)
    {
        fs.common()[str] = 64;
    }

    foreach (const QString& str, BFPNames::AnalogTgcStrs)
    {
        fs.common()[str] = 128;
    }
    fs.common()[BFPNames::AnalogTgc8Str] = 6;
    fs.common()[BFPNames::AnalogTgc9Str] = 8;
    fs.common()[BFPNames::AnalogTgc10Str] = 10;
    fs.common()[BFPNames::AnalogTgc11Str] = 12;
    fs.common()[BFPNames::AnalogTgc12Str] = 14;
    fs.common()[BFPNames::AnalogTgc13Str] = 16;
    fs.common()[BFPNames::AnalogTgc14Str] = 18;
    fs.common()[BFPNames::AnalogTgc15Str] = 0;

    foreach (const QString& str, BFPNames::PhasedProbeTgcStrs)
    {
        fs.common()[str] = 128;
    }

    foreach (const QString& str, commonParaStrs())
    {
        fs.common()[str] = 0;
    }
    fs.common()[BFPNames::WtStartPointStr] = 255;
    fs.common()[BFPNames::RvFNoStr] = 3;
    fs.common()[BFPNames::RvPWFNoStr] = 3;
    fs.common()[BFPNames::Rv4DFNoStr] = 3;
    fs.common()[BFPNames::TxFNoStr] = 5;
    fs.common()[BFPNames::TXFNo_Steer1Str] = 5;
    fs.common()[BFPNames::TxIndex5Str] = 5;
    fs.common()[BFPNames::TxIndex7Str] = 5;
    fs.common()[BFPNames::TriplexTxFNoStr] = 5;
    fs.common()[BFPNames::TxGataCompensationStr] = 0;
    fs.common()[BFPNames::SoGataCompensationStr] = 40;
    fs.common()[BFPNames::ATGC_GATEStr] = 0;
    fs.common()[BFPNames::CPDSteerStr] = 5;
    fs.common()[BFPNames::CPDSteer2Str] = 5;
    fs.common()[BFPNames::CPDSteer3Str] = 5;
    fs.common()[BFPNames::CPDSteer4Str] = 5;
    fs.common()[BFPNames::CPDSteer5Str] = 5;
    fs.common()[BFPNames::TCPDSteerStr] = 5;
    fs.common()[BFPNames::CPDSteer_CEUSStr] = 5;
    fs.common()[BFPNames::CpdMinValueStr] = 30;
    fs.common()[BFPNames::CpdMaxValueStr] = 180;
    fs.common()[BFPNames::HFSSPStr] = 3;
    fs.common()[BFPNames::HFSZStr] = 4;
    fs.common()[BFPNames::GaussFilterSwitchStr] = 1;
    fs.common()[BFPNames::ThiModeStr] = 1;
    fs.common()[BFPNames::MaxTransmitApertureControlStr] = 63;
    fs.common()[BFPNames::MaxGainStr] = 198;
    fs.common()[BFPNames::TransmitStepDisStr] = 1;
    fs.common()[BFPNames::ReceiveFocusDisStr] = 1;
    fs.common()[BFPNames::MaxReceiveApertureStr] = 63;
    fs.common()[BFPNames::BTxEndClampStr] = 100;
    fs.common()[BFPNames::SpklSmooth_CaseSelStr] = 0;
    fs.common()[BFPNames::SpklSooth_alphaStr] = 0;
    fs.common()[BFPNames::BModeDutyStr] = 0;
    fs.common()[BFPNames::MF_PostScaleStr] = 255;
    fs.common()[BFPNames::MF_Coef_Fundamental_Steer0Str] = 0;
    fs.common()[BFPNames::MF_Coef_Fundamental_SteerXStr] = 0;
    fs.common()[BFPNames::MF_Coef_Harmonic_Steer0Str] = 0;
    fs.common()[BFPNames::MF_Coef_Harmonic_SteerXStr] = 0;
    fs.common()[BFPNames::XCorrOnStr] = 1;
    fs.common()[BFPNames::XCorrStepStr] = 1;
    fs.common()[BFPNames::XCorrCutOffSelStr] = 1;
    fs.common()[BFPNames::XCorrCoefThresholdStr] = 1;
    fs.common()[BFPNames::AmplificationCoefStr] = 8;
    fs.common()[BFPNames::TissueAmplificationCoeffStr] = 8;

    fs.multiBeamParas().setName("M");
    fs.multiBeamParas()[BFPNames::OverlappingMBStr] = 2;
    fs.multiBeamParas()[BFPNames::InterpolatorOrderStr] = 0.0;
    fs.multiBeamParas()[BFPNames::StripRemoveEnableStr] = false;

    fs.arbitraryWaveParas().setName("A");
    fs.arbitraryWaveParas()[BFPNames::ArbitraryWaveStr] = 0;
    fs.arbitraryWaveParas()[BFPNames::ArbitraryWaveFormExtendStr] = 0;
    fs.arbitraryWaveParas()[BFPNames::ArbitraryWaveForm1Str] = "";
    fs.arbitraryWaveParas()[BFPNames::ArbitraryWaveForm2Str] = "";
    fs.arbitraryWaveParas()[BFPNames::ArbitraryWaveForm3Str] = "";
    fs.arbitraryWaveParas()[BFPNames::ArbitraryWaveForm4Str] = "";
    fs.arbitraryWaveParas()[BFPNames::ArbitraryWaveForm6Str] = "";
    fs.arbitraryWaveParas()[BFPNames::ArbitraryWaveForm7Str] = "";

    fs.nullCompounding().setName("N");
    fs.nullCompounding().setItemName("P");
    for (int i = 0; i < 1; i++)
    {
        fs.nullCompounding().append(VariantHash());
        fs.nullCompounding().last().setName(fs.nullCompounding().itemName());
        initializeParasOfTransmit(fs.nullCompounding().last());
    }

    fs.freqCompounding().setName("F");
    fs.freqCompounding().setItemName("P");
    for (int i = 0; i < 2; i++)
    {
        VariantHashList groupList("G", "P");
        for (int j = 0; j < 2; j++)
        {
            groupList.append(VariantHash());
            groupList.last().setName(groupList.itemName());
            initializeParasOfTransmit(groupList.last());
        }
        fs.freqCompounding().insert(QString::number(i), groupList);
    }

    fs.spaceCompounding().setName("S");
    fs.spaceCompounding().setItemName("P");
    for (int i = 0; i < 4; i++)
    {
        VariantHashList groupList("G", "P");
        // groupList最后一个存储CpdSteer
        // xbit，因为要支持多帧复合，需要增加几个角度，同时将相关的角度从lotus转过来
        //所以，原有的最后一个CpdSteer后面再追加TCpdSteer、CpdSteer_CEUS、CpdSteerFive、CpdSteerSeven、CpdSteerNine、CpdSteerEleven、
        //      CpdSteer前面追加发射6、发射7
        for (int j = 0; j < 12; j++)
        {
            groupList.append(VariantHash());
            groupList.last().setName(groupList.itemName());
            initializeParasOfTransmit(groupList.last());
        }
        fs.spaceCompounding().insert(QString::number(i), groupList);
    }

    fs.dynamicParas().setName("D");
    fs.dynamicParas().setItemName("P");
    for (int i = 0; i < 16; i++)
    {
        fs.dynamicParas().append(VariantHash());
        fs.dynamicParas().last().setName(fs.dynamicParas().itemName());
        initializeDynamicParas(fs.dynamicParas().last());
    }

    fs.vsParas().setName("VS");
    fs.vsParas()[BFPNames::VS_ModeStr] = false;
    fs.vsParas()[BFPNames::VS_MLAStr] = 2;
}

void FreqSetting::initializeParasOfTransmit(VariantHash& trans)
{
    trans[PulseNumStr] = 1;
    trans[HighFreqStr] = 0;
    trans[FrequencyStr] = 0;
    trans[FilterCoefStr] = 0;
    trans[FocusNumStr] = 0;
    trans[ThiModeStr] = 0;
    trans[TripFrequencyDeltaStr] = 0;
    trans[TripFilterCoeDeltaStr] = 0;
    trans[TransmitPulseExStr] = 0;
    trans[CodeTransmitionLength1Str] = 0;
    trans[CodeTransmitionValue1Str] = 0;
    trans[CodeTransmitionLength2Str] = 0;
    trans[CodeTransmitionValue2Str] = 0;
    trans[DTHIFreqStr] = 0;
    trans[BTX_DutySelStr] = 0;
}

void FreqSetting::initializeDynamicParas(VariantHash& dynamicParas)
{
    dynamicParas[BFPNames::DynStartDepthStr] = 0; // CM
    dynamicParas[BFPNames::DynStartDBStr] = 0;    // Index 0~15
    dynamicParas[BFPNames::DynEndDepthStr] = 0;   // CM
    dynamicParas[BFPNames::DynEndDBStr] = 0;      // Index 0~15
}

void FreqSetting::controlTable2TransmitParas(ISimpleSonoParameters* sonoParas, FreqSetting* fs)
{
    bool scpdOn = sonoParas->pBV(BFPNames::ScpdOnStr);
    bool fcpdOn = sonoParas->pBV(BFPNames::FcpdOnStr);
    bool trapezoidalMode = sonoParas->pBV(BFPNames::TrapezoidalModeStr);
    int scpdValue = sonoParas->pIV(BFPNames::ScpdStr);
    QString scpdIndexStr = trapezoidalMode ? "0" : QString::number(scpdValue - 1);
    QString fcpdIndexStr =
        sonoParas->pIV(BFPNames::SRAIDStr) == 0 ? "0" : QString::number(sonoParas->pIV(BFPNames::SRAIDStr) - 1);

    if (!scpdOn && !fcpdOn && !trapezoidalMode)
    {
        QString itemDefaultName = fs->nullCompounding().itemName();
        fs->nullCompounding().getHashValue(0, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[ThiModeStr] = sonoParas->pV(BFPNames::ThiMode1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[CodeTransmitionLength1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength11Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[CodeTransmitionValue1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue11Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[CodeTransmitionLength2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength12Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[CodeTransmitionValue2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue12Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[DTHIFreqStr] = sonoParas->pV(BFPNames::DTHITxFreq1Str);
        fs->nullCompounding().getHashValue(0, itemDefaultName)[BTX_DutySelStr] =
            sonoParas->pV(BFPNames::BTX1_DutySelStr);
    }
    else if (!scpdOn && fcpdOn && !trapezoidalMode)
    {
        QString itemDefaultName = fs->freqCompounding()[fcpdIndexStr].itemName();
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionLength1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength11Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionValue1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue11Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionLength2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength12Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionValue2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue12Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[DTHIFreqStr] =
            sonoParas->pV(BFPNames::DTHITxFreq1Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(0, itemDefaultName)[BTX_DutySelStr] =
            sonoParas->pV(BFPNames::BTX1_DutySelStr);

        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionLength1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength21Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionValue1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue21Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionLength2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength22Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionValue2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue22Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[DTHIFreqStr] =
            sonoParas->pV(BFPNames::DTHITxFreq2Str);
        fs->freqCompounding()[fcpdIndexStr].getHashValue(1, itemDefaultName)[BTX_DutySelStr] =
            sonoParas->pV(BFPNames::BTX2_DutySelStr);
    }
    else if (scpdValue >= 2 && !trapezoidalMode)
    {
        QString itemDefaultName = fs->spaceCompounding()[scpdIndexStr].itemName();
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionLength1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength11Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionValue1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue11Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionLength2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength12Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionValue2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue12Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[DTHIFreqStr] =
            sonoParas->pV(BFPNames::DTHITxFreq1Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[BTX_DutySelStr] =
            sonoParas->pV(BFPNames::BTX1_DutySelStr);

        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionLength1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength31Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionValue1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue31Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionLength2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength32Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionValue2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue32Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[DTHIFreqStr] =
            sonoParas->pV(BFPNames::DTHITxFreq3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[BTX_DutySelStr] =
            sonoParas->pV(BFPNames::BTX3_DutySelStr);

        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[CodeTransmitionLength1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength41Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[CodeTransmitionValue1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue41Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[CodeTransmitionLength2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength42Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[CodeTransmitionValue2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue42Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[DTHIFreqStr] =
            sonoParas->pV(BFPNames::DTHITxFreq4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[BTX_DutySelStr] =
            sonoParas->pV(BFPNames::BTX4_DutySelStr);

        // scpdValue 大于2 的情况，一定存在了相关的Steer
        //所以，原有的最后一个CpdSteer后面再追加TCpdSteer、CpdSteer_CEUS、CpdSteerFive、CpdSteerSeven、CpdSteerNine、CpdSteerEleven、
        //      CpdSteer前面追加发射6、发射7
        if (scpdValue > 2 && fs->spaceCompounding()[scpdIndexStr].count() > 11)
        {
            fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[PulseNumStr] =
                sonoParas->pV(BFPNames::PulseNumOfTransmit6Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[HighFreqStr] =
                sonoParas->pV(BFPNames::HighFreqOfTransmit6Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[FrequencyStr] =
                sonoParas->pV(BFPNames::FrequencyOfTransmit6Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[FilterCoefStr] =
                sonoParas->pV(BFPNames::FilterCoefOfTransmit6Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[ThiModeStr] =
                sonoParas->pV(BFPNames::ThiMode6Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[TransmitPulseExStr] =
                sonoParas->pV(BFPNames::TransmitPulseEx6Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[BTX_DutySelStr] =
                sonoParas->pV(BFPNames::BTX6_DutySelStr);

            fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[PulseNumStr] =
                sonoParas->pV(BFPNames::PulseNumOfTransmit7Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[HighFreqStr] =
                sonoParas->pV(BFPNames::HighFreqOfTransmit7Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[FrequencyStr] =
                sonoParas->pV(BFPNames::FrequencyOfTransmit7Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[FilterCoefStr] =
                sonoParas->pV(BFPNames::FilterCoefOfTransmit7Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[ThiModeStr] =
                sonoParas->pV(BFPNames::ThiMode7Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[TransmitPulseExStr] =
                sonoParas->pV(BFPNames::TransmitPulseEx7Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[BTX_DutySelStr] =
                sonoParas->pV(BFPNames::BTX7_DutySelStr);

            fs->spaceCompounding()[scpdIndexStr].getHashValue(5, itemDefaultName)[BFPNames::CPDSteerStr] =
                sonoParas->pV(BFPNames::CPDSteerStr);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(6, itemDefaultName)[BFPNames::TCPDSteerStr] =
                sonoParas->pV(BFPNames::TCPDSteerStr);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(7, itemDefaultName)[BFPNames::CPDSteer_CEUSStr] =
                sonoParas->pV(BFPNames::CPDSteer_CEUSStr);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(8, itemDefaultName)[BFPNames::CPDSteer2Str] =
                sonoParas->pV(BFPNames::CPDSteer2Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(9, itemDefaultName)[BFPNames::CPDSteer3Str] =
                sonoParas->pV(BFPNames::CPDSteer3Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(10, itemDefaultName)[BFPNames::CPDSteer4Str] =
                sonoParas->pV(BFPNames::CPDSteer4Str);
            fs->spaceCompounding()[scpdIndexStr].getHashValue(11, itemDefaultName)[BFPNames::CPDSteer5Str] =
                sonoParas->pV(BFPNames::CPDSteer5Str);
        }
        else
        {
            /** 整合Lotus
             * SonoBook9预设值，并比对图像时缺少CPDSteerStr导致图像和SonoBook9不一致，这里的CPDSteer当时在Lotus下是为了支持大于3帧的复合，用CPDSteer来模拟
             *  新机型上不需要
             */
            if (fs->spaceCompounding()[scpdIndexStr].count() > 3 &&
                fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName).contains(BFPNames::CPDSteerStr))
            {
                fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[BFPNames::CPDSteerStr] =
                    sonoParas->pV(BFPNames::CPDSteerStr);
            }
            //增加发射6、发射7的时候，一起将TCPDSteerStr和CPDSteer_CEUSStr给增加了
            if (fs->spaceCompounding()[scpdIndexStr].count() > 4 &&
                fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName).contains(BFPNames::TCPDSteerStr))
            {
                fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[BFPNames::TCPDSteerStr] =
                    sonoParas->pV(BFPNames::TCPDSteerStr);
            }
            if (fs->spaceCompounding()[scpdIndexStr].count() > 5 && fs->spaceCompounding()[scpdIndexStr]
                                                                        .getHashValue(5, itemDefaultName)
                                                                        .contains(BFPNames::CPDSteer_CEUSStr))
            {
                fs->spaceCompounding()[scpdIndexStr].getHashValue(5, itemDefaultName)[BFPNames::CPDSteer_CEUSStr] =
                    sonoParas->pV(BFPNames::CPDSteer_CEUSStr);
            }
        }
    }
    else if (scpdValue == 1)
    {
        QString itemDefaultName = fs->spaceCompounding()[scpdIndexStr].itemName();
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionLength1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength31Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionValue1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue31Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionLength2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength32Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[CodeTransmitionValue2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue32Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[DTHIFreqStr] =
            sonoParas->pV(BFPNames::DTHITxFreq3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(0, itemDefaultName)[BTX_DutySelStr] =
            sonoParas->pV(BFPNames::BTX3_DutySelStr);

        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionLength1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength41Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionValue1Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue41Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionLength2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionLength42Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[CodeTransmitionValue2Str] =
            sonoParas->pV(BFPNames::CodeTransmitionValue42Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[DTHIFreqStr] =
            sonoParas->pV(BFPNames::DTHITxFreq4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[BTX_DutySelStr] =
            sonoParas->pV(BFPNames::BTX4_DutySelStr);

        /** 整合Lotus
         * SonoBook9预设值，并比对图像时缺少CPDSteerStr导致图像和SonoBook9不一致，这里的CPDSteer当时在Lotus下是为了支持大于3帧的复合，用CPDSteer来模拟
         *  新机型上不需要
         */
        if (fs->spaceCompounding()[scpdIndexStr].count() > 2 &&
            fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName).contains(BFPNames::CPDSteerStr))
        {
            fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[BFPNames::CPDSteerStr] =
                sonoParas->pV(BFPNames::CPDSteerStr);
        }

        //增加发射6、发射7的时候，一起将TCPDSteerStr和CPDSteer_CEUSStr给增加了
        if (fs->spaceCompounding()[scpdIndexStr].count() > 3 &&
            fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName).contains(BFPNames::TCPDSteerStr))
        {
            fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[BFPNames::TCPDSteerStr] =
                sonoParas->pV(BFPNames::TCPDSteerStr);
        }
        if (fs->spaceCompounding()[scpdIndexStr].count() > 4 &&
            fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName).contains(BFPNames::CPDSteer_CEUSStr))
        {
            fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[BFPNames::CPDSteer_CEUSStr] =
                sonoParas->pV(BFPNames::CPDSteer_CEUSStr);
        }
    }
    else if (trapezoidalMode)
    {
        QString itemDefaultName = fs->spaceCompounding()[scpdIndexStr].itemName();
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr3Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(1, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx3Str);

        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[FocusNumStr] =
            sonoParas->pV(BFPNames::FocusNumOfTransmit4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[TripFrequencyDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFrequencyDeltaTr4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[TripFilterCoeDeltaStr] =
            sonoParas->pV(BFPNames::TriplexFilterCoefDeltaTr4Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx4Str);

        fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit6Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit6Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit6Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit6Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode6Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(3, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx6Str);

        fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[PulseNumStr] =
            sonoParas->pV(BFPNames::PulseNumOfTransmit7Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[HighFreqStr] =
            sonoParas->pV(BFPNames::HighFreqOfTransmit7Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[FrequencyStr] =
            sonoParas->pV(BFPNames::FrequencyOfTransmit7Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[FilterCoefStr] =
            sonoParas->pV(BFPNames::FilterCoefOfTransmit7Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[ThiModeStr] =
            sonoParas->pV(BFPNames::ThiMode7Str);
        fs->spaceCompounding()[scpdIndexStr].getHashValue(4, itemDefaultName)[TransmitPulseExStr] =
            sonoParas->pV(BFPNames::TransmitPulseEx7Str);

        /** 整合Lotus
         * SonoBook9预设值，并比对图像时缺少CPDSteerStr导致图像和SonoBook9不一致，这里的CPDSteer当时在Lotus下是为了支持大于3帧的复合，用CPDSteer来模拟
         *  新机型上不需要
         */
        if (fs->spaceCompounding()[scpdIndexStr].count() > 2 &&
            fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName).contains(BFPNames::CPDSteerStr))
        {
            fs->spaceCompounding()[scpdIndexStr].getHashValue(2, itemDefaultName)[BFPNames::CPDSteerStr] =
                sonoParas->pV(BFPNames::CPDSteerStr);
        }
    }

    int dynamicRange = sonoParas->pIV(BFPNames::DynamicRangeStr);
    fs->dynamicParas()[dynamicRange][BFPNames::DynStartDepthStr] = sonoParas->pV(BFPNames::DynStartDepthStr); // CM
    fs->dynamicParas()[dynamicRange][BFPNames::DynStartDBStr] = sonoParas->pV(BFPNames::DynStartDBStr);   // Index 0~15
    fs->dynamicParas()[dynamicRange][BFPNames::DynEndDepthStr] = sonoParas->pV(BFPNames::DynEndDepthStr); // CM
    fs->dynamicParas()[dynamicRange][BFPNames::DynEndDBStr] = sonoParas->pV(BFPNames::DynEndDBStr);       // Index 0~15

    fs->multiBeamParas()[BFPNames::OverlappingMBStr] = sonoParas->pV(BFPNames::OverlappingMBStr);
    fs->multiBeamParas()[BFPNames::InterpolatorOrderStr] = sonoParas->pDV(BFPNames::InterpolatorOrderStr);
    fs->multiBeamParas()[BFPNames::StripRemoveEnableStr] = sonoParas->pBV(BFPNames::StripRemoveEnableStr);

    //虚拟源布线
    fs->vsParas()[BFPNames::VS_ModeStr] = sonoParas->pV(BFPNames::VS_ModeStr);
    fs->vsParas()[BFPNames::VS_MLAStr] = sonoParas->pV(BFPNames::VS_MLAStr);

    fs->arbitraryWaveParas()[BFPNames::ArbitraryWaveStr] = sonoParas->pV(BFPNames::ArbitraryWaveStr);
    fs->arbitraryWaveParas()[BFPNames::ArbitraryWaveFormExtendStr] =
        sonoParas->pV(BFPNames::ArbitraryWaveFormExtendStr);
    fs->arbitraryWaveParas()[BFPNames::ArbitraryWaveForm1Str] = sonoParas->pV(BFPNames::ArbitraryWaveForm1Str);
    fs->arbitraryWaveParas()[BFPNames::ArbitraryWaveForm2Str] = sonoParas->pV(BFPNames::ArbitraryWaveForm2Str);
    fs->arbitraryWaveParas()[BFPNames::ArbitraryWaveForm3Str] = sonoParas->pV(BFPNames::ArbitraryWaveForm3Str);
    fs->arbitraryWaveParas()[BFPNames::ArbitraryWaveForm4Str] = sonoParas->pV(BFPNames::ArbitraryWaveForm4Str);
    fs->arbitraryWaveParas()[BFPNames::ArbitraryWaveForm6Str] = sonoParas->pV(BFPNames::ArbitraryWaveForm6Str);
    fs->arbitraryWaveParas()[BFPNames::ArbitraryWaveForm7Str] = sonoParas->pV(BFPNames::ArbitraryWaveForm7Str);
}

void FreqSetting::copyVariantHash(const VariantHash& src, VariantHash& dest, const QStringList& names)
{
    foreach (const QString& name, names)
    {
        dest[name] = src[name];
    }
}

void FreqSetting::copyOneVariantHashToAll(int index, VariantHashList& all)
{
    if (index >= 0 && index < all.count())
    {
        const VariantHash& one = all.at(index);
        for (int i = 0; i < all.count(); i++)
        {
            if (i != index)
            {
                all[i] = one;
            }
        }
    }
}

void FreqSetting::cleanArbitraryWaveParasFromCommon(bool assignParas2A)
{
    foreach (const QString& awParaStr, arbitraryWaveParaStrs())
    {
        if (m_Common.contains(awParaStr))
        {
            if (assignParas2A)
            {
                m_ArbitraryWaveParas[awParaStr] = m_Common[awParaStr];
            }
            m_Common.remove(awParaStr);
        }
    }
}

QStringList FreqSetting::commonParaStrs()
{
    return QStringList() << BFPNames::LogCompressionStr << BFPNames::LogCompressionHarStr
                         << BFPNames::DynDeltaOnXContrastStr << BFPNames::FilterCpdStr << BFPNames::MFCStr
                         << BFPNames::HF_AlphaStr << BFPNames::HF_Alpha_IncStr << BFPNames::IIR_ONStr << BFPNames::HFStr
                         << BFPNames::HFIncStr << BFPNames::DRDiffWithCompoundOnStr << BFPNames::DRDiffWithSraOnStr
                         << BFPNames::DRDiffWithThiOnStr << BFPNames::WtStartPointStr << BFPNames::WtCoefficienceStr
                         << BFPNames::RvPWFNoStr << BFPNames::RvFNoStr << BFPNames::Rv4DFNoStr << BFPNames::TxFNoStr
                         << BFPNames::TXFNo_Steer1Str << BFPNames::TxIndex5Str << BFPNames::TxIndex7Str
                         << BFPNames::TriplexTxFNoStr << BFPNames::TxGataCompensationStr
                         << BFPNames::SoGataCompensationStr << BFPNames::ATGC_GATEStr << BFPNames::CPDSteerStr
                         << BFPNames::CPDSteer2Str << BFPNames::CPDSteer3Str << BFPNames::CPDSteer4Str
                         << BFPNames::CPDSteer5Str << BFPNames::CpdMinValueStr << BFPNames::CpdMaxValueStr
                         << BFPNames::HFSSPStr << BFPNames::HFSZStr << BFPNames::WeightedCurveStr
                         << BFPNames::ConvexCpdSteerStr << BFPNames::FCA_AlphaStr << BFPNames::FCA_DeltaStr
                         << BFPNames::FCA_SwitchStr << BFPNames::FCA_ThresholdUpLimitStr
                         << BFPNames::FCA_ThresholdLowLimitStr << BFPNames::ScaningSignalEntireCompensationStr
                         << BFPNames::ScaningSignalIndividualCompensationStr << BFPNames::BCRelativePosStr
                         << BFPNames::GaussFilterSwitchStr << BFPNames::ThiModeStr
                         << BFPNames::MaxTransmitApertureControlStr << BFPNames::MinTransmitApertureControlStr
                         << BFPNames::MaxGainStr << BFPNames::BFreqDeltaStr << BFPNames::TransmitStepDisStr
                         << BFPNames::TransmitBeamDevEleNumStr << BFPNames::ReceiveFocusDisStr
                         << BFPNames::ReceiveBeamDevEleNumStr << BFPNames::ReceiveUnitVariFocusNumStr
                         << BFPNames::MinReceiveApertureStr << BFPNames::MaxReceiveApertureStr << BFPNames::FScpdStr
                         << BFPNames::BTxEndClampStr << BFPNames::SpklSmooth_CaseSelStr << BFPNames::SpklSooth_alphaStr
                         << BFPNames::BModeDutyStr << BFPNames::MF_PostScaleStr
                         << BFPNames::MF_Coef_Fundamental_Steer0Str << BFPNames::MF_Coef_Fundamental_SteerXStr
                         << BFPNames::MF_Coef_Harmonic_Steer0Str << BFPNames::MF_Coef_Harmonic_SteerXStr
                         << BFPNames::XCorrOnStr << BFPNames::XCorrStepStr << BFPNames::XCorrCutOffSelStr
                         << BFPNames::XCorrCoefThresholdStr << BFPNames::AmplificationCoefStr
                         << BFPNames::TissueAmplificationCoeffStr;
}

QStringList FreqSetting::arbitraryWaveParaStrs()
{
    // 特殊处理，任意波形参数配置在common，显示在TransmitParas
    return QStringList() << BFPNames::ArbitraryWaveFormExtendStr << BFPNames::ArbitraryWaveForm1Str
                         << BFPNames::ArbitraryWaveForm2Str << BFPNames::ArbitraryWaveForm3Str
                         << BFPNames::ArbitraryWaveForm4Str << BFPNames::ArbitraryWaveForm6Str
                         << BFPNames::ArbitraryWaveForm7Str << BFPNames::ArbitraryWaveStr;
    //由于RelatedParasController::sendFreqRelatedParas中任意波形发射的计算和下发的控制采用ArbitraryWave来触发
    //为了保证触发前其他参数已赋值完成，调整此处顺序，对调图界面的参数摆放有些影响
}

QStringList FreqSetting::forcibleParaStrs()
{
    return QStringList() << BFPNames::RvFNoStr << BFPNames::TxFNoStr << BFPNames::CPDSteerStr << BFPNames::CPDSteer2Str
                         << BFPNames::CPDSteer3Str << BFPNames::CPDSteer4Str << BFPNames::CPDSteer5Str
                         << BFPNames::WeightedCurveStr << BFPNames::ArbitraryWaveStr << BFPNames::ThiModeStr;
}

QStringList FreqSetting::multiBeamParaStrs()
{
    return QStringList() << BFPNames::OverlappingMBStr << BFPNames::InterpolatorOrderStr
                         << BFPNames::StripRemoveEnableStr;
}

const QString FreqSetting::PulseNumStr = "PulseNum";     //发射脉冲数
const QString FreqSetting::HighFreqStr = "HighFreq";     //发射高低频标识
const QString FreqSetting::FrequencyStr = "Frequency";   //发射频率
const QString FreqSetting::FilterCoefStr = "FilterCoef"; //匹配滤波系数
const QString FreqSetting::FocusNumStr = "FocusNum";
const QString FreqSetting::ThiModeStr = "ThiMode";
const QString FreqSetting::TripFrequencyDeltaStr = "TripFrequencyDelta";
const QString FreqSetting::TripFilterCoeDeltaStr = "TripFilterCoeDelta";
const QString FreqSetting::TransmitPulseExStr = "TransmitPulseEx"; //发射脉冲扩展
const QString FreqSetting::CodeTransmitionLength1Str = "CodeTransmitionLength1";
const QString FreqSetting::CodeTransmitionValue1Str = "CodeTransmitionValue1";
const QString FreqSetting::CodeTransmitionLength2Str = "CodeTransmitionLength2";
const QString FreqSetting::CodeTransmitionValue2Str = "CodeTransmitionValue2";
const QString FreqSetting::DTHIFreqStr = "DTHIFreq";
const QString FreqSetting::BTX_DutySelStr = "BTX_DutySel";
