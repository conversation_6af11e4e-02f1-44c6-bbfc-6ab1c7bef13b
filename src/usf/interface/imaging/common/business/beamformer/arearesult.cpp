/*
 * =====================================================================================
 *
 *       Filename:  arearesult.cpp
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  09/03/2014 02:23:49 PM
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:   (), |<EMAIL>|
 *   Organization:
 *
 * =====================================================================================
 */

#include "arearesult.h"

#include <cmath>

#include <QTransform>

#include "iareasource.h"
#include "isonoparameters.h"
#include "bfpnames.h"
#include "parameter.h"
#include "probedataset.h"
#include "formula.h"
#include "probeparameters.h"
#include "probephysicalgeometry.h"
#include "assertlog.h"
#include "logger.h"
#include "usglobal.h"
#include "modelconfig.h"
#include <qmath.h>

LOG4QT_DECLARE_STATIC_LOGGER(log, AreaResult)

qreal LinearAreaResult::m_centerLine = 0;

QTransform ConvexAreaResult::flipAndRectZoomCoefTransform() const
{
    QSizeF size = areaSource()->wholeImageSize();

    return Formula::flipAndRectZoomCoefTransform(m_responseFlip ? pBV(BFPNames::UpStr) : true,
                                                 m_responseFlip ? pBV(BFPNames::LeftStr) : true, size,
                                                 areaSource()->imageZoomCoefNormalized());
}

AreaResult::AreaResult(const ISonoParameters* sonoParameters, bool positionConsiderZoomOn, QObject* parent,
                       AreaResultType type)
    : QObject(parent)
    , m_sonoParameters(sonoParameters)
    , m_positionConsiderZoomOn(positionConsiderZoomOn)
    , m_areaTrigger(NULL)
    , m_Type(type)
{
    m_areaSource = NULL;

    connectByName(BFPNames::ProbeIdStr);

    connectByName(BFPNames::ImageZoomCoefStr);

    if (m_positionConsiderZoomOn)
    {
        connectByName(BFPNames::ZoomOnStr);
    }
}

AreaResult::~AreaResult()
{
    disconnect();
    if (m_areaTrigger)
    {
        m_areaTrigger->blockSignals(true);
        delete m_areaTrigger;
        m_areaTrigger = NULL;
    }
}

void AreaResult::setAreaSource(IAreaSource* areaSource)
{

    ASSERT_X_LOG(!m_areaSource, "area source already set", PRETTY_FUNCTION);

    ASSERT_LOG(areaSource);

    if (m_areaTrigger != NULL)
    {
        m_areaTrigger->blockSignals(true);
        m_areaTrigger->disconnect();
        delete m_areaTrigger;
        m_areaTrigger = NULL;
    }

    m_areaSource = areaSource;

    if (m_areaTrigger)
    {

        log()->debug("destroying previous area trigger");
        m_areaTrigger->blockSignals(true);
        delete m_areaTrigger;
        m_areaTrigger = NULL;
    }

    int timeout =
        (m_sonoParameters->pBV(BFPNames::ColorMStr) || m_sonoParameters->pBV(BFPNames::IsRoiVisibleStr)) ? 10 : 50;

    m_areaTrigger = new DelayParamTrigger(m_sonoParameters, m_areaSource->changedSources(), this, timeout);

    ASSERT_LOG(m_areaTrigger);

    connect(m_areaTrigger, SIGNAL(oneTriggered()), this, SIGNAL(resultChanging()));
    connect(m_areaTrigger, SIGNAL(triggerCompleted()), this, SLOT(onParamChanged()));
    m_areaTrigger->blockSignals(false);

    // force re-calc once
    onParamChanged();
}

void AreaResult::connectByName(const QString& paramName, const char* slotName)
{

    Parameter* param = m_sonoParameters->parameter(paramName);

    connect(param, SIGNAL(valueChanged(QVariant)), this, slotName, Qt::UniqueConnection);
}

void AreaResult::connectByName(const QString& paramName)
{

    connectByName(paramName, SLOT(onParamChanged()));
}

int AreaResult::pIV(const QString& name) const
{
    return m_sonoParameters->pIV(name);
}

bool AreaResult::pBV(const QString& name) const
{
    return m_sonoParameters->pBV(name);
}

QVariant AreaResult::pV(const QString& name) const
{
    return m_sonoParameters->pV(name);
}

qreal AreaResult::pRV(const QString& name) const
{
    return m_sonoParameters->pV(name).toReal();
}

int AreaResult::totalLines(AreaResultType type)
{
    int lines = m_sonoParameters->pIV(BFPNames::B_RX_LNUMStr);
    switch (type)
    {
    case AreaResult_TypeC:
        lines = m_sonoParameters->pIV(BFPNames::C_RX_LNUMStr);
        break;
    default:
        break;
    }
    return lines;
}

bool AreaResult::isSupportAnyDensity()
{
    return ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity, false).toBool();
}

void AreaResult::onParamChanged()
{

    //此类在非主线程上使用时必须
    ParameterReadLock locker;

    if (m_areaSource == NULL)
    {
        // areasource not ready
        return;
    }

    int probeId = pIV(BFPNames::ProbeIdStr);

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(probeId);

    int startLine, stopLine;
    bool zoomOn = useZoomOn();

    if (zoomOn)
    {
        ProbePhysicalGeometry area = m_areaSource->area();
        startLine = area.left(), stopLine = area.right();
    }
    else
    {
        startLine = 0;
        stopLine = 0;
    }

    ProbeParameters* probeParam = NULL;
    switch (m_Type)
    {
    case AreaResult_TypeOriginal:
    case AreaResult_TypeGstStreamB:
        probeParam = new BProbeParameters(probeDataInfo, m_areaSource->outerAreaStartDepthMM(),
                                          m_areaSource->pixelSizeMM(), zoomOn, startLine, stopLine,
                                          pRV(BFPNames::LineSpacingMMStr), pRV(BFPNames::AngleSpacingRadStr));
        break;
    case AreaResult_TypeGstStreamC:
        probeParam =
            new CProbeParameters(probeDataInfo, m_areaSource->outerAreaStartDepthMM(), m_areaSource->pixelSizeMM(),
                                 zoomOn, startLine, stopLine, pRV(BFPNames::LineSpacingMMStr),
                                 pRV(BFPNames::AngleSpacingRadForCStr), pRV(BFPNames::DSCCenterLineStr));
        break;
    default:
    {
        probeParam = new ProbeParameters(probeDataInfo, m_areaSource->outerAreaStartDepthMM(),
                                         m_areaSource->pixelSizeMM(), zoomOn, startLine, stopLine,
                                         pRV(BFPNames::LineSpacingMMStr), pRV(BFPNames::AngleSpacingRadStr));
    }
    break;
    }

    m_cachedPixelSizeMM = m_areaSource->pixelSizeMM();

    if (isSupportAnyDensity())
    {
        probeParam->setTotalLines(totalLines(m_Type));
    }

    if (onSourceChanged(*probeParam))
    {
        emit resultChanged();
    }

    delete probeParam;
}

bool AreaResult::useZoomOn() const
{
    return m_positionConsiderZoomOn && pBV(BFPNames::ZoomOnStr);
}

ConvexAreaResult::ConvexAreaResult(const ISonoParameters* sonoParameters, bool positionConsiderZoomOn,
                                   bool responseFlip, QObject* parent, AreaResultType type)
    : AreaResult(sonoParameters, positionConsiderZoomOn, parent, type)
    , m_responseFlip(responseFlip)
{

    if (m_responseFlip)
    {
        connectByName(BFPNames::UpStr);
        connectByName(BFPNames::LeftStr);
    }

    m_verticalDistance = 0;
    m_rotationAngle = 0.0f;
}

ConvexAreaResult::~ConvexAreaResult()
{
}

qreal ConvexAreaResult::depthCompound() const
{
    return pixel2MM(pIV(BFPNames::PointNumPerLineStr));
}

qreal ConvexAreaResult::rotationAngle() const
{
    return m_rotationAngle;
}

bool ConvexAreaResult::onSourceChanged(const ProbeParameters& probeParam)
{

    if (probeParam.probe().IsLinear)
    {
        return false;
    }

    const ProbePhysicalGeometry area = areaSource()->area();

    qreal left;
    qreal right;

    if (probeParam.getDSCCenterLine())
    {
        log()->debug("DSCCenterLine is true");
        left = area.left() - 0.5f;
        right = area.right() - 0.5f;
    }
    else
    {
        log()->debug("DSCCenterLine is false");
        left = area.left();
        right = area.right();
    }

    // TODO 待ProbeParameter的参考线功能合并后可直接
    //得出相对于-x轴的角度
    // 2023-04-24 Modify by AlexWang 解决在Zoom状态下进入C模式时ROI框左右移动范围不正常
    m_startRadian = areaSource()->convexProbeLineAngleRad(left, &probeParam);
    //(area.left());

    m_stopRadian = areaSource()->convexProbeLineAngleRad(right, &probeParam);
    // ProbeParam计算图像区域时，
    //以一条扫描线的左侧为基准点
    //图像区域的结束线是包含在
    //显示之中的,直接计算所得是
    //结束线的左侧角度,因此必须+1
    //(right+1);

    //当前start/stopRadian的0度参考线是探头起始线
    //结果则是以-x轴为0度，需要叠加起始参考线相对于-x轴的偏移

    const qreal referenceRadOffX = M_PI_2 - areaSource()->probeAngleRad(&probeParam) / 2;

    m_startRadian += referenceRadOffX;
    m_stopRadian += referenceRadOffX;

    // outerArea以左右对齐显示时，一半的弧度
    qreal halfRad;

    if (useZoomOn())
    {
        //考虑zoomon=true时，图像不再按照outerArea进行定位，而是左右对称，上
        //弧角与imageZoomCoef上边界对齐
        halfRad = (m_stopRadian - m_startRadian) / 2;
        const qreal centerOffset = M_PI_2 - (m_startRadian + m_stopRadian) / 2;
        m_startRadian += centerOffset;
        m_stopRadian += centerOffset;
    }
    else
    {
        halfRad = probeParam.probeAngleRad(true) / 2;
    }

    // 2023-04-24 Write by AlexWang 解决在Zoom状态下进入C模式时ROI框左右移动范围不正常
    //计算偏转角度，在设置凸阵的转换矩阵时旋转该偏转角度
    m_rotationAngle = areaSource()->rotationAngle(&probeParam);

    m_innerRadius = mm2Pixel(area.top() + probeParam.getInnerRadius(pBV(BFPNames::PA_VERT_DIST_EnableStr)));

    m_outerRadius = mm2Pixel(area.bottom() + probeParam.probe().WaferRadius);

    // 2023-04-24 Write by AlexWang 解决在Zoom状态下进入C模式时ROI框无法移动到顶部
    qreal fanCenterY = pBV(BFPNames::PA_VERT_DIST_EnableStr) ? -mm2Pixel(probeParam.probe().WaferRadius)
                                                             : -mm2Pixel(areaSource()->perpendicularDisMM(&probeParam));

    QPointF untransformed(0, fanCenterY);

    // if we need translate m_fanCenterPos into wholeImage rect
    m_fanCenterPos = untransformed * flipAndRectZoomCoefTransform();
    m_fanCenterPos = m_fanCenterPos - areaSource()->centerPosOffset();

    //    if(pBV(BFPNames::BCImagesOnStr) && (m_Type >= AreaResult_TypeNULL))
    //    {
    //        m_fanCenterPos.rx() +=
    //        (areaSource()->wholeImageSize().width()*(1-pRV(BFPNames::ImageZoomCoefStr)/qreal(100)))/2.0f;
    //    }

    m_verticalDistance = pBV(BFPNames::PA_VERT_DIST_EnableStr) ? mm2Pixel(probeParam.probe().WaferRadius) : 0;

    //    qDebug() << PRETTY_FUNCTION
    //             << "wholeImageSize:" << areaSource()->wholeImageSize()
    //             << "startRadian:" << m_startRadian
    //             << "stopRadian:" << m_stopRadian
    //             << "innerRadius:" << m_innerRadius
    //             << "outerRadius:" << m_outerRadius;

    return true;
}

bool LinearAreaResult::onSourceChanged(const ProbeParameters& probeParam)
{

    if (!probeParam.probe().IsLinear)
    {
        return false;
    }
    // steeringAngle为线阵扫描线在探头表面发出后与
    //垂直向外方向逆时针的夹角
    m_steeringRad = areaSource()->steeringRad();

    m_CPDSteerRad = areaSource()->cPDSteerRad();

    qreal streeingRad = m_steeringRad + m_CPDSteerRad;

    const ProbePhysicalGeometry rectArea = areaSource()->area();

    // 2023-03-27 Add by AlexWang
    // 解决进入RegionZoom后进入c模式roi框移动范围异常，始终使用StartLine和StopLine来计算中心线的线号 2023-04-20 Modify
    // by AlexWang  部分窗口不需要考虑缩放后的中心线问题，如RegionZoom和PanZoom的导航区
    //    int startLine = pIV(BFPNames::StartLineStr);
    //    int stopLine = pIV(BFPNames::StopLineStr);
    qreal centerLineTmp = 0;
    if (probeParam.getProbeParametersType() == ProbeParametersTypeC)
    {
        centerLineTmp = (useZoomOn() ? m_centerLine : areaSource()->middleLineNo(&probeParam));

        log()->trace("c centerLine %1 , type %2", centerLineTmp, ProbeParametersTypeC);
    }
    else
    {
        centerLineTmp = (useZoomOn() ? rectArea.midX() : areaSource()->middleLineNo(&probeParam));
        m_centerLine = centerLineTmp;
        log()->trace("b centerLine %1 , type %2", centerLineTmp, probeParam.getProbeParametersType());
    }

    qreal lineSpacingMM = pRV(BFPNames::LineSpacingMMStr);

    const qreal centerLine = centerLineTmp;
    //当前x坐标均为内部区域上顶边中点为原点
    const qreal topStartXWithoutSteer = mm2Pixel((qreal)(rectArea.left() - centerLine) / probeParam.lines() *
                                                 probeParam.probeWidthMM(lineSpacingMM, true));

    m_stopXWithoutFlip = mm2Pixel((qreal)(rectArea.right() - centerLine) / probeParam.lines() *
                                  probeParam.probeWidthMM(lineSpacingMM, true));

    m_width = m_stopXWithoutFlip - topStartXWithoutSteer;

    // 2023-03-30 Write by AlexWang 避免起始深度大于探头roi区域的顶部和底部引发异常
    // 2023-04-20 Modify by AlexWang  部分窗口不需要考虑缩放后的中心线问题，如RegionZoom和PanZoom的导航区
    qreal outerAreaStartDepthMM = areaSource()->outerAreaStartDepthMM();
    //    if(RealCompare::IsGreater(outerAreaStartDepthMM, rectArea.top()) ||
    //    RealCompare::IsGreater(outerAreaStartDepthMM, rectArea.bottom()))
    //    {
    //        outerAreaStartDepthMM = 0;
    //    }

    const qreal startYWithoutSteer = mm2Pixel(rectArea.top() - outerAreaStartDepthMM);

    const qreal stopYWithoutSteer = mm2Pixel(rectArea.bottom() - outerAreaStartDepthMM);
    m_stopYWithoutSteer = stopYWithoutSteer;
    //当前y坐标均为内部区域上顶边为0
    //
    //
    qreal steeringRadCos;

    if (m_Type >= AreaResult_TypeNULL)
        steeringRadCos = std::cos(streeingRad);
    else
        steeringRadCos = std::cos(-streeingRad);

    m_startYWithoutFlip = startYWithoutSteer * steeringRadCos;
    m_stopYWithoutFlip = stopYWithoutSteer * steeringRadCos;

    m_hypotenuse = m_stopYWithoutFlip - m_startYWithoutFlip;

    m_heightWithSteer = m_hypotenuse * steeringRadCos;

    const qreal steeringRadSin = std::sin(m_steeringRad);

    QPointF topLeft = QPointF(topStartXWithoutSteer - startYWithoutSteer * steeringRadSin, m_startYWithoutFlip);

    //构造一个不带有翻转的变换
    QTransform transform = Formula::flipAndRectZoomCoefTransform(true, true, areaSource()->wholeImageSize(),
                                                                 areaSource()->imageZoomCoefNormalized());

    const QPointF topLeftInRect = topLeft * transform;

    m_startXWithoutFlip = topLeftInRect.x();
    //    if(pBV(BFPNames::BCImagesOnStr) && (m_Type >= AreaResult_TypeNULL))
    //    {
    //        m_startXWithoutFlip +=
    //        (areaSource()->wholeImageSize().width()*(1-pRV(BFPNames::ImageZoomCoefStr)/qreal(100)))/2.0f;
    //    }

    m_startYWithoutFlip = topLeftInRect.y();
    m_stopYWithoutFlip = m_startYWithoutFlip + m_heightWithSteer;

    return true;
}

LinearAreaResult::LinearAreaResult(const ISonoParameters* sonoParameters, bool positionConsiderZoomOn,
                                   bool responseFlip, QObject* parent, AreaResultType type)
    : AreaResult(sonoParameters, positionConsiderZoomOn, parent, type)
{

    if (responseFlip)
    {
        connectByName(BFPNames::UpStr, SIGNAL(resultChanged()));
        connectByName(BFPNames::LeftStr, SIGNAL(resultChanged()));
    }

    m_steeringRad = 0;
    m_CPDSteerRad = 0;
}

FourDConvexAreaResult::FourDConvexAreaResult(const ISonoParameters* sonoParameters, bool positionConsiderZoomOn,
                                             bool responseFlip, QObject* parent)
    : ConvexAreaResult(sonoParameters, positionConsiderZoomOn, responseFlip, parent)
    , m_sonoParameters(sonoParameters)
{
}

bool FourDConvexAreaResult::onSourceChanged(const ProbeParameters& probeParam)
{
    ConvexAreaResult::onSourceChanged(probeParam);

    m_knotRadian = probeParam.convexProbeLineAngleRad(m_sonoParameters->pIV(BFPNames::RoiMidLineFourDStr) +
                                                      m_sonoParameters->pIV(BFPNames::FourDKnotLineStr));

    //当前start/stopRadian的0度参考线是探头起始线
    //结果则是以-x轴为0度，需要叠加起始参考线相对于-x轴的偏移
    const qreal referenceRadOffX = M_PI_2 - probeParam.probeAngleRad(true) / 2;
    m_knotRadian += referenceRadOffX;

    m_knotDepth = mm2Pixel(m_sonoParameters->pDV(BFPNames::FourDKnotDepthMMStr) +
                           m_sonoParameters->pDV(BFPNames::RoiMidDepthMMFourDStr) + probeParam.probe().WaferRadius);

    return true;
}

PanLinearAreaResult::PanLinearAreaResult(const ISonoParameters* sonoParameters, bool positionConsiderZoomOn,
                                         bool responseFlip, QObject* parent, AreaResultType type)
    : LinearAreaResult(sonoParameters, positionConsiderZoomOn, responseFlip, parent, type)
{
}

PanLinearAreaResult::~PanLinearAreaResult()
{
}

bool PanLinearAreaResult::onSourceChanged(const ProbeParameters& probeParam)
{
    const ProbePhysicalGeometry rectArea = areaSource()->area();

    QSize imageSize = pV(BFPNames::RenderImageSizeStr).toSize();

    m_startXWithoutFlip = 1.0 * rectArea.left() / imageSize.width() * areaSource()->wholeImageSize().width();
    m_stopXWithoutFlip = 1.0 * rectArea.right() / imageSize.width() * areaSource()->wholeImageSize().width();

    m_startYWithoutFlip = 1.0 * rectArea.top() / imageSize.height() * areaSource()->wholeImageSize().height();
    m_stopYWithoutFlip = 1.0 * rectArea.bottom() / imageSize.height() * areaSource()->wholeImageSize().height();

    m_width = m_stopXWithoutFlip - m_startXWithoutFlip;

    return true;
}
