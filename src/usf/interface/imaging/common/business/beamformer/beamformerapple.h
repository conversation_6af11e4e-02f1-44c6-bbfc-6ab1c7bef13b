#ifndef BEAMFORMERAPPLE_H
#define BEAMFORMERAPPLE_H

#include "beamformersonoeye.h"
#include "lineiimageparameterhandle.h"
#include "commonareasource.h"
#if defined(SYS_UNIX) || defined(SYS_ANDROID)
#include "ecgreceiver.h"

#endif

class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BeamFormerApple : public BeamFormerSonoeye
{
    Q_OBJECT
public:
    explicit BeamFormerApple(QObject* parent = 0);
    virtual ~BeamFormerApple();
    virtual void createStaticParameters();
    virtual IBFKitFactory* createBFKitFactory();

    virtual void onExitProbeState(bool isSuspendRead = false);
    virtual bool isProbeOnline() const;
    virtual void update(unsigned char* buf, int len);
    bool selectSocket(int socket, bool writeSocket = false) override;
    virtual bool standby();
    virtual bool wake();
    virtual void restoreData() override;
    virtual void resentProbeParamAndBlockData();
    virtual void setSystemScanMode(SystemScanMode mode);
    virtual void setStartSupportFreezeOutage(bool value) override;
    virtual void initCWDPRFParameterCalculator();
    virtual void onCWEnChanging(const QVariant& value);
    virtual void onCWDSampleRateChanging(const QVariant& value);
    virtual void onGettingCWDSampleRateMax(int& value);
    virtual void onGettingCWDSampleRateText(QString& value);
    virtual void onSetSonoParameters();
    virtual void shutdown();
    virtual QVector<ChipFactory::ChipNameEnum> supportChipList() const; //返回芯片类型
protected:
    virtual void createPostProcessHandler();
    virtual void initializePostParameterCalculators();
    virtual void initializeSpecialParameters();
    virtual void initializeIImageParameters();
    virtual void initializeProbeDection();
    virtual void sendCurvedCpdTxBlockDataGroup();
    virtual bool isScpdEnabled() const;
    virtual void updatePrfList();
    virtual void updateCPDSteerStr();
    virtual void getLGCControlTableValue(int& ctValue, const QString para);
    virtual void sendFilterCpd();
    virtual void sendNormalBlockDataGroup(const QString& probeName, const QStringList& sendDataNames = QStringList());

    virtual void controlTMTDICWDParas();

protected slots:
    virtual void onGettingFreqIndexCWDText(QString& value);
    virtual void onGettingiImageModelNoShowValue(QVariant& value);
    virtual void onBeforeLineiImageSettingIdsChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void oniImageChanged(const QVariant& value);
    virtual void oniImageShowChanged(const QVariant& value);
    virtual void onGettingiImageShowText(QString& value);

    virtual void onSmoothChanging(const QVariant& value);

    virtual void onVolumeChanged(const QVariant& value);
    virtual void onVolumeTDIChanged(const QVariant& value);
    virtual void onVolumeCWDChanged(const QVariant& value);

    virtual void onGettingLineImageDebugText(QString& value);

    virtual void onBeforeFreezeChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onFreezeChanged(const QVariant& value);

    virtual void onScanWidthChanged(const QVariant& value);

    virtual void onBeforeCurvedExapandingChanged(const QVariant& oldValue, QVariant& newValue);

    virtual void onBeforeScpdChanged(const QVariant& oldValue, QVariant& newValue);

    virtual void onBeforeScpdOnChanged(const QVariant& oldValue, QVariant& newValue);

    virtual void onBeforeTrapezoidalModeChanged(const QVariant& oldValue, QVariant& newValue);

    virtual void onTrapezoidalModeChanging(const QVariant& value);

    virtual void onTrapezoidalModeChanged(const QVariant& value, bool changed);

    virtual void onBeforeScpdTrapeChanged(const QVariant& oldValue, QVariant& newValue);

    virtual void onScpdTrapeChanging(const QVariant& value);

    virtual void onLeftChanging(const QVariant& value);

    virtual void onCPDSteerChanging(const QVariant& value);

    virtual void onBeforeZoomOnChanged(const QVariant& oldValue, QVariant& newValue);

    virtual void onZoomOnChanging(const QVariant& value) override;

    /** 2024-11-28 Write by AlexWang
     * @brief onZoomOnChanged
     * @param vlaue
     */
    virtual void onZoomOnChanged(const QVariant& value) override;

    virtual void onZoomSelectChanging(const QVariant& value) override;

    virtual void onIsRoiVisibleChanging(const QVariant& value);

    virtual void onCurvedExapandingChanged(const QVariant& value);

    virtual void onTrapezoidalCPDSteerChanging(const QVariant& value);

    //    virtual void onTrapezoidalCPDSteer2Changing(const QVariant& value);

    virtual void onProbeIdChanged(const QVariant& value);

    virtual void onGettingTGC9ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingTGC10ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingTGC11ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingTGC12ControlTableValue(const QVariant& value, int& controlTableValue);

    virtual void onGettingCQYZMax(int& value);

    virtual void onGettingWallFilterDopText(QString& value);

    virtual void setPreset(const PresetParameters& presets);
    virtual void setPresetFinished();
    virtual void setCurProbeFinishedOnSystemBooted();

    virtual void sendCpdSteerCTValue(int value);
    virtual void sendCpdSteerCTValue(int value1, int value2);

    virtual void onBeforeSyncModeChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onSyncModeChanging(const QVariant& value);

    virtual void controlRotationEn();
    virtual void controlBiopsyEn();
    void controlMB();

    virtual void onGettingAcousticPowerTestCodeText(QString& value);

    virtual void onGettingSRINoiseFilterTypeText(QString& value);
    virtual void onGettingSRIEdgeFilterTypeText(QString& value);

    virtual void onGettingMVelocityControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onTHIChanging(const QVariant& value);

    virtual void onCQYZChanging(const QVariant& value);
    virtual void onCQYZChanged(const QVariant& value, bool bChange);
    virtual void onDDisplayFormatChanged(const QVariant& value);
    virtual void onDTDIDisplayFormatChanged(const QVariant& value);
    //控制表重新下发后，需要重置自动冻结的计时
    virtual void handleSendingDataMsg();

    virtual void onGettingWallThresholdControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingWallThresholdPDControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingWallThresholdTDIControlTableValue(const QVariant& value, int& controlTableValue);

    virtual void onGettingDScanLineControlTableValue(const QVariant& value, int& controlTableValue);

    virtual void onTriplexModeChanged(const QVariant& value);
    virtual void onBCDGeometryChanged();

    virtual void onGettingCQYZControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onFreqSpectrumChanged(const QVariant& value);

    virtual void onTDIEnChanging(const QVariant& value);

    virtual void onSteeringAngleChanged(const QVariant& value, bool changed);

    virtual void onBeforeDopSteeringAngleChanged(const QVariant& oldValue, QVariant& newValue);
    virtual void onDopSteeringAngleChanged(const QVariant& value, bool changed);
    virtual void onGettingDopSteeringAngleShowValue(QVariant& value);
    virtual void onGettingDopSteeringAngleMin(int& value);
    virtual void onGettingDopSteeringAngleMax(int& value);
    virtual void onGettingDopSteeringAngleControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPhasedProbeTgc0ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPhasedProbeTgc1ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPhasedProbeTgc2ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPhasedProbeTgc3ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPhasedProbeTgc4ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPhasedProbeTgc5ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPhasedProbeTgc6ControlTableValue(const QVariant& value, int& controlTableValue);
    virtual void onGettingPhasedProbeTgc7ControlTableValue(const QVariant& value, int& controlTableValue);

    virtual void onIsBiopsyVisibleChanged(const QVariant& value);
    virtual void onFcpdOnChanging(const QVariant& value);
    virtual void onHighDensityChanged(const QVariant& value);
    virtual void onMBIncChanging(const QVariant& value);
    virtual void onMBIncChanged(const QVariant& value);
    virtual void onGettingMBPreset(QVariant& value);
#if defined(SYS_UNIX) || defined(SYS_ANDROID)
    virtual void onECGGainChanging(const QVariant& value);
    virtual void onECGVelocityChanging(const QVariant& value);
    virtual void onGettingECGVelocityShowValue(QVariant& value);
    virtual void onGettingECGPosShowValue(QVariant& value);
    virtual void onGettingBECGDelayTimeShowValue(QVariant& value);
#endif

private:
    void updateConvexCQYZ(const int angle);
    void updateLinearCQYZ(const int angle);
    bool isCurvedExapandingEnable();

    void updateMaxCQYZ(bool trapezodialOn, bool scpdOn, int scpdValue, bool curvedExapanding);
    void updateTrapCureveBSteer();
    void sendShutDownProbeBlockData();
    void createBFIODevice();

private:
    LineiImageParameterHandle m_LineiImageParameterHandle;
#if defined(SYS_UNIX) || defined(SYS_ANDROID)
    EcgReceiver* m_EcgReceiver;
#endif
    bool m_IsStartUp{true};
    int m_CurvedExapandingBak{-1};
    int m_CurvedScpdBak{-1};
    int m_ThiModeBak{-1};
    int m_CurMaxCQYZ;
    bool m_THIReset; //是否重置THI
    int m_MBBak;
};

#endif // BEAMFORMERAPPLE_H
