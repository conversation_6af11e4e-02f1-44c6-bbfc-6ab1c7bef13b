#include "newbfdpixelsizesecparametercalculator.h"
#include "bfpnames.h"
#include "formula.h"
#include "modelconfig.h"

NewBFDPixelSizeSecParameter::NewBFDPixelSizeSecParameter(int prfDopHZ, int dopAccumlateNum)
{
    Q_ASSERT(prfDopHZ != 0);
    double linePeriod = 1.0 / prfDopHZ;
    if (dopAccumlateNum < 1)
    {
        dopAccumlateNum = 1;
    }
    m_DPixelSizeSec = dopAccumlateNum * linePeriod;
}

float NewBFDPixelSizeSecParameter::dPixelSizeSec() const
{
    return m_DPixelSizeSec;
}

NewBFDPixelSizeSecParameterCalculator::NewBFDPixelSizeSecParameterCalculator(ISonoParameters* sonoParameters)
    : BaseBFParameterCalculator(sonoParameters)
{
}

QVariant NewBFDPixelSizeSecParameterCalculator::calculate()
{
    double prf = pDV(BFPNames::PRFDopStr);
    if (pIV(BFPNames::CWEnStr) && !ModelConfig::instance().value(ModelConfig::HPrfCW, false).toBool())
    {
        prf = pDV(BFPNames::PRFCWDStr);
    }

    NewBFDPixelSizeSecParameter calculator(prf, pIV(BFPNames::DopAccumulateNumStr));
    return calculator.dPixelSizeSec();
}

QStringList NewBFDPixelSizeSecParameterCalculator::relatedParameters() const
{
    return QStringList() << BFPNames::CWEnStr << BFPNames::PRFDopStr << BFPNames::PRFCWDStr << BFPNames::DVelocityStr
                         << BFPNames::DVelocityTDIStr << BFPNames::CWDVelocityStr;
}
