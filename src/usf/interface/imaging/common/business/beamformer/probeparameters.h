/**
 * @file probeparameters.h
 * @brief
 * <AUTHOR>
 * @version 0.1
 * @date 2012-07-12
 */

#ifndef PROBEPARAMETERS_H
#define PROBEPARAMETERS_H
#include "usfinterfaceimagingbusiness_global.h"

#include "basebfstaticparameters.h"
#include "formula.h"
#include "infostruct.h"
#include "modelconfig.h"
#include "realcompare.h"
#include <qmath.h>
class ISonoParameters;

enum ProbeParametersType
{
    /*!
     *
     */
    ProbeParametersTypeB = 0,
    /*!
     *
     */
    ProbeParametersTypeC = 1,
    /*!
     *
     */
    ProbeParametersTypeNULL = 2
};

/**
 * @brief 探头的一些物理尺寸
 */
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT ProbeParameters
{
public:
    /**
     * @brief 计算探头角度时，角度为0度的线
     *
     * Convex Probe:
     * LeftHorizontalLine  ___________________
     *                             / | \
     *                            /  |  \
     *                           /   |   \
     *                          /    |
     *                         /     | CenterLine
     *                        / StartLine
     *
     * Linear Probe
     *                       __________________
     *                          |    |    |
     *                          |    |    |
     *                          |    |    |
     *                          |    |    |
     *                          |    |
     *                          |    | CenterLine
     *                          | StartLine
     * LeftHorizontalLine is ignored
     */
    enum ZeroAngleLine
    {
        StartLine,
        LeftHorizontalLine,
        CenterLine
    };
    /**
     * @brief ProbeParameters
     * @param probe
     * @param startDepthMM 图像的起始深度，凸阵为顶部弧底的深度
     * @param pixelSizeMM 用于计算线阵探头的线间距、探头宽度、线号与物理宽度转换时使用
     * @param zoomOn
     * @param startLine
     * @param stopLine
     */
    ProbeParameters(const ProbeDataInfo& probe, qreal startDepthMM = 0.0f, qreal pixelSizeMM = 0.0f,
                    bool zoomOn = false, int startLine = 0, int stopLine = 0, qreal lineSpacingMM = 0.0f,
                    qreal angleSpacingRad = 0.0f, bool DSCCenterLine = false);

    /*! 2023-04-26 Write by AlexWang
     * \brief ProbeParameters
     * \param sonoParameters
     * \param considerZoomOn    考虑放大状态
     */
    ProbeParameters(const ISonoParameters* sonoParameters, bool considerZoomOn = true);
    /**
     * @brief lines 总的线数
     *
     * @return
     */
    virtual int lines() const;
    /*!
     * \brief actualLines  实际的线数
     * \return
     */
    virtual int actualLines() const;

    /**
     * @brief validLines 有效线数
     *
     * @return
     */
    virtual int validLines() const;
    /**
     * @brief maxLineNo 最大线数
     * 最小线数为0
     *
     * @return
     */
    void setTotalLines(int totalLines);
    int maxLineNo() const;
    virtual int middleLineNo() const;
    static int middleLineNo(int startLine, int stopLine);
    /**
     * @brief lines 返回线数
     *
     * @param startLine
     * @param stopLine
     * @param highDensity  高密度，低密度
     *
     * @return
     */
    static int lines(int startLine, int stopLine, bool highDensity);
    static int lines(int startLine, int stopLine, int highDensity);
    /**
     * @brief lineInterval 线间隔
     *
     * @param highDensity 高密度为：1，低密度为：2
     *
     * @return
     */
    static int lineInterval(bool highDensity);
    static int lineInterval(int highDensity);
    /**
     * @brief stopLine 终止线号
     * @param startLine
     * @param lines
     * @param lineInterval
     * @return
     */
    static int stopLine(int startLine, int lines, int lineInterval);
    /**
     * @brief radiusMM 探头的半径
     * 只对凸阵探头有效
     * radiusMM() = perpendicularDisMM() + arcFloorDisMM();
     * @return
     */
    float radiusMM() const;
    /**
     * @brief perpendicularDisMM 探头的垂距
     * 半径* cos( angle/2)
     * 只对凸阵探头有效
     *
     * @return
     */
    qreal perpendicularDisMM() const;
    /*! 2023-04-20 Write by AlexWang
     * \brief perpendicularDisMMEx
     * \param zoomOn
     * \param startLine
     * \param stopLine
     * \return
     */
    qreal perpendicularDisMMEx(bool zoomOn, int startLine, int stopLine) const;
    /**
     * @brief arcFloorDisMM 凸阵探头弧底到弧顶的距离
     * radiusMM() - perpendicularDisMM()
     * @return
     */
    qreal arcFloorDisMM() const;
    /**
     * @brief bottomDepthMM 探头图像区底部的深度
     *
     * @param imageHeightMM 图像区占显示区域的高度，就是SonoParameters中的DepthMM
     *
     * @return
     */
    qreal bottomDepthMM(qreal imageHeightMM) const;

    qreal imageDepthMM(qreal imageHeightMM) const;
    /**
     * @brief probeLineInterval 下发FPGA的线间距的值,FPGA底部使用此值计算DSC时的线号
     *
     * 目前的eco ebit qbit
     * 下发的值都是高密度时的值，当系统切换为低密度时，FPGA底部会对此值除以2再运算,这样会产生0.5的误差
     * 为了保持软件计算和FPGA一致，软件直接控制返回值为最接近计算出的小数的偶数值，这样FPGA在低密度时不会再有误差。经过测试
     * 此方案的最大误差为1.2xxx%，比较好的方案是FPGA底部修改，当低密度时，先使用下发值，最后对得到的线号做除以2的运算
     *
     * @param useZoomCoefFactor 是否考虑zoomcoef影响, ECO 下发的线间距值，不随zoomcoef影响，EBit、QBit受zoomcoef影响
     * @param zoomCoefPercent 如果useZoomCoefFactor=false, 此参数忽略，否则使用
     * @param useHighDensityFactor 是否要随 HighDensity
     * 的值变化而变化，目前发行版的机型都=false，历史上早期的ECO1需要随HighDensity变化
     * @param highDensity HighDensity 的值，如果useHighDensityFactor=false，此参数忽略
     * @return
     */
    template <typename T = int>
    T probeLineInterval(bool useZoomCoefFactor = false, int zoomCoefPercent = 100, bool useHighDensityFactor = false,
                        bool highDensity = true) const;

    int probeLineIntervals(bool useZoomCoefFactor, int zoomCoefPercent, bool useHighDensityFactor,
                           int highDensity) const;
    /**
     * @brief probeLineSpacingMM 线阵的线间距，已经通过下发的线间距参数，反算出线间距
     *
     * 因为FPGA精度的影响，和避免乘法器的处理，下发给FPGA的线间距参数需要一些归一化、取倒数、取整的运算，这样
     * FPGA实际用到的线间距和探头的物理线间距有一个误差，为了达到软件的图形绘制与FPGA的DSC图形匹配，软件也要用这种
     * 有一定误差的参数计算
     * TODO:未来软件做DSC时，可能需要实现可切换
     * @return
     */
    template <typename T = int> qreal probeLineSpacingMM() const;

    /**
     * @brief probeWidthMM 线阵探头的宽度
     *
     * @return
     */
    qreal probeWidthMM() const;

    qreal probeWidthMM(qreal lineSpacingMM, bool isNeedReal = false) const;
    /**
     * @brief probeAngleInterval 凸阵下发FPGA的角间隔,FPGA底部使用此值计算DSC时的线号
     *
     * 目前的eco ebit qbit
     * 下发的值都是高密度时的值，当系统切换为低密度时，FPGA底部会对此值除以2再运算,这样会产生0.5的误差
     * 为了保持软件计算和FPGA一致，软件直接控制返回值为最接近计算出的小数的偶数值，这样FPGA在低密度时不会再有误差。经过测试
     * 此方案的最大误差为1.2xxx%，比较好的方案是FPGA底部修改，当低密度时，先使用下发值，最后对得到的线号做除以2的运算
     *
     * @param useHighDensitFactor 是否要随 HighDensity
     * 的值变化而变化，目前发行版的机型都=false，历史上早期的ECO1需要随HighDensity变化 而变化
     * @param highDensity HighDensity 的值，如果useHighDensityFactor=false，此参数忽略
     * @return
     */
    template <typename T = int>
    inline qreal probeAngleInterval(bool useHighDensityFactor = false, bool highDensity = true) const
    {
        //如果是相控阵 WaferRadius = 0，需转换成1
        float r = (RealCompare::AreEqual(m_Probe.WaferRadius, 0.0f) ? 1.0f : m_Probe.WaferRadius);
        //角间隔
        qreal interval = highDensityFactor(useHighDensityFactor, highDensity) * 16384.0F /
                         ((m_Probe.WaferLength / r) / (BaseBFStaticParameters::ANGLE_PRECISION * M_PI / 180.0F));

        return interval;
    }
    /**
     * @brief probeAngleSpacingRad 凸阵的角间隔，已经通过下发的角间隔参数，反算出角间隔
     *
     * 因为FPGA精度的影响，和避免乘法器的处理，下发给FPGA的角间隔参数需要一些归一化、取倒数、取整的运算，这样
     * FPGA实际用到的角间隔和探头的物理角间隔有一个误差，为了达到软件的图形绘制与FPGA的DSC图形匹配，软件也要用这种
     * 有一定误差的参数计算
     * TODO:未来软件做DSC时，可能需要实现可切换
     * @return
     */
    template <typename T = int> inline qreal probeAngleSpacingRad() const
    {

        if (ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity, false).toBool())
        {
            return m_Probe.WaferLength * m_Probe.WaferNum / m_TotalLines / m_Probe.WaferRadius;
        }
        T interval = probeAngleInterval<qreal>();
        // 此处不考虑老版本的ECO1，直接使用 2 * 16384.0F
        return 2 * 16384.0F * (BaseBFStaticParameters::ANGLE_PRECISION * M_PI / 180.0F) / (qreal)interval;
    }
    //    template <> qreal probeAngleSpacingRad() const;
    /**
     * @brief probeAngleRad 凸阵探头的角度，单位:弧度
     *
     * @return
     */
    qreal probeAngleRad(bool isNeedReal = false) const;
    /**
     * @brief probeAngleRad 凸阵探头的角度，单位:弧度
     *
     * @return
     */
    qreal probeAngleRadEx(bool zoomOn = false, int startLine = 0, int stopLine = 0) const;
    /**
     * @brief convexProbeLineAngleRad 凸阵探头某根线的角度，单位:弧度
     * 线的角度以0线为起点的一个相对角度，小于0线为负的角度
     *
     * @param lineNo 线号
     * @param zeroAngleLine 0度线所在的位置
     * @return
     */
    qreal convexProbeLineAngleRad(qreal lineNo, ZeroAngleLine zeroAngleLine = StartLine) const;
    /*! 2023-04-19 Write by AlexWang
     * \brief convexProbeLineAngleRadEx  凸阵探头某根线的角度，单位:弧度
     * \param lineNo   线号
     * \param zoomOn
     * \param startLine
     * \param stopLine
     * \param zeroAngleLine 0度线所在的位置
     * \return
     */
    qreal convexProbeLineAngleRadEx(qreal lineNo, bool zoomOn = false, int startLine = 0, int stopLine = 0,
                                    ZeroAngleLine zeroAngleLine = StartLine) const;
    /*! 2032-05-04 Write by AlexWang
     * \brief convexProbeLineAngleRadEx
     * \param lineNo
     * \return
     */
    qreal convexProbeLineAngleRadEx(qreal lineNo);
    /**
     * @brief convexProbeLineNo 凸阵探头根据角度计算线号
     * 线的角度以0线为起点的一个相对角度，小于0线为负的角度
     * 是 @ref convexProbeLineAngleRad 的逆变换
     *
     * @param angleRad 线的角度
     * @param zeroAngleLine zeroAngleLine 0度线所在的位置
     * @return
     */
    qreal convexProbeLineNo(qreal angleRad, ZeroAngleLine zeroAngleLine = StartLine) const;
    /**
     * @brief linearProbeLineDisMM 线阵探头某根线相对于0线的距离，单位:mm
     *
     * @param zeroDisLine 距离0的线所在的位置
     * @return
     */
    qreal linearProbeLineDisMM(qreal lineNo, ZeroAngleLine zeroDisLine = StartLine) const;
    /** 2023-03-30 Write by AlexWang
     * @brief linearProbeLineDisMM  线阵探头某根线相对于0线的距离，单位:mm
     * @param lineNo        当前线号
     * @param lines         当前总线数
     * @param zeroDisLine   距离0的线所在的位置
     * @return
     */
    //    qreal linearProbeLineDisMM(qreal lineNo, int lines, ZeroAngleLine zeroDisLine = StartLine) const;

    /**
     * @brief linearProbeLineNo 线阵探头根据 disMM 计算线号
     * 线阵探头某根线相对于0线的距离，单位:mm
     * 是 @ref linearProbeLineDisMM 的逆变换
     *
     * @param zeroDisLine 距离0的线所在的位置
     * @return
     */
    qreal linearProbeLineNo(qreal disMM, ZeroAngleLine zeroDisLine = StartLine) const;

    /**
     * @brief 获取ProbeDataInfo的const引用
     * 用于获取持有的探头的底层信息
     *
     * @return
     */
    const ProbeDataInfo& probe() const;

    bool getDSCCenterLine() const;

    qreal angleSpacingRad() const
    {
        return m_angleSpacingRad;
    }
    qreal startDepthMM() const
    {
        return m_StartDepthMM;
    }

    virtual qreal getInnerRadius(bool paVertDistEnable) const;

    virtual int getProbeParametersType() const
    {
        return ProbeParametersTypeNULL;
    }

    /** 2025-06-10 Write by AlexWang
     * @brief isSupportAnyDensity
     * @return
     */
    static bool isSupportAnyDensity();

protected:
    const ProbeDataInfo& m_Probe;

private:
    int highDensityFactor(bool useHighDensityFactor = false, bool highDensity = true) const;
    int highDensityFactor(bool useHighDensityFactor, int highDensity) const;
    double pixelSizeMMZoomCoefFactor(double pixelSizeMM, bool useZoomCoefFactor = false,
                                     int zoomCoefPercent = 100) const;

public:
    qreal m_StartDepthMM;
    qreal m_PixelSizeMM;
    bool m_ZoomOn;
    int m_StartLine;
    int m_StopLine;
    qreal m_lineSpacingMM;
    qreal m_angleSpacingRad;
    /**
     * @brief m_DSCCenterLine只对凸阵的C图像有效
     *
     */
    bool m_DSCCenterLine;
    /**
     * 任意线密度/非任意线密度 状态下的总线数
     */

    int m_TotalLines;
};

template <> inline qreal ProbeParameters::probeAngleInterval(bool useHighDensityFactor, bool highDensity) const
{
    //如果是相控阵 WaferRadius = 0，需转换成1
    float r = (RealCompare::AreEqual(m_Probe.WaferRadius, 0.0f) ? 1.0f : m_Probe.WaferRadius);
    //角间隔
    qreal interval = highDensityFactor(useHighDensityFactor, highDensity) * 16384.0F /
                     ((m_Probe.WaferLength / r) / (BaseBFStaticParameters::ANGLE_PRECISION * M_PI / 180.0F));

    if (!useHighDensityFactor)
    {
        return Formula::real2Even(interval);
    }
    else
    {
        return qRound(interval);
    }
}

// template <typename T> qreal ProbeParameters::probeAngleSpacingRad() const
//{
//    if (ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity, false).toBool())
//    {
//        return m_Probe.WaferLength * m_Probe.WaferNum / m_TotalLines / m_Probe.WaferRadius;
//    }
//    T interval = probeAngleInterval<qreal>();
//    // 此处不考虑老版本的ECO1，直接使用 2 * 16384.0F
//    return 2 * 16384.0F * (BaseBFStaticParameters::ANGLE_PRECISION * M_PI / 180.0F) / (qreal)interval;
//}

template <> inline qreal ProbeParameters::probeAngleSpacingRad<qreal>() const
{
    if (ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity, false).toBool())
    {
        return m_Probe.WaferLength * m_Probe.WaferNum / m_TotalLines / m_Probe.WaferRadius;
    }
    qreal interval = probeAngleInterval<qreal>();
    // 此处不考虑老版本的ECO1，直接使用 2 * 16384.0F
    return 2 * 16384.0F * (BaseBFStaticParameters::ANGLE_PRECISION * M_PI / 180.0F) / (qreal)interval;
}

template <> inline qreal ProbeParameters::probeAngleSpacingRad<int>() const
{
    if (ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity, false).toBool())
    {
        return m_Probe.WaferLength * m_Probe.WaferNum / m_TotalLines / m_Probe.WaferRadius;
    }
    int interval = probeAngleInterval<int>();
    // 此处不考虑老版本的ECO1，直接使用 2 * 16384.0F
    return 2 * 16384.0F * (BaseBFStaticParameters::ANGLE_PRECISION * M_PI / 180.0F) / (qreal)interval;
}

/**
 * @brief 处理B图像的总线数
 */
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT BProbeParameters : public ProbeParameters
{
public:
    BProbeParameters(const ProbeDataInfo& probe, qreal startDepthMM = 0.0f, qreal pixelSizeMM = 0.0f,
                     bool zoomOn = false, int startLine = 0, int stopLine = 0, qreal lineSpacingMM = 0.0f,
                     qreal angleSpacingRad = 0.0f, bool DSCCenterLine = false);
    //    virtual int lines() const;
    //    virtual int validLines() const;
    //    virtual int middleLineNo() const;
    virtual qreal getInnerRadius(bool paVertDistEnable) const;

    virtual int getProbeParametersType() const
    {
        return ProbeParametersTypeB;
    }
};

/**
 * @brief 处理C图像的总线数
 */
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT CProbeParameters : public BProbeParameters
{
public:
    CProbeParameters(const ProbeDataInfo& probe, qreal startDepthMM = 0.0f, qreal pixelSizeMM = 0.0f,
                     bool zoomOn = false, int startLine = 0, int stopLine = 0, qreal lineSpacingMM = 0.0f,
                     qreal angleSpacingRad = 0.0f, bool DSCCenterLine = false);

    virtual qreal getInnerRadius(bool paVertDistEnable) const;

    virtual int getProbeParametersType() const
    {
        return ProbeParametersTypeC;
    }
};

#endif // PROBEPARAMETERS_H
