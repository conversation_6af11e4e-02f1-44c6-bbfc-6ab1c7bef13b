#include "beamformerbase.h"
#include "logger.h"
//#include <unistd.h>
#include "abstractbfdatahandler.h"
#include "bffocusparameters.h"
#include "bfpnames.h"
#include "controltable.h"
#include "controltablesender.h"
#include "controltableparameter.h"
#include "ibfkitfactory.h"
#include "ibfrawdata2showdata.h"
#include "icolormapmanager.h"
#include "newtonsamplepointsutil.h"
#include "realcompare.h"
#include "regionutil.h"
#include "resource.h"
#include "setting.h"
#include "ultrasounddevice.h"
#include "util.h"
#include "xmlbfparameterloader.h"
#include <QPoint>
#include <qmath.h>
//#include "gpiodevice.h"
#include "acousticpowerpresscalculator.h"
#include "applogger.h"
#include "aprangesettings.h"
#include "basebfstaticparameters.h"
#include "baseblockdatasender.h"
#include "baseblockdataparanameconverter.h"
#include "bfadfreqparameter.h"
#include "bfcmaxvelparametercalculator.h"
#include "bfdepthparameters.h"
#include "bfdmaxvelparametercalculator.h"
#include "bfdpixelsizesecparametercalculator.h"
#include "bffpsparameter.h"
#include "bfmitiparameters.h"
#include "bfprfcolorparametercaculator.h"
#include "bfprfdparametercalculator.h"
#include "bfprtofbparametercaculator.h"
#include "bfprtofcparametercaculator.h"
#include "bfprtofdparametercaculator.h"
#include "bfscanwidthparameter.h"
#include "bitoperator.h"
#include "colorcoefsettings.h"
#include "cwdprfcalculator.h"
#include "dsamplinggatecontroller.h"
#include "edgesettings.h"
#include "elastoprfcalculator.h"
#include "emptyrelatedparascontroller.h"
#include "formula.h"
#include "freemlinecontroller.h"
#include "freqparascontainer.h"
#include "freqrelatedparas.h"
#include "freqsetting.h"
#include "generalinfo.h"
#include "hardwarecontrolutil.h"
#include "imageeventargs.h"
#include "minzoomedcqyz.h"
#include "mlinecontroller.h"
#include "modelconfig.h"
#include "modelsettings.h"
#include "muxpnames.h"
#include "newbfdpixelsizesecparametercalculator.h"
#include "newcfmprfcalculator.h"
#include "newdopprfcalculator.h"
#include "newtoniinterpolationalgorithm.h"
#include "newtriplexmodehelper.h"
#include "panzoomboxcontroller.h"
#include "presetparameters.h"
#include "probeblockdataset.h"
#include "probedataset.h"
#include "probeimageregion.h"
#include "probeparameters.h"
#include "relatedparascontroller.h"
#include "roianddsamplinggatecontroller.h"
#include "roicontroller.h"
#include "smallzoomboxcontroller.h"
#include "zoomboxcontroller.h"
#include <QRect>
#include <QScopedPointer>
#include <QTime>
#include <QTransform>
#ifdef USE_4D
#include "fourdparascontainer.h"
#include "fourdroicontroller.h"
#endif
#include "appsetting.h"
#include "autofocusadjuster.h"
#include "bfscanareawidthparameter.h"
#include "cqyzprefervaluecalculator.h"
#include "cqyzstepconfig.h"
#include "imagerenderlayoutrects.h"
#include "istatemanager.h"
#include "linedatahead.h"
#include "needleparas.h"
#include "needlesettingset.h"
#include "parameter.h"
#include "probeinfomodel.h"
#include "probesswitchcontroller.h"
#include "linedensityparametershandler.h"
#ifdef USE_USCONTROLENGINE
#include "parameterdef.h"
#include "usbasedefine.h"
#include "uscontrolengineuiapi.h"
#endif
#include "calculatorutil.h"
#include "stateeventnames.h"
#include "statefilterlocker.h"
#include "usfobject.h"
#include "toolnames.h"
#include "postprocess/postprocesshandlerbase.h"
#include "systemscanmodeclassifier.h"
#include <QDebug>
#include "infostruct.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, BeamFormerBase)

BeamFormerBase::BeamFormerBase(QObject* parent)
    : IBeamFormer(parent)
    , m_IsParametersResetting(true)
    , m_IsFPGAElastographyOn(false)
    , m_IsHwKeySended(false)
    , m_IsLayoutChange(false)
    , m_RedundantPoints(0)
    , m_BFIODevice(NULL)
    , m_ControlTable(new ControlTable(this))
    , m_CTFreezeParameter(NULL)
    , m_BlockDataSender(NULL)
    , m_BlockDataParaNameConverter(NULL)
    , m_BFDataHandler(NULL)
    , m_DepthParameters(new BFDepthParameters())
    , m_StaticParameters(NULL)
    , m_FpsParameter(NULL)
    , m_BFMitiParameters(NULL)
    , m_CMaxVelParameter(NULL)
    , m_DMaxVelParameter(NULL)
    , m_DPixelSizeSecParameter(NULL)
    , m_PRFColorParameter(NULL)
    , m_PRFDParameter(NULL)
    , m_RawData(NULL)
    , m_FocusNumBChanged(false)
    , m_OldFocusPosMM(0.0f)
    , m_OldImageMode(MODE_B)
    , m_ProbeChanged(false)
    , m_ECGPosBak(-1)
    , m_ThiBak(-1)
    , m_SteeringAngleBak(-1)
    , m_RotationBak(-1)
    , m_FocusNumBBak(-1)
    , m_ScpdBak(-1)
    , m_SRABak(-1)
    , m_HighDensityBak(-1)
    , m_RelatedParasController(NULL)
    , m_MLineController(NULL)
    , m_ZoomBoxController(NULL)
    , m_SmallZoomBoxController(NULL)
    , m_ROIController(NULL)
    , m_DSamplingGateController(NULL)
    , m_ROIAndDSamplingGateController(NULL)
    , m_PRTOfBParameter(NULL)
    , m_PRTOfCParameter(NULL)
    , m_PRTOfDParameter(NULL)
    , m_SonoNeedleCfmPrfCalculator(NULL)
    , m_CfmPrfCalculator(NULL)
    , m_TdiCfmPrfCalculator(NULL)
    , m_MVICfmPrfCalculator(NULL)
    , m_TriplexCfmPrfCalculator(NULL)
    , m_PWDopPrfCalculator(NULL)
    , m_TDDopPrfCalculator(NULL)
    , m_TriplexDopPrfCalculator(NULL)
    , m_CWDopPrfCalculator(NULL)
    , m_ElastoPrfCalculator(NULL)
    , m_TriplexModeHelper(NULL)
    , m_CWDPRFCalculator(NULL)
    , m_NewDPixelSizeSecParameter(NULL)
    , m_SoundController(NULL)
    , m_APRangeSettings(new APRangeSettings())
    , m_APPressCalculator(NULL)
    , m_MPixelSizeSecParameter(NULL)
    , m_ImageRenderLayoutRects(new ImageRenderLayoutRects())
    , m_CQYZStepConfig(NULL)
    , m_CQYZPreferValueCalculator(NULL)
    , m_NeedleSettingSet(NULL)
    , m_ProbeInfoModel(NULL)
    , TrapezoidalModeChangingCnt(0)
    , m_GainValue(0)
    , m_PostProcessHandler(nullptr)
{
    connect(this, SIGNAL(dataUpdated()), this, SLOT(onUpdateData()));
    m_Probe = ProbeDataSet::instance().firstProbe();
    m_Probe.Name = "DefaultProbe"; //初始化时，给定一个特殊的名字作为标记

    m_SupportFreezeOutage = ModelConfig::instance().value(ModelConfig::SupportFreezeOutage, false).toBool();
}

BeamFormerBase::~BeamFormerBase()
{
    Util::SafeDeletePtr(m_FpsParameter);
    Util::SafeDeletePtr(m_BFMitiParameters);
    Util::SafeDeletePtr(m_CMaxVelParameter);
    Util::SafeDeletePtr(m_DMaxVelParameter);
    Util::SafeDeletePtr(m_DPixelSizeSecParameter);
    Util::SafeDeletePtr(m_PRFColorParameter);
    Util::SafeDeletePtr(m_PRFDParameter);

    Util::SafeDeletePtr(m_StaticParameters);
    Util::SafeDeletePtr(m_DepthParameters);
    Util::SafeDeletePtr(m_BlockDataSender);
    Util::SafeDeletePtr(m_ControlTable);
    Util::SafeDeletePtr(m_BFIODevice);
    Util::SafeDeletePtr(m_RawData);

    Util::SafeDeletePtr(m_PRTOfBParameter);
    Util::SafeDeletePtr(m_PRTOfCParameter);
    Util::SafeDeletePtr(m_PRTOfDParameter);

    Util::SafeDeletePtr(m_SonoNeedleCfmPrfCalculator);
    Util::SafeDeletePtr(m_CfmPrfCalculator);
    Util::SafeDeletePtr(m_TdiCfmPrfCalculator);
    Util::SafeDeletePtr(m_MVICfmPrfCalculator);
    Util::SafeDeletePtr(m_TriplexCfmPrfCalculator);
    Util::SafeDeletePtr(m_PWDopPrfCalculator);
    Util::SafeDeletePtr(m_TDDopPrfCalculator);
    Util::SafeDeletePtr(m_ElastoPrfCalculator);
    Util::SafeDeletePtr(m_TriplexDopPrfCalculator);
    Util::SafeDeletePtr(m_CWDopPrfCalculator);
    Util::SafeDeletePtr(m_TriplexModeHelper);
    Util::SafeDeletePtr(m_CWDPRFCalculator);
    Util::SafeDeletePtr(m_NewDPixelSizeSecParameter);
    Util::SafeDeletePtr(m_SoundController);
    Util::SafeDeletePtr(m_APRangeSettings);
    Util::SafeDeletePtr(m_APPressCalculator);
    Util::SafeDeletePtr(m_MPixelSizeSecParameter);
    Util::SafeDeletePtr(m_ImageRenderLayoutRects);
    Util::SafeDeletePtr(m_CQYZStepConfig);
    Util::SafeDeletePtr(m_CQYZPreferValueCalculator);
    Util::SafeDeletePtr(m_NeedleSettingSet);
    Util::SafeDeletePtr(m_PostProcessHandler);
}

const PostProcessHandlerBase* BeamFormerBase::postProcessHandler() const
{
    return m_PostProcessHandler;
}

/*================================================================================================================================*/
/**@brief 设置BeamFormer的底层设备
 * @param bfIODevice
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
void BeamFormerBase::setBFIODevice(UltrasoundDevice* bfIODevice)
{
    if (m_BFIODevice == bfIODevice)
    {
        return;
    }

    Util::SafeDeletePtr(m_BFIODevice);

    m_BFIODevice = bfIODevice;

    m_ControlTable->setBFIODevice(m_BFIODevice);
    m_BFIODevice->addObserver(this);
}

IUltrasoundDevice* BeamFormerBase::bfIODevice()
{
    return m_BFIODevice;
}

/*================================================================================================================================*/
/**@brief BeamFormerBase::initialize
 * @param -
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
void BeamFormerBase::initialize()
{
    createBFIODevice();
    initializeControlTable();
    initializeTimer();
    createStaticParameters();
    createRelatedParasController();
    initializeProbeDection();
    createPostProcessHandler();
    initializePostProcessHandler();
}

void BeamFormerBase::setControlTableLen(int len)
{
    m_ControlTable->setControlDataLen(len);
}

void BeamFormerBase::setSonoParametersForShutDown(SonoParameters* sonoParameters)
{
    //仅初始化关机前所需的参数
    m_SonoParameters = sonoParameters;
    connectParametersSignals();

    initializeImageSize();
    m_RawData = new DataArg(imageWidth(), imageHeight(), 8);

    m_BFIODevice->setReadingSize(getIOReadSize());

    QScopedPointer<IBFKitFactory> kit(createBFKitFactory());
    m_BFDataHandler = kit->createBFDataHandler(m_RawData->data(), imageWidth() * imageHeight(), this);
    m_ControlTable->setIsInitialized(true);
}

bool BeamFormerBase::open()
{
    int readSize = getIOReadSize();
    m_BFIODevice->setReadingSize(readSize);
    qint32 code = 0;
    if (Setting::instance().defaults().isIODeviceVirtual())
    {
        code = m_BFIODevice->open();
    }
    else
    {
#ifndef USE_TARGET_PALM
        code = m_BFIODevice->open();
        if (code != 0)
        {
            return false;
        }
        log()->info() << PRETTY_FUNCTION << "code:" << code;
#else
        m_BFIODevice->addObserver(this);
        m_BFDataHandler->startScanProbe();
        if (!m_ProbesSwitchController.isNull())
        {
            qDebug() << PRETTY_FUNCTION << "ProbesSwitchController";
            m_ProbesSwitchController->setReadingSize(readSize);
            m_ProbesSwitchController->initializeUsbProbeDection();
        }
#endif
    }

    freeze(false);
    //系统启动后，需要下发一次控制表，系统启动前是冻结状态
    m_ControlTable->send();
    sendHardwareKey();
    setGrayMap(pV(BFPNames::GrayMapStr).toByteArray());
    //打开或关闭高压使能，在系统启动时，需要关闭高压使能20ms，然后打开高压使能；系统重启或关闭时，需要关闭高压使能
    QTimer::singleShot(20, this, SLOT(setHighVoltage()));

    if (Setting::instance().defaults().isIODeviceVirtual())
    {
        QTimer::singleShot(300, this, SLOT(startUpdateData()));
    }
    else
    {
#ifndef USE_TARGET_PALM
        //等待不稳定数据结束后，再开始刷新数据
        QTimer::singleShot(300, this, SLOT(startUpdateData()));
#endif
    }
    connect(&ProbeDataSet::instance(), SIGNAL(probeDataChanged()), this, SLOT(onProbeDataChanged()));
    return true;
}

void BeamFormerBase::close()
{
    disconnect(&ProbeDataSet::instance(), SIGNAL(probeDataChanged()), this, SLOT(onProbeDataChanged()));
    m_SoundController->closeSpeaker();

    freeze(false);

    //由于closeTransferSignal 后，无法read data，无法关闭读线程，必须先关闭读线程
    m_BFIODevice->closeRead();
    m_BFIODevice->closeWrite();

    {
        ControlTableSyncSender cs(m_ControlTable);
        closeTransferSignal();
        //关闭高压
        setHighVoltage(false);
        setPV(BFPNames::KeyBoardLightEnStr, false); //软件关闭时关掉灯
    }

    freeze(true);

    m_BFIODevice->close();
}

void BeamFormerBase::sendHardwareKey()
{
}

void BeamFormerBase::suspendRead()
{
    {
        ControlTableSyncSender cs(m_ControlTable);
        m_CTFreezeParameter->setValue((const QVariant&)true);
        m_ControlTable->send(m_CTFreezeParameter);
    }

    suspendBFIODeviceRead();
}

void BeamFormerBase::resumeRead()
{
    m_BFIODevice->resumeRead();
    m_CTFreezeParameter->setValue((const QVariant&)false);
    ControlTableSender cs(m_ControlTable);
    m_ControlTable->send(m_CTFreezeParameter);
}

void BeamFormerBase::shutdown()
{
    freeze(true);

    ControlTableSyncSender cs(m_ControlTable);
    setPV(BFPNames::ShutDownStr, true);
}

QSize BeamFormerBase::imageSize() const
{
    return pV(BFPNames::ImageSizeStr).toSize();
}

int BeamFormerBase::imageWidth() const
{
    return imageSize().width();
}

int BeamFormerBase::imageHeight() const
{
    return imageSize().height();
}

QSize BeamFormerBase::bImageSize() const
{
    return pV(BFPNames::BImageSizeStr).toSize();
}

QSize BeamFormerBase::mImageSize() const
{
    return pV(BFPNames::MImageSizeStr).toSize();
}

QSize BeamFormerBase::dImageSize() const
{
    return pV(BFPNames::DImageSizeStr).toSize();
}

double BeamFormerBase::startDepthMM() const
{
    return m_PostProcessHandler->startDepthMM();
}

double BeamFormerBase::depthMM() const
{
    return m_PostProcessHandler->depthMM();
}

double BeamFormerBase::imageBottomDepthMM() const
{
    ProbeParameters p(curProbe(), startDepthMM(), pixelSizeMM(), zoomOn(), pIV(BFPNames::StartLineStr),
                      pIV(BFPNames::StopLineStr));
    return m_PostProcessHandler->imageBottomDepthMM();
}

double BeamFormerBase::pixelSizeMM() const
{
    return m_PostProcessHandler->pixelSizeMM();
}

double BeamFormerBase::mPixelSizeSec() const
{
    return pFV(BFPNames::MPixelSizeSecStr);
}

double BeamFormerBase::mPixelSizeMM() const
{
    return pDV(BFPNames::MPixelSizeMMStr);
}

double BeamFormerBase::dPixelSizeCMS() const
{
    return pDV(BFPNames::DPixelSizeCMSStr);
}

double BeamFormerBase::dPixelSizeSec() const
{
    return pDV(BFPNames::DPixelSizeSecStr);
}

void BeamFormerBase::freeze(bool on)
{
    setPV(BFPNames::FreezeStr, on);
}

bool BeamFormerBase::isFrozen() const
{
    return pBV(BFPNames::FreezeStr);
}

void BeamFormerBase::setSystemScanMode(SystemScanMode mode)
{
    onSetSystemScanMode(mode);
}

void BeamFormerBase::onSetSystemScanMode(SystemScanMode mode)
{
    if (mode == SystemScanModeE && !m_IsFPGAElastographyOn)
    {
        m_IsFPGAElastographyOn = true;
        controlIsFPGAElastoGraphyOn();
    }
    else if (m_IsFPGAElastographyOn && mode != SystemScanModeE)
    {
        m_IsFPGAElastographyOn = false;
        controlIsFPGAElastoGraphyOn();
    }
    ControlTableSender ct(m_ControlTable);

    controlLGCEnabled(mode);
    // zoomon的控制比较特殊，只能是切换模式时关闭，不然无法实现冻结/解冻状态切换还保留放大状态
    // 2023-06-02 Modifby by AlexWang 解决切换其他模式时保持放大预备状态
    // setPV(BFPNames::ZoomSelectStr, false);
    // 2023-03-21 Modify by AlexWang 解决切换其他模式时保持放大状态
    // setPV(BFPNames::ZoomOnStr, false);
    setPV(BFPNames::SystemScanModeStr, (int)mode);
    setPV(BFPNames::ImageModesStr, getImageModes());

    // 2023-05-12 Write by AlexWang
    // 解决在C模式冻结返回后再进入B模式后无法进入局部放大预备状态，当再次切回B模式时恢复ZoomEnable
    m_StateManager->setIsZoomEnabled(isZoomEnabled());
}

SystemScanMode BeamFormerBase::systemScanMode() const
{
    return (SystemScanMode)pIV(BFPNames::SystemScanModeStr);
}

void BeamFormerBase::setActiveB(int index)
{
    setPV(BFPNames::ActiveBStr, index);
}

int BeamFormerBase::activeB()
{
    return pIV(BFPNames::ActiveBStr);
}

void BeamFormerBase::setGrayMap(unsigned char map[])
{
    setGrayMap(QByteArray((const char*)map, 256));
}

void BeamFormerBase::setGrayMap(const QByteArray& map)
{
    if (map.size() == 256)
    {
        setPV(BFPNames::GrayMapStr, map);
        if (!m_ControlTable->isInitialized() || isFrozen())
        {
            return;
        }

        m_ControlTable->sendBlock(map.size(), (uchar*)map.data(), false,
                                  Setting::instance().defaults().grayMapDataAddress());
    }
}

void BeamFormerBase::rebootFPGA(bool value)
{
    ControlTableSender ctSender(m_ControlTable);

    setPV(BFPNames::ERebootStr, value);
    setPV(BFPNames::NRebootStr, !value);
}

const ProbeDataInfo& BeamFormerBase::curProbe() const
{
    return m_Probe;
}

//此函数只有在当前插槽上插了探头后才会调用
void BeamFormerBase::setCurProbe(const ProbeDataInfo& value)
{
    //系统第一次设置探头时，如果探头码相等，可以通过探头的名字是否为初始化的名字判断
    bool first = (curProbe().Name == "DefaultProbe");
    if (curProbe().Code != value.Code || first)
    {
        m_BlockDataSender->clear();
        m_Probe = value;

        m_ProbeChanged = true;

        Q_ASSERT(bfIODevice()->isOpen());

        if (m_SupportFreezeOutage)
        {
            bfIODevice()->setDeviceBusy(true);
        }

        DataFlagTimerLocker flagLocker(&m_UnstableParaTimer);

        //在Probe State时，切换探头，通过此函数，使状态机先执行切换到BMode的state
#ifndef __APPLE__
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
#endif

        {
            ControlTableSyncSender cs(m_ControlTable);
            setPV(BFPNames::ConfigDoneStr, false);
        }
        setCurProbeFinishedOnSystemBooted();

        onBeginSetCurProbe(first);

        changeProbePara();
        sendFreqdynamic();
        sendDynamicdata();
        sendAllTxRvParas();

        onSetCurProbe(first);

        flagLocker.setDelayedStopTime(m_BFIODevice->leftWriteTime());
    }
    resentProbeParamAndBlockData();

    if (m_SupportFreezeOutage)
    {
        bfIODevice()->setDeviceBusy(false);
    }
}

int BeamFormerBase::socketCount() const
{
    return m_BFDataHandler->socketCount();
}

int BeamFormerBase::curSocket() const
{
    return pIV(BFPNames::SocketStr);
}

bool BeamFormerBase::selectSocket(int socket, bool writeSocket)
{
    Q_UNUSED(writeSocket)
    imageIntoUnstable();
    setPV(BFPNames::SocketStr, socket);

    return true;
}

bool BeamFormerBase::selectProbe(int socket, int probeCode)
{
    ControlTableSender ctSender(m_ControlTable);

    selectSocket(socket);
    setCurProbe(ProbeDataSet::instance().probe(probeCode));

    return true;
}

void BeamFormerBase::startScanProbe()
{
}

QVector<int> BeamFormerBase::allProbeCodesConnected() const
{
    return m_BFDataHandler->probeCodes();
}

void BeamFormerBase::onEnterProbeState()
{
    ControlTableSender cs(m_ControlTable);

    resumeRead();

    ControlTableParameter* txOffP = qobject_cast<ControlTableParameter*>(parameter(BFPNames::TxOffStr));
    if (txOffP != NULL)
    {
        //进入探头选择界面，关闭发射
        m_ControlTable->send(txOffP, txOffP->trueValue(), true, false);
    }
}

void BeamFormerBase::onExitProbeState(bool isSuspendRead)
{
    {
        ControlTableSyncSender cs(m_ControlTable);

        ControlTableParameter* txOffP = qobject_cast<ControlTableParameter*>(parameter(BFPNames::TxOffStr));
        if (txOffP != NULL)
        {
            //退出探头选择界面，开启发射
            m_ControlTable->send(txOffP, 1 - txOffP->trueValue(), true, false);
        }
    }

    if (isSuspendRead)
    {
        suspendRead();
    }
}

bool BeamFormerBase::isProbeOnline() const
{
    return pBV(BFPNames::ProbeConnectedStr);
}

void BeamFormerBase::reset()
{
    m_ControlTable->resendControlTable();
    m_BlockDataSender->resend();
}

const QString& BeamFormerBase::fpgaVersion() const
{
    return m_BFDataHandler->fpgaVersion();
}

const QString& BeamFormerBase::cpldVersion() const
{
    return m_BFDataHandler->cpldVersion();
}

const QByteArray& BeamFormerBase::fpgaSN() const
{
    return m_BFDataHandler->fpgaSN();
}

bool BeamFormerBase::isFpgaKeyValid() const
{
    return m_BFDataHandler->isFpgaKeyValid();
}

const QString& BeamFormerBase::fpgaUSBVersion() const
{
    return m_BFDataHandler->USBVersion();
}

const QString& BeamFormerBase::fpgaDSPVersion() const
{
    return m_BFDataHandler->DSPVersion();
}

const QString& BeamFormerBase::fpgaDBFVersion() const
{
    return m_BFDataHandler->DBFVersion();
}

const QString& BeamFormerBase::fpgaDSCVersion() const
{
    return m_BFDataHandler->DSCVersion();
}

const QString& BeamFormerBase::fpgaECVersion() const
{
    return m_BFDataHandler->ECVersion();
}

const QString& BeamFormerBase::fpgaTXBF1Version() const
{
    return m_BFDataHandler->TXBF1Version();
}

const QString& BeamFormerBase::fpgaTXBF2Version() const
{
    return m_BFDataHandler->TXBF2Version();
}

bool BeamFormerBase::isImageUnstable() const
{
    return m_UnstableParaTimer.isImageUnstable();
}

bool BeamFormerBase::isImageShapeUnstable() const
{
    return m_UnstableParaTimer.isImageShapeUnstable();
}

bool BeamFormerBase::isUpdateImage() const
{
    return m_BFDataHandler->isUpdateImage();
}

void BeamFormerBase::setUpdateImage(bool value)
{
    m_BFDataHandler->setUpdateImage(value);
}

PhysicalGeometryController* BeamFormerBase::mLineController() const
{
    return m_MLineController;
}

PhysicalGeometryController* BeamFormerBase::zoomBoxController() const
{
    return m_ZoomBoxController;
}

PhysicalGeometryController* BeamFormerBase::smallZoomBoxController() const
{
    return m_SmallZoomBoxController;
}

PhysicalGeometryController* BeamFormerBase::panZoomBoxController() const
{
    return m_PostProcessHandler->panZoomBoxController();
}

PhysicalGeometryController* BeamFormerBase::rOIController() const
{
    return m_ROIController;
}

PhysicalGeometryController* BeamFormerBase::dSamplingGateController() const
{
    return m_DSamplingGateController;
}

PhysicalGeometryController* BeamFormerBase::rOIAndDSamplingGateController() const
{
    return m_ROIAndDSamplingGateController;
}

PhysicalLineController* BeamFormerBase::freeMLineController() const
{
    return m_FreeMLineController;
}

PhysicalGeometryController* BeamFormerBase::fourdROIController() const
{
    return m_FourDROIController;
}

void BeamFormerBase::reloadCurProbe()
{
    //系统第一次设置探头时，如果探头码相等，可以通过探头的名字是否为初始化的名字判断
    bool first = (curProbe().Name == "DefaultProbe");
    if (!first)
    {
        m_ProbeChanged = true;

        freeze(false);

        Q_ASSERT(bfIODevice()->isOpen());

        if (m_SupportFreezeOutage)
        {
            bfIODevice()->setDeviceBusy(true);
        }

        DataFlagTimerLocker flagLocker(&m_UnstableParaTimer);

        {
            ControlTableSyncSender cs(m_ControlTable);
            setPV(BFPNames::ConfigDoneStr, false);
        }

        changeProbePara();
        sendFreqdynamic();
        sendDynamicdata();

        // TODO:reload时，会更换块数据，某些和块数据相关的参数需要强制设置才会下发新的块数据
        //目前是每次设置频率相关参数时，都强制设置此类参数，可能会影响效率，以后可能要改为
        //需要强制时才进行强制设置
        setPreset(m_SonoParameters->currentPreset());

        flagLocker.setDelayedStopTime(m_BFIODevice->leftWriteTime());

        if (m_SupportFreezeOutage)
        {
            bfIODevice()->setDeviceBusy(false);
        }
    }
}

void BeamFormerBase::exportControlTable(const QString& fileName)
{
    m_ControlTable->exportControlTable(fileName);
}

bool BeamFormerBase::importControlTable(const QString& fileName)
{
    return m_ControlTable->importControlTable(fileName);
}

bool BeamFormerBase::importControlTable(const uchar* data, uint size, uint destoffset)
{
    return m_ControlTable->importControlTable(data, size, destoffset);
}

bool BeamFormerBase::importBlockData(const QString& fileName)
{
    QFileInfo fInfo(fileName);
    bool regResult = QRegExp("data\\d{1,3}").exactMatch(fInfo.baseName());
    if (!regResult)
        return false;

#ifdef USE_TARGET_PALM
    const int blockLen = 1024;
#else
    const int blockLen = 2048;
#endif

    QFile file(fileName);
    int address = fInfo.baseName().midRef(QString("data").size()).toInt();
    char* writeBuf = new char[blockLen];

    bool isFinished = false;
    ControlTable::m_OldBlockNum = 0;
    if (file.open(QIODevice::ReadOnly))
    {
        int count = file.size() / blockLen;
        for (int j = 0; j < count; j++)
        {
            file.seek(j * blockLen);
            file.read(writeBuf, blockLen);
            isFinished = (j + 1 == count);
            m_ControlTable->sendBlock(blockLen, (unsigned char*)writeBuf, isFinished, address, j);
        }
        file.close();
    }

    delete[] writeBuf;

    return true;
}

bool BeamFormerBase::importBlockData(char* data, int len, int addr)
{
    if (data == nullptr)
        return false;

    bool result = false;
#ifdef USE_TARGET_PALM
    const int blockLen = 1024;
#else
    const int blockLen = 2048;
#endif
    int count = len / blockLen;

    bool isFinished = false;
    ControlTable::m_OldBlockNum = 0;

    for (int j = 0; j < count; j++)
    {
        isFinished = (j + 1 == count);
        result = m_ControlTable->sendBlock(blockLen, (unsigned char*)(data + j * blockLen), isFinished, addr, j);
    }
    return result;
}

int BeamFormerBase::logFrameCount() const
{
    return m_BFDataHandler->logFrameCount();
}

void BeamFormerBase::setLogFrameCount(int value)
{
    m_BFDataHandler->setLogFrameCount(value);
}

void BeamFormerBase::writeRawData(const QString& additionInfo)
{
    m_BFDataHandler->writeRawData(additionInfo);
}

int BeamFormerBase::controlDataLen() const
{
    return m_ControlTable->controlDataLen();
}

unsigned char BeamFormerBase::controlDataValue(int index) const
{
    return m_ControlTable->byteValue(index);
}

void BeamFormerBase::setControlDataValue(unsigned char value, int index)
{
    m_ControlTable->setByteValue(value, index);
}

void BeamFormerBase::sendNormalBlockDataGroup(const QString& probeName, const QStringList& sendDataNames)
{
    m_BlockDataSender->sendNormalBlockDataGroup(probeName, sendDataNames);
}

void BeamFormerBase::sendBlockDataGroupByValue(const QString& probeName, const QString& sendDataName,
                                               const char* sendValue, int sendValueSize)
{
    m_BlockDataSender->sendBlockDataGroupByValue(probeName, sendDataName, sendValue, sendValueSize);
}

void BeamFormerBase::setSoundController(ISoundController* controller)
{
    m_SoundController = controller;
}

FocusParasModel* BeamFormerBase::focusParasModel() const
{
    return NULL;
}

FGCSettingModel* BeamFormerBase::fgcSettingModel() const
{
    return NULL;
}

FocusesCombineModel* BeamFormerBase::focusesCombineModel() const
{
    return NULL;
}

AcousticPowerPressCalculator* BeamFormerBase::APPressCalculator() const
{
    return m_APPressCalculator;
}

NeedleSettingSet* BeamFormerBase::needleSettingSet() const
{
    return m_NeedleSettingSet;
}

IProbeInfoModel* BeamFormerBase::probeInfoModel()
{
    if (m_ProbeInfoModel == NULL)
    {
        m_ProbeInfoModel = new ProbeInfoModel(this);
    }
    return m_ProbeInfoModel;
}

BaseBFStaticParameters* BeamFormerBase::staticParameters() const
{
    return m_StaticParameters;
}

bool BeamFormerBase::standby()
{
    if (!isStandby())
    {
        bool value = m_SonoParameters->isRealTime();
        if (value)
        {
            m_SonoParameters->setIsRealTime(false);
        }
        freeze(false);

        m_BFIODevice->suspendRead();

        {
            ControlTableSyncSender cs(m_ControlTable);
            closeTransferSignal();
            setHighVoltage(false);
        }
        freeze(true);

        if (value)
        {
            m_SonoParameters->setIsRealTime(true);
        }

        UltrasoundDevice* device = dynamic_cast<UltrasoundDevice*>(m_BFIODevice);

        bool res = false;
        if (device != NULL)
        {
            res = device->standby();
            if (res)
            {
                qDebug() << "setUsbSignaltoLow";
                //                GpioDevice::instance().setUsbSignalLow();
                HardWareControlUtil::disableUSBReset();
            }
        }
        setIsStandby(res);
        return res;
    }
    return true;
}

bool BeamFormerBase::wake()
{
    if (isStandby())
    {
        qDebug() << "BeamFormerBase::wake";
        //        GpioDevice::instance().setUsbSignalLow();//拉低放到休眠中

        UltrasoundDevice* device = dynamic_cast<UltrasoundDevice*>(m_BFIODevice);
        bool wakeSuc = false;
        if (device != NULL)
        {
            wakeSuc = device->wake();
        }
        if (wakeSuc)
        {
            qDebug() << "usbdevice wake success";
            //探头断开不下发数据不更换探头,解冻不会下发块数据
            //强制发送休眠前当前探头位的探头块数据
            bool value = m_SonoParameters->isRealTime();
            if (value)
            {
                m_SonoParameters->setIsRealTime(false);
            }
            freeze(false);
            {
                qDebug() << "start send probe data";
                ControlTableSyncSender cs(m_ControlTable);
                m_ControlTable->send();
                sendHardwareKey();
                setGrayMap(pV(BFPNames::GrayMapStr).toByteArray());

                //                Util::usleep(20000);
                setHighVoltage();
                //                Util::usleep(300000);

                resentCurProbeBlockData();

                if (pBV(BFPNames::FreqSpectrumStr))
                {
                    ControlTableParameter* freqSpectrumP =
                        qobject_cast<ControlTableParameter*>(parameter(BFPNames::FreqSpectrumStr));
                    if (freqSpectrumP != NULL)
                    {
                        m_ControlTable->send(freqSpectrumP, 1 - freqSpectrumP->trueValue(), true, false);
                    }
                }
                if (pBV(BFPNames::TriplexModeStr))
                {
                    ControlTableParameter* triplexModeP =
                        qobject_cast<ControlTableParameter*>(parameter(BFPNames::TriplexModeStr));
                    if (triplexModeP != NULL)
                    {
                        m_ControlTable->send(triplexModeP, 1 - triplexModeP->trueValue(), true, false);
                    }
                }
                if (pBV(BFPNames::FreeMModeStr))
                {
                    parameter(BFPNames::FreeMBlockStr)->update();
                }
            }

            Util::processEvents(QEventLoop::ExcludeUserInputEvents);
            setIsStandby(false);
            freeze(true);
            if (value)
            {
                m_SonoParameters->setIsRealTime(true);
            }
        }
        else
        {
            log()->info(QString("%1 usbdevice wake failed").arg(PRETTY_FUNCTION));
            return false;
        }
    }
    return true;
}

void BeamFormerBase::restoreData()
{
}

void BeamFormerBase::resentProbeParamAndBlockData()
{
}

void BeamFormerBase::resentCurProbeBlockData()
{
    sendNormalBlockDataGroup(curProbe().Name);
    //基元检测功能需要将部分数据块独立，添加common_elem文件夹，将其中的normal数据块放进去，外部原有的normal数据块保留，解决data42、43还在zeus中使用冲突的问题；
    //其他参数关联类型的数据块，在setPreset中根据参数变化下发；
    //必须要在sendNormalBlockDataGroup之后下发，否则会被覆盖
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name, m_BlockDataParaNameConverter->groupParaName("Common"),
                                              0);
    //"CurvedCpd"由CPDSteer,BFPNames::WeightedCurveStr由RvFNo/RvPWFNo
    // BFPNames::RvFNoStr由RvFNo/RvPWFNo,BFPNames::CQYZStr由CPDSteer
    //"CurvedCpdTx"由TxFNo/CPDSteer,"CWFocus"由BFPNames::DScanLineStr触发
    QStringList paraNames = QStringList()
                            << BFPNames::THIStr << BFPNames::FreqSpectrumStr << BFPNames::FreqIndexBStr
                            << BFPNames::TxFNoStr << BFPNames::CPDSteerStr << BFPNames::DopRvFNumStr
                            << BFPNames::VesselThresholdStr << BFPNames::ThiModeStr << BFPNames::FilterCpdStr
                            << BFPNames::WallFilterSettingStr << BFPNames::CPAWallFilterSettingStr
                            << BFPNames::DScanLineStr << BFPNames::CXFNumStr << BFPNames::CpaDBIndexStr;
    foreach (QString paraName, paraNames)
    {
        parameter(paraName)->update();
    }
    calculateBlockDataAndSend();
}

void BeamFormerBase::resetFPGA()
{
    if (m_IsFPGAElastographyOn)
    {
        freeze(false);
        rebootFPGA(false);
        freeze(true);
    }
}
void BeamFormerBase::onProbeDataChanged()
{
    updateProbe();
}
void BeamFormerBase::updateProbe()
{
    // AdjustmentOfBStr后台保存之后更新了probeinfo,但是没有更新curProbe(),所以这里重新获取probeinfo
    m_Probe = ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
    emit probeUpdated();

    QList<int> angles = needleAngles();
    if (m_NeedleSettingSet != nullptr)
    {
        m_NeedleSettingSet->setAllNeedleAngles(angles);
        m_NeedleSettingSet->setAbsNeedleAngles(curProbe().Name, curProbe().NeedleAngles);
        m_NeedleSettingSet->save();
        m_NeedleSettingSet->load();
        if (pBV(BFPNames::NeedleModeStr))
        {
            parameter(BFPNames::NeedleAngleIndexStr)->update();
        }
    }
    ProbeDataSet::instance().writeDepthShowList(curProbe().Name, pV(BFPNames::DepthCMListStr).toString().split(","));
    emit needleAnglesChanged(angles);
}

void BeamFormerBase::onBeforeFreezeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    if (newValue.toBool())
    {
        m_ControlTable->backupControlTable();
        // usb 读线程停止状态，无法写数据到usb，必须先写数据(freeze)到usb，再停止读线程
        //冻结时先以同步模式下发冻结指令
        this->m_ControlTable->setWriteMode(ControlTable::Syn);
        //同步方式冻结之前，要清空写数据队列，否则可能，同步控制了冻结，但之后异步队队列中
        //会下发解冻参数，导致冻结失效
        m_BFIODevice->clearWriteQueue();
    }
    else
    {
        //解冻时，先启动读线程
        m_BFIODevice->resumeRead();
    }
}

void BeamFormerBase::onFreezeChanged(const QVariant& value)
{
    if (value.toBool())
    {
        // usb 读线程停止状态，无法写数据到usb，必须先写数据(freeze)到usb，再停止读线程
        this->m_ControlTable->setWriteMode(ControlTable::Asyn);
        suspendBFIODeviceRead();
        //读线程停止,恢复2B/4B的最后冻结帧的判定值为false
        ImageModeType mode = imageMode();
        if (mode == MODE_2B || mode == MODE_4B)
        {
            m_BFDataHandler->setNeedCheckFrame(false);
            m_BFDataHandler->setIsLastActiveBFrame(false);
        }
        m_PostParameterHelper.setIsPostAvailable(true);
        m_PostParameterHelper.setRestoreParameterValues(m_SonoParameters);
    }
    else
    {
        m_BFDataHandler->startScanProbe();
        m_RefreshTimer.start();
        //        controlImageZoomCoef();
        //        //pw下回调图片然后按freeze，首先触发的是systemscanmodechange，然后才是freezechange，
        //        //这样会有一个问题systemscanmodechange中设置的imgfrz值无法下发下去，所以在这里update一下
        //        parameter(BFPNames::ImgFrzStr)->update();
        imageShapeIntoUnstable();
        m_PostParameterHelper.restoreParameterValue(m_SonoParameters);
    }

    controlSoundOpened(ISoundController::General);
}

void BeamFormerBase::onFrequencyCompoundingChanging(const QVariant& value)
{
    FreqRelatedParasSender fpSender(m_RelatedParasController);

    setPV(BFPNames::SRAIDStr, value);
    if (value.toBool())
    {
        controlFocusNumB();
    }

    setPV(BFPNames::FcpdOnStr, value);
}

void BeamFormerBase::onFrequencyCompoundingOtherChanging(const QVariant& value)
{
    Q_UNUSED(value);
    FreqRelatedParasSender fpSender(m_RelatedParasController);

    if (!isScpdEnabled())
    {
        setPV(BFPNames::FcpdOnStr, false);
    }
}

void BeamFormerBase::onGettingSocketControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (m_BFDataHandler != NULL)
    {
        const QVector<int>& socketMap = m_BFDataHandler->socketMap();
        if (!socketMap.isEmpty())
        {
            int iv = value.toInt();
            if (iv >= 0 && iv < socketMap.count())
            {
                controlTableValue = socketMap[iv];
            }
        }
    }
}

void BeamFormerBase::onTHIChanging(const QVariant& value)
{
    //这里注释的原因是该信号未连接任何槽函数，也为了复用类"ImageDataTransfer"中的定时器 sjh
    // emit updateUnstableTime(1500);
    imageIntoUnstable(1000);

    THIChangingHandle(value);
}

void BeamFormerBase::onGettingTHIPreset(QVariant& value)
{
    if (m_ThiBak != -1)
    {
        value = (m_ThiBak == 1 ? true : false);
    }
}

/**
 * @brief onImageZoomCoefChanging
 *
 * FPGA对图像整体缩小的参数，控制表下发用ImageZoomCoefBinStr，是
 * 浮点数的二进制表示，
 *
 * @param value 是百分制的值，目前60～90，step:5
 * ImageZoomCoefBinStr=(value/100)的倒数的二进制表示
 * 此参数会影响系统所用的PixelSizeMM，
 * 但是计算某些参数时：probe interval probe 垂距 斜距，血流采样框上下边界，Doppler采样门深度
 * PixelSizeMM必须与此参数无关，底下FPGA已经统一计算，
 * 计算与ZoomCoef无关的PixelSizeMM = (pDV(BFPNames::PixelSizeMMStr) * (pDV(BFPNames::ImageZoomCoefStr) / 100.0f))
 * @todo 可能未来还需修改某些参数 //TODO
 */
void BeamFormerBase::onImageZoomCoefChanging(const QVariant& value)
{
    m_PostProcessHandler->onImageZoomCoefChanging(value);
}

void BeamFormerBase::onImageZoomCoefChanged(const QVariant& value)
{
    Q_UNUSED(value);
    //    imageShapeIntoUnstable();
}

void BeamFormerBase::onGettingImageZoomCoefText(QString& value)
{
    m_PostProcessHandler->onGettingImageZoomCoefText(value);
}

void BeamFormerBase::onGettingColorTransparencyText(QString& value)
{
    value = QString("%1 %").arg(qobject_cast<Parameter*>(sender())->intValue());
}

void BeamFormerBase::onGettingTDITransparencyText(QString& value)
{
    value = QString("%1 %").arg(qobject_cast<Parameter*>(sender())->intValue());
}

void BeamFormerBase::onGettingMVITransparencyText(QString& value)
{
    value = QString("%1 %").arg(qobject_cast<Parameter*>(sender())->intValue());
}

void BeamFormerBase::onGettingSonoNerveTransText(QString& value)
{
    value = QString("%1 %").arg(qobject_cast<Parameter*>(sender())->intValue());
}

void BeamFormerBase::onGettingSonoMSKTransText(QString& value)
{
    value = QString("%1 %").arg(qobject_cast<Parameter*>(sender())->intValue());
}

void BeamFormerBase::onImageZoomCoefBChanging(const QVariant& value)
{
    ControlTableSender ct(m_ControlTable);

    m_PostProcessHandler->onImageZoomCoefBChanging(value);
}

void BeamFormerBase::onImageZoomCoefOtherChanging(const QVariant& value)
{
    onImageZoomCoefBChanging(value);
}

void BeamFormerBase::onFullScreenZoomInIndexChanging(const QVariant& value)
{
    Setting::instance().defaults().setFullScreenZoomInIndex(value.toInt());
}

void BeamFormerBase::onColorInvertStateChanging(const QVariant& value)
{
    if (pBV(BFPNames::ECGEnStr))
    {
        imageIntoUnstable();
    }
    setPV(BFPNames::ColorInvertStr, value);
}

void BeamFormerBase::onDPDInvertStateChanging(const QVariant& value)
{
    onColorInvertStateChanging(value);
}

void BeamFormerBase::onTDIInvertStateChanging(const QVariant& value)
{
    onColorInvertStateChanging(value);
}

void BeamFormerBase::onMVIInvertStateChanging(const QVariant& value)
{
    onColorInvertStateChanging(value);
}

void BeamFormerBase::onGettingFreqIndexColorText(QString& value)
{
    value = curProbe().Freqs[qobject_cast<Parameter*>(sender())->intValue()];
}

void BeamFormerBase::onGettingFreqIndexColorMax(int& value)
{
    value = curProbe().FreqIndexes.count() - 1;
}

void BeamFormerBase::onGettingFreqIndexColorControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = curProbe().FreqIndexes[value.toInt()];
}

void BeamFormerBase::onGettingFreqIndexPDText(QString& value)
{
    onGettingFreqIndexColorText(value);
}

void BeamFormerBase::onGettingFreqIndexPDMax(int& value)
{
    onGettingFreqIndexColorMax(value);
}

void BeamFormerBase::onGettingFreqIndexSNText(QString& value)
{
    onGettingFreqIndexColorText(value);
}

void BeamFormerBase::onGettingFreqIndexSNMax(int& value)
{
    onGettingFreqIndexColorMax(value);
}

void BeamFormerBase::onGettingFreqIndexTDIText(QString& value)
{
    onGettingFreqIndexColorText(value);
}

void BeamFormerBase::onGettingFreqIndexTDIMax(int& value)
{
    onGettingFreqIndexColorMax(value);
}

void BeamFormerBase::onGettingFreqIndexDopText(QString& value)
{
    onGettingFreqIndexColorText(value);
}

void BeamFormerBase::onGettingFreqIndexDopMax(int& value)
{
    onGettingFreqIndexColorMax(value);
}

void BeamFormerBase::onGettingFreqIndexTDText(QString& value)
{
    onGettingFreqIndexColorText(value);
}

void BeamFormerBase::onGettingFreqIndexTDMax(int& value)
{
    onGettingFreqIndexColorMax(value);
}

void BeamFormerBase::onVolumeChanged(const QVariant& value)
{
    m_PostProcessHandler->onVolumeChanged(value);
}

void BeamFormerBase::onGettingFreqIndexElastoText(QString& value)
{
    onGettingFreqIndexColorText(value);
}

void BeamFormerBase::onGettingFreqIndexElastoMax(int& value)
{
    onGettingFreqIndexColorMax(value);
}

void BeamFormerBase::onGettingFreqIndexMVIText(QString& value)
{
    onGettingFreqIndexColorText(value);
}

void BeamFormerBase::onGettingFreqIndexMVIMax(int& value)
{
    onGettingFreqIndexColorMax(value);
}

void BeamFormerBase::onColorLineDensityChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onGettingColorLineDensityText(QString& value)
{
    value = pBV(BFPNames::ColorLineDensityStr) ? QString("High") : QString("Low");
}

void BeamFormerBase::onGettingColorLineDensitySNText(QString& value)
{
    value = pBV(BFPNames::ColorLineDensitySNStr) ? QString("High") : QString("Low");
}

void BeamFormerBase::onGettingTDILineDensityText(QString& value)
{
    value = pBV(BFPNames::TDILineDensityStr) ? QString("High") : QString("Low");
}

void BeamFormerBase::onGettingElastoColorLineDensityText(QString& value)
{
    value = pBV(BFPNames::ElastoColorLineDensityStr) ? QString("High") : QString("Low");
}

void BeamFormerBase::onFlowChanging(const QVariant& value)
{
    ControlTableSender ct(m_ControlTable);

    int max = pMax(BFPNames::FlowStr);
    int intValue = value.toInt();

    if (intValue == max)
    {
        setPV(BFPNames::LowVelocityBloodStr, false);
        setPV(BFPNames::ExceedLowBloodStr, 0);
        setPV(BFPNames::ExtremeLowBloodStr, 0);
    }
    else if (intValue == (max - 1))
    {
        setPV(BFPNames::LowVelocityBloodStr, true);
        setPV(BFPNames::ExceedLowBloodStr, 0);
        setPV(BFPNames::ExtremeLowBloodStr, 0);
    }
    else if (intValue == (max - 2))
    {
        setPV(BFPNames::LowVelocityBloodStr, true);
        setPV(BFPNames::ExceedLowBloodStr, 1);
        setPV(BFPNames::ExtremeLowBloodStr, 0);
    }
    else if (intValue == (max - 3))
    {
        setPV(BFPNames::LowVelocityBloodStr, true);
        setPV(BFPNames::ExceedLowBloodStr, 0);
        setPV(BFPNames::ExtremeLowBloodStr, 1);
    }
}

void BeamFormerBase::onGettingFlowText(QString& value)
{
    int max = pMax(BFPNames::FlowStr);
    int intValue = pIV(BFPNames::FlowStr);

    if (intValue == max)
    {
        value = "High";
    }
    else if (intValue == (max - 1))
    {
        value = "Middle";
    }
    else if (intValue == (max - 2))
    {
        value = "Low";
    }
    else if (intValue == (max - 3))
    {
        value = "Extreme Low";
    }
}

void BeamFormerBase::onFlowTDIChanging(const QVariant& value)
{
    onFlowChanging(value);
}

void BeamFormerBase::onGettingFlowTDIText(QString& value)
{
    onGettingFlowText(value);
}

void BeamFormerBase::onBeforeZoomSelectChanged(const QVariant& oldValue, QVariant& newValue)
{
    // 2023-06-17 Write by AlexWang [bug:65095] 开启局部放大预备状态时，如果穿刺增强开启就将其关闭
    if (newValue.toBool() && pBV(BFPNames::NeedleModeStr))
    {
        setPV(BFPNames::NeedleModeStr, false);
    }
}

void BeamFormerBase::onZoomSelectChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlTrapezoidalMode();
    // 2023-06-09 Write by AlexWang 优化状态机中多B下进入局部放大预备状态时切换激活区域的判定逻辑
    m_StateManager->setZoomSelect(value.toBool());
}

void BeamFormerBase::onBeforeZoomOnChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    if (newValue.toBool())
    {
        setPV(BFPNames::ZoomSelectStr, false);

        if (m_FocusNumBBak == -1)
        {
            m_FocusNumBBak = pIV(BFPNames::FocusNumBStr);
        }
        setPV(BFPNames::FocusNumBStr, 0);

        if (m_ScpdBak == -1)
        {
            m_ScpdBak = pIV(BFPNames::ScpdStr);
        }
        setPV(BFPNames::ScpdStr, 0);
    }
    else
    {
        if (m_FocusNumBBak != -1)
        {
            setPV(BFPNames::FocusNumBStr, m_FocusNumBBak);
            m_FocusNumBBak = -1;
        }

        if (m_ScpdBak != -1)
        {
            setPV(BFPNames::ScpdStr, m_ScpdBak);
            m_ScpdBak = -1;
        }
    }

    parameter(BFPNames::FocusNumShowStr)->setEnabled(!newValue.toBool());
}

void BeamFormerBase::onZoomOnChanging(const QVariant& value)
{
    m_StateManager->setZoomOn(value.toBool());

    // 2023-06-13 Write by AlexWang [bug:65066] 更新RegionZoomOn参数值
    m_StateManager->setRegionZoomOn(value.toBool());
    m_DepthParameters->setZoomOn(value.toBool());
    if (value.toBool())
    {
        m_OldImageMode = pIV(BFPNames::ImageModeStr);
        setPV(BFPNames::ImageModeStr, (int)MODE_ZOOM);
        // 2025-05-06 Write by AlexWang [BUG:80330] 切换到4B时，将图像模式设置成4B
        if (m_OldImageMode == MODE_4B)
        {
            setPV(BFPNames::ImageModeStr, (int)m_OldImageMode);
        }
    }
    else
    {
        ControlTableSender ct(m_ControlTable);

        //以下代码必须保证此执行顺序，前一步计算出的参数都被后一步所使用
        //设定放大深度为0，恢复放大前startLine和stopLine，
        //更新很多深度相关的值(此处需要startLine和stopLine)，
        //更新模式为非放大状态，内部回调用focuspos的update，内部会依赖
        // CQYZ中的bottomDepthMM
        setPV(BFPNames::ZoomDepthMMStr, 0);
        setPV(BFPNames::ZoomedCQYZStr, pIV(BFPNames::CQYZStr));
        parameter(BFPNames::ScanWidthStr)->update();
        parameter(BFPNames::CQYZStr)->update();

        // 2023-09-12 Modify by AlexWang 退出局部放大后，会在其他图像模式如2B/4B，需要保持
        if (pIV(BFPNames::ImageModesStr) == MODE_ZOOM)
            setPV(BFPNames::ImageModeStr, m_OldImageMode);
    }

    FreqRelatedParasSender fpSender(m_RelatedParasController);
    controlTrapezoidalMode();
    controlScpdOn();
    controlFcpdOn();

    // 2023-06-14 Write by AlexWang [bug:65071] 进入局部放大状态后禁止调节深度参数
    parameter(BFPNames::CQYZStr)->setEnabled(!value.toBool());

    // 2023-06-14 Write by AlexWang [bug:65072] 进入局部放大状态后禁止调节扫描宽度参数
    parameter(BFPNames::ScanWidthStr)->setEnabled(!value.toBool());
}

void BeamFormerBase::onZoomOnChanged(const QVariant& value)
{
    Q_UNUSED(value);
    imageShapeIntoUnstable();
}

/**
 * @brief onGettingZoomedCQYZMin 要满足下发的探头斜距 < 2048，计算最小放大的CQYZ要满足下列方程
 *
 * @param value
 */
void BeamFormerBase::onGettingZoomedCQYZMin(int& value)
{
    value = MinZoomedCQYZ::minValue(this, curProbe());
}

void BeamFormerBase::onGettingZoomedCQYZMax(int& value)
{
    value = pIV(BFPNames::CQYZStr);
}

void BeamFormerBase::onBeforeSteeringAngleSNChanged(const QVariant& oldValue, QVariant& newValue)
{
    onBeforeSteeringAngleChanged(oldValue, newValue);
}

void BeamFormerBase::onSteeringAngleSNChanged(const QVariant& value)
{
    setPV(BFPNames::SteeringAngleStr, value, true);
}

void BeamFormerBase::onGettingSteeringAngleSNShowValue(QVariant& value)
{
    int iv = pIV(BFPNames::SteeringAngleSNStr);
    if (iv >= 0 && iv < m_StaticParameters->steeringAngles().count())
    {
        value = m_StaticParameters->steeringAngles().at(iv);
    }
}

void BeamFormerBase::onGettingSteeringAngleSNMin(int& value)
{
    onGettingSteeringAngleMin(value);
}

void BeamFormerBase::onGettingSteeringAngleSNMax(int& value)
{
    onGettingSteeringAngleMax(value);
}

void BeamFormerBase::onGettingSteeringAngleSNControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingSteeringAngleControlTableValue(value, controlTableValue);
}

void BeamFormerBase::onBeforeDopSteeringAngleChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    Q_UNUSED(newValue);
}

void BeamFormerBase::onDopSteeringAngleChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onDopSteeringAngleChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value);
    Q_UNUSED(changed);
}

void BeamFormerBase::onGettingDopSteeringAngleShowValue(QVariant& value)
{
    onGettingSteeringAngleShowValue(value);
}

void BeamFormerBase::onGettingDopSteeringAngleMin(int& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onGettingDopSteeringAngleMax(int& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onGettingDopSteeringAngleControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    Q_UNUSED(controlTableValue);
}

void BeamFormerBase::onBeforeSteeringAngleChanged(const QVariant& oldValue, QVariant& newValue)
{
    //凸阵和相控阵忽略控制
    if (!curProbe().IsLinear)
    {
        //确保角度居中没有偏转，在onSteeringAngleChanging中有控制表参数联动控制，
        //在支持任意线密度的FPGA方案中，血流位置会受角度影响
        newValue = pMax(BFPNames::SteeringAngleStr) / 2;
    }
    else
    {
        //频谱打开调节steer,关闭喇叭延时打开,避开中间喇叭过度音
        controlSoundOpened(ISoundController::General);
    }
}

void BeamFormerBase::onSteeringAngleChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onSteeringAngleChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value);
    Q_UNUSED(changed);
}

void BeamFormerBase::onGettingSteeringAngleShowValue(QVariant& value)
{
    int iv = pIV(BFPNames::SteeringAngleStr);
    if (iv >= 0 && iv < m_StaticParameters->steeringAngles().count())
    {
        value = m_StaticParameters->steeringAngles().at(iv);
    }
}

void BeamFormerBase::onGettingSteeringAngleMin(int& value)
{
    // 2014-1-24 FPGA要求限制SteeringAngle的20、-20度的角度
    value = 1;
}

void BeamFormerBase::onGettingSteeringAngleMax(int& value)
{
    // 2014-1-24 FPGA要求限制SteeringAngle的20、-20度的角度
    value = m_StaticParameters->steeringAngleIndexes().count() - 2;
}

void BeamFormerBase::onGettingSteeringAngleControlTableValue(const QVariant& value, int& controlTableValue)
{
    int iv = value.toInt();
    if (iv >= 0 && iv < m_StaticParameters->steeringAngleIndexes().count())
    {
        controlTableValue = m_StaticParameters->steeringAngleIndexes().at(iv);
    }
}

void BeamFormerBase::onGettingSteeringAnglePreset(QVariant& value)
{
    if (m_SteeringAngleBak != -1)
    {
        value = m_SteeringAngleBak;
    }
}

void BeamFormerBase::onBeforeNeedleModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    // 2023-06-17 Write by AlexWang [bug:65095] 开启穿刺增强状时，如果局部放大预备状态/局部放大状态开启就将其关闭
    if (newValue.toBool())
    {
        if (pBV(BFPNames::ZoomOnStr) || pBV(BFPNames::ZoomSelectStr))
        {
            if (m_USFObject != NULL)
            {
                m_USFObject->sendSyncEventTo(UEventType::TOOL_CLOSEZOOMONANDBMODE);
            }
            else
            {
                log()->info(QString("%1 notify subject run RunZoomOnCloseAndEnterBMode failed").arg(PRETTY_FUNCTION));
            }
        }
    }
}

void BeamFormerBase::onNeedleModeChanging(const QVariant& value)
{
    // superNeedle is a
    // autoNeedle is b
    // angle index is c
    // a open   b,c enable ===>(b open, c disable),(b close, c enable)
    // a close   b,c disable
    imageIntoUnstable();
    parameter(BFPNames::NeedleAngleIndexStr)->setEnabled(value.toBool());
    parameter(BFPNames::IsAutoNeedleAngleEnableStr)->setEnabled(value.toBool());
    parameter(BFPNames::IsAutoNeedleAngleEnableStr)->update();

    controlBSteer();
    controlTrapezoidalMode();

    if (value.toBool())
    {
        setPV(BFPNames::BSteeringScanStr, 20); //关闭BSteer,
        setPV(BFPNames::TrapezoidalModeStr, false);
        parameter(BFPNames::NeedleAngleIndexStr)->update();
    }
    else
    {
        //关闭NeedleMode，此时BSteer一定为0，所以需要发送CpdSteer的控制字和数据块
        parameter(BFPNames::CPDSteerStr)->update();
        isSonoNeedleOn() ? parameter(BFPNames::SteeringAngleSNStr)->update()
                         : parameter(BFPNames::SteeringAngleStr)->update();
    }
}

void BeamFormerBase::onNeedleAngleIndexChanging(const QVariant& value)
{
    ControlTableParameter* ctPara = qobject_cast<ControlTableParameter*>(parameter(BFPNames::NeedleAngleIndexStr));
    if (ctPara != NULL && pBV(BFPNames::NeedleModeStr))
    {
        int angle = 0;
        int iv = value.toInt();
        if (iv >= 0 && iv < needleAngles().count())
        {
            angle = needleAngles().at(iv);
        }

        m_NeedleSettingSet->setAllNeedleAngles(needleAngles());

        double steerCos = 1 / qCos(Formula::degree2Rad(angle));
        double steerTan = qTan(Formula::degree2Rad(angle));
        setPV(BFPNames::CCosAngleStr, Util::floatToBinary(steerCos));
        setPV(BFPNames::CTgAngleStr, Util::floatToBinary(steerTan));
        setPV(BFPNames::NeedleSteeringAngleCodingStr, qAbs(angle));

        //下发NeedleAngleIndex块数据
        BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name, QStringList() << BFPNames::CPDSteerStr);

        //获取needlesteer和BSteer/Cpd的偏移量
        int needleOffset = qAbs(angle);
        int bCpdOffset = 0;
        if (isBSteerOn())
        {
            bCpdOffset = qAbs(pIV(BFPNames::BSteeringScanStr) - pMax(BFPNames::BSteeringScanStr) / 2);
        }
        else
        {
            bCpdOffset = pIV(BFPNames::CPDSteerStr);
        }

        // data44~data55,31/31=1K, data58,62/31=2K
        //使用B的偏转角度正常下发
        m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name, BFPNames::CPDSteerStr, bCpdOffset,
                                                  QStringList() << "data44"
                                                                << "data45"
                                                                << "data46"
                                                                << "data47"
                                                                << "data48"
                                                                << "data49"
                                                                << "data50"
                                                                << "data51"
                                                                << "data52"
                                                                << "data53"
                                                                << "data54"
                                                                << "data55"
                                                                << "data58");

        // data56,31/31=1K,每档1K中有8个角度，总共分成31*8=248份,每份128字节
        // needleAngle为正，选择needleOffset档中的第2份和第6份插入到bCpdOffset档中的第3份和第7份中
        // needleAngle为负，选择needleOffset档中的第4份和第8份插入到bCpdOffset档中的第3份和第7份中
        int index = bCpdOffset * 8;
        int inertIndex1 = 0;
        int inertIndex2 = 0;

        if (angle >= 0)
        {
            inertIndex1 = needleOffset * 8 + 1;
            inertIndex2 = needleOffset * 8 + 5;
        }
        else
        {
            inertIndex1 = needleOffset * 8 + 3;
            inertIndex2 = needleOffset * 8 + 7;
        }
        QList<int> indexs = QList<int>() << index << index + 1 << inertIndex1 << index + 3 << index + 4 << index + 5
                                         << inertIndex2 << index + 7;
        m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name, "NeedleSteer56", indexs);

        // data58,data60,62/31=2K,每档2K中有2个角度，总共分成31*2=62份,每份1K字节
        //为了实现分别从58和60中获取数据，将两个合并为一个块数据data60,
        // 58在前，60在后,合并后data60总大小变成124k，总份数变成124份
        // needleAngle为正，从data58中选择needleOffset档中的第2份插入到bCpdOffset档中的第0份
        // needleAngle为负，从data60中选择needleOffset档中的第2份插入到bCpdOffset档中的第0份
        index = 62 + bCpdOffset * 2;
        inertIndex1 = 0;
        if (angle >= 0)
        {
            inertIndex1 = needleOffset * 2 + 1;
        }
        else
        {
            inertIndex1 = 62 + needleOffset * 2 + 1;
        }

        m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name, "NeedleSteer60",
                                                  QList<int>() << inertIndex1 << index + 1);

        sendNeedleParas();
    }

    //    setPV(BFPNames::IsAutoNeedleAngleEnableStr, false);
    //    //根据SRS要求，在自动调节、手动调节角度后关闭自动调节检测功能，暂时屏蔽
}

void BeamFormerBase::onGettingNeedleAngleIndexShowValue(QVariant& value)
{
    int iv = pIV(BFPNames::NeedleAngleIndexStr);
    if (iv >= 0 && iv < needleAngles().count())
    {
        value = needleAngles().at(iv);
    }
}

void BeamFormerBase::onGettingNeedleAngleIndexText(QString& value)
{
    int iv = pIV(BFPNames::NeedleAngleIndexStr);
    if (iv >= 0 && iv < needleAngleStrs().count())
    {
        value = needleAngleStrs().at(iv);
    }
}

void BeamFormerBase::onGettingNeedleAngleIndexMax(int& value)
{
    value = needleAngles().count() - 1;
}

void BeamFormerBase::onGettingNeedleAngleIndexControlTableValue(const QVariant& value, int& controlTableValue)
{
    int iv = value.toInt();
    if (iv >= 0 && iv < needleAngleCTValues().count())
    {
        controlTableValue = needleAngleCTValues().at(iv);
    }
}

void BeamFormerBase::onNeedleSizeChanging(const QVariant& value)
{
    parameter(BFPNames::NeedleAngleIndexStr)->update();
}

void BeamFormerBase::onGettingNeedleSizeText(QString& value)
{
    static const char* needleSizeText[] = {QT_TRANSLATE_NOOP("BeamFormerBase", "Thin"),
                                           QT_TRANSLATE_NOOP("BeamFormerBase", "Thick")};

    value = Util::translate("BeamFormerBase", needleSizeText[pIV(BFPNames::NeedleSizeStr)]);
}

void BeamFormerBase::onGettingNeedleGainMax(int& value)
{
    value = 50;
}

void BeamFormerBase::onGettingNeedleGainStep(int& value)
{
    value = 1;
}

void BeamFormerBase::onBeforeFocusNumBChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (pBV(BFPNames::ScpdOnStr) || pBV(BFPNames::FcpdOnStr))
    {
        if (newValue.toInt() > pMax(BFPNames::FocusNumBStr))
        {
            newValue = oldValue;
        }
    }

    m_FocusNumBChanged = (oldValue != newValue);
    if (m_FocusNumBChanged)
    {
        m_OldFocusPosMM =
            BFFocusParameters().getAverageFocusDepthMM(curProbe(), oldValue.toInt(), pIV(BFPNames::FocusPosBStr));
    }
}

void BeamFormerBase::onGettingFocusNumBIsValidValue(const QVariant& value, bool& valid)
{
    if (pBV(BFPNames::ScpdOnStr) || pBV(BFPNames::FcpdOnStr))
    {
        if (value.toInt() > pMax(BFPNames::FocusNumBStr))
        {
            valid = false;
        }
    }
}

void BeamFormerBase::onFocusNumBChanging(const QVariant& value)
{
    if (m_FocusNumBChanged)
    {
        setPV(BFPNames::FocusPosBStr,
              BFFocusParameters().getFocusPosByDepthMM(curProbe(), m_OldFocusPosMM, value.toInt()));
        // Bug:18591 由于现在档位值可调,计算出来的档位值可能不合理,需要调整回合理档位.
        adjustFocusPosByDepth(BFPNames::FocusNumBStr, BFPNames::FocusPosBStr);
        m_FocusNumBChanged = false;
    }
}

void BeamFormerBase::onFocusNumBChanged(const QVariant& value)
{
    if (pIV(BFPNames::SyncModeStr) == Sync_None || pIV(BFPNames::SystemScanModeStr) == SystemScanModeAV)
    {
        setPV(BFPNames::FocusNumShowStr, value);
    }

    emit updateMitiValue();
}

void BeamFormerBase::onGettingFocusNumBShowValue(QVariant& value)
{
    value = value.toInt() + 1;
}

void BeamFormerBase::onGettingFocusNumBMax(int& value)
{
    if (pBV(BFPNames::ScpdOnStr) || pBV(BFPNames::FcpdOnStr) || pBV(BFPNames::TrapezoidalModeStr))
    {
        value = 1;
    }
    else
    {
        ControlTableParameter* p = qobject_cast<ControlTableParameter*>(sender());
        if (p != NULL)
        {
            int min, max, step;
            p->calcMinMaxStepByBitInfos(min, max, step);
            value = max;
        }
        else
        {
            value = 3;
        }

        value = qMin(value, BFFocusParameters().focusNumMax(curProbe()));
    }
}

void BeamFormerBase::onGettingFocusNumBPreset(QVariant& value)
{
    if (m_FocusNumBBak != -1)
    {
        value = m_FocusNumBBak;
    }
}

void BeamFormerBase::onTxWeightDebugEnableChanging(const QVariant& value)
{
}

void BeamFormerBase::onTxWeightValueChanging(const QVariant& value)
{
}

void BeamFormerBase::onTxWeightPosChanging(const QVariant& value)
{
}

void BeamFormerBase::onBeforeFocusPosBChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    newValue = BFFocusParameters().adviseFocusPos(curProbe(), pIV(BFPNames::FocusNumBStr), newValue.toInt(),
                                                  imageBottomDepthMM());
}

void BeamFormerBase::onGettingFocusPosBIsValidValue(const QVariant& value, bool& valid)
{
    QVariant oldValue = value;
    QVariant newValue = value;
    onBeforeFocusPosBChanged(value, newValue);
    valid = (newValue == oldValue);
}

void BeamFormerBase::onFocusPosBChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onFocusPosBChanged(const QVariant& value)
{
    Q_UNUSED(value);
    emit updateMitiValue();
}

void BeamFormerBase::onGettingFocusPosBText(QString& value)
{
    value = QString();
}

void BeamFormerBase::onGettingFocusPosBMax(int& value)
{
    value = BFFocusParameters().focusCompositesCount(curProbe(), pIV(BFPNames::FocusNumBStr)) - 1;
    //菜单上焦点的最大可调范围固定，但实际可调范围受深度限制，菜单调节时体验不好，无法调节到最大
    //在获取焦点最大值的接口中，增加实际可调范围的计算逻辑
    value = BFFocusParameters().adviseFocusPos(curProbe(), pIV(BFPNames::FocusNumBStr), value, imageBottomDepthMM());
}

void BeamFormerBase::onFocusNumMChanged(const QVariant& value)
{
    if (pIV(BFPNames::SyncModeStr) == Sync_M)
    {
        setPV(BFPNames::FocusNumShowStr, value);
    }
}

void BeamFormerBase::onBeforeFocusPosMChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    newValue = BFFocusParameters().adviseFocusPos(curProbe(), pIV(BFPNames::FocusNumMStr), newValue.toInt(),
                                                  imageBottomDepthMM());
}

void BeamFormerBase::onGettingFocusPosMIsValidValue(const QVariant& value, bool& valid)
{
    QVariant oldValue = value;
    QVariant newValue = value;
    onBeforeFocusPosMChanged(value, newValue);
    valid = (newValue == oldValue);
}

void BeamFormerBase::onFocusPosMChanged(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onGettingFocusPosMMax(int& value)
{
    value = BFFocusParameters().focusCompositesCount(curProbe(), pIV(BFPNames::FocusNumMStr)) - 1;
}

void BeamFormerBase::onHalfHeightChanging(const QVariant& value)
{
    m_PostProcessHandler->onHalfHeightChanging(value);
}

void BeamFormerBase::onGettingHalfHeightControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (imageMode() == MODE_4B)
    {
        controlTableValue = false;
    }
}

void BeamFormerBase::onImageModeChanging(const QVariant& value)
{
    ImageModeType imageMode = (ImageModeType)(value.toInt());

    // 2025-05-06 Write by AlexWang [BUG:80330] 4B进入放大时，图像模式会变成MODE_ZOOM，但此时实际的布局为4B
    if (imageMode == MODE_4B || imageMode == MODE_UDBM || pIV(BFPNames::LayoutStr) == Layout_2x2)
    {
        setPV(BFPNames::HalfHeightStr, true);
    }
    else
    {
        setPV(BFPNames::HalfHeightStr, false);
    }

    if (imageMode != MODE_LCRB && imageMode != MODE_LBRC)
    {
        setPV(BFPNames::BCImagesOnStr, false);
    }
    //导致单B 2B切换 Imagezoomcoef重置
    //    controlImageZoomCoef();
}

void BeamFormerBase::onDImageSizeChanging(const QVariant& value)
{
    // TODO 未来能不能提供一个通用的接口来实现根据不同模式来取到需要的某个参数变量名
    Q_UNUSED(value);
    if (pBV(BFPNames::CWEnStr) && !isHPrfCW())
    {
        parameter(BFPNames::BaseLineCWDStr)->update();
    }
    else if (pBV(BFPNames::TDIEnStr))
    {
        parameter(BFPNames::BaseLineDTDIStr)->update();
    }
    else
    {
        parameter(BFPNames::BaseLineStr)->update();
    }
}
void BeamFormerBase::onImageModeChanged(const QVariant& value)
{
    Q_UNUSED(value);
    emit updateMitiValue();
}

void BeamFormerBase::onGettingImageModeControlTableValue(const QVariant& value, int& controlTableValue)
{
    int intValue = value.toInt();
    ImageModeType imageMode = (ImageModeType)intValue;

    if (imageMode == MODE_2B || imageMode == MODE_4B)
    {
        //根据曹三要求，多b下的imagemode要下发为单b
        intValue = MODE_B;
        intValue |= pIV(BFPNames::ActiveBStr);
    }

    controlTableValue = intValue;
}

void BeamFormerBase::onSyncModeChanging(const QVariant& value)
{
    SyncModeType syncMode = (SyncModeType)value.toInt();

    int scanMode = pIV(BFPNames::SystemScanModeStr);

    controlHighDensity();

    parameter(BFPNames::NeedleModeStr)->setEnabled(syncMode == Sync_None && curProbe().IsLinear);
    if (syncMode != Sync_None)
    {
        setPV(BFPNames::NeedleModeStr, false);
    }

    if (syncMode == Sync_M)
    {
        setPV(BFPNames::FocusNumMStr, 0);
        qreal focusPosMM = BFFocusParameters().getAverageFocusDepthMM(curProbe(), pIV(BFPNames::FocusNumBStr),
                                                                      pIV(BFPNames::FocusPosBStr));
        int focusPos = BFFocusParameters().getFocusPosByDepthMM(curProbe(), focusPosMM, 0);
        //进入M模式，M焦点位置=B焦点平均位置
        setPDV(BFPNames::FocusPosMStr, focusPos);
        parameter(BFPNames::FocusNumMStr)->update();
        parameter(BFPNames::FocusPosMStr)->update();
        AutoFocusAdjuster::instance().updateMCoefficient(m_SonoParameters);
    }
    else if (syncMode == Sync_None)
    {
        parameter(BFPNames::FocusNumBStr)->update();
        parameter(BFPNames::FocusPosBStr)->update();
    }
    else
    {
        if (pIV(BFPNames::FocusNumBStr) == 0)
        {
            parameter(BFPNames::FocusNumBStr)->update();
            parameter(BFPNames::FocusPosBStr)->update();
        }
        else
        {
            //其他模式设置焦点数为1个
            m_ControlTable->send(qobject_cast<ControlTableParameter*>(parameter(BFPNames::FocusNumBStr)), 0, true,
                                 false);
            // FocusPosBStr的位置自动找平均深度对应的焦点位置
            qreal focusPosMM = BFFocusParameters().getAverageFocusDepthMM(curProbe(), pIV(BFPNames::FocusNumBStr),
                                                                          pIV(BFPNames::FocusPosBStr));
            int focusPos = BFFocusParameters().getFocusPosByDepthMM(curProbe(), focusPosMM, 0);
            m_ControlTable->send(qobject_cast<ControlTableParameter*>(parameter(BFPNames::FocusPosBStr)), focusPos,
                                 true, false);
        }
    }

    parameter(BFPNames::FocusNumShowStr)->setEnabled(syncMode == Sync_None);
    if (syncMode == Sync_None)
    {
        setPV(BFPNames::FocusNumShowStr, pV(BFPNames::FocusNumBStr));
    }
    else if (syncMode == Sync_M)
    {
        setPV(BFPNames::FocusNumShowStr, pV(BFPNames::FocusNumMStr));
    }
    else
    {
        setPV(BFPNames::FocusNumShowStr, pV(BFPNames::FocusNumCStr));
    }

    if (syncMode == Sync_None)
    {
        setPV(BFPNames::AcousticPowerTestCodeStr, 0);
    }
    else
    {
    }

    setPV(BFPNames::FreqSpectrumStr, false);
    setPV(BFPNames::TriplexModeStr, false);

    switch (syncMode)
    {
    case Sync_C:
        setPV(BFPNames::FocusPosCStr, 0);
        break;
    case Sync_CD:
        updateDMaxVel();
        parameter(BFPNames::PW_DummyCountStr)->update();
        break;
    case Sync_D:
        updateDMaxVel();
        parameter(BFPNames::PW_DummyCountStr)->update();
        break;
    case Sync_M:
        break;
    case Sync_CM:
        break;
    default:
        break;
    }

    // modify FrameScapeTime, set CFrameScapeTime on c mode, otherwise set BFrameScapeTime
    if (syncMode == Sync_C)
    {
        setPV(BFPNames::FrameScapeTimeShowStr, pIV(BFPNames::CFrameScapeTimeStr));
        parameter(BFPNames::CFrameScapeTimeStr)->update();
    }
    else
    {
        setPV(BFPNames::FrameScapeTimeShowStr, pIV(BFPNames::FrameScapeTimeStr));
        parameter(BFPNames::FrameScapeTimeStr)->update();
    }

    if (scanMode == SystemScanModeAV)
    {
        updateDMaxVel();
    }
    controlCFMVelLevel();
    controlAcousticPower();
    controlWaveVelocity();
    controlAccCount();
    controlTGCCTValue();

    if (pIV(BFPNames::SraGainColorDeltaStr) != 0)
    {
        parameter(BFPNames::GainStr)->update();
    }

    FreqRelatedParasSender fpSender(m_RelatedParasController);
    controlTrapezoidalMode();
    controlScpdOn();
    controlFcpdOn();
    controlECGPos();
    controlECGDly();
    controlEdge();
    if (syncMode == Sync_D)
    {
        setPV(BFPNames::BCRelativePosInDModeStr, pIV(BFPNames::BCRelativePosInDModeStr));
    }
}

void BeamFormerBase::onSyncModeChanged(const QVariant& value)
{
    Q_UNUSED(value);
    emit updateMitiValue();
}

void BeamFormerBase::onBeforeHighDensityChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    Q_UNUSED(newValue);
}

void BeamFormerBase::onHighDensityChanging(const QVariant& value)
{
    Q_UNUSED(value);
    changeProbeInterval();
    sendCQYZBlockDataGroup();
}

void BeamFormerBase::onHighDensityChanged(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onGettingHighDensityText(QString& value)
{
    value = pBV(BFPNames::HighDensityStr) ? QString("High") : QString("Low");
}

void BeamFormerBase::onGettingHighDensityControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onGettingHighDensityPreset(QVariant& value)
{
    if (m_HighDensityBak != -1)
    {
        value = (m_HighDensityBak == 1 ? true : false);
    }
}

void BeamFormerBase::setStartSupportFreezeOutage(bool value)
{
}

void BeamFormerBase::onGettingPWRSyncEnableControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = value.toBool() ? 1 : 0;
}

void BeamFormerBase::onGettingMBSyncEnableControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = value.toBool() ? 1 : 0;
}

void BeamFormerBase::onPWRSync1ShowValueChanging(const QVariant& value)
{
    setPV(BFPNames::PWRSync1Str, pIV(BFPNames::ADFreqMHzStr) * 1000 / value.toInt());
}

void BeamFormerBase::onPWRSync2ShowValueChanging(const QVariant& value)
{
    setPV(BFPNames::PWRSync2Str, pIV(BFPNames::ADFreqMHzStr) * 1000 / value.toInt());
}

void BeamFormerBase::onMBSync1ShowValueChanging(const QVariant& value)
{
    setPV(BFPNames::MBSync1Str, pIV(BFPNames::ADFreqMHzStr) * 1000 / value.toInt());
}

void BeamFormerBase::onMBSync2ShowValueChanging(const QVariant& value)
{
    setPV(BFPNames::MBSync2Str, (int)pDV(BFPNames::ADFreqMHzStr) / value.toDouble());
}

void BeamFormerBase::onMBSync3ShowValueChanging(const QVariant& value)
{
    setPV(BFPNames::MBSync3Str, (int)pDV(BFPNames::ADFreqMHzStr) / value.toDouble());
}

void BeamFormerBase::onBeforeCQYZChanged(const QVariant& oldValue, QVariant& newValue)
{
    Parameter* para = parameter(BFPNames::CQYZStr);
    if (para != NULL)
    {
        int max = para->max();
        int min = para->min();
        int newValueInt = newValue.toInt();
        if (newValueInt > max)
        {
            newValue = max;
        }
        else if (newValueInt < min)
        {
            newValue = min;
        }
    }
}

void BeamFormerBase::onCQYZChanging(const QVariant& value)
{
    m_PostProcessHandler->onCQYZChanging(value);

    changeProbeGeometry();
    if (m_SonoParameters->isRealTime())
    {
        sendCQYZBlockDataGroup();
        sendMDFData();
    }
}

void BeamFormerBase::onCQYZChanged(const QVariant& value, bool bChange)
{
    Q_UNUSED(value);
    Q_UNUSED(bChange);
    parameter(BFPNames::DepthShowMMStr)->setValue(imageBottomDepthMM());
    imageShapeIntoUnstable(70);
}

void BeamFormerBase::onIsSupportIntegerDepthChanged(const QVariant& value)
{
    resetOriginalDepthMMList();
    if (value.toBool())
    {
        QString valueList = pV(BFPNames::DepthCMListStr).toString();
        QStringList depthCMList;
        if (!valueList.isNull() && !valueList.isEmpty() && valueList != "0")
        {
            depthCMList = valueList.split(",");
        }
        if (depthCMList.count() > 0)
        {
            double depthShowCM = QString("%1").arg(imageBottomDepthMM() / 10.0, 0, 'f', 1).toDouble();
            int i = CalculatorUtil::findSmallerNearestIndex(depthCMList, depthShowCM);
            if (i != -1)
            {
                setPV(BFPNames::CQYZLevelStr, i, true);
                return;
            }
        }
        parameter(BFPNames::CQYZLevelStr)->update();
    }
    else
    {
        setPV(BFPNames::RenderImageZoomCofStr, 1.0f);
        int cqyzValue = pIV(BFPNames::CQYZStr);
        if (!parameter(BFPNames::CQYZStr)->checkValueIsValid(cqyzValue))
        {
            int cqyzValueMax = pMax(BFPNames::CQYZStr);
            if (cqyzValue + 1 > cqyzValueMax)
            {
                setPV(BFPNames::CQYZStr, cqyzValueMax, true);
            }
            else
            {
                setPV(BFPNames::CQYZStr, cqyzValue + 1, true);
            }
        }
        else
        {
            parameter(BFPNames::CQYZStr)->update();
        }
    }
}

void BeamFormerBase::onGettingCQYZText(QString& value)
{
    value = QString("%1 cm").arg(imageBottomDepthMM() / 10.0, 0, 'f', 1);
}

void BeamFormerBase::onGettingCQYZMin(int& value)
{
    value = curProbe().MinCQYZ;
}

void BeamFormerBase::onGettingCQYZMax(int& value)
{
    value = curProbe().MaxCQYZ;
}

void BeamFormerBase::onGettingCQYZIsValidValue(const QVariant& value, bool& valid)
{
    if (value.toInt() > pMax(BFPNames::CQYZStr) || value.toInt() < pMin(BFPNames::CQYZStr))
    {
        valid = false;
    }
    valid = true;
}

void BeamFormerBase::onGettingCQYZControlTableValue(const QVariant& value, int& controlTableValue)
{
    //此时sonoparameters中的cqyz还没变，不能使用 m_DepthParameters，只能使用临时变量
    //    BFDepthParameters depthPara(value.toInt(), pBV(BFPNames::HalfHeightStr),
    //                                zoomOn(),
    //                                BFDepthParameters::zoomMulti(
    //                                pIV(BFPNames::ZoomMultiIndexStr),
    //                                parameter(BFPNames::ZoomMultiIndexStr)->max()));

    BFDepthParameters depthPara(pIV(BFPNames::ADFreqMHzStr), imageHeight(), value.toInt(), pBV(BFPNames::HalfHeightStr),
                                zoomOn(), pIV(BFPNames::ZoomedCQYZStr));
    depthPara.setPixelLen(BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) /
                          pFV(BFPNames::FixedSWImageZoomCofStr));

    controlTableValue = depthPara.getRealCQYZ();

    if (ModelConfig::instance().value(ModelConfig::PixelLenStep, 1).toInt() == 4)
    {
        if (pBV(BFPNames::CWEnStr) && pBV(BFPNames::FreqSpectrumStr))
        {
            controlTableValue = 320;
        }
    }
}

void BeamFormerBase::onGettingCqyzOfDControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = value.toInt() - 1;
}

void BeamFormerBase::onMVelocityChanging(const QVariant& value)
{
    setPV(BFPNames::MPixelSizeSecStr, m_StaticParameters->mPixelSizeSeces()[value.toInt()]);
}

void BeamFormerBase::onGettingMVelocityShowValue(QVariant& value)
{
    value = value.toInt() + 1;
}

void BeamFormerBase::onFreeMVelocityChanging(const QVariant& value)
{
    setECGDly(BFPNames::FreeMECGDlyStr, BFPNames::FreeMECGDlyDeltasStr, value);
}

void BeamFormerBase::onGettingFreeMVelocityShowValue(QVariant& value)
{
    value = value.toInt() + 1;
}

void BeamFormerBase::onDVelocityChanging(const QVariant& value)
{
    setECGDly(BFPNames::DECGDlyStr, BFPNames::DECGDlyDeltasStr, value);
}

void BeamFormerBase::onGettingDVelocityShowValue(QVariant& value)
{
    value = value.toInt() + 1;
}

void BeamFormerBase::onDVelocityTDIChanging(const QVariant& value)
{
    setECGDly(BFPNames::DTDIECGDlyStr, BFPNames::DTDIECGDlyDeltasStr, value);
}

void BeamFormerBase::onGettingDVelocityTDIShowValue(QVariant& value)
{
    value = value.toInt() + 1;
}

void BeamFormerBase::onCWDVelocityChanging(const QVariant& value)
{
    setECGDly(BFPNames::CWECGDlyStr, BFPNames::CWDECGDlyDeltasStr, value);
}

void BeamFormerBase::onGettingCWDVelocityShowValue(QVariant& value)
{
    value = value.toInt() + 1;
}

void BeamFormerBase::onColorImageModeChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlColorImageModeParas();
    controlCTMPDTDIParas();
    controlColorFreqIndex();
    controlColorInvert();

    parameter(BFPNames::CPriorityStr)->update();
}

void BeamFormerBase::onFreqSpectrumChanging(const QVariant& value)
{
    if (value.toBool())
    {
        // FreqSpectrum 设置为true之前，先设置ImgFrzStr 为true
        ControlTableParameter* ctPara = qobject_cast<ControlTableParameter*>(parameter(BFPNames::ImgFrzStr));
        if (ctPara != NULL)
        {
            ControlTableForcibleSender sender(m_ControlTable);
            ControlTableSyncSender syncSender(m_ControlTable);
            ctPara->setValue((const QVariant&)true);
            Util::usleep(50000);
        }
        setPV(BFPNames::TriplexModeStr, false);
    }
    else
    {
        setPV(BFPNames::ImgFrzStr, false);
    }
    controlAcousticPower();
    controlAccCount();
    controlPWTMParas();
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_BlockDataParaNameConverter->groupParaName(BFPNames::FreqSpectrumStr),
                                              value.toBool() ? 1 : 0);

    if (pBV(BFPNames::CWEnStr))
    {
        controlSoundOpened(ISoundController::CW);
        if (ModelConfig::instance().value(ModelConfig::PixelLenStep, 1).toInt() == 4)
        {
            updateCQYZ();
        }
    }
    else
    {
        controlSoundOpened(ISoundController::General);
    }

    //限制BCD模式下切换到C菜单的时候调节QBeam和QFlow
    parameter(BFPNames::QBeamOnStr)->setEnabled(!value.toBool());
    parameter(BFPNames::QFlowOnStr)->setEnabled(!value.toBool());

    // 2023-09-06 Modify by AlexWang [bug:66599] 解决在局部放大状态下，执行冻结解冻后导致扫描宽度按钮置为可用
    if (!pBV(BFPNames::ZoomOnStr))
        parameter(BFPNames::ScanWidthStr)->setEnabled(!value.toBool());
    parameter(BFPNames::LeftStr)->setEnabled(!value.toBool());
    //添加键盘左右翻转在不同模式下的支持情况
    m_StateManager->setSupportLR(!value.toBool());

    controlRotationEn();
    controlBSteer();
}

void BeamFormerBase::onFreqSpectrumChanged(const QVariant& value)
{
    imageShapeIntoUnstable();
    if (isLGCAvailable())
    {
        parameter(BFPNames::LGCControlEnStr)->setEnabled(!value.toBool());
    }
}

void BeamFormerBase::onGettingLowVelocityBloodText(QString& value)
{
    value = pBV(BFPNames::LowVelocityBloodStr) ? QString("Low") : QString("High");
}

// baseline 0~6 对应的在D图像区的Y坐标，把D图像Y轴分隔成8个区间
//下发的baseline是反的
//(0,0)---------------------------------
//
//--------------------------------------6
//
//--------------------------------------5
//
//--------------------------------------4
//
//--------------------------------------3
//
//--------------------------------------2
//
//--------------------------------------1
//
//--------------------------------------0
//
//---------------------------------------(640, 256)
void BeamFormerBase::onBaseLineChanging(const QVariant& value)
{
    m_PostProcessHandler->onBaseLineChanging(value);
}

void BeamFormerBase::onBaseLineChanged(const QVariant& value)
{
    Q_UNUSED(value);
    imageShapeIntoUnstable();
}

void BeamFormerBase::onGettingBaseLineShowValue(QVariant& value)
{
    m_PostProcessHandler->onGettingBaseLineShowValue(value);
}

void BeamFormerBase::onGettingBaseLineControlTableValue(const QVariant& value, int& controlTableValue)
{
    m_PostProcessHandler->onGettingBaseLineControlTableValue(value, controlTableValue);
}

void BeamFormerBase::onBaseLineDTDIChanging(const QVariant& value)
{
    onBaseLineChanging(value);
}

void BeamFormerBase::onBaseLineDTDIChanged(const QVariant& value)
{
    onBaseLineChanged(value);
}

void BeamFormerBase::onGettingBaseLineDTDIShowValue(QVariant& value)
{
    onGettingBaseLineShowValue(value);
}

void BeamFormerBase::onGettingBaseLineDTDIControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingBaseLineControlTableValue(value, controlTableValue);
}

void BeamFormerBase::onBaseLineCWDChanging(const QVariant& value)
{
    onBaseLineChanging(value);
}

void BeamFormerBase::onBaseLineCWDChanged(const QVariant& value)
{
    onBaseLineChanged(value);
}

void BeamFormerBase::onGettingBaseLineCWDShowValue(QVariant& value)
{
    onGettingBaseLineShowValue(value);
}

void BeamFormerBase::onGettingBaseLineCWDControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingBaseLineControlTableValue(value, controlTableValue);
}

void BeamFormerBase::onOverlappingMBChanged(const QVariant& value)
{
}

void BeamFormerBase::onStripRemoveEnableChanged(const QVariant& value)
{
}

void BeamFormerBase::onMBChanging(const QVariant& value)
{
}

void BeamFormerBase::onMBChanged(const QVariant& value)
{
}

void BeamFormerBase::onDynamicRangeChanging(const QVariant& value)
{
    Q_UNUSED(value);
    DynamicParasSender drSender(m_RelatedParasController);
}

void BeamFormerBase::onGettingDynamicRangeShowValue(QVariant& value)
{
    m_PostProcessHandler->onGettingDynamicRangeShowValue(value);
}

void BeamFormerBase::onGettingDynamicRangeControlTableValue(const QVariant& value, int& controlTableValue)
{
    m_PostProcessHandler->onGettingDynamicRangeControlTableValue(value, controlTableValue);
}

void BeamFormerBase::onGettingMDynamicRangeShowValue(QVariant& value)
{
    m_PostProcessHandler->onGettingMDynamicRangeShowValue(value);
}

void BeamFormerBase::onGettingNotReduceGainText(QString& value)
{
    value = pBV(BFPNames::NotReduceGainStr) ? QString("On") : QString("Off");
}

void BeamFormerBase::onGettingFrameAvgControlTableValue(const QVariant& value, int& controlTableValue)
{
    //这里必须和2个Offset经过计算，否则 在DynamicParasSender中修改的控制表中的值，又会恢复成原始值
    controlTableValue = m_RelatedParasController->frameAvgWithOffset(value.toInt());
}

void BeamFormerBase::onEdgeChanging(const QVariant& value)
{
    setPV(BFPNames::LeeEnStr, value.toInt() > 0 ? 1 : 0);
    m_RelatedParasController->sendEdgeParas(value.toInt());

    if (!pBV(BFPNames::THIStr))
    {
        setPV(BFPNames::EdgeShowStr, value);
    }
}

void BeamFormerBase::onEdgeIncChanging(const QVariant& value)
{
    setPV(BFPNames::LeeEnStr, value.toInt() > 0 ? 1 : 0);
    m_RelatedParasController->sendEdgeParas(value.toInt());

    if (!pBV(BFPNames::THIStr) && (syncMode() & Sync_C) == Sync_C)
    {
        setPV(BFPNames::EdgeShowStr, value);
    }
}

void BeamFormerBase::onGettingEdgeControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onEdgeThiChanging(const QVariant& value)
{
    setPV(BFPNames::LeeEnStr, value.toInt() > 0 ? 1 : 0);
    m_RelatedParasController->sendEdgeParas(value.toInt());

    if (pBV(BFPNames::THIStr))
    {
        setPV(BFPNames::EdgeShowStr, value);
    }
}

void BeamFormerBase::onEdgeThiIncChanging(const QVariant& value)
{
    setPV(BFPNames::LeeEnStr, value.toInt() > 0 ? 1 : 0);
    m_RelatedParasController->sendEdgeParas(value.toInt());

    if (pBV(BFPNames::THIStr) && (syncMode() & Sync_C) == Sync_C)
    {
        setPV(BFPNames::EdgeShowStr, value);
    }
}

void BeamFormerBase::onEdgeShowChanging(const QVariant& value)
{
    // TODO:如果使用parameter(BFPNames::EdgeShowStr)->update(); 必须强制刷新要控制的参数
    // C下独立控制B的edge参数
    if ((syncMode() & Sync_C) == Sync_C)
    {
        setPV(pBV(BFPNames::THIStr) ? BFPNames::EdgeThiIncStr : BFPNames::EdgeIncStr, value, true);
    }
    else
    {
        setPV(pBV(BFPNames::THIStr) ? BFPNames::EdgeThiStr : BFPNames::EdgeStr, value, true);
    }
}

void BeamFormerBase::onSpectralInvertChanging(const QVariant& value)
{
    Q_UNUSED(value);
    ControlTableForcibleSender sender(m_ControlTable);
    m_SonoParameters->setPV(BFPNames::SpecFrhStr, 0);
    m_SonoParameters->setPV(BFPNames::SpecFrhStr, 1);
}

void BeamFormerBase::onSpectralInvertChanged(const QVariant& value)
{
    Q_UNUSED(value);
    imageShapeIntoUnstable();
}

void BeamFormerBase::onGettingSmoothText(QString& value)
{
    if (pIV(BFPNames::SmoothStr) == 0)
    {
        value = "Off";
    }
    else
    {
        value = QString::number(pIV(BFPNames::SmoothStr));
    }
}

void BeamFormerBase::onDopplerStartDepthMMChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onDopplerTDIStartDepthMMChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onSampleRateDopChanging(const QVariant& value)
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::SampleRateDopShowStr, value);
    }
}

void BeamFormerBase::onGettingSampleRateDopShowValue(QVariant& value)
{
    value = pMax(BFPNames::SampleRateDopStr) - (value.toInt() - pMin(BFPNames::SampleRateDopStr));
}

void BeamFormerBase::onBeforeDopOutGainChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
}

void BeamFormerBase::onGettingSampleRateDopTDIShowValue(QVariant& value)
{
    value = pMax(BFPNames::SampleRateDopTDIStr) - (value.toInt() - pMin(BFPNames::SampleRateDopTDIStr));
}

void BeamFormerBase::onGettingSampleRateDopSNShowValue(QVariant& value)
{
    value = pMax(BFPNames::SampleRateDopSNStr) - (value.toInt() - pMin(BFPNames::SampleRateDopSNStr));
}

void BeamFormerBase::onGettingSampleRateDopMVIShowValue(QVariant& value)
{
    value = pMax(BFPNames::SampleRateDopTDIStr) - (value.toInt() - pMin(BFPNames::SampleRateDopTDIStr));
}

void BeamFormerBase::onSampleRateDopTMChanging(const QVariant& value)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::SampleRateDopShowStr, value);
    }
}

void BeamFormerBase::onGettingSampleRateDopTMShowValue(QVariant& value)
{
    value = pMax(BFPNames::SampleRateDopTMStr) - (value.toInt() - pMin(BFPNames::SampleRateDopTMStr));
}

void BeamFormerBase::onSampleRateDopShowChanging(const QVariant& value)
{
    setPV(pBV(BFPNames::TriplexModeStr) ? BFPNames::SampleRateDopTMStr : BFPNames::SampleRateDopStr, value);
}

void BeamFormerBase::onGettingSampleRateDopShowShowValue(QVariant& value)
{
    value = pMax(BFPNames::SampleRateDopShowStr) - (value.toInt() - pMin(BFPNames::SampleRateDopShowStr));
}

void BeamFormerBase::onGettingSampleRateDopElastoShowValue(QVariant& value)
{
    value = pMax(BFPNames::SampleRateDopElastoStr) - (value.toInt() - pMin(BFPNames::SampleRateDopElastoStr));
}

void BeamFormerBase::onDSampleRateDopChanging(const QVariant& value)
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::DSampleRateDopShowStr, value, true);
    }
}

void BeamFormerBase::onGettingDSampleRateDopShowValue(QVariant& value)
{
    value = pMax(BFPNames::DSampleRateDopStr) - (value.toInt() - pMin(BFPNames::DSampleRateDopStr));
}

void BeamFormerBase::onGettingDSampleRateDopTDIShowValue(QVariant& value)
{
    value = pMax(BFPNames::DSampleRateDopTDIStr) - (value.toInt() - pMin(BFPNames::DSampleRateDopTDIStr));
}

void BeamFormerBase::onDSampleRateDopTMChanging(const QVariant& value)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::DSampleRateDopShowStr, value);
    }
}

void BeamFormerBase::onGettingDSampleRateDopTMShowValue(QVariant& value)
{
    value = pMax(BFPNames::DSampleRateDopTMStr) - (value.toInt() - pMin(BFPNames::DSampleRateDopTMStr));
}

void BeamFormerBase::onDSampleRateDopShowChanging(const QVariant& value)
{
    setPV(pBV(BFPNames::TriplexModeStr) ? BFPNames::DSampleRateDopTMStr : BFPNames::DSampleRateDopStr, value);
}

void BeamFormerBase::onGettingDSampleRateDopShowShowValue(QVariant& value)
{
    value = pMax(BFPNames::DSampleRateDopShowStr) - (value.toInt() - pMin(BFPNames::DSampleRateDopShowStr));
}

void BeamFormerBase::onCWDSampleRateChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onGettingCWDSampleRateShowValue(QVariant& value)
{
    value = pMax(BFPNames::CWDSampleRateStr) - (value.toInt() - pMin(BFPNames::CWDSampleRateStr));
}

void BeamFormerBase::onSampleVolumeChanging(const QVariant& value)
{
    int iv = value.toInt();
    if (iv >= 0 && iv < m_StaticParameters->dopplerVolMMs().count())
    {
        setPV(!pBV(BFPNames::TDIEnStr) ? BFPNames::SampleVolumeMMStr : BFPNames::SampleVolumeTDIMMStr,
              m_StaticParameters->dopplerVolMMs().at(iv));
    }
}

void BeamFormerBase::onSampleVolumeMMChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onGettingSampleVolumeMMText(QString& value)
{
    value = QString("%1 mm").arg(pFV(BFPNames::SampleVolumeMMStr), 0, 'f', 1);
}

void BeamFormerBase::onSampleVolumeTDIChanging(const QVariant& value)
{
    int iv = value.toInt();
    if (iv >= 0 && iv < m_StaticParameters->dopplerVolMMs().count())
    {
        setPV(BFPNames::SampleVolumeTDIMMStr, m_StaticParameters->dopplerVolMMs().at(iv));
    }
}

void BeamFormerBase::onGettingSampleVolumeTDIMMText(QString& value)
{
    value = QString("%1 mm").arg(pFV(BFPNames::SampleVolumeTDIMMStr), 0, 'f', 1);
}

void BeamFormerBase::onPWDepthDeltaChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onUpChanging(const QVariant& value)
{
    Q_UNUSED(value);
    imageIntoUnstable();
}

void BeamFormerBase::onLeftChanging(const QVariant& value)
{
    Q_UNUSED(value);
    imageIntoUnstable();
}

void BeamFormerBase::onBeforeScpdChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    Q_UNUSED(newValue);
}

void BeamFormerBase::onScpdChanging(const QVariant& value)
{
    FreqRelatedParasSender fpSender(m_RelatedParasController);
    setPV(BFPNames::SCPDIDStr, value);
    setPV(BFPNames::ScpdOnStr, value.toInt() > 0);
}

void BeamFormerBase::onGettingScpdText(QString& value)
{
    int val = pIV(BFPNames::ScpdStr);
    if (val == 0)
    {
        value = QString("Off");
    }
}

void BeamFormerBase::onGettingScpdControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onGettingScpdPreset(QVariant& value)
{
    if (m_ScpdBak != -1)
    {
        value = m_ScpdBak;
    }
}

void BeamFormerBase::onSRAIDChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onGettingSRAIDText(QString& value)
{
    int val = pIV(BFPNames::SRAIDStr);
    if (val == 0)
    {
        value = QString("Off");
    }
}

void BeamFormerBase::onGettingSCPDIDControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

/**
 * @brief onScpdTrapeChanging 此参数专门记录梯形成像时scpd的值，1表示梯形成像，0表示非梯形成像
 * 由于Scpd和TrapezoidalMode都是由同一位置的控制表参数控制，所以必须分开存储，互相控制，由于开了
 * 梯形成像，ScpdOn 的Enabled为False，所以ScpdTrape能够控制ScpdOn,但Scpd参数无法控制TrapezoidalMode
 *
 * @param value
 */
void BeamFormerBase::onScpdTrapeChanging(const QVariant& value)
{
    if (isTrapezoidalModeEnabled())
    {
        setPV(BFPNames::TrapezoidalModeStr, value.toInt() == 1);
    }

    if (!isScpdEnabled())
    {
        setPV(BFPNames::ScpdOnStr, false);
    }
}

void BeamFormerBase::onScpdOtherChanging(const QVariant& value)
{
    Q_UNUSED(value);
    FreqRelatedParasSender fpSender(m_RelatedParasController);

    if (!isScpdEnabled())
    {
        setPV(BFPNames::ScpdOnStr, false);
    }
    if (!isTrapezoidalModeEnabled())
    {
        setPV(BFPNames::TrapezoidalModeStr, false);
    }
}

void BeamFormerBase::onAcousticPowerBChanging(const QVariant& value)
{
    if (!pBV(BFPNames::THIStr))
    {
        setPV(BFPNames::AcousticPowerBShowStr, value);
        controlCompensationCoef();
        // I9 才控制
        // setPV(BFPNames::TransmitPowerPositiveVolStr, value);
        // setPV(BFPNames::TransmitPowerNegativeVolStr, value);
        emit updateMitiValue();
    }
}

void BeamFormerBase::onAcousticPowerBChanged(const QVariant& value)
{
    Q_UNUSED(value);
    m_APPressCalculator->updateOtherParas();
}

void BeamFormerBase::onGettingAcousticPowerBMax(int& value)
{
    value = m_APRangeSettings->getMaxValue(curProbe().Name, APRangeSettings::B, value);
}

void BeamFormerBase::onGettingAcousticPowerBControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue = m_APPressCalculator->getAPValue(BFPNames::AcousticPowerBStr, controlTableValue);
    controlTableValue = getAcousticPowerCTValue(controlTableValue);
}

void BeamFormerBase::onBeforeAcousticPowerBChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (oldValue.toInt() != 0 && newValue.toInt() == 0)
    {
        qCritical() << PRETTY_FUNCTION << " oldValue" << oldValue << "newValue" << newValue;
    }
}

void BeamFormerBase::onBeforeAcousticPowerBThiChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (oldValue.toInt() != 0 && newValue.toInt() == 0)
    {
        qCritical() << PRETTY_FUNCTION << " oldValue" << oldValue << "newValue" << newValue;
    }
}

void BeamFormerBase::onAcousticPowerBThiChanging(const QVariant& value)
{
    if (pBV(BFPNames::THIStr))
    {
        setPV(BFPNames::AcousticPowerBShowStr, value);
        controlCompensationCoef();
        // I9 才控制
        // setPV(BFPNames::TransmitPowerPositiveVolStr, value);
        // setPV(BFPNames::TransmitPowerNegativeVolStr, value);
        emit updateMitiValue();
    }
}

void BeamFormerBase::onAcousticPowerBThiChanged(const QVariant& value)
{
    Q_UNUSED(value);
    m_APPressCalculator->updateOtherParas();
}

void BeamFormerBase::onGettingAcousticPowerBThiMax(int& value)
{
    value = m_APRangeSettings->getMaxValue(curProbe().Name, APRangeSettings::B, value);
}

void BeamFormerBase::onGettingAcousticPowerBThiControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue = m_APPressCalculator->getAPValue(BFPNames::AcousticPowerBThiStr, controlTableValue);
    controlTableValue = getAcousticPowerCTValue(controlTableValue);
}

void BeamFormerBase::onAcousticPowerBShowChanging(const QVariant& value)
{
    // setPV(pBV(BFPNames::THIStr) ? BFPNames::AcousticPowerBThiStr : BFPNames::AcousticPowerBStr, value);
    setPV(m_APPressCalculator->currentParaName(), value);
}

void BeamFormerBase::onGettingAcousticPowerBShowText(QString& value)
{
    // QString apParaName = pBV(BFPNames::THIStr) ? BFPNames::AcousticPowerBThiStr : BFPNames::AcousticPowerBStr;
    QString apParaName = m_APPressCalculator->currentParaName();
    value = m_APPressCalculator->acousticPowerText(apParaName);
}

void BeamFormerBase::onGettingAcousticPowerBShowMax(int& value)
{
    // value = m_APRangeSettings->getMaxValue(curProbe().Name, APRangeSettings::B, value);
    value = parameter(m_APPressCalculator->currentParaName())->max();
}

void BeamFormerBase::onGettingAcousticPowerBShowStep(int& value)
{
    QString apParaName = m_APPressCalculator->currentParaName();
    value = qCeil(parameter(apParaName)->valueCount() / 100.0);
}

void BeamFormerBase::onAcousticPowerColorChanging(const QVariant& value)
{
    setPV(BFPNames::AcousticPowerBShowStr, value);
    controlCompensationCoef();
    // I9 才控制
    // setPV(BFPNames::TransmitPowerPositiveVolStr, value);
    // setPV(BFPNames::TransmitPowerNegativeVolStr, value);
    emit updateMitiValue();
}

void BeamFormerBase::onAcousticPowerColorChanged(const QVariant& value)
{
    Q_UNUSED(value);
    m_APPressCalculator->updateOtherParas();
}

void BeamFormerBase::onGettingAcousticPowerColorText(QString& value)
{
    value = m_APPressCalculator->acousticPowerText(BFPNames::AcousticPowerColorStr);
}

void BeamFormerBase::onGettingAcousticPowerColorMax(int& value)
{
    value = m_APRangeSettings->getMaxValue(curProbe().Name, APRangeSettings::C, value);
}

void BeamFormerBase::onGettingAcousticPowerColorControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue = m_APPressCalculator->getAPValue(BFPNames::AcousticPowerColorStr, controlTableValue);
    controlTableValue = getAcousticPowerCTValue(controlTableValue);
}

void BeamFormerBase::onAcousticPowerDopChanging(const QVariant& value)
{
    setPV(BFPNames::AcousticPowerBShowStr, value);
    controlCompensationCoef();
    // I9 才控制
    // setPV(BFPNames::TransmitPowerPositiveVolStr, value);
    // setPV(BFPNames::TransmitPowerNegativeVolStr, value);
    emit updateMitiValue();
}

void BeamFormerBase::onAcousticPowerDopChanged(const QVariant& value)
{
    Q_UNUSED(value);
    m_APPressCalculator->updateOtherParas();
}

void BeamFormerBase::onGettingAcousticPowerDopText(QString& value)
{
    value = m_APPressCalculator->acousticPowerText(BFPNames::AcousticPowerDopStr);
}

void BeamFormerBase::onGettingAcousticPowerDopMax(int& value)
{
    value = m_APRangeSettings->getMaxValue(curProbe().Name, APRangeSettings::D, value);
}

void BeamFormerBase::onGettingAcousticPowerDopControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue = m_APPressCalculator->getAPValue(BFPNames::AcousticPowerDopStr, controlTableValue);
    controlTableValue = getAcousticPowerCTValue(controlTableValue);
}

void BeamFormerBase::onAcousticPowerMChanging(const QVariant& value)
{
    if (!pBV(BFPNames::THIStr))
    {
        setPV(BFPNames::AcousticPowerBShowStr, value);
        controlCompensationCoef();
        emit updateMitiValue();
    }
}

void BeamFormerBase::onAcousticPowerMChanged(const QVariant& value)
{
    Q_UNUSED(value);
    m_APPressCalculator->updateOtherParas();
}

void BeamFormerBase::onGettingAcousticPowerMMax(int& value)
{
    value = m_APRangeSettings->getMaxValue(curProbe().Name, APRangeSettings::M, value);
}

void BeamFormerBase::onGettingAcousticPowerMControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue = m_APPressCalculator->getAPValue(BFPNames::AcousticPowerMStr, controlTableValue);
    controlTableValue = getAcousticPowerCTValue(controlTableValue);
}

void BeamFormerBase::onAcousticPowerMThiChanging(const QVariant& value)
{
    if (pBV(BFPNames::THIStr))
    {
        setPV(BFPNames::AcousticPowerBShowStr, value);
        controlCompensationCoef();
        emit updateMitiValue();
    }
}

void BeamFormerBase::onAcousticPowerMThiChanged(const QVariant& value)
{
    Q_UNUSED(value);
    m_APPressCalculator->updateOtherParas();
}

void BeamFormerBase::onGettingAcousticPowerMThiMax(int& value)
{
    value = m_APRangeSettings->getMaxValue(curProbe().Name, APRangeSettings::M, value);
}

void BeamFormerBase::onGettingAcousticPowerMThiControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue = m_APPressCalculator->getAPValue(BFPNames::AcousticPowerMThiStr, controlTableValue);
    controlTableValue = getAcousticPowerCTValue(controlTableValue);
}

void BeamFormerBase::onAcousticPowerElastoChanging(const QVariant& value)
{
    setPV(BFPNames::AcousticPowerBShowStr, value);
    controlCompensationCoef();
    // I9 才控制
    // setPV(BFPNames::TransmitPowerPositiveVolStr, value);
    // setPV(BFPNames::TransmitPowerNegativeVolStr, value);
    emit updateMitiValue();
}

void BeamFormerBase::onAcousticPowerElastoChanged(const QVariant& value)
{
    Q_UNUSED(value);
    m_APPressCalculator->updateOtherParas();
}

void BeamFormerBase::onGettingAcousticPowerElastoText(QString& value)
{
    value = m_APPressCalculator->acousticPowerText(BFPNames::AcousticPowerElastoStr);
}

void BeamFormerBase::onGettingAcousticPowerElastoMax(int& value)
{
    value = m_APRangeSettings->getMaxValue(curProbe().Name, APRangeSettings::E, value);
}

void BeamFormerBase::onGettingAcousticPowerElastoControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue = m_APPressCalculator->getAPValue(BFPNames::AcousticPowerElastoStr, controlTableValue);
    controlTableValue = getAcousticPowerCTValue(controlTableValue);
}

void BeamFormerBase::onCWTransmitPowerPositiveVolChanging(const QVariant& value)
{
    setPV(BFPNames::AcousticPowerBShowStr, value);
    controlCompensationCoef();
    emit updateMitiValue();
}

void BeamFormerBase::onGettingCWTransmitPowerPositiveVolText(QString& value)
{
    value = m_APPressCalculator->acousticPowerText(BFPNames::CWTransmitPowerPositiveVolStr);
}

void BeamFormerBase::onGettingCWTransmitPowerPositiveVolMax(int& value)
{
    value = m_APRangeSettings->getMaxValue(curProbe().Name, APRangeSettings::CW, value);
}

void BeamFormerBase::onGettingCWTransmitPowerPositiveVolControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onDScanLineChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::CWEnStr))
    {
        sendCWFocusBlockDataGroup();
    }
}

void BeamFormerBase::onCWScanLineDeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::CWEnStr))
    {
        sendCWFocusBlockDataGroup();
    }
}

void BeamFormerBase::onCWMidDepthMMDeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::CWEnStr))
    {
        sendCWFocusBlockDataGroup();
    }
}

void BeamFormerBase::onPWScanLineDeltaChanging(const QVariant& value)
{
    //后台调节pwScanLineDelta参数时，只在下发控制表中更新DScanLine，不会在ui上显示出来
    Q_UNUSED(value);
    parameter(BFPNames::DScanLineStr)->update();
}

void BeamFormerBase::onGettingDScanLineControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = value.toInt() + pIV(BFPNames::PWScanLineDeltaStr);
}

void BeamFormerBase::onGettingStartLinePreset(QVariant& value)
{
    //放大状态下，要重新计算
    if (zoomOn())
    {
        value = BFScanWidthParameter(curProbe(), pIV(BFPNames::ScanWidthStr), isSupportAnyDensity(),
                                     pIV(BFPNames::B_RX_LNUMStr))
                    .startLine();
    }
}

void BeamFormerBase::onGettingStopLinePreset(QVariant& value)
{
    //放大状态下，要重新计算
    if (zoomOn())
    {
        value = BFScanWidthParameter(curProbe(), pIV(BFPNames::ScanWidthStr), isSupportAnyDensity(),
                                     pIV(BFPNames::B_RX_LNUMStr))
                    .stopLine();
    }
}

void BeamFormerBase::onStartLineChanged(const QVariant& value)
{
    controlSonoNeedleParas();
}

void BeamFormerBase::onGettingStartLineControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onStopLineChanged(const QVariant& value)
{
    controlSonoNeedleParas();
}

void BeamFormerBase::onGettingStopLineControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

// static const int COLOR_ALIGN_LINE = 4;
// void BeamFormerBase::onGettingStartLineColorControlTableValue(const QVariant &value, int &controlTableValue)
//{
//    Q_UNUSED(value);
//    if (curProbe().IsPhasedArray && pBV(BFPNames::ColorMStr))
//    {
//        //进入ColorM模式时，ROI的左右线号要缩小到MScanLine的左右附近区域，from yangcheng
//        controlTableValue = pIV(BFPNames::MScanLineStr) - COLOR_ALIGN_LINE;
//    }
//}

// void BeamFormerBase::onGettingStopLineColorControlTableValue(const QVariant &value, int &controlTableValue)
//{
//    Q_UNUSED(value);
//    if (curProbe().IsPhasedArray && pBV(BFPNames::ColorMStr))
//    {
//        //进入ColorM模式时，ROI的左右线号要缩小到MScanLine的左右附近区域，from yangcheng
//        controlTableValue = pIV(BFPNames::MScanLineStr) + COLOR_ALIGN_LINE;
//    }
//}

void BeamFormerBase::onGettingTopBorderColorControlTableValue(const QVariant& value, int& controlTableValue)
{
    //+1后，可以保证血流上边界在ROI框内部，不+1的时候，如果value为奇数，下发的值会偏上一个像素
    controlTableValue = (value.toInt() + 1) >> 1;
    if (imageMode() == MODE_4B)
    {
        controlTableValue = controlTableValue << 1;
    }
}

void BeamFormerBase::onSampleDepthDopChanging(const QVariant& value)
{
    const ProbeDataInfo& probe = ProbeDataSet::instance().getProbe(pV(BFPNames::ProbeIdStr).toInt());
    QList<CWGainParam> cws = probe.CwGainParams;

    if (cws.isEmpty())
    { //默认增益为220
        setPV(BFPNames::CWGainStr, 220);
        return;
    }

    float depthPosCM = value.toFloat() *
                       BFDepthParameters::pixelSizeMMWithoutZoomCoef(
                           pDV(BFPNames::PixelSizeMMStr), pIV(BFPNames::ImageZoomCoefStr),
                           pDV(BFPNames::RenderImageZoomCofStr), pFV(BFPNames::FixedSWImageZoomCofStr)) /
                       10;
    //    qDebug()<<"onSampleDepthDopChanging:"<<depthPosCM<<cws.count();
    int cwcount = cws.count();

    if (RealCompare::IsGreaterOrEqual(depthPosCM, cws.at(cwcount - 1).depthCM))
    {
        setPV(BFPNames::CWGainStr, cws.at(cwcount - 1).gain);
        //        qDebug()<<"onSampleDepthDopChanging: gain "<< cws.at(cwcount - 1).gain<<cws.at(cwcount - 1).depthCM;
        return;
    }

    for (int i = 0; i < cwcount - 1; i++)
    {
        if (RealCompare::IsGreaterOrEqual(depthPosCM, cws.at(i).depthCM) &&
            RealCompare::IsSmaller(depthPosCM, cws.at(i + 1).depthCM))
        {
            setPV(BFPNames::CWGainStr, cws.at(i).gain);
            //            qDebug()<<"onSampleDepthDopChanging: gain "<< cws.at(i).gain<<cws.at(i).depthCM;
            break;
        }
    }
}

void BeamFormerBase::onGettingBottomBorderColorControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = value.toInt() >> 1;
    if (imageMode() == MODE_4B)
    {
        controlTableValue = controlTableValue << 1;
    }
}

// TODO: 此处需优化，去掉LineDensity* 参数，与UI对应的预设值 采用 OrderColorLineDensity*，控制字参数
// 采用ColorLineDensity
void BeamFormerBase::onOrderLineDensityPDChanging(const QVariant& value)
{
    //    parameter(BFPNames::LineDensityPDStr)->setDirectValue(getRealDensity(value));
    controlRealDensity(BFPNames::LineDensityPDStr, value.toInt());
}

void BeamFormerBase::onGettingOrderLineDensityPDText(QString& value)
{
    updateRealDensityText(BFPNames::LineDensityPDStr, value);
}

void BeamFormerBase::onOrderLineDensityDPDChanging(const QVariant& value)
{
    controlRealDensity(BFPNames::LineDensityDPDStr, value.toInt());
}

void BeamFormerBase::onGettingOrderLineDensityDPDText(QString& value)
{
    updateRealDensityText(BFPNames::LineDensityDPDStr, value);
}

void BeamFormerBase::onOrderLineDensityTDIChanging(const QVariant& value)
{
    controlRealDensity(BFPNames::LineDensityTDIStr, value.toInt());
}

void BeamFormerBase::onGettingOrderLineDensityTDIText(QString& value)
{
    updateRealDensityText(BFPNames::LineDensityTDIStr, value);
}

void BeamFormerBase::onGettingGainControlTableValue(const QVariant& value, int& controlTableValue)
{
    m_PostProcessHandler->onGettingGainControlTableValue(value, controlTableValue);
}

void BeamFormerBase::onGettingGainThiControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingGainControlTableValue(value, controlTableValue);
}

void BeamFormerBase::onBWGainDeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::TDIEnStr))
    {
        parameter(pBV(BFPNames::THIStr) ? BFPNames::GainThiStr : BFPNames::GainStr)->update();
    }
}

void BeamFormerBase::onMaxGainChanging(const QVariant& value)
{
    m_PostProcessHandler->onMaxGainChanging(value);
}

void BeamFormerBase::onGainShowChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlBTgcGain();
}

void BeamFormerBase::onGettingGainShowText(QString& value)
{
    m_PostProcessHandler->onGettingGainShowText(value);
}

void BeamFormerBase::onGettingTGC1ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(0);
}

void BeamFormerBase::onGettingTGC2ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(1);
}

void BeamFormerBase::onGettingTGC3ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(2);
}

void BeamFormerBase::onGettingTGC4ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(3);
}

void BeamFormerBase::onGettingTGC5ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(4);
}

void BeamFormerBase::onGettingTGC6ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(5);
}

void BeamFormerBase::onGettingTGC7ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(6);
}

void BeamFormerBase::onGettingTGC8ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += tgcDelta(7);
}

void BeamFormerBase::onGettingUniformityTgc00ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(0);
}

void BeamFormerBase::onGettingUniformityTgc01ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(1);
}

void BeamFormerBase::onGettingUniformityTgc02ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(2);
}

void BeamFormerBase::onGettingUniformityTgc03ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(3);
}

void BeamFormerBase::onGettingUniformityTgc04ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(4);
}

void BeamFormerBase::onGettingUniformityTgc05ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(5);
}

void BeamFormerBase::onGettingUniformityTgc06ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(6);
}

void BeamFormerBase::onGettingUniformityTgc07ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(7);
}

void BeamFormerBase::onGettingUniformityTgc08ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(8);
}

void BeamFormerBase::onGettingUniformityTgc09ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(9);
}

void BeamFormerBase::onGettingUniformityTgc10ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(10);
}

void BeamFormerBase::onGettingUniformityTgc11ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(11);
}

void BeamFormerBase::onGettingUniformityTgc12ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(12);
}

void BeamFormerBase::onGettingUniformityTgc13ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(13);
}

void BeamFormerBase::onGettingUniformityTgc14ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(14);
}

void BeamFormerBase::onGettingUniformityTgc15ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue -= dTgcDelta(15);
}

void BeamFormerBase::onGettingDebugPara0ControlTableValue(const QVariant& value, int& controlTableValue)
{
}

void BeamFormerBase::onTgcDelta0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(0, TGCDeltaNormal);
}

void BeamFormerBase::onTgcDelta1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(1, TGCDeltaNormal);
}

void BeamFormerBase::onTgcDelta2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(2, TGCDeltaNormal);
}

void BeamFormerBase::onTgcDelta3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(3, TGCDeltaNormal);
}

void BeamFormerBase::onTgcDelta4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(4, TGCDeltaNormal);
}

void BeamFormerBase::onTgcDelta5Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(5, TGCDeltaNormal);
}

void BeamFormerBase::onTgcDelta6Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(6, TGCDeltaNormal);
}

void BeamFormerBase::onTgcDelta7Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(7, TGCDeltaNormal);
}

void BeamFormerBase::onTriplexTgcDelta0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(0, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexTgcDelta1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(1, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexTgcDelta2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(2, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexTgcDelta3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(3, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexTgcDelta4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(4, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexTgcDelta5Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(5, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexTgcDelta6Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(6, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexTgcDelta7Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(7, TGCDeltaTriplex);
}

void BeamFormerBase::onThiTriplexTgcDelta0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(0, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexTgcDelta1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(1, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexTgcDelta2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(2, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexTgcDelta3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(3, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexTgcDelta4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(4, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexTgcDelta5Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(5, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexTgcDelta6Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(6, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexTgcDelta7Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(7, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTgcDelta0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(0, TGCDeltaThi);
}

void BeamFormerBase::onThiTgcDelta1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(1, TGCDeltaThi);
}

void BeamFormerBase::onThiTgcDelta2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(2, TGCDeltaThi);
}

void BeamFormerBase::onThiTgcDelta3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(3, TGCDeltaThi);
}

void BeamFormerBase::onThiTgcDelta4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(4, TGCDeltaThi);
}

void BeamFormerBase::onThiTgcDelta5Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(5, TGCDeltaThi);
}

void BeamFormerBase::onThiTgcDelta6Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(6, TGCDeltaThi);
}

void BeamFormerBase::onThiTgcDelta7Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendTgcDelta(7, TGCDeltaThi);
}

void BeamFormerBase::onDTgcDelta0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(0, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(1, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(2, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(3, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(4, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta5Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(5, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta6Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(6, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta7Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(7, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta8Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(8, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta9Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(9, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta10Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(10, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta11Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(11, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta12Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(12, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta13Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(13, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta14Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(14, TGCDeltaNormal);
}

void BeamFormerBase::onDTgcDelta15Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(15, TGCDeltaNormal);
}

void BeamFormerBase::onTriplexDTgcDelta0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(0, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(1, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(2, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(3, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(4, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta5Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(5, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta6Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(6, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta7Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(7, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta8Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(8, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta9Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(9, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta10Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(10, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta11Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(11, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta12Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(12, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta13Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(13, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta14Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(14, TGCDeltaTriplex);
}

void BeamFormerBase::onTriplexDTgcDelta15Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(15, TGCDeltaTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(0, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(1, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(2, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(3, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(4, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta5Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(5, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta6Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(6, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta7Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(7, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta8Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(8, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta9Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(9, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta10Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(10, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta11Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(11, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta12Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(12, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta13Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(13, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta14Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(14, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiTriplexDTgcDelta15Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(15, TGCDeltaThiTriplex);
}

void BeamFormerBase::onThiDTgcDelta0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(0, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(1, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(2, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(3, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(4, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta5Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(5, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta6Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(6, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta7Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(7, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta8Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(8, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta9Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(9, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta10Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(10, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta11Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(11, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta12Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(12, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta13Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(13, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta14Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(14, TGCDeltaThi);
}

void BeamFormerBase::onThiDTgcDelta15Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendDTgcDelta(15, TGCDeltaThi);
}

void BeamFormerBase::onGettingStripRemoveEnableControlTableValue(const QVariant& value, int& controlTableValue)
{
}

void BeamFormerBase::onZoomDepthMMChanging(const QVariant& value)
{
    //由于pixelLenMM中添加extCQYZ控制，此处原有的处理无需控制，因此传入false
    setPV(BFPNames::ZoomDepthStr,
          qRound(value.toDouble() / BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr), false)));
    setPV(BFPNames::StartDepthMMStr, startDepthMM());

    if (contains(BFPNames::ZoomDepthPixelStr))
    {
        //以下这些参数都要以未放大时的参数计算
        BFDepthParameters depthP(pIV(BFPNames::ADFreqMHzStr), pV(BFPNames::ImageSizeStr).toSize().height(),
                                 pIV(BFPNames::CQYZStr), pBV(BFPNames::HalfHeightStr));
        // depthP.setPixelLen(BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) /
        // pFV(BFPNames::FixedSWImageZoomCofStr));
        //放大框深度值的像素值，除以2下发，控制表用了9bit，是考虑图像高度为600像素时设计的
        setPV(BFPNames::ZoomDepthPixelStr, qRound(value.toDouble() / depthP.pixelSizeMM()) >> 1);
    }
}

void BeamFormerBase::onBeforeFreqIndexBChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    Q_UNUSED(newValue);
}

void BeamFormerBase::onFreqIndexBChanging(const QVariant& value)
{
    setPV(BFPNames::HarmonicStr, false);
    sendFreqdynamic();
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_BlockDataParaNameConverter->groupParaName(BFPNames::FreqIndexBStr),
                                              value.toInt() * 2 + (pBV(BFPNames::THIStr) ? 1 : 0));
    {
        FreqRelatedParasSender fpSender(m_RelatedParasController);
        parameter(rvFNoStr())->update();
    }
    if (pBV(BFPNames::CurvedExapandingStr))
    {
        parameter(BFPNames::CurvedExapandingStr)->update();
    }
    parameter(BFPNames::MF_Coef_Fundamental_Steer0Str)->update();
    parameter(BFPNames::MF_Coef_Harmonic_Steer0Str)->update();
    imageIntoUnstable();
}

void BeamFormerBase::onGettingFreqIndexBText(QString& value)
{
    if (pBV(BFPNames::THIStr))
    {
        value = curProbe().FreqsOnThi[qobject_cast<Parameter*>(sender())->intValue()];
    }
    else
    {
        value = curProbe().Freqs[qobject_cast<Parameter*>(sender())->intValue()];
    }
}

void BeamFormerBase::onGettingFreqIndexBMax(int& value)
{
    value = curProbe().FreqIndexes.count() - 1;
}

void BeamFormerBase::onGettingFreqIndexBControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = curProbe().FreqIndexes[value.toInt()];
}

void BeamFormerBase::oniImageChanging(const QVariant& value)
{
    if (pIV(BFPNames::iImageLibStr) == 0)
    {
        m_RelatedParasController->sendiImageParas(value.toInt());
    }
}

void BeamFormerBase::onGettingDetailWeightBinControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    int min, max, step;
    getParameterMinMaxStep(BFPNames::DetailWeightBinStr, min, max, step);

    if (pBV(BFPNames::FcpdOnStr) && !pBV(BFPNames::ScpdOnStr))
    {
        controlTableValue += pIV(BFPNames::DetailWeightDeltaSraOnStr);
    }
    else if (!pBV(BFPNames::FcpdOnStr) && !pBV(BFPNames::ScpdOnStr))
    {
        controlTableValue += pIV(BFPNames::DetailWeightDeltaSraCpdOffStr);
    }

    controlTableValue = qBound(min, controlTableValue, max);
}

void BeamFormerBase::onDetailWeightChanging(const QVariant& value)
{
    //    if (!pBV(BFPNames::SwitchTo1p0Str))
    {
        setPV(BFPNames::DetailWeightBinStr, value);
    }
}

void BeamFormerBase::onDetailWeight1p0Changing(const QVariant& value)
{
    //    if (pBV(BFPNames::SwitchTo1p0Str))
    {
        setPV(BFPNames::DetailWeightBinStr, value);
    }
}

void BeamFormerBase::onMinWeightChanging(const QVariant& value)
{
    //    if (!pBV(BFPNames::SwitchTo1p0Str))
    {
        setPV(BFPNames::MinWeightBinStr, value);
    }
}

void BeamFormerBase::onMaxWeightChanging(const QVariant& value)
{
    //    if (!pBV(BFPNames::SwitchTo1p0Str))
    {
        setPV(BFPNames::MaxWeightBinStr, value);
    }
}

void BeamFormerBase::onFusionCoefChanging(const QVariant& value)
{
    //    if (pBV(BFPNames::SwitchTo1p0Str))
    {
        setPV(BFPNames::MinWeightBinStr, value);
        setPV(BFPNames::MaxWeightBinStr, value);
    }
}

void BeamFormerBase::onEdgeWeightChanging(const QVariant& value)
{
    //    if (!pBV(BFPNames::SwitchTo1p0Str))
    {
        setPV(BFPNames::EdgeWeightBinStr, value);
    }
}

void BeamFormerBase::onEdgeWeight1p0Changing(const QVariant& value)
{
    //    if (pBV(BFPNames::SwitchTo1p0Str))
    {
        setPV(BFPNames::EdgeWeightBinStr, value);
    }
}

void BeamFormerBase::onDetailWeightDeltaSraOnChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::FcpdOnStr) && !pBV(BFPNames::ScpdOnStr))
    {
        parameter(BFPNames::DetailWeightBinStr)->update();
    }
}

void BeamFormerBase::onDetailWeightDeltaSraCpdOffChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (!pBV(BFPNames::FcpdOnStr) && !pBV(BFPNames::ScpdOnStr))
    {
        parameter(BFPNames::DetailWeightBinStr)->update();
    }
}

void BeamFormerBase::onRvFNoChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (!pBV(BFPNames::FreqSpectrumStr) && !pBV(BFPNames::TriplexModeStr))
    {
        sendRvFNoData();
    }
}

void BeamFormerBase::onRvPWFNoChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::FreqSpectrumStr) || pBV(BFPNames::TriplexModeStr))
    {
        sendRvFNoData();
    }
}

void BeamFormerBase::onTxFNoChanging(const QVariant& value)
{
    setPV(BFPNames::D_TxFNoStr, value);
    setPV(BFPNames::BTxFNoStr, value);
    sendBTxParas();

    BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name,
                                     QStringList() << BFPNames::TxFNoStr << "CurvedCpdTx");
    m_BlockDataSender->sendParaBlockDataGroup(
        curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::TxFNoStr), value.toInt());
    sendCurvedCpdTxBlockDataGroup();
}

void BeamFormerBase::onTXFNo_Steer1Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onTxIndex5Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onTxIndex7Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onCPDSteerChanging(const QVariant& value)
{
    if (curProbe().IsLinear)
    {
        if (!isBSteerOn() && !isTrapezoidalModeOn())
        {
            sendCpdSteerCTValue(value.toInt());
            m_BlockDataSender->sendParaBlockDataGroup(
                curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr), value.toInt());
        }
    }
    else
    {
        sendCpdSteerCTValue(value.toInt());
        BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name,
                                         QStringList() << BFPNames::CQYZStr << "CurvedCpd");
        sendCQYZBlockDataGroup();
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName("CurvedCpd"), qBound(0, value.toInt() - 3, 4));
        sendCurvedCpdTxBlockDataGroup();
    }
    if (isFixedDepth())
    {
        controlCQYZ(pIV(BFPNames::CQYZLevelStr));
    }
}

void BeamFormerBase::onCPDSteer2Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onCPDSteer3Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onCPDSteer4Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onCPDSteer5Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onTrapezoidalCPDSteerChanging(const QVariant& value)
{
    if (isTrapezoidalModeOn())
    {
        sendCpdSteerCTValue(value.toInt());
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr), value.toInt());
    }
}

void BeamFormerBase::onWeightedCurveChanging(const QVariant& value)
{
    Parameter* rvfno = parameter(rvFNoStr());
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_BlockDataParaNameConverter->groupParaName(BFPNames::WeightedCurveStr),
                                              rvfno->valueCount() * value.toInt() + rvfno->intValue());
    setPV(BFPNames::BWeightedCurveTypeStr, value);
}

void BeamFormerBase::onGettingWeightedCurveText(QString& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onCfmFnumChanging(const QVariant& value)
{
    Q_UNUSED(value);
    setPV(BFPNames::CTxFNoStr, value);
    sendCfmTxParas();
}

void BeamFormerBase::onCfmRxFnumChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendCfmRvParas();
}

void BeamFormerBase::onDopFNumChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendDopTxParas();
}

void BeamFormerBase::onDopRvFNumChanging(const QVariant& value)
{
    sendDopRvParas();
    m_BlockDataSender->sendParaBlockDataGroup(
        curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::DopRvFNumStr), value.toInt());
}

void BeamFormerBase::onFocusNumCChanged(const QVariant& value)
{
    Q_UNUSED(value);
    emit updateMitiValue();
}

void BeamFormerBase::onBeforeFocusPosCChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    newValue = BFFocusParameters().adviseFocusPos(curProbe(), pIV(BFPNames::FocusNumCStr), newValue.toInt(),
                                                  imageBottomDepthMM());
}

void BeamFormerBase::onGettingFocusPosCIsValidValue(const QVariant& value, bool& valid)
{
    QVariant oldValue = value;
    QVariant newValue = value;
    onBeforeFocusPosCChanged(value, newValue);
    valid = (newValue == oldValue);
}

void BeamFormerBase::onFocusPosCChanged(const QVariant& value)
{
    Q_UNUSED(value);
    emit updateMitiValue();
}

void BeamFormerBase::onGettingHalfHeight2ControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (imageMode() == MODE_4B)
    {
        controlTableValue = false;
    }
}

void BeamFormerBase::onGainDopChanging(const QVariant& value)
{
    m_PostProcessHandler->onGainDopChanging(value);
}

void BeamFormerBase::onGainDopTMChanging(const QVariant& value)
{
    onGainDopChanging(value);
}

void BeamFormerBase::onGainDopTDIChanging(const QVariant& value)
{
    onGainDopChanging(value);
}

void BeamFormerBase::onGainDopCWDChanging(const QVariant& value)
{
    onGainDopChanging(value);
}

void BeamFormerBase::onBeforeLCDLightChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    Q_UNUSED(newValue);
    if (isFrozen())
    {
        // ECO2的参数LCDLight下发前，设置为同步模式，下发结束后要设置回异步模式
        m_ControlTable->setSyncMode();
    }
}

void BeamFormerBase::onLCDLightChanging(const QVariant& value)
{
    setPV(BFPNames::PWMDutyRatioStr, value);
    ModelSettings::instance().setValue(ModelSettings::LCDLight, value);
}

void BeamFormerBase::onLCDLightChanged(const QVariant& value)
{
    Q_UNUSED(value);
    if (isFrozen())
    {
        // ECO2的参数LCDLight下发结束后要设置回异步模式
        m_ControlTable->setASyncMode();
    }
}

void BeamFormerBase::onGettingLCDLightControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = qRound(255.0f * (1.0f - (float)value.toInt() / 100.0f));
    if (controlTableValue > 255)
    {
        controlTableValue = 255;
    }
}

static QString bloodEffectionText(int effectionValue)
{
    if (effectionValue == 0)
    {
        return "Smooth";
    }
    else if (effectionValue == 1)
    {
        return "HRes";
    }
    else if (effectionValue > 1)
    {
        return QString("HRes%1").arg(effectionValue);
    }
    else
    {
        return "Smooth";
    }
}

void BeamFormerBase::onGettingBloodEffectionText(QString& value)
{
    value = bloodEffectionText(pIV(BFPNames::BloodEffectionStr));
}

void BeamFormerBase::onGettingCinePlaySpeedAdjustText(QString& value)
{
    m_PostProcessHandler->onGettingCinePlaySpeedAdjustText(value);
}

void BeamFormerBase::onGettingBloodEffectionPDText(QString& value)
{
    value = bloodEffectionText(pIV(BFPNames::BloodEffectionPDStr));
}

void BeamFormerBase::onGettingBloodEffectionSNText(QString& value)
{
    value = bloodEffectionText(pIV(BFPNames::BloodEffectionSNStr));
}

void BeamFormerBase::onGettingBloodEffectionTDIText(QString& value)
{
    value = bloodEffectionText(pIV(BFPNames::BloodEffectionTDIStr));
}

void BeamFormerBase::onGettingBloodEffectionMVIText(QString& value)
{
    value = bloodEffectionText(pIV(BFPNames::BloodEffectionMVIStr));
}

void BeamFormerBase::onGettingBPulseNumMin(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingBPulseNumMax(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingBPulseNumControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::BPulseNumStr);
}

void BeamFormerBase::onGettingCPulseNumMin(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingCPulseNumMax(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingCPulseNumControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::CPulseNumStr);
}

void BeamFormerBase::onGettingDPulseNumMin(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingDPulseNumMax(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingDPulseNumControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::DPulseNumStr);
}

void BeamFormerBase::onGettingDPulseNumTDIMin(int& value)
{
    onGettingDPulseNumMin(value);
}

void BeamFormerBase::onGettingDPulseNumTDIMax(int& value)
{
    onGettingDPulseNumMax(value);
}

void BeamFormerBase::onGettingDPulseNumTDIControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingDPulseNumControlTableValue(value, controlTableValue);
}

void BeamFormerBase::onGettingCWDPluseNumMin(int& value)
{
    onGettingDPulseNumMin(value);
}

void BeamFormerBase::onGettingCWDPluseNumMax(int& value)
{
    onGettingDPulseNumMax(value);
}

void BeamFormerBase::onGettingCWDPluseNumControlTableValue(const QVariant& value, int& controlTableValue)
{
    onGettingDPulseNumControlTableValue(value, controlTableValue);
}

void BeamFormerBase::onGettingPulseNumOfTransmit1Min(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit1Max(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit1ControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::PulseNumOfTransmit1Str);
}

void BeamFormerBase::onGettingPulseNumOfTransmit2Min(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit2Max(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit2ControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::PulseNumOfTransmit2Str);
}

void BeamFormerBase::onGettingPulseNumOfTransmit3Min(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit3Max(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit3ControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::PulseNumOfTransmit3Str);
}

void BeamFormerBase::onGettingPulseNumOfTransmit4Min(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit4Max(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit4ControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::PulseNumOfTransmit4Str);
}

void BeamFormerBase::onGettingPulseNumOfTransmit5Min(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit5Max(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit5ControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::PulseNumOfTransmit5Str);
}

void BeamFormerBase::onGettingPulseNumOfTransmit6Min(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit6Max(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit6ControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::PulseNumOfTransmit6Str);
}

void BeamFormerBase::onGettingPulseNumOfTransmit7Min(int& value)
{
    value = minPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit7Max(int& value)
{
    value = maxPulseNum(qobject_cast<ControlTableParameter*>(sender()));
}

void BeamFormerBase::onGettingPulseNumOfTransmit7ControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = pulseNumV2CTValue(value, BFPNames::PulseNumOfTransmit7Str);
}

void BeamFormerBase::onGettingFrequencyOfTransmit1ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += frequencyDelta(1);
}

void BeamFormerBase::onGettingFilterCoefOfTransmit1ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += filterCoefDelta(1);
}

void BeamFormerBase::onGettingFrequencyOfTransmit2ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += frequencyDelta(2);
}

void BeamFormerBase::onGettingFilterCoefOfTransmit2ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += filterCoefDelta(2);
}

void BeamFormerBase::onGettingFrequencyOfTransmit3ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += frequencyDelta(3);
}

void BeamFormerBase::onGettingFilterCoefOfTransmit3ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += filterCoefDelta(3);
}

void BeamFormerBase::onGettingFrequencyOfTransmit4ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += frequencyDelta(4);
}

void BeamFormerBase::onGettingFilterCoefOfTransmit4ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    controlTableValue += filterCoefDelta(4);
}

void BeamFormerBase::onTriplexFrequencyDeltaTr1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendFrequencyDelta(1);
}

void BeamFormerBase::onTriplexFilterCoefDeltaTr1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendFilterCoefDelta(1);
}

void BeamFormerBase::onTriplexFrequencyDeltaTr2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendFrequencyDelta(2);
}

void BeamFormerBase::onTriplexFilterCoefDeltaTr2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendFilterCoefDelta(2);
}

void BeamFormerBase::onTriplexFrequencyDeltaTr3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendFrequencyDelta(3);
}

void BeamFormerBase::onTriplexFilterCoefDeltaTr3Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendFilterCoefDelta(3);
}

void BeamFormerBase::onTriplexFrequencyDeltaTr4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendFrequencyDelta(4);
}

void BeamFormerBase::onTriplexFilterCoefDeltaTr4Changing(const QVariant& value)
{
    Q_UNUSED(value);
    sendFilterCoefDelta(4);
}

void BeamFormerBase::onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    bool isSameScanMode = (oldValue.toInt() == newValue.toInt());

    if (pBV(BFPNames::TriplexModeStr))
    {
        // 相同模式下，保存三同步的开启状态
        if (isSameScanMode)
        {
            setPV(BFPNames::TriplexModeStr, true);

            if (pBV(BFPNames::TripleModeResetEnStr) &&
                !pBV(BFPNames::QuadplexModeStr)) // 如果四同步没开，参数变更不能影响到三同步的开关状态
            {
                setPV(BFPNames::TripleModeResetEnStr, false);
                parameter(BFPNames::QuadplexModeStr)->update();
                setPV(BFPNames::TripleModeResetEnStr, true);
            }
            else
            {
                parameter(BFPNames::QuadplexModeStr)->update();
            }
        }
        else
        {
            setPV(BFPNames::TriplexModeStr, false);
            setPV(BFPNames::QuadplexModeStr, false);
            // 非相控阵下重新开启TriplexMode的可调节，因为开启四同步会暂时禁用三同步
            if (!curProbe().IsPhasedArray)
            {
                parameter(BFPNames::TriplexModeStr)->setEnabled(true);
            }
            if (m_TriplexModeHelper != NULL)
            {
                m_TriplexModeHelper->leaveTripleMode();
            }
        }
    }

    //从4DLive回到B模式重设B图像的稳定时间，解决图像不稳定导致花屏的问题 sjh
    if ((SystemScanMode)(oldValue.toInt()) == SystemScanModeFourDLive &&
        (SystemScanMode)(newValue.toInt()) == SystemScanModeB)
    {
        // B 模式双焦点并打开SRA或者焦点数为4和8的情况下进入4D,退出4D回到B模式时的图像稳定时间较长，这里单独处理 sjh
        if ((pIV(BFPNames::FocusNumBStr) == 1 && !pBV(BFPNames::ScpdOnStr) && pBV(BFPNames::FcpdOnStr)) ||
            pIV(BFPNames::FocusNumBStr) == 3 || pIV(BFPNames::FocusNumBStr) == 7)
        {
            emit updateUnstableTime(600);
        }
        else
        {
            emit updateUnstableTime(300);
        }
    }

    if (!isSameScanMode && ((SystemScanMode)(newValue.toInt()) == SystemScanModeMVI))
    {
        setPV(BFPNames::BMVIBImagesStr, true);
    }

    //切换到不支持超影同屏的模式下，要关闭
    if (pBV(BFPNames::PictureModeONStr) && !SystemScanModeClassifier::isSuppprtPicture(newValue.toInt()))
    {
        setPV(BFPNames::PictureModeONStr, false);
    }
}

void BeamFormerBase::onLayoutChanging(const QVariant& value)
{
    m_StateManager->setLayout(value.toInt());
    m_IsLayoutChange = true;
    onSystemScanModeChanging((SystemScanMode)pIV(BFPNames::SystemScanModeStr));
    if (imageMode() == MODE_UDBM || imageMode() == MODE_BM)
    {
        parameter(BFPNames::CQYZStr)->update();
        updateRenderLayoutInfos(m_ImageRenderLayoutRects->getDisplayFormat(systemScanMode()));
    }
    m_IsLayoutChange = false;
}

void BeamFormerBase::onSystemScanModeChanging(const QVariant& value)
{
    //    imageIntoUnstable();

    m_ImageRenderLayoutRects->updateRenderImageScale();
    setPV(BFPNames::LayoutBImageSizeStr, m_ImageRenderLayoutRects->getLayoutBImageSize());
    setPV(BFPNames::BImageSizeStr, m_ImageRenderLayoutRects->getBImageSize());
    setPV(BFPNames::RenderBImageSizeStr, m_ImageRenderLayoutRects->getRenderBImageSize());
    setPV(BFPNames::MImageSizeStr, m_ImageRenderLayoutRects->getMImageSize());
    setPV(BFPNames::DImageSizeStr, m_ImageRenderLayoutRects->getDImageSize());
    //下面这四个参数决定了pw模式下的图像曲与坐标区的位置,特别是第二个
    setPV(BFPNames::ImageModeRectsStr, m_ImageRenderLayoutRects->getModeRects(imageSize()));
    setPV(BFPNames::RenderWidgetRectsStr,
          m_ImageRenderLayoutRects
              ->getRenderWidgetRects()); //这里的temp2[1]控制pw下方的坐标轴的位置(会受到temp4[0]的影响)
    setPV(BFPNames::ImageRegionsStr, m_ImageRenderLayoutRects->getImageRegions());
    setPV(BFPNames::ImageRectsStr, m_ImageRenderLayoutRects->getImageRects());
    setPV(BFPNames::DSCImageRectsStr, m_ImageRenderLayoutRects->getDSCImageRects());

    setPV(BFPNames::ElastoStrainListStr, QVariant());
    //    setPV(BFPNames::IsDopplerScanLineVisibleStr, false);
    //    setPV(BFPNames::IsCWDScanLineVisibleStr, false);

    bool mviMode = false;

    {
        ControlTableSender ctSender(m_ControlTable);

        setPV(BFPNames::FreqSpectrumStr, false);
        // cw模式下切换探头,扫描模式改变触发prtofd变化,这时cwen还是true,会进行很多
        // cwprf相关计算;而eco6增加了hprf,cwprf计算还会进行prf跳档操作,
        //为了保证cwprf的正确,在扫描模式改变之前设置cwen为false,测试不会导致其他问题
        setPV(BFPNames::CWEnStr, false);

        ImageModeType modeType = MODE_B;
        switch (pIV(BFPNames::LayoutStr))
        {
        case Layout_1x1:
        default:
            modeType = MODE_B;
            break;
        case Layout_1x2:
            modeType = MODE_2B;
            break;
        case Layout_2x2:
            modeType = MODE_4B;
            break;
        }

        switch ((SystemScanMode)(value.toInt()))
        {
        case SystemScanModeB:
        case SystemScanModeFreeHand3D:
        {
            // ZoomOn状态下回调图片后解冻需要判断ZoomOn状态下发不同的ImageMode,否则解冻后还处于ZoomOn状态但ImageMode是MODE_B
            ImageModeType imageMode = pBV(BFPNames::ZoomOnStr) ? MODE_ZOOM : modeType;
            setPV(BFPNames::ImageModeStr, (int)imageMode);
            setPV(BFPNames::SyncModeStr, (int)Sync_None);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        }
        case SystemScanModeFourDPre:
        {
            // ZoomOn状态下回调图片后解冻需要判断ZoomOn状态下发不同的ImageMode,否则解冻后还处于ZoomOn状态但ImageMode是MODE_B
            ImageModeType imageMode = pBV(BFPNames::ZoomOnStr) ? MODE_ZOOM : MODE_B;
            setPV(BFPNames::ImageModeStr, (int)imageMode);
            setPV(BFPNames::SyncModeStr, (int)Sync_None);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        }
        case SystemScanMode2B:
            setPV(BFPNames::ImageModeStr, (int)MODE_2B);
            setPV(BFPNames::SyncModeStr, (int)Sync_None);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanMode4B:
            setPV(BFPNames::ImageModeStr, (int)MODE_4B);
            setPV(BFPNames::SyncModeStr, (int)Sync_None);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeLRBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_BM);
            setPV(BFPNames::SyncModeStr, (int)Sync_M);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::IsUDBMStr, 0);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeUDBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_M);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::IsUDBMStr, 1);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeM:
            setPV(BFPNames::ImageModeStr, (int)MODE_M);
            setPV(BFPNames::SyncModeStr, (int)Sync_M);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeLRFreeM:
            setPV(BFPNames::ImageModeStr, (int)MODE_BM);
            setPV(BFPNames::SyncModeStr, (int)Sync_M);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::IsUDBMStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, true);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeUDFreeM:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_M);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::IsUDBMStr, true);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, true);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeColorDoppler:
            setPV(BFPNames::ImageModeStr,
                  (pBV(BFPNames::BCImagesOnStr) && !pBV(BFPNames::ElastoEnStr)) ? (int)MODE_LCRB : (int)modeType);
            setPV(BFPNames::SyncModeStr, (int)Sync_C);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeSonoNeedle:
        case SystemScanModePowerDoppler:
            setPV(BFPNames::ImageModeStr,
                  (pBV(BFPNames::BCImagesOnStr) && !pBV(BFPNames::ElastoEnStr)) ? (int)MODE_LCRB : (int)modeType);
            setPV(BFPNames::SyncModeStr, (int)Sync_C);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD, true);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeDPowerDoppler:
            setPV(BFPNames::ImageModeStr,
                  (pBV(BFPNames::BCImagesOnStr) && !pBV(BFPNames::ElastoEnStr)) ? (int)MODE_LCRB : (int)modeType);
            setPV(BFPNames::SyncModeStr, (int)Sync_C);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, true);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeAV:
            setPV(BFPNames::ImageModeStr, (int)MODE_B);
            setPV(BFPNames::SyncModeStr, (int)Sync_D);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            setPV(BFPNames::TriplexModeStr, true);
            break;
        case SystemScanModeBPW:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_D);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeColorPW:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CD);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModePowerPW:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CD);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeDPowerPW:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CD);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, true);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeCWD:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_D);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, true);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeCWDColorDoppler:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CD);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, true);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeCWDDirectionalPowerDoppler:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CD);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, true);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, true);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeCWDPowerDoppler:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CD);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, true);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeTissueDoppler:
            setPV(BFPNames::ImageModeStr, (int)modeType);
            setPV(BFPNames::SyncModeStr, (int)Sync_C);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, true);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeTissuePW:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CD);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, true);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeColorM:
            setPV(BFPNames::ImageModeStr, (int)MODE_M);
            setPV(BFPNames::SyncModeStr, (int)Sync_M);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeColorLRBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_BM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CM);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeColorUDBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CM);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModePDM:
            setPV(BFPNames::ImageModeStr, (int)MODE_M);
            setPV(BFPNames::SyncModeStr, (int)Sync_M);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModePDLRBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_BM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CM);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModePDUDBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CM);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeDPDM:
            setPV(BFPNames::ImageModeStr, (int)MODE_M);
            setPV(BFPNames::SyncModeStr, (int)Sync_M);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, true);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeDPDLRBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_BM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CM);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, true);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeDPDUDBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CM);
            setPV(BFPNames::ColorImageModeStr, (int)Color_PD);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::Bi_CPAStr, true);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeTDIM:
            setPV(BFPNames::ImageModeStr, (int)MODE_M);
            setPV(BFPNames::SyncModeStr, (int)Sync_M);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, true);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeTDILRBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_BM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CM);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, true);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeTDIUDBM:
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CM);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, true);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, true);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeE:
            // to do need edit
            setPV(BFPNames::BCImagesOnStr, true); // 设置bcimageon会修改imagemode
                                                  //            setPV(BFPNames::ImageModeStr, (int)MODE_LCRB);
            setPV(BFPNames::SyncModeStr, (int)Sync_C);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF); // to do colormap使用的压力柱的map
            setPV(BFPNames::ElastoEnStr, true);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            break;
        case SystemScanModeFourDLive:
            setPV(BFPNames::ImageModeStr, (int)MODE_B);
            setPV(BFPNames::SyncModeStr, (int)Sync_None);
            setPV(BFPNames::ColorImageModeStr, (int)Color_None);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, true);
            break;
        case SystemScanModeCP:
            break;
        case SystemScanModeMVI:
            if (pBV(BFPNames::BMVIBImagesStr) && !pBV(BFPNames::IsDopplerScanLineVisibleStr))
            {
                setPV(BFPNames::BCImagesOnStr, true);
                setPV(BFPNames::ImageModeStr, (int)MODE_LCRB);
            }
            else
            {
                setPV(BFPNames::BCImagesOnStr, false);
                setPV(BFPNames::ImageModeStr, (int)MODE_B);
            }
            setPV(BFPNames::SyncModeStr, (int)Sync_C);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            mviMode = true;
            break;
        case SystemScanModeMVIPW:
            setPV(BFPNames::BCImagesOnStr, false);
            setPV(BFPNames::ImageModeStr, (int)MODE_UDBM);
            setPV(BFPNames::SyncModeStr, (int)Sync_CD);
            setPV(BFPNames::ColorImageModeStr, (int)Color_CF);
            setPV(BFPNames::ElastoEnStr, false);
            setPV(BFPNames::TDIEnStr, false);
            setPV(BFPNames::CWEnStr, false);
            setPV(BFPNames::ColorMStr, false);
            setPV(BFPNames::FreeMModeStr, false);
            setPV(BFPNames::FourDStr, false);
            mviMode = true;
            break;
        default:
            break;
        }

        setPV(BFPNames::MVIModeStr, mviMode);

        if (!m_IsLayoutChange)
        {
            setPV(BFPNames::IsDopplerScanLineVisibleStr, false);
            setPV(BFPNames::IsCWDScanLineVisibleStr, false);
            setPV(BFPNames::IsMLineVisibleStr, false);
        }
    }
    //这里注释的原因是该信号未连接任何槽函数，也为了复用类"ImageDataTransfer"中的定时器 sjh
    //    if ((SystemScanMode)(value.toInt()) == SystemScanModeLRBM ||
    //            (SystemScanMode)(value.toInt()) == SystemScanModeM)
    //    {
    //        emit updateUnstableTime(1500);
    //    }
    if (!pBV(BFPNames::FourDStr) && !m_IsLayoutChange)
    {
        imageIntoUnstable();
    }

    parameter(BFPNames::IsUDBMStr)
        ->setEnabled(systemScanMode() == SystemScanModeUDBM || systemScanMode() == SystemScanModeLRBM ||
                     systemScanMode() == SystemScanModeUDFreeM || systemScanMode() == SystemScanModeLRFreeM);

    controlBCImagesOn();
    controlECGPos();

    // temporary
    controlRotationEn();
}

void BeamFormerBase::onVesselThresholdChanging(const QVariant& value)
{
    m_BlockDataSender->sendParaBlockDataGroup(
        ProbeBlockDataSet::paraName(ProbeBlockDataSet::iImage),
        m_BlockDataParaNameConverter->groupParaName(BFPNames::VesselThresholdStr),
        parameter(BFPNames::VesselThresholdStr)->valueCount() * pIV(BFPNames::XContrastValueStr) + value.toInt());
}

void BeamFormerBase::onLineDensityPDChanging(const QVariant& value)
{
}

void BeamFormerBase::onGettingLineDensityPDText(QString& value)
{
}

void BeamFormerBase::onLineDensityDPDChanging(const QVariant& value)
{
}

void BeamFormerBase::onGettingLineDensityDPDText(QString& value)
{
}

void BeamFormerBase::onScanWidthChanging(const QVariant& value)
{
    if (zoomOn())
    {
        return;
    }

    // scanwidth is not controltable parameter, so call beginSend and endSend manually.
    ControlTableSender ct(m_ControlTable);

    BFScanWidthParameter sp(curProbe(), value.toInt(), isSupportAnyDensity(), pIV(BFPNames::B_RX_LNUMStr));

    if (!isSupportAnyDensity())
    {
        //非任意线密度
        setPV(BFPNames::StartScanLineColorStr, sp.startLine());
        setPV(BFPNames::StopScanLineColorStr, sp.stopLine());
    }
    else
    {
        //任意线密度
        //此方法原来写在BeamFormerLineDensity::onScanWidthChanging中，
        // StartScanLineColor、StopScanLineColor的设置在StartLine、StopLine之后
        //导致的问题是C模式调节ScanWidth,当扫查宽度小于ROI框的宽度时，图像卡住，移动一下ROI框就好了
        //卡住的原因是,自动调节ROI时,ZeusComplexParameter::onRoiTriggerCompleted函数不会触发
        //解决：这里按照以前的设计,将StartScanLineColor、StopScanLineColor的设置放到StartLine、StopLine之前
        updateColorRegionLine();
    }
    setPV(BFPNames::StartLineStr, sp.startLine());
    setPV(BFPNames::StopLineStr, sp.stopLine());

    controlTrapezoidalMode();
    controlScpdOn();
    sendFreqRelatedParas();
}

void BeamFormerBase::onScanWidthChanged(const QVariant& value)
{
    Q_UNUSED(value);
    int scanWidth = m_SonoParameters->parameter(BFPNames::ScanWidthStr)->text().split('%').at(0).toInt();
    if (scanWidth < 100)
    {
        parameter(BFPNames::CurvedExapandingStr)->setEnabled(false);
    }
    else
    {
        parameter(BFPNames::CurvedExapandingStr)->setEnabled(true);
    }
    imageShapeIntoUnstable();
}

void BeamFormerBase::onGettingScanWidthText(QString& value)
{
    BFScanWidthParameter sp(curProbe(), pIV(BFPNames::ScanWidthStr), isSupportAnyDensity(),
                            pIV(BFPNames::B_RX_LNUMStr));

    value = QString("%1 %").arg(qRound((qreal)sp.percent() * 100));
}

void BeamFormerBase::onGettingScanWidthMax(int& value)
{
    value = Setting::instance().defaults().scanWidthLevel() - 1;
}

void BeamFormerBase::onFocusNumShowChanging(const QVariant& value)
{
    if (pIV(BFPNames::SyncModeStr) == Sync_None || pIV(BFPNames::SystemScanModeStr) == SystemScanModeAV)
    {
        setPV(BFPNames::FocusNumBStr, value);
    }
}

void BeamFormerBase::onGettingFocusNumShowShowValue(QVariant& value)
{
    value = value.toInt() + 1;
}

void BeamFormerBase::onGettingFocusNumShowMax(int& value)
{
    if (pIV(BFPNames::SyncModeStr) == Sync_None || pIV(BFPNames::SystemScanModeStr) == SystemScanModeAV)
    {
        value = pMax(BFPNames::FocusNumBStr);
    }
}

void BeamFormerBase::onDynStartDepthChanging(const QVariant& value)
{
    m_RelatedParasController->setDynStartDepth(value);
}

void BeamFormerBase::onDynStartDBChanging(const QVariant& value)
{
    m_RelatedParasController->setDynStartDB(value);
}

void BeamFormerBase::onGettingDynStartDBShowValue(QVariant& value)
{
    value = FreqSetting::dynamicRange2DB(value.toInt());
}

void BeamFormerBase::onDynEndDepthChanging(const QVariant& value)
{
    m_RelatedParasController->setDynEndDepth(value);
}

void BeamFormerBase::onDynEndDBChanging(const QVariant& value)
{
    m_RelatedParasController->setDynEndDB(value);
}

void BeamFormerBase::onGettingDynEndDBShowValue(QVariant& value)
{
    value = FreqSetting::dynamicRange2DB(value.toInt());
}

void BeamFormerBase::onDRDiffWithCompoundOnChanging(const QVariant& value)
{
    m_RelatedParasController->setDRDiffWithCompoundOn(value);
}

void BeamFormerBase::onDRDiffWithSraOnChanging(const QVariant& value)
{
    m_RelatedParasController->setDRDiffWithSraOn(value);
}

void BeamFormerBase::onDRDiffWithThiOnChanging(const QVariant& value)
{
    m_RelatedParasController->setDRDiffWithThiOn(value);
}

void BeamFormerBase::onPersistDiffWithCpdOnChanging(const QVariant& value)
{
    m_RelatedParasController->setPersistDiffWithCpdOn(value);
    parameter(BFPNames::FrameAvgStr)->update();
}

void BeamFormerBase::onPersistDiffWithSraOnChanging(const QVariant& value)
{
    m_RelatedParasController->setPersistDiffWithSraOn(value);
    parameter(BFPNames::FrameAvgStr)->update();
}

void BeamFormerBase::onPersistentDeltaTHIChanging(const QVariant& value)
{
    m_RelatedParasController->setPersistentDeltaTHIStr(value);
    parameter(BFPNames::FrameAvgStr)->update();
}

void BeamFormerBase::onPersistentDeltaCFMChanging(const QVariant& value)
{
    m_RelatedParasController->setPersistentDeltaCFMStr(value);
    parameter(BFPNames::FrameAvgStr)->update();
}

void BeamFormerBase::onPersistentDeltaPDChanging(const QVariant& value)
{
    m_RelatedParasController->setPersistentDeltaPDStr(value);
    parameter(BFPNames::FrameAvgStr)->update();
}

void BeamFormerBase::onPersistentDeltaSNChanging(const QVariant& value)
{
    m_RelatedParasController->setPersistentDeltaSNStr(value);
    parameter(BFPNames::FrameAvgStr)->update();
}

void BeamFormerBase::onPersistentDeltaTDIChanging(const QVariant& value)
{
    m_RelatedParasController->setPersistentDeltaTDIStr(value);
    parameter(BFPNames::FrameAvgStr)->update();
}

void BeamFormerBase::onPersistentDeltaMVIChanging(const QVariant& value)
{
    m_RelatedParasController->setPersistentDeltaMVIStr(value);
    parameter(BFPNames::FrameAvgStr)->update();
}

void BeamFormerBase::onBeforeScpdOnChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
}

void BeamFormerBase::onScpdOnChanging(const QVariant& value)
{
    if (value.toBool())
    {
        if (isScpdEnabled())
        {
            int scpdValue = defaultScpd();
            int presetScpdValue = m_SonoParameters->currentPreset().value(BFPNames::ScpdStr).toInt();
            int spacialCompound = spacialCompoundInPresets(m_SonoParameters->currentPreset());

            if (presetScpdValue > 0)
            {
                scpdValue = presetScpdValue;
            }
            else if (spacialCompound > 0)
            {
                scpdValue = spacialCompound;
            }

            setPV(BFPNames::ScpdStr, scpdValue);

            controlFocusNumB();
        }
    }
    else if (!pBV(BFPNames::TrapezoidalModeStr) && !pBV(BFPNames::CurvedExapandingStr))
    {
        setPV(BFPNames::ScpdStr, 0);
    }
    if (isFixedDepth())
    {
        parameter(BFPNames::CQYZLevelStr)->update();
    }
    parameter(BFPNames::FcpdOnStr)->setEnabled(isFcpdControlEnabled());
    parameter(BFPNames::DetailWeightBinStr)->update();
}

void BeamFormerBase::onFcpdOnChanging(const QVariant& value)
{
    if (isFcpdEnabled())
    {
        setPV(BFPNames::FrequencyCompoundingStr, value);
    }

    if (pIV(BFPNames::SraGainColorDeltaStr) != 0 && (syncMode() == Sync_C || pBV(BFPNames::ColorMStr)) &&
        pBV(BFPNames::FcpdOnStr))
    {
        parameter(BFPNames::GainStr)->update();
    }

    parameter(BFPNames::DetailWeightBinStr)->update();
}

void BeamFormerBase::onBeforeTrapezoidalModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
}

void BeamFormerBase::onTrapezoidalModeChanging(const QVariant& value)
{
    ControlTableSender ctSender(m_ControlTable);
    FreqRelatedParasSender fpSender(m_RelatedParasController);

    if (value.toBool())
    {
        if (isTrapezoidalModeEnabled())
        {
            controlScpdOn();
            controlFcpdOn();

            setPV(BFPNames::ScpdTrapeStr, 1);

            controlFocusNumB();
        }
    }
    else
    {
        setPV(BFPNames::ScpdTrapeStr, 0);

        controlScpdOn();
        controlFcpdOn();
    }

    controlBSteer();

    int steer = (value.toBool() ? pIV(BFPNames::TrapezoidalCPDSteerStr) : pIV(BFPNames::CPDSteerStr));
    sendCpdSteerCTValue(steer);
    m_BlockDataSender->sendParaBlockDataGroup(
        curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr), steer);

    m_StateManager->setIsZoomEnabled(isZoomEnabled());
}

void BeamFormerBase::onTrapezoidalModeChanged(const QVariant& value)
{
    Q_UNUSED(value);
    imageShapeIntoUnstable();
}

void BeamFormerBase::onVirtualVertexTrapezoidalModeChanging(const QVariant& value)
{
    setPV(BFPNames::PA_VERT_DIST_EnableStr, value.toBool());
}

void BeamFormerBase::onPA_VERT_DIST_EnableChanging(const QVariant& value)
{
    ControlTableSender ctSender(m_ControlTable);

    if (value.toBool())
    {

        if (pIV(BFPNames::BSteeringAngleCodingStr) != 10)
        {
            setPV(BFPNames::BSteeringAngleCodingStr, 10); //虚拟顶点打开默认设置为10，目前不支持调节
        }
        else
        {
            parameter(BFPNames::BSteeringAngleCodingStr)->update();
        }
    }
    else
    {
        int steer = pIV(BFPNames::CPDSteerStr);
        sendCpdSteerCTValue(steer);
        m_BlockDataSender->sendParaBlockDataGroup(
            curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CPDSteerStr), steer);
    }
    if (m_LineDensityParametersHandler)
    {
        m_LineDensityParametersHandler->changeProbeInterval();
        m_LineDensityParametersHandler->calculateBlockDataAndSend();
    }
    m_StateManager->setIsZoomEnabled(isZoomEnabled());
}

void BeamFormerBase::onZoomMultiIndexChanging(const QVariant& value)
{
    m_DepthParameters->setZoomMulti(
        BFDepthParameters::zoomMulti(value.toInt(), parameter(BFPNames::ZoomMultiIndexStr)->max()));
}

void BeamFormerBase::onScrollChanging(const QVariant& value)
{
    setPV(BFPNames::IsScrollStr, (value.toInt() > 0) ? true : false);
    int ctV;
    onGettingScrollControlTableValue(value, ctV);
    setPV(BFPNames::ScrollDepthMMStr, ctV * BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)));
    setPV(BFPNames::StartDepthMMStr, startDepthMM());
    changeProbeDis();
}

void BeamFormerBase::onGettingScrollControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = value.toInt() * pIV(BFPNames::CQYZStr) * 8;
}

void BeamFormerBase::onActiveBChanging(const QVariant& value)
{
    // m_CurrentIndex = value.toInt();
    // m_LineDensityParametersHandler->setCurrentIndex(m_CurrentIndex);
    if (imageMode() == MODE_2B || imageMode() == MODE_4B)
    {
        m_StateManager->setActiveIndex(value.toInt());
        //使用同步发送,冻结切换时usb线程非running状态
        ControlTableSyncSender cs(m_ControlTable);
        //下发控制表中图像模式的值
        //        parameter(BFPNames::ImageModeStr)->update();
        parameter(BFPNames::ImageModeStr)->setDirectValue(pV(BFPNames::ImageModeStr));
        if (!isFrozen())
        {
#ifdef SYS_APPLE
            QList<QVariant> currentLayoutInfos = pV(BFPNames::ImageGLRenderLayoutsStr).toList();
            if (m_ImageRenderLayoutRects->modifyRenderLayoutInfo(currentLayoutInfos, systemScanMode(), value.toInt()))
            {
                setPV(BFPNames::ImageGLRenderLayoutsStr, currentLayoutInfos);
            }
#else
            QList<QVariant> currentLayoutInfos = pV(BFPNames::ImageRenderLayoutsStr).toList();
            if (m_ImageRenderLayoutRects->modifyRenderLayoutInfo(currentLayoutInfos, systemScanMode(), value.toInt()))
            {
                setPV(BFPNames::ImageRenderLayoutsStr, currentLayoutInfos);
            }
#endif
        }
    }
}

void BeamFormerBase::onTGCChanging(const QVariant& value)
{
    ControlTableSender ct(m_ControlTable);

    m_PostProcessHandler->onTGCChanging(value);
}

void BeamFormerBase::onLineDataModeChanging(const QVariant& value)
{
    Q_UNUSED(value);
    // m_BFIODevice->setReadingSize(getIOReadSize());
}

void BeamFormerBase::onSraGainColorDeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if ((syncMode() == Sync_C || pBV(BFPNames::ColorMStr)) && pBV(BFPNames::FcpdOnStr))
    {
        parameter(BFPNames::GainStr)->update();
    }
}

void BeamFormerBase::onBeforeIsUDBMChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    Q_UNUSED(newValue);
}

void BeamFormerBase::onIsUDBMChanging(const QVariant& value)
{
    if (systemScanMode() == SystemScanModeUDBM || systemScanMode() == SystemScanModeLRBM)
    {
        if (value.toBool())
        {
            setPV(BFPNames::SystemScanModeStr, SystemScanModeUDBM);
        }
        else
        {
            setPV(BFPNames::SystemScanModeStr, SystemScanModeLRBM);
        }
    }
    else if (systemScanMode() == SystemScanModeUDFreeM || systemScanMode() == SystemScanModeLRFreeM)
    {
        if (value.toBool())
        {
            setPV(BFPNames::SystemScanModeStr, SystemScanModeUDFreeM);
        }
        else
        {
            setPV(BFPNames::SystemScanModeStr, SystemScanModeLRFreeM);
        }
    }

    controlMDisplayFormat(value.toBool());
}

void BeamFormerBase::onGettingIsUDBMText(QString& value)
{
    value = pBV(BFPNames::IsUDBMStr) ? QString("Vertical") : QString("Horizontal");
}

void BeamFormerBase::onXContrastValueChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (ModelConfig::instance().value(ModelConfig::PixelLenStep, 1).toInt() == 1)
    {
        parameter(BFPNames::VesselThresholdStr)->update();
    }
}

void BeamFormerBase::onEnhanceChanging(const QVariant& value)
{
    Q_UNUSED(value);
    parameter(BFPNames::GainStr)->update();
}

void BeamFormerBase::onRotationChanging(const QVariant& value)
{
    m_PostProcessHandler->onRotationChanging(value);
}

void BeamFormerBase::onRotationChanged(const QVariant& value)
{
    Q_UNUSED(value);
    imageShapeIntoUnstable();
}

void BeamFormerBase::onGettingRotationText(QString& value)
{
    m_PostProcessHandler->onGettingRotationText(value);
}

void BeamFormerBase::onGettingFourDDirectionSetText(QString& value)
{
    int rotation = pIV(BFPNames::FourDDirectionSetStr);
    value = QString("%1°").arg(rotation);
}
void BeamFormerBase::onGettingRotationControlTableValue(const QVariant& value, int& controlTableValue)
{
    controlTableValue = value.toInt() / 90;
}

void BeamFormerBase::onGettingRotationPreset(QVariant& value)
{
    if (m_RotationBak != -1)
    {
        value = m_RotationBak;
    }
}

void BeamFormerBase::onADFreqMHzChanging(const QVariant& value)
{
    m_DepthParameters->setPixelLen(BFADFreqParameter::pixelLenMM(value.toInt()) /
                                   pFV(BFPNames::FixedSWImageZoomCofStr) * pFV(BFPNames::ProbeDSCImageZoomCofStr));
}

void BeamFormerBase::onImageRectsChanging(const QVariant& value)
{
    Q_UNUSED(value)
    SystemScanMode mode = systemScanMode();
    int layoutType =
        (pBV(BFPNames::BCImagesOnStr) || pBV(BFPNames::ElastoEnStr)) ? (int)Layout_1x1 : pIV(BFPNames::LayoutStr);

    switch (layoutType)
    {
    case Layout_1x2:
        mode = SystemScanMode2B;
        break;
    case Layout_2x2:
        mode = SystemScanMode4B;
        break;
    default:
        break;
    }
    setPV(BFPNames::ImageLayoutNumStr, m_ImageRenderLayoutRects->currentModeLayoutNum(mode));
}

void BeamFormerBase::onImageSizeChanging(const QVariant& value)
{
    m_DepthParameters->setImageHeight(value.toSize().height());
}

void BeamFormerBase::onFCA_DeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    setPV(BFPNames::FCA_GammaStr,
          (int)(256.0f * (pIV(BFPNames::FCA_AlphaStr) / 10.0f / (255.0f - pIV(BFPNames::FCA_DeltaStr)))));
}

void BeamFormerBase::onFCA_AlphaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    onFCA_DeltaChanging(pV(BFPNames::FCA_DeltaStr));
}

void BeamFormerBase::onTriplexModeChanging(const QVariant& value)
{
    setPV(BFPNames::TriplexRefreshStr, value);

    if (value.toBool())
    {
        //降功耗版工程在三同步模式下是频谱刷新优先，因此在三同步开启时(byte_13[3]),频谱(byte_6[3])应当立即关闭
        //由于现有方案中退出三同步，不是刷新频谱，因此无需再控制
        setPV(BFPNames::FreqSpectrumStr, false);
        if (m_TriplexModeHelper != NULL)
        {
            m_TriplexModeHelper->initTriplexMode();
        }
        parameter(BFPNames::Triplex_DummyCountStr)->update();
    }
}

void BeamFormerBase::onTriplexModeChanged(const QVariant& value)
{
    if (!value.toBool())
    {
        if (m_TriplexModeHelper != NULL)
        {
            sendBTxParas();
            m_TriplexModeHelper->leaveTripleMode();
        }
    }
    else
    {
        if (m_TriplexModeHelper != NULL)
        {
            if (pIV(BFPNames::SystemScanModeStr) != SystemScanModeAV)
            {
                sendTriplexTxParas();
            }
            m_TriplexModeHelper->enterTriplexMode();
        }
    }

    controlCFMVelLevel();
    controlAcousticPower();
    controlAccCount();
    controlPWTDITMParas();
    controlCTMPDTDIParas();
    controlTMTDICWDParas();
    controlPWTMParas();
    controlTGCCTValue();
    controlTransmitCTValue();
    controlSoundOpened(ISoundController::General);
    controlFrameScapeEnable();

    if ((syncMode() & Sync_C) == Sync_C)
    {
        QString gainParaStr = BFPNames::GainColorStr;

        if (pBV(BFPNames::MVIModeStr))
        {
            gainParaStr = BFPNames::GainMVIStr;
        }
        else
        {
            gainParaStr = (colorImageMode() == Color_PD)
                              ? (isSonoNeedleOn() ? BFPNames::GainSNStr : BFPNames::GainPDStr)
                              : BFPNames::GainColorStr;
        }

        parameter(gainParaStr)->update();
    }
}

void BeamFormerBase::onTriplexModeTDIChanging(const QVariant& value)
{
    onTriplexModeChanging(value);
}

void BeamFormerBase::onBCImagesOnChanging(const QVariant& value)
{
    m_PostProcessHandler->onBCImagesOnChanging(value);

    if (value.toBool())
    {
        m_IsLayoutChange = true;
        imageIntoUnstable();
    }
    else
    {
        if (imageMode() == MODE_LCRB || imageMode() == MODE_LBRC)
        {
            imageIntoUnstable();
        }
        m_IsLayoutChange = false;
    }

    //    controlImageZoomCoef();
}

void BeamFormerBase::onBi_CPAChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlColorImageModeParas();
    controlColorInvert();
}

void BeamFormerBase::onTDIEnChanging(const QVariant& value)
{
    if (!value.toBool())
    {
        isSonoNeedleOn() ? parameter(BFPNames::ColorLineDensitySNStr)->update()
                         : parameter(BFPNames::ColorLineDensityStr)->update();
        parameter(BFPNames::FlowStr)->update();
        parameter(BFPNames::DSampleRateDopStr)->update();
        parameter(BFPNames::SampleVolumeStr)->update();
        parameter(BFPNames::CPulseNumStr)->update();
    }
    else
    {
        parameter(BFPNames::TDILineDensityStr)->update();
        parameter(BFPNames::FlowTDIStr)->update();
        parameter(BFPNames::DSampleRateDopTDIStr)->update();
        parameter(BFPNames::SampleVolumeTDIStr)->update();
        /*
            [Apple][bug：系统反馈]进入TDI,冻结解冻后，血流变少的问题

            1、解决方案：比较控制表参数发现是CPulseNum/TDIPulseNum参数的值变小了，
                追踪CPulseNum参数其实是Color频率里面的参数。TDI是单独的频率文件，里面的参数名是CPulseNum；
                所以原来TDIPulseNum的参数需要去掉
            2、影响范围：
                TDI模式冻结解冻
        */
        parameter(BFPNames::CPulseNumStr)->update();
    }

    // TDI改变会根据BWGainDelta参数去改变黑白Gain,所以这里update一下
    parameter(pBV(BFPNames::THIStr) ? BFPNames::GainThiStr : BFPNames::GainStr)->update();

    controlWaveVelocity();
    controlPWTDITMParas();
    controlTMTDICWDParas();
    controlCTMPDTDIParas();
    controlTDICWDParas();
    controlColorImageModeParas();
    controlColorFreqIndex();
    controlDopFreqIndex();
    controlColorInvert();
}

void BeamFormerBase::onCWEnChanging(const QVariant& value)
{
    Q_UNUSED(value);
    controlWaveVelocity();
    controlTMTDICWDParas();
    controlTDICWDParas();
    controlDopFreqIndex();
    controlPWTDITMParas();
    sendCWFocusBlockDataGroup();
    sendCWDynamicRangeBlockDataGroup();
    controlECGDly();
}

void BeamFormerBase::onColorMChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pIV(BFPNames::SraGainColorDeltaStr) != 0)
    {
        parameter(BFPNames::GainStr)->update();
    }

    controlHighDensity();
    controlAcousticPower();
    controlWaveVelocity();
    controlTGCCTValue();
    //进入ColorM模式时，ROI的左右线号要缩小到MScanLine的左右附近区域，from yangcheng
    parameter(BFPNames::StartLineColorStr)->update();
    parameter(BFPNames::StopLineColorStr)->update();
    parameter(BFPNames::ThiModeStr)->update();
    if (pBV(BFPNames::TDIEnStr))
    {
        parameter(BFPNames::GainTDIStr)->update();
    }
}

void BeamFormerBase::onPixelRatioChanging(const QVariant& value)
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::PixelRatioShowStr, value);
    }
}

void BeamFormerBase::onPixelRatioTMChanging(const QVariant& value)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::PixelRatioShowStr, value);
    }
}

void BeamFormerBase::onPixelRatioShowChanging(const QVariant& value)
{
    setPV(pBV(BFPNames::TriplexModeStr) ? BFPNames::PixelRatioTMStr : BFPNames::PixelRatioStr, value);
}

void BeamFormerBase::onPWEnhanceChanging(const QVariant& value)
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::PWEnhanceShowStr, value);
    }
}

void BeamFormerBase::onPWEnhanceTMChanging(const QVariant& value)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::PWEnhanceShowStr, value);
    }
}

void BeamFormerBase::onPWEnhanceShowChanging(const QVariant& value)
{
    setPV(pBV(BFPNames::TriplexModeStr) ? BFPNames::PWEnhanceTMStr : BFPNames::PWEnhanceStr, value);
}

void BeamFormerBase::onPWDynamicRangeChanging(const QVariant& value)
{
    m_PostProcessHandler->onPWDynamicRangeChanging(value);
}

void BeamFormerBase::onGettingPWDynamicRangeShowValue(QVariant& value)
{
    m_PostProcessHandler->onGettingPWDynamicRangeShowValue(value);
}

void BeamFormerBase::onPWDynamicRangeTMChanging(const QVariant& value)
{
    m_PostProcessHandler->onPWDynamicRangeTMChanging(value);
}

void BeamFormerBase::onGettingPWDynamicRangeTMShowValue(QVariant& value)
{
    m_PostProcessHandler->onGettingPWDynamicRangeTMShowValue(value);
}

void BeamFormerBase::onPWDynamicRangeShowChanging(const QVariant& value)
{
    m_PostProcessHandler->onPWDynamicRangeShowChanging(value);
}

void BeamFormerBase::onGettingPWDynamicRangeShowShowValue(QVariant& value)
{
    m_PostProcessHandler->onGettingPWDynamicRangeShowShowValue(value);
}

void BeamFormerBase::onGettingPWDynamicRangeTDIShowValue(QVariant& value)
{
    m_PostProcessHandler->onGettingPWDynamicRangeTDIShowValue(value);
}

void BeamFormerBase::onGettingPWDynamicRangeCWDShowValue(QVariant& value)
{
    m_PostProcessHandler->onGettingPWDynamicRangeCWDShowValue(value);
}

void BeamFormerBase::onDummyEnSampleChanging(const QVariant& value)
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::DummyEnSampleShowStr, value);
    }
}

void BeamFormerBase::onDummyEnSampleTMChanging(const QVariant& value)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::DummyEnSampleShowStr, value);
    }
}

void BeamFormerBase::onDummyEnSampleShowChanging(const QVariant& value)
{
    setPV(pBV(BFPNames::TriplexModeStr) ? BFPNames::DummyEnSampleTMStr : BFPNames::DummyEnSampleStr, value);
}

void BeamFormerBase::onCFMVelLevelChanging(const QVariant& value)
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::CFMVelLevelShowStr, value);
    }
}

void BeamFormerBase::onGettingCFMVelLevelMax(int& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onCFMVelLevelTMChanging(const QVariant& value)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::CFMVelLevelShowStr, value);
    }
}

void BeamFormerBase::onGettingCFMVelLevelTMMax(int& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onCFMVelLevelShowChanging(const QVariant& value)
{
    setPV(pBV(BFPNames::TriplexModeStr) ? BFPNames::CFMVelLevelTMStr : BFPNames::CFMVelLevelStr, value);
}

void BeamFormerBase::onGettingCFMVelLevelShowMax(int& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onAudioFilterCoefSelChanging(const QVariant& value)
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::AudioFilterCoefSelShowStr, value);
    }
}

void BeamFormerBase::onAudioFilterCoefSelTMChanging(const QVariant& value)
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::AudioFilterCoefSelShowStr, value);
    }
}

void BeamFormerBase::onAudioFilterCoefSelShowChanging(const QVariant& value)
{
    setPV(pBV(BFPNames::TriplexModeStr) ? BFPNames::AudioFilterCoefSelTMStr : BFPNames::AudioFilterCoefSelStr, value);
}

void BeamFormerBase::onBFPipelineNumberChanging(const QVariant& value)
{
    ModelSettings::instance().setValue(ModelSettings::BFPipelineNumber, value);
}

void BeamFormerBase::onGettingCWeightedCurveTypeText(QString& value)
{
    value = weightedCurveTypeText(BFPNames::CWeightedCurveTypeStr);
}

void BeamFormerBase::onGettingNeedleWeightedCurveTypeText(QString& value)
{
    value = weightedCurveTypeText(BFPNames::NeedleWeightedCurveTypeStr);
}

void BeamFormerBase::onGettingDWeightedCurveTypeText(QString& value)
{
    value = weightedCurveTypeText(BFPNames::DWeightedCurveTypeStr);
}

void BeamFormerBase::onFScpdChanging(const QVariant& value)
{
    if (!pBV(BFPNames::ScpdOnStr) && pBV(BFPNames::FcpdOnStr))
    {
        if (value.toBool())
        {
            setPV(BFPNames::CPDSteerStr, 0);
        }
    }
}

void BeamFormerBase::onTransmitStepDisChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendBTxParas();
}

void BeamFormerBase::onReceiveFocusDisChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendBRvParas();
}

void BeamFormerBase::onReceiveUnitVariFocusNumChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendBRvParas();
}

void BeamFormerBase::onCfmTransmitStepDisChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendCfmTxParas();
}

void BeamFormerBase::onCfmReceiveFocusDisChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendCfmRvParas();
}

void BeamFormerBase::onCfmReceiveUnitVariFocusNumChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendCfmRvParas();
}

void BeamFormerBase::onDopTransmitStepDisChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendDopTxParas();
}

void BeamFormerBase::onDopReceiveFocusDisChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendDopRvParas();
}

void BeamFormerBase::onDopReceiveUnitVariFocusNumChanging(const QVariant& value)
{
    Q_UNUSED(value);
    sendDopRvParas();
}

void BeamFormerBase::onMaxSteeringAngleChanging(const QVariant& value)
{
    Q_UNUSED(value);
    isSonoNeedleOn() ? parameter(BFPNames::SteeringAngleSNStr)->update()
                     : parameter(BFPNames::SteeringAngleStr)->update();
}

void BeamFormerBase::onGettingMaxSteeringAngleShowValue(QVariant& value)
{
    int iv = pIV(BFPNames::MaxSteeringAngleStr);
    if (iv >= 0 && iv < m_StaticParameters->steeringAngles().count())
    {
        value = m_StaticParameters->steeringAngles().at(iv);
    }
}

void BeamFormerBase::onCTxPulseDutyChanging(const QVariant& value)
{
    if (!pBV(BFPNames::FreqSpectrumStr) && !pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::CTxPulseDutyBinStr, value);
    }
}

void BeamFormerBase::onCTxPulseDuty_PWChanging(const QVariant& value)
{
    if (pBV(BFPNames::FreqSpectrumStr) || pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::CTxPulseDutyBinStr, value);
    }
}

void BeamFormerBase::onCfmMaxTransmitApertureControlChanging(const QVariant& value)
{
    if (!pBV(BFPNames::FreqSpectrumStr) && !pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::CfmMaxTransmitApertureControlBinStr, value);
    }
}

void BeamFormerBase::onCfmMaxTransmitApertureControl_PWChanging(const QVariant& value)
{
    if (pBV(BFPNames::FreqSpectrumStr) || pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::CfmMaxTransmitApertureControlBinStr, value);
    }
}

void BeamFormerBase::onCfmMinTransmitApertureControlChanging(const QVariant& value)
{
    if (!pBV(BFPNames::FreqSpectrumStr) && !pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::CfmMinTransmitApertureControlBinStr, value);
    }
}

void BeamFormerBase::onCfmMinTransmitApertureControl_PWChanging(const QVariant& value)
{
    if (pBV(BFPNames::FreqSpectrumStr) || pBV(BFPNames::TriplexModeStr))
    {
        setPV(BFPNames::CfmMinTransmitApertureControlBinStr, value);
    }
}

void BeamFormerBase::onThiModeChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onGettingThiModeControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    if (curProbe().IsPhasedArray && pBV(BFPNames::ColorMStr))
    {
        controlTableValue = 0;
    }

    m_BlockDataSender->sendParaBlockDataGroup(
        curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::ThiModeStr), controlTableValue);
}

void BeamFormerBase::onFilterCpdChanging(const QVariant& value)
{
    m_BlockDataSender->sendParaBlockDataGroup(
        curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::FilterCpdStr), value.toBool() ? 1 : 0);
}

void BeamFormerBase::onBeforeWallFilterSettingChanged(const QVariant& oldValue, QVariant& newValue)
{
    QList<int> settings = VariantUtil::variant2Ints(newValue, VariantUtil::String);
    QVariant strs = VariantUtil::ints2Variant(settings, VariantUtil::String);
    int settingCount = parameter(BFPNames::WallFilterColorStr)->valueCount();

    if (strs != newValue || settings.count() != settingCount)
    {
        settings = VariantUtil::variant2Ints(oldValue, VariantUtil::String);
        strs = VariantUtil::ints2Variant(settings, VariantUtil::String);
        if (settings.count() == settingCount && oldValue == strs)
        {
            newValue = oldValue;
        }
        else
        {
            newValue = ParaListSettings::defaultVariantIds(settingCount, VariantUtil::String);
        }
    }
}

void BeamFormerBase::onGettingWallFilterSettingIsValidValue(const QVariant& value, bool& valid)
{
    QVariant oldValue = value;
    QVariant newValue = value;
    onBeforeWallFilterSettingChanged(value, newValue);
    valid = (newValue == oldValue);
}

void BeamFormerBase::onBeforeCPAWallFilterSettingChanged(const QVariant& oldValue, QVariant& newValue)
{
    QList<int> settings = VariantUtil::variant2Ints(newValue, VariantUtil::String);
    QVariant strs = VariantUtil::ints2Variant(settings, VariantUtil::String);
    int settingCount = parameter(BFPNames::HDCPAWallFilterStr)->valueCount();

    if (strs != newValue || settings.count() != settingCount)
    {
        settings = VariantUtil::variant2Ints(oldValue, VariantUtil::String);
        strs = VariantUtil::ints2Variant(settings, VariantUtil::String);
        if (settings.count() == settingCount && oldValue == strs)
        {
            newValue = oldValue;
        }
        else
        {
            newValue = ParaListSettings::defaultVariantIds(settingCount, VariantUtil::String);
        }
    }
}

void BeamFormerBase::onGettingCPAWallFilterSettingIsValidValue(const QVariant& value, bool& valid)
{
    QVariant oldValue = value;
    QVariant newValue = value;
    onBeforeCPAWallFilterSettingChanged(value, newValue);
    valid = (newValue == oldValue);
}

void BeamFormerBase::onWallFilterSettingChanging(const QVariant& value)
{
    m_BlockDataSender->sendParaBlockDataGroup(
        curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::WallFilterSettingStr),
        VariantUtil::variant2Ints(value, VariantUtil::String));
}

void BeamFormerBase::onGettingAdapPostProcControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::THIStr))
    {
        controlTableValue += pIV(BFPNames::AdapPostProcDeltaStr);
    }
}

void BeamFormerBase::onAdapPostProcDeltaChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (pBV(BFPNames::THIStr))
    {
        parameter(BFPNames::AdapPostProcStr)->update();
    }
}

void BeamFormerBase::onGettingCPriorityControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (colorImageMode() == Color_PD) // PD DPD 固定为255
    {
        controlTableValue = 255;
    }
    else
    {
        controlTableValue = value.toInt();
    }
}

void BeamFormerBase::onIsDopplerScanLineVisibleChanging(const QVariant& value)
{
    if (value.toBool())
    {
        setPV(BFPNames::BCImagesOnStr, false);
    }
    controlBCImagesOn();
}

void BeamFormerBase::onIsCWDScanLineVisibleChanging(const QVariant& value)
{
    if (value.toBool())
    {
        setPV(BFPNames::BCImagesOnStr, false);
    }
    controlBCImagesOn();
}

void BeamFormerBase::onIsCenterLineVisibleChanging(const QVariant& value)
{
    if (value.toBool() && isBSteerOn())
    {
        setPV(BFPNames::BSteeringScanStr, 20);
    }
    controlBSteer();
}

void BeamFormerBase::onHprfEnChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onElastoEnChanging(const QVariant& value)
{
    if (value.toBool())
    {
        setPV(BFPNames::BSteeringScanStr, 20); //关闭BSteer
        setPV(BFPNames::TrapezoidalModeStr, false);
        setPV(BFPNames::VirtualVertexTrapezoidalModeStr, false);
        setPV(BFPNames::IsBiopsyVisibleStr, false);
        setPV(BFPNames::NeedleModeStr, false);

        parameter(BFPNames::ElastoColorLineDensityStr)->update();
        parameter(BFPNames::SampleRateDopElastoStr)->update();

        //在E模式下 ElastoGaussFilterSwitch = 0
        parameter(BFPNames::ElastoGaussFilterSwitchStr)->update();

        //所有探头进入弹性成像模式后，在E模式下，强制使用双波束方式
        parameter(BFPNames::ElastoMBColorStr)->update();
        parameter(BFPNames::ElastoMBStr)->update();

        //设置steering角度为0
        if (m_SteeringAngleBak == -1)
        {
            m_SteeringAngleBak = pIV(BFPNames::SteeringAngleStr);
        }
        setPV(BFPNames::SteeringAngleStr, 3);

        //设置Rotation为0
        if (m_RotationBak == -1)
        {
            m_RotationBak = pIV(BFPNames::RotationStr);
        }
        setPV(BFPNames::RotationStr, 0);
        onImageRectsChanging(QVariant());
    }
    else
    {
        if (isSonoNeedleOn())
        {
            parameter(BFPNames::ColorLineDensitySNStr)->update();
            parameter(BFPNames::SampleRateDopSNStr)->update();
        }
        else
        {
            parameter(BFPNames::ColorLineDensityStr)->update();
            parameter(BFPNames::SampleRateDopStr)->update();
        }
        parameter(BFPNames::GaussFilterSwitchStr)->update();
        parameter(BFPNames::MBColorStr)->update();
        parameter(BFPNames::MBStr)->update();
        if (m_SteeringAngleBak != -1)
        {
            setPV(BFPNames::SteeringAngleStr, m_SteeringAngleBak);
            m_SteeringAngleBak = -1;
        }

        if (m_RotationBak != -1)
        {
            setPV(BFPNames::RotationStr, m_RotationBak);
            m_RotationBak = -1;
        }
        // UTGC和ATGC直接使用了B的参数,退出时需要使用B的值更新一次
        FreqRelatedParasSender fpSender(m_RelatedParasController);
    }

    parameter(BFPNames::FreqIndexBStr)->setEnabled(!value.toBool());

    controlColorImageModeParas();
    controlCTMPDTDIParas();
    controlColorFreqIndex();
    controlColorInvert();
    controlAcousticPower();
    controlScanWidthEn();
    controlBiopsyEn();
    controlNeedleModeEn();
    controlBSteer();
    controlTrapezoidalMode();
    controlRotationEn();

    emit updateMiti();
}

void BeamFormerBase::onGettingKeyBoardLightLevelShowValue(QVariant& value)
{
    value = value.toInt() + 1;
}

void BeamFormerBase::onGettingFourDDynamicRangeShowValue(QVariant& value)
{
    value = value.toInt() * ModelConfig::instance()
                                .value(ModelConfig::DynamicRangeStep, Setting::instance().defaults().dynamicRangeStep())
                                .toInt() +
            ModelConfig::instance()
                .value(ModelConfig::DynamicRangeBase, Setting::instance().defaults().dynamicRangeBase())
                .toInt();
}

void BeamFormerBase::onFourDRenderChanging(const QVariant& value)
{
    Q_UNUSED(value);
    // reload renderModeId from FourDRenderMode.ini;
    // onFourDRenderModeIndexChanging(pV(BFPNames::FourDRenderModeIndexStr));
}

void BeamFormerBase::onGettingFourDRenderText(QString& value)
{
    switch (value.toInt())
    {
    case 0:
        value = "Surface";
        break;
    case 1:
        value = "Skeleton";
        break;
    case 2:
        value = "Vessel";
        break;
    case 3:
        value = "XRay";
        break;
    case 4:
        value = "Depth View";
        break;
    default:
        value = "Surface";
        break;
    }
}

void BeamFormerBase::onGettingFourDSmoothText(QString& value)
{
    switch (value.toInt())
    {
    case 0:
        value = "Off";
        break;
    case 1:
        value = "IE1";
        break;
    case 2:
        value = "IE2";
        break;
    case 3:
        value = "IE3";
        break;
    default:
        value = "IE1";
        break;
    }
}

void BeamFormerBase::onGettingFourDFrameRateText(QString& value)
{
    switch (value.toInt())
    {
    case 0:
        value = "Normal";
        break;
    case 1:
        value = "Fast";
        break;
    default:
        value = "Normal";
        break;
    }
}

void BeamFormerBase::onGettingFourDEnterModeText(QString& value)
{
    value = pBV(BFPNames::FourDEnterModeStr) ? "4D" : "3D";
}

void BeamFormerBase::onFourDPaletteChanging(const QVariant& value)
{
    Q_UNUSED(value);
    // setPV(BFPNames::FourDColorMapIndexStr, value);
}

void BeamFormerBase::onFourDAngleRatioIndexChanging(const QVariant& value)
{
    // read FourDAngleRatio.ini
    QSettings angleRatioSettings(Resource::fourDAngleRatioFileName(), QSettings::IniFormat);
    angleRatioSettings.beginReadArray("AngleRatio");
    angleRatioSettings.setArrayIndex(value.toInt());
    qint32 angleRatio = angleRatioSettings.value("AngleRatio").toInt();
    angleRatioSettings.endArray();
    setPV(BFPNames::FourDAngleRatioStr, angleRatio);
}

void BeamFormerBase::onFourDQualityIndexChanging(const QVariant& value)
{
#ifdef USE_4D
    int valueInt = value.toInt();
    Q_ASSERT(valueInt >= 0 &&
             valueInt < FourDParasContainer::get<FourDQualityPara>(FourDParasInfo::FourDQuality).parasCount());
    FourDQualityPara fourdQuality =
        FourDParasContainer::get<FourDQualityPara>(FourDParasInfo::FourDQuality).oneParas(valueInt);
#ifdef USE_DEBUG
    qDebug() << "onFourDQualityIndexChanging****************" << valueInt << "m_QualityValue,"
             << fourdQuality.m_QualityValue << "m_MultiBeamValue," << fourdQuality.m_MultiBeamValue << "m_DensityIndex,"
             << fourdQuality.m_DensityIndex;
#endif
    setPV(BFPNames::FourDQualityValueStr, fourdQuality.m_QualityValue);
    setPV(BFPNames::MBStr, fourdQuality.m_MultiBeamValue);
    setPV(BFPNames::HighDensityStr, fourdQuality.m_DensityIndex);
#endif
}
void BeamFormerBase::onFourDRoiRatioChanging(const QVariant& value)
{
    Q_UNUSED(value)
    // TODO set this value to 0 for debug use(beacuse no one remember how to change this value),
    // need to change to a property value in tk1 platform
    setPV(BFPNames::FourDRoiRatioStr, 0);
}

void BeamFormerBase::onFourDRoiZoomValueChanging(const QVariant& value)
{
    Q_UNUSED(value)
    // TODO set this value to 0.4 for debug use(beacuse no one remember how to change this value),
    // need to change to a property value in tk1 platform
    setPV(BFPNames::FourDRoiZoomValueStr, 0.4);
}

void BeamFormerBase::onFourDRectMinRatioChanging(const QVariant& value)
{
    Q_UNUSED(value)
    // TODO set this value to 0.3 for debug use(beacuse no one remember how to change this value),
    // need to change to a property value in tk1 platform
    setPV(BFPNames::FourDRectMinRatioStr, 0.3);
}

void BeamFormerBase::onFourDRenderModeIndexChanging(const QVariant& value)
{
    // read FourDRenderMode.ini
    QSettings fourDRenderModeSettings(Resource::fourDRenderModeFileName(), QSettings::IniFormat);
    int size = fourDRenderModeSettings.beginReadArray("RenderMode");
    if (value.toInt() >= size)
    {
        return;
    }
    fourDRenderModeSettings.setArrayIndex(value.toInt());

    QList<int> renderModeList;
    QStringList renderNameList = QStringList() << "SurfaceId"
                                               << "SkeletonId"
                                               << "VesselId"
                                               << "xRayId"
                                               << "DepthViewId"
                                               << "VirtualHDId";
    foreach (QString renderName, renderNameList)
    {
        int para = fourDRenderModeSettings.value(renderName).toInt();
        renderModeList.append(para);
    }
    fourDRenderModeSettings.endArray();

    QString groupParas = getRenderModeParas(renderModeList);
    setPV(BFPNames::FourDGroupParasStr, groupParas);
}

void BeamFormerBase::onFourDParasSettingIndexChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onGettingChisonFourDRenderText(QString& value)
{
    switch (value.toInt())
    {
    case 0:
        value = "Surface";
        break;
    case 1:
        value = "Silhouette";
        break;
    case 2:
        value = "Depth View";
        break;
    default:
        value = "Surface";
        break;
    }
}
void BeamFormerBase::onGettingChisonFourDMouseStateText(QString& value)
{
    switch (value.toInt())
    {
    case 0:
        value = "Loop";
        break;
    case 1:
        value = "CurvedLine";
        break;
    case 2:
        value = "Panning";
        break;
    default:
        value = "Light";
        break;
    }
}

void BeamFormerBase::onGettingVolumeText(QString& value)
{
    m_PostProcessHandler->onGettingVolumeText(value);
}

void BeamFormerBase::onGettingVolumeTDIText(QString& value)
{
    m_PostProcessHandler->onGettingVolumeTDIText(value);
}

void BeamFormerBase::onGettingVolumeCWDText(QString& value)
{
    m_PostProcessHandler->onGettingVolumeCWDText(value);
}

void BeamFormerBase::onElastoRebootTimeChanging(const QVariant& value)
{
    ModelSettings::instance().setValue(ModelSettings::ElastoRebootTime, value);
}

void BeamFormerBase::onFreeMModeChanging(const QVariant& value)
{
    controlWaveVelocity();
    // 自由m模式使用的是B模式的声功率参数，而B/M模式使用的是M模式的声功率参数
    // 所以从B/M模式切换到自由m模式需要重新计算声功率
    controlAcousticPower();

    // freemode退出后，重新设置MPixelSizeMM,保持与BPixelSizeMM相等
    if (!value.toBool())
    {
        qreal pixelSizeMM = m_SonoParameters->pDV(BFPNames::PixelSizeMMStr);
        m_SonoParameters->setPV(BFPNames::MPixelSizeMMStr, pixelSizeMM);
    }
}

void BeamFormerBase::onPixelSizeMMChanging(const QVariant& value, bool isChanged)
{
    Q_UNUSED(value);
    if (!isChanged)
    {
        return;
    }

    // calc bimagesize
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(sonoParameters()->pIV(BFPNames::ProbeIdStr));
    if (!probeDataInfo.IsLinear)
    {
        if (m_ImageRenderLayoutRects->updateBImageSize())
        {
            // 当Bimagesize 发生变化时需要重建图元
            int displayFormat = m_ImageRenderLayoutRects->getDisplayFormat(systemScanMode());
            updateRenderLayoutInfos(displayFormat);
        }
    }
    if (m_SonoParameters->pBV(BFPNames::PA_VERT_DIST_EnableStr))
    {
        parameter(BFPNames::BSteeringAngleCodingStr)
            ->update(); //虚拟顶点打开的情况下，触发BSteeringAngleCodingStr更新，重新计算垂距
    }
}

void BeamFormerBase::onProbeDSCImageZoomCofChanged(const QVariant& value, bool isChanged)
{
    Q_UNUSED(value);
    if (!isChanged)
    {
        return;
    }
    if (m_ImageRenderLayoutRects->updateBImageSize())
    {
        // 当Bimagesize 发生变化时需要重建图元
        int displayFormat = m_ImageRenderLayoutRects->getDisplayFormat(systemScanMode());
        updateRenderLayoutInfos(displayFormat);
    }
}

void BeamFormerBase::oniImageEffectChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::oniImageEffectRotationChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onSTART_GAIN_0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    updataDTGC(BFPNames::START_GAIN_0Str, BFPNames::STOP_GAIN_0Str, BFPNames::POSITION_STEP_NUMStr,
               BFPNames::STOP_GAIN_POSITIONStr, BFPNames::POSITION_STEP_CLKStr);
}

void BeamFormerBase::onGettingSTART_GAIN_0Text(QString& value)
{
    value = gainParaText(BFPNames::START_GAIN_0Str);
}

void BeamFormerBase::onSTOP_GAIN_0Changing(const QVariant& value)
{
    Q_UNUSED(value);
    updataDTGC(BFPNames::START_GAIN_0Str, BFPNames::STOP_GAIN_0Str, BFPNames::POSITION_STEP_NUMStr,
               BFPNames::STOP_GAIN_POSITIONStr, BFPNames::POSITION_STEP_CLKStr);
}

void BeamFormerBase::onGettingSTOP_GAIN_0Text(QString& value)
{
    value = gainParaText(BFPNames::STOP_GAIN_0Str);
}

void BeamFormerBase::onSTOP_GAIN_POSITIONChanging(const QVariant& value)
{
    Q_UNUSED(value);
    updataDTGC(BFPNames::START_GAIN_0Str, BFPNames::STOP_GAIN_0Str, BFPNames::POSITION_STEP_NUMStr,
               BFPNames::STOP_GAIN_POSITIONStr, BFPNames::POSITION_STEP_CLKStr);
}

void BeamFormerBase::onSTART_GAIN_1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    updataDTGC(BFPNames::START_GAIN_1Str, BFPNames::STOP_GAIN_1Str, BFPNames::POSITION_STEP_NUM_2Str,
               BFPNames::STOP_GAIN_POSITION_2Str, BFPNames::POSITION_STEP_CLK_2Str);
}

void BeamFormerBase::onGettingSTART_GAIN_1Text(QString& value)
{
    value = gainParaText(BFPNames::START_GAIN_1Str);
}

void BeamFormerBase::onSTOP_GAIN_1Changing(const QVariant& value)
{
    Q_UNUSED(value);
    updataDTGC(BFPNames::START_GAIN_1Str, BFPNames::STOP_GAIN_1Str, BFPNames::POSITION_STEP_NUM_2Str,
               BFPNames::STOP_GAIN_POSITION_2Str, BFPNames::POSITION_STEP_CLK_2Str);
}

void BeamFormerBase::onGettingSTOP_GAIN_1Text(QString& value)
{
    value = gainParaText(BFPNames::STOP_GAIN_1Str);
}

void BeamFormerBase::onSTOP_GAIN_POSITION_2Changing(const QVariant& value)
{
    Q_UNUSED(value);
    updataDTGC(BFPNames::START_GAIN_1Str, BFPNames::STOP_GAIN_1Str, BFPNames::POSITION_STEP_NUM_2Str,
               BFPNames::STOP_GAIN_POSITION_2Str, BFPNames::POSITION_STEP_CLK_2Str);
}

void BeamFormerBase::onSTART_GAIN_DChanging(const QVariant& value)
{
    Q_UNUSED(value);
    updataDTGC(BFPNames::START_GAIN_DStr, BFPNames::STOP_GAIN_DStr, BFPNames::POSITION_STEP_NUM_DStr,
               BFPNames::STOP_GAIN_POSITION_DStr, BFPNames::POSITION_STEP_CLK_DStr);
}

void BeamFormerBase::onGettingSTART_GAIN_DText(QString& value)
{
    value = gainParaText(BFPNames::START_GAIN_DStr);
}

void BeamFormerBase::onSTOP_GAIN_DChanging(const QVariant& value)
{
    Q_UNUSED(value);
    updataDTGC(BFPNames::START_GAIN_DStr, BFPNames::STOP_GAIN_DStr, BFPNames::POSITION_STEP_NUM_DStr,
               BFPNames::STOP_GAIN_POSITION_DStr, BFPNames::POSITION_STEP_CLK_DStr);
}

void BeamFormerBase::onGettingSTOP_GAIN_DText(QString& value)
{
    value = gainParaText(BFPNames::STOP_GAIN_DStr);
}

void BeamFormerBase::onSTOP_GAIN_POSITION_DChanging(const QVariant& value)
{
    Q_UNUSED(value);
    updataDTGC(BFPNames::START_GAIN_DStr, BFPNames::STOP_GAIN_DStr, BFPNames::POSITION_STEP_NUM_DStr,
               BFPNames::STOP_GAIN_POSITION_DStr, BFPNames::POSITION_STEP_CLK_DStr);
}

void BeamFormerBase::onAFE_LOW_POWERChanging(const QVariant& value)
{
    int maxValue = (value.toBool() ? 2 : 3);
    parameter(BFPNames::AFE_LPF_FCutOff_ShowStr)->setMax(maxValue);
    parameter(BFPNames::AFE_LPF_FCutOff_ShowStr)->update();
    //    qDebug() << QString("onAFE_LOW_POWERChanging() AFE_LOW_POWER = %1").arg(value.toBool());
}

void BeamFormerBase::onGettingAFE_LPF_FCutOff_ShowText(QString& value)
{
    QStringList names;
    names << "10 MHz"
          << "15MHz"
          << "20MHz"
          << "25MHz";
    int intValue = pIV(BFPNames::AFE_LPF_FCutOff_ShowStr);
    if ((intValue >= 0) && (intValue < names.count()))
    {
        value = QString::number(intValue) + QString(" (%1)").arg(names.at(intValue));
    }
}

void BeamFormerBase::onGettingAFE_PGA_GAINText(QString& value)
{
    QStringList names;
    names << "24 dB"
          << "27 dB"
          << "21 dB";
    int intValue = pIV(BFPNames::AFE_PGA_GAINStr);
    if ((intValue >= 0) && (intValue < names.count()))
    {
        value = QString::number(intValue) + QString(" (%1)").arg(names.at(intValue));
    }
}

void BeamFormerBase::onGettingAFE_LNA_GAIN_ShowText(QString& value)
{
    QStringList names;
    names << "18 dB"
          << "21 dB"
          << "15 dB";
    int intValue = pIV(BFPNames::AFE_LNA_GAIN_ShowStr);
    if ((intValue >= 0) && (intValue < names.count()))
    {
        value = QString::number(intValue) + QString(" (%1)").arg(names.at(intValue));
    }
}

void BeamFormerBase::onGettingAFE_HPF_FCutOff_ShowText(QString& value)
{
    QStringList names;
    names << "100k"
          << "110k"
          << "120k"
          << "130k"
          << "140k"
          << "150k"
          << "160k"
          << "170k"
          << "20k"
          << "30k"
          << "40k"
          << "50k"
          << "60k"
          << "70k"
          << "80k"
          << "90k"
          << "260k"
          << "270k"
          << "290k"
          << "300k"
          << "310k"
          << "180k"
          << "190k"
          << "200k"
          << "210k"
          << "220k"
          << "230k"
          << "240k"
          << "250k";
    int intValue = pIV(BFPNames::AFE_HPF_FCutOff_ShowStr);
    if ((intValue >= 0) && (intValue < names.count()))
    {
        value = QString::number(intValue) + QString(" (%1)").arg(names.at(intValue));
    }
}

void BeamFormerBase::onAFE_LNA_GAINChanged(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onGettingAcousticPowerTestCodeText(QString& value)
{
    int aptc = pIV(BFPNames::AcousticPowerTestCodeStr);

    QString showValue = "";
    switch (aptc)
    {
    case 0:
        showValue = "B";
        break;
    case 1:
        showValue = "M";
        break;
    case 2:
        showValue = "C";
        break;
    case 3:
        showValue = "D";
        break;
    default:
        break;
    }
    value = showValue;
}

void BeamFormerBase::onGettingFrameScapeEnableText(QString& value)
{
    if (pIV(BFPNames::FrameScapeEnableStr) == 0)
    {
        value = QString("Off");
    }
    else
    {
        value = QString("On");
    }
}

// void BeamFormerBase::onFrameScapeTimeShowChanging(const QVariant &value)
//{
//    SyncModeType type = syncMode();
//    if (type == Sync_C)
//    {
//        setPV(BFPNames::CFrameScapeTimeStr, value);
//    }
//    else
//    {
//        setPV(BFPNames::BFrameScapeTimeStr, value);
//    }
//}

// void BeamFormerBase::onGettingFrameScapeTimeShowText(QString &value)
//{
//    int d = pIV(BFPNames::FrameScapeTimeShowStr);
//    float time = (d+1) * 0.8192;
//    value = QString().number(d) + " (" + QString().number(time) + "ms" + ")";
//}

void BeamFormerBase::onBeforeFrameScapeTimeChanged(const QVariant& oldValue, QVariant& newValue)
{
    SyncModeType type = syncMode();
    if (type != Sync_None)
    {
        newValue = oldValue;
    }
}

void BeamFormerBase::onFrameScapeTimeChanging(const QVariant& value)
{
    SyncModeType type = syncMode();
    if (type == Sync_None)
    {
        //        ControlTableSyncSender cs(m_ControlTable);
        //        Parameter *pParameter = parameter(BFPNames::BFrameScapeTimeStr);
        //        m_ControlTable->send(qobject_cast<ControlTableParameter*>(pParameter), value.toInt(), true, false);
        setPV(BFPNames::BFrameScapeTimeStr, value);
    }
}

void BeamFormerBase::onGettingFrameScapeTimeText(QString& value)
{
    int d = pIV(BFPNames::FrameScapeTimeStr);
    float time = (d + 1) * 0.8192;
    value = QString().number(d) + " (" + QString().number(time) + "ms" + ")";
}

void BeamFormerBase::onBeforeFrameScapeTimeSlowChanged(const QVariant& oldValue, QVariant& newValue)
{
    SyncModeType type = syncMode();
    if (type != Sync_None)
    {
        newValue = oldValue;
    }
}

void BeamFormerBase::onFrameScapeTimeSlowChanging(const QVariant& value)
{
    SyncModeType type = syncMode();
    if (type == Sync_None)
    {
        //        ControlTableSyncSender cs(m_ControlTable);
        //        Parameter *pParameter = parameter(BFPNames::BFrameScapeTimeStr);
        //        m_ControlTable->send(qobject_cast<ControlTableParameter*>(pParameter), value.toInt(), true, false);
        setPV(BFPNames::BFrameScapeTimeStr, value);
    }
}

void BeamFormerBase::onGettingFrameScapeTimeSlowText(QString& value)
{
    int d = pIV(BFPNames::FrameScapeTimeSlowStr);
    float time = (d + 1) * 0.8192;
    value = QString().number(d) + " (" + QString().number(time) + "ms" + ")";
}

void BeamFormerBase::onGettingCFrameScapeTimeText(QString& value)
{
    int d = pIV(BFPNames::CFrameScapeTimeStr);
    float time = (d + 1) * 0.8192;
    value = QString().number(d) + " (" + QString().number(time) + "ms" + ")";
}

void BeamFormerBase::onGainDeltaChanging(const QVariant& value)
{
    if (syncMode() != Sync_None)
    {
        return;
    }

    ControlTableSyncSender cs(m_ControlTable);
    Parameter* pParameter = parameter(pBV(BFPNames::THIStr) ? BFPNames::GainThiStr : BFPNames::GainStr);
    pParameter->update();
    int ctGain = m_ControlTable->currentControlTableValue(pParameter) + value.toInt();
    ctGain = qBound(pParameter->min(), ctGain, pParameter->max());
    m_ControlTable->send(qobject_cast<ControlTableParameter*>(pParameter), ctGain, true, false);
    //    DebugPrintf("-------------ctGain = %d, ControlTable Gain = %d", ctGain,
    //    m_ControlTable->currentControlTableValue(pParameter));
}

void BeamFormerBase::onDynamicRangeDeltaChanging(const QVariant& value)
{
    if (syncMode() != Sync_None)
    {
        return;
    }

    ControlTableSyncSender cs(m_ControlTable);
    Parameter* parameter = m_SonoParameters->parameter(BFPNames::DynamicRangeStr);
    parameter->update();
    int ctDynamicRange = m_ControlTable->currentControlTableValue(parameter) + value.toInt();
    ctDynamicRange = qBound(parameter->min(), ctDynamicRange, parameter->max());
    m_ControlTable->send(qobject_cast<ControlTableParameter*>(parameter), ctDynamicRange, true, false);
    //    DebugPrintf("-------------ctDynamicRange = %d, ControlTable DynamicRange = %d", ctDynamicRange,
    //    m_ControlTable->currentControlTableValue(parameter));
}

void BeamFormerBase::onGainDelta1Changing(const QVariant& value)
{
    if (syncMode() != Sync_None)
    {
        return;
    }

    ControlTableSyncSender cs(m_ControlTable);
    Parameter* pParameter = parameter(pBV(BFPNames::THIStr) ? BFPNames::GainThiStr : BFPNames::GainStr);
    pParameter->update();
    int ctGain = m_ControlTable->currentControlTableValue(pParameter) + value.toInt();
    ctGain = qBound(pParameter->min(), ctGain, pParameter->max());
    m_ControlTable->send(qobject_cast<ControlTableParameter*>(pParameter), ctGain, true, false);
    //    DebugPrintf("------------- ctGain = %d, ControlTable Gain = %d", ctGain,
    //    m_ControlTable->currentControlTableValue(pParameter));
}

void BeamFormerBase::onDynamicRangeDelta1Changing(const QVariant& value)
{
    if (syncMode() != Sync_None)
    {
        return;
    }

    ControlTableSyncSender cs(m_ControlTable);
    Parameter* parameter = m_SonoParameters->parameter(BFPNames::DynamicRangeStr);
    parameter->update();
    int ctDynamicRange = m_ControlTable->currentControlTableValue(parameter) + value.toInt();
    ctDynamicRange = qBound(parameter->min(), ctDynamicRange, parameter->max());
    m_ControlTable->send(qobject_cast<ControlTableParameter*>(parameter), ctDynamicRange, true, false);
    //    DebugPrintf("-------------ctDynamicRange = %d, ControlTable DynamicRange = %d", ctDynamicRange,
    //    m_ControlTable->currentControlTableValue(parameter));
}

void BeamFormerBase::onAFE_LPF_FCutOff_ShowChanging(const QVariant& value)
{
    static int relatedValues[] = {2, 3, 0, 1};
    int index = value.toInt();
    if ((index < 0) || (index >= (int)sizeof(relatedValues)))
    {
        return;
    }

    int hValue = relatedValues[index];
    bool bLowPower = pBV(BFPNames::AFE_LOW_POWERStr);
    int lValue = (bLowPower ? 0 : hValue);
    int curValue = (hValue << 2) + lValue;
    setPV(BFPNames::AFE_LPF_FCutOffStr, curValue);
    //    qDebug() << QString("onAFE_LOW_POWERChanging() AFE_LPF_FCutOff_Show = %1").arg(index);
    //    qDebug() << QString("onAFE_LOW_POWERChanging() AFE_LPF_FCutOff = %1").arg(pIV(BFPNames::AFE_LPF_FCutOffStr));
}

void BeamFormerBase::onAFE_LNA_GAIN_ShowChanging(const QVariant& value)
{
    int ivalue = value.toInt();
    int pValue = (ivalue > 1 ? (ivalue + 1) : ivalue);
    setPV(BFPNames::AFE_LNA_GAINStr, pValue);

    parameter(BFPNames::START_GAIN_0Str)->update();
    parameter(BFPNames::STOP_GAIN_0Str)->update();
    //    qDebug() << QString("onAFE_LNA_GAIN_ShowChanging() AFE_LNA_GAIN = %1").arg(pIV(BFPNames::AFE_LNA_GAINStr));
}

void BeamFormerBase::onAFE_HPF_FCutOff_ShowChanging(const QVariant& value)
{
    int ivalue = value.toInt();
    int pValue = (ivalue > 20 ? (ivalue + 3) : ivalue);
    setPV(BFPNames::AFE_HPF_FCutOffStr, pValue);
    //    qDebug() << QString("AFE_HPF_FCutOff_ShowChanging() AFE_HPF_FCutOff =
    //    %1").arg(pIV(BFPNames::AFE_HPF_FCutOffStr));
}

void BeamFormerBase::onAFE_PGA_GAINChanging(const QVariant& value)
{
    Q_UNUSED(value);
    //    qDebug() << QString("onAFE_PGA_GAINChanging() AFE_PGA_GAIN = %1").arg(value.toInt());
    parameter(BFPNames::START_GAIN_0Str)->update();
    parameter(BFPNames::STOP_GAIN_0Str)->update();
}

void BeamFormerBase::onGettingFreeHand3DVolmnSpacingText(QString& value)
{
    value = QString::number((value.toFloat() / 100), 'f', 2);
}

void BeamFormerBase::onLGC0Changing(const QVariant& value)
{
    Q_UNUSED(value)

    updatePhasedProbeTgc(0);
}

void BeamFormerBase::onLGC1Changing(const QVariant& value)
{
    Q_UNUSED(value)

    updatePhasedProbeTgc(1);
}

void BeamFormerBase::onLGC2Changing(const QVariant& value)
{
    Q_UNUSED(value)

    updatePhasedProbeTgc(2);
}

void BeamFormerBase::onLGC3Changing(const QVariant& value)
{
    Q_UNUSED(value)

    updatePhasedProbeTgc(3);
}

void BeamFormerBase::onLGC4Changing(const QVariant& value)
{
    Q_UNUSED(value)

    updatePhasedProbeTgc(4);
}

void BeamFormerBase::onLGC5Changing(const QVariant& value)
{
    Q_UNUSED(value)

    updatePhasedProbeTgc(5);
}

void BeamFormerBase::onLGC6Changing(const QVariant& value)
{
    Q_UNUSED(value)

    updatePhasedProbeTgc(6);
}

void BeamFormerBase::onLGC7Changing(const QVariant& value)
{
    Q_UNUSED(value)

    updatePhasedProbeTgc(7);
}

void BeamFormerBase::onGettingPhasedProbeTgc0ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    controlTableValue += gettingLGCValue(0);
}

void BeamFormerBase::onGettingPhasedProbeTgc1ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    controlTableValue += gettingLGCValue(1);
}

void BeamFormerBase::onGettingPhasedProbeTgc2ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    controlTableValue += gettingLGCValue(2);
}

void BeamFormerBase::onGettingPhasedProbeTgc3ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    controlTableValue += gettingLGCValue(3);
}

void BeamFormerBase::onGettingPhasedProbeTgc4ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    controlTableValue += gettingLGCValue(4);
}

void BeamFormerBase::onGettingPhasedProbeTgc5ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    controlTableValue += gettingLGCValue(5);
}

void BeamFormerBase::onGettingPhasedProbeTgc6ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    controlTableValue += gettingLGCValue(6);
}

void BeamFormerBase::onGettingPhasedProbeTgc7ControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)

    controlTableValue += gettingLGCValue(7);
}

void BeamFormerBase::onLGCMAXChanging(const QVariant& value)
{
    foreach (QString lGCName, BFPNames::LGCStrs)
    {
        Parameter* p = parameter(lGCName);
        if (!p->isNull())
        {
            p->setMax(value.toInt());
        }
    }
}

void BeamFormerBase::onLGCMINChanging(const QVariant& value)
{
    foreach (QString lGCName, BFPNames::LGCStrs)
    {
        Parameter* p = parameter(lGCName);
        if (!p->isNull())
        {
            p->setMin(value.toInt());
        }
    }
}

void BeamFormerBase::onChannelIndexChanging(const QVariant& value)
{
    updateMaxRadiusAngle();
    updateChannelIndex(value);
}

void BeamFormerBase::onGettingChannelIndexText(QString& value)
{
    int val = pIV(BFPNames::ChannelIndexStr);
    switch (val)
    {
    case 0:
        value = QString("Channel 16");
        break;
    case 1:
        value = QString("Channel 32");
        break;
    case 2:
        value = QString("Channel 64");
        break;
    case 3:
        value = QString("Channel 128");
        break;
    default:
        break;
    }
}

void BeamFormerBase::onDECGDlyChanging(const QVariant& value)
{
    setPV(BFPNames::ECGDlyShowStr, value);
}

void BeamFormerBase::onFreeMECGDlyChanging(const QVariant& value)
{
    setPV(BFPNames::ECGDlyShowStr, value);
}

void BeamFormerBase::onDTDIECGDlyChanging(const QVariant& value)
{
    setPV(BFPNames::ECGDlyShowStr, value);
}

void BeamFormerBase::onMGainChanging(const QVariant& value)
{
    m_PostProcessHandler->onMaxGainChanging(value);
}

void BeamFormerBase::onMGainThiChanging(const QVariant& value)
{
    onMGainChanging(value);
}

void BeamFormerBase::onBeforeMDisplayFormatChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (parameter(BFPNames::MDisplayFormatStr)->isEnabled())
    {
        // 当force update时重新计算相关参数
        if (oldValue.toInt() == newValue.toInt())
        {
            changeLayoutFormat(newValue.toInt());
            m_ImageRenderLayoutRects->onDisplayFormatChanged(newValue.toInt());
        }
    }
}

void BeamFormerBase::onMDisplayFormatChanging(const QVariant& value)
{
    if (parameter(BFPNames::MDisplayFormatStr)->isEnabled())
    {
        if (!isNeedUpdateLayoutFormat())
        {
            return;
        }

        switch ((MDisplayFormat)value.toInt())
        {
        case Up_Down_1_1:
        case Up_Down_1_2:
        case Up_Down_2_1:
            changeLayoutFormat(value.toInt());
            break;
        case Left_Right_1_1:
        case Left_Right_1_2:
        case Left_Right_2_1:
            m_ImageRenderLayoutRects->onDisplayFormatChanged(value.toInt());
            break;
        default:
            break;
        }
        imageShapeIntoUnstable();
    }
}

void BeamFormerBase::onGettingMDisplayFormatShowValue(QVariant& value)
{
    switch ((MDisplayFormat)value.toInt())
    {
    case Left_Right_1_1:
        value = "1:1";
        break;
    case Left_Right_1_2:
        value = "1:2";
        break;
    case Left_Right_2_1:
        value = "2:1";
        break;
    case Up_Down_1_1:
        value = "1:1";
        break;
    case Up_Down_1_2:
        value = "1:2";
        break;
    case Up_Down_2_1:
        value = "2:1";
        break;
    default:
        break;
    }
}

void BeamFormerBase::onBeforeDDisplayFormatChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (parameter(BFPNames::DDisplayFormatStr)->isEnabled())
    {
        // 当force update时重新计算相关参数
        if (oldValue.toInt() == newValue.toInt())
        {
            changeLayoutFormat(newValue.toInt());
            m_ImageRenderLayoutRects->updateRenderLayoutInfos(newValue.toInt());
        }
    }
}

void BeamFormerBase::onDDisplayFormatChanging(const QVariant& value)
{
    if (parameter(BFPNames::DDisplayFormatStr)->isEnabled())
    {
        if (!isNeedUpdateLayoutFormat())
        {
            return;
        }

        changeLayoutFormat(value.toInt());
    }
}

void BeamFormerBase::onGettingDDisplayFormatShowValue(QVariant& value)
{
    onGettingMDisplayFormatShowValue(value);
}

void BeamFormerBase::onBeforeDTDIDisplayFormatChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (parameter(BFPNames::DTDIDisplayFormatStr)->isEnabled())
    {
        // 当force update时重新计算相关参数
        if (oldValue.toInt() == newValue.toInt())
        {
            changeLayoutFormat(newValue.toInt());
            m_ImageRenderLayoutRects->updateRenderLayoutInfos(newValue.toInt());
        }
    }
}

void BeamFormerBase::onDTDIDisplayFormatChanging(const QVariant& value)
{
    if (parameter(BFPNames::DTDIDisplayFormatStr)->isEnabled())
    {
        if (!isNeedUpdateLayoutFormat())
        {
            return;
        }

        changeLayoutFormat(value.toInt());
    }
}

void BeamFormerBase::onDTDIDisplayFormatChanged(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onGettingDTDIDisplayFormatShowValue(QVariant& value)
{
    onGettingMDisplayFormatShowValue(value);
}

void BeamFormerBase::onCQYZLevelChanging(const QVariant& value)
{
    if (!isFixedDepth())
    {
        return;
    }
    controlCQYZ(value.toInt());
}

void BeamFormerBase::onGettingCQYZLevelMin(int& value)
{
    value = 0;
    if (!isFixedDepth())
    {
        return;
    }
    int cqyzLevelMin = calculateValidMinCQYZLevel(pMin(BFPNames::CQYZStr));
    value = cqyzLevelMin >= 0 ? cqyzLevelMin : 0;
}

void BeamFormerBase::onGettingCQYZLevelIsValidValue(const QVariant& value, bool& valid)
{
    if (!isFixedDepth() || value.toInt() > pMax(BFPNames::CQYZLevelStr) || value.toInt() < pMin(BFPNames::CQYZLevelStr))
    {
        valid = false;
    }
    valid = true;
}

void BeamFormerBase::onGettingCQYZLevelStep(int& value)
{
    if (isFixedDepth())
    {
        value = 1;
    }
    else
    {
        value = 0;
    }
}

void BeamFormerBase::onGettingCQYZLevelMax(int& value)
{
    value = 0;
    if (!isFixedDepth())
    {
        return;
    }
    int cqyzLevelMax = calculateValidMinCQYZLevel(pMax(BFPNames::CQYZStr));
    value = cqyzLevelMax >= 0 ? cqyzLevelMax : 0;
}

void BeamFormerBase::onGettingCQYZLevelText(QString& value)
{
    if (!isFixedDepth())
    {
        return;
    }
    onGettingCQYZText(value);
}

void BeamFormerBase::onDepthCMListChanging(const QVariant& value)
{
    QString valueList = value.toString();
    QStringList depthCMList;
    if (!valueList.isNull() && !valueList.isEmpty() && valueList != "0")
    {
        depthCMList = valueList.split(",");
    }
    if (depthCMList.count() > 0)
    {
        Parameter* levelParameter = parameter(BFPNames::CQYZLevelStr);
        levelParameter->setMax(depthCMList.count() - 1);
        double showDepth = QString("%1").arg(imageBottomDepthMM() / 10.0, 0, 'f', 1).toDouble();
        for (int i = 0; i < depthCMList.count(); i++)
        {
            if (RealCompare::IsGreaterOrEqual(depthCMList[i].toDouble(), showDepth))
            {
                levelParameter->setValue(i, true);
                return;
            }
        }
        levelParameter->setValue(levelParameter->max(), true);
    }
}

void BeamFormerBase::onBeforeDepthCMListChanged(const QVariant& oldValue, QVariant& newValue)
{
    QString valueList = newValue.toString();
    QStringList depthCMList;
    if (!valueList.isNull() && !valueList.isEmpty() && valueList != "0")
    {
        depthCMList = valueList.split(",");
    }
    if (depthCMList.count() <= 0)
    {
        newValue = oldValue;
    }
}

void BeamFormerBase::onCpaDBIndexChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onCXFNumChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onGettingPatientTemperatureText(QString& value)
{
    value = QString("%1℃").arg(pFV(BFPNames::PatientTemperatureStr));
}

void BeamFormerBase::onArbitraryWaveChanged(const QVariant& value)
{
    Q_UNUSED(value)
    sendArbitraryWave();
}

void BeamFormerBase::onArbitraryWaveFormExtendChanged(const QVariant& value)
{
    Q_UNUSED(value)
    if (AppSetting::isPresetMode())
    { //开启PresetMode时，由于系统组会单独调节某个参数，因此单个参数需要响应联动
        sendArbitraryWave();
    }
}

void BeamFormerBase::onArbitraryWaveForm1Changed(const QVariant& value)
{
    Q_UNUSED(value)
    if (AppSetting::isPresetMode())
    { //开启PresetMode时，由于系统组会单独调节某个参数，因此单个参数需要响应联动
        sendArbitraryWave();
    }
}

void BeamFormerBase::onArbitraryWaveForm2Changed(const QVariant& value)
{
    Q_UNUSED(value)
    if (AppSetting::isPresetMode())
    { //开启PresetMode时，由于系统组会单独调节某个参数，因此单个参数需要响应联动
        sendArbitraryWave();
    }
}

void BeamFormerBase::onArbitraryWaveForm3Changed(const QVariant& value)
{
    Q_UNUSED(value)
    if (AppSetting::isPresetMode())
    { //开启PresetMode时，由于系统组会单独调节某个参数，因此单个参数需要响应联动
        sendArbitraryWave();
    }
}

void BeamFormerBase::onArbitraryWaveForm4Changed(const QVariant& value)
{
    Q_UNUSED(value)
    if (AppSetting::isPresetMode())
    { //开启PresetMode时，由于系统组会单独调节某个参数，因此单个参数需要响应联动
        sendArbitraryWave();
    }
}

void BeamFormerBase::onArbitraryWaveForm6Changed(const QVariant& value)
{
    Q_UNUSED(value)
    if (AppSetting::isPresetMode())
    { //开启PresetMode时，由于系统组会单独调节某个参数，因此单个参数需要响应联动
        sendArbitraryWave();
    }
}

void BeamFormerBase::onArbitraryWaveForm7Changed(const QVariant& value)
{
    Q_UNUSED(value)
    if (AppSetting::isPresetMode())
    { //开启PresetMode时，由于系统组会单独调节某个参数，因此单个参数需要响应联动
        sendArbitraryWave();
    }
}

void BeamFormerBase::onTransferSignalChanged(const QVariant& value)
{
}

void BeamFormerBase::onB_RX_LNUMChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onB_RX_LNUMChanged(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onBeforeB_TX_LNUMChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
}

void BeamFormerBase::onB_TX_LNUMChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onC_RX_LNUMChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onC_RX_LNUMChanged(const QVariant& value)
{
}

void BeamFormerBase::onC_TX_LNUMChanging(const QVariant& value)
{
    Q_UNUSED(value);
}

void BeamFormerBase::onOrderColorLineDensityChanging(const QVariant& value)
{
    controlRealDensity(BFPNames::LineDensityCStr, value.toInt());
}

void BeamFormerBase::onGettingOrderColorLineDensityText(QString& value)
{
    updateRealDensityText(BFPNames::ColorLineDensityStr, value);
}

void BeamFormerBase::onOrderHighDensityChanging(const QVariant& value)
{
    controlRealDensity(BFPNames::HighDensityStr, value.toInt());
}

void BeamFormerBase::onGettingOrderHighDensityText(QString& value)
{
    updateRealDensityText(BFPNames::HighDensityStr, value);
}

void BeamFormerBase::onGettingAFE_HPF_FCutOffControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onGettingADC_HPF_CORNER_FREQText(QString& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onGettingADC_HPF_CORNER_FREQControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onGettingAFE_PGA_CLAMP_LVLText(QString& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onGettingAFE_PGA_CLAMP_LVLMax(int& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onGettingAFE_PGA_CLAMP_LVLControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onGettingAFE_PGA_HI_FREQText(QString& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onMBColorChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onMBPDChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onPrtOfDChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onDopSteerAngleIndexChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onRedundantPointsChanged(QVariant points)
{
    m_RedundantPoints = points.toInt();
}

void BeamFormerBase::onGettingLinePackageCountControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onMF_Coef_Harmonic_Steer0Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onMF_Coef_Harmonic_SteerXChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onFilterCoefOfTransmit5Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onBeforeVS_ModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
}

void BeamFormerBase::onVS_ModeChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onVS_ModeChanged(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onBeforeVS_MLAChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
}

void BeamFormerBase::onVS_MLAChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onGettingVS_MLAText(QString& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onGettingVS_MLAControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onGettingXCorrCoefThresholdControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onGettingAmplificationCoefControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onGettingTissueAmplificationCoeffControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onGettingBOverlapWetControlTableValue(const QVariant& value, int& controlTableValue)
{
    Q_UNUSED(value)
    Q_UNUSED(controlTableValue)
}

void BeamFormerBase::onDepthMMChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onRoiMidDepthMMChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onRoiHalfDepthMMChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onECGEnChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onPlaneAreaMethodChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onFreqCenterChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onBPointNumPerLineChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onFocusOffsetChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onZminChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onZmaxChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onPhasedAngleChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onTxNumPerRxBeamChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onMinTxNumPerRxBeamChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onAmplifyChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onMaxAlphaChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onMinAlphaChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onAlphaStepNumChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onDelayOffsetChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onOptimizeFPSChanging(const QVariant& value)
{
    bool isHighDensity = m_SonoParameters->pBV(BFPNames::HighDensityStr);
    if (value.toBool() && (syncMode() & Sync_C) == Sync_C && isHighDensity)
    {
        m_HighDensityBak = m_SonoParameters->pIV(BFPNames::HighDensityStr);
        m_SonoParameters->setPV(BFPNames::HighDensityStr, !isHighDensity);
    }
    else
    {
        if (m_HighDensityBak != -1)
        {
            m_SonoParameters->setPV(BFPNames::HighDensityStr, m_HighDensityBak);
            m_HighDensityBak = -1;
        }
    }
}

void BeamFormerBase::onMF_Coef_Fundamental_Steer0Changing(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onMF_Coef_Fundamental_SteerXChanging(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::onBMVIBImagesChanging(const QVariant& value)
{
    if (pIV(BFPNames::SystemScanModeStr) == SystemScanModeMVI)
    {
        setPV(BFPNames::BCImagesOnStr, value.toBool());
    }
}

QString BeamFormerBase::gainParaText(QString value)
{
    int base = pIV(value);
    int lnaValue = pIV(BFPNames::AFE_LNA_GAINStr);
    int pgaValue = pIV(BFPNames::AFE_PGA_GAINStr);
    int dB_pga = qRound(-4.5 * pgaValue * pgaValue + 7.5 * pgaValue + 24);
    int dB_lna = qRound(-2.0 * lnaValue * lnaValue + 5 * lnaValue + 18);

    float target = dB_pga + dB_lna - 36 + 0.25 * base;
    return QString().number(base) + QString(" (%1 dB)").arg(QString().number(target));
}

void BeamFormerBase::updataDTGC(const QString& start_gain, const QString& stop_gain, const QString& position_step_num,
                                QString& stop_gain_position, QString& position_step_clk)
{
    int stopGain = pV(stop_gain).toInt();
    int StartGain = pV(start_gain).toInt();
    if (stopGain <= StartGain)
    {
        stopGain = StartGain + 1;
        setPV(stop_gain, stopGain);
    }
    int y = stopGain - StartGain;
    setPV(position_step_num, y * 2);
    float stopGainPosition = pV(stop_gain_position).toInt() / 2 / y;
    setPV(position_step_clk, qCeil(stopGainPosition), true);
}

void BeamFormerBase::onGettingCanSend(bool& value)
{
    if (ModelConfig::instance().value(ModelConfig::CanFreezeSend, false).toBool())
    {
        // for TK1 platform
        value = true;
    }
    else
    {
        // for other platform
        const ControlTableParameter* parameter = qobject_cast<const ControlTableParameter*>(sender());
        if (parameter != NULL)
        {
            if (!parameter->isCanFrozenSend())
            {
                value = !isFrozen();
            }
            else
            {
                value = true;
            }
        }
    }
}

void BeamFormerBase::onMVIModeChanging(const QVariant& value)
{
    setPV(BFPNames::MVIShowStr, value.toBool());
    if (value.toBool())
    {
        setPV(BFPNames::DynFlowModeStr, true);
        parameter(BFPNames::MVILineDensityStr)->update();
        parameter(BFPNames::SampleRateDopMVIStr)->update();
        parameter(BFPNames::BaseLineMVIStr)->update();
    }
    else
    {
        setPV(BFPNames::DynFlowModeStr, false);
        setPV(BFPNames::BCImagesOnStr, false);
        parameter(BFPNames::ColorLineDensityStr)->update();
        parameter(BFPNames::DSampleRateDopStr)->update();
    }

    controlColorImageModeParas();
    controlColorFreqIndex();
    controlColorInvert();
    controlAcousticPower();
}

void BeamFormerBase::onGettingMVILineDensityText(QString& value)
{
    value = pBV(BFPNames::MVILineDensityStr) ? "High" : "Low";
}

void BeamFormerBase::updateFPS()
{
    //    qDebug() << PRETTY_FUNCTION << "FPS Changing !";
    setPV(BFPNames::FPSStr, qRound(m_FpsParameter->fps()));
    setPV(BFPNames::FPSForAPStr, qRound(m_FpsParameter->fpsForAP()));
    if (Setting::instance().defaults().isRealFps())
    {
        m_RefreshTimer.setInterval(1000 / pIV(BFPNames::FPSStr));
    }
}

void BeamFormerBase::updateMiti()
{
    m_BFMitiParameters->calculate();

    setPV(BFPNames::MIStr, m_BFMitiParameters->mi());
    setPV(BFPNames::TISStr, m_BFMitiParameters->tis());
    setPV(BFPNames::TIBStr, m_BFMitiParameters->tib());
    setPV(BFPNames::TICStr, m_BFMitiParameters->tic());
}

void BeamFormerBase::updateCMaxVel()
{
    setPV(BFPNames::CMaxVelCMSStr, m_CMaxVelParameter->calculate());
}

void BeamFormerBase::updateDMaxVel()
{
    setPV(BFPNames::DMaxVelCMSStr, m_DMaxVelParameter->calculate());
    setPV(BFPNames::DPixelSizeCMSStr, pDV(BFPNames::DMaxVelCMSStr) / dImageSize().height());
    if ((pIV(BFPNames::SyncModeStr) & Sync_D) == Sync_D && !pBV(BFPNames::FreezeStr))
    { // 只有进入PW模式下，调节角度才影响图像刷新
        imageShapeIntoUnstable();
    }
}

void BeamFormerBase::updateAcousticPower()
{
    controlAcousticPower();
}

void BeamFormerBase::notifyHardwareKeyChanged()
{
    log()->trace("BeamFormerBase::notifyHardwareKeyChanged()");
    emit hardWareKeyValidChanged(m_BFDataHandler->isFpgaKeyValid());
}

void BeamFormerBase::setPreset(const PresetParameters& presets)
{
    Q_ASSERT(bfIODevice()->isOpen());

    if (m_SupportFreezeOutage)
    {
        bfIODevice()->setDeviceBusy(true);
    }

    m_PostParameterHelper.setIsPostAvailable(false);
    QStringList names = presets.names();
    if (names.isEmpty())
    {
        setPV(BFPNames::ConfigDoneStr, true);

        if (m_SupportFreezeOutage)
        {
            bfIODevice()->setDeviceBusy(false);
        }

        return;
    }

    ControlTableParameter::setForceUpdate(m_ControlTable->isImported());
    m_ECGPosBak = -1;
    m_ThiBak = -1;
    m_SteeringAngleBak = -1;
    m_RotationBak = -1;
    m_FocusNumBBak = -1;
    m_ScpdBak = -1;
    m_SRABak = -1;
    m_HighDensityBak = -1;

    if (m_IsFPGAElastographyOn)
    {
        //弹性模式下切换检查模式,退出弹性模式是通过setSystemScanMode(SystemScanModeB)实现的,
        //但是下面调用setSystemScanMode时会受到ControlTableSender限制,不是立即下发控制字,
        //等ControlTableSender析构下发控制字时不会再执行controlIsFPGAElastoGraphyOn中
        //的延时操作,导致rebootFPGA失败,所以在ControlTableSender之前先退出弹性模式.
        m_IsFPGAElastographyOn = false;
        controlIsFPGAElastoGraphyOn();
    }

    DataFlagTimerLocker flagLocker(&m_UnstableParaTimer);

    TimeLogger tl;

    {
        ControlTableSender ctSender(m_ControlTable);
        DynamicParasSender drSender(m_RelatedParasController);
        FreqRelatedParasSender fpSender(m_RelatedParasController, true);
        BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name, QStringList());
        //关闭放大，进入B模式
        setSystemScanMode(SystemScanModeB);
        if (m_PRTOfCParameter != NULL)
        {
            //需要在foreach之前设置,防止在foreach中setPV时影响到这2个参数
            m_PRTOfCParameter->setPresetColorBottomBorder(presets.value(BFPNames::RoiMidDepthMMStr).toDouble() +
                                                          presets.value(BFPNames::RoiHalfDepthMMStr).toDouble());
            m_PRTOfCParameter->setPresetTDIBottomBorder(presets.value(BFPNames::RoiMidDepthMMTDIStr).toDouble() +
                                                        presets.value(BFPNames::RoiHalfDepthMMTDIStr).toDouble());
        }

        m_APPressCalculator->setPresetCqyz(presets.value(BFPNames::CQYZStr).toInt());

        PresetParameters currentPreset = m_SonoParameters->preset();
        QStringList currentPresetNames = currentPreset.names();

        const PresetParameters& defaultPreset = m_SonoParameters->defaultPreset();
        //如果预设置中没有包含当前需要预设置的参数，则给这写参数赋值为其最小值
        foreach (const QString& name, currentPresetNames)
        {
            if (!presets.contains(name))
            {
                Parameter* p = parameter(name);

                QVariant defV = p->min();
                if (defaultPreset.contains(name))
                {
                    defV = defaultPreset.value(name);
                }

                if (!p->isDirectValue())
                {
                    setPV(name, defV);
                }
                else
                {
                    setPDV(name, defV);
                }
            }
        }

        //确定切换预设值时贝塞尔曲线控制点的初始化位置是默认值还是预设值
        if (presets.contains(BFPNames::FourDKnotDepthMMStr) || presets.contains(BFPNames::FourDKnotLineStr))
        {
            setPV(BFPNames::FourDKnotPosChangedStr, true);
        }
        else
        {
            setPV(BFPNames::FourDKnotPosChangedStr, false);
        }

        if (!AppSetting::isVirtualHD() && presets.contains(BFPNames::FourDRenderStr) &&
            presets.value(BFPNames::FourDRenderStr).toInt() == 4)
        {
            setPV(BFPNames::FourDRenderStr, 0);
            names.removeOne(BFPNames::FourDRenderStr);
        }

        // ebit80增加独立的FourDGain、FourDDynamicRange
        if (currentPreset.contains(BFPNames::FourDGainStr) && !presets.contains(BFPNames::FourDGainStr))
        {
            //            setPV(BFPNames::FourDGainStr, presets.value(
            //                        presets.value(BFPNames::THIStr).toBool() ?
            //                        BFPNames::GainThiStr : BFPNames::GainStr).toInt());

            setPV(BFPNames::FourDGainStr, 135);
        }

        if (currentPreset.contains(BFPNames::FourDDynamicRangeStr) && !presets.contains(BFPNames::FourDDynamicRangeStr))
        {
            setPV(BFPNames::FourDDynamicRangeStr, 6);
            // setPV(BFPNames::FourDDynamicRangeStr, presets.value(BFPNames::DynamicRangeStr).toInt());
        }
        // ebit60v2增加独立的MGain、MDynamicRange，如果预设置中没有，需要
        // 用B模式的参数
        if (currentPreset.contains(BFPNames::MGainStr) && !presets.contains(BFPNames::MGainStr))
        {
            setPV(BFPNames::MGainStr,
                  presets.value(presets.value(BFPNames::THIStr).toBool() ? BFPNames::GainThiStr : BFPNames::GainStr)
                      .toInt());
        }
        if (currentPreset.contains(BFPNames::MDynamicRangeStr) && !presets.contains(BFPNames::MDynamicRangeStr))
        {
            setPV(BFPNames::MDynamicRangeStr, presets.value(BFPNames::DynamicRangeStr).toInt());
        }

        //如果切换探头，两个探头的预设值CQYZ相等，很多参数将无法设置到，这里必须update
        //先设置好CQYZ，很多和深度相关的参数先更新好，向focuspos之类的参数就不会被advise
        setOnePreset(BFPNames::CQYZStr, presets, true);
        parameter(BFPNames::CQYZStr)->update();

        //将需要提前设置的参数，在此先设置，避免在setPreset过程中，出现图形不稳定
        // FocusNumBStr必须在FocusPosBStr 之前设置，因为FocusNumBStr设置时，会改变FocusPosBStr
        setOnePreset(BFPNames::ImageZoomCoefStr, presets, true);
        parameter(BFPNames::ImageZoomCoefStr)->update();
        setPV(BFPNames::FocusNumBStr, presets.value(BFPNames::FocusNumBStr));
        setPV(BFPNames::FocusPosBStr, presets.value(BFPNames::FocusPosBStr));

        // ROISteerAngles must be set before DopSteerAngle
        setPV(BFPNames::ROISteerAnglesStr, presets.value(BFPNames::ROISteerAnglesStr));

        if (!parameter(BFPNames::LGCControlEnStr)->isNull())
        {
            setPV(BFPNames::LGCControlEnStr, false);
        }

        //设置预设值，必须先设置好ColormapManager中custom graymap的samplepoints和graymapdata
        if (!ModelConfig::instance().value(ModelConfig::IsGrayMapHorMove, false).toBool())
        {
            setCustomGrayMap(ColorMapTypeDef::B, BFPNames::BGrayCurveIndexStr, BFPNames::BSamplePointsStr, presets);
            setCustomGrayMap(ColorMapTypeDef::BTHI, BFPNames::BTHIGrayCurveIndexStr, BFPNames::BTHISamplePointsStr,
                             presets);
            setCustomGrayMap(ColorMapTypeDef::M, BFPNames::MGrayCurveIndexStr, BFPNames::MSamplePointsStr, presets);
            setCustomGrayMap(ColorMapTypeDef::PW, BFPNames::PwGrayCurveIndexStr, BFPNames::PwSamplePointsStr, presets);
            setCustomGrayMap(ColorMapTypeDef::PWTDI, BFPNames::PwTDIGrayCurveIndexStr, BFPNames::PwTDISamplePointsStr,
                             presets);
            setCustomGrayMap(ColorMapTypeDef::CWD, BFPNames::CwdGrayCurveIndexStr, BFPNames::CwdSamplePointsStr,
                             presets);
            setCustomGrayMap(ColorMapTypeDef::Elasto, BFPNames::EGrayCurveIndexStr, BFPNames::ESamplePointsStr,
                             presets);
            setCustomGrayMap(ColorMapTypeDef::FourD, BFPNames::FourDGrayCurveIndexStr, BFPNames::FourDSamplePointsStr,
                             presets);
        }

        EmptyRelatedParasController* emptyController =
            qobject_cast<EmptyRelatedParasController*>(m_RelatedParasController);

        if (emptyController != NULL && !names.contains(BFPNames::UniformityTgc00Str))
        {
            foreach (const QString& name, BFPNames::UniformityTgcStrs)
            { //如果Preset中没存UniformityTgc的参数，重置为64
                setPV(name, 64);
            }
        }

        if (currentPresetNames.contains(BFPNames::FreqSettingIdsStr) && !names.contains(BFPNames::FreqSettingIdsStr))
        {
            setPV(BFPNames::FreqSettingIdsStr, BaseFreqSetting::defaultVariantIds());
        }

        {
            QStringList idsNames = QStringList() << BFPNames::ColorFreqStrIdsStr << BFPNames::PDFreqStrIdsStr
                                                 << BFPNames::SNFreqStrIdsStr << BFPNames::TDIFreqStrIdsStr
                                                 << BFPNames::DopFreqStrIdsStr << BFPNames::TDFreqStrIdsStr
                                                 << BFPNames::CWDFreqStrIdsStr << BFPNames::ElastoFreqStrIdsStr
                                                 << BFPNames::MVIFreqStrIdsStr;

            foreach (const QString& ids, idsNames)
            {
                if (currentPresetNames.contains(ids) && !names.contains(ids))
                {
                    setPV(ids, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
                }
            }
        }

        {
            QStringList idsNames = QStringList() << BFPNames::LeeSettingIdsStr << BFPNames::AFSettingIdsStr
                                                 << BFPNames::LineiImageCVSettingIdsStr;

            QStringList paraNames = QStringList() << BFPNames::EdgeStr << BFPNames::EdgeStr << BFPNames::iImageStr
                                                  << BFPNames::iImageStr << BFPNames::iImageStr;

            for (int i = 0; i < idsNames.count(); i++)
            {
                const QString& ids = idsNames[i];
                if (currentPresetNames.contains(ids) && !names.contains(ids))
                {
                    setPV(ids, ParaListSettings::defaultVariantIds(pMax(paraNames[i]) + 1));
                }
            }
        }

        {
            QStringList idsNames = QStringList()
                                   << BFPNames::CAFStrIdsStr << BFPNames::CLFStrIdsStr << BFPNames::PDAFStrIdsStr
                                   << BFPNames::PDLFStrIdsStr << BFPNames::SNAFStrIdsStr << BFPNames::SNLFStrIdsStr
                                   << BFPNames::TDIAFStrIdsStr << BFPNames::TDILFStrIdsStr << BFPNames::MVIAFStrIdsStr
                                   << BFPNames::MVILFStrIdsStr;
            QStringList paraNames = QStringList() << BFPNames::ColorCoefStr << BFPNames::ColorCoefStr
                                                  << BFPNames::PDCoefStr << BFPNames::PDCoefStr << BFPNames::SNCoefStr
                                                  << BFPNames::SNCoefStr << BFPNames::TDICoefStr << BFPNames::TDICoefStr
                                                  << BFPNames::MVICoefStr << BFPNames::MVICoefStr;

            for (int i = 0; i < idsNames.count(); i++)
            {
                const QString& ids = idsNames[i];
                if (currentPresetNames.contains(ids) && !names.contains(ids))
                {
                    setPV(ids, ParaListSettings::defaultVariantIds(pMax(paraNames[i]) + 1, VariantUtil::String));
                }
            }
        }
        if (currentPresetNames.contains(BFPNames::LineiImageSettingIdsStr) &&
            !names.contains(BFPNames::LineiImageSettingIdsStr))
        {
            initializeIImageParameters();
        }

        //如果预设置中曲线index ！=0,则不需要设置samplepoints
        if (presets.value(BFPNames::BGrayCurveIndexStr).toInt() != 0)
        {
            names.removeOne(BFPNames::BSamplePointsStr);
        }
        if (presets.value(BFPNames::BTHIGrayCurveIndexStr).toInt() != 0)
        {
            names.removeOne(BFPNames::BTHISamplePointsStr);
        }
        if (presets.value(BFPNames::MGrayCurveIndexStr).toInt() != 0)
        {
            names.removeOne(BFPNames::MSamplePointsStr);
        }
        if (presets.value(BFPNames::PwGrayCurveIndexStr).toInt() != 0)
        {
            names.removeOne(BFPNames::PwSamplePointsStr);
        }
        if (presets.value(BFPNames::PwTDIGrayCurveIndexStr).toInt() != 0)
        {
            names.removeOne(BFPNames::PwTDISamplePointsStr);
        }
        if (presets.value(BFPNames::CwdGrayCurveIndexStr).toInt() != 0)
        {
            names.removeOne(BFPNames::CwdSamplePointsStr);
        }
        if (presets.value(BFPNames::EGrayCurveIndexStr).toInt() != 0)
        {
            names.removeOne(BFPNames::ESamplePointsStr);
        }
        if (presets.value(BFPNames::FourDGrayCurveIndexStr).toInt() != 0)
        {
            names.removeOne(BFPNames::FourDSamplePointsStr);
        }
        //---------------------

        //如果当前探头的AdjustmentOfBs不为空,AdjustmentOfBStr使用的是probeinfo中的值,这里不需要设置
        if (!curProbe().AdjustmentOfBs.isEmpty())
        {
            names.removeOne(BFPNames::AdjustmentOfBStr);
        }

        foreach (QString name, names)
        {
            Parameter* p = parameter(name);
            if (p->isPresetParameter())
            {
                setOnePreset(name, presets, p->isDirectValue());
            }
        }

        parameter(BFPNames::AFE_LPF_FCutOff_ShowStr)->update();
        parameter(BFPNames::AFE_LNA_GAIN_ShowStr)->update();
        parameter(BFPNames::AFE_HPF_FCutOff_ShowStr)->update();

        QList<QPoint> pts;
        for (int i = 0; i < 5; i++)
        {
            pts.append(QPoint(i * 64, i * 64));
        }

        // SamplePointsStr 有可能是空的
        if (pV(BFPNames::BSamplePointsStr).toByteArray().isEmpty())
        {
            setPV(BFPNames::BSamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        }

        if (pV(BFPNames::BTHISamplePointsStr).toByteArray().isEmpty())
        {
            setPV(BFPNames::BTHISamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        }

        if (pV(BFPNames::MSamplePointsStr).toByteArray().isEmpty())
        {
            setPV(BFPNames::MSamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        }

        if (pV(BFPNames::PwSamplePointsStr).toByteArray().isEmpty())
        {
            setPV(BFPNames::PwSamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        }

        if (pV(BFPNames::PwTDISamplePointsStr).toByteArray().isEmpty())
        {
            setPV(BFPNames::PwTDISamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        }

        if (pV(BFPNames::CwdSamplePointsStr).toByteArray().isEmpty())
        {
            setPV(BFPNames::CwdSamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        }

        if (pV(BFPNames::FourDSamplePointsStr).toByteArray().isEmpty())
        {
            setPV(BFPNames::FourDSamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        }

        setPV(BFPNames::CWDSampleRateBakStr, presets.value(BFPNames::CWDSampleRateStr));

        Parameter* cpParameter = parameter(BFPNames::CPColorMapIndexStr);
        cpParameter->setMax(parameter(BFPNames::BColorMapIndexStr)->max());
        cpParameter->setValue(presets.value(BFPNames::BColorMapIndexStr));

        //设置完预设置，必须再重设一次FocusPosB，因为在设置FocusNumB时会修改FocusPos
        //        setPV(BFPNames::FocusPosBStr, presets.value(BFPNames::FocusPosBStr));

        setPV(BFPNames::CurvedExapandingStr, false);
        int spacialCompound = spacialCompoundInPresets(m_SonoParameters->currentPreset());
        if (spacialCompound > 0)
        {
            setPV(BFPNames::ScpdStr, spacialCompound);
        }

        // update freq 的text，下发的控制字
        parameter(BFPNames::FreqIndexBStr)->update();
        controlColorFreqIndex();
        controlDopFreqIndex();
        if (ModelConfig::instance().value(ModelConfig::RealTHI, false).toBool())
        {
            parameter(BFPNames::THIStr)->update();
            // Edge parameter will be updated in onTHIChange()
        }
        else
        {
            // ECO's RealTHI is false, Edge parameter should be updated.
            parameter(BFPNames::EdgeStr)->update();
        }
        parameter(BFPNames::iImageStr)->update();
        //        if (ModelConfig::instance().value(ModelConfig::IsNewiImage, false).toBool())
        //        {
        //            //如果iimage中的参数值与开机默认值一样的话会导致此参数相关的块数据开机未下发
        //            // SingleNewiImageSettings::instance().settings().paraNames()
        //            parameter(BFPNames::iImageEffectStr)->update();
        //            parameter(BFPNames::iImageEffectRotationStr)->update();
        //        }
        controlColorCoefPara();

        //切换preset时SteeringAngle在MaxSteeringAngle之前changing,这时使用的MaxSteeringAngle是前一个
        // preset的值,由于MaxSteeringAngle是fs参数,所以在freq参数刷新后重新设置一下SteeringAngle默认值
        setPV(BFPNames::SteeringAngleStr, presets.value(BFPNames::SteeringAngleStr));

        // QBeamOnStr修改为IsDirectValue类型,在相关参数设置之后update,保证MBColor下发正确值
        parameter(BFPNames::QBeamOnStr)->update();

        // setGain 放在最后，在setFreqIndex和setSpacialCompound中增益会改变
        //兼容eco1和eco3的做法：
        if (presets.contains(BFPNames::GainStr))
        {
            QVariant presetGain = presets.value(BFPNames::GainStr);
            if (presetGain != pV(BFPNames::GainStr))
            {
                setPV(BFPNames::GainStr, presetGain);
            }
        }

        //因为ScpdTrape参数是一个directValue，必须在设置完scpe、fcpd的预设置后刷新一次，
        //用于控制好此参数和scpd、fcpd参数
        //只有线阵才能进行 TrapezoidalMode

        // 不是预设置参数，在切预设时先把它重置为false，避免对SCPD产生影响
        setPV(BFPNames::ZoomOnStr, false);

        //在后面ScanWidthStr参数update时会调用controlScpdOn()函数重新刷新scpd或scpdtrape
        if (presets.contains(BFPNames::ScpdTrapeStr) && curProbe().IsLinear)
        {
            parameter(BFPNames::ScpdTrapeStr)->update();
        }
        // 切预设时，因为梯形开关不是预设开关，所以梯形成像永远都应该是关闭状态
        setPV(BFPNames::TrapezoidalModeStr, false);

        setPV(BFPNames::IsBiopsyVisibleStr, false);
        setPV(BFPNames::NeedleModeStr, false);
        setPV(BFPNames::IsCenterLineVisibleStr, false);
        setPV(BFPNames::IsBHorizontalRulerVisibleStr, false);

        setPV(BFPNames::SonoNeedleStr, false);
        setPV(BFPNames::TDIEnStr, false);

        if (presets.contains(BFPNames::BSteeringScanStr))
        {
            parameter(BFPNames::BSteeringScanStr)->update();
            controlBSteer();
        }

        //由于ScanWidth 的最大值可能会变，因此Preset直接保存StartLine和StopLine，这里重新计算出ScanWidth
        BFScanWidthParameter sp(curProbe(), pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr),
                                isSupportAnyDensity(), pIV(BFPNames::B_RX_LNUMStr));

        setPDV(BFPNames::ScanWidthStr, sp.scanWidth());

        parameter(BFPNames::ScanWidthStr)->update();

        // FourDStartLine和FourDStopLine复用了StartLine和StopLine的地址，并且是非预设置参数，
        //开机时先下发StartLine，然后FourDStartLine再下发了0，后面StartLine由于值一样不再下发
        parameter(BFPNames::StartLineStr)->update();
        parameter(BFPNames::StopLineStr)->update();
        parameter(BFPNames::FrameScapeTimeStr)->update();

        //全屏放大时,设置preset,会使用preset中的zoomcoef,会导致不是100%
        //        controlImageZoomCoef();

        initECGDlyDelta(presets);
        if (m_ProbeChanged)
        {
            // TODO:探头切换时需要更新该探头的所有块数据，块数据下发可能会有重复包含，也可能包含不全面
            //由于频率相关的块数据添加在 FreqSetting::forcibleParaStrs() 中，每次切换频率会强制下发
            //不需要添加在此处
            QStringList blockParaNames = QStringList() << BFPNames::FilterCpdStr << BFPNames::SteeringAngleStr
                                                       << BFPNames::DopRvFNumStr << BFPNames::WallFilterSettingStr
                                                       << BFPNames::CPAWallFilterSettingStr;

            foreach (const QString& paraName, blockParaNames)
            {
                parameter(paraName)->update();
            }

            m_ProbeChanged = false;
        }

        // SampleDopNormal
        int prfNormal = pIV(BFPNames::SampleRateDopNormalStr);
        if ((prfNormal > 0) && (prfNormal != pIV(BFPNames::SampleRateDopStr)))
        {
            setPV(BFPNames::SampleRateDopStr, pIV(BFPNames::SampleRateDopNormalStr));
        }

        setPV(BFPNames::BFrameScapeTimeStr, pIV(BFPNames::FrameScapeTimeStr));
        setPV(BFPNames::FrameScapeTimeShowStr, pIV(BFPNames::FrameScapeTimeStr));

        // changeProbePara();
        //先保证下发控制表

        if (!presets.contains(BFPNames::BCRelativePosInDModeStr))
        {
            setPV(BFPNames::BCRelativePosInDModeStr, pIV(BFPNames::BCRelativePosStr));
        }
        else
        {
            setPV(BFPNames::BCRelativePosInDModeStr, presets.value(BFPNames::BCRelativePosInDModeStr).toInt());
        }

        if (!presets.contains(BFPNames::PWScanLineDeltaStr))
        {
            setPV(BFPNames::PWScanLineDeltaStr, 0);
        }
    }

    //第一次设置预设置，增加400ms时间
    static bool first = true;
    flagLocker.setDelayedStopTime(m_BFIODevice->leftWriteTime() + (first ? 400 : 0));
    first = false;

    // changepreset

    setPV(BFPNames::DepthCMListStr, ProbeDataSet::instance().depthShowString(curProbe().Name));

    resetOriginalDepthMMList();
    // 预设值需要设定每个CQYZLevel值
    if (presets.contains(BFPNames::CQYZLevelStr))
    {
        setPV(BFPNames::CQYZLevelStr, presets.value(BFPNames::CQYZLevelStr).toInt(), true);
    }
    else
    {
        updateCQYZ(pIV(BFPNames::CQYZStr));
    }
    //为掌上超声添加，重新下发当前探头数据块;
    resentCurProbeBlockData();

    //发送块数据时，设成false，发送完块数据，设置完预设值后，设为true
    //由于FPGA采样每个控制字有先后顺序，这里单独下发一次控制表，也可以同时帮FPGA解决以下问题：
    // FPGA setPreset 时，有时会无始波，这里每次setPreset之后，再下发一次ControlTable来暂时解决 2013-04-12
    // 2022.8.18 luodebing
    // 插上探头开机时，自动选择探头选择默认预设值进行打图，此过程的ConfigDone未做延迟会导致开机出图时有过渡图像
    //此处的ConfigDone操作挪到
    // setPresetFinished中根据不同机型做处理.Apple平台的实际执行在setCurProbe中执行，并且正常选择预设值的ConfigDone操作由unFreeze执行
    setPresetFinished();

    PanZoomBoxController* controller = dynamic_cast<PanZoomBoxController*>(panZoomBoxController());
    if (controller != nullptr)
    {
        controller->onPresetChanged();
    }
    //    //gray map 再下发,放到open()函数中进行
    //    setGrayMap(pV(BFPNames::GrayMapStr).toByteArray());

    tl.logger("BeamFormerBase::setPreset");
    setPV(BFPNames::ActiveBStr, 0);
    setPV(BFPNames::MDisplayFormatStr, 1);
    setPV(BFPNames::DDisplayFormatStr, 1);
    setPV(BFPNames::DTDIDisplayFormatStr, 1);

    prepareSonoNeedleParameter();

    ControlTableParameter::setForceUpdate(false);
    if (m_SupportFreezeOutage)
    {
        bfIODevice()->setDeviceBusy(false);
    }
    AutoFocusAdjuster::instance().updateCoefficient(m_SonoParameters);
}

void BeamFormerBase::refreshImage()
{
    if (!isFrozen() && isUpdateImage())
    {
        //        emit updateData(m_RawData);
    }
}

void BeamFormerBase::imageIntoUnstable(int ms)
{
    if (!isFrozen())
    {
        m_RawData->clear();
        m_UnstableParaTimer.imageIntoUnstable(ms);
    }
}

void BeamFormerBase::imageIntoStable()
{
    if (!isFrozen())
    {
        m_UnstableParaTimer.imageIntoStable();
    }
}

void BeamFormerBase::imageShapeIntoUnstable(int ms)
{
    if (!isFrozen() /* && !zoomOn()*/)
    {
        m_UnstableParaTimer.imageShapeIntoUnstable(ms);
    }
}

void BeamFormerBase::imageShapeIntoStable()
{
    if (!isFrozen() /* && !zoomOn()*/)
    {
        m_UnstableParaTimer.imageShapeIntoStable();
    }
}

void BeamFormerBase::onFpgaVersionUpdated()
{
    GeneralInfo::instance().setFpgaVersion(fpgaVersion());
}

void BeamFormerBase::onCPLDVersionUpdated()
{
    GeneralInfo::instance().setCPLDVersion(cpldVersion());
}

void BeamFormerBase::onFpgaSNUpdated()
{
    memcpy(Resource::FpgaSN, fpgaSN().data(), sizeof(Resource::FpgaSN));
}

void BeamFormerBase::onUpdateData()
{
}

void BeamFormerBase::startUpdateData()
{
    m_BFIODevice->addObserver(this);
    m_RefreshTimer.start();
}

void BeamFormerBase::setHighVoltage(bool on)
{
    ControlTableParameter* hvonPara = qobject_cast<ControlTableParameter*>(parameter(BFPNames::HV_OnStr));
    if (hvonPara != NULL)
    {
        setPV(BFPNames::HV_OnStr, on ? 1 : 0);
    }
}

QList<QMap<QString, int>> BeamFormerBase::hwProbeCodes() const
{
    return m_BFDataHandler->hwProbeCodes();
}

QList<QVector<int>> BeamFormerBase::currentHwCodes() const
{
    return m_BFDataHandler->currentHwCodes();
}

QList<QVector<int>> BeamFormerBase::currentOriginHwCodes() const
{
    return m_BFDataHandler->currentOriginHwCodes();
}

QList<int> BeamFormerBase::currentCodes() const
{
    return m_BFDataHandler->currentCodes();
}

void BeamFormerBase::clearHwProbeCodes()
{
    m_BFDataHandler->clearHwProbeCodes();
}

void BeamFormerBase::onFpgaHWVersionUpdated()
{
    qDebug() << PRETTY_FUNCTION << fpgaDBFVersion();
    GeneralInfo::instance().setUSBVersion(fpgaUSBVersion());
    GeneralInfo::instance().setDSPVersion(fpgaDSPVersion());
    GeneralInfo::instance().setDBFVersion(fpgaDBFVersion());
    GeneralInfo::instance().setTXBF1Version(fpgaTXBF1Version());
    GeneralInfo::instance().setTXBF2Version(fpgaTXBF2Version());
}

void BeamFormerBase::onBCDGeometryChanged()
{
    controlSoundOpened(ISoundController::General);
}

void BeamFormerBase::onProbeTemperatureUpdated()
{
    if (!isFrozen() && curProbe().IsTemperatureDetected)
    {
        emit probeTemperatureWarning(m_BFDataHandler->probeTemperature());
    }
}

void BeamFormerBase::onTeeProbeTemperatureUpdated()
{
    if (!isFrozen() && curProbe().isTeeProbe)
    {
        emit teeProbeTemperatureWarning(m_BFDataHandler->TeeProbeTemperature(), m_BFDataHandler->TeeProbeAngle());
    }
}

void BeamFormerBase::updatePhasedProbeTgc(int index)
{
    if (index >= 0 && index < BFPNames::PhasedProbeTgcStrs.count())
    {
        parameter(BFPNames::PhasedProbeTgcStrs.at(index))->update();
    }
}

int BeamFormerBase::gettingLGCValue(int index)
{
    if (index >= 0 && index < BFPNames::LGCStrs.count())
    {
        return pIV(BFPNames::LGCStrs.at(index));
    }
    else
    {
        return 0;
    }
}

void BeamFormerBase::onIsBiopsyVisibleChanged(const QVariant& value)
{
    Q_UNUSED(value)
}

void BeamFormerBase::enableConfigDone()
{
    ControlTableSyncSender cs(m_ControlTable);
    setPV(BFPNames::ConfigDoneStr, true);
}

QVector<ChipFactory::ChipNameEnum> BeamFormerBase::supportChipList() const
{
    return QVector<ChipFactory::ChipNameEnum>();
}

void BeamFormerBase::onPictureModeONChanging(const QVariant& value)
{
    m_ImageRenderLayoutRects->updateRenderImageScale();
    setPV(BFPNames::LayoutBImageSizeStr, m_ImageRenderLayoutRects->getLayoutBImageSize());
    setPV(BFPNames::BImageSizeStr, m_ImageRenderLayoutRects->getBImageSize());
    setPV(BFPNames::RenderBImageSizeStr, m_ImageRenderLayoutRects->getRenderBImageSize());
    setPV(BFPNames::MImageSizeStr, m_ImageRenderLayoutRects->getMImageSize());
    setPV(BFPNames::DImageSizeStr, m_ImageRenderLayoutRects->getDImageSize());
    //下面这四个参数决定了pw模式下的图像曲与坐标区的位置,特别是第二个
    setPV(BFPNames::ImageModeRectsStr, m_ImageRenderLayoutRects->getModeRects(imageSize()));
    setPV(BFPNames::RenderWidgetRectsStr,
          m_ImageRenderLayoutRects
              ->getRenderWidgetRects()); //这里的temp2[1]控制pw下方的坐标轴的位置(会受到temp4[0]的影响)
    setPV(BFPNames::ImageRegionsStr, m_ImageRenderLayoutRects->getImageRegions());
    setPV(BFPNames::ImageRectsStr, m_ImageRenderLayoutRects->getImageRects());
    setPV(BFPNames::DSCImageRectsStr, m_ImageRenderLayoutRects->getDSCImageRects());
}

void BeamFormerBase::disableConfigDone()
{
    ControlTableSyncSender cs(m_ControlTable);
    setPV(BFPNames::ConfigDoneStr, false);
}

void BeamFormerBase::sendVSBlockData(bool manual)
{
    Q_UNUSED(manual)
}

void BeamFormerBase::onConnectSignals()
{
    foreach (Parameter* p, parameters())
    {
        if (!p->isNull())
        {
            onSetOneParameter(p);
        }
    }
}

QString BeamFormerBase::weightedCurveTypeText(const QString& paraName) const
{
    int v = pIV(paraName);

    if (v == 0)
    {
        return "RectWind";
    }
    else if (v == 1)
    {
        return "TriangleWind";
    }
    else if (v == 2)
    {
        return "HammingWind";
    }
    else if (v == 3)
    {
        return "HanningWind";
    }
    else
    {
        return "Undefined";
    }
}

void BeamFormerBase::updatePrfList()
{
}

bool BeamFormerBase::isHPrfCW() const
{
    return ModelConfig::instance().value(ModelConfig::HPrfCW, false).toBool();
}

void BeamFormerBase::controlFilterCpd(bool isTHI)
{
    if (!isTHI) // Fpga支持的Thi，如果Thi关闭则FilterCpd必须关闭
    {
        setPV(BFPNames::FilterCpdStr, false);
    }
    else
    {
        sendFilterCpd();
    }
}

void BeamFormerBase::sendFilterCpd()
{
    setPV(BFPNames::FilterCpdStr, m_SonoParameters->currentPreset().value(BFPNames::FilterCpdStr));
}

void BeamFormerBase::controlIsFPGAElastoGraphyOn()
{
    StateFilterLocker locker(m_StateManager, StateEventNames::NothingStateFilter());
    rebootFPGA(m_IsFPGAElastographyOn);
    m_BFIODevice->suspendRead();
    setPV(BFPNames::TransferSignalStr, false); //关闭上传使能
    Util::sleep(ModelSettings::instance().value(ModelSettings::ElastoRebootTime, 2).toInt());
    setPV(BFPNames::TransferSignalStr, true); //打开上传使能

    {
        qDebug() << "start send probe data";
        ControlTableSyncSender cs(m_ControlTable);
        m_ControlTable->send();
        setGrayMap(pV(BFPNames::GrayMapStr).toByteArray());
        resentCurProbeBlockData();
    }
    m_BFIODevice->resumeRead();
}

void BeamFormerBase::setECGDly(const QString& ecgDly, const QString& ecgDlyDeltas, const QVariant& value)
{
    QList<int> settings = VariantUtil::variant2Ints(pV(ecgDlyDeltas), VariantUtil::String);
    int mVelocity = value.toInt();
    int listCount = settings.count();
    if ((mVelocity + 1) > listCount)
    {
        mVelocity = listCount - 1;
    }
    setPV(ecgDly, settings[mVelocity], true);
}

void BeamFormerBase::initECGDlyDelta(const PresetParameters& presets)
{
    PresetParameters currentPreset = m_SonoParameters->preset();
    QList<QString> ecgDlyDeltaList;
    QList<QString> velocityList;

    ecgDlyDeltaList << BFPNames::MECGDlyDeltasStr << BFPNames::FreeMECGDlyDeltasStr << BFPNames::DECGDlyDeltasStr
                    << BFPNames::CWDECGDlyDeltasStr << BFPNames::DTDIECGDlyDeltasStr;
    velocityList << BFPNames::MVelocityStr << BFPNames::FreeMVelocityStr << BFPNames::DVelocityStr
                 << BFPNames::CWDVelocityStr << BFPNames::DVelocityTDIStr;

    int ECGDlyValue = pV(BFPNames::ECGDlyStr).toInt();
    int mECGDlyValue = pV(BFPNames::MECGDlyStr).toInt();
    int cwECGDlyValue = pV(BFPNames::CWECGDlyStr).toInt();
    QList<int> list;
    for (int i = 0; i < ecgDlyDeltaList.count(); i++)
    {
        list.clear();
        if (currentPreset.contains(ecgDlyDeltaList[i]) && !presets.contains(ecgDlyDeltaList[i]))
        {
            for (int j = 0; j < parameter(velocityList[i])->max() + 1; j++)
            {
                if (ecgDlyDeltaList[i] == BFPNames::MECGDlyDeltasStr)
                {
                    list << mECGDlyValue;
                }
                else if (ecgDlyDeltaList[i] == BFPNames::CWDECGDlyDeltasStr)
                {
                    list << cwECGDlyValue;
                }
                else
                {
                    list << ECGDlyValue;
                }
            }
            setPV(ecgDlyDeltaList[i], VariantUtil::ints2Variant(list, VariantUtil::String));
        }
    }
}

void BeamFormerBase::changeECGDeltaElement(const QString& ecgDlyStr, const QString& ecgDlyDeltasStr,
                                           const QString& velocityStr, const QVariant& value)
{
    setPV(ecgDlyStr, value);
    QList<int> settings = VariantUtil::variant2Ints(pV(ecgDlyDeltasStr), VariantUtil::String);
    int indexOfSettings = pV(velocityStr).toInt();
    if (indexOfSettings + 1 > settings.count())
    {
        indexOfSettings = settings.count() - 1;
    }
    settings[indexOfSettings] = value.toInt();
    setPV(ecgDlyDeltasStr, VariantUtil::ints2Variant(settings, VariantUtil::String));
}

QString BeamFormerBase::getRenderModeParas(const QList<int>& renderIDList)
{
    QSettings fourDParasSettings(Resource::fourDParasSettingFileName(), QSettings::IniFormat);
    fourDParasSettings.beginReadArray("PresetParas");
    QString renderParas;

    QStringList paraNameList = QStringList() << "Threshold"
                                             << "SmoothIndex"
                                             << "PaletteIndex"
                                             << "DynamicRange"
                                             << "Gain";
    int renderModeID = 0;
    foreach (int renderID, renderIDList)
    {
        fourDParasSettings.setArrayIndex(renderID);
        renderParas += QString::number(renderModeID++) + "-";
        foreach (QString paraName, paraNameList)
        {
            renderParas += fourDParasSettings.value(paraName).toString() + "-";
        }
        if (renderParas.endsWith("-"))
        {
            renderParas.replace(renderParas.count() - 1, 1, '|');
        }
    }
    fourDParasSettings.endArray();
    if (renderParas.endsWith("|"))
    {
        renderParas.chop(1);
    }
    return renderParas;
}

bool BeamFormerBase::isLGCAvailable() const
{
    return curProbe().IsPhasedArray && AppSetting::isFunctionEnabled(LicenseItemKey::KeyLGC);
}

void BeamFormerBase::controlLGCEnabled(SystemScanMode mode)
{
    if (isLGCAvailable())
    {
        bool isLGCCtrlEn = true;
        switch (mode)
        {
        case SystemScanModeBPW:
        case SystemScanModeColorPW:
        case SystemScanModePowerPW:
        case SystemScanModeDPowerPW:
        case SystemScanModeMVIPW:
        case SystemScanModeCWD:
        case SystemScanModeCWDColorDoppler:
        case SystemScanModeCWDDirectionalPowerDoppler:
        case SystemScanModeCWDPowerDoppler:
        case SystemScanModeTissuePW:
        case SystemScanModeSonoNeedle:
            isLGCCtrlEn = !pBV(BFPNames::FreqSpectrumStr);
            break;
        case SystemScanModeColorM:
        case SystemScanModeM:
            isLGCCtrlEn = false;
            break;
        default:
            break;
        }
        parameter(BFPNames::LGCControlEnStr)
            ->setEnabled(isLGCCtrlEn && AppSetting::isFunctionEnabled(LicenseItemKey::KeyLGC));
    }
    else
    {
        parameter(BFPNames::LGCControlEnStr)->setEnabled(false);
    }
}

void BeamFormerBase::initializeControlTable()
{
    if (NULL == m_ControlTable)
    {
        return;
    }

    m_ControlTable->setBFIODevice(m_BFIODevice);

    connect(m_ControlTable, SIGNAL(startSending()), this, SLOT(handleSendingDataMsg()));
}

void BeamFormerBase::initializeTimer()
{
    m_RefreshTimer.setInterval(1000 / Setting::instance().defaults().fps());
    connect(&m_RefreshTimer, SIGNAL(timeout()), this, SLOT(refreshImage()));

    m_UnstableParaTimer.setImageUnstableTime(
        ModelConfig::instance().value(ModelConfig::ImageUnstableTimeMs, 300).toInt());
    // eco的ImageShapeUnstableTime是设置的300ms
    m_UnstableParaTimer.setImageShapeUnstableTime(
        ModelConfig::instance().value(ModelConfig::ImageShapeUnstableTimeMs, 300).toInt());
    connect(&m_UnstableParaTimer, SIGNAL(imageUnstable()), this, SIGNAL(imageUnstable()));
    connect(&m_UnstableParaTimer, SIGNAL(imageShapeUnstable()), this, SIGNAL(imageShapeUnstable()));
    connect(&m_UnstableParaTimer, SIGNAL(imageStable()), this, SIGNAL(imageStable()));
    connect(&m_UnstableParaTimer, SIGNAL(imageShapeStable()), this, SIGNAL(imageShapeStable()));
}

void BeamFormerBase::sendMDFData()
{
}

bool BeamFormerBase::isZoomEnabled() const
{
    // 2023-05-25 Write by AlexWang 解决BCMode无法进使用局部放大功能
    bool isSystemScanModeB = systemScanMode() == SystemScanModeB || systemScanMode() == SystemScanModeColorDoppler ||
                             systemScanMode() == SystemScanModePowerDoppler ||
                             systemScanMode() == SystemScanModeDPowerDoppler ||
                             systemScanMode() == SystemScanModeTissueDoppler ||
                             systemScanMode() == SystemScanModeSonoNeedle || systemScanMode() == SystemScanModeMVI;
    bool isTrapezoidalMode = false;
    if (pBV(BFPNames::VirtualVertexTrapezoidalModeStr) || pBV(BFPNames::TrapezoidalModeStr))
    {
        isTrapezoidalMode = true;
    }

    //    bool isDopplerScanLineVisible = pBV(BFPNames::IsDopplerScanLineVisibleStr);
    //    return isSystemScanModeB && !isTrapezoidalMode && !isDopplerScanLineVisible;

    if (isSystemScanModeB)
    {
        return !isTrapezoidalMode;
    }

    return false;
}

void BeamFormerBase::changeLayoutFormat(int format)
{
    double oldHeightFactor = m_DepthParameters->heightFactor();
    QSize oldBImageSzie = pV(BFPNames::BImageSizeStr).toSize();
    m_DepthParameters->setHeightFactor(RenderLayoutDesigner::imageRowScaleFacator(format, 0).yScale);
    parameter(BFPNames::CQYZStr)->update();
    double newHeightFactor = m_DepthParameters->heightFactor();
    // check force update
    if (!RealCompare::AreEqual(oldHeightFactor, newHeightFactor))
    {
        QSize newBImageSzie = pV(BFPNames::BImageSizeStr).toSize();
        if (newBImageSzie == oldBImageSzie)
        {
            m_ImageRenderLayoutRects->updateBImageSize();
            updateRenderLayoutInfos(m_ImageRenderLayoutRects->getDisplayFormat(systemScanMode()));
        }
    }
}

SystemScanMode BeamFormerBase::currentSystemScanMode() const
{
    SystemScanMode scanMode = systemScanMode();

    if (pBV(BFPNames::BCImagesOnStr) && scanMode != SystemScanModeE)
    {
        // 同2B模式
        scanMode = SystemScanMode2B;
    }

    switch (pIV(BFPNames::LayoutStr))
    {
    case Layout_1x2:
        scanMode = SystemScanMode2B;
        break;
    case Layout_2x2:
        scanMode = SystemScanMode4B;
        break;
    default:
        break;
    }

    return scanMode;
}

void BeamFormerBase::updateRenderLayoutInfos(int format)
{
    //更新rect  这样在c下panzoom后调节深度 roi框就不会异常
    setPV(BFPNames::LayoutBImageSizeStr, m_ImageRenderLayoutRects->getLayoutBImageSize());
    m_ImageRenderLayoutRects->updateRenderLayoutInfos(format);
}

bool BeamFormerBase::isNeedUpdateLayoutFormat()
{
    ImageScale oldScale = pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>();
    m_ImageRenderLayoutRects->updateRenderImageScale();
    ImageScale newScale = pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>();

    return !(RealCompare::AreEqual(oldScale.xScale, newScale.xScale) &&
             RealCompare::AreEqual(oldScale.yScale, newScale.yScale));
}

void BeamFormerBase::controlCQYZ(int cqyzLevel)
{
    QString valueList = pV(BFPNames::DepthCMListStr).toString();
    QStringList depthCMList;
    if (!valueList.isNull() && !valueList.isEmpty() && valueList != "0")
    {
        depthCMList = valueList.split(",");
    }
    if (depthCMList.count() > 0 && cqyzLevel >= 0 && cqyzLevel < depthCMList.count())
    {
        ProbeParameters p(curProbe(), startDepthMM(), pixelSizeMM(), zoomOn(), pIV(BFPNames::StartLineStr),
                          pIV(BFPNames::StopLineStr));
        // 如果是凸阵，则深度值表示为从图阵弧底到图像底部的距离
        double depthMM = (depthCMList[cqyzLevel].toDouble() * 10.0f) + p.arcFloorDisMM() - p.startDepthMM();
        //考虑有偏转角时，需要扩大探头深度
        int CPDSteer = pBV(BFPNames::ScpdOnStr) ? m_SonoParameters->pIV(BFPNames::CPDSteerStr) : 0;
        int BSteeringScan =
            m_SonoParameters->pIV(BFPNames::BSteeringScanStr) - m_SonoParameters->pMax(BFPNames::BSteeringScanStr) / 2;
        int angle = BSteeringScan == 0 ? CPDSteer : 0; //经系统 zeus 一致商定BSteer关闭时，CPD才参与计算
        qDebug() << PRETTY_FUNCTION << "angle:" << angle;
        double depthMMWithAngle = depthMM / cos(angle * M_PI / 180);

        int preferCQYZ = m_CQYZPreferValueCalculator->clacPreferCQYZ(depthMMWithAngle);
        if (preferCQYZ > 0)
        {
            if (!parameter(BFPNames::CQYZStr)->checkValueIsValid(preferCQYZ))
            {
                QVariant validValue = preferCQYZ;
                this->onBeforeCQYZChanged(pIV(BFPNames::CQYZStr), validValue);
                preferCQYZ = validValue.toInt();
            }
            double realmaxDepth = m_DepthParameters->depthMM(preferCQYZ);
            double renderImageZoomCof = realmaxDepth / depthMM;
            setPV(BFPNames::RenderImageZoomCofStr, renderImageZoomCof);
            setPV(BFPNames::CQYZStr, preferCQYZ, true);
        }
    }
}

void BeamFormerBase::updateCQYZ(int cqyz)
{
    if (isFixedDepth())
    {
        int cqyzLevel = calculateValidMinCQYZLevel(cqyz);
        if (cqyzLevel >= 0)
        {
            qDebug() << PRETTY_FUNCTION << "change CQYZLevel to" << cqyzLevel;
            setPV(BFPNames::CQYZLevelStr, cqyzLevel, true);
        }
    }
    else
    {
        setPV(BFPNames::CQYZStr, cqyz);
    }
}

int BeamFormerBase::calculateValidMinCQYZLevel(int cqyz)
{
    QString valueList = pV(BFPNames::DepthCMListStr).toString();
    QStringList depthCMList;
    if (!valueList.isNull() && !valueList.isEmpty() && valueList != "0")
    {
        depthCMList = valueList.split(",");
    }
    if (depthCMList.count() > 0)
    {
        ProbeParameters p(curProbe(), startDepthMM(), pixelSizeMM(), zoomOn(), pIV(BFPNames::StartLineStr),
                          pIV(BFPNames::StopLineStr));
        double realmaxDepth = m_DepthParameters->depthMM(cqyz);
        //考虑有偏转角时，需要扩大探头深度
        int CPDSteer = pBV(BFPNames::ScpdOnStr) ? m_SonoParameters->pIV(BFPNames::CPDSteerStr) : 0;
        int BSteeringScan =
            m_SonoParameters->pIV(BFPNames::BSteeringScanStr) - m_SonoParameters->pMax(BFPNames::BSteeringScanStr) / 2;
        int angle = BSteeringScan == 0 ? CPDSteer : 0; //经系统 zeus 一致商定BSteer关闭时，CPD才参与计算
        double depthMM = realmaxDepth * cos(angle * M_PI / 180);
        // 如果是凸阵，则深度值表示为从图阵弧底到图像底部的距离
        double depthShowCM =
            QString("%1").arg((depthMM + p.startDepthMM() - p.arcFloorDisMM()) / 10.0, 0, 'f', 1).toDouble();
        return CalculatorUtil::findSmallerNearestIndex(depthCMList, depthShowCM);
    }
    return 0;
}

void BeamFormerBase::controlRealDensity(const QString& pname, int value)
{
    // 因为Density枚举的低、中、高三档对应的值并非0-2，
    // 所以此处通过list映射具体的Density值，UI上调节的参数始终从小线性变化
    QList<int> densityList = {LowDensity, MediumDensity, HighDensity};
    int density = densityList.at(value);
    setPV(pname, density);
}

int BeamFormerBase::getRealDensity(QVariant value)
{
    QList<int> densityList = {LowDensity, MediumDensity, HighDensity};
    int density = densityList.at(value.toInt());
    return density;
}

void BeamFormerBase::updateColorRegionLine()
{
    BFScanWidthParameter sp(curProbe(), pIV(BFPNames::ScanWidthStr), isSupportAnyDensity(),
                            pIV(BFPNames::C_RX_LNUMStr));

    setPV(BFPNames::StartScanLineColorStr, sp.startLine());
    setPV(BFPNames::StopScanLineColorStr, sp.stopLine());
}

void BeamFormerBase::controlXContrastValue()
{
    parameter(BFPNames::XContrastValueStr)->setEnabled(isXContrastEnabled());
}

bool BeamFormerBase::isXContrastEnabled() const
{
    SyncModeType syncmode = (SyncModeType)m_SonoParameters->pIV(BFPNames::SyncModeStr);
    bool isFrozen = m_SonoParameters->pBV(BFPNames::FreezeStr);
    bool flagD = (syncmode & Sync_D) == Sync_D && m_SonoParameters->pBV(BFPNames::FreqSpectrumStr);
    bool isBiopsyVisible = m_SonoParameters->pBV(BFPNames::IsBiopsyVisibleStr);

    return (!isFrozen && !flagD && !isBiopsyVisible);
}

void BeamFormerBase::createBFIODevice()
{
    BeamFormerBase::createBFIODevice(BFIODeviceType::DUMMY);
}

void BeamFormerBase::createBFIODevice(BFIODeviceType bfIOType)
{
    m_BFIODevice = new UltrasoundDevice(bfIOType, this);

    connect(m_BFIODevice, SIGNAL(deviceStateChanged(bool)), this, SLOT(onDeviceStateChanged(bool)));
    connect(m_BFIODevice, SIGNAL(deviceOpenError(int)), this, SIGNAL(BFIODeviceOpenError(int)));
    connect(m_BFIODevice, SIGNAL(deviceWaitTimeout()), this, SLOT(onDeviceWaitTimeout()));
}

double BeamFormerBase::getRealDepthMM()
{
    // 2025-05-12 Write by AlexWang [BUG:78687] 进入局部放大后的深度需要使用ZoomHalfDepthMM参数计算
    // 后面的图像深度计算会减去探头垂距，在此必须提前加上探头垂距
    double depthMM = m_DepthParameters->depthMM();
    if (pBV(BFPNames::ZoomOnStr))
    {
        ProbeParameters p(curProbe(), startDepthMM(), pixelSizeMM(), zoomOn(), pIV(BFPNames::StartLineStr),
                          pIV(BFPNames::StopLineStr));
        depthMM = m_SonoParameters->pDV(BFPNames::ZoomHalfDepthMMStr) * 2 + p.arcFloorDisMM();
    }
    return depthMM;
}

bool BeamFormerBase::isSupportVS()
{
    return ModelConfig::instance().value(ModelConfig::IsSupportVS, false).toBool();
}

void BeamFormerBase::updateRealDensityText(const QString& pname, QString& value)
{
    QStringList densityShowList{"Low", "Mid", "High"};
    value = densityShowList.at(value.toInt());
}

void BeamFormerBase::updateTGCControltable(const QString& name)
{
    setPDV(name, pIV(name)); //更新控制字
    // TGC 本身的值没有变更，但是controltable计算时需要各种deta值，deta值更新也会影响计算
    parameter(name)->update();
    m_ControlTable->send(parameter(name)); //下发控制字
}

void BeamFormerBase::updateMaxRadiusAngle()
{
    //参照lotus的配置文件CommonSettings.xml中的Channel，这里改成了128, P9 = 64
    //    int channel = ModelConfig::instance().value(ModelConfig::Channel, 64).toInt();
    int channel = getChannelNum();

    // 线阵
    if (curProbe().IsLinear)
    {
        setPV(BFPNames::MAX_RADIUS_ANGLEStr, channel / 2 * curProbe().WaferLength * 8192);
    }
    // 相控阵
    else if (curProbe().IsPhasedArray)
    {
        // 最大基元半径MAX_RADIUS[23:0]/ 最大基元角度MAX_ANGLE [23:0]  相控阵的基元间距采用pitch值
        setPV(BFPNames::MAX_RADIUS_ANGLEStr, channel / 2 * curProbe().Pitch * 8192);
    }
    // 凸阵
    else
    {
        setPV(BFPNames::MAX_RADIUS_ANGLEStr,
              channel / 2 * (curProbe().WaferLength / curProbe().WaferRadius * 180 / M_PI) * 8192);
    }
}

void BeamFormerBase::updateChannelIndex(const QVariant& value)
{
    BoardCombType intVal = (BoardCombType)value.toInt();

    setPV(BFPNames::Channel16EnableStr, intVal == BoardCombType::CH16_3SOCKET_80Elem);

    setPV(BFPNames::Channel32EnableStr, (intVal == BoardCombType::CH32_3SOCKET_80Elem ||
                                         intVal == BoardCombType::CH32_2SOCKET_80Elem_1SOCKET_128Elem));

    setPV(BFPNames::Channel64EnableStr, (intVal == BoardCombType::CH64_3SOCKET_128Elem_156PIN ||
                                         intVal == BoardCombType::CH64_3SOCKET_128Elem_260PIN ||
                                         intVal == BoardCombType::CH64_3SOCKET_128Elem_160PIN));
}

int BeamFormerBase::getChannelNum()
{
    int value = 32;
    BoardCombType val = (BoardCombType)pIV(BFPNames::ChannelIndexStr);
    switch (val)
    {
    case BoardCombType::CH16_3SOCKET_80Elem:
        value = 16;
        break;
    case BoardCombType::CH32_3SOCKET_80Elem:
    case BoardCombType::CH32_2SOCKET_80Elem_1SOCKET_128Elem:
        value = 32;
        break;
    case BoardCombType::CH64_3SOCKET_128Elem_156PIN:
    case BoardCombType::CH64_3SOCKET_128Elem_260PIN:
    case BoardCombType::CH64_3SOCKET_128Elem_160PIN:
        value = 64;
        break;
    default:
        break;
    }

    return value;
}

bool BeamFormerBase::isFixedDepth() const
{
    return pBV(BFPNames::IsSupportIntegerDepthStr);
}

void BeamFormerBase::updateCQYZ()
{
    if (!isFixedDepth())
    {
        parameter(BFPNames::CQYZStr)->update();
    }
    else
    {
        parameter(BFPNames::CQYZLevelStr)->update();
    }
}

void BeamFormerBase::resetOriginalDepthMMList()
{
    if (NULL == m_CQYZPreferValueCalculator)
    {
        return;
    }

    // 记录所有CQYZ有序数值
    int maxCQYZ = isFixedDepth() ? curProbe().MaxCQYZ : pMax(BFPNames::CQYZStr);
    int minCQYZ = isFixedDepth() ? curProbe().MinCQYZ : pMin(BFPNames::CQYZStr);
    QVector<DepthMMVal> depthList;
    int CQYZ = minCQYZ;
    QString probeName = curProbe().Name;

    while (CQYZ <= maxCQYZ)
    {
        depthList.append(DepthMMVal(CQYZ, m_DepthParameters->depthMM(CQYZ)));
        int CQYZStep = m_CQYZStepConfig->getStep(probeName, CQYZ, true);
        Util::multiValue(CQYZ, minCQYZ, maxCQYZ + CQYZStep, true, false, CQYZStep);
    }

    m_CQYZPreferValueCalculator->setOriginalDepthList(depthList);
}

QList<int> BeamFormerBase::needleAngles() const
{
    QVector<int> settings = curProbe().NeedleAngles;
    QList<int> angles;
    int value = 0;
    for (int i = (settings.count() - 1); i >= 0; i--)
    {
        value = 0 - settings[i];
        if (!angles.contains(value))
        {
            angles.append(value);
        }
    }

    for (int i = 0; i < settings.count(); i++)
    {
        value = settings[i];
        if (!angles.contains(value))
        {
            angles.append(value);
        }
    }

    return angles;
}

QStringList BeamFormerBase::needleAngleStrs() const
{
    QStringList angleStrs;
    foreach (int angle, needleAngles())
    {
        angleStrs.append(QString::number(angle));
    }
    return angleStrs;
}

QList<int> BeamFormerBase::needleAngleCTValues() const
{
    QList<int> ctValues;
    int count = needleAngles().count();

    for (int i = (count / 2 - 1); i >= 0; i--)
    {
        ctValues.append(17 + i);
    }

    if (count % 2 != 0)
    {
        ctValues.append(0);
    }

    for (int i = 0; i < (count / 2); i++)
    {
        ctValues.append(1 + i);
    }

    return ctValues;
}

void BeamFormerBase::sendNeedleParas()
{
    if (m_NeedleSettingSet == nullptr)
    {
        return;
    }

    NeedleParas needleParas = m_NeedleSettingSet->getNeedleParas(m_Probe.Name, pIV(BFPNames::NeedleSizeStr),
                                                                 pIV(BFPNames::NeedleAngleIndexStr));

    setPV(BFPNames::NeedleGainStr, needleParas.needleGain());
    setPV(BFPNames::NeedleDynamicRangeStr, needleParas.needleDynamicRange());
    setPV(BFPNames::NeedleSSampleAdjustStr, needleParas.needleSSampleAdjust());
    setPV(BFPNames::FrequencyOfTransmit5Str, needleParas.frequencyOfTransmit5());
    setPV(BFPNames::HighFreqOfTransmit5Str, needleParas.highFreqOfTransmit5());
    setPV(BFPNames::PulseNumOfTransmit5Str, needleParas.pulseNumOfTransmit5());
    setPV(BFPNames::FocusNumOfTransmit5Str, needleParas.focusNumOfTransmit5());
    setPV(BFPNames::FilterCoefOfTransmit5Str, needleParas.filterCoefOfTransmit5());
    setPV(BFPNames::NeedleWeightedCurveTypeStr, needleParas.needleWeightedCurveType());
    setPV(BFPNames::TransmitPulseEx5Str, needleParas.transmitPulseEx5());
    setPV(BFPNames::NeedleTHIStr, needleParas.needleTHI());
    //实际应该是 Needle 帧的LogSel(918控制字),目前SonoAir用的是Log Compression（362控制字）,FPGA后期针对于SonoAir的会改
    // atom的sendNeedleParas重写一下
    // TODO：后面可以统一
    setPV(BFPNames::NeedleLogCompressionStr, needleParas.needleLogCompression());

    setPV(BFPNames::CFMDigitalTgc0Str, needleParas.utgc0());
    setPV(BFPNames::CFMDigitalTgc1Str, needleParas.utgc1());
    setPV(BFPNames::CFMDigitalTgc2Str, needleParas.utgc2());
    setPV(BFPNames::CFMDigitalTgc3Str, needleParas.utgc3());
    setPV(BFPNames::CFMDigitalTgc4Str, needleParas.utgc4());
    setPV(BFPNames::CFMDigitalTgc5Str, needleParas.utgc5());
    setPV(BFPNames::CFMDigitalTgc6Str, needleParas.utgc6());
    setPV(BFPNames::CFMDigitalTgc7Str, needleParas.utgc7());
    setPV(BFPNames::CFMDigitalTgc8Str, needleParas.utgc8());
    setPV(BFPNames::CFMDigitalTgc9Str, needleParas.utgc9());
    setPV(BFPNames::CFMDigitalTgc10Str, needleParas.utgc10());
    setPV(BFPNames::CFMDigitalTgc11Str, needleParas.utgc11());
    setPV(BFPNames::CFMDigitalTgc12Str, needleParas.utgc12());
    setPV(BFPNames::CFMDigitalTgc13Str, needleParas.utgc13());
    setPV(BFPNames::CFMDigitalTgc14Str, needleParas.utgc14());
    setPV(BFPNames::CFMDigitalTgc15Str, needleParas.utgc15());
}

void BeamFormerBase::sendArbitraryWave()
{
#ifdef USE_USCONTROLENGINE
    if (pBV(BFPNames::ArbitraryWaveStr))
    {
        int probeID = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
        const ProbeDataInfo& dataInfo = ProbeDataSet::instance().getProbe(probeID);
        QString probename = dataInfo.Name;
        QString currentDir = QCoreApplication::applicationDirPath();
        USBaseDefine::ArbitraryWFPara para = {
            pBV(BFPNames::ArbitraryWaveFormExtendStr),
            this,
            sendBlockDataFunc,
            (probename.toLower()).toStdString(),
            (currentDir + Resource::pathSeparator + Resource::dynDataDir()).toStdString(),
            "",
            "",
            "",
            "",
            "",
            ""};
        bool scpdOn = pBV(BFPNames::ScpdOnStr);
        bool fcpdOn = pBV(BFPNames::FcpdOnStr);
        bool trapezoidalMode = pBV(BFPNames::TrapezoidalModeStr);
        int scpdValue = pIV(BFPNames::ScpdStr);

        if (!scpdOn && !fcpdOn && !trapezoidalMode)
        {
            para.waveform1 = pSV(BFPNames::ArbitraryWaveForm1Str).toStdString();
        }
        else if (!scpdOn && fcpdOn && !trapezoidalMode)
        {
            para.waveform1 = pSV(BFPNames::ArbitraryWaveForm1Str).toStdString();
            para.waveform2 = pSV(BFPNames::ArbitraryWaveForm2Str).toStdString();
        }
        else if (scpdValue >= 2 && !trapezoidalMode)
        {
            para.waveform1 = pSV(BFPNames::ArbitraryWaveForm1Str).toStdString();
            para.waveform3 = pSV(BFPNames::ArbitraryWaveForm3Str).toStdString();
            para.waveform4 = pSV(BFPNames::ArbitraryWaveForm4Str).toStdString();
            // compound界面显示档位3、4、5、6的时候，需要waveform6、waveform7
            if (scpdValue > 2)
            {
                para.waveform6 = pSV(BFPNames::ArbitraryWaveForm6Str).toStdString();
                para.waveform7 = pSV(BFPNames::ArbitraryWaveForm7Str).toStdString();
            }
        }
        else if (scpdValue == 1)
        {
            para.waveform3 = pSV(BFPNames::ArbitraryWaveForm3Str).toStdString();
            para.waveform4 = pSV(BFPNames::ArbitraryWaveForm4Str).toStdString();
        }
        else if (trapezoidalMode)
        {
            para.waveform3 = pSV(BFPNames::ArbitraryWaveForm3Str).toStdString();
            para.waveform4 = pSV(BFPNames::ArbitraryWaveForm4Str).toStdString();
            para.waveform6 = pSV(BFPNames::ArbitraryWaveForm6Str).toStdString();
            para.waveform7 = pSV(BFPNames::ArbitraryWaveForm7Str).toStdString();
        }
        pSV(BFPNames::ArbitraryWaveForm1Str).toStdString();
        qDebug() << PRETTY_FUNCTION << QString::fromStdString(para.probeName) << QString::fromStdString(para.dynPath);
        ::sendBlockData(&para, GlobalDef::ArbitraryWF);
    }
#endif
}

void BeamFormerBase::sendBlockDataFunc(char* data, int len, int addr, void* object)
{
    BeamFormerBase* bf = static_cast<BeamFormerBase*>(object);
    bf->importBlockData(data, len, addr);
}

void BeamFormerBase::controlMDisplayFormat(bool isUDBM)
{
    Parameter* parameter = this->parameter(BFPNames::MDisplayFormatStr);
    if (parameter->isEnabled())
    {
        int displayFormat = parameter->value().toInt(), defalutFormat = (int)Up_Down_1_1, max = (int)Up_Down_1_2,
            min = (int)Up_Down_2_1;

        if (!isUDBM)
        {
            defalutFormat = (int)Left_Right_1_1;
            max = (int)Left_Right_1_2;
            min = (int)Left_Right_2_1;
        }

        parameter->setMax(max);
        parameter->setMin(min);

        displayFormat =
            ((displayFormat >= parameter->min() && displayFormat <= parameter->max()) ? displayFormat : defalutFormat);
        parameter->setValue(displayFormat);
        parameter->update();
    }
}

void BeamFormerBase::handleSendingDataMsg()
{
    emit startSendingData();
}

// void BeamFormerBase::trigerInfoLineEnableSignal()
//{
//    int len = pIV(BFPNames::LinePackageCountStr) + 1;
//    qDebug() << PRETTY_FUNCTION << len;
//    for(int i =0 ; i < len; i++)
//    {
//        setPV(BFPNames::InfoLineEnableSignalStr, false, true);
//        setPV(BFPNames::InfoLineEnableSignalStr, true, true);
//    }
//}

void BeamFormerBase::onSetOneParameter(Parameter* parameter)
{
    ControlTableParameter* cTP = qobject_cast<ControlTableParameter*>(parameter);
    if (cTP != NULL)
    {
        connect(cTP, SIGNAL(gettingCanSend(bool&)), this, SLOT(onGettingCanSend(bool&)));
        m_ControlTable->bindParameter(cTP);
    }
}

void BeamFormerBase::onSetSonoParameters()
{
    if (m_RelatedParasController == NULL)
    {
        createRelatedParasController();
    }
    initializeRelatedParasController();
    // 在beamform中,m_IfConnectSignals在后处理的beamform中不需要连接信号，因此也可以将这个作为后处理的标记,所以
    // 不用进行以下的初始化工作
    if (m_IfConnectSignals)
    {
        onBufferSonoParametersChanged(m_SonoParameters);
        initializeImageSize();
        m_ImageRenderLayoutRects->initialize();
        m_RawData = new DataArg(imageWidth(), imageHeight(), 8);

        //        m_BFIODevice->setReadingSize(getIOReadSize());

        m_BlockDataSender = new BaseBlockDataSender(m_SonoParameters, m_ControlTable);

        initializeBlockDataParaNameConverter();

        QScopedPointer<IBFKitFactory> kit(createBFKitFactory());
        m_BFDataHandler = kit->createBFDataHandler(m_RawData->data(), imageWidth() * imageHeight(), this);
        m_BFDataHandler->setUnstableParaTimer(&m_UnstableParaTimer);

        if (!Setting::instance().defaults().isIODeviceVirtual())
        {
            if (!m_ProbesSwitchController.isNull())
            {
                m_ProbesSwitchController->setSonoParameters(m_SonoParameters);
                m_ProbesSwitchController->setAbstractBFDataHandler(m_BFDataHandler);
            }
        }

        connect(m_BFDataHandler, SIGNAL(probeChanged(QVector<int>, QVector<bool>)), this,
                SIGNAL(probeChanged(QVector<int>, QVector<bool>)));
        connect(m_BFDataHandler, SIGNAL(fpgaVersionUpdated()), this, SLOT(onFpgaVersionUpdated()));
        connect(m_BFDataHandler, SIGNAL(cpldVersionUpdated()), this, SLOT(onCPLDVersionUpdated()));
        connect(m_BFDataHandler, SIGNAL(fpgaSNUpdated()), this, SLOT(onFpgaSNUpdated()));
        connect(m_BFDataHandler, SIGNAL(highTemperatureDetected()), this, SIGNAL(highTemperatureDetected()));
        connect(m_BFDataHandler, SIGNAL(voltageInfoChanged(QVector<VoltageData>, QVector<unsigned short>)), this,
                SIGNAL(voltageInfoChanged(QVector<VoltageData>, QVector<unsigned short>)));
        connect(m_BFDataHandler, SIGNAL(headDataUpdated(uchar*, int)), this, SIGNAL(headDataUpdated(uchar*, int)));
        // add xule
        connect(m_BFDataHandler, SIGNAL(fpgaHWVersionUpdated()), this, SLOT(onFpgaHWVersionUpdated()),
                Qt::DirectConnection);
        connect(m_BFDataHandler, SIGNAL(probeTemperatureUpdated()), this, SLOT(onProbeTemperatureUpdated()));
        connect(m_BFDataHandler, SIGNAL(teeProbeTemperatureUpdated()), this, SLOT(onTeeProbeTemperatureUpdated()));
        connect(m_BFDataHandler, SIGNAL(probeKeyStateChanged(QVector<quint8>)), this,
                SIGNAL(probeKeyStateChanged(QVector<quint8>)));
        connect(m_BFDataHandler, SIGNAL(fanSpeedUpdated(QVector<LineData::FanInfo>)), this,
                SIGNAL(fanSpeedUpdated(QVector<LineData::FanInfo>)));
        connect(m_SonoParameters, SIGNAL(presetChanged(PresetParameters)), this, SLOT(setPreset(PresetParameters)));

        createPhysicalGeometryControllers();
        initializeParameterCalculators();
        initializeRelatedParasController();
        initializeSonoParameters();

        RegionManager::instance().getRegionParameters(m_SonoParameters->parameters());

        m_CTFreezeParameter = qobject_cast<ControlTableParameter*>(parameter(BFPNames::FreezeStr)->cloneTo(this));

        createParameterModels();
        // TODO: 此处可能多余
        connect(parameter(BFPNames::PixelSizeMMStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onPixelSizeMMChanging(QVariant, bool)));
        connect(parameter(BFPNames::ProbeDSCImageZoomCofStr), SIGNAL(valueChanged(QVariant, bool)), this,
                SLOT(onProbeDSCImageZoomCofChanged(QVariant, bool)));
    }
}

void BeamFormerBase::initializeBlockDataParaNameConverter()
{
    m_BlockDataParaNameConverter = new BaseBlockDataParaNameConverter(m_SonoParameters);
}

void BeamFormerBase::initializeSonoParameters()
{
    {
        ControlTableSender ctSender(m_ControlTable);

        ProbeParameters pParameters(curProbe());
        setPV(BFPNames::IsSupportIntegerDepthStr,
              ModelConfig::instance().value(ModelConfig::IsFixedDepth, false).toBool());
        //将探头信息设置，放到前面，后面的计算和ProbeId有关
        changeProbePara();

        setSystemScanMode(SystemScanModeB);
        setPV(BFPNames::FreqSettingIdsStr, BaseFreqSetting::defaultVariantIds());
        setPV(BFPNames::ColorFreqStrIdsStr, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
        setPV(BFPNames::PDFreqStrIdsStr, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
        setPV(BFPNames::SNFreqStrIdsStr, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
        setPV(BFPNames::TDIFreqStrIdsStr, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
        setPV(BFPNames::DopFreqStrIdsStr, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
        setPV(BFPNames::TDFreqStrIdsStr, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
        setPV(BFPNames::CWDFreqStrIdsStr, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
        setPV(BFPNames::ElastoFreqStrIdsStr, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
        setPV(BFPNames::MVIFreqStrIdsStr, BaseFreqSetting::defaultVariantIds(VariantUtil::String));
        setPV(BFPNames::AFSettingIdsStr, EdgeSettings::defaultVariantIds());
        setPV(BFPNames::LeeSettingIdsStr, EdgeSettings::defaultVariantIds());
        setPV(BFPNames::CAFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::ColorCoefStr) + 1, VariantUtil::String));
        setPV(BFPNames::CLFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::ColorCoefStr) + 1, VariantUtil::String));
        setPV(BFPNames::PDAFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::PDCoefStr) + 1, VariantUtil::String));
        setPV(BFPNames::PDLFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::PDCoefStr) + 1, VariantUtil::String));
        setPV(BFPNames::SNAFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::SNCoefStr) + 1, VariantUtil::String));
        setPV(BFPNames::SNLFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::SNCoefStr) + 1, VariantUtil::String));
        setPV(BFPNames::MVIAFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::MVICoefStr) + 1, VariantUtil::String));
        setPV(BFPNames::MVILFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::MVICoefStr) + 1, VariantUtil::String));
        setPV(BFPNames::TDIAFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::TDICoefStr) + 1, VariantUtil::String));
        setPV(BFPNames::TDILFStrIdsStr,
              ParaListSettings::defaultVariantIds(pMax(BFPNames::TDICoefStr) + 1, VariantUtil::String));

        setPV(BFPNames::RenderImageZoomCofStr, 1.0f);
        setPV(BFPNames::CQYZStr, 7);                     // type:Int min:0 max:63 step:1 isPreset:1
        setPV(BFPNames::FrequencyCompoundingStr, false); // type:Bool min:0 max:1 step:1 isPreset:1 truevalue:1
#ifndef USE_TARGET_PALM
        setPV(BFPNames::FreqIndexColorStr, 2); // type:Int min:0 max:3 step:1 isPreset:1
#else
        setPV(BFPNames::FreqIndexColorStr, 0); // type:Int min:0 max:3 step:1 isPreset:1
#endif
        setPV(BFPNames::ColorLineDensityStr, false);         // type:Bool min:0 max:1 step:1 isPreset:1 truevalue:0
        setPV(BFPNames::TDILineDensityStr, false);           // type:Bool min:0 max:1 step:1 isPreset:1 truevalue:0
        setPV(BFPNames::ColorLineDensitySNStr, false);       // type:Bool min:0 max:1 step:1 isPreset:1 truevalue:0
        setPV(BFPNames::SteeringAngleStr, 3);                // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::FocusNumBStr, 0);                    // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::FocusPosBStr, 3);                    // type:Int min:0 max:15 step:1 isPreset:1
        setPDV(BFPNames::FocusNumMStr, 0);                   // type:Int min:0 max:3 step:1 isPreset:1
        setPDV(BFPNames::FocusPosMStr, 3);                   // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::HighDensityStr, true);               // type:Bool min:0 max:1 step:1 isPreset:1 truevalue:0
        setPV(BFPNames::MVelocityStr, 2);                    // type:Int min:0 max:3 step:1 isPreset:1
        setPDV(BFPNames::DVelocityStr, 0);                   // type:Int min:0 max:3 step:1 isPreset:1
        setPDV(BFPNames::DVelocityTDIStr, 0);                // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::ColorImageModeStr, (int)Color_None); // type:Int min:0 max:7 step:1 isPreset:0
        setPV(BFPNames::FreqSpectrumStr, false);             // type:Bool min:0 max:1 step:1 isPreset:0 truevalue:1
        setPV(BFPNames::LowVelocityBloodStr, true);          // type:Bool min:0 max:1 step:1 isPreset:1 truevalue:0
        setPV(BFPNames::BaseLineStr, 3);                     // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::BaseLineDTDIStr, 3);                 // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::GainColorStr, 100);                  // type:Int min:0 max:255 step:5 isPreset:1
        setPV(BFPNames::CETStr, 0);                          // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::TNRStr, 0);                          // type:Int min:0 max:1 step:1 isPreset:1
        setPV(BFPNames::TNFtrStr, 0);                        // type:Int min:0 max:1 step:1 isPreset:1
        setPV(BFPNames::DynamicRangeStr, 12);                // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::FourDStr, false);                    // type:Bool min:0 max:1 step:1 isPreset:0 truevalue:1
        setPV(BFPNames::FrameAvgStr, 0);                     // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::EdgeStr, 0);                         // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::AccCountColorStr, 8);                // type:Int min:0 max:31 step:1 isPreset:1
        setPDV(BFPNames::AccCountDopStr, 8);                 // type:Int min:0 max:31 step:1 isPreset:1
        setPV(BFPNames::SampleRateDopStr, 1);                // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::DSampleRateDopStr, 1);               // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::WallFilterDopStr, 0);                // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::WallFilterColorStr, 0);              // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::WallThresholdStr, 0);                // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::SampleVolumeStr, 0);                 // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::FrameAvgColorStr, 0);                // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::CVRTStr, 0);                         // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::ScpdStr, 0);                         // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::AcousticPowerTestCodeStr, 0);        // type:Int min:0 max:3 step:1 isPreset:0
        setPV(BFPNames::MScanLineStr, 80);                   // type:Int min:0 max:255 step:5 isPreset:1
        setPDV(BFPNames::DScanLineStr, 80);                  // type:Int min:0 max:255 step:5 isPreset:1
        setPDV(BFPNames::DScanLineTDIStr, 80);
        setPV(BFPNames::ScanWidthStr, parameter(BFPNames::ScanWidthStr)->max());
        setPV(BFPNames::StartLineColorStr, pParameters.middleLineNo() - 40); // type:Int min:0 max:255 step:5 isPreset:0
        setPV(BFPNames::StopLineColorStr, pParameters.middleLineNo() + 40);  // type:Int min:0 max:255 step:5 isPreset:0
        setPV(BFPNames::TopBorderColorStr, 40);                              // type:Int min:0 max:255 step:5 isPreset:0
        setPV(BFPNames::BottomBorderColorStr, 80);                           // type:Int min:0 max:255 step:5 isPreset:0
        setPV(BFPNames::SampleDepthDopStr, 64);                              // type:Int min:0 max:255 step:5 isPreset:1
        setPV(BFPNames::TGCStr, QByteArray(TGC_COUNT, (char)127)); // type:ByteArray min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::FreqIndexBStr, 2);                         // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::SmoothStr, 0);                             // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::FocusNumCStr, 0);                          // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::FocusPosCStr, 3);                          // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::GainDopStr, 128);                          // type:Int min:0 max:255 step:5 isPreset:1
        setPV(BFPNames::VolumeStr, 0);                             // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::MBStr, 1);                                 // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::BStearingAngleStr, 0);                     // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::FreqIndexFrequencyCompoundingStr, 0);      // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::FocusPosFrequencyCompoundingStr, 0);       // type:Int min:0 max:15 step:1 isPreset:1
        setPV(BFPNames::AcousticPowerBStr, 10);                    // type:Int min:0 max:255 step:5 isPreset:1
        setPDV(BFPNames::AcousticPowerColorStr, 10);               // type:Int min:0 max:255 step:5 isPreset:1
        setPDV(BFPNames::AcousticPowerDopStr, 10);                 // type:Int min:0 max:255 step:5 isPreset:1
        setPDV(BFPNames::AcousticPowerElastoStr, 10);              // type:Int min:0 max:255 step:5 isPreset:1
        setPV(BFPNames::LCDLightStr, ModelSettings::instance().value(ModelSettings::LCDLight, 50));
        setPV(BFPNames::LCDLightShowStr, (int)(100 - 100.0 * pIV(BFPNames::LCDLightStr) / 255.0));
        setPV(BFPNames::CVLTStr, 0);                 // type:Int min:0 max:127 step:1 isPreset:1
        setPV(BFPNames::ExceedLowBloodStr, 0);       // type:Int min:0 max:1 step:1 isPreset:1
        setPV(BFPNames::DSCStr, false);              // type:Bool min:0 max:1 step:1 isPreset:0 truevalue:1
        setPV(BFPNames::CFVelocityThresholdStr, 4);  // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::StateClearFlagStr, false);   // type:Bool min:0 max:1 step:1 isPreset:0 truevalue:1
        setPV(BFPNames::ColorRegionThresholdStr, 1); // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::WrResetStr, false);          // type:Bool min:0 max:1 step:1 isPreset:0 truevalue:1
        setPV(BFPNames::CHETStr, 0);                 // type:Int min:0 max:127 step:1 isPreset:1
        setPV(BFPNames::CTGCStr, 0);                 // type:Int min:0 max:255 step:5 isPreset:1
        setPV(BFPNames::THISelStr, 0);               // type:Bool min:0 max:1 step:1 isPreset:0 truevalue:1

        setPV(BFPNames::BCosAngleStr, 0); // type:Int min:0 max:255 step:5 isPreset:0
        setPV(BFPNames::BTgAngleStr, 0);  // type:Int min:0 max:255 step:5 isPreset:0
        setPV(BFPNames::CCosAngleStr, 0); // type:Int min:0 max:255 step:5 isPreset:0
        setPV(BFPNames::CTgAngleStr, 0);  // type:Int min:0 max:255 step:5 isPreset:0

        setPV(BFPNames::DSPResetStr, false);      // type:Bool min:0 max:1 step:1 isPreset:0 truevalue:1
        setPV(BFPNames::DBFResetStr, false);      // type:Bool min:0 max:1 step:1 isPreset:0 truevalue:1
        setPV(BFPNames::DBFInfoStr, 0);           // type:Int min:0 max:3 step:1 isPreset:0
        setPV(BFPNames::TestSignalStr, false);    // type:Bool min:0 max:1 step:1 isPreset:0 truevalue:1
        setPV(BFPNames::TestParameterStr, 0);     // type:Int min:0 max:3 step:1 isPreset:0
        setPV(BFPNames::BPulseNumStr, 1);         // type:Int min:0 max:3 step:1 isPreset:1
        setPV(BFPNames::CPulseNumStr, 1);         // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::DPulseNumStr, 1);         // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::VHSiStr, 0);              // type:Int min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::PhaseProbeIdStr, 0);      // type:Bool min:0 max:1 step:1 isPreset:0
        setPV(BFPNames::ZoomOnStr, false);        // type:Bool min:0 max:1 step:1 isPreset:0
        setPV(BFPNames::NotReduceGainStr, false); // type:Bool min:0 max:1 step:1 isPreset:1
        setPV(BFPNames::ShutDownStr, false);      // type:Bool min:0 max:1 step:1 isPreset:0
        setPV(BFPNames::UpStr, true);             // type:Bool min:0 max:1 step:1 isPreset:1
        setPV(BFPNames::LeftStr, true);           // type:Bool min:0 max:1 step:1 isPreset:1
        setPV(BFPNames::AlphaStr, 200);           // type:Int min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::BetaStr, 200);            // type:Int min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::GammaStr, 200);           // type:Int min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::ImageZoomCoefStr, 100);   // type:Int min:60 max:90 step:1 isPreset:1
        setPV(BFPNames::ImageZoomCoefBStr, pIV(BFPNames::ImageZoomCoefStr));
        setPV(BFPNames::ImageZoomCoefOtherStr, 100);
        setPV(BFPNames::ZoomMultiIndexStr, 0);                              // type:Int min:1 max:4 step:1 isPreset:0
        setPV(BFPNames::ZoomMidLineStr, pParameters.middleLineNo());        // type:Int min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::ZoomHalfLinesStr, pParameters.middleLineNo() / 2);  // type:Int min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::ZoomMidDepthMMStr, pDV(BFPNames::DepthMMStr) / 2);  // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::ZoomHalfDepthMMStr, pDV(BFPNames::DepthMMStr) / 4); // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::ScrollStr, 0);                                      // type:Int min:0 max:255 step:1 isPreset:0
        setPV(BFPNames::IsScrollStr, false);                                // type:Bool min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::ActiveBStr, 0);                                     // type:Int min:0 max:3 step:1 isPreset:0
        setPV(BFPNames::FPSStr, 10);                                        // type:Int min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::HarmonicStr, false);                                // type:Bool min:0 max:1 step:1 isPreset:1
        setPV(BFPNames::BaseLineColorStr, 3);                               // type:Int min:0 max:6 step:1 isPreset:1
        setPV(BFPNames::BaseLineTDIStr, 3);                                 // type:Int min:0 max:6 step:1 isPreset:1
        setPV(BFPNames::DopplerThetaStr, 0);                               // type:Int min:-70 max:70 step:10 isPreset:1
        setPV(BFPNames::DopplerThetaTDIStr, 0);                            // type:Int min:-70 max:70 step:10 isPreset:1
        setPV(BFPNames::RoiMidLineStr, pParameters.middleLineNo());        // type:Int min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::RoiHalfLinesStr, pParameters.middleLineNo() / 2);  // type:Int min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::RoiMidDepthMMStr, pDV(BFPNames::DepthMMStr) / 2);  // type:Double min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::RoiHalfDepthMMStr, pDV(BFPNames::DepthMMStr) / 4); // type:Double min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::RoiMidLineTDIStr, pParameters.middleLineNo());     // type:Int min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::RoiHalfLinesTDIStr, pParameters.middleLineNo() / 2); // type:Int min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::RoiMidDepthMMTDIStr, pDV(BFPNames::DepthMMStr) / 2); // type:Double min:0 max:0 step:1
                                                                             // isPreset:1
        setPV(BFPNames::RoiHalfDepthMMTDIStr,
              pDV(BFPNames::DepthMMStr) / 4);                             // type:Double min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::RoiMidLineElastoStr, pParameters.middleLineNo()); // type:Int min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::RoiHalfLinesElastoStr, pParameters.middleLineNo() / 2); // type:Int min:0 max:0 step:1
                                                                                // isPreset:1
        setPV(BFPNames::RoiMidDepthMMElastoStr,
              pDV(BFPNames::DepthMMStr) / 2); // type:Double min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::RoiHalfDepthMMElastoStr,
              pDV(BFPNames::DepthMMStr) / 4); // type:Double min:0 max:0 step:1 isPreset:1
        //        setPV(BFPNames::RoiMidLineMVIStr, pParameters.middleLineNo());                      //type:Int min:0
        //        max:0 step:1 isPreset:1 setPV(BFPNames::RoiHalfLinesMVIStr, pParameters.middleLineNo() / 2);
        //        //type:Int min:0 max:0 step:1 isPreset:1 setPV(BFPNames::RoiMidDepthMMMVIStr,
        //        pDV(BFPNames::DepthMMStr) / 2);                //type:Double min:0 max:0 step:1 isPreset:1
        //        setPV(BFPNames::RoiHalfDepthMMMVIStr, pDV(BFPNames::DepthMMStr) / 4);               //type:Double
        //        min:0 max:0 step:1 isPreset:1
        setPV(BFPNames::SampleVolumeMMStr,
              m_StaticParameters->dopplerVolMMs().first()); // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::DopplerStartDepthMMStr,
              pDV(BFPNames::DepthMMStr) / 2); // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::DopplerTDIStartDepthMMStr,
              pDV(BFPNames::DopplerStartDepthMMStr)); // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::CMaxVelCMSStr, 147.0);        // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::DMaxVelCMSStr, 220.0);        // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::BGammaStr, 4);                // type:Int min:0 max:8 step:1 isPreset:1
        setPV(BFPNames::BRejectionStr, 0);            // type:Int min:0 max:255 step:1 isPreset:1
        setPV(BFPNames::MGammaStr, 4);                // type:Int min:0 max:8 step:1 isPreset:1
        setPV(BFPNames::MRejectionStr, 0);            // type:Int min:0 max:255 step:1 isPreset:1
        setPV(BFPNames::PwGammaStr, 4);               // type:Int min:0 max:8 step:1 isPreset:1
        setPV(BFPNames::PwRejectionStr, 0);           // type:Int min:0 max:255 step:1 isPreset:1
        setPV(BFPNames::CwdGammaStr, 4);              // type:Int min:0 max:8 step:1 isPreset:1
        setPV(BFPNames::CwdRejectionStr, 0);          // type:Int min:0 max:255 step:1 isPreset:1
        setPV(BFPNames::THIStateStr, false);          // type:Bool min:0 max:1 step:1 isPreset:1
        setPV(BFPNames::ColorInvertStateStr, false);  // type:Bool min:0 max:1 step:1 isPreset:1
        setPV(BFPNames::BColorMapIndexStr, 0);        // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::CfColorMapIndexStr, 0);       // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::PdColorMapIndexStr, 0);       // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::DpdColorMapIndexStr, 0);      // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::PwColorMapIndexStr, 0);       // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::MColorMapIndexStr, 0);        // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::CwdColorMapIndexStr, 0);      // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::BGrayCurveIndexStr, 0);       // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::MGrayCurveIndexStr, 0);       // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::PwGrayCurveIndexStr, 0);      // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::CwdGrayCurveIndexStr, 0);     // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::EGrayCurveIndexStr, 0);       // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::VarColorMapIndexStr, 0);      // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::CfContrastStr, 0);            // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::PdContrastStr, 0);            // type:Int min:0 max:7 step:1 isPreset:1
        setPV(BFPNames::DpdContrastStr,
              0); // type:Int min:0 max:7 step:1 isPreset:1
                  //    setPV(BFPNames::StartDepthMMStr, 0);  //type:Double min:0 max:0 step:1 isPreset:0
                  //    setPV(BFPNames::PixelSizeMMStr, 0);  //type:Double min:0 max:0 step:1 isPreset:0
                  //    setPV(BFPNames::MPixelSizeMMStr, 0);  //type:Double min:0 max:0 step:1 isPreset:0
                  //    setPV(BFPNames::DPixelSizeCMSStr, 0);  //type:Double min:0 max:0 step:1 isPreset:0
                  //    setPV(BFPNames::DPixelSizeSecStr, 0);  //type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::ZoomDepthMMStr, 0);        // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::ScrollDepthMMStr, 0);      // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::HotLogoDepthMMStr, 5.0f);  // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::HotLogoLineStr, -5.0f);    // type:Double min:0 max:0 step:1 isPreset:0
        setPV(BFPNames::ProbeConnectedStr, false); // type:Bool min:0 max:1 step:1 isPreset:0
        setPV(BFPNames::GainStr, 200);             // type:Int min:0 max:255 step:5 isPreset:1
        setPV(BFPNames::MGainStr, 200);            // type:Int min:0 max:255 step:5 isPreset:1
        setPV(BFPNames::MGainThiStr, 200);
        setPV(BFPNames::ConfigDoneStr, false);
        setPV(BFPNames::MIStr, -1.0f);
        setPV(BFPNames::TISStr, -1.0f);
        setPV(BFPNames::TIBStr, -1.0f);
        setPV(BFPNames::FullScreenZoomInIndexStr, Setting::instance().defaults().fullScreenZoomInIndex());
        setPV(BFPNames::BFPipelineNumberStr, ModelSettings::instance().value(ModelSettings::BFPipelineNumber, 9));
        setPV(BFPNames::KeyBoardLightEnStr, ModelSettings::instance().value(ModelSettings::KeyBoardLightEn, false));
        setPV(BFPNames::KeyBoardLightLevelStr, ModelSettings::instance().value(ModelSettings::KeyBoardLightLevel, 7));
        setPV(BFPNames::ElastoTransparencyStr, 95);
        setPV(BFPNames::ElastoRebootTimeStr, ModelSettings::instance().value(ModelSettings::ElastoRebootTime, 2));

        //        setPV(BFPNames::FourDThresholdStr, 20);
        //        setPV(BFPNames::FourDRenderStr, 0);
        //        setPV(BFPNames::FourDSmoothStr, 0);
        setPV(BFPNames::FourDFrameRateStr, 0);
        //        setPV(BFPNames::FourDPaletteStr, 1);
        setPV(BFPNames::FourDLightStr, 1);
        setPV(BFPNames::FourDVirtualHDOnStr, false);
        // setPV(BFPNames::ChisonFourDVirtualHDOnStr, false);
        setPV(BFPNames::FourDDirectionSetStr, 0);
        setPV(BFPNames::FourDMotorAngleStr, 65);
        setPV(BFPNames::FourDMaxVolumeCountStr, 64);
        parameter(BFPNames::MDisplayFormatStr)->setEnabled(false);
        setPV(BFPNames::MDisplayFormatStr, 1);

        parameter(BFPNames::DDisplayFormatStr)->setEnabled(false);
        setPV(BFPNames::DDisplayFormatStr, 1);

        parameter(BFPNames::DTDIDisplayFormatStr)->setEnabled(false);
        setPV(BFPNames::DTDIDisplayFormatStr, 1);

#ifdef USE_TARGET_PALM
        setPV(BFPNames::LinePackageCountStr, 16);
#else
        // 如果modelconfig.ini中没有配置linePackageCount，用默认值32.有些FPGA包数会自动+1，要用默认值31，以保持是2的整数次方
        int linePackageCount = ModelConfig::instance().value(ModelConfig::LinePackageCount, 32).toInt();

        setPV(BFPNames::PanZoomSelectStr, false);
        setPV(BFPNames::PanZoomOffsetDepthPixelStr, 0);
        setPV(BFPNames::FreezePanZoomStr, false);
        setPV(BFPNames::ChangeStatusStr, false);
        setPV(BFPNames::LinePackageCountStr, linePackageCount);
#endif

        initMVIParams();

        if (Setting::instance().defaults().isIODeviceVirtual())
        {
            setPV(BFPNames::RedundantPointsStr, 0);
        }
        else
        {
            setPV(BFPNames::RedundantPointsStr, ModelConfig::instance().value(ModelConfig::RedundantPoints, 0));
        }

        setPV(BFPNames::LineDataModeStr, !Setting::instance().defaults().isWholeImage());

        setPV(BFPNames::FreeHand3DVolmnSpacingStr, 32);

        initializeSpecialParameters();

        setPV(BFPNames::FixedSWImageZoomCofStr,
              pV(BFPNames::DSCImageSizeStr).toSize().height() / pFV(BFPNames::PointNumPerLineStr));
        bool dscZoomOn = ModelConfig::instance().value(ModelConfig::DSCImageZoomOn, false).toBool();
        setPV(BFPNames::DSCImageZoomOnStr, dscZoomOn);
        setPV(BFPNames::ProbeDSCImageZoomCofStr, 1.0);

        if (pIV(BFPNames::ADFreqMHzStr) == 0)
        {
            setPV(BFPNames::ADFreqMHzStr, 32);
        }
        if (pIV(BFPNames::ADOffsetTimeNsStr) == 0)
        {
            setPV(BFPNames::ADOffsetTimeNsStr, 36000);
        }

        foreach (const QString& name, BFPNames::UniformityTgcStrs)
        {
            setPV(name, 64);
        }

        setPV(BFPNames::WtStartPointStr, 255);

        QList<QPoint> pts;
        for (int i = 0; i < 5; i++)
        {
            pts.append(QPoint(i * 64, i * 64));
        }

        setPV(BFPNames::BSamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        setPV(BFPNames::MSamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        setPV(BFPNames::PwSamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));
        setPV(BFPNames::CwdSamplePointsStr, NewtonSamplePointsUtil::fromPointList(pts));

        setPV(BFPNames::MSampleRateStr, 10);

        //控制预设是否支持TDI的变量，默认值false不支持
        setPV(BFPNames::SupportTDIStr, false);
        setPV(BFPNames::B_RX_LNUMStr, curProbe().WaferNum * 2);
        setPV(BFPNames::C_RX_LNUMStr, curProbe().WaferNum * 2);

        openTransferSignal();

        {
            //后台相关的参数须由此初始化，必须放在p->update();代码之前，并且尽量避免非后台参数，在这之前手动初始化
            //因为onDRDiffWithCompoundOnChanging
            // onDRDiffWithSraOnChanging还有DRRate相关参数时，会修改freqSetting中的数值
            DynamicParasSender drSender(m_RelatedParasController);
            FreqRelatedParasSender fpSender(m_RelatedParasController);
        }

        foreach (Parameter* p, parameters())
        {
            p->update();
        }

        QByteArray map = QByteArray(256, 0);
        for (int i = 0; i < map.size(); i++)
        {
            map[i] = i;
        }
        setGrayMap(map);
    }

    m_ControlTable->setIsInitialized(true);
}

void BeamFormerBase::initializeSpecialParameters()
{
    setPV(BFPNames::EnableFreezeAfterProbeFoundStr, true);
    initializeIImageParameters();
}

void BeamFormerBase::initializeIImageParameters()
{
    QStringList levels = Resource::iimageLevelParams();
    if (levels.size() > 0)
    {
        QList<int> value;
        for (int i = 0; i < pMax(BFPNames::iImageStr) + 1; ++i)
        {
            if (i < levels.size())
            {
                value.append(levels.at(i).toInt());
            }
            else
            {
                value.append(levels.last().toInt());
            }
        }
        setPV(BFPNames::LineiImageSettingIdsStr, VariantUtil::ints2Variant(value));
    }
}

void BeamFormerBase::createPhysicalGeometryControllers()
{
    QList<BCDGeometryController*> roiControllers;
    QList<BCDGeometryController*> gateControllers;

    m_MLineController = new MLineController(m_SonoParameters, this);
    m_MLineController->setControlTable(m_ControlTable);

    m_ZoomBoxController = new ZoomBoxController(m_SonoParameters, this);

    m_SmallZoomBoxController = new SmallZoomBoxController(m_SonoParameters, this);
    m_SmallZoomBoxController->setControlTable(m_ControlTable);
    connect(m_SmallZoomBoxController, SIGNAL(imageShapeUnstable()), this, SLOT(imageShapeIntoUnstable()));

    roiControllers.append(new ROIController(m_SonoParameters, this));
    m_ROIController = roiControllers.last();
    m_ROIController->setControlTable(m_ControlTable);
    connect(m_ROIController, SIGNAL(imageShapeUnstable(int)), this, SLOT(imageShapeIntoUnstable(int)));
    connect(m_ROIController, SIGNAL(elastoImageShapeUnstable()), this, SIGNAL(elastoImageShapeUnstable()));
#ifdef USE_4D
    m_FourDROIController = new FourDRoiController(m_SonoParameters, this);
    m_FourDROIController->setControlTable(m_ControlTable);
    connect(m_FourDROIController, SIGNAL(imageShapeUnstable(int)), this, SLOT(imageShapeIntoUnstable(int)));
#endif
    gateControllers.append(new DSamplingGateController(m_SonoParameters, this));
    m_DSamplingGateController = gateControllers.last();
    m_DSamplingGateController->setControlTable(m_ControlTable);
    connect(m_DSamplingGateController, SIGNAL(imageUnstable(int)), this, SLOT(imageIntoUnstable(int)));
    connect(m_DSamplingGateController, SIGNAL(bcdGeometryChanged()), this, SLOT(onBCDGeometryChanged()));
    initializeDSamplingGateControllers(gateControllers);

    roiControllers.append(new RoiAndDSamplingGateController(m_SonoParameters, this));
    m_ROIAndDSamplingGateController = roiControllers.last();
    m_ROIAndDSamplingGateController->setControlTable(m_ControlTable);
    connect(m_ROIAndDSamplingGateController, SIGNAL(imageUnstable(int)), this, SLOT(imageIntoUnstable(int)));
    connect(m_ROIAndDSamplingGateController, SIGNAL(bcdGeometryChanged()), this, SLOT(onBCDGeometryChanged()));
    connect(m_ROIAndDSamplingGateController, SIGNAL(imageShapeUnstable(int)), this, SLOT(imageShapeIntoUnstable(int)));
    initializeROIControllers(roiControllers);

    // add by liujia
    m_FreeMLineController = new FreeMLineController(m_SonoParameters, this);
    m_FreeMLineController->setControlTable(m_ControlTable);
    connect(m_FreeMLineController, SIGNAL(imageShapeUnstable(int)), this, SLOT(imageShapeIntoUnstable(int)));
}

void BeamFormerBase::initializeROIControllers(const QList<BCDGeometryController*>& roiControllers)
{
    foreach (BCDGeometryController* controller, roiControllers)
    {
        controller->setGateMinHeightMM(m_StaticParameters->dopplerVolMMs().first());
        controller->setGateMaxHeightMM(m_StaticParameters->dopplerVolMMs().last());
        controller->setRoiEdgeLines(0);
    }
}

void BeamFormerBase::initializeDSamplingGateControllers(const QList<BCDGeometryController*>& gateControllers)
{
    foreach (BCDGeometryController* controller, gateControllers)
    {
        controller->setGateMinHeightMM(m_StaticParameters->dopplerVolMMs().first());
        controller->setGateMaxHeightMM(m_StaticParameters->dopplerVolMMs().last());
    }
}

void BeamFormerBase::initializeImageSize()
{
    // RenderWidgetSize、RenderImageSize 主要用于界面绘制
    QSize widgetSize = ModelConfig::instance().value(ModelConfig::RenderWidgetSize, QSize(640, 512)).toSize();
    setPV(BFPNames::RenderWidgetSizeStr, widgetSize);
    QSize renderImageSize = ModelConfig::instance().value(ModelConfig::RenderImageSize, QSize(640, 512)).toSize();
    setPV(BFPNames::RenderImageSizeStr, renderImageSize);
    int renderImageWidth = renderImageSize.width(), renderImageHeight = renderImageSize.height();

    // DSCImageSize 主要用于像素、物理坐标相互转换
    QSize dscSize = ModelConfig::instance().value(ModelConfig::DSCImageSize, QSize(640, 512)).toSize();
    setPV(BFPNames::DSCImageSizeStr, dscSize);

    // ImageSize用来描述从FPGA出来的原始图像大小（主要用于控制字参数计算）
    //宽在线数据版本上并没有实际意义，用DSC Width赋值
    //高在线数据版本上等于PointNumPerLine
    setPV(BFPNames::ImageSizeStr, QSize(dscSize.width(), pIV(BFPNames::PointNumPerLineStr)));

    setPV(BFPNames::LayoutBImageSizeStr, QSize(renderImageWidth, renderImageHeight));
    setPV(BFPNames::BImageSizeStr, QSize(renderImageWidth, renderImageHeight));
    setPV(BFPNames::MImageSizeStr, QSize(renderImageWidth / 2, renderImageHeight));
    setPV(BFPNames::DImageSizeStr, QSize(renderImageWidth, renderImageHeight / 2));
    setPV(BFPNames::RectStr, QRect(0, 0, renderImageWidth, renderImageHeight));

    int curvedWidth = ModelConfig::instance().value(ModelConfig::CurvedWidth, dscSize.width()).toInt();
    setPV(BFPNames::CurvedPanoramicImageSizeStr, QSize(curvedWidth, dscSize.height()));
}

void BeamFormerBase::initializeParameterCalculators()
{

    m_APPressCalculator = new AcousticPowerPressCalculator(curProbe(), m_SonoParameters);

    foreach (const QString& p, m_APPressCalculator->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updateAcousticPower()));
    }

    m_CQYZStepConfig = new CQYZStepConfig();

    if (m_CQYZPreferValueCalculator == NULL)
    {
        m_CQYZPreferValueCalculator = new CQYZPreferValueCalculator();
    }

    m_NeedleSettingSet = new NeedleSettingSet();
}

void BeamFormerBase::initializePostParameterCalculators()
{
}

void BeamFormerBase::createStaticParameters()
{
    m_StaticParameters = new BaseBFStaticParameters();
}

void BeamFormerBase::createRelatedParasController()
{
    m_RelatedParasController = new RelatedParasController(this);
}

void BeamFormerBase::initializeRelatedParasController()
{
    m_RelatedParasController->setControlTable(m_ControlTable);
    m_RelatedParasController->setSonoParameters(m_SonoParameters);
    if (m_SonoParameters != NULL)
    {
        m_RelatedParasController->setDynamicParameter(
            qobject_cast<ControlTableParameter*>(parameter(BFPNames::DynamicRangeStr)));
        m_RelatedParasController->setFrameAvgParameter(
            qobject_cast<ControlTableParameter*>(parameter(BFPNames::FrameAvgStr)));
    }
    else
    {
        m_RelatedParasController->setDynamicParameter(nullptr);
        m_RelatedParasController->setFrameAvgParameter(nullptr);
    }
}

void BeamFormerBase::initializeProbeDection()
{
}

void BeamFormerBase::createParameterModels()
{
}

void BeamFormerBase::createPostProcessHandler()
{
    if (m_PostProcessHandler != nullptr)
    {
        return;
    }

    m_PostProcessHandler = new PostProcessHandlerBase();
}

void BeamFormerBase::initializePostProcessHandler()
{
    if (m_PostProcessHandler == nullptr)
    {
        return;
    }

    m_PostProcessHandler->setRelatedParasController(m_RelatedParasController);
    m_PostProcessHandler->setImageRenderLayoutRects(m_ImageRenderLayoutRects);
    m_PostProcessHandler->setStateManager(m_StateManager);
    m_PostProcessHandler->setDepthParameters(m_DepthParameters);
    m_PostProcessHandler->setStaticParameters(m_StaticParameters);
}

void BeamFormerBase::setCustomGrayMap(int type, const QString& indexStr, const QString& samplePointsStr,
                                      const PresetParameters& presets)
{
    if (presets.contains(indexStr))
    {
        setCustomGrayMap(type, presets.value(indexStr).toInt(), presets.value(samplePointsStr));
    }
    else
    {
        setCustomGrayMap(type, 1, presets.value(samplePointsStr));
    }
}

void BeamFormerBase::setCustomGrayMap(int type, int index, const QVariant& samplePoints)
{
    if (index == 0)
    {
        QList<QPoint> pts = NewtonSamplePointsUtil::toPointList(samplePoints);
        if (pts.count() == ColorMapTypeDef::NewtonSamplePointCount)
        {
            uchar map[256];
            memset(map, 0, sizeof(map));
            // custom calculate
            NewtonIinterpolationAlgorithm alg;
            alg.setParameter(NewtonSamplePointsUtil::toVariantList(samplePoints));
            alg.handle(map, sizeof(map), map, sizeof(map));

            m_ColorMapManager->setGrayMapCurve(type, 0, ByteBuffer(map, sizeof(map)), pts);
        }
        else
        {
            m_ColorMapManager->setGrayMapCurve(type, 0, m_ColorMapManager->grayMap(type, 1),
                                               m_ColorMapManager->grayMapPoints(type, 1));
        }
    }
    else
    {
        m_ColorMapManager->setGrayMapCurve(type, 0, m_ColorMapManager->grayMap(type, index),
                                           m_ColorMapManager->grayMapPoints(type, index));
    }
}

QList<QVariant> BeamFormerBase::getImageModes()
{
    QList<QVariant> modes;

    switch (systemScanMode())
    {
    case SystemScanModeB:
    case SystemScanModeFourDPre:
    {
        modes << ImageEventArgs::ImageB;
    }
    break;
    case SystemScanModeM:
    case SystemScanModeColorM:
    case SystemScanModePDM:
    case SystemScanModeDPDM:
    case SystemScanModeTDIM:
    {
        modes << ImageEventArgs::ImageM;
    }
    break;
    case SystemScanModeColorDoppler:
    case SystemScanModeSonoNeedle:
    case SystemScanModePowerDoppler:
    case SystemScanModeDPowerDoppler:
    case SystemScanModeTissueDoppler:
    case SystemScanModeE:
    case SystemScanModeMVI:
    {
        modes << ImageEventArgs::ImageC;
    }
    break;
    case SystemScanModeLRBM:
    case SystemScanModeLRFreeM:
    case SystemScanModeUDBM:
    case SystemScanModeUDFreeM:
    {
        modes << ImageEventArgs::ImageB << ImageEventArgs::ImageM;
    }
    break;
    case SystemScanModeColorLRBM:
    case SystemScanModePDLRBM:
    case SystemScanModeDPDLRBM:
    case SystemScanModeTDILRBM:
    {
        modes << ImageEventArgs::ImageC << ImageEventArgs::ImageM;
    }
    break;
    case SystemScanModeAV:
    case SystemScanModeBPW:
    case SystemScanModeCWD:
    {
        modes << ImageEventArgs::ImageB << ImageEventArgs::ImageD;
    }
    break;
    case SystemScanModeColorPW:
    case SystemScanModePowerPW:
    case SystemScanModeDPowerPW:
    case SystemScanModeTissuePW:
    case SystemScanModeMVIPW:
    case SystemScanModeCWDColorDoppler:
    case SystemScanModeCWDDirectionalPowerDoppler:
    case SystemScanModeCWDPowerDoppler:
    {
        modes << ImageEventArgs::ImageC << ImageEventArgs::ImageD;
    }
    break;
    case SystemScanModeColorUDBM:
    case SystemScanModePDUDBM:
    case SystemScanModeDPDUDBM:
    case SystemScanModeTDIUDBM:
    {
        modes << ImageEventArgs::ImageC << ImageEventArgs::ImageM;
    }
    break;
    case SystemScanMode2B:
    case SystemScanMode4B:
    {
        int layoutNum = pIV(BFPNames::ImageLayoutNumStr);
        for (int i = 0; i < layoutNum; i++)
        {
            modes << ImageEventArgs::ImageB;
        }
    }
    break;
    default:
        break;
    }

    return modes;
}

bool BeamFormerBase::isProbeIntervalUseHighDensity() const
{
    //使用新控制表后，由FPGA根据高低密度计算
    return false;
}

void BeamFormerBase::changeProbeInterval()
{
    ProbeParameters pParameters(curProbe(), startDepthMM(), pixelSizeMM(), zoomOn(), pIV(BFPNames::StartLineStr),
                                pIV(BFPNames::StopLineStr));

    if (this->curProbe().IsLinear)
    {
        setPV(BFPNames::LineSpacingStr,
              pParameters.probeLineIntervals(isProbePhyParaAffectedByZoomCoef(), pIV(BFPNames::ImageZoomCoefStr),
                                             isProbeIntervalUseHighDensity(), pBV(BFPNames::HighDensityStr)));
        setPV(BFPNames::LineSpacingMMStr, pParameters.probeLineSpacingMM());
    }
    else
    {
        setPV(BFPNames::AngleSpacingStr,
              pParameters.probeAngleInterval(isProbeIntervalUseHighDensity(), pBV(BFPNames::HighDensityStr)));
        setPV(BFPNames::AngleSpacingRadStr, pParameters.probeAngleSpacingRad());
    }
}

void BeamFormerBase::changeProbeDis()
{
    ProbeParameters pParameters(curProbe(), startDepthMM(), pixelSizeMM(), zoomOn(), pIV(BFPNames::StartLineStr),
                                pIV(BFPNames::StopLineStr));

    double slopeDis = pParameters.radiusMM();
    double perpendicularDis = pParameters.perpendicularDisMM();
    double pixelsizeMM = BFDepthParameters::pixelSizeMMWithoutZoomCoef(
        pDV(BFPNames::PixelSizeMMStr), pIV(BFPNames::ImageZoomCoefStr), pDV(BFPNames::RenderImageZoomCofStr),
        pFV(BFPNames::FixedSWImageZoomCofStr));

    setPV(BFPNames::SlopeDisStr, qRound(slopeDis / pixelsizeMM));
    setPV(BFPNames::PerpendicularDisStr, qRound(perpendicularDis / pixelsizeMM));
}

void BeamFormerBase::changeProbeGeometry()
{
    changeProbeInterval();
    changeProbeDis();
}

void BeamFormerBase::changeProbePara()
{
    ControlTableSender ctSender(m_ControlTable);
    m_PostParameterHelper.setIsPostAvailable(false);

    setPV(BFPNames::ProbeCodeStr, curProbe().Code);
    setPV(BFPNames::ProbeIdStr, curProbe().Id);

    setPV(BFPNames::DSCImageRectsStr, m_ImageRenderLayoutRects->getDSCImageRects());

    // phasedarray 下发的探头类型为Linear，
    //但为方便计算坐标变换等内容，在probe.ini中phasedarray探头的 IsLinear 为 false
    setPV(BFPNames::LinearStr, (curProbe().IsPhasedArray || curProbe().IsLinear));
    setPV(BFPNames::DeepStr, curProbe().IsDeep);
    setPV(BFPNames::Deep2Str, curProbe().IsDeep);
    setPV(BFPNames::PhaseProbeIdStr, curProbe().IsPhasedArray);
    setPV(BFPNames::MEProbStr, curProbe().IsMEProbe);
    if (curProbe().IsMEProbe)
    {
        if (curProbe().WaferNum == 192)
        {
            setPV(BFPNames::WaferNumCodeStr, 0);
        }
        else if (curProbe().WaferNum == 256)
        {
            setPV(BFPNames::WaferNumCodeStr, 1);
        }
    }
    setPV(BFPNames::ProbeDimension1P25Str, curProbe().IsDimensionProbe);
    setPV(BFPNames::Pen_prbStr, curProbe().IsPencilProbe);

    setPV(BFPNames::WaferNumDiffStr, curProbe().WaferNumDiff);
    setPV(BFPNames::HighFreqPhasedProbeStr, curProbe().IsHighFreqPhasedProbe);
    setPV(BFPNames::SuperHFreqStr, curProbe().SuperHFreq);
    setPV(BFPNames::ConvexRadiusStr, qRound(curProbe().WaferRadius));
    changeProbeSteerValues();
    setPV(BFPNames::NeedleModeStr, false);
    setPV(BFPNames::IsCenterLineVisibleStr, false);
    setPV(BFPNames::IsBHorizontalRulerVisibleStr, false);
    setPV(BFPNames::IsBiopsyVisibleStr, false);
    setPV(BFPNames::BiopsyAngleStr, curProbe().BiopsyAngle);
    setPV(BFPNames::BiopsyXPosMMStr, curProbe().BiopsyXPosMM);
    setPV(BFPNames::BiopsyYPosMMStr, curProbe().BiopsyYPosMM);
    // added by Jin Yuqi
    setPV(BFPNames::BiopsyAngleOffsetStr, curProbe().BiopsyAngleOffset);
    setPV(BFPNames::BiopsyXPosMMOffsetStr, curProbe().BiopsyXPosMMOffset);
    setPV(BFPNames::BiopsyYPosMMOffsetStr, curProbe().BiopsyYPosMMOffset);

    //从windows平台的xbit迁移变量的时候，增加高压开关相关参数的下发
    setPV(BFPNames::SHVSW_NOStr, curProbe().SHVSW_NO);
    setPV(BFPNames::EHVSW_NOStr, curProbe().EHVSW_NO);
    setPV(BFPNames::HVSW_NUMStr, curProbe().HVSW_NUM);
    setPV(BFPNames::HVSW_DNStr, curProbe().HVSW_DN);
    setPV(BFPNames::HVSW_ConfigStr, curProbe().HVSW_Config);
    //从windows平台的xbit迁移变量的时候，增加探头基元数的下发, 实际使用的基元数
    if (curProbe().IsPhasedArray && curProbe().WaferEnd != 0)
    {
        setPV(BFPNames::PROB_ELEM_NUMStr, curProbe().WaferEnd - curProbe().WaferBegin + 1);
    }
    else if (curProbe().WaferNumDiff != 128)
    {
        setPV(BFPNames::PROB_ELEM_NUMStr, curProbe().WaferNum - curProbe().WaferNumDiff * 2);
    }
    else
    {
        setPV(BFPNames::PROB_ELEM_NUMStr, curProbe().WaferNum);
    }

    setPV(BFPNames::LensCutDepthStr, curProbe().LensCutDepth);
    setPV(BFPNames::TRANSMIT_APERTURE_ADD_SYMMETRYStr, curProbe().TRANSMIT_APERTURE_ADD_SYMMETRY);
    setPV(BFPNames::FreqCenterStr, curProbe().FreqCenter);

    setPV(BFPNames::ClampStr, curProbe().Clamp);

    setPV(BFPNames::WeightSlopeStr, curProbe().WeightSlope);
    setPV(BFPNames::TxSyncSwitchStr, curProbe().TxSyncSwitch);
    setPV(BFPNames::RXGateStr, curProbe().RXGate);
    setPV(BFPNames::BFSRAStr, curProbe().BFSRA);
    setPV(BFPNames::CfmTransmitClampStr, curProbe().CfmTransmitClamp);
    setPV(BFPNames::DSCMidLineDiffStr,
          curProbe().DSCMidLineDiff >= 0 ? curProbe().DSCMidLineDiff : (8 - curProbe().DSCMidLineDiff));
    setPV(BFPNames::TeeFlagStr, curProbe().isTeeProbe);
    controlBiopsyEn();

    if (!curProbe().IsLinear)
    {
        //其他情况，会在setPreset中进行处理，这里只作简单预防
        parameter(BFPNames::TrapezoidalModeStr)->setEnabled(false);
        parameter(BFPNames::VirtualVertexTrapezoidalModeStr)->setEnabled(false);
        if (curProbe().IsPhasedArray)
        {
            parameter(BFPNames::PA_VERT_DIST_EnableStr)->setEnabled(true);
        }
    }
    else
    {
        setPV(BFPNames::LineCpdParaStr, qRound(BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) *
                                               qPow(2, 10) / curProbe().WaferLength));
        parameter(BFPNames::PA_VERT_DIST_EnableStr)->setEnabled(false);
        parameter(BFPNames::VirtualVertexTrapezoidalModeStr)->setEnabled(true);
    }

    controlNeedleModeEn();
    parameter(BFPNames::SteeringAngleStr)->setEnabled(curProbe().IsLinear);
    parameter(BFPNames::DopSteeringAngleStr)->setEnabled(curProbe().IsLinear);
    //临时方案：改变探头时需要提前将梯形成像关闭，否则(parameter(BFPNames::BSteeringScanStr)->update())更新时发现虚拟顶点未关闭，不更新扩展角的值，导致正常状态下图像不对。
    setPV(BFPNames::VirtualVertexTrapezoidalModeStr, false);
    //临时方案：正常来说这个值是和VirtualVertexTrapezoidalModeStr同步的。但是相控阵会单独将其置为true。
    //这个时候在切换回线阵，因为VirtualVertexTrapezoidalModeStr之前是false，上面的代码不会触发更新,导致PA_VERT_DIST_EnableStr没有和VirtualVertexTrapezoidalModeStr同步。
    setPV(BFPNames::PA_VERT_DIST_EnableStr, false);
    parameter(BFPNames::BSteeringScanStr)->update();
    controlBSteer();
    controlECG();
    controlSonoNeedle();
    parameter(BFPNames::ThiModeStr)->update();

    // desc: 临时修改：medica展会参展要求，只有线阵才支持三同步、四同步。
    //    parameter(BFPNames::TriplexModeStr)->setEnabled(!curProbe().IsPhasedArray);
    //    parameter(BFPNames::QuadplexModeStr)->setEnabled(curProbe().IsLinear);

    parameter(BFPNames::CWEnStr)->setEnabled(curProbe().IsNonPhasedProbeCW || curProbe().IsPhasedArray);
    parameter(BFPNames::TDIEnStr)->setEnabled(curProbe().IsPhasedArray);
    parameter(BFPNames::FreeMModeStr)->setEnabled(curProbe().IsPhasedArray);
    setLGCEnable();
    parameter(BFPNames::SampleDepthDopStr)->update();
    updatePrfList();

    parameter(BFPNames::FourDStr)->setEnabled(curProbe().IsFourD);

    emit probeIdChanged();
    emit needleAnglesChanged(needleAngles());

    controlSuperNeedle();

    updateMaxRadiusAngle();
}

bool BeamFormerBase::isProbePhyParaAffectedByZoomCoef() const
{
    return true;
}

void BeamFormerBase::changeProbeSteerValues()
{
    // ECO3 使用CPDSteer控制Cos 和 Tan 结果
}

double BeamFormerBase::pixelSizeMMInProbePhyPara() const
{
    return isProbePhyParaAffectedByZoomCoef()
               ? pDV(BFPNames::PixelSizeMMStr)
               : BFDepthParameters::pixelSizeMMWithoutZoomCoef(
                     pDV(BFPNames::PixelSizeMMStr), pIV(BFPNames::ImageZoomCoefStr),
                     pDV(BFPNames::RenderImageZoomCofStr), pFV(BFPNames::FixedSWImageZoomCofStr));
}

int BeamFormerBase::getAcousticPowerCTValue(int index) const
{
    // range:0~15时，才用此方法，I9直接用的是0~255控制，不用此方法
    if (pMax(BFPNames::AcousticPowerBStr) < 16)
    {
        if (index >= 0 && index < curProbe().BAcousticPowers.count())
        {
            return curProbe().BAcousticPowers.at(index);
        }
        else
        {
            if (index >= 0 && index < 16)
            {
                return (index + 1) * 8 + 127; // 8~128 + 127
            }
        }
    }
    else
    {
        return index;
    }
    return -1;
}

void BeamFormerBase::adjustFocusPosByDepth(const QString& focusNumName, const QString& focusPosName)
{
    m_PostProcessHandler->adjustFocusPosByDepth(focusNumName, focusPosName);
}

int BeamFormerBase::adviseFocusPosB()
{
    return BFFocusParameters().adviseFocusPos(curProbe(), pIV(BFPNames::FocusNumBStr), pIV(BFPNames::FocusPosBStr),
                                              imageBottomDepthMM());
}

int BeamFormerBase::adviseFocusPosM()
{
    return BFFocusParameters().adviseFocusPos(curProbe(), pIV(BFPNames::FocusNumMStr), pIV(BFPNames::FocusPosMStr),
                                              imageBottomDepthMM());
}

int BeamFormerBase::adviseFocusPosC()
{
    return BFFocusParameters().adviseFocusPos(curProbe(), pIV(BFPNames::FocusNumCStr), pIV(BFPNames::FocusPosCStr),
                                              imageBottomDepthMM());
}

bool BeamFormerBase::isScpdEnabled() const
{
    return !((syncMode() != Sync_None && systemScanMode() != SystemScanModeAV) ||
             !isLinerProbeAvailableAccordingScanWidth() || pBV(BFPNames::TrapezoidalModeStr) ||
             pBV(BFPNames::ZoomOnStr));
}

bool BeamFormerBase::isFcpdEnabled() const
{
    return !((syncMode() != Sync_None && systemScanMode() != SystemScanModeAV) || pBV(BFPNames::TrapezoidalModeStr));
}

bool BeamFormerBase::isFcpdControlEnabled() const
{
    return !((syncMode() != Sync_None && systemScanMode() != SystemScanModeAV) || pBV(BFPNames::ScpdOnStr) ||
             pBV(BFPNames::TrapezoidalModeStr));
}

bool BeamFormerBase::isTrapezoidalModeEnabled() const
{
    //非B、放大状态下、扫描线过少时必须关闭，界面变灰
    //其他状态下可以开启，界面上可以控制scpdon，并且是线阵
    return !((syncMode() != Sync_None && systemScanMode() != SystemScanModeAV) ||
             !isLinerProbeAvailableAccordingScanWidth()) &&
           curProbe().IsLinear && !isBSteerOn() && !pBV(BFPNames::NeedleModeStr) && !pBV(BFPNames::ElastoEnStr) &&
           !pBV(BFPNames::ZoomOnStr) && !pBV(BFPNames::ZoomSelectStr) && !isSonoNeedleOn();
}

bool BeamFormerBase::isTrapezoidalModeOn() const
{
    return pBV(BFPNames::TrapezoidalModeStr);
}

/**
 * @brief BeamFormerBase::isLinerProbeAvailableAccordingScanWidth
 * 线阵探头，当扫描宽度小于100%时，会造成图像缺失
 * linear不以扫查宽度来限制而是以以下公式：
 * depth > （扫查宽度）/2tg5
 * @return
 */
bool BeamFormerBase::isLinerProbeAvailableAccordingScanWidth() const
{
    if (!zoomOn()) //放大情况不做限制
    {
        if (curProbe().IsLinear) // 4，线阵探头扫查宽度限制
        {
            double halfWidthMM =
                (pIV(BFPNames::StopLineStr) - pIV(BFPNames::StartLineStr)) / 2 * curProbe().WaferLength / 2;
            double radians = Formula::degree2Rad(5);
            double threshold = halfWidthMM / qTan(radians);

            if (depthMM() > threshold)
            {
                return false;
            }
        }
    }

    return true;
}

/**
 * @brief controlScpdOn 控制scpd开关，调用此函数前，需先调用controlTrapezoidalMode，
 * 保证TrapezoidalMode的变量已经是更新过，如果TrapezoidalMode为true，则controlScpdOn中，
 * 会关闭ScpdOn，会刷新 ScpdTrape参数，此参数中会控制ScpdOn的状态
 */
void BeamFormerBase::controlScpdOn()
{
    QString destScpdStr;
    if (pBV(BFPNames::TrapezoidalModeStr))
    {
        destScpdStr = BFPNames::ScpdTrapeStr;
    }
    else
    {
        destScpdStr = BFPNames::ScpdStr;
    }

    int dest = pIV(destScpdStr);

    int ctValue = m_ControlTable->currentControlTableValue(parameter(BFPNames::ScpdStr));
    if (ctValue < 0)
    {
        return;
    }
    else
    {
        bool change = (ctValue != dest);
        if (change)
        {
            parameter(destScpdStr)->update();
            FreqRelatedParasSender fpSender(m_RelatedParasController);
        }
        parameter(BFPNames::ScpdOnStr)->setEnabled(isScpdEnabled());
    }
}

void BeamFormerBase::controlFcpdOn()
{
    parameter(BFPNames::FcpdOnStr)->setEnabled(isFcpdControlEnabled());
}

void BeamFormerBase::controlTrapezoidalMode()
{
    parameter(BFPNames::TrapezoidalModeStr)->setEnabled(isTrapezoidalModeEnabled());
}

bool BeamFormerBase::isBSteerOn() const
{
    return pIV(BFPNames::BSteeringScanStr) != 20;
}

bool BeamFormerBase::isBSteerEnabled() const
{
    bool isLinear = curProbe().IsLinear;
    bool isTrapezoidalMode = pBV(BFPNames::TrapezoidalModeStr);
    bool isNeedleMode = pBV(BFPNames::NeedleModeStr);
    bool isBiopsyShown = pBV(BFPNames::IsBiopsyVisibleStr);
    bool isCenterLineVisible = pBV(BFPNames::IsCenterLineVisibleStr);
    bool isBHorizontalRulerVisible = pBV(BFPNames::IsBHorizontalRulerVisibleStr);
    bool isFreqSpectrum = pBV(BFPNames::FreqSpectrumStr);
    bool isElasto = pBV(BFPNames::ElastoEnStr);
    bool isRegionZoomSelect = pBV(BFPNames::ZoomSelectStr); // 2023-09-20 Write by AlexWang [bug:66962]
    bool isRegionZoomOn = pBV(BFPNames::ZoomOnStr);         // 2023-09-20 Write by AlexWang [bug:66962]
    bool isEnabled = isLinear && !isTrapezoidalMode && !isNeedleMode && !isBiopsyShown && !isCenterLineVisible &&
                     !isFreqSpectrum && !isElasto && !isBHorizontalRulerVisible && !isSonoNeedleOn() &&
                     !isRegionZoomSelect &&
                     !isRegionZoomOn; // 2023-09-20 Write by AlexWang [bug:66962]
                                      // 进入局部放大预备状态或时局部放大状态时，2D Steer功能去使能
    return isEnabled;
}

void BeamFormerBase::controlBSteer()
{
    parameter(BFPNames::BSteeringScanStr)->setEnabled(isBSteerEnabled());
}

void BeamFormerBase::controlECG()
{
    parameter(BFPNames::ECGEnStr)
        ->setEnabled(Resource::supportProbeId(Resource::FunctionName::ECG).contains(QString::number(curProbe().Id)) ||
                     curProbe().IsPhasedArray);
}

void BeamFormerBase::controlSonoNeedle()
{
    parameter(BFPNames::SonoNeedleStr)
        ->setEnabled(
            Resource::supportProbeId(Resource::FunctionName::SonoNeedle).contains(QString::number(curProbe().Id)));
}

void BeamFormerBase::controlSuperNeedle()
{
    parameter(BFPNames::NeedleModeStr)
        ->setEnabled(
            Resource::supportProbeId(Resource::FunctionName::SuperNeedle).contains(QString::number(curProbe().Id)));
}

void BeamFormerBase::controlECGPos()
{
    bool flagD = (syncMode() & Sync_D) == Sync_D;
    bool flagM = (syncMode() & Sync_M) == Sync_M;
    bool flag4B = systemScanMode() == SystemScanMode4B;

    if (flagD || flagM || flag4B)
    {
        if (m_ECGPosBak == -1)
        {
            m_ECGPosBak = pIV(BFPNames::ECGPosStr);
        }

        if (pIV(BFPNames::ECGPosStr) != 3)
        {
            setPV(BFPNames::ECGPosStr, 3);
        }
    }
    else
    {
        if (m_ECGPosBak != -1)
        {
            setPV(BFPNames::ECGPosStr, m_ECGPosBak);
            m_ECGPosBak = -1;
        }
    }

    bool isEnable = false;
    if (pBV(BFPNames::ECGEnStr))
    {
        isEnable = !(flagD || flagM || flag4B);
    }

    parameter(BFPNames::ECGPosStr)->setEnabled(isEnable);
}

void BeamFormerBase::controlECGDly()
{
    if ((syncMode() & Sync_M) == Sync_M)
    {
        parameter(BFPNames::MECGDlyStr)->update();
    }
    else if (pBV(BFPNames::CWEnStr))
    {
        parameter(BFPNames::CWECGDlyStr)->update();
    }
    else
    {
        parameter(BFPNames::ECGDlyStr)->update();
    }
}

void BeamFormerBase::controlBiopsyEn()
{
    parameter(BFPNames::IsBiopsyVisibleStr)
        ->setEnabled(!pBV(BFPNames::ElastoEnStr) &&
                     !(RealCompare::AreEqual(parameter(BFPNames::BiopsyAngleStr)->value().toDouble(), 0.0) &&
                       RealCompare::AreEqual(parameter(BFPNames::BiopsyXPosMMStr)->value().toDouble(), 0.0)));
}

void BeamFormerBase::controlScanWidthEn()
{
    parameter(BFPNames::ScanWidthStr)->setEnabled(!pBV(BFPNames::ElastoEnStr));
}

void BeamFormerBase::controlNeedleModeEn()
{
    parameter(BFPNames::NeedleModeStr)
        ->setEnabled(syncMode() == Sync_None && curProbe().IsLinear && !pBV(BFPNames::ElastoEnStr));
}

void BeamFormerBase::controlRotationEn()
{
    parameter(BFPNames::RotationStr)->setEnabled(!pBV(BFPNames::ElastoEnStr) && !pBV(BFPNames::FreqSpectrumStr));
}

void BeamFormerBase::controlFocusNumB()
{
    if (pIV(BFPNames::FocusNumBStr) > pMax(BFPNames::FocusNumBStr))
    {
        setPV(BFPNames::FocusNumBStr, pMax(BFPNames::FocusNumBStr));
    }
}

bool BeamFormerBase::isZoomCoefEnabled() const
{
    return m_PostProcessHandler->isZoomCoefEnabled();
}

void BeamFormerBase::controlImageZoomCoef()
{
    if (!isFrozen())
    {
        setPDV(BFPNames::ImageZoomCoefStr,
               isZoomCoefEnabled() ? pIV(BFPNames::ImageZoomCoefBStr) : pIV(BFPNames::ImageZoomCoefOtherStr));
        parameter(BFPNames::ImageZoomCoefStr)->update();
        parameter(BFPNames::ImageZoomCoefStr)->setEnabled(isZoomCoefEnabled());
    }
}

void BeamFormerBase::controlHighDensity()
{
    //    parameter(BFPNames::HighDensityStr)->setEnabled(!pBV(BFPNames::ColorMStr));

    //    if (pBV(BFPNames::ColorMStr))
    //    {
    //        m_SonoParameters->setPV(BFPNames::HighDensityStr, true);
    //    }
    //    else
    {
        if ((syncMode() & Sync_C) == Sync_C)
        {
            //            if (m_HighDensityBak == -1)
            //            {
            //                m_HighDensityBak = m_SonoParameters->pIV(BFPNames::HighDensityStr);
            //            }
            if (m_SonoParameters->pBV(BFPNames::OptimizeFPSStr))
            {
                if (m_HighDensityBak == -1)
                {
                    m_HighDensityBak = m_SonoParameters->pIV(BFPNames::HighDensityStr);
                    m_SonoParameters->setPV(BFPNames::HighDensityStr, false);
                }
            }
        }
        else
        {
            if (m_HighDensityBak != -1)
            {
                m_SonoParameters->setPV(BFPNames::HighDensityStr, m_HighDensityBak);
                m_HighDensityBak = -1;
            }
        }
    }
}

void BeamFormerBase::controlEdge()
{
    if ((syncMode() & Sync_C) == Sync_C)
    {
        QVariant edgeValue = m_SonoParameters->pBV(BFPNames::THIStr) ? m_SonoParameters->pV(BFPNames::EdgeThiIncStr)
                                                                     : m_SonoParameters->pV(BFPNames::EdgeIncStr);
        m_SonoParameters->setPV(BFPNames::EdgeShowStr, edgeValue);
    }
    else
    {
        QVariant edgeValue = m_SonoParameters->pBV(BFPNames::THIStr) ? m_SonoParameters->pV(BFPNames::EdgeThiStr)
                                                                     : m_SonoParameters->pV(BFPNames::EdgeStr);
        m_SonoParameters->setPV(BFPNames::EdgeShowStr, edgeValue);
    }
}

void BeamFormerBase::controlWaveVelocity()
{
    SyncModeType syncmode = syncMode();
    if ((SyncModeType)(syncmode & Sync_M) == Sync_M)
    {
        pBV(BFPNames::FreeMModeStr) ? parameter(BFPNames::FreeMVelocityStr)->update()
                                    : parameter(BFPNames::MVelocityStr)->update();
    }
    else if ((SyncModeType)(syncmode & Sync_D) == Sync_D)
    {
        if (pBV(BFPNames::CWEnStr) && !isHPrfCW())
        {
            parameter(BFPNames::CWDVelocityStr)->update();
        }
        else
        {
            pBV(BFPNames::TDIEnStr) ? parameter(BFPNames::DVelocityTDIStr)->update()
                                    : parameter(BFPNames::DVelocityStr)->update();
        }
    }
}

void BeamFormerBase::controlAcousticPower()
{
    ControlTableSender cs(m_ControlTable);

    SyncModeType syncmode = syncMode();

    bool flagE = pBV(BFPNames::ElastoEnStr);
    bool flagC =
        (((syncmode & Sync_C) == Sync_C && !pBV(BFPNames::FreqSpectrumStr) && !pBV(BFPNames::TriplexModeStr)) ||
         pBV(BFPNames::ColorMStr));
    bool flagD = (syncmode & Sync_D) == Sync_D && (pBV(BFPNames::FreqSpectrumStr) || pBV(BFPNames::TriplexModeStr)) &&
                 (pIV(BFPNames::SystemScanModeStr) != SystemScanModeAV);
    bool flagM = (syncmode == Sync_M) && !pBV(BFPNames::FreeMModeStr);
    bool flagB = !flagC && !flagD && !flagE && !flagM;

    // parameter(BFPNames::AcousticPowerBStr)->setEnabled(flagB);
    // parameter(BFPNames::AcousticPowerBThiStr)->setEnabled(flagB);
    // parameter(BFPNames::AcousticPowerBShowStr)->setEnabled(flagB);
    // parameter(BFPNames::AcousticPowerColorStr)->setEnabled(flagC);
    // parameter(BFPNames::AcousticPowerDopStr)->setEnabled(flagD);

    //每次相关参数改变都重新获取appara和apconfig
    m_APPressCalculator->reset();

    if (flagB)
    {
        parameter(pBV(BFPNames::THIStr) ? BFPNames::AcousticPowerBThiStr : BFPNames::AcousticPowerBStr)->update();
    }
    else if (flagC)
    {
        parameter(BFPNames::AcousticPowerColorStr)->update();
    }
    else if (flagD)
    {
        if (pBV(BFPNames::CWEnStr) && !isHPrfCW())
        {
            parameter(BFPNames::CWTransmitPowerPositiveVolStr)->update();
        }
        else
        {
            parameter(BFPNames::AcousticPowerDopStr)->update();
        }
    }
    else if (flagM)
    {
        parameter(pBV(BFPNames::THIStr) ? BFPNames::AcousticPowerMThiStr : BFPNames::AcousticPowerMStr)->update();
    }
    else if (flagE)
    {
        parameter(BFPNames::AcousticPowerElastoStr)->update();
    }
    else
    {
        Q_ASSERT_X(false, "controlAcousticPower", "bad mode flag");
    }

    controlCompensationCoef();
}

void BeamFormerBase::controlAccCount()
{
    bool flagD = (syncMode() & Sync_D) == Sync_D && (pBV(BFPNames::FreqSpectrumStr) || pBV(BFPNames::TriplexModeStr));
    if (flagD)
    {
        parameter(BFPNames::AccCountDopStr)->update();
    }
    else
    {
        ColorImageModeType colorMode = colorImageMode();

        if (colorMode == (int)Color_CF)
        {
            if (pBV(BFPNames::TDIEnStr))
            {
                parameter(BFPNames::AccCountTDIStr)->update();
            }
            else if (pBV(BFPNames::MVIModeStr))
            {
                parameter(BFPNames::AccCountMVIStr)->update();
            }
            else
            {
                parameter(BFPNames::AccCountColorStr)->update();
            }
        }
        else if (colorMode == (int)Color_PD)
        {
            isSonoNeedleOn() ? parameter(BFPNames::AccCountSNStr)->update()
                             : parameter(BFPNames::AccCountPDStr)->update();
        }
    }
}

void BeamFormerBase::controlColorImageModeParas()
{
    ControlTableSender cs(m_ControlTable);

    controlAccCount();

    ColorImageModeType colorMode = colorImageMode();
    if (colorMode == (int)Color_CF)
    {
        if (pBV(BFPNames::TDIEnStr))
        {
            parameter(BFPNames::TDILineDensityStr)->update();
            parameter(BFPNames::GainTDIStr)->update();
            parameter(BFPNames::PowerThresholdTDIStr)->update();
            parameter(BFPNames::TDIVelocityThresholdStr)->update();
            parameter(BFPNames::TDIRegionThresholdStr)->update();
            parameter(BFPNames::BloodEffectionTDIStr)->update();
            parameter(BFPNames::PacketSizeTDIStr)->update();
            parameter(BFPNames::WFGainControlTDIStr)->update();
            parameter(BFPNames::LComeBackTDIStr)->update();
            parameter(BFPNames::AComeBackTDIStr)->update();
            parameter(BFPNames::TDIIIReStr)->update();
            parameter(BFPNames::TDIWallfilterStr)->update();
            parameter(BFPNames::FrameAvgTDIStr)->update();
            parameter(BFPNames::MBTDIStr)->update();
            parameter(BFPNames::PersistentDeltaTDIStr)->update();
            parameter(BFPNames::TDICETStr)->update();
            parameter(BFPNames::TDICVRTStr)->update();
            parameter(BFPNames::TDICVLTStr)->update();
            parameter(BFPNames::TDICHETStr)->update();
            parameter(BFPNames::TDICTGCStr)->update();
        }
        else if (pBV(BFPNames::MVIModeStr))
        {
            parameter(BFPNames::MVILineDensityStr)->update();
            parameter(BFPNames::GainMVIStr)->update();
            parameter(BFPNames::PowerThresholdMVIStr)->update();
            parameter(BFPNames::MVIVelocityThresholdStr)->update();
            parameter(BFPNames::MVIRegionThresholdStr)->update();
            parameter(BFPNames::BloodEffectionMVIStr)->update();
            parameter(BFPNames::PacketSizeMVIStr)->update();
            parameter(BFPNames::WFGainControlMVIStr)->update();
            parameter(BFPNames::LComeBackMVIStr)->update();
            parameter(BFPNames::AComeBackMVIStr)->update();
            parameter(BFPNames::MVIIIReStr)->update();
            parameter(BFPNames::WallFilterMVIStr)->update();
            parameter(BFPNames::HDCPAWallFilterStr)->update();
            parameter(BFPNames::FrameAvgMVIStr)->update();
            parameter(BFPNames::MBMVIStr)->update();
            parameter(BFPNames::PersistentDeltaMVIStr)->update();
            parameter(BFPNames::MVICETStr)->update();
            parameter(BFPNames::MVICVRTStr)->update();
            parameter(BFPNames::MVICVLTStr)->update();
            parameter(BFPNames::MVICHETStr)->update();
            parameter(BFPNames::MVICTGCStr)->update();
            parameter(BFPNames::MVIPostGainStr)->update();
            parameter(BFPNames::HDCPAWFGainCtrlStr)->update();
            parameter(BFPNames::HDCPACETStr)->update();
            parameter(BFPNames::PLFIR_Coef0Str)->update();
            parameter(BFPNames::PLFIR_Coef1Str)->update();
            parameter(BFPNames::PLFIR_Coef2Str)->update();
            parameter(BFPNames::PAFIR_Coef0Str)->update();
            parameter(BFPNames::PAFIR_Coef1Str)->update();
            parameter(BFPNames::PAFIR_Coef2Str)->update();
            parameter(BFPNames::PAFIR_Coef3Str)->update();
            parameter(BFPNames::PLFIR_ShiftStr)->update();
            parameter(BFPNames::PLFIR_GainStr)->update();
            parameter(BFPNames::PAFIR_ShiftStr)->update();
            parameter(BFPNames::PAFIR_GainStr)->update();
            parameter(BFPNames::PAComeBackStr)->update();
            parameter(BFPNames::PLComeBackStr)->update();
            parameter(BFPNames::PLFIR_Coef0Str)->update();
            parameter(BFPNames::PLFIR_Coef0Str)->update();
            parameter(BFPNames::PLFIR_Coef0Str)->update();
        }
        else
        {
            parameter(BFPNames::ColorLineDensityStr)->update();
            parameter(BFPNames::GainColorStr)->update();
            parameter(BFPNames::PowerThresholdStr)->update();
            parameter(BFPNames::CFVelocityThresholdStr)->update();
            parameter(BFPNames::ColorRegionThresholdStr)->update();
            parameter(BFPNames::BloodEffectionStr)->update();
            parameter(BFPNames::PacketSizeStr)->update();
            parameter(BFPNames::WFGainControlStr)->update();
            parameter(BFPNames::LComeBackStr)->update();
            parameter(BFPNames::AComeBackStr)->update();
            parameter(BFPNames::CFMIIReStr)->update();
            parameter(BFPNames::WallFilterColorStr)->update();
            parameter(BFPNames::FrameAvgColorStr)->update();
            parameter(BFPNames::MBColorStr)->update();
            parameter(BFPNames::PersistentDeltaCFMStr)->update();
            parameter(BFPNames::CETStr)->update();
            parameter(BFPNames::CVRTStr)->update();
            parameter(BFPNames::CVLTStr)->update();
            parameter(BFPNames::CHETStr)->update();
            parameter(BFPNames::CTGCStr)->update();
        }
    }
    else if (colorMode == (int)Color_PD)
    {
        if (isSonoNeedleOn())
        {
            parameter(BFPNames::ColorLineDensitySNStr)->update();
            parameter(BFPNames::GainSNStr)->update();
            parameter(BFPNames::PowerThresholdSNStr)->update();
            parameter(BFPNames::SNVelocityThresholdStr)->update();
            parameter(BFPNames::SNRegionThresholdStr)->update();
            parameter(BFPNames::BloodEffectionSNStr)->update();
            parameter(BFPNames::PacketSizeSNStr)->update();
            parameter(BFPNames::WFGainControlSNStr)->update();
            parameter(BFPNames::LComeBackSNStr)->update();
            parameter(BFPNames::AComeBackSNStr)->update();
            parameter(BFPNames::SNIIReStr)->update();
            parameter(BFPNames::WallFilterSNStr)->update();
            parameter(BFPNames::FrameAvgSNStr)->update();
            parameter(BFPNames::MBSNStr)->update();
            parameter(BFPNames::PersistentDeltaSNStr)->update();
            parameter(BFPNames::SNCETStr)->update();
            parameter(BFPNames::SNCVRTStr)->update();
            parameter(BFPNames::SNCVLTStr)->update();
            parameter(BFPNames::SNCHETStr)->update();
            parameter(BFPNames::SNCTGCStr)->update();
        }
        else
        {
            parameter(BFPNames::ColorLineDensityStr)->update();
            parameter(BFPNames::GainPDStr)->update();
            parameter(BFPNames::PowerThresholdPDStr)->update();
            parameter(BFPNames::PDVelocityThresholdStr)->update();
            parameter(BFPNames::PDRegionThresholdStr)->update();
            parameter(BFPNames::BloodEffectionPDStr)->update();
            parameter(BFPNames::PacketSizePDStr)->update();
            parameter(BFPNames::WFGainControlPDStr)->update();
            parameter(BFPNames::LComeBackPDStr)->update();
            parameter(BFPNames::AComeBackPDStr)->update();
            parameter(BFPNames::PDIIReStr)->update();
            parameter(BFPNames::WallFilterPDStr)->update();
            parameter(BFPNames::FrameAvgPDStr)->update();
            parameter(BFPNames::MBPDStr)->update();
            parameter(BFPNames::PersistentDeltaPDStr)->update();
            parameter(BFPNames::PDCETStr)->update();
            parameter(BFPNames::PDCVRTStr)->update();
            parameter(BFPNames::PDCVLTStr)->update();
            parameter(BFPNames::PDCHETStr)->update();
            parameter(BFPNames::PDCTGCStr)->update();
        }
    }
    controlColorCoefPara();
    controlBSteer();
}

void BeamFormerBase::controlColorCoefPara()
{
    ColorImageModeType colorMode = colorImageMode();
    if (colorMode == (int)Color_PD)
    {
        isSonoNeedleOn() ? parameter(BFPNames::SNCoefStr)->update() : parameter(BFPNames::PDCoefStr)->update();
    }
    else
    {
        if (pBV(BFPNames::TDIEnStr))
        {
            parameter(BFPNames::TDICoefStr)->update();
        }
        else if (pBV(BFPNames::MVIModeStr))
        {
            parameter(BFPNames::MVICoefStr)->update();
        }
        else
        {
            parameter(BFPNames::ColorCoefStr)->update();
        }
    }
}

void BeamFormerBase::controlPWTDITMParas()
{
    ControlTableSender cs(m_ControlTable);

    if (pBV(BFPNames::TriplexModeStr))
    {
        parameter(BFPNames::PWEnhanceTMStr)->update();
    }
    else if (pBV(BFPNames::TDIEnStr))
    {
        parameter(BFPNames::PWEnhanceTDIStr)->update();
    }
    else if (pBV(BFPNames::CWEnStr))
    {
        parameter(BFPNames::PWEnhanceCWDStr)->update();
    }
    else
    {
        parameter(BFPNames::PWEnhanceStr)->update();
    }
}

void BeamFormerBase::controlCTMPDTDIParas()
{
    QString targetName = BFPNames::DummyEnSampleStr;
    if (pBV(BFPNames::TriplexModeStr))
    {
        targetName = BFPNames::DummyEnSampleTMStr;
    }
    else if (pBV(BFPNames::TDIEnStr))
    {
        targetName = BFPNames::DummyEnSampleTDIStr;
    }
    else if (pBV(BFPNames::MVIModeStr))
    {
        targetName = BFPNames::DummyEnSampleMVIStr;
    }
    else if (colorImageMode() == Color_PD)
    {
        if (isSonoNeedleOn())
        {
            targetName = BFPNames::DummyEnSampleSNStr;
        }
        else
        {
            targetName = BFPNames::DummyEnSamplePDStr;
        }
    }
    parameter(targetName)->update();
}

void BeamFormerBase::controlTMTDICWDParas()
{
    ControlTableSender cs(m_ControlTable);

    if (pBV(BFPNames::CWEnStr))
    {
        parameter(BFPNames::GainDopCWDStr)->update();
        parameter(BFPNames::PixelRatioCWDStr)->update();
        parameter(BFPNames::PWDynamicRangeCWDStr)->update(true);
        parameter(BFPNames::AudioFilterCoefSelCWDStr)->update();
    }
    else if (pBV(BFPNames::TriplexModeStr))
    {
        parameter(BFPNames::GainDopTMStr)->update();
        parameter(BFPNames::PixelRatioTMStr)->update();
        parameter(BFPNames::PWDynamicRangeTMStr)->update(true);
        parameter(BFPNames::AudioFilterCoefSelTMStr)->update();
    }
    else if (pBV(BFPNames::TDIEnStr))
    {
        parameter(BFPNames::GainDopTDIStr)->update();
        parameter(BFPNames::PixelRatioTDIStr)->update();
        parameter(BFPNames::PWDynamicRangeTDIStr)->update(true);
        parameter(BFPNames::AudioFilterCoefSelTDIStr)->update();
    }
    else
    {
        parameter(BFPNames::GainDopStr)->update();
        parameter(BFPNames::PixelRatioStr)->update();
        parameter(BFPNames::PWDynamicRangeStr)->update(true);
        parameter(BFPNames::AudioFilterCoefSelStr)->update();
    }
}

void BeamFormerBase::controlTDICWDParas()
{
    ControlTableSender cs(m_ControlTable);

    if (pBV(BFPNames::CWEnStr) && !isHPrfCW())
    {
        parameter(BFPNames::GateSegmentCWDStr)->update();
        parameter(BFPNames::BaseLineCWDStr)->update();
        parameter(BFPNames::WallFilterCWDStr)->update();
        parameter(BFPNames::VolumeCWDStr)->update();
        parameter(BFPNames::CWDPluseNumStr)->update();
        parameter(BFPNames::CWSampleNumStr)->update();
        parameter(BFPNames::CWAudioSegmentStr)->update();
        parameter(BFPNames::CWAudioPreGainStr)->update();
        parameter(BFPNames::CWAudioPostGainStr)->update();
        parameter(BFPNames::CWMidGainStr)->update();
        parameter(BFPNames::CWTimeFilterStr)->update();
        parameter(BFPNames::CWOutGainStr)->update();
        parameter(BFPNames::CWFilterLengthStr)->update();
        parameter(BFPNames::CWVelocityFilterStr)->update();
    }
    else if (pBV(BFPNames::TDIEnStr))
    {
        parameter(BFPNames::GateSegmentTDIStr)->update();
        parameter(BFPNames::BaseLineDTDIStr)->update();
        parameter(BFPNames::WallFilterDopTDIStr)->update();
        parameter(BFPNames::VolumeTDIStr)->update();
        parameter(BFPNames::DPulseNumTDIStr)->update();
        parameter(BFPNames::TDSampleNumStr)->update();
        parameter(BFPNames::TDAudioSegmentStr)->update();
        parameter(BFPNames::TDAudioPreGainStr)->update();
        parameter(BFPNames::TDAudioPostGainStr)->update();
        parameter(BFPNames::TDMidGainStr)->update();
        parameter(BFPNames::TDTimeFilterStr)->update();
        parameter(BFPNames::TDOutGainStr)->update();
        parameter(BFPNames::TDFilterLengthStr)->update();
        parameter(BFPNames::TDVelocityFilterStr)->update();
    }
    else
    {
        parameter(BFPNames::GateSegmentStr)->update();
        parameter(BFPNames::BaseLineStr)->update();
        parameter(BFPNames::WallFilterDopStr)->update();
        parameter(BFPNames::VolumeStr)->update();
        parameter(BFPNames::DPulseNumStr)->update();
        parameter(BFPNames::DopSampleNumStr)->update();
        parameter(BFPNames::DopAudioSegmentStr)->update();
        parameter(BFPNames::DopAudioPreGainStr)->update();
        parameter(BFPNames::DopAudioPostGainStr)->update();
        parameter(BFPNames::DopMidGainStr)->update();
        parameter(BFPNames::DopTimeFilterStr)->update();
        parameter(BFPNames::DopOutGainStr)->update();
        parameter(BFPNames::DopFilterLengthStr)->update();
        parameter(BFPNames::DopVelocityFilterStr)->update();
    }
}

/**
 * @brief BeamFormerBase::controlCompensationCoef
 * 增益补偿,当B进入C或者D时，需要进行一个增益补偿
 * 追加1个控制字用来补偿从b->c,b图像增益变化的，
 * 这个需要软件在进入C后根据c的声功率与B的声功率算出一个比值，
 * 将比值乘以256后下发下去来保持b的图像增益，最大支持8倍
 * 大于等于8倍的时候发全1
 */
void BeamFormerBase::controlCompensationCoef()
{
    double compensationCoef = 1;

    bool flagC =
        (((syncMode() & Sync_C) == Sync_C && !pBV(BFPNames::FreqSpectrumStr) && !pBV(BFPNames::TriplexModeStr)) ||
         pBV(BFPNames::ColorMStr));
    bool flagD = (syncMode() & Sync_D) == Sync_D && (pBV(BFPNames::FreqSpectrumStr) || pBV(BFPNames::TriplexModeStr)) &&
                 (pIV(BFPNames::SystemScanModeStr) != SystemScanModeAV);

    int acousticPowerB = pBV(BFPNames::THIStr) ? parameter(BFPNames::AcousticPowerBThiStr)->controlTableValue()
                                               : parameter(BFPNames::AcousticPowerBStr)->controlTableValue();

    if (flagD)
    {
        bool isCW = pBV(BFPNames::CWEnStr) && !isHPrfCW();
        compensationCoef = acousticPowerB * 1.0 /
                           (isCW ? parameter(BFPNames::CWTransmitPowerPositiveVolStr)->controlTableValue()
                                 : parameter(BFPNames::AcousticPowerDopStr)->controlTableValue());
    }
    else if (flagC)
    {
        compensationCoef = acousticPowerB * 1.0 / parameter(BFPNames::AcousticPowerColorStr)->controlTableValue();
    }

    if (compensationCoef < 8)
    {
        setPV(BFPNames::CompensationCoefStr, (int)(pMax(BFPNames::GainStr) * compensationCoef));
    }
    else
    {
        setPV(BFPNames::CompensationCoefStr, (int)((pMax(BFPNames::GainStr) + 1) * 8 - 1));
    }
}

void BeamFormerBase::controlColorFreqIndex()
{
    if (pBV(BFPNames::NeedleModeStr))
    {
        // SpuerNeedle模式和彩色模式共用了CFMDigitalTgc0-15，在后台设置页面中两者的设置tab是互斥的,这里也加上互斥控制
        //否则SpuerNeedle下冻结解冻后needleAngle后台参数刷新后会再次被colorFreq的后台参数覆盖。
        return;
    }

    if (pBV(BFPNames::TDIEnStr))
    {
        parameter(BFPNames::FreqIndexTDIStr)->update();
    }
    else if (pBV(BFPNames::MVIModeStr))
    {
        parameter(BFPNames::FreqIndexMVIStr)->update();
    }
    else if (colorImageMode() == Color_PD)
    {
        isSonoNeedleOn() ? parameter(BFPNames::FreqIndexSNStr)->update()
                         : parameter(BFPNames::FreqIndexPDStr)->update();
    }
    else if (pBV(BFPNames::ElastoEnStr))
    {
        parameter(BFPNames::FreqIndexElastoStr)->update();
    }
    else
    {
        parameter(BFPNames::FreqIndexColorStr)->update();
    }
}

void BeamFormerBase::controlDopFreqIndex()
{
    if (pBV(BFPNames::TDIEnStr))
    {
        parameter(BFPNames::FreqIndexTDStr)->update();
    }
    else if (pBV(BFPNames::CWEnStr) && !isHPrfCW())
    {
        parameter(BFPNames::FreqIndexCWDStr)->update();
    }
    else
    {
        parameter(BFPNames::FreqIndexDopStr)->update();
    }
}

void BeamFormerBase::controlPWTMParas()
{
    parameter(rvFNoStr())->update();
    controlPWTMColorParas();
}

void BeamFormerBase::controlPWTMColorParas()
{
    if (!pBV(BFPNames::FreqSpectrumStr) && !pBV(BFPNames::TriplexModeStr))
    {
        parameter(BFPNames::CfmMaxTransmitApertureControlStr)->update();
        parameter(BFPNames::CfmMinTransmitApertureControlStr)->update();
        parameter(BFPNames::CTxPulseDutyStr)->update();
    }
    else
    {
        parameter(BFPNames::CfmMaxTransmitApertureControl_PWStr)->update();
        parameter(BFPNames::CfmMinTransmitApertureControl_PWStr)->update();
        parameter(BFPNames::CTxPulseDuty_PWStr)->update();
    }
}

void BeamFormerBase::controlColorInvert()
{
    if (pBV(BFPNames::TDIEnStr)) // TDI
    {
        parameter(BFPNames::TDIInvertStateStr)->update();
    }
    else if (colorImageMode() == Color_PD && pBV(BFPNames::Bi_CPAStr)) // DPD
    {
        parameter(BFPNames::DPDInvertStateStr)->update();
    }
    else if (pBV(BFPNames::ElastoEnStr))
    {
        parameter(BFPNames::ElastoColorMapIndexStr)->update();
    }
    else if (pBV(BFPNames::MVIModeStr))
    {
        parameter(BFPNames::MVIInvertStateStr)->update();
    }
    else
    {
        parameter(BFPNames::ColorInvertStateStr)->update();
    }
}

void BeamFormerBase::controlCFMVelLevel()
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        parameter(BFPNames::CFMVelLevelStr)->update();
    }
    else
    {
        parameter(BFPNames::CFMVelLevelTMStr)->update();
    }
}

void BeamFormerBase::controlFrameScapeEnable()
{
    if (pBV(BFPNames::TriplexModeStr))
    {
        parameter(BFPNames::FrameScapeEnableTMStr)->update();
    }
    else
    {
        parameter(BFPNames::FrameScapeEnableStr)->update();
    }
}

void BeamFormerBase::controlTGCCTValue()
{
    QStringList tgcStrs;
    for (int i = 0; i < TGC_COUNT; ++i)
    {
        tgcStrs << QString("%1%2").arg(BFPNames::TGCStr).arg(i + 1);
    }
    tgcStrs << BFPNames::UniformityTgcStrs;

    foreach (const QString& name, tgcStrs)
    {
        sendParasControltable(name);
    }
}

void BeamFormerBase::sendParasControltable(const QString& name)
{
    setPDV(name, pIV(name));               //更新控制字
    m_ControlTable->send(parameter(name)); //下发控制字
}

void BeamFormerBase::controlBTgcGain()
{
    m_PostProcessHandler->controlBTgcGain();
}

void BeamFormerBase::controlBCImagesOn()
{
    bool dualImagesOn =
        ((systemScanMode() == SystemScanModeColorDoppler || systemScanMode() == SystemScanModePowerDoppler ||
          systemScanMode() == SystemScanModeDPowerDoppler || systemScanMode() == SystemScanModeMVI ||
          systemScanMode() == SystemScanModeB) &&
         !pBV(BFPNames::IsDopplerScanLineVisibleStr) && !pBV(BFPNames::IsCWDScanLineVisibleStr) &&
         pIV(BFPNames::LayoutStr) == Layout_1x1);

    parameter(BFPNames::BCImagesOnStr)->setEnabled(dualImagesOn);
    parameter(BFPNames::BMVIBImagesStr)->setEnabled(dualImagesOn);

    // 实现切模式退出BBC(初始BBC的模式除外，如MVI、Elasto)，冻结解冻保持BBC状态
    static SystemScanMode preSystemScanMode = systemScanMode();
    if ((preSystemScanMode != systemScanMode()) && (systemScanMode() != SystemScanModeMVI) &&
        (systemScanMode() != SystemScanModeE))
    {
        setPV(BFPNames::BCImagesOnStr, false);
    }

    preSystemScanMode = systemScanMode();
}

void BeamFormerBase::setLGCEnable()
{
    if (!parameter(BFPNames::LGCEnStr)->isNull())
    {
        setPV(BFPNames::LGCEnStr, isLGCAvailable());
    }
}

int BeamFormerBase::spacialCompoundInPresets(const PresetParameters& presets)
{
    if (presets.contains(BFPNames::SpacialCompoundStr) && presets.contains(BFPNames::ScpdStr) &&
        presets.value(BFPNames::ScpdStr).toInt() == 0)
    {
        //老的eco1是以SpacialCompoundStr作为SCPD的控制的,现在统一为ScpdStr做控制，
        //此处是为了兼容老的eco1的preset，老的eco1的preset中同时存了SpacialCompoundStr和ScpdStr，
        //并且SpacialCompoundStr是有效的，ScpdStr的值为0，这里使用SpacialCompoundStr设置给ScpdStr
        // 20130423
        return presets.value(BFPNames::SpacialCompoundStr).toInt();
    }
    else
    {
        return 0;
    }
}

int BeamFormerBase::minPulseNum(ControlTableParameter* pulsePara)
{
    if (pulsePara != NULL)
    {
        int min, max, step;
        pulsePara->calcMinMaxStepByBitInfos(min, max, step);
        return min + 1;
    }
    else
    {
        return 0;
    }
}

int BeamFormerBase::maxPulseNum(ControlTableParameter* pulsePara)
{
    if (pulsePara != NULL)
    {
        int min, max, step;
        pulsePara->calcMinMaxStepByBitInfos(min, max, step);
        return max + 1;
    }
    else
    {
        return 0;
    }
}

int BeamFormerBase::pulseNumV2CTValue(const QVariant& value, const QString& pulseNumName)
{
    int max = pMax(pulseNumName);
    if (max > 0)
    {
        // Para->BPulseNum:1、2、3、4 对应控制表的值:1、2、3、0
        return value.toInt() % max;
    }
    else
    {
        return 0;
    }
}

void BeamFormerBase::updateHeightFactoryWithPostProcess()
{
    if (m_SonoParameters != nullptr)
    {
        m_DepthParameters->setHeightFactor(pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>().yScale);
    }
}

void BeamFormerBase::suspendBFIODeviceRead()
{
    //在暂停读数据之前，先等待100ms，因为此时有可能接口层还在下发数据，此时如果马上停止读数据，
    //可能导致FPGA无法接收到下发的数据，因为此时FPGA在往68013发送数据还没返回，没有机会接收数据，
    //目前主要用于控制冻结操作，否则有可能出现，按了冻结键，但实际FPGA没冻结，探头还在发射
    Util::usleep(100000);
    m_BFIODevice->suspendRead();
}

void BeamFormerBase::setGainCoe(int compoundOld, int freqOld, int compoundNew, int freqNew)
{
    int coeOld = curProbe().gainCoe(compoundOld, freqOld);
    int coeNew = curProbe().gainCoe(compoundNew, freqNew);

    if (coeOld <= 0 || coeNew <= 0)
    {
        return;
    }

    qDebug() << "setGainCoe" << coeOld << coeNew;

    int gainNew = (float)pIV(BFPNames::GainStr) * (float)coeNew / (float)coeOld;

    int min = pMin(BFPNames::GainStr);
    int max = pMax(BFPNames::GainStr);

    if (gainNew > max)
    {
        gainNew = max;
    }
    else if (gainNew < min)
    {
        gainNew = min;
    }

    setPV(BFPNames::GainStr, gainNew);
}

void BeamFormerBase::sendFreqdynamic()
{
    if (!m_ControlTable->isInitialized() || isFrozen())
    {
        return;
    }
}

void BeamFormerBase::sendDynamicdata()
{
    m_BlockDataSender->sendNormalBlockDataGroup(curProbe().Name);
    calculateBlockDataAndSend();

    if (m_BlockDataSender->containsPara(curProbe().Name, BFPNames::FreqSpectrumStr))
    {
        //强制下发 B Mode data40
        setPV(BFPNames::FreqSpectrumStr, false, true);
    }

    //    TimeLogger tl;
    //    if (!m_ControlTable->isInitialized() || isFrozen())
    //    {
    //        return;
    //    }

    //    {
    //        ControlTableSender cSender(m_ControlTable);
    //        setPV(BFPNames::WrResetStr, true);
    //        //发送块数据时，设成false，发送完块数据，设置完预设值后，设为true
    //        setPV(BFPNames::ConfigDoneStr, false);
    //    }
    //    //Thread.Sleep(100);

    //    setPV(BFPNames::WrResetStr, false);
    //    closeTransferSignal();

    //    const int blockLen = 2048;
    //    uchar* readData = new uchar[blockLen];

    //    QString filename;
    //    QString log;
    ////    int count = 0;
    //    for(int index=30; index<140; index++)
    //    {
    //        filename = Resource::dynDataDir() + QString("/%1/data%2").arg(this->curProbe().DynFileName).arg(index);
    //        if (QFile::exists(filename))
    //        {
    //            QFile file(filename);
    //            if (file.open(QIODevice::ReadOnly))
    //            {
    //                //AppLogger::info("sendDynamicdata %1", filename);
    //                memset(readData, 0, blockLen);
    //                while(file.read((char *)readData, blockLen) > 0)
    //                {
    //                    m_ControlTable->sendBlock(blockLen, readData, false, index);
    //                    memset(readData, 0, blockLen);
    //                }

    ////                if ((++count) % 10 == 0)
    ////                {
    ////                    Util::processEvents(QEventLoop::ExcludeUserInputEvents, 1, QString());
    ////                }
    //            }
    //            else
    //            {
    //                AppLogger::fileNotOpenError(file, filename);
    //            }
    //        }
    //        else
    //        {
    //            log.append(QString("%1 ").arg(filename));
    //        }
    //    }

    ////    if (!log.isEmpty())
    ////    {
    ////        AppLogger::info(QString("Dynamicdata %1 not exists").arg(log));
    ////    }
    //    AppLogger::info(QString("send %1's Dynamicdata finished").arg(curProbe().Name));

    //    delete [] readData;

    //    tl.logger("sendDynamicdata");

    //    openTransferSignal();

    //    //数据块发送完毕则将该参数置1
    //    setPV(BFPNames::ConfigDoneStr, true);
}

void BeamFormerBase::sendRvFNoData()
{
    sendBRvParas();

    BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name,
                                     QStringList() << BFPNames::RvFNoStr << BFPNames::WeightedCurveStr);
    QString paraName = m_BlockDataParaNameConverter->groupParaName(BFPNames::RvFNoStr);
    // TODO,造影功能还没有，先将相关的代码写在这，造影专有的data40（来自xbit）
    //    if (pBV(BFPNames::ContrastSWStr))
    //    {
    //        paraName = "RvFNoCEUS";
    //    }
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name, paraName, pIV(rvFNoStr()));
    onWeightedCurveChanging(pIV(BFPNames::WeightedCurveStr));
}

void BeamFormerBase::sendTxRvParas(int index, float dis, int rvUnitVariFocusNum, const QString& fNumIntegerStr,
                                   const QString& fNumDecimalStr)
{
    if (index >= 0 && index < m_StaticParameters->txValues().count())
    {
        double fNum = m_StaticParameters->txValues()[index];
        double ret = dis * rvUnitVariFocusNum / (fNum * curProbe().Pitch);
        int integerPart = (int)ret;
        int decimalPart = Util::floatToBinary(ret - integerPart, 12, false);

        setPV(fNumIntegerStr, integerPart);
        setPV(fNumDecimalStr, decimalPart);
    }
}

void BeamFormerBase::sendAllTxRvParas()
{
    sendBTxParas();
    sendBRvParas();
    sendCfmTxParas();
    sendCfmRvParas();
    sendDopTxParas();
    sendDopRvParas();
}

void BeamFormerBase::sendBTxParas()
{
    //任意线密度方案下，不需要
    sendTxRvParas(pIV(BFPNames::TxFNoStr), pFV(BFPNames::TransmitStepDisStr), 1, BFPNames::FNumIntegerStr,
                  BFPNames::FNumDecimalStr);
}

void BeamFormerBase::sendTriplexTxParas()
{
    //任意线密度方案下，不需要
    sendTxRvParas(pIV(BFPNames::TriplexTxFNoStr), pFV(BFPNames::TransmitStepDisStr), 1, BFPNames::FNumIntegerStr,
                  BFPNames::FNumDecimalStr);
}

void BeamFormerBase::sendBRvParas()
{
    sendTxRvParas(pIV(rvFNoStr()), pFV(BFPNames::ReceiveFocusDisStr), pIV(BFPNames::ReceiveUnitVariFocusNumStr),
                  BFPNames::ReceiveFNumIntegerStr, BFPNames::ReceiveFNumDecimalStr);
}

void BeamFormerBase::sendCfmTxParas()
{
    sendTxRvParas(pIV(BFPNames::CfmFnumStr), pFV(BFPNames::CfmTransmitStepDisStr), 1, BFPNames::CfmFNumIntegerStr,
                  BFPNames::CfmFNumDecimalStr);
}

void BeamFormerBase::sendCfmRvParas()
{
    sendTxRvParas(pIV(BFPNames::CfmRxFnumStr), pFV(BFPNames::CfmReceiveFocusDisStr),
                  pIV(BFPNames::CfmReceiveUnitVariFocusNumStr), BFPNames::CfmReceiveFNumIntegerStr,
                  BFPNames::CfmReceiveFNumDecimalStr);
}

void BeamFormerBase::sendDopTxParas()
{
    sendTxRvParas(pIV(BFPNames::DopFNumStr), pFV(BFPNames::DopTransmitStepDisStr), 1, BFPNames::DopFNumIntegerStr,
                  BFPNames::DopFNumDecimalStr);
}

void BeamFormerBase::sendDopRvParas()
{
    sendTxRvParas(pIV(BFPNames::DopRvFNumStr), pFV(BFPNames::DopReceiveFocusDisStr),
                  pIV(BFPNames::DopReceiveUnitVariFocusNumStr), BFPNames::DopReceiveFNumIntegerStr,
                  BFPNames::DopReceiveFNumDecimalStr);
}

int BeamFormerBase::rvApertureCoef(int rvFno) const
{
    if (rvFno >= 0 && rvFno < m_StaticParameters->txValues().count())
    {
        double fNum = m_StaticParameters->txValues()[rvFno];
        // B接收孔径系数=取整[8192*变焦距离/(B-RvFNo*pitch)]
        // C接收孔径系数=取整[8192*变焦距离/(C-RvFNo*pitch)]
        // D接收孔径系数=取整[8192*变焦距离/(D-RvFNo*pitch)]
        // 变焦距离=8*0.01925=0.154mm
        return (int)(8192 * BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) * 8 / (curProbe().Pitch * fNum));
    }
    else
    {
        return 0;
    }
}

int BeamFormerBase::txApertureCoef(int txFno) const
{
    if (txFno >= 0 && txFno < m_StaticParameters->txValues().count())
    {
        double fNum = m_StaticParameters->txValues()[txFno];
        // B发射孔径系数=取整[8192/(B-TxFNo*pitch)]
        // C发射孔径系数=取整[8192/(C-TxFNo*pitch)]
        // D发射孔径系数=取整[8192/(D-TxFNo*pitch)]
        return 8192 / curProbe().Pitch / fNum;
    }
    else
    {
        return 0;
    }
}

double BeamFormerBase::txApertureCoefOriginalValue(int txFno) const
{
    if (txFno >= 0 && txFno < m_StaticParameters->txValues().count())
    {
        return m_StaticParameters->txValues()[txFno];
    }
    else
    {
        return 0.0;
    }
}

void BeamFormerBase::sendCQYZBlockDataGroup()
{
    if (NULL == m_BlockDataSender)
    {
        return;
    }

    int cpdNum = 5;
    int cqyzNum = 31;
    int cpdIndex =
        qBound(0, pIV(BFPNames::CPDSteerStr) - 3, cpdNum - 1); // cpdsteer:0~30, 0~3:下发0,7~30:下发4,其余为1.2.3
    //此函数只有ebit有用，如果以后ebit要增加newiimage的各种功能，要考虑这里的cqyz的使用
    int cqyzIndex = qBound(0, pIV(BFPNames::CQYZStr) - 1, cqyzNum - 1);
    int densityIndex = pBV(BFPNames::HighDensityStr) ? 1 : 0; // 0:lowdensity 1:highdensity

    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_BlockDataParaNameConverter->groupParaName(BFPNames::CQYZStr),
                                              densityIndex * cpdNum * cqyzNum + cpdIndex * cqyzNum + cqyzIndex);
}

void BeamFormerBase::sendCurvedCpdTxBlockDataGroup()
{
    int cpdNum = 5;
    int txNum = parameter(BFPNames::TxFNoStr)->valueCount();
    int cpdIndex =
        qBound(0, pIV(BFPNames::CPDSteerStr) - 3, cpdNum - 1); // cpdsteer:0~30, 0~3:下发0,7~30:下发4,其余为1.2.3

    BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name, QStringList() << "CurvedCpdTx");
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_BlockDataParaNameConverter->groupParaName("CurvedCpdTx"),
                                              cpdIndex * 2 * txNum + pIV(BFPNames::TxFNoStr));
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_BlockDataParaNameConverter->groupParaName("CurvedCpdTx"),
                                              (cpdIndex * 2 + 1) * txNum + pIV(BFPNames::TxFNoStr));
}

int BeamFormerBase::cWFocusLevelCount() const
{
    return 32;
}

double BeamFormerBase::cWBlockDataDepthMM() const
{
    return curProbe().IsHighFreqPhasedProbe ? 5 : 15;
}

double BeamFormerBase::cWBlockDataDepthStep() const
{
    return curProbe().IsHighFreqPhasedProbe ? 2.5 : 5;
}

int BeamFormerBase::cwScanLine() const
{
    return pIV(BFPNames::DScanLineStr) + pIV(BFPNames::CWScanLineDeltaStr); // CW 不会同时开TDI,所以不用 DScanLineTDIStr
}

void BeamFormerBase::sendCWFocusBlockDataGroup()
{
    //每次 CW采样门参数变化， DScanLine 都会update，为避免重复所以只在DScanLine 变更时才下发此数据块
    double cwMidDepthMM = pDV(BFPNames::DopplerStartDepthMMStr) + pIV(BFPNames::CWMidDepthMMDeltaStr);

    int focusLevel =
        qBound(0, qRound((cwMidDepthMM - cWBlockDataDepthMM()) / cWBlockDataDepthStep()), cWFocusLevelCount() - 1);
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name, m_BlockDataParaNameConverter->groupParaName("CWFocus"),
                                              cwScanLine() * cWFocusLevelCount() + focusLevel);
}

void BeamFormerBase::sendCWDynamicRangeBlockDataGroup()
{
    m_BlockDataSender->sendParaBlockDataGroup(
        curProbe().Name, m_BlockDataParaNameConverter->groupParaName("CWDynamicRange"), pBV(BFPNames::CWEnStr) ? 1 : 0);
}

void BeamFormerBase::openTransferSignal()
{
    setPV(BFPNames::TransferSignalStr, true);
}

void BeamFormerBase::closeTransferSignal()
{
    return; // 2013-06-27 暂时关闭，防止不上传数据

    //停止上传，会导致usb接口read函数无法返回
    setPV(BFPNames::TransferSignalStr, false);
    //    Thread.Sleep(50);      //等待150ms
}

void BeamFormerBase::onBeginSetCurProbe(bool first)
{
    Q_UNUSED(first);
}

void BeamFormerBase::onSetCurProbe(bool first)
{
    //第一次设置探头，下发2次块数据，尝试解决线阵探头插在第一个位置时，
    //第一次开机有时没有图像的问题
    if (first)
    {
        //        sendDynamicdata();
    }
}

void BeamFormerBase::setPresetFinished()
{
    setPV(BFPNames::ConfigDoneStr, true);
}

void BeamFormerBase::setCurProbeFinishedOnSystemBooted()
{
}

void BeamFormerBase::sendCpdSteerCTValue(int value)
{
    ControlTableSender sender(m_ControlTable);
    double steerCos = 1 / qCos(Formula::degree2Rad(value));
    double steerTan = qTan(Formula::degree2Rad(value));
    setPV(BFPNames::BCosAngleStr, Util::floatToBinary(steerCos));
    setPV(BFPNames::BTgAngleStr, Util::floatToBinary(steerTan));
    setPV(BFPNames::BSteeringAngleCodingStr, qAbs(value));
}

void BeamFormerBase::sendFreqRelatedParas()
{
    FreqRelatedParasSender fpSender(m_RelatedParasController);
}

ImageModeType BeamFormerBase::imageMode() const
{
    return (ImageModeType)(pIV(BFPNames::ImageModeStr));
}

SyncModeType BeamFormerBase::syncMode() const
{
    return (SyncModeType)(pIV(BFPNames::SyncModeStr));
}

ColorImageModeType BeamFormerBase::colorImageMode() const
{
    return (ColorImageModeType)(pIV(BFPNames::ColorImageModeStr));
}

bool BeamFormerBase::zoomOn() const
{
    return pBV(BFPNames::ZoomOnStr);
}

int BeamFormerBase::zoomInTopOffset(double zoomInPercent, bool halfHeight)
{
    int coef = halfHeight ? 2 : 1;

    ImageModeType mode = imageMode();

    int rotation = pIV(BFPNames::RotationStr);
    bool isHorizontalRotate = (rotation == 90 || rotation == 270);

    // 90度/270度 的2B,左右BM模式中的B图像进行zoomcoef的缩放时，图像始波区顶部与图像region(0, 0, 320, 512)顶部
    // 的高度，要用 640/2 = 320 来进行缩放
    int height = (isHorizontalRotate && (mode == MODE_BM || mode == MODE_2B || pBV(BFPNames::BCImagesOnStr)))
                     ? (imageWidth() / 2)
                     : imageHeight();

    return qRound(height * (1.0F - zoomInPercent) / 2 / coef);
}

QString BeamFormerBase::rvFNoStr() const
{
    if ((!pBV(BFPNames::FreqSpectrumStr) && !pBV(BFPNames::TriplexModeStr)) ||
        pIV(BFPNames::SystemScanModeStr) == SystemScanModeAV)
    {
        return BFPNames::RvFNoStr;
    }
    else
    {
        return BFPNames::RvPWFNoStr;
    }
}

int BeamFormerBase::tgcDelta(int index, bool digital) const
{
#ifdef SYS_APPLE
    return 0;
#endif
    if (!((syncMode() & Sync_C) == Sync_C || pBV(BFPNames::ColorMStr) ||
          (pBV(BFPNames::TriplexModeStr) && systemScanMode() != SystemScanModeAV)))
    {
        //在C、CM、TM模式才使用tgcdelta, AV模式下需要保持与B一致，也不能使用tgcdelta
        return 0;
    }

    QString name;
    if (!pBV(BFPNames::TriplexModeStr))
    {
        if (!pBV(BFPNames::THIStr))
        {
            name = digital ? BFPNames::DTgcDelta0Str : BFPNames::TgcDelta0Str;
        }
        else
        {
            name = digital ? BFPNames::ThiDTgcDelta0Str : BFPNames::ThiTgcDelta0Str;
        }
    }
    else
    {
        if (!pBV(BFPNames::THIStr))
        {
            name = digital ? BFPNames::TriplexDTgcDelta0Str : BFPNames::TriplexTgcDelta0Str;
        }
        else
        {
            name = digital ? BFPNames::ThiTriplexDTgcDelta0Str : BFPNames::ThiTriplexTgcDelta0Str;
        }
    }

    if (!name.isEmpty())
    {
        name.remove(name.length() - 1, 1);
        return pIV(name + QString::number(index));
    }
    else
    {
        return 0;
    }
}

int BeamFormerBase::dTgcDelta(int index) const
{
    return tgcDelta(index, true);
}

BeamFormerBase::TGCDeltaType BeamFormerBase::tgcDeltaCondValue()
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        if (!pBV(BFPNames::THIStr))
        {
            return TGCDeltaNormal;
        }
        else
        {
            return TGCDeltaThi;
        }
    }
    else
    {
        if (!pBV(BFPNames::THIStr))
        {
            return TGCDeltaTriplex;
        }
        else
        {
            return TGCDeltaThiTriplex;
        }
    }

    return TGCDeltaInvalid;
}

void BeamFormerBase::sendTgcDelta(int index, BeamFormerBase::TGCDeltaType condValue)
{
    if (condValue == tgcDeltaCondValue())
    {
        QString name = BFPNames::TGC1Str;
        name.remove(name.length() - 1, 1);
        updateTGCControltable(name + QString::number(index + 1));
    }
}

void BeamFormerBase::sendDTgcDelta(int index, BeamFormerBase::TGCDeltaType condValue)
{
    if (condValue == tgcDeltaCondValue())
    {
        QString name = BFPNames::UniformityTgc00Str;
        name.remove(name.length() - 2, 2);
        updateTGCControltable(name + QString("%1").arg(index, 2, 10, QChar('0')));
    }
}

int BeamFormerBase::transmitDelta(int index, const QString& oneParaName) const
{
    if (!pBV(BFPNames::TriplexModeStr))
    {
        // TM模式才使用transmit Delta
        return 0;
    }

    QString name = oneParaName;

    if (!name.isEmpty())
    {
        name.remove(name.length() - 1, 1);
        return pIV(name + QString::number(index));
    }
    else
    {
        return 0;
    }
}

int BeamFormerBase::frequencyDelta(int index) const
{
    return transmitDelta(index, BFPNames::TriplexFrequencyDeltaTr1Str);
}

int BeamFormerBase::filterCoefDelta(int index) const
{
    return transmitDelta(index, BFPNames::TriplexFilterCoefDeltaTr1Str);
}

int BeamFormerBase::sendTransmitDelta(int index, const QString& oneParaName)
{
    QString name = oneParaName;
    name.remove(name.length() - 1, 1);
    sendParasControltable(name + QString::number(index));
    return -1;
}

int BeamFormerBase::sendFrequencyDelta(int index)
{
    sendTransmitDelta(index, BFPNames::FrequencyOfTransmit1Str);
    return -1;
}

int BeamFormerBase::sendFilterCoefDelta(int index)
{
    sendTransmitDelta(index, BFPNames::FilterCoefOfTransmit1Str);
    return -1;
}

void BeamFormerBase::controlTransmitCTValue()
{
    QStringList paras = QStringList() << BFPNames::FrequencyOfTransmit1Str << BFPNames::FilterCoefOfTransmit1Str
                                      << BFPNames::FrequencyOfTransmit2Str << BFPNames::FilterCoefOfTransmit2Str
                                      << BFPNames::FrequencyOfTransmit3Str << BFPNames::FilterCoefOfTransmit3Str
                                      << BFPNames::FrequencyOfTransmit4Str << BFPNames::FilterCoefOfTransmit4Str;

    foreach (const QString& name, paras)
    {
        sendParasControltable(name);
    }
}

void BeamFormerBase::controlSoundOpened(ISoundController::IntervalType type)
{
    if (m_SoundController == NULL)
    {
        return;
    }

    if (isFrozen())
    {
        m_SoundController->closeSpeaker();
        return;
    }

    bool flagD = ((syncMode() & Sync_D) == Sync_D) && (pBV(BFPNames::FreqSpectrumStr)) &&
                 (pIV(BFPNames::SystemScanModeStr) != SystemScanModeAV);
    // CWEnStr跟flagD条件一样
    if (pBV(BFPNames::TriplexModeStr) || flagD)
    {
        m_SoundController->delayOpenSpeaker(type);
    }
    else
    {
        m_SoundController->closeSpeaker();
    }
}

// add xule
IControlTable* BeamFormerBase::getControlTable() const
{
    return m_ControlTable;
}

int BeamFormerBase::defaultScpd() const
{
    // qbit default scpd is 2
    // i9 default scpd is 2 or 1
    return 2;
}

void BeamFormerBase::reSendModeFreqSetting()
{
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::Color);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::PD);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::TDI);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::Dop);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::TD);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::CWD);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::SonoNeedle);
    m_RelatedParasController->sendModeFreqSetting(FreqParasContainer::MVI);
    DynamicParasSender drSender(m_RelatedParasController);
    FreqRelatedParasSender fpSender(m_RelatedParasController);
}

void BeamFormerBase::calculateBlockDataAndSend()
{
}

qint32 BeamFormerBase::readRegister(quint32 addr, quint32* data, quint32 size)
{
    return m_BFIODevice->readRegister(addr, data, size);
}

qint32 BeamFormerBase::writeRegister(quint32 addr, quint32* data, quint32 size)
{
    return m_BFIODevice->writeRegister(addr, data, size);
}

void BeamFormerBase::onAfterWake(bool wake)
{
    emit afterWake(wake);
    if (wake && pBV(BFPNames::ElastoEnStr))
    {
        qDebug() << "start send ElastoEnStr data ## " << m_IsFPGAElastographyOn;
        m_IsFPGAElastographyOn = false;
        controlIsFPGAElastoGraphyOn();
        Util::usleep(50000);
        m_IsFPGAElastographyOn = true;
        controlIsFPGAElastoGraphyOn();
    }
}

void BeamFormerBase::onBufferSonoParametersChanged(SonoParameters* sp)
{
    //如果跟beamformer超声参数一致，则不需要重复连接信号
    m_PostProcessHandler->setIsAutoConnect(sp != m_SonoParameters);
    m_PostProcessHandler->setIsUpateParameters(sp != m_SonoParameters);
    m_PostProcessHandler->setSonoParameters(sp);
}

void BeamFormerBase::THIChangingHandle(const QVariant& value)
{
    controlSoundOpened(ISoundController::Thi);

    if (value.toBool())
    {
        parameter(BFPNames::GainThiStr)->update(true);
        if ((syncMode() & Sync_C) == Sync_C)
        {
            parameter(BFPNames::EdgeThiIncStr)->update();
        }
        else
        {
            parameter(BFPNames::EdgeThiStr)->update();
        }
        parameter(BFPNames::MGainThiStr)->update();
    }
    else
    {
        parameter(BFPNames::GainStr)->update(true);
        if ((syncMode() & Sync_C) == Sync_C)
        {
            parameter(BFPNames::EdgeIncStr)->update();
        }
        else
        {
            parameter(BFPNames::EdgeStr)->update();
        }
        parameter(BFPNames::MGainStr)->update();
    }
    parameter(BFPNames::AdapPostProcStr)->update();

    controlTGCCTValue();
    controlAcousticPower();

    BlockDataSendProcedure procedure(m_BlockDataSender, curProbe().Name,
                                     QStringList()
                                         << BFPNames::FreqIndexBStr << BFPNames::THIStr << BFPNames::FilterCpdStr);
    //有FreqIndexB统一控制频率相关参数的下发
    parameter(BFPNames::FreqIndexBStr)->update();
    if (pBV(BFPNames::ElastoEnStr))
    {
        parameter(BFPNames::FreqIndexElastoStr)->update();
    }
    m_BlockDataSender->sendParaBlockDataGroup(
        curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::THIStr), value.toBool() ? 1 : 0);
    // eco系列增加filtercpd参数,仅仅只需要控制data86的下发,不需要下发控制字,
    //所以使用虚函数分离eco系列和ebit系列
    controlFilterCpd(value.toBool());

    // ModeConfig中RealTHI是true的时候,thistatetool中的valuechanged是响应不到的,这里设置一下用来更新valuechanged
    if (ModelConfig::instance().value(ModelConfig::RealTHI, false).toBool())
    {
        setPV(BFPNames::THIStateStr, value);
    }
}

bool BeamFormerBase::isSupportAnyDensity()
{
    return ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity, false).toBool();
}

void BeamFormerBase::setOrderDensity(const QString& pname, int value)
{
    QList<int> densityList = {LowDensity, MediumDensity, HighDensity};
    int order = densityList.indexOf(value);
    setPV(pname, order);
}

/*================================================================================================================================*/
/**@brief BeamFormerBase::update
 * @param buf
 * @param len
 * @note  */
/*--------------------------------------------------------------------------------------------------------------------------------*/
void BeamFormerBase::update(unsigned char* buf, int len)
{
    if (m_BFIODevice->isOpen() /* && !isFrozen()*/)
    {
        //        qDebug() << "BeamFormerBase::update thread" << QThread::currentThreadId();

        emit updateImageData(ByteBuffer(buf, len));

        //        m_BFDataHandler->handleRawData(buf, len);

        emit dataUpdated();
    }
}

int BeamFormerBase::getIOReadSize() const
{
    int readSize = imageWidth() * imageHeight();
    int redundant = 0;
    if (!Setting::instance().defaults().isIODeviceVirtual())
    {
        redundant = pIV(BFPNames::RedundantPointNumPerLineStr);
    }
    readSize = pIV(BFPNames::LinePackageCountStr) *
               ((pIV(BFPNames::PointNumPerLineStr) - redundant) * LineData::LINE_UNIT_COUNT_PER_PACK +
                sizeof(LineDataHead) + m_RedundantPoints * 2);
    return readSize;
}

void BeamFormerBase::onDeviceStateChanged(bool isOpen)
{
    if (!m_SupportFreezeOutage)
    {
        m_StateManager->setIsDeviceOpen(isOpen);
    }
    else
    {
        if (!isOpen)
        {
            bool open = bfIODevice()->usbDevice() != nullptr ? true : false;
            m_StateManager->setIsDeviceOpen(open);
        }
    }
}

bool BeamFormerBase::isSonoNeedleOn() const
{
    return pBV(BFPNames::SonoNeedleStr);
}

void BeamFormerBase::prepareSonoNeedleParameter()
{
    if (m_StaticParameters != nullptr && m_SonoParameters != nullptr)
    {
        QList<int> settings = VariantUtil::variant2Ints(pV(BFPNames::ROISteerAnglesStr), VariantUtil::String);
        const QVector<float> angles = m_StaticParameters->steeringAngles();
        int steerAngleLevel = 0;
        if (settings.count() != angles.count() / 2)
        {
            steerAngleLevel = angles.count() / 2;
        }
        else
        {
            steerAngleLevel = CalculatorUtil::toMirrorList(settings).count() / 2;
        }

        setPDV(BFPNames::SteeringAngleSNStr, steerAngleLevel);
        controlSonoNeedleParas();
    }
}

void BeamFormerBase::updateScanWidthOfConvexExtend()
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    // Steer Angle
    double alpha = pIV(BFPNames::TrapezoidalCPDSteerStr) * (M_PI / 180.0f);
    int minCQYZ = probeDataInfo.MinCQYZ;
    BFDepthParameters bfDepthParameters = *m_DepthParameters;
    bfDepthParameters.setCQYZ(minCQYZ);
    // probe Min Depth
    double minProbeDepth = bfDepthParameters.depthMM();
    ProbeParameters probeParameters(probeDataInfo);
    int validLines = probeParameters.validLines();
    int lines = validLines;
    double ac = probeDataInfo.WaferRadius;
    double ac_pow2 = ac * ac;
    BFScanWidthParameter spMax(probeDataInfo, m_SonoParameters->pMax(BFPNames::ScanWidthStr), isSupportAnyDensity(),
                               m_SonoParameters->pIV(BFPNames::B_RX_LNUMStr));
    BFScanWidthParameter spMin(probeDataInfo, m_SonoParameters->pMin(BFPNames::ScanWidthStr), isSupportAnyDensity(),
                               m_SonoParameters->pIV(BFPNames::B_RX_LNUMStr));
    // assert(maxScanWith == Setting::instance().defaults().scanWidthLevel() - 1);
    int maxScanWith = spMax.scanWidth();
    // 极限线数逼近循环中的线数步长
    int linesStep = qRound((qreal)(qAbs(spMax.stopLine() - spMax.startLine()) + 1) / (qreal)(maxScanWith + 1));
    // get limit_min lines under minProbeDepth
    while (lines <= validLines)
    {
        double beta = lines * pDV(BFPNames::AngleSpacingRadStr) / 2.0f / (pBV(BFPNames::HighDensityStr) ? 2.0 : 1.0);
        double cos_beta = cos(beta);
        double ac_cos_beta = ac * cos_beta;
        double ac_cos_beta_2 = 2 * ac * cos_beta;
        double ab = ac_cos_beta + minProbeDepth;
        double ab_pow2 = ab * ab;
        double bc_pow2 = ac_pow2 + ab_pow2 - ab * ac_cos_beta_2;
        double bc = sqrt(bc_pow2);
        double gamma = acos((ab_pow2 + bc_pow2 - ac_pow2) / (2 * ab * bc));

        if (alpha >= beta + gamma)
        {
            break;
        }
        lines -= linesStep;
    }

    // limit_min lines in the range: [lines, lines + linesStep - 1]
    int linesLimit = lines + linesStep - 1;

    // 当算出来的安全扫描宽度小于等于探头最小扫描宽度时，意味着使用该探头总是安全的，不需要额外限制扫描宽度
    if (linesLimit <= qAbs(spMin.stopLine() - spMin.startLine()) + 1)
    {
        return;
    }

    // 当算出来的安全扫描宽度大于探头最小扫描宽度时，需要检查当前扫描宽度是否达到安全扫描宽度
    int curScanWidth = pIV(BFPNames::ScanWidthStr);
    int curLines = qAbs(pIV(BFPNames::StartLineStr) - pIV(BFPNames::StopLineStr)) + 1;
    int securityScanWidth = curScanWidth;
    while (securityScanWidth < maxScanWith)
    {
        if (curLines > linesLimit)
        {
            break;
        }
        securityScanWidth += 1;
        curLines += linesStep - 1;
    }
    // 未达到安全扫描宽度, 主动调整扫描宽度
    if (securityScanWidth > curScanWidth)
    {
        setPV(BFPNames::ScanWidthStr, securityScanWidth);
    }
}

void BeamFormerBase::controlSonoNeedleParas()
{
    int startLine = 0;
    int stopLine = 0;
    if (isSupportAnyDensity())
    {
        startLine = pIV(BFPNames::StartScanLineColorStr);
        stopLine = pIV(BFPNames::StopScanLineColorStr);
    }
    else
    {
        startLine = pIV(BFPNames::StartLineStr);
        stopLine = pIV(BFPNames::StopLineStr);
    }

    int midLine = (stopLine + startLine) / 2;
    int halfLine = (stopLine - startLine + 1) / 2;
    double halfDepthMM = pDV(BFPNames::DepthMMStr) / 2;
    setPDV(BFPNames::RoiMidLineSNStr, midLine);
    setPDV(BFPNames::RoiHalfLinesSNStr, halfLine);
    setPDV(BFPNames::RoiMidDepthMMSNStr, halfDepthMM);
    setPDV(BFPNames::RoiHalfDepthMMSNStr, halfDepthMM);
}

void BeamFormerBase::onDepthMMChanged(const QVariant& value)
{
    controlSonoNeedleParas();
}
void BeamFormerBase::onDeviceWaitTimeout()
{
    ControlTableSyncSender cs(m_ControlTable);
    qDebug() << PRETTY_FUNCTION << " ControlTableSyncSender ";
}

void BeamFormerBase::initMVIParams()
{
    //    setPV(BFPNames::MVILineDensityStr, false);
    //    setPV(BFPNames::BaseLineMVIStr, 3);
    //    setPV(BFPNames::MVIModeStr, 0);
    setPV(BFPNames::GainMVIStr, 125);
    setPV(BFPNames::MVICHETStr, 32);
    setPV(BFPNames::MVICTGCStr, 220);
    setPV(BFPNames::WallFilterMVIStr, 0);
    setPV(BFPNames::DummyEnSampleMVIStr, 1);
    setPV(BFPNames::PacketSizeMVIStr, 14);
    setPV(BFPNames::WFGainControlMVIStr, 9);
    setPV(BFPNames::MVIPostGainStr, 1);
    setPV(BFPNames::HDCPAWFGainCtrlStr, 8);
    setPV(BFPNames::LComeBackMVIStr, 0);
    setPV(BFPNames::AComeBackMVIStr, 0);
    setPV(BFPNames::MVIIIReStr, 1);
    setPV(BFPNames::MVICoefStr, 4);
    //    setPV(BFPNames::MVIDBIndexStr, 0);
    //    setPV(BFPNames::MVIPixelFilterStr, 0);
    setPV(BFPNames::MVIRegionThresholdStr, 1);
    setPV(BFPNames::BloodEffectionMVIStr, 0);
    setPV(BFPNames::AccCountMVIStr, 8);
    setPV(BFPNames::FrameAvgMVIStr, 6);
    setPV(BFPNames::WallThresholdMVIStr, 0);
    setPV(BFPNames::MVIVelocityThresholdStr, 3);
    setPV(BFPNames::MVICETStr, 12);
    setPV(BFPNames::HDCPACETStr, 10);
    setPV(BFPNames::MVICVRTStr, 15);
    setPV(BFPNames::MVICVLTStr, 10);
    setPV(BFPNames::PersistentDeltaMVIStr, -1);
    //    setPV(BFPNames::FreqIndexMVIStr, 112);
    setPV(BFPNames::MVIColorMapIndexStr, 24);
    setPV(BFPNames::MVIType1ColorMapIndexStr, 0);
    setPV(BFPNames::MVIType2ColorMapIndexStr, 1);
    setPV(BFPNames::MVIType3ColorMapIndexStr, 24);
    setPV(BFPNames::MVITransparencyStr, 95);
    setPV(BFPNames::SampleRateDopMVIStr, 10);
    //    setPV(BFPNames::PLFIR_Coef0Str, 251);
    //    setPV(BFPNames::PLFIR_Coef1Str, 137);
    //    setPV(BFPNames::PLFIR_Coef2Str, 11);
    //    setPV(BFPNames::PAFIR_Coef0Str, 235);
    //    setPV(BFPNames::PAFIR_Coef1Str, 172);
    //    setPV(BFPNames::PAFIR_Coef2Str, 68);
    //    setPV(BFPNames::PAFIR_Coef3Str, 14);
    //    setPV(BFPNames::PLFIR_ShiftStr, 13);
    //    setPV(BFPNames::PLFIR_GainStr, 15);
    //    setPV(BFPNames::PAFIR_ShiftStr, 13);
    //    setPV(BFPNames::PAFIR_GainStr, 11);
    //    setPV(BFPNames::PAComeBackStr, 0);
    //    setPV(BFPNames::PLComeBackStr, 0);
    setPV(BFPNames::MVIDynamicRangeStr, 9);
    //    setPV(BFPNames::MVIInvertStateStr, 0);
    setPV(BFPNames::MVITypeStr, 2);
    //    setPV(BFPNames::ShowBInROIStr, true);
    //    setPV(BFPNames::MBMVIStr, 1);
    //    setPV(BFPNames::MVIInvertStateStr, false);

    //    setPV(BFPNames::RoiMidLineMVIStr, pV(BFPNames::RoiMidLineStr));
    //    setPV(BFPNames::RoiHalfLinesMVIStr, pV(BFPNames::RoiHalfLinesStr));
    //    setPV(BFPNames::RoiMidDepthMMMVIStr, pV(BFPNames::RoiMidDepthMMStr));
    //    setPV(BFPNames::RoiHalfDepthMMMVIStr, pV(BFPNames::RoiHalfDepthMMStr));
}

bool BeamFormerBase::getDepthMMByCQYZLevel(const QString& probename, int level, double* depth)
{
    if (isFixedDepth())
    {
        QString depthCM = pV(BFPNames::DepthCMListStr).toString();
        QStringList depthCMList = depthCM.split(",");
        if (!depthCM.isEmpty() && level >= 0 && level < depthCMList.size())
        {
            *depth = (depthCMList[level].toDouble() * 10.0f);
            return true;
        }
    }
    return false;
}

bool BeamFormerBase::getDepthMMByCQYZ(const QString& probename, int level, double* depth)
{
    if (!isFixedDepth() && !curProbe().isNull())
    {
        return false;
    }
    return false;
}

bool BeamFormerBase::getFocusPosDepthMM(int focusNum, int focusPos, float* depth)
{
    if (curProbe().isNull())
    {
        return false;
    }

    if (focusNum < 0 || focusNum > BFFocusParameters().focusNumMax(curProbe()))
    {
        return false;
    }

    const QVector<int>& focuses = BFFocusParameters().focusPoses(curProbe(), focusNum);
    if (focuses.count() <= 0 || focusPos * (focusNum + 1) >= focuses.count())
    {
        return false;
    }

    QVector<float> ret = BFFocusParameters().getFocusDepthMMs(curProbe(), focusNum, focusPos);
    int size = sizeof(float) * (focusNum + 1);
    memset(depth, 0, size);
    memcpy(depth, ret.data(), size);

    return true;
}

APRangeSettings* BeamFormerBase::apRangeSettings()
{
    return m_APRangeSettings;
}
