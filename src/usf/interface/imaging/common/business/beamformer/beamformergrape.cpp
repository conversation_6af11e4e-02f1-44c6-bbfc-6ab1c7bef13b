#include "beamformergrape.h"
#include "bfpnames.h"
#include "sonoparameters.h"
#include "bfstaticparametersgrape.h"
#include "bfphoenixkitfactory.h"
#include "linedensityparametershandler.h"
#include "iblockdatasender.h"
#include "iblockdataparanameconverter.h"
#include "variantutil.h"
#include "probedataset.h"
#include "linedensityparametershandler.h"
#include "probeparameters.h"
#include "cwdprfcalculator.h"
#include "acousticpowerpresscalculator.h"
#include "bfdepthparameters.h"
#include "controltablesender.h"
#include "util.h"
#include "abstractbfdatahandler.h"
#include "icontroltable.h"
#include "controltableparameter.h"
#include "bffpsparameterphoenix.h"
#include "sonoparameters.h"
#include "isonoparameters.h"
#include "istatemanager.h"
#include "bfadfreqparameter.h"

BeamFormerGrape::BeamFormerGrape(QObject* parent)
    : BeamFormerApple(parent)
    , m_CurMaxCQYZ(64)
{
    m_LineDensityParametersHandler = new LineDensityParametersHandler();
    m_IsParametersResetting = true;
}

BeamFormerGrape::~BeamFormerGrape()
{
    if (m_LineDensityParametersHandler != nullptr)
    {
        delete m_LineDensityParametersHandler;
        m_LineDensityParametersHandler = nullptr;
    }
}

IBFKitFactory* BeamFormerGrape::createBFKitFactory()
{
    //探头解析的处理
    return new BFPhoenixKitFactory();
}

bool BeamFormerGrape::selectSocket(int socket, bool writeSocket)
{
    if (writeSocket)
    {
        setPV(BFPNames::ProbeCodeBurnSlotStr, socket);
        return true;
    }
    else
    {
        return BeamFormerBase::selectSocket(socket);
    }
}

void BeamFormerGrape::onSetSonoParameters()
{
    m_LineDensityParametersHandler->setSonoParameters(m_SonoParameters);
    m_LineDensityParametersHandler->setIsAutoConnect(false);
    m_LineDensityParametersHandler->setControlTable(m_ControlTable);

    BeamFormerApple::onSetSonoParameters();
}

bool BeamFormerGrape::standby()
{
    if (!isStandby())
    {
        bool value = m_SonoParameters->isRealTime();
        if (value)
        {
            m_SonoParameters->setIsRealTime(false);
        }
        //去掉解冻操作，解决待机唤醒后图像buffer清空的问题
        // freeze(false);

        m_BFIODevice->suspendRead();

        {
            ControlTableSyncSender cs(m_ControlTable);
            closeTransferSignal();
            setHighVoltage(false);
        }
        freeze(true);

        if (value)
        {
            m_SonoParameters->setIsRealTime(true);
        }

        UltrasoundDevice* device = dynamic_cast<UltrasoundDevice*>(m_BFIODevice);

        bool res = false;
        if (device != NULL)
        {
            res = device->standby();
            if (res)
            {
                qDebug() << "setUsbSignaltoLow";
                // GpioDevice::instance().setUsbSignalLow();
                // HardWareControlUtil::disableUSBReset();
            }
        }
        setIsStandby(res);
        return res;
    }
    return true;
}

bool BeamFormerGrape::wake()
{
    if (isStandby())
    {
        //等待FPGA准备好之后，执行唤醒操作
        Util::usleep(1000000);
        qDebug() << "BeamFormerBase::wake";
        //        GpioDevice::instance().setUsbSignalLow();//

        UltrasoundDevice* device = dynamic_cast<UltrasoundDevice*>(m_BFIODevice);
        bool wakeSuc = false;
        if (device != NULL)
        {
            wakeSuc = device->wake();
        }
        if (wakeSuc)
        {
            qDebug() << "usbdevice wake success";
            bool value = m_SonoParameters->isRealTime();
            if (value)
            {
                m_SonoParameters->setIsRealTime(false);
            }
            m_BFDataHandler->startScanProbe();
            // freeze(false);
            m_BFIODevice->resumeRead();
            {
                qDebug() << "start send probe data";
                ControlTableSyncSender cs(m_ControlTable);
                m_ControlTable->send();
                sendHardwareKey();
                setGrayMap(pV(BFPNames::GrayMapStr).toByteArray());

                setHighVoltage();

                resentCurProbeBlockData();

                if (pBV(BFPNames::FreqSpectrumStr))
                {
                    ControlTableParameter* freqSpectrumP =
                        qobject_cast<ControlTableParameter*>(parameter(BFPNames::FreqSpectrumStr));
                    if (freqSpectrumP != NULL)
                    {
                        m_ControlTable->send(freqSpectrumP, 1 - freqSpectrumP->trueValue(), true, false);
                    }
                }
                if (pBV(BFPNames::TriplexModeStr))
                {
                    parameter(BFPNames::TriplexModeStr)->update();
                }
                if (pBV(BFPNames::FreeMModeStr))
                {
                    parameter(BFPNames::FreeMBlockStr)->update();
                }
            }

            Util::processEvents(QEventLoop::ExcludeUserInputEvents);
            setIsStandby(false);
            // freeze(true);
            if (value)
            {
                m_SonoParameters->setIsRealTime(true);
            }
        }
        else
        {
            qDebug() << QString("%1 device wake failed").arg(PRETTY_FUNCTION);
            return false;
        }
    }
    return true;
}

void BeamFormerGrape::setPreset(const PresetParameters& presets)
{
    m_IsParametersResetting = true;
    //由于B_RX_LNUMStr受PhasedAngleStr影响，StartLineStr受B_RX_LNUMStr影响，但由ScanWidthStr触发计算，因此切换预设值时，参数赋值顺序为：
    // PhasedAngleStr -> B_RX_LNUMStr -> ScanWidthStr -> StartLineStr
    // PhasedAngleStr 原本在BeamFormerBase::setPreset 中自动赋值，由于顺序原因单独拧出来先赋值
    // ScanWidthStr 在BeamFormerBase::setPreset 中自动赋值，不变动
    const PresetParameters& defaultPreset = m_SonoParameters->defaultPreset();
    if (!presets.contains(BFPNames::PhasedAngleStr))
    {
        Parameter* p = parameter(BFPNames::PhasedAngleStr);

        int defV = p->min();
        if (defaultPreset.contains(BFPNames::PhasedAngleStr))
        {
            defV = defaultPreset.value(BFPNames::PhasedAngleStr).toInt();
        }

        if (!p->isDirectValue())
        {
            setPV(BFPNames::PhasedAngleStr, defV);
        }
        else
        {
            setPDV(BFPNames::PhasedAngleStr, defV);
        }
    }
    else
    {
        setOnePreset(BFPNames::PhasedAngleStr, presets, parameter(BFPNames::PhasedAngleStr)->isDirectValue());
    }

    //发射线数的计算需要多波束参数，因此提前赋值
    setOnePreset(BFPNames::MBStr, presets, parameter(BFPNames::MBStr)->isDirectValue());
    setOnePreset(BFPNames::MBColorStr, presets, parameter(BFPNames::MBColorStr)->isDirectValue());

    //该参数再选预设的时候，如果值有变化，会修改AcousticPowerB的值
    setOnePreset(BFPNames::CWTransmitPowerPositiveVolStr, presets,
                 parameter(BFPNames::CWTransmitPowerPositiveVolStr)->isDirectValue());

    //选预设的时候，保证RevLNumB、RevLNumC选择的是正确档位的值
    setOnePreset(BFPNames::HighDensityStr, presets, parameter(BFPNames::HighDensityStr)->isDirectValue());

    //    setOnePreset(BFPNames::ColorLineDensityStr, presets,
    //    parameter(BFPNames::ColorLineDensityStr)->isDirectValue()); setPV(BFPNames::LineDensityCStr,
    //    pIV(BFPNames::ColorLineDensityStr));
    setOnePreset(BFPNames::LineDensityCStr, presets, true);

    int densityNumberB = pIV(BFPNames::MBStr) == 2 ? curProbe().RevLNum4Beams[Density(pIV(BFPNames::HighDensityStr))]
                                                   : curProbe().RevLNumB[Density(pIV(BFPNames::HighDensityStr))];
    int densityNumberC = curProbe().RevLNumC[Density(pIV(BFPNames::LineDensityCStr))];
    m_LineDensityParametersHandler->doScaleOnRX_LNUM(densityNumberB);
    m_LineDensityParametersHandler->doScaleOnRX_LNUM(densityNumberC);
    setPV(BFPNames::B_RX_LNUMStr, densityNumberB);
    setPV(BFPNames::C_RX_LNUMStr, densityNumberC);
    parameter(BFPNames::HighDensityStr)->update();

    setOrderDensity(BFPNames::OrderHighDensityStr, pIV(BFPNames::HighDensityStr));
    setOrderDensity(BFPNames::OrderColorLineDensityStr, pIV(BFPNames::ColorLineDensityStr));
    //从预设更新cqyz时，需要先恢复最大的cqyz值，否则最大值会被限制
    m_CurMaxCQYZ = curProbe().MaxCQYZ;
    //    setParameterValue(USPNames::Contrast, m_SonoParameters->pIV(BFPNames::ContrastSWStr));
    //    setParameterValue(USPNames::Needle, m_SonoParameters->pIV(BFPNames::NeedleModeStr));
    //    setParameterValue(USPNames::NeedleCoef, m_SonoParameters->pIV(BFPNames::FilterCoefOfTransmit5Str));
    //    setParameterValue(USPNames::THI, m_SonoParameters->pIV(BFPNames::THIStr));
    //    setParameterValue(USPNames::FilterCPD, m_SonoParameters->pIV(BFPNames::FilterCpdStr));
    //    setParameterValue(USPNames::FG0, m_SonoParameters->pIV(BFPNames::MF_Coef_Fundamental_Steer0Str));
    //    setParameterValue(USPNames::FG1, m_SonoParameters->pIV(BFPNames::MF_Coef_Fundamental_SteerXStr));
    //    setParameterValue(USPNames::HG0, m_SonoParameters->pIV(BFPNames::MF_Coef_Harmonic_Steer0Str));
    //    setParameterValue(USPNames::HG1, m_SonoParameters->pIV(BFPNames::MF_Coef_Harmonic_SteerXStr));
    //    setParameterValue(USPNames::MFC, m_SonoParameters->pIV(BFPNames::MFCStr));
    //    setParameterValue(USPNames::FreqIndex, m_SonoParameters->pIV(BFPNames::FreqIndexBStr));

    BeamFormerApple::setPreset(presets);
    //由于匹配滤波相关的数据块需要知道当前预设值的THI状态，因此需要预设值设置完成后再触发信号
    parameter(BFPNames::MF_Coef_Fundamental_Steer0Str)->update();
    parameter(BFPNames::MF_Coef_Fundamental_SteerXStr)->update();
    parameter(BFPNames::MF_Coef_Harmonic_Steer0Str)->update();
    parameter(BFPNames::MF_Coef_Harmonic_SteerXStr)->update();

    m_LineDensityParametersHandler->calculateBlockDataAndSend();
    m_IsParametersResetting = false;

    //临时方案，按照设计不需要再次setOnePreset，但实际运行，pIV获取的值不正确
    setOnePreset(BFPNames::RoiMidLineStr, presets);
    setOnePreset(BFPNames::RoiHalfLinesStr, presets);
    setOnePreset(BFPNames::DScanLineStr, presets);

    int midLine = pIV(BFPNames::RoiMidLineStr);
    int halfLine = pIV(BFPNames::RoiHalfLinesStr);
    int DScanLine = pIV(BFPNames::DScanLineStr);
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    double linespacing = probeDataInfo.probeWide() / densityNumberC;
    setPV(BFPNames::LineSpacingColorStr, linespacing);
    setPV(BFPNames::StartScanDisColorStr, linespacing * (midLine - halfLine));
    setPV(BFPNames::StopScanDisColorStr, linespacing * (midLine + halfLine));
    setPV(BFPNames::ScanDisDopplerStr, linespacing * DScanLine);

    linespacing = probeDataInfo.probeWide() / densityNumberB;
    setPV(BFPNames::LineSpacingDensityBStr, linespacing);
    setPV(BFPNames::ScanDisMLineStr, linespacing * pIV(BFPNames::MScanLineStr));
    //由于DopOutGain在配置文件中为控制表参数，受范围限制，必须执行update更新value值不采用controltablevalue
    parameter(BFPNames::DopOutGainStr)->update();
    parameter(BFPNames::LineDensityCStr)->update();
}

void BeamFormerGrape::createStaticParameters()
{
    m_StaticParameters = new BFStaticParametersGrape();
}

void BeamFormerGrape::changeProbePara()
{
    BeamFormerBase::changeProbePara();
    //参照lotus，同步PB_Pitch参数的计算
    setPV(BFPNames::PB_PitchStr, (int)(curProbe().Pitch * 1048576));

    //凸阵的时候设置为1
    if (!curProbe().IsLinear && !curProbe().IsPhasedArray)
    {
        setPV(BFPNames::CurvedExpandingCodeStr, 1);
    }
    else
    {
        setPV(BFPNames::CurvedExpandingCodeStr, 0);
    }
}

void BeamFormerGrape::changeProbeInterval()
{
    m_LineDensityParametersHandler->changeProbeInterval();
}

int BeamFormerGrape::getIOReadSize() const
{
    // m_RedundantPoints和PCIE机型相关，由FPGA决定，为了方便FPGA内部DMA芯片存储。目前XBit和Phoenix一样，需要设置为8,
    // Sonobook9需要设置为0。
    int readSize = (pIV(BFPNames::LinePackageCountStr) + 1) *
                   ((pIV(BFPNames::PointNumPerLineStr) + m_RedundantPoints) * LineData::LINE_UNIT_COUNT_PER_PACK +
                    sizeof(LineDataHead));
    return readSize;
}

void BeamFormerGrape::controlIsFPGAElastoGraphyOn()
{
}

QVector<ChipFactory::ChipNameEnum> BeamFormerGrape::supportChipList() const
{
    return QVector<ChipFactory::ChipNameEnum>() << ChipFactory::ChipNameEnum::AFE5828;
}

int BeamFormerGrape::getCWDLines(int speed, int sampleRate) const
{
    if (sampleRate < m_StaticParameters->CWDSampleRateValues().count() &&
        speed < m_StaticParameters->CWDSpeedRates().count())
    {
        return m_StaticParameters->CWDSampleRateValues()[sampleRate] / m_StaticParameters->CWDSpeedRates()[speed];
    }
    else
    {
        // log()->error("getCWDLines error, speed:%1, sampleRate: %2",speed, sampleRate);
        return 0;
    }
}

void BeamFormerGrape::onB_RX_LNUMChanged(const QVariant& value)
{
    Q_UNUSED(value);
    if (!m_IsParametersResetting)
    {
        m_LineDensityParametersHandler->calculateBlockDataAndSend();
    }
}

void BeamFormerGrape::onC_RX_LNUMChanged(const QVariant& value)
{
    Q_UNUSED(value);
    if (!m_IsParametersResetting)
    {
        m_LineDensityParametersHandler->calculateBlockDataAndSend();
    }
}

void BeamFormerGrape::onMBChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (!m_IsParametersResetting)
    {
        parameter(BFPNames::HighDensityStr)->update();
    }
}

void BeamFormerGrape::onCQYZChanging(const QVariant& value)
{
    BeamFormerApple::onCQYZChanging(value);
    //参照lotus，同步SAMP_DIST_RECIP参数的计算
    setPV(BFPNames::SAMP_DIST_RECIPStr, (int)(8192 / pDV(BFPNames::PixelSizeMMStr)));

    m_LineDensityParametersHandler->updateFocDepth();
}

void BeamFormerGrape::onGettingCQYZMax(int& value)
{
    value = m_CurMaxCQYZ;
}

void BeamFormerGrape::onHighDensityChanging(const QVariant& value)
{
    m_LineDensityParametersHandler->handleHighDensityChanging(value);
}

void BeamFormerGrape::onStripRemoveEnableChanged(const QVariant& value)
{
    Q_UNUSED(value);
    if (!m_IsParametersResetting)
    {
        m_LineDensityParametersHandler->calculateBlockDataAndSend();
    }
}

void BeamFormerGrape::onOverlappingMBChanged(const QVariant& value)
{
    Q_UNUSED(value);
    if (!m_IsParametersResetting)
    {
        m_LineDensityParametersHandler->calculateBlockDataAndSend();
    }
}

void BeamFormerGrape::onSyncModeChanging(const QVariant& value)
{
    BeamFormerApple::onSyncModeChanging(value);
    if (Sync_C == (SyncModeType)value.toInt())
    {
        parameter(BFPNames::CfmRxFnumStr)->update();
    }
    if (!m_IsParametersResetting)
    {
        m_LineDensityParametersHandler->calculateBlockDataAndSend();
    }
}

void BeamFormerGrape::onSystemScanModeChanging(const QVariant& value)
{
    BeamFormerBase::onSystemScanModeChanging(value);

    // TODO: 此处需优化，去掉LineDensity* 参数，与UI对应的预设值 采用 OrderColorLineDensity*，控制字参数
    // 采用ColorLineDensity
    switch ((SystemScanMode)value.toInt())
    {
    case SystemScanModeColorDoppler:
        setPV(BFPNames::ColorLineDensityStr, pIV(BFPNames::LineDensityCStr));
        setOrderDensity(BFPNames::OrderColorLineDensityStr, pIV(BFPNames::LineDensityCStr));
        break;
    case SystemScanModePowerDoppler:
        setPV(BFPNames::ColorLineDensityStr, pIV(BFPNames::LineDensityPDStr));
        setOrderDensity(BFPNames::OrderLineDensityPDStr, pIV(BFPNames::LineDensityPDStr));
        break;
    case SystemScanModeDPowerDoppler:
        setPV(BFPNames::ColorLineDensityStr, pIV(BFPNames::LineDensityDPDStr));
        setOrderDensity(BFPNames::OrderLineDensityDPDStr, pIV(BFPNames::LineDensityDPDStr));
        break;
    case SystemScanModeTissueDoppler:
        setPV(BFPNames::ColorLineDensityStr, pIV(BFPNames::LineDensityTDIStr));
        setOrderDensity(BFPNames::OrderLineDensityTDIStr, pIV(BFPNames::LineDensityTDIStr));
        break;
    default:
        break;
    }
}

void BeamFormerGrape::onCfmRxFnumChanging(const QVariant& value)
{
    BeamFormerBase::onCfmRxFnumChanging(value);
    m_BlockDataSender->sendParaBlockDataGroup(
        curProbe().Name, m_BlockDataParaNameConverter->groupParaName(BFPNames::CfmRxFnumStr), value.toInt());
}

void BeamFormerGrape::onDScanLineChanging(const QVariant& value)
{
    BeamFormerBase::onDScanLineChanging(value);

    if (pBV(BFPNames::CWEnStr))
    {
        // CW的 都按照C的计算，不管C有没有开
        if (value.toInt() < pIV(BFPNames::C_RX_LNUMStr) / 2)
        {
            setPV(BFPNames::CW_TX_RX_SWITCHStr, 0);
        }
        else if (value.toInt() >= pIV(BFPNames::C_RX_LNUMStr) / 2)
        {
            setPV(BFPNames::CW_TX_RX_SWITCHStr, 1);
        }
    }
}

void BeamFormerGrape::onMF_Coef_Fundamental_Steer0Changing(const QVariant& value)
{
    // MF_Coef_Fundamental_Steer0和MF_Coef_Fundamental_SteerX分别抽取一档组合成16k下发
    QList<int> indexs = QList<int>() << value.toInt() << pIV(BFPNames::MF_Coef_Fundamental_SteerXStr);
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_LineDensityParametersHandler->getMF_Coef_BlockDataName(true), indexs);
}

void BeamFormerGrape::onMF_Coef_Fundamental_SteerXChanging(const QVariant& value)
{
    // MF_Coef_Fundamental_Steer0和MF_Coef_Fundamental_SteerX分别抽取一档组合成16k下发
    QList<int> indexs = QList<int>() << pIV(BFPNames::MF_Coef_Fundamental_Steer0Str) << value.toInt();
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_LineDensityParametersHandler->getMF_Coef_BlockDataName(true), indexs);
}

void BeamFormerGrape::onMF_Coef_Harmonic_Steer0Changing(const QVariant& value)
{
    // MF_Coef_Harmonic_Steer0和MF_Coef_Harmonic_SteerX分别抽取一档组合成16k下发
    QList<int> indexs = QList<int>() << value.toInt() << pIV(BFPNames::MF_Coef_Harmonic_SteerXStr);
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_LineDensityParametersHandler->getMF_Coef_BlockDataName(false), indexs);
}

void BeamFormerGrape::onMF_Coef_Harmonic_SteerXChanging(const QVariant& value)
{
    // MF_Coef_Harmonic_Steer0和MF_Coef_Harmonic_SteerX分别抽取一档组合成16k下发
    QList<int> indexs = QList<int>() << pIV(BFPNames::MF_Coef_Harmonic_Steer0Str) << value.toInt();
    m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name,
                                              m_LineDensityParametersHandler->getMF_Coef_BlockDataName(false), indexs);
}

void BeamFormerGrape::onFilterCoefOfTransmit5Changing(const QVariant& value)
{
    if (pBV(BFPNames::NeedleModeStr))
    {
        m_BlockDataSender->sendParaBlockDataGroup(curProbe().Name, "MfcFilterNeedle", value.toInt());
    }
}

void BeamFormerGrape::onTXFNo_Steer1Changing(const QVariant& value)
{
    //来源:应曹三要求，新增TXFNo_Steer1参数，地址同BTXFnumCoef，计算方式同BTXFnumCoef，不涉及到数据块的下发
    setPV(BFPNames::BTXFnumCoefStr, txApertureCoef(value.toInt()));
}

void BeamFormerGrape::onTxIndex5Changing(const QVariant& value)
{
    setPV(BFPNames::TXFNUM_FIVESTEERStr, txApertureCoef(value.toInt()));
}

void BeamFormerGrape::onTxIndex7Changing(const QVariant& value)
{
    setPV(BFPNames::TXFNUM_SEVENSTEERStr, txApertureCoef(value.toInt()));
}

void BeamFormerGrape::onGettingAFE_LPF_FCutOffControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (value.toInt() < m_StaticParameters->AFE_LPF_FCutOffControlTable().size())
    {
        controlTableValue = m_StaticParameters->AFE_LPF_FCutOffControlTable()[value.toInt()];
    }
}

void BeamFormerGrape::onGettingAFE_HPF_FCutOffControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (value.toInt() < m_StaticParameters->AFE_HPF_FCutOffControlTable().size())
    {
        controlTableValue = m_StaticParameters->AFE_HPF_FCutOffControlTable()[value.toInt()];
    }
}

void BeamFormerGrape::onGettingADC_HPF_CORNER_FREQControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (value.toInt() < m_StaticParameters->ADC_HPF_CORNER_FREQControlTable().size())
    {
        controlTableValue = m_StaticParameters->ADC_HPF_CORNER_FREQControlTable()[value.toInt()];
    }
}

void BeamFormerGrape::onGettingAFE_PGA_CLAMP_LVLControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (value.toInt() < m_StaticParameters->AFE_PGA_CLAMP_LVLControlTable().size())
    {
        controlTableValue = m_StaticParameters->AFE_PGA_CLAMP_LVLControlTable()[value.toInt()];
    }
}

void BeamFormerGrape::onGettingAFE_PGA_CLAMP_LVLMax(int& value)
{
    value = m_StaticParameters->AFE_PGA_CLAMP_LVL().size() - 1;
}

void BeamFormerGrape::onGettingAFE_LPF_FCutOffText(QString& value)
{
    int index = pIV(BFPNames::AFE_LPF_FCutOffStr);
    if (index < m_StaticParameters->AFE_LPF_FCutOff().size())
    {
        value = QString("%1 Mhz").arg(m_StaticParameters->AFE_LPF_FCutOff()[index]);
    }
}

void BeamFormerGrape::onGettingAFE_HPF_FCutOffText(QString& value)
{
    int index = pIV(BFPNames::AFE_HPF_FCutOffStr);
    if (index < m_StaticParameters->AFE_HPF_FCutOff().size())
    {
        value = QString("%1 khz").arg(m_StaticParameters->AFE_HPF_FCutOff()[index]);
    }
}

void BeamFormerGrape::onGettingADC_HPF_CORNER_FREQText(QString& value)
{
    int index = pIV(BFPNames::ADC_HPF_CORNER_FREQStr);
    if (index < m_StaticParameters->ADC_HPF_CORNER_FREQ().size())
    {
        value = QString("%1 MHz").arg(m_StaticParameters->ADC_HPF_CORNER_FREQ()[index]);
    }
}

void BeamFormerGrape::onGettingAFE_PGA_CLAMP_LVLText(QString& value)
{
    int index = pIV(BFPNames::AFE_PGA_CLAMP_LVLStr);
    if (index < m_StaticParameters->AFE_PGA_CLAMP_LVL().size())
    {
        value = QString("%1 Vpp").arg(m_StaticParameters->AFE_PGA_CLAMP_LVL()[index]);
    }
}

void BeamFormerGrape::onGettingAFE_PGA_HI_FREQText(QString& value)
{
    int index = pIV(BFPNames::AFE_PGA_HI_FREQStr);
    if (index < m_StaticParameters->AFE_PGA_HI_FREQ().size())
    {
        value = QString("%1").arg(m_StaticParameters->AFE_PGA_HI_FREQ()[index]);
    }
}

void BeamFormerGrape::onSteeringAngleChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
}

void BeamFormerGrape::onSteeringAngleChanging(const QVariant& value)
{ // PW的SteerAngle档位控制先保持与C一致，但是控制表的下发参数独立(与FPGA版本一致)
    BeamFormerApple::onSteeringAngleChanging(value);
    if (curProbe().IsLinear)
    {
        QVector<float> dopAngles = m_StaticParameters->steeringAngles();
        int index = value.toInt();
        if (dopAngles.length() >= (index + 1))
        {
            //实际打图发现，相控阵的DopSteerAngle设置值的话，pw图像效果不好，目前只有线阵的时候再设置值
            setPV(BFPNames::DopSteeringDirectionStr, (dopAngles.at(index) >= 0 ? 0 : 1));
            setPV(BFPNames::DopSteeringAngleStr, value);
        }
    }
    else
    {
        setPV(BFPNames::DopSteeringDirectionStr, 0);
        //由于相控阵PW的实际steerangle为0，但控制还是采用与C一样的档位值，因此需要设置为3
        setPV(BFPNames::DopSteeringAngleStr, 3);
    }
}

void BeamFormerGrape::onBeforeDopSteeringAngleChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
}

void BeamFormerGrape::onDopSteeringAngleChanged(const QVariant& value, bool changed)
{
    if (curProbe().IsLinear)
    {
        QVector<float> dopAngles = m_StaticParameters->steeringAngles();
        int index = value.toInt();
        if (dopAngles.length() >= (index + 1))
        {
            //实际打图发现，相控阵的DopSteerAngle设置值的话，pw图像效果不好，目前只有线阵的时候再设置值
            setPV(BFPNames::DopSteeringDirectionStr, (dopAngles.at(index) >= 0 ? 0 : 1));
        }
    }
    else
    {
        setPV(BFPNames::DopSteeringDirectionStr, 0);
    }
    BeamFormerApple::onDopSteeringAngleChanged(value, changed);
    //    QVector<float> dopAngles = m_StaticParameters->steeringAngles();
    //    float dopAngle = pIV(BFPNames::DopSteeringDirectionStr) == 0 ? value.toFloat() : -value.toFloat();
    //    int index = dopAngles.indexOf(dopAngle);
    //    if (!m_SonoParameters->pBV(BFPNames::FreqSpectrumStr) && changed)
    //    {
    //        setPV(BFPNames::SteeringAngleStr, index, true);
    //    }
}

void BeamFormerGrape::onGettingDopSteeringAngleMin(int& value)
{
    Q_UNUSED(value)
}

void BeamFormerGrape::onGettingDopSteeringAngleMax(int& value)
{
    Q_UNUSED(value)
}

void BeamFormerGrape::onGettingDopSteeringAngleControlTableValue(const QVariant& value, int& controlTableValue)
{
    if (curProbe().IsLinear)
    {
        QVector<float> dopAngles = m_StaticParameters->steeringAngles();
        controlTableValue = qAbs(dopAngles.value(value.toInt()));
    }
    else
    {
        controlTableValue = 0;
    }
}

void BeamFormerGrape::onGettingPrtOfBControlTableValue(const QVariant& value, int& controlTableValue)
{
    //温升控制，所以要加Debug * 512, getPrtDelta是增加fps控制后添加
    controlTableValue = value.toInt() + (pIV(BFPNames::DebugPara0Str) + m_APPressCalculator->prtDelta()) * 512;

    if (pBV(BFPNames::TriplexModeStr)) // 3同步
    {
        controlTableValue += pIV(BFPNames::BTriplexPrt_DeltaStr);
    }

    static int PRTOFB_MAX = 65535;
    if (controlTableValue < 0)
    {
        controlTableValue = 0;
    }
    else if (controlTableValue > PRTOFB_MAX)
    {
        controlTableValue = PRTOFB_MAX;
    }
}

void BeamFormerGrape::onGettingCQYZControlTableValue(const QVariant& value, int& controlTableValue)
{
    //此时sonoparameters中的cqyz还没变，不能使用 m_DepthParameters，只能使用临时变量
    //    BFDepthParameters depthPara(value.toInt(), pBV(BFPNames::HalfHeightStr),
    //                                zoomOn(),
    //                                BFDepthParameters::zoomMulti(
    //                                pIV(BFPNames::ZoomMultiIndexStr),
    //                                parameter(BFPNames::ZoomMultiIndexStr)->max()));

    BFDepthParameters depthPara(pIV(BFPNames::ADFreqMHzStr), imageHeight(), value.toInt(), pBV(BFPNames::HalfHeightStr),
                                zoomOn(), pIV(BFPNames::ZoomedCQYZStr));
    depthPara.setPixelLen(BFADFreqParameter::pixelLenMM(pIV(BFPNames::ADFreqMHzStr)) /
                          pFV(BFPNames::FixedSWImageZoomCofStr));

    if (imageMode() == MODE_4B)
    {
        controlTableValue = depthPara.getRealCQYZ(false);
    }
    else
    {
        controlTableValue = depthPara.getRealCQYZ();
    }

    if (ModelConfig::instance().value(ModelConfig::PixelLenStep, 1).toInt() == 4)
    {
        if (pBV(BFPNames::CWEnStr) && pBV(BFPNames::FreqSpectrumStr))
        {
            controlTableValue = 320;
        }
    }
}

void BeamFormerGrape::onCWDSampleRateChanging(const QVariant& value)
{
    if (pBV(BFPNames::ChangedByAdjustmentStr))
    {
        setPV(BFPNames::CWDSampleRateBakStr, value);
    }
}

void BeamFormerGrape::onGettingCWDSampleRateText(QString& value)
{
    if (pBV(BFPNames::CWEnStr))
    {
        double prf = m_CWDPRFCalculator->calculate().toDouble();
        value = Formula::formatFrequency(prf);
    }
}

void BeamFormerGrape::onGettingCWDSampleRateMax(int& value)
{
    // 正确调节频率最大值 Seven
    value = m_StaticParameters->CWDSampleRateValues().count() - 1;
}

void BeamFormerGrape::initCWDPRFParameterCalculator()
{
    if (m_CWDPRFCalculator == nullptr && m_IfConnectSignals)
    {
        m_CWDPRFCalculator = new CWDPRFCalculator(m_SonoParameters, m_StaticParameters);
        foreach (const QString& p, m_CWDPRFCalculator->relatedParameters())
        {
            connect(parameter(p), SIGNAL(valueChanging(QVariant)), this, SLOT(updatePRFCWD()));
        }
    }
}

void BeamFormerGrape::onBaseLineCWDChanging(const QVariant& value)
{
    //
    int intervalCount = pMax(BFPNames::BaseLineCWDStr) - pMin(BFPNames::BaseLineCWDStr) + 2;
    setPV(BFPNames::DBaseLineYPosStr, (intervalCount - 1 - value.toInt()) * (dImageSize().height() / intervalCount));
}

void BeamFormerGrape::onMVelocityChanging(const QVariant& value)
{
    setPV(BFPNames::MSampleRateStr, m_StaticParameters->mSampleRates()[value.toInt()]);
}

void BeamFormerGrape::onScanWidthChanged(const QVariant& value)
{
    BeamFormerSonoeye::onScanWidthChanged(value);
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (probeDataInfo.IsLinear && pBV(BFPNames::TrapezoidalModeStr))
    {
        QVariant v = pV(BFPNames::TrapezoidalModeStr);
        onBeforeTrapezoidalModeChanged(QVariant(), v);
    }
    else if (!probeDataInfo.IsLinear && !probeDataInfo.IsPhasedArray && pBV(BFPNames::CurvedExapandingStr))
    {
        QVariant v = pV(BFPNames::CurvedExapandingStr);
        onBeforeCurvedExapandingChanged(QVariant(), v);
    }
    else if (pIV(BFPNames::ScpdStr) != 0)
    {
        QVariant v = pV(BFPNames::ScpdStr);
        onBeforeScpdChanged(QVariant(), v);
    }
}

void BeamFormerGrape::onBeforeScpdChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    updateMaxCQYZ(pBV(BFPNames::TrapezoidalModeStr), pBV(BFPNames::ScpdOnStr), newValue.toInt(),
                  pBV(BFPNames::CurvedExapandingStr));
}

void BeamFormerGrape::updateMaxCQYZ(bool trapezodialOn, bool scpdOn, int scpdValue, bool curvedExapanding)
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (probeDataInfo.IsLinear)
    {
        int angle = -1;
        if (trapezodialOn)
        {
            angle = pIV(BFPNames::TrapezoidalCPDSteerStr);
        }
        else if (scpdValue > 0 || scpdOn)
        {
            angle = pIV(BFPNames::CPDSteerStr);
        }
        if (angle != -1)
        {
            updateLinearCQYZ(angle);
            return;
        }
    }
    else if (!probeDataInfo.IsPhasedArray && curvedExapanding)
    {
        updateConvexCQYZ(pIV(BFPNames::TrapezoidalCPDSteerStr));
        return;
    }
    if (m_CurMaxCQYZ != probeDataInfo.MaxCQYZ)
    {
        m_CurMaxCQYZ = probeDataInfo.MaxCQYZ;
        updateCQYZ();
    }
}

void BeamFormerGrape::updateLinearCQYZ(const int angle)
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    int lines =
        ProbeParameters::lines(pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr), pIV(BFPNames::HighDensityStr));
    double halfMaxWidth = lines * pDV(BFPNames::LineSpacingMMStr) / 2.0f;
    double radians = angle * (M_PI / 180.0f);
    double maxDepth = halfMaxWidth / qTan(radians);
    BFDepthParameters bfDepthParameters = *m_DepthParameters;
    int maxCQYZ = probeDataInfo.MaxCQYZ;
    int cqyz = probeDataInfo.MinCQYZ;
    qDebug() << PRETTY_FUNCTION << "lines:" << lines << "halfMaxWidth:" << halfMaxWidth << "angle:" << angle
             << "radians:" << radians << "bfDepthParameters.depthMM():" << bfDepthParameters.depthMM()
             << "bfDepthParameters.getRealCQYZ:" << bfDepthParameters.getRealCQYZ() << "maxDepth:" << maxDepth;

    bfDepthParameters.setCQYZ(cqyz);
    int step = 0;
    while ((bfDepthParameters.depthMM() < maxDepth) && (cqyz <= maxCQYZ))
    {
        step = m_CQYZStepConfig->getStep(probeDataInfo.Name, cqyz, true);
        cqyz += step;
        bfDepthParameters.setCQYZ(cqyz);
        qDebug() << PRETTY_FUNCTION << "maxCQYZ:" << maxCQYZ << "cqyz:" << cqyz << "step:" << step
                 << "maxDepth:" << maxDepth << "depthMM:" << bfDepthParameters.depthMM();
    }
    m_CurMaxCQYZ = cqyz - step;
    if (m_CurMaxCQYZ < pIV(BFPNames::CQYZStr))
    {
        updateCQYZ(m_CurMaxCQYZ);
    }
}

void BeamFormerGrape::updateConvexCQYZ(const int angle)
{
    double alpha = angle * (M_PI / 180.0f);
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    int lines =
        ProbeParameters::lines(pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr), pIV(BFPNames::HighDensityStr));
    double beta = lines * pDV(BFPNames::AngleSpacingRadStr) / 2.0f;
    double cos_beta = cos(beta);
    double ac = probeDataInfo.WaferRadius;
    double ac_pow2 = ac * ac;
    double ac_cos_beta = ac * cos_beta;
    double ac_cos_beta_2 = 2 * ac * cos_beta;

    BFDepthParameters bfDepthParameters = *m_DepthParameters;
    int maxCQYZ = probeDataInfo.MaxCQYZ;
    int cqyz = probeDataInfo.MinCQYZ;
    int step = 0;
    while (cqyz <= maxCQYZ)
    {
        bfDepthParameters.setCQYZ(cqyz);
        double ab = ac_cos_beta + bfDepthParameters.depthMM();
        double ab_pow2 = ab * ab;
        double bc_pow2 = ac_pow2 + ab_pow2 - ab * ac_cos_beta_2;
        double bc = sqrt(bc_pow2);
        double gamma = acos((ab_pow2 + bc_pow2 - ac_pow2) / (2 * ab * bc));
        qDebug() << PRETTY_FUNCTION << "alpha:" << alpha << "beta:" << beta << "gamma:" << gamma;
        if (alpha > beta + gamma)
        {
            break;
        }
        step = m_CQYZStepConfig->getStep(probeDataInfo.Name, cqyz, true);
        cqyz += step;
    }
    m_CurMaxCQYZ = cqyz - step;
    if (m_CurMaxCQYZ < pIV(BFPNames::CQYZStr))
    {
        updateCQYZ(m_CurMaxCQYZ);
    }
}

void BeamFormerGrape::updatePRFCWD()
{
    double prf = m_CWDPRFCalculator->calculate().toDouble();
    setPV(BFPNames::PRFCWDStr, prf);
}

void BeamFormerGrape::onGettingFreqIndexDopText(QString& value)
{
    value = curProbe().FreqsDop[qobject_cast<Parameter*>(sender())->intValue()];
}

void BeamFormerGrape::onGettingFreqIndexColorText(QString& value)
{
    value = curProbe().FreqsColor[qobject_cast<Parameter*>(sender())->intValue()];
}

void BeamFormerGrape::createBFIODevice()
{
    BeamFormerBase::createBFIODevice(BFIODeviceType::PCIE);
}

void BeamFormerGrape::initializeProbeDection()
{
    //探头识别不采用sonoAir的方式
}

void BeamFormerGrape::initializeBFFpsParameter()
{
    // TODO: 后续统一处理
    m_FpsParameter = new BFFpsParameterPhoenix(m_SonoParameters, nullptr, nullptr);
    foreach (const QString& p, m_FpsParameter->relatedParameters())
    {
        connect(parameter(p), SIGNAL(valueChanged(QVariant)), this, SLOT(updateFPS()));
    }
}
