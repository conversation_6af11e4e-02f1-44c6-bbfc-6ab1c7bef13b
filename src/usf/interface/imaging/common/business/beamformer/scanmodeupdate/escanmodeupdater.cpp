#include "escanmodeupdater.h"
#include "infostruct.h"
#include "beamformerbase.h"
#include "sonoparameters.h"

EScanModeUpdater::EScanModeUpdater()
{
}

EScanModeUpdater::~EScanModeUpdater()
{
}

void EScanModeUpdater::update(SystemScanMode scan_mode)
{
    Q_ASSERT(scan_mode == SystemScanModeE);

    m_BeamFormer->setPV(BFPNames::ImageModeLeftFilterStr, QString::number((int)ImageEventArgs::ImageElasto));
    // 本意进E，实际进C， 注册需要
    m_BeamFormer->setSystemScanMode(SystemScanModeColorDoppler);
}
