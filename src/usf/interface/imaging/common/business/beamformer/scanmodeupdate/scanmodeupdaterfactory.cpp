#include "scanmodeupdaterfactory.h"
#include "basedscanmodeupdater.h"
#include "bccwprescanmodeupdater.h"
#include "bcdprescanmodeupdater.h"
#include "bcdscanmodeupdater.h"
#include "bcmprescanmodeupdater.h"
#include "bcmscanmodeupdater.h"
#include "bcscanmodeupdater.h"
#include "bcwprescanmodeupdater.h"
#include "bdprescanmodeupdater.h"
#include "bdscanmodeupdater.h"
#include "bmprescanmodeupdater.h"
#include "bmscanmodeupdater.h"
#include "bpdcwprescanmodeupdater.h"
#include "bpddprescanmodeupdater.h"
#include "bpdscanmodeupdater.h"
#include "bscanmodeupdater.h"
#include "btdidscanmodeupdater.h"
#include "btviscanmodeupdater.h"
#include "cpscanmodeupdater.h"
#include "fourbscanmodeupdater.h"
#include "infostruct.h"
#include "mscanmodeupdater.h"
#include "sononeedlescanmodeupdater.h"
#include "stateeventnames.h"
#include "stressechomodeupdater.h"
#include "twobscanmodeupdater.h"
#include "bccwscanmodeupdater.h"
#include "bcwscanmodeupdater.h"
#include "bdpdcwscanmodeupdater.h"
#include "bdpddscanmodeupdater.h"
#include "bmvidscanmodeupdater.h"
#include "bpdcwscanmodeupdater.h"
#include "bpddscanmodeupdater.h"
#include "freemscanmodeupdater.h"
#include "escanmodeupdater.h"

ScanModeUpdaterFactory::ScanModeUpdaterFactory()
{
}

ScanModeUpdaterFactory* ScanModeUpdaterFactory::Instance()
{
    static ScanModeUpdaterFactory instance;
    return &instance;
}

BaseScanModeUpdater* ScanModeUpdaterFactory::createScanModeUpdater(int scan_mode, const QString& state_name)
{
    BaseScanModeUpdater* scanModeUpdater = NULL;
    if (scan_mode == SystemScanModeB)
    {
        if (state_name == StateEventNames::BMPreModeState)
        {
            scanModeUpdater = new BMPreScanModeUpdater;
        }
        else if (state_name == StateEventNames::StressEcho)
        {
            scanModeUpdater = new StressEchoModeUpdater;
        }
        else if (state_name == StateEventNames::BDPreModeState)
        {
            scanModeUpdater = new BDPreScanModeUpdater;
        }
        else if (state_name == StateEventNames::BCWPreModeState)
        {
            scanModeUpdater = new BCWPreScanModeUpdater;
        }
        else
        {
            scanModeUpdater = new BScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanMode2B)
    {
        if (state_name == StateEventNames::TwoBMode1Active)
            scanModeUpdater = new TwoBScanMode1ActiveUpdater;
        else if (state_name == StateEventNames::TwoBMode2Active)
            scanModeUpdater = new TwoBScanMode2ActiveUpdater;
    }
    else if (scan_mode == SystemScanMode4B)
    {
        if (state_name == StateEventNames::FourBMode1Active)
            scanModeUpdater = new FourBScanMode1ActiveUpdater;
        else if (state_name == StateEventNames::FourBMode2Active)
            scanModeUpdater = new FourBScanMode2ActiveUpdater;
        else if (state_name == StateEventNames::FourBMode3Active)
            scanModeUpdater = new FourBScanMode3ActiveUpdater;
        else if (state_name == StateEventNames::FourBMode4Active)
            scanModeUpdater = new FourBScanMode4ActiveUpdater;
    }
    else if (scan_mode == SystemScanModeM)
    {
        scanModeUpdater = new MScanModeUpdater;
    }
    else if (scan_mode == SystemScanModeColorDoppler)
    {
        if (state_name == StateEventNames::BCDPreModeState)
        {
            scanModeUpdater = new BCDPreScanModeUpdater;
        }
        else if (state_name == StateEventNames::BCCWModeState)
        {
            scanModeUpdater = new BCCWPreScanModeUpdater;
        }
        else if (state_name == StateEventNames::BCMModeState)
        {
            scanModeUpdater = new BCMPreScanModeUpdater;
        }
        else
        {
            scanModeUpdater = new BCScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModeColorPW)
    {
        if (state_name == StateEventNames::BCDModeState)
        {
            scanModeUpdater = new BCDScanModeUpdater;
        }
        else if (state_name == StateEventNames::BCDPWModeState)
        {
            scanModeUpdater = new BCDPWScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModeLRBM || scan_mode == SystemScanModeUDBM)
    {
        scanModeUpdater = new BMScanModeUpdater;
    }
    else if (scan_mode == SystemScanModeColorLRBM || scan_mode == SystemScanModeColorUDBM ||
             scan_mode == SystemScanModePDLRBM || scan_mode == SystemScanModePDUDBM ||
             scan_mode == SystemScanModeDPDLRBM || scan_mode == SystemScanModeDPDUDBM ||
             scan_mode == SystemScanModeTDILRBM || scan_mode == SystemScanModeTDIUDBM)
    {
        scanModeUpdater = new BCMScanModeUpdater;
    }
    else if (scan_mode == SystemScanModeBPW)
    {
        if (state_name == StateEventNames::BDModeState)
        {
            scanModeUpdater = new BDScanModeUpdater;
        }
        else if (state_name == StateEventNames::BDPWModeState)
        {
            scanModeUpdater = new BDPWScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModeCWDColorDoppler)
    {
        if (state_name == StateEventNames::BCCWModeState)
        {
            scanModeUpdater = new BCCWScanModeUpdater;
        }
        else if (state_name == StateEventNames::BCCWPWModeState)
        {
            scanModeUpdater = new BCCWPWScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModeCWD)
    {
        if (state_name == StateEventNames::BCWModeState)
        {
            scanModeUpdater = new BCWScanModeUpdater;
        }
        else if (state_name == StateEventNames::BCWPWModeState)
        {
            scanModeUpdater = new BCWPWScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModeCWDDirectionalPowerDoppler)
    {
        if (state_name == StateEventNames::BDPDCWModeState)
        {
            scanModeUpdater = new BDPDCWScanModeUpdater;
        }
        else if (state_name == StateEventNames::BDPDCWPWModeState)
        {
            scanModeUpdater = new BDPDCWPWScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModeDPowerPW)
    {
        if (state_name == StateEventNames::BDPDDModeState)
        {
            scanModeUpdater = new BDPDDScanModeUpdater;
        }
        else if (state_name == StateEventNames::BDPDDPWModeState)
        {
            scanModeUpdater = new BDPDDPWScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModeMVIPW)
    {
        if (state_name == StateEventNames::BMVIDModeState)
        {
            scanModeUpdater = new BMVIDScanModeUpdater;
        }
        else if (state_name == StateEventNames::BMVIDPWModeState)
        {
            scanModeUpdater = new BMVIDPWScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModeCWDPowerDoppler)
    {
        if (state_name == StateEventNames::BPDCWModeState)
        {
            scanModeUpdater = new BPDCWScanModeUpdater;
        }
        else if (state_name == StateEventNames::BPDCWPWModeState)
        {
            scanModeUpdater = new BPDCWPWScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModePowerPW)
    {
        if (state_name == StateEventNames::BPDDModeState)
        {
            scanModeUpdater = new BPDDScanModeUpdater;
        }
        else if (state_name == StateEventNames::BPDDPWModeState)
        {
            scanModeUpdater = new BPDDPWScanModeUpdater;
        }
    }
    else if (scan_mode == SystemScanModeTissuePW)
    {
        if (state_name == StateEventNames::BTDIDModeState)
        {
            scanModeUpdater = new BTDIDScanModeUpdater;
        }
        else if (state_name == StateEventNames::BTDIDPWModeState)
        {
            scanModeUpdater = new BTDIDPWScanModeUpdater;
        }
    }

    else if (scan_mode == SystemScanModePowerDoppler || scan_mode == SystemScanModeDPowerDoppler ||
             scan_mode == SystemScanModeTissueDoppler)
    {
        if (state_name == StateEventNames::BPDDPreModeState || state_name == StateEventNames::BTDIDPreModeState ||
            state_name == StateEventNames::BDPDDPreModeState)
            scanModeUpdater = new BPDDPreScanModeUpdater;
        else if (state_name == StateEventNames::BPDCWPreModeState || state_name == StateEventNames::BDPDCWPreModeState)
            scanModeUpdater = new BPDCWPreScanModeUpdater;
        else
            scanModeUpdater = new BPDScanModeUpdater;
    }
    else if (scan_mode == SystemScanModeSonoNeedle)
    {
        scanModeUpdater = new SonoNeedleScanModeUpdater;
    }
    else if (scan_mode == SystemScanModeSonoNeedle)
    {
        scanModeUpdater = new SonoNeedleScanModeUpdater;
    }
    else if (scan_mode == SystemScanModeMVI)
    {
        if (state_name == StateEventNames::BPDDPreModeState)
            scanModeUpdater = new BPDDPreScanModeUpdater;
        else
            scanModeUpdater = new BTVIScanModeUpdater;
    }
    else if (scan_mode == SystemScanModeCP)
    {
        scanModeUpdater = new CPScanModeUpdater;
    }
    else if (scan_mode == SystemScanModeUDFreeM)
    {
        scanModeUpdater = new FreeMScanModeUpdater;
    }
    else if (scan_mode == SystemScanModeE)
    {
        scanModeUpdater = new EScanModeUpdater;
    }

    Q_ASSERT(scanModeUpdater != NULL);
    return scanModeUpdater;
}
