#include "basebfstaticparameters.h"
#include "resource.h"
#include <QFile>
#include <QTextStream>

const double BaseBFStaticParameters::ANGLE_PRECISION = 0.00175;
const int BaseBFStaticParameters::ULTRASOUND_VELOCITY_MS = 1540;
const int BaseBFStaticParameters::LPFINVALIDPOS = 7;

BaseBFStaticParameters::BaseBFStaticParameters()
{

    m_DopplerVolMMs = QVector<float>() << 0.5F << 1.0F << 2.0F << 3.0F << 5.0F << 10.0F << 15.0F << 20.0F;
    m_SteeringAngles = QVector<float>() << 20.0F << 10.0F << 5.0F << 0.0F << -5.0F << -10.0F << -20.0F; //偏转角度
    m_SteeringAngleIndexes = QVector<int>() << 3 << 2 << 1 << 0 << 5 << 6 << 7;
    m_AbsSteeringAngles = QList<int>() << 5 << 10 << 20;

    m_NeedleAngles = QVector<float>() << -30.0F << -25.0F << -20.0F << -15.0F << -10.0F << -5.0F << 0.0F << 5.0F
                                      << 10.0F << 15.0F << 20.0F << 25.0F << 30.0F; //偏转角度
    m_NeedleAngleIndexes = QVector<int>() << 14 << 13 << 11 << 12 << 10 << 9 << 8 << 1 << 2 << 4 << 3 << 5 << 6;
    m_NeedleAngle2Index.insert(0, 0);
    m_NeedleAngle2Index.insert(5, 1);
    m_NeedleAngle2Index.insert(10, 2);
    m_NeedleAngle2Index.insert(15, 3);
    m_NeedleAngle2Index.insert(20, 4);
    m_NeedleAngle2Index.insert(25, 5);
    m_NeedleAngle2Index.insert(30, 6);

    // 2014-2-20 16:09 zhangyong e-mail M间隔要从0开始计算，例如12，是0~12其实是13个PRT
    // 所以以前文档提供的个数都要+1
    //    ECO3和TIGER1 1398开始 TIGER1.5 现在的4档是
    //    每 12+1个PRT出一线m
    //    每 25+1个PRT出一线m
    //    每 62+1个PRT出一线m
    //    每101+1个PRT出一线m

    // 2014-2-26 根据zhuminwen查阅FPGA代码，发现新的代码判断clock的个数通过最高位1位(D8位)做判断，这样可以优化FPGA的速度
    //但是，导致clock比以前多1个，但是老的代码(高老师版本)还是使用传统的判断8位全1来判断，所以不用+1
    //    ECO1的四档是,这个是Tiger 1317搭配的FPGA一致
    //    每6个PRT出一线m
    //    每12个PRT出一线m
    //    每24个PRT出一线m
    //    每48个PRT出一线m

    float linePeriod = 0.426625f / 1000.0f;
    m_MPixelSizeSeces = QVector<float>() << linePeriod * 13 << linePeriod * 26 << linePeriod * 63 << linePeriod * 102;
    m_MSampleRates = QVector<int>() << 51 << 41 << 31 << 21;
    // m_RealFreqs = QVector<float>() << 2.46f << 2.91f << 3.55f << 4.00f << 5.33f << 6.40f << 8.00f << 10.6f << 2.28f
    // << 2.13f << 2.00f << 1.78f << 1.52f << 4.57f << 16.0f << 16.0f;
    m_RealFreqs = QVector<float>() << 2.5f << 3.08f << 3.64f << 4.0f << 5.0f << 6.67f << 8.0f << 10.0f << 1.67f << 2.0f
                                   << 2.22f << 2.67f << 2.85f << 3.33f << 4.44f << 5.71f;
    m_TxValues = QVector<float>() << 1.0f << 1.5f << 2.0f << 2.5f << 3.0f << 3.5f << 4.0f << 4.5;

    QVector<int> linesOfSpeed1 = QVector<int>() << 38 << 50 << 60 << 75 << 86 << 100 << 111 << 120 << 133 << 150 << 167
                                                << 200 << 250 /*<< 300 << 375 << 429*/;
    QVector<int> linesOfSpeed2 = QVector<int>() << 31 << 42 << 50 << 63 << 71 << 83 << 93 << 100 << 111 << 125 << 139
                                                << 167 << 208 /*<< 250 << 313 << 357*/;
    QVector<int> linesOfSpeed3 = QVector<int>() << 25 << 33 << 40 << 50 << 57 << 67 << 74 << 80 << 89 << 100 << 111
                                                << 133 << 167 /*<< 200 << 250 << 286*/;
    m_CWDLines = QVector<QVector<int>>() << linesOfSpeed1 << linesOfSpeed2 << linesOfSpeed3;

    m_CWDRate = QVector<int>() << 160 << 200 << 250 << 400 << 500 << 600 << 700 << 800;
    m_CWDSampleRateValues = QVector<double>()
                            << 6250 << 8330 << 10000 << 12500 << 14280 << 16670 << 18520 << 20000 << 22220 << 25000
                            << 27780 << 33330 << 41670 /*<< 50000 << 62500 << 125000*/;
    /* lotos
                            m_AccCountCoef = new double[8];
                            m_AccCountCoef[0] = 0.0066;
                            m_AccCountCoef[1] = 0.0055;
                            m_AccCountCoef[2] = 0.0044;
                            m_AccCountCoef[3] = 0.0033;
                            m_AccCountCoef[4] = 0.0026;
                            m_AccCountCoef[5] = 0.0018;
                            m_AccCountCoef[6] = 0.0011;
                            m_AccCountCoef[7] = 0.0088;
    lotos*/

    m_AccCountCoef = QVector<double>() << 0.0066 << 0.0055 << 0.0044 << 0.0033 << 0.0026 << 0.0018 << 0.0011 << 0.0007;

    m_MSpeedParas = QList<int>() << 11 << 21 << 31 << 41;

    m_DopSteeringAngles = QVector<float>()
                          << -30.0F << -28.0F << -26.0F << -24.0F << -22.0F << -20.0F << -18.0F << -16.0F << -15.0F
                          << -14.0F << -12.0F << -10.0F << -8.0F << -6.0F << -5.0F << -4.0F << -2.0F << 0.0F << 2.0F
                          << 4.0F << 5.0F << 6.0F << 8.0F << 10.0F << 12.0F << 14.0F << 15.0F << 16.0F << 18.0F << 20.0F
                          << 22.0F << 24.0F << 26.0F << 28.0F << 30.0F;

    readMDFCoef();
}

BaseBFStaticParameters::~BaseBFStaticParameters()
{
}

const QVector<float>& BaseBFStaticParameters::dopplerVolMMs() const
{
    return m_DopplerVolMMs;
}

const QVector<float>& BaseBFStaticParameters::steeringAngles() const
{
    return m_SteeringAngles;
}

const QVector<int>& BaseBFStaticParameters::steeringAngleIndexes() const
{
    return m_SteeringAngleIndexes;
}

const QVector<float>& BaseBFStaticParameters::needleAngles() const
{
    return m_NeedleAngles;
}

const QVector<int>& BaseBFStaticParameters::needleAngleIndexes() const
{
    return m_NeedleAngleIndexes;
}

const QHash<int, int>& BaseBFStaticParameters::needleAngle2Indexe() const
{
    return m_NeedleAngle2Index;
}

const QVector<float>& BaseBFStaticParameters::mPixelSizeSeces() const
{
    return m_MPixelSizeSeces;
}

const QVector<int>& BaseBFStaticParameters::mSampleRates() const
{
    return m_MSampleRates;
}

const QVector<float>& BaseBFStaticParameters::realFreqs() const
{
    return m_RealFreqs;
}

const QVector<float>& BaseBFStaticParameters::txValues() const
{
    return m_TxValues;
}

const QVector<double>& BaseBFStaticParameters::CWDSampleRateValues() const
{
    return m_CWDSampleRateValues;
}

const QVector<int>& BaseBFStaticParameters::CWDSpeedRates() const
{
    return m_CWDRate;
}

const QVector<QVector<int>>& BaseBFStaticParameters::CWDLines() const
{
    return m_CWDLines;
}

const QVector<double>& BaseBFStaticParameters::accCountCoef() const
{
    return m_AccCountCoef;
}

const QList<int>& BaseBFStaticParameters::absSteeringAngles() const
{
    return m_AbsSteeringAngles;
}

const QList<int>& BaseBFStaticParameters::mSpeedParas() const
{
    return m_MSpeedParas;
}

const QVector<int>& BaseBFStaticParameters::mdfGains() const
{
    return m_MDFGains;
}

const QVector<int>& BaseBFStaticParameters::mdfShifts() const
{
    return m_MDFShifts;
}

const QVector<QString> BaseBFStaticParameters::AFE_InImpedenceText() const
{
    return m_AFE_InImpedenceText;
}

const QVector<QString> BaseBFStaticParameters::AFE_LNA_GAINText() const
{
    return m_AFE_LNA_GAINText;
}

const QVector<QString> BaseBFStaticParameters::AFE_PGA_GAINText() const
{
    return m_AFE_PGA_GAINText;
}

const QVector<float> BaseBFStaticParameters::AFE_LPF_FCutOff() const
{
    return m_AFE_LPF_FCutOff;
}

const QVector<int> BaseBFStaticParameters::AFE_LPF_FCutOffControlTable() const
{
    return m_AFE_LPF_FCutOffControlTable;
}

const QVector<float> BaseBFStaticParameters::AFE_HPF_FCutOff() const
{
    return m_AFE_HPF_FCutOff;
}

const QVector<QString> BaseBFStaticParameters::AFE_LNA_BiasText() const
{
    return m_AFE_LNA_BiasText;
}

const QVector<QString> BaseBFStaticParameters::AFE_CW_HPF_FB_RESText() const
{
    return m_AFE_CW_HPF_FB_RESText;
}

const QVector<QString> BaseBFStaticParameters::AFE_CW_InImpedenceText() const
{
    return m_AFE_CW_InImpedenceText;
}

const QVector<QString> BaseBFStaticParameters::AFE_CW_LNA_GAINText() const
{
    return m_AFE_CW_LNA_GAINText;
}

const QVector<double> BaseBFStaticParameters::CWD1stFilterCoefficient() const
{
    return m_CWD1stFilterCoefficient;
}

const QVector<double> BaseBFStaticParameters::CWD2stFilterCoefficient() const
{
    return m_CWD2stFilterCoefficient;
}

const QVector<QVector<double>> BaseBFStaticParameters::CWDAccumulateNumList() const
{
    return m_CWDAccumulateNumList;
}

void BaseBFStaticParameters::initParameters()
{
}

void BaseBFStaticParameters::SetCWDAccumulateNumList()
{
}

void BaseBFStaticParameters::SetCWD1stExtractingFactor()
{
}

void BaseBFStaticParameters::SetCWD2stExtractingFactor()
{
}

const QVector<QVector<int>> BaseBFStaticParameters::CWD1stExtractingFactor() const
{
    return m_CWD1stExtractingFactor;
}

const QVector<QVector<int>> BaseBFStaticParameters::CWD2stExtractingFactor() const
{
    return m_CWD2stExtractingFactor;
}

const QVector<float>& BaseBFStaticParameters::dopSteeringAngles() const
{
    return m_DopSteeringAngles;
}

void BaseBFStaticParameters::readMDFCoef()
{
    QFile file(Resource::mDFCoefFileName());
    if (!file.open(QIODevice::ReadOnly))
    {
        return;
    }

    QTextStream text(&file);
    QStringList strList;
    while (!text.atEnd())
    {
        QString str = text.readLine();
        if (!str.isEmpty())
        {
            strList.append(str);
        }
    }

    if (strList.count() % 2 == 1)
    {
        strList.append("0");
    }

    for (int i = 0; i < strList.count(); i++)
    {
        if (i % 2 == 0)
        {
            m_MDFGains.append(strList[i].toInt());
        }

        if (i % 2 == 1)
        {
            m_MDFShifts.append(strList[i].toInt());
        }
    }
}

const QVector<float>& BaseBFStaticParameters::ADC_HPF_CORNER_FREQ() const
{
    return m_ADC_HPF_CORNER_FREQ;
}

const QVector<float>& BaseBFStaticParameters::AFE_PGA_CLAMP_LVL() const
{
    return m_AFE_PGA_CLAMP_LVL;
}

const QVector<int>& BaseBFStaticParameters::AFE_PGA_HI_FREQ() const
{
    return m_AFE_PGA_HI_FREQ;
}

const QVector<int>& BaseBFStaticParameters::AFE_HPF_FCutOffControlTable() const
{
    return m_AFE_HPF_FCutOffControlTable;
}

const QVector<int>& BaseBFStaticParameters::ADC_HPF_CORNER_FREQControlTable() const
{
    return m_ADC_HPF_CORNER_FREQControlTable;
}

const QVector<int>& BaseBFStaticParameters::AFE_PGA_CLAMP_LVLControlTable() const
{
    return m_AFE_PGA_CLAMP_LVLControlTable;
}
