#ifndef MEASURECONTEXT_H
#define MEASURECONTEXT_H

#include "abstractstandbyobj.h"
#include "usfinterfaceimagingbusiness_global.h"
#include "imageinfodef.h"
#include <QImage>
#include <QObject>
#include <QVariant>

class SonoParameters;
class IBufferStoreManager;
class QVariant;
class ImageEventArgs;
class ILineBuffer;
class CineLooper;
class IScreenMeasureController;

#define MAXDURATION 2 // 2秒

/**
 * @brief 提供测量过程像素与物理单位换算比值,不同模式下比值不同.
 */
class USF_INTERFACE_IMAGING_COMMON_BUSINESS_EXPORT MeasureContext : public QObject, public AbstractStandbyObj
{
    Q_OBJECT
public:
    enum FrameType
    {
        TWOD = 0,  // B + C
        WAVE = 1,  //波形
        SOUND = 2, //声音
    };
    MeasureContext(IBufferStoreManager* bufferStoreManager, QObject* parent = 0);
    MeasureContext(QObject* parent = 0);
    void setSonoParameter(SonoParameters* sonoParameter);

    double pixelSizeMM() const;
    double pixelSizeCM() const;

    bool isFreeze() const;
    bool isBSystemScanMode() const;

    bool isRotation() const;

    double mPixelSizeMM() const;
    double mPixelSizeCM() const;

    bool invert() const;
    /**
     * @brief isMultiBImageParaEqual 2B或4B模式下判断pixelsizemm,left,up,zoomcoef,rotation,startdepth是否相等
     * 不相等的话需要分区域测量
     * @return
     */
    bool isMultiBImageParaEqual() const;
    /**
     * @brief isMultiBPixelSizeNull 判断pixelsizemm0123是否为空,用来兼容此前版本保存的图片
     * @return
     */
    bool isMultiBPixelSizeNull() const;
    /**
     * @brief mPixelSec M图像区域每个X轴方向像素代表的时间，单位:s
     *
     * @return
     */
    double mPixelSec() const;
    /**
     * @brief dBaseLineYPos PW BaseLine 的Y坐标，以PW图像区域左上角为原点
     *
     * @return
     */
    int dBaseLineYPos() const;
    /**
     * @brief dBaseLineWidth PW BaseLine 的长度
     *
     * @return
     */
    int dBaseLineWidth() const;
    /**
     * @brief dPixelSizeCMS PW图像区域每个Y轴方向像素代表的速度，单位:cm/s
     *
     * @return
     */
    double dPixelSizeCMS() const;
    /**
     * @brief dPixelSizeSec PW图像区域每个X轴方向像素代表的时间，单位:s
     *
     * @return
     */
    double dPixelSizeSec() const;

    void colorVelocity(double& top, double& bottom);
    /**
     * @brief dVelInvert PW速度是否在BaseLine上下颠倒
     *
     * false: baseLine上面的坐标速度为正，下面的坐标速度为负
     * true: baseLine上面的坐标速度为负，下面的坐标速度为正
     *
     * @return
     */
    bool dVelInvert() const;

    void setBImage(const QImage& img, int index = 0);
    const QImage& bImage(int index = 0) const;

    void setDImage(const QImage& image);
    const QImage& dImage() const;

    void setMImage(const QImage& image);
    const QImage& mImage() const;

    const QImage& image() const;
    void setImage(const QImage& value);

    const QImage& renderImage() const;
    void setRenderImage(const QImage& image);
    bool isImageValid() const;
    const QImage& rawDataImage() const;
    void setRawDataImage(const QImage& value);
    bool isRawDataImageValid() const;

    void dVelocity(double& top, double& bottom);
    int threshold() const;
    int dTraceSmooth() const;
    int rotation() const;
    int elastoDR() const;

    SonoParameters* currentSonoParameters() const;

    int measLayoutIndex() const;
    void setMeasLayoutIndex(int index);

    virtual bool standby();
    virtual bool wake();
    void setImageWithoutValidate(const QImage& value);

    bool playMovieForAutoMeas();
    void updateSonoparameter();
    QRect activeBRect(const QPointF pt);
    void sendGetColorRawDataSignal(int type, int x, int y);
    void setCurrentPointRawData(const int& data);
    int CurrentPointRawData();

    void setBufferManager(ILineBuffer* bufferManager);
    void setCineLooper(CineLooper* cineLooper);
    void setCurrentIndex(int index, bool needUpdateFreezeBar = true);
    int frameCount();
    void stopPlayMovie();

    void setRealTimeMeasureFrameIndex(int);
    int getRealTimeMeasureFrameIndex();

    void setRealTimeMeasureFrontIndex(int);
    int getRealTimeMeasureFrontIndex();

    void enableRuler();

    bool refreshSonoParameter() const;
    void setRefreshSonoParameter(bool NeedUpdate);

    bool isSingleBSystemScanMode() const;
    bool isEcgOn() const;
    int currentIndex();
    int calDuration(int& startIndex, int& endIndex, bool& isEnoughFrames);
    void PlayFromCurIndex(int curIndex);
    void calTime(int curIndex, int& startIndex, int& endIndex, bool& isEnoughFrames);
    void setAutoEFActiveLayout(int curLayout); //设置AutoEF的激活区
    void setAutoEFIndexs(int edvindex, int esvindex);
    void setAutoEFEDCurIndex(qint64 edvCurIndex);
    void setAutoEFESCurIndex(qint64 esvCurIndex);
    void setAutoEFStartEndIndexs(int startindex, int endindex);
    int autoEFActiveLayout();
    qint64 autoEFEDCurIndex();
    qint64 autoEFESCurIndex();
    void setAutoEFEDFrameIndex(int edvIndex);
    void setAutoEFESFrameIndex(int esvIndex);
    int startIndex();
    int endIndex();
    qint64 startTimeStamp();
    qint64 endTimeStamp();
    int edFrameIndex();
    int esFrameIndex();
    void setAutoEFHasResult(bool value);
    bool hasAutoEFResult();
    void setImageRenderPartition(int partition);
    void setBCImageOn(bool value);
    void ConnectSignals(bool connect);
    void drawUnActiveLayout();
    int oldPartitionChanged();
    int getFrameIndexByFrameTimestamp(const qint64& timestamp) const;
    qint64 getFramestampByIndex(int frameIndex) const;
    int whloeFrameIndex(const int type, const int typeIndex);
    void setLastLayoutForAutoEF(int lastLayoutForAutoEF);
    void setCurLayoutForAutoEF(int curLayoutForAutoEF);
    int getCurLayoutForAutoEF() const;
    int Layout();
    QByteArray getWholeFrameData(int frameType, const int index);
    int fMN() const;
    void setFMN(int fmn);

    int measuredBIndex() const;
    void setMeasuredBIndex(int measuredBIndex);
    QRect calImageValidArea(int width, int height);
    void* rawData() const;
    void setRawData(void* RawData);

    int rawDataHeight() const;
    void setRawDataHeight(int rawDataHeight);

    int rawDataWidth() const;
    void setRawDataWidth(int rawDataWidth);

    const QImage& croppedDImage() const;
    void setCroppedDImage(const QImage& croppedDImage);

    int waveDataWidth() const;
    void setWaveDataWidth(int WaveDataWidth);

    int getFrameIndex() const;

public slots:
    void slotHasVelocityRawData(int layoutIndex);
    void onChangeFrameIndex(qint64 index);
    void onUpdateESVIndex(qint64 index);
    void onUpdateEDVIndex(qint64 index);
    void slotAutoEFCompleted();
    void slotHasAutoEFResult(QVariant value);
    void clearMeasureContextImage();
    void onRenderImageChanged(const QPixmap& pix, ModeImageType modeType, int frameIndex);

private slots:
    void onBeforeCurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onDVelInvertChanged(const QVariant& value);
    void onFreezeChanged(const QVariant& value);
    void currentPointLayoutIndex(int index);
    void onNewImage(ImageEventArgs* imageEventArgs);
    void onCurrentIndexChanged(int index);
    void slotOnEDFrameChanged(QVariant value);
    void slotOnESFrameChanged(QVariant value);
    void slotOnEDCurIndexChanged(QVariant value);
    void slotOnESCurIndexChanged(QVariant value);

signals:
    void currentIndexChanged(int index);
    void newImage(ImageEventArgs* imageEventArgs);
    void imageValidChanged(bool valid);
    void invertChanged();
    void rawDataImageValidChanged(bool valid);
    void anchorPosChanged(const QPointF& pos, QRectF);
    void anchorVisibleChanged(bool isShow, QRectF);
    void signalCreateInputWidget(const QString& title, const QString& unit);
    void signalCurrentTextValue(bool, const QString& value);
    void signalHideInputWidget();
    void signalCreateFetalHeadPosWidget(const QSizeF&);
    void signalCurrentValue(const QString& value);
    void signalHideFetalHeadPosWidget();
    void signalCreateFetalHeadDirectionWidget(const QSizeF&);
    void signalCurrentDirectionValue(const QString& value);
    void signalHideFetalHeadDirectionWidget();
    void signalGetColorRawData(int type, int index, int x,
                               int y); // index:当前点处于第几个layout里  type:当前需要取什么原始数据，1表示血流速度
                                       // x:当前点的x坐标  y:当前点的y坐标
    void signalIsGetAllLayoutRawData(bool, const int& layoutCount);
    void signalSetAllLayoutRawDataTigger(bool, const int& layoutCount);
    void playMovie();
    void stopMovie();
    void freezeChanged(bool isFreeze);
    void signalHasVelocityRawData(int);
    void requestImage() const;
    void updateESVIndex(qint64 index);
    void updateEDVIndex(qint64 index);
    void changeFrameIndex(qint64 index);
    void AutoEFCompleted();
    void onHasAutoEFResultChanged(QVariant);
    void onImageRenderPartitionChanged(QVariant);
    void onActivePartitionChanged(QVariant);
    void onEDFrameChanged(QVariant);
    void onESFrameChanged(QVariant);
    void onEDCurIndexChanged(QVariant);
    void onESCurIndexChanged(QVariant);
    void onAutoEFOnChanged(QVariant);
    void onActiveBChanged(QVariant);
    void updateSonoParameter(SonoParameters*);
    void fmnChanged();
    void editFinish();
    void imageRefreshed();

private:
    IBufferStoreManager* m_BufferStoreManager;
    ILineBuffer* m_LineBufferManager;
    // CineLooper* m_Loop;
    SonoParameters* m_SonoParameters;
    QImage m_BImage;
    QImage m_BTwoImage;
    QImage m_BThreeImage;
    QImage m_BFourImage;
    QImage m_MImage;
    QImage m_DImage;
    QImage m_CroppedDImage;
    QImage m_Image;
    QImage m_PureBImage; //例如在BC模式下 会去除C的图像 只有B图像
    /*
     * TODO: 随着图像链路的不断更新 现在会出现一个问题，那就是在进入测量状态获取到的图像跟冻结后的实际图像会有差异
     * 因此增加m_ValidImage 仅仅是用来判断当前测量是否可用
     * 这个点后面可以优化 通过是实时还是冻结判断 不通过Image是否未为空判断
     */
    QImage m_ValidImage;
    QImage m_RawDataImage;
    QImage m_RenderImage;  // 添加非整帧的QImage, 这里主要是用于各模式类型的图像
    int m_MeasLayoutIndex; //当前多B测量时，测量区在第几个
    int m_CurPointRawData;
    int m_RealTimeMeasureFrameIndex;
    int m_RealTimeMeasureFrontIndex;
    bool m_RefreshSonoParameter;
    int m_StartFrameIndexForAutoEF;
    int m_EndFrameIndexForAutoEF;
    int m_curLayoutForAutoEF;
    int m_lastLayoutForAutoEF;
    int m_FMN; //近 中 远
    int m_measuredBIndex{0};
    //线数据
    void* m_RawData{nullptr};
    int m_RawDataHeight{0};
    int m_RawDataWidth{0};
    int m_WaveDataWidth{0};
    int m_FrameIndex;
};

#endif // MEASURECONTEXT_H
