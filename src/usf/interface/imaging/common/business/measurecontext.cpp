#include "measurecontext.h"
#include "bfpnames.h"
#include "ibufferstoremanager.h"
#include "ilinebuffer.h"
#include "infostruct.h"
#include "parameter.h"
#include "realcompare.h"
#include "sonoparameters.h"
#include "toolnames.h"
#include "util.h"
#include "bfcoordtransform.h"
#include "probedataset.h"
#include "formula.h"

MeasureContext::MeasureContext(IBufferStoreManager* bufferStoreManager, QObject* parent)
    : QObject(parent)
    , m_BufferStoreManager(bufferStoreManager)
    , m_SonoParameters(bufferStoreManager->curSonoParameters())
    , m_MeasLayoutIndex(-1)
    , m_RealTimeMeasureFrameIndex(-1)
    , m_RealTimeMeasureFrontIndex(-1)
    , m_FMN(0)
    , m_WaveDataWidth(0)
{
    if (m_BufferStoreManager != NULL)
    {
        connect(m_BufferStoreManager, SIGNAL(beforeCurSonoParametersChanged()), this,
                SLOT(onBeforeCurSonoParametersChanged()));
        connect(m_BufferStoreManager, SIGNAL(curSonoParametersChanged()), this, SLOT(onCurSonoParametersChanged()));
        onCurSonoParametersChanged();
    }
}

MeasureContext::MeasureContext(QObject* parent)
    : QObject(parent)
    , m_BufferStoreManager(NULL)
    , m_SonoParameters(NULL)
    , m_MeasLayoutIndex(-1)
    , m_LineBufferManager(nullptr)
    , m_RealTimeMeasureFrameIndex(-1)
    , m_RealTimeMeasureFrontIndex(-1)
    , m_CurPointRawData(-1)
    , m_RefreshSonoParameter(false)
    , m_StartFrameIndexForAutoEF(-1)
    , m_EndFrameIndexForAutoEF(-1)
    , m_curLayoutForAutoEF(0)
    , m_lastLayoutForAutoEF(0)
    , m_FMN(0)
    , m_WaveDataWidth(0)
    , m_FrameIndex(-1)
{
}
/**
 * @brief IOS平台不创建bufferStoreManager， 故只能每次外界设当前sonoParameter
 */
void MeasureContext::setSonoParameter(SonoParameters* sonoParameter)
{
    if (m_SonoParameters != nullptr)
    {
        ConnectSignals(false);
    }

    m_SonoParameters = sonoParameter;

    emit updateSonoParameter(m_SonoParameters);

    if (m_SonoParameters != nullptr)
    {
        ConnectSignals(true);
    }
}

bool MeasureContext::isFreeze() const
{
    Q_ASSERT(m_SonoParameters != nullptr);

    if (m_SonoParameters == nullptr)
    {
        return false;
    }
    return !m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr);
}

bool MeasureContext::isBSystemScanMode() const
{
    Q_ASSERT(m_SonoParameters != nullptr);

    return m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanModeB ||
           m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanMode2B ||
           m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanMode4B;
}

bool MeasureContext::isRotation() const
{
    Q_ASSERT(m_SonoParameters != nullptr);

    return m_SonoParameters->pIV(BFPNames::RotationStr) != 0;
}

double MeasureContext::pixelSizeMM() const
{
    Q_ASSERT(m_SonoParameters != nullptr);
    return m_SonoParameters->pDV(BFPNames::PixelSizeMMStr);
}

bool MeasureContext::invert() const
{
    Q_ASSERT(m_SonoParameters != nullptr);

    return m_SonoParameters->pBV(BFPNames::UpStr);
}

double MeasureContext::pixelSizeCM() const
{
    return pixelSizeMM() / 10.0f;
}

double MeasureContext::mPixelSizeMM() const
{
    return m_SonoParameters->pDV(BFPNames::MPixelSizeMMStr);
}

double MeasureContext::mPixelSizeCM() const
{
    return mPixelSizeMM() / 10.0f;
}

bool MeasureContext::isMultiBImageParaEqual() const
{
    bool isAllEqual = true, isCurEqual = true;

    SonoParameters* firstB;
    if (m_SonoParameters->pIV(BFPNames::LayoutStr) == Layout_1x2 ||
        m_SonoParameters->pIV(BFPNames::LayoutStr) == Layout_2x2)
    {
        int layoutCount = m_SonoParameters->pIV(BFPNames::LayoutStr);
        firstB = m_BufferStoreManager->sonoParameters(0);
        if (firstB != nullptr)
        {
            for (int i = 1; i < layoutCount; i++)
            {
                SonoParameters* curLayoutB = m_BufferStoreManager->sonoParameters(i);
                if (curLayoutB != nullptr)
                {
                    isCurEqual =
                        RealCompare::AreEqual(firstB->pDV(BFPNames::PixelSizeMMStr),
                                              curLayoutB->pDV(BFPNames::PixelSizeMMStr)) &&
                        RealCompare::AreEqual(firstB->pDV(BFPNames::StartDepthMMStr),
                                              curLayoutB->pDV(BFPNames::StartDepthMMStr)) &&
                        firstB->pBV(BFPNames::LeftStr) == curLayoutB->pBV(BFPNames::LeftStr) &&
                        firstB->pBV(BFPNames::UpStr) == curLayoutB->pBV(BFPNames::UpStr) &&
                        firstB->pIV(BFPNames::ImageZoomCoefStr) == curLayoutB->pIV(BFPNames::ImageZoomCoefStr) &&
                        firstB->pIV(BFPNames::RotationStr) == curLayoutB->pIV(BFPNames::RotationStr);

                    isAllEqual = (isAllEqual && isCurEqual);
                }
            }
        }
    }

    return isAllEqual;
}

bool MeasureContext::isMultiBPixelSizeNull() const
{
    //由于改进采用多个参数的方式，因此直接返回false，而加载无多B情况下，m_BufferStoreManager->curSonoParameters()的不具备返回false的条件
    // m_SonoParameters在currentPointLayoutIndex会被刷新，用做返回测量的参数，而最初的参数用做连接信号
    return false;

    //    return m_SonoParameters->parameter(BFPNames::PixelSizeMM_0Str)->isNull() ||
    //            m_SonoParameters->parameter(BFPNames::PixelSizeMM_1Str)->isNull() ||
    //            m_SonoParameters->parameter(BFPNames::PixelSizeMM_2Str)->isNull() ||
    //            m_SonoParameters->parameter(BFPNames::PixelSizeMM_3Str)->isNull();
}

double MeasureContext::mPixelSec() const
{
    return m_SonoParameters->pDV(BFPNames::MPixelSizeSecStr);
}

int MeasureContext::dBaseLineYPos() const
{
    return m_SonoParameters->pIV(BFPNames::DBaseLineYPosStr);
}

int MeasureContext::dBaseLineWidth() const
{
    if (m_SonoParameters->contains(BFPNames::DBaseLineWidthStr))
    {
        return m_SonoParameters->pIV(BFPNames::DBaseLineWidthStr);
    }
    else
    {
        //如果没有DBaseLineWidthStr，则使用图像区宽度, 兼容已保存的图片
        return m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize().width();
    }
}

double MeasureContext::dPixelSizeCMS() const
{
    return m_SonoParameters->pDV(BFPNames::DPixelSizeCMSStr);
}

double MeasureContext::dPixelSizeSec() const
{
    return m_SonoParameters->pDV(BFPNames::DPixelSizeSecStr);
}

void MeasureContext::colorVelocity(double& top, double& bottom)
{
    double maxVel = m_SonoParameters->pDV(BFPNames::CMaxVelCMSStr);
    QString baseLineParaName =
        m_SonoParameters->pBV(BFPNames::TDIEnStr) ? BFPNames::BaseLineTDIStr : BFPNames::BaseLineColorStr;
    int baseLine = m_SonoParameters->pIV(baseLineParaName);
    int maxBaseLine = m_SonoParameters->pMax(baseLineParaName);

    float fraction = (float)(maxBaseLine - baseLine) / maxBaseLine;

    float topVal = fraction * maxVel;
    float bottomVal = topVal - maxVel;
    top = topVal;
    bottom = bottomVal;
}

bool MeasureContext::dVelInvert() const
{
    return m_SonoParameters->pBV(BFPNames::SpectralInvertStr);
}

void MeasureContext::setBImage(const QImage& img, int index)
{
    switch (index)
    {
    case 0:
        m_BImage = img;
        break;
    case 1:
        m_BTwoImage = img;
        break;
    case 2:
        m_BThreeImage = img;
        break;
    case 3:
        m_BFourImage = img;
        break;
    default:
        m_BImage = img;
    }
}

const QImage& MeasureContext::bImage(int index) const
{
    if (m_BImage.isNull())
    {
        emit requestImage();
    }

    switch (index)
    {
    case 0:
        return m_BImage;
    case 1:
        return m_BTwoImage;
    case 2:
        return m_BThreeImage;
    case 3:
        return m_BFourImage;
    default:
        return m_BImage;
    }
}

void MeasureContext::setDImage(const QImage& image)
{
    m_DImage = image;
}

const QImage& MeasureContext::dImage() const
{
    if (m_DImage.isNull())
    {
        emit requestImage();
    }
    return m_DImage;
}

void MeasureContext::setMImage(const QImage& image)
{
    m_MImage = image;
}

const QImage& MeasureContext::mImage() const
{
    if (m_MImage.isNull())
    {
        emit requestImage();
    }
    return m_MImage;
}

int MeasureContext::threshold() const
{
    if (m_SonoParameters->parameterSet().contains(BFPNames::ThresholdStr))
    {
        return m_SonoParameters->pIV(BFPNames::ThresholdStr);
    }
    else
    {
        return 1; // 兼容前后版本
    }
}

int MeasureContext::dTraceSmooth() const
{
    if (m_SonoParameters->parameterSet().contains(BFPNames::DTraceSmoothStr))
    {
        return m_SonoParameters->pIV(BFPNames::DTraceSmoothStr);
    }
    else
    {
        return 2; // 兼容前后版本
    }
}
int MeasureContext::rotation() const
{
    return m_SonoParameters->pIV(BFPNames::RotationStr);
}

int MeasureContext::elastoDR() const
{
    return m_SonoParameters->pIV(BFPNames::ElastoDRStr);
}

SonoParameters* MeasureContext::currentSonoParameters() const
{
    return m_SonoParameters;
}

bool MeasureContext::standby()
{
    if (!isStandby())
    {
        setIsStandby(true);
    }
    return true;
}

bool MeasureContext::wake()
{
    if (isStandby())
    {
        setIsStandby(false);
    }
    return true;
}

void MeasureContext::setImageWithoutValidate(const QImage& value)
{
    m_Image = value;
}

// TODO: 在easyview场景下 这个函数就不适用
void MeasureContext::updateSonoparameter()
{
    if (m_RefreshSonoParameter)
        m_SonoParameters = m_BufferStoreManager->activeSonoParameters();
}

QRect MeasureContext::activeBRect(const QPointF pt)
{
    const QList<QVariant> regionList = m_SonoParameters->pV(BFPNames::ImageRegionsStr).toList();
    for (int index = 0; index < regionList.count(); index++)
    {
        if (regionList.at(index).toRectF().contains(pt))
        {
            return regionList.at(index).toRect();
        }
    }
    return regionList.at(m_SonoParameters->pIV(BFPNames::ActiveBStr)).toRect();
}

const QImage& MeasureContext::image() const
{
    if (m_Image.isNull())
    {
        emit requestImage();
    }
    return m_Image;
}

void MeasureContext::setImage(const QImage& value)
{
    bool oldIsNull = m_ValidImage.isNull();
    m_ValidImage = value;
    setImageWithoutValidate(value);
    bool newIsNull = m_ValidImage.isNull();
    if (newIsNull != oldIsNull)
    {
        emit imageValidChanged(!newIsNull);
    }
}

const QImage& MeasureContext::renderImage() const
{
    return m_RenderImage;
}

void MeasureContext::setRenderImage(const QImage& image)
{
    m_RenderImage = image;
}

bool MeasureContext::isImageValid() const
{
    return !m_Image.isNull();
}

const QImage& MeasureContext::rawDataImage() const
{
    return m_RawDataImage;
}

void MeasureContext::setRawDataImage(const QImage& value)
{
    Q_ASSERT(m_SonoParameters != NULL);
    if (m_SonoParameters != NULL)
    {
        bool oldIsNull = m_RawDataImage.isNull();
        m_RawDataImage = m_SonoParameters->pBV(BFPNames::ElastoEnStr) ? value : QImage();
        bool newIsNull = m_RawDataImage.isNull();
        if (newIsNull != oldIsNull)
        {
            emit rawDataImageValidChanged(!newIsNull);
        }
    }
}

bool MeasureContext::isRawDataImageValid() const
{
    return !m_RawDataImage.isNull();
}

// baseline 0~6 对应的在D图像区的Y坐标，把D图像Y轴分隔成8个区间
//下发的baseline是反的
//(0,0)---------------------------------
//
//--------------------------------------6
//
//--------------------------------------5
//
//--------------------------------------4
//
//--------------------------------------3
//
//--------------------------------------2
//
//--------------------------------------1
//
//--------------------------------------0
//
//---------------------------------------(640, 256)
// void BeamFormerBase::onBaseLineChanging(const QVariant &value)
//{
//    int intervalCount = pMax(BFPNames::BaseLineStr) - pMin(BFPNames::BaseLineStr) + 2;
//    setPV(BFPNames::DBaseLineYPosStr, (intervalCount - 1 - value.toInt()) * (dImageSize().height() / intervalCount));
//}

void MeasureContext::dVelocity(double& top, double& bottom)
{
    int dDeight = m_SonoParameters->pV(BFPNames::DImageSizeStr).toSize().height();
    int invertValue = dVelInvert() ? -1 : 1;
    top = dBaseLineYPos() * dPixelSizeCMS() * invertValue;
    bottom = (dBaseLineYPos() - dDeight) * dPixelSizeCMS() * invertValue;
}

void MeasureContext::onBeforeCurSonoParametersChanged()
{
    m_SonoParameters = NULL;
}

void MeasureContext::onCurSonoParametersChanged()
{
    if (m_SonoParameters != nullptr)
    {
        ConnectSignals(false);
    }

    m_SonoParameters = m_BufferStoreManager->curSonoParameters();
    // for notify rule
    if (m_SonoParameters != NULL)
    {
        Util::connectSignal(m_SonoParameters->parameter(BFPNames::SpectralInvertStr), SIGNAL(valueChanged(QVariant)),
                            this, SLOT(onDVelInvertChanged(QVariant)), true);
        Util::connectSignal(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
                            SLOT(onFreezeChanged(QVariant)), true);
        onFreezeChanged(m_SonoParameters->pV(BFPNames::FreezeStr));
        ConnectSignals(true);
    }
}

void MeasureContext::onFreezeChanged(const QVariant& value)
{
    if (m_SonoParameters->isRealTime())
    {
        emit freezeChanged(value.toBool());
    }
    else
    {
        emit freezeChanged(true);
    }
    if (!value.toBool() && !isStandby())
    {
        setImage(QImage());
        setRawDataImage(QImage());
    }
    else
    {
        emit imageValidChanged(true);
    }
}

void MeasureContext::onDVelInvertChanged(const QVariant& value)
{
    // for notify rule
    emit invertChanged();
}

void MeasureContext::currentPointLayoutIndex(int index)
{
    if (m_LineBufferManager->frameCount(index) <= 0)
    {
        return;
    }

    if (m_SonoParameters != nullptr)
    {
        ConnectSignals(false);
    }

    if (index >= 0 && index < 4)
    {
        m_SonoParameters = m_BufferStoreManager->sonoParameters(index);
        //解决回调时，彩虹测速只有激活区有值的问题，方案是图标移动到哪里激活对应区域
        //        if(!m_BufferStoreManager->curSonoParameters()->isRealTime()) //解决非激活区，测量彩虹测速无值的问题
        {
            m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::ActiveBStr, index);
        }
    }
    else
    {
        if (m_RefreshSonoParameter)
            m_SonoParameters = m_BufferStoreManager->activeSonoParameters();
    }

    if (m_SonoParameters != nullptr)
    {
        ConnectSignals(true);
    }

    setMeasLayoutIndex(index);
}

void MeasureContext::onNewImage(ImageEventArgs* imageEventArgs)
{
    emit newImage(imageEventArgs);
}

int MeasureContext::measLayoutIndex() const
{
    return m_MeasLayoutIndex;
}

void MeasureContext::setMeasLayoutIndex(int index)
{
    m_MeasLayoutIndex = index;
}

void MeasureContext::sendGetColorRawDataSignal(int type, int x, int y)
{
    emit signalGetColorRawData(type, m_MeasLayoutIndex, x, y);
}

void MeasureContext::setCurrentPointRawData(const int& data)
{
    m_CurPointRawData = data;
}

int MeasureContext::CurrentPointRawData()
{
    return m_CurPointRawData;
}

void MeasureContext::onCurrentIndexChanged(int index)
{
    emit currentIndexChanged(index);
}

bool MeasureContext::refreshSonoParameter() const
{
    return m_RefreshSonoParameter;
}

void MeasureContext::setRefreshSonoParameter(bool NeedUpdate)
{
    m_RefreshSonoParameter = NeedUpdate;
}

int MeasureContext::frameCount()
{
    if (m_LineBufferManager == nullptr)
    {
        return -1;
    }
    return m_LineBufferManager->frameCount();
}

void MeasureContext::stopPlayMovie()
{
    emit stopMovie();
}

void MeasureContext::setRealTimeMeasureFrameIndex(int frameIndex)
{
    m_RealTimeMeasureFrameIndex = frameIndex;
}

int MeasureContext::getRealTimeMeasureFrameIndex()
{
    return m_RealTimeMeasureFrameIndex;
}

void MeasureContext::setRealTimeMeasureFrontIndex(int frontIndex)
{
    m_RealTimeMeasureFrontIndex = frontIndex;
}

int MeasureContext::getRealTimeMeasureFrontIndex()
{
    return m_RealTimeMeasureFrontIndex;
}

void MeasureContext::enableRuler()
{
    emit imageValidChanged(true);
}

void MeasureContext::slotHasVelocityRawData(int layoutIndex)
{
    //由slotHasVelocityRawData可知已经收到宙斯的原始数据，
    //在冻结与回调的状态下通知彩虹测速测量已有原始数据
    if (!m_BufferStoreManager->curSonoParameters()->isRealTime() ||
        m_BufferStoreManager->curSonoParameters()->pBV(BFPNames::FreezeStr))
    {
        emit signalHasVelocityRawData(layoutIndex);
    }
}

bool MeasureContext::playMovieForAutoMeas()
{
    int count = m_LineBufferManager->frameCount();
    if (count == 0)
    {
        return false;
    }
    else
    {
        m_LineBufferManager->setCurrentIndex(0);
        emit playMovie();
    }
    return true;
}

void MeasureContext::setBufferManager(ILineBuffer* bufferManager)
{
    m_LineBufferManager = bufferManager;
}

void MeasureContext::setCurrentIndex(int index, bool needUpdateFreezeBar)
{
    m_LineBufferManager->setCurrentIndex(index, -1, needUpdateFreezeBar);
}

bool MeasureContext::isSingleBSystemScanMode() const
{
    Q_ASSERT(m_SonoParameters != nullptr);

    return m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanModeB;
}

bool MeasureContext::isEcgOn() const
{
    return m_SonoParameters->pIV(BFPNames::ECGEnStr);
}

void MeasureContext::slotOnEDFrameChanged(QVariant value)
{
    if (m_LineBufferManager == nullptr)
    {
        return;
    }
    QVariant val = m_LineBufferManager->getFrameIndexByFrameTimestamp(
        m_SonoParameters->pV(BFPNames::EDFrameStampStr).toLongLong());
    emit onEDFrameChanged(val);
}

void MeasureContext::slotOnESFrameChanged(QVariant value)
{
    if (m_LineBufferManager == nullptr)
    {
        return;
    }

    QVariant val = m_LineBufferManager->getFrameIndexByFrameTimestamp(
        m_SonoParameters->pV(BFPNames::ESFrameStampStr).toLongLong());
    emit onESFrameChanged(val);
}

void MeasureContext::slotOnEDCurIndexChanged(QVariant value)
{
    if (m_LineBufferManager == nullptr)
    {
        return;
    }
    QVariant val = m_LineBufferManager->getFrameIndexByFrameTimestamp(value.toLongLong());
    emit onEDCurIndexChanged(val);
}

void MeasureContext::slotOnESCurIndexChanged(QVariant value)
{
    if (m_LineBufferManager == nullptr)
    {
        return;
    }
    QVariant val = m_LineBufferManager->getFrameIndexByFrameTimestamp(value.toLongLong());
    emit onESCurIndexChanged(val);
}

int MeasureContext::getFrameIndex() const
{
    return m_FrameIndex;
}

int MeasureContext::measuredBIndex() const
{
    return m_measuredBIndex;
}

void MeasureContext::setMeasuredBIndex(int measuredBIndex)
{
    m_measuredBIndex = measuredBIndex;
}

// TODO: 下面这个逻辑 目前只能计算线阵 待完善
QRect MeasureContext::calImageValidArea(int width, int height)
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
    if (probeDataInfo.IsLinear)
    {
        QSize imageSize = m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
        BFCoordTransform bf(m_SonoParameters);

        // 2025-05-14 Write by AlexWang [BUG:80139]
        // 考虑在非局部放大状态和局部放大状态时的通用性，通过StartDepthMM确定起始深度，使用bottomDepthMM计算实际深度
        // 使用矩阵转换公式计算出实际坐标位置
        QPoint leftTop;
        bf.convertPhysicsToLogic(m_SonoParameters->pIV(BFPNames::StartLineStr),
                                 m_SonoParameters->pDV(BFPNames::StartDepthMMStr), leftTop);
        leftTop = Formula::flipAndRectZoomCoefTransform(m_SonoParameters->pBV(BFPNames::UpStr),
                                                        m_SonoParameters->pBV(BFPNames::LeftStr), imageSize,
                                                        m_SonoParameters->pIV(BFPNames::ImageZoomCoefStr) / qreal(100))
                      .map(leftTop);

        ProbeParameters p(m_SonoParameters);
        QPoint rightBottom;
        bf.convertPhysicsToLogic(m_SonoParameters->pIV(BFPNames::StopLineStr),
                                 p.bottomDepthMM(m_SonoParameters->pDV(BFPNames::DepthMMStr)), rightBottom);
        rightBottom = Formula::flipAndRectZoomCoefTransform(
                          m_SonoParameters->pBV(BFPNames::UpStr), m_SonoParameters->pBV(BFPNames::LeftStr), imageSize,
                          m_SonoParameters->pIV(BFPNames::ImageZoomCoefStr) / qreal(100))
                          .map(rightBottom);

        if (leftTop.x() > rightBottom.x())
        {
            int lx = leftTop.x();
            leftTop.setX(rightBottom.x());
            rightBottom.setX(lx);
        }

        if (leftTop.y() > rightBottom.y())
        {
            int ly = leftTop.y();
            leftTop.setY(rightBottom.y());
            rightBottom.setY(ly);
        }

        leftTop.setX(leftTop.x() < 0 ? 0 : leftTop.x());
        leftTop.setY(leftTop.y() < 0 ? 0 : leftTop.y());

        int w = rightBottom.x() - leftTop.x();
        int h = rightBottom.y() - leftTop.y();

        w = w > width ? width : w;
        h = h > height ? height : h;

        return QRect(leftTop.x(), leftTop.y(), w, h);
    }
    else
    {
        return QRect(0, 0, width, height);
    }
}

int MeasureContext::waveDataWidth() const
{
    return m_WaveDataWidth;
}

void MeasureContext::setWaveDataWidth(int WaveDataWidth)
{
    m_WaveDataWidth = WaveDataWidth;
}

const QImage& MeasureContext::croppedDImage() const
{
    return m_CroppedDImage;
}

void MeasureContext::setCroppedDImage(const QImage& croppedDImage)
{
    m_CroppedDImage = croppedDImage.copy();
}

int MeasureContext::fMN() const
{
    return m_FMN;
}

void MeasureContext::setFMN(int fmn)
{
    bool isChange = (fmn != m_FMN);
    m_FMN = fmn;

    if (isChange)
    {
        emit fmnChanged();
    }
}

int MeasureContext::getCurLayoutForAutoEF() const
{
    return m_curLayoutForAutoEF;
}

int MeasureContext::Layout()
{
    return m_SonoParameters->pIV(BFPNames::LayoutStr);
}

QByteArray MeasureContext::getWholeFrameData(int frameType, const int index)
{
    if (m_LineBufferManager == nullptr)
    {
        return nullptr;
    }
    return m_LineBufferManager->getWholeFrameData(frameType, index);
}

void MeasureContext::setCurLayoutForAutoEF(int curLayoutForAutoEF)
{
    m_curLayoutForAutoEF = curLayoutForAutoEF;
}

void MeasureContext::setLastLayoutForAutoEF(int lastLayoutForAutoEF)
{
    m_lastLayoutForAutoEF = lastLayoutForAutoEF;
}

void MeasureContext::drawUnActiveLayout()
{
    if (m_LineBufferManager == nullptr)
    {
        return;
    }
    m_LineBufferManager->drawUnActiveRenderPartitionImages();
}

int MeasureContext::oldPartitionChanged()
{
    return m_lastLayoutForAutoEF;
}

int MeasureContext::getFrameIndexByFrameTimestamp(const qint64& timestamp) const
{
    if (m_LineBufferManager == nullptr)
    {
        return -1;
    }
    return m_LineBufferManager->getFrameIndexByFrameTimestamp(timestamp);
}

qint64 MeasureContext::getFramestampByIndex(int frameIndex) const
{
    if (m_LineBufferManager == nullptr)
    {
        return -1;
    }
    return m_LineBufferManager->getFramestampByIndex(frameIndex);
}

int MeasureContext::whloeFrameIndex(const int type, const int typeIndex)
{
    if (m_LineBufferManager == nullptr)
    {
        return -1;
    }
    return m_LineBufferManager->whloeFrameIndex(type, typeIndex);
}

void MeasureContext::onChangeFrameIndex(qint64 index)
{
    emit changeFrameIndex(index);
}

void MeasureContext::onUpdateESVIndex(qint64 index)
{
    emit updateESVIndex(index);
}

void MeasureContext::onUpdateEDVIndex(qint64 index)
{
    emit updateEDVIndex(index);
}

void MeasureContext::slotAutoEFCompleted()
{
    if (m_SonoParameters != nullptr)
    {
        emit AutoEFCompleted();
    }
}

void MeasureContext::slotHasAutoEFResult(QVariant value)
{
    emit onHasAutoEFResultChanged(value);
}

void MeasureContext::clearMeasureContextImage()
{
    if (!m_SonoParameters->pBV(BFPNames::FreezeStr) && m_SonoParameters->isRealTime())
    {
        return;
    }
    m_BImage = QImage();
    m_BTwoImage = QImage();
    m_BThreeImage = QImage();
    m_BFourImage = QImage();
    m_MImage = QImage();
    m_DImage = QImage();
    m_Image = QImage();
    m_RawDataImage = QImage();
    m_RenderImage = QImage();

    emit imageRefreshed();
}

void MeasureContext::onRenderImageChanged(const QPixmap& pix, ModeImageType modeType, int frameIndex)
{
    if (modeType == ModeImageType::ImageB)
    {
        setBImage(pix.toImage());
    }
    else if (modeType == ModeImageType::ImageD)
    {
        setDImage(pix.toImage());
    }

    m_FrameIndex = frameIndex;
}

int MeasureContext::startIndex()
{
    if (m_LineBufferManager == nullptr)
    {
        return -1;
    }
    if (m_SonoParameters)
    {
        return m_LineBufferManager->getFrameIndexByFrameTimestamp(
            m_SonoParameters->pV(BFPNames::AutoEFStartFrameStr).toLongLong());
    }
    return -1;
}

int MeasureContext::endIndex()
{
    if (m_LineBufferManager == nullptr)
    {
        return -1;
    }
    if (m_SonoParameters)
    {
        return m_LineBufferManager->getFrameIndexByFrameTimestamp(
            m_SonoParameters->pV(BFPNames::AutoEFEndFrameStr).toLongLong());
    }
    return -1;
}

qint64 MeasureContext::startTimeStamp()
{
    if (m_LineBufferManager == nullptr)
    {
        return -1;
    }
    if (m_SonoParameters)
    {
        return m_SonoParameters->pV(BFPNames::AutoEFStartFrameStr).toLongLong();
    }
    return -1;
}

qint64 MeasureContext::endTimeStamp()
{
    if (m_LineBufferManager == nullptr)
    {
        return -1;
    }
    if (m_SonoParameters)
    {
        return m_SonoParameters->pV(BFPNames::AutoEFEndFrameStr).toLongLong();
    }
    return -1;
}

int MeasureContext::edFrameIndex()
{
    if (m_SonoParameters)
    {
        return m_SonoParameters->pIV(BFPNames::EDFrameStr);
    }
    return -1;
}

int MeasureContext::esFrameIndex()
{
    if (m_SonoParameters)
    {
        return m_SonoParameters->pIV(BFPNames::ESFrameStr);
    }
    return -1;
}

void MeasureContext::setAutoEFHasResult(bool value)
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::HasAutoEFResultStr, value);
    }
    if (m_BufferStoreManager->curSonoParameters() != nullptr)
    {
        m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::HasAutoEFResultStr, value);
    }
}

bool MeasureContext::hasAutoEFResult()
{
    return m_SonoParameters->pBV(BFPNames::HasAutoEFResultStr);
}

int MeasureContext::calDuration(int& startIndex, int& endIndex, bool& isEnoughFrames)
{
    int frameCount = m_LineBufferManager->frameCount();

    int sumDuration = 0;

    for (int i = frameCount - 1; i >= 0; i--)
    {
        if (sumDuration >= MAXDURATION)
        {
            startIndex = i;
            endIndex = frameCount - 1;
            isEnoughFrames = true;

            return sumDuration;
        }

        sumDuration += m_LineBufferManager->indexDuration(TWOD, i);
    }

    return sumDuration;
}

void MeasureContext::PlayFromCurIndex(int curIndex)
{
    int count = m_LineBufferManager->frameCount();

    if (count >= curIndex)
    {
        m_LineBufferManager->setCurrentIndex(curIndex);
        emit playMovie();
    }
}

void MeasureContext::calTime(int curIndex, int& startIndex, int& endIndex, bool& isEnoughFrames)
{
    if (m_BufferStoreManager->curSonoParameters()->pIV(BFPNames::FPSStr) <= 0)
        return;

    int fps = m_BufferStoreManager->curSonoParameters()->pIV(BFPNames::FPSStr);
    double totalTime = m_LineBufferManager->frameCount() / (fps * 1.0);
    double curIndexPreTime = curIndex / (fps * 1.0);
    //    double curIndexAfterTime = (m_LineBufferManager->frameCount() - curIndex) /
    //    (m_BufferStoreManager->curSonoParameters()->pIV(BFPNames::FPSStr) * 1.0);

    /*
     *首先将当前帧向前2S电影过算法;
     *如果当前帧向前电影不足2S，则从第0帧开始向后2S电影过算法;
     *若所有帧不足2S，则弹窗提示
     */
    isEnoughFrames = false;
    if (curIndexPreTime >= MAXDURATION)
    {
        isEnoughFrames = true;
        startIndex = curIndex - fps * MAXDURATION;
        endIndex = curIndex;
    }
    else if (totalTime >= MAXDURATION)
    {
        isEnoughFrames = true;
        startIndex = 0;
        endIndex = fps * MAXDURATION - 1;
    }
}

void MeasureContext::setAutoEFActiveLayout(int curLayout)
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::AutoEFCurLayoutStr, curLayout);
    }
}

void MeasureContext::setAutoEFIndexs(int edvindex, int esvindex)
{
    if (m_SonoParameters != nullptr)
    {
        qint64 startStamp = m_SonoParameters->pV(BFPNames::AutoEFStartFrameStr).toLongLong();
        qint64 endStamp = m_SonoParameters->pV(BFPNames::AutoEFEndFrameStr).toLongLong();
        int begin = m_LineBufferManager->getFrameIndexByFrameTimestamp(startStamp);
        int end = m_LineBufferManager->getFrameIndexByFrameTimestamp(endStamp);

        int edv = edvindex;
        int esv = esvindex;
        bool isESMax = (esv > edv) ? true : false;

        m_SonoParameters->setPV(BFPNames::EDFrameStampStr, m_LineBufferManager->getFramestampByIndex(edvindex));
        m_SonoParameters->setPV(BFPNames::ESFrameStampStr, m_LineBufferManager->getFramestampByIndex(esvindex));

        m_SonoParameters->parameter(BFPNames::EDFrameStr)->setMin(isESMax ? begin : esv + 1);
        m_SonoParameters->parameter(BFPNames::EDFrameStr)->setMax(isESMax ? esv - 1 : end);
        m_SonoParameters->parameter(BFPNames::ESFrameStr)->setMin(isESMax ? edv + 1 : begin);
        m_SonoParameters->parameter(BFPNames::ESFrameStr)->setMax(isESMax ? end : edv - 1);

        m_SonoParameters->setPV(BFPNames::EDFrameStr, edvindex);
        m_SonoParameters->setPV(BFPNames::ESFrameStr, esvindex);
    }
}

void MeasureContext::setAutoEFEDCurIndex(qint64 edvCurIndex)
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::EDCurIndexStr, edvCurIndex);
    }
}

void MeasureContext::setAutoEFESCurIndex(qint64 esvCurIndex)
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::ESCurIndexStr, esvCurIndex);
    }
}

void MeasureContext::setAutoEFStartEndIndexs(int startindex, int endindex)
{
    qint64 startFrameStamp = m_LineBufferManager->getFramestampByIndex(startindex);
    qint64 endFrameStamp = m_LineBufferManager->getFramestampByIndex(endindex);
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::AutoEFStartFrameStr, startFrameStamp);
        m_SonoParameters->setPV(BFPNames::AutoEFEndFrameStr, endFrameStamp);
    }
}

int MeasureContext::autoEFActiveLayout()
{
    return m_SonoParameters->pIV(BFPNames::AutoEFCurLayoutStr);
}

qint64 MeasureContext::autoEFEDCurIndex()
{
    return m_SonoParameters->pV(BFPNames::EDCurIndexStr).toLongLong();
}

qint64 MeasureContext::autoEFESCurIndex()
{
    return m_SonoParameters->pV(BFPNames::ESCurIndexStr).toLongLong();
}

void MeasureContext::setAutoEFEDFrameIndex(int edvIndex)
{
    m_SonoParameters->setPV(BFPNames::EDFrameStr, edvIndex);
}

void MeasureContext::setAutoEFESFrameIndex(int esvIndex)
{
    m_SonoParameters->setPV(BFPNames::ESFrameStr, esvIndex);
}

int MeasureContext::currentIndex()
{
    return m_LineBufferManager->currentIndex();
}

void MeasureContext::setImageRenderPartition(int partition)
{
    // partition: 0, 默认; 1, EDV图像区（左侧）; 2, ESV图像区（右侧）

    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::ImageRenderPartitionStr, partition);
    }
}

void MeasureContext::setBCImageOn(bool value)
{
    if (m_SonoParameters != nullptr)
    {
        m_SonoParameters->setPV(BFPNames::BCImagesOnStr, value);
    }
    if (m_BufferStoreManager->curSonoParameters() != nullptr)
    {
        m_BufferStoreManager->curSonoParameters()->setPV(BFPNames::BCImagesOnStr, value);
    }
}

void MeasureContext::ConnectSignals(bool connect)
{
    Util::connectSignal(m_SonoParameters->parameter(BFPNames::HasAutoEFResultStr), SIGNAL(valueChanged(QVariant)), this,
                        SLOT(slotHasAutoEFResult(QVariant)), connect, Qt::UniqueConnection);
    Util::connectSignal(m_SonoParameters->parameter(BFPNames::ImageRenderPartitionStr), SIGNAL(valueChanged(QVariant)),
                        this, SIGNAL(onImageRenderPartitionChanged(QVariant)), connect, Qt::UniqueConnection);
    Util::connectSignal(m_SonoParameters->parameter(BFPNames::AutoEFCurLayoutStr), SIGNAL(valueChanged(QVariant)), this,
                        SIGNAL(onActivePartitionChanged(QVariant)), connect, Qt::UniqueConnection);
    Util::connectSignal(m_SonoParameters->parameter(BFPNames::EDFrameStr), SIGNAL(valueChanged(QVariant)), this,
                        SLOT(slotOnEDFrameChanged(QVariant)), connect, Qt::UniqueConnection);
    Util::connectSignal(m_SonoParameters->parameter(BFPNames::ESFrameStr), SIGNAL(valueChanged(QVariant)), this,
                        SLOT(slotOnESFrameChanged(QVariant)), connect, Qt::UniqueConnection);
    Util::connectSignal(m_SonoParameters->parameter(BFPNames::EDCurIndexStr), SIGNAL(valueChanged(QVariant)), this,
                        SLOT(slotOnEDCurIndexChanged(QVariant)), connect, Qt::UniqueConnection);
    Util::connectSignal(m_SonoParameters->parameter(BFPNames::ESCurIndexStr), SIGNAL(valueChanged(QVariant)), this,
                        SLOT(slotOnESCurIndexChanged(QVariant)), connect, Qt::UniqueConnection);
    Util::connectSignal(m_SonoParameters->parameter(BFPNames::AutoEFOnStr), SIGNAL(valueChanged(QVariant)), this,
                        SIGNAL(onAutoEFOnChanged(QVariant)), connect, Qt::UniqueConnection);
}
