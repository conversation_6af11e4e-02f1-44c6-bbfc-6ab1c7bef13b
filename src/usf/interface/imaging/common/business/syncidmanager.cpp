#include "syncidmanager.h"
#include <QApplication>
#include <QThread>
#include <QMutexLocker>
#include <QDebug>
#include "bfpnames.h"
#include "zeusparameternames.h"
using namespace ZeusParameterNames;
SyncIDManager* SyncIDManager::m_Instance = nullptr;
SyncIDManager::SyncIDManager(QObject* parent)
    : QObject(parent)
    , m_SonoParameters(nullptr)
    , m_CurrentPrePostSyncId(0)
{
}

SyncIDManager& SyncIDManager::instance()
{
    if (m_Instance == nullptr)
    {
        m_Instance = new SyncIDManager();
        if (m_Instance->thread() != QApplication::instance()->thread())
        {
            m_Instance->moveToThread(QApplication::instance()->thread());
        }
    }
    return *m_Instance;
}

unsigned char SyncIDManager::currentSyncId(SyncIDMode syncIDMode) const
{
    QMutexLocker locker(&m_Mutex);
    if (syncIDMode == SyncIDMode::ControlTableSyncID)
    {
        return static_cast<unsigned char>(
            m_SonoParameters->parameter(BFPNames::DataSyncIDStr)->controlTableValueSended());
    }
    else if (syncIDMode == SyncIDMode::PrePostSyncId)
    {
        return m_CurrentPrePostSyncId;
    }
    else
    {
        return 0;
    }
}

void SyncIDManager::updateSyncId(const SyncIDMode syncIDMode)
{
    QMutexLocker locker(&m_Mutex);
    if (syncIDMode == SyncIDMode::PrePostSyncId)
    {
        //        qCritical() << "zjj-"
        //                    << "zeusName=" << zeusName;
        ++m_CurrentPrePostSyncId;
    }
    else if (syncIDMode == SyncIDMode::ControlTableSyncID)
    {
        //目前方案的控制表SyncID不在此更新
    }
}

void SyncIDManager::setSonoParameters(SonoParameters* sonoParameters)
{
    m_SonoParameters = sonoParameters;
}
