add_definitions(-DUSF_INTERFACE_IMAGING_COMMON_BUSINESS_LIBRARY)

if(WIN32)
    add_definitions(-DNOMINMAX)
endif()

include_depends(usf.com.core.base usf.com.core.utilitymodel usf.i.peri.bfiodevice usf.i.preset.bs usf.i.peri.printer usf.com.core.dev)

if(${USE_QT_AUDIO})
    include_depends(usf.i.peri.media)
endif()

include_directories( beamformer
                     beamformer/relatedparas
                     beamformer/datasender
                     beamformer/fpgaupgrade
                     beamformer/calculator
                     beamformer/geometrycontroller
					 beamformer/linedensity
					 beamformer/scanmodeupdate
					 beamformer/bffactory
					 beamformer/colormap
					 beamformer/probe
                     ${CMAKE_CURRENT_SOURCE_DIR}
                     ${LAYOUTCALCULATOR_INCLUDE_DIRS}
                     ${AJECPLDUPDATE_INCLUDE_DIRS}
                     ${GENERATEDATA_INCLUDE_DIRS}
                     #${ULTRASOUNDDEVICE_INCLUDE_DIRS}
                     )

aux_source_directory(beamformer/calculator DIR_CALCULAROR_SRCS)
aux_source_directory(beamformer/geometrycontroller DIR_GEOMETRYCONTROLLER_SRCS)

target_files(${USE_4D} beamformer/relatedparas/fourdparascontainer.cpp beamformer/relatedparas/fourdsettingparas.cpp beamformer/relatedparas/fourdparasinfo.h)
set(FOURD_RELATED_CPPS ${TARGET_FILE})

include_directories(colormap probe buffer elementtest controltable stressecho stressecho/analyzemodel)

set(colormap
    colormap/icolormapalgorithm.cpp
    colormap/icolormapcalculator.cpp
    colormap/icolormapcalculatormodel.cpp
    colormap/icolormapmanager.cpp
)

set(probe
    probe/iprobeselectionmodel.cpp
)

set(buffer
    buffer/ilinebuffermanager.cpp
    buffer/bufferunit.cpp
    buffer/frameinfo.cpp
    buffer/ibufferstorer.cpp
)

set(elementtest
    elementtest/ielementtestcontroller.cpp
    elementtest/ielementdetectalgorithom.cpp
)

set(controltable
    controltable/controltablesender.cpp
)

set(linedensity
    beamformer/linedensity/datablockcalculatorutil.cpp
    beamformer/linedensity/datablockcalculator.cpp
    beamformer/linedensity/multibeamcalculator.cpp
)

set(dataacquisition
    beamformer/dataacquisition/dataacquisition.cpp
    )

#if(APPLE)
if(USE_TARGET_PALM)
    set(APPLE_FILES 
    beamformer/fpgaupgrade/fpgaupgrademodel_palm.cpp 
    )
endif(USE_TARGET_PALM)
#endif(APPLE)

set(COLORMAP_SRC
    beamformer/colormap/baselinealgorithm.cpp
    beamformer/colormap/colormapcalculator.cpp
    beamformer/colormap/colormapcalculatorset.cpp
    beamformer/colormap/contrastalgorithm.cpp
    beamformer/colormap/gammacorrectionalgorithm.cpp
    beamformer/colormap/invertalgorithm.cpp
    beamformer/colormap/mappingalgorithm.cpp
    beamformer/colormap/newtoniinterpolationalgorithm.cpp
    beamformer/colormap/rejectionalgorithm.cpp
    beamformer/colormap/brightnessalgorithm.cpp
    beamformer/colormap/colormapcalculatormodel.cpp
    beamformer/colormap/nobaselineinvertalgorithm.cpp
    beamformer/colormap/gainalgorithm.cpp
    beamformer/colormap/colorbalancecorrection.cpp
  )

set(scanmodeupdate
    beamformer/scanmodeupdate/basescanmodeupdater.cpp
    beamformer/scanmodeupdate/bscanmodeupdater.cpp
    beamformer/scanmodeupdate/bcscanmodeupdater.cpp
    beamformer/scanmodeupdate/bcdscanmodeupdater.cpp
    beamformer/scanmodeupdate/bmscanmodeupdater.cpp
    beamformer/scanmodeupdate/bmprescanmodeupdater.cpp
    beamformer/scanmodeupdate/bcmscanmodeupdater.cpp
    beamformer/scanmodeupdate/bcmprescanmodeupdater.cpp
    beamformer/scanmodeupdate/scanmodeupdaterfactory.cpp
    beamformer/scanmodeupdate/basedscanmodeupdater.cpp
    beamformer/scanmodeupdate/bdscanmodeupdater.cpp
    beamformer/scanmodeupdate/btdidscanmodeupdater.cpp
    beamformer/scanmodeupdate/bpdscanmodeupdater.cpp
    beamformer/scanmodeupdate/bcdprescanmodeupdater.cpp
    beamformer/scanmodeupdate/sononeedlescanmodeupdater.cpp
    beamformer/scanmodeupdate/bpddprescanmodeupdater.cpp
    beamformer/scanmodeupdate/bpdcwprescanmodeupdater.cpp
    beamformer/scanmodeupdate/btviscanmodeupdater.cpp
    beamformer/scanmodeupdate/escanmodeupdater.cpp
    beamformer/scanmodeupdate/twobscanmodeupdater.cpp
    beamformer/scanmodeupdate/fourbscanmodeupdater.cpp
    beamformer/scanmodeupdate/stressechomodeupdater.cpp
    beamformer/scanmodeupdate/mscanmodeupdater.cpp
    beamformer/scanmodeupdate/cpscanmodeupdater.cpp
    beamformer/scanmodeupdate/bdprescanmodeupdater.cpp
    beamformer/scanmodeupdate/bccwprescanmodeupdater.cpp
    beamformer/scanmodeupdate/bcwprescanmodeupdater.cpp
	beamformer/scanmodeupdate/bccwscanmodeupdater.cpp
	beamformer/scanmodeupdate/bcwscanmodeupdater.cpp
	beamformer/scanmodeupdate/bdpdcwscanmodeupdater.cpp
    beamformer/scanmodeupdate/bdpddscanmodeupdater.cpp
    beamformer/scanmodeupdate/bmvidscanmodeupdater.cpp
    beamformer/scanmodeupdate/bpdcwscanmodeupdater.cpp
    beamformer/scanmodeupdate/bpddscanmodeupdater.cpp
    beamformer/scanmodeupdate/freemscanmodeupdater.cpp
)

set(bffactory
    beamformer/bffactory/ibeamformerfactory.cpp
    beamformer/bffactory/beamformerfactorycreator.cpp
    beamformer/bffactory/beamformerapplefactory.cpp
    beamformer/bffactory/beamformersonoportfactory.cpp
    beamformer/bffactory/beamformergrapefactory.cpp
    beamformer/bffactory/beamformeratomfactory.cpp
    beamformer/bffactory/beamformereco1factory.cpp
    beamformer/bffactory/beamformereco3factory.cpp
    beamformer/bffactory/beamformereco5factory.cpp
    beamformer/bffactory/beamformereco6factory.cpp
    beamformer/bffactory/beamformereco60factory.cpp
    beamformer/bffactory/beamformerphoenixfactory.cpp
    beamformer/bffactory/beamformerp9factory.cpp
    beamformer/bffactory/beamformerxbitfactory.cpp
    beamformer/bffactory/beamformerqbitfactory.cpp
    beamformer/bffactory/beamformersonobook6factory.cpp
    beamformer/bffactory/beamformersonobook9factory.cpp
    beamformer/bffactory/beamformertigerfactory.cpp
    beamformer/bffactory/beamformersonomaxfactory.cpp
    beamformer/bffactory/beamformernewecofactory.cpp
)

set(postprocess
    beamformer/postprocess/postprocesshandlerbase.cpp
    beamformer/postprocess/postprocesshandlersonobook9.cpp
    beamformer/postprocess/postprocesshandlerapple.cpp
    beamformer/postprocess/postprocesshandlerlinedensity.cpp
    beamformer/postprocess/postprocesshandlerxbit.cpp
)

set(probe_src
    beamformer/probe/probedataset.cpp 
    beamformer/probe/probedatasetstaticrepository.cpp
    beamformer/probe/probeblockdataset.cpp 
    beamformer/probe/probeblockdatagroups.cpp 
    beamformer/probe/blockdatagroup.cpp 
    beamformer/probe/blockdata.cpp
    beamformer/probe/mitidata.cpp
    beamformer/probe/mitidatagroup.cpp
    beamformer/probe/probemitidatagroups.cpp
    beamformer/probe/probemitidataset.cpp
    beamformer/probe/focussetting.cpp
    beamformer/probe/focusgaincompensation.cpp
    beamformer/probe/appara.cpp
    beamformer/probe/apconfig.cpp
    beamformer/probe/apconfiggroup.cpp
    beamformer/probe/aprange.cpp
    beamformer/probe/aprangesettings.cpp
    beamformer/probe/fpspara.cpp
    beamformer/probe/fpsparagroup.cpp
    beamformer/probe/cqyzstepconfig.cpp
    beamformer/probe/probedepthlistconfig.cpp
    beamformer/probe/needleparas.cpp
    beamformer/probe/needlesetting.cpp
    beamformer/probe/needlesettinggroup.cpp
    beamformer/probe/needlesettingset.cpp
    beamformer/probe/probedatasetfacade.cpp
)

add_library_qt(usf.i.imaging.com.bs
    zeusparameternames.cpp
    iimagemanager.cpp
    measurecontext.cpp
    icinelooper.cpp
	iimagesavehelper.cpp
	algorithmparametercalculator.cpp
	iprobepresetmodel.cpp
    iimageinterfaceforexam.cpp
    memoryleakcheck.h
    izeustool.cpp
    bfhwinfoformula.cpp
    syncidmanager.cpp
    ${common}
    ${colormap}
    ${probe}
    ${buffer}
    ${elementtest}
    ${controltable}
    ${postprocess}
    beamformer/abstractbfdatahandler.cpp
    beamformer/basebfstaticparameters.cpp
    beamformer/baseprobegeometriestransform.cpp
    beamformer/beamformerbase.cpp
    beamformer/beamformereco3.cpp
    beamformer/beamformerqbit.cpp
    beamformer/beamformerebit.cpp
    beamformer/beamformereco1.cpp
    beamformer/beamformereco5.cpp
    beamformer/beamformertiger.cpp
    beamformer/beamformersonoeye.cpp
    beamformer/beamformerapple.cpp
    beamformer/beamformersonoport.cpp
    beamformer/beamformergrape.cpp
    beamformer/beamformerneweco.cpp
    beamformer/beamformersonobook9.cpp
    beamformer/beamformerlinedensity.cpp
    beamformer/beamformerphoenix.cpp
    beamformer/beamformerxbit.cpp
    beamformer/beamformeratom.cpp
    beamformer/linedensityparametershandler.cpp
    beamformer/beamformersonomax.cpp
    beamformer/bfcoordtransform.cpp
    beamformer/bfdatahandlerorange.cpp
    beamformer/bfdatahandlertiger.cpp
    beamformer/bfdatahandlersonoeye.cpp
    beamformer/bfdatahandlerapple.cpp
    beamformer/bfdatahandlersonobook9.cpp
    beamformer/bfdatahandlerphoenix.cpp
    beamformer/bfsonobook9kitfactory.cpp
    beamformer/bfphoenixkitfactory.cpp
    beamformer/bfxbitkitfactory.cpp
    beamformer/bfatomkitfactory.cpp
    beamformer/focusposarrayfactory.cpp
    beamformer/bforangekitfactory.cpp
    beamformer/bfrawdata2showdatabase.cpp
    beamformer/bfrawdata2showdatadirectmap.cpp
    beamformer/bftgcparameters.cpp
    beamformer/bftigerkitfactory.cpp
    beamformer/colorbarmodel.cpp
    beamformer/colormapassembly.cpp
    ${COLORMAP_SRC}
    beamformer/flagtimer.cpp
    beamformer/ibeamformer.cpp
    beamformer/ibeamformerinitialization.cpp
    beamformer/ibeamformerkitfactory.cpp
    beamformer/ibfdisplayinfo.cpp
    beamformer/ibfimagestate.cpp
    beamformer/ibfkitfactory.cpp
    beamformer/ibfparameters.cpp
    beamformer/ibfrawdata2imagedata.cpp
    beamformer/ibfrawdata2showdata.cpp
    beamformer/ifpgainfo.cpp
    beamformer/imagerender.cpp
    beamformer/iprobeoperator.cpp
    beamformer/physicalgeometryandpointtransform.cpp
    beamformer/physicalpointingeometrytransform.cpp
    beamformer/physicalgeometrytransform.cpp
    beamformer/physicallinetransform.cpp
    beamformer/physicalpointtransform.cpp
    beamformer/probeparameters.cpp
    beamformer/probephysicalgeometry.cpp
    beamformer/probephysicalline.cpp
    beamformer/probephysicalpoint.cpp
    beamformer/probephysicalsize.cpp
    beamformer/probepresetmodel.cpp
    beamformer/probescaner.cpp
    beamformer/probescaners.cpp
    beamformer/probebuttonscaner.cpp
    beamformer/probesswitchcontroller.cpp
    beamformer/directprobesswitchcontroller.cpp
    beamformer/probeidentifydataobserver.cpp
    beamformer/twophysicalgeometrytransform.cpp
    beamformer/unstableparatimer.cpp
    beamformer/probeimageregion.cpp
    beamformer/aiocontroller.cpp
    beamformer/ibeamformertool.cpp
    beamformer/minzoomedcqyz.cpp
    beamformer/bfstaticparameterseco1.cpp
    beamformer/bfstaticparameterseco.cpp
    beamformer/bfstaticparametersebit.cpp
    beamformer/bfadfreqparameter.cpp
    beamformer/relatedparas/freqsetting.cpp 
    beamformer/relatedparas/freqrelatedparas.cpp 
    beamformer/relatedparas/relatedparascontroller.cpp 
    beamformer/relatedparas/leesettings.cpp 
    beamformer/relatedparas/afsettings.cpp 
    beamformer/relatedparas/chipfactory.cpp
    beamformer/relatedparas/ichip.cpp
    beamformer/relatedparas/chipafe5832.cpp
    beamformer/relatedparas/chipafe5848.cpp
    beamformer/relatedparas/chipafe5828.cpp
    beamformer/relatedparas/paralistsettings.cpp
    beamformer/relatedparas/edgesettings.cpp
    beamformer/relatedparas/irelatedparascontroller.cpp 
    beamformer/relatedparas/emptyrelatedparascontroller.cpp
    beamformer/relatedparas/iimagesettings.cpp
    beamformer/relatedparas/smoothsettings.cpp
    beamformer/datasender/baseblockdatasender.cpp
    beamformer/datasender/iblockdatasender.cpp
    beamformer/datasender/iblockdataparanameconverter.cpp
    beamformer/datasender/baseblockdataparanameconverter.cpp
    beamformer/datasender/blockdataparanameconvertersonobook9.cpp
    beamformer/datasender/blockdataparanameconverterphoenix.cpp
    beamformer/iareasource.cpp
    beamformer/arearesult.cpp
    beamformer/commonareasource.cpp
    beamformer/relatedparas/cafsettings.cpp
    beamformer/relatedparas/clfsettings.cpp
    beamformer/relatedparas/plfirsettings.cpp
    beamformer/relatedparas/pafirsettings.cpp
    beamformer/relatedparas/colorcoefsettings.cpp
    beamformer/relatedparas/basefreqsetting.cpp
    beamformer/relatedparas/colorfreqsetting.cpp
    beamformer/relatedparas/dopfreqsetting.cpp
    beamformer/relatedparas/dopfreqsetting.h
    beamformer/relatedparas/cwfreqsetting.cpp
    beamformer/relatedparas/freqparascontainer.cpp
    beamformer/relatedparas/fgcsettingmodel.cpp
    beamformer/relatedparas/elastofreqsetting.cpp
    beamformer/newtriplexmodehelper.cpp
    beamformer/fpgaupgrade/fpgaupgradecontrol.cpp
    beamformer/fpgaupgrade/fpgaupgrademodel.cpp
    beamformer/fpgaupgrade/fpgaupgrademodelhelper.cpp
    beamformer/fpgaupgrade/fpgaupfactory.cpp
    beamformer/fpgaupgrade/fpgaupdatebase.cpp
    ${APPLE_FILES}
    beamformer/relatedparas/focusparasmodel.cpp
    beamformer/relatedparas/focusescombinemodel.cpp
    beamformer/relatedparas/focusescombinesetting.cpp
    beamformer/bfdatahandlerqbit.cpp
    beamformer/bfdatahandlerebit.cpp
    beamformer/bfdatahandlerxbit.cpp
    beamformer/bfdatahandleratom.cpp
    beamformer/bfqbitkitfactory.cpp
    beamformer/bfebitkitfactory.cpp
    beamformer/bf8bitrawdata2showdatadirectmap.cpp
    beamformer/probedataloader.cpp
    beamformer/ivideocontrol.cpp
    beamformer/videocontroleco.cpp
    beamformer/videocontrolhandler.cpp
    beamformer/videocontrolqbit.cpp
    beamformer/videomodelqbit.cpp
    beamformer/bfstaticparametersqbit.cpp
    beamformer/bfstaticparameterssonobook6.cpp
    beamformer/bfstaticparameterssonobook9.cpp
    beamformer/bfstaticparametersphoenix.cpp
    beamformer/bfstaticparametersp9.cpp
    beamformer/bfstaticparameterssonoeye.cpp
    beamformer/bfstaticparametersapple.cpp
    beamformer/bfstaticparametersxbit.cpp
    beamformer/bfstaticparametersatom.cpp
    beamformer/bfstaticparametersgrape.cpp
    beamformer/beamformereco6.cpp
    beamformer/bfeco6kitfactory.cpp
    beamformer/hprfcontroller.cpp
    beamformer/beamformersonobook6.cpp
    beamformer/bfsonoeyekitfactory.cpp
    beamformer/bfapplekitfactory.cpp
    beamformer/bfsonobookkitfactory.cpp
    beamformer/bfdatahandlersonobook.cpp
    beamformer/physicalfreelinetransform.cpp
    beamformer/probephysicalfreeline.cpp
    beamformer/freemdatagenerator.cpp
    beamformer/layoutinfoloader.cpp
    beamformer/imagelayoutorganizer.cpp
    beamformer/layoutrectcalculator.cpp
    ${FOURD_RELATED_CPPS}
    ${DIR_CALCULAROR_SRCS}
    ${DIR_GEOMETRYCONTROLLER_SRCS}
    beamformer/colormapassemblyhelper.cpp
    beamformer/postparameterhelper.cpp
    beamformer/calculator/bfscanareawidthparameter.cpp
    beamformer/displaystyleinfoloader.cpp
    beamformer/displaystyleinfoloader.h
    beamformer/gridlayoutanalyzer.cpp
    beamformer/gridlayoutanalyzer.h
    beamformer/gridrenderlayoutorganizer.cpp
    beamformer/gridrenderlayoutorganizer.h
    beamformer/layoutxmlanalyzer.cpp
    beamformer/layoutxmlanalyzer.h
    beamformer/renderlayoutconfigureloader.cpp
    beamformer/renderlayoutconfigureloader.h
    beamformer/renderlayoutdesigner.cpp
    beamformer/renderlayoutdesigner.h
    beamformer/templaterenderlayoutpool.cpp
    beamformer/templaterenderlayoutpool.h
    beamformer/templaterenderlayoutpools.cpp
    beamformer/templaterenderlayoutpools.h
    beamformer/calculator/cqyzprefervaluecalculator.cpp
    beamformer/calculator/teeprobecalculator.cpp
    beamformer/virtualprobecodemodel.cpp
    beamformer/lineiimageparameterhandle.cpp
    beamformer/calculator/autofocusadjuster.cpp
    beamformer/probeinfomodel.cpp
    beamformer/iprobeinfomodel.cpp
    ${linedensity}
    ${dataacquisition}
    ${scanmodeupdate}
    ${bffactory}
    beamformer/colormapalgorithmcreator.cpp
    beamformer/colormapmanager.cpp
    ${probe_src}
)

target_link_libraries(usf.i.imaging.com.bs
					   usf.com.core.base
	                   usf.i.peri.bfiodevice
                       usf.com.core.utilitymodel
					   usf.i.preset.bs
					   usf.i.peri.printer
                                           usf.com.core.dev
                       ${LAYOUTCALCULATOR_LIBRARIES}
                       ${AJECPLDUPDATE_LIBRARIES}
                       ${GENERATEDATA_LIBRARIES}
                       #${ULTRASOUNDDEVICE_LIBRARIES}
					   )
					   
if(${USE_QT_AUDIO})
target_link_libraries( usf.i.imaging.com.bs usf.i.peri.media)
endif()

add_custom_command(TARGET usf.i.imaging.com.bs
    POST_BUILD
    COMMAND cmake -DsourceDirector=${LAYOUTCALCULATOR_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
    )

if(${USE_AJECPLDUPDATE})
    add_custom_command(TARGET usf.i.imaging.com.bs
        POST_BUILD
        COMMAND cmake -DsourceDirector=${AJECPLDUPDATE_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif()

if(${USE_XBF_GenerateData})
    add_custom_command(TARGET usf.i.imaging.com.bs
        POST_BUILD
        COMMAND cmake -DsourceDirector=${GENERATEDATA_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_XBF_GenerateData})

if(${USE_USCONTROLENGINE})
    target_include_directories(usf.i.imaging.com.bs PRIVATE ${USCONTROLENGINE_INCLUDE_DIRS})
    target_link_libraries(usf.i.imaging.com.bs ${USCONTROLENGINE_LIBRARIES})
    add_custom_command(TARGET usf.i.imaging.com.bs
           POST_BUILD
           COMMAND cmake -DsourceDirector=${USCONTROLENGINE_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
           )
endif(${USE_USCONTROLENGINE})
