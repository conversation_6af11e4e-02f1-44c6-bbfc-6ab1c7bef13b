#ifndef ABSTRACTDSCPIPELINE_H
#define ABSTRACTDSCPIPELINE_H

#include "zeusadapter_global.h"
#include "chisonultrasoundtypes.h"
#include <QObject>
#include <QHash>
#include <QMutex>
#include <QWaitCondition>
class SonoParameters;
class IBufferPushControler;
class AbstractDSCImageTile;
class LineImageArgs;
#ifdef USE_4D
class FourDVolumeLineImageArgs;
#endif
class SimpleMultiCallTrigger;

/**
 * @brief The AbstractDSCPipeline class
 *
 * 有Gstreamer, inviwo, zeus三个实现
 */
class USF_INTERFACE_IMAGING_ZEUSADAPTER_EXPORT AbstractDSCPipeline : public QObject
{
    Q_OBJECT
public:
    explicit AbstractDSCPipeline(
        QList<IBufferPushControler*> iBufferPushControler,
        ChisonUltrasound::ChisonUltrasoundMode type = ChisonUltrasound::ChisonUltrasound_RealTime,
        QObject* parent = NULL);
    ~AbstractDSCPipeline();
    /**
     * call from dsc thread
     */
    Q_INVOKABLE void start();
    Q_INVOKABLE void stop();

    void waitStartedComplete();

    void setSonoParameters(SonoParameters* sonoParameters);

    void haltProcessImageChanged(const int layoutIndex, const int frozenIndex);
    void recoveryProcessImageChanged(const int layoutIndex);

    void clearFrozenIndex(const int layoutIndex);

    bool isDSC2DImagePushed(const int layoutIndex) const;

    virtual void setColorMap(uchar* image2DMap, uchar* waveMap);

    virtual void onEntryStressEchoAnalyze();

    virtual void onExitStressEchoAnalyze();

    void clearThreadData();

    void setEnforceEnable(bool state);

public slots:
    /**
     * 接收ImageTile发出来的数据
     */
    virtual void onNewImage(LineImageArgs* lineImageArgs);

    virtual void onNewMeasureImage(LineImageArgs* lineImageArgs);
    /**
     * BeamFormer模块可以通过这两个函数通知内部当前图形数据处于稳定或者不稳定的状态
     * */
    virtual void onImageShapeStable();

    virtual void onImageShapeUnstable();

    virtual void onPlayCineStatusChanged(int layoutIndex, bool playCineStatus);

    void onColorMapChanged(const int systemScanMode, uchar* image2DMap = NULL, uchar* waveMap = NULL,
                           const int layoutIndex = -1);

private slots:
    void onBeforeActiveBChanged(const QVariant& oldValue, QVariant& newValue);
    void onRequestFlush(const QVariant& value);

signals:
    /**
     * @brief rawDataInfo 优化之前的数据
     */
    void rawDataInfo(void* data, int width, int height, int bitCount, int layoutIndex);
    /**
     * 模块的数据出口，所有的数据经由此处出去，可以通过这个信号，
     * 也可以重新包装成回调函数
     * */
    void newImage(LineImageArgs* lineImageArgs);

    void newMeasureImage(LineImageArgs* lineImageArgs);

    void newGrayImage(LineImageArgs* lineImageArgs);

    void beforePipelineStop(int imageTileIndex);

    void pipelineCreated(int imageTileIndex);

    void stopRequested();

    void requestFlush();

    void fpsChanged(const QString& name, const float fps);

    void datainfo(void* data, int width, int height, int imageType, int frameSteerType);

    void imageDataReady();

#ifdef USE_4D
    void newFourDImage(FourDVolumeLineImageArgs* volumeImageArgs);
#endif

    void flushedAllImages();

protected:
    virtual void onStart() = 0;
    virtual void onStop() = 0;

    virtual void onSetSonoParameters() = 0;

protected:
    QList<IBufferPushControler*> m_BufferPushControler;
    SonoParameters* m_SonoParameters;
    QHash<IBufferPushControler*, AbstractDSCImageTile*> m_ImageTiles;
    ChisonUltrasound::ChisonUltrasoundMode m_ChisonUltrasoundMode;
    uchar* m_Image2DMap;
    uchar* m_WaveMap;
    int m_ActiveBIndex;
    SimpleMultiCallTrigger* m_SimpleMultiCallTrigger;

    bool m_StartReached;
    QMutex m_StartMutex;
    QWaitCondition m_StartCondition;
};

#endif // ABSTRACTDSCPIPELINE_H
