#include "zeusapihelper.h"
#include "isonobuffer.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include <QSize>
#include "usimageprocess.h"
#include "zeusparameternames.h"
#include "resource.h"
#include "bytebuffer.h"
#include "infostruct.h"
#include "syncidmanager.h"
#if defined(SYS_WINDOWS) && defined(USE_RDMA)
#include "gdrmap.h"
#endif
#include <QSet>
#include <QFile>
#include <QDebug>

using namespace ZeusParameterNames;

//#define SAVE_ZEUS_DATABLOCK
QStringList ZeusAPIInterface::EnForceParameterNames = QStringList();

ZeusAPIInterface::ZeusAPIInterface(QList<ISonoBuffer*> iSonoBuffers, bool IsSupportPreProcess)
    : m_SonoParameters(nullptr)
    , m_SonoBufferList(iSonoBuffers)
    , m_ZeusCudaAddr(nullptr)
    , m_IsSupportPreProcess(IsSupportPreProcess)
{
    if (EnForceParameterNames.isEmpty())
    {
        EnForceParameterNames << POSTPARANAME(ProcessSonoParameterIndex::VerticalFlip)
                              << POSTPARANAME(ProcessSonoParameterIndex::HorizontalFlip)
                              << POSTPARANAME(ProcessSonoParameterIndex::PWInvert)
                              << POSTPARANAME(ProcessSonoParameterIndex::BaseLine)
                              << POSTPARANAME(ProcessSonoParameterIndex::DynamicRange)
                              << POSTPARANAME(ProcessSonoParameterIndex::RotationAngle)
                              << POSTPARANAME(ProcessSonoParameterIndex::BMGainValueAfterFreeze)
                              << POSTPARANAME(ProcessSonoParameterIndex::BMDRAfterFreeze)
                              << POSTPARANAME(ProcessSonoParameterIndex::PreMGainValueAfterFreeze)
                              << POSTPARANAME(ProcessSonoParameterIndex::PreMDynamicRangeAfterFreeze)
                              << POSTPARANAME(ProcessSonoParameterIndex::ColorRawDataCallbackEnable)
                              << POSTPARANAME(ProcessSonoParameterIndex::ColorMap2D)
                              << POSTPARANAME(ProcessSonoParameterIndex::ColorMapWave);
        //                              << PARANAME(ProcessSonoParameterIndex::Zoom);

        for (int i = 0; i < TGC_COUNT; ++i)
        {
            EnForceParameterNames << POSTPARANAMEARG(ProcessSonoParameterIndex::BMDTGCAfterFreeze_Gain$).arg(i);
        }

        for (int i = 0; i < LGC_COUNT; ++i)
        {
            EnForceParameterNames << POSTPARANAMEARG(ProcessSonoParameterIndex::BMHTGC_AfterFreezeValue$).arg(i);
        }
    }
}

ZeusAPIInterface::~ZeusAPIInterface()
{
    foreach (HandleInfo info, m_ZeusHanles.values())
    {
        ::ZeusRelease(info.handle);
    }
}

// QVariant ZeusAPIInterface::lastValue(int bufferIndex, QString paraName) const
//{
//    if (isHandleValid(bufferIndex) && m_ZeusValueLists[bufferIndex].contains(paraName))
//    {
//        const ZeusHashValue& para = m_ZeusValueLists[bufferIndex].value(paraName);
//        return para.m_Value;
//    }
//    return 0;
//}

// void ZeusAPIInterface::setLastValue(int bufferIndex, const ZeusHashValue& value)
//{
//    if (!isHandleValid(bufferIndex))
//    {
//        return;
//    }

//    m_ZeusValueLists[bufferIndex][value.m_Name] = value;
//}

void ZeusAPIInterface::setSonoParameters(SonoParameters* sonoParameters)
{
    if (sonoParameters != m_SonoParameters)
    {
        m_SonoParameters = sonoParameters;
        createHandle();
    }
}

bool ZeusAPIInterface::setZeusParameter(int bufferIndex, const QList<ZeusHashValue>& zeusValues)
{
    if (!isHandleValid(bufferIndex))
    {
        return false;
    }

    foreach (const ZeusHashValue& value, zeusValues)
    {
        setZeusParameter(bufferIndex, value.m_Name, value.m_Value, value.m_Type, value.m_Size);
    }
    return true;
}

bool ZeusAPIInterface::setZeusParameter(int bufferIndex, const QString& zeusName, const QVariant value, const int type,
                                        const int size)
{
    if (!isHandleValid(bufferIndex))
    {
        return false;
    }

    switch (type)
    {
    case Dsc_Int:
        setZeusParameterInt(bufferIndex, zeusName, value.toInt());
        break;
    case Dsc_Double:
        setZeusParameterFloat(bufferIndex, zeusName, value.toDouble());
        break;
    case Dsc_Bool:
        setZeusParameterBool(bufferIndex, zeusName, value.toBool());
        break;
    case Dsc_Array:
        setZeusParameterPointer(bufferIndex, zeusName, value.toByteArray().data(), value.toByteArray().size());
        break;
    case Dsc_Pointer:
        setZeusParameterPointer(bufferIndex, zeusName, (void*)value.toULongLong(), size);
        break;
    default:
        break;
    }
#ifdef SAVE_ZEUS_DATABLOCK
    if (Dsc_Pointer == type)
    {
        QFile file(QString("log/%1_datablock").arg(zeusName));
        if (file.open(QIODevice::WriteOnly))
        {
            file.write((const char*)value.toULongLong(), size);
            file.close();
        }
    }
#endif
    return true;
}

void ZeusAPIInterface::setZeusParameterInt(int bufferIndex, const QString& zeusName, const int value)
{
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
    qCritical() << PRETTY_FUNCTION << zeusName << value /* << "m_ImageTileIndex:" << m_ImageTileIndex*/;
#endif
    ::ZeusSetParameterInt(m_ZeusHanles[bufferIndex].handle, zeusName.toStdString().c_str(), value);
    // setLastValue(bufferIndex, ZeusHashValue(zeusName, value, Dsc_Int, 0));
}

void ZeusAPIInterface::setZeusParameterFloat(int bufferIndex, const QString& zeusName, const float value)
{
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION << zeusName << value << "m_ImageTileIndex:" << m_ImageTileIndex;
#endif
    ::ZeusSetParameterFloat(m_ZeusHanles[bufferIndex].handle, zeusName.toStdString().c_str(), value);
    // setLastValue(bufferIndex, ZeusHashValue(zeusName, value, Dsc_Double, 0));
}

void ZeusAPIInterface::setZeusParameterBool(int bufferIndex, const QString& zeusName, const bool value)
{
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION << zeusName << value << "m_ImageTileIndex:" << m_ImageTileIndex;
#endif
    ::ZeusSetParameterBool(m_ZeusHanles[bufferIndex].handle, zeusName.toStdString().c_str(), value);
    // setLastValue(bufferIndex, ZeusHashValue(zeusName, value, Dsc_Bool, 0));
}

void* ZeusAPIInterface::zeusCudaAddr() const
{
    return m_ZeusCudaAddr;
}

// void ZeusAPIInterface::reflushParas(int bufferIndex)
//{
//    if (!isHandleValid(bufferIndex))
//    {
//        return;
//    }

//    QList<ZeusHashValue> paraList;
//    foreach (const ZeusHashValue& para, m_ZeusValueLists[bufferIndex])
//    {
//        paraList.append(para);
//    }

//    setZeusParameter(bufferIndex, paraList);
//}

void ZeusAPIInterface::setZeusParameterPointer(int bufferIndex, const QString& zeusName, const void* value, int size)
{

#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION << zeusName << (quintptr)value << size << "m_ImageTileIndex:" << m_ImageTileIndex;
#endif
    if (value == nullptr)
    {
        return;
    }

    ::ZeusSetParameterPointer(m_ZeusHanles[bufferIndex].handle, zeusName.toStdString().c_str(), (void*)value, size);
}

void ZeusAPIInterface::exportProperties(int bufferIndex, const QString& dir)
{
    if (isHandleValid(bufferIndex))
    {
        ::ZeusExportProperties(m_ZeusHanles[bufferIndex].handle, dir.toLatin1().data());
    }
}

#ifdef USE_RDMA
quint64 ZeusAPIInterface::allocCudaAddress(int bufferIndex, size_t buffsize)
{
    uint64_t pa_[8192] = {0}; //这个数组大小根据申请显存大小/64KB   如 512MB/64KB = 8192
#ifdef SYS_WINDOWS
    Qt::HANDLE hdio_;
    Qt::HANDLE mh_;
    ulong page_size, entries;
    void* d_idata_ = ::ZeusAllocCudaAddress(m_ZeusHanles[bufferIndex].handle, buffsize);
    hdio_ = gdrdrv_open();
    gdr_pin_buffer(hdio_, (uint64_t)d_idata_, buffsize, 0, 0, &mh_, &page_size, &entries, pa_);
    m_ZeusCudaAddr = d_idata_;
    qCritical() << PRETTY_FUNCTION << ", alloc cuda addr:" << d_idata_ << ", gdr open handle:" << hdio_
                << ", physical address, 0:" << pa_[0] << ", 1:" << pa_[1] << ", 2:" << pa_[2];
#endif
    // physical address
    return (quint64)pa_[0];
}
#endif

bool ZeusAPIInterface::setZeusParameterTrigger(int bufferIndex, const QString& zeusName)
{
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION << zeusName << "m_ImageTileIndex:" << m_ImageTileIndex;
#endif
    if (!isHandleValid(bufferIndex))
    {
        return false;
    }
    ::ZeusSetParameterTrigger(m_ZeusHanles[bufferIndex].handle, zeusName.toStdString().c_str());
    return true;
}

bool ZeusAPIInterface::setZeusPreProcessDataCallBack(int bufferIndex, PreProcessDataCallBack callback, void* userData)
{
    if (isHandleValid(bufferIndex))
    {
        ::ZeusSetPreProcessDataCallBack(m_ZeusHanles[bufferIndex].handle, callback, userData);
        return true;
    }
    return false;
}

bool ZeusAPIInterface::setZeusPostProcessDataCallBack(int bufferIndex, ZeusPostProcessDataCallBack callback,
                                                      void* userData)
{
    if (isHandleValid(bufferIndex))
    {
        ::ZeusSetPostProcessDataCallBack(m_ZeusHanles[bufferIndex].handle, callback, userData);
        return true;
    }
    return false;
}

bool ZeusAPIInterface::SetZeusPostProcessParaCallBack(int bufferIndex, PostProcessParaCallBack callback, void* userData)
{
    if (isHandleValid(bufferIndex))
    {
        ::ZeusSetPostProcessParaCallBack(m_ZeusHanles[bufferIndex].handle, callback, userData);
        return true;
    }
    return false;
}

bool ZeusAPIInterface::SetZeusPostProcessPropCallBack(int bufferIndex, PostProcessPropCallBack callback, void* userData)
{
    if (isHandleValid(bufferIndex))
    {
        ::ZeusSetPostProcessPropCallBack(m_ZeusHanles[bufferIndex].handle, callback, userData);
        return true;
    }
    return false;
}

bool ZeusAPIInterface::SetZeusExternalFunction(int bufferIndex, ExternalFunction function,
                                               ZeusExternalFunctionType functionType, void* userData)
{
    if (!isHandleValid(bufferIndex))
    {
        return false;
    }

    ::ZeusSetExternalFunction(m_ZeusHanles[bufferIndex].handle, function, (int)functionType, userData);
    return true;
}

bool ZeusAPIInterface::pushDataForPreProcess(int bufferIndex, DataForPreProcess* data, int dataType, int dataCount,
                                             InfoForPostProcess* info)
{
    if (!isHandleValid(bufferIndex))
    {
        return false;
    }

    ::ZeusPushDataForPreProcess(m_ZeusHanles[bufferIndex].handle, data, dataType, dataCount, info);
    return true;
}

bool ZeusAPIInterface::pushDataForPostProcess(int bufferIndex, DataForPostProcess* data, int dataType, int dataCount,
                                              InfoForPostProcess info)
{
    if (!isHandleValid(bufferIndex))
    {
        return false;
    }
    ::ZeusPushDataForPostProcess(m_ZeusHanles[bufferIndex].handle, data, dataType, dataCount, info);
    return true;
}

bool ZeusAPIInterface::isHandleValid(int bufferIndex) const
{
    return (m_ZeusHanles.contains(bufferIndex) && (nullptr != m_ZeusHanles[bufferIndex].handle));
}

void ZeusAPIInterface::setPostProcessFlag(int bufferIndex, bool flag)
{
    if (isHandleValid(bufferIndex))
    {
        m_ZeusHanles[bufferIndex].postProcessFlag = flag;
    }
}

void ZeusAPIInterface::setPreProcessFlag(int bufferIndex, bool flag)
{
    if (isHandleValid(bufferIndex))
    {
        m_ZeusHanles[bufferIndex].preProcessFlag = flag;
    }
}

bool ZeusAPIInterface::postProcessFlag(int bufferIndex) const
{
    if (isHandleValid(bufferIndex))
    {
        return m_ZeusHanles[bufferIndex].postProcessFlag;
    }
    return false;
}

bool ZeusAPIInterface::preProcessFlag(int bufferIndex) const
{
    if (isHandleValid(bufferIndex))
    {
        return m_ZeusHanles[bufferIndex].preProcessFlag;
    }
    return false;
}

void ZeusAPIInterface::createHandle()
{
    QSize imageSize = m_SonoParameters->pV(BFPNames::BImageSizeStr).toSize();
    CreationInfo createionInfo;
    memset(&createionInfo, 0, sizeof(CreationInfo));
    createionInfo.ImageWidth = imageSize.width();
    createionInfo.ImageHeight = imageSize.height();
    createionInfo.UserData = this;
    createionInfo.IsMultiOutput = true;
    createionInfo.CVIEType = 0;
    createionInfo.NetworkEncrypted = true;
    createionInfo.IsNeedInitOpenGL = true;
    createionInfo.MRDLineDataCacheFilePath = (char*)"";
    createionInfo.ResPath = (char*)Resource::ZeusNetworksPath();
    createionInfo.IsSupportPreProcess = m_IsSupportPreProcess;
#ifdef USE_PREPROCESS
    createionInfo.Use16BitPW = true;
#else
    createionInfo.Use16BitPW = false;
#endif

#ifdef USE_RDMA
    createionInfo.WorkMode = 1;
#endif

    foreach (ISonoBuffer* pBuffer, m_SonoBufferList)
    {
        int bufferIndex = pBuffer->bufferIndex();
        if (m_ZeusHanles.contains(bufferIndex))
        {
            continue;
        }
        HandleInfo info;
        info.handle = ::ZeusCreate(&createionInfo);
        createionInfo.IsNeedInitOpenGL = false;
        m_ZeusHanles.insert(bufferIndex, info);
    }
}

ZeusHashValue::ZeusHashValue()
{
}

ZeusHashValue::ZeusHashValue(QString name, QVariant value, int type, int size)
    : m_Name(name)
    , m_Value(value)
    , m_Type(type)
    , m_Size(size)
{
}
