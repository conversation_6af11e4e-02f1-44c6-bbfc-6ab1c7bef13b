#include "zeusimagetile.h"
#include "zeuscontext.h"
#include "lineimageargs.h"
#include "ibufferreadwritelock.h"
#include "assertlog.h"
#include "isonobuffer.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "ibufferpushcontroler.h"
#include <QDebug>

ZeusImageTile::ZeusImageTile(const int imageTileIndex, IBufferPushControler* iBufferPushControler,
                             ChisonUltrasound::ChisonUltrasoundMode type, ZeusAPIInterface* api,
                             IImageSaveHelper* imageSaveHelper, QObject* parent)
    : AbstractDSCImageTile(imageTileIndex, iBufferPushControler, type, parent)
    , m_ZeusContext(new ZeusContext(iBufferPushControler->getISonoBuffer(), imageTileIndex, api, imageSaveHelper, this))
    , m_Image2DMap(NULL)
    , m_WaveMap(NULL)
    , m_DataPushed(false)
{
    qRegisterMetaType<LineImageArgs*>("LineImageArgs*");

    connect(m_ZeusContext, SIGNAL(rawDataInfo(void*, int, int, int, int)), this,
            SIGNAL(rawDataInfo(void*, int, int, int, int)), Qt::DirectConnection);
    connect(m_ZeusContext, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewImage(LineImageArgs*)),
            Qt::DirectConnection);
    connect(m_ZeusContext, SIGNAL(newMeasureImage(LineImageArgs*)), this, SLOT(onNewMeasureImage(LineImageArgs*)),
            Qt::DirectConnection);
    connect(m_ZeusContext, SIGNAL(newGrayImage(LineImageArgs*)), this, SLOT(onNewGrayImage(LineImageArgs*)),
            Qt::DirectConnection);
    connect(m_ZeusContext, SIGNAL(fpsChanged(const QString&, const float)), this,
            SIGNAL(fpsChanged(const QString&, const float)));
    connect(m_ZeusContext, SIGNAL(datainfo(void*, int, int, int, int)), this,
            SIGNAL(datainfo(void*, int, int, int, int)), Qt::DirectConnection);
    connect(m_ZeusContext, SIGNAL(requestFlush()), this, SIGNAL(requestFlush()), Qt::DirectConnection);
    connect(m_ZeusContext, &ZeusContext::imageDataReady, this, &AbstractDSCImageTile::imageDataReady);
}

ZeusImageTile::~ZeusImageTile()
{
    disconnect(this);
    if (m_ZeusContext != NULL)
    {
        delete m_ZeusContext;
        m_ZeusContext = NULL;
    }
}

void ZeusImageTile::setColorMap(uchar* image2DMap, uchar* waveMap, int activeIndex)
{
    m_Image2DMap = image2DMap;
    m_WaveMap = waveMap;
    m_ZeusContext->setColorMap(image2DMap, waveMap, activeIndex);
}

void ZeusImageTile::onEntryStressEchoAnalyze()
{
    AbstractDSCImageTile::onEntryStressEchoAnalyze();
    m_ZeusContext->onEntryStressEchoAnalyze();
}

void ZeusImageTile::onExitStressEchoAnalyze()
{
    AbstractDSCImageTile::onExitStressEchoAnalyze();
    {
        QMutexLocker gstlocker(&m_DeliverBufferMutex);
        m_DeliverBuffer.clear();
        m_DeliverBufferOld.clear();
    }
    m_ZeusContext->onExitStressEchoAnalyze();
}

bool ZeusImageTile::enForceDSCImage()
{
    if (m_DataPushed)
    {
        m_ZeusContext->enForceDSCColorMap();
    }
    return m_DataPushed;
}

void ZeusImageTile::onPlayCineStatusChanged(const bool playCineStatus)
{
    m_ZeusContext->onPlayCineStatusChanged(playCineStatus);
}

bool ZeusImageTile::onImageChanged(const QList<BufferUnit>& bufferUnits, const FrameUnitInfo& frameUnitInfo)
{
#ifdef SYS_APPLE
    if (m_BufferChanged)
    {
        m_BufferChanged = false;
        return false;
    }
#else
    if (m_BufferChanged.testAndSetOrdered(1, 0))
    {
        qCritical() << PRETTY_FUNCTION << "m_BufferChanged.testAndSetOrdered(1, 0) error";
        return false;
    }
#endif
    if (m_ZeusContext != NULL && m_SonoParameters != NULL)
    {
        m_DataPushed = true;
        m_ZeusContext->pushData(bufferUnits, frameUnitInfo);
    }
    return true;
}

void ZeusImageTile::onClearFrozenIndex()
{
}

void ZeusImageTile::onStart()
{
}

void ZeusImageTile::onStop()
{
}

void ZeusImageTile::startInternal()
{
    recoveryProcessImageChanged();
    {
        m_ZeusContext->onExecParameterChanged();
    }
    emit pipelineCreated(m_ImageTileIndex);
}

void ZeusImageTile::stopInternal()
{
    haltProcessImageChanged(m_FrozenIndex);
    emit beforePipelineStop(m_ImageTileIndex);
}

void ZeusImageTile::onHaltProcessImageChanged()
{
}

void ZeusImageTile::onRecoveryProcessImageChanged()
{
}

void ZeusImageTile::clearThreadData(int count)
{
    {
        QWriteLocker locker(&m_LockInMainAndDSCThread);
        m_ZeusContext->setLostFrame(count);
    }
}

bool ZeusImageTile::isImage2DPushed() const
{
    return (m_ZeusContext != nullptr) ? m_ZeusContext->image2DPushed() : true;
}

void ZeusImageTile::setEnforceEnable(bool state)
{
    if (m_ZeusContext != nullptr)
    {
        m_ZeusContext->setEnforceEnable(state);
    }
}

void ZeusImageTile::onBeforeParamChanged()
{
#ifndef SYS_APPLE
    m_IsNewImagePaused.testAndSetOrdered(0, 1);
#endif
    {
        QWriteLocker locker(&m_LockInMainAndDSCThread);
        m_ZeusContext->updateSonoParametersChanning(true);
        if (m_WaitingSignalCounter < 0)
        {
            m_WaitingSignalCounter = 0;
        }
        ++m_WaitingSignalCounter;
    }
    if (m_BlockingChangedSlot)
    {
        return;
    }
    stopInternal();
}

void ZeusImageTile::onParamChanged(unsigned int counter)
{
    {
        QWriteLocker locker(&m_LockInMainAndDSCThread);
        --m_WaitingSignalCounter;
        if (m_WaitingSignalCounter > 0)
        {
            return;
        }
        m_ZeusContext->updateSonoParametersChanning(false);
    }
    if (m_IsSonoParameterCleared || (counter != m_SonoparametersChangedCounter))
    {
        return;
    }
    if (m_BlockingChangedSlot)
    {
        return;
    }
    {
        IBufferReadLocker locker(m_SonoBuffer, PRETTY_FUNCTION);
        if (m_IsSonoParameterCleared)
        {
            return;
        }
        /*
         * 布局区域由多变少的时候，多出来的区域不在需要建立pipeline
         * 否则会产生死锁
         * */
        if (!m_InEasyPlayMode && (m_ImageTileIndex >= m_SonoParameters->pIV(BFPNames::LayoutStr)))
        {
            return;
        }
    }
    startInternal();
}

void ZeusImageTile::onBeforeSonoParametersChanged()
{
    m_DataPushed = false;
    stopInternal();
    m_ZeusContext->beforeSonoParametersChanged();
}

void ZeusImageTile::onSonoParametersChanged()
{
    if (m_SonoParameters == NULL)
    {
        return;
    }
    m_ZeusContext->setSonoParameters(m_SonoParameters);
    startInternal();
}

// void ZeusImageTile::onClearSonoParametersChanged()
//{
//    m_DataPushed = false;
//    stopInternal();
//    m_ZeusContext->clearSonoParameters();
//}

// void ZeusImageTile::onlineImageBufferChanged()
//{
//    stopInternal();
//    m_ZeusContext->clearSonoParameters();
//}

void ZeusImageTile::onNewImage(LineImageArgs* lineImageArgs)
{
    lineImageArgs->setIndex(m_ImageTileIndex);
    emit newImage(lineImageArgs);
}

void ZeusImageTile::onNewMeasureImage(LineImageArgs* lineImageArgs)
{
    lineImageArgs->setIndex(m_ImageTileIndex);
    emit newMeasureImage(lineImageArgs);
}

void ZeusImageTile::onNewGrayImage(LineImageArgs* lineImageArgs)
{
    lineImageArgs->setIndex(m_ImageTileIndex);
    emit newGrayImage(lineImageArgs);
}
