#include "zeuspipeline.h"
#include "zeusimagetile.h"
#include "ibufferpushcontroler.h"
#include "isonobuffer.h"
#include "bfpnames.h"
#include "sonoparameters.h"
#include "memoryleakcheck.h"
#include "zeusimagetile.h"
#include "logger.h"
#include "assertlog.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, ZeusPipeline)

ZeusPipeline::ZeusPipeline(QList<IBufferPushControler*> iBufferPushControler, ZeusAPIInterface* api,
                           IImageSaveHelper* imageSaveHelper, ChisonUltrasound::ChisonUltrasoundMode type,
                           QObject* parent)
    : AbstractDSCPipeline(iBufferPushControler, type, parent)
    , m_zeus<PERSON><PERSON>per(api)
    , m_ImageSaveHelper(imageSaveHelper)
{
}

ZeusPipeline::~ZeusPipeline()
{
    qDeleteAll(m_ImageTiles.values());
    m_ImageTiles.clear();
}

void ZeusPipeline::onStart()
{
    int layout = m_BufferPushControler.size();
    qDeleteAll(m_ImageTiles.values());
    m_ImageTiles.clear();

    log()->debug("ready to create ImageTiles, layout is %1!", layout);

    for (int i = 0; i < layout; ++i)
    {
        IBufferPushControler* iBufferPushControler = m_BufferPushControler[i];

        ASSERT_X_LOG(iBufferPushControler != NULL, PRETTY_FUNCTION, "iBufferPushControler can not be null");

        AbstractDSCImageTile* tile =
            new ZeusImageTile(iBufferPushControler->getISonoBuffer()->bufferIndex(), iBufferPushControler,
                              m_ChisonUltrasoundMode, m_zeusHelper, m_ImageSaveHelper);
        tile->initialize();
        connect(tile, SIGNAL(rawDataInfo(void*, int, int, int, int)), this,
                SIGNAL(rawDataInfo(void*, int, int, int, int)), Qt::DirectConnection);
        connect(tile, SIGNAL(newGrayImage(LineImageArgs*)), this, SIGNAL(newGrayImage(LineImageArgs*)),
                Qt::DirectConnection);
        connect(tile, SIGNAL(newMeasureImage(LineImageArgs*)), this, SLOT(onNewMeasureImage(LineImageArgs*)),
                Qt::DirectConnection);
        connect(tile, SIGNAL(requestFlush()), this, SIGNAL(requestFlush()), Qt::DirectConnection);
        connect(tile, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewImage(LineImageArgs*)), Qt::DirectConnection);
        connect(tile, SIGNAL(beforePipelineStop(int)), this, SIGNAL(beforePipelineStop(int)), Qt::DirectConnection);
        connect(tile, SIGNAL(pipelineCreated(int)), this, SIGNAL(pipelineCreated(int)), Qt::DirectConnection);
        connect(tile, SIGNAL(fpsChanged(const QString&, const float)), this,
                SIGNAL(fpsChanged(const QString&, const float)), Qt::DirectConnection);
        connect(tile, SIGNAL(datainfo(void*, int, int, int, int)), this, SIGNAL(datainfo(void*, int, int, int, int)),
                Qt::DirectConnection);
        connect(tile, &AbstractDSCImageTile::imageDataReady, this, &AbstractDSCPipeline::imageDataReady);
        if ((m_Image2DMap != NULL) && (m_WaveMap != NULL))
        {
            tile->setColorMap(m_Image2DMap, m_WaveMap, 0);
        }

        tile->start();

        m_ImageTiles.insert(iBufferPushControler, tile);
        CHECK_NEW(AbstractDSCImageTile, tile);
    }
    if (m_ChisonUltrasoundMode == ChisonUltrasound::ChisonUltrasound_RealTime)
    {
        /*在系统启动的时候，先只建立一个pipeline*/
        int activeBIndex = m_SonoParameters->pIV(BFPNames::ActiveBStr);
        if ((activeBIndex < m_BufferPushControler.size()) && (activeBIndex < m_ImageTiles.size()))
        {
            m_ImageTiles[m_BufferPushControler[activeBIndex]]->firstInit();
        }
    }
}

void ZeusPipeline::onStop()
{
    foreach (AbstractDSCImageTile* imageTile, m_ImageTiles.values())
    {
        imageTile->stop();
        disconnect(imageTile, SIGNAL(rawDataInfo(void*, int, int, int, int)), this,
                   SIGNAL(rawDataInfo(void*, int, int, int, int)));
        disconnect(imageTile, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewImage(LineImageArgs*)));
        disconnect(imageTile, SIGNAL(beforePipelineStop(int)), this, SIGNAL(beforePipelineStop(int)));
        disconnect(imageTile, SIGNAL(pipelineCreated(int)), this, SIGNAL(pipelineCreated(int)));
        disconnect(imageTile, SIGNAL(fpsChanged(const QString&, const float)), this,
                   SIGNAL(fpsChanged(const QString&, const float)));
    }
    qDeleteAll(m_ImageTiles.values());
    m_ImageTiles.clear();
}

void ZeusPipeline::onSetSonoParameters()
{
}
