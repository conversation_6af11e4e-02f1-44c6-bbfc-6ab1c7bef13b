#include "zeuscontext.h"
#include "assertlog.h"
#include "bfpnames.h"
#include "bfscanareawidthparameter.h"
#include "bytelineimageargs.h"
#include "fpscalculator.h"
#include "generalinfo.h"
#include "imageeventargs.h"
#include "imageinfodef.h"
#include "iimagesavehelper.h"
#include "linedatahead.h"
#include "modelconfig.h"
#include "parameter.h"
#include "platform_xx_resourcehelper.h"
#include "probedataset.h"
#include "simplemulticalltrigger.h"
#include "sonoparameters.h"
#include "systemscanmodeclassifier.h"
#include "usimageprocess.h"
#include "variantutil.h"
#include <QDebug>
#include <QMutexLocker>
#include <QRect>
#include <QSize>
#include <QtCore/qmath.h>
#include <iostream>
#ifdef USE_PAML_IIMAGE
#include "chengine.h"
#endif
#ifdef USE_IIMAGE
#include "iImageStrategySwitcher.h"
#include "iimageprocesshelper.h"
#endif
#include <QDateTime>
//#define FPS_CHECK
#include "dscecgdataprocesshelper.h"
#include "zeusparameternames.h"
#include "isonobuffer.h"
#include "memoryleakcheck.h"
#include "resource.h"
#include "setting.h"
#include "util.h"
#include <QDir>
#ifdef USE_IMGNETCLIENT
#include "imgnetclient.h"
#endif
#include "autofreezesetting.h"
#include "bitoperator.h"
#include "logger.h"
#include "probeparameters.h"
#include "realcompare.h"
#include "zeusapihelper.h"
#include "zeusmappinghelper.h"
#include "paracontrol/postprocessparacontroller.h"
#include "syncidmanager.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, ZeusContext)

using namespace ZeusParameterNames;

//#define ZEUS_ADAPTER_DEBUG_ENABLE

unsigned char ZeusContext::m_SyncId = 0;
QMutex ZeusContext::m_SyncMutex;

QStringList ZeusContext::PersistenceResetParameters = QStringList();
QStringList ZeusContext::m_ZeusEnForceParameterNames = QStringList();

ZeusContext::ZeusContext(ISonoBuffer* sonoBuffer, const int imageTileIndex, ZeusAPIInterface* api,
                         IImageSaveHelper* imageSaveHelper, QObject* parent)
    : QObject(parent)
    , m_ZeusHandle(NULL)
    , m_ImageTileIndex(imageTileIndex)
    , m_IsNewImagePaused(true)
    , m_SonoParameters(NULL)
    , m_SonoParametersChanning(false)
    , m_LineImageArgs(new ByteLineImageArgs)
    , m_Trigger(new SimpleMultiCallTrigger)
#ifdef USE_IIMAGE
    , m_iImageProcessFacade(0)
#endif
    , m_FpsCalculatorOut(new FpsCalculator)
    , m_FpsCalculatorIn(new FpsCalculator)

    , m_EcgDataHelper(new DscEcgDataProcessHelper)
    , m_UsePWFrameIndex(true)
    , m_IImageType(0)
    , m_InnerSyncId(0)
    , m_SonoBuffer(sonoBuffer)
    , m_SyncMode(Sync_None)
    , m_LostFrame(0)
    , m_PostProcessParaController(new PostProcessParaController(imageTileIndex, api))
    , m_EnableRawData(ENABLE_RAW_DATA_NONE)
    , m_ImageSaveHelper(imageSaveHelper)
{
    connect(m_SonoBuffer, SIGNAL(getSyncId(int&)), this, SLOT(onGetSyncId(int&)), Qt::DirectConnection);
    connect(m_PostProcessParaController, SIGNAL(getSyncId(int&)), this, SLOT(onGetSyncId(int&)), Qt::DirectConnection);
    connect(this, SIGNAL(requesFlushByBufferIndex(int)), m_SonoBuffer, SLOT(onRequesFlushByBufferIndex(int)),
            Qt::DirectConnection);

    qRegisterMetaType<QVariant>("QVariant&");
    m_FpsCalculatorIn->setAdditionInfo("ZeusContext_DSC_In");
    m_FpsCalculatorIn->setLogOut(true);
    connect(m_FpsCalculatorIn, SIGNAL(update(const QString&, float)), this, SLOT(onFpsUpdate(const QString&, float)));
    m_FpsCalculatorOut->setAdditionInfo("ZeusContext_DSC_Out");
    m_FpsCalculatorOut->setLogOut(true);
    connect(m_FpsCalculatorOut, SIGNAL(update(const QString&, float)), this, SLOT(onFpsUpdate(const QString&, float)));

    connect(m_EcgDataHelper, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewImage(LineImageArgs*)));

    connect(m_Trigger, SIGNAL(triggerCompleted()), this, SLOT(onExecParameterChanged()));

    m_SetColorActivateTimer = new QTimer(this);
    connect(m_SetColorActivateTimer, SIGNAL(timeout()), this, SLOT(onTimeout()));
    m_SetColorActivateTimer->setSingleShot(true);

    m_SonoParameters = m_SonoBuffer->sonoParameters();
#ifdef USE_IIMAGE

    UiImageStrategySwitcher::enuStrategyType iImage_type =
        (UiImageStrategySwitcher::enuStrategyType)m_SonoParameters->pIV(BFPNames::LineiImageTypeStr);
    m_iImageProcessFacade = new UiImageStrategySwitcher(imageTileIndex, m_SonoParameters, iImage_type);

    connect(m_iImageProcessFacade, SIGNAL(fpsChanged(const QString&, const float)), this,
            SIGNAL(fpsChanged(const QString&, const float)),
            Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
    connect(m_iImageProcessFacade, SIGNAL(requestFlush()), this, SLOT(onRequestFlush()), Qt::DirectConnection);
#endif

    if (PersistenceResetParameters.isEmpty())
    {
        PersistenceResetParameters << BFPNames::HighDensityStr << BFPNames::ProbeIdStr;
    }
    memset(m_Image2DMap, 0, COLORMAP_BUFFER_SIZE);
    memset(m_WaveMap, 0, COLORMAP_BUFFER_SIZE);
    memset(m_LoadImage2DMap, 0, COLORMAP_BUFFER_SIZE);
    memset(m_LoadWaveMap, 0, COLORMAP_BUFFER_SIZE);
    memset(m_StressImage2DMap, 0, COLORMAP_BUFFER_SIZE);
    memset(m_StressWaveMap, 0, COLORMAP_BUFFER_SIZE);

    QSize renderImageSize = ModelConfig::instance().value(ModelConfig::RenderImageSize, QSize(640, 512)).toSize();
    m_Image2DData = new uchar[renderImageSize.width() * renderImageSize.height() * 4];
    m_WaveData = new uchar[renderImageSize.width() * renderImageSize.height() * 4];
}

ZeusContext::~ZeusContext()
{
    QMutexLocker locker(&m_ZeusHandleMutex);
    disconnect(this);
    if (nullptr != m_SonoBuffer)
    {
        m_SonoBuffer->disconnect();
    }
    if (m_SetColorActivateTimer != NULL && m_SetColorActivateTimer->isActive())
    {
        m_SetColorActivateTimer->stop();
        m_SetColorActivateTimer->disconnect();
        delete m_SetColorActivateTimer;
        m_SetColorActivateTimer = NULL;
    }
    delete m_LineImageArgs;
    m_LineImageArgs = NULL;
    m_Trigger->disconnect();
    delete m_Trigger;
    m_Trigger = NULL;
    m_FpsCalculatorIn->disconnect();
    delete m_FpsCalculatorIn;
    m_FpsCalculatorIn = NULL;
    m_FpsCalculatorOut->disconnect();
    delete m_FpsCalculatorOut;
    m_FpsCalculatorOut = NULL;
#ifdef USE_IIMAGE
    if (m_iImageProcessFacade)
    {
        m_iImageProcessFacade->disconnect();
        m_iImageProcessFacade->deleteLater();
        m_iImageProcessFacade = nullptr;
    }
#endif
    if (nullptr != m_EcgDataHelper)
    {
        m_EcgDataHelper->disconnect();
        m_EcgDataHelper->deleteLater();
        m_EcgDataHelper = nullptr;
    }

    if (m_PostProcessParaController != nullptr)
    {
        delete m_PostProcessParaController;
        m_PostProcessParaController = nullptr;
    }
}

void ZeusContext::setColorMap(uchar* image2DMap, uchar* waveMap, int activeIndex)
{
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION << "activeIndex:" << activeIndex << "m_ImageTileIndex:" << m_ImageTileIndex
             << "m_SonoParameters:" << (quintptr)m_SonoParameters << "m_SonoBuffer->isLoad():" << m_SonoBuffer->isLoad()
             << "m_IsInStressEchoAnalyzeState:" << m_IsInStressEchoAnalyzeState << "image2DMap:" << (quintptr)image2DMap
             << "waveMap:" << (quintptr)waveMap;
#endif
    if (activeIndex != m_ImageTileIndex && !m_IsInStressEchoAnalyzeState && !m_SonoBuffer->isLoad())
    {
        return;
    }
    if (image2DMap != NULL)
    {
        IBufferReadLocker locker(m_SonoBuffer);
        if (m_IsInStressEchoAnalyzeState)
        {
            memcpy(m_StressImage2DMap, image2DMap, COLORMAP_BUFFER_SIZE);
        }
        else if (m_SonoParameters == NULL || !m_SonoBuffer->isLoad())
        {
            memcpy(m_Image2DMap, image2DMap, COLORMAP_BUFFER_SIZE);
        }
        else
        {
            memcpy(m_LoadImage2DMap, image2DMap, COLORMAP_BUFFER_SIZE);
        }
    }
    if (waveMap != NULL)
    {
        IBufferReadLocker locker(m_SonoBuffer);
        if (m_IsInStressEchoAnalyzeState)
        {
            memcpy(m_StressWaveMap, waveMap, COLORMAP_BUFFER_SIZE);
        }
        else if (m_SonoParameters == NULL || !m_SonoBuffer->isLoad())
        {
            memcpy(m_WaveMap, waveMap, COLORMAP_BUFFER_SIZE);
        }
        else
        {
            memcpy(m_LoadWaveMap, waveMap, COLORMAP_BUFFER_SIZE);
        }
    }
    {
        IBufferReadLocker locker(m_SonoBuffer);
        if ((m_SonoParameters != NULL) && (!m_SonoParametersChanning || m_SonoBuffer->isLoad()))
        {
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
            qDebug() << PRETTY_FUNCTION << "activeIndex:" << activeIndex << "m_ImageTileIndex:" << m_ImageTileIndex
                     << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime();
#endif
            QMutexLocker locker(&m_ZeusHandleMutex);
            updateColorMap();
        }
    }
    if (Setting::instance().defaults().colorMapLog())
    {
        Util::Mkdir("colormap");
        QFile qfile1(QString("colormap/colormap1.dat"));
        if (qfile1.open(QIODevice::WriteOnly))
        {
            qfile1.write((const char*)image2DMap, 256 * 4);
            qfile1.close();
        }
        QFile qfile2(QString("colormap/colormap2.dat"));
        if (qfile2.open(QIODevice::WriteOnly))
        {
            qfile2.write((const char*)waveMap, 256 * 4);
            qfile2.close();
        }
    }
}

void ZeusContext::updateSonoParametersChanning(bool sonoParametersChanning)
{
    m_SonoParametersChanning = sonoParametersChanning;
}

void ZeusContext::setLostFrame(int count)
{
    QMutexLocker locker(&m_LostFrameMutex);
    m_LostFrame = count;
}

int ZeusContext::lostFrameCount()
{
    QMutexLocker locker(&m_LostFrameMutex);
    return m_LostFrame;
}

bool ZeusContext::image2DPushed() const
{
    return m_Image2DPushed;
}

void ZeusContext::setEnforceEnable(bool state)
{
    m_PostProcessParaController->setEnforceEnable(state);
}

void ZeusContext::beforeSonoParametersChanged()
{
    m_Trigger->blockSignals(true);
    m_IsNewImagePaused = true;
    {
        QMutexLocker locker(&m_ZeusHandleMutex);
        disconnectSonoParameters();
        //        m_SonoParameters = NULL;
    }

    m_Trigger->stop();
}

void ZeusContext::setSonoParameters(SonoParameters* sonoParameters)
{
    if (m_IsNewImagePaused || (m_SonoParameters != sonoParameters))
    {
        {
            IBufferReadLocker locker(m_SonoBuffer, PRETTY_FUNCTION);
        }
        {
            QMutexLocker locker(&m_ZeusHandleMutex);
            m_SonoParameters = sonoParameters;

            if (m_SonoParameters == m_PostProcessParaController->sonoParameters())
            {
                m_PostProcessParaController->updateSonoParameters();
            }
            m_PostProcessParaController->setSonoParameters(m_SonoParameters);
            QSize imageSize = m_SonoParameters->pV(BFPNames::BImageSizeStr).toSize();

            createHandle(imageSize.width(), imageSize.height());
            connectSonoParameters();
        }
        m_Trigger->blockSignals(false);
    }
    else
    {
        onExecParameterChanged();
    }
}

void ZeusContext::clearSonoParameters()
{
    QMutexLocker locker(&m_ZeusHandleMutex);
    disconnectSonoParameters();
    // TODO!!!! dscParaControl need clearSonoParameters??
    m_IsNewImagePaused = true;
    //    m_SonoParameters = NULL;
}

void ZeusContext::pushData(const QList<BufferUnit>& bufferUnits, const FrameUnitInfo& frameUnitInfo)
{
    calAvgFunRunTimeIn(pushData) QMutexLocker locker(&m_ZeusHandleMutex);
    static bool reseted = false;
    static int saveIndex = 0;
    //    qDebug() << PRETTY_FUNCTION << "FrameIndex: " << bufferUnits.last().frameIndex() << ", FrameType: " <<
    //    frameUnitInfo.imageType()
    //             << ", ByPass: " << bufferUnits.last().isByPass();
    //    if (2 == frameUnitInfo.imageType())
    //    {
    //        qCritical() << PRETTY_FUNCTION
    //                    << ", m_IsNewImagePaused: " << m_IsNewImagePaused
    //                    << ", frameInfo mode: " << frameUnitInfo.first().mode()
    //                    << ", frameInfo type: " << frameUnitInfo.imageType();
    //    }
    if (frameUnitInfo.m_persistenceReset)
    {
        if (!reseted)
        {
            m_PostProcessParaController->apiInterface()->setZeusParameterTrigger(
                m_ImageTileIndex, POSTPARANAME(ProcessSonoParameterIndex::PersistenceReset));
            reseted = true;
        }
    }
    else
    {
        reseted = false;
    }
    if (m_IsNewImagePaused)
    {
        calAvgFunRunTimeOut(pushData) return;
    }
    switch (frameUnitInfo.first().mode())
    {
    case ImageBufferDef::ECG_Data:
    {
        foreach (const BufferUnit& bu, bufferUnits)
        {
            m_EcgDataHelper->onImageChanged(bu);
        }
    }
    default:
        break;
    }

    int dataProcessedCount = 0;
    int totalDataCount = bufferUnits.count();

    if ((frameUnitInfo.imageType() == ImageBufferDef::Image2D) && (totalDataCount > 0))
    {
        //        qDebug() << PRETTY_FUNCTION
        //                 << "m_ImageTileIndex:" << m_ImageTileIndex
        //                 << "m_WavePushed:" << m_WavePushed
        //                 << "m_InnerSyncId:" << m_InnerSyncId;
        //        if (SystemScanModeClassifier::isLikeD(m_SystemScanMode))
        //        {
        //            log()->debug() << PRETTY_FUNCTION
        //                           << ", scan mode: " << m_SystemScanMode
        //                           << ", post receive PW data.";
        //        }

        if (SystemScanModeClassifier::isLikeD(m_SystemScanMode) && !m_SonoParameters->pBV(BFPNames::FreqSpectrumStr) &&
            !m_WavePushed)
        {
            requesFlushByBufferIndex(1);
        }
    }
    bool isContainUSPipeLineVersion = m_SonoParameters->contains(BFPNames::USPipeLineVersionStr);
    bool isWave = m_SonoParameters->pIV(BFPNames::SyncModeStr) & (Sync_D | Sync_M);

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
    bool trapeEnable = (probeDataInfo.IsLinear ? m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr)
                                               : m_SonoParameters->pBV(BFPNames::CurvedExapandingStr)) ||
                       m_SonoParameters->pBV(BFPNames::ScpdOnStr);
    while (dataProcessedCount < totalDataCount)
    {
        int dataCount = qMin(totalDataCount, MAX_PUSH_DATA_NUMBER);
        QHash<int, int> pushCounts;
        QList<ImageTypeAndIndex> imageTypeAndIndexs;
        foreach (const FrameInfo& frameInfo, frameUnitInfo)
        {
            imageTypeAndIndexs.append(calcDataPostProcessIndex(frameInfo));
            pushCounts.insert(imageTypeAndIndexs.last().ImageType, 0);
        }
        InfoForPostProcess info;
        QSet<int> steerings;
        for (int i = 0; i < dataCount; i++)
        {
            for (int j = 0; j < bufferUnits[i].count(); j++)
            {
                if (!trapeEnable && bufferUnits[i + dataProcessedCount].steering() != 0)
                {
                    continue;
                }
                steerings.insert(bufferUnits[i + dataProcessedCount].steering());
                //                qCritical() << PRETTY_FUNCTION << ", steerings insert:" << bufferUnits[i +
                //                dataProcessedCount].steering()
                //                            << ", steerings:" << steerings;
                ImageTypeAndIndex imageTypeAndIndex = imageTypeAndIndexs[j];
                if (!isContainUSPipeLineVersion || imageTypeAndIndex.Offset == 0)
                { //非血流数据时，向zeus赋值的方式不变
                    //以及1.7之前的版本，没有包含SR9的血流延迟271ms处理
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataType = imageTypeAndIndex.ImageType;
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Data =
                        (char*)bufferUnits[i + dataProcessedCount][j].data();
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataWidth = imageTypeAndIndex.Width;
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataHeight =
                        imageTypeAndIndex.Height;
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Steering =
                        bufferUnits[i + dataProcessedCount].steering();
                    ++pushCounts[imageTypeAndIndex.ImageType];
                }
                else if (i == 0)
                { //血流数据时，向zeus只赋值一帧数据，且为bufferUnits中的最后一帧，保证实时打图及时显示最新的图像
                    // imageTypeAndIndex.Offset == 1 与 imageTypeAndIndex.Offset == 2 无需增加判断，上个条件剩余判断
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataType = imageTypeAndIndex.ImageType;
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Data =
                        (char*)bufferUnits[dataProcessedCount + dataCount - 1][j].data();
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataWidth = imageTypeAndIndex.Width;
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataHeight =
                        imageTypeAndIndex.Height;
                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Steering =
                        bufferUnits[dataProcessedCount + dataCount - 1].steering();
                    ++pushCounts[imageTypeAndIndex.ImageType];
                }
                else
                {
                }
                info.info[2] = bufferUnits[i + dataProcessedCount].EcgEnd();
                if (bufferUnits[i + dataProcessedCount].isWaveFullScreen())
                {
                    info.info[2] |= 0x8000;
                }
            }
        }
        m_FpsCalculatorIn->cal();
        // 2025-06-11 Modify by AlexWang 使用c++11的for...range遍历
        for (const ImageTypeAndIndex& imageTypeAndIndex : qAsConst(imageTypeAndIndexs))
        {
            //            int systemScanMode = getSystemScanMode();
            //            setFrameIndex(systemScanMode, bufferUnits.last().frameIndex(),
            //            bufferUnits.last().cacheFrameIndex(),
            //                          imageTypeAndIndex.ImageType);
#ifdef SYS_APPLE
            if (imageTypeAndIndex.ImageType == PostImageType_M && systemScanMode != SystemScanModeUDBM)
            {
                return;
            }
#endif
#ifdef USE_TARGET_PALM
            if (pushCounts[imageTypeAndIndex.ImageType] <= 0 ||
                (imageTypeAndIndex.ImageType == 0 && steerings.count() != pushCounts[imageTypeAndIndex.ImageType]))
            {
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
                qCritical() << PRETTY_FUNCTION << "steerings:" << steerings.count()
                            << "count:" << pushCounts[imageTypeAndIndex.ImageType];
#endif
                //从BUG80947，花屏现象，测试激活时，导出来的before/after数据来看，当B数据的帧偏转不符合要求时，C数据继续推送了
                //原有的设计中，B和C是关联在一起的，B数据不满足三帧偏转的信息时，B和C的数据一起丢弃，防止C数据也异常，却刷新了图像
                break;
            }
#endif
            qint64 statrTime = bufferUnits.last().startTime();
            int frontIndex = isWave ? -1 : bufferUnits.last().frontIndex();
            // info下标,软件使用从1开始，目前使用到第12个。cacheFrameIndex历史中有效数据是16位，目前调用setCacheFrameIndex设置为0
            info.info[10] = bufferUnits.last().cacheFrameIndex() & 0x0000FFFF;
            //用info数组的6和7来存储
            info.info[7] = (int)((statrTime >> 32) & 0xFFFFFFFF); // 存储高32位
            info.info[6] = (int)(statrTime & 0xFFFFFFFF);         // 存储低32位
            info.info[5] = ((bufferUnits.last().imageRenderPartition() << 16) | (frontIndex & 0x0000FFFF));
            info.info[4] = bufferUnits.last().steering() & 0x0000000F;
            info.info[3] = bufferUnits.last().isByPass();
            info.info[3] = frameUnitInfo.needUpdateForzonIndex() << 1 | info.info[3];
            info.info[2] = (imageTypeAndIndexs.first().ImageType << 16) | info.info[2];
            // frameIndex在一直实时打图的时候（如全M模式）会超过2^16,从而占用高16位，所以高16位也要保留
            info.info[1] = bufferUnits.last().frameIndex();
            info.info[11] = bufferUnits.last().frameIndex();
            if (imageTypeAndIndex.ImageType == PostImageType_D)
            {
                info.info[11] = bufferUnits.last().waveFrameIndex();
            }

            info.info[12] = 0;
            // info[12] 现用于冻结/回调时，告知AI算法当前图像是最终界面刷新的图像
            bool isMeasureData = false;
            if ((m_SyncMode & Sync_D) == Sync_D && imageTypeAndIndex.ImageType == PostImageType_D)
            {
                // 当前模式含有频频时，以推送频谱数据为测量的有效数据，若以推送二维图数据为有效数据，则存在测量获取的频谱数据不符合要求
                isMeasureData = true;
            }
            else if (m_SyncMode == Sync_None && imageTypeAndIndex.ImageType == PostImageType_B)
            {
                // B模式下，以推送二维图数据为有效数据
                isMeasureData = true;
            }
            else
            {
            }

            if (isMeasureData && bufferUnits.last().isMeasureData())
            {
                info.info[12] = 1;
            }
            // M支持后处理，增加一个M的当前位置信息给zeus，放在低两个字节
            //扫查：满屏时，等于 当前位置+满屏线数；否则，等于当前位置
            //冻结：等于 当前位置
            if (imageTypeAndIndex.ImageType == PostImageType_M)
            {
                info.info[0] = bufferUnits.last().isWaveFullScreen()
                                   ? bufferUnits.last().EcgEnd() + imageTypeAndIndex.Height
                                   : bufferUnits.last().EcgEnd();
            }

            m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess->SyncId =
                SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId);
            if (imageTypeAndIndex.ImageType != PostImageType_B)
            {
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
                qDebug() << PRETTY_FUNCTION << "m_ImageTileIndex:" << m_ImageTileIndex
                         << "ImageType:" << imageTypeAndIndex.ImageType << "info.info[2]:" << info.info[2];
#endif
            }
            else
            {
                // info9目前用作b数据的线数
                info.info[9] = imageTypeAndIndex.Height;
            }
            if (Setting::instance().defaults().beforeZeusSaveFlag())
            //            if (imageTypeAndIndex.ImageType == PostImageType_D)
            {
                QString filePath =
                    QString("%1before_%2/").arg(Resource::zeusContextDataSaverDir()).arg(imageTypeAndIndex.ImageType);
                Util::Mkdir(filePath.toStdString().c_str());
                QString saveString = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");
                info.info[8] = saveIndex;
                for (int i = 0; i < dataCount; ++i)
                {
                    m_ImageSaveHelper->saveImage8(
                        (char*)m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Data,
                        m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataWidth,
                        m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataHeight *
                            ((imageTypeAndIndex.ImageType == PostImageType_C ||
                              imageTypeAndIndex.ImageType == PostImageType_D)
                                 ? 2
                                 : 1),
                        QString("%1%2_%3_%4.jpeg")
                            .arg(filePath)
                            .arg(saveString)
                            .arg(saveIndex)
                            .arg(QString::number(
                                m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Steering)));

                    QFile qfile(QString("%1%2_%3_%4.dat")
                                    .arg(filePath)
                                    .arg(saveString)
                                    .arg(saveIndex)
                                    .arg(QString::number(
                                        m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Steering)));
                    if (qfile.open(QIODevice::WriteOnly))
                    {
                        qfile.write((const char*)m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Data,
                                    m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataWidth *
                                        m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataHeight *
                                        ((imageTypeAndIndex.ImageType == PostImageType_C ||
                                          imageTypeAndIndex.ImageType == PostImageType_D)
                                             ? 2
                                             : 1));
                    }
                }
            }

            m_PostProcessParaController->apiInterface()->pushDataForPostProcess(
                m_ImageTileIndex, m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess,
                imageTypeAndIndex.ImageType, pushCounts[imageTypeAndIndex.ImageType], info);
            //            if(imageTypeAndIndex.ImageType == PostImageType_B)
            //            {
            //                QString saveString = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");
            //                for(int i = 0; i < dataCount; ++i)
            //                {
            //                    m_ImageSaveHelper->saveImage8(
            //                                (char*)m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Data,
            //                            m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataWidth,
            //                            m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataHeight,
            //                            QString("./ii/%1_%2.jpeg").arg(saveString).arg(
            //                                QString::number(m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Steering)));
            //                    m_ImageSaveHelper->saveImage2Hex(
            //                                (char*)m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].Data,
            //                            m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataWidth,
            //                            m_DataProcessInfo[imageTypeAndIndex.Offset].DataPostProcess[i].DataHeight
            //                            * (imageTypeAndIndex.ImageType == PostImageType_C ? 2 : 1),
            //                            QString("./ii/"));
            //                }
            //            }
        }
        if (Setting::instance().defaults().beforeZeusSaveFlag())
        {
            saveIndex++;
        }
        dataProcessedCount += dataCount;
    }
    calAvgFunRunTimeOut(pushData)
}

void ZeusContext::onPostProcessedData(void* data, int width, int height, InfoForPostProcess info)
{
    if (Setting::instance().defaults().beforeIImageSaveFlag())
    {
        static int iImageSaveCounter = 0;
        QString filePath = QString("%1imagebefore_%2_%3_%4")
                               .arg(Resource::iImageDataSaverDir())
                               .arg(iImageSaveCounter)
                               .arg(width)
                               .arg(height);
        Util::Mkdir(filePath.toStdString().c_str());
        m_ImageSaveHelper->saveImage2Hex((char*)data, width, height, QString("./%1/").arg(filePath), false);

        QString savePath =
            QString("./%1/%2.jpeg").arg(filePath).arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"));
        m_ImageSaveHelper->saveImage8((char*)data, width, height, savePath);
    }

    //在冻结下，打开存储数据开关，要讲数据更新到sonobuffer，存图时存储到病例中
    if ((!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr)) &&
        Setting::instance().defaults().rawDataUponSTILSAVEFlag())
    {
        m_SonoBuffer->setRawDataImage((uchar*)data, width, height);
    }
    //    QString saveString = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");
    //    QString saveDir = QString("./io");
    //    Util::Mkdir(saveDir.toStdString().c_str());
    //    QString savePath = QString("%1/%2.jpeg").arg(saveDir).arg(saveString);
    //    ImageSaveHelper::instance()->saveImage8((char*)data, width, height, savePath);
    int frameSteerType = BitOperator::getBitValue(info.info[4], 0, 4);
#ifdef USE_IIMAGE
    if (m_iImageProcessFacade->IsiImageEnabled())
    {
        if (!m_iImageProcessFacade->ProcessData((unsigned char*)data, (unsigned char*)data, width, height,
                                                frameSteerType))
        {
            return;
        }
        if (Setting::instance().defaults().afterIImageSaveFlag())
        {
            static int iImageSaveCounter = 0;
            QString filePath = QString("%1imageafter_%2_%3_%4")
                                   .arg(Resource::iImageDataSaverDir())
                                   .arg(iImageSaveCounter)
                                   .arg(width)
                                   .arg(height);
            Util::Mkdir(filePath.toStdString().c_str());
            m_ImageSaveHelper->saveImage2Hex((char*)data, width, height, QString("./%1/").arg(filePath), false);

            QString savePath =
                QString("./%1/%2.jpeg").arg(filePath).arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"));
            m_ImageSaveHelper->saveImage8((char*)data, width, height, savePath);
        }
    }
    else
    {
        // TODO: 未发现实际使用该信号的槽函数，可以考虑去掉
        emit rawDataInfo(data, width, height, 8, m_Layout);
    }
#endif

#ifdef USE_PAML_IIMAGE
    if (GeneralInfo::instance().m_probeTransmitType == 1)
    {
        bool islinear = m_SonoParameters->pIV(BFPNames::LinearStr);
        static int roiX = 0;
        static int roiWidth = 504;
        if (islinear)
        {
            roiX = 1;
            roiWidth = 503;
        }
        // writebinarydata((unsigned char *)data,width*height,"/linebmode1.bin");
        ::opnzMRDS((uint8_t*)data, width, height, roiX, 0, roiWidth, height,
                   m_SonoParameters->pIV(BFPNames::Q_IMAGEStr), 1, (uint8_t*)data);
        // writebinarydata((unsigned char *)data,width*height,"/linebmode2.bin");
    }
    else
        ::opnzMRDS((uint8_t*)data, width, height, 0, 0, width, height, m_SonoParameters->pIV(BFPNames::Q_IMAGEStr), 1,
                   (uint8_t*)data);

#endif

    //    ImageEventArgs *imageEventArgs = m_LineImageArgs->imageEvnetArgs();
    if (!m_SonoBuffer->isLoad())
    {
        // 传递判断自动冻结的数据
        emit datainfo(data, width, height, ImageEventArgs::ImageB, frameSteerType);
    }
}

void ZeusContext::onNewProcessedData(void* data, int width, int height, int dataType, int bitWidth, int syncId,
                                     InfoForPostProcess info)
{
    //    qCritical() << "dataType=" << dataType
    //                << "syncId=" << syncId
    //                << "PrePostSyncId=" << SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId);
    if (m_IsNewImagePaused || !validSyncID(syncId) || lostFrameCount() > 0)
    {
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
        qCritical() << PRETTY_FUNCTION << "lost one frame"
                    << "m_IsNewImagePaused" << m_IsNewImagePaused << "currentSyncId"
                    << SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId) << "syncId" << syncId
                    << "lostFrameCount()" << lostFrameCount();
#endif
        {
            QMutexLocker locker(&m_LostFrameMutex);
            m_LostFrame--;
        }
        return;
    }

    ImageEventArgs* imageEventArgs = m_LineImageArgs->imageEvnetArgs();

    uchar* curData = m_Image2DData;

#ifndef SYS_APPLE
    int expBLineNum = 0;
    QVariant density;
    switch (dataType)
    {
    case 0:
    case 4: // b of bbc
        // 过滤宽高不对应，多是设置参数和图像链路缓存中的尺寸不对应导致
        // TODO: info[9] 后续用统一的syncID赋值
        density = m_SonoParameters->pV(BFPNames::HighDensityStr);
        if (ProbeParameters::isSupportAnyDensity())
        {
            expBLineNum = ProbeParameters::lines(m_SonoParameters->pIV(BFPNames::StartLineStr),
                                                 m_SonoParameters->pIV(BFPNames::StopLineStr), density.toInt());
        }
        else
        {
            expBLineNum = ProbeParameters::lines(m_SonoParameters->pIV(BFPNames::StartLineStr),
                                                 m_SonoParameters->pIV(BFPNames::StopLineStr), density.toBool());
        }
        if (expBLineNum != info.info[9])
        {
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
            qCritical() << PRETTY_FUNCTION << "post process image size not match parameter:"
                        << "expLineNum =" << expBLineNum << "image size =" << info.info[9]
                        << "save index:" << info.info[8];
#endif
            return;
        }

        m_Image2DPushed = true;
        curData = m_Image2DData;
        imageEventArgs->setImageType(ImageEventArgs::ImageB);
#ifdef USE_IMGNETCLIENT
        if (Setting::instance().defaults().IsImgnetClient())
        {
            ::ultrasound_kind_image_send((uchar*)data, 3, width, height, 4);
        }
#endif
        if (Setting::instance().defaults().afterZeusSaveFlag())
        {
            QString filePath = QString("%1after_b/").arg(Resource::zeusContextDataSaverDir());
            Util::Mkdir(filePath.toStdString().c_str());
            m_ImageSaveHelper->saveImage32((char*)data, width, height, QString("%1%2_").arg(filePath).arg(info.info[8]),
                                           false, imageEventArgs->imageType());
        }
        break;
    case 1:
        //        log()->debug() << PRETTY_FUNCTION
        //                       << ", PW post process called!"
        //                       << ", width: " << width
        //                       << ", height: " << height;
        //        {
        //            QString filePath = QString("%1after_PW/").arg(Resource::zeusContextDataSaverDir());
        //            Util::Mkdir(filePath.toStdString().c_str());
        //            m_ImageSaveHelper->saveImage32((char*)data, width, height, filePath, false,
        //            imageEventArgs->imageType());
        //        }
        m_WavePushed = true;
        curData = m_WaveData;
        imageEventArgs->setImageType(ImageEventArgs::ImageD);
        if (m_SonoParameters->isRealTime())
        {
            if (Setting::instance().defaults().afterZeusSaveFlag())
            {
                QString filePath = QString("%1imagePW_1/").arg(Resource::zeusContextDataSaverDir());
                Util::Mkdir(filePath.toStdString().c_str());
                m_ImageSaveHelper->saveImage32((char*)data, width, height, filePath, false, ImageEventArgs::ImageD);
            }
            // 传递判断自动冻结的数据
            emit datainfo(data, width, height, ImageEventArgs::ImageD, 0);
        }
#ifdef USE_IMGNETCLIENT
        if (Setting::instance().defaults().IsImgnetClient())
        {
            ::ultrasound_kind_image_send((uchar*)data, 2, width, height, 4);
        }
#endif
        if (Setting::instance().defaults().afterZeusSaveFlag())
        {
            QString filePath = QString("%1after_d/").arg(Resource::zeusContextDataSaverDir());
            Util::Mkdir(filePath.toStdString().c_str());
            m_ImageSaveHelper->saveImage32((char*)data, width, height, filePath, false, imageEventArgs->imageType());
            QFile qfile(QString("%1wave_after.dat").arg(filePath));
            if (qfile.open(QIODevice::WriteOnly | QFile::Append))
            {
                qfile.write((const char*)data, width * height);
                qfile.close();
            }
        }
        break;
    case 2:
        m_WavePushed = true;
        curData = m_WaveData;
        imageEventArgs->setImageType(ImageEventArgs::ImageM);
        if (Setting::instance().defaults().afterZeusSaveFlag())
        {
            QString filePath = QString("%1after_m/").arg(Resource::zeusContextDataSaverDir());
            Util::Mkdir(filePath.toStdString().c_str());
            m_ImageSaveHelper->saveImage32((char*)data, width, height, filePath, false, imageEventArgs->imageType());
        }
        break;
    case 6:
        m_WavePushed = true;
        imageEventArgs->setImageType(ImageEventArgs::ImageECG);
        if (Setting::instance().defaults().afterZeusSaveFlag())
        {
            QString filePath = QString("%1after_ecg/").arg(Resource::zeusContextDataSaverDir());
            Util::Mkdir(filePath.toStdString().c_str());
            m_ImageSaveHelper->saveImage32((char*)data, width, height, filePath, false, imageEventArgs->imageType());
        }
        break;
    case 7:
        // qDebug() << "tjy111" << PRETTY_FUNCTION << "image data only B";
        imageEventArgs->setImageType(ImageEventArgs::ImageB);
        break;
    }
#endif
    qint64 frameStartTime = (((qint64)info.info[7] << 32) | static_cast<uint32_t>(info.info[6]));
    int frameIndex = info.info[1];
    int imageType = BitOperator::getBitValue(info.info[2], 16, 16);
    bool needUpdateForzonIndex = BitOperator::getBitValue(info.info[3], 1);
    bool postRawDataAttached = BitOperator::getBitValue(info.info[29], 9, 1);
    int postRawDataType = BitOperator::getBitValue(info.info[29], 10, 4);
    int frontIndex = (short)BitOperator::getBitValue(info.info[5], 0, 16);
    int imageRenderPartition = BitOperator::getBitValue(info.info[5], 16, 16);
    int cacheIndex = BitOperator::getBitValue(info.info[10], 0, 16);
    int imageBufferFrameIndex = info.info[11];

    int ecgend = BitOperator::getBitValue(info.info[2], 0, 16);
    //    qDebug() << "$$$$$$$$$$2222" << PRETTY_FUNCTION << "frameIndex:" << frameIndex << "datatype:" << dataType
    //             <<"cacheIndex"<<cacheIndex << "frontIndex" << frontIndex;

    setFrameIndex(getSystemScanMode(), frameIndex, cacheIndex, imageType);
    setFrontIndex(getSystemScanMode(), frontIndex, imageType);

    memcpy(curData, data, width * height * 4);
    imageEventArgs->setImageBufferFrameIndex(imageBufferFrameIndex);
    imageEventArgs->setLayout(m_Layout);
    imageEventArgs->setSystemScanMode(m_SystemScanMode);
    imageEventArgs->setImageData((uchar*)curData);
    imageEventArgs->setBitCount(32);
    imageEventArgs->setWidth(width);
    imageEventArgs->setHeight(height);
    imageEventArgs->setSyncId(syncId);
    imageEventArgs->setNeedUpdateForzonIndex(needUpdateForzonIndex);
    imageEventArgs->setDataType(dataType);
    imageEventArgs->setFrontIndex(frontIndex);
    imageEventArgs->setFrameStartTime(frameStartTime);
    imageEventArgs->setImageRenderPartition(imageRenderPartition);
    imageEventArgs->setECGEnd(ecgend);
    imageEventArgs->setMeasureImageReady(false);
    m_EcgDataHelper->getByteLineImageArgs()->imageEvnetArgs()->setNeedUpdateForzonIndex(needUpdateForzonIndex);
    m_EcgDataHelper->getByteLineImageArgs()->imageEvnetArgs()->setFrameStartTime(frameStartTime);

    // info[12] 现用于冻结/回调时，告知AI算法当前图像是最终界面刷新的图像
    if (info.info[12] == 1)
    {
        imageEventArgs->setMeasureImageReady(true);
    }

    //    qCritical()<<"bypass"<<BitOperator::getBitValue(info.info[3], 0, 1);
    if (!postRawDataAttached)
    {
        postRawDataType = -1;
    }

    imageEventArgs->setPostRawDataType(postRawDataType);
    if (dataType == 7)
    {
        emit newGrayImage(m_LineImageArgs);
    }
    else
    {
        if (dataType != 6)
        {
            emit newImage(m_LineImageArgs);
            emit newMeasureImage(m_LineImageArgs);
        }
        else
        {
            emit newImage(m_EcgDataHelper->getByteLineImageArgs());
            emit newMeasureImage(m_EcgDataHelper->getByteLineImageArgs());
        }
    }
}

void ZeusContext::onZeusNewProcessedData(void* UserData, CallbackInfo callbackInfo, InfoForPostProcess info, int syncId)
{
    ZeusContext* fakeThis = reinterpret_cast<ZeusContext*>(UserData);
    if (fakeThis != NULL)
    {
        fakeThis->onNewProcessedData(callbackInfo.Data, callbackInfo.Width, callbackInfo.Height, callbackInfo.DataType,
                                     callbackInfo.BitWidth, syncId, info);
    }
}
void ZeusContext::onZeusPostProcessedData(void* userData, void* data, int width, int height, InfoForPostProcess info)
{
    ZeusContext* fakeThis = reinterpret_cast<ZeusContext*>(userData);
    if (fakeThis != NULL)
    {
        fakeThis->onPostProcessedData(data, width, height, info);
    }
}
void ZeusContext::onPostProcessedGrayData(void* data, int width, int height, InfoForPostProcess info)
{
    if (m_IsNewImagePaused)
    {
        return;
    }

    //在冻结下，打开存储数据开关，要讲数据更新到sonobuffer，存图时存储到病例中
    if ((!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr)) &&
        Setting::instance().defaults().grayDataUponSTILSAVEFlag())
    {
        m_SonoBuffer->setGrayRawDataImage((uchar*)data, width, height);
    }

    if (m_SonoParameters->pBV(BFPNames::Save8BitBeforeColorMapEnableStr))
    {
        saveImage2DDataBeforeColorMap((char*)data, width, height);
        return;
    }
    int frameIndex = BitOperator::getBitValue(info.info[1], 16, 16);
    int cacheIndex = BitOperator::getBitValue(info.info[1], 0, 16);
    int imageType = BitOperator::getBitValue(info.info[2], 16, 16);

    setFrameIndex(getSystemScanMode(), frameIndex, cacheIndex, imageType);
    ImageEventArgs* imageEventArgs = m_LineImageArgs->imageEvnetArgs();
    imageEventArgs->setImageType(ImageEventArgs::ImageB);
    imageEventArgs->setLayout(m_Layout);
    imageEventArgs->setSystemScanMode(m_SystemScanMode);
    imageEventArgs->setImageData((uchar*)data);
    imageEventArgs->setBitCount(8);
    imageEventArgs->setWidth(width);
    imageEventArgs->setHeight(height);
    emit newGrayImage(m_LineImageArgs);
}

void ZeusContext::onZeusPostProcessedDataBeforeColorMap(void* userData, void* data, int width, int height,
                                                        InfoForPostProcess info)
{
    ZeusContext* fakeThis = reinterpret_cast<ZeusContext*>(userData);
    if (fakeThis != NULL)
    {
        fakeThis->onPostProcessedGrayData(data, width, height, info);
    }
}

void ZeusContext::enForceDSCColorMap()
{
    if (m_SonoParameters != NULL && (!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr)))
    {
        if (m_Image2DPushed || m_WavePushed)
        {
            m_PostProcessParaController->apiInterface()->setZeusParameter(
                m_ImageTileIndex, POSTPARANAME(ProcessSonoParameterIndex::DataSyncID),
                SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId), Dsc_Int);
            m_PostProcessParaController->apiInterface()->setZeusParameterTrigger(
                m_ImageTileIndex, POSTPARANAME(ProcessSonoParameterIndex::ColorMap2D) + "EnForce");
        }
    }
}

void ZeusContext::onEntryStressEchoAnalyze()
{
    m_IsInStressEchoAnalyzeState = true;
    SyncIDManager::instance().updateSyncId(SyncIDMode::PrePostSyncId);
    m_PostProcessParaController->setSyncId(SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId));
}

void ZeusContext::onExitStressEchoAnalyze()
{
    m_IsInStressEchoAnalyzeState = false;
    QMutexLocker locker(&m_ZeusHandleMutex);
    SyncIDManager::instance().updateSyncId(SyncIDMode::PrePostSyncId);
    m_PostProcessParaController->setSyncId(SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId));
    updateColorMap();
}

void ZeusContext::onPlayCineStatusChanged(const bool playCineStatus)
{
    m_IsLooping = playCineStatus;
    setPostProcessParaControllerEnforceEnable();
}

void ZeusContext::onExecParameterChanged()
{
    {
        IBufferReadLocker locker(m_SonoBuffer, PRETTY_FUNCTION);
    }
    {
        QMutexLocker locker(&m_ZeusHandleMutex);
        if (!m_PostProcessParaController->apiInterface()->isHandleValid(m_ImageTileIndex) ||
            (m_SonoParameters == NULL) || (m_ImageTileIndex >= m_SonoParameters->pIV(BFPNames::LayoutStr)))
        {
            return;
        }
        execParameterChangedInLock();
    }

    // 2023-06-12 Modify by AlexWang
    // 解决局部放大状态时图像区域图像不居中问题，关闭任意线密度使能。从MainModule::showMainWindow()移至此
    m_SonoParameters->setPV(BFPNames::AnyDensityEnStr, false, true);

    // 2023-06-20 Write by AlexWang [bug:65302][bug:65305] 加载完参数后向Zeus下发放大相关参数
    //    for (auto parametres : qAsConst(m_ZeusParameters))
    //    {
    //        parametres->onSetSononParametersComplete();
    //    }
}

void ZeusContext::execParameterChangedInLock()
{
#ifdef USE_IIMAGE
    m_iImageProcessFacade->Reset();
#endif
    bool isChange = true;
    if (m_SonoParameters->isRealTime() && Sync_D == (m_SonoParameters->pIV(BFPNames::SyncModeStr) & Sync_D) &&
        Sync_D == (m_SyncMode & Sync_D) && m_SystemScanMode != m_SonoParameters->pIV(BFPNames::SystemScanModeStr))
    { //只有在实时打图，扫查模式在BPW、CPW(或者CPA、DPD、TDI的PW)中切换时，且布局比例不变动，才不进行图像绘制布局的变更
        //因为布局变更时，目前会重建绘制Image，但若重建后的Image在激活时未及时通过图像刷新而更新，待切换为非激活时，Image便没有机会更新，导致绘制一直采用备份Image
        //目前图像绘制布局是靠syncId的方式在后续具体绘制时来控制，后续可重构为推送给Zeus数据时，可用传递控制参数的方式来
        //确定图像绘制布局的结果，即绘制布局同待绘制图像一起传入绘制，确保绘制的准确性
        isChange = false;
    }

    m_WavePushed = false;
    m_Image2DPushed = false;
    m_IsNewImagePaused = true;
    //为zeus库的参数赋值时，要遵循必要的原则，先常规功能，后特殊功能
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
    m_SyncMode = m_SonoParameters->pIV(BFPNames::SyncModeStr);
    m_SystemScanMode = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);
    int systemScanMode = (m_SystemScanMode == SystemScanModeAV ? SystemScanModeBPW : getSystemScanMode());
    m_Layout = m_SonoParameters->pIV(BFPNames::LayoutStr);
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION << "m_ImageTileIndex:" << m_ImageTileIndex
             << "m_SonoParameters:" << (quintptr)m_SonoParameters << "systemScanMode:" << systemScanMode
             << "layout:" << m_SonoParameters->pIV(BFPNames::LayoutStr)
             << "ScpdOn:" << m_SonoParameters->pBV(BFPNames::ScpdOnStr)
             << "isRealTime:" << m_SonoParameters->isRealTime();
#endif

    //    m_PostProcessParaController->flushSentPara();
    m_PostProcessParaController->controlSystemScanMode();
    m_PostProcessParaController->controlImageRects();
    m_PostProcessParaController->controlImageTilePara();
    updateColorMap(false);

    // C模式切换到B模式时，由于多个信号会触发setColorMap函数，但多次触发时设置的colormap值不相同，
    //导致偶发出现推送给zeus
    // B数据时colormap还不是B模式的colormap，此时采用更新syncid的方式，过滤掉colormap不正常时zeus返回的图像
    if (isChange)
    {
        //在execParameterChangedInLock执行过程中，m_SyncId在参数更新完成之后设值，确保只设值一次
        SyncIDManager::instance().updateSyncId(SyncIDMode::PrePostSyncId);
        m_PostProcessParaController->setSyncId(SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId));
    }

    if (systemScanMode == SystemScanModeE)
    {
        m_LineImageArgs->setImageType(ImageEventArgs::ImageElasto);
    }
    else if (SystemScanModeClassifier::isLikeC(m_SystemScanMode) && !SystemScanModeClassifier::isLikeD(systemScanMode))
    {
#ifndef SYS_APPLE
        m_LineImageArgs->setImageType(ImageEventArgs::ImageB);
#else
        m_LineImageArgs->setImageType(ImageEventArgs::ImageC);
#endif
    }
    else if (SystemScanModeClassifier::isLikeD(systemScanMode))
    {
        m_LineImageArgs->setImageType(ImageEventArgs::ImageD);
    }
    else if (SystemScanModeClassifier::isLikeM(systemScanMode))
    {
        m_LineImageArgs->setImageType(ImageEventArgs::ImageM);
    }
    else
    {
        m_LineImageArgs->setImageType(ImageEventArgs::ImageB);
    }
    if (!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        m_PostProcessParaController->apiInterface()->setZeusParameterTrigger(
            m_ImageTileIndex, POSTPARANAME(ProcessSonoParameterIndex::PersistenceReset));
    }
    if (!m_SonoParameters->pBV(BFPNames::PA_VERT_DIST_EnableStr)) //防止老的梯形成像将虚拟顶点梯形成像关闭
    {
        bool trapeEnable = probeDataInfo.IsLinear ? m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr)
                                                  : m_SonoParameters->pBV(BFPNames::CurvedExapandingStr);
        m_PostProcessParaController->apiInterface()->setZeusParameter(
            m_ImageTileIndex, POSTPARANAME(ProcessSonoParameterIndex::TrapezoidalMode), trapeEnable, Dsc_Bool);
    }

    m_IsNewImagePaused = false;
}

void ZeusContext::onTimeout()
{
    if (m_PostProcessParaController->apiInterface()->isHandleValid(m_ImageTileIndex))
    {
        m_PostProcessParaController->apiInterface()->setZeusParameter(m_ImageTileIndex, "EnableColorDisplay", true,
                                                                      Dsc_Bool);
    }
    m_SonoParameters->setPV(BFPNames::ColorLineChangingStr, 0);
}

void ZeusContext::resetPersistence()
{
    //    QMutexLocker locker(&m_ZeusHandleMutex);
    //    if(m_ZeusHandle != NULL)
    //    {
    //        setZeusParameterTrigger(POSTPARANAME(PersistenceReset));
    //    }
}

void ZeusContext::onGetSyncId(int& syncId)
{
    syncId = SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId);
}

// TODO:这个函数的本意是对指定参数赋值，但是由于部分参数需要强制刷新，直接把强制刷新的判断操作写到这里了，这样会增加所有参数的判断负担,后续考虑重构
void ZeusContext::onParameterValueChanged(const QString& name, const QVariant& value, const int type)
{
    QMutexLocker locker(&m_ZeusHandleMutex);
    if (m_Trigger->isTriggering() || m_IsNewImagePaused || m_SonoParameters == nullptr)
    {
        //由于超声参数变更与超声参数值变更在 ZeusContext 中存在独立控制的情况，
        //在beforeSonoParametersChanged中超声参数存在设置为空的环节，而原本的控制参数m_IsNewImagePaused
        //存在响应triggerCompleted信号时，被设置为false，导致控制失效，使用了超声参数为空的情况
        return;
    }
    if (m_PostProcessParaController->apiInterface()->isHandleValid(m_ImageTileIndex))
    {
        m_PostProcessParaController->apiInterface()->setZeusParameter(m_ImageTileIndex, name, value, type);
        //        qDebug() << PRETTY_FUNCTION
        //                 << "m_SonoParameters->isRealTime:" << m_SonoParameters->isRealTime()
        //                 << "FreezeStr:" << m_SonoParameters->pBV(BFPNames::FreezeStr)
        //                 << "m_IsLooping:" << m_IsLooping;
        if (m_ZeusEnForceParameterNames.contains(name) &&
            (!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr)))
        {
            if (!m_IsLooping || SystemScanModeClassifier::isLikeD(m_SystemScanMode))
            {
                if (m_Image2DPushed || m_WavePushed)
                {
                    m_PostProcessParaController->apiInterface()->setZeusParameter(
                        m_ImageTileIndex, POSTPARANAME(ProcessSonoParameterIndex::DataSyncID),
                        SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId), Dsc_Int);

                    QString paramName = name;

                    if (paramName.endsWith("Enable"))
                    {
                        paramName.remove(QRegExp("Enable$"));
                    }

                    m_PostProcessParaController->apiInterface()->setZeusParameterTrigger(m_ImageTileIndex,
                                                                                         paramName + "EnForce");
                }
            }
        }
    }
}

void ZeusContext::oncolorValueChanged()
{
#ifdef SYS_APPLE
    if (!m_SonoParameters->pIV(BFPNames::FreezeStr))
    {
        if (m_ZeusHandle != NULL)
        {
            setZeusParameter("EnableColorDisplay", false, ZeusParameterNames::Dsc_Bool);
        }
        if (m_SetColorActivateTimer->isActive())
        {
            m_SetColorActivateTimer->stop();
        }
        m_SetColorActivateTimer->setInterval(200);
        m_SetColorActivateTimer->start();
    }
#endif
}

void ZeusContext::onFreqSpectrumChanged(const QVariant& value)
{
    m_UsePWFrameIndex = value.toBool();
}
//三同步切换
void ZeusContext::onTriplexModeChanged(const QVariant& value)
{
    if (value.toBool())
    {
        m_UsePWFrameIndex = true;
    }
    else
    {
        if (m_SonoParameters != nullptr)
        {
            onFreqSpectrumChanged(m_SonoParameters->pV(BFPNames::FreqSpectrumStr));
        }
    }
}
//四同步切换
void ZeusContext::onQuadplexModeStrChanged(const QVariant& value)
{
    if (value.toBool())
    {
        m_UsePWFrameIndex = true;
    }
    else
    {
        if (m_SonoParameters != nullptr)
        {
            onTriplexModeChanged(m_SonoParameters->pV(BFPNames::TriplexModeStr));
        }
    }
}

void ZeusContext::onVAFunctionChanged(const QVariant& value)
{
    changeRawDataEnableReason(ENABLE_RAW_DATA_DOPPLER, value.toBool());
    m_PostProcessParaController->apiInterface()->setZeusParameter(m_ImageTileIndex, "BRawDataCallbackEnable",
                                                                  m_EnableRawData != 0, ZeusParameterNames::Dsc_Bool);
}

void ZeusContext::onSave8BitBeforeColorMapEnableChanged(const QVariant& value)
{
    changeRawDataEnableReason(ENABLE_RAW_DATA_SAVE_IMG, value.toBool());
    m_PostProcessParaController->apiInterface()->setZeusParameter(m_ImageTileIndex, "BRawDataCallbackEnable",
                                                                  m_EnableRawData != 0, ZeusParameterNames::Dsc_Bool);
}

void ZeusContext::onCPFunctionChanged(const QVariant& value)
{
    bool isRealTimeCP = m_SonoParameters->isRealTime() && value.toBool();
    changeRawDataEnableReason(ENABLE_RAW_DATA_CP, isRealTimeCP);
    m_PostProcessParaController->apiInterface()->setZeusParameter(m_ImageTileIndex, "BRawDataCallbackEnable",
                                                                  m_EnableRawData != 0, ZeusParameterNames::Dsc_Bool);
}

void ZeusContext::onColorMappedBDataCallbackEnableChanged(const QVariant& value)
{
    m_PostProcessParaController->apiInterface()->setZeusParameter(m_ImageTileIndex, "ColorMappedBDataCallbackEnable",
                                                                  value.toBool(), ZeusParameterNames::Dsc_Bool);
}

void ZeusContext::onIImageTypeChanged(const QVariant& value)
{
    m_IImageType = value.toInt();
}

void ZeusContext::onImageRenderLayoutsChanged(const QVariant& value)
{
    m_PostProcessParaController->controlImageRects();

    SyncIDManager::instance().updateSyncId(SyncIDMode::PrePostSyncId);
    m_PostProcessParaController->setSyncId(SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId));
}

void ZeusContext::onWaveImageScaleFactorChanged(const QVariant& value)
{
    QMutexLocker locker(&m_ZeusHandleMutex);
    QList<ZeusHashValue> zeusValues;
    if (m_SonoParameters == NULL)
    {
        return;
    }
    updateImageRects(zeusValues);
    m_PostProcessParaController->apiInterface()->setZeusParameter(m_ImageTileIndex, zeusValues);
}

void ZeusContext::onFpsUpdate(const QString& addinfo, float fpsV)
{
    emit fpsChanged(addinfo, fpsV);
}

void ZeusContext::onRequestFlush()
{
    emit requestFlush();
}

void ZeusContext::onNewImage(LineImageArgs* lineImageArgs)
{
    emit newImage(lineImageArgs);
}

void ZeusContext::connectSonoParameters()
{
    if (m_SonoParameters == NULL)
    {
        return;
    }
    onQuadplexModeStrChanged(m_SonoParameters->pV(BFPNames::QuadplexModeStr));
    onIImageTypeChanged(m_SonoParameters->pV(BFPNames::IImageTypeStr));
    onSave8BitBeforeColorMapEnableChanged(m_SonoParameters->pV(BFPNames::VAFunctionStr));
    connect(m_SonoParameters->parameter(BFPNames::WaveImageScaleFactorStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onWaveImageScaleFactorChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreqSpectrumChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onTriplexModeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::QuadplexModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onQuadplexModeStrChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::VAFunctionStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onVAFunctionChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::ColorMappedBDataCallbackEnableStr), SIGNAL(valueChanged(QVariant)),
            this, SLOT(onColorMappedBDataCallbackEnableChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::Save8BitBeforeColorMapEnableStr), SIGNAL(valueChanged(QVariant)),
            this, SLOT(onSave8BitBeforeColorMapEnableChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::CurvedPanoramicEnableStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onCPFunctionChanged(QVariant)));
    //    connect(m_SonoParameters->parameter(BFPNames::PanZoomSelectStr), SIGNAL(valueChanged(QVariant)),
    //            this, SLOT(onWaveImageScaleFactorChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::IImageTypeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onIImageTypeChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::ImageRenderLayoutsStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onImageRenderLayoutsChanged(QVariant)));
    foreach (const QString& name, PersistenceResetParameters)
    {
        connect(m_SonoParameters->parameter(name), SIGNAL(valueChanged(QVariant)), this, SLOT(resetPersistence()));
    }
#ifdef USE_IIMAGE
    m_iImageProcessFacade->ConnectSonoParameters(m_SonoParameters);
#endif
}

void ZeusContext::disconnectSonoParameters()
{
    if (m_SonoParameters == NULL)
    {
        return;
    }

    disconnect(m_SonoParameters->parameter(BFPNames::WaveImageScaleFactorStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onWaveImageScaleFactorChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onFreqSpectrumChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::TriplexModeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onTriplexModeChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::QuadplexModeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onQuadplexModeStrChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::VAFunctionStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onVAFunctionChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::ColorMappedBDataCallbackEnableStr), SIGNAL(valueChanged(QVariant)),
               this, SLOT(onColorMappedBDataCallbackEnableChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::Save8BitBeforeColorMapEnableStr), SIGNAL(valueChanged(QVariant)),
               this, SLOT(onSave8BitBeforeColorMapEnableChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::CurvedPanoramicEnableStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onCPFunctionChanged(QVariant)));
    //    disconnect(m_SonoParameters->parameter(BFPNames::PanZoomSelectStr), SIGNAL(valueChanged(QVariant)),
    //               this, SLOT(onWaveImageScaleFactorChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::IImageTypeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onIImageTypeChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::ImageRenderLayoutsStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onImageRenderLayoutsChanged(QVariant)));
    foreach (const QString& name, PersistenceResetParameters)
    {
        disconnect(m_SonoParameters->parameter(name), SIGNAL(valueChanged(QVariant)), this, SLOT(resetPersistence()));
    }
#ifdef USE_IIMAGE
    m_iImageProcessFacade->DisconnectSonoParameters(m_SonoParameters);
#endif
}

void ZeusContext::setFrameIndex(const int systemScanMode, const int frameIndex, const int imageType)
{
    if (SystemScanModeClassifier::isLikeM(systemScanMode))
    {
        if (imageType == PostImageType_M || imageType == PostImageType_CM)
        {
            m_LineImageArgs->imageEvnetArgs()->setFrameIndex(frameIndex);
        }
    }
    else if (SystemScanModeClassifier::isLikeD(systemScanMode))
    {
        if (m_UsePWFrameIndex)
        {
            if ((imageType == PostImageType_D))
            {
                m_LineImageArgs->imageEvnetArgs()->setFrameIndex(frameIndex);
            }
        }
        else
        {
            if ((imageType == PostImageType_B))
            {
                m_LineImageArgs->imageEvnetArgs()->setFrameIndex(frameIndex);
            }
        }
    }
    else
    {
        if ((imageType == PostImageType_B))
        {

            m_LineImageArgs->imageEvnetArgs()->setFrameIndex(frameIndex);
        }
    }
}

void ZeusContext::setFrontIndex(const int systemScanMode, const int frontIndex, const int imageType)
{
    if ((SystemScanModeClassifier::isLikeM(systemScanMode)) || SystemScanModeClassifier::isLikeD(systemScanMode))
        return;
    if ((imageType == PostImageType_B))
    {
        m_LineImageArgs->imageEvnetArgs()->setFrontIndex(frontIndex);
    }
}

void ZeusContext::setFrameIndex(const int systemScanMode, const int frameIndex, const int cacheIndex,
                                const int imageType)
{
    setFrameIndex(systemScanMode, frameIndex, imageType);
#ifdef USE_VA
    m_LineImageArgs->imageEvnetArgs()->setCacheIndex(cacheIndex);
#endif
}

void ZeusContext::updateImageRects(QList<ZeusHashValue>& zeusValues)
{
    QSize imageSize;
    QList<QRect> imageRects;
    bool force = false;
    int layout = m_SonoParameters->pIV(BFPNames::LayoutStr);
    if ((layout == Layout_1x2 || m_SonoParameters->pBV(BFPNames::BCImagesOnStr)) &&
        m_SonoParameters->pBV(BFPNames::PanZoomSelectStr))
    {
        force = true;
    }
    imageSize = m_SonoParameters->pV(BFPNames::RenderImageSizeStr).toSize();
    if (Layout_2x2 == layout)
    {
        imageSize.setHeight(imageSize.height() / 2);
        imageSize.setWidth(imageSize.width() / 2);
    }
    ZeusHashValue zeusHashValue;

    zeusHashValue.m_Value = imageSize.width();
    zeusHashValue.m_Type = Dsc_Int;
    zeusHashValue.m_Size = 0;
    zeusHashValue.m_Name = POSTPARANAME(ProcessSonoParameterIndex::DisplayWidth);
    zeusValues.append(zeusHashValue);
    zeusHashValue.m_Value = imageSize.height();
    zeusHashValue.m_Name = POSTPARANAME(ProcessSonoParameterIndex::DisplayHeight);
    zeusValues.append(zeusHashValue);
#ifdef SYS_APPLE
    QList<QVariant> listVariant = m_SonoParameters->pV(BFPNames::ImageRectsStr).toList();
    imageRects = VariantUtil::variant2Rects(listVariant);
    if (SystemScanModeClassifier::isLikeD(m_SonoParameters->pIV(BFPNames::SystemScanModeStr)))
    {
        zeusHashValue.m_Value = 256;
    }
    else
    {
        zeusHashValue.m_Value = m_SonoParameters->pV(BFPNames::ImageSizeStr).toSize().height();
    }
    zeusHashValue.m_Name = POSTPARANAME(WavePointNum);
    zeusValues.append(zeusHashValue);
    zeusHashValue.m_Name = POSTPARANAME(WaveLineNum);
    zeusHashValue.m_Value = imageSize.width();
    zeusValues.append(zeusHashValue);
#else
    int imageHeight = m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize().height();
    ImageScale imageScale = m_SonoParameters->pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>();
    if (force)
    {
        imageScale.xScale = 1.0;
        imageScale.yScale = 1.0;
    }
#ifdef ZEUS_ADAPTER_DEBUG_ENABLE
    qDebug() << "imageScale.xScale" << imageScale.xScale << "imageScale.yScale" << imageScale.yScale;
#endif
    imageHeight = qCeil(imageScale.yScale * imageHeight);

    BFDSCScanAreaWidthParameter bfDSCScanAreaWidthParameter(m_SonoParameters);
    int renderWidth = qCeil(bfDSCScanAreaWidthParameter.renderImageWidth());
    if (getSystemScanMode() != SystemScanModeCP)
    {
        renderWidth =
            qBound(qCeil(m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize().width() * imageScale.yScale),
                   renderWidth, m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize().width());
    }

    //多布局的情况下，上下布局因为图像本身已经被缩小，所以b图像y方向要乘以布局系数
    imageRects.append(QRect(0, 0, renderWidth, imageHeight));

    QList<QVariant> dscImageRects = m_SonoParameters->pV(BFPNames::DSCImageRectsStr).toList();
    QRect dscImageRect = dscImageRects.last().toRect();
    QSize dscImageSize = dscImageRect.size();

    imageHeight = dscImageSize.height();
    imageScale = m_SonoParameters->pV(BFPNames::WaveImageScaleFactorStr).value<ImageScale>();
    if (force)
    {
        imageScale.xScale = 1.0;
        imageScale.yScale = 1.0;
    }
    imageHeight = qCeil(imageScale.yScale * imageHeight);
    if (m_SonoParameters->pBV(BFPNames::BCImagesOnStr) && (getSystemScanMode() != SystemScanModeE))
    {
        imageRects.append(QRect(0, 0, renderWidth, imageHeight));
    }
    else
    {
        imageRects.append(QRect(0, 0, qCeil(dscImageSize.width() * imageScale.xScale), imageHeight));
    }
    if (SystemScanModeClassifier::isLikeD(m_SonoParameters->pIV(BFPNames::SystemScanModeStr)) ||
        m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanModeAV)
    {
#ifdef USE_PREPROCESS
        zeusHashValue.m_Value = 256; // PW前处理按照16bit方式处理，每个线固定返回256点，每个店16bit
#else
        zeusHashValue.m_Value = m_SonoParameters->pIV(BFPNames::PointNumPerLineStr);
#endif
    }
    else
    {
        zeusHashValue.m_Value = m_SonoParameters->pIV(BFPNames::PointNumPerLineStr);
    }
    zeusHashValue.m_Name = POSTPARANAME(ProcessSonoParameterIndex::WavePointNum);
    zeusValues.append(zeusHashValue);
    zeusHashValue.m_Name = POSTPARANAME(ProcessSonoParameterIndex::WaveLineNum);
    zeusHashValue.m_Value = imageRects.last().width();
    zeusValues.append(zeusHashValue);
#endif
    int index = 1;
    foreach (const QRect& imageRect, imageRects)
    {
        zeusHashValue.m_Name = POSTPARANAMEARG(ProcessSonoParameterIndex::Layout$StartX).arg(index);
        zeusHashValue.m_Value = imageRect.x();
        zeusValues.append(zeusHashValue);
        zeusHashValue.m_Name = POSTPARANAMEARG(ProcessSonoParameterIndex::Layout$StartY).arg(index);
        zeusHashValue.m_Value = imageRect.y();
        zeusValues.append(zeusHashValue);
        zeusHashValue.m_Name = POSTPARANAMEARG(ProcessSonoParameterIndex::Layout$Width).arg(index);
        zeusHashValue.m_Value = imageRect.width();
        zeusValues.append(zeusHashValue);
        zeusHashValue.m_Name = POSTPARANAMEARG(ProcessSonoParameterIndex::Layout$Height).arg(index);
        zeusHashValue.m_Value = imageRect.height();
        zeusValues.append(zeusHashValue);
        ++index;
    }
}

void ZeusContext::updateColorMap(bool isChangeSyncID)
{
    uchar* image2DMap =
        m_IsInStressEchoAnalyzeState ? m_StressImage2DMap : m_SonoBuffer->isLoad() ? m_LoadImage2DMap : m_Image2DMap;
    uchar* imageWaveMap =
        m_IsInStressEchoAnalyzeState ? m_StressWaveMap : m_SonoBuffer->isLoad() ? m_LoadWaveMap : m_WaveMap;

    m_PostProcessParaController->controlColorMap(image2DMap, imageWaveMap);

    if (Setting::instance().defaults().colorMapLog())
    {
        Util::Mkdir("updateColorMap");
        QFile qfile1(QString("updateColorMap/colormap1.dat"));
        if (qfile1.open(QIODevice::WriteOnly))
        {
            qfile1.write((const char*)(image2DMap), 256 * 4);
            qfile1.close();
        }
        QFile qfile2(QString("updateColorMap/colormap2.dat"));
        if (qfile2.open(QIODevice::WriteOnly))
        {
            qfile2.write((const char*)(imageWaveMap), 256 * 4);
            qfile2.close();
        }
    }
}

void ZeusContext::createHandle(int imageWidth, int imageHeight)
{
    if (!m_PostProcessParaController->apiInterface()->postProcessFlag(m_ImageTileIndex))
    {
        m_PostProcessParaController->apiInterface()->setZeusPostProcessDataCallBack(m_ImageTileIndex,
                                                                                    onZeusNewProcessedData, this);
        m_PostProcessParaController->apiInterface()->SetZeusExternalFunction(
            m_ImageTileIndex, onZeusPostProcessedData, ZeusExternalFunctionType::ExternalOptimation, this);
#ifdef SYS_APPLE
#else
        m_PostProcessParaController->apiInterface()->SetZeusExternalFunction(
            m_ImageTileIndex, onZeusPostProcessedDataBeforeColorMap, ZeusExternalFunctionType::RawDataForB, this);
#endif
        m_PostProcessParaController->apiInterface()->setPostProcessFlag(m_ImageTileIndex, true);
    }
    execParameterChangedInLock();
}

ZeusContext::ImageTypeAndIndex ZeusContext::calcDataPostProcessIndex(const FrameInfo& frameInfo)
{
    ImageTypeAndIndex imageTypeAndIndex;
    imageTypeAndIndex.Width = frameInfo.pointNum();
    imageTypeAndIndex.Height = frameInfo.lineNum();

    switch (frameInfo.mode())
    {
    case ImageBufferDef::B_Data:
        imageTypeAndIndex.ImageType = PostImageType_B;
        imageTypeAndIndex.Offset = 0;
        break;
    case ImageBufferDef::C_Data:
        imageTypeAndIndex.ImageType = PostImageType_C;
        imageTypeAndIndex.Offset = 1;
        break;
    case ImageBufferDef::Elasto_Data:
        imageTypeAndIndex.ImageType = PostImageType_E;
        imageTypeAndIndex.Offset = 1;
        break;
    case ImageBufferDef::D_Data:
        imageTypeAndIndex.ImageType = PostImageType_D;
        imageTypeAndIndex.Offset = 0;
#ifdef USE_PREPROCESS
        imageTypeAndIndex.Width = 256; // PW前处理按照16bit方式处理，每个线固定返回256点，每个店16bit
#endif
        break;
    case ImageBufferDef::CWD_Data:
        imageTypeAndIndex.ImageType = PostImageType_D;
        imageTypeAndIndex.Offset = 0;
#ifdef USE_PREPROCESS
        //        imageTypeAndIndex.Width = imageTypeAndIndex.Width / 2;  //SonoAir上 CWD_Data 暂定同 D_Data
        imageTypeAndIndex.Width = 256; // Atom
#endif
        break;
    case ImageBufferDef::M_Data:
        imageTypeAndIndex.ImageType = PostImageType_M;
        imageTypeAndIndex.Offset = 0;
        break;
    case ImageBufferDef::CM_Data:
        imageTypeAndIndex.ImageType = PostImageType_CM;
        imageTypeAndIndex.Offset = 0;
        break;
    case ImageBufferDef::DynFlow_Data:
        imageTypeAndIndex.ImageType = PostImageType_E;
        imageTypeAndIndex.Offset = 2;
        break;
    case ImageBufferDef::ECG_Data:
        imageTypeAndIndex.ImageType = PostImageType_ECG;
        imageTypeAndIndex.Offset = 0;
        // imageTypeAndIndex.Height = 1024;
        break;
    default:
        Q_ASSERT(false);
    }

    Q_ASSERT((imageTypeAndIndex.Offset >= 0) && (imageTypeAndIndex.Offset < DATA_POST_PROCESS_INFO_MAX_COUNT));
    return imageTypeAndIndex;
}

bool ZeusContext::validSyncID(const int SyncID) const
{
    if (SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId) == SyncID)
    {
        return true;
    }
    return false;
}

void ZeusContext::saveImage2DDataBeforeColorMap(char* data, int width, int height)
{
    if (m_SonoParameters != NULL && width > 0 && height > 0)
    {
        QString filePath = QString("%1before_colormap/").arg(Resource::zeusContextDataSaverDir());
        Util::Mkdir(filePath.toStdString().c_str());
        m_ImageSaveHelper->saveImage8AndHex(data, width, height, filePath);
    }
}

int ZeusContext::getSystemScanMode()
{
    if (m_SonoParameters != NULL)
    {
        if (m_SystemScanMode == SystemScanModeAV)
        {
            return SystemScanModeB;
        }
        else if (m_SystemScanMode == SystemScanModeCP)
        {
            return SystemScanModeB;
        }
        else if (m_SystemScanMode == SystemScanModeSonoNeedle)
        {
            return SystemScanModePowerDoppler;
        }
        else
        {
            return m_SystemScanMode;
        }
    }
    return -1;
}

void ZeusContext::changeRawDataEnableReason(int reason, bool enable)
{
    if (enable)
    {
        m_EnableRawData |= reason;
    }
    else
    {
        m_EnableRawData &= ~reason;
    }
}

void ZeusContext::setImage2DPushed(bool push)
{
    m_Image2DPushed = push;
    setPostProcessParaControllerEnforceEnable();
}

void ZeusContext::setImageWavePushed(bool push)
{
    m_WavePushed = push;
    setPostProcessParaControllerEnforceEnable();
}

void ZeusContext::setPostProcessParaControllerEnforceEnable()
{
    //    m_PostProcessParaController->setEnforceEnable(!m_IsLooping && (m_Image2DPushed || m_WavePushed));
}
