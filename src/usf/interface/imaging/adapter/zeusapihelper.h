#ifndef ZEUSAPIHELPER_H
#define ZEUSAPIHELPER_H

#include "zeusadapter_global.h"
#include "zeusinfostruct.h"
#include <QObject>
#include <QVariant>

class ISonoBuffer;
class SonoParameters;
class DscParameter;
class PairParameterName;
class ByteBuffer;

#define COLORMAP_BUFFER_SIZE 1024

struct ZeusHashValue
{
    ZeusHashValue();
    ZeusHashValue(QString name, QVariant value, int type, int size);
    QString m_Name;
    QVariant m_Value;
    int m_Type;
    int m_Size;
};

/*
   zeus接口封装类，统一维护多布局的zeusHandle，对外提供zues接口服务
*/
class USF_INTERFACE_IMAGING_ZEUSADAPTER_EXPORT ZeusAPIInterface : public QObject
{
    Q_OBJECT
public:
    explicit ZeusAPIInterface(QList<ISonoBuffer*> iSonoBuffers, bool IsSupportPreProcess = true);
    ~ZeusAPIInterface();

    //    QVariant lastValue(int bufferIndex, QString paraName) const;
    //    void setLastValue(int bufferIndex, const ZeusHashValue& value);
    void setSonoParameters(SonoParameters* sonoParameters);
    bool setZeusParameter(int bufferIndex, const QList<ZeusHashValue>& zeusValues);
    bool setZeusParameter(int bufferIndex, const QString& zeusName, const QVariant value, const int type,
                          const int size = 0);
    bool setZeusParameterTrigger(int bufferIndex, const QString& zeusName);

    bool setZeusPreProcessDataCallBack(int bufferIndex, PreProcessDataCallBack callback, void* userData);
    bool setZeusPostProcessDataCallBack(int bufferIndex, ZeusPostProcessDataCallBack callback, void* userData);
    bool SetZeusPostProcessParaCallBack(int bufferIndex, PostProcessParaCallBack callback, void* userData);
    bool SetZeusPostProcessPropCallBack(int bufferIndex, PostProcessPropCallBack callback, void* userData);

    //根据ZeusExternalFunctionType 设置zeus外部函数
    bool SetZeusExternalFunction(int bufferIndex, ExternalFunction function, ZeusExternalFunctionType functionType,
                                 void* userData);
    bool pushDataForPreProcess(int bufferIndex, DataForPreProcess* data, int dataType, int dataCount,
                               InfoForPostProcess* info);
    bool pushDataForPostProcess(int buffferIndex, DataForPostProcess* data, int dataType, int dataCount,
                                InfoForPostProcess info);

    bool isHandleValid(int bufferIndex) const;

    void setPostProcessFlag(int bufferIndex, bool flag);
    void setPreProcessFlag(int bufferIndex, bool flag);
    bool postProcessFlag(int bufferIndex) const;
    bool preProcessFlag(int bufferIndex) const;

    // tianyi todo
    void setZeusParameterPointer(int bufferIndex, const QString& zeusName, const void* value, int size);

    // for test
    void exportProperties(int bufferIndex, const QString& dir);

#ifdef USE_RDMA
    quint64 allocCudaAddress(int bufferIndex, size_t buffsize = 512 * 1024 * 1024);
#endif

    void* zeusCudaAddr() const;

    //    void reflushParas(int bufferIndex);

    static QStringList EnForceParameterNames;

protected:
    void createHandle();
    void setZeusParameterInt(int bufferIndex, const QString& zeusName, const int value);
    void setZeusParameterFloat(int bufferIndex, const QString& zeusName, const float value);
    void setZeusParameterBool(int bufferIndex, const QString& zeusName, const bool value);
    //    void setZeusParameterPointer(int bufferIndex, const QString &zeusName, const void *value, int size);
private:
    struct HandleInfo
    {
        HandleInfo()
            : postProcessFlag(false)
            , preProcessFlag(false)
            , handle(nullptr)
        {
        }

        bool postProcessFlag;
        bool preProcessFlag;
        void* handle;
    };
    SonoParameters* m_SonoParameters;
    QList<ISonoBuffer*> m_SonoBufferList;
    QHash<int, HandleInfo> m_ZeusHanles;
    //    QHash<int, QHash<QString, ZeusHashValue> > m_ZeusValueLists;
    void* m_ZeusCudaAddr;
    bool m_IsSupportPreProcess;
};

#endif // ZEUSAPIHELPER_H
