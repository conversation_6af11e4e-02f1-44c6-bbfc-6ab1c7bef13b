add_definitions(-DUSF_INTERFACE_IMAGING_ZEUSADAPTER_LIBRARY)
#add_definitions(-DABSTRACT_DSC_ADAPTER_DEBUG_ENABLE)
#add_definitions(-DZEUS_ADAPTER_DEBUG_ENABLE)
#add_definitions(-DMEMORY_LEAK_CHECK)

include_depends(usf.i.imaging.datatransfer usf.i.imaging.imgcomunit usf.com.core.utilitymodel usf.i.imaging.buffer gdr)

include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${ZEUS_INCLUDE_DIR} ${PAML_IIMAGE_INCLUDE_DIRS} ${IMGNETCLIENT_INCLUDE_DIR} ${AUTOFREEZE_INCLUDE_DIR} ${GDR_INCLUDE_DIR})

if(BUILD_SYS STREQUAL "windows")
    set(resourcehelper resourcehelper_windows.cpp)
    #Android API does not use glew.h and glut.h header files. All related functions are commented out
elseif(BUILD_SYS STREQUAL "android")
    set(resourcehelper resourcehelper_android.cpp)
elseif(BUILD_SYS STREQUAL "unix")
    set(resourcehelper resourcehelper_unix.cpp)
endif()

set(src
    zeuspipeline.cpp
    zeusimagetile.cpp
    zeuscontext.cpp
    zeusmappinghelper.cpp
    zeusapihelper.cpp
    paracontrol/postprocessparacontroller.cpp
    paracontrol/preprocessparacontroller.cpp
    abstractdscpipeline.cpp
    abstractdscimagetile.cpp
    dscthread.cpp
    imagedebughelper.cpp
    dscecgdataprocesshelper.cpp
    abstractdscparametertrigger.cpp
    zoomparametertrigger.cpp
)

add_library_qt(usf.i.imaging.adapter ${src} ${resourcehelper})

target_link_libraries(usf.i.imaging.adapter
    usf.i.imaging.datatransfer
    usf.i.imaging.imgcomunit
    usf.com.core.utilitymodel
    usf.i.imaging.buffer
    ${ZEUS_LIBRARIES}
    ${PAML_IIMAGE_LIBRARIES}
    ${IMGNETCLIENT_LIBRARIES}
    ${AUTOFREEZE_LIBRARIES}
    ${GLUT_LIBRARIES}
    )

add_custom_command(TARGET usf.i.imaging.adapter
                    POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_directory ${ZEUS_ROOT}${THIRDPARTYLIB_NAME} ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
                    )

if(${USE_IMGNETCLIENT})
   add_custom_command(TARGET usf.i.imaging.adapter
                      POST_BUILD
                      COMMAND ${CMAKE_COMMAND} -E copy_directory ${IMGNETCLIENT_ROOT}${THIRDPARTYLIB_NAME} ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
                      )
endif(${USE_IMGNETCLIENT})

if(${USE_AUTOFREEZE})
    add_custom_command(TARGET usf.i.imaging.adapter
                    POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_directory ${AUTOFREEZE_RUNTIMELIB_PATH}/ ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
                    )
endif()

if(NOT BUILD_TAR STREQUAL "aarch" AND NOT BUILD_TAR STREQUAL "arm")
    add_custom_command(TARGET usf.i.imaging.adapter
                       POST_BUILD
                       COMMAND ${CMAKE_COMMAND} -E copy_directory ${GLUT_ROOT}${THIRDPARTYLIB_NAME} ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
                       )
endif()

if(WIN32)
    target_link_libraries(usf.i.imaging.adapter ${GDR_LIBRARIES})
    add_custom_command(TARGET usf.i.imaging.adapter
                       POST_BUILD
                       COMMAND ${CMAKE_COMMAND} -E copy_directory ${GDR_RUNTIMELIB_PATH} ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
                       )
endif()
