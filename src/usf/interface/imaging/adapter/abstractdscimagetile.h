#ifndef ABSTRACTDSCIMAGETILE_H
#define ABSTRACTDSCIMAGETILE_H

#include "zeusadapter_global.h"
#include "chisonultrasoundtypes.h"
#include "bufferunit.h"
#include "frameinfo.h"
#include <QObject>
#include <QReadWriteLock>
#include <QPair>
#include <QMutex>
#include <QQueue>
#include <QTimer>
#include <QHash>
#include <QAtomicInt>

class ISonoBuffer;
class IBufferPushControler;
class SonoParameters;
class LineImageArgs;
class ImageDebugHelper;
class FourDVolumeLineImageArgs;

/**
 * @brief The AbstractDSCImageTile class
 *
 *  DSCImageTile, SonoBuffer, BufferPushControler一一对应
 */
class USF_INTERFACE_IMAGING_ZEUSADAPTER_EXPORT AbstractDSCImageTile : public QObject
{
    Q_OBJECT
public:
    explicit AbstractDSCImageTile(const int imageTileIndex, IBufferPushControler* iBufferPushControler,
                                  ChisonUltrasound::ChisonUltrasoundMode type, QObject* parent = NULL);
    ~AbstractDSCImageTile();

    void initialize();
    /**
     * @brief start
     * 启动模块运行
     */
    void start();
    void stop();
    /**
     * @brief haltProcessImageChanged
     * 在实时冻结的时候，由于onImageChanged是在阻塞线程中执行的，可能会有很多信号没有执行到
     * 在冻结的时候不希望这些信号在继续执行
     */
    void haltProcessImageChanged(const int frozenIndex);
    void recoveryProcessImageChanged();
    /**
     * @brief clearFrozenIndex 清空索引标记
     */
    void clearFrozenIndex();

    void firstInit();

    virtual void setColorMap(uchar* image2DMap, uchar* waveMap, int activeIndex);

    virtual void onEntryStressEchoAnalyze();

    virtual void onExitStressEchoAnalyze();

    virtual bool enForceDSCImage();

    virtual void clearThreadData(int count);

    virtual bool isImage2DPushed() const;

    virtual void setEnforceEnable(bool state);
public slots:
    virtual void onPlayCineStatusChanged(const bool playCineStatus);

signals:
    /*
     * colormap之前的数据
     */
    void newGrayImage(LineImageArgs* lineImageArgs);
    /**
     * @brief newImage 处理完的数据出口
     * @param lineImageArgs
     */
    void newImage(LineImageArgs* lineImageArgs);

    /**
     * @brief rawDataInfo 优化之前的数据
     */
    void rawDataInfo(void* data, int width, int height, int bitCount, int layoutIndex);

    void newMeasureImage(LineImageArgs* lineImageArgs);
    /**
     * @brief beforePipelineStop 内部处理线程销毁/重建
     */
    void beforePipelineStop(int imageTileIndex);
    void pipelineCreated(int imageTileIndex);

    void startDeliverPushTimer();
    void stopDeliverPushTimer();

    void fpsChanged(const QString& name, const float fps);
    void datainfo(void* data, int width, int height, int imageType, int frameSteerType);
    void imageDataReady();
#ifdef USE_4D
    void newFourDImage(FourDVolumeLineImageArgs* volumeImageArgs);
#endif

    void flushedAllImages();

signals:
    void requestFlush();

private slots:
    void onFlushedAllImages();

    void onImageChangedDirectly(const QList<BufferUnit>& bufferUnits, const FrameUnitInfo& frameUnitInfo);

    void onDeliverPushTimerTimeout();
    /**
     * 间接调用onBeforeParamChanged
     * 在监控的超声参数发生变化的时候，在参数变化之前先执行onBeforeParamChangedDirectly，
     * 变化后再执行onParamChanged
     * */
    void onBeforeParamChangedDirectly(const QVariant& oldValue, QVariant& newValue);

    void onParamChangedDirectly(const QVariant& value, bool changed);
    /**
     * 超声参数指针发生变化之前，在这里需要释放掉之前的超声参数的信号连接以及依据之前
     * 超声参数建立的pipeline
     * */
    void onBeforeSonoParametersChangedDirectly();
    /**
     * 超声参数指针发生变化，在这里建立pipeline
     * */
    void onSonoParameterChangedDirectly();
    /**
     * @brief onClearSonoParametersDirectly
     * 清空超声参数
     */
    //    void onClearSonoParametersDirectly();
    /**
     * @brief onBeforeBuffersChangedDirectly
     * 数据缓存区销毁之前执行
     */
    void onBeforeBuffersChangedDirectly(int sonobufferIndex);
    /**
     * @brief onBuffersChangedDirectly
     * 数据缓存区已经建立，可以使用
     */
    void onBuffersChangedDirectly(int sonobufferIndex);

    /**
     * @brief onBeforeBuffersChangedDirectly
     * 数据缓存区销毁之前执行
     */
    void onBeforeBuffersChangedDirectly();
    /**
     * @brief onBuffersChangedDirectly
     * 数据缓存区已经建立，可以使用
     */
    void onBuffersChangedDirectly();

    void onCacheQueueCleardDirectly(const int imageBufferIndex);
    /**
     * @brief onLineImageBufferChangedDirectly
     * 宽景的缓存区建立
     */
    void onLineImageBufferChangedDirectly();

    void onFpsUpdate(const QString& addinfo, float fpsV);

    void onLineImageDebugChanged(const QVariant& value);

    void onNewImage(LineImageArgs* lineImageArgs);
    void onNewMeasureImage(LineImageArgs* lineImageArgs);

protected:
    void haltProcessImageChangedInner(const int frozenIndex);

    void recoveryProcessImageChangedInner();

    void connectSonoParameters();

    void disConnectSonoParameters();

    virtual bool onImageChanged(const QList<BufferUnit>& bufferUnits, const FrameUnitInfo& frameUnitInfo) = 0;

    virtual void onClearFrozenIndex() = 0;
    /**
     * @brief onStart 开始/停止
     */
    virtual void onStart() = 0;
    virtual void onStop() = 0;
    /**
     * @brief startInternal 内部子模块开始/停止
     */
    virtual void startInternal() = 0;
    virtual void stopInternal() = 0;
    /**
     * @brief haltProcessImageChanged 图形处理暂定/恢复
     */
    virtual void onHaltProcessImageChanged() = 0;
    virtual void onRecoveryProcessImageChanged() = 0;
    /**
     * 参数值发生变化之前执行
     */
    Q_INVOKABLE virtual void onBeforeParamChanged() = 0;
    /**
     * 参数值发生变化之后执行
     */
    Q_INVOKABLE virtual void onParamChanged(unsigned int counter) = 0;
    /**
     * 超声参数切换之前执行
     */
    Q_INVOKABLE virtual void onBeforeSonoParametersChanged() = 0;
    /**
     * 超声参数切换之后执行
     */
    Q_INVOKABLE virtual void onSonoParametersChanged() = 0;
    /**
     * 清除超声参数
     */
    //    Q_INVOKABLE virtual void onClearSonoParametersChanged() = 0;
    /**
     * 处理宽景新增LineBuffer之后执行
     */
    //    Q_INVOKABLE virtual void onlineImageBufferChanged() = 0;

    Q_INVOKABLE virtual void onCacheQueueCleard();

protected:
    /**
     * @brief m_ImageTileIndex 区域索引
     */
    int m_ImageTileIndex;
    /**
     * @brief m_IBufferPushControler 数据推送线程
     */
    IBufferPushControler* m_IBufferPushControler;
    /**
     * @brief m_SonoBuffer 数据缓存
     */
    ISonoBuffer* m_SonoBuffer;
    /**
     * @brief 同步主线程与DSCThread线程
     */
    QReadWriteLock m_LockInMainAndDSCThread;

    SonoParameters* m_SonoParameters;
    /**
     * @brief m_WaitingSignalCounter 超声参数变化计数
     */
    int m_WaitingSignalCounter;
    /**
     * @brief m_SonoParametersChanging 超声参数处于变化中
     */
    bool m_SonoParametersChanging;
    /**
     * @brief m_IsSonoParameterCleared 超声参数是否被清除
     */
    bool m_IsSonoParameterCleared;
    /**
     * @brief m_SonoparametersChangedCounter 超声参数被清空计数器
     */
    unsigned int m_SonoparametersChangedCounter;
    /**
     * @brief 是否禁用参数变化时的响应函数
     *        需要param的onBeforeChanged和valueChanged信号平衡
     *        因此连接参数过程中不可响应信号，否则难以平衡
     */
    bool m_BlockingChangedSlot;
    /**
     * @brief m_FrozenIndex
     */
    int m_FrozenIndex;
    /**
     * 标记内部子模块是否处于停止状态
     * */
    bool m_Stopping;
    /**
     * 标记模块是否处于运行状态
     * */
    bool m_StartIng;
    /**
     * @brief m_IsNewImagePaused
     * 暂停处理新数据
     */
#ifdef SYS_APPLE
    bool m_IsNewImagePaused;
#else
    QAtomicInt m_IsNewImagePaused;
#endif
    /**
     * 数据分发结构
     */
    typedef QPair<QList<BufferUnit>, FrameUnitInfo> FrameBufferUnitInfo;
    QMutex m_DeliverBufferMutex;
    QHash<int, QQueue<FrameBufferUnitInfo>> m_DeliverBuffer;
    QHash<int, QQueue<FrameBufferUnitInfo>> m_DeliverBufferOld;
    QTimer* m_DeliverPushTimer;
    // tianyi 临时修改，方便帧相关控制推的最大帧数，这个等调试版本确定后再来修改这个值
    //    const static int m_DeliverBufferCount = 3;
    const static int m_DeliverBufferCount = 1;
    const static int m_DeliverPushTime = 1;
#ifdef SYS_APPLE
    bool m_BufferChanged;
#else
    QAtomicInt m_BufferChanged;
#endif
    bool m_InEasyPlayMode;
    int m_ImageDebug;
    ImageDebugHelper* m_ImageDebugHelper;
#ifdef USE_4D
    FourDVolumeLineImageArgs* m_volumeLineImageArgs;
#endif
    bool m_WaitForFrozen;
};

#endif // ABSTRACTDSCIMAGETILE_H
