#ifndef ZEUSIMAGETILE_H
#define ZEUSIMAGETILE_H

#include <QObject>
#include "zeusadapter_global.h"
#include "abstractdscimagetile.h"
#include "chisonultrasoundtypes.h"

class IBufferPushControler;
class ZeusContext;
class SimpleMultiCallTrigger;
class ZeusAPIInterface;
class IImageSaveHelper;

class USF_INTERFACE_IMAGING_ZEUSADAPTER_EXPORT ZeusImageTile : public AbstractDSCImageTile
{
    Q_OBJECT
public:
    explicit ZeusImageTile(const int imageTileIndex, IBufferPushControler* iBufferPushControler,
                           ChisonUltrasound::ChisonUltrasoundMode type, ZeusAPIInterface* api,
                           IImageSaveHelper* imageSaveHelper, QObject* parent = NULL);
    ~ZeusImageTile();

    void setColorMap(uchar* image2DMap, uchar* waveMap, int activeIndex);

    virtual void onEntryStressEchoAnalyze();

    virtual void onExitStressEchoAnalyze();

    virtual bool enForceDSCImage();

    virtual void clearThreadData(int count);

    virtual bool isImage2DPushed() const;

    virtual void setEnforceEnable(bool state);
public slots:
    virtual void onPlayCineStatusChanged(const bool playCineStatus);

protected:
    virtual bool onImageChanged(const QList<BufferUnit>& bufferUnits, const FrameUnitInfo& frameUnitInfo);

    virtual void onClearFrozenIndex();
    /**
     * @brief onStart 开始/停止
     */
    virtual void onStart();
    virtual void onStop();
    /**
     * @brief startInternal 内部子模块开始/停止
     */
    virtual void startInternal();
    virtual void stopInternal();
    /**
     * @brief haltProcessImageChanged 图形处理暂定/恢复
     */
    virtual void onHaltProcessImageChanged();
    virtual void onRecoveryProcessImageChanged();
    /**
     * 参数值发生变化之前执行
     */
    Q_INVOKABLE virtual void onBeforeParamChanged();
    /**
     * 参数值发生变化之后执行
     */
    Q_INVOKABLE virtual void onParamChanged(unsigned int counter);
    /**
     * 超声参数切换之前执行
     */
    Q_INVOKABLE virtual void onBeforeSonoParametersChanged();
    /**
     * 超声参数切换之后执行
     */
    Q_INVOKABLE virtual void onSonoParametersChanged();

    //    Q_INVOKABLE virtual void onClearSonoParametersChanged();
    /**
     * 处理宽景新增LineBuffer之后执行
     */
    //    Q_INVOKABLE virtual void onlineImageBufferChanged();

private slots:
    void onNewImage(LineImageArgs* lineImageArgs);
    void onNewMeasureImage(LineImageArgs* lineImageArgs);
    void onNewGrayImage(LineImageArgs* lineImageArgs);

private:
    ZeusContext* m_ZeusContext;
    uchar* m_Image2DMap;
    uchar* m_WaveMap;
    SimpleMultiCallTrigger* m_ColorMapTrigger;
    bool m_DataPushed;
};

#endif // ZEUSIMAGETILE_H
