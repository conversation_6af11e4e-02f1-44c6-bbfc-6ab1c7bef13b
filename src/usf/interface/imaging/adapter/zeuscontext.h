#ifndef ZEUSCONTEXT_H
#define ZEUSCONTEXT_H

#include "zeusadapter_global.h"
#include "bufferunit.h"
#include "frameinfo.h"
#include "zeusinfostruct.h"
#include "zeusapihelper.h"
#include <QObject>
#include <QPair>
#include <QList>
#include <QTimer>
#include <QMutex>
#include <QVariant>

#define MAX_PUSH_DATA_NUMBER 11

class SonoParameters;
class LineImageArgs;
class ByteLineImageArgs;
class PairParameterName;
class SimpleMultiCallTrigger;
#ifdef USE_IIMAGE
class UiImageStrategySwitcher;
#endif
class FpsCalculator;
class DscEcgDataProcessHelper;
class DscParameter;
class ISonoBuffer;
class ZeusMappingHelper;
class ZeusAPIInterface;
class PostProcessParaController;
struct ZeusHashValue;
class IImageSaveHelper;
enum PostImageType
{
    PostImageType_B = 0,
    PostImageType_C = 1,
    PostImageType_E = 2,
    PostImageType_M = 128,
    PostImageType_D = 129,
    PostImageType_CM = 130,
    PostImageType_ECG = 133,
};

class USF_INTERFACE_IMAGING_ZEUSADAPTER_EXPORT ZeusContext : public QObject
{
    Q_OBJECT
public:
    const static int DATA_POST_PROCESS_INFO_MAX_COUNT = 3;
    const static int ENABLE_RAW_DATA_NONE = 0x0;
    const static int ENABLE_RAW_DATA_DOPPLER = 0x1;
    const static int ENABLE_RAW_DATA_CP = 0x2;
    const static int ENABLE_RAW_DATA_SAVE_IMG = 0x4;

    explicit ZeusContext(ISonoBuffer* sonoBuffer, const int imageTileIndex, ZeusAPIInterface* api,
                         IImageSaveHelper* imageSaveHelper, QObject* parent = NULL);
    ~ZeusContext();

    void setColorMap(uchar* image2DMap, uchar* waveMap, int activeIndex);

    void updateSonoParametersChanning(bool sonoParametersChanning);

    void beforeSonoParametersChanged();
    void setSonoParameters(SonoParameters* sonoParameters);
    void clearSonoParameters();

    void pushData(const QList<BufferUnit>& bufferUnits, const FrameUnitInfo& frameUnitInfo);

    void onNewProcessedData(void* data, int width, int height, int dataType, int bitWidth, int syncId,
                            InfoForPostProcess info);
    static void onZeusNewProcessedData(void* UserData, CallbackInfo callbackInfo, InfoForPostProcess info, int syncId);

    void onPostProcessedData(void* data, int width, int height, InfoForPostProcess info);
    static void onZeusPostProcessedData(void* userData, void* data, int width, int height, InfoForPostProcess info);

    void onPostProcessedGrayData(void* data, int width, int height, InfoForPostProcess info);
    static void onZeusPostProcessedDataBeforeColorMap(void* userData, void* data, int width, int height,
                                                      InfoForPostProcess info);

    void enForceDSCColorMap();

    void onEntryStressEchoAnalyze();

    void onExitStressEchoAnalyze();
    void setLostFrame(int count = 0);
    int lostFrameCount();
    bool image2DPushed() const;

    /**
     * @brief 设置后处理参数在冻结下的发送Enforce的能力
     * 现采取在流程中控制，即冻结/回调流程中控制，不放在后端响应中
     * @param state
     */
    void setEnforceEnable(bool state);
public slots:
    virtual void onPlayCineStatusChanged(const bool playCineStatus);

    void onExecParameterChanged();

    void onTimeout();

    void resetPersistence();

    void onGetSyncId(int& syncId);

private slots:
    void onParameterValueChanged(const QString& name, const QVariant& value, const int type);
    void oncolorValueChanged();
    void onFreqSpectrumChanged(const QVariant& value);
    void onTriplexModeChanged(const QVariant& value);
    void onQuadplexModeStrChanged(const QVariant& value);
    void onWaveImageScaleFactorChanged(const QVariant& value);

    void onFpsUpdate(const QString& addinfo, float fpsV);
    void onRequestFlush();
    void onNewImage(LineImageArgs* lineImageArgs);
    void onVAFunctionChanged(const QVariant& value);
    void onSave8BitBeforeColorMapEnableChanged(const QVariant& value);
    void onCPFunctionChanged(const QVariant& value);
    void onColorMappedBDataCallbackEnableChanged(const QVariant& value);
    void onIImageTypeChanged(const QVariant& value);
    void onImageRenderLayoutsChanged(const QVariant& value);

private:
    int getSystemScanMode();
    void saveImage2DDataBeforeColorMap(char* data, int width, int height);
    void connectSonoParameters();

    void disconnectSonoParameters();

    void execParameterChangedInLock();

    void setFrameIndex(const int systemScanMode, const int frameIndex, const int cacheIndex, const int imageType);

    void setFrameIndex(const int systemScanMode, const int frameIndex, const int imageType);

    void setFrontIndex(const int systemScanMode, const int frontIndex, const int imageType);

    void updateImageRects(QList<ZeusHashValue>& zeusValues);

    void updateColorMap(bool isChangeSyncID = true);

    void createHandle(int imageWidth, int imageHeight);

    void changeRawDataEnableReason(int reason, bool enable);

    void setImage2DPushed(bool push);
    void setImageWavePushed(bool push);
    void setPostProcessParaControllerEnforceEnable();
    typedef struct
    {
        int ImageType;
        int Offset;
        int Width;
        int Height;
    } ImageTypeAndIndex;

    ImageTypeAndIndex calcDataPostProcessIndex(const FrameInfo& frameInfo);

    bool validSyncID(const int SyncID) const;

signals:
    void newGrayImage(LineImageArgs* lineImageArgs);
    void newColorMapGrayImage(LineImageArgs* lineImageArgs);
    void newImage(LineImageArgs* lineImageArgs);
    void newMeasureImage(LineImageArgs* lineImageArgs);

    void fpsChanged(const QString& name, const float fps);
    void datainfo(void* data, int width, int height, int imageType, int frameSteerType);
    void requestFlush();
    void requesFlushByBufferIndex(const int bufferIndex);
    void rawDataInfo(void* data, int width, int height, int bitCount, int layoutIndex);
    void imageDataReady();

private:
    typedef struct
    {
        int DataType;
        DataForPostProcess DataPostProcess[MAX_PUSH_DATA_NUMBER];
    } DataPostProcessInfo;
    void* m_ZeusHandle;
    QMutex m_ZeusHandleMutex;
    QMutex m_parameterMutex;
    int m_ImageTileIndex;
    bool m_IsNewImagePaused;
    SonoParameters* m_SonoParameters;
    bool m_SonoParametersChanning;
    ByteLineImageArgs* m_LineImageArgs;
    DataPostProcessInfo m_DataProcessInfo[DATA_POST_PROCESS_INFO_MAX_COUNT];
    uchar m_Image2DMap[COLORMAP_BUFFER_SIZE];
    uchar m_WaveMap[COLORMAP_BUFFER_SIZE];
    uchar m_LoadImage2DMap[COLORMAP_BUFFER_SIZE];
    uchar m_LoadWaveMap[COLORMAP_BUFFER_SIZE];
    uchar m_StressImage2DMap[COLORMAP_BUFFER_SIZE];
    uchar m_StressWaveMap[COLORMAP_BUFFER_SIZE];
    bool m_IsInStressEchoAnalyzeState{false};
    SimpleMultiCallTrigger* m_Trigger;
    QTimer* m_SetColorActivateTimer;
#ifdef USE_IIMAGE
    UiImageStrategySwitcher* m_iImageProcessFacade;
#endif
    FpsCalculator* m_FpsCalculatorOut;
    FpsCalculator* m_FpsCalculatorIn;
    DscEcgDataProcessHelper* m_EcgDataHelper;
    bool m_UsePWFrameIndex;
    int m_IImageType;
    static QStringList PersistenceResetParameters;
    static QMutex m_SyncMutex;
    static unsigned char m_SyncId;
    unsigned char m_InnerSyncId;
    ISonoBuffer* m_SonoBuffer;
    int m_Layout{1};
    int m_SystemScanMode{0};
    int m_SyncMode;
    bool m_WavePushed{false};
    bool m_Image2DPushed{false};
    static QStringList m_ZeusEnForceParameterNames;
    bool m_IsLooping{false};
    QMutex m_LostFrameMutex;
    int m_LostFrame;
    PostProcessParaController* m_PostProcessParaController;
    int m_EnableRawData;
    IImageSaveHelper* m_ImageSaveHelper;
    // 以下内存用于储存Zeus后处理返回的32bit显示图像，二维图与波形图分别储存
    uchar* m_Image2DData;
    uchar* m_WaveData;
};

#endif // ZEUSCONTEXT_H
