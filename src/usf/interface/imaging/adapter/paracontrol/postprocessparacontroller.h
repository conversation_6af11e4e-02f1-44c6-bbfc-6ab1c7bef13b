﻿#ifndef POSTPROCESSPARACONTROLLER_H
#define POSTPROCESSPARACONTROLLER_H

#include "zeusadapter_global.h"
#include "sonoparametersclientbase.h"
#include "zeusparameternames.h"
#include "commonareasource.h"
#include <QObject>

class ZeusAPIInterface;
class ZeusMappingHelper;

/**
 * @brief The PostProcessParaController class
 *
 * 本类在ZeusContext中实例化，多幅时ZeusContext多个实例，冻结/回调时超声参数变更，实例化的对象也需变更超声参数，
 * 本类的作用是根据超声参数值，直接/或者数值转换后，赋值给Zeus参数，本类中不涉及到超声参数值的变更，即：不执行超声参数的setPV
 */
class USF_INTERFACE_IMAGING_ZEUSADAPTER_EXPORT PostProcessParaController : public QObject,
                                                                           public SonoParametersClientBase
{
    Q_OBJECT
public:
    explicit PostProcessParaController(int imageTileIndex, ZeusAPIInterface* api, QObject* parent = nullptr);

    ~PostProcessParaController();
    ZeusAPIInterface* apiInterface() const
    {
        return m_APIInterface;
    }

    /**
     * @brief updateSonoParameters
     * 由于连续回调时，超声参数指针未发生变化，因此采用直接调用超声参数的响应处理，确保完成给Zeus的赋值
     */
    void updateSonoParameters();

    void setParaValue(QString paraName, const QVariant& value, ZeusParameterNames::ParameterType valueType,
                      int pointSize = 0);
    void setEnforceEnable(bool enable);
    void setSyncId(int id);
    // todo 下面的参数控制在zeuscontext中调用，后续要将其移动到内部处理
    void controlSystemScanMode();
    void controlImageRects();
    void controlColorMap(uchar* image2DMap, uchar* imageWaveMap);
    /**
     * @brief controlImageTilePara
     * TODO: 由于凸阵拓展成像的软件控制参数Scpd/ScpdTrape存在与梯形成像复用，并且Scpd/ScpdTrape也属于控制表参数，
     * 在controlScpdOn中Scpd/ScpdTrape参数，在凸阵拓展成像时，并未按照预期触发信号。因此目前暂时沿用AbstractDSCImageTile发出的信号，
     * 在ZeusContext中调用controlImageTilePara，进而到达执行onScpdChanged的目的。
     */
    void controlImageTilePara();

    //    void flushSentPara();
signals:
    void getSyncId(int& syncId);

protected slots:
    void onDepthMMChanged(const QVariant& value, bool changed);
    void onStartDepthMMChanged(const QVariant& value, bool changed);
    void onRealDepthMMChanged(const QVariant& value, bool changed);
    void onStartLineChanged(const QVariant& value, bool changed);
    void onStopLineChanged(const QVariant& value, bool changed);
    void onActiveBChanged(const QVariant& value, bool changed);
    void onPointNumPerLineChanged(const QVariant& value, bool changed);
    void onCPriorityChagned(const QVariant& value, bool changed);
    void onCfmSlopeChanged(const QVariant& value, bool changed);
    void onRotationChanged(const QVariant& value, bool changed);
    void onWtStartPointChanged(const QVariant& value, bool changed);
    void onWtCoefficienceChanged(const QVariant& value, bool changed);
    void onQ_IMAGEChanged(const QVariant& value, bool changed);
    void onTDOutGainChanged(const QVariant& value, bool changed);
    void onGainDopChanged(const QVariant& value, bool changed);
    void onGainDopTDIChanged(const QVariant& value, bool changed);
    void onDopVelocityFilterChanged(const QVariant& value, bool changed);
    void onTDVelocityFilterChanged(const QVariant& value, bool changed);
    void onFrameAvgChanged(const QVariant& value, bool changed);
    void onFrameAvgColorChanged(const QVariant& value, bool changed);
    void onFrameAvgTDIChanged(const QVariant& value, bool changed);
    void onFrameAvgSNChanged(const QVariant& value, bool changed);
    void onFrameAvgMVIChanged(const QVariant& value, bool changed);
    void onNeedleModeChanged(const QVariant& value, bool changed);
    void onDopMidGainChanged(const QVariant& value, bool changed);
    void onSpklSooth_alphaChanged(const QVariant& value, bool changed);
    void onSpklSmooth_CaseSelChanged(const QVariant& value, bool changed);
    void onHF_AlphaChanged(const QVariant& value, bool changed);
    void onHFChanged(const QVariant& value, bool changed);
    // [Apple][功能新增:9138]C模式下独立控制B的Edge Enhance 、HF和HF_Alpha
    void onHF_Alpha_IncChanged(const QVariant& value, bool changed);
    void onHFIncChanged(const QVariant& value, bool changed);
    void onAFGainChanged(const QVariant& value, bool changed);
    void onAFShiftChanged(const QVariant& value, bool changed);
    void onAFCoef0Changed(const QVariant& value, bool changed);
    void onAFCoef1Changed(const QVariant& value, bool changed);
    void onAFCoef2Changed(const QVariant& value, bool changed);
    void onAFCoef3Changed(const QVariant& value, bool changed);
    void onAFCoef4Changed(const QVariant& value, bool changed);
    void onLeeGainChanged(const QVariant& value, bool changed);
    void onLeeShiftChanged(const QVariant& value, bool changed);
    void onLeeMCoefChanged(const QVariant& value, bool changed);
    void onLeeSCoefChanged(const QVariant& value, bool changed);
    // if (!m_IntParameters.contains(BFPNames::ColorDataTypeStr)) tianyi TODO
    void onColorDataTypeChanged(const QVariant& value, bool changed);
    void onPowerThresholdChanged(const QVariant& value, bool changed);
    void onPowerThresholdPDChanged(const QVariant& value, bool changed);
    void onPowerThresholdSNChanged(const QVariant& value, bool changed);
    void onPowerThresholdTDIChanged(const QVariant& value, bool changed);
    void onPowerThresholdMVIChanged(const QVariant& value, bool changed);
    //
    void onCAFCoef0Changed(const QVariant& value, bool changed);
    void onCAFCoef1Changed(const QVariant& value, bool changed);
    void onCAFCoef2Changed(const QVariant& value, bool changed);
    void onCAFCoef3Changed(const QVariant& value, bool changed);
    void onCAFShiftChanged(const QVariant& value, bool changed);
    void onCAFGainChanged(const QVariant& value, bool changed);
    void onCLFCoef0Changed(const QVariant& value, bool changed);
    void onCLFCoef1Changed(const QVariant& value, bool changed);
    void onCLFCoef2Changed(const QVariant& value, bool changed);
    void onCLFGainChanged(const QVariant& value, bool changed);
    void onCLFShiftChanged(const QVariant& value, bool changed);
    void onBloodEffectionChanged(const QVariant& value, bool changed);
    void onBloodEffectionPDChanged(const QVariant& value, bool changed);
    void onBloodEffectionTDIChanged(const QVariant& value, bool changed);
    void onBloodEffectionMVIChanged(const QVariant& value, bool changed);
    void onPAFIR_Coef0Changed(const QVariant& value, bool changed);
    void onPAFIR_Coef1Changed(const QVariant& value, bool changed);
    void onPAFIR_Coef2Changed(const QVariant& value, bool changed);
    void onPAFIR_Coef3Changed(const QVariant& value, bool changed);
    void onPAFIR_GainChanged(const QVariant& value, bool changed);
    void onPAFIR_ShiftChanged(const QVariant& value, bool changed);
    void onPLFIR_Coef0Changed(const QVariant& value, bool changed);
    void onPLFIR_Coef1Changed(const QVariant& value, bool changed);
    void onPLFIR_Coef2Changed(const QVariant& value, bool changed);
    void onPLFIR_GainChanged(const QVariant& value, bool changed);
    void onPLFIR_ShiftChanged(const QVariant& value, bool changed);
    void onColorEDASESizeChanged(const QVariant& value, bool changed);
    void onColorEDASETypeChanged(const QVariant& value, bool changed);
    void onCFVelocityThresholdChanged(const QVariant& value, bool changed);
    void onScanWidthChanged(const QVariant& value, bool changed);

    void onScpdOnChanged(const QVariant& value, bool changed);
    void onZoomOnChanged(const QVariant& value, bool changed);
    //在onRegionZoomTriggerCompleted中下发对应参数AnyDensityEn
    //    void onAnyDensityEnChaned(const QVariant& value, bool changed);
    void onAComeBackChanged(const QVariant& value, bool changed);
    void onAComeBackPDChanged(const QVariant& value, bool changed);
    void onAComeBackTDIChanged(const QVariant& value, bool changed);
    void onAComeBackMVIChanged(const QVariant& value, bool changed);
    void onLComeBackChanged(const QVariant& value, bool changed);
    void onLComeBackPDChanged(const QVariant& value, bool changed);
    void onLComeBackTDIChanged(const QVariant& value, bool changed);
    void onLComeBackMVIChanged(const QVariant& value, bool changed);
    void onCFMIIReChanged(const QVariant& value, bool changed);
    void onColorEDAEnableChanged(const QVariant& value, bool changed);
    void onFreezeChanged(const QVariant& value, bool changed);
    void onUpChanged(const QVariant& value, bool changed);
    void onLeftChanged(const QVariant& value, bool changed);
    void onScpdChanged(const QVariant& value, bool changed);
    void onHighDensityChanged(const QVariant& value, bool changed);
    void onVS_ModeChanged(const QVariant& value, bool changed);
    void onVS_MLAChanged(const QVariant& value, bool changed);
    void onPixelSizeMMChanged(const QVariant& value, bool changed);
    void onBaseLineChanged(const QVariant& value, bool changed);
    void onIsSupportIntegerDepthChanged(const QVariant& value, bool changed);
    void onBaseLineCWDChanged(const QVariant& value, bool changed);
    void onBaseLineDTDIChanged(const QVariant& value, bool changed);
    void onPWDynamicRangeChanged(const QVariant& value, bool changed);
    void onPWDynamicRangeTMChanged(const QVariant& value, bool changed);
    void onPWDynamicRangeTDIChanged(const QVariant& value, bool changed);
    void onPWDynamicRangeCWDChanged(const QVariant& value, bool changed);
    void onColorLineChangingChanged(const QVariant& value, bool changed);
    void onNeedleAngleIndexChanged(const QVariant& value, bool changed);
    void onProbeIdChanged(const QVariant& value, bool changed);
    void onLowFilterAxialSigmaChanged(const QVariant& value, bool changed);
    void onLowFilterLateralSigmaChanged(const QVariant& value, bool changed);
    void onCPDSteerChanged(const QVariant& value, bool changed);
    void onCPDSteer2Changed(const QVariant& value, bool changed);
    void onCPDSteer3Changed(const QVariant& value, bool changed);
    void onCPDSteer4Changed(const QVariant& value, bool changed);
    void onCPDSteer5Changed(const QVariant& value, bool changed);
    void onTrapezoidalCPDSteerChanged(const QVariant& value, bool changed);
    void onTrapezoidalCPDSteer2Changed(const QVariant& value, bool changed);
    void onXContrastValueChanged(const QVariant& value, bool changed);
    void onSpectralInvertChanged(const QVariant& value, bool changed);
    void onBSteeringScanChanged(const QVariant& value, bool changed);
    void onCompoundDebugChanged(const QVariant& value, bool changed);
    void onColorTransparencyChanged(const QVariant& value, bool changed);
    void onTDITransparencyChanged(const QVariant& value, bool changed);
    void onMVITransparencyChanged(const QVariant& value, bool changed);
    void onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue);
    void onSystemScanModeChanged(const QVariant& value, bool changed);
    void onShowBInROIChanged(const QVariant& value, bool changed);
    void onMVITypeChanged(const QVariant& value, bool changed);
    void onDopOutGainChanged(const QVariant& value, bool changed);
    void onIsRoiVisibleChanged(const QVariant& value, bool changed);
    void onPostRawDataEnableChanged(const QVariant& value, bool changed);
    void onMBChanged(const QVariant& value, bool changed);
    void onStripRemoveEnableChanged(const QVariant& value, bool changed);
    void onOverlappingMBChanged(const QVariant& value, bool changed);
    void onInterpolatorOrderChanged(const QVariant& value, bool changed);

    void onGainChanged(const QVariant& value, bool changed);
    void onGainThiChanged(const QVariant& value, bool changed);
    void onMGainChanged(const QVariant& value, bool changed);
    void onMGainThiChanged(const QVariant& value, bool changed);
    void onDynamicRangeChanged(const QVariant& value, bool changed);
    //    void onMDynamicRangeChanged(const QVariant& value, bool changed);
    void onTGCChanged(const QVariant& value, bool changed);
    void onLGC0Changed(const QVariant& value, bool changed);
    void onLGC1Changed(const QVariant& value, bool changed);
    void onLGC2Changed(const QVariant& value, bool changed);
    void onLGC3Changed(const QVariant& value, bool changed);
    void onLGC4Changed(const QVariant& value, bool changed);
    void onLGC5Changed(const QVariant& value, bool changed);
    void onLGC6Changed(const QVariant& value, bool changed);
    void onLGC7Changed(const QVariant& value, bool changed);
    void onTDIEnChanged(const QVariant& value, bool changed);
    void onAngleSpacingRadChanged(const QVariant& value, bool changed);
    void onLineSpacingMMChanged(const QVariant& value, bool changed);
    void onColorLineDensityChanged(const QVariant& value, bool changed);
    void onWaveImageScaleFactorChanged(const QVariant& value, bool changed);

    void onSRIHfnoiseChanged(const QVariant& value, bool changed);
    void onSRIEdgeThresholdChanged(const QVariant& value, bool changed);
    void onSRIDetailPreservationChanged(const QVariant& value, bool changed);
    void onSRIOverallStrengthChanged(const QVariant& value, bool changed);
    void onSRIEdgeRampDownChanged(const QVariant& value, bool changed);
    void onSRIEdgeDirectionThreshChanged(const QVariant& value, bool changed);
    void onSRIHoleFillerThreshChanged(const QVariant& value, bool changed);
    void onSRINoiseFilterTypeChanged(const QVariant& value, bool changed);
    void onSRIEdgeFilterTypeChanged(const QVariant& value, bool changed);
    void onSRIEnableChanged(const QVariant& value, bool changed);
    void onSRINoiseLevelChanged(const QVariant& value, bool changed);
    void onSRIEdgeEnhanceChanged(const QVariant& value, bool changed);
    void onSRIFilterStrengthChanged(const QVariant& value, bool changed);
    void onStartLineColorChanged(const QVariant& value, bool changed);
    void onTopBorderColorChanged(const QVariant& value, bool changed);
    void onTDILineDensityChanged(const QVariant& value, bool changed);
    void onElastoColorLineDensityChanged(const QVariant& value, bool changed);
    void onMVILineDensityChanged(const QVariant& value, bool changed);
    // void onColorLineDensitySNChanged(const QVariant& value, bool changed);
    void onSteeringAngleChanged(const QVariant& value, bool changed);
    void onSteeringAngleSNChanged(const QVariant& value, bool changed);
    // PanZoom相关参数
    void onPanZoomSelectChanged(const QVariant& value, bool changed);
    void onImageZoomCoefChanged(const QVariant& value, bool changed);
    void onDisplayStartXForPanZoomChanged(const QVariant& value, bool changed);
    void onDisplayStartYForPanZoomChanged(const QVariant& value, bool changed);
    void onDisplayWidthForPanZoomChanged(const QVariant& value, bool changed);
    void onDisplayHeightForPanZoomChanged(const QVariant& value, bool changed);

    void onLineNumChanged();

    //虚拟顶点相关
    void onPA_VERT_DIST_EnableChanged(const QVariant& value);
    void onBSteeringAngleCodingChanged(const QVariant& value);

    //凸阵扩展
    void onCurvedExapandingChanged(const QVariant& value);

protected:
    virtual void onBeforeSetSonoParameters();
    virtual void onSetSonoParameters();

private:
    void dealSmoothChanged();
    void processCPDSteerChanged(const QString& name, const QVariant& value);

    bool isHPrfCW() const;
    bool isSupportAnyDensity() const;
    void calMultiBeam();
    bool isFreezeState() const;

    void controlProcessAfterFreezeEnable(bool isEqualValue);
    void updatelgcValue(QVariant value, int tgcIndex);
    void controlPostBlockData();
    void controlNeedleArgs();

    QByteArray convertStringToFloatByteArray(const QVariant& value);
    int getSystemScanMode() const;
    void sendPara(QString paraName, const QVariant& value, ZeusParameterNames::ParameterType type, int pointerSize = 0);
    void doEnforce(QString paraName);
    void sendROIPara();
    void sendPanZoomPara();
    void sendRegionZoomPara();
    ROIAreaSource::UseMode ROIType();

    // 以下接口是直接给Zeus赋值参数的，用于pangu中多个超声参数信号槽响应函数的调用
    void doPWDynamicRangeChanged(const QVariant& value, bool changed);
    void doDopVelocityFilterChanged(const QVariant& value, bool changed);
    void doFrameAvgColorChanged(const QVariant& value, bool changed);
    void doBloodEffectionChanged(const QVariant& value, bool changed);
    void doLComeBackChanged(const QVariant& value, bool changed);
    void doAComeBackChanged(const QVariant& value, bool changed);
    void doPowerThresholdChanged(const QVariant& value, bool changed);
    void doGainChanged(const QVariant& value, bool changed);
    void doMGainChanged(const QVariant& value, bool changed);
    void doColorTransparencyChanged(const QVariant& value, bool changed);
    void doBaseLineChanged(const QVariant& value, bool changed);
    void doOutGainChanged(const QVariant& value, bool changed);

    // 由于Zeus是按照多普勒模型来定义参数的，对于pangu来说存在多个超声参数对应一个Zeus参数，
    // 因此以下接口用于实际的信号槽响应，区分各种多普勒模式之间参数设置
    bool isMVI() const;
    bool isTDI() const;
    bool isSN() const;
    bool isPD() const;
    bool isColor() const;
    bool isCW() const;
    bool isTriplex() const;
    bool isTHI() const;

private:
    int m_HandleIndex;
    ZeusAPIInterface* m_APIInterface;
    ZeusMappingHelper* m_ZeusMappingHelper;
    bool m_EnforceEnable;
    int m_InnerScanId; //使用zeuscontext的syncid，后续重构syncId 的时候整体处理
    bool m_IsSonoparametersChanging;
    QHash<QString, QVariant> m_Values;
};

#endif // POSTPROCESSPARACONTROLLER_H
