#ifndef PREPROCESSPARACONTROLLER_H
#define PREPROCESSPARACONTROLLER_H
#include <QObject>
#include "zeusadapter_global.h"
#include "sonoparametersclientbase.h"
#include "zeusparameternames.h"

struct PRFINFO
{
    PRFINFO(int cnt1, int cnt2, int dec1, int dec2)
        : dataCnt1(cnt1)
        , dataCnt2(cnt2)
        , decimator1(dec1)
        , decimator2(dec2)
    {
    }
    int dataCnt1;
    int dataCnt2;
    int decimator1;
    int decimator2;
};

class ZeusAPIInterface;

/**
 * @brief The PreProcessParaController class
 *
 * 本类在PreProcessBufferController中实例化，用做给Zeus用于二次采样前 / IQ处理链路的参数赋值
 */
class USF_INTERFACE_IMAGING_ZEUSADAPTER_EXPORT PreProcessParaController : public QObject,
                                                                          public SonoParametersClientBase
{
    Q_OBJECT
public:
    explicit PreProcessParaController(int imageTileIndex, ZeusAPIInterface* api, QObject* parent = nullptr);

    ZeusAPIInterface* apiInterface() const
    {
        return m_APIInterface;
    }
    void sendPara(QString paraName, const QVariant& value, ZeusParameterNames::ParameterType valueType,
                  int pointSize = 0);

protected:
    virtual void onSetSonoParameters();

signals:
protected slots:

    // color
    void onWallFilterSettingChanged(const QVariant& value);
    void onWallFilterColorChanged(const QVariant& value);
    //    void onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue);
    void onWallThresholdSChanged(const QVariant& value);
    void onCfmNoiseVltChanged(const QVariant& value);
    void onCfmNoiseSlopeChanged(const QVariant& value);
    void onCfmFlashVltChanged(const QVariant& value);
    void onCfmFlashSlopeChanged(const QVariant& value);
    void onBi_CPAChanged(const QVariant& value);
    void onTDIEnChanged(const QVariant& value);
    void onMVIModeChanged(const QVariant& value);
    void onMVIPostGainChanged(const QVariant& value);
    void onCfmEngCtrl0Changed(const QVariant& value);
    void onCfmEngCtrl1Changed(const QVariant& value);
    void onCfmEngCtrl2Changed(const QVariant& value);
    void onCfmEngCtrl3Changed(const QVariant& value);
    void onCfmEngCtrl4Changed(const QVariant& value);
    void onCfmEngCtrl5Changed(const QVariant& value);
    void onCfmEngCtrl6Changed(const QVariant& value);
    void onCfmEngCtrl7Changed(const QVariant& value);
    void onCfmEngCtrl8Changed(const QVariant& value);
    void onCfmEngCtrl9Changed(const QVariant& value);
    void onCfmEngCtrl10Changed(const QVariant& value);
    void onCfmEngCtrl11Changed(const QVariant& value);
    void onCfmEngCtrl12Changed(const QVariant& value);
    void onPreColorPersistenceEnableChanged(const QVariant& value);
    void onColorPersistenceAlgorithmChanged(const QVariant& value);
    void onColorPersistenceHighChanged(const QVariant& value);
    void onColorPersistenceLowChanged(const QVariant& value);
    void onColorPersistenceANChanged(const QVariant& value);
    void onColorPersistenceRatioChanged(const QVariant& value);
    void onColorPersistenceUsingN_1Changed(const QVariant& value);
    void onCETChanged(const QVariant& value);
    void onCVRTChanged(const QVariant& value);
    void onCVLTChanged(const QVariant& value);
    void onCHETChanged(const QVariant& value);
    void onWFGainControlChanged(const QVariant& value);
    void onPacketSizeChanged(const QVariant& value);
    void onCFMEnergeCtlChanged(const QVariant& value);
    void onEtBfWfChanged(const QVariant& value);
    void onCFMQuitSamp_HeadChanged(const QVariant& value);
    void onCFMQuitSamp_TailChanged(const QVariant& value);
    void onCTGCChanged(const QVariant& value);

    // Color_TDI
    void onTDICETChanged(const QVariant& value);
    void onTDICVRTChanged(const QVariant& value);
    void onTDICVLTChanged(const QVariant& value);
    void onTDICHETChanged(const QVariant& value);
    void onPacketSizeTDIChanged(const QVariant& value);
    void onWFGainControlTDIChanged(const QVariant& value);
    void onTDICTGCChanged(const QVariant& value);
    void onTDIWallfilterChanged(const QVariant& value);

    // Color_PD
    void onPDCETChanged(const QVariant& value);
    void onPDCVRTChanged(const QVariant& value);
    void onPDCVLTChanged(const QVariant& value);
    void onPDCHETChanged(const QVariant& value);
    void onWFGainControlPDChanged(const QVariant& value);
    void onPacketSizePDChanged(const QVariant& value);
    void onPDCTGCChanged(const QVariant& value);

    // color_PD---SonoNeedle
    void onSNCETChanged(const QVariant& value);
    void onSNCVRTChanged(const QVariant& value);
    void onSNCVLTChanged(const QVariant& value);
    void onSNCHETChanged(const QVariant& value);
    void onWFGainControlSNChanged(const QVariant& value);
    void onPacketSizeSNChanged(const QVariant& value);
    void onSNCTGCChanged(const QVariant& value);
    void onWallFilterSNChanged(const QVariant& value);

    // color_MVI
    void onMVICETChanged(const QVariant& value);
    void onMVICVRTChanged(const QVariant& value);
    void onMVICVLTChanged(const QVariant& value);
    void onMVICHETChanged(const QVariant& value);
    void onHDCPACETChanged(const QVariant& value);
    void onWFGainControlMVIChanged(const QVariant& value);
    void onHDCPAWFGainCtrlChanged(const QVariant& value);
    void onPacketSizeMVIChanged(const QVariant& value);
    void onMVICTGChanged(const QVariant& value);
    void onHDCPAWallFilterChanged(const QVariant& value);
    void onCPAWallFilterSettingChanged(const QVariant& value);
    void onWallFilterMVIChanged(const QVariant& value);

    // pw
    void onAudioFilterCoefSelChanged(const QVariant& value);
    void onWallFilterDopChanged(const QVariant& value);
    void onPixelRatioChanged(const QVariant& value);
    void onDopSampleNumChanged(const QVariant& value);
    void onSampleVolumeChanged(const QVariant& value);
    void onSampleVolumeMMChanged(const QVariant& value);
    void onDopAccumulateNumChanged(const QVariant& value);
    void onDopFilterLengthChanged(const QVariant& value);
    void onGateSegmentChanged(const QVariant& value);
    void onPWEnhanceChanged(const QVariant& value);
    void onDopTimeFilterChanged(const QVariant& value);
    void onTriplexModeChanged(const QVariant& value);
    void onPRFDopChanged(const QVariant& value);
    void onADFreqMHzChanged(const QVariant& value);
    void onDopFilterCoefChanged(const QVariant& value);
    void onDopAudioPreGainChanged(const QVariant& value);
    void onDopAudioPostGainChanged(const QVariant& value);
    void onDopAudioSegmentChanged(const QVariant& value);
    void onSonicSpeedChanged(const QVariant& value);
    void onPWSecondSampDatNumChanged(const QVariant& value);
    void onIsSonoPWOnChanged(const QVariant& value);
    void onIsSmallPWChanged(const QVariant& value);
    void onHprfEnChanged(const QVariant& value);
    // void onCurrentGateChanged(const QVariant& value);
    void onGainDopChanged(const QVariant& value);

    // pw_TDI
    void onAudioFilterCoefSelTDIChanged(const QVariant& value);
    void onWallFilterDopTDIChanged(const QVariant& value);
    void onPixelRatioTDIChanged(const QVariant& value);
    void onTDSampleNumChanged(const QVariant& value);
    void onSampleVolumeTDIChanged(const QVariant& value);
    void onSampleVolumeTDIMMChanged(const QVariant& value);
    void onTDFilterLengthChanged(const QVariant& value);
    void onGateSegmentTDIChanged(const QVariant& value);
    void onPWEnhanceTDIChanged(const QVariant& value);
    void onTDTimeFilterChanged(const QVariant& value);
    void onTriplexModeTDIChanged(const QVariant& value);
    void onTDAudioPreGainChanged(const QVariant& value);
    void onTDAudioPostGainChanged(const QVariant& value);
    void onTDAudioSegmentChanged(const QVariant& value);
    void onGainDopTDIChanged(const QVariant& value);
    // cw
    void onCWDSampleRateChanged(const QVariant& value);
    void onCWEnChanged(const QVariant& value);
    void onCWSampleNumChanged(const QVariant& value);
    void onCWFilterLengthChanged(const QVariant& value);
    void onGateSegmentCWDChanged(const QVariant& value);
    void onPixelRatioCWDChanged(const QVariant& value);
    void onAudioFilterCoefSelCWDChanged(const QVariant& value);
    void onWallFilterCWDChanged(const QVariant& value);
    void onCWTimeFilterChanged(const QVariant& value);
    void onCWAudioSegmentChanged(const QVariant& value);
    void onCWAudioPostGainChanged(const QVariant& value);
    void onCWAudioPreGainChanged(const QVariant& value);
    void onPWEnhanceCWDChanged(const QVariant& value);
    void onPRFCWDChanged(const QVariant& value);
    void onGainDopCWDChanged(const QVariant& value);
    void onCWDAccumulateNumChanged(const QVariant& value);

private:
    bool readBlockData(char* data, const QString& paramName, int address, int partIndex, int partCount = 0);

    // Color WallFilter
    void updateWallFilterColorData();
    void updateCPAWallFilterColorData();

    // pw Filter
    void initPWFilter();
    void updataAudioFilterCoefSelData();
    void updataWallFilterDopData();
    void updataWindowFilterCoefSelData();

    // cw Filter
    void initCWPRFGear();

    QVariant getControlTableValue(Parameter* para, const QVariant& value);

private:
    int m_HandleIndex;
    ZeusAPIInterface* m_APIInterface;

    enum
    {
        AudioFilterGearlength = 256,
        AudioFilterGearCount = 8,
        WallFilterlengthPerGear = 256,
        WallFilterGearCount = 4,
        WindowFilterlengthPerGear = 256,
        WindowFilterGearCount = 8,
    };
    // pw的filter直接加载到内存中
    int64_t m_AudioFilter[AudioFilterGearlength * AudioFilterGearCount];
    int64_t m_WallFilter[WallFilterlengthPerGear * WallFilterGearCount];
    int8_t m_WindowFilter[WindowFilterlengthPerGear * WindowFilterGearCount];

    QMap<int, PRFINFO> m_PRFDatas;

signals:
};

#endif // PREPROCESSPARACONTROLLER_H
