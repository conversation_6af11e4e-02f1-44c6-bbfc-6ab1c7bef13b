#include "postprocessparacontroller.h"
#include "probeparameters.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "systemscanmodeclassifier.h"
#include "probeparameters.h"
#include "probephysicalgeometry.h"
#include "multibeamcalculator.h"
#include "probeblockdatagroups.h"
#include "probeblockdataset.h"
#include "probedataset.h"
#include "zeusapihelper.h"
#include "imageinfodef.h"
#include "bfscanareawidthparameter.h"
#include "zeusmappinghelper.h"
#include "setting.h"
#include "util.h"
#include "bfdepthparameters.h"
#include "syncidmanager.h"

using namespace ZeusParameterNames;

PostProcessParaController::PostProcessParaController(int imageTileIndex, ZeusAPIInterface* api, QObject* parent)
    : QObject(parent)
    , m_HandleIndex(imageTileIndex)
    , m_APIInterface(api)
    , m_ZeusMappingHelper(new ZeusMappingHelper())
    , m_EnforceEnable(false)
    , m_InnerScanId(0)
    , m_IsSonoparametersChanging(false)
{
    //由于参数 BPostEnable，CPostEnable，DscMethod 的值，只与机型相关，因此只需要利用ModelConfig的值设置一次
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BPostEnable),
             ModelConfig::instance().value(ModelConfig::BPostEnable, true), Dsc_Bool);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::CPostEnable),
             ModelConfig::instance().value(ModelConfig::CPostEnable, true), Dsc_Bool);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::DscMethod),
             ModelConfig::instance().value(ModelConfig::DscMethod, 0), Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::KeepEdgeBeams), true, Dsc_Bool);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::StripRemoveFPGA), false, Dsc_Bool);

    // zeus要求Atom下发这个使能，使dynamicrange再加16，解决波形图暗的问题
    // 设置PW的固定参数，在开机不进入PW打图时，直接回调PW图像，频谱效果弱
    if (ModelConfig::instance().value(ModelConfig::IsPWDynamicRangeAdjustmentEnable, false).toBool())
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::PWDynamicRangeAdjustmentEnable), true, Dsc_Bool);
    }
}

PostProcessParaController::~PostProcessParaController()
{
    if (m_ZeusMappingHelper != nullptr)
    {
        delete m_ZeusMappingHelper;
        m_ZeusMappingHelper = nullptr;
    }
}

void PostProcessParaController::updateSonoParameters()
{
    onBeforeSetSonoParameters();
    onSetSonoParameters();
}

void PostProcessParaController::setEnforceEnable(bool enable)
{
    m_EnforceEnable = enable;
}

void PostProcessParaController::setSyncId(int id)
{
    if (m_SonoParameters != nullptr && isFreezeState())
    {
        m_APIInterface->setZeusParameter(m_HandleIndex, POSTPARANAME(ProcessSonoParameterIndex::DataSyncID), id,
                                         Dsc_Int);
    }
}

void PostProcessParaController::controlSystemScanMode()
{
    int systemScanMode =
        (pIV(BFPNames::SystemScanModeStr) == SystemScanModeAV ? SystemScanModeBPW : getSystemScanMode());

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::SystemScanMode),
             m_ZeusMappingHelper->convertToZeusSystemScanMode(systemScanMode, pIV(BFPNames::ImageModeStr)), Dsc_Int);
}

void PostProcessParaController::onDepthMMChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    // 2023-05-05 Modify by AlexWang 解决放大状态凸阵下图像显示不全，需要考虑放大因素影响计算探头底部深度
    // const  ProbeDataInfo& probeDataInfo =
    // ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    ProbeParameters probeParameters(m_SonoParameters, true);
    // 2023-06-16 Write by AlexWang [bug:65077] 线阵在局部放大状态计算图像高度时要减去StartDetphMM
    double depth = probeParameters.imageDepthMM(value.toDouble());
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::Depth), depth, Dsc_Double);

    double extraScale = 1.0 / pFV(BFPNames::ProbeDSCImageZoomCofStr);
    if (pBV(BFPNames::DSCImageZoomOnStr))
    {
        extraScale = extraScale * pFV(BFPNames::FixedSWImageZoomCofStr);
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ExtraScale), extraScale, Dsc_Double);
    if (m_SonoParameters->pBV(BFPNames::ZoomSelectStr))
    {
        sendRegionZoomPara();
    }
}

void PostProcessParaController::onStartDepthMMChanged(const QVariant& value, bool changed)
{
    sendRegionZoomPara();
}

void PostProcessParaController::onRealDepthMMChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    double pointInterval = value.toDouble() / pIV(BFPNames::PointNumPerLineStr);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PointInterval), pointInterval, Dsc_Double);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::RealDepth), value, Dsc_Double);
}

void PostProcessParaController::onActiveBChanged(const QVariant& value, bool changed)
{
    sendRegionZoomPara();
}

void PostProcessParaController::onStartLineChanged(const QVariant& value, bool changed)
{
    sendRegionZoomPara();
}

void PostProcessParaController::onStopLineChanged(const QVariant& value, bool changed)
{
    sendRegionZoomPara();
}

void PostProcessParaController::onPointNumPerLineChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PointNum), value, Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorPointNum), value, Dsc_Int);
}

void PostProcessParaController::onCPriorityChagned(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::CPriority), value, Dsc_Int);
}

void PostProcessParaController::onCfmSlopeChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::CfmSlope), value, Dsc_Int);
}

void PostProcessParaController::onRotationChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::RotationAngle), value, Dsc_Int);
}

void PostProcessParaController::onWtStartPointChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::WtStartPoint), value, Dsc_Int);
}

void PostProcessParaController::onWtCoefficienceChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::WtCoefficience), value, Dsc_Int);
}

void PostProcessParaController::onQ_IMAGEChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::IImageIndex), value, Dsc_Int);
}

void PostProcessParaController::onTDOutGainChanged(const QVariant& value, bool changed)
{
    if (!isTDI())
    {
        return;
    }

    doOutGainChanged(value, changed);
}

void PostProcessParaController::onLineNumChanged()
{
    //非任意线密度下使用，线密度按照倍数计算
    int densityIndexList[3] = {LowDensity, MediumDensity, HighDensity};
    int densityIndex = densityIndexList[0];
#ifdef USE_HIGHDENSITY_INT
    int highDensity = pIV(BFPNames::HighDensityStr);
    if (highDensity == 1)
    {
        densityIndex = densityIndexList[1];
    }
    else if (highDensity == 2)
    {
        densityIndex = densityIndexList[2];
    }
#else
    bool highDensity = pBV(BFPNames::HighDensityStr);
    densityIndex = highDensity ? 1 : 0;
#endif
    int lineNum = ProbeParameters::lines(pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr), highDensity);
    int lineNumAfterBeamforming = lineNum;

    if (ModelConfig::instance().value(ModelConfig::IsSupportVS, false).toBool() && pBV(BFPNames::VS_ModeStr))
    {
        int nMLA = 4 << pIV(BFPNames::VS_MLAStr); // VS_MLA值是0/1/2/3，实际是4/8/16/32
        lineNumAfterBeamforming /= 2;             // zeus要求，线数是原来的一半
        lineNumAfterBeamforming *= nMLA;
    }

    //在stripRemoveEnable关闭的情况下，值跟LineNumAfterBeamforming保持一致
    bool stripRemoveEnable = pBV(BFPNames::StripRemoveEnableStr) && (pIV(BFPNames::MBStr) == 2);

    if (!stripRemoveEnable)
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineNum), lineNum, Dsc_Int);
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineNumAfterBeamforming), lineNumAfterBeamforming, Dsc_Int);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::HighDensity), densityIndex, Dsc_Int);

    calMultiBeam();

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::NeedleLineNum),
             m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::LineNum)), Dsc_Int);
}

void PostProcessParaController::sendROIPara()
{
    ROIAreaSource roiAreaSource(m_SonoParameters, QSizeF(), nullptr, ROIType());
    ProbePhysicalGeometry rect = roiAreaSource.area();
    int cSteerAngle = m_SonoParameters->parameter(roiAreaSource.SteerAngleName())->showValue().toInt();
#ifdef USE_HIGHDENSITY_INT
    int highDensity = m_SonoParameters->pIV(roiAreaSource.lineDensityName());
    int lineNum = ProbeParameters::lines(rect.left(), rect.right(), highDensity);
#else
    bool highDensity = m_SonoParameters->pBV(roiAreaSource.lineDensityName());
    int lineNum = ProbeParameters::lines(rect.left(), highDensity ? rect.right() + 1 : rect.right(), highDensity);
#endif
    double pixelSizeMM =
        m_SonoParameters->pDV(BFPNames::PixelSizeMMStr) * (m_SonoParameters->pDV(BFPNames::ImageZoomCoefStr) / 100);
    // 2023-03-29 Add by AlexWang
    // 解决进入RegionZoom后进入c模式，roi框选取的血流和roi框位置上下不匹配，血流的位置从探头表面开始计算深度，计算的时候必须将起始深度减去，注意要换算成像素值
    qreal startDepth = 0; //起始深度
    qreal depthPixel = 0; //深度像素值

    // 20230-03-30 Add by AlexWang 避免起始深度大于探头roi区域的顶部和底部引发异常
    qreal topPixel = rect.top() / pixelSizeMM;
    qreal bottomPixel = rect.bottom() / pixelSizeMM;
    if (m_SonoParameters->pBV(BFPNames::ZoomOnStr))
    {
        startDepth = m_SonoParameters->pDV(BFPNames::StartDepthMMStr);
        depthPixel = startDepth / pixelSizeMM;
        if (RealCompare::IsGreater(depthPixel, topPixel) || RealCompare::IsGreater(depthPixel, bottomPixel))
        {
            depthPixel = 0;
        }
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorLineNum), lineNum, Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::StartLineColor), rect.left(), Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::StopLineColor), rect.right(), Dsc_Int);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::TopBorderColor), (int)(rect.top() / pixelSizeMM - depthPixel),
             Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BottomBorderColor),
             (int)(rect.bottom() / pixelSizeMM - depthPixel), Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::CSteeringAngle), cSteerAngle, Dsc_Int);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::StartDepth), startDepth, Dsc_Double);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ROITopDepth), rect.top(), Dsc_Double);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ROIBottomDepth), rect.bottom(), Dsc_Double);
}

void PostProcessParaController::sendPanZoomPara()
{
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ZoomEnable), pBV(BFPNames::PanZoomSelectStr), Dsc_Bool);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ZoomCoef), pIV(BFPNames::ImageZoomCoefStr), Dsc_Int);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::OutputDisplayStartXForZoom),
             pIV(BFPNames::DisplayStartXForPanZoomStr), Dsc_Int);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::OutputDisplayStartYForZoom),
             pIV(BFPNames::DisplayStartYForPanZoomStr), Dsc_Int);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::OutputDisplayWidthForZoom),
             pIV(BFPNames::DisplayWidthForPanZoomStr), Dsc_Int);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::OutputDisplayHeightForZoom),
             pIV(BFPNames::DisplayHeightForPanZoomStr), Dsc_Int);

    //    doEnforce(POSTPARANAME(ProcessSonoParameterIndex::Zoom)); // TODO: 确认目前还需要么
}

void PostProcessParaController::sendRegionZoomPara()
{
    // 2023-06-20 Write by AlexWang [bug:65074] 增加针对Zeus系统的局部放大处理
    QMap<QString, QString> paramMap;
    paramMap[POSTPARANAME(ProcessSonoParameterIndex::AnyDensityEn)] = BFPNames::AnyDensityEnStr;
    paramMap[POSTPARANAME(ProcessSonoParameterIndex::ZoomOn)] = BFPNames::ZoomOnStr;
    paramMap[POSTPARANAME(ProcessSonoParameterIndex::StartLine)] = BFPNames::StartLineStr;
    paramMap[POSTPARANAME(ProcessSonoParameterIndex::StopLine)] = BFPNames::StopLineStr;
    paramMap[POSTPARANAME(ProcessSonoParameterIndex::RealDepth)] = BFPNames::DepthMMStr;
    paramMap[POSTPARANAME(ProcessSonoParameterIndex::StartDepth)] = BFPNames::StartDepthMMStr;

    QMap<QVariant::Type, ParameterType> typeMap;
    typeMap[QVariant::Int] = Dsc_Int;
    typeMap[QVariant::Double] = Dsc_Double;
    typeMap[QVariant::Bool] = Dsc_Bool;

    for (auto it = paramMap.begin(); it != paramMap.end(); it++)
    {
        QVariant value = m_SonoParameters->pV(it.value());
        QVariant::Type type = value.type();
        sendPara(it.key(), value, typeMap[type]);
    }
}

ROIAreaSource::UseMode PostProcessParaController::ROIType()
{
    SystemScanMode systemscanmode = (SystemScanMode)m_SonoParameters->pIV(BFPNames::SystemScanModeStr);
    if (systemscanmode == SystemScanModeSonoNeedle)
    {
        return ROIAreaSource::SonoNeedle;
    }
    else if (systemscanmode == SystemScanModeTissueDoppler || systemscanmode == SystemScanModeTissuePW)
    {
        return ROIAreaSource::TDI;
    }
    else if (systemscanmode == SystemScanModeMVI || systemscanmode == SystemScanModeMVIPW)
    {
        return ROIAreaSource::MVI;
    }
    else if (systemscanmode == SystemScanModeFourDPre)
    {
        return ROIAreaSource::FourDPre;
    }
    else if (systemscanmode == SystemScanModeElasto)
    {
        return ROIAreaSource::Elasto;
    }

    return ROIAreaSource::Color;
}

void PostProcessParaController::onPA_VERT_DIST_EnableChanged(const QVariant& value)
{
    if (pBV(BFPNames::PA_VERT_DIST_EnableStr))
    {
        const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
        double angleSpacingCompound = 0;
        if (probeDataInfo.IsLinear)
        {
            angleSpacingCompound =
                pIV(BFPNames::BSteeringAngleCodingStr) * 2.0f / pIV(BFPNames::B_RX_LNUMStr) * (M_PI / 180);
        }
        else if (probeDataInfo.IsPhasedArray)
        {
            angleSpacingCompound = pDV(BFPNames::PhasedAngleStr) / pIV(BFPNames::B_RX_LNUMStr) * (M_PI / 180);
        }
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacingCompound), angleSpacingCompound, Dsc_Double);
    }
    //注意，相控阵下，虚拟顶点和梯形成像是互斥的
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::TrapezoidalMode), pBV(BFPNames::VirtualVertexTrapezoidalModeStr),
             Dsc_Bool);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PaVertDistEnable), value.toBool(), Dsc_Bool);
    int cpdSteer = m_SonoParameters->pIV(BFPNames::TrapezoidalCPDSteerStr);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::SteeringAngleForT), cpdSteer, Dsc_Int);
}

void PostProcessParaController::onBSteeringAngleCodingChanged(const QVariant& value)
{
    if (pBV(BFPNames::PA_VERT_DIST_EnableStr))
    {
        const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
        int extendAngle = value.toInt();
        double probeWidth = 0;
        double verticalDisDouble = 0;
        if (probeDataInfo.IsLinear)
        {
            probeWidth = probeDataInfo.probeWide();
            verticalDisDouble = probeWidth / 2 / tan(extendAngle * M_PI / 180);
        }
        else if (probeDataInfo.IsPhasedArray)
        {
            probeWidth = probeDataInfo.Pitch * probeDataInfo.WaferNum;
            double tanValue = tan(pDV(BFPNames::PhasedAngleStr) * 0.5 * M_PI / 180);
            verticalDisDouble = probeWidth / 2 / tanValue;
        }
        // zeus希望pixelsizeMM取缩放前的值
        double pixelSizeMM = BFDepthParameters::pixelSizeMMWithoutZoomCoef(pDV(BFPNames::PixelSizeMMStr),
                                                                           pIV(BFPNames::ImageZoomCoefStr), 1, 1);
        int verticalDisInt = std::round(verticalDisDouble / pixelSizeMM);
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::VerticalDis), verticalDisInt, Dsc_Int);
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TrapzoidalAngle), extendAngle, Dsc_Int);
    }
}

void PostProcessParaController::onCurvedExapandingChanged(const QVariant& value)
{
    if (value.toBool())
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TrapzoidalAngle), pIV(BFPNames::TrapezoidalCPDSteerStr),
                 Dsc_Int);
    }
}

void PostProcessParaController::onGainDopChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    // TODO???  没找到对应zeus参数
}

void PostProcessParaController::onGainDopTDIChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    // TODO???  没找到对应zeus参数
}

void PostProcessParaController::onDopVelocityFilterChanged(const QVariant& value, bool changed)
{
#ifdef USE_TARGET_PALM
    // SonoAir的CW，在FPGA端采用的是PW的方案
    if (!pBV(BFPNames::TDIEnStr))
    {
        doDopVelocityFilterChanged(value, changed);
    }
#else
    if (!(pBV(BFPNames::TDIEnStr) || pBV(BFPNames::CWEnStr)))
    {
        doDopVelocityFilterChanged(value, changed);
    }
#endif
}

void PostProcessParaController::onTDVelocityFilterChanged(const QVariant& value, bool changed)
{
    if (pBV(BFPNames::TDIEnStr))
    {
        doDopVelocityFilterChanged(value, changed);
    }
}

void PostProcessParaController::onFrameAvgChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PersistenceLevel), value, Dsc_Int);
}

void PostProcessParaController::onFrameAvgColorChanged(const QVariant& value, bool changed)
{
    if (isColor())
    {
        doFrameAvgColorChanged(value, changed);
    }
}

void PostProcessParaController::onFrameAvgTDIChanged(const QVariant& value, bool changed)
{
    if (isTDI())
    {
        doFrameAvgColorChanged(value, changed);
    }
}

void PostProcessParaController::onFrameAvgSNChanged(const QVariant& value, bool changed)
{
    if (isSN())
    {
        doFrameAvgColorChanged(value, changed);
    }
}

void PostProcessParaController::onFrameAvgMVIChanged(const QVariant& value, bool changed)
{
    if (isMVI())
    {
        doFrameAvgColorChanged(value, changed);
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::PersistenceLevelDyn), value, Dsc_Int);
    }
}

void PostProcessParaController::onNeedleModeChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::NeedleEnable), value, Dsc_Bool);
}

void PostProcessParaController::onDopMidGainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::MidGain), value, Dsc_Int);
}

void PostProcessParaController::onSpklSooth_alphaChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMSMAlpha), value, Dsc_Int);
}

void PostProcessParaController::onSpklSmooth_CaseSelChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMSMModeSel), value, Dsc_Int);
}

void PostProcessParaController::onHF_AlphaChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    // C模式下独立控制B的Edge Enhance 、HF和HF_Alpha，此处不能根据 changed 判断来做return处理
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMHFAlpha), value, Dsc_Int);
}

void PostProcessParaController::onHFChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    // C模式下独立控制B的Edge Enhance 、HF和HF_Alpha，此处不能根据 changed 判断来做return处理
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMHFModeSel), value, Dsc_Int);
}

void PostProcessParaController::onHF_Alpha_IncChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    // C模式下独立控制B的Edge Enhance 、HF和HF_Alpha，此处不能根据 changed 判断来做return处理
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMHFAlpha), value, Dsc_Int);
}

void PostProcessParaController::onHFIncChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    // C模式下独立控制B的Edge Enhance 、HF和HF_Alpha，此处不能根据 changed 判断来做return处理
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMHFModeSel), value, Dsc_Int);
}

void PostProcessParaController::onAFGainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMSFIRGain), value, Dsc_Int);
}

void PostProcessParaController::onAFShiftChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMSFIRShift), value, Dsc_Int);
}

void PostProcessParaController::onAFCoef0Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMAFCoef0), value, Dsc_Int);
}

void PostProcessParaController::onAFCoef1Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMAFCoef1), value, Dsc_Int);
}

void PostProcessParaController::onAFCoef2Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMAFCoef2), value, Dsc_Int);
}

void PostProcessParaController::onAFCoef3Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMAFCoef3), value, Dsc_Int);
}

void PostProcessParaController::onAFCoef4Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMAFCoef4), value, Dsc_Int);
}

void PostProcessParaController::onLeeGainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMLEEGain), value, Dsc_Int);
}

void PostProcessParaController::onLeeShiftChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMLEEShift), value, Dsc_Int);
}

void PostProcessParaController::onLeeMCoefChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMLEE_MCoef), value, Dsc_Int);
}

void PostProcessParaController::onLeeSCoefChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMLEE_SCoef), value, Dsc_Int);
}

void PostProcessParaController::onColorDataTypeChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorDataType), value, Dsc_Int);
}

void PostProcessParaController::onPowerThresholdChanged(const QVariant& value, bool changed)
{
    if (isColor())
    {
        doPowerThresholdChanged(value, changed);
    }
}

void PostProcessParaController::onPowerThresholdPDChanged(const QVariant& value, bool changed)
{
    if (isPD())
    {
        doPowerThresholdChanged(value, changed);
    }
}

void PostProcessParaController::onPowerThresholdSNChanged(const QVariant& value, bool changed)
{
    if (isSN())
    {
        doPowerThresholdChanged(value, changed);
    }
}

void PostProcessParaController::onPowerThresholdTDIChanged(const QVariant& value, bool changed)
{
    if (isTDI())
    {
        doPowerThresholdChanged(value, changed);
    }
}

void PostProcessParaController::onPowerThresholdMVIChanged(const QVariant& value, bool changed)
{
    if (isMVI())
    {
        doPowerThresholdChanged(value, changed);
    }
}

void PostProcessParaController::onCAFCoef0Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorAFCoef0), value, Dsc_Int);
}

void PostProcessParaController::onCAFCoef1Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorAFCoef1), value, Dsc_Int);
}

void PostProcessParaController::onCAFCoef2Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorAFCoef2), value, Dsc_Int);
}

void PostProcessParaController::onCAFCoef3Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorAFCoef3), value, Dsc_Int);
}

void PostProcessParaController::onCAFShiftChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorAFIRShift), value, Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PAFIRShift), value, Dsc_Int);
}

void PostProcessParaController::onCAFGainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorAFIRGain), value, Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PAFIRGain), value, Dsc_Int);
}

void PostProcessParaController::onCLFCoef0Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorLFCoef0), value, Dsc_Int);
}

void PostProcessParaController::onCLFCoef1Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorLFCoef1), value, Dsc_Int);
}

void PostProcessParaController::onCLFCoef2Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorLFCoef2), value, Dsc_Int);
}

void PostProcessParaController::onCLFGainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorLFIRGain), value, Dsc_Int);
}

void PostProcessParaController::onCLFShiftChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorLFIRShift), value, Dsc_Int);
}

void PostProcessParaController::onBloodEffectionChanged(const QVariant& value, bool changed)
{
    if (isColor())
    {
        doBloodEffectionChanged(value, changed);
    }
}

void PostProcessParaController::onBloodEffectionPDChanged(const QVariant& value, bool changed)
{
    if (isPD())
    {
        doBloodEffectionChanged(value, changed);
    }
}

void PostProcessParaController::onBloodEffectionTDIChanged(const QVariant& value, bool changed)
{
    if (isTDI())
    {
        doBloodEffectionChanged(value, changed);
    }
}

void PostProcessParaController::onBloodEffectionMVIChanged(const QVariant& value, bool changed)
{
    if (isMVI())
    {
        doBloodEffectionChanged(value, changed);
    }
}

void PostProcessParaController::onPAFIR_Coef0Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PAFCoef0), value, Dsc_Int);
}

void PostProcessParaController::onPAFIR_Coef1Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PAFCoef1), value, Dsc_Int);
}

void PostProcessParaController::onPAFIR_Coef2Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PAFCoef2), value, Dsc_Int);
}

void PostProcessParaController::onPAFIR_Coef3Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PAFCoef3), value, Dsc_Int);
}

void PostProcessParaController::onPAFIR_GainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PAFIRGain), value, Dsc_Int);
}

void PostProcessParaController::onPAFIR_ShiftChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PAFIRShift), value, Dsc_Int);
}

void PostProcessParaController::onPLFIR_Coef0Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PLFCoef0), value, Dsc_Int);
}

void PostProcessParaController::onPLFIR_Coef1Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PLFCoef1), value, Dsc_Int);
}

void PostProcessParaController::onPLFIR_Coef2Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PLFCoef2), value, Dsc_Int);
}

void PostProcessParaController::onPLFIR_GainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PLFIRGain), value, Dsc_Int);
}

void PostProcessParaController::onPLFIR_ShiftChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PLFIRShift), value, Dsc_Int);
}

void PostProcessParaController::onColorEDASESizeChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorEDASESize), value, Dsc_Int);
}

void PostProcessParaController::onColorEDASETypeChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorEDASEType), value, Dsc_Int);
}

void PostProcessParaController::onCFVelocityThresholdChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::CFVelocityThreshold), value, Dsc_Int);
}

void PostProcessParaController::onScanWidthChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    //    if (!changed)
    //    {
    //        return;
    //    }
    onLineNumChanged();
}

void PostProcessParaController::onScpdOnChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::CompoundEnable), value, Dsc_Bool);
}

void PostProcessParaController::onZoomOnChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    onLineNumChanged();
    // 使用ZoomOn来控制线数的计算
}

void PostProcessParaController::onAComeBackChanged(const QVariant& value, bool changed)
{
    if (isColor())
    {
        doAComeBackChanged(value, changed);
    }
}

void PostProcessParaController::onAComeBackPDChanged(const QVariant& value, bool changed)
{
    if (isPD())
    {
        doAComeBackChanged(value, changed);
    }
}

void PostProcessParaController::onAComeBackTDIChanged(const QVariant& value, bool changed)
{
    if (isTDI())
    {
        doAComeBackChanged(value, changed);
    }
}

void PostProcessParaController::onAComeBackMVIChanged(const QVariant& value, bool changed)
{
    if (isMVI())
    {
        doAComeBackChanged(value, changed);
    }
}

void PostProcessParaController::onLComeBackChanged(const QVariant& value, bool changed)
{
    if (isColor())
    {
        doLComeBackChanged(value, changed);
    }
}

void PostProcessParaController::onLComeBackPDChanged(const QVariant& value, bool changed)
{
    if (isPD())
    {
        doLComeBackChanged(value, changed);
    }
}

void PostProcessParaController::onLComeBackTDIChanged(const QVariant& value, bool changed)
{
    if (isTDI())
    {
        doLComeBackChanged(value, changed);
    }
}

void PostProcessParaController::onLComeBackMVIChanged(const QVariant& value, bool changed)
{
    if (isMVI())
    {
        doLComeBackChanged(value, changed);
    }
}

void PostProcessParaController::onCFMIIReChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::CFMIIREnable), value, Dsc_Int);
}

void PostProcessParaController::onColorEDAEnableChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorEDAEnable), value, Dsc_Bool);
}

void PostProcessParaController::onFreezeChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    controlProcessAfterFreezeEnable(true);
}

void PostProcessParaController::onUpChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::VerticalFlip), !value.toBool(), Dsc_Bool);
}

void PostProcessParaController::onLeftChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::HorizontalFlip), !value.toBool(), Dsc_Bool);
}

void PostProcessParaController::onScpdChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    int compoundNum = 1;
    if (pBV(BFPNames::PA_VERT_DIST_EnableStr))
    {
        //原来的逻辑是scpd值加1
        //    int scpd = value.toInt() + 1;
        int valueInt = value.toInt();
        // atom的compound档位增加了，具体每档对应的复合帧数
        //  scpd值：O(Off)     1     2     3     4     5    6
        // 复合帧数：  1        2     3     5     7     9    11
        if (valueInt <= 2)
        {
            compoundNum = valueInt + 1;
        }
        else
        {
            compoundNum = 2 * valueInt - 1;
        }
    }
    else
    {
        compoundNum = 3;
        // 梯形模式下需要4帧复合，因此需要判断
        if (pBV(BFPNames::TrapezoidalModeStr))
        {
            compoundNum = 4;
            // 在回调2帧复合版本的梯形图像时，由于默认会发送给zeus4帧的compoundnum
            // 在梯形模式打开且ScpdTrapeStr=1时，判定为2帧复合版本的图像将compoundnum修改成2
            if (pIV(BFPNames::ScpdTrapeStr) == 1)
            {
                compoundNum = 2;
            }
        }
        // 由于凸阵拓展同样走的梯形网络，现在版本的网络不再支持compoundnum=3的情况
        // 目前先修改成凸阵拓展时，将compoundnum修改成2
        // 如果zeus后续修改支持或者凸阵拓展优化成4帧复合，可以去掉
        if (pBV(BFPNames::CurvedExapandingStr))
        {
            compoundNum = 2;
        }
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::CompoundNum), compoundNum, Dsc_Int);
    //当是两帧复合的时候，CompoundMethodFor2Frames参数要设置成1
    int compoundMethodFor2Frames = 0;
    if (2 == compoundNum)
    {
        compoundMethodFor2Frames = 1;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::CompoundMethodFor2Frames), compoundMethodFor2Frames, Dsc_Int);
}

void PostProcessParaController::onHighDensityChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    double densityFactor = 2.0;
#ifdef USE_HIGHDENSITY_INT
    int totalLineNum = 2;
    if (pIV(BFPNames::HighDensityStr) == HighDensity)
    {
        totalLineNum = 4;
    }
    if (isSupportAnyDensity())
    {
        densityFactor = 1.0;
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLines), pIV(BFPNames::B_RX_LNUMStr), Dsc_Int);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLines), probeDataInfo.WaferNum * 2, Dsc_Int);
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLinesColor), probeDataInfo.WaferNum * 2, Dsc_Int);
    }
#else
    // 固定线密度下TotalLines和TotalLinesColor固定为探头基元数的2倍（如SonoAir）
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLines), probeDataInfo.WaferNum * 2, Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLinesColor), probeDataInfo.WaferNum * 2, Dsc_Int);
#endif
    double lineSpacing = m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::LineSpacing)).toDouble();
    double angleSpacing = m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacing)).toDouble();
    //以下参数的设置在Zeus系统中未发现其变化，可以考虑去掉，减轻维护
#ifdef USE_HIGHDENSITY_INT
    if (pIV(BFPNames::HighDensityStr) == LowDensity)
#else
    if (!pBV(BFPNames::HighDensityStr))
#endif
    {
        lineSpacing *= densityFactor;
        angleSpacing *= densityFactor;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacingCompound), lineSpacing, Dsc_Double);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacingCompound), angleSpacing, Dsc_Double);

    onLineNumChanged();
    controlNeedleArgs();
}

void PostProcessParaController::onVS_ModeChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    if (!changed)
    {
        return;
    }

    onLineNumChanged();
}

void PostProcessParaController::onVS_MLAChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    if (!changed)
    {
        return;
    }

    onLineNumChanged();
}

void PostProcessParaController::onPixelSizeMMChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    double pixelSizeMM = value.toDouble() * (pDV(BFPNames::ImageZoomCoefStr) / 100);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PixelSizeMM), pixelSizeMM, Dsc_Double);
    sendROIPara();
}

void PostProcessParaController::onBaseLineChanged(const QVariant& value, bool changed)
{
    // Sonoair上的CW是伪CW，实际上是PW，因此isHPrfCW()是返回true，ATOM上的CW是真CW，isHPrfCW()返回false。
    //此处修改是因为Sonoair上调整基线出现问题，但同时需要考虑ATOM上调整基线的情况，因此需要考虑isHPrfCW()为false的情况,使用islikeCW来增加额外的判断。
    if (isTDI() || (!isHPrfCW() && SystemScanModeClassifier::isLikeCW(pIV(BFPNames::SystemScanModeStr))))
    {
        return;
    }

    doBaseLineChanged(value, changed);
}

void PostProcessParaController::onIsSupportIntegerDepthChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::IntegerDepthEnable), value.toBool(), Dsc_Bool);
}

void PostProcessParaController::onBaseLineCWDChanged(const QVariant& value, bool changed)
{
    if (!SystemScanModeClassifier::isLikeCW(pIV(BFPNames::SystemScanModeStr)))
    {
        return;
    }

    doBaseLineChanged(value, changed);
}

void PostProcessParaController::onBaseLineDTDIChanged(const QVariant& value, bool changed)
{
    if (!isTDI())
    {
        return;
    }

    doBaseLineChanged(value, changed);
}

void PostProcessParaController::onPWDynamicRangeChanged(const QVariant& value, bool changed)
{
#ifdef USE_TARGET_PALM
    // SonoAir的CW，在FPGA端采用的是PW的方案
    if (!(pBV(BFPNames::TriplexModeStr) || pBV(BFPNames::TDIEnStr)))
    {
        doPWDynamicRangeChanged(value, changed);
    }
#else
    if (!(pBV(BFPNames::TriplexModeStr) || pBV(BFPNames::TDIEnStr) || pBV(BFPNames::CWEnStr)))
    {
        doPWDynamicRangeChanged(value, changed);
    }
#endif
}

void PostProcessParaController::onPWDynamicRangeTMChanged(const QVariant& value, bool changed)
{
    if (isTriplex())
    {
        doPWDynamicRangeChanged(value, changed);
    }
}

void PostProcessParaController::onPWDynamicRangeTDIChanged(const QVariant& value, bool changed)
{
    if (isTDI())
    {
        doPWDynamicRangeChanged(value, changed);
    }
}

void PostProcessParaController::onPWDynamicRangeCWDChanged(const QVariant& value, bool changed)
{
    if (isCW())
    {
        doPWDynamicRangeChanged(value, changed);
    }
}

void PostProcessParaController::onColorLineChangingChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    // DO NOTHING
}

void PostProcessParaController::onNeedleAngleIndexChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    if (!changed)
    {
        return;
    }

    int angle = parameter(BFPNames::NeedleAngleIndexStr)->showValue().toInt();
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::NeedleAngle), angle, Dsc_Int);
}

void PostProcessParaController::onProbeIdChanged(const QVariant& value, bool changed)
{
    // TODO!!!探头的捆绑参数，考虑一次下发,zeusInterface考虑设置setlastvalue（禁止下发参数到zeus）以及一次刷新的逻辑
    if (!changed)
    {
        return;
    }
    onPixelSizeMMChanged(pV(BFPNames::PixelSizeMMStr), true);
    onDepthMMChanged(pIV(BFPNames::DepthMMStr), true);
    onRealDepthMMChanged(pFV(BFPNames::RealDepthMMStr), true);

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(value.toInt());
    ProbeParameters probeParameters(probeDataInfo);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::WaferRadius), probeDataInfo.WaferRadius, Dsc_Double);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ProbeAngle), probeParameters.probeAngleRad(), Dsc_Double);
    //        m_Values.insert(ProcessSonoParameterIndex::probeParameterName(ProcessSonoParameterIndex::WaferRadius),
    //        probeDataInfo.WaferRadius);
    //        m_Values.insert(ProcessSonoParameterIndex::probeParameterName(ProcessSonoParameterIndex::ProbeAngle),
    //                        probeParameters.probeAngleRad());
#ifdef USE_HIGHDENSITY_INT
    int totalLineNum = 2;
    if (pIV(BFPNames::HighDensityStr) == HighDensity)
    {
        totalLineNum = 4;
    }
#endif
    double densityFactor = 2.0;
#ifdef USE_HIGHDENSITY_INT
    if (isSupportAnyDensity())
    {
        densityFactor = 1.0;
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLines), pIV(BFPNames::B_RX_LNUMStr), Dsc_Int);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLines), probeDataInfo.WaferNum * totalLineNum, Dsc_Int);
    }
#else
    // 固定线密度下TotalLines和TotalLinesColor固定为探头基元数的2倍（如SonoAir）
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLines), probeDataInfo.WaferNum * 2, Dsc_Int);
#endif

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacing), pDV(BFPNames::AngleSpacingRadStr) / densityFactor,
             Dsc_Double);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacing), pDV(BFPNames::LineSpacingMMStr) / densityFactor,
             Dsc_Double);

#ifdef USE_HIGHDENSITY_INT
    if (isSupportAnyDensity())
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLinesColor), pIV(BFPNames::C_RX_LNUMStr), Dsc_Int);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLinesColor), probeDataInfo.WaferNum * totalLineNum,
                 Dsc_Int);
    }
#else
    // 固定线密度下TotalLines和TotalLinesColor固定为探头基元数的2倍（如SonoAir）
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLinesColor), probeDataInfo.WaferNum * 2, Dsc_Int);
#endif

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacingColor),
             pDV(BFPNames::AngleSpacingRadStr) / densityFactor, Dsc_Double);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacingColor),
             ((qreal)probeDataInfo.WaferLength * probeDataInfo.WaferNum) / pIV(BFPNames::C_RX_LNUMStr), Dsc_Double);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::IsLinear), probeDataInfo.IsLinear, Dsc_Bool);

#ifdef USE_HIGHDENSITY_INT
    if (pIV(BFPNames::HighDensityStr) == LowDensity)
#else
    if (!pBV(BFPNames::HighDensityStr))
#endif
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacingCompound), pDV(BFPNames::AngleSpacingRadStr),
                 Dsc_Double);
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacingCompound), pDV(BFPNames::LineSpacingMMStr),
                 Dsc_Double);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacingCompound),
                 pDV(BFPNames::AngleSpacingRadStr) / densityFactor, Dsc_Double);

        sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacingCompound),
                 pDV(BFPNames::LineSpacingMMStr) / densityFactor, Dsc_Double);
    }

    controlNeedleArgs();
}

void PostProcessParaController::onLowFilterAxialSigmaChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    if (!changed)
    {
        return;
    }
    dealSmoothChanged();
}

void PostProcessParaController::onLowFilterLateralSigmaChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    if (!changed)
    {
        return;
    }
    dealSmoothChanged();
}

void PostProcessParaController::onTrapezoidalCPDSteerChanged(const QVariant& value, bool changed)
{
    if (!pBV(BFPNames::PA_VERT_DIST_EnableStr))
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::TrapzoidalAngle), value.toInt(), Dsc_Int);
    }
}

void PostProcessParaController::onTrapezoidalCPDSteer2Changed(const QVariant& value, bool changed)
{
    onCPDSteer2Changed(value, changed);
}

void PostProcessParaController::onCPDSteerChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    processCPDSteerChanged(POSTPARANAME(ProcessSonoParameterIndex::SteeringAngle), value);
}

void PostProcessParaController::onCPDSteer2Changed(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    processCPDSteerChanged(POSTPARANAME(ProcessSonoParameterIndex::SteeringAngle2), value);
}

void PostProcessParaController::onCPDSteer3Changed(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    processCPDSteerChanged(POSTPARANAME(ProcessSonoParameterIndex::SteeringAngle3), value);
}

void PostProcessParaController::onCPDSteer4Changed(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    processCPDSteerChanged(POSTPARANAME(ProcessSonoParameterIndex::SteeringAngle4), value);
}

void PostProcessParaController::onCPDSteer5Changed(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    processCPDSteerChanged(POSTPARANAME(ProcessSonoParameterIndex::SteeringAngle5), value);
}

void PostProcessParaController::onXContrastValueChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    double constastLevel = value.toDouble();
    bool contrastEnable = !RealCompare::AreEqual(constastLevel, 1.0);
#ifdef SYS_APPLE
    contrastEnable = false;
#endif
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::XcontrastEnable), contrastEnable, Dsc_Bool);

    constastLevel = RealCompare::AreEqual(constastLevel, 0.0) ? 0.9 : constastLevel;
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ContrastLevel), constastLevel, Dsc_Double);
}

void PostProcessParaController::onSpectralInvertChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PWInvert), value, Dsc_Bool);
}

void PostProcessParaController::onBSteeringScanChanged(const QVariant& value, bool changed)
{
    int steering = value.toInt() - pMax(BFPNames::BSteeringScanStr) / 2;
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::B2DSteeringAngle), steering, Dsc_Int);

    if (pBV(BFPNames::TrapezoidalModeStr))
    {
        onCPDSteerChanged(pV(BFPNames::TrapezoidalCPDSteerStr), changed);
        onCPDSteer2Changed(pV(BFPNames::TrapezoidalCPDSteer2Str), changed);
    }
    else
    {
        onCPDSteerChanged(pV(BFPNames::CPDSteerStr), changed);
        onCPDSteer2Changed(pV(BFPNames::CPDSteer2Str), changed);
        onCPDSteer3Changed(pV(BFPNames::CPDSteer3Str), changed);
        onCPDSteer4Changed(pV(BFPNames::CPDSteer4Str), changed);
        onCPDSteer5Changed(pV(BFPNames::CPDSteer5Str), changed);
    }
}

void PostProcessParaController::onCompoundDebugChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    int debugType = value.toInt();
    if ((debugType > 0) && (debugType < 7))
    {
        // 4帧复合版本梯形下fpga不会再上无偏转的数据,在debugType调整为2时会出现崩溃，因此在这里
        if (debugType == 2 && pBV(BFPNames::TrapezoidalModeStr))
        {
            debugType = 0;
        }
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::CompoundDebug), debugType, Dsc_Int);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::CompoundDebug), 0, Dsc_Int);
    }
}

void PostProcessParaController::onColorTransparencyChanged(const QVariant& value, bool changed)
{
    if (isColor())
    {
        doColorTransparencyChanged(value, changed);
    }
}

void PostProcessParaController::onTDITransparencyChanged(const QVariant& value, bool changed)
{
    if (isTDI())
    {
        doColorTransparencyChanged(value, changed);
    }
}

void PostProcessParaController::onMVITransparencyChanged(const QVariant& value, bool changed)
{
    if (isMVI())
    {
        doColorTransparencyChanged(value, changed);
    }
}

void PostProcessParaController::onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)
    // do nothing
}

void PostProcessParaController::onSystemScanModeChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    bool isOn = false;
    QString transparencyName;
    if (value.toInt() == SystemScanModeSonoNeedle)
    {
        isOn = true;
        transparencyName = BFPNames::ColorTransparencyStr;
    }
    else if (value.toInt() == SystemScanModeTissueDoppler || value.toInt() == SystemScanModeTissuePW ||
             value.toInt() == SystemScanModeTDIM || value.toInt() == SystemScanModeTDILRBM ||
             value.toInt() == SystemScanModeTDIUDBM)
    {
        isOn = true;
        transparencyName = BFPNames::TDITransparencyStr;
    }
    else if ((value.toInt() == SystemScanModeMVI) || (value.toInt() == SystemScanModeMVIPW))
    {
        isOn = true;
        transparencyName = BFPNames::MVITransparencyStr;
    }
    else if (SystemScanModeClassifier::isLikeD(value.toInt()))
    {
        if (SystemScanModeClassifier::isLikeCW(value.toInt()))
        {
            if (isHPrfCW())
            {
                onBaseLineChanged(pIV(BFPNames::BaseLineStr), true);
            }
            else
            {
                onBaseLineCWDChanged(pIV(BFPNames::BaseLineCWDStr), true);
            }
        }
        else
        {
            onBaseLineChanged(pIV(BFPNames::BaseLineStr), true);
        }
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorTransparencyEnable), isOn, Dsc_Bool);

    doColorTransparencyChanged(pIV(transparencyName), true);
}

void PostProcessParaController::onShowBInROIChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::NeedToEraseB), !value.toBool(), Dsc_Bool);
}

void PostProcessParaController::onMVITypeChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    int mviType = value.toInt();

    mviType = (mviType == 2) ? 2 : 1;

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::DynFlowMixType), mviType, Dsc_Int);
}

void PostProcessParaController::onDopOutGainChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)

    if (isTDI())
    {
        return;
    }

    doOutGainChanged(value, changed);
}

void PostProcessParaController::onIsRoiVisibleChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::EnableColorDisplay), !value.toBool(), Dsc_Bool);
}

void PostProcessParaController::onPostRawDataEnableChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorRawDataCallbackEnable), value, Dsc_Bool);
}

void PostProcessParaController::onMBChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::MultiBeamNumber), qPow(2, value.toInt()), Dsc_Int);

    calMultiBeam();
}

void PostProcessParaController::onStripRemoveEnableChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    bool stripRemoveEnable = value.toBool() && (pIV(BFPNames::MBStr) == 2);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::StripRemoveEnable), stripRemoveEnable, Dsc_Bool);

    calMultiBeam();
}

void PostProcessParaController::onOverlappingMBChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    // value 取值范围：[0,3] 步长 0.5 由于担心浮点数出现精度误差，因此采用10倍上取整方式传值
    int overlappingMB = ceil(value.toDouble() * 10);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::OverlappingMB), overlappingMB, Dsc_Int);

    calMultiBeam();
}

void PostProcessParaController::onInterpolatorOrderChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(changed)
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::InterpolatorOrder), value, Dsc_Int);

    calMultiBeam();
}

void PostProcessParaController::onGainChanged(const QVariant& value, bool changed)
{
    if (!isTHI())
    {
        doGainChanged(value, changed);
    }
}

void PostProcessParaController::onGainThiChanged(const QVariant& value, bool changed)
{
    if (isTHI())
    {
        doGainChanged(value, changed);
    }
}

void PostProcessParaController::onMGainChanged(const QVariant& value, bool changed)
{
    if (!isTHI())
    {
        doMGainChanged(value, changed);
    }
}

void PostProcessParaController::onMGainThiChanged(const QVariant& value, bool changed)
{
    if (isTHI())
    {
        doMGainChanged(value, changed);
    }
}

void PostProcessParaController::onDynamicRangeChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    if (isFreezeState())
    {
        bool isEqualValue = true;
        if (m_IsSonoparametersChanging)
        {
            sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMDR), value, Dsc_Int);
            sendPara(POSTPARANAME(ProcessSonoParameterIndex::PreMDynamicRange), value, Dsc_Int);
        }
        else
        {
            QVariant lastBMDRValue = m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::BMDR));
            QVariant lastPreMDynamicRange = m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::PreMDynamicRange));
            isEqualValue = ((lastBMDRValue == value) && (lastPreMDynamicRange == value));
        }

        sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMDRAfterFreeze), value, Dsc_Int);
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::PreMDynamicRangeAfterFreeze), value, Dsc_Int);
        controlProcessAfterFreezeEnable(isEqualValue);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMDR), value, Dsc_Int);
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::PreMDynamicRange), value, Dsc_Int);
    }
}

void PostProcessParaController::onTGCChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    QByteArray postTgc = value.toByteArray();
    bool bFreezeState = isFreezeState();
    for (int i = 0; i < TGC_COUNT; ++i)
    {

        if (bFreezeState)
        {
            bool isEqualValue = true;
            if (m_IsSonoparametersChanging)
            {
                sendPara(POSTPARANAMEARG(ProcessSonoParameterIndex::BMDTGC_Gain$).arg(i), (uchar)postTgc[i], Dsc_Int);
            }
            else
            {
                char lastPostTgc =
                    m_Values.value(POSTPARANAMEARG(ProcessSonoParameterIndex::BMDTGC_Gain$).arg(i)).toByteArray()[0];
                isEqualValue = (lastPostTgc == value);
            }
            sendPara(POSTPARANAMEARG(ProcessSonoParameterIndex::BMDTGCAfterFreeze_Gain$).arg(i), (uchar)postTgc[i],
                     Dsc_Int);
            controlProcessAfterFreezeEnable(isEqualValue);
        }
        else
        {
            sendPara(POSTPARANAMEARG(ProcessSonoParameterIndex::BMDTGC_Gain$).arg(i), (uchar)postTgc[i], Dsc_Int);
        }
    }
}

void PostProcessParaController::onLGC0Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    updatelgcValue(value, 0);
}

void PostProcessParaController::onLGC1Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    updatelgcValue(value, 1);
}

void PostProcessParaController::onLGC2Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    updatelgcValue(value, 2);
}

void PostProcessParaController::onLGC3Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    updatelgcValue(value, 3);
}

void PostProcessParaController::onLGC4Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    updatelgcValue(value, 4);
}

void PostProcessParaController::onLGC5Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    updatelgcValue(value, 5);
}

void PostProcessParaController::onLGC6Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    updatelgcValue(value, 6);
}

void PostProcessParaController::onLGC7Changed(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    updatelgcValue(value, 7);
}

void PostProcessParaController::onTDIEnChanged(const QVariant& value, bool changed)
{
    // BeamFormerBase::onGettingGainControlTableValue函数中
    // TDI打开的时候，Gain的值会加上BWGainDelta，这里在TDI改变的时候，更新Gain
    Q_UNUSED(value)
    Q_UNUSED(changed)
    QString gainName = pBV(BFPNames::THIStr) ? BFPNames::GainThiStr : BFPNames::GainStr;
    parameter(gainName)->update(true);
}

void PostProcessParaController::onAngleSpacingRadChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    double densityFactor = 2.0;
    if (isSupportAnyDensity())
    {
        densityFactor = 1.0;
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacingColor), value.toDouble() / densityFactor,
                 Dsc_Double);
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacing), value.toDouble() / densityFactor, Dsc_Double);

#ifdef USE_HIGHDENSITY_INT
    if (pIV(BFPNames::HighDensityStr) == LowDensity)
#else
    if (!pBV(BFPNames::HighDensityStr))
#endif
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacingCompound), value, Dsc_Double);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacingCompound), value.toDouble() / densityFactor,
                 Dsc_Double);
    }
}

void PostProcessParaController::onLineSpacingMMChanged(const QVariant& value, bool changed)
{
    double densityFactor = 2.0;
    if (isSupportAnyDensity())
    {
        densityFactor = 1.0;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacing), value.toDouble() / densityFactor, Dsc_Double);

#ifdef USE_HIGHDENSITY_INT
    if (pIV(BFPNames::HighDensityStr) == LowDensity)
#else
    if (!pBV(BFPNames::HighDensityStr))
#endif
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacingCompound), value, Dsc_Double);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacingCompound), value.toDouble() / densityFactor,
                 Dsc_Double);
    }

    controlNeedleArgs();
}

void PostProcessParaController::onColorLineDensityChanged(const QVariant& value, bool changed)
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    double linespacing = ((qreal)probeDataInfo.WaferLength * probeDataInfo.WaferNum) / pIV(BFPNames::C_RX_LNUMStr);

    double densityFactor = 2.0;
    if (isSupportAnyDensity())
    {
        densityFactor = 1.0;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::TotalLinesColor), pIV(BFPNames::C_RX_LNUMStr), Dsc_Int);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacingColor),
             pDV(BFPNames::AngleSpacingRadStr) / densityFactor, Dsc_Double);

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacingColor), linespacing, Dsc_Double);
    sendROIPara();
}

void PostProcessParaController::onWaveImageScaleFactorChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    controlImageRects();

    // [Apple][BUG:80596]【V1.8Release】【必发】CW模式下打图，调节DISP Format，B图像会出现无图现象
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::OutputDisplayWidthForZoom),
             pIV(BFPNames::DisplayWidthForPanZoomStr), Dsc_Int);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::OutputDisplayHeightForZoom),
             pIV(BFPNames::DisplayHeightForPanZoomStr), Dsc_Int);
}

void PostProcessParaController::onSRIHfnoiseChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterHFNoise), value, Dsc_Double);
}

void PostProcessParaController::onSRIEdgeThresholdChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterEdgeThreshold), value, Dsc_Double);
}

void PostProcessParaController::onSRIDetailPreservationChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterDetailPreservation), value, Dsc_Double);
}

void PostProcessParaController::onSRIOverallStrengthChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterOverallStrength), value, Dsc_Double);
}

void PostProcessParaController::onSRIEdgeRampDownChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterEdgeRampDown), value, Dsc_Int);
}

void PostProcessParaController::onSRIEdgeDirectionThreshChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterEdgeDirectionThresh), value, Dsc_Int);
}

void PostProcessParaController::onSRIHoleFillerThreshChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterHoleFillerThresh), value, Dsc_Int);
}

void PostProcessParaController::onSRINoiseFilterTypeChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterNoiseFilterType), value, Dsc_Int);
}

void PostProcessParaController::onSRIEdgeFilterTypeChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterEdgeFilterType), value, Dsc_Int);
}

void PostProcessParaController::onSRIEnableChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterEnable), value, Dsc_Bool);
}

void PostProcessParaController::onSRINoiseLevelChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterNoiseLevel), convertStringToFloatByteArray(value),
             Dsc_Array);
}

void PostProcessParaController::onSRIEdgeEnhanceChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterEdgeEnhance), convertStringToFloatByteArray(value),
             Dsc_Array);
}

void PostProcessParaController::onSRIFilterStrengthChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ImageFilterFilterStrength), convertStringToFloatByteArray(value),
             Dsc_Array);
}

void PostProcessParaController::onStartLineColorChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    sendROIPara();
}

void PostProcessParaController::onTopBorderColorChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    sendROIPara();
}

void PostProcessParaController::onTDILineDensityChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    sendROIPara();
}

void PostProcessParaController::onElastoColorLineDensityChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    sendROIPara();
}

void PostProcessParaController::onMVILineDensityChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    sendROIPara();
}

// void PostProcessParaController::onColorLineDensitySNChanged(const QVariant &value, bool changed)
//{
//    Q_UNUSED(value)
//    Q_UNUSED(changed)
//    sendROIPara();
//}

void PostProcessParaController::onSteeringAngleChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    sendROIPara();
}

void PostProcessParaController::onSteeringAngleSNChanged(const QVariant& value, bool changed)
{
    Q_UNUSED(value)
    Q_UNUSED(changed)
    sendROIPara();
}

void PostProcessParaController::onPanZoomSelectChanged(const QVariant& value, bool changed)
{
    sendPanZoomPara();
}

void PostProcessParaController::onImageZoomCoefChanged(const QVariant& value, bool changed)
{
    sendPanZoomPara();
}

void PostProcessParaController::onDisplayStartXForPanZoomChanged(const QVariant& value, bool changed)
{
    sendPanZoomPara();
}

void PostProcessParaController::onDisplayStartYForPanZoomChanged(const QVariant& value, bool changed)
{
    sendPanZoomPara();
}

void PostProcessParaController::onDisplayWidthForPanZoomChanged(const QVariant& value, bool changed)
{
    sendPanZoomPara();
}

void PostProcessParaController::onDisplayHeightForPanZoomChanged(const QVariant& value, bool changed)
{
    sendPanZoomPara();
}

void PostProcessParaController::onBeforeSetSonoParameters()
{
    m_IsSonoparametersChanging = true;
}

void PostProcessParaController::onSetSonoParameters()
{
    if (m_SonoParameters == nullptr)
    {
        return;
    }
    sendPanZoomPara();
    sendROIPara();
    sendRegionZoomPara();

    controlPostBlockData();
    autoCallSelfObjectConnetedChangedSlots();

    m_IsSonoparametersChanging = false;
}

void PostProcessParaController::dealSmoothChanged()
{
    double axialSigma = pFV(BFPNames::LowFilterAxialSigmaStr);
    double lateralSigma = pFV(BFPNames::LowFilterLateralSigmaStr);
    bool smoothEnable = !(RealCompare::AreEqual(axialSigma, 0.0) && RealCompare::AreEqual(lateralSigma, 0.0));
#ifdef SYS_APPLE
    smoothEnable = false;
#endif
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::SmoothEnable), smoothEnable, Dsc_Bool);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::AxialSigma), axialSigma, Dsc_Double);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::LateralSigma), lateralSigma, Dsc_Double);
}

void PostProcessParaController::processCPDSteerChanged(const QString& name, const QVariant& value)
{
    if (m_SonoParameters == nullptr)
    {
        return;
    }

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    int v = 0;
    if (probeDataInfo.IsLinear)
    {
        if ((!parameter(BFPNames::BSteeringScanStr)->isEnabled()) ||
            (m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::B2DSteeringAngle)).toInt() == 0))
        {
            v = value.toInt();
        }
    }
    else
    {
        v = value.toInt();
    }

    sendPara(name, v, Dsc_Int);
}

bool PostProcessParaController::isHPrfCW() const
{
    return ModelConfig::instance().value(ModelConfig::HPrfCW, false).toBool();
}

bool PostProcessParaController::isSupportAnyDensity() const
{
    return ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity).toBool();
}

void PostProcessParaController::calMultiBeam()
{
    bool stripRemoveEnable = pBV(BFPNames::StripRemoveEnableStr) && (pIV(BFPNames::MBStr) == 2);
    if (stripRemoveEnable)
    {
        MultiBeamCalculator calculator(m_SonoParameters);

        sendPara(POSTPARANAME(ProcessSonoParameterIndex::StartLine), calculator.startLine(), Dsc_Int);
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::StopLine), calculator.stopLine(), Dsc_Int);

        ProbeParameters probePara(m_SonoParameters, true);

        sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacing),
                 probePara.probeWidthMM() / calculator.totalLine(), Dsc_Double);

        sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacing),
                 probePara.probeAngleRad() / calculator.totalLine(), Dsc_Double);

        sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineNum), calculator.scanLine(), Dsc_Int);
    }
    else
    {
        double densityFactor = 2.0;
        if (isSupportAnyDensity())
        {
            densityFactor = 1.0;
        }

        sendPara(POSTPARANAME(ProcessSonoParameterIndex::StartLine), pIV(BFPNames::StartLineStr), Dsc_Int);
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::StopLine), pIV(BFPNames::StopLineStr), Dsc_Int);

        sendPara(POSTPARANAME(ProcessSonoParameterIndex::LineSpacing), pDV(BFPNames::LineSpacingMMStr) / densityFactor,
                 Dsc_Double);

        sendPara(POSTPARANAME(ProcessSonoParameterIndex::AngleSpacing),
                 pDV(BFPNames::AngleSpacingRadStr) / densityFactor, Dsc_Double);
    }
}

bool PostProcessParaController::isFreezeState() const
{
    return !isRealTime() || pBV(BFPNames::FreezeStr);
}

void PostProcessParaController::controlProcessAfterFreezeEnable(bool isEqualValue)
{
    bool bFreezeState = isFreezeState();
    if (bFreezeState)
    {
        bool isProcessAfterFreezeEnable =
            m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::ProcessAfterFreezeEnable)).toBool();
        if (!isProcessAfterFreezeEnable && !isEqualValue)
        {
            sendPara(POSTPARANAME(ProcessSonoParameterIndex::ProcessAfterFreezeEnable), true, Dsc_Bool);
        }
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::ProcessAfterFreezeEnable), false, Dsc_Bool);
    }
}

void PostProcessParaController::updatelgcValue(QVariant value, int tgcIndex)
{
    char lgcValue = value.toByteArray()[0];
    if (isFreezeState())
    {
        bool isEqualValue = true;
        if (m_IsSonoparametersChanging)
        {
            sendPara(POSTPARANAMEARG(ProcessSonoParameterIndex::BMHTGC_Value$).arg(tgcIndex), (uchar)lgcValue, Dsc_Int);
        }
        else
        {
            char lastPostLgc = m_Values.value(POSTPARANAMEARG(ProcessSonoParameterIndex::BMHTGC_Value$).arg(tgcIndex))
                                   .toByteArray()[0];
            isEqualValue = (lastPostLgc == value);
        }
        sendPara(POSTPARANAMEARG(ProcessSonoParameterIndex::BMHTGC_AfterFreezeValue$).arg(tgcIndex), (uchar)lgcValue,
                 Dsc_Int);
        controlProcessAfterFreezeEnable(isEqualValue);
    }
    else
    {
        sendPara(POSTPARANAMEARG(ProcessSonoParameterIndex::BMHTGC_Value$).arg(tgcIndex), (uchar)lgcValue, Dsc_Int);
    }
}

void PostProcessParaController::controlPostBlockData()
{
    QHash<QString, ProcessSonoParameterIndex::PostProcessSonoParameterIndex> blockNamesHash;
    blockNamesHash.insert("data42", ProcessSonoParameterIndex::BlockData42);
    blockNamesHash.insert("data43", ProcessSonoParameterIndex::BlockData43);
    blockNamesHash.insert("data254", ProcessSonoParameterIndex::BlockData254);

    ProbeBlockDataSet& dataSet = ProbeBlockDataSet::instance();
    const ProbeDataInfo& probeInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
    if (dataSet.contains(probeInfo.Name))
    {
        ProbeBlockDataGroups& pbdGroups = dataSet.probeBolckDataGroups(probeInfo.Name);
        BlockDataGroup& bdGroups = pbdGroups.normalGroup();
        for (int i = 0; i < bdGroups.count(); i++)
        {
            BlockData& blockData = bdGroups[i];
            QString blockDatafileName = blockData.fileName();
            if (blockNamesHash.contains(blockDatafileName))
            {
                QString filename = pbdGroups.dataFilePath(bdGroups, blockData);
                if (blockData.open(filename))
                {
                    QByteArray data(blockData.size(), 0);
                    if (blockData.read(data.data(), data.length()) == data.length())
                    {
                        sendPara(POSTPARANAME(blockNamesHash[blockDatafileName]), data, Dsc_Array, data.length());
                    }
                    blockData.close();
                }
            }
        }
        int groupIndex = pbdGroups.indexOfParaGroups("PostProcess");
        if (groupIndex >= 0)
        {
            BlockDataGroup& bdg = pbdGroups.paraGroup(groupIndex);
            for (int i = 0; i < bdg.count(); ++i)
            {
                BlockData& blockData = bdg[i];
                QString blockDatafileName = blockData.fileName();
                if (blockNamesHash.contains(blockDatafileName))
                {
                    QString filename = pbdGroups.dataFilePath(bdg, blockData);
                    if (blockData.open(filename))
                    {
                        QByteArray data(blockData.size(), 0);
                        if (blockData.read(data.data(), data.length()) == data.length())
                        {
                            sendPara(POSTPARANAME(blockNamesHash[blockDatafileName]), data, Dsc_Array, data.length());
                        }
                        blockData.close();
                    }
                }
            }
        }
    }
}

void PostProcessParaController::controlNeedleArgs()
{
    // TODO: LineSpacing和线密度的关系需要在前面做统一修改
    double densityFactor = 1.0;

    // task:10528,如果不加非任意线密度的判断，那么NeedleLineSpacing和LineSpacingCompound会不一致。
    // TODO：后续会对这块代码进行整理优化。
    if (!pBV(BFPNames::HighDensityStr) && !isSupportAnyDensity())
    {
        densityFactor = 2.0;
    }

    double valueDouble = m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::LineSpacing)).toDouble();
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::NeedleLineSpacing), valueDouble * densityFactor, Dsc_Double);
}

QByteArray PostProcessParaController::convertStringToFloatByteArray(const QVariant& value)
{
    QStringList sl = value.toString().split(",");
    QList<float> lFloat = Util::stringList2FloatList(sl);
    QByteArray newValue;
    newValue.resize(lFloat.count() * 4);
    char* newValueData = newValue.data();
    for (int i = 0; i < lFloat.count(); ++i)
    {
        memcpy(newValueData + i * 4, &(lFloat[i]), 4);
    }
    return newValue;
}

void PostProcessParaController::controlImageRects()
{
    QSize imageSize;
    QList<QRect> imageRects;
    bool force = false;
    int layout = pIV(BFPNames::LayoutStr);
    if ((layout == Layout_1x2 || pBV(BFPNames::BCImagesOnStr)) && pBV(BFPNames::PanZoomSelectStr))
    {
        force = true;
    }
    imageSize = pV(BFPNames::RenderImageSizeStr).toSize();
    if (Layout_2x2 == layout)
    {
        imageSize.setHeight(imageSize.height() / 2);
        imageSize.setWidth(imageSize.width() / 2);
    }
    QList<ZeusHashValue> valus;

    ZeusHashValue zeusHashValue;

    zeusHashValue.m_Value = imageSize.width();
    zeusHashValue.m_Type = Dsc_Int;
    zeusHashValue.m_Size = 0;
    zeusHashValue.m_Name = POSTPARANAME(ProcessSonoParameterIndex::DisplayWidth);
    valus.append(zeusHashValue);
    zeusHashValue.m_Value = imageSize.height();
    zeusHashValue.m_Name = POSTPARANAME(ProcessSonoParameterIndex::DisplayHeight);
    valus.append(zeusHashValue);
#ifdef SYS_APPLE
    QList<QVariant> listVariant = pV(BFPNames::ImageRectsStr).toList();
    imageRects = VariantUtil::variant2Rects(listVariant);
    if (SystemScanModeClassifier::isLikeD(pIV(BFPNames::SystemScanModeStr)))
    {
        zeusHashValue.Value = 256;
    }
    else
    {
        zeusHashValue.Value = pV(BFPNames::ImageSizeStr).toSize().height();
    }
    zeusHashValue.Name = POSTPARANAME(ProcessSonoParameterIndex::WavePointNum);
    valus.append(zeusHashValue);
    zeusHashValue.Name = POSTPARANAME(ProcessSonoParameterIndex::WaveLineNum);
    zeusHashValue.Value = imageSize.width();
    valus.append(zeusHashValue);
#else
    int imageHeight = pV(BFPNames::DSCImageSizeStr).toSize().height();
    ImageScale imageScale = pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>();
    if (force)
    {
        imageScale.xScale = 1.0;
        imageScale.yScale = 1.0;
    }
    qDebug() << "imageScale.xScale" << imageScale.xScale << "imageScale.yScale" << imageScale.yScale;
    imageHeight = qCeil(imageScale.yScale * imageHeight);

    BFDSCScanAreaWidthParameter bfDSCScanAreaWidthParameter(m_SonoParameters);
    int renderWidth = qCeil(bfDSCScanAreaWidthParameter.renderImageWidth());
    if (getSystemScanMode() != SystemScanModeCP)
    {
        renderWidth = qBound(qCeil(pV(BFPNames::DSCImageSizeStr).toSize().width() * imageScale.yScale), renderWidth,
                             pV(BFPNames::DSCImageSizeStr).toSize().width());
    }

    //多布局的情况下，上下布局因为图像本身已经被缩小，所以b图像y方向要乘以布局系数
    imageRects.append(QRect(0, 0, renderWidth, imageHeight));

    QList<QVariant> dscImageRects = pV(BFPNames::DSCImageRectsStr).toList();
    QRect dscImageRect = dscImageRects.last().toRect();
    QSize dscImageSize = dscImageRect.size();

    imageHeight = dscImageSize.height();
    imageScale = pV(BFPNames::WaveImageScaleFactorStr).value<ImageScale>();
    if (force)
    {
        imageScale.xScale = 1.0;
        imageScale.yScale = 1.0;
    }
    imageHeight = qCeil(imageScale.yScale * imageHeight);
    if (pBV(BFPNames::BCImagesOnStr) && (getSystemScanMode() != SystemScanModeE))
    {
        imageRects.append(QRect(0, 0, renderWidth, imageHeight));
    }
    else
    {
        imageRects.append(QRect(0, 0, qCeil(dscImageSize.width() * imageScale.xScale), imageHeight));
    }
    if (SystemScanModeClassifier::isLikeD(pIV(BFPNames::SystemScanModeStr)) ||
        pIV(BFPNames::SystemScanModeStr) == SystemScanModeAV)
    {
#ifdef USE_PREPROCESS
        zeusHashValue.m_Value = 256; // PW前处理按照16bit方式处理，每个线固定返回256点，每个店16bit
#else
        zeusHashValue.Value = pIV(BFPNames::PointNumPerLineStr);
#endif
    }
    else
    {
        zeusHashValue.m_Value = pIV(BFPNames::PointNumPerLineStr);
    }
    zeusHashValue.m_Name = POSTPARANAME(ProcessSonoParameterIndex::WavePointNum);
    valus.append(zeusHashValue);
    zeusHashValue.m_Name = POSTPARANAME(ProcessSonoParameterIndex::WaveLineNum);
    zeusHashValue.m_Value = imageRects.last().width();
    valus.append(zeusHashValue);
#endif
    int index = 1;
    foreach (const QRect& imageRect, imageRects)
    {
        zeusHashValue.m_Name = POSTPARANAMEARG(ProcessSonoParameterIndex::Layout$StartX).arg(index);
        zeusHashValue.m_Value = imageRect.x();
        valus.append(zeusHashValue);
        zeusHashValue.m_Name = POSTPARANAMEARG(ProcessSonoParameterIndex::Layout$StartY).arg(index);
        zeusHashValue.m_Value = imageRect.y();
        valus.append(zeusHashValue);
        zeusHashValue.m_Name = POSTPARANAMEARG(ProcessSonoParameterIndex::Layout$Width).arg(index);
        zeusHashValue.m_Value = imageRect.width();
        valus.append(zeusHashValue);
        zeusHashValue.m_Name = POSTPARANAMEARG(ProcessSonoParameterIndex::Layout$Height).arg(index);
        zeusHashValue.m_Value = imageRect.height();
        valus.append(zeusHashValue);
        ++index;
    }

    //这里刷新的参数不需要下发enforce
    m_APIInterface->setZeusParameter(m_HandleIndex, valus);
}

void PostProcessParaController::controlColorMap(uchar* image2DMap, uchar* imageWaveMap)
{
    if (!m_APIInterface->isHandleValid(m_HandleIndex))
    {
        return;
    }

    m_APIInterface->setZeusParameterTrigger(m_HandleIndex,
                                            POSTPARANAME(ProcessSonoParameterIndex::ColorMap2DChangedTrigger));
    QByteArray image2DMapData(reinterpret_cast<const char*>(image2DMap), COLORMAP_BUFFER_SIZE);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorMap2D), image2DMapData, Dsc_Array, COLORMAP_BUFFER_SIZE);

    m_APIInterface->setZeusParameterTrigger(m_HandleIndex,
                                            POSTPARANAME(ProcessSonoParameterIndex::ColorMapWaveChangedTrigger));
    QByteArray imageWaveMapData(reinterpret_cast<const char*>(imageWaveMap), COLORMAP_BUFFER_SIZE);
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorMapWave), imageWaveMapData, Dsc_Array, COLORMAP_BUFFER_SIZE);

    if (Setting::instance().defaults().colorMapLog())
    {
        Util::Mkdir("updateColorMap");
        QFile qfile1(QString("updateColorMap/colormap1.dat"));
        if (qfile1.open(QIODevice::WriteOnly))
        {
            qfile1.write((const char*)(image2DMap), 256 * 4);
            qfile1.close();
        }
        QFile qfile2(QString("updateColorMap/colormap2.dat"));
        if (qfile2.open(QIODevice::WriteOnly))
        {
            qfile2.write((const char*)(imageWaveMap), 256 * 4);
            qfile2.close();
        }
    }
}

void PostProcessParaController::controlImageTilePara()
{
    onScpdChanged(pV(BFPNames::ScpdStr), true);
}

// void PostProcessParaController::flushSentPara()
//{
//    m_APIInterface->reflushParas(m_HandleIndex);
//}

int PostProcessParaController::getSystemScanMode() const
{
    int systemScanMode = pIV(BFPNames::SystemScanModeStr);
    if (m_SonoParameters != NULL)
    {
        if (systemScanMode == SystemScanModeAV)
        {
            return SystemScanModeB;
        }
        else if (systemScanMode == SystemScanModeCP)
        {
            return SystemScanModeB;
        }
        else if (systemScanMode == SystemScanModeSonoNeedle)
        {
            return SystemScanModePowerDoppler;
        }
        else
        {
            return systemScanMode;
        }
    }
    return -1;
}

void PostProcessParaController::sendPara(QString POSTPARANAME, const QVariant& value, ParameterType type,
                                         int pointerSize)
{
    m_Values[POSTPARANAME] = value;
    m_APIInterface->setZeusParameter(m_HandleIndex, POSTPARANAME, value, type, pointerSize);
    doEnforce(POSTPARANAME);
}

void PostProcessParaController::doEnforce(QString POSTPARANAME)
{
    if (!m_EnforceEnable || m_SonoParameters == nullptr ||
        !ZeusAPIInterface::EnForceParameterNames.contains(POSTPARANAME))
    {
        return;
    }

    if (isFreezeState() || SystemScanModeClassifier::isLikeD(getSystemScanMode()))
    {
        if (POSTPARANAME.endsWith("Enable"))
        {
            POSTPARANAME.remove(QRegExp("Enable$"));
        }
        m_APIInterface->setZeusParameterTrigger(m_HandleIndex, POSTPARANAME + "EnForce");
    }
}

void PostProcessParaController::doPWDynamicRangeChanged(const QVariant& value, bool changed)
{
    int DynamicRangeIndex = value.toInt();
    int dynamicrangevalue =
        46 + 2 * DynamicRangeIndex; // PW动态范围参数从控制表到Zeus的映射关系：DR_zeus = 46 + 2 * DR_ct
    if (changed)
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::DynamicRange), dynamicrangevalue, Dsc_Int);
    }
}

void PostProcessParaController::doDopVelocityFilterChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::MinThr), value, Dsc_Int);
}

void PostProcessParaController::doFrameAvgColorChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PersistenceLevelColor), value, Dsc_Int);
}

void PostProcessParaController::doBloodEffectionChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::BloodEffect), value, Dsc_Int);
}

void PostProcessParaController::doLComeBackChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::LComeBack), value, Dsc_Bool);
}

void PostProcessParaController::doAComeBackChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::AComeBack), value, Dsc_Bool);
}

void PostProcessParaController::doPowerThresholdChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::PowerThreshold), value, Dsc_Int);
}

void PostProcessParaController::doGainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    if (isFreezeState())
    {
        bool isEqualValue = true;
        if (m_IsSonoparametersChanging)
        {
            sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMGainValue), value, Dsc_Int);
        }
        else
        {
            QVariant lastBMGainValue = m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::BMGainValue));
            isEqualValue = (lastBMGainValue == value);
        }
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMGainValueAfterFreeze), value, Dsc_Int);
        controlProcessAfterFreezeEnable(isEqualValue);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::BMGainValue), value, Dsc_Int);
    }
}

void PostProcessParaController::doMGainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    if (isFreezeState())
    {
        bool isEqualValue = true;
        if (m_IsSonoparametersChanging)
        {
            sendPara(POSTPARANAME(ProcessSonoParameterIndex::PreMGainValue), value, Dsc_Int);
        }
        else
        {
            QVariant lastPreMGainValue = m_Values.value(POSTPARANAME(ProcessSonoParameterIndex::PreMGainValue));
            isEqualValue = (lastPreMGainValue == value);
        }
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::PreMGainValueAfterFreeze), value, Dsc_Int);
        controlProcessAfterFreezeEnable(isEqualValue);
    }
    else
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::PreMGainValue), value, Dsc_Int);
    }
}

void PostProcessParaController::doColorTransparencyChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }
    sendPara(POSTPARANAME(ProcessSonoParameterIndex::ColorTransparency), value, Dsc_Int);
}

void PostProcessParaController::doBaseLineChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    int baselineIndex = value.toInt();
    int baselineArray[] = {96, 64, 32, 0, 224, 192, 160};
    if ((baselineIndex >= 0) && ((unsigned long)baselineIndex < sizeof(baselineArray) / sizeof(int)))
    {
        sendPara(POSTPARANAME(ProcessSonoParameterIndex::BaseLine), baselineArray[baselineIndex], Dsc_Int);
    }
}

void PostProcessParaController::doOutGainChanged(const QVariant& value, bool changed)
{
    if (!changed)
    {
        return;
    }

    int delta = ModelConfig::instance().value(ModelConfig::OutGainDelta, 0).toInt();
    int outGain = value.toInt() + delta;

    sendPara(POSTPARANAME(ProcessSonoParameterIndex::OutGain), outGain, Dsc_Int);
}

bool PostProcessParaController::isMVI() const
{
    return pBV(BFPNames::MVIModeStr);
}

bool PostProcessParaController::isTDI() const
{
    return pBV(BFPNames::TDIEnStr);
}

bool PostProcessParaController::isSN() const
{
    return pBV(BFPNames::SonoNeedleStr);
}

bool PostProcessParaController::isPD() const
{
    return (ColorImageModeType)pIV(BFPNames::ColorImageModeStr) == Color_PD;
}

bool PostProcessParaController::isColor() const
{
    return (ColorImageModeType)pIV(BFPNames::ColorImageModeStr) == Color_CF &&
           !(pBV(BFPNames::TDIEnStr) || pBV(BFPNames::MVIModeStr) || pBV(BFPNames::ElastoEnStr));
}

bool PostProcessParaController::isCW() const
{
    return pBV(BFPNames::CWEnStr);
}

bool PostProcessParaController::isTriplex() const
{
    return pBV(BFPNames::TriplexModeStr);
}

bool PostProcessParaController::isTHI() const
{
    return pBV(BFPNames::THIStr);
}
