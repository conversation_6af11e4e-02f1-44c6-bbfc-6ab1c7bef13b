#include "preprocessparacontroller.h"
#include "bfpnames.h"
#include "parameter.h"
#include "zeusapihelper.h"
#include "zeusparameternames.h"
#include "variantutil.h"
#include "probeblockdataset.h"
#include "infostruct.h"
#include "probedataset.h"
#include "resource.h"
#include "infostruct.h"
#include "controltableparameter.h"
#include <QDir>
#include <qmath.h>
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, PreProcessParaController)

#define CW_POINT_NUMBER 15120

#define CHECK_RESULT_BREAK(checkItem, expectResult)                                                                    \
    if ((checkItem) != (expectResult))                                                                                 \
    {                                                                                                                  \
        qWarning() << PRETTY_FUNCTION << "Check failed: '" #checkItem "' != '" #expectResult "'";                      \
        break;                                                                                                         \
    }

using namespace ZeusParameterNames;

PreProcessParaController::PreProcessParaController(int imageTileIndex, ZeusAPIInterface* api, QObject* parent)
    : QObject(parent)
    , m_HandleIndex(imageTileIndex)
    , m_APIInterface(api)
{
    initPWFilter();
    initCWPRFGear();
    //目前未使用的参数，但是需要设置初始值
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECDynFLogSel), 11, Dsc_Int);
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECBaseLine), 3, Dsc_Int);
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCpa_DR), 72, Dsc_Int);
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCPAEn), 0, Dsc_Int);
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECFacMode), 0, Dsc_Int);
}

void PreProcessParaController::sendPara(QString PREPARANAME, const QVariant& value, ParameterType valueType,
                                        int pointSize)
{
    if (valueType != Dsc_Pointer)
    {
        m_APIInterface->setZeusParameter(m_HandleIndex, PREPARANAME, value, valueType, pointSize);
    }
    else
    {
        QVariant sendValue = value;
        ControlTableParameter* ct = dynamic_cast<ControlTableParameter*>(sender());
        if (ct != nullptr)
        {
            sendValue = ct->controlTableValue();
        }
        //        qCritical() << PRETTY_FUNCTION
        //                    << "m_HandleIndex" << m_HandleIndex
        //                    << "zeusPREPARANAME" << PREPARANAME
        //                    << "sendValue" << sendValue
        //                    << "valueType" << valueType;
        m_APIInterface->setZeusParameter(m_HandleIndex, PREPARANAME, sendValue, valueType);
    }
}

void PreProcessParaController::onSetSonoParameters()
{
    // tianyi TODO 一些默认参数要靠这个来下发一次，还有没有其他好的方法
    autoCallSelfObjectConnetedChangedSlots();
}

void PreProcessParaController::onWallFilterSettingChanged(const QVariant& value)
{
    Q_UNUSED(value);
    updateWallFilterColorData();
}

void PreProcessParaController::onWallFilterColorChanged(const QVariant& value)
{
    Q_UNUSED(value);
    updateWallFilterColorData();
}

void PreProcessParaController::onWallThresholdSChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECHDCPAWallThr), value, Dsc_Int);
}

void PreProcessParaController::onCfmNoiseVltChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmNoiseVlt), value, Dsc_Int);
}

void PreProcessParaController::onCfmNoiseSlopeChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmNoiseSlope), value, Dsc_Int);
}

void PreProcessParaController::onCfmFlashVltChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmFlashVlt), value, Dsc_Int);
}

void PreProcessParaController::onCfmFlashSlopeChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmFlashSlope), value, Dsc_Int);
}

void PreProcessParaController::onBi_CPAChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECBiCPAEn), value, Dsc_Int);
}

void PreProcessParaController::onTDIEnChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECTDIEn), value, Dsc_Int);
}

void PreProcessParaController::onMVIModeChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECSMIEn), value, Dsc_Int);
}

void PreProcessParaController::onMVIPostGainChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECSMIPosGain), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl0Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl0), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl1Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl1), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl2Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl2), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl3Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl3), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl4Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl4), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl5Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl5), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl6Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl6), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl7Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl7), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl8Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl8), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl9Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl9), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl10Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl10), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl11Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl11), value, Dsc_Int);
}

void PreProcessParaController::onCfmEngCtrl12Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEngCtrl12), value, Dsc_Int);
}

void PreProcessParaController::onPreColorPersistenceEnableChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PreColorPersistenceEnable), value, Dsc_Bool);
}

void PreProcessParaController::onColorPersistenceAlgorithmChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::ColorPersistenceAlgorithm), value, Dsc_Int);
}

void PreProcessParaController::onColorPersistenceHighChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::ColorPersistenceHigh), value, Dsc_Double);
}

void PreProcessParaController::onColorPersistenceLowChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::ColorPersistenceLow), value, Dsc_Double);
}

void PreProcessParaController::onColorPersistenceANChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::ColorPersistenceAN), value, Dsc_Double);
}

void PreProcessParaController::onColorPersistenceRatioChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::ColorPersistenceRatio), value, Dsc_Double);
}

void PreProcessParaController::onColorPersistenceUsingN_1Changed(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::ColorPersistenceUsingN_1), value, Dsc_Bool);
}

void PreProcessParaController::onCETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCET), value, Dsc_Int);
}

void PreProcessParaController::onCVRTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVRT), value, Dsc_Int);
}

void PreProcessParaController::onCVLTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVLT), value, Dsc_Int);
}

void PreProcessParaController::onCHETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCHET), value, Dsc_Int);
}

void PreProcessParaController::onWFGainControlChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECWFGainCtrl), value, Dsc_Int);
}

void PreProcessParaController::onPacketSizeChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECAccumTimes), value, Dsc_Int);
}

void PreProcessParaController::onCFMEnergeCtlChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCfmEnergeCtrl), value, Dsc_Int);
}

void PreProcessParaController::onEtBfWfChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECEtBfWf), value, Dsc_Int);
}

void PreProcessParaController::onCFMQuitSamp_HeadChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECQuitSamp_Head), value, Dsc_Int);
}

void PreProcessParaController::onCFMQuitSamp_TailChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECQuitSamp_Tail), value, Dsc_Int);
}

void PreProcessParaController::onCTGCChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCGainThr), value, Dsc_Int);
}

void PreProcessParaController::onTDICETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCET), value, Dsc_Int);
}

void PreProcessParaController::onTDICVRTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVRT), value, Dsc_Int);
}

void PreProcessParaController::onTDICVLTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVLT), value, Dsc_Int);
}

void PreProcessParaController::onTDICHETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCHET), value, Dsc_Int);
}

void PreProcessParaController::onPacketSizeTDIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECAccumTimes), value, Dsc_Int);
}

void PreProcessParaController::onWFGainControlTDIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECWFGainCtrl), value, Dsc_Int);
}

void PreProcessParaController::onTDICTGCChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCGainThr), value, Dsc_Int);
}

void PreProcessParaController::onTDIWallfilterChanged(const QVariant& value)
{
    Q_UNUSED(value);
    updateWallFilterColorData();
}

void PreProcessParaController::onPDCETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCET), value, Dsc_Int);
}

void PreProcessParaController::onPDCVRTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVRT), value, Dsc_Int);
}

void PreProcessParaController::onPDCVLTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVLT), value, Dsc_Int);
}

void PreProcessParaController::onPDCHETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCHET), value, Dsc_Int);
}

void PreProcessParaController::onWFGainControlPDChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECWFGainCtrl), value, Dsc_Int);
}

void PreProcessParaController::onPacketSizePDChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECAccumTimes), value, Dsc_Int);
}

void PreProcessParaController::onPDCTGCChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCGainThr), value, Dsc_Int);
}

void PreProcessParaController::onSNCETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCET), value, Dsc_Int);
}

void PreProcessParaController::onSNCVRTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVRT), value, Dsc_Int);
}

void PreProcessParaController::onSNCVLTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVLT), value, Dsc_Int);
}

void PreProcessParaController::onSNCHETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCHET), value, Dsc_Int);
}

void PreProcessParaController::onWFGainControlSNChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECWFGainCtrl), value, Dsc_Int);
}

void PreProcessParaController::onPacketSizeSNChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECAccumTimes), value, Dsc_Int);
}

void PreProcessParaController::onSNCTGCChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCGainThr), value, Dsc_Int);
}

void PreProcessParaController::onWallFilterSNChanged(const QVariant& value)
{
    Q_UNUSED(value);
    updateWallFilterColorData();
}

void PreProcessParaController::onMVICETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCET), value, Dsc_Int);
}

void PreProcessParaController::onMVICVRTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVRT), value, Dsc_Int);
}

void PreProcessParaController::onMVICVLTChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCVLT), value, Dsc_Int);
}

void PreProcessParaController::onMVICHETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCHET), value, Dsc_Int);
}

void PreProcessParaController::onHDCPACETChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCETDyn), value, Dsc_Int);
}

void PreProcessParaController::onWFGainControlMVIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECWFGainCtrl), value, Dsc_Int);
}

void PreProcessParaController::onHDCPAWFGainCtrlChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECWFGainCtrlDyn), value, Dsc_Int);
}

void PreProcessParaController::onPacketSizeMVIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECAccumTimes), value, Dsc_Int);
}

void PreProcessParaController::onMVICTGChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRECCGainThr), value, Dsc_Int);
}

void PreProcessParaController::onHDCPAWallFilterChanged(const QVariant& value)
{
    Q_UNUSED(value);
    updateCPAWallFilterColorData();
}

void PreProcessParaController::onCPAWallFilterSettingChanged(const QVariant& value)
{
    Q_UNUSED(value);
    updateCPAWallFilterColorData();
}

void PreProcessParaController::onWallFilterMVIChanged(const QVariant& value)
{
    Q_UNUSED(value);
    updateWallFilterColorData();
}

void PreProcessParaController::onAudioFilterCoefSelChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updataAudioFilterCoefSelData();
}

void PreProcessParaController::onWallFilterDopChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updataWallFilterDopData();
}

void PreProcessParaController::onPixelRatioChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVN), value, Dsc_Int);
    updataWindowFilterCoefSelData();
}

void PreProcessParaController::onDopSampleNumChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWSampNumsel), value, Dsc_Int);
}

void PreProcessParaController::onSampleVolumeChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVolum), value, Dsc_Int);
}

void PreProcessParaController::onSampleVolumeMMChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVolumValue), value, Dsc_Int);
}

void PreProcessParaController::onDopAccumulateNumChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWAccumNum), value, Dsc_Int);
}

void PreProcessParaController::onDopFilterLengthChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVL), value, Dsc_Int);
}

void PreProcessParaController::onGateSegmentChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVK), value, Dsc_Int);
}

void PreProcessParaController::onPWEnhanceChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWDEnhance), value, Dsc_Int);
}

void PreProcessParaController::onDopTimeFilterChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWTDTimefilter), value, Dsc_Int);
}

void PreProcessParaController::onTriplexModeChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::TriplexRefresh), value, Dsc_Int);
}

void PreProcessParaController::onPRFDopChanged(const QVariant& value)
{
    //需要进行单位转换下发，后续zeus会处理
    QVariant newValue = value.toFloat() / 1000.0;
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRF), newValue, Dsc_Double);
}

void PreProcessParaController::onADFreqMHzChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::ADFreqMHz), value, Dsc_Int);
}

void PreProcessParaController::onDopFilterCoefChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWDDFSel), value, Dsc_Int);
}

void PreProcessParaController::onDopAudioPreGainChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::AudioPreGain), value, Dsc_Int);
}

void PreProcessParaController::onDopAudioPostGainChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::AudioPostGain), value, Dsc_Int);
}

void PreProcessParaController::onDopAudioSegmentChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::AudioSegment), value, Dsc_Int);
}

void PreProcessParaController::onSonicSpeedChanged(const QVariant& value)
{
    //需要进行单位转换下发，后续zeus会处理
    QVariant newValue = value.toInt() / 1000;
    sendPara(PREPARANAME(ProcessSonoParameterIndex::SonicSpeed), newValue, Dsc_Int);
}

void PreProcessParaController::onPWSecondSampDatNumChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWSecondSampDatNum), value, Dsc_Int);
}

void PreProcessParaController::onIsSonoPWOnChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::IsSonoPWOn), value, Dsc_Bool);
}

void PreProcessParaController::onIsSmallPWChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::IsSmallPW), value, Dsc_Bool);
}

void PreProcessParaController::onHprfEnChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWHPRF), value, Dsc_Bool);
}

void PreProcessParaController::onGainDopChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWDGain_Value), value, Dsc_Int);
}

void PreProcessParaController::onAudioFilterCoefSelTDIChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updataAudioFilterCoefSelData();
}

void PreProcessParaController::onWallFilterDopTDIChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updataWallFilterDopData();
}

void PreProcessParaController::onPixelRatioTDIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVN), value, Dsc_Int);
    updataWindowFilterCoefSelData();
}

void PreProcessParaController::onTDSampleNumChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWSampNumsel), value, Dsc_Int);
}

void PreProcessParaController::onSampleVolumeTDIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVolum), value, Dsc_Int);
}

void PreProcessParaController::onSampleVolumeTDIMMChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVolumValue), value, Dsc_Double);
}

void PreProcessParaController::onTDFilterLengthChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVL), value, Dsc_Int);
}

void PreProcessParaController::onGateSegmentTDIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVK), value, Dsc_Int);
}

void PreProcessParaController::onPWEnhanceTDIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWDEnhance), value, Dsc_Int);
}

void PreProcessParaController::onTDTimeFilterChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWTDTimefilter), value, Dsc_Int);
}

void PreProcessParaController::onTriplexModeTDIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::TriplexRefresh), value, Dsc_Int);
}

void PreProcessParaController::onTDAudioPreGainChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::AudioPreGain), value, Dsc_Int);
}

void PreProcessParaController::onTDAudioPostGainChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::AudioPostGain), value, Dsc_Int);
}

void PreProcessParaController::onTDAudioSegmentChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::AudioSegment), value, Dsc_Int);
}

void PreProcessParaController::onGainDopTDIChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWDGain_Value), value, Dsc_Int);
}

void PreProcessParaController::onCWDSampleRateChanged(const QVariant& value)
{
    // 设置 CW gear 级数
    // log()->info() <<"read CWGear data: "<< value;
    QString path = "cw-data/gear";
    QMap<int, PRFINFO>::iterator iter = m_PRFDatas.find(value.toInt());

    if (iter != m_PRFDatas.end())
    {
        sendPara(PREPARANAME(ProcessSonoParameterIndex::CWAntiAliasTabNum1), iter->dataCnt1, Dsc_Int);
        sendPara(PREPARANAME(ProcessSonoParameterIndex::CWAntiAliasTabNum2), iter->dataCnt2, Dsc_Int);
        sendPara(PREPARANAME(ProcessSonoParameterIndex::CWDecimatorRate1st), iter->decimator1, Dsc_Int);
        sendPara(PREPARANAME(ProcessSonoParameterIndex::CWDecimatorRate2nd), iter->decimator2, Dsc_Int);

        int gearDataLen = iter->dataCnt1 + iter->dataCnt2;
        int* pGearData = new int[gearDataLen];

        QString gearPath = Resource::ZeusNetworksPath() + path + QString::number(value.toInt());
        QFile file(gearPath);
        if (file.open(QIODevice::ReadOnly))
        {
            int ll = file.read((char*)pGearData, gearDataLen * sizeof(int));
            if (ll == gearDataLen * sizeof(int))
            {
                int prfsData1[256] = {0};
                int prfsData2[256] = {0};

                for (int len = 0; len < gearDataLen; len++)
                {
                    if (len < iter->dataCnt1)
                    {
                        prfsData1[len] = pGearData[len];
                    }
                    else
                    {
                        prfsData2[len - iter->dataCnt1] = pGearData[len];
                    }
                }
                QVariant inValueA;
                inValueA.setValue(QByteArray((const char*)prfsData1, sizeof(prfsData1)));
                sendPara(PREPARANAME(ProcessSonoParameterIndex::CWAntiAliasValues1), inValueA, Dsc_Array,
                         sizeof(prfsData1));

                QVariant inValueB;
                inValueB.setValue(QByteArray((const char*)prfsData2, sizeof(prfsData2)));
                sendPara(PREPARANAME(ProcessSonoParameterIndex::CWAntiAliasValues2), inValueB, Dsc_Array,
                         sizeof(prfsData2));
            }
            else
            {
                log()->fatal() << "fatal msg: read cwprfData datalen is not expect: " << ll / 4
                               << " curGear: " << value.toInt() << " gearDataPath : " << gearPath;
            }
        }
        else
        {
            log()->fatal() << "open file: " << gearPath << " failed!";
        }
        delete[] pGearData;
        pGearData = NULL;
    }
    else
    {
        // log()->fatal() <<"fatal msg: gear value is not expect[0-15]: "<<gear;
    }
}

void PreProcessParaController::onCWEnChanged(const QVariant& value)
{
    if (value.toBool())
    {
        sendPara(PREPARANAME(ProcessSonoParameterIndex::PWCWScanMode), 1, Dsc_Int);
        // point number 一定要设对 TODO Seven
        sendPara(PREPARANAME(ProcessSonoParameterIndex::CWPointNum), CW_POINT_NUMBER, Dsc_Int);
        // 当前版本是4， I、Q、I、Q
        sendPara(PREPARANAME(ProcessSonoParameterIndex::CWLineNum), 4, Dsc_Int);
        sendPara(PREPARANAME(ProcessSonoParameterIndex::CWSecondSampDatNum), 1, Dsc_Int);
        sendPara(PREPARANAME(ProcessSonoParameterIndex::CWInvert), 0, Dsc_Int);
        sendPara(PREPARANAME(ProcessSonoParameterIndex::IsSmallPW), false, Dsc_Bool);
        //  CurrentGate 暂不设置
    }
    else
    {
        sendPara(PREPARANAME(ProcessSonoParameterIndex::PWCWScanMode), 0, Dsc_Int);
        sendPara(PREPARANAME(ProcessSonoParameterIndex::IsSmallPW), true, Dsc_Bool);
    }
}

void PreProcessParaController::onCWSampleNumChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWSampNumsel), value, Dsc_Int);
}

void PreProcessParaController::onCWFilterLengthChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVL), value, Dsc_Int);
}

void PreProcessParaController::onGateSegmentCWDChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVK), value, Dsc_Int);
}

void PreProcessParaController::onPixelRatioCWDChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWVN), value, Dsc_Int);
    updataWindowFilterCoefSelData();
}

void PreProcessParaController::onAudioFilterCoefSelCWDChanged(const QVariant& value)
{
    Q_UNUSED(value)
    updataAudioFilterCoefSelData();
}

void PreProcessParaController::onWallFilterCWDChanged(const QVariant& value)
{
    Q_UNUSED(value);
    updataWallFilterDopData();
}

void PreProcessParaController::onCWTimeFilterChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWTDTimefilter), value, Dsc_Int);
}

void PreProcessParaController::onCWAudioSegmentChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::AudioSegment), value, Dsc_Int);
}

void PreProcessParaController::onCWAudioPostGainChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::AudioPostGain), value, Dsc_Int);
}

void PreProcessParaController::onCWAudioPreGainChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::AudioPreGain), value, Dsc_Int);
}

void PreProcessParaController::onPWEnhanceCWDChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWDEnhance), value, Dsc_Int);
}

void PreProcessParaController::onPRFCWDChanged(const QVariant& value)
{
    //需要进行单位转换下发，后续zeus会处理
    QVariant newValue = value.toFloat() / 1000.0;
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PRF), newValue, Dsc_Double);
}

void PreProcessParaController::onGainDopCWDChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWDGain_Value), value, Dsc_Int);
}

void PreProcessParaController::onCWDAccumulateNumChanged(const QVariant& value)
{
    sendPara(PREPARANAME(ProcessSonoParameterIndex::PWAccumNum), value, Dsc_Int);
}

bool PreProcessParaController::readBlockData(char* data, const QString& paramName, int address, int partIndex,
                                             int partCount)
{
    bool result = false;

    //    qDebug() << PRETTY_FUNCTION << paramName << address << partCount << partIndex;

    do
    {
        ProbeBlockDataSet& blockDataSet = ProbeBlockDataSet::instance();
        const ProbeDataInfo& probeInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));

        CHECK_RESULT_BREAK(blockDataSet.contains(probeInfo.Name), true);

        ProbeBlockDataGroups& blockDataGroups = blockDataSet.probeBolckDataGroups(probeInfo.Name);
        QString path = Resource::dynDataDir() + QDir::separator() + blockDataGroups.path();

        if (!paramName.isEmpty())
        {
            int groupIndex = blockDataGroups.indexOfParaGroups(paramName);
            CHECK_RESULT_BREAK(groupIndex >= 0, true);

            BlockDataGroup& group = blockDataGroups.paraGroup(groupIndex);
            path += QDir::separator() + group.path();

            if (partCount == 0)
            {
                partCount = group.paraIndexCount();
            }
        }

        BlockData blockData(address, path, partCount);
        QString fileName = path + QDir::separator() + blockData.fileName();

        result = blockData.open(fileName);
        CHECK_RESULT_BREAK(result, true);

        result = blockData.seek(partIndex * blockData.partSize());
        CHECK_RESULT_BREAK(result, true);

        int readSize = blockData.read(data, blockData.partSize());
        CHECK_RESULT_BREAK(readSize == blockData.partSize(), true);

        blockData.close();

        result = true;
    } while (0);

    return result;
}

void PreProcessParaController::updateWallFilterColorData()
{
    int wallFilterColorIndex = -1;
    // TODO 复用性参数优化
    SystemScanMode scanMode = (SystemScanMode)pIV(BFPNames::SystemScanModeStr);
    if (scanMode == SystemScanModeSonoNeedle)
    {
        wallFilterColorIndex = pIV(BFPNames::WallFilterSNStr);
    }
    else if (scanMode == SystemScanModeTissueDoppler || scanMode == SystemScanModeTissuePW)
    {
        wallFilterColorIndex = pIV(BFPNames::TDIWallfilterStr);
    }
    else if (scanMode == SystemScanModeMVI || scanMode == SystemScanModeMVIPW)
    {
        wallFilterColorIndex = pIV(BFPNames::WallFilterMVIStr);
    }
    else
    {
        wallFilterColorIndex = pIV(BFPNames::WallFilterColorStr);
    }

    QList<int> wallFilterColorIndexes =
        VariantUtil::variant2Ints(pV(BFPNames::WallFilterSettingStr), VariantUtil::String);

    if (pIV(BFPNames::ProbeIdStr) < 0 || wallFilterColorIndex >= wallFilterColorIndexes.count())
    {
        return;
    }

    char wallfilterData[512] = {0};

    readBlockData(wallfilterData, "WallFilterSetting", 80, wallFilterColorIndexes[wallFilterColorIndex]);

    // color的数据2个字节为一个数据
    int16_t* data = reinterpret_cast<int16_t*>(wallfilterData);

    float fcoef[256] = {0.0};
    for (int i = 0; i < 256; ++i)
    {
        fcoef[i] = data[i];
    }
    m_APIInterface->setZeusParameter(m_HandleIndex, PREPARANAME(ProcessSonoParameterIndex::PRECWF80Coe),
                                     quintptr(fcoef), Dsc_Pointer, 256 * sizeof(float));
}

void PreProcessParaController::updateCPAWallFilterColorData()
{
    int wallFilterColorIndex = pIV(BFPNames::HDCPAWallFilterStr);
    QList<int> wallFilterColorIndexes =
        VariantUtil::variant2Ints(pV(BFPNames::CPAWallFilterSettingStr), VariantUtil::String);

    if (pIV(BFPNames::ProbeIdStr) < 0 || wallFilterColorIndex >= wallFilterColorIndexes.count())
    {
        return;
    }

    char wallfilterData[512] = {0};
    readBlockData(wallfilterData, "WallFilterSetting", 100, wallFilterColorIndexes[wallFilterColorIndex]);

    // color的数据2个字节为一个数据
    int16_t* data = reinterpret_cast<int16_t*>(wallfilterData);

    float fcoef[256] = {0.0};
    for (int i = 0; i < 256; ++i)
    {
        fcoef[i] = data[i];
    }
    m_APIInterface->setZeusParameter(m_HandleIndex, PREPARANAME(ProcessSonoParameterIndex::PRECWF100Coe),
                                     quintptr(fcoef), Dsc_Pointer, 256 * sizeof(float));
}

void PreProcessParaController::initPWFilter()
{
    QString audioFilterFilePath = Resource::ZeusNetworksPath() + QString("pw-data/AudioFilter");
    QFile file(audioFilterFilePath);
    if (file.open(QIODevice::ReadOnly))
    {
        QByteArray bytes = file.read(file.size());
        int64_t threshold = qPow(2, 35);
        int64_t subvalue = qPow(2, 36);

        for (int i = 0; i < AudioFilterGearlength * AudioFilterGearCount; i++)
        {
            memcpy(&m_AudioFilter[i], bytes.constData() + i * sizeof(int64_t), sizeof(int64_t));

            int64_t value = m_AudioFilter[i];
            if (value > threshold)
            {
                m_AudioFilter[i] = value - subvalue;
            }
        }
    }

    QFile wallFile(Resource::ZeusNetworksPath() + QString("pw-data/wallfilter"));
    if (wallFile.open(QIODevice::ReadOnly))
    {
        Q_ASSERT(wallFile.size() == sizeof(m_WallFilter));
        // 读取文件内容到内存中
        qint64 bytesRead = wallFile.read((char*)m_WallFilter, sizeof(m_WallFilter));

        // 检查读取是否成功
        if (bytesRead != sizeof(m_WallFilter))
        {
            qCritical() << PRETTY_FUNCTION << "read wallfilter file failed!";
        }
        wallFile.close();
    }

    QFile winFile(Resource::ZeusNetworksPath() + QString("pw-data/windowfilter"));
    if (winFile.open(QIODevice::ReadOnly))
    {
        Q_ASSERT(winFile.size() == sizeof(m_WindowFilter));
        // 读取文件内容到内存中
        qint64 bytesRead = winFile.read((char*)m_WindowFilter, sizeof(m_WindowFilter));

        // 检查读取是否成功
        if (bytesRead != sizeof(m_WindowFilter))
        {
            qCritical() << PRETTY_FUNCTION << "read windowFilter file failed!";
        }
        winFile.close();
    }
}

void PreProcessParaController::updataAudioFilterCoefSelData()
{
    QString bfpName = BFPNames::AudioFilterCoefSelStr;
    if (SystemScanModeTissuePW == pIV(BFPNames::SystemScanModeStr))
    {
        bfpName = BFPNames::AudioFilterCoefSelTDIStr;
    }
    else if (SystemScanModeCWD == pIV(BFPNames::SystemScanModeStr) ||
             SystemScanModeCWDColorDoppler == pIV(BFPNames::SystemScanModeStr) ||
             SystemScanModeCWDDirectionalPowerDoppler == pIV(BFPNames::SystemScanModeStr) ||
             SystemScanModeCWDPowerDoppler == pIV(BFPNames::SystemScanModeStr))
    {
        bfpName = BFPNames::AudioFilterCoefSelCWDStr;
    }

    int index = pIV(bfpName);
    if (index < AudioFilterGearCount)
    {
        int64_t* p = &m_AudioFilter[index * AudioFilterGearlength];
        m_APIInterface->setZeusParameter(m_HandleIndex, PREPARANAME(ProcessSonoParameterIndex::AudioFilter),
                                         quintptr(p), Dsc_Pointer, AudioFilterGearlength * sizeof(int64_t));
    }
}

void PreProcessParaController::updataWallFilterDopData()
{
    QString bfpName = BFPNames::WallFilterDopStr;
    if (SystemScanModeTissuePW == pIV(BFPNames::SystemScanModeStr))
    {
        bfpName = BFPNames::WallFilterDopTDIStr;
    }
    else if (SystemScanModeCWD == pIV(BFPNames::SystemScanModeStr) ||
             SystemScanModeCWDColorDoppler == pIV(BFPNames::SystemScanModeStr) ||
             SystemScanModeCWDDirectionalPowerDoppler == pIV(BFPNames::SystemScanModeStr) ||
             SystemScanModeCWDPowerDoppler == pIV(BFPNames::SystemScanModeStr))
    {
        bfpName = BFPNames::WallFilterCWDStr;
    }

    int m = pIV(BFPNames::SystemScanModeStr);

    int ctIndex = parameter(bfpName)->controlTableValue();
    if (ctIndex < WallFilterGearCount)
    {
        int64_t* p = &m_WallFilter[ctIndex * WallFilterlengthPerGear];
        m_APIInterface->setZeusParameter(m_HandleIndex, PREPARANAME(ProcessSonoParameterIndex::PWWallFilter),
                                         quintptr(p), Dsc_Pointer, WallFilterlengthPerGear * sizeof(int64_t));
    }
}

void PreProcessParaController::updataWindowFilterCoefSelData()
{
    QString bfpName = BFPNames::PixelRatioStr;
    if (SystemScanModeTissuePW == pIV(BFPNames::SystemScanModeStr))
    {
        bfpName = BFPNames::PixelRatioTDIStr;
    }
    else if (SystemScanModeCWD == pIV(BFPNames::SystemScanModeStr) ||
             SystemScanModeCWDColorDoppler == pIV(BFPNames::SystemScanModeStr) ||
             SystemScanModeCWDDirectionalPowerDoppler == pIV(BFPNames::SystemScanModeStr) ||
             SystemScanModeCWDPowerDoppler == pIV(BFPNames::SystemScanModeStr))
    {
        bfpName = BFPNames::PixelRatioCWDStr;
    }

    int ctIndex = parameter(bfpName)->controlTableValue();
    if (ctIndex < WindowFilterGearCount)
    {
        int8_t* p = m_WindowFilter + ctIndex * WindowFilterlengthPerGear;
        m_APIInterface->setZeusParameter(m_HandleIndex, PREPARANAME(ProcessSonoParameterIndex::PWWindowFilter),
                                         quintptr(p), Dsc_Pointer, WindowFilterlengthPerGear * sizeof(int8_t));
        //        QString winfilterstr;
        //        for (int i = 0; i < WindowFilterlengthPerGear; i++)
        //        {
        //            winfilterstr += QString::number(m_WindowFilter[ctValue.toInt() * WindowFilterlengthPerGear + i]);
        //        }
        //        qCritical() << "windowfilter:" << winfilterstr;
    }
}

void PreProcessParaController::initCWPRFGear()
{
    QMap<int, PRFINFO> _prf;
    _prf.insert(15, PRFINFO(118, 32, 7, 2));
    _prf.insert(14, PRFINFO(102, 32, 8, 2));
    _prf.insert(13, PRFINFO(82, 32, 10, 2));
    _prf.insert(12, PRFINFO(102, 48, 8, 3));
    _prf.insert(11, PRFINFO(82, 48, 10, 3));
    _prf.insert(10, PRFINFO(90, 62, 9, 4));
    _prf.insert(9, PRFINFO(82, 62, 10, 4));
    _prf.insert(8, PRFINFO(90, 80, 9, 5));
    _prf.insert(7, PRFINFO(82, 80, 10, 5));
    _prf.insert(6, PRFINFO(90, 96, 9, 6));
    _prf.insert(5, PRFINFO(82, 96, 10, 6));
    _prf.insert(4, PRFINFO(82, 128, 10, 7));
    _prf.insert(3, PRFINFO(82, 128, 10, 8));
    _prf.insert(2, PRFINFO(82, 192, 10, 10));
    _prf.insert(1, PRFINFO(82, 192, 10, 12));
    _prf.insert(0, PRFINFO(82, 256, 10, 16));
    m_PRFDatas = _prf;
}

QVariant PreProcessParaController::getControlTableValue(Parameter* para, const QVariant& value)
{
    QVariant ret = value;
    if (para != nullptr)
    {
        ControlTableParameter* ctParameter = dynamic_cast<ControlTableParameter*>(para);
        ret = (nullptr != ctParameter) ? ctParameter->controlTableValue() : value;
    }

    return ret;
}
