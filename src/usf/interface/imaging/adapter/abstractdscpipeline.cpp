#include "abstractdscpipeline.h"
#include "abstractdscimagetile.h"
#include "memoryleakcheck.h"
#include "systemscanmodeclassifier.h"
#include "parameter.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "simplemulticalltrigger.h"
#include "assertlog.h"
#include <QDebug>
#include "lineimageargs.h"
AbstractDSCPipeline::AbstractDSCPipeline(QList<IBufferPushControler*> iBufferPushControler,
                                         ChisonUltrasound::ChisonUltrasoundMode type, QObject* parent)
    : QObject(parent)
    , m_BufferPushControler(iBufferPushControler)
    , m_SonoParameters(NULL)
    , m_ChisonUltrasoundMode(type)
    , m_Image2DMap(NULL)
    , m_WaveMap(NULL)
    , m_SimpleMultiCallTrigger(new SimpleMultiCallTrigger())
    , m_StartReached(false)
{
    connect(m_SimpleMultiCallTrigger, SIGNAL(triggerCompleted(const QVariant&)), this,
            SLOT(onRequestFlush(const QVariant&)), Qt::DirectConnection);
}

AbstractDSCPipeline::~AbstractDSCPipeline()
{
    CHECK_DELETE_LIST(AbstractDSCImageTile, m_ImageTiles.values());
    qDeleteAll(m_ImageTiles.values());
    m_ImageTiles.clear();
    disconnect(m_SimpleMultiCallTrigger, SIGNAL(triggerCompleted(const QVariant&)), this,
               SLOT(onRequestFlush(const QVariant&)));
    delete m_SimpleMultiCallTrigger;
    m_SimpleMultiCallTrigger = NULL;
}

void AbstractDSCPipeline::start()
{
    onStart();
    foreach (IBufferPushControler* ibpc, m_BufferPushControler)
    {
        connect(m_ImageTiles[ibpc], SIGNAL(flushedAllImages()), this, SIGNAL(flushedAllImages()), Qt::DirectConnection);
    }
    {
        QMutexLocker locker(&m_StartMutex);
        // actually at this time pipleine not fully prepared,
        // but subclass may want to call continue playing in onPostPrepare
        // to allow this condition we set startReached to true here
        m_StartReached = true;
        m_StartCondition.wakeAll();
    }
}

void AbstractDSCPipeline::stop()
{
    if (m_ImageTiles.count() <= 0)
        return;
    foreach (IBufferPushControler* ibpc, m_BufferPushControler)
    {
        disconnect(m_ImageTiles[ibpc], SIGNAL(flushedAllImages()), this, SIGNAL(flushedAllImages()));
    }
    onStop();
    emit stopRequested();
}

void AbstractDSCPipeline::waitStartedComplete()
{
    QMutexLocker locker(&m_StartMutex);
    if (!m_StartReached)
    {
        qDebug() << "waiting start reached";
        m_StartCondition.wait(&m_StartMutex);
    }
    qDebug() << "start reached";
}

void AbstractDSCPipeline::setSonoParameters(SonoParameters* sonoParameters)
{
    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::ActiveBStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)),
                   this, SLOT(onBeforeActiveBChanged(QVariant, QVariant&)));
    }
    if (m_SonoParameters != sonoParameters)
    {
        m_SonoParameters = sonoParameters;
        onSetSonoParameters();
        connect(m_SonoParameters->parameter(BFPNames::ActiveBStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)),
                this, SLOT(onBeforeActiveBChanged(QVariant, QVariant&)), Qt::UniqueConnection);
        m_ActiveBIndex = m_SonoParameters->pIV(BFPNames::ActiveBStr);
    }
}

void AbstractDSCPipeline::haltProcessImageChanged(const int layoutIndex, const int frozenIndex)
{
    if (layoutIndex >= 0 && m_BufferPushControler.count() > layoutIndex && m_ImageTiles.count() > layoutIndex)
    {
        AbstractDSCImageTile* tile = m_ImageTiles[m_BufferPushControler[layoutIndex]];
#ifdef USE_4D
        disconnect(tile, SIGNAL(newFourDImage(FourDVolumeLineImageArgs*)), this,
                   SIGNAL(newFourDImage(FourDVolumeLineImageArgs*)));
#endif
        disconnect(tile, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewImage(LineImageArgs*)));
        tile->haltProcessImageChanged(frozenIndex);
    }
}

void AbstractDSCPipeline::recoveryProcessImageChanged(const int layoutIndex)
{
    if (layoutIndex >= 0 && m_BufferPushControler.count() > layoutIndex && m_ImageTiles.count() > layoutIndex)
    {
        AbstractDSCImageTile* tile = m_ImageTiles[m_BufferPushControler[layoutIndex]];
#ifdef USE_4D
        connect(tile, SIGNAL(newFourDImage(FourDVolumeLineImageArgs*)), this,
                SIGNAL(newFourDImage(FourDVolumeLineImageArgs*)),
                Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
#endif
        connect(tile, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewImage(LineImageArgs*)),
                Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
        m_ImageTiles[m_BufferPushControler[layoutIndex]]->recoveryProcessImageChanged();
    }
}

void AbstractDSCPipeline::clearFrozenIndex(const int layoutIndex)
{
    if (layoutIndex >= 0 && m_ImageTiles.count() > layoutIndex)
    {
        AbstractDSCImageTile* tile = m_ImageTiles[m_BufferPushControler[layoutIndex]];
        m_ImageTiles[m_BufferPushControler[layoutIndex]]->clearFrozenIndex();
    }
}

bool AbstractDSCPipeline::isDSC2DImagePushed(const int layoutIndex) const
{
    bool ret = true;
    if (layoutIndex >= 0 && m_ImageTiles.count() > layoutIndex)
    {
        ret = m_ImageTiles[m_BufferPushControler[layoutIndex]]->isImage2DPushed();
    }
    return ret;
}

void AbstractDSCPipeline::setColorMap(uchar* image2DMap, uchar* waveMap)
{
    m_Image2DMap = image2DMap;
    m_WaveMap = waveMap;
    foreach (AbstractDSCImageTile* tile, m_ImageTiles.values())
    {
        tile->setColorMap(image2DMap, waveMap, m_SonoParameters->pIV(BFPNames::ActiveBStr));
    }
}

void AbstractDSCPipeline::onEntryStressEchoAnalyze()
{
    foreach (AbstractDSCImageTile* tile, m_ImageTiles.values())
    {
        tile->onEntryStressEchoAnalyze();
    }
}

void AbstractDSCPipeline::onExitStressEchoAnalyze()
{
    foreach (AbstractDSCImageTile* tile, m_ImageTiles.values())
    {
        tile->onExitStressEchoAnalyze();
    }
}

void AbstractDSCPipeline::clearThreadData()
{
    int activeIndex = m_SonoParameters->pIV(BFPNames::ActiveBStr);
    if (m_SonoParameters->isRealTime() && m_ImageTiles.count() > activeIndex)
    {
        m_ImageTiles[m_BufferPushControler[activeIndex]]->clearThreadData(1);
    }
}

void AbstractDSCPipeline::setEnforceEnable(bool state)
{
    int activeIndex = m_SonoParameters->pIV(BFPNames::ActiveBStr);
    if (m_ImageTiles.count() > activeIndex)
    {
        m_ImageTiles[m_BufferPushControler[activeIndex]]->setEnforceEnable(state);
    }
}

void AbstractDSCPipeline::onNewImage(LineImageArgs* lineImageArgs)
{
    emit newImage(lineImageArgs);
}

void AbstractDSCPipeline::onNewMeasureImage(LineImageArgs* lineImageArgs)
{
    emit newMeasureImage(lineImageArgs);
}

void AbstractDSCPipeline::onImageShapeStable()
{
    //这里的操作放开后会导致2个问题：彩色ROI移动后卡顿、pw修改display无频谱问题
    //    int activeIndex = m_SonoParameters->pIV(BFPNames::ActiveBStr);
    //    if(m_SonoParameters->isRealTime() && m_ImageTiles.count() > activeIndex)
    //    {
    //        m_ImageTiles[ m_BufferPushControler[activeIndex] ]->clearThreadData(1);
    //    }
}

void AbstractDSCPipeline::onImageShapeUnstable()
{
    //    int activeIndex = m_SonoParameters->pIV(BFPNames::ActiveBStr);
    //    if(m_SonoParameters->isRealTime() && m_ImageTiles.count() > activeIndex)
    //    {
    //        m_ImageTiles[ m_BufferPushControler[activeIndex] ]->onImageShapeUnstable();
    //    }
}

void AbstractDSCPipeline::onPlayCineStatusChanged(int layoutIndex, bool playCineStatus)
{
    if (layoutIndex >= 0 && layoutIndex < m_BufferPushControler.count())
    {
        m_ImageTiles[m_BufferPushControler[layoutIndex]]->onPlayCineStatusChanged(playCineStatus);
    }
}

void AbstractDSCPipeline::onColorMapChanged(const int systemScanMode, uchar* image2DMap, uchar* waveMap,
                                            const int layoutIndex)
{
    if ((layoutIndex >= 0) && (layoutIndex < m_BufferPushControler.count()))
    {
        if (SystemScanModeClassifier::colorMapCount(systemScanMode) == 2)
        {
            m_ImageTiles[m_BufferPushControler[layoutIndex]]->setColorMap(image2DMap, waveMap,
                                                                          m_SonoParameters->pIV(BFPNames::ActiveBStr));
        }
        else
        {
            m_ImageTiles[m_BufferPushControler[layoutIndex]]->setColorMap(image2DMap, image2DMap,
                                                                          m_SonoParameters->pIV(BFPNames::ActiveBStr));
        }
    }
    else
    {
        uchar* map1 = NULL;
        uchar* map2 = NULL;
        if (SystemScanModeClassifier::colorMapCount(systemScanMode) == 2)
        {
            map1 = m_Image2DMap;
            map2 = m_WaveMap;
        }
        else
        {
            map1 = m_Image2DMap;
            map2 = m_Image2DMap;
        }
        if (m_SonoParameters->isRealTime())
        {
            foreach (AbstractDSCImageTile* imageTile, m_ImageTiles.values())
            {
                imageTile->setColorMap(map1, map2, m_SonoParameters->pIV(BFPNames::ActiveBStr));
            }
        }
        else
        {
            if (m_ActiveBIndex >= 0 && m_ActiveBIndex < m_BufferPushControler.count())
            {
                m_ImageTiles[m_BufferPushControler[m_ActiveBIndex]]->setColorMap(
                    map1, map2, m_SonoParameters->pIV(BFPNames::ActiveBStr));
            }
        }
    }
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION
             << "m_SonoParameters->pBV(BFPNames::FreezeStr):" << m_SonoParameters->pBV(BFPNames::FreezeStr);
#endif
    if (m_SonoParameters->pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime())
    {
        m_SimpleMultiCallTrigger->onTriggered(m_SonoParameters->pIV(BFPNames::ActiveBStr));
    }
}

void AbstractDSCPipeline::onBeforeActiveBChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    m_ActiveBIndex = newValue.toInt();
    qDebug() << PRETTY_FUNCTION << "m_ActiveBIndex:" << m_ActiveBIndex
             << "isRealTime:" << m_SonoParameters->isRealTime();
    if (m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        if (m_ActiveBIndex >= 0 && m_ActiveBIndex < m_ImageTiles.keys().count())
        {
            m_ImageTiles[m_BufferPushControler[m_ActiveBIndex]]->setColorMap(m_Image2DMap, m_WaveMap, m_ActiveBIndex);
        }
    }
}

void AbstractDSCPipeline::onRequestFlush(const QVariant& value)
{
    int layoutIndex = value.toInt();
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION << "value.isValid():" << value.isValid() << "layoutIndex:" << layoutIndex
             << "m_BufferPushControler.count():" << m_BufferPushControler.count();
#endif
    if (value.isValid() && (layoutIndex >= 0) && (m_BufferPushControler.count() > layoutIndex))
    {
        emit requestFlush();
    }
}
