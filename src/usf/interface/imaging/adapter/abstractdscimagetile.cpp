#include "abstractdscimagetile.h"
#include "assertlog.h"
#include "bfpnames.h"
#include "ibufferpushcontroler.h"
#include "imagedebughelper.h"
#include "infostruct.h"
#include "isonobuffer.h"
#include "lineimageargs.h"
#include "logger.h"
#include "parameter.h"
#include "sonoparameters.h"
#include <QCoreApplication>
#include <QTime>
#include <QWriteLocker>
#include <boost/foreach.hpp>
#include <thread>
#ifdef USE_4D
#include "fourdvolumelineimageargs.h"
#endif
// LOG4QT_DECLARE_STATIC_LOGGER(log, AbstractDSCImageTile)

static const QString CHANGED_SOURCES[] = {
    //    BFPNames::DSCImageRectsStr,
    BFPNames::LayoutStr,
    BFPNames::LineDataModeStr,                 //数据模式发生变化
    BFPNames::SystemScanModeStr,               //扫查模式发生变化
    BFPNames::ProbeIdStr,                      //探头ID发生变化
    BFPNames::TrapezoidalModeStr,              //梯形成像
    BFPNames::VirtualVertexTrapezoidalModeStr, //虚拟顶点梯形成像
    BFPNames::CurvedExapandingStr, BFPNames::ScpdStr,
    BFPNames::CompoundByFPGAStr,       // Compound
    BFPNames::ECGEnStr,                //开启ECG时将绘制ECG图线
    BFPNames::EasyPlayImageZoomCofStr, // EasyView
    BFPNames::HalfHeightStr,
    //                                          BFPNames::TwoDImageScaleFactorStr,
    //                                          BFPNames::ImageRenderLayoutsStr,
    BFPNames::ImageModeStr};

AbstractDSCImageTile::AbstractDSCImageTile(const int imageTileIndex, IBufferPushControler* iBufferPushControler,
                                           ChisonUltrasound::ChisonUltrasoundMode type, QObject* parent)
    : QObject(parent)
    , m_ImageTileIndex(imageTileIndex)
    , m_IBufferPushControler(iBufferPushControler)
    , m_SonoBuffer(iBufferPushControler->getISonoBuffer())
    , m_SonoParameters(NULL)
    , m_WaitingSignalCounter(0)
    , m_SonoParametersChanging(false)
    , m_IsSonoParameterCleared(true)
    , m_SonoparametersChangedCounter(0)
    , m_BlockingChangedSlot(true)
    , m_FrozenIndex(-1)
    , m_Stopping(false)
    , m_StartIng(false)
    , m_IsNewImagePaused(0)
    , m_DeliverPushTimer(NULL)
    , m_BufferChanged(0)
    , m_InEasyPlayMode(false)
    , m_ImageDebug(0)
    , m_ImageDebugHelper(new ImageDebugHelper)
#ifdef USE_4D
    , m_volumeLineImageArgs(new FourDVolumeLineImageArgs())
#endif
    , m_WaitForFrozen(false)
{
    Q_UNUSED(type);
    qRegisterMetaType<BufferUnit>("BufferUnit");
    qRegisterMetaType<BufferUnit>("BufferUnit&");
    qRegisterMetaType<QList<BufferUnit>>("QList<BufferUnit>");
    qRegisterMetaType<QList<BufferUnit>>("QList<BufferUnit>&");
    qRegisterMetaType<FrameUnitInfo>("FrameUnitInfo");
    qRegisterMetaType<FrameUnitInfo>("FrameUnitInfo&");

    m_DeliverPushTimer = new QTimer(this);
    m_DeliverPushTimer->setInterval(m_DeliverPushTime);
    connect(m_DeliverPushTimer, SIGNAL(timeout()), this, SLOT(onDeliverPushTimerTimeout()));
    connect(this, SIGNAL(startDeliverPushTimer()), m_DeliverPushTimer, SLOT(start()));
    connect(this, SIGNAL(stopDeliverPushTimer()), m_DeliverPushTimer, SLOT(stop()));
}

AbstractDSCImageTile::~AbstractDSCImageTile()
{
    disconnect(m_IBufferPushControler, SIGNAL(flushedAllImages()), this, SLOT(onFlushedAllImages()));
    disconnect(this);
    if (m_DeliverPushTimer->isActive())
    {
        m_DeliverPushTimer->stop();
    }
    delete m_DeliverPushTimer;
    delete m_ImageDebugHelper;
#ifdef USE_4D
    delete m_volumeLineImageArgs;
#endif
}

void AbstractDSCImageTile::initialize()
{
    connect(m_SonoBuffer, SIGNAL(beforeSonoParametersChanged()), this, SLOT(onBeforeSonoParametersChangedDirectly()),
            Qt::DirectConnection);
    connect(m_SonoBuffer, SIGNAL(sonoParametersChanged()), this, SLOT(onSonoParameterChangedDirectly()),
            Qt::DirectConnection);
    //    connect(m_SonoBuffer, SIGNAL(clearSonoParameters()), this, SLOT(onClearSonoParametersDirectly()),
    //            Qt::DirectConnection);
    connect(m_SonoBuffer, SIGNAL(beforeBufferCleared()), this, SLOT(onBeforeBuffersChangedDirectly()),
            Qt::DirectConnection);
    connect(m_SonoBuffer, SIGNAL(bufferCleared()), this, SLOT(onBuffersChangedDirectly()), Qt::DirectConnection);
    connect(m_SonoBuffer, SIGNAL(beforeBuffersChanged(int)), this, SLOT(onBeforeBuffersChangedDirectly(int)),
            Qt::DirectConnection);
    connect(m_SonoBuffer, SIGNAL(buffersChanged(int)), this, SLOT(onBuffersChangedDirectly(int)), Qt::DirectConnection);
    connect(m_SonoBuffer, SIGNAL(lineImageBufferChanged()), this, SLOT(onLineImageBufferChangedDirectly()),
            Qt::DirectConnection);
    connect(m_SonoBuffer, SIGNAL(cacheQueueCleard(int)), this, SLOT(onCacheQueueCleardDirectly(int)),
            Qt::DirectConnection);

    connect(m_IBufferPushControler, SIGNAL(flushedAllImages()), this, SLOT(onFlushedAllImages()), Qt::DirectConnection);
}

void AbstractDSCImageTile::start()
{
    m_StartIng = true;
    onStart();
}

void AbstractDSCImageTile::stop()
{
    m_StartIng = false;
    {
        IBufferReadLocker locker(m_SonoBuffer, PRETTY_FUNCTION);
        if (m_SonoParameters != NULL)
        {
            disConnectSonoParameters();
        }
    }
    stopInternal();
}

void AbstractDSCImageTile::haltProcessImageChanged(const int frozenIndex)
{
    Q_UNUSED(frozenIndex);
    haltProcessImageChangedInner(m_FrozenIndex);
}

void AbstractDSCImageTile::recoveryProcessImageChanged()
{
    recoveryProcessImageChangedInner();
}

void AbstractDSCImageTile::haltProcessImageChangedInner(const int frozenIndex)
{
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << "#####" << PRETTY_FUNCTION << " ImageTileIndex:" << m_ImageTileIndex << " frozenIndex:" << frozenIndex;
#endif
    m_FrozenIndex = frozenIndex;
#ifdef SYS_APPLE
    m_IsNewImagePaused = true;
#else
    m_IsNewImagePaused.testAndSetOrdered(0, 1);
#endif
    disconnect(m_IBufferPushControler, SIGNAL(imageChanged(const QList<BufferUnit>&, const FrameUnitInfo&)), this,
               SLOT(onImageChangedDirectly(const QList<BufferUnit>&, const FrameUnitInfo&)));
#ifndef SYS_APPLE
    onHaltProcessImageChanged();
#endif
    QMutexLocker bufferLocker(&m_DeliverBufferMutex);
    m_DeliverBuffer.clear();
    m_DeliverBufferOld.clear();
    emit stopDeliverPushTimer();
#ifdef SYS_APPLE
    m_BufferChanged = false;
#else
    m_BufferChanged.testAndSetOrdered(1, 0);
#endif
}

void AbstractDSCImageTile::recoveryProcessImageChangedInner()
{
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << "#####" << PRETTY_FUNCTION << "ImageTileIndex:" << m_ImageTileIndex;
#endif
#ifdef SYS_APPLE
    m_IsNewImagePaused = false;
#else
    m_IsNewImagePaused.testAndSetOrdered(1, 0);
#endif
    connect(m_IBufferPushControler, SIGNAL(imageChanged(const QList<BufferUnit>&, const FrameUnitInfo&)), this,
            SLOT(onImageChangedDirectly(const QList<BufferUnit>&, const FrameUnitInfo&)),
            Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
    onRecoveryProcessImageChanged();
    QMutexLocker gstlocker(&m_DeliverBufferMutex);
    //#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    //    qDebug() << "#####" << PRETTY_FUNCTION
    //             << "ImageTileIndex:" << m_ImageTileIndex
    //             << "m_DeliverBuffer:" << m_DeliverBuffer.count()
    //             << "m_DeliverBufferOld:" << m_DeliverBufferOld.count();
    //#endif
    m_DeliverBuffer.clear();
    m_DeliverBufferOld.clear();
    emit startDeliverPushTimer();
}

void AbstractDSCImageTile::clearFrozenIndex()
{
#ifdef SYS_APPLE
    m_BufferChanged = false;
#else
    m_BufferChanged.testAndSetOrdered(1, 0);
#endif
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << "#####" << PRETTY_FUNCTION << "ImageTileIndex:" << m_ImageTileIndex << "m_FrozenIndex:" << m_FrozenIndex
             << "m_BufferChanged:" << m_BufferChanged;
#endif
    m_FrozenIndex = -1;
    onClearFrozenIndex();
}

void AbstractDSCImageTile::firstInit()
{
    m_SonoParametersChanging = true;
    onSonoParameterChangedDirectly();
}

void AbstractDSCImageTile::clearThreadData(int count)
{
}

bool AbstractDSCImageTile::isImage2DPushed() const
{
    return true;
}

void AbstractDSCImageTile::setEnforceEnable(bool state)
{
    Q_UNUSED(state);
}

void AbstractDSCImageTile::setColorMap(uchar* image2DMap, uchar* waveMap, int activeIndex)
{
    Q_UNUSED(image2DMap);
    Q_UNUSED(waveMap);
    Q_UNUSED(activeIndex);
}

void AbstractDSCImageTile::onEntryStressEchoAnalyze()
{
    m_InEasyPlayMode = true;
}

void AbstractDSCImageTile::onExitStressEchoAnalyze()
{
    m_InEasyPlayMode = false;
}

bool AbstractDSCImageTile::enForceDSCImage()
{
    return false;
}

void AbstractDSCImageTile::onPlayCineStatusChanged(const bool playCineStatus)
{
}

/**
 *初始化系统监听哪些超声参数的变化
 */
void AbstractDSCImageTile::connectSonoParameters()
{
    BOOST_FOREACH (const QString& paramName, CHANGED_SOURCES)
    {
        Parameter* param = m_SonoParameters->parameter(paramName);
        connect(param, SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
                SLOT(onBeforeParamChangedDirectly(const QVariant&, QVariant&)),
                Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
        connect(param, SIGNAL(valueChanged(const QVariant&, bool)), this,
                SLOT(onParamChangedDirectly(const QVariant&, bool)),
                Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    }
    connect(m_SonoParameters->parameter(BFPNames::LineImageDebugStr), SIGNAL(valueChanged(const QVariant&)), this,
            SLOT(onLineImageDebugChanged(const QVariant&)), Qt::DirectConnection);
    onLineImageDebugChanged(m_SonoParameters->pV(BFPNames::LineImageDebugStr));
}

void AbstractDSCImageTile::disConnectSonoParameters()
{
    BOOST_FOREACH (const QString& paramName, CHANGED_SOURCES)
    {
        Parameter* param = m_SonoParameters->parameter(paramName);
        disconnect(param, SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
                   SLOT(onBeforeParamChangedDirectly(const QVariant&, QVariant&)));
        disconnect(param, SIGNAL(valueChanged(const QVariant&, bool)), this,
                   SLOT(onParamChangedDirectly(const QVariant&, bool)));
    }
    disconnect(m_SonoParameters->parameter(BFPNames::LineImageDebugStr), SIGNAL(valueChanged(const QVariant&)), this,
               SLOT(onLineImageDebugChanged(const QVariant&)));
}

void AbstractDSCImageTile::onFlushedAllImages()
{
    {
        QMutexLocker gstlocker(&m_DeliverBufferMutex);
        // 2025-06-12 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
        // 绑定到临时对象，减少遍历时临时拷贝kyes
        const auto& keys = m_DeliverBuffer.keys();
        for (int mode : keys)
        {
            QQueue<FrameBufferUnitInfo>& fbuiQueue = m_DeliverBuffer[mode];
            if (!fbuiQueue.isEmpty())
            {
                m_WaitForFrozen = true;
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
                qDebug() << PRETTY_FUNCTION << "m_WaitForFrozen:" << m_WaitForFrozen
                         << "fbuiQueue.size():" << fbuiQueue.size();
#endif
                return;
            }
        }
    }
    emit flushedAllImages();
}

void AbstractDSCImageTile::onImageChangedDirectly(const QList<BufferUnit>& bufferUnits,
                                                  const FrameUnitInfo& frameUnitInfo)
{
    if (!frameUnitInfo.needPostProcess() || m_SonoParameters == NULL)
    {
        return;
    }

    FrameBufferUnitInfo fbui;
    fbui.first = bufferUnits;
    fbui.second = frameUnitInfo;
    fbui.second.detach();

    QMutexLocker gstlocker(&m_DeliverBufferMutex);
    //#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    //    if (2 == frameUnitInfo.imageType())
    //    {
    //        qCritical()<<"#####"<<PRETTY_FUNCTION
    //                <<" ImageTileIndex:"<<m_ImageTileIndex
    //                <<" frameUnitInfo.first().mode():"<<frameUnitInfo.first().mode()
    //                <<" frameUnitInfo.imageType():"<<frameUnitInfo.imageType()
    //                <<" m_GstreamerBuffer.count():"<<m_DeliverBuffer.count()
    //                <<" m_GstreamerPushTimer.isActive():"<<m_DeliverPushTimer->isActive();
    //    }
    //#endif
    if (m_DeliverBuffer.contains(frameUnitInfo.first().mode()))
    {
        QQueue<FrameBufferUnitInfo>& fbuiQueue = m_DeliverBuffer[frameUnitInfo.first().mode()];
        int framePushCount =
            frameUnitInfo.needPushMultipleFrames() ? m_SonoBuffer->frameAvgCount() : m_DeliverBufferCount;
        if (!m_SonoParameters->pBV(BFPNames::AutoEFOnStr) && framePushCount >= 0 && fbuiQueue.count() > framePushCount)
        {
            fbuiQueue.dequeue();
        }
        fbuiQueue.enqueue(fbui);
    }
    else
    {
        QQueue<FrameBufferUnitInfo> fbuiQueue;
        fbuiQueue.enqueue(fbui);
        m_DeliverBuffer.insert(frameUnitInfo.first().mode(), fbuiQueue);
    }
}

void AbstractDSCImageTile::onDeliverPushTimerTimeout()
{
    //#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    //    qDebug()<<"#####"<<PRETTY_FUNCTION
    //            <<" ImageTileIndex:"<<m_ImageTileIndex
    //            <<" m_GstreamerBuffer.count():"<<m_DeliverBuffer.count();
    //#endif
    QQueue<FrameBufferUnitInfo> fbuiPushQueue;
    {
        QMutexLocker gstlocker(&m_DeliverBufferMutex);
        if (!m_DeliverBufferOld.isEmpty())
        {
            // 2025-06-12 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
            // 绑定到临时对象，减少遍历时临时拷贝kyes
            const auto& keys = m_DeliverBufferOld.keys();
            for (int mode : keys)
            {
                QQueue<FrameBufferUnitInfo>& fbuiQueue = m_DeliverBufferOld[mode];
                while (!fbuiQueue.isEmpty())
                {
                    const FrameBufferUnitInfo& info = fbuiQueue.dequeue();
                    if (!info.first.last().isByPass())
                    {
                        fbuiPushQueue.enqueue(info);
                        break;
                    }
                }
            }
            m_DeliverBufferOld.clear();
        }
        // 2025-06-12 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
        // 绑定到临时对象，减少遍历时临时拷贝kyes
        const auto& keys = m_DeliverBuffer.keys();
        for (int mode : keys)
        {
            QQueue<FrameBufferUnitInfo>& fbuiQueue = m_DeliverBuffer[mode];

            while (!fbuiQueue.isEmpty())
            {
                const FrameBufferUnitInfo& info = fbuiQueue.dequeue();
                fbuiPushQueue.enqueue(info);
                if (!info.first.last().isByPass())
                {
                    break;
                }
            }
        }
        //        m_DeliverBuffer.clear();
        if (fbuiPushQueue.isEmpty())
        {
            return;
        }
    }
#ifndef SYS_APPLE
    if (m_BufferChanged.testAndSetOrdered(1, 0))
    {
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
        qCritical() << "#####" << PRETTY_FUNCTION << "ImageTileIndex:" << m_ImageTileIndex
                    << "m_BufferChanged:" << m_BufferChanged;
#endif
        return;
    }
#else
    if (m_BufferChanged)
    {
        m_BufferChanged = false;
        return;
    }
#endif
    {
        IBufferReadLocker locker(m_SonoBuffer, PRETTY_FUNCTION);
        //        qDebug() << "#####" << PRETTY_FUNCTION
        //                 << "ImageTileIndex:" << m_ImageTileIndex
        //                 << "fbuiPushQueue.size():" << fbuiPushQueue.size();
        while (!fbuiPushQueue.isEmpty())
        {
            FrameBufferUnitInfo fbui = fbuiPushQueue.dequeue();
            //            if (2 == fbui.second.imageType())
            //            {
            //                qCritical() << PRETTY_FUNCTION
            //                           << ", m_ImageDebug: " << m_ImageDebug;
            //            }
            if (m_ImageDebug > 0)
            {
                m_ImageDebugHelper->onImageChanged(fbui.first, fbui.second);
                continue;
            }
            if (!onImageChanged(fbui.first, fbui.second))
            {
                return;
            }
        }
    }
    if (m_WaitForFrozen)
    {
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
        qDebug() << PRETTY_FUNCTION << "m_WaitForFrozen:" << m_WaitForFrozen;
#endif
        {
            QMutexLocker gstlocker(&m_DeliverBufferMutex);
            // 2025-06-12 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
            // 绑定到临时对象，减少遍历时临时拷贝kyes
            const auto& keys = m_DeliverBuffer.keys();
            for (int mode : keys)
            {
                QQueue<FrameBufferUnitInfo>& fbuiQueue = m_DeliverBuffer[mode];
                if (!fbuiQueue.isEmpty())
                {
                    return;
                }
            }
            m_WaitForFrozen = false;
        }
        emit flushedAllImages();
    }
}

void AbstractDSCImageTile::onBeforeParamChangedDirectly(const QVariant& oldValue, QVariant& newValue)
{
    if (oldValue == newValue)
    {
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
        Parameter* p = dynamic_cast<Parameter*>(sender());
        qDebug() << PRETTY_FUNCTION << "oldValue:" << oldValue << "newValue:" << newValue
                 << (p != NULL ? p->name() : "NULL");
#else
        Q_UNUSED(oldValue);
        Q_UNUSED(newValue);
#endif
        return;
    }
    /*冻结状态下，可以进入静态宽景，此时修改了扫查模式，但是不需要修改gstreamer pipeline*/
    if (!m_InEasyPlayMode && (m_IsSonoParameterCleared || m_SonoParameters->pBV(BFPNames::FreezeStr)))
    {
        return;
    }
    onBeforeParamChanged();
}

void AbstractDSCImageTile::onParamChangedDirectly(const QVariant& value, bool changed)
{
    if (!changed)
    {
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
        Parameter* p = dynamic_cast<Parameter*>(sender());
        qDebug() << PRETTY_FUNCTION << "value:" << value << (p != NULL ? p->name() : "NULL");
#else
        Q_UNUSED(value);
#endif
        return;
    }
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << "#####" << PRETTY_FUNCTION << "m_ImageTileIndex:" << m_ImageTileIndex
             << "m_SonoparametersChangedCounter:" << m_SonoparametersChangedCounter
             << "m_IsSonoParameterCleared:" << m_IsSonoParameterCleared << "m_SonoParameters::Freeze:"
             << (!m_IsSonoParameterCleared ? m_SonoParameters->pV(BFPNames::FreezeStr).toString() : "NULL");
#endif
    /*冻结状态下，可以进入静态宽景，此时修改了扫查模式，
      但是不需要修改gstreamer pipeline*/
    if (!m_InEasyPlayMode && (m_IsSonoParameterCleared || m_SonoParameters->pBV(BFPNames::FreezeStr)))
    {
        return;
    }

    bool retStatus = staticMetaObject.invokeMethod(this, "onParamChanged", Qt::QueuedConnection,
                                                   Q_ARG(const unsigned int, m_SonoparametersChangedCounter));

#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << "#####" << PRETTY_FUNCTION << " ImageTileIndex:" << m_ImageTileIndex << "Call onParamChanged invoke ok"
             << retStatus;
#endif
}

void AbstractDSCImageTile::onBeforeSonoParametersChangedDirectly()
{
    {
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
        qDebug() << "#####" << PRETTY_FUNCTION << " ImageTileIndex:" << m_ImageTileIndex
                 << " Set m_SonoParametersChanging to true.";
#endif
        QWriteLocker writeLocker(&m_LockInMainAndDSCThread);
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
        qDebug() << "|||||" << PRETTY_FUNCTION << "m_LockInMainAndDSCThread";
#endif
        m_SonoParametersChanging = true;
    }
    onBeforeSonoParametersChanged();
}

void AbstractDSCImageTile::onSonoParameterChangedDirectly()
{
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << "#####" << PRETTY_FUNCTION << " ImageTileIndex:" << m_ImageTileIndex << " m_SonoParameters:" << Qt::hex
             << (quintptr)m_SonoParameters << " SonoBuffer->sonoParameters:" << Qt::hex
             << (quintptr)m_SonoBuffer->sonoParameters() << " m_SonoParametersChanging:" << m_SonoParametersChanging
             << " m_SonoparametersChangedCounter:" << m_SonoparametersChangedCounter;
#endif
    if ((m_SonoParameters == NULL) || (m_SonoParameters != m_SonoBuffer->sonoParameters()) || m_SonoParametersChanging)
    {
        m_IsSonoParameterCleared = false;

        m_BlockingChangedSlot = true;
        if ((m_SonoParameters == NULL) || (m_SonoParameters != m_SonoBuffer->sonoParameters()))
        {
            m_SonoParameters = m_SonoBuffer->sonoParameters();
            QWriteLocker locker(&m_LockInMainAndDSCThread);
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
            qDebug() << "|||||" << PRETTY_FUNCTION << "m_LockInMainAndDSCThread";
#endif
            m_WaitingSignalCounter = 0;
            m_SonoparametersChangedCounter++;
        }
        //整帧模式和线数据冻结情况下，不需要连接超声参数的变化
        if (m_SonoParameters->isRealTime())
        {
            connectSonoParameters();
        }
        m_BlockingChangedSlot = false;
#ifdef SYS_APPLE
        m_IsNewImagePaused = true;
#endif
        bool invokeOk = staticMetaObject.invokeMethod(this, "onSonoParametersChanged", Qt::QueuedConnection);
#ifdef SYS_APPLE
        QTime dieTime = QTime::currentTime().addMSecs(200);
        while (QTime::currentTime() < dieTime)
            while (m_IsNewImagePaused == true && QTime::currentTime() < dieTime)
                QCoreApplication::processEvents(QEventLoop::AllEvents);
#endif

#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
        qDebug() << "#####" << PRETTY_FUNCTION << " ImageTileIndex:" << m_ImageTileIndex
                 << "onSonoParametersChanged invoke status" << invokeOk;
#endif

        ASSERT_X_LOG(invokeOk, PRETTY_FUNCTION, "invoke failed");
    }
}

// void AbstractDSCImageTile::onClearSonoParametersDirectly()
//{
//#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
//    qDebug() << "#####" << PRETTY_FUNCTION << " ImageTileIndex:" << m_ImageTileIndex;
//#endif
//    onClearSonoParametersChanged();
//    m_SonoParameters = NULL;
//    m_IsSonoParameterCleared = true;
//}

void AbstractDSCImageTile::onBeforeBuffersChangedDirectly()
{
#ifdef SYS_APPLE
    m_BufferChanged = true;
#else
    m_BufferChanged.testAndSetOrdered(0, 1);
#endif
#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << "#####" << PRETTY_FUNCTION << "ImageTileIndex:" << m_ImageTileIndex
             << "m_BufferChanged:" << m_BufferChanged;
#endif
    haltProcessImageChangedInner(m_FrozenIndex);
}

void AbstractDSCImageTile::onBuffersChangedDirectly()
{
    recoveryProcessImageChangedInner();
}
void AbstractDSCImageTile::onBeforeBuffersChangedDirectly(int sonobufferIndex)
{
    onBeforeBuffersChangedDirectly();
}

void AbstractDSCImageTile::onBuffersChangedDirectly(int sonobufferIndex)
{
    onBuffersChangedDirectly();
}

void AbstractDSCImageTile::onCacheQueueCleardDirectly(const int imageBufferIndex)
{
    Q_UNUSED(imageBufferIndex);
    if ((m_SonoParameters != NULL) &&
        (m_SonoParameters->pBV(BFPNames::FreezeStr))) //冻结状态 播放电影 时候需要等待m_DeliverBuffer播放完毕
    {
        // 2025-06-12 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
        // 绑定到临时对象，减少遍历时临时拷贝kyes
        const auto& keys = m_DeliverBuffer.keys();
        for (int mode : keys)
        {
            QQueue<FrameBufferUnitInfo>& fbuiQueue = m_DeliverBuffer[mode];
            while (true)
            {
                if (fbuiQueue.size() > 0)
                    std::this_thread::yield();
                else
                    break;
            }
        }
        return;
    }
    //实时状态
    QMutexLocker gstlocker(&m_DeliverBufferMutex);
    // 2025-06-12 Modify by AlexWang 使用c++11的for...range遍历，使用const auto& 绑定到临时对象，减少遍历时临时拷贝kyes
    const auto& keys = m_DeliverBuffer.keys();
    for (int mode : keys)
    {
        QQueue<FrameBufferUnitInfo>& fbuiQueue = m_DeliverBuffer[mode];
        if (!fbuiQueue.isEmpty())
        {
            m_DeliverBufferOld.insert(mode, fbuiQueue);
        }
    }
    m_DeliverBuffer.clear();
}

void AbstractDSCImageTile::onCacheQueueCleard()
{
}

void AbstractDSCImageTile::onLineImageBufferChangedDirectly()
{
    stopInternal();

    bool invokeOk = staticMetaObject.invokeMethod(this, "onlineImageBufferChanged", Qt::QueuedConnection);

#ifdef ABSTRACT_DSC_ADAPTER_DEBUG_ENABLE
    qDebug() << "#####" << PRETTY_FUNCTION << " ImageTileIndex:" << m_ImageTileIndex
             << "onlineImageBufferChanged invoke status" << invokeOk;
#endif

    ASSERT_X_LOG(invokeOk, PRETTY_FUNCTION, "invoke failed");
}

void AbstractDSCImageTile::onFpsUpdate(const QString& addinfo, float fpsV)
{
    emit fpsChanged(QString("%1_%2").arg(addinfo).arg(m_ImageTileIndex), fpsV);
}

void AbstractDSCImageTile::onLineImageDebugChanged(const QVariant& value)
{
    m_ImageDebug = value.toInt();
    m_ImageDebugHelper->setImageDebug(m_ImageDebug);
    if (m_ImageDebug > 0)
    {
        m_ImageDebugHelper->beforeSonoParametersChanged();
        m_ImageDebugHelper->sonoParameterChanged(m_SonoParameters);
        connect(m_ImageDebugHelper, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewImage(LineImageArgs*)),
                Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
    }
    else
    {
        m_ImageDebugHelper->clearSonoParameters();
        disconnect(m_ImageDebugHelper, SIGNAL(newImage(LineImageArgs*)), this, SLOT(onNewImage(LineImageArgs*)));
    }
}

void AbstractDSCImageTile::onNewImage(LineImageArgs* lineImageArgs)
{
    lineImageArgs->setIndex(m_ImageTileIndex);
    emit newImage(lineImageArgs);

    //    if(m_WaitForFrozen)
    //    {
    //        qDebug() << PRETTY_FUNCTION
    //                 << "m_WaitForFrozen:" << m_WaitForFrozen;
    //        {
    //            QMutexLocker gstlocker(&m_DeliverBufferMutex);
    //            foreach(int mode, m_DeliverBuffer.keys())
    //            {
    //                QQueue<FrameBufferUnitInfo> &fbuiQueue = m_DeliverBuffer[mode];
    //                if(!fbuiQueue.isEmpty())
    //                {
    //                    return;
    //                }
    //            }
    //            m_WaitForFrozen = false;
    //        }
    //        emit flushedAllImages();
    //    }
}

void AbstractDSCImageTile::onNewMeasureImage(LineImageArgs* lineImageArgs)
{
    // lineImageArgs->setIndex(m_ImageTileIndex);
    emit newMeasureImage(lineImageArgs);
}
