#ifndef SONOBUFFER_H
#define SONOBUFFER_H

#include "buffer_global.h"

#include <QMutex>
#include <QSemaphore>
#include <QTimer>

#include "bytebuffer.h"
#include "frameinfo.h"
#include "icineloopbuffer.h"
#include "iimageframeinfosetter.h"
#include "imagebufferdef.h"
#include "infostruct.h"
#include "iprocessedimagebufferdatasetter.h"
#include "isonobuffer.h"
#include "postrawdatamanager.h"
#include "errorcode.h"

class SonoParameters;
class ImageBufferGroup;
template <typename T> class QVector;
class BufferUnit;
class FrameUnitInfo;
class IRawImageBufferDataSetter;
class MultiParamTrigger;
class ImageEventArgs;
class SonoBufferStorer;
class SonoParametersChangedSynchronizer;
class IImageSaveHelper;
class SonobufferImageSaveHelper;
/**
 * @brief The SonoBuffer class 管理Layout中一个区域的buffer和SonoParameters
 *
 * SonoParameters 的管理方式：
 * 实时状态：active的持有GlobalSonoParameters，非active的持有当时GlobalSonoParameters的clone
 * 冻结状态：都是用GlobalSonoParameters的clone，如果此时修改参数，是修改clone后的参数，不会修改到GlobalSonoParameters
 *         中，在解冻后，参数仍会使用GlobalSonoParameters的参数
 *
 * SonoBuffer中持有ImageBufferGroup的三个实例，一个实时的，一个回调的，一个StresEcho的
 * SonoBuffer代表一个layout缓存信息, SonoBuffers代表多个layout的缓存信息，目前最多4个
 */
class USF_INTERFACE_IMAGING_BUFFERSHARED_EXPORT SonoBuffer : public ISonoBuffer,
                                                             public IImageFrameInfoSetter,
                                                             public ICineLoopBuffer,
                                                             public IProcessedImageBufferDataSetter
{
    Q_OBJECT
public:
    explicit SonoBuffer(int index, IImageSaveHelper* imageSaveHelper, QObject* parent = 0);
    ~SonoBuffer();

    QString bufferName() const;
    /**
     * @brief 返回当前可写的ImageBufferGroup
     */
    IRawImageBufferDataSetter* dataSetter();

    ImageBufferGroup* dataBuffer();

    PostRawDataManager* postRawDataManager();

    QList<int> createNewLineImageBuffer(const QList<FrameUnitInfo>& frameGroupInfo, const int bufferSize = 256);
    void clearProcessLineImageBuffer();
    void changeLineImageBuffer(const QList<int> indexs);
    void destoryLineImageBuffer(const QList<int> indexs);
    void enqueue(BufferUnit* bufferUnit, const int imageBufferIndex);

    /**
     * @brief IFrameInfoSwitcher 调用
     * @param frameGroupInfo
     */
    void setFrameGroupInfo(const QList<FrameUnitInfo>& frameGroupInfo);
    /**
     * @brief IFrameInfoSwitcher 调用
     * @param index
     * @param frameUnitInfo
     */
    void setFrameUnitInfo(int index, const FrameUnitInfo& frameUnitInfo);

    /**
     * @brief cloneFrome
     * @param sonoBuffer
     */
    void cloneDataFrom(SonoBuffer* sonoBuffer);
    void backupWave();
    void backupLastLineImageData();
    void backupLastLineImageData(SonoBuffer* sonoBuffer);
    void recoverLastBackup2DLineImageData();
    void setUseOldBackUpData(const int imageBufferIndex, const bool useOld);

    void setEnableLineImageBackUp(const bool enable);
    /**
     * @brief 返回的时ImageBufferGroup中的LineImageBuffer的个数
     * @return
     */
    int bufferCount();
    /**
     * @brief 获取索引
     */
    int startIndex(bool toShowValue = true);
    int endIndex(bool toShowValue = true);
    int currentIndex(bool toShowValue = true);
    int frameCount();
    int frameCount(const int bufferIndex);
    int frameCountByFrameType(int frameType);
    int startIndex(const int bufferIndex, const int index);
    int endIndex(const int bufferIndex, const int index);
    int currentIndex(const int bufferIndex, const int index);
    int indexDuration(int frameType, const int index);

    void onRequestFlushFrameByIndex(const int bufferIndex, const int index);
    /**
     * @brief CineLoopPlayer线程请求清空波形缓冲
     * @param bufferIndex
     */
    void onRequestClearCache(const int bufferIndex, const int index);

    void setPlayCineStatus(bool value);

    int cineBufferIndex();

    bool isCineActive(const int bufferIndex);

    bool isCineFpsCanModify(const int bufferIndex);

    bool isStressEcho() const;
    /**
     * @brief 设置复合参数值
     * @param scpdValue
     */
    void setScpdValue(const int scpdValue);

    virtual int residueSize(const int imageBufferIndex);
    bool isActive() const;
    void setActive(bool value);
    bool isFrozen() const;
    void setFreeze(bool value);
    //由于setFreeze的控制权受图像处理链路影响，不能快速直接响应冻结/解冻操作
    //而WriteAble的标记会用于数据接收使用，从设计角度，执行冻结后立即停止数据接收
    void setWorkState(bool isScaning);
    void setRestoreFlag(bool flag);
    void reStore();
    void setMemory(const ByteBuffer& mem);
    void setGlobalSonoParameters(SonoParameters* value);

    void syncGlobalSonoparamters();

    void clearFreezeParasRecord();
    /**
     * @brief 冻结播放电影的时候由ChisonContex设置的当前索引，
     * 可以及时更新播放冻结条的索引显示
     * @param currentIndex
     */
    bool updateCurrentIndexOnFrozen(const int currentIndex, const int imageType);
    /**
     * @brief UI由实时到冻结，由播放电影到暂停，用于同步UI显示索引与
     * 内部保存索引
     */
    void syncDisplayIndex(const int displayIndex, const bool isUpdateFrameCount);
    /**
     * @brief 同步播放电影区间
     * @param startIndex
     */
    void setStartIndex(const int startIndex);
    void setEndIndex(const int endIndex);
    /**
     * @brief setCurrentIndex逐帧播放时设置当前的图像索引号
     * @param currentIndex
     */
    void setCurrentIndex(const int currentIndex);

    void requestFlush(const int frameIndex = -1, const int bufferIndex = -1, bool isMeasureData = false);

    double defaultFps(const int bufferIndex = -1);

    void setDefaultFps(int bufferIndex, double fps);

    void onNewImage(ImageEventArgs* imageEventArgs);

    /**
     * @brief 获取SonoBuffer索引
     */
    virtual int sonoBufferIndex() const;
    /**
     * @brief 获取当前使用的超声参数
     */
    virtual SonoParameters* sonoParameters() const;
    virtual QList<FrameUnitInfo> dispalyFrameUnitInfos();

    virtual void acquire(int n = 1);
    virtual void release(int n = 1);

    virtual bool isLoad() const;

    bool isRealTimeSaveRunning() const;

    virtual int getNextBuffer(QList<BufferUnit>& bufferUnits, FrameUnitInfo& frameUnitInfo, const int imageBufferIndex);
    virtual int getNextBuffer(QList<BufferUnit>& bufferUnits, FrameUnitInfo& frameUnitInfo, int& residueCacheSize,
                              const int imageBufferIndex, const int lineNumber);
    virtual int getBufferByIndex(QList<BufferUnit>& bufferUnits, FrameUnitInfo& frameUnitInfo,
                                 const int imageBufferIndex, const int frameIndex = 0);
    virtual int bufferIndex() const;

    virtual bool markDataPushOnce(int imageBufferIndex);

    virtual int scpdvalue();

    virtual int scpdvalue(const int bufferIndex);

    virtual int frameAvgCount();

    virtual void setRawDataImage(uchar* data, int width, int height);

    virtual void setGrayRawDataImage(uchar* data, int width, int height);

    /* 返回存储时的大小, totalFrame true:所有帧 false:单帧
     */
    quint64 fileByteSize(bool totalFrame = false) const;
    /**
     * @brief            超声参数  sonoparameters.bin
     *                   图像数据  *.bin
     *                   其他必要信息  frameinfo.bin
     * @param dirPath
     * @return
     */
    bool saveCine(const QString& dirPath, bool hasInterval = false);

    bool realTimeSaveCine(const QString& dirPath);

    bool saveCurrent(const QString& dirPath, int frameIndex = -1, int frontIndex = -1);

    /**
     * @brief load 加载超声参数
     * @param dirPath
     * @return
     */
    LoadErrorCode load(const QString& dirPath);

    bool checkLoadFilesValidity(const QString& dirPath);

    bool loadStressEcho(const QString& dirPath);

    void removeAll(bool clearUnActive = true);

    void setMaxUsedSize(const int maxSize);

    void setIsRealTimeStoreState(const bool value);

    void prepareRestore(const bool isActive);

    void onRestore(const bool isActive);

    void onStressEchoRestore();

    void onStopPipeline(bool clearRealTimeData);

    void deleteLoadSonoparameters();

    void requestStaticImage(const bool isLoad);

    void requestPictureImage();

    void setIsFileMapEnable(bool isEnable);

    bool isFileMapEnable() const;

    void setIsRealTimeSaveCine(bool value);

    bool isRealTimeSaveCine() const;

    bool saveCineFinished() const;

    void setSaveCineFinished(bool finished);

    void removeBuffer(int bufferIndex);

    void updateSystemScanModeChanging(bool changing);

    void enableIsSetPreset();

    bool isOnSetPreset() const;

    int getFrameIndexByFrameTimestamp(const qint64& timestamp, bool needRealIndex = true);

    qint64 getFramestampByIndex(int frameIndex, bool needRealIndex = true);

    int whloeFrameIndex(const int type, const int typeIndex);

    void drawUnActiveRenderPartitionImages();

    bool existUnActiveRender() const;
    QByteArray getWholeFrameData(int frameType, const int index);

    void setPictureImage(QImage image);
    QImage pictureImage();
signals:
    void createCineLoopPlayer(const int layoutIndex, ICineLoopBuffer* cineLoopBuffer);

    void destoryCineLoopPlayer(const int layoutIndex);

    void updateCinePlayStartIndex(const int layoutIndex);

    void updateCinePlayEndIndex(const int layoutIndex);

    void updateCinePlayCurrentIndex(const int layoutIndex);

    void cineStopLoop(const int layoutIndex);

    void staticImage(ImageEventArgs* imageEventArgs);

    void pictureImage(QImage image);

    void notifyLoadingProgess(const quint64 bytes);

    void notifySavingProgess(const quint64 bytes);
    /**
     * @brief 实时保存队列已经存满需要保存的数据
     */
    void realTimeSaveBufferIsFull();

    void startForceFlushTimer();

    void stopForceFlushTimer();

    void fpsChanged(const QString& name, const float fps);

    void realTimeSaveCineState(int state);

    void freezeSaveCineState(int state, bool);
public slots:
    void forceFlush();

    void onRequesFlushByBufferIndex(const int bufferIndex);

private:
    const QHash<int, ImageEventArgs*>& getSaveImages() const;
    void destoryRGBAImages(QHash<int, ImageEventArgs*>& images);
    void destoryLoadRGBAImages();
    void destoryRealTimeRGBAImages();

    void onBeforeSetSonoParameters();

    void onSetSonoParameters();

    void beforeLoadImageData(bool isStressEcho);

    void onload();

    void onloadFailed();

    void onStressEchoLoad();

    void onStressEchoLoadFailed();

    void connectConnections(ImageBufferGroup* bufferGroup);

    void disconnectConnections(ImageBufferGroup* bufferGroup);

    void setSonoParameters(SonoParameters* sonoParameters);

    ImageBufferGroup* currentImageBufferGroup() const;

    void connectClearAction();
    /**
     * @brief 建立清空的trigger
     */
    void recreateClearTrigger(bool forceCreate);

    void connectSonoParameters();
    void disconnectSonoParameters();

    void updateUnActiveRenderPartitionRGBAImages(ImageEventArgs* imageEventArgs);
    void updateUnActiveRenderPartitionRGBAImages(const QHash<int, ImageEventArgs*>& images);

    void controlPictureModeOnParametersSignal(bool connectSignal);
private slots:

    //    void onPostTGCChanged(const QVariant& value);
    void onPostLGCChanged(const QVariant& value);
    void onProbeIdChanged(const QVariant& value);
    void onForceFlushTimeout();
    void onCurrentIndexChanged(const int frameIndex);

    void onBeforeBuffersChanged();

    void onBuffersChanged();

    void onRealTimeSaveCineState(int state, int frameCount, const QString& dirPath);

    void onFrameAvgChanged();

    void onBeforeFreqSpectrumChanged(const QVariant& oldValue, QVariant& newValue);

    void onNeedleModeChanged(const QVariant& value);

    void updateUnActiveRenderPartition();

    void onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue);

    void onBeforeLayoutChanged(const QVariant& oldValue, QVariant& newValue);

    void onIsDopplerScanLineVisibleChanged(QVariant value);

    void onIsCWDScanLineVisibleChanged(QVariant value);

    void onIsMLineVisibleChanged(QVariant value);

private:
    /*全局超声参数*/
    SonoParameters* m_GlobalSonoParameters;
    /*冻结时使用的超声参数*/
    SonoParameters* m_FreezeSonoParameters;
    /**
     * 当前使用的超声参数，其指向m_GlobalSonoParameters、m_FreezeSonoParameters...
     * 中任意一个
     */
    SonoParameters* m_SonoParameters;

    SonoParameters* m_RealTimeSaveSonoParameters;
    QMutex m_RealTimeSaveParamLock;

    ByteBuffer m_Memory;
    ByteBuffer m_LoadMemory;
    QHash<int, ImageEventArgs*> m_RealTimeRGBAImages;
    QHash<int, ImageEventArgs*> m_LoadRGBAImages;
    //目前当前sonobuffer也存在多个布局的情况，因此用这个来存储非激活区的整帧数据
    QHash<int, ImageEventArgs*> m_UnActiveRenderPartitionRGBAImages;
    //目前当前sonobuffer也存在多个布局的情况，因此用这个标记来标记非激活区域的索引
    imagePatition m_UnActiveRenderPartition;
    QTimer* m_ForceFlushTimer;
    QMutex m_RGBAImagesSync;

    int m_SonoBufferIndex;
    bool m_IsLoaded;
    bool m_IsStressEcho;
    bool m_IsActive;
    bool m_IsFrozen;
    bool m_IsPlayCine;
    bool m_IsBufferCleared;
    bool m_IsPaused;
    bool m_SaveCineFinished;
    bool m_IsRestore;
    bool m_IsPrepareRestore;
    bool m_SystemScanModeChanging;
    bool m_IsOnSetPreset;
    bool m_IsNeedleMode;
    /**
     * @brief 在接收不同图像的过程中触发清屏操作的参数组合
     */
    MultiParamTrigger* m_ClearTrigger;
    ImageBufferGroup* m_Buffer;
    ImageBufferGroup* m_ProcessedBuffer;
    bool m_UseProcessedBuffer;
    bool m_UseProcessedBufferOnLoad;
    ImageBufferGroup* m_LoadBuffer;
    SonoBufferStorer* m_Storer;
    ImageBufferGroup* m_StressEchoBuffer; // TODO 重构，未来希望抽象这个buffer，不仅仅是支持stressecho
    SonoBufferStorer* m_StressEchoStorer;
    QHash<int, ImageEventArgs*> m_StressRGBAImages;
    SonoParametersChangedSynchronizer* m_SyncParameters;

    QSemaphore m_Semaphore;

    QStringList m_FrameAvgList;

    PostRawDataManager* m_postRawDataManager;

    SonobufferImageSaveHelper* m_ImageSaveHelper;
};

#endif // SONOBUFFER_H
