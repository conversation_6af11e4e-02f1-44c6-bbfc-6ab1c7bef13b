#ifndef SONOBUFFERS_H
#define SONOBUFFERS_H

#include "buffer_global.h"

#include <QMutex>
#include <QObject>
#include <QVector>
#include <QWaitCondition>

#include "baselinebuffer.h"
#include "iimageframeinfos.h"
#include "irawimagebuffers.h"
#include "sonoparametersclientbase.h"

class ISonoBuffer;
class SonoBuffer;
class SonoParameters;
class ImageMemoryPool;
class ICineLoopBuffer;
class ImageEventArgs;
class SonoBuffersStorer;
class IProcessedImageBufferDataSetter;
class IImageSaveHelper;
#ifdef USE_4D
class FourDBufferPreProcess;
class FourDVolumeLineImageArgs;
#endif

/**
 * @brief The SonoBuffers class
 *
 * SonoBuffer代表一个layout缓存信息, SonoBuffers代表多个layout的缓存信息，目前最多4个
 */
class USF_INTERFACE_IMAGING_BUFFERSHARED_EXPORT SonoBuffers : public BaseLineBuffer,
                                                              public SonoParametersClientBase,
                                                              public IRawImageBuffers,
                                                              public IImageFrameInfos
{
    Q_OBJECT
public:
    explicit SonoBuffers(ImageMemoryPool* mem, bool isSupportLayout, IImageSaveHelper* imageSaveHelper,
                         QObject* parent = 0);
    ~SonoBuffers();

    QString bufferName() const;

    bool isSupportLayout() const;
    void setIsSupportLayout(bool value);
    int layout() const;
    IImageFrameInfoSetter* activeFrameInfoSetter() const;
    IRawImageBufferDataSetter* activeDataSetter() const;
    IProcessedImageBufferDataSetter* activeProcessedDataSetter() const;
    QList<ISonoBuffer*> getSonoBuffer() const;
    QVector<SonoBuffer*> buffers() const;

    /**
     * @brief 冻结时在播放电影的时候由ChisonContex更新收到的图像的Index
     */
    void updateCurrentIndexOnFrozen(const int layoutIndex, const int currentIndex, const int imageType);

    void prepareRestore(const int activeIndex);
    /**
     * @brief 由电影播放状态返回到实时状态时，需要释放加载的文件
     */
    void onRestore();
    /**
     * @brief 由电影播放状态返回到实时状态时，需要释放加载的超声参数
     * 需要等到图形widget变换完成后才能释放这部分内存
     */
    void deleteLoadSonoparameters();

    void reStore();

    void setRestoreFlag(bool flag);

    int activeLayoutIndex() const;

    int bufferCount() const;
    /**
     * @brief bufferType
     * @return
     */
    BufferTypeEnum::BufferType bufferType() const;
    /**
     * @brief UI由实时打图到冻结\UI由电影播放到暂停 需要同步显示的FrameIndex
     * @param isUpdateFrameCount 标记是否需要更新FrameCount
     * 实时打图==>冻结时, 需要更新FrameCount/EndIndex
     * 电影回放==>冻结时, 不需要更新FrameCount/EndIndex
     * @param layoutIndex 默认值-1 表示当前激活区域，否则表示指定区域
     */
    void syncDisplayIndex(const int displayIndex, const bool isUpdateFrameCount, const int layoutIndex = -1);
    /**
     * @brief 获取/设置索引
     */
    int startIndex(bool toShowValue = true, const int layoutIndex = -1) const;
    void setStartIndex(const int startIndex, const int layoutIndex = -1);
    int endIndex(bool toShowValue = true, const int layoutIndex = -1) const;
    void setEndIndex(const int endIndex, const int layoutIndex = -1);
    int currentIndex(bool toShowValue = true, const int layoutIndex = -1) const;
    /**
     * @brief setCurrentIndex逐帧播放时设置当前的图像索引号
     * @param currentIndex
     */
    void setCurrentIndex(const int currentIndex, const int layoutIndex = -1, bool needUpdateFreezeBar = true);

    int frameCount(const int layoutIndex = -1) const;

    int indexDuration(const int frameType, const int index, const int layoutIndex = -1);

    int frameCountByFrameType(int frameType, const int layoutIndex = -1) const;
    /*!
     * 返回存储时的大小, totalFrame true:所有帧 false:单帧
     */
    quint64 fileByteSize(bool totalFrame = false) const;
    /**
     * @brief
     * 保存的文件结构：     全局超声参数 sonoparameters.bin
     *                               elasticcolumn.bin
     *                   结构信息     layoutinfo.bin
     *                   1/2/3/4    超声参数  sonoparameters.bin
     *                              图像数据  *.bin
     *                              其他必要信息  frameinfo.bin
     * @param dirPath
     * @return
     */
    bool saveCine(const QString& dirPath);

    bool realTimeSaveCine(const QString& dirPath);

    bool saveCurrent(const QString& dirPath, int frameIndex = -1, int frontIndex = -1);

    /**
     * @brief load 从文件加载电影图片
     * @param dirPath
     * @param layoutIndex
     * @return
     */
    LoadErrorCode load(const QString& dirPath, const int layoutIndex = -1);

    bool checkLoadFilesValidity(const QString& filePath, const int layoutIndex = -1);

    SonoParameters* getSonoParametersByLayoutIndex(const int layoutIndex) const;

    void removeAll(const int layoutIndex = -1, bool refresh = true);

    void setMaxUsedSize(const int maxSize = 256);

    bool getOneFrame(const int frameIndex, ImageEventArgs* imageEventArgs);

    void onNewImage(ImageEventArgs* imageEventArgs);

    double defaultFps(const int layoutIndex = -1) const;

    void onload();

    void requestFlush(const int layoutIndex = -1, const int frameIndex = -1, const int bufferIndex = -1,
                      bool isMeasureData = false);

    void requestStaticImage(const int layout, const bool isLoad = false);

    void requestPictureImage();

    void setIsRealTimeSaveCine(bool value, const int layoutIndex = -1);

    bool isRealTimeSaveCine(const int layoutIndex = -1) const;

    /**
     * @brief update更新所有索引号
     */
    void update();

    //    void updateCurrentIndex();

    void backUpWave();
    void clearImageBufferDataOnOncePushLimit(int imagebufferIndex, int layoutIndex = -1);
#ifdef USE_4D
    bool getFourdVolumeArgByIndex(int index, FourDVolumeLineImageArgs& arg);
    void startFourdBufferProcess(quint32 sliceNum);
    void stopFourdBufferProcess();
#endif

    void setFreeze(const bool frozen);
    //由于setFreeze的控制权受图像处理链路影响，不能快速直接响应冻结/解冻操作
    //而WriteAble的标记会用于数据接收使用，从设计角度，执行冻结后立即停止数据接收
    void setWorkState(bool isScaning);

    int startIndex(const int frameIndex, const int bufferIndex, const int layoutIndex = -1) const;

    int endIndex(const int frameIndex, const int bufferIndex, const int layoutIndex = -1) const;
    int scpdValue(const int layoutIndex = -1) const;

    void removeAll(const int layoutIndex, const int bufferIndex);

    void syncAvtiveBGlobalSonoparamters();

    void clearAvtiveBFreezeParasRecord();

    void onSetPreset();

    int getFrameIndexByFrameTimestamp(const qint64& timestamp, int layoutIndex = -1, bool needRealIndex = true);

    qint64 getFramestampByIndex(int frameIndex, int layoutIndex = -1, bool needRealIndex = true);

    int whloeFrameIndex(const int type, const int typeIndex);

    void drawUnActiveRenderPartitionImages(int layoutIndex = -1);
    bool existUnActiveRender(int layoutIndex = -1) const;
    QByteArray getWholeFrameData(int frameType, const int index);

    void setActiveBufferPictureImage(QImage image);
    QImage activeBufferPictureImage();

protected:
    void onSetSonoParameters();
signals:
    /**
     * @brief 电影播放信号
     */
    void createCineLoopPlayer(const int layoutIndex, ICineLoopBuffer* cineLoopBuffer);

    void destoryCineLoopPlayer(const int layoutIndex);

    void updateCinePlayStartIndex(const int layoutIndex);

    void updateCinePlayEndIndex(const int layoutIndex);

    void updateCinePlayCurrentIndex(const int layoutIndex);

    void cineStopLoop(const int layoutIndex);

    void sonoBufferLoaded(const int layoutNum, const bool isLayout, const int activeIndex, bool isLoaded);

    void staticImage(ImageEventArgs* imageEventArgs);

    void pictureImage(QImage image);

    void beforeActiveIndexChanged();
    void activeIndexChanged(int index);
    void beforeLayoutChanged();
    void layoutChanged(int layout);
    void imageSavingProgressChanged(int percent);
    void fileByteSizeGot(int byteSize);
    void beginPaused();
    void endPaused();
    void fpsChanged(const QString& name, const float fps);
    void realTimeSaveCineState(int state);

    void beforeBufferCleared(int bufferGroupIndex);

    void bufferCleared(int bufferGroupIndex);

    void beforeBuffersChanged(int sonobufferIndex);

    void buffersChanged(int sonobufferIndex);

    void getSyncId(int& syncId);

private:
    SonoBuffer* activeBuffer() const;
    SonoBuffer* at(int index) const;
    /**
     * @brief setLayout 设置Layout
     *
     * @param value [1~4]，如果 @a isSupportLayout true则支持[1,4],
     * 1,2:将一整块内存分成2块使用, 4:将一整块内存分成4块使用
     * 否则只支持 1，将一整块内存全部给buffer使用
     */
    void setLayout(int value, const bool changed = true);

    void setActiveIndex(int value);

    void changePostLGC(const int index, const QVariant& value);

#ifdef USE_4D
    void switchFourdBufferPreProcess();
#endif
private slots:
    /**
     * @brief 需要监测到复合参数的变化
     */
    void onNeedleModeChanged(const QVariant& value);
    void onNeedleAngleIndexChanging(const QVariant& value);
    void onScpdChanged(const QVariant& value);
    void onScpdTrapeChanged(const QVariant& value);
    void onLayoutChanging(const QVariant& value, const bool changed = true);
    void onBeforeActiveBChanged(const QVariant& oldValue, QVariant& newValue);
    void onActiveBChanging(const QVariant& value);
    void onBeforeFreezeChanged(const QVariant& oldValue, QVariant& newValue);
    void onFreezeChanging(const QVariant& value);
    void onFreezeChanged(const QVariant& value);
    void onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue);
    void onSystemScanModeChanged(const QVariant& value);
    void onFreqSpectrumChanged(const QVariant& value);
    void onTriplexModeChanged(const QVariant& value);
    void onQuadplexModeStrChanged(const QVariant& value);
    void onSaveCineStateChanged(int state, bool freezeSave = false);
    void onPWSoundDelayTimeChanged(const QVariant& value);
    void onPWWaveImageDelayTimeChanged(const QVariant& value);
    void onPW2DImageDelayTimeChanged(const QVariant& value);
    // void onPWECGDelayTimeChanged(const QVariant& value);
    void onMWaveImageDelayTimeChanged(const QVariant& value);
    void onM2DImageDelayTimeChanged(const QVariant& value);
    //    void onMECGDelayTimeChanged(const QVariant& value);
    //    void onECGDelayTimeChanged(const QVariant& value);
    //    void onECG2DImageDelayTimeChanged(const QVariant& value);
    /*!
     * 保存电影时，向外通知保存的进度
     * \param progess 百分比进度值
     */
    void onNotifySavingProgess(quint64 byteSize);
    /*!
     * 回调电影时，向外通知回调的进度
     * \param progess 百分比进度值
     */
    void onNotifyLoadingProgess(quint64 byteSize);
    void onClearActiveBuffer();

private:
    int memNum(int layout) const;

    void updateLGCOnLayout1x1();

    void calcBufferMem();

    void setBuffersMem(int bufferNum);

    bool checkAllBufferSaveFinished();

private:
    static QString m_Version;
    QVector<SonoBuffer*> m_Buffers;
    bool m_IsSupportLayout;
    int m_Layout;
    int m_ActiveIndex;
    bool m_IsRealTimeStoreState;
    bool m_IsFrozen;
    ImageMemoryPool* m_MemoryPool;
    SonoBuffersStorer* m_Storer;
    bool m_Loaded;
    int m_LoadLayout;
    int m_LoadActive;
    quint64 m_FinishedByteSize;
    quint64 m_FileByteSize;

    int m_SystemScanMode;
    QMutex m_ReadOneFrameLock;
    QWaitCondition m_ReadOneFrameCondition;
    ImageEventArgs* m_ImageEventArgs;
    QMutex m_AllBufferSavedLock;
    bool m_IsLoading;
};

#endif // SONOBUFFERS_H
