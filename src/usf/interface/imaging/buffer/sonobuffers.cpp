#include "sonobuffers.h"
#include "assertlog.h"
#include "bfpnames.h"
#include "dataarg.h"
#include "imageeventargs.h"
#include "imagememorypool.h"
#include "infostruct.h"
#include "irawimagebufferdatasetter.h"
#include "logger.h"
#include "memoryleakcheck.h"
#include "modelconfig.h"
#include "parameter.h"
#include "setting.h"
#include "settingitem.h"
#include "sonobuffer.h"
#include "sonobuffersstorer.h"
#include "sonoparameters.h"
#include "systemscanmodeclassifier.h"
#include "util.h"
#include <QDir>
#include <QRunnable>
#ifdef USE_4D
#include "fourdbufferpreprocess.h"
#include "fourdvolumelineimageargs.h"
#endif
#include "abstractstate.h"
#include "imagebuffergroup.h"
#include "postparametersync.h"
#include "stateeventnames.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, SonoBuffers)

QString SonoBuffers::m_Version = "0.1";

SonoBuffers::SonoBuffers(ImageMemoryPool* mem, bool isSupportLayout, IImageSaveHelper* imageSaveHelper, QObject* parent)
    : BaseLineBuffer(parent)
    , m_Buffers(QVector<SonoBuffer*>(Layout_Max))
    , m_IsSupportLayout(isSupportLayout)
    , m_Layout(-1)
    , m_ActiveIndex(-1)
    , m_MemoryPool(mem)
    , m_Loaded(false)
    , m_FinishedByteSize(0)
    , m_FileByteSize(0)
    , m_IsRealTimeStoreState(false)
    , m_SystemScanMode(SystemScanModeB)
    , m_Storer(new SonoBuffersStorer())
    , m_LoadLayout(-1)
    , m_LoadActive(-1)
    , m_IsFrozen(false)
    , m_ImageEventArgs(NULL)
    , m_IsLoading(false)
{
    CHECK_NEW(SonoBuffersStorer, m_Storer);
    if (m_IsSupportLayout)
    {
        m_Buffers = QVector<SonoBuffer*>(Layout_Max);
    }
    else
    {
        m_Buffers = QVector<SonoBuffer*>(Layout_1x1);
        m_Layout = Layout_1x1;
        m_ActiveIndex = 0;
    }

    bool isFileMapEnable = Setting::instance().defaults().isFileMapEnable();
    for (int i = 0; i < m_Buffers.count(); i++)
    {
        SonoBuffer* buffer = new SonoBuffer(i, imageSaveHelper, this);
        buffer->setIsFileMapEnable(isFileMapEnable);
        m_Buffers[i] = buffer;
        CHECK_NEW(SonoBuffer, m_Buffers[i]);
        connect(m_Buffers[i], SIGNAL(createCineLoopPlayer(int, ICineLoopBuffer*)), this,
                SIGNAL(createCineLoopPlayer(int, ICineLoopBuffer*)), Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(destoryCineLoopPlayer(int)), this, SIGNAL(destoryCineLoopPlayer(int)),
                Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(updateCinePlayStartIndex(int)), this, SIGNAL(updateCinePlayStartIndex(int)),
                Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(updateCinePlayEndIndex(int)), this, SIGNAL(updateCinePlayEndIndex(int)),
                Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(updateCinePlayCurrentIndex(int)), this, SIGNAL(updateCinePlayCurrentIndex(int)),
                Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(cineStopLoop(int)), this, SIGNAL(cineStopLoop(int)), Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(notifyLoadingProgess(quint64)), this, SLOT(onNotifyLoadingProgess(quint64)));
        connect(m_Buffers[i], SIGNAL(notifySavingProgess(quint64)), this, SLOT(onNotifySavingProgess(quint64)));
        connect(m_Buffers[i], SIGNAL(realTimeSaveBufferIsFull()), this, SIGNAL(realTimeBufferIsFull()));
        connect(m_Buffers[i], SIGNAL(freezeSaveCineState(int, bool)), this, SLOT(onSaveCineStateChanged(int, bool)));
        connect(m_Buffers[i], SIGNAL(realTimeSaveCineState(int)), this, SLOT(onSaveCineStateChanged(int)));
        connect(m_Buffers[i], SIGNAL(staticImage(ImageEventArgs*)), this, SIGNAL(staticImage(ImageEventArgs*)),
                Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(pictureImage(QImage)), this, SIGNAL(pictureImage(QImage)), Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(fpsChanged(const QString&, const float)), this,
                SIGNAL(fpsChanged(const QString&, const float)), Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(beforeSonoParametersChanged(SonoParameters*, SonoParameters*)), this,
                SIGNAL(beforeSonoParametersChanged(SonoParameters*, SonoParameters*)), Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(sonoParametersChanged(SonoParameters*)), this,
                SIGNAL(sonoParametersChanged(SonoParameters*)), Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(sonoParametersChanged()), this, SIGNAL(sonoParametersChanged()),
                Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(beforeBufferCleared(int)), this, SIGNAL(beforeBufferCleared(int)),
                Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(bufferCleared(int)), this, SIGNAL(bufferCleared(int)), Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(beforeBuffersChanged(int)), this, SIGNAL(beforeBuffersChanged(int)),
                Qt::DirectConnection);
        connect(m_Buffers[i], SIGNAL(buffersChanged(int)), this, SIGNAL(buffersChanged(int)), Qt::DirectConnection);
    }

    if (isFileMapEnable)
    {
        setBuffersMem(m_Buffers.count());
    }
}

SonoBuffers::~SonoBuffers()
{
    CHECK_DELETE_LIST(SonoBuffer, m_Buffers);
    qDeleteAll(m_Buffers);
    m_Buffers.clear();
    if (m_Storer != NULL)
    {
        CHECK_DELETE(SonoBuffersStorer, m_Storer);
        delete m_Storer;
        m_Storer = NULL;
    }
}

QString SonoBuffers::bufferName() const
{
    return QString("SonoBuffers");
}

bool SonoBuffers::isSupportLayout() const
{
    return m_IsSupportLayout;
}

void SonoBuffers::setIsSupportLayout(bool value)
{
    m_IsSupportLayout = value;
}

int SonoBuffers::layout() const
{
    return m_Layout;
}

IImageFrameInfoSetter* SonoBuffers::activeFrameInfoSetter() const
{
    return at(m_ActiveIndex);
}

IRawImageBufferDataSetter* SonoBuffers::activeDataSetter() const
{
    return at(m_ActiveIndex)->dataSetter();
}

IProcessedImageBufferDataSetter* SonoBuffers::activeProcessedDataSetter() const
{
    return at(m_ActiveIndex);
}

SonoBuffer* SonoBuffers::activeBuffer() const
{
    return at(m_ActiveIndex);
}

SonoBuffer* SonoBuffers::at(int index) const
{
    if (index >= 0 && index < Layout_Max)
    {
        return m_Buffers[index];
    }
    else
    {
        return NULL;
    }
}

void SonoBuffers::updateCurrentIndexOnFrozen(const int layoutIndex, const int currentIndex, const int imageType)
{
    ASSERT_LOG(isLayoutIndexValid(layoutIndex));
    if (layoutIndex == m_ActiveIndex)
    {
        if (!at(layoutIndex)->updateCurrentIndexOnFrozen(currentIndex, imageType))
        {
            return;
        }
        if (m_SonoParameters->pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime())
        {
            emit currentIndexChanged(currentIndex, layoutIndex, frameCount(layoutIndex));
        }
    }
}

void SonoBuffers::prepareRestore(const int activeIndex)
{
    foreach (SonoBuffer* buffer, m_Buffers)
    {
        buffer->prepareRestore(activeIndex == buffer->bufferIndex());
    }
}

void SonoBuffers::onRestore()
{
    //    qDebug() << PRETTY_FUNCTION
    //             << "m_LoadLayout:" << m_LoadLayout
    //             << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime();
    foreach (SonoBuffer* buffer, m_Buffers)
    {
        buffer->onRestore(m_ActiveIndex == buffer->bufferIndex());
    }
    if (m_Layout > Layout_Min)
    {
        emit sonoBufferLoaded(m_Layout, true, m_ActiveIndex, false);
    }
    else
    {
        emit sonoBufferLoaded(m_Buffers.first()->bufferCount(), false, m_ActiveIndex, false);
    }
    m_Loaded = false;
}

void SonoBuffers::deleteLoadSonoparameters()
{
    foreach (SonoBuffer* buffer, m_Buffers)
    {
        buffer->deleteLoadSonoparameters();
    }
}

void SonoBuffers::reStore()
{
    activeBuffer()->reStore();
}

void SonoBuffers::setRestoreFlag(bool flag)
{
    activeBuffer()->setRestoreFlag(flag);
}

int SonoBuffers::activeLayoutIndex() const
{
    return m_ActiveIndex;
}

int SonoBuffers::bufferCount() const
{
    return m_Buffers.count();
}

BufferTypeEnum::BufferType SonoBuffers::bufferType() const
{
    return BufferTypeEnum::LineImage;
}

void SonoBuffers::syncDisplayIndex(const int displayIndex, const bool isUpdateFrameCount, const int layoutIndex)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        at(layoutIndex)->syncDisplayIndex(displayIndex, isUpdateFrameCount);
    }
    else
    {
        activeBuffer()->syncDisplayIndex(displayIndex, isUpdateFrameCount);
    }
}

void SonoBuffers::requestFlush(const int layoutIndex, const int frameIndex, const int bufferIndex, bool isMeasureData)
{
    if (m_IsLoading)
    {
        return;
    }
//    if(layoutIndex == 100)
//    {
//        activeBuffer()->setPlayCineStatus(true);
//    }
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "layoutIndex:" << layoutIndex << "frameIndex:" << frameIndex
             << "m_SystemScanMode:" << m_SystemScanMode
             << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime()
             << "m_SonoParameters->pBV(BFPNames::FreezeStr):" << m_SonoParameters->pBV(BFPNames::FreezeStr)
             << "PwBackup2DImage:" << ModelConfig::instance().value(ModelConfig::PwBackup2DImage, false).toBool();
#endif
    if (ModelConfig::instance().value(ModelConfig::PwBackup2DImage, false).toBool() && m_SonoParameters->isRealTime() &&
        !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        if (SystemScanModeClassifier::isLikeD(m_SystemScanMode) && !activeBuffer()->isOnSetPreset())
        {
            activeBuffer()->recoverLastBackup2DLineImageData();
        }
    }
    if (isLayoutIndexValid(layoutIndex))
    {
        at(layoutIndex)->requestFlush(frameIndex, bufferIndex, isMeasureData);
    }
    else
    {
        activeBuffer()->requestFlush(frameIndex, bufferIndex, isMeasureData);
    }
}

int SonoBuffers::startIndex(bool toShowValue, const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->startIndex();
    }
    else
    {
        return activeBuffer()->startIndex();
    }
}
int SonoBuffers::startIndex(const int frameIndex, const int bufferIndex, const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->startIndex(bufferIndex, frameIndex);
    }
    else
    {
        return activeBuffer()->startIndex(bufferIndex, frameIndex);
    }
}

int SonoBuffers::endIndex(const int frameIndex, const int bufferIndex, const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->endIndex(bufferIndex, frameIndex);
    }
    else
    {
        return activeBuffer()->endIndex(bufferIndex, frameIndex);
    }
}
int SonoBuffers::scpdValue(const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->scpdvalue();
    }
    else
    {
        return activeBuffer()->scpdvalue();
    }
}
void SonoBuffers::removeAll(const int layoutIndex, const int bufferIndex)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        IBufferReadLocker locker(m_Buffers[layoutIndex], PRETTY_FUNCTION);
        m_Buffers[layoutIndex]->removeBuffer(bufferIndex);
    }
}

void SonoBuffers::syncAvtiveBGlobalSonoparamters()
{
    activeBuffer()->syncGlobalSonoparamters();
    onScpdTrapeChanged(m_SonoParameters->pV(BFPNames::ScpdTrapeStr));
}

void SonoBuffers::clearAvtiveBFreezeParasRecord()
{
    activeBuffer()->clearFreezeParasRecord();
}

void SonoBuffers::setStartIndex(const int startIndex, const int layoutIndex)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        at(layoutIndex)->setStartIndex(startIndex);
        emit startIndexChanged(startIndex, layoutIndex);
    }
    else
    {
        activeBuffer()->setStartIndex(startIndex);
        emit startIndexChanged(startIndex, m_ActiveIndex);
    }
}

int SonoBuffers::endIndex(bool toShowValue, const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->endIndex(toShowValue);
    }
    else
    {
        return activeBuffer()->endIndex(toShowValue);
    }
}

void SonoBuffers::setEndIndex(const int endIndex, const int layoutIndex)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        at(layoutIndex)->setEndIndex(endIndex);
        emit endIndexChanged(endIndex, layoutIndex);
    }
    else
    {
        activeBuffer()->setEndIndex(endIndex);
        emit endIndexChanged(endIndex, m_ActiveIndex);
    }
}

int SonoBuffers::currentIndex(bool toShowValue, const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->currentIndex(toShowValue);
    }
    else
    {
        return activeBuffer()->currentIndex(toShowValue);
    }
}

void SonoBuffers::setCurrentIndex(const int currentIndex, const int layoutIndex, bool needUpdateFreezeBar)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        if (at(layoutIndex)->frameCount() >= 1)
        {
            at(layoutIndex)->setCurrentIndex(currentIndex);
            emit currentIndexChanged(currentIndex, layoutIndex, frameCount(layoutIndex));
        }
    }
    else
    {
        if (activeBuffer()->frameCount() >= 1)
        {
            activeBuffer()->setCurrentIndex(currentIndex);
            if (needUpdateFreezeBar)
            {
                emit currentIndexChanged(currentIndex, m_ActiveIndex, frameCount(m_ActiveIndex));
            }
        }
    }
}

int SonoBuffers::frameCount(const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->frameCount();
    }
    else
    {
        return activeBuffer()->frameCount();
    }
}

int SonoBuffers::indexDuration(const int frameType, const int index, const int layoutIndex)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->indexDuration(frameType, index);
    }
    else
    {
        return activeBuffer()->indexDuration(frameType, index);
    }
}

int SonoBuffers::frameCountByFrameType(int frameType, const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->frameCountByFrameType(frameType);
    }
    else
    {
        return activeBuffer()->frameCountByFrameType(frameType);
    }
}

QList<ISonoBuffer*> SonoBuffers::getSonoBuffer() const
{
    QList<ISonoBuffer*> ret;

    for (int i = 0; i < m_Buffers.size(); i++)
    {
        ret.append(reinterpret_cast<ISonoBuffer*>(m_Buffers[i]));
    }
    return ret;
}

QVector<SonoBuffer*> SonoBuffers::buffers() const
{
    return m_Buffers;
}

quint64 SonoBuffers::fileByteSize(bool totalFrame) const
{
    quint64 total = 0;
    for (int i = 0; i < layout(); i++)
    {
        IBufferReadLocker locker(m_Buffers[i], PRETTY_FUNCTION);
        total += m_Buffers[i]->fileByteSize(totalFrame);
    }
    return total;
}

bool SonoBuffers::saveCine(const QString& dirPath)
{
    m_FileByteSize = fileByteSize(true);

    QDir dir(dirPath);
    for (int i = 0; i < layout(); i++)
    {
        dir.mkdir(QString("%1").arg(i));
        if (!m_Buffers[i]->saveCine(QString("%1/%2").arg(dirPath).arg(i)))
        {
#ifdef BUFFER_DEBUG_ENABLE
            qDebug() << "&&&&&" << PRETTY_FUNCTION << "save failed:" << dirPath;
#endif
            return false;
        }
    }
    if (!m_IsFrozen)
    {
        PostParameterSync::syncRealTimeArgsValue2PostArgs(m_SonoParameters, true);
    }
    if (!m_Storer->save(dirPath, sonoParameters()))
    {
        return false;
    }

#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "save success" << dirPath;
#endif
    return true;
}

class SonoBufferSaveTask : public QRunnable
{
public:
    SonoBufferSaveTask(SonoBuffer* sonoBuffer, const QString& dir)
        : m_SonoBuffer(sonoBuffer)
        , m_Dir(dir)
    {
    }

private:
    void run()
    {
        bool saveOK = m_SonoBuffer->saveCine(m_Dir, true);
        if (!saveOK)
        {
            Q_ASSERT(false);
        }
        // m_SonoBuffer->setSaveCineFinished(true);
    }
    SonoBuffer* m_SonoBuffer;
    QString m_Dir;
};

bool SonoBuffers::realTimeSaveCine(const QString& dirPath)
{
    QDir dir(dirPath);

    if (!m_IsFrozen)
    {
        PostParameterSync::syncRealTimeArgsValue2PostArgs(m_SonoParameters, true);
    }

    for (int i = 0; i < layout(); i++)
    {
        dir.mkdir(QString("%1").arg(i));
        QString dirName = QString("%1/%2").arg(dirPath).arg(i);

        SonoBuffer* sonoBuffer = m_Buffers[i];
        sonoBuffer->setSaveCineFinished(false);

        if (sonoBuffer->isActive())
        {
            if (!sonoBuffer->realTimeSaveCine(dirName))
            {
                return false;
            }
        }
        else
        {
            SonoBufferSaveTask* task = new SonoBufferSaveTask(sonoBuffer, dirName);
            Util::runRunnable(task);
        }
    }

    if (!m_Storer->save(dirPath, sonoParameters()))
    {
        return false;
    }
    return true;
}

bool SonoBuffers::saveCurrent(const QString& dirPath, int frameIndex, int frontIndex)
{
    m_FileByteSize = fileByteSize(false);

    QDir dir(dirPath);
    for (int i = 0; i < layout(); i++)
    {
        dir.mkdir(QString("%1").arg(i));
        if (!m_Buffers[i]->saveCurrent(QString("%1/%2").arg(dirPath).arg(i), m_ActiveIndex == i ? frameIndex : -1,
                                       m_ActiveIndex == i ? frontIndex : -1))
        {
#ifdef BUFFER_DEBUG_ENABLE
            qDebug() << "&&&&&" << PRETTY_FUNCTION << "save failed:" << dirPath;
#endif
            return false;
        }
    }
    if (!m_IsFrozen)
    {
        PostParameterSync::syncRealTimeArgsValue2PostArgs(m_SonoParameters, true);
    }
    if (!m_Storer->save(dirPath, sonoParameters()))
    {
        return false;
    }

#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "save success" << dirPath;
#endif
    return true;
}

LoadErrorCode SonoBuffers::load(const QString& dirPath, const int layoutIndex)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "dirPath:" << dirPath << "layoutIndex:" << layoutIndex;
#endif
    emit beginPaused();
    //加载layout信息，仅判断了文件是否能只读打开或者文件存不存在
    if (!m_Storer->load(dirPath))
    {
        emit endPaused();
        return LoadErrorCode::Layoutinfo_bin_LoadFailed;
    }

    if (isLayoutIndexValid(layoutIndex))
    {
        LoadErrorCode errorCode = m_Buffers[m_ActiveIndex]->load(QString("%1/%2").arg(dirPath).arg(layoutIndex));
        if (errorCode != LoadErrorCode::Success)
        {
            emit endPaused();
            return errorCode;
        }
        m_LoadLayout = 1;
        m_LoadActive = 0;
    }
    else
    {
        int layout = m_Storer->layout();
        for (int i = 0; i < layout; i++)
        {
            m_IsLoading = true;
            LoadErrorCode errorCode = m_Buffers[i]->load(QString("%1/%2").arg(dirPath).arg(i));
            if (errorCode != LoadErrorCode::Success)
            {
                m_IsLoading = false;
                emit endPaused();
                return errorCode;
            }
        }
        m_LoadLayout = layout;
        m_LoadActive = m_Storer->activeIndex();
    }
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << " Load Success";
#endif
    emit endPaused();
    return LoadErrorCode::Success;
}

bool SonoBuffers::checkLoadFilesValidity(const QString& filePath, const int layoutIndex)
{
    if (!m_Storer->checkLoadFilesValidity(filePath))
    {
        return false;
    }

    if (isLayoutIndexValid(layoutIndex))
    {
        return m_Buffers[m_ActiveIndex]->checkLoadFilesValidity(QString("%1/%2").arg(filePath).arg(layoutIndex));
    }
    else
    {
        QDir dir(filePath);
        QStringList layoutDirList = dir.entryList(QDir::Dirs | QDir::NoDot | QDir::NoDotDot);
        for (int i = 0; i < layoutDirList.count(); ++i)
        {
            if (!m_Buffers[i]->checkLoadFilesValidity(QString("%1/%2").arg(filePath).arg(layoutDirList[i])))
            {
                return false;
            }
        }
        return true;
    }
}

SonoParameters* SonoBuffers::getSonoParametersByLayoutIndex(const int layoutIndex) const
{
    //    qDebug() << PRETTY_FUNCTION
    //             << "layoutIndex:" << layoutIndex
    //             << "m_SonoParameters:" << (quintptr) m_SonoParameters
    //             << "isRealTime:" << m_SonoParameters->isRealTime()
    //             << "freeze:" << m_SonoParameters->pBV(BFPNames::FreezeStr);
    if (isLayoutIndexValid(layoutIndex))
    {
        if (m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr) &&
            (m_ActiveIndex == layoutIndex))
        {
            return m_SonoParameters;
        }
        return at(layoutIndex)->sonoParameters();
    }
    else
    {
        return m_SonoParameters;
    }
}

void SonoBuffers::removeAll(const int layoutIndex, bool refresh)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "layoutIndex:" << layoutIndex << "refresh:" << refresh;
#endif
    Q_UNUSED(refresh)
    if (isLayoutIndexValid(layoutIndex))
    {
        IBufferReadLocker locker(m_Buffers[layoutIndex], PRETTY_FUNCTION);
        if ((m_SonoParameters->isRealTime()) && (SystemScanModeClassifier::isLikeD(m_SystemScanMode)) &&
            !activeBuffer()->isOnSetPreset())
        {
            m_Buffers[layoutIndex]->removeAll(false);
        }
        else
        {
            m_Buffers[layoutIndex]->removeAll(true);
        }
    }
    else
    {
        for (int i = 0; i < m_Buffers.count(); i++)
        {
            IBufferReadLocker locker(m_Buffers[i], PRETTY_FUNCTION);
            if ((m_SonoParameters->isRealTime()) && (SystemScanModeClassifier::isLikeD(m_SystemScanMode)) &&
                !activeBuffer()->isOnSetPreset())
            {
                m_Buffers[i]->removeAll(false);
            }
            else
            {
                m_Buffers[i]->removeAll(true);
            }
        }
    }
}

void SonoBuffers::setMaxUsedSize(const int maxSize)
{
    for (int i = 0; i < m_Buffers.count(); i++)
    {
        m_Buffers[i]->setMaxUsedSize(maxSize);
    }
}

bool SonoBuffers::getOneFrame(int frameIndex, ImageEventArgs* imageEventArgs)
{
    bool retValue;
    activeBuffer()->requestFlush(frameIndex);
    m_ReadOneFrameLock.lock();
    m_ImageEventArgs = imageEventArgs;
    retValue = m_ReadOneFrameCondition.wait(&m_ReadOneFrameLock, 1000);
    m_ImageEventArgs = NULL;
    m_ReadOneFrameLock.unlock();
    return retValue;
}

void SonoBuffers::onNewImage(ImageEventArgs* imageEventArgs)
{
    if ((m_ImageEventArgs != NULL) && (imageEventArgs->imageType() == ImageEventArgs::ImageB) &&
        ((m_SystemScanMode == SystemScanModeFreeHand3D)
         /*|| (m_SystemScanMode == SystemScanModeCP)*/))
    {
        m_ReadOneFrameLock.lock();
        if (m_ImageEventArgs != NULL)
        {
            ASSERT_LOG(imageEventArgs->imageSize() == m_ImageEventArgs->imageSize());
            memcpy(m_ImageEventArgs->imageData(), imageEventArgs->imageData(), m_ImageEventArgs->imageSize());
        }
        m_ReadOneFrameLock.unlock();
        m_ReadOneFrameCondition.wakeOne();
    }
    else
    {
        if ((imageEventArgs->index() >= 0) && (imageEventArgs->index() < m_Buffers.count()))
        {
            m_Buffers[imageEventArgs->index()]->onNewImage(imageEventArgs);
        }
    }
}

double SonoBuffers::defaultFps(const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->defaultFps();
    }
    else
    {
        return activeBuffer()->defaultFps();
    }
}

void SonoBuffers::onSetSonoParameters()
{
    if ((m_ActiveIndex >= 0) && (m_IsSupportLayout))
    {
        m_ActiveIndex = m_SonoParameters->pIV(BFPNames::ActiveBStr);
    }
    //需要按顺序执行
    onLayoutChanging(m_SonoParameters->pV(BFPNames::LayoutStr), false);
    QVariant activeBIndex = m_SonoParameters->pV(BFPNames::ActiveBStr);
    onBeforeActiveBChanged(0, activeBIndex);
    onActiveBChanging(m_SonoParameters->pV(BFPNames::ActiveBStr));
    if (m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr))
    {
        onScpdTrapeChanged(m_SonoParameters->pV(BFPNames::ScpdTrapeStr));
    }
    else
    {
        onScpdChanged(m_SonoParameters->pV(BFPNames::ScpdStr));
    }
    m_IsFrozen = m_SonoParameters->pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime();

    if (!m_IsFrozen)
    {
        onFreqSpectrumChanged(m_SonoParameters->pV(BFPNames::FreqSpectrumStr));
        onTriplexModeChanged(m_SonoParameters->pV(BFPNames::TriplexModeStr));
        onQuadplexModeStrChanged(m_SonoParameters->pV(BFPNames::QuadplexModeStr));
    }

    if (m_IsFirstSetSonoParameters)
    {
        for (int i = 0; i < m_Buffers.count(); i++)
        {
            m_Buffers[i]->setGlobalSonoParameters(m_SonoParameters);
        }
    }

#ifdef USE_4D
    switchFourdBufferPreProcess();
#endif
}

void SonoBuffers::setLayout(int value, const bool changed)
{
    if (m_IsSupportLayout)
    {
        if (value < Layout_Min || value > Layout_Max)
        {
            return;
        }
    }
    else
    {
        if (value != 1)
        {
            return;
        }
    }
    if (changed)
    {
        //大于m_Layout的全部停止
        if (m_SonoParameters->isRealTime())
        {
            for (int i = 1; i < m_Buffers.count(); i++)
            {
                m_Buffers[i]->onStopPipeline(m_SonoParameters->isRealTime());
            }
        }
        else
        {
            for (int i = value; i < m_Buffers.count(); i++)
            {
                m_Buffers[i]->onStopPipeline(m_SonoParameters->isRealTime());
            }
        }
    }
    else
    {
        for (int i = value; i < m_Buffers.count(); i++)
        {
            if (i != m_ActiveIndex)
            {
                m_Buffers[i]->onStopPipeline(false);
            }
        }
    }

    /*在回调状态下才执行此部分内容。回调状态下的解冻，由onRestore恢复SonoBuffer的Active状态
     *这样做的目的可以减少一次gstreamer重建的过程
     */
    if (m_Loaded && (m_SonoParameters->isRealTime()))
    {
        for (int i = 0; i < value; i++)
        {
            if (i != m_ActiveIndex)
            {
                m_Buffers[i]->setActive(false);
            }
            else
            {
                m_Buffers[i]->setActive(true);
            }
        }
    }

    /*在回调状态下解冻，不需要调用 setGlobalSonoParameters*/
    if ((m_SonoParameters->isRealTime()) && (!m_Loaded))
    {
        PostParameterSync::syncRealTimeArgsValue2PostArgs(m_SonoParameters, true);
        int oldMemNum = memNum(m_Layout);
        int newMemNum = memNum(value);
        QList<ByteBuffer> blocks = m_MemoryPool->memoryBlocks(newMemNum);
        int iCount = value < blocks.count() ? value : blocks.count();
        for (int i = 0; i < iCount; i++)
        {
            m_Buffers[i]->setGlobalSonoParameters(m_SonoParameters);
        }
    }

    if (m_Layout != value)
    {
        m_Layout = value;
        //        emit layoutChanged(m_Layout);
    }
}

void SonoBuffers::setActiveIndex(int value)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_ActiveIndex:" << m_ActiveIndex << "value:" << value;
#endif
    if (value >= 0 && value < m_Layout)
    {
        /*为了可以复制上次一激活区域的数据，需要先enable当前区域，再拷贝数据，
         然后才能disable上一次激活区域*/
        if ((m_Layout > Layout_Min) && isLayoutIndexValid(m_ActiveIndex))
        {
            m_Buffers[value]->setActive(true);
            if ((value != m_ActiveIndex) && !m_SonoParameters->pBV(BFPNames::FreezeStr) &&
                m_SonoParameters->isRealTime())
            {
                //此处代码来源:
                //[Apple]修正2B下在一个激活区域按实时保存，然后快速切换到其他激活区域再快速切回来崩溃问题。
                //此处的设计应该是切换激活区域时，立即结束保存操作，目前业务流的效果是立即结束保存，因此注释掉此处代码
                //                while(m_Buffers[value]->isRealTimeSaveRunning())
                //                {
                //                    Util::usleep(50);
                //                }
                //                const QList<FrameUnitInfo> frameGroupInfo =
                //                m_Buffers[m_ActiveIndex]->frameGroupInfo();
                //                m_Buffers[value]->setFrameGroupInfo(frameGroupInfo);
                m_Buffers[value]->cloneDataFrom(m_Buffers[m_ActiveIndex]);
            }
        }
        else
        {
            m_Buffers[value]->setActive(true);
        }
        if ((m_ActiveIndex != value) && (m_ActiveIndex >= 0) && (m_ActiveIndex < Layout_Max))
        {
            m_Buffers[m_ActiveIndex]->setActive(false);
        }
        m_ActiveIndex = value;
        //        emit activeIndexChanged(m_ActiveIndex);
        update();
    }
}

void SonoBuffers::onload()
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << PRETTY_FUNCTION << "m_LoadLayout:" << m_LoadLayout
             << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime()
             << "Freeze:" << m_SonoParameters->pBV(BFPNames::FreezeStr);
#endif
    if ((m_LoadLayout >= Layout_Min) && (m_LoadLayout <= Layout_Max))
    {
        update();

        requestStaticImage(m_LoadLayout, true);
        m_IsLoading = false;
        requestFlush(m_LoadActive);
        m_Loaded = true;
    }
    m_LoadLayout = -1;
    //这里的判断的原因是回调解冻过程是先回到实时冻结状态，更新sonparameters，这个状态下刷新实时下的多布局图像，然后再解冻
    //针对多b非激活区的刷新，只需要刷新整帧静态图即可，不需要重新走后处理链路刷新
    if (m_SonoParameters->isRealTime() && m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        int layout = m_SonoParameters->pIV(BFPNames::LayoutStr);
        if (Layout_1x1 == layout)
        {
            //[Apple][bug:65127]【zeus3.0】C模式、PD\DPD、M模式下回调一个TVI\TVD电影解冻后，过渡图像会有一时卡顿
            // 1、实现方案：
            //分析：
            //   共通的问题：解冻之后，布局先变化了，但是新数据还没有刷新。
            //   例如：在B模式回调BC或者BCPW，解冻后，都是布局先变了，数据后刷新。
            //   在2B和4B，回调解冻是好的。
            //解决：回调解冻时，先 requestFlush 旧的数据，来保证数据和布局一起刷新。
            // 2、影响范围：
            // 3、Test Case：
            //   回调、解冻。
            requestFlush();
        }
        else
        {
            requestStaticImage(layout);
        }
    }

    requestPictureImage();
    //在解决回调偶发崩溃的问题的时候，SonoBuffers::onload()里面的修改，判断条件永远不成立，
    //直接将m_Loaded = true;的操作放到可以执行的地方。
    //    if((m_LoadLayout >= Layout_Min) && (m_LoadLayout <= Layout_Max))
    //        m_Loaded = true;
}

void SonoBuffers::onNeedleModeChanged(const QVariant& value)
{
    if (m_SonoParameters->isRealTime())
    {
        if (value.toBool())
        {
            at(m_ActiveIndex)->setScpdValue(m_SonoParameters->pIV(BFPNames::ScpdStr));
        }
        else
        {
            onScpdChanged(m_SonoParameters->pV(BFPNames::ScpdStr));
        }
    }
}

void SonoBuffers::onNeedleAngleIndexChanging(const QVariant& value)
{
    Q_UNUSED(value);
    if (m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        activeDataSetter()->clear();
    }
}

void SonoBuffers::onScpdChanged(const QVariant& value)
{
    if (m_SonoParameters->isRealTime())
    {
        if (SystemScanModeClassifier::isWithOutCompound(m_SystemScanMode))
        {
            at(m_ActiveIndex)->setScpdValue(0);
        }
        else
        {
            at(m_ActiveIndex)->setScpdValue(value.toInt());
        }
    }
}

void SonoBuffers::onScpdTrapeChanged(const QVariant& value)
{
    if (m_SonoParameters->isRealTime())
    {
        if (m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr))
        {
            if (SystemScanModeClassifier::isWithOutCompound(m_SystemScanMode))
            {
                at(m_ActiveIndex)->setScpdValue(0);
            }
            else
            {
                at(m_ActiveIndex)->setScpdValue(value.toInt());
            }
        }
        else
        {
            onScpdChanged(m_SonoParameters->pV(BFPNames::ScpdStr));
        }
    }
}

void SonoBuffers::onLayoutChanging(const QVariant& value, const bool changed)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "value:" << value << "m_ActiveIndex:" << m_ActiveIndex;
#endif
    if (m_SonoParameters->isRealTime() && !m_Loaded)
    {
        calcBufferMem();
        emit beforeLayoutChanged();
    }
    setLayout(value.toInt(), changed);
    if (m_SonoParameters->isRealTime() && !m_Loaded)
    {
        removeAll();
        emit layoutChanged(value.toInt());
    }
}

void SonoBuffers::onBeforeActiveBChanged(const QVariant& oldValue, QVariant& newValue)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "oldValue:" << oldValue << "newValue:" << newValue
             << "m_Loaded:" << m_Loaded << "isRealTime:" << m_SonoParameters->isRealTime()
             << "layout:" << m_SonoParameters->pIV(BFPNames::LayoutStr);
#endif
    emit beforeActiveIndexChanged();

    IBufferWriteLocker locker(this, PRETTY_FUNCTION);

    if (m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        PostParameterSync::syncRealTimeArgsValue2PostArgs(m_SonoParameters, true);
        removeAll(newValue.toInt());
    }
    if (m_IsSupportLayout)
    {
        setActiveIndex(newValue.toInt());
    }
    else
    {
        setActiveIndex(0);
    }
    emit activeIndexChanged(newValue.toInt());
}

void SonoBuffers::onActiveBChanging(const QVariant& value)
{
}

void SonoBuffers::onBeforeFreezeChanged(const QVariant& oldValue, QVariant& newValue)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "oldValue:" << oldValue.toBool() << "newValue:" << newValue.toBool();
#endif
    m_IsFrozen = newValue.toBool();

    if (!m_IsFrozen)
    {
        removeAll(activeLayoutIndex());
    }
    if (m_IsFrozen)
    {
        //        if(!oldValue.toBool())
        //        {
        //            PostParameterSync::syncRealTimeArgsValue2PostArgs(m_SonoParameters, true);
        //        }
    }
    else
    {
        updateLGCOnLayout1x1();
    }
}

void SonoBuffers::onFreezeChanging(const QVariant& value)
{
}

void SonoBuffers::onFreezeChanged(const QVariant& value)
{
    if (value.toBool())
    {
        PostParameterSync::syncRealTimeArgsValue2PostArgs(m_SonoParameters, true);
    }
    else
    {
        PostParameterSync::syncPostArgs2RealTimeArgsValue(m_SonoParameters, true);
    }
}

void SonoBuffers::setFreeze(const bool frozen)
{
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_ActiveIndex:" << m_ActiveIndex << "frozen:" << frozen;
    for (int i = 0; i < m_Layout; i++)
    {
        at(i)->setFreeze(m_IsFrozen);
    }
    emit isFrozenChanged(m_IsFrozen, m_ActiveIndex);
    update();
#ifdef USE_4D
    switchFourdBufferPreProcess();
#endif
}

void SonoBuffers::setWorkState(bool isScaning)
{
    for (int i = 0; i < m_Layout; i++)
    {
        at(i)->setWorkState(isScaning);
    }
}

void SonoBuffers::requestStaticImage(const int layout, const bool isLoad)
{
    //    qDebug() << PRETTY_FUNCTION
    //             << "layout:" << layout
    //             << "isLoad:" << isLoad
    //             << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime()
    //             << "m_LoadActive:" << m_LoadActive
    //             << "m_ActiveIndex:" << m_ActiveIndex;
    if (m_SonoParameters->isRealTime() || isLoad)
    {
        for (int i = 0; i < layout; i++)
        {
            if ((isLoad && (i != m_LoadActive)) || (!isLoad && (i != m_ActiveIndex)))
            {
                at(i)->requestStaticImage(isLoad);
            }
        }
    }
}

void SonoBuffers::requestPictureImage()
{
    activeBuffer()->requestPictureImage();
}

void SonoBuffers::setIsRealTimeSaveCine(bool value, const int layoutIndex)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->setIsRealTimeSaveCine(value);
    }
    else
    {
        return activeBuffer()->setIsRealTimeSaveCine(value);
    }
}

bool SonoBuffers::isRealTimeSaveCine(const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->isRealTimeSaveCine();
    }
    else
    {
        return activeBuffer()->isRealTimeSaveCine();
    }
}

void SonoBuffers::updateLGCOnLayout1x1()
{
    foreach (const QString& lgc, BFPNames::LGCStrs)
    {
        m_SonoParameters->parameter(lgc)->update();
    }
}

void SonoBuffers::calcBufferMem()
{
    if (NULL == m_SonoParameters)
    {
        return;
    }

    SonoBuffer* activeBuffer = at(0);
    if (activeBuffer != NULL)
    {
        if (!activeBuffer->isFileMapEnable())
        {
            setBuffersMem(m_SonoParameters->pIV(BFPNames::LayoutStr));
        }
    }
}

void SonoBuffers::setBuffersMem(int bufferNum)
{
    if (NULL == m_MemoryPool)
    {
        return;
    }

    QList<ByteBuffer> blocks = m_MemoryPool->memoryBlocks(bufferNum);
    for (int i = 0; i < m_Buffers.count(); i++)
    {
        IBufferWriteLocker locker(m_Buffers[i], PRETTY_FUNCTION);
        int blockIndex = qMin(bufferNum - 1, i);
        m_Buffers[i]->setMemory(blocks[blockIndex]);
    }
}

bool SonoBuffers::checkAllBufferSaveFinished()
{
    QMutexLocker locker(&m_AllBufferSavedLock);

    bool allSaveFinished = true;
    for (int i = 0; i < layout(); ++i)
    {
        if (!m_Buffers[i]->saveCineFinished())
        {
            allSaveFinished = false;
            break;
        }
    }
    return allSaveFinished;
}

void SonoBuffers::onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "old:" << oldValue << "new:" << newValue
             << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime()
             << "m_SonoParameters->pBV(BFPNames::FreezeStr):" << m_SonoParameters->pBV(BFPNames::FreezeStr)
             << "PwBackup2DImage:" << ModelConfig::instance().value(ModelConfig::PwBackup2DImage, false).toBool();
#endif
    if (m_SonoParameters->isRealTime())
    {
        activeBuffer()->updateSystemScanModeChanging(true);
    }
    if (ModelConfig::instance().value(ModelConfig::PwBackup2DImage, false).toBool() && m_SonoParameters->isRealTime() &&
        !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        if (SystemScanModeClassifier::isLikeM(newValue.toInt()))
        {
            activeBuffer()->setEnableLineImageBackUp(false);
        }
        else
        {
            activeBuffer()->setEnableLineImageBackUp(true);
        }
        if (!SystemScanModeClassifier::isLikeD(oldValue.toInt()) && SystemScanModeClassifier::isLikeD(newValue.toInt()))
        {
            IBufferReadLocker locker(this, PRETTY_FUNCTION);
            activeBuffer()->backupLastLineImageData();
            activeBuffer()->setUseOldBackUpData(0, true);
        }
    }
}

void SonoBuffers::onSystemScanModeChanged(const QVariant& value)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "old:" << m_SystemScanMode << "new:" << value.toInt()
             << "m_SonoParameters->isRealTime():" << m_SonoParameters->isRealTime()
             << "FreezeStr:" << m_SonoParameters->pBV(BFPNames::FreezeStr);
#endif
    m_SystemScanMode = value.toInt();
    if (m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        if (m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr))
        {
            at(m_ActiveIndex)->setScpdValue(m_SonoParameters->pIV(BFPNames::ScpdTrapeStr));
        }
        else if (SystemScanModeClassifier::isWithOutCompound(m_SystemScanMode))
        {
            at(m_ActiveIndex)->setScpdValue(0);
        }
        else
        {
            at(m_ActiveIndex)->setScpdValue(m_SonoParameters->pIV(BFPNames::ScpdStr));
        }
        removeAll(m_ActiveIndex);
    }
    if (m_SonoParameters->isRealTime())
    {
        activeBuffer()->updateSystemScanModeChanging(false);
    }
#ifdef USE_4D
    switchFourdBufferPreProcess();
#endif
}
#ifdef USE_4D
void SonoBuffers::switchFourdBufferPreProcess()
{
    if (!m_SonoParameters->pBV(BFPNames::FreezeStr) && m_SystemScanMode == SystemScanModeFourDLive)
    {
        activeBuffer()->dataBuffer()->startFourDBufferProcess(
            activeBuffer()->sonoParameters()->pIV(BFPNames::FourDSliceNumStr));
    }
    else
    {
        activeBuffer()->dataBuffer()->stopFourDBufferProcess();
    }
}
bool SonoBuffers::getFourdVolumeArgByIndex(int index, FourDVolumeLineImageArgs& arg)
{
    if (activeBuffer() != NULL)
    {
        int indexReal = index;
        if (index == -1)
        {
            indexReal = activeBuffer()->currentIndex();
        }
        QList<BufferUnit> bufferUnits;
        FrameUnitInfo frameUnitInfo;
        while (activeBuffer()->getBufferByIndex(bufferUnits, frameUnitInfo, 0, indexReal) == -1)
        {
            Util::usleep(1);
        }
        if (bufferUnits.count() > 0)
        {
            arg.setInfos(bufferUnits, frameUnitInfo, indexReal);
            return true;
        }
    }
    return false;
}
void SonoBuffers::startFourdBufferProcess(quint32 sliceNum)
{
    activeBuffer()->dataBuffer()->startFourDBufferProcess(sliceNum);
}
void SonoBuffers::stopFourdBufferProcess()
{
    activeBuffer()->dataBuffer()->stopFourDBufferProcess();
}
#endif

void SonoBuffers::onClearActiveBuffer()
{
    activeBuffer()->clearProcessLineImageBuffer();
}

void SonoBuffers::onSetPreset()
{
    QVariant activeB = 0;
    onBeforeActiveBChanged(0, activeB);
    activeBuffer()->enableIsSetPreset();
}

int SonoBuffers::getFrameIndexByFrameTimestamp(const qint64& timestamp, int layoutIndex, bool needRealIndex)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->getFrameIndexByFrameTimestamp(timestamp, needRealIndex);
    }
    return activeBuffer()->getFrameIndexByFrameTimestamp(timestamp, needRealIndex);
}

qint64 SonoBuffers::getFramestampByIndex(int frameIndex, int layoutIndex, bool needRealIndex)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        return at(layoutIndex)->getFramestampByIndex(frameIndex, needRealIndex);
    }
    return activeBuffer()->getFramestampByIndex(frameIndex, needRealIndex);
}

int SonoBuffers::whloeFrameIndex(const int type, const int typeIndex)
{
    return activeBuffer()->whloeFrameIndex(type, typeIndex);
}

void SonoBuffers::drawUnActiveRenderPartitionImages(int layoutIndex)
{
    if (isLayoutIndexValid(layoutIndex))
    {
        // isLayoutIndexValid 仅仅用做判断在允许的布局数区间，不做实际的布局数越界检查
        if (layoutIndex < m_Layout)
        {
            at(layoutIndex)->drawUnActiveRenderPartitionImages();
        }
    }
    else
    {
        activeBuffer()->drawUnActiveRenderPartitionImages();
    }
}

bool SonoBuffers::existUnActiveRender(int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        // isLayoutIndexValid 仅仅用做判断在允许的布局数区间，不做实际的布局数越界检查
        return layoutIndex < m_Layout ? at(layoutIndex)->existUnActiveRender() : false;
    }
    return activeBuffer()->existUnActiveRender();
}

QByteArray SonoBuffers::getWholeFrameData(int frameType, const int index)
{
    return activeBuffer()->getWholeFrameData(frameType, index);
}

void SonoBuffers::setActiveBufferPictureImage(QImage image)
{
    activeBuffer()->setPictureImage(image);
}

QImage SonoBuffers::activeBufferPictureImage()
{
    return activeBuffer()->pictureImage();
}

//上下同步切换
void SonoBuffers::onFreqSpectrumChanged(const QVariant& value)
{
    IBufferWriteLocker locker(this, PRETTY_FUNCTION);
    SonoBuffer* sonoBuffer = activeBuffer();
    IBufferReadLocker lockerBuffer(sonoBuffer, PRETTY_FUNCTION);

    sonoBuffer->dataSetter()->updateFreqSpectrum(value.toBool());
}
//三同步切换
void SonoBuffers::onTriplexModeChanged(const QVariant& value)
{
    IBufferWriteLocker locker(this, PRETTY_FUNCTION);
    SonoBuffer* sonoBuffer = activeBuffer();
    IBufferReadLocker lockerBuffer(sonoBuffer, PRETTY_FUNCTION);

    sonoBuffer->dataSetter()->updateTriplexMode(value.toBool());
}
//四同步切换
void SonoBuffers::onQuadplexModeStrChanged(const QVariant& value)
{
    IBufferWriteLocker locker(this, PRETTY_FUNCTION);
    SonoBuffer* sonoBuffer = activeBuffer();
    IBufferReadLocker lockerBuffer(sonoBuffer, PRETTY_FUNCTION);

    sonoBuffer->dataSetter()->updateQuadplexMode(value.toBool());
}

void SonoBuffers::onSaveCineStateChanged(int state, bool freezeSave)
{
    SonoBuffer* buffer = qobject_cast<SonoBuffer*>(sender());

    // onSaveCineStateChanged,如果信号连接处使用了Direction，实时存图就会出现晚到的ActiveBuffer为空
    if (buffer != NULL)
    {
        buffer->setSaveCineFinished(true);
    }
    if (checkAllBufferSaveFinished())
    {
        if (!freezeSave)
        {
            emit realTimeSaveCineState(state);
        }
    }
}

void SonoBuffers::onPWSoundDelayTimeChanged(const QVariant& value)
{
    removeAll();
}

void SonoBuffers::onPWWaveImageDelayTimeChanged(const QVariant& value)
{
    removeAll();
}

void SonoBuffers::onPW2DImageDelayTimeChanged(const QVariant& value)
{
    removeAll();
}

// void SonoBuffers::onPWECGDelayTimeChanged(const QVariant &value)
//{
//    removeAll();
//}

void SonoBuffers::onMWaveImageDelayTimeChanged(const QVariant& value)
{
    removeAll();
}

void SonoBuffers::onM2DImageDelayTimeChanged(const QVariant& value)
{
    removeAll();
}

// void SonoBuffers::onMECGDelayTimeChanged(const QVariant &value)
//{
//    removeAll();
//}

// void SonoBuffers::onECGDelayTimeChanged(const QVariant &value)
//{
//    removeAll();
//}

// void SonoBuffers::onECG2DImageDelayTimeChanged(const QVariant &value)
//{
//    removeAll();
//}

void SonoBuffers::onNotifySavingProgess(quint64 byteSize)
{
    if (m_FileByteSize != 0)
    {
        m_FinishedByteSize += byteSize;
        emit notifySavingProgess((qreal)(m_FinishedByteSize / (double)m_FileByteSize));
    }
}

void SonoBuffers::onNotifyLoadingProgess(quint64 byteSize)
{
    if (m_FileByteSize != 0)
    {
        m_FinishedByteSize += byteSize;
        emit notifyLoadingProgess((qreal)(m_FinishedByteSize / (double)m_FileByteSize));
    }
}

int SonoBuffers::memNum(int layout) const
{
    // 1/2:2blocks 3/4:4blocks
    return m_IsSupportLayout ? (((layout - 1) / 2 + 1) * 2) : 1;
}
void SonoBuffers::update()
{
    if (activeBuffer() != NULL)
    {
        emit startIndexChanged(activeBuffer()->startIndex(), m_ActiveIndex);
        emit currentIndexChanged(activeBuffer()->currentIndex(), m_ActiveIndex, frameCount(m_ActiveIndex));
        emit endIndexChanged(activeBuffer()->endIndex(), m_ActiveIndex);
    }
}

// void SonoBuffers::updateCurrentIndex()
//{
//    if(activeBuffer() != NULL)
//    {
//        if(!sonoParameters()->pBV(BFPNames::FreezeStr) && sonoParameters()->isRealTime())
//        {
//            emit endIndexChanged(endIndex(), m_ActiveIndex);
//        }
//        emit currentIndexChanged(currentIndex(), m_ActiveIndex, frameCount(m_ActiveIndex));
//    }
//}

void SonoBuffers::backUpWave()
{
    SonoBuffer* buffer = activeBuffer();
    if (nullptr != buffer)
    {
        buffer->backupWave();
    }
}

void SonoBuffers::clearImageBufferDataOnOncePushLimit(int imagebufferIndex, int layoutIndex)
{
    if (!isLayoutIndexValid(layoutIndex))
    {
        layoutIndex = m_ActiveIndex;
    }
    //限制每次推送数据量时，标记已推送下次上数据就会清空缓存
    m_Buffers[layoutIndex]->markDataPushOnce(imagebufferIndex);
}
