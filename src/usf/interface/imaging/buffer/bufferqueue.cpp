#include "bufferqueue.h"
#include "assertlog.h"
#include <QDebug>

BufferQueue::BufferQueue()
    : m_Memory(NULL)
    , m_Front(0)
    , m_Rear(0)
    , m_Size(0)
    , m_FixedSize(0)
    , m_EnqueueFlag(false)
{
}

BufferQueue::BufferQueue(uchar* mem, const QList<int>& elementSizes, const int size)
    : m_Memory(NULL)
    , m_Front(0)
    , m_Rear(0)
    , m_Size(0)
    , m_FixedSize(0)
    , m_EnqueueFlag(false)
{
    construct(mem, elementSizes, size);
}

BufferQueue::~BufferQueue()
{
}

void BufferQueue::debugInfo() const
{
    qDebug() << PRETTY_FUNCTION << "m_EnqueueFlag:" << m_EnqueueFlag << "m_ElementSizes:" << m_ElementSizes
             << "m_Size:" << m_Size << "m_FixedSize:" << m_FixedSize << "m_Front:" << m_Front << "m_Rear:" << m_Rear;
}

void BufferQueue::construct(uchar* mem, const QList<int>& elementSizes, const int size)
{
    ASSERT_X_LOG(size > 0, "BufferQueue::construct()", "size must > 0");
    if (m_Memory != mem || m_ElementSizes != elementSizes || m_Size != size)
    {
        m_Memory = mem;
        m_ElementSizes = elementSizes;
        m_Size = size;
        m_FixedSize = m_Size;
        m_Front = 0;
        m_Rear = 0;
        m_EnqueueFlag = false;
        m_Queue = QVector<BufferUnit>(m_Size);

        int itemSize = elementSize();
        int elementCount = m_ElementSizes.count();

        for (int i = 0; i < m_FixedSize; i++)
        {
            BufferUnit unit(elementCount);

            int eleSizes = 0;
            for (int j = 0; j < elementCount; j++)
            {
                int eleSize = m_ElementSizes.at(j);
                unit[j] = ByteBuffer(mem + i * itemSize + eleSizes, eleSize);
                eleSizes += eleSize;
            }
            m_Queue[i] = unit;
        }
    }
}

const QList<int>& BufferQueue::elementSizes() const
{
    return m_ElementSizes;
}

int BufferQueue::elementSize() const
{
    int size = 0;
    foreach (int item, m_ElementSizes)
    {
        size += item;
    }

    return size;
}

int BufferQueue::memorySize() const
{
    return m_FixedSize * elementSize();
}

int BufferQueue::fixedSize() const
{
    return m_FixedSize;
}

int BufferQueue::usedSize() const
{
    return m_Size;
}

void BufferQueue::setUsedSize(int value)
{
    if (value <= 0 || value > fixedSize())
    {
        value = fixedSize();
    }

    m_Size = value;
}

int BufferQueue::length() const
{
    //防止除0错误
    if (m_Size == 0)
    {
        return 0;
    }

    return isFull() ? m_Size : ((m_Rear - m_Front + m_Size) % m_Size);
}

BufferUnit BufferQueue::front() const
{
    if (isEmpty())
    {
        return BufferUnit();
    }
    return m_Queue[m_Front];
}

BufferUnit BufferQueue::rear() const
{
    if (isEmpty())
    {
        return BufferUnit();
    }

    return m_Queue[(m_Rear - 1 + m_Size) % m_Size];
}

BufferUnit* BufferQueue::getRear()
{
    if (isEmpty())
    {
        return NULL;
    }

    return &(m_Queue[(m_Rear - 1 + m_Size) % m_Size]);
}

BufferUnit BufferQueue::at(int index) const
{
    if (index < 0 || index >= length())
    {
        return BufferUnit();
    }

    return m_Queue[(m_Front + index) % m_Size];
}

const BufferUnit* BufferQueue::getByIndex(int index) const
{
    if (index < 0 || index >= length())
    {
        return NULL;
    }

    return &(m_Queue[(m_Front + index) % m_Size]);
}

BufferUnit* BufferQueue::getByIndex(int index)
{
    if (index < 0 || index >= length())
    {
        return NULL;
    }

    return &(m_Queue[(m_Front + index) % m_Size]);
}

const BufferUnit* BufferQueue::getByRealIndex(int index) const
{
    if (index < 0 || index >= m_Size)
    {
        return NULL;
    }

    //此处注意，需要按照设计原则返回有效数据
    bool isValidIndex = isFull() || (m_Front <= m_Rear && (index >= m_Front && index <= m_Rear)) ||
                        (m_Front > m_Rear && !(index > m_Front && index < m_Rear));

    if (isValidIndex)
    {
        return &(m_Queue[index % m_Size]);
    }
    else
    {
        return NULL;
    }
}

BufferUnit* BufferQueue::getByRealIndex(int index)
{
    if (index < 0 || index >= m_Size)
    {
        return NULL;
    }

    //此处注意，需要按照设计原则返回有效数据
    bool isValidIndex = isFull() || (m_Front <= m_Rear && (index >= m_Front && index <= m_Rear)) ||
                        (m_Front > m_Rear && !(index > m_Front && index < m_Rear));

    if (isValidIndex)
    {
        return &(m_Queue[index % m_Size]);
    }
    else
    {
        return NULL;
    }
}

BufferUnit BufferQueue::operator[](int index) const
{
    return at(index);
}

QList<BufferUnit> BufferQueue::getElements(int index, int n) const
{
    if (index < 0 || index >= length())
    {
        return QList<BufferUnit>();
    }

    if (n > length())
    {
        n = length();
    }

    QList<BufferUnit> elements;
    for (int i = 0; i < n; i++)
    {
        elements.append(m_Queue[(m_Front + index + i) % m_Size]);
    }
    return elements;
}

bool BufferQueue::isEmpty() const
{
    return (m_Front == m_Rear) && !m_EnqueueFlag;
}

bool BufferQueue::isFull() const
{
    return (m_Rear == m_Front) && m_EnqueueFlag;
}

bool BufferQueue::isNull() const
{
    return m_Queue.isEmpty();
}

bool BufferQueue::isValid() const
{
    return !m_ElementSizes.isEmpty();
}

BufferUnit& BufferQueue::nextRear(bool enqueue)
{
    if (enqueue)
    {
        //此时可能 full，要把increaseFront increaseActiveFront 放在这里做
        //否则如果full,冻结时获取的第一帧可能被写入部分线数据
        if (isFull())
        {
            increaseFront();
        }
    }

    ASSERT_X_LOG(!m_Queue.isEmpty(), "BufferQueue::nextRear()", "m_Queue must not be empty");

    return m_Queue[m_Rear];
}

void BufferQueue::enqueue()
{
    m_Rear = (m_Rear + 1) % m_Size;
    m_EnqueueFlag = true;
}

void BufferQueue::enqueue(const BufferUnit& element)
{
    if (element.isEmpty())
    {
        return;
    }

    ASSERT_X_LOG(m_Queue[m_Rear].count() == element.count(), "BufferQueue::enqueue", "element is not valid");

    if (isFull())
    {
        increaseFront();
    }

    for (int i = 0; i < element.count(); i++)
    {
        if (!element[i].isNull())
        {
            m_Queue[m_Rear][i].copyFrom(element[i]);
        }
        // TODO:else {}
    }

    m_Queue[m_Rear].setStartTime(element.startTime());
    m_Queue[m_Rear].setDuration(element.duration());

    m_Rear = (m_Rear + 1) % m_Size;
    m_EnqueueFlag = true;
}

bool BufferQueue::dequeueRear()
{
    if (isEmpty())
    {
        return false;
    }
    m_Rear = (m_Rear + m_Size - 1) % m_Size;
    return true;
}

void BufferQueue::clear()
{
    m_Front = 0;
    m_Rear = 0;
    m_EnqueueFlag = false;
}

int BufferQueue::frontValue() const
{
    return m_Front;
}

int BufferQueue::rearValue() const
{
    return m_Rear;
}

void BufferQueue::increaseFront()
{
    if (!isEmpty())
    {
        m_EnqueueFlag = false;
        m_Front = (m_Front + 1) % m_Size;
    }
}
