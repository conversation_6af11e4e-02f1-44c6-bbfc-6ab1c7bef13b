#include "fileframememory.h"
#include "assertlog.h"
#include "fileframeinfomapper.h"
#include "fileframemapper.h"
#include "memoryleakcheck.h"
#include <QDebug>
#include <QDir>
#include <QFileInfo>

namespace FrameFile
{

FileFrameMemory::FileFrameMemory(const int index, const QString& dirPath)
    : m_FileFrameInfoMapper(NULL)
    , m_IsReadOnly(true)
    , m_ErrorCode(LoadErrorCode::Success)
    , m_TotalFrameCount(0)
    , m_NewFrameType(FrameFileType_None)
    , m_OldNewFrameType(FrameFileType_None)
    , m_2DNewFrameType(FrameFileType_None)
    , m_UpdatedGrayNewFrameType(false)
    , m_DirPath(dirPath)
    , m_ScpdValue(0)
    , m_SaveForward(false)
    , m_ImageIndex(index)
    , m_IsWirteEnable(true)
    , m_BackupUnActiveImage(false)
    , m_BackupImageType(-1)
    , m_UnActiveRestoreIndex(0)
{
    QString fileMapInfoPath = m_DirPath + QString("/%1").arg(FILEMAPINFO_FILE_NAME);
    QString indexFilePath = m_DirPath + QString("/%1").arg(WHOLEFRAME_INDEX_NAME);
    m_FileFrameInfoMapper = new FileFrameInfoMapper(m_ImageIndex, fileMapInfoPath, indexFilePath);
    CHECK_NEW(FileFrameInfoMapper, m_FileFrameInfoMapper);
    m_FileFrameInfoMapper->setIsWirteEnable(m_IsWirteEnable);
    if (m_FileFrameInfoMapper->isValid() == LoadErrorCode::Success)
    {
        m_TotalFrameCount = m_FileFrameInfoMapper->frameCount();
        QList<FrameFileHeadInfo> frameFileHeadInfos = m_FileFrameInfoMapper->frameFileHeadInfos();
        foreach (const FrameFileHeadInfo& frameFileHeadInfo, frameFileHeadInfos)
        {
            QString filePath = m_DirPath + "/" + QString(IMAGECONTEXT_FILE_NAME).arg(frameFileHeadInfo.frameType);
            QFileInfo fileInfo(filePath);
            filePath = QDir::toNativeSeparators(filePath);
            QDir dir;
            if (!dir.exists(fileInfo.absolutePath()))
            {
                dir.mkpath(fileInfo.absolutePath());
            }
            FileFrameMapper* fileFrameMapper =
                new FileFrameMapper(m_ImageIndex, frameFileHeadInfo.frameFileBeginIndex, filePath, frameFileHeadInfo);
            CHECK_NEW(FileFrameMapper, fileFrameMapper);
            if (fileFrameMapper && fileFrameMapper->isValid() == LoadErrorCode::Success)
            {
                m_FileFrameMappers.insert(frameFileHeadInfo.frameType, fileFrameMapper);
            }
            else
            {
                m_ErrorCode = fileFrameMapper->isValid();
                return;
            }
        }
    }
    else
    {
        m_ErrorCode = m_FileFrameInfoMapper->isValid();
    }
    m_OldNewFrameType = FrameFileType_None;
    m_2DNewFrameType = FrameFileType_None;
    m_UpdatedGrayNewFrameType = false;
    m_FrameTypes = m_FileFrameMappers.keys();
    m_NewFrameType = calcNewFrameType(m_FrameTypes);

#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "Create m_ImageIndex:" << m_ImageIndex << " @" << Qt::hex << (quintptr)this;
#endif
}

FileFrameMemory::FileFrameMemory(const int index, const QString& dirPath, const int totalFrameCount)
    : m_FileFrameInfoMapper(NULL)
    , m_IsReadOnly(false)
    , m_ErrorCode(LoadErrorCode::Success)
    , m_TotalFrameCount(totalFrameCount)
    , m_NewFrameType(FrameFileType_None)
    , m_OldNewFrameType(FrameFileType_None)
    , m_2DNewFrameType(FrameFileType_None)
    , m_UpdatedGrayNewFrameType(false)
    , m_DirPath(dirPath)
    , m_ScpdValue(0)
    , m_SaveForward(false)
    , m_ImageIndex(index)
    , m_IsWirteEnable(false)
    , m_BackupUnActiveImage(false)
    , m_BackupImageType(-1)
    , m_UnActiveRestoreIndex(0)
{
    //    QString fileMapInfoPath = m_DirPath + QString("/%1").arg(FILEMAPINFO_FILE_NAME);
    FrameFileHeadInfo frameFileHeadInfo = defaultFrameFileHeadInfo(FrameFileType_Info);
    frameFileHeadInfo.mapFrameTotalCount = m_TotalFrameCount;
    m_FileFrameInfoMapper = new FileFrameInfoMapper(m_ImageIndex, frameFileHeadInfo);
    CHECK_NEW(FileFrameInfoMapper, m_FileFrameInfoMapper);
    m_FileFrameInfoMapper->setIsWirteEnable(m_IsWirteEnable);
    m_FrameFileUnit = defalutFrameFileUnit();

#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "Create m_ImageIndex:" << m_ImageIndex << " @" << Qt::hex << (quintptr)this;
#endif
}

FileFrameMemory::~FileFrameMemory()
{
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "Destory m_ImageIndex:" << m_ImageIndex << " @" << Qt::hex << (quintptr)this;
#endif
    if (m_FileFrameInfoMapper != NULL)
    {
        CHECK_DELETE(FileFrameInfoMapper, m_FileFrameInfoMapper);
        delete m_FileFrameInfoMapper;
    }
    CHECK_DELETE_LIST(FileFrameMapper, m_FileFrameMappers.values());
    qDeleteAll(m_FileFrameMappers.values());
    m_FileFrameMappers.clear();
    m_FrameTypes.clear();
}

void FileFrameMemory::setIsRealTimeSave(bool bRealTime)
{
    m_FileFrameInfoMapper->setRealTimeSaveState(bRealTime);
}

int FileFrameMemory::getCombineFrameSteerOnBit(const FrameFileUnitInfo& info, int frameIndex)
{
    return m_FileFrameInfoMapper->getCombineFrameSteerOnBit(info, frameIndex);
}

LoadErrorCode FileFrameMemory::isValid() const
{
    return m_ErrorCode;
}

void FileFrameMemory::clearForNew()
{
    if (m_IsReadOnly)
    {
        return;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION;
#endif
    clear();
    // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
    QMutexLocker lck(&m_FrameMutex);
    m_FileFrameInfoMapper->clearForNew();
    CHECK_DELETE_LIST(FileFrameMapper, m_FileFrameMappers.values());
    qDeleteAll(m_FileFrameMappers.values());
    m_FileFrameMappers.clear();
    m_FrameTypes.clear();
    m_NewFrameFlag.clear();
    m_FrameFileUnit.frameUnits.clear();
}

void FileFrameMemory::clear(bool clearUnActive)
{
    if (m_IsReadOnly)
    {
        return;
    }
    // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
    QMutexLocker lck(&m_FrameMutex);
    foreach (const int key, m_FileFrameMappers.keys())
    {
        m_FileFrameMappers[key]->clear(clearUnActive);
        if (clearUnActive || m_FileFrameMappers[key]->isActive())
        {
            m_FrameFileUnit.frameUnits[key].frameSteer = 0;
            m_FrameFileUnit.frameUnits[key].startFrameIndex = 0;
            m_FrameFileUnit.frameUnits[key].endFrameIndex = 0;
        }
    }
    m_FileFrameInfoMapper->clear(clearUnActive);
    m_FrameFileBeginIndexs.clear();
    m_FrameFileQueueFull.clear();
    m_UnActiveRestoreIndex = 0;

    if (clearUnActive)
    {
        m_UnActiveFrameTypes.clear();
        m_UnActiveFrameUnits.clear();
        m_BackupImageType = -1;
        m_FrameFileUnit.frameIndex = 0;
        m_FrameFileUnit.timeDelayMS = 0;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "clearUnActive:" << clearUnActive;
    foreach (const int key, m_FrameFileUnit.frameUnits.keys())
    {
        qDebug() << PRETTY_FUNCTION << "frameType:" << m_FrameFileUnit.frameUnits[key].frameType
                 << "startFrameIndex:" << m_FrameFileUnit.frameUnits[key].startFrameIndex
                 << "endFrameIndex:" << m_FrameFileUnit.frameUnits[key].endFrameIndex;
    }
#endif
}

bool FileFrameMemory::isNull() const
{
    return m_FileFrameInfoMapper->isNull();
}

void FileFrameMemory::setActive(const int frameType, const bool active)
{
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "frameType:" << frameType << "active:" << active;
#endif
    m_FileFrameInfoMapper->setActive(frameType, active);
    if (m_FileFrameMappers.contains(frameType))
    {
        m_FileFrameMappers[frameType]->setActive(active);
    }
}

bool FileFrameMemory::isActive(const int frameType) const
{
    if (m_FileFrameMappers.contains(frameType))
    {
        return m_FileFrameMappers[frameType]->isActive();
    }
    return false;
}

void FileFrameMemory::debugInfo() const
{
    if (m_FileFrameInfoMapper != NULL)
    {
        m_FileFrameInfoMapper->debugInfo();
    }
}

void FileFrameMemory::setBackupUnActiveImage(const bool backup, const int backupImageType,
                                             const QList<int>& backupFrameTypes)
{
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_BackupUnActiveImage:" << m_BackupUnActiveImage
             << "m_BackupImageType:" << m_BackupImageType << "backup:" << backup
             << "backupImageType:" << backupImageType;
#endif
    if (!backup)
    {
        m_UnActiveRestoreIndex = 0;
        m_UnActiveFrameTypes.append(backupFrameTypes);
    }
    else
    {
        //在需要备份之前，需要同步清理缓存，依靠异步情况缓存会导致变清理边备份的问题
        clear();
    }
    if (backup && (m_BackupImageType != backupImageType))
    {
        m_UnActiveFrameTypes.clear();
        m_UnActiveFrameUnits.clear();
    }
    m_BackupUnActiveImage = backup;
    m_BackupImageType = backupImageType;
}

bool FileFrameMemory::backupUnActiveImage() const
{
    return m_BackupUnActiveImage;
}

int FileFrameMemory::backupImageType() const
{
    return m_BackupImageType;
}

void FileFrameMemory::clearFrameFileUnit(const int frameType, const bool clear)
{
    if (m_IsReadOnly)
    {
        return;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << " m_ImageIndex:" << m_ImageIndex << " FrameType:" << frameType
             << " m_FileFrameMappers.keys:" << m_FileFrameMappers.keys()
             << " m_FrameFileUnit.frameUnits.keys:" << m_FrameFileUnit.frameUnits.keys() << " clear:" << clear;
#endif

    if (m_FileFrameMappers.contains(frameType))
    {
        if (clear)
        {
            FileFrameMapper* mapper = m_FileFrameMappers.value(frameType);
            mapper->clear();
        }
    }
    if (m_FrameFileUnit.frameUnits.contains(frameType))
    {
        if (clear)
        {
            m_FrameFileUnit.frameUnits.insert(frameType, defalutFrameFileUnitInfo(frameType));
        }
    }
}

void FileFrameMemory::setScpdValue(const int scpdValue)
{
    m_ScpdValue = scpdValue;
    m_FileFrameInfoMapper->setScpdValue(scpdValue);
    foreach (FileFrameMapper* fileFrameMapper, m_FileFrameMappers.values())
    {
        fileFrameMapper->setScpdValue(scpdValue);
    }
}

int FileFrameMemory::scpdValue() const
{
    return m_ScpdValue;
}

// pw上激活模式下,在未满帧的情况,第0帧和第1帧所包含的B数据的索引都是0,此处在计算B数据索引时需要减一
//但是如果已经满帧,则不存在这样的问题
int FileFrameMemory::extralScpdValue(const int frameType) const
{
    FrameFileUnit frameFUZero = frameFileUnit(0);
    if (frameFUZero.frameUnits.contains(frameType))
    {
        const FrameFileUnitInfo& frameFUInfoZero = frameFUZero.frameUnits[frameType];
        const FrameFileUnitInfo& frameFUInfoOne = frameFileUnitInfo(frameType, 1);
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << "frameType:" << frameType
                 << "frameFUInfoOne.startFrameIndex:" << frameFUInfoOne.startFrameIndex
                 << "frameFUInfoOne.endFrameIndex:" << frameFUInfoOne.endFrameIndex
                 << "frameFUInfoZero.startFrameIndex:" << frameFUInfoZero.startFrameIndex
                 << "frameFUInfoZero.endFrameIndex:" << frameFUInfoZero.endFrameIndex;
#endif
        if ((frameFUInfoOne.startFrameIndex == 0) && (frameFUInfoOne.endFrameIndex == 0) &&
            (frameFUInfoZero.startFrameIndex == 0) && (frameFUInfoZero.endFrameIndex == 0))
        {
            return 1;
        }
    }
    return 0;
}

void FileFrameMemory::setSaveForward(bool forward)
{
    m_SaveForward = forward;
    // m_FileFrameInfoMapper->setScpdValue(forward);
    //这里的m_SaveForward暂时没用到，不修改这个参数
    foreach (FileFrameMapper* fileFrameMapper, m_FileFrameMappers.values())
    {
        fileFrameMapper->setSaveForward(forward);
    }
}

bool FileFrameMemory::saveForward() const
{
    return m_SaveForward;
}

bool FileFrameMemory::newFrameType(const int newType)
{
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_ImageIndex:" << m_ImageIndex << "newType:" << newType
             << "m_NewFrameType:" << m_NewFrameType << "m_OldNewFrameType:" << m_OldNewFrameType
             << "m_FrameTypes:" << m_FrameTypes << "frameCount():" << frameCount();
#endif
    //    if(newType != m_NewFrameType)
    //    {
    //        return false;
    //    }

    if (frameCount() > m_ScpdValue || m_SaveForward)
    {
        if ((m_NewFrameType != m_2DNewFrameType) && (m_OldNewFrameType == m_2DNewFrameType) &&
            (newType == m_2DNewFrameType))
        {
            m_UpdatedGrayNewFrameType = true;
            m_OldNewFrameType = newType;
            //            qDebug() << PRETTY_FUNCTION
            //                     << "1"
            //                     << "m_OldNewFrameType:" << m_OldNewFrameType
            //                     << "m_NewFrameType:" << m_NewFrameType
            //                     << "m_UpdatedGrayNewFrameType:" << m_UpdatedGrayNewFrameType
            //                     << "newType:" << newType;
            return true;
        }
        else if (m_UpdatedGrayNewFrameType && (newType == m_NewFrameType))
        {
            m_UpdatedGrayNewFrameType = false;
            //            qDebug() << PRETTY_FUNCTION
            //                     << "2"
            //                     << "m_OldNewFrameType:" << m_OldNewFrameType
            //                     << "m_NewFrameType:" << m_NewFrameType
            //                     << "m_UpdatedGrayNewFrameType:" << m_UpdatedGrayNewFrameType
            //                     << "newType:" << newType;
            return false;
        }
        //        qDebug() << PRETTY_FUNCTION
        //                 << "3"
        //                 << "m_OldNewFrameType:" << m_OldNewFrameType
        //                 << "m_NewFrameType:" << m_NewFrameType
        //                 << "m_UpdatedGrayNewFrameType:" << m_UpdatedGrayNewFrameType
        //                 << "newType:" << newType;
        m_OldNewFrameType = newType;
        return newType == m_NewFrameType;
    }
    else
    {
        foreach (const struct FrameFileHeadInfo& fileHeadInfo, m_FileFrameInfoMapper->frameFileHeadInfos())
        {
            if (fileHeadInfo.frameUnitCount == 1)
            {
                return true;
            }
        }
    }
    return true;
}

int FileFrameMemory::newFrameType() const
{
    return m_NewFrameType;
}

void FileFrameMemory::updateNewFrameType(const int type, const bool add)
{
    if (add)
    {
        if (!m_FrameTypes.contains(type))
        {
            m_FrameTypes.append(type);
        }
    }
    else
    {
        if (m_FrameTypes.contains(type))
        {
            m_FrameTypes.removeAll(type);
        }
    }
    m_NewFrameType = calcNewFrameType(m_FrameTypes);
    m_2DNewFrameType = calc2DNewFrameType(m_FrameTypes);
}

void FileFrameMemory::changeNewFrameType()
{
    QList<int> frameTypes;
    foreach (const int& frameType, m_FrameTypes)
    {
        if (m_FileFrameMappers.contains(frameType))
        {
            if (m_FileFrameMappers[frameType]->isActive())
            {
                frameTypes.append(frameType);
            }
        }
    }
    m_NewFrameType = calcNewFrameType(frameTypes);
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_NewFrameType:" << m_NewFrameType << "frameTypes:" << frameTypes;
#endif
}

int FileFrameMemory::startIndex(const int type) const
{
    return m_FileFrameInfoMapper->startIndex(type);
}

int FileFrameMemory::currentIndex(const int type) const
{
    return m_FileFrameInfoMapper->currentIndex(type);
}

int FileFrameMemory::endIndex(const int type) const
{
    return m_FileFrameInfoMapper->endIndex(type);
}

int FileFrameMemory::startFrameOffset(const int type) const
{
    return m_FileFrameInfoMapper->startFrameOffset(type);
}

int FileFrameMemory::startFrameOffset(const int type, const int startIndex) const
{
    return m_FileFrameInfoMapper->startFrameOffset(type, startIndex);
}

int FileFrameMemory::frontIndex(const int type) const
{
    if (m_FileFrameMappers.contains(type))
    {
        return m_FileFrameMappers[type]->frontIndex();
    }
    return 0;
}

int FileFrameMemory::whloeFrameIndex(const int type, const int typeIndex)
{
    return m_FileFrameInfoMapper->frameIndex(type, typeIndex);
}

int FileFrameMemory::frameCount() const
{
    return m_FileFrameInfoMapper->frameCount();
}

int FileFrameMemory::frameCount(const int type) const
{
    // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
    QMutexLocker lck(&m_FrameMutex);
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_ImageIndex:" << m_ImageIndex << "type:" << type << "m_IsReadOnly:" << m_IsReadOnly
             << "isActive(type):" << isActive(type) << "frameCount:" << frameCount()
             << "m_FrameFileUnit.frameUnits.keys:" << m_FrameFileUnit.frameUnits.keys()
             << "frameCount(type):" << m_FileFrameInfoMapper->frameCount(type);
#endif
    if ((!m_IsReadOnly) && (!isActive(type)) && (frameCount() <= 0) && (m_FrameFileUnit.frameUnits.contains(type)))
    {
        return m_FrameFileUnit.frameUnits[type].endFrameIndex + 1;
    }
    return m_FileFrameInfoMapper->frameCount(type);
}

void FileFrameMemory::setFrameTotalCount(int frameCount)
{
    m_TotalFrameCount = frameCount;
    if (m_FileFrameInfoMapper != NULL)
    {
        m_FileFrameInfoMapper->setTotalFrameCount(frameCount);
    }
    foreach (const int key, m_FileFrameMappers.keys())
    {
        m_FileFrameMappers[key]->setTotalFrameCount(frameCount);
    }
}
void FileFrameMemory::setFrameInfoFileHeadTotalFrameCount(int frameTotalCount, int frameType)
{
    if (m_FileFrameInfoMapper != NULL)
    {
        if (frameType == -1)
        {
            foreach (const struct FrameFileHeadInfo& info, m_FileFrameInfoMapper->frameFileHeadInfos())
            {
                m_FileFrameInfoMapper->frameFileHeadInfo(info.frameType).mapFrameTotalCount = frameTotalCount;
            }
        }
        else
        {
            m_FileFrameInfoMapper->frameFileHeadInfo(frameType).mapFrameTotalCount = frameTotalCount;
        }
    }
}
int FileFrameMemory::frameTotalCount() const
{
    return m_TotalFrameCount;
}

int FileFrameMemory::frameTotalCount(const int type) const
{
    FileFrameMapper* mapper = m_FileFrameMappers.value(type, NULL);

    return mapper == NULL ? 0 : mapper->frameTotalCount();
}

bool FileFrameMemory::appendFrameType(const int frameType, const int frameSize, const int frameUnitCount,
                                      const bool add, const int mapFrameTotalCount)
{
    if (m_IsReadOnly)
    {
        return false;
    }
    FrameFileHeadInfo frameFileHeadInfo = defaultFrameFileHeadInfo(frameType);
    frameFileHeadInfo.frameSize = frameSize;
    frameFileHeadInfo.frameUnitCount = frameUnitCount;
    frameFileHeadInfo.mapFrameTotalCount = mapFrameTotalCount;
    FileFrameMapper* fileFrameMapper = NULL;
    if (!m_FileFrameMappers.contains(frameType))
    {
        QString filePath = m_DirPath + "/" + QString(IMAGECONTEXT_FILE_NAME).arg(frameType);
        QFileInfo fileInfo(filePath);
        filePath = QDir::toNativeSeparators(filePath);
        QDir dir;
        if (!dir.exists(fileInfo.absolutePath()))
        {
            dir.mkpath(fileInfo.absolutePath());
        }
        fileFrameMapper = new FileFrameMapper(m_ImageIndex, filePath, frameFileHeadInfo);
        CHECK_NEW(FileFrameMapper, fileFrameMapper);
        fileFrameMapper->setScpdValue(m_ScpdValue);
        fileFrameMapper->setActive(add);
        m_FileFrameMappers.insert(frameType, fileFrameMapper);
        if (add)
        {
            m_FrameTypes.append(frameType);
        }
        m_NewFrameType = calcNewFrameType(m_FrameTypes);
        m_2DNewFrameType = calc2DNewFrameType(m_FrameTypes);
        struct FrameFileUnitInfo frameFileUnitInfo = defalutFrameFileUnitInfo();
        frameFileUnitInfo.frameType = frameType;

        // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
        QMutexLocker lck(&m_FrameMutex);
        m_FrameFileUnit.frameUnits.insert(frameType, frameFileUnitInfo);
        m_FileFrameInfoMapper->updateFrameFileUnit(m_FrameFileUnit);
    }
    else
    {
        fileFrameMapper = m_FileFrameMappers[frameType];
        fileFrameMapper->updateFrameFileHeadInfo(frameFileHeadInfo);
    }

    if ((fileFrameMapper != NULL) && fileFrameMapper->isValid() == LoadErrorCode::Success)
    {
        m_FileFrameInfoMapper->updateFrameFileHeadInfo(frameType, frameSize, frameUnitCount, mapFrameTotalCount);
        return true;
    }
    return false;
}

const struct FrameFileUnit FileFrameMemory::frameFileUnit(const int frameIndex) const
{
    return m_FileFrameInfoMapper->frameFileUnit(frameIndex);
}

const struct FrameFileUnitInfo FileFrameMemory::frameFileUnitInfo(const int frameType, const int frameIndex,
                                                                  bool realPos) const
{
    // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
    QMutexLocker lck(&m_FrameMutex);
    if ((frameCount() <= 0) && (m_FrameFileUnit.frameUnits.contains(frameType)))
    {
        return m_FrameFileUnit.frameUnits[frameType];
    }
    if (realPos)
    {
        return m_FileFrameInfoMapper->frameFileUnit(frameIndex, m_SaveUseFrontIndex).frameUnits[frameType];
    }
    else
    {
        return m_FileFrameInfoMapper->frameFileUnitAt(frameIndex).frameUnits[frameType];
    }
}

int FileFrameMemory::getFrameSizeByType(const int frameType) const
{
    return m_FileFrameInfoMapper->getFrameSizeByType(frameType);
}

uchar* FileFrameMemory::data(const int frameType)
{
    FileFrameMapper* mapper = m_FileFrameMappers.value(frameType, NULL);
    Q_ASSERT(mapper != NULL);
    return mapper == NULL ? NULL : mapper->rear();
}

void FileFrameMemory::updateStartAndEndFrameIndexByType(const int frameType, const int startFrameIndex,
                                                        const int endFrameIndex)
{
    // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
    QMutexLocker lck(&m_FrameMutex);
    if (m_FrameFileUnit.frameUnits.contains(frameType))
    {
        m_FrameFileUnit.frameUnits[frameType].startFrameIndex = startFrameIndex;
        m_FrameFileUnit.frameUnits[frameType].endFrameIndex = endFrameIndex;
        if (m_FileFrameMappers.contains(frameType))
        {
            m_FileFrameMappers[frameType]->setRearValue(endFrameIndex);
        }
        m_FileFrameInfoMapper->updateHeadInfoStartOffsetIndex(frameType, endFrameIndex);
    }
}

bool FileFrameMemory::nextRear()
{
    return m_FileFrameInfoMapper->nextRear();
}

int FileFrameMemory::enqueue(const int frameType, const int steer, const bool newFlag, const bool updateStart,
                             const int defaultFrameTimeDelay)
{
    Q_UNUSED(defaultFrameTimeDelay);
    if (m_IsReadOnly)
    {
        return 0;
    }
    /*记录当前更新的帧信息*/
    struct FrameFileUnitInfo& frameFileUnitInfo = m_FrameFileUnit.frameUnits[frameType];

    FileFrameMapper* mapper = m_FileFrameMappers.value(frameType, NULL);
    if (mapper != NULL)
    {
        bool newFrameFlag = m_NewFrameFlag.value(frameType, true);
        if (newFrameFlag && newFlag)
        {
            frameFileUnitInfo.startFrameIndex = mapper->frameIndex();
            frameFileUnitInfo.endFrameIndex = frameFileUnitInfo.startFrameIndex;
            m_NewFrameFlag.insert(frameType, false);
        }
        else
        {
            frameFileUnitInfo.endFrameIndex = mapper->frameIndex();
            if (updateStart)
            {
                frameFileUnitInfo.startFrameIndex = frameFileUnitInfo.endFrameIndex;
            }
        }
        const FrameFileHeadInfo& frameFileHeadInfo = m_FileFrameInfoMapper->frameFileHeadInfo(frameType);
        if ((mapper->isQueueFull()) && (frameFileHeadInfo.startFrameOffset != 0))
        {
            int newStartFrameOffset =
                frameFileHeadInfo.startFrameOffset > 0 ? frameFileHeadInfo.startFrameOffset - 1 : 0;
            m_FileFrameInfoMapper->updateHeadInfoStartOffsetIndex(frameType, newStartFrameOffset);

#ifdef ENABLE_FRAME_FILE_DEBUG
            qDebug() << PRETTY_FUNCTION << " m_ImageIndex:" << m_ImageIndex << " FrameType:" << frameType
                     << " NewStartFrameOffset:" << newStartFrameOffset << " FrameIndex:" << mapper->frameIndex()
                     << " OldStartFrameOffset:" << frameFileHeadInfo.startFrameOffset;
#endif
        }
        mapper->next();
        // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
        QMutexLocker lck(&m_FrameMutex);
        m_FrameFileBeginIndexs.insert(frameType, mapper->fileBeginIndex());
        m_FrameFileQueueFull.insert(frameType, mapper->isQueueFull());
    }
    QMutexLocker lck(&m_FrameMutex);
    m_FileFrameInfoMapper->combineFrameSteer(frameFileUnitInfo, steer);

    return m_FileFrameInfoMapper->isQueueFull() ? m_FileFrameInfoMapper->frameCount()
                                                : m_FileFrameInfoMapper->frameIndex();
}

int FileFrameMemory::dequeueRear(int frameType)
{
    if (m_IsReadOnly || m_FileFrameInfoMapper->frameCount() == 0)
    {
        return 0;
    }

    int dequeueCount = 0;
    /*记录当前更新的帧信息*/
    FileFrameMapper* mapper = m_FileFrameMappers.value(frameType, NULL);
    if (mapper != NULL)
    {
        //删除当前整帧中对应数据的帧数
        const struct FrameFileUnitInfo& frameFileUnitInfo = m_FrameFileUnit.frameUnits[frameType];
        const struct FrameFileUnitInfo& preFrameFileUnitInfo =
            m_FileFrameInfoMapper->frameFileUnitInfo(frameType, m_FileFrameInfoMapper->frameCount() - 1);
        const FrameFileHeadInfo& frameFileHeadInfo = m_FileFrameInfoMapper->frameFileHeadInfo(frameType);
        for (int i = preFrameFileUnitInfo.endFrameIndex; i < frameFileUnitInfo.endFrameIndex; i++)
        {
            if ((frameFileHeadInfo.startFrameOffset != 0) && (mapper->frontIndex() != 0))
            {
                m_FileFrameInfoMapper->updateHeadInfoStartOffsetIndex(
                    frameType, frameFileHeadInfo.startFrameOffset > 0 ? frameFileHeadInfo.startFrameOffset + 1 : 0);
            }
            mapper->pre();
            dequeueCount++;
        }
        // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
        QMutexLocker lck(&m_FrameMutex);
        m_FrameFileBeginIndexs.insert(frameType, mapper->fileBeginIndex());
        m_FrameFileQueueFull.insert(frameType, mapper->isQueueFull());
    }
    return dequeueCount;
}

int FileFrameMemory::nextFrame(const bool update)
{
    calAvgFunRunTimeIn(FileFrameMemory_nextFrame);
    /*如果是新帧，将这些信息更新到文件*/
    //    if(frameType == m_NewFrameType)
    {
//        m_FrameFileUnit.frameIndex = (m_FrameFileUnit.frameIndex + 1) % m_TotalFrameCount;
//        m_FrameFileUnit.timeDelayMS = defaultFrameTimeDelay;
#ifdef ENABLE_FRAME_FILE_DEBUG
        foreach (const FrameFileUnitInfo& ffui, m_FrameFileUnit.frameUnits)
        {
            qDebug() << PRETTY_FUNCTION << "@ 1 m_BackupUnActiveImage:" << m_BackupUnActiveImage
                     << "m_ImageIndex:" << m_ImageIndex << "m_UnActiveRestoreIndex:" << m_UnActiveRestoreIndex
                     << "m_UnActiveFrameUnits.count:" << m_UnActiveFrameUnits.count() << "frameType:" << ffui.frameType
                     << "startFrameIndex:" << ffui.startFrameIndex << "endFrameIndex:" << ffui.endFrameIndex
                     << "m_FileFrameInfoMapper->frameCount:" << m_FileFrameInfoMapper->frameCount();
        }
#endif
        if (m_BackupUnActiveImage)
        {
            m_FileFrameInfoMapper->enqueueUnActiveFrameFileUnit(m_FrameFileUnit, update);

            if (update && !m_UnActiveFrameUnits.isEmpty())
            {
                m_UnActiveFrameUnits[m_UnActiveFrameUnits.count() - 1] = m_FrameFileUnit;
            }
            else
            {
                m_UnActiveFrameUnits.append(m_FrameFileUnit);
            }
        }
        else
        {
            if (m_UnActiveRestoreIndex >= 0 && m_UnActiveRestoreIndex < m_UnActiveFrameUnits.count() &&
                m_FileFrameInfoMapper->frameIndex() < m_UnActiveFrameUnits.count() &&
                m_FileFrameInfoMapper->frameCount() < m_UnActiveFrameUnits.count())
            {
                foreach (const int frameType, m_UnActiveFrameTypes)
                {
                    if (m_FrameFileUnit.frameUnits.contains(frameType) &&
                        (m_FileFrameMappers.contains(frameType) && !m_FileFrameMappers[frameType]->isActive()))
                    {
                        m_FrameFileUnit.frameUnits[frameType] =
                            m_UnActiveFrameUnits[m_UnActiveRestoreIndex].frameUnits[frameType];
                    }
                }
                //                m_FileFrameInfoMapper->updateFrameFileUnit(m_FrameFileUnit, m_UnActiveRestoreIndex);
                m_UnActiveRestoreIndex++;
                //                return m_FileFrameInfoMapper->frameCount();
            }
        }
        // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
        QMutexLocker lck(&m_FrameMutex);
        m_FileFrameInfoMapper->enqueueFrameFileUnit(m_FrameFileUnit, m_FrameFileBeginIndexs, m_FrameFileQueueFull);
        //        for(QHash<int,struct FrameFileUnitInfo>::iterator iter = m_FrameFileUnit.frameUnits.begin();
        //            iter != m_FrameFileUnit.frameUnits.end();
        //            iter ++)
        //        {
        ////            FileFrameMapper *mapper = m_FileFrameMappers.value(iter.key(), NULL);
        ////            if(mapper != NULL)
        ////            {
        ////                iter.value().startFrameIndex = iter.value().endFrameIndex;
        ////            }
        //        }
        for (QHash<int, bool>::iterator iter = m_NewFrameFlag.begin(); iter != m_NewFrameFlag.end(); iter++)
        {
            iter.value() = true;
        }
    }
    calAvgFunRunTimeOut(FileFrameMemory_nextFrame);
    QMutexLocker lck0(&m_FrameMutex);
    return m_FileFrameInfoMapper->frameCount();
}

void FileFrameMemory::updatecurrentFrameTimestamp(const qint64& timestamp)
{
    QMutexLocker lck(&m_FrameMutex);
    m_FileFrameInfoMapper->updatecurrentFrameTimestamp(timestamp);
}

void FileFrameMemory::preFrame()
{
    // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
    QMutexLocker lck(&m_FrameMutex);
    int frameCount = m_FileFrameInfoMapper->frameCount();
    if (frameCount <= 0)
    {
        return;
    }
    int clearIndex = frameCount - 1;
    // think frameFileUnit or frameFileUnitAt
    m_FrameFileUnit = m_FileFrameInfoMapper->frameFileUnit(clearIndex);
    m_FileFrameInfoMapper->clearFrameAfterIndex(clearIndex);
}

bool FileFrameMemory::clearFrameAfterIndex(const int frameIndex)
{
    return m_FileFrameInfoMapper->clearFrameAfterIndex(frameIndex);
}

int FileFrameMemory::calcStartIndex(const int frameType, const int sIndex, const int eIndex)
{
    return m_FileFrameInfoMapper->calcStartIndex(frameType, sIndex, eIndex);
}

uchar* FileFrameMemory::data(const int frameType, const int frameIndex)
{
    if (frameIndex < 0)
    {
        qCritical() << "frameIndex < 0" << PRETTY_FUNCTION;
        return NULL;
    }
    FileFrameMapper* mapper = m_FileFrameMappers.value(frameType, NULL);
    if (nullptr == mapper)
    {
        qCritical() << "mapper is nullptr" << PRETTY_FUNCTION;
    }
    return mapper == NULL ? NULL : mapper->data(frameIndex);
}

void FileFrameMemory::unrefData(const int frameType, const int frameIndex)
{
    if (frameIndex < 0)
    {
        return;
    }
    FileFrameMapper* fileFrameMapper = m_FileFrameMappers.value(frameType, NULL);
    if (fileFrameMapper)
    {
        fileFrameMapper->unrefByIndex(frameIndex);
    }
}

bool FileFrameMemory::save(const QString& dirPath, const int startIndex, const int endIndex)
{
    QString fileMapInfoPath = dirPath + QString("/%1").arg(FILEMAPINFO_FILE_NAME);
    QString indexFilePath = dirPath + QString("/%1").arg(WHOLEFRAME_INDEX_NAME);
    m_FileFrameInfoMapper->saveToFile(fileMapInfoPath, indexFilePath, startIndex, endIndex);
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_ImageIndex:" << m_ImageIndex << "startIndex:" << startIndex
             << "endIndex:" << endIndex;
#endif
    foreach (FileFrameMapper* fileFrameMapper, m_FileFrameMappers.values())
    {
        int saveStartIndex = startIndex;
        int saveEndIndex = endIndex;
        int frameType = fileFrameMapper->frameType();
        int typeFrameCount = frameCount(frameType);
        if (frameType == -1)
        {
            continue;
        }
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << "&&&&&" << PRETTY_FUNCTION << "frameType:" << frameType << "typeFrameCount:" << typeFrameCount
                 << "saveStartIndex:" << saveStartIndex << "saveEndIndex:" << saveEndIndex;
#endif
        calcSaveIndex(saveStartIndex, saveEndIndex, frameType);
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << "saveStartIndex:" << saveStartIndex << "saveEndIndex:" << saveEndIndex;
#endif
        if ((saveStartIndex < 0) || (saveEndIndex < 0))
        {
            break;
        }
        if (frameType == ImageBufferDef::FourD_Data)
        {
            saveStartIndex =
                m_FileFrameInfoMapper->frameFileUnit(startIndex).frameUnits[ImageBufferDef::FourD_Data].startFrameIndex;
            saveEndIndex =
                m_FileFrameInfoMapper->frameFileUnit(endIndex).frameUnits[ImageBufferDef::FourD_Data].endFrameIndex;
        }
        QString fileFramePath = dirPath + "/" + QString(IMAGECONTEXT_FILE_NAME).arg(frameType);
        fileFrameMapper->saveToFile(fileFramePath, saveStartIndex, saveEndIndex, typeFrameCount);
    }
    return true;
}

bool FileFrameMemory::saveCurrent(const QString& dirPath, const int index, const int frameAvgCount, int front)
{
    QString fileMapInfoPath = dirPath + QString("/%1").arg(FILEMAPINFO_FILE_NAME);
    if (!m_FileFrameInfoMapper->saveCurrentToFile(fileMapInfoPath, index, frameAvgCount))
    {
        return false;
    }

    FrameFileUnit frameFileUnit = m_FileFrameInfoMapper->frameFileUnit(index, front);

    foreach (const FrameFileUnitInfo& frameFileUnitInfo, frameFileUnit.frameUnits)
    {
        FileFrameMapper* fileFrameMapper = m_FileFrameMappers.value(frameFileUnitInfo.frameType, NULL);
        if (fileFrameMapper != NULL)
        {
            qDebug() << PRETTY_FUNCTION << "frameType:" << frameFileUnitInfo.frameType
                     << "startFrameIndex:" << frameFileUnitInfo.startFrameIndex
                     << "endFrameIndex:" << frameFileUnitInfo.endFrameIndex;
            QString fileFramePath = dirPath + "/" + QString(IMAGECONTEXT_FILE_NAME).arg(fileFrameMapper->frameType());
            if (fileFrameMapper->frameType() == ImageBufferDef::FourD_Data)
            {
                if (!fileFrameMapper->saveToFile(fileFramePath, frameFileUnitInfo.startFrameIndex,
                                                 frameFileUnitInfo.endFrameIndex))
                {
                    return false;
                }
            }
            else
            {
                if (frameFileUnitInfo.frameType == ImageBufferDef::Sound_Data)
                {
                    continue;
                }
                const struct FrameFileHeadInfo& ffhi =
                    m_FileFrameInfoMapper->frameFileHeadInfo(frameFileUnitInfo.frameType);
                if (ffhi.frameUnitCount == 1)
                {
                    if (!fileFrameMapper->saveCurrentToFile(fileFramePath, frameFileUnitInfo.endFrameIndex,
                                                            frameAvgCount))
                    {
                        return false;
                    }
                }
                else
                {
                    int endIndex = frameFileUnitInfo.endFrameIndex;
                    int copyCount = 0;
                    int saveWidth = fileFrameMapper->frameUnitCount();
                    int offset = startFrameOffset(frameFileUnitInfo.frameType, index);
                    int startIndex = qMax(frameFileUnitInfo.startFrameIndex - offset - 1, 0);
                    int typeFrameCount = frameCount(frameFileUnitInfo.frameType);
                    copyCount = (endIndex - startIndex + typeFrameCount) % typeFrameCount + 1;
                    if (copyCount > saveWidth)
                    {
                        startIndex = (endIndex - (saveWidth - 1) + typeFrameCount) % typeFrameCount;
                    }
                    qDebug() << "&&&&&" << PRETTY_FUNCTION << "offset:" << offset << "startIndex:" << startIndex
                             << "endIndex:" << endIndex << "typeFrameCount:" << typeFrameCount
                             << "copyCount:" << copyCount;
                    if (!fileFrameMapper->saveToFile(fileFramePath, startIndex, endIndex, typeFrameCount))
                    {
                        return false;
                    }
                }
            }
        }
    }
    return true;
}

int FileFrameMemory::frameIndex() const
{
    return m_FileFrameInfoMapper->frameIndex();
}

bool FileFrameMemory::saveFrameInfoMapper(const QString& dirPath, const int startIndex, const int endIndex,
                                          const int frameAvgCount, bool forward)
{
    QString fileMapInfoPath = dirPath + QString("/%1").arg(FILEMAPINFO_FILE_NAME);
    bool ret = false;
    if (startIndex == endIndex)
    {
        m_FileFrameInfoMapper->saveUseFrontIndex(m_SaveUseFrontIndex);
        ret = m_FileFrameInfoMapper->saveCurrentToFile(fileMapInfoPath, startIndex, frameAvgCount);
    }
    else
    {
        if (!forward)
        {
            QString indexFilePath = dirPath + QString("/%1").arg(WHOLEFRAME_INDEX_NAME);
            ret = m_FileFrameInfoMapper->saveToFile(fileMapInfoPath, indexFilePath, startIndex, endIndex);
        }
        else
        {
            QString indexFilePath = dirPath + QString("/%1").arg(WHOLEFRAME_INDEX_NAME);
            ret = m_FileFrameInfoMapper->forwardSaveToFile(fileMapInfoPath, indexFilePath, startIndex, endIndex);
        }
    }
    return ret;
}

void FileFrameMemory::copyDataByFrameType(FileFrameMemory* fileFrameMemory, const int frameType, const int startIndex,
                                          const int endIndex)
{
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "frameType:" << frameType << "startIndex:" << startIndex << "endIndex:" << endIndex
             << "m_IsWirteEnable:" << m_IsWirteEnable;
#endif
    FileFrameMapper* destFileFrameMapper = m_FileFrameMappers.value(frameType);
    FileFrameMapper* srcFileFrameMapper = fileFrameMemory->m_FileFrameMappers.value(frameType);
    destFileFrameMapper->copyDataFrom(srcFileFrameMapper, startIndex, endIndex, m_IsWirteEnable);
    struct FrameFileUnitInfo frameFileUnitInfo =
        m_FrameFileUnit.frameUnits.value(frameType, defalutFrameFileUnitInfo(frameType));
    frameFileUnitInfo.startFrameIndex = destFileFrameMapper->frameIndex();
    frameFileUnitInfo.endFrameIndex = destFileFrameMapper->frameIndex();
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "frameType:" << frameFileUnitInfo.frameType
             << "startFrameIndex:" << frameFileUnitInfo.startFrameIndex
             << "endFrameIndex:" << frameFileUnitInfo.endFrameIndex;
#endif
    m_FileFrameInfoMapper->updateHeadInfoStartOffsetIndex(frameType, destFileFrameMapper->frameIndex());

    // 2023-11-09 Write by AlexWang BUG:67414 有潜在多线程竞争修改QHash数据，导致异常崩溃
    QMutexLocker lck(&m_FrameMutex);
    m_NewFrameFlag.insert(frameType, true);

    m_FrameFileBeginIndexs.insert(frameType, destFileFrameMapper->fileBeginIndex());
    m_FrameFileQueueFull.insert(frameType, destFileFrameMapper->isQueueFull());
    m_FrameFileUnit.frameUnits.insert(frameFileUnitInfo.frameType, frameFileUnitInfo);
}

FileFrameMapper* FileFrameMemory::fileFrameMapper(int frameType) const
{
    return m_FileFrameMappers.value(frameType, NULL);
}

bool FileFrameMemory::isReadOnly() const
{
    return m_IsReadOnly;
}

bool FileFrameMemory::isQueueFull() const
{
    return m_FileFrameInfoMapper->isQueueFull();
}

void FileFrameMemory::calcSaveIndex(int& saveStartIndex, int& saveEndIndex, int frameType)
{
    //    qDebug() << "calcSaveIndex: frameType" << frameType << "calc index:" << saveStartIndex << saveEndIndex;
    int typeFrameCount = frameTotalCount(frameType);
    const struct FrameFileHeadInfo& ffhi = m_FileFrameInfoMapper->frameFileHeadInfo(frameType);
    int startFrameOffset = m_FileFrameInfoMapper->startFrameOffset(frameType, saveStartIndex);
    int startIndex = ((ffhi.frameUnitCount == 1) && (frameType != ImageBufferDef::Sound_Data))
                         ? saveStartIndex + scpdValue()
                         : saveStartIndex;
    startIndex = (startIndex < saveEndIndex) ? startIndex : saveEndIndex;
    struct FrameFileUnitInfo startFFUI = frameFileUnitInfo(frameType, startIndex);
    int oldStartIndex = frameFileUnitInfo(frameType, saveStartIndex).startFrameIndex;
    saveStartIndex = startFFUI.startFrameIndex -
                     (((ffhi.frameUnitCount == 1) && (frameType != ImageBufferDef::Sound_Data)) ? scpdValue() : 0);
    saveStartIndex = (saveStartIndex >= 0) ? saveStartIndex : 0;
    if (saveStartIndex > oldStartIndex)
    {
        qWarning() << "calcSaveIndex: oldStartIndex" << oldStartIndex << "saveStartIndex" << saveStartIndex;
    }
    saveStartIndex = (saveStartIndex > oldStartIndex) ? oldStartIndex : saveStartIndex;
    // TODO 此处针对三同步队列满帧头指针不为0的情况下,
    //上述减去scpd的操作会导致数据索引越过起始索引，因此在这里限制存储起始索引不能低于第一帧的起始索引，
    //这段逻辑属于补丁修改，后续需要重新整理整个缓存逻辑
    if ((ffhi.frameUnitCount == 1) && (frameType != ImageBufferDef::Sound_Data))
    {
        saveStartIndex = qMax(saveStartIndex, frameFileUnitInfo(frameType, 0).startFrameIndex);
    }
    //    qDebug() << "calcSaveIndex: frameType" << frameType << "startIndex:" << oldStartIndex << "to" <<
    //    saveStartIndex; if (ffhi.frameUnitCount == 1)
    //    {
    //        startFFUI = frameFileUnitInfo(frameType, saveStartIndex + scpdValue());
    //        saveStartIndex = startFFUI.startFrameIndex - scpdValue();
    //        qWarning() << "calcSaveIndex: frameType" << frameType << "startIndex change to" << saveStartIndex;
    //    }
    struct FrameFileUnitInfo stopFFUI = frameFileUnitInfo(frameType, saveEndIndex);
    saveEndIndex = stopFFUI.endFrameIndex;
    qWarning() << "[LUKAS] calcSaveIndex: frameType" << frameType << "endIndex" << saveEndIndex;
    if ((saveStartIndex > 0) || (saveEndIndex > 0))
    {
        //        const struct FrameFileHeadInfo &ffhi = m_FileFrameInfoMapper->frameFileHeadInfo(frameType);
        if (ffhi.frameUnitCount == 1)
        {
            int copyCount = (saveEndIndex - saveStartIndex + typeFrameCount) % typeFrameCount + 1;
            if (copyCount < scpdValue() + 1)
            {
                saveStartIndex = (saveEndIndex - (scpdValue() + 1 - 1) + typeFrameCount) % typeFrameCount;
            }
        }
        else
        {
            saveStartIndex = (saveStartIndex - startFrameOffset + typeFrameCount) % typeFrameCount;
        }
    }

//    qDebug() << "calcSaveIndex: frameType" << frameType << "final index:" << saveStartIndex << saveEndIndex;
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_ImageIndex:" << m_ImageIndex << "frameType:" << frameType
             << "startFFUI.startFrameIndex:" << startFFUI.startFrameIndex
             << "startFFUI.endFrameIndex:" << startFFUI.endFrameIndex
             << "stopFFUI.startFrameIndex:" << stopFFUI.startFrameIndex
             << "stopFFUI.endFrameIndex:" << stopFFUI.endFrameIndex << "saveStartIndex:" << saveStartIndex
             << "typeFrameCount:" << typeFrameCount << "startFrameOffset:" << startFrameOffset
             << "saveEndIndex:" << saveEndIndex;
#endif
}

QList<int> FileFrameMemory::calculateFourDFrameIndexList(const int startIndex, const int endIndex)
{
    QList<int> list;
    int frameCount = frameTotalCount(ImageBufferDef::FourD_Data);
    for (int i = startIndex; i <= endIndex; i++)
    {
        FrameFile::FrameFileUnitInfo frameFUInfo = frameFileUnitInfo(ImageBufferDef::FourD_Data, i);
        int buffercount = (frameFUInfo.endFrameIndex + frameCount - frameFUInfo.startFrameIndex + 1) % frameCount;
        for (int i = 0; i < buffercount; i++)
        {
            list << ((frameFUInfo.startFrameIndex + i + frameCount) % frameCount);
        }
    }

    return list;
}

void FileFrameMemory::setIsWirteEnable(bool isEnable)
{
    m_IsWirteEnable = isEnable;
    if (m_FileFrameInfoMapper != NULL)
    {
        m_FileFrameInfoMapper->setIsWirteEnable(m_IsWirteEnable);
    }
}

bool FileFrameMemory::isFileMapEnable() const
{
    return (m_IsWirteEnable || m_IsReadOnly);
}

int FileFrameMemory::lastPaintFrameIndex()
{
    return m_FileFrameInfoMapper->lastPaintFrameIndex();
}

void FileFrameMemory::setLastPaintFrameIndex(int frameIndex)
{
    return m_FileFrameInfoMapper->setLastPaintFrameIndex(frameIndex);
}

qint64 FileFrameMemory::getFramestampByIndex(int frameIndex) const
{
    return m_FileFrameInfoMapper->getFramestampByIndex(frameIndex);
}

int FileFrameMemory::getFrameIndexByFrameTimestamp(const qint64& timestamp) const
{
    return m_FileFrameInfoMapper->getFrameIndexByFrameTimestamp(timestamp);
}

void FileFrameMemory::setStartFrameTimeStamp(const qint64& timestamp, bool updateFrameTimeStampList)
{
    m_FileFrameInfoMapper->setStartFrameTimeStamp(timestamp, updateFrameTimeStampList);
}

const qint64& FileFrameMemory::getStartFrameTimeStamp() const
{
    return m_FileFrameInfoMapper->getStartFrameTimeStamp();
}

void FileFrameMemory::saveUseFrontIndex(int index)
{
    m_SaveUseFrontIndex = index;
}

int FileFrameMemory::getSaveUseFrontIndex()
{
    return m_SaveUseFrontIndex;
}

} // namespace FrameFile
