#include "fileframeinfomapper.h"
#include "assertlog.h"
#include "bitoperator.h"
#include "filemapunit.h"
#include "memoryleakcheck.h"
#include <QDataStream>
#include <QDebug>
#include <QHashIterator>
#include <QMutexLocker>

namespace FrameFile
{

FileFrameInfoMapper::FileFrameInfoMapper(const int imageIndex, const FrameFileHeadInfo& frameFileHeadInfo,
                                         const QList<int>& frameInfo)
    : m_ImageIndex(imageIndex)
    , m_ErrorCode(LoadErrorCode::Success)
    , m_IsReadOnly(false)
    , m_IsQueueFull(false)
    , m_IsRealtimeSaveState(false)
    , m_CurrentIndex(-1)
    , m_ScpdValue(0)
    , m_FrontIndex(0)
    , m_FrameTypes(frameInfo)
    , m_IsWirteEnable(true)
    , m_FrameSteerBitCount(4)
    , m_LastPaintFrameIndex(-1)
{
    initFrameFileHead(frameFileHeadInfo);
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << " Create m_ImageIndex:" << m_ImageIndex << " @" << Qt::hex << (quintptr)this;
#endif
}

FileFrameInfoMapper::FileFrameInfoMapper(const int imageIndex, const QString& imageFilePath,
                                         const QString& wholeIndexFilePath)
    : m_ImageIndex(imageIndex)
    , m_ErrorCode(LoadErrorCode::Success)
    , m_IsReadOnly(true)
    , m_IsQueueFull(false)
    , m_IsRealtimeSaveState(false)
    , m_CurrentIndex(-1)
    , m_ScpdValue(0)
    , m_FrontIndex(0)
    , m_IsWirteEnable(false)
    , m_FrameSteerBitCount(4)
    , m_LastPaintFrameIndex(-1)
{
    QFile file(imageFilePath);
    if (file.open(QFile::ReadWrite))
    {
        if (file.size() == 0)
        {
            m_FrameFileHead = defaultFrameFileHead();
            m_FrameUnits.clear();
            m_FrameTimeStamps.clear();
            m_ErrorCode = LoadErrorCode::Success;
            file.close();
            return;
        }

        FileMapUnit* fileMapUnit = getFileMapUnit(0, file);
        load(fileMapUnit);
        CHECK_DELETE(FileMapUnit, fileMapUnit);
        delete fileMapUnit;
        file.close();

        QFile indexFile(wholeIndexFilePath);
        if (indexFile.exists() && indexFile.open(QFile::ReadOnly))
        {
            QDataStream stream(&indexFile);
            stream >> m_FrameWholeIndex;
            indexFile.close();
        }
    }
    else
    {
        m_ErrorCode = LoadErrorCode::Headinfo_bin_OpenFailed;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "Create m_ImageIndex:" << m_ImageIndex << "@" << (quintptr)this << m_FrameWholeIndex;
#endif
}

FileFrameInfoMapper::~FileFrameInfoMapper()
{
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_ImageIndex:" << m_ImageIndex << "@" << (quintptr)this;
#endif
    clear();
}

void FileFrameInfoMapper::setRealTimeSaveState(bool bRealTime)
{
    m_IsRealtimeSaveState = bRealTime;
}

int FileFrameInfoMapper::getCombineFrameSteerOnBit(const FrameFileUnitInfo& info, int frameIndex)
{
    if (info.frameType == ImageBufferDef::Image2D && (info.endFrameIndex != info.startFrameIndex))
    {
        if ((frameIndex <= info.endFrameIndex) && (frameIndex >= info.startFrameIndex))
        {
            int curPos = (info.endFrameIndex - frameIndex) * m_FrameSteerBitCount;
            return BitOperator::getBitValue(info.frameSteer, curPos, m_FrameSteerBitCount);
        }
    }

    return info.frameSteer;
}

void FileFrameInfoMapper::combineFrameSteer(FrameFileUnitInfo& info, int newSteer)
{
    int offsetBitIndex = info.endFrameIndex - info.startFrameIndex;
    if (info.frameType == ImageBufferDef::Image2D && offsetBitIndex != 0)
    {
        info.frameSteer = (info.frameSteer << (m_FrameSteerBitCount * offsetBitIndex)) | newSteer;
    }
    else
    {
        info.frameSteer = newSteer;
    }
}

void FileFrameInfoMapper::updatecurrentFrameTimestamp(const qint64& timestamp)
{
    {
        QMutexLocker locker(&m_MutexCurrentIndex);
        if (0 == m_CurrentIndex)
        {
            m_FrameUnits[m_CurrentIndex].timeDelayMS = timestamp - m_StatrFrameTimeStamp;
        }
        else
        {
            m_FrameUnits[m_CurrentIndex].timeDelayMS = timestamp - m_FrameTimeStamps[m_CurrentIndex - 1];
        }
    }
    m_FrameTimeStamps.insert(m_CurrentIndex, timestamp);
}

void FileFrameInfoMapper::setActive(const int frameType, const bool active)
{
    m_ImageActive.insert(frameType, active);
}

bool FileFrameInfoMapper::isActive(int frameType) const
{
    return m_ImageActive.value(frameType, true);
}

void FileFrameInfoMapper::debugInfo()
{
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "front:" << m_FrontIndex << "current:" << m_CurrentIndex
             << "queuefull:" << m_IsQueueFull << "datasize:" << m_FrameUnits.size()
             << "frameCount:" << m_FrameFileHead.frameHeadInfo.frameCount
             << "frameSize:" << m_FrameFileHead.frameHeadInfo.frameSize
             << "totalFrameCount:" << m_FrameFileHead.frameHeadInfo.mapFrameTotalCount;
#endif
    // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历，使用const auto& 绑定到临时对象，减少遍历时临时拷贝kyes
    const auto& keys = m_FrameFileHead.frameInfos.keys();
    for (const int type : keys)
    {
        const struct FrameFileHeadInfo& ffhi = m_FrameFileHead.frameInfos[type];
        qDebug() << PRETTY_FUNCTION << "frameType:" << ffhi.frameType << "frameCount:" << ffhi.frameCount
                 << "frameUnitCount:" << ffhi.frameUnitCount << "startFrameOffset:" << ffhi.startFrameOffset;
    }
    for (int i = 0; i < m_FrameUnits.count(); ++i)
    {
        // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
        // 绑定到临时对象，减少遍历时临时拷贝kyes
        const auto& keys = m_FrameUnits[i].frameUnits.keys();
        for (const int type : keys)
        {
            const FrameFileUnitInfo& ffui = m_FrameUnits[i].frameUnits[type];
            qDebug() << PRETTY_FUNCTION << "i:" << i << "frameType:" << ffui.frameType << "\t"
                     << "frameSteer:" << ffui.frameSteer << "\t"
                     << "startFrameIndex:" << ffui.startFrameIndex << "\t"
                     << "endFrameIndex:" << ffui.endFrameIndex;
        }
    }
}

void FileFrameInfoMapper::clearForNew()
{
    if (m_IsReadOnly)
    {
        return;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION;
#endif
    clear();
    m_FrameTypes.clear();
    m_FrameWholeIndex.clear();
    m_FrameFileHead.frameInfoCounts = 0;
    m_FrameFileHead.frameInfos.clear();
    m_ImageActive.clear();
    m_LastPaintFrameIndex = -1;
}

void FileFrameInfoMapper::clear(bool clearUnActive)
{
    if (m_IsReadOnly)
    {
        return;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "clearUnActive:" << clearUnActive
             << "m_UnActiveFrameUnits.count:" << m_UnActiveFrameUnits.count() << "@" << (quintptr)this;
#endif
    if (clearUnActive)
    {
        m_UnActiveFrameUnits.clear();
    }
    // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
    for (int type : qAsConst(m_FrameTypes))
    {
        m_FrameWholeIndex[type].clear();
    }
    {
        // 2023-11-09 Write by AlexWang BUG:67414
        // m_CurrentIndex有潜在线程竞争，有概率使enqueueFrameFileUnit中的m_CurrentIndex值为-1
        QMutexLocker locker(&m_MutexCurrentIndex);
        m_CurrentIndex = -1;
        m_FrontIndex = 0;
        m_IsQueueFull = false;
        m_LastPaintFrameIndex = -1;
        clearFrameFileHead();
    }
    if (!clearUnActive)
    {
        QHash<int, int> fileBeginIndexs;
        QHash<int, bool> fileQueueFull;
        // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
        for (const FrameFileUnit& frameFileUnit : qAsConst(m_UnActiveFrameUnits))
        {
#ifdef ENABLE_FRAME_FILE_DEBUG
            foreach (const FrameFileUnitInfo& ffui, frameFileUnit.frameUnits)
            {
                qDebug() << PRETTY_FUNCTION << "m_ImageIndex:" << m_ImageIndex << "frameType:" << ffui.frameType
                         << "startFrameIndex:" << ffui.startFrameIndex << "endFrameIndex:" << ffui.endFrameIndex;
            }
#endif
            enqueueFrameFileUnit(frameFileUnit, fileBeginIndexs, fileQueueFull);
        }
    }
}

void FileFrameInfoMapper::clearUnActiveFrameUnits()
{
    m_UnActiveFrameUnits.clear();
}

LoadErrorCode FileFrameInfoMapper::isValid() const
{
    return m_ErrorCode;
}

bool FileFrameInfoMapper::isReadOnly() const
{
    return m_IsReadOnly;
}

bool FileFrameInfoMapper::isQueueFull() const
{
    return m_IsQueueFull;
}

bool FileFrameInfoMapper::isNull() const
{
    //由于frameFileUnit函数中使用m_FrameFileHead.frameHeadInfo.frameCount作为除数，
    //在m_FrameFileHead.frameHeadInfo.frameCount被重置为0后，立即使用时存在signal SIGFPE, Arithmetic exception
    //在clearFrameFileHead函数中m_FrameFileHead.frameHeadInfo.frameCount先设置为0，再设置m_FrameFileHead.frameInfos中frameCount为0
    //在使用上存在时间差，因此在m_FrameFileHead.frameHeadInfo.frameCount为0时，应当立即返回true
    if (m_FrameFileHead.frameHeadInfo.frameCount == 0)
    {
        return true;
    }
    return false;
}

void FileFrameInfoMapper::saveUseFrontIndex(int index)
{
    m_SaveUseFrontIndex = index;
}

int FileFrameInfoMapper::getSaveUseFrontIndex()
{
    return m_SaveUseFrontIndex;
}

void FileFrameInfoMapper::setScpdValue(const int scpdValue)
{
    m_ScpdValue = scpdValue;
}

int FileFrameInfoMapper::startIndex(const int type) const
{
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "type:" << type << "m_CurrentIndex:" << m_CurrentIndex
             << "m_FrontIndex:" << m_FrontIndex
             << "startFrameIndex:" << m_FrameUnits[m_FrontIndex].frameUnits[type].startFrameIndex;
#endif
    QMutexLocker locker(&m_MutexCurrentIndex);
    if (m_CurrentIndex == -1)
    {
        return -1;
    }
    return m_FrameUnits[m_FrontIndex].frameUnits[type].startFrameIndex;
}

int FileFrameInfoMapper::currentIndex(const int type) const
{
    QMutexLocker locker(&m_MutexCurrentIndex);
    if (m_CurrentIndex == -1)
    {
        return -1;
    }
    return m_FrameUnits[m_CurrentIndex].frameUnits[type].startFrameIndex;
}

int FileFrameInfoMapper::endIndex(const int type) const
{
    QMutexLocker locker(&m_MutexCurrentIndex);
    if (m_CurrentIndex == -1)
    {
        return -1;
    }
    return m_FrameUnits[m_CurrentIndex].frameUnits[type].endFrameIndex;
}

int FileFrameInfoMapper::startFrameOffset(const int type)
{
    if (m_FrameFileHead.frameInfos.contains(type))
    {
        const FrameFileHeadInfo& headInfo = frameFileHeadInfo(type);
        return headInfo.startFrameOffset;
    }
    else
    {
        return 0;
    }
}

int FileFrameInfoMapper::startFrameOffset(const int type, const int startIndex)
{
    FrameFileUnitInfo startFrameFileUnitInfo = frameFileUnitInfo(type, startIndex);
    FrameFileUnitInfo zeroFrameFileUnitInfo = frameFileUnitInfo(type, 0);
    const FrameFileHeadInfo& headInfo = frameFileHeadInfo(type);
    int frameOffset = 0;

    if (startFrameFileUnitInfo.startFrameIndex >= zeroFrameFileUnitInfo.startFrameIndex)
    {
        frameOffset = startFrameFileUnitInfo.startFrameIndex - zeroFrameFileUnitInfo.startFrameIndex;
    }
    else
    {
        frameOffset = startFrameFileUnitInfo.startFrameIndex + headInfo.mapFrameTotalCount -
                      zeroFrameFileUnitInfo.startFrameIndex;
    }
    frameOffset += headInfo.startFrameOffset;
    frameOffset = frameOffset <= headInfo.frameUnitCount ? frameOffset : headInfo.frameUnitCount - 1;

#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "type:" << type << "startIndex:" << startIndex
             << "start.startFrameIndex:" << startFrameFileUnitInfo.startFrameIndex
             << "zero.startFrameIndex:" << zeroFrameFileUnitInfo.startFrameIndex
             << "headInfo.startFrameOffset:" << headInfo.startFrameOffset
             << "headInfo.frameUnitCount:" << headInfo.frameUnitCount
             << "headInfo.mapFrameTotalCount:" << headInfo.mapFrameTotalCount << "frameOffset:" << frameOffset;
#endif

    return frameOffset;
}

bool flag = true;
int FileFrameInfoMapper::frameIndex(const int type, const int typeIndex) const
{
    if (m_IsReadOnly && !m_FrameWholeIndex.contains(type))
    {
        return typeIndex;
    }
    int recordIndex = m_FrameWholeIndex[type].value(typeIndex, 0);
    if (recordIndex >= m_FrontIndex)
    {
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << " typeIndex:" << typeIndex << " retIndex:" << recordIndex - m_FrontIndex;
#endif
        return recordIndex - m_FrontIndex;
    }
    else
    {
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << " typeIndex:" << typeIndex
                 << " retIndex2:" << maxFrameCount() - m_FrontIndex + recordIndex;
#endif
        return maxFrameCount() - m_FrontIndex + recordIndex;
    }
}

int FileFrameInfoMapper::frameIndex() const
{
    QMutexLocker locker(&m_MutexCurrentIndex);
    return m_CurrentIndex + 1;
}

int FileFrameInfoMapper::frameCount() const
{
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION
             << "m_FrameFileHead.frameHeadInfo.frameCount:" << m_FrameFileHead.frameHeadInfo.frameCount
             << "m_IsQueueFull:" << m_IsQueueFull;
#endif
    return m_FrameFileHead.frameHeadInfo.frameCount;
}

int FileFrameInfoMapper::frameCount(const int type) const
{
    return m_FrameFileHead.frameInfos.contains(type) ? m_FrameFileHead.frameInfos[type].frameCount : 0;
}
/**
 * @brief 总帧数-复合帧数 得到总缓冲区大小
 * @return
 */
int FileFrameInfoMapper::maxFrameCount() const
{
    return m_FrameFileHead.frameHeadInfo.mapFrameTotalCount;
}

int FileFrameInfoMapper::maxFrameCount(const int type) const
{
    return m_FrameFileHead.frameInfos.contains(type) ? m_FrameFileHead.frameInfos[type].mapFrameTotalCount
                                                     : maxFrameCount();
}

const QList<struct FrameFileHeadInfo> FileFrameInfoMapper::frameFileHeadInfos() const
{
    return m_FrameFileHead.frameInfos.values();
}

struct FrameFileHeadInfo& FileFrameInfoMapper::frameFileHeadInfo(const int frameType)
{
    Q_ASSERT(m_FrameFileHead.frameInfos.contains(frameType));
    return m_FrameFileHead.frameInfos[frameType];
}
#if SYS_APPLE
const struct FrameFileHeadInfo& FileFrameInfoMapper::frameFileHeadInfo(const int frameType) const
{
    Q_ASSERT(m_FrameFileHead.frameInfos.contains(frameType));
    return m_FrameFileHead.frameInfos[frameType];
}
#endif

int FileFrameInfoMapper::getFrameSizeByType(const int frameType) const
{
    if (m_FrameFileHead.frameInfos.contains(frameType))
    {
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << " FrameType:" << frameType << " FrameSize "
                 << m_FrameFileHead.frameInfos.value(frameType).frameSize;
#endif
        return m_FrameFileHead.frameInfos.value(frameType).frameSize;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << " FrameType:" << frameType << " FrameSize " << 0;
#endif
    return 0;
}

void FileFrameInfoMapper::updateFrameFileHeadInfo(const int frameType, const int frameSize, const int frameUnitCount,
                                                  const int mapFrameTotalCount)
{
    if (!m_FrameTypes.contains(frameType))
    {
        m_FrameTypes.append(frameType);
    }
    struct FrameFileHeadInfo frameFileHeadInfo =
        m_FrameFileHead.frameInfos.value(frameType, defaultFrameFileHeadInfo(frameType));
    frameFileHeadInfo.frameSize = frameSize;
    frameFileHeadInfo.frameUnitCount = frameUnitCount;
    frameFileHeadInfo.mapFrameCount = m_FrameFileHead.frameHeadInfo.mapFrameCount;
    frameFileHeadInfo.mapFrameTotalCount = mapFrameTotalCount;
    m_FrameFileHead.frameInfos.insert(frameFileHeadInfo.frameType, frameFileHeadInfo);

    m_FrameFileHead.frameInfoCounts = m_FrameTypes.size();
}

void FileFrameInfoMapper::updateHeadInfoStartOffsetIndex(const int frameType, const int startOffsetIndex)
{
    Q_ASSERT(m_FrameFileHead.frameInfos.contains(frameType));

    struct FrameFileHeadInfo frameFileHeadInfo = m_FrameFileHead.frameInfos.value(frameType);
    frameFileHeadInfo.startFrameOffset = startOffsetIndex;
    frameFileHeadInfo.frameCount += startOffsetIndex;
    m_FrameFileHead.frameInfos.insert(frameFileHeadInfo.frameType, frameFileHeadInfo);

#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << " frameType:" << frameType
             << " startFrameOffset:" << frameFileHeadInfo.startFrameOffset;
#endif
}

const FrameFileUnit& FileFrameInfoMapper::frameFileUnit(const int frameIndex, int front) const
{
    int frontIndex = m_FrontIndex;
    if (front != -1)
        frontIndex = front;

    int totalFrameCount = m_IsQueueFull ? maxFrameCount() : frameCount();
    int realIndex = (frameIndex + frontIndex) % totalFrameCount;

#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_IsQueueFull:" << m_IsQueueFull << "frameCount:" << frameCount()
             << "frameIndex:" << frameIndex << "m_FrontIndex:" << m_FrontIndex << "realIndex" << realIndex
             << "m_FrameUnits.count():" << m_FrameUnits.count() << "maxFrameCount():" << maxFrameCount();
#endif

    if (realIndex >= m_FrameUnits.count())
    {
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << m_FrameWholeIndex;
#endif
        Q_ASSERT(false);
    }
    return m_FrameUnits[realIndex];
}

const FrameFileUnit& FileFrameInfoMapper::frameFileUnitAt(const int frameIndex) const
{
    Q_ASSERT(frameIndex >= 0 && frameIndex < m_FrameUnits.count());
    return m_FrameUnits[frameIndex];
}

const struct FrameFileUnitInfo FileFrameInfoMapper::frameFileUnitInfo(const int frameType, const int frameIndex) const
{
    return frameFileUnit(frameIndex).frameUnits[frameType];
}

void FileFrameInfoMapper::updateFrameFileUnit(const FrameFileUnit& frameFileUnit)
{
    m_OldFrameUnit = frameFileUnit;

    // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
    for (const struct FrameFileUnitInfo& frameFileUnitInfo : frameFileUnit.frameUnits)
    {
        if (!m_FrameFileHead.frameInfos.contains(frameFileUnitInfo.frameType))
        {
            m_FrameFileHead.frameInfos.insert(frameFileUnitInfo.frameType,
                                              defaultFrameFileHeadInfo(frameFileUnitInfo.frameType));
        }
        struct FrameFileHeadInfo& frameFileHeadInfo = m_FrameFileHead.frameInfos[frameFileUnitInfo.frameType];
        if (!m_FrameWholeIndex.contains(frameFileHeadInfo.frameType))
        {
            m_FrameWholeIndex.insert(frameFileUnitInfo.frameType, QHash<int, int>());
        }
    }
}

bool FileFrameInfoMapper::nextRear()
{
    QMutexLocker locker(&m_MutexCurrentIndex);
    if (!m_IsWirteEnable && m_IsQueueFull)
    {
        int frameCount = maxFrameCount();
        if (m_FrontIndex == ((m_CurrentIndex + 1) % frameCount))
        {
            m_FrontIndex = (++m_FrontIndex) % frameCount;
//            m_FrameFileHead.frameHeadInfo.frameCount = frameCount - 1;
#ifdef ENABLE_FRAME_FILE_DEBUG
            qDebug() << PRETTY_FUNCTION << "m_FrontIndex:" << m_FrontIndex << "m_CurrentIndex:" << m_CurrentIndex
                     << "maxFrameCount:" << frameCount << "frameCount:" << m_FrameFileHead.frameHeadInfo.frameCount;
            QList<int> indexs;
            indexs << (m_CurrentIndex - 1 + frameCount) % frameCount;
            indexs << m_CurrentIndex % frameCount;
            indexs << (m_CurrentIndex + 1) % frameCount;
            foreach (int index, indexs)
            {
                foreach (const int frameType, m_FrameFileHead.frameInfos.keys())
                {
                    const FrameFileUnitInfo& ffui = m_FrameUnits[index].frameUnits[frameType];
                    qDebug() << PRETTY_FUNCTION << "index:" << index << "ffui.frameType:" << ffui.frameType
                             << "ffui.startFrameIndex:" << ffui.startFrameIndex
                             << "ffui.endFrameIndex:" << ffui.endFrameIndex << "ffui.frameSteer:" << ffui.frameSteer;
                }
            }
#endif
            // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
            // 绑定到临时对象，减少遍历时临时拷贝kyes
            const auto& keys = m_FrameFileHead.frameInfos.keys();
            for (const int frameType : keys)
            {
                if (!isActive(frameType))
                {
                    for (int i = 0; i <= m_ScpdValue; ++i)
                    {
                        int copyToIndex = (m_FrontIndex + m_ScpdValue - i) % frameCount;
                        int copyFromIndex = (m_FrontIndex + m_ScpdValue - i - 1 + frameCount) % frameCount;
                        const FrameFileUnitInfo& ffui = m_FrameUnits[copyFromIndex].frameUnits[frameType];
                        m_FrameUnits[copyToIndex].frameUnits[frameType] = ffui;
#ifdef ENABLE_FRAME_FILE_DEBUG
                        qDebug() << PRETTY_FUNCTION << "frameType:" << frameType << "copyFromIndex:" << copyFromIndex
                                 << "copyToIndex:" << copyToIndex << "startFrameIndex:" << ffui.startFrameIndex
                                 << "endFrameIndex:" << ffui.endFrameIndex << "frameSteer:" << ffui.frameSteer;
#endif
                    }
                }
            }
            return true;
        }
    }
    return false;
}

void FileFrameInfoMapper::enqueueFrameFileUnit(const FrameFileUnit& frameFileUnit,
                                               const QHash<int, int>& fileBeginIndexs,
                                               const QHash<int, bool>& fileQueueFull)
{
    calAvgFunRunTimeIn(enqueueFrameFileUnit);
    if (m_IsReadOnly)
    {
        calAvgFunRunTimeOut(enqueueFrameFileUnit);
        return;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    QString debugString;
#endif
    QMutexLocker locker(&m_MutexCurrentIndex);
    /*计算帧索引*/
    m_CurrentIndex++;

    //    qDebug() << PRETTY_FUNCTION
    //             << "enqueueFrame curIndex:" << m_CurrentIndex
    //             << "maxCount" << maxFrameCount();
    if (m_CurrentIndex >= (maxFrameCount() - (m_IsWirteEnable ? 0 : 1)))
    {
        m_IsQueueFull = true;
    }
    if (m_CurrentIndex >= maxFrameCount())
    {
        //实时存电影的情况下，如果队列已满了，则应该丢掉当前帧，不然会覆盖掉已经存下的起始帧
        if (m_IsRealtimeSaveState)
        {
            m_CurrentIndex = maxFrameCount() - 1;
            return;
        }
        m_CurrentIndex = m_CurrentIndex % maxFrameCount();
    }

    /*更新各种类型帧的文件头信息*/
    // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
    for (const struct FrameFileUnitInfo& frameFileUnitInfo : frameFileUnit.frameUnits)
    {
        struct FrameFileHeadInfo& frameFileHeadInfo = m_FrameFileHead.frameInfos[frameFileUnitInfo.frameType];
        const struct FrameFileUnitInfo& oldFrameFileUnitInfo = m_OldFrameUnit.frameUnits[frameFileUnitInfo.frameType];
        QHash<int, int>& frameIndex = m_FrameWholeIndex[frameFileHeadInfo.frameType];
        if ((oldFrameFileUnitInfo.startFrameIndex != frameFileUnitInfo.startFrameIndex) ||
            (oldFrameFileUnitInfo.endFrameIndex != frameFileUnitInfo.endFrameIndex ||
             (oldFrameFileUnitInfo.frameSteer != frameFileUnitInfo.frameSteer)) ||
            frameIndex.isEmpty())
        {
            for (int i = frameFileUnitInfo.startFrameIndex; i <= frameFileUnitInfo.endFrameIndex; i++)
            {
                frameIndex.insert(i, m_CurrentIndex);
            }
        }

        int fileBeginIndex = fileBeginIndexs.value(frameFileUnitInfo.frameType, 0);
        bool isQueueFull = fileQueueFull.value(frameFileUnitInfo.frameType, false);

        if (isQueueFull)
        {
            frameFileHeadInfo.frameCount = frameFileHeadInfo.mapFrameTotalCount;
        }
        else
        {
            frameFileHeadInfo.frameCount = frameFileUnitInfo.endFrameIndex + 1;
        }
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << "isQueueFull:" << isQueueFull << "maxFrameCount():" << maxFrameCount()
                 << "frameFileHeadInfo.mapFrameTotalCount:" << frameFileHeadInfo.mapFrameTotalCount
                 << "frameFileHeadInfo.frameCount:" << frameFileHeadInfo.frameCount
                 << "frameFileHeadInfo.frameType:" << frameFileHeadInfo.frameType;
#endif
        frameFileHeadInfo.frameFileBeginIndex = fileBeginIndex;
#ifdef ENABLE_FRAME_FILE_DEBUG
        debugString.append(QString(" FrameType is %1, StartIndex is %2, EndIndex is %3;")
                               .arg(frameFileUnitInfo.frameType)
                               .arg(frameFileUnitInfo.startFrameIndex)
                               .arg(frameFileUnitInfo.endFrameIndex));
#endif
    }

    //    foreach(const struct FrameFileUnitInfo &ffui, frameFileUnit.frameUnits)
    //    {
    //        qDebug() << PRETTY_FUNCTION
    //                 << "m_CurrentIndex:"<<m_CurrentIndex
    //                 << "frameType:" << ffui.frameType
    //                 << "startFrameIndex:" << ffui.startFrameIndex
    //                 << "endFrameIndex:" << ffui.endFrameIndex;
    //        Q_ASSERT(ffui.startFrameIndex <= ffui.endFrameIndex);
    //    }

    m_FrameUnits[m_CurrentIndex] = frameFileUnit;
    /*更新文件头总帧数*/
    if (m_IsQueueFull)
    {
        if (m_IsWirteEnable)
        {
            m_FrontIndex = (m_CurrentIndex + 1) % maxFrameCount();
        }
        //由于FileFrameInfoMapper::nextRear中会根据m_FrontIndex和m_CurrentIndex的判断条件来更新
        // m_FrameFileHead.frameHeadInfo.frameCount，同时更新m_FrontIndex，但m_CurrentIndex的更新在当前函数中，
        //当前函数的调用在LineImageBuffer::enqueue中受FileFrameMemory::newFrameType的控制，
        //导致LineImageBuffer::enqueue中执行bufferUnit->setFrameIndex时，frameCount值不符合预期
        //因此需要在m_CurrentIndex变更的当前函数中，把m_FrameFileHead.frameHeadInfo.frameCount更新正确
        m_FrameFileHead.frameHeadInfo.frameCount = maxFrameCount() - 1;
    }
    else
    {
        m_FrameFileHead.frameHeadInfo.frameCount = m_CurrentIndex + 1;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_ImageIndex:" << m_ImageIndex << "m_IsQueueFull:" << m_IsQueueFull
             << "m_FrameFileHead.frameHeadInfo.frameCount:" << m_FrameFileHead.frameHeadInfo.frameCount
             << "m_CurrentIndex:" << m_CurrentIndex;
#endif
    m_OldFrameUnit = frameFileUnit;
    calAvgFunRunTimeOut(enqueueFrameFileUnit);
}

void FileFrameInfoMapper::dequeueFrameFileUnit(FrameFileUnit& frameFileUnit)
{
    // TODO
}

void FileFrameInfoMapper::updateFrameFileUnit(const FrameFileUnit& frameFileUnit, const int frameIndex)
{
    // 2023-11-09 Write by AlexWang BUG:67414 m_CurrentIndex有潜在线程竞争
    QMutexLocker locker(&m_MutexCurrentIndex);
    if (m_IsReadOnly)
    {
        qDebug() << PRETTY_FUNCTION << "m_IsReadOnly:" << m_IsReadOnly;
        ASSERT_LOG(false);
        return;
    }
    if (frameIndex > m_CurrentIndex)
    {
        qDebug() << PRETTY_FUNCTION << "m_CurrentIndex:" << m_CurrentIndex << "frameIndex:" << frameIndex;
        ASSERT_LOG(false);
        return;
    }
    /*更新各种类型帧的文件头信息*/
    // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
    for (const struct FrameFileUnitInfo& frameFileUnitInfo : frameFileUnit.frameUnits)
    {
        struct FrameFileHeadInfo& frameFileHeadInfo = m_FrameFileHead.frameInfos[frameFileUnitInfo.frameType];
        QHash<int, int>& frameIndexs = m_FrameWholeIndex[frameFileHeadInfo.frameType];
        for (int i = frameFileUnitInfo.startFrameIndex; i <= frameFileUnitInfo.endFrameIndex; i++)
        {
            if (!frameIndexs.contains(i))
            {
                frameIndexs.insert(i, frameIndex);
            }
        }

        frameFileHeadInfo.frameCount = frameFileUnitInfo.endFrameIndex + 1;
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << "m_ImageIndex:" << m_ImageIndex
                 << "frameFileHeadInfo.frameType:" << frameFileHeadInfo.frameType
                 << "frameFileHeadInfo.frameCount:" << frameFileHeadInfo.frameCount
                 << "frameFileHeadInfo.frameFileBeginIndex:" << frameFileHeadInfo.frameFileBeginIndex
                 << "frameIndexs:" << frameIndexs;
#endif
    }
    m_FrameUnits[frameIndex] = frameFileUnit;
}

void FileFrameInfoMapper::enqueueUnActiveFrameFileUnit(const FrameFileUnit& frameFileUnit, const bool update)
{
    if (update && !m_UnActiveFrameUnits.isEmpty())
    {
        m_UnActiveFrameUnits[m_UnActiveFrameUnits.count() - 1] = frameFileUnit;
    }
    else
    {
        m_UnActiveFrameUnits.append(frameFileUnit);
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    foreach (const FrameFileUnit& frameFileUnit, m_UnActiveFrameUnits)
    {
        foreach (const FrameFileUnitInfo& ffui, frameFileUnit.frameUnits)
        {
            qDebug() << PRETTY_FUNCTION << "m_ImageIndex:" << m_ImageIndex << "frameType:" << ffui.frameType
                     << "startFrameIndex:" << ffui.startFrameIndex << "endFrameIndex:" << ffui.endFrameIndex;
        }
    }
#endif
}

bool FileFrameInfoMapper::clearFrameAfterIndex(const int frameIndex)
{
    // 2023-11-09 Write by AlexWang BUG:67414 m_CurrentIndex有潜在线程竞争
    QMutexLocker locker(&m_MutexCurrentIndex);
    int frameCount = frameIndex;
    /*修正 m_CurrentIndex*/
    m_CurrentIndex = frameIndex - 1;

    if (m_IsQueueFull)
    {
        frameCount = (m_CurrentIndex - m_FrontIndex + m_FrameFileHead.frameHeadInfo.mapFrameTotalCount) %
                         m_FrameFileHead.frameHeadInfo.mapFrameTotalCount +
                     1;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "m_CurrentIndex" << m_CurrentIndex << "m_FrontIndex" << m_FrontIndex
             << "m_FrameFileHead.frameHeadInfo.mapFrameTotalCount " << m_FrameFileHead.frameHeadInfo.mapFrameTotalCount;
#endif
    /*修正 m_FrameFileHead 文件头信息*/
    m_FrameFileHead.frameHeadInfo.frameCount = frameCount;

    // if count is empty
    if (0 == frameCount)
    {
        return true;
    }

    if (m_CurrentIndex < 0 || (m_CurrentIndex >= m_FrameUnits.size()) || m_FrontIndex < 0 ||
        (m_FrontIndex >= m_FrameUnits.size()))
    {
        qCritical() << "over size"
                    << "CurrentIndex" << m_CurrentIndex << "FrontIndex" << m_FrontIndex << PRETTY_FUNCTION;

        //这个地方可能存在崩溃，输出log，抛出异常，后期测试完成要return false
        //        return false;
    }

    struct FrameFileUnit frameFileUnitEnd = m_FrameUnits[m_CurrentIndex];
    struct FrameFileUnit frameFileUnitStart = m_FrameUnits[m_FrontIndex];
    // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
    for (const int frameType : qAsConst(m_FrameTypes))
    {
        struct FrameFileHeadInfo& frameFileHeadInfo = m_FrameFileHead.frameInfos[frameType];
        int oldFrameCount = frameFileHeadInfo.frameCount;

        frameFileHeadInfo.frameCount =
            (frameFileUnitEnd.frameUnits[frameType].endFrameIndex -
             frameFileUnitStart.frameUnits[frameType].startFrameIndex + frameFileHeadInfo.mapFrameTotalCount) %
                frameFileHeadInfo.mapFrameTotalCount +
            1;
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << " frameType:" << frameType << " old frameCount:" << oldFrameCount
                 << " new frameCount:" << frameFileHeadInfo.frameCount
                 << " startFrameIndex:" << frameFileUnitStart.frameUnits[frameType].startFrameIndex
                 << " endFrameIndex:" << frameFileUnitEnd.frameUnits[frameType].endFrameIndex;
#else
        Q_UNUSED(oldFrameCount);
#endif
    }
    return true;
}

int FileFrameInfoMapper::calcStartIndex(const int frameType, const int sIndex, const int eIndex)
{
    int si = startIndex(frameType);
    int so = startFrameOffset(frameType);
    int startIndex = si - so > 0 ? si - so : 0;
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "si:" << si << "so:" << so << "startIndex:" << startIndex;
#endif
    int typeFrameCount = frameCount(frameType);
    FrameFile::FrameFileUnitInfo lastFFUI = frameFileUnitInfo(frameType, frameCount() - 1);
    if (isQueueFull())
    {
        startIndex = lastFFUI.endFrameIndex + 1;
    }
    else
    {
        startIndex = 0;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "sIndex:" << sIndex << "eIndex:" << eIndex
             << "frameIndex:" << frameCount() - 1 << "typeFrameCount:" << typeFrameCount
             << "startFrameIndex:" << lastFFUI.startFrameIndex << "endFrameIndex:" << lastFFUI.endFrameIndex
             << "isQueueFull:" << isQueueFull() << "startIndex:" << startIndex;
#endif
    const struct FrameFileHeadInfo ffhi = frameFileHeadInfo(frameType);
    int width = (eIndex - startIndex + typeFrameCount) % typeFrameCount + 1;
    if (width > ffhi.frameUnitCount)
    {
        startIndex = (eIndex - (ffhi.frameUnitCount - 1) + typeFrameCount) % typeFrameCount;
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "width:" << width << "startIndex:" << startIndex;
#endif
    return startIndex;
}

int FileFrameInfoMapper::saveCompoundStartFrames(QHash<int, QHash<int, int>>& frameWholeIndex,
                                                 QVector<struct FrameFileUnit>& frameUnits,
                                                 struct FrameFileHead& frameFileHead, const int startIndex,
                                                 const int endIndex, QHash<int, int>& startWholeIndex)
{
    int wholeIndex = 0;
    QHash<int, QList<int>> compoundFrameIndexs;
    struct FrameFileUnit firstFrameFileUnit = frameFileUnit(startIndex);
    // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历，使用const auto& 绑定到临时对象，减少遍历时临时拷贝kyes
    const auto& keys = firstFrameFileUnit.frameUnits.keys();
    for (int key : keys)
    {
        frameWholeIndex.insert(key, QHash<int, int>());
        int typeTotalCount = frameCount(key);
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << __FUNCTION__ << "old info"
                 << "wholeIndex:" << wholeIndex << "typeTotalCount:" << typeTotalCount
                 << "frameType:" << firstFrameFileUnit.frameUnits[key].frameType
                 << "startFrameIndex:" << firstFrameFileUnit.frameUnits[key].startFrameIndex
                 << "endFrameIndex:" << firstFrameFileUnit.frameUnits[key].endFrameIndex
                 << "frameSteer:" << firstFrameFileUnit.frameUnits[key].frameSteer;
#endif
        const struct FrameFileHeadInfo& frameFileHeadInfo = frameFileHead.frameInfos[key];
        struct FrameFileUnitInfo& modifyFrameFileUnitInfo = firstFrameFileUnit.frameUnits[key];
        if (frameFileHeadInfo.frameUnitCount == 1)
        {
            //当声音处于非激活区域下,存储的数据记为0
            bool enoughData = isActive(key) ? true : (key == FrameFileType_Sound ? false : true);
            const struct FrameFileUnit& lastFFU = frameFileUnit(endIndex);
            const struct FrameFileUnitInfo& lastFFUI = lastFFU.frameUnits[key];
            const struct FrameFileUnit& realFirstFFU = frameFileUnit(startIndex + m_ScpdValue);
            const struct FrameFileUnitInfo& realFirstFFUI = realFirstFFU.frameUnits[key];
            int typeMaxFrameCount = maxFrameCount(lastFFUI.frameType);
            int totalMaxFrameCount = maxFrameCount();
#ifdef ENABLE_FRAME_FILE_DEBUG
            qDebug() << __FUNCTION__ << "totalMaxFrameCount:" << totalMaxFrameCount
                     << "typeMaxFrameCount:" << typeMaxFrameCount
                     << "lastFFUI.startFrameIndex:" << lastFFUI.startFrameIndex
                     << "lastFFUI.endFrameIndex:" << lastFFUI.endFrameIndex
                     << "startFrameIndex:" << modifyFrameFileUnitInfo.startFrameIndex
                     << "endFrameIndex:" << modifyFrameFileUnitInfo.endFrameIndex << "m_ScpdValue:" << m_ScpdValue;
#endif
            // Atom机型，解决PW存电影，偶发崩溃的问题，崩溃点在该函数下面（拷贝在下两行），其中的【第2行】,compoundFrameIndex访问越界
            //         第1行：QList<int> compoundFrameIndex = compoundFrameIndexs[key]
            //         第2行：struct FrameFileUnit ffu = frameFileUnit(compoundFrameIndex[wholeIndex]);
            //调试发现，越界访问的时候，frameType是512(0x200)，就是声音数据
            //解决办法： 在要往 compoundFrameIndexs 里 insert 数据的时候，判断如果是声音数据就不处理
            int diff = modifyFrameFileUnitInfo.endFrameIndex - modifyFrameFileUnitInfo.startFrameIndex;
            if ((key != FrameFileType_Sound) && enoughData &&
                (m_ScpdValue > 0)) // && (lastFFUI.endFrameIndex - modifyFrameFileUnitInfo.startFrameIndex +
                                   // typeMaxFrameCount) % typeMaxFrameCount < m_ScpdValue)
            {
                QList<int> compoundFrameIndex;
                const struct FrameFileUnit& zeroFFU = frameFileUnit(0);
                const struct FrameFileUnitInfo& zeroFFUI = zeroFFU.frameUnits[key];
#ifdef ENABLE_FRAME_FILE_DEBUG
                qDebug() << __FUNCTION__ << "endFrameIndex:" << lastFFUI.endFrameIndex
                         << "startFrameIndex:" << modifyFrameFileUnitInfo.startFrameIndex
                         << "m_ScpdValue:" << m_ScpdValue;
#endif
                Q_ASSERT((lastFFUI.endFrameIndex - zeroFFUI.startFrameIndex + typeMaxFrameCount) % typeMaxFrameCount >=
                         m_ScpdValue);
                //                int maxFrameIndex = lastFFUI.endFrameIndex;
                int minFrameIndex = zeroFFUI.startFrameIndex;
                //                int minFrameIndex = (realFirstFFUI.startFrameIndex - m_ScpdValue >=
                //                zeroFFUI.startFrameIndex) ? realFirstFFUI.startFrameIndex - m_ScpdValue :
                //                zeroFFUI.startFrameIndex;
                int maxFrameIndex = realFirstFFUI.endFrameIndex;

                if (maxFrameIndex < minFrameIndex)
                {
                    maxFrameIndex += totalMaxFrameCount;
                }

                maxFrameIndex =
                    (maxFrameIndex < minFrameIndex + m_ScpdValue) ? minFrameIndex + m_ScpdValue : maxFrameIndex;

                for (int i = maxFrameIndex; i >= minFrameIndex; --i)
                {
                    const QHash<int, int>& wholeFrameIndex = m_FrameWholeIndex.value(frameFileHeadInfo.frameType);
                    int frameIndex = wholeFrameIndex.value(i % totalMaxFrameCount, 0);
                    //队满进入循环的时候，子帧映射的整帧索引可能还没更新，因此这里需要确保整帧索引要大于队列头指针
                    frameIndex = qMax(frameIndex, m_FrontIndex);
                    if (compoundFrameIndex.contains(frameIndex))
                    {
                        continue;
                        //                        Q_ASSERT(false);
                    }
                    compoundFrameIndex.prepend(frameIndex);
                    if (compoundFrameIndex.count() >= m_ScpdValue + 1)
                    {
                        break;
                    }
                }

                //                qDebug() << PRETTY_FUNCTION << "frameType:" << frameFileHeadInfo.frameType <<
                //                "compundFrameIndex:" << compoundFrameIndex;

                for (int i = maxFrameIndex; compoundFrameIndex.count() < m_ScpdValue + 1; i++)
                {
                    const QHash<int, int>& wholeFrameIndex = m_FrameWholeIndex.value(frameFileHeadInfo.frameType);
                    if (!wholeFrameIndex.contains(i % totalMaxFrameCount))
                    {
                        break;
                    }
                    int frameIndex = wholeFrameIndex.value(i % totalMaxFrameCount, 0);

                    //队满进入循环的时候，子帧映射的整帧索引可能还没更新，因此这里需要确保整帧索引要大于队列头指针
                    frameIndex = qMax(frameIndex, m_FrontIndex);

                    if (compoundFrameIndex.contains(frameIndex))
                    {
                        continue;
                    }

                    compoundFrameIndex.append(frameIndex);
                }

                //                qDebug() << PRETTY_FUNCTION << "frameType:" << frameFileHeadInfo.frameType <<
                //                "compundFrameIndex:" << compoundFrameIndex;

                compoundFrameIndexs.insert(frameFileHeadInfo.frameType, compoundFrameIndex);
                if (wholeIndex >= compoundFrameIndex.count())
                {
                    modifyFrameFileUnitInfo.frameSteer = lastFFUI.frameSteer;
                    diff = lastFFUI.endFrameIndex - lastFFUI.startFrameIndex;
                    startWholeIndex[key] = wholeIndex;
                }
                else
                {
                    // compoundFrameIndex数组存储的索引是真实帧索引，需要用frameFileUnitAt直接取,否则在队满循环时，取的帧是对应不上的
                    const struct FrameFileUnit& ffu = frameFileUnitAt(compoundFrameIndex[wholeIndex]);
                    modifyFrameFileUnitInfo.frameSteer = ffu.frameUnits[key].frameSteer;
                    diff = ffu.frameUnits[key].endFrameIndex - ffu.frameUnits[key].startFrameIndex;
                    startWholeIndex[key] = compoundFrameIndex[wholeIndex] - m_FrontIndex;
                }

                frameWholeIndex[frameFileHeadInfo.frameType].insert(wholeIndex, wholeIndex);
            }
            modifyFrameFileUnitInfo.startFrameIndex = 0;
            modifyFrameFileUnitInfo.endFrameIndex = diff;
        }
        else
        {
            int diff =
                (modifyFrameFileUnitInfo.endFrameIndex - modifyFrameFileUnitInfo.startFrameIndex + typeTotalCount) %
                typeTotalCount;
#ifdef ENABLE_FRAME_FILE_DEBUG
            qDebug() << __FUNCTION__ << "frameFileHeadInfo.startFrameOffset:" << frameFileHeadInfo.startFrameOffset
                     << "modifyFrameFileUnitInfo.endFrameIndex:" << modifyFrameFileUnitInfo.endFrameIndex
                     << "modifyFrameFileUnitInfo.startFrameIndex:" << modifyFrameFileUnitInfo.startFrameIndex
                     << "diff:" << diff << "typeTotalCount:" << typeTotalCount;
#endif
            modifyFrameFileUnitInfo.startFrameIndex = frameFileHeadInfo.startFrameOffset;
            modifyFrameFileUnitInfo.endFrameIndex = (modifyFrameFileUnitInfo.startFrameIndex + diff) % typeTotalCount;
            startWholeIndex[key] = wholeIndex;
        }
        QHash<int, int>& childFrameWholeIndex = frameWholeIndex[modifyFrameFileUnitInfo.frameType];
        if (modifyFrameFileUnitInfo.frameType == ImageBufferDef::FourD_Data)
        {
            int saveStartFrameIndex = frameFileUnit(startIndex).frameUnits[ImageBufferDef::FourD_Data].startFrameIndex;
            int saveEndFrameIndex = frameFileUnit(endIndex).frameUnits[ImageBufferDef::FourD_Data].endFrameIndex;
            int bufferQueueCount = m_FrameFileHead.frameInfos[ImageBufferDef::FourD_Data].frameCount;
            int saveFrameCount = saveEndFrameIndex >= saveStartFrameIndex
                                     ? (saveEndFrameIndex - saveStartFrameIndex + 1)
                                     : (saveEndFrameIndex + bufferQueueCount + 1 - saveStartFrameIndex);
            int startOffset = saveStartFrameIndex;
            modifyFrameFileUnitInfo.startFrameIndex =
                ((modifyFrameFileUnitInfo.startFrameIndex + bufferQueueCount - startOffset) % bufferQueueCount) %
                saveFrameCount;
            modifyFrameFileUnitInfo.endFrameIndex =
                ((modifyFrameFileUnitInfo.endFrameIndex + bufferQueueCount - startOffset) % bufferQueueCount) %
                saveFrameCount;
        }
        for (int j = modifyFrameFileUnitInfo.startFrameIndex; j <= modifyFrameFileUnitInfo.endFrameIndex; j++)
        {
            if (!childFrameWholeIndex.contains(j))
            {
                childFrameWholeIndex.insert(j, 0);
            }
        }
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << __FUNCTION__ << "wholeIndex:" << wholeIndex << "frameType:" << modifyFrameFileUnitInfo.frameType
                 << "frameSteer:" << modifyFrameFileUnitInfo.frameSteer
                 << "startFrameIndex:" << modifyFrameFileUnitInfo.startFrameIndex
                 << "endFrameIndex:" << modifyFrameFileUnitInfo.endFrameIndex;
#endif
    }
    frameUnits[wholeIndex] = firstFrameFileUnit;
    //    if(firstFrameFileUnit.frameUnits.contains(ImageBufferDef::Sound_Data) && startIndex != 0)
    //    {
    //        FrameFileUnitInfo unit = frameFileUnit(0).frameUnits[ImageBufferDef::B_Data];
    //        frameUnits[wholeIndex].frameUnits.remove(ImageBufferDef::B_Data);
    //        frameUnits[wholeIndex].frameUnits.insert(ImageBufferDef::B_Data, unit);
    //    }
    ++wholeIndex;
    while (!compoundFrameIndexs.isEmpty() && (wholeIndex < m_ScpdValue + 1))
    {
        const struct FrameFileUnit& prevFileUnit = frameUnits[wholeIndex - 1];
        const struct FrameFileUnit& oldFileUnit = frameFileUnit(startIndex + wholeIndex - 1);
        struct FrameFileUnit fileUnit = frameFileUnit(startIndex + wholeIndex);
        // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
        // 绑定到临时对象，减少遍历时临时拷贝kyes
        const auto& keys = fileUnit.frameUnits.keys();
        for (const int key : keys)
        {
            const struct FrameFileHeadInfo& frameFileHeadInfo = frameFileHead.frameInfos[key];
            QHash<int, int>& childFrameWholeIndex = frameWholeIndex[frameFileHeadInfo.frameType];
            if ((frameFileHeadInfo.frameUnitCount == 1) && (compoundFrameIndexs.contains(key)))
            {
                QList<int> compoundFrameIndex = compoundFrameIndexs[key];
                struct FrameFileUnit ffu;
                if ((wholeIndex < compoundFrameIndex.count()))
                {
                    // compoundFrameIndex数组存储的索引是真实帧索引，需要用frameFileUnitAt直接取,否则在队满循环时，取的帧是对应不上的
                    ffu = frameFileUnitAt(compoundFrameIndex[wholeIndex]);
                    startWholeIndex[key] = compoundFrameIndex[wholeIndex] - m_FrontIndex;
                }
                else
                {
                    ffu = frameFileUnit(wholeIndex);
                    startWholeIndex[key] = wholeIndex;
                }
                int diff = ffu.frameUnits[key].endFrameIndex - ffu.frameUnits[key].startFrameIndex;
                const FrameFileUnitInfo& preFrameFileUnitInfo = frameUnits[wholeIndex - 1].frameUnits[key];
                fileUnit.frameUnits[key] = ffu.frameUnits[key];
                fileUnit.frameUnits[key].startFrameIndex = preFrameFileUnitInfo.endFrameIndex + 1;
                fileUnit.frameUnits[key].endFrameIndex = fileUnit.frameUnits[key].startFrameIndex + diff;
                for (int i = fileUnit.frameUnits[key].startFrameIndex; i <= fileUnit.frameUnits[key].endFrameIndex; i++)
                {
                    if (!childFrameWholeIndex.contains(i))
                    {
                        childFrameWholeIndex.insert(i, wholeIndex);
                    }
                }
#ifdef ENABLE_FRAME_FILE_DEBUG
                qDebug() << __FUNCTION__ << "wholeIndex:" << wholeIndex
                         << "frameType:" << fileUnit.frameUnits[key].frameType
                         << "frameSteer:" << fileUnit.frameUnits[key].frameSteer
                         << "startFrameIndex:" << fileUnit.frameUnits[key].startFrameIndex
                         << "endFrameIndex:" << fileUnit.frameUnits[key].endFrameIndex;
#endif
            }
            else
            {
                int indexCommonDiff = frameUnits[0].frameUnits[key].startFrameIndex -
                                      frameFileUnit(startIndex).frameUnits[key].startFrameIndex;
                modifyInfoAtIndex(fileUnit, frameWholeIndex, wholeIndex, key, prevFileUnit, oldFileUnit,
                                  indexCommonDiff);
                startWholeIndex[key] = wholeIndex;
            }
        }
        frameUnits[wholeIndex] = fileUnit;
        wholeIndex++;
    }
    return wholeIndex;
}

void FileFrameInfoMapper::modifyInfoAtIndex(struct FrameFileUnit& fileUnit,
                                            QHash<int, QHash<int, int>>& frameWholeIndex, const int wholeIndex,
                                            const int frameType, const struct FrameFileUnit& prevFileUnit,
                                            const struct FrameFileUnit& oldFileUnit, int indexCommonDiff)
{
    int typeTotalCount = frameCount(frameType);
    struct FrameFileUnitInfo& modifyFrameFileUnitInfo = fileUnit.frameUnits[frameType];
    //    if(frameType == ImageBufferDef::FourD_Data)
    //    {
    //        int saveStartFrameIndex =
    //        frameFileUnit(startIndex).frameUnits[ImageBufferDef::FourD_Data].startFrameIndex; int saveEndFrameIndex =
    //        frameFileUnit(endIndex).frameUnits[ImageBufferDef::FourD_Data].endFrameIndex; int bufferQueueCount =
    //        m_FrameFileHead.frameInfos[ImageBufferDef::FourD_Data].frameCount; int saveFrameCount = saveEndFrameIndex
    //        >= saveStartFrameIndex
    //                ? (saveEndFrameIndex -  saveStartFrameIndex + 1)
    //                : (saveEndFrameIndex + bufferQueueCount + 1 - saveStartFrameIndex);
    //        int startOffset = saveStartFrameIndex;
    //        modifyFrameFileUnitInfo.startFrameIndex = ((modifyFrameFileUnitInfo.startFrameIndex + bufferQueueCount -
    //        startOffset) % bufferQueueCount)
    //                % saveFrameCount;
    //        modifyFrameFileUnitInfo.endFrameIndex = ((modifyFrameFileUnitInfo.endFrameIndex + bufferQueueCount -
    //        startOffset) % bufferQueueCount)
    //                % saveFrameCount;
    //    }
    int newDiff = (modifyFrameFileUnitInfo.endFrameIndex - modifyFrameFileUnitInfo.startFrameIndex + typeTotalCount) %
                  typeTotalCount;
    int oldDiff =
        (modifyFrameFileUnitInfo.startFrameIndex - oldFileUnit.frameUnits[frameType].endFrameIndex + typeTotalCount) %
        typeTotalCount;
    oldDiff = (oldDiff < 0) ? 0 : oldDiff;
    //    modifyFrameFileUnitInfo.startFrameIndex = (prevFileUnit.frameUnits[frameType].endFrameIndex + oldDiff) %
    //    typeTotalCount; modifyFrameFileUnitInfo.endFrameIndex = (modifyFrameFileUnitInfo.startFrameIndex + newDiff) %
    //    typeTotalCount; int lastDiff = prevFileUnit.frameUnits[frameType].startFrameIndex -
    //    oldFileUnit.frameUnits[frameType].startFrameIndex; lastDiff = (lastDiff > typeTotalCount / 2) ? lastDiff -
    //    typeTotalCount : lastDiff; lastDiff = (lastDiff < -(typeTotalCount / 2)) ? lastDiff + typeTotalCount :
    //    lastDiff; modifyFrameFileUnitInfo.startFrameIndex += indexCommonDiff; modifyFrameFileUnitInfo.startFrameIndex
    //    = (modifyFrameFileUnitInfo.startFrameIndex < prevFileUnit.frameUnits[frameType].startFrameIndex)
    if (modifyFrameFileUnitInfo.startFrameIndex + indexCommonDiff >= prevFileUnit.frameUnits[frameType].endFrameIndex)
    {
        modifyFrameFileUnitInfo.startFrameIndex += indexCommonDiff;
    }
    else
    {
        modifyFrameFileUnitInfo.startFrameIndex =
            (prevFileUnit.frameUnits[frameType].endFrameIndex + oldDiff) % typeTotalCount;
    }

    if (modifyFrameFileUnitInfo.endFrameIndex + indexCommonDiff >= modifyFrameFileUnitInfo.startFrameIndex)
    {
        modifyFrameFileUnitInfo.endFrameIndex += indexCommonDiff;
    }
    else
    {
        modifyFrameFileUnitInfo.endFrameIndex =
            (modifyFrameFileUnitInfo.startFrameIndex + newDiff + typeTotalCount) % typeTotalCount;
    }

    QHash<int, int>& childFrameWholeIndex = frameWholeIndex[modifyFrameFileUnitInfo.frameType];
    for (int i = modifyFrameFileUnitInfo.startFrameIndex; i <= modifyFrameFileUnitInfo.endFrameIndex; i++)
    {
        if (!childFrameWholeIndex.contains(i))
        {
            childFrameWholeIndex.insert(i, wholeIndex);
        }
    }
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << __FUNCTION__ << "wholeIndex:" << wholeIndex << "frameType:" << modifyFrameFileUnitInfo.frameType
             << "frameSteer:" << modifyFrameFileUnitInfo.frameSteer
             << "startFrameIndex:" << modifyFrameFileUnitInfo.startFrameIndex
             << "endFrameIndex:" << modifyFrameFileUnitInfo.endFrameIndex;
#endif
}

bool FileFrameInfoMapper::saveToFile(const QString& imageFilePath, const QString& indexFilePath, const int startIndex,
                                     const int endIndex)
{
    QFile file(imageFilePath);
    if (file.open(QFile::ReadWrite))
    {
        if (m_FrameFileHead.frameHeadInfo.frameCount == 0)
        {
            file.close();
            return true;
        }
        Q_ASSERT(startIndex >= 0);
        Q_ASSERT(endIndex >= 0);
#ifdef ENABLE_FRAME_FILE_DEBUG
        for (int i = startIndex; i <= endIndex; ++i)
        {
            const struct FrameFileUnit& ffu = m_FrameUnits[i];
            foreach (const struct FrameFileUnitInfo& ffui, ffu.frameUnits)
            {
                qDebug() << PRETTY_FUNCTION << "i:" << i << "frameType:" << ffui.frameType
                         << "startFrameIndex:" << ffui.startFrameIndex << "endFrameIndex:" << ffui.endFrameIndex;
            }
        }
#endif
        QHash<int, QHash<int, int>> frameWholeIndex;
        QVector<struct FrameFileUnit> frameUnits = QVector<struct FrameFileUnit>(endIndex - startIndex + 1);
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << __PRETTY_FUNCTION__ << "imageFilePath:" << imageFilePath << "indexFilePath:" << indexFilePath
                 << "saveCount:" << frameUnits.count() << "startIndex:" << startIndex << "endIndex:" << endIndex;
#endif
        /*头结构信息*/
        struct FrameFileHead frameFileHead;
        frameFileHead.frameHeadInfo = m_FrameFileHead.frameHeadInfo;
        frameFileHead.frameInfoCounts = m_FrameFileHead.frameInfoCounts;
        frameFileHead.frameHeadInfo = m_FrameFileHead.frameHeadInfo;
        frameFileHead.frameHeadInfo.frameCount = frameUnits.size();
        frameFileHead.frameHeadInfo.frameFileBeginIndex = 0;
        frameFileHead.frameHeadInfo.startFrameOffset = 0;
        // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
        for (struct FrameFileHeadInfo frameFileHeadInfo : qAsConst(m_FrameFileHead.frameInfos))
        {
            if (frameFileHeadInfo.frameType == ImageBufferDef::FourD_Data)
            {
                frameFileHeadInfo.startFrameOffset = 0;
            }
            else
            {
                frameFileHeadInfo.startFrameOffset = startFrameOffset(frameFileHeadInfo.frameType, startIndex);
            }
            frameFileHead.frameInfos.insert(frameFileHeadInfo.frameType, frameFileHeadInfo);
#ifdef ENABLE_FRAME_FILE_DEBUG
            qDebug() << __PRETTY_FUNCTION__ << "frameType:" << frameFileHeadInfo.frameType
                     << "startFrameOffset:" << frameFileHeadInfo.startFrameOffset;
#endif
        }
        QHash<int, int> startWholeIndex;
        int wholeIndex =
            saveCompoundStartFrames(frameWholeIndex, frameUnits, frameFileHead, startIndex, endIndex, startWholeIndex);
        //        const struct FrameFileUnit &firstFileUnit = frameUnits[0];
        //        const struct FrameFileUnit &oldFirstFileUnit = frameFileUnit(startIndex);
        for (int i = wholeIndex, j = wholeIndex - 1; i < frameUnits.count(); i++)
        {
            const struct FrameFileUnit& prevFileUnit = frameUnits[i - 1];
            FrameFileUnit oldFileUnit = frameFileUnit(startIndex + i - 1);
            struct FrameFileUnit fileUnit = frameFileUnit(startIndex + i);
            struct FrameFileUnit initfileUnit = fileUnit;
            // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
            // 绑定到临时对象，减少遍历时临时拷贝kyes
            const auto& keys = fileUnit.frameUnits.keys();
            for (const int key : keys)
            {
                //如果当前整帧索引小于子数据compand最后一帧最应的整帧索引，那么其FrameFileUnitInfo保持与最后一帧compand的FrameFileUnitInfo一致
                int typeStartWholeIndex = qMax(startWholeIndex[key], startIndex);
                if (startIndex + i <= typeStartWholeIndex)
                {
                    fileUnit.frameUnits[key] = frameUnits[j].frameUnits[key];
                }
                else
                {
                    int indexCommonDiff = frameUnits[0].frameUnits[key].startFrameIndex -
                                          frameFileUnit(startIndex).frameUnits[key].startFrameIndex;
                    modifyInfoAtIndex(fileUnit, frameWholeIndex, wholeIndex, key, prevFileUnit, oldFileUnit,
                                      indexCommonDiff);
                }

                //                    qCritical()<<"wholeIndex"<<wholeIndex
                //                               <<"frameSteer"<<fileUnit.frameUnits[1].frameSteer
                //                               <<"startIndex"<<fileUnit.frameUnits[1].startFrameIndex
                //                               <<"endIndex"<<fileUnit.frameUnits[1].endFrameIndex
                //                               <<"---------"
                //                               <<"initstartIndex"<<initfileUnit.frameUnits[1].startFrameIndex
                //                               <<"initendIndex"<<initfileUnit.frameUnits[1].endFrameIndex
                //                               <<"---------"
                //                               <<"oldstartIndex"<<oldFileUnit.frameUnits[1].startFrameIndex
                //                               <<"oldendIndex"<<oldFileUnit.frameUnits[1].endFrameIndex
                //                               <<"-------"
                //                               <<"prestartIndex"<<prevFileUnit.frameUnits[1].startFrameIndex
                //                               <<"preendIndex"<<prevFileUnit.frameUnits[1].endFrameIndex
                //                               <<"-------"
                //                               <<"startWholeIndex"<<startWholeIndex[1]
                //                               <<"-------"
                //                               <<"indexCommonDiff"<<frameUnits[0].frameUnits[key].startFrameIndex -
                //                               frameFileUnit(startIndex).frameUnits[key].startFrameIndex;
            }
            frameUnits[wholeIndex] = fileUnit;
            wholeIndex++;
        }
        // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
        // 绑定到临时对象，减少遍历时临时拷贝kyes
        const auto& keys = m_FrameFileHead.frameInfos.keys();
        for (const int key : keys)
        {
            frameFileHead.frameInfos[key].frameCount =
                frameUnits[frameUnits.count() - 1].frameUnits[frameFileHead.frameInfos[key].frameType].endFrameIndex +
                1;
#ifdef ENABLE_FRAME_FILE_DEBUG
            qDebug() << __PRETTY_FUNCTION__ << "frameType:" << frameFileHead.frameInfos[key].frameType
                     << "frameCount:" << frameFileHead.frameInfos[key].frameCount;
#endif
        }

#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << "frameUnits.count:" << frameUnits.count()
                 << "frameCount:" << frameFileHead.frameHeadInfo.frameCount;
        foreach (struct FrameFileHeadInfo frameFileHeadInfo, frameFileHead.frameInfos)
        {
            for (int i = 0; i < frameFileHeadInfo.frameCount; ++i)
            {
                qDebug() << __PRETTY_FUNCTION__ << "frameType:" << frameFileHeadInfo.frameType << "i:" << i
                         << "frameIndex" << frameWholeIndex[frameFileHeadInfo.frameType][i];
            }
        }
#endif
        return saveFile(file, frameFileHead, frameUnits, indexFilePath, frameWholeIndex);
    }
    return false;
}

bool FileFrameInfoMapper::forwardSaveToFile(const QString& imageFilePath, const QString& indexFilePath,
                                            const int startIndex, const int endIndex)
{
    QFile file(imageFilePath);
    bool copyFlag = true;

    if (file.open(QFile::ReadWrite))
    {
        if (m_FrameFileHead.frameHeadInfo.frameCount == 0)
        {
            file.close();
            return true;
        }
        Q_ASSERT(startIndex >= 0);
        Q_ASSERT(endIndex >= 0);

        QHash<int, QHash<int, int>> frameWholeIndex;
        QVector<struct FrameFileUnit> frameUnits = QVector<struct FrameFileUnit>(endIndex - startIndex + 1);
        int wholeIndex = 0;
        struct FrameFileUnit firstFrameFileUnit = frameFileUnit(startIndex);
        // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历，使用const auto&
        // 绑定到临时对象，减少遍历时临时拷贝kyes
        const auto& keys = firstFrameFileUnit.frameUnits.keys();
        for (int key : keys)
        {
            frameWholeIndex.insert(key, QHash<int, int>());
        }
        int totalSaveCount = frameUnits.count();
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << "imageFilePath:" << imageFilePath << "indexFilePath:" << indexFilePath
                 << "startIndex:" << startIndex << "endIndex:" << endIndex << "totalSaveCount: << totalSaveCount";
#endif
        // BM模式时，保证电影文件起始位置有足够compound数量的前置B帧，这些前置B帧的帧索引从0开始递增。后续帧的B帧索引在超过最后一帧前置B帧的索引之前，维持最后一帧前置B帧的帧索引。
        // 例如，当compound为3时(scpd=2)，原本的B帧索引: 0 0 0 0 1 1 1 1 2 2 2 2 3 3 3 3 ...
        // 调整后：                                   0 1 2 2 2 2 2 2 2 2 2 2 3 3 3 3 ...
        // 其中前面3帧作为前置B帧
        //        int preBCount = 0;      // 前置B帧数量
        int pre2DCount = 0;            // 前置2D帧数量
                                       //        int lastBIndex = -1;    // 最近的B帧索引
        QHash<int, int> last2DIndexes; // 最近的2D帧索引

        qint64 lastFrameTimeStamp = m_StatrFrameTimeStamp;
        for (int i = endIndex; i >= startIndex; --i)
        {
            struct FrameFileUnit fileUnit = frameFileUnit(i);
            //实时向前存储电影，计算帧持续时间要用后一帧的时间戳减去前一帧的时间戳
            fileUnit.timeDelayMS = abs(lastFrameTimeStamp - m_FrameTimeStamps.value(i, 0));
            lastFrameTimeStamp = m_FrameTimeStamps.value(i, 0);
            QHash<int, FrameFileUnitInfo>::iterator iter;
            bool newPre2D = false;

            for (iter = fileUnit.frameUnits.begin(); iter != fileUnit.frameUnits.end(); iter++)
            {
                struct FrameFileUnitInfo& modifyFrameFileUnitInfo = iter.value();
                QHash<int, int>& childFrameWholeIndex = frameWholeIndex[modifyFrameFileUnitInfo.frameType];
                int last2DIndex = last2DIndexes.value(modifyFrameFileUnitInfo.frameType, -1);

                int tempIndex = modifyFrameFileUnitInfo.startFrameIndex;
                int frameTypeCount = frameCount(modifyFrameFileUnitInfo.frameType);

                //                if ((modifyFrameFileUnitInfo.frameType == ImageBufferDef::B_Data)
                if ((m_FrameFileHead.frameInfos[modifyFrameFileUnitInfo.frameType].frameUnitCount == 1) &&
                    (modifyFrameFileUnitInfo.frameType != ImageBufferDef::Sound_Data) &&
                    (fileUnit.frameUnits.contains(ImageBufferDef::M_Data) ||
                     fileUnit.frameUnits.contains(ImageBufferDef::D_Data)))
                {
                    int rawStartIndex = frameTypeCount - modifyFrameFileUnitInfo.endFrameIndex - 1;
                    int rawEndIndex = frameTypeCount - tempIndex - 1;

                    if (pre2DCount <= m_ScpdValue)
                    {
                        modifyFrameFileUnitInfo.startFrameIndex =
                            (rawStartIndex > last2DIndex) ? rawStartIndex : last2DIndex + 1;
                        modifyFrameFileUnitInfo.endFrameIndex = (rawEndIndex > modifyFrameFileUnitInfo.startFrameIndex)
                                                                    ? rawEndIndex
                                                                    : modifyFrameFileUnitInfo.startFrameIndex;

                        int index = m_FrameWholeIndex[modifyFrameFileUnitInfo.frameType].value(
                            frameTypeCount - modifyFrameFileUnitInfo.startFrameIndex - 1);
                        int steer = frameFileUnit(index).frameUnits[modifyFrameFileUnitInfo.frameType].frameSteer;

                        modifyFrameFileUnitInfo.frameSteer = steer;
                        newPre2D = true;
                    }
                    else
                    {
                        modifyFrameFileUnitInfo.startFrameIndex =
                            (rawStartIndex < last2DIndex) ? last2DIndex : rawStartIndex;
                        modifyFrameFileUnitInfo.endFrameIndex = (rawEndIndex < last2DIndex) ? last2DIndex : rawEndIndex;
                    }

                    //                    qDebug() << "forwardSaveToFile: B from" << rawStartIndex << rawEndIndex <<
                    //                    "to"
                    //                                << modifyFrameFileUnitInfo.startFrameIndex <<
                    //                                modifyFrameFileUnitInfo.endFrameIndex;

                    //                    lastBIndex = modifyFrameFileUnitInfo.endFrameIndex;
                    last2DIndexes[modifyFrameFileUnitInfo.frameType] = modifyFrameFileUnitInfo.endFrameIndex;
                }
                else
                {
                    modifyFrameFileUnitInfo.startFrameIndex =
                        frameTypeCount - modifyFrameFileUnitInfo.endFrameIndex - 1;
                    modifyFrameFileUnitInfo.endFrameIndex = frameTypeCount - tempIndex - 1;
                }

                Q_ASSERT(modifyFrameFileUnitInfo.startFrameIndex >= 0 && modifyFrameFileUnitInfo.endFrameIndex >= 0);
                for (int i = modifyFrameFileUnitInfo.startFrameIndex; i <= modifyFrameFileUnitInfo.endFrameIndex; i++)
                {
                    if (!childFrameWholeIndex.contains(i))
                    {
                        childFrameWholeIndex.insert(i, wholeIndex);
                    }
                }

#ifdef ENABLE_FRAME_FILE_DEBUG
                qDebug() << PRETTY_FUNCTION << "i:" << i << "frameType:" << modifyFrameFileUnitInfo.frameType
                         << "frameSteer:" << modifyFrameFileUnitInfo.frameSteer
                         << "startFrameIndex:" << modifyFrameFileUnitInfo.startFrameIndex
                         << "endFrameIndex:" << modifyFrameFileUnitInfo.endFrameIndex << "totalSaveCount"
                         << totalSaveCount;
#else
                Q_UNUSED(totalSaveCount);
#endif
            }

            if (newPre2D)
            {
                pre2DCount++;
            }

            //            qDebug() << "forwardSaveToFile: Add: B" << fileUnit.frameUnits[1].startFrameIndex <<
            //            fileUnit.frameUnits[1].endFrameIndex
            //                       << "M" << fileUnit.frameUnits[8].startFrameIndex <<
            //                       fileUnit.frameUnits[8].endFrameIndex;
            frameUnits[wholeIndex++] = fileUnit;
        }
#ifdef ENABLE_FRAME_FILE_DEBUG
        FrameFileUnitInfo ffui = frameUnits[0].frameUnits.value(1);
        qDebug() << PRETTY_FUNCTION << "frameSteer:" << ffui.frameSteer << "startFrameIndex:" << ffui.startFrameIndex
                 << "endFrameIndex:" << ffui.endFrameIndex;
#endif
        /*修改头结构信息*/
        struct FrameFileHead frameFileHead;
        frameFileHead.frameHeadInfo = m_FrameFileHead.frameHeadInfo;
        frameFileHead.frameInfoCounts = m_FrameFileHead.frameInfoCounts;
        frameFileHead.frameHeadInfo = m_FrameFileHead.frameHeadInfo;
        frameFileHead.frameHeadInfo.frameCount = frameUnits.size();
        frameFileHead.frameHeadInfo.frameFileBeginIndex = 0;
        // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
        for (struct FrameFileHeadInfo frameFileHeadInfo : qAsConst(m_FrameFileHead.frameInfos))
        {
            frameFileHeadInfo.startFrameOffset = startFrameOffset(frameFileHeadInfo.frameType, startIndex);
            frameFileHead.frameInfos.insert(frameFileHeadInfo.frameType, frameFileHeadInfo);
#ifdef ENABLE_FRAME_FILE_DEBUG
            qDebug() << PRETTY_FUNCTION << "frameFileHeadInfo.frameType:" << frameFileHeadInfo.frameType
                     << "frameCount:" << frameFileHeadInfo.frameCount;
#endif
        }

#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << "frameUnits.count:" << frameUnits.count()
                 << "frameCount:" << frameFileHead.frameHeadInfo.frameCount;
#endif

        quint64 fileSize = calcFrameFileSize(frameFileHead);
        file.resize(fileSize);
        uchar* map = file.map(0, fileSize);
        saveFrameFile(map, frameFileHead, frameUnits);
        file.unmap(map);
        file.close();

        QFile indexFile(indexFilePath);
        if (indexFile.open(QFile::WriteOnly))
        {
            QDataStream stream(&indexFile);
            stream << frameWholeIndex;
            indexFile.close();
            return copyFlag;
        }
    }
    return false;
}

bool FileFrameInfoMapper::saveCurrentToFile(const QString& filePath, const int index, const int frameAvgCount)
{
    QFile file(filePath);
    bool copyFlag = true;

    if (file.open(QFile::ReadWrite))
    {
        if (m_FrameFileHead.frameHeadInfo.frameCount == 0)
        {
            file.close();
            return true;
        }
        Q_ASSERT(index >= 0);

        //存储帧数最大不能超过当前帧前面包含的帧数和B的最大帧数
        int saveFrameCount = m_ScpdValue + frameAvgCount + 1;
        {
            int maxAllowSaveCount = index + 1;
            // m_FrameTypes参数在回调的时候没有维护，所以需要使用m_FrameFileHead
            if (m_FrameFileHead.frameInfos.contains(ImageBufferDef::B_Data))
            {
                //取当前索引对应的b数据的最大帧数
                maxAllowSaveCount = qMin(frameFileUnitInfo(ImageBufferDef::B_Data, index).endFrameIndex + 1, index + 1);
            }
            saveFrameCount = qMin(saveFrameCount, maxAllowSaveCount);
        }
        struct FrameFileUnit firstSave2DImageFrameFileUnit = frameFileUnit(index, m_SaveUseFrontIndex);
#ifdef ENABLE_FRAME_FILE_DEBUG
        qDebug() << PRETTY_FUNCTION << "index:" << index << "m_ScpdValue:" << m_ScpdValue
                 << "saveFrameCount:" << saveFrameCount << "maxFrameCount:" << maxFrameCount()
                 << "frameCount:" << frameCount() << "m_FrameUnits.count():" << m_FrameUnits.count();
        foreach (const int key, firstSave2DImageFrameFileUnit.frameUnits.keys())
        {
            qDebug() << PRETTY_FUNCTION << "frameType:" << firstSave2DImageFrameFileUnit.frameUnits[key].frameType
                     << "startFrameIndex:" << firstSave2DImageFrameFileUnit.frameUnits[key].startFrameIndex
                     << "endFrameIndex:" << firstSave2DImageFrameFileUnit.frameUnits[key].endFrameIndex;
        }
        for (int i = 0; i < m_FrameUnits.count(); ++i)
        {
            foreach (struct FrameFileHeadInfo frameFileHeadInfo, m_FrameFileHead.frameInfos)
            {
                qDebug() << PRETTY_FUNCTION << "i:" << i << "frameType:" << frameFileHeadInfo.frameType
                         << "start:" << m_FrameUnits[i].frameUnits[frameFileHeadInfo.frameType].startFrameIndex
                         << "end:" << m_FrameUnits[i].frameUnits[frameFileHeadInfo.frameType].endFrameIndex
                         << "sterring:" << m_FrameUnits[i].frameUnits[frameFileHeadInfo.frameType].frameSteer;
            }
        }
#endif

        QVector<struct FrameFileUnit> frameUnits = QVector<struct FrameFileUnit>(saveFrameCount);
        int currentMaxFrameCount = maxFrameCount();
        for (int i = 0; i < saveFrameCount; i++)
        {
            int frameInterval = saveFrameCount - i - 1;
            int realIndex = (currentMaxFrameCount + index - frameInterval) % currentMaxFrameCount;
#ifdef ENABLE_FRAME_FILE_DEBUG
            qDebug() << PRETTY_FUNCTION << "i:" << i << "realIndex:" << realIndex;
#endif
            frameUnits[i] = frameFileUnit(realIndex, m_SaveUseFrontIndex);
            frameUnits[i].frameIndex = i;
            // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
            for (struct FrameFileHeadInfo frameFileHeadInfo : qAsConst(m_FrameFileHead.frameInfos))
            {
                struct FrameFileUnitInfo frameFileUnitInfo =
                    frameUnits[i].frameUnits.value(frameFileHeadInfo.frameType);
#ifdef ENABLE_FRAME_FILE_DEBUG
                qDebug() << PRETTY_FUNCTION << "@1 old info:"
                         << "frameType:" << frameFileUnitInfo.frameType << "frameSteer:" << frameFileUnitInfo.frameSteer
                         << "startFrameIndex:" << frameFileUnitInfo.startFrameIndex
                         << "endFrameIndex:" << frameFileUnitInfo.endFrameIndex
                         << "frameUnitCount:" << frameFileHeadInfo.frameUnitCount;
#endif
                if (frameFileHeadInfo.frameType == ImageBufferDef::FourD_Data)
                {
                    frameFileUnitInfo.endFrameIndex =
                        frameFileUnitInfo.endFrameIndex >= frameFileUnitInfo.startFrameIndex
                            ? (frameFileUnitInfo.endFrameIndex - frameFileUnitInfo.startFrameIndex)
                            : (frameFileUnitInfo.endFrameIndex + frameCount(frameFileHeadInfo.frameType) -
                               frameFileUnitInfo.startFrameIndex);
                    frameFileUnitInfo.startFrameIndex = 0;
                }
                else
                {
                    if (frameFileHeadInfo.frameUnitCount == 1)
                    {
                        // m_FrameWholeIndex为空说明当前是单帧下存储单帧，因为没有子帧和整帧的映射关系，因此直接取就行
                        if (m_FrameWholeIndex.isEmpty())
                        {
                            frameFileUnitInfo.frameSteer = frameFileUnit(i, m_SaveUseFrontIndex)
                                                               .frameUnits.value(frameFileHeadInfo.frameType)
                                                               .frameSteer;
                        }
                        else
                        {
                            //同BUG81587错误的点一样，满帧之后image2DFrameIndex会取到意料之外的值，也就是负数，此时取出的frameindex就会出错，会导致偏转计算错误
                            int image2DFrameIndex = 0;
                            bool flag = false;
                            // if else解决上激活问题
                            if (isActive(frameFileHeadInfo.frameType) && frameFileHeadInfo.frameType == 1)
                            {
                                image2DFrameIndex =
                                    (firstSave2DImageFrameFileUnit.frameUnits[frameFileHeadInfo.frameType]
                                         .endFrameIndex -
                                     frameInterval + currentMaxFrameCount) %
                                    currentMaxFrameCount;
                            }
                            else
                            {
                                image2DFrameIndex = m_FrontIndex + frameInterval;
                                flag = true;
                            }

                            int frameIndex = m_FrameWholeIndex[frameFileHeadInfo.frameType].value(image2DFrameIndex);
                            const FrameFileUnitInfo curInfo = frameFileUnitAt(flag ? image2DFrameIndex : frameIndex)
                                                                  .frameUnits.value(frameFileHeadInfo.frameType);
                            frameFileUnitInfo.frameSteer = getCombineFrameSteerOnBit(curInfo, image2DFrameIndex);
#ifdef ENABLE_FRAME_FILE_DEBUG
                            qDebug() << PRETTY_FUNCTION << "2D"
                                     << "image2DFrameIndex:" << image2DFrameIndex << "startFrameIndex:"
                                     << (firstSave2DImageFrameFileUnit.frameUnits[frameFileHeadInfo.frameType]
                                             .startFrameIndex)
                                     << "i:" << i << "frameIndex:" << frameIndex;
#endif
                        }
#ifdef ENABLE_FRAME_FILE_DEBUG
                        qDebug() << PRETTY_FUNCTION << "2D"
                                 << "saveFrameCount:" << saveFrameCount
                                 << "frameCount:" << m_FrameFileHead.frameInfos[frameFileHeadInfo.frameType].frameCount
                                 << "frameFileUnitInfo.frameSteer:" << frameFileUnitInfo.frameSteer;
#endif
                        frameFileUnitInfo.startFrameIndex = i;
                        frameFileUnitInfo.endFrameIndex = i;
                    }
                    else
                    {
                        int offset = startFrameOffset(frameFileUnitInfo.frameType, index);
                        int startIndex = qMax(frameFileUnitInfo.startFrameIndex - offset - 1, 0);
                        int imageWaveFrameIndex = frameFileUnitInfo.endFrameIndex - startIndex;
                        frameFileUnitInfo.startFrameIndex = 0;
                        frameFileUnitInfo.endFrameIndex = imageWaveFrameIndex + 1 >= frameFileHeadInfo.frameUnitCount
                                                              ? frameFileHeadInfo.frameUnitCount - 1
                                                              : imageWaveFrameIndex;
#ifdef ENABLE_FRAME_FILE_DEBUG
                        qDebug() << PRETTY_FUNCTION << "Wave"
                                 << "imageWaveFrameIndex:" << imageWaveFrameIndex << "offset:" << offset
                                 << "startIndex:" << startIndex
                                 << "startFrameIndex:" << frameFileUnitInfo.startFrameIndex
                                 << "endFrameIndex:" << frameFileUnitInfo.endFrameIndex;
#endif
                    }
#ifdef ENABLE_FRAME_FILE_DEBUG
                    qDebug() << PRETTY_FUNCTION << "frameType:" << frameFileUnitInfo.frameType
                             << "frameSteer:" << frameFileUnitInfo.frameSteer
                             << "startFrameIndex:" << frameFileUnitInfo.startFrameIndex
                             << "endFrameIndex:" << frameFileUnitInfo.endFrameIndex;
#endif
                }
                frameUnits[i].frameUnits.insert(frameFileHeadInfo.frameType, frameFileUnitInfo);
            }
        }
        struct FrameFileHead frameFileHead;
        frameFileHead.frameHeadInfo = m_FrameFileHead.frameHeadInfo;
        frameFileHead.frameInfoCounts = m_FrameFileHead.frameInfoCounts;
        frameFileHead.frameHeadInfo = m_FrameFileHead.frameHeadInfo;
        frameFileHead.frameHeadInfo.startFrameOffset = 0;
        frameFileHead.frameHeadInfo.frameCount = saveFrameCount;
        frameFileHead.frameHeadInfo.frameFileBeginIndex = 0;
        // 2025-06-10 Modify by AlexWang 使用c++11的for...range遍历
        for (struct FrameFileHeadInfo frameFileHeadInfo : qAsConst(m_FrameFileHead.frameInfos))
        {
            struct FrameFileUnitInfo frameFileUnitInfo =
                frameUnits.last().frameUnits.value(frameFileHeadInfo.frameType);
            frameFileHeadInfo.frameCount = frameFileUnitInfo.endFrameIndex + 1;
            frameFileHeadInfo.startFrameOffset = 0;
            frameFileHeadInfo.frameFileBeginIndex = 0;
            frameFileHead.frameInfos.insert(frameFileHeadInfo.frameType, frameFileHeadInfo);
        }

        int fileSize = calcFrameFileSize(frameFileHead);
        file.resize(fileSize);
        uchar* map = file.map(0, fileSize);
        saveFrameFile(map, frameFileHead, frameUnits);
        file.unmap(map);
        file.close();
        return copyFlag;
    }
    return false;
}

bool FileFrameInfoMapper::saveFile(QFile& file, struct FrameFileHead& frameFileHead,
                                   QVector<struct FrameFileUnit>& frameUnits, const QString& indexFilePath,
                                   QHash<int, QHash<int, int>>& frameWholeIndex)
{
    quint64 fileSize = calcFrameFileSize(frameFileHead);
    file.resize(fileSize);
    uchar* map = file.map(0, fileSize);
    saveFrameFile(map, frameFileHead, frameUnits);
    file.unmap(map);
    file.close();

    QFile indexFile(indexFilePath);
    if (indexFile.open(QFile::WriteOnly))
    {
        QDataStream stream(&indexFile);
        stream << frameWholeIndex;
        indexFile.close();
        return true;
    }
    return false;
}

void FileFrameInfoMapper::setTotalFrameCount(int frameCount)
{
    m_FrameUnits.clear();
    m_FrameFileHead.frameHeadInfo.mapFrameTotalCount = frameCount;
    m_FrameUnits = QVector<struct FrameFileUnit>(m_FrameFileHead.frameHeadInfo.mapFrameTotalCount);
    clearFrameFileHead();
}

void FileFrameInfoMapper::setIsWirteEnable(bool isEnable)
{
    m_IsWirteEnable = isEnable;
}

bool FileFrameInfoMapper::isFileMapEnable() const
{
    return m_IsWirteEnable;
}

FileMapUnit* FileFrameInfoMapper::getFileMapUnit(const int mapUnitIndex, QFile& file)
{
    Q_UNUSED(mapUnitIndex);
    FileMapUnit* mapUnit = new FileMapUnit(m_ImageIndex, &file, 0, file.size(), 1, 0);
    CHECK_NEW(FileMapUnit, mapUnit);
    if (mapUnit && mapUnit->isValid())
    {
        m_ErrorCode = LoadErrorCode::Success;
    }
    else
    {
        mapUnit = NULL;
        m_ErrorCode = LoadErrorCode::Image_bin_MapError;
    }

    return mapUnit;
}

void FileFrameInfoMapper::load(FileMapUnit* fileMapUnit)
{
    // 2023-11-09 Write by AlexWang BUG:67414 m_CurrentIndex有潜在线程竞争
    QMutexLocker locker(&m_MutexCurrentIndex);
    loadFrameFile(fileMapUnit->mapValue(), m_FrameFileHead, m_FrameUnits);
    if (m_FrameFileHead.frameHeadInfo.frameCount == m_FrameFileHead.frameHeadInfo.mapFrameTotalCount)
    {
        m_IsQueueFull = true;
    }
    m_FrontIndex = m_FrameFileHead.frameHeadInfo.frameFileBeginIndex;
    m_CurrentIndex = (m_FrontIndex + frameCount() - 1) % maxFrameCount();
#ifdef ENABLE_FRAME_FILE_DEBUG
    qDebug() << PRETTY_FUNCTION << "front:" << m_FrontIndex << "current:" << m_CurrentIndex
             << "queuefull:" << m_IsQueueFull << "datasize:" << m_FrameUnits.size()
             << "frameCount:" << m_FrameFileHead.frameHeadInfo.frameCount
             << "frameSize:" << m_FrameFileHead.frameHeadInfo.frameSize
             << "totalFrameCount:" << m_FrameFileHead.frameHeadInfo.mapFrameTotalCount;
    foreach (const int type, m_FrameFileHead.frameInfos.keys())
    {
        const struct FrameFileHeadInfo& ffhi = m_FrameFileHead.frameInfos[type];
        qDebug() << __FUNCTION__ << "frameType:" << ffhi.frameType << "frameCount:" << ffhi.frameCount
                 << "frameUnitCount:" << ffhi.frameUnitCount << "startFrameOffset:" << ffhi.startFrameOffset;
    }
    for (int i = 0; i < m_FrameUnits.count(); ++i)
    {
        foreach (const int type, m_FrameUnits[i].frameUnits.keys())
        {
            const FrameFileUnitInfo& ffui = m_FrameUnits[i].frameUnits[type];
            qDebug() << __FUNCTION__ << "i:" << i << "frameType:" << ffui.frameType << "\t"
                     << "frameSteer:" << ffui.frameSteer << "\t"
                     << "startFrameIndex:" << ffui.startFrameIndex << "\t"
                     << "endFrameIndex:" << ffui.endFrameIndex;
        }
    }
#endif
}

void FileFrameInfoMapper::initFrameFileHead(const FrameFileHeadInfo& frameFileHeadInfo)
{
    m_FrameFileHead = defaultFrameFileHead();
    m_FrameFileHead.frameInfoCounts = m_FrameTypes.size();
    m_FrameFileHead.frameHeadInfo = frameFileHeadInfo;
    m_FrameUnits = QVector<struct FrameFileUnit>(m_FrameFileHead.frameHeadInfo.mapFrameTotalCount);
    clearFrameFileHead();
}

void FileFrameInfoMapper::clearFrameFileHead()
{
    m_FrameTimeStamps.clear();
    m_FrameFileHead.frameHeadInfo.frameCount = 0;
    m_FrameFileHead.frameHeadInfo.frameFileBeginIndex = 0;
    m_FrameFileHead.frameHeadInfo.startFrameOffset = 0;
    for (QHash<int, struct FrameFileHeadInfo>::iterator iter = m_FrameFileHead.frameInfos.begin();
         iter != m_FrameFileHead.frameInfos.end(); iter++)
    {
        struct FrameFileHeadInfo& frameFileHeadInfo = iter.value();
        frameFileHeadInfo.frameCount = 0;
        frameFileHeadInfo.frameFileBeginIndex = 0;
        frameFileHeadInfo.startFrameOffset = 0;
    }
}

int FileFrameInfoMapper::lastPaintFrameIndex()
{
    QMutexLocker locker(&m_LastPaintFrameIndexMutex);

    return m_LastPaintFrameIndex;
}

void FileFrameInfoMapper::setLastPaintFrameIndex(int frameIndex)
{
    QMutexLocker locker(&m_LastPaintFrameIndexMutex);

    //    qDebug() << PRETTY_FUNCTION << "frameIndex: " << frameIndex;

    m_LastPaintFrameIndex = frameIndex;
}

qint64 FileFrameInfoMapper::getFramestampByIndex(int frameIndex) const
{
    return m_FrameTimeStamps.contains(frameIndex) ? m_FrameTimeStamps.value(frameIndex) : 0;
}

int FileFrameInfoMapper::getFrameIndexByFrameTimestamp(const qint64& timestamp) const
{
    return m_FrameTimeStamps.key(timestamp);
}

void FileFrameInfoMapper::setStartFrameTimeStamp(const qint64& timestamp, bool updateFrameTimeStampList)
{
    m_StatrFrameTimeStamp = timestamp;
    if (updateFrameTimeStampList)
    {
        qint64 durationTime = m_StatrFrameTimeStamp;
        for (int i = 0; i < m_FrameFileHead.frameHeadInfo.frameCount; i++)
        {
            durationTime += m_FrameUnits[i].timeDelayMS;
            m_FrameTimeStamps[i] = durationTime;
        }
    }
}

const qint64& FileFrameInfoMapper::getStartFrameTimeStamp() const
{
    return m_StatrFrameTimeStamp;
}

} // namespace FrameFile
