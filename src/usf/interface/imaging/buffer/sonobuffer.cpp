#include "sonobuffer.h"
#include "assertlog.h"
#include "bfadfreqparameter.h"
#include "bfdepthparameters.h"
#include "bfpnames.h"
#include "cleartriggersource.h"
#include "imagebuffergroup.h"
#include "imageeventargs.h"
#include "imagesavehelper.h"
#include "infostruct.h"
#include "irawimagebufferdatasetter.h"
#include "lineimagebufferhelper.h"
#include "logger.h"
#include "memoryleakcheck.h"
#include "modelconfig.h"
#include "multiparamtrigger.h"
#include "parameter.h"
#include "postparametersync.h"
#include "probedataset.h"
#include "setting.h"
#include "settingitem.h"
#include "sonobufferstorer.h"
#include "sonoparameters.h"
#include "sonoparameterschangedsynchronizer.h"
#include "systemscanmodeclassifier.h"
#include "sonobufferimagesavehelper.h"
#include "util.h"
#include <QDir>
#include <QImage>
#include <QSize>

static const int DEFAULT_STABLE_DELAY_MS = 500;
const int DEFAULT_CINE_COUNT = 256;
LOG4QT_DECLARE_STATIC_LOGGER(log, SonoBuffer)

SonoBuffer::SonoBuffer(int index, IImageSaveHelper* imageSaveHelper, QObject* parent)
    : ISonoBuffer(parent)
    , m_GlobalSonoParameters(NULL)
    , m_FreezeSonoParameters(NULL)
    , m_SonoParameters(NULL)
    , m_LoadMemory(new uchar[1024 * 1024 * 10], 1024 * 1024 * 10)
    , m_ForceFlushTimer(new QTimer)
    , m_SonoBufferIndex(index)
    , m_IsLoaded(false)
    , m_IsStressEcho(false)
    , m_IsActive(false)
    , m_IsFrozen(false)
    , m_IsPlayCine(false)
    , m_IsBufferCleared(false)
    , m_IsPaused(false)
    , m_SaveCineFinished(false)
    , m_IsRestore(false)
    , m_ClearTrigger(NULL)
    , m_Buffer(new ImageBufferGroup(index, /*Setting::instance().defaults().cineCacheSize()*/ DEFAULT_CINE_COUNT,
                                    imageSaveHelper, this))
    , m_ProcessedBuffer(NULL)
    , m_UseProcessedBuffer(false)
    , m_UseProcessedBufferOnLoad(false)
    , m_LoadBuffer(NULL)
    , m_Storer(new SonoBufferStorer())
    , m_StressEchoBuffer(NULL)
    , m_StressEchoStorer(NULL)
    , m_SyncParameters(new SonoParametersChangedSynchronizer(GstreamerAndSonoBufferSyncSource::syncSource()))
    , m_SystemScanModeChanging(false)
    , m_RealTimeSaveSonoParameters(NULL)
    , m_IsPrepareRestore(false)
    , m_IsOnSetPreset(false)
    , m_IsNeedleMode(false)
    , m_postRawDataManager(new PostRawDataManager)
    , m_UnActiveRenderPartition(Partition_None)
    , m_ImageSaveHelper(new SonobufferImageSaveHelper(imageSaveHelper))
{
    qRegisterMetaType<QHash<int, int>>("QHash<int,int>");
    connectConnections(m_Buffer);
    CHECK_NEW(ImageBufferGroup, m_Buffer);
    CHECK_NEW(SonoBufferStorer, m_Storer);
    CHECK_NEW(SonoParametersChangedSynchronizer, m_SyncParameters);

    connect(this, SIGNAL(startForceFlushTimer()), m_ForceFlushTimer, SLOT(start()));
    connect(this, SIGNAL(stopForceFlushTimer()), m_ForceFlushTimer, SLOT(stop()));
    connect(m_ForceFlushTimer, SIGNAL(timeout()), this, SLOT(onForceFlushTimeout()));
    m_ForceFlushTimer->setInterval(300);

    m_FrameAvgList << BFPNames::FrameAvgStr
                   << BFPNames::FrameAvgColorStr
                   //                    << BFPNames::FrameAvgPDStr
                   << BFPNames::FrameAvgTDIStr << BFPNames::FrameAvgSNStr << BFPNames::FrameAvgMVIStr;
}

SonoBuffer::~SonoBuffer()
{
    beforeSonoParametersChanged();
    // clearSonoParameters();
    CHECK_DELETE(ImageBufferGroup, m_Buffer);
    delete m_Buffer;
    destoryRealTimeRGBAImages();
    destoryLoadRGBAImages();
    if (m_ProcessedBuffer != NULL)
    {
        CHECK_DELETE(ImageBufferGroup, m_ProcessedBuffer);
        delete m_ProcessedBuffer;
        m_ProcessedBuffer = NULL;
    }
    if (m_LoadBuffer != NULL)
    {
        CHECK_DELETE(ImageBufferGroup, m_LoadBuffer);
        delete m_LoadBuffer;
        m_LoadBuffer = NULL;
    }
    CHECK_DELETE(SonoBufferStorer, m_Storer);
    delete m_Storer;
    m_Storer = NULL;
    if (m_StressEchoBuffer != NULL)
    {
        CHECK_DELETE(ImageBufferGroup, m_StressEchoBuffer);
        delete m_StressEchoBuffer;
        m_StressEchoBuffer = NULL;
    }
    if (m_StressEchoStorer != NULL)
    {
        CHECK_DELETE(SonoBufferStorer, m_StressEchoStorer);
        delete m_StressEchoStorer;
        m_StressEchoStorer = NULL;
    }
    if (m_ClearTrigger != NULL)
    {
        CHECK_DELETE(MultiParamTrigger, m_ClearTrigger);
        delete m_ClearTrigger;
        m_ClearTrigger = NULL;
    }

    if (m_FreezeSonoParameters != NULL && m_FreezeSonoParameters != m_GlobalSonoParameters)
    {
        CHECK_DELETE(SonoParameters, m_FreezeSonoParameters);
        delete m_FreezeSonoParameters;
        m_FreezeSonoParameters = NULL;
    }

    if (m_SyncParameters != NULL)
    {
        CHECK_DELETE(SonoParametersChangedSynchronizer, m_SyncParameters);
        delete m_SyncParameters;
        m_SyncParameters = NULL;
    }
    if (m_LoadMemory.data() != NULL)
    {
        delete[] m_LoadMemory.data();
        m_LoadMemory.clear();
    }
    if (m_ForceFlushTimer->isActive())
    {
        m_ForceFlushTimer->stop();
    }
    delete m_ForceFlushTimer;
    m_ForceFlushTimer = NULL;

    if (m_postRawDataManager != nullptr)
    {
        delete m_postRawDataManager;
        m_postRawDataManager = nullptr;
    }

    if (m_ImageSaveHelper != nullptr)
    {
        delete m_ImageSaveHelper;
        m_ImageSaveHelper = nullptr;
    }
}

QString SonoBuffer::bufferName() const
{
    return QString("SonoBuffer_%1").arg(m_SonoBufferIndex);
}

IRawImageBufferDataSetter* SonoBuffer::dataSetter()
{
    return m_Buffer;
}

ImageBufferGroup* SonoBuffer::dataBuffer()
{
    return m_Buffer;
}

PostRawDataManager* SonoBuffer::postRawDataManager()
{
    return m_postRawDataManager;
}

QList<int> SonoBuffer::createNewLineImageBuffer(const QList<FrameUnitInfo>& frameGroupInfo, const int bufferSize)
{
    ASSERT_LOG(m_ProcessedBuffer == NULL);
    if (m_ProcessedBuffer == NULL)
    {
        m_ProcessedBuffer = new ImageBufferGroup(m_SonoBufferIndex, bufferSize, m_ImageSaveHelper->imageSaveHelper());
        m_ProcessedBuffer->setFixedQueueSize(bufferSize);
        m_ProcessedBuffer->setIsFileMapEnable(m_Buffer->isFileMapEnable());
        m_Memory.setUsedLen(0);
        CHECK_NEW(ImageBufferGroup, m_ProcessedBuffer);
    }
    m_ProcessedBuffer->setMemory(m_Memory);
    m_ProcessedBuffer->setScpdValue(0);
    m_ProcessedBuffer->setFrameGroupInfo(frameGroupInfo);
    m_ProcessedBuffer->setWriteAble(true);
    m_Buffer->setUsedSize(m_ProcessedBuffer->memSizeInfo().usedMemLen);
    m_Buffer->clear();
    m_Buffer->constructMem();

    QList<int> retIndexs;
    for (int i = 0; i < m_ProcessedBuffer->displayBufferCount(); i++)
    {
        retIndexs << i;
    }

    return retIndexs;
}

void SonoBuffer::clearProcessLineImageBuffer()
{
    if (m_ProcessedBuffer != NULL)
    {
        m_ProcessedBuffer->clear();
    }
}

void SonoBuffer::changeLineImageBuffer(const QList<int> indexs)
{
    IBufferWriteLocker locker(this, PRETTY_FUNCTION);
    qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex << "indexes:" << indexs;
    if (m_ProcessedBuffer != NULL)
    {
        m_UseProcessedBuffer = true;
        if (m_IsLoaded)
        {
            m_UseProcessedBufferOnLoad = true;
        }
        disconnectConnections(m_Buffer);
        connectConnections(m_ProcessedBuffer);
        emit lineImageBufferChanged();
        m_ProcessedBuffer->updateFrameTypes(true);
        m_ProcessedBuffer->setWriteAble(false);
    }
}

void SonoBuffer::destoryLineImageBuffer(const QList<int> indexs)
{
    IBufferWriteLocker locker(this, PRETTY_FUNCTION);
    qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex << "indexes:" << indexs;
    if (m_ProcessedBuffer != NULL)
    {
        m_UseProcessedBuffer = false;
        m_UseProcessedBufferOnLoad = false;
        disconnectConnections(m_ProcessedBuffer);
        connectConnections(m_Buffer);
        CHECK_DELETE(ImageBufferGroup, m_ProcessedBuffer);
        m_Buffer->setUsedSize(m_Memory.len() - m_ProcessedBuffer->memSizeInfo().usedMemLen);
        delete m_ProcessedBuffer;
        m_ProcessedBuffer = NULL;
        emit lineImageBufferChanged();
        m_Buffer->updateFrameTypes();
    }
}

void SonoBuffer::enqueue(BufferUnit* bufferUnit, const int imageBufferIndex)
{
    if (m_ProcessedBuffer != NULL)
    {
        BufferUnit& frame = m_ProcessedBuffer->nextRear(imageBufferIndex);

        frame.copyFrom(*bufferUnit);
        m_ProcessedBuffer->updateNextRearStartTime(imageBufferIndex);
        m_ProcessedBuffer->enqueue(imageBufferIndex, true);
    }
}

int SonoBuffer::bufferCount()
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->displayBufferCount();
}

int SonoBuffer::startIndex(bool toShowValue)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->startIndex(toShowValue);
}

int SonoBuffer::startIndex(const int bufferIndex, const int index)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->startIndex(bufferIndex, index);
}

int SonoBuffer::endIndex(bool toShowValue)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->endIndex(toShowValue);
}

int SonoBuffer::endIndex(const int bufferIndex, const int index)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->endIndex(bufferIndex, index);
}

int SonoBuffer::currentIndex(const int bufferIndex, const int index)
{
    //    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->currentIndex(bufferIndex, index);
}

int SonoBuffer::indexDuration(int frameType, const int index)
{
    QList<BufferUnit> bufferUnits;
    FrameUnitInfo frameUnitInfo;
    int duration = 0;
    getBufferByIndex(bufferUnits, frameUnitInfo, frameType, index);

    if (bufferUnits.size() > 0)
    {
        return bufferUnits.at(0).duration();
    }

    return duration;
}

QByteArray SonoBuffer::getWholeFrameData(int frameType, const int index)
{
    QList<BufferUnit> bufferUnits;
    FrameUnitInfo frameUnitInfo;
    getBufferByIndex(bufferUnits, frameUnitInfo, frameType, index);
    BufferUnit unit = bufferUnits.last();
    ByteBuffer byteBuffer = unit.last();
    QByteArray data(byteBuffer.len() / 2, Qt::Uninitialized);
    for (int i = 0; i < byteBuffer.len() / 2; i++)
    {
        data[i] = byteBuffer[2 * i];
    }

    return data;
}

void SonoBuffer::setPictureImage(QImage image)
{
    m_ImageSaveHelper->setPictureImageData(image);

    if (m_SonoParameters->pBV(BFPNames::PictureModeONStr))
    {
        return;
    }

    bool suppprtPicture =
        !m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr) &&
        !m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr) &&
        !m_SonoParameters->pBV(BFPNames::IsMLineVisibleStr) &&
        SystemScanModeClassifier::isSuppprtPicture(m_SonoParameters->pIV(BFPNames::SystemScanModeStr));

    if (suppprtPicture)
    {
        m_SonoParameters->setPV(BFPNames::PictureModeONStr, true);
    }
}

QImage SonoBuffer::pictureImage()
{
    return m_ImageSaveHelper->PictureImageData();
}

int SonoBuffer::currentIndex(bool toShowValue)
{
    //    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->currentIndex(toShowValue);
}

int SonoBuffer::frameCount()
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->frameCount();
}

int SonoBuffer::frameCount(const int bufferIndex)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->frameCount(bufferIndex);
}
int SonoBuffer::frameCountByFrameType(int frameType)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->frameCountByFrameType(frameType);
}
void SonoBuffer::onRequestFlushFrameByIndex(const int bufferIndex, const int index)
{
    if (m_IsPaused)
    {
        return;
    }
    currentImageBufferGroup()->onRequestFlushFrameByIndex(bufferIndex, index);
}

void SonoBuffer::onRequestClearCache(const int bufferIndex, const int index)
{
    if (m_IsPaused)
    {
        return;
    }
    currentImageBufferGroup()->onRequestClearCache(bufferIndex, index);
}

void SonoBuffer::setPlayCineStatus(bool value)
{
    {
        IBufferReadLocker locker(this, PRETTY_FUNCTION);
        currentImageBufferGroup()->setPlayCineStatus(value);
    }
    emit playCineStatusChanged(value);
}

int SonoBuffer::cineBufferIndex()
{
    return m_SonoBufferIndex;
}

bool SonoBuffer::isCineActive(const int bufferIndex)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->isCineActive(bufferIndex);
}

bool SonoBuffer::isCineFpsCanModify(const int bufferIndex)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->isCineFpsCanModify(bufferIndex);
}

bool SonoBuffer::isStressEcho() const
{
    return m_IsStressEcho;
}

void SonoBuffer::setScpdValue(const int scpdValue)
{
    currentImageBufferGroup()->setScpdValue(scpdValue, m_IsNeedleMode);
}

int SonoBuffer::residueSize(const int imageBufferIndex)
{
    if (m_IsPaused)
    {
        return 0;
    }
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    if (m_SonoParameters->isRealTime() && !m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        currentImageBufferGroup()->resetResidueSize(imageBufferIndex);
    }
    return currentImageBufferGroup()->residueSize(imageBufferIndex);
}

bool SonoBuffer::isActive() const
{
    return m_IsActive;
}

void SonoBuffer::setActive(bool value)
{
    if (value == m_IsActive)
    {
        return;
    }

    m_IsActive = value;
    if (m_IsActive) //激活
    {
        if ((!m_IsFrozen) && (currentImageBufferGroup()->displayBufferCount() > 0))
        {
            emit playCineStatusChanged(true);
        }
        //非冻结,非加载
        if ((!m_IsFrozen) && (!m_IsLoaded))
        {
            if (m_SonoParameters != m_GlobalSonoParameters)
            {
                setSonoParameters(m_GlobalSonoParameters);
                m_Buffer->updateFrameTypes();
            }
        }
        else if (m_IsFrozen) //冻结状态下，不需要做任何事情
        {
        }
    }
    else if (!m_IsActive) //非激活
    {
        emit playCineStatusChanged(false);
        //冻结或是加载时，应该什么都不做
        //非加载,非冻结 状态下，拷贝超声参数到冻结参数，并设置使用冻结参数
        if ((!m_IsLoaded) && (!m_IsFrozen))
        {
            if (m_FreezeSonoParameters != NULL && m_FreezeSonoParameters != m_GlobalSonoParameters)
            {
                SonoParameters::clone(m_GlobalSonoParameters, m_FreezeSonoParameters);
            }
            else
            {
                m_FreezeSonoParameters = m_GlobalSonoParameters->clone(NULL);
                m_FreezeSonoParameters->setRecordState(true);
            }
            m_FreezeSonoParameters->setPV(BFPNames::FreezeStr, true);
            m_FreezeSonoParameters->setPV(BFPNames::ActiveBStr, m_SonoBufferIndex);
            setSonoParameters(m_FreezeSonoParameters);
            m_Buffer->updateFrameTypes();
        }
        //停止播放电影
        emit cineStopLoop(m_SonoBufferIndex);
    }
    IBufferWriteLocker locker(m_Buffer, PRETTY_FUNCTION);
    qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex;
    m_Buffer->setWriteAble(m_IsActive && !m_IsFrozen);
}

bool SonoBuffer::isFrozen() const
{
    return m_IsFrozen;
}
void SonoBuffer::setRestoreFlag(bool flag)
{
    m_IsRestore = flag;
}
void SonoBuffer::reStore()
{
    m_Buffer->updateFrameTypes();
    SyncModeType syncMode = (SyncModeType)m_SonoParameters->pIV(BFPNames::SyncModeStr);
    bool FreqSpectrum = (Sync_CD == syncMode || Sync_D == syncMode);
    m_SonoParameters->setPV(BFPNames::FreqSpectrumStr, FreqSpectrum);
    m_Buffer->updateFreqSpectrum(m_SonoParameters->pBV(BFPNames::FreqSpectrumStr));
    if (currentImageBufferGroup()->frameCount() > 0)
    {
        emit createCineLoopPlayer(m_SonoBufferIndex, this);
        emit currentIndexChanged(m_Buffer->currentIndex(false));
        emit updateCinePlayCurrentIndex(m_SonoBufferIndex);
        emit freezeChanged(m_IsFrozen);
    }
}
void SonoBuffer::setFreeze(bool value)
{
    qDebug() << PRETTY_FUNCTION << "freeze:" << value << "m_SonoBufferIndex:" << m_SonoBufferIndex;
    if (m_IsFrozen != value)
    {
        m_IsFrozen = value;
        m_IsOnSetPreset = false;
        if (m_IsActive || m_IsFrozen || currentImageBufferGroup()->frameCount() > 0)
        {
            emit freezeChanged(m_IsFrozen);
        }
        /*非冻结，激活状态*/
        if (!m_IsFrozen && m_IsActive)
        {
            emit playCineStatusChanged(true);
            emit destoryCineLoopPlayer(m_SonoBufferIndex);
            m_IsLoaded = false;
            m_IsStressEcho = false;

            if (m_StressEchoBuffer != NULL)
            {
                disconnectConnections(m_StressEchoBuffer);
                connectConnections(m_Buffer);
                if (m_Buffer->frameGroupInfo().size() > 0)
                {
                    m_Buffer->updateFrameTypes();
                }
                QMutexLocker lockerImages(&m_RGBAImagesSync);
                m_StressRGBAImages.clear();
            }
            else if (m_LoadBuffer != NULL)
            {
                disconnectConnections(m_LoadBuffer);
                connectConnections(m_Buffer);
                if (m_Buffer->frameGroupInfo().size() > 0)
                {
                    m_Buffer->updateFrameTypes();
                }
            }
            m_IsPaused = true;
            setSonoParameters(m_GlobalSonoParameters);
            IBufferWriteLocker locker(m_Buffer, PRETTY_FUNCTION);
            qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex;
            bool clearUnActive =
                SystemScanModeClassifier::isLikeD(m_GlobalSonoParameters->pIV(BFPNames::SystemScanModeStr)) ? false
                                                                                                            : true;
            m_Buffer->clear(clearUnActive);
            destoryRealTimeRGBAImages();
            //            m_Buffer->setWriteAble(true); //移动到setWorkState中控制
            m_Buffer->updateFrameTypes();
            m_IsPaused = false;
        }
        /*非冻结，不激活状态 应该不做任何处理*/
        else if (!m_IsFrozen)
        {
        }
        /*冻结, 激活状态*/
        else if (m_IsFrozen && m_IsActive)
        {
            /*为支持后处理下的参数切换，此处需要切换成冻结参数*/
            if (m_FreezeSonoParameters != NULL)
            {
                SonoParameters::clone(m_GlobalSonoParameters, m_FreezeSonoParameters);
                m_FreezeSonoParameters->setIsRealTime(m_GlobalSonoParameters->isRealTime());
                m_FreezeSonoParameters->setPreset(m_GlobalSonoParameters->currentPreset());
            }
            else
            {
                m_FreezeSonoParameters = m_GlobalSonoParameters->clone();
                m_FreezeSonoParameters->setIsRealTime(m_GlobalSonoParameters->isRealTime());
                m_FreezeSonoParameters->setRecordState(true);
            }
            setSonoParameters(m_FreezeSonoParameters);
            //此处需要强制更新，确保能将超声参数统一
            m_GlobalSonoParameters->setPV(BFPNames::ActiveBStr, m_SonoBufferIndex, true);
            m_Buffer->updateFrameTypes(true);
            qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
                     << "m_SonoParameters:" << (quintptr)m_SonoParameters
                     << "m_FreezeSonoParameters:" << (quintptr)m_FreezeSonoParameters
                     << "m_GlobalSonoParameters:" << (quintptr)m_GlobalSonoParameters;
            //            m_Buffer->setWriteAble(false); //移动到setWorkState中控制
            //同步电影帧数
            if (currentImageBufferGroup()->frameCount() > 0)
            {
                emit playCineStatusChanged(false);
                emit createCineLoopPlayer(m_SonoBufferIndex, this);
            }
        }
        /*冻结，非激活状态*/
        else if (m_IsFrozen && !m_IsActive)
        {
            //同步电影帧数,有数据的时候建立播放器
            if (currentImageBufferGroup()->frameCount() > 0)
            {
                emit createCineLoopPlayer(m_SonoBufferIndex, this);
            }
        }
    }
}

void SonoBuffer::setWorkState(bool isScaning)
{
    if (isScaning)
    {
        m_Buffer->setWriteAble(true);
    }
    else
    {
        m_Buffer->setWriteAble(false);
    }
}

void SonoBuffer::setMemory(const ByteBuffer& mem)
{
    if (m_Memory != mem)
    {
        //        QSize imageSize = ModelConfig::instance().value(ModelConfig::RenderImageSize, QSize(640,
        //        512)).toSize();
        m_Memory = mem;
        m_Buffer->setMemory(m_Memory);
    }
}
/**
 * @brief SonoBuffer::setGlobalSonoParameters
 * @param 在layout修改时，所有的sonobuffer都会执行这个方法，
 * 在非激活的窗口，初始化一部分参数。
 */
void SonoBuffer::setGlobalSonoParameters(SonoParameters* value)
{
    //    /*在加载模式下,如果m_GlobalSonoParameters不为空，则不修改设置过来的超声参数*/
    if (!value->isRealTime() && m_GlobalSonoParameters != NULL)
    {
        return;
    }
    if ((m_GlobalSonoParameters != value) || (m_GlobalSonoParameters->isRealTime()))
    {
        //        qDebug() << PRETTY_FUNCTION
        //                 << "m_SonoBufferIndex:" << m_SonoBufferIndex
        //                 << "value:" << (quintptr) value
        //                 << "m_SonoParameters:" << (quintptr) m_SonoParameters
        //                 << "m_GlobalSonoParameters:" << (quintptr) m_GlobalSonoParameters
        //                 << "m_FreezeSonoParameters:" << (quintptr) m_FreezeSonoParameters;

        m_GlobalSonoParameters = value;
        if (m_IsActive)
        {
            m_IsLoaded = false;
            if (m_LoadBuffer != NULL)
            {
                disconnectConnections(m_LoadBuffer);
                connectConnections(m_Buffer);
                if (m_Buffer->frameGroupInfo().size() > 0)
                {
                    m_Buffer->updateFrameTypes();
                }
            }
            setSonoParameters(m_GlobalSonoParameters);
            m_Buffer->updateFrameTypes();
        }
        else
        {
            if (m_FreezeSonoParameters != NULL && m_FreezeSonoParameters != m_GlobalSonoParameters)
            {
                SonoParameters::clone(m_GlobalSonoParameters, m_FreezeSonoParameters);
                m_FreezeSonoParameters->setPV(BFPNames::FreezeStr, true);
            }
            else
            {
                m_FreezeSonoParameters = m_GlobalSonoParameters->clone(NULL);
                m_FreezeSonoParameters->setPV(BFPNames::FreezeStr, true);
                m_FreezeSonoParameters->setRecordState(true);
            }
            m_FreezeSonoParameters->setIsRealTime(false);
            m_FreezeSonoParameters->setPV(BFPNames::ActiveBStr, m_SonoBufferIndex);
            setSonoParameters(m_FreezeSonoParameters);
            m_Buffer->updateFrameTypes();
            sonoParameters()->setPV(BFPNames::IsBiopsyVisibleStr, false);
            sonoParameters()->setPV(BFPNames::IsCenterLineVisibleStr, false);
            sonoParameters()->setPV(BFPNames::IsBHorizontalRulerVisibleStr, false);
            sonoParameters()->setPV(BFPNames::NeedleModeStr, false);
            //            sonoParameters()->setPV(BFPNames::SystemScanModeStr,SystemScanModeB,true);
        }
    }
}

void SonoBuffer::syncGlobalSonoparamters()
{
    //解冻时，如果是切预设/回调解冻，则不保留区域所作的修改,否则将当前区域的所做的修改更新到全局超声参数中
    if (!m_IsLoaded && !m_IsOnSetPreset && m_FreezeSonoParameters != NULL)
    {
        m_FreezeSonoParameters->cloneRecordTo(m_GlobalSonoParameters);
    }
}

void SonoBuffer::clearFreezeParasRecord()
{
    if (m_FreezeSonoParameters != nullptr)
    {
        m_FreezeSonoParameters->clearRecord();
    }
}

void SonoBuffer::setSonoParameters(SonoParameters* sonoParameters)
{
    //    qDebug() << PRETTY_FUNCTION
    //             << "m_SonoBufferIndex:" << m_SonoBufferIndex
    //             << "sonoParameters:" << (quintptr) sonoParameters
    //             << "m_SonoParameters:" << (quintptr) m_SonoParameters
    //             << "m_GlobalSonoParameters:" << (quintptr) m_GlobalSonoParameters
    //             << "m_FreezeSonoParameters:" << (quintptr) m_FreezeSonoParameters
    //             << "m_IsLoaded:" << m_IsLoaded
    //             << "m_IsStressEcho:" << m_IsStressEcho;

    beforeSonoParametersChanged();
    disconnectSonoParameters();
    m_SonoParameters = sonoParameters;
    connectSonoParameters();
    onSetSonoParameters();
    onProbeIdChanged(m_SonoParameters->pV(BFPNames::ProbeIdStr));
    onNeedleModeChanged(m_SonoParameters->pV(BFPNames::NeedleModeStr));
    onFrameAvgChanged();
    updateUnActiveRenderPartition();
}

bool SonoBuffer::updateCurrentIndexOnFrozen(const int currentIndex, const int imageType)
{
    return currentImageBufferGroup()->updateCurrentIndexOnFrozen(currentIndex, imageType);
}

void SonoBuffer::syncDisplayIndex(const int displayIndex, const bool isUpdateFrameCount)
{
    if (displayIndex < 0)
    {
        return;
    }

    if (!m_IsLoaded && isUpdateFrameCount)
    {
        m_Buffer->syncDisplayIndex(displayIndex);
    }
    else
    {
        currentImageBufferGroup()->setCurrentIndex(displayIndex);
    }
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
             << "displayIndex:" << displayIndex << "isUpdateFrameCount:" << isUpdateFrameCount;
#endif
    emit currentIndexChanged(currentImageBufferGroup()->currentIndex(false));
}

void SonoBuffer::setStartIndex(const int startIndex)
{
    currentImageBufferGroup()->setStartIndex(startIndex);
    emit updateCinePlayStartIndex(m_SonoBufferIndex);
}

void SonoBuffer::setEndIndex(const int endIndex)
{
    currentImageBufferGroup()->setEndIndex(endIndex);
    emit updateCinePlayEndIndex(m_SonoBufferIndex);
}

void SonoBuffer::setCurrentIndex(const int currentIndex)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
             << "currentIndex:" << currentIndex;
#endif
    currentImageBufferGroup()->setCurrentIndex(currentIndex);
    emit currentIndexChanged(currentImageBufferGroup()->currentIndex(false));
    emit updateCinePlayCurrentIndex(m_SonoBufferIndex);
}

void SonoBuffer::requestFlush(const int frameIndex, const int bufferIndex, bool isMeasureData)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
             << "currentIndex:" << currentIndex << "bufferIndex:" << bufferIndex;
#endif
    emit currentIndexChanged((-1 == frameIndex) ? currentIndex(false) : frameIndex, bufferIndex, isMeasureData);
}

double SonoBuffer::defaultFps(const int bufferIndex)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->defaultFps(bufferIndex);
}

void SonoBuffer::setDefaultFps(int bufferIndex, double fps)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    currentImageBufferGroup()->setDefaultFps(bufferIndex, fps);
}

void SonoBuffer::onNewImage(ImageEventArgs* imageEventArgs)
{
    if ((m_UnActiveRenderPartition != Partition_None) &&
        (m_UnActiveRenderPartition == (imagePatition)imageEventArgs->imageRenderPartition()))
    {
        QMutexLocker lockerImages(&m_RGBAImagesSync);
        updateUnActiveRenderPartitionRGBAImages(imageEventArgs);
        return;
    }

    if (m_IsLoaded)
    {
        QMutexLocker lockerImages(&m_RGBAImagesSync);
        if (m_LoadRGBAImages.contains(imageEventArgs->imageType()))
        {
            ImageEventArgs* backUp = m_LoadRGBAImages[imageEventArgs->imageType()];
            if (backUp->imageSize() == imageEventArgs->imageSize())
            {
                memcpy(backUp->imageData(), imageEventArgs->imageData(), imageEventArgs->imageSize());
            }
            else
            {
                delete[] backUp->imageData();
                backUp->setImageData(new uchar[imageEventArgs->imageSize()]);
                backUp->setWidth(imageEventArgs->width());
                backUp->setHeight(imageEventArgs->height());
            }
            backUp->setFrameIndex(imageEventArgs->frameIndex());
            backUp->setECGEnd(imageEventArgs->ecgEnd());
        }
    }
    else
    {
        ImageEventArgs* backUp = nullptr;
        if (m_RealTimeRGBAImages.contains(imageEventArgs->imageType()))
        {
            backUp = m_RealTimeRGBAImages[imageEventArgs->imageType()];

            backUp->setWidth(imageEventArgs->width());
            backUp->setHeight(imageEventArgs->height());
            backUp->setFrameIndex(imageEventArgs->frameIndex());
            backUp->setECGEnd(imageEventArgs->ecgEnd());
        }
        else
        {
            backUp = new ImageEventArgs();
            *backUp = *imageEventArgs;
            m_RealTimeRGBAImages.insert(imageEventArgs->imageType(), backUp);
        }
    }
}

void SonoBuffer::forceFlush()
{
    if ((m_SonoParameters != NULL) && !sonoParameters()->pBV(BFPNames::FreezeStr) && !m_IsStressEcho)
    {
        //#ifdef BUFFER_DEBUG_ENABLE
        //            qDebug()<<"&&&&&"<<PRETTY_FUNCTION
        //                    <<"m_RGBAImages.keys"<<m_RGBAImages.keys();
        //#endif
        m_Buffer->forceFlush();
        emit startForceFlushTimer();
    }
}

void SonoBuffer::onRequesFlushByBufferIndex(const int bufferIndex)
{
    emit currentIndexChanged(currentIndex(false), bufferIndex);
}

const QHash<int, ImageEventArgs*>& SonoBuffer::getSaveImages() const
{
    bool saveUnActiveRenderPartition =
        (m_SonoParameters->pBV(BFPNames::HasAutoEFResultStr) &&
         ((CurLayout)m_SonoParameters->pIV(BFPNames::AutoEFCurLayoutStr) >= CurLayout::Layout_1x2_1));
    return saveUnActiveRenderPartition ? m_UnActiveRenderPartitionRGBAImages
                                       : (m_IsLoaded ? m_LoadRGBAImages : m_RealTimeRGBAImages);
}

void SonoBuffer::destoryRGBAImages(QHash<int, ImageEventArgs*>& images)
{
    foreach (ImageEventArgs* backUp, images.values())
    {
        CHECK_DELETE(char, backUp->imageData());
        CHECK_DELETE(ImageEventArgs, backUp);
        delete[] backUp->imageData();
        delete backUp;
    }
    images.clear();
}

void SonoBuffer::destoryLoadRGBAImages()
{
    QMutexLocker lockerImages(&m_RGBAImagesSync);
    destoryRGBAImages(m_LoadRGBAImages);
}

void SonoBuffer::onForceFlushTimeout()
{
    emit stopForceFlushTimer();
    if ((m_SonoParameters != NULL) && !sonoParameters()->pBV(BFPNames::FreezeStr) && !m_IsStressEcho)
    {
        //#ifdef BUFFER_DEBUG_ENABLE
        //            qDebug()<<"&&&&&"<<PRETTY_FUNCTION
        //                    <<" m_RGBAImages.keys:"<<m_RGBAImages.keys()
        //                    <<" imageCount:"<<m_Buffer->imageCount();
        //#endif
        QMutexLocker lockerImages(&m_RGBAImagesSync);
        if (m_RealTimeRGBAImages.count() != m_Buffer->imageCount())
        {
            m_Buffer->forceFlush();
            emit startForceFlushTimer();
        }
    }
}

void SonoBuffer::onCurrentIndexChanged(const int frameIndex)
{
    qDebug() << PRETTY_FUNCTION << "frameIndex:" << frameIndex;
    setCurrentIndex(frameIndex);
}

bool SonoBuffer::saveCineFinished() const
{
    return m_SaveCineFinished;
}

void SonoBuffer::setSaveCineFinished(bool finished)
{
    m_SaveCineFinished = finished;
}

void SonoBuffer::destoryRealTimeRGBAImages()
{
    //    QMutexLocker lockerImages(&m_RGBAImagesSync);
    //    destoryRGBAImages(m_RealTimeRGBAImages);
}

int SonoBuffer::sonoBufferIndex() const
{
    return m_SonoBufferIndex;
}

SonoParameters* SonoBuffer::sonoParameters() const
{
    return m_SonoParameters;
}

QList<FrameUnitInfo> SonoBuffer::dispalyFrameUnitInfos()
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);

    if (m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        return m_ProcessedBuffer == NULL ? currentImageBufferGroup()->displayFrameGroupInfo()
                                         : m_ProcessedBuffer->displayFrameGroupInfo();
    }
    else
    {
        return currentImageBufferGroup()->displayFrameGroupInfo();
    }
}

void SonoBuffer::acquire(int n)
{
    m_Semaphore.acquire(n);
}

void SonoBuffer::release(int n)
{
    m_Semaphore.release();
}

bool SonoBuffer::isLoad() const
{
    return m_IsLoaded || m_IsStressEcho;
}

bool SonoBuffer::isRealTimeSaveRunning() const
{
    return m_Buffer->isRealTimeSaveRunning();
}

int SonoBuffer::getNextBuffer(QList<BufferUnit>& bufferUnits, FrameUnitInfo& frameUnitInfo, const int imageBufferIndex)
{
    if (m_IsPaused || m_SystemScanModeChanging)
    {
#ifdef BUFFER_DEBUG_ENABLE
        qCritical() << PRETTY_FUNCTION << " BufferHasNoData m_IsPaused:" << m_IsPaused
                    << " m_SystemScanModeChanging:" << m_SystemScanModeChanging;
#endif
        return BufferHasNoData;
    }

    IBufferTryReadLocker locker(this, PRETTY_FUNCTION);
    if (locker.isLock())
    {
        if (m_IsBufferCleared)
        {
            m_IsBufferCleared = false;
            emit bufferCleared();
        }
        return currentImageBufferGroup()->getNextBuffer(bufferUnits, frameUnitInfo, imageBufferIndex);
    }
    return BufferIsLocked;
}

int SonoBuffer::getNextBuffer(QList<BufferUnit>& bufferUnits, FrameUnitInfo& frameUnitInfo, int& residueCacheSize,
                              const int imageBufferIndex, const int lineNumber)
{
    if (m_IsPaused || m_SystemScanModeChanging)
    {
#ifdef BUFFER_DEBUG_ENABLE
        qCritical() << PRETTY_FUNCTION << " BufferHasNoData m_IsPaused:" << m_IsPaused
                    << " m_SystemScanModeChanging:" << m_SystemScanModeChanging;
#endif
        return BufferHasNoData;
    }

    IBufferTryReadLocker locker(this, PRETTY_FUNCTION);
    if (locker.isLock())
    {
        if (m_IsBufferCleared)
        {
            m_IsBufferCleared = false;
            emit bufferCleared();
        }
        return currentImageBufferGroup()->getNextBuffer(bufferUnits, frameUnitInfo, residueCacheSize, imageBufferIndex,
                                                        lineNumber);
    }
    return BufferIsLocked;
}

int SonoBuffer::getBufferByIndex(QList<BufferUnit>& bufferUnits, FrameUnitInfo& frameUnitInfo,
                                 const int imageBufferIndex, const int frameIndex)
{
    if (m_IsPaused)
    {
        return BufferHasNoData;
    }

    IBufferTryReadLocker locker(this, PRETTY_FUNCTION);
    if (locker.isLock())
    {
        return currentImageBufferGroup()->getBufferByIndex(bufferUnits, frameUnitInfo, imageBufferIndex, frameIndex);
    }
    return BufferIsLocked;
}

int SonoBuffer::bufferIndex() const
{
    return m_SonoBufferIndex;
}

bool SonoBuffer::markDataPushOnce(int imageBufferIndex)
{
    if (m_IsPaused)
    {
        return false;
    }

    IBufferTryReadLocker locker(this, PRETTY_FUNCTION);
    if (locker.isLock())
    {
        ImageBufferGroup* pCurImageBufferGroup = currentImageBufferGroup();
        if (pCurImageBufferGroup == nullptr)
        {
            return false;
        }

        return pCurImageBufferGroup->markDataPushOnce(imageBufferIndex);
    }
    return true;
}

int SonoBuffer::scpdvalue()
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->scpdValue();
}

int SonoBuffer::scpdvalue(const int bufferIndex)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->scpdValue(bufferIndex);
}

int SonoBuffer::frameAvgCount()
{
    //    IBufferReadLocker locker(currentImageBufferGroup(), PRETTY_FUNCTION);
    return currentImageBufferGroup()->frameAvgCount();
}

void SonoBuffer::setRawDataImage(uchar* data, int width, int height)
{
    m_ImageSaveHelper->setRawDataBeforeIImageData(data, width, height);
}

void SonoBuffer::setGrayRawDataImage(uchar* data, int width, int height)
{
    m_ImageSaveHelper->setGrayImageData(data, width, height);
}

quint64 SonoBuffer::fileByteSize(bool totalFrame) const
{
    IBufferReadLocker locker(currentImageBufferGroup(), PRETTY_FUNCTION);
    return currentImageBufferGroup()->fileByteSize(totalFrame);
}

bool SonoBuffer::saveCine(const QString& dirPath, bool hasInterval)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    ImageBufferGroup* buffer = m_ProcessedBuffer == NULL ? currentImageBufferGroup() : m_ProcessedBuffer;
    if (!buffer->saveCine(dirPath, hasInterval))
    {
#ifdef BUFFER_DEBUG_ENABLE
        qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
                 << "save failed:" << (quintptr)m_ProcessedBuffer;
#endif
        return false;
    }
    if (sonoParameters()->isRealTime() && !sonoParameters()->pBV(BFPNames::FreezeStr))
    {
        PostParameterSync::syncRealTimeArgsValue2PostArgs(sonoParameters(), true);
    }
    QMutexLocker lockerImages(&m_RGBAImagesSync);
    return m_Storer->save(dirPath, getSaveImages(), sonoParameters());
}

bool SonoBuffer::realTimeSaveCine(const QString& dirPath)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    ImageBufferGroup* buffer = m_ProcessedBuffer == NULL ? currentImageBufferGroup() : m_ProcessedBuffer;
    connect(buffer, SIGNAL(realTimeSaveCineState(int, int, QString)), this,
            SLOT(onRealTimeSaveCineState(int, int, QString)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    if (!buffer->realTimeSaveCine(dirPath))
    {
#ifdef BUFFER_DEBUG_ENABLE
        qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
                 << "save failed:" << (quintptr)m_ProcessedBuffer;
#endif
        return false;
    }

    m_RealTimeSaveParamLock.lock();
    if (m_RealTimeSaveSonoParameters != NULL)
    {
        m_RealTimeSaveSonoParameters->deleteLater();
        m_RealTimeSaveSonoParameters = NULL;
    }
    m_RealTimeSaveSonoParameters = sonoParameters()->clone(NULL);
    m_RealTimeSaveParamLock.unlock();

    QMutexLocker lockerImages(&m_RGBAImagesSync);
    return m_Storer->saveFrameInfo(dirPath, getSaveImages());
}

void SonoBuffer::onRealTimeSaveCineState(int state, int frameCount, const QString& dirPath)
{
    m_RealTimeSaveParamLock.lock();
    if (state >= 0 && m_RealTimeSaveSonoParameters != NULL)
    {
        qDebug() << PRETTY_FUNCTION << frameCount;
        Parameter* p = m_RealTimeSaveSonoParameters->parameter(BFPNames::BufferIndexStartPosStr);
        p->setValue(1);
        p->setMin(1);
        p->setMax(frameCount);
        p = m_RealTimeSaveSonoParameters->parameter(BFPNames::BufferIndexEndPosStr);
        p->setValue(frameCount);
        p->setMin(1);
        p->setMax(frameCount);
        PostParameterSync::syncRealTimeArgsValue2PostArgs(sonoParameters(), true);
        if (!m_Storer->saveSonoParameters(dirPath, m_RealTimeSaveSonoParameters))
        {
            state = -1;
        }
        m_RealTimeSaveSonoParameters->deleteLater();
        m_RealTimeSaveSonoParameters = NULL;
    }
    else
    {
        state = -1;
    }
    m_SaveCineFinished = true;
    m_RealTimeSaveParamLock.unlock();
    emit realTimeSaveCineState(state);
}

void SonoBuffer::onFrameAvgChanged()
{
    //目前设计的要求是取所有帧相关等级中最大的数
    int ret = 0;
    foreach (const QString name, m_FrameAvgList)
    {
        Parameter* p = m_SonoParameters->parameter(name);
        if (nullptr != p)
        {
            int ctValue = -1;
            p->controlTableValue(p->value(), ctValue);
            ret = qMax(ret, ctValue == -1 ? p->value().toInt() : ctValue);
        }
    }
    currentImageBufferGroup()->setFrameAvgCount(ret);
}

void SonoBuffer::onBeforeFreqSpectrumChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue);
    // TODO：由于BeamFormerBase::onSystemScanModeChanging中会先执行
    // setPV(BFPNames::BImageSizeStr, m_ImageRenderLayoutRects->getBImageSize());
    // setPV(BFPNames::DImageSizeStr, m_ImageRenderLayoutRects->getDImageSize());
    // setPV(BFPNames::FreqSpectrumStr, false);
    //各种ImageSize会触发imageUnstable，引起receive中clear；而FreqSpectrumStr参数的信号触发
    //会执行buffer备份，在B/C模式下进入PW模式时，在LineImageBuffer::onBeforeActiveChanged中
    //利用useOldBackData控制不备份，但PW模式回到B/C模式时，未控制不备份，此处的修改就是弥补此缺陷
    // PW切换到B执行备份，由于多线程原因，PW循环备份大量数据过程中，clear被执行，会导致后续备份操作出现空数据/空指针
    //同时此处的备份设计是用于PW模式的上下激活切换，再深入分析其实是整个工程代码中执行clear的操作未统一规划
    //导致太多条件能执行clear，使得PW模式的非激活区域的数据被clear，没有图像

    if (SystemScanModeClassifier::isLikeD(m_SonoParameters->pIV(BFPNames::SystemScanModeStr)))
    {
        m_Buffer->onFreqSpectrumChanged(newValue.toBool());
    }
}

void SonoBuffer::onNeedleModeChanged(const QVariant& value)
{
    m_IsNeedleMode = value.toBool();
    int systemScanMode = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);

    if (SystemScanModeClassifier::isWithOutCompound(systemScanMode))
    {
        setScpdValue(0);
    }
    else
    {
        if (m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr))
        {
            setScpdValue(m_SonoParameters->pIV(BFPNames::ScpdTrapeStr));
        }
        else
        {
            setScpdValue(m_SonoParameters->pIV(BFPNames::ScpdStr));
        }
    }
}

void SonoBuffer::updateUnActiveRenderPartition()
{
    imagePatition patition = (imagePatition)m_SonoParameters->pIV(BFPNames::ImageRenderPartitionStr);
    CurLayout curLayout = (CurLayout)m_SonoParameters->pIV(BFPNames::AutoEFCurLayoutStr);

    imagePatition lastUnActiveRenderPatition = m_UnActiveRenderPartition;
    if (curLayout == Layout_1x1_1)
    {
        m_UnActiveRenderPartition = Partition_None;
    }
    else
    {
        m_UnActiveRenderPartition = (curLayout == Layout_1x2_1) ? Partition_Right : Partition_Left;
    }

    //在多布局的情况下，切换激活区的时候，需要刷新非激活区的整帧数据
    if ((m_UnActiveRenderPartition != Partition_None) && (lastUnActiveRenderPatition != Partition_None) &&
        (lastUnActiveRenderPatition != m_UnActiveRenderPartition))
    {
        QMutexLocker lockerImages(&m_RGBAImagesSync);
        updateUnActiveRenderPartitionRGBAImages(m_IsLoaded ? m_LoadRGBAImages : m_RealTimeRGBAImages);
    }
    //    qCritical()<< "m_UnActiveRenderPartition" << m_UnActiveRenderPartition
    //               << "curLayout" << curLayout
    //               << "patition" << patition;
}

void SonoBuffer::onBeforeSystemScanModeChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (m_SonoParameters->pBV(BFPNames::PictureModeONStr))
    {
        //切换到不支持超影同屏的模式下或者切回B模式，要关闭
        bool comeBackBMode = ((newValue.toInt() == SystemScanModeB) && (oldValue != newValue));
        if (comeBackBMode || !SystemScanModeClassifier::isSuppprtPicture(newValue.toInt()))
        {
            m_SonoParameters->setPV(BFPNames::PictureModeONStr, false);
        }
    }
}

void SonoBuffer::onBeforeLayoutChanged(const QVariant& oldValue, QVariant& newValue)
{
    Q_UNUSED(oldValue)
    if ((newValue.toInt() > 1) && m_SonoParameters->pBV(BFPNames::PictureModeONStr))
    {
        m_SonoParameters->setPV(BFPNames::PictureModeONStr, false);
    }
}

void SonoBuffer::onIsDopplerScanLineVisibleChanged(QVariant value)
{
    if (value.toBool() && m_SonoParameters->pBV(BFPNames::PictureModeONStr))
    {
        m_SonoParameters->setPV(BFPNames::PictureModeONStr, false);
    }
}

void SonoBuffer::onIsCWDScanLineVisibleChanged(QVariant value)
{
    if (value.toBool() && m_SonoParameters->pBV(BFPNames::PictureModeONStr))
    {
        m_SonoParameters->setPV(BFPNames::PictureModeONStr, false);
    }
}

void SonoBuffer::onIsMLineVisibleChanged(QVariant value)
{
    if (value.toBool() && m_SonoParameters->pBV(BFPNames::PictureModeONStr))
    {
        m_SonoParameters->setPV(BFPNames::PictureModeONStr, false);
    }
}

bool SonoBuffer::existUnActiveRender() const
{
    return Partition_None != m_UnActiveRenderPartition;
}

bool SonoBuffer::saveCurrent(const QString& dirPath, int frameIndex, int frontIndex)
{
    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    ImageBufferGroup* buffer = m_ProcessedBuffer == NULL ? currentImageBufferGroup() : m_ProcessedBuffer;
    if (!buffer->saveCurrent(dirPath, frameIndex, frontIndex))
    {
#ifdef BUFFER_DEBUG_ENABLE
        qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
                 << "save failed:" << (quintptr)m_ProcessedBuffer;
#endif
        return false;
    }

    //在冻结下，打开存储数据开关，要讲数据更新到sonobuffer，存图时存储到病例中
    if ((!m_SonoParameters->isRealTime() || m_SonoParameters->pBV(BFPNames::FreezeStr)) &&
        Setting::instance().defaults().rawDataUponSTILSAVEFlag())
    {
        m_ImageSaveHelper->saveRawDataBeforeIImageData(dirPath);
        m_ImageSaveHelper->saveGrayImageData(dirPath);
    }

    if (m_SonoParameters->pBV(BFPNames::PictureModeONStr))
    {
        m_ImageSaveHelper->savePictureImageData(dirPath);
    }

    if (sonoParameters()->isRealTime() && !sonoParameters()->pBV(BFPNames::FreezeStr))
    {
        PostParameterSync::syncRealTimeArgsValue2PostArgs(sonoParameters(), true);
    }
    QMutexLocker lockerImages(&m_RGBAImagesSync);
    return m_Storer->save(dirPath, getSaveImages(), sonoParameters());
}

LoadErrorCode SonoBuffer::load(const QString& dirPath)
{
    m_IsPaused = true;
    emit playCineStatusChanged(false);
    emit destoryCineLoopPlayer(m_SonoBufferIndex);

    LoadErrorCode loadStatus = LoadErrorCode::Success;
    destoryLoadRGBAImages();
    //在这个范围内执行需要加锁
    {
#ifdef BUFFER_DEBUG_ENABLE
        qDebug() << "&&&&&" << PRETTY_FUNCTION << "dirPath:" << dirPath << "m_SonoBufferIndex:" << m_SonoBufferIndex;
#endif
        IBufferWriteLocker locker(this, PRETTY_FUNCTION);
        //加载图像数据过程中，m_IsLoaded 不需要设置，等到加载结束后onload被触发时，再设置
        //        m_IsLoaded = true;
        m_IsLoaded = false;
        if (m_LoadBuffer != NULL)
        {
            disconnectConnections(m_LoadBuffer);
            connectConnections(m_Buffer);
            CHECK_DELETE(ImageBufferGroup, m_LoadBuffer);
            delete m_LoadBuffer;
            m_LoadBuffer = NULL;
        }
        m_LoadBuffer = new ImageBufferGroup(m_SonoBufferIndex, Setting::instance().defaults().cineCacheSize(),
                                            m_ImageSaveHelper->imageSaveHelper(), NULL);
        m_Storer->clear();
        CHECK_NEW(ImageBufferGroup, m_LoadBuffer);
        QDir dir(dirPath);
        if (!dir.exists())
        {
            m_IsPaused = false;
            return LoadErrorCode::Success;
        }

        ByteBuffer byteBuffer;
        LoadErrorCode sonoparameterErrorCode = m_Storer->load(dirPath, byteBuffer, m_GlobalSonoParameters);
        if (sonoparameterErrorCode != LoadErrorCode::Success)
        {
            m_IsPaused = false;
            return sonoparameterErrorCode;
        }
        m_LoadBuffer->setMemory(m_LoadMemory);

        beforeLoadImageData(false);
        loadStatus = m_LoadBuffer->load(dirPath);
        m_ImageSaveHelper->loadPictureImageData(dirPath);
    }
    m_IsPaused = false;
    if (loadStatus == LoadErrorCode::Success)
    {
        onload();
    }
    else
    {
        onloadFailed();
    }
    return loadStatus;
}

bool SonoBuffer::checkLoadFilesValidity(const QString& dirPath)
{
    QDir dir(dirPath);
    if (dir.exists())
    {
        if (m_Storer->checkLoadFilesValidity(dirPath))
        {
            return ((Util::diskFileIsExists(dirPath, "frameinfo.bin")) &&
                    (Util::diskFileIsExists(dirPath, FILEMAPINFO_FILE_NAME)));
        }
    }
    return false;
}

bool SonoBuffer::loadStressEcho(const QString& dirPath)
{
    m_IsPaused = true;
    emit playCineStatusChanged(false);
    emit destoryCineLoopPlayer(m_SonoBufferIndex);

    LoadErrorCode loadStatus = LoadErrorCode::Success;
    destoryLoadRGBAImages();
    {
        IBufferWriteLocker locker(this, PRETTY_FUNCTION);
        qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex;
        if (m_StressEchoBuffer != NULL)
        {
            CHECK_DELETE(ImageBufferGroup, m_StressEchoBuffer);
            delete m_StressEchoBuffer;
            m_StressEchoBuffer = NULL;
        }
        m_StressEchoBuffer = new ImageBufferGroup(m_SonoBufferIndex, Setting::instance().defaults().cineCacheSize(),
                                                  m_ImageSaveHelper->imageSaveHelper(), NULL);
        CHECK_NEW(ImageBufferGroup, m_StressEchoBuffer);
        if (m_StressEchoStorer == NULL)
        {
            m_StressEchoStorer = new SonoBufferStorer();
            CHECK_NEW(SonoBufferStorer, m_StressEchoStorer);
        }
        m_StressEchoStorer->clear();

        ByteBuffer byteBuffer;
        LoadErrorCode errorCode = m_StressEchoStorer->load(dirPath, byteBuffer, m_GlobalSonoParameters);
        if (errorCode != LoadErrorCode::Success)
        {
            m_IsPaused = false;
            return false;
        }
        m_StressEchoBuffer->setMemory(m_LoadMemory);
        beforeLoadImageData(true);
        loadStatus = m_StressEchoBuffer->load(dirPath);
        m_ImageSaveHelper->loadPictureImageData(dirPath);
    }
    m_IsPaused = false;

    if (loadStatus == LoadErrorCode::Success)
    {
        onStressEchoLoad();
        return true;
    }
    else
    {
        onStressEchoLoadFailed();
        return false;
    }
}
void SonoBuffer::removeBuffer(int bufferIndex)
{
    m_IsPaused = true;
    IBufferWriteLocker locker(m_Buffer, PRETTY_FUNCTION);
    qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex;
    m_Buffer->removeBuffer(bufferIndex);
    m_IsPaused = false;
}

void SonoBuffer::updateSystemScanModeChanging(bool changing)
{
    m_SystemScanModeChanging = changing;
}

void SonoBuffer::enableIsSetPreset()
{
    m_IsOnSetPreset = true;
}

bool SonoBuffer::isOnSetPreset() const
{
    return m_IsOnSetPreset;
}

int SonoBuffer::getFrameIndexByFrameTimestamp(const qint64& timestamp, bool needRealIndex)
{
    //    IBufferReadLocker locker(this, PRETTY_FUNCTION);
    return currentImageBufferGroup()->getFrameIndexByFrameTimestamp(timestamp, needRealIndex);
}

qint64 SonoBuffer::getFramestampByIndex(int frameIndex, bool needRealIndex)
{
    return currentImageBufferGroup()->getFramestampByIndex(frameIndex, needRealIndex);
}

int SonoBuffer::whloeFrameIndex(const int type, const int typeIndex)
{
    return currentImageBufferGroup()->whloeFrameIndex(type, typeIndex);
}

void SonoBuffer::drawUnActiveRenderPartitionImages()
{
    QHash<int, ImageEventArgs*>::const_iterator iter = m_UnActiveRenderPartitionRGBAImages.constBegin();
    for (; iter != m_UnActiveRenderPartitionRGBAImages.constEnd(); iter++)
    {
        int syncId = 0;
        emit getSyncId(syncId);
        iter.value()->setSyncId(syncId);
        iter.value()->setLayout(sonoParameters()->pIV(BFPNames::LayoutStr));
        iter.value()->setSystemScanMode(sonoParameters()->pIV(BFPNames::SystemScanModeStr));
        emit staticImage(iter.value());
    }
}

void SonoBuffer::removeAll(bool clearUnActive)
{
    emit beforeBufferCleared();

    if (clearUnActive)
    {
        destoryRealTimeRGBAImages();
    }

#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex;
#endif
    IBufferWriteLocker locker(m_Buffer, PRETTY_FUNCTION);
    m_Buffer->clear(clearUnActive);
    emit bufferClearing();
    m_IsBufferCleared = true;
}

void SonoBuffer::setMaxUsedSize(const int maxSize)
{
    currentImageBufferGroup()->setMaxUsedSize(maxSize);
}

void SonoBuffer::setIsRealTimeStoreState(const bool value)
{
    currentImageBufferGroup()->setIsRealTimeStoreState(value);
}

void SonoBuffer::prepareRestore(const bool isActive)
{
    if (isActive)
    {
        m_IsPrepareRestore = true;
    }
}

void SonoBuffer::onRestore(const bool isActive)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex << "isActive:" << isActive;
#endif
    emit playCineStatusChanged(false);
    {
        IBufferWriteLocker locker(this, PRETTY_FUNCTION);
        disconnectConnections(m_LoadBuffer);
        connectConnections(m_Buffer);
        m_IsPrepareRestore = false;
        m_IsLoaded = false;
        m_IsActive = isActive;
    }

    if (m_IsActive)
    {
        ASSERT_LOG(m_GlobalSonoParameters != NULL);
        setSonoParameters(m_GlobalSonoParameters);
        m_Buffer->updateFrameTypes();
    }
    else
    {
        if (m_FreezeSonoParameters == NULL)
        {
            IBufferWriteLocker locker(this, PRETTY_FUNCTION);
            qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex;
            emit beforeBuffersChanged(m_SonoBufferIndex);
            emit beforeSonoParametersChanged();
            // emit clearSonoParameters();
        }
        else
        {
            setSonoParameters(m_FreezeSonoParameters);
            m_Buffer->updateFrameTypes();
            if (currentImageBufferGroup()->frameCount() > 0)
            {
#ifdef BUFFER_DEBUG_ENABLE
                qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
                         << "m_IsLoaded:" << m_IsLoaded;
#endif
                emit currentIndexChanged(m_Buffer->currentIndex(false));
            }
        }
    }

    if (m_LoadBuffer != NULL)
    {
        emit destoryCineLoopPlayer(m_SonoBufferIndex);
        CHECK_DELETE(ImageBufferGroup, m_LoadBuffer);
        delete m_LoadBuffer;
        m_LoadBuffer = NULL;
        destoryLoadRGBAImages();
    }
}

void SonoBuffer::onStopPipeline(bool clearRealTimeData)
{
    IBufferWriteLocker locker(this, PRETTY_FUNCTION);
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
             << "clearRealTimeData:" << clearRealTimeData;
#endif
    disconnectSonoParameters();
    m_SonoParameters = NULL;
    emit beforeSonoParametersChanged();
    emit destoryCineLoopPlayer(m_SonoBufferIndex);
    emit playCineStatusChanged(false);
    // emit clearSonoParameters();
    emit clearFrameTypes();
    m_IsActive = false;
    if (clearRealTimeData)
    {
        m_Buffer->clearFrameUnitInfoGroup();
        m_Buffer->clear();
        destoryRealTimeRGBAImages();
    }
}

void SonoBuffer::deleteLoadSonoparameters()
{
    m_Storer->clearAll();
    if (m_StressEchoStorer != NULL)
    {
        CHECK_DELETE(SonoBufferStorer, m_StressEchoStorer);
        delete m_StressEchoStorer;
        m_StressEchoStorer = NULL;
    }
}

void SonoBuffer::onBeforeSetSonoParameters()
{
    emit beforeSonoParametersChanged();

    emit sonoParametersChanging();
}

void SonoBuffer::onSetSonoParameters()
{
    if (m_IsActive || m_IsLoaded || m_IsStressEcho || currentImageBufferGroup()->frameCount() > 0)
    {
        emit sonoParametersChanged();
    }
    else
    {
        IBufferWriteLocker locker(this, PRETTY_FUNCTION);
        qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex;
        // emit clearSonoParameters();
    }
}

void SonoBuffer::beforeLoadImageData(bool isStressEcho)
{
    if (m_IsLoaded)
    {
        disconnectConnections(m_LoadBuffer);
    }
    else
    {
        disconnectConnections(m_Buffer);
    }

    if (isStressEcho)
    {
        connectConnections(m_StressEchoBuffer);
    }
    else
    {
        connectConnections(m_LoadBuffer);
    }
}

void SonoBuffer::requestStaticImage(const bool isLoad)
{
#ifdef SYS_APPLE
    requestFlush();
#else
    QMutexLocker lockerImages(&m_RGBAImagesSync);
    QHash<int, ImageEventArgs*>* images = &m_RealTimeRGBAImages;
    if (m_IsStressEcho)
    {
        images = &m_StressRGBAImages;
    }
    else if (isLoad)
    {
        images = &m_LoadRGBAImages;
    }
    QHash<int, ImageEventArgs*>::iterator iter;
    for (iter = images->begin(); iter != images->end(); iter++)
    {
        int syncId = 0;
        emit getSyncId(syncId);
        iter.value()->setSyncId(syncId);
        iter.value()->setLayout(sonoParameters()->pIV(BFPNames::LayoutStr));
        iter.value()->setSystemScanMode(sonoParameters()->pIV(BFPNames::SystemScanModeStr));
        iter.value()->setImageRenderPartition(sonoParameters()->pIV(BFPNames::ImageRenderPartitionStr));
        qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex << "index:" << iter.value()->index()
                 << "syncId:" << iter.value()->syncId() << "layout:" << iter.value()->layout();
        emit staticImage(iter.value());
    }
#endif
}

void SonoBuffer::requestPictureImage()
{
    if (m_SonoParameters->pBV(BFPNames::PictureModeONStr))
    {
        QImage image = pictureImage();
        if (!image.isNull())
        {
            emit pictureImage();
        }
    }
}

void SonoBuffer::setIsFileMapEnable(bool isEnable)
{
    if (m_Buffer != NULL)
    {
        m_Buffer->setIsFileMapEnable(isEnable);
    }
}

bool SonoBuffer::isFileMapEnable() const
{
    bool isEnable = false;
    if (m_Buffer != NULL)
    {
        isEnable = m_Buffer->isFileMapEnable();
    }
    return isEnable;
}

void SonoBuffer::setIsRealTimeSaveCine(bool value)
{
    if (m_Buffer != NULL)
    {
        m_Buffer->setIsRealTimeSaveCine(value);
    }
}

bool SonoBuffer::isRealTimeSaveCine() const
{
    if (m_Buffer != NULL)
    {
        return m_Buffer->isRealTimeSaveCine();
    }
    return false;
}

void SonoBuffer::onload()
{
    emit freezeChanged(true);

    m_IsLoaded = true;

    {
        QMutexLocker lockerImages(&m_RGBAImagesSync);
        QHash<int, ImageEventArgs*>::const_iterator iter = m_Storer->images().constBegin();
        while (iter != m_Storer->images().constEnd())
        {
            ImageEventArgs* newImage = new ImageEventArgs();
            *newImage = *iter.value();
            newImage->setImageData(new uchar[newImage->imageSize()]);
            memcpy(newImage->imageData(), iter.value()->imageData(), iter.value()->imageSize());
            m_LoadRGBAImages.insert(iter.key(), newImage);
            iter++;
        }
    }

    m_LoadBuffer->setWriteAble(false);

    setSonoParameters(m_Storer->sonoParameters());

    //    currentImageBufferGroup()->setScpdValue(m_SonoParameters->pIV(BFPNames::ScpdStr));

    if (m_LoadBuffer->frameCount() > 0)
    {
        emit createCineLoopPlayer(m_SonoBufferIndex, this);
    }

    emit clearFrozenIndex();
    m_LoadBuffer->updateFrameTypes();
    //    emit currentIndexChanged(currentImageBufferGroup()->currentIndex());

    QMutexLocker lockerImages(&m_RGBAImagesSync);
    updateUnActiveRenderPartitionRGBAImages(m_LoadRGBAImages);
}

void SonoBuffer::onloadFailed()
{
    onRestore(m_IsActive);
}

void SonoBuffer::onStressEchoLoad()
{
    m_IsStressEcho = true;

    {
        QMutexLocker lockerImages(&m_RGBAImagesSync);
        m_StressRGBAImages = m_StressEchoStorer->images();
    }

    m_StressEchoBuffer->setWriteAble(false);

    setSonoParameters(m_StressEchoStorer->sonoParameters());

    emit clearFrozenIndex();

    //    currentImageBufferGroup()->setScpdValue(m_SonoParameters->pIV(BFPNames::ScpdStr));
    m_StressEchoBuffer->updateFrameTypes();
    requestPictureImage();
}

void SonoBuffer::onStressEchoLoadFailed()
{
    onStressEchoRestore();
}

void SonoBuffer::onStressEchoRestore()
{
    if (!m_IsStressEcho)
    {
        return;
    }
    m_IsStressEcho = false;
    {
        QMutexLocker lockerImages(&m_RGBAImagesSync);
        m_StressRGBAImages.clear();
    }
    disconnectConnections(m_StressEchoBuffer);
    if (m_IsLoaded)
    {
        connectConnections(m_LoadBuffer);
        setSonoParameters(m_Storer->sonoParameters());
        if (m_Buffer->frameGroupInfo().size() > 0)
        {
            emit createCineLoopPlayer(m_SonoBufferIndex, this);
            emit updateCinePlayCurrentIndex(m_SonoBufferIndex);
            m_LoadBuffer->updateFrameTypes();
        }
    }
    else
    {
        connectConnections(m_Buffer);
        if (m_IsActive)
        {
            setSonoParameters(m_FreezeSonoParameters);
            if (m_Buffer->frameGroupInfo().size() > 0)
            {
                // TODO 重构，目前的设计，由于无法确定restore是否会回到冻结还是实时扫查状态，
                //因此没办法确定是否需要创建播放器，因此在这里统一创建播放器，
                //如果restore后会回到实时扫查状态，也会自动销毁创建的播放器，这种情况会存在多创建了一次播放器的过程
                if (m_IsFrozen && (m_Buffer->frameCount() > 0))
                {
                    emit createCineLoopPlayer(m_SonoBufferIndex, this);
                    emit updateCinePlayCurrentIndex(m_SonoBufferIndex);
                }
                m_Buffer->updateFrameTypes(true);
            }
        }
        else
        {
            int layout = m_GlobalSonoParameters->pIV(BFPNames::LayoutStr);
            if (m_SonoBufferIndex < layout)
            {
                setSonoParameters(m_FreezeSonoParameters);
                if (m_Buffer->frameGroupInfo().size() > 0)
                {
                    // TODO 重构，目前的设计，由于无法确定restore是否会回到冻结还是实时扫查状态，
                    //因此没办法确定是否需要创建播放器，因此在这里统一创建播放器，
                    //如果restore后会回到实时扫查状态，也会自动销毁创建的播放器，这种情况会存在多创建了一次播放器的过程
                    if (m_IsFrozen && (m_Buffer->frameCount() > 0))
                    {
                        emit createCineLoopPlayer(m_SonoBufferIndex, this);
                        emit updateCinePlayCurrentIndex(m_SonoBufferIndex);
                    }
                    m_Buffer->updateFrameTypes(true);
                }
            }
            else
            {
                onStopPipeline(false);
            }
        }
    }

    if (m_StressEchoBuffer != NULL)
    {
        CHECK_DELETE(ImageBufferGroup, m_StressEchoBuffer);
        delete m_StressEchoBuffer;
        m_StressEchoBuffer = NULL;
    }
}

void SonoBuffer::connectConnections(ImageBufferGroup* bufferGroup)
{
    if (bufferGroup == NULL)
    {
        return;
    }
    connect(bufferGroup, SIGNAL(changeFrameTypes(QHash<int, int>, bool)), this,
            SIGNAL(changeFrameTypes(QHash<int, int>, bool)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(timerStart(int)), this, SIGNAL(timerStart(int)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(changeFps(int, double)), this, SIGNAL(changeFps(int, double)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(changeWaveDisplayFps(int, double)), this, SIGNAL(changeWaveDisplayFps(int, double)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(beforeBuffersChanged()), this, SLOT(onBeforeBuffersChanged()),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(buffersChanged()), this, SLOT(onBuffersChanged()),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(notifyLoadingProgess(quint64)), this, SIGNAL(notifyLoadingProgess(quint64)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(notifySavingProgess(quint64)), this, SIGNAL(notifySavingProgess(quint64)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(realTimeSaveBufferIsFull()), this, SIGNAL(realTimeSaveBufferIsFull()),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(freezeSaveCineState(int, bool)), this, SIGNAL(freezeSaveCineState(int, bool)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(activeChanged(bool, int)), this, SIGNAL(activeChanged(bool, int)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(requestFlushNextBuffer(int)), this, SIGNAL(requestFlushNextBuffer(int)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(cacheQueueCleard(int)), this, SIGNAL(cacheQueueCleard(int)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(currentIndexChanged(int)), this, SLOT(onCurrentIndexChanged(int)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(fpsChanged(const QString&, const float)), this,
            SIGNAL(fpsChanged(const QString&, const float)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(beforeBufferCleared(int)), this, SIGNAL(beforeBufferCleared(int)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
    connect(bufferGroup, SIGNAL(bufferCleared(int)), this, SIGNAL(bufferCleared(int)),
            Qt::ConnectionType(Qt::UniqueConnection | Qt::DirectConnection));
}

void SonoBuffer::disconnectConnections(ImageBufferGroup* bufferGroup)
{
    if (bufferGroup == NULL)
    {
        return;
    }
    disconnect(bufferGroup, SIGNAL(changeFrameTypes(QHash<int, int>, bool)), this,
               SIGNAL(changeFrameTypes(QHash<int, int>, bool)));
    disconnect(bufferGroup, SIGNAL(timerStart(int)), this, SIGNAL(timerStart(int)));
    disconnect(bufferGroup, SIGNAL(changeFps(int, double)), this, SIGNAL(changeFps(int, double)));
    disconnect(bufferGroup, SIGNAL(changeWaveDisplayFps(int, double)), this, SIGNAL(changeWaveDisplayFps(int, double)));
    disconnect(bufferGroup, SIGNAL(beforeBuffersChanged()), this, SLOT(onBeforeBuffersChanged()));
    disconnect(bufferGroup, SIGNAL(buffersChanged()), this, SLOT(onBuffersChanged()));
    disconnect(bufferGroup, SIGNAL(notifyLoadingProgess(quint64)), this, SIGNAL(notifyLoadingProgess(quint64)));
    disconnect(bufferGroup, SIGNAL(notifySavingProgess(quint64)), this, SIGNAL(notifySavingProgess(quint64)));
    disconnect(bufferGroup, SIGNAL(realTimeSaveBufferIsFull()), this, SIGNAL(realTimeSaveBufferIsFull()));
    disconnect(bufferGroup, SIGNAL(freezeSaveCineState(int, bool)), this, SIGNAL(freezeSaveCineState(int, bool)));
    //    disconnect(bufferGroup, SIGNAL(lineImageBufferChanged()),
    //               this, SIGNAL(lineImageBufferChanged()));
    disconnect(bufferGroup, SIGNAL(activeChanged(bool, int)), this, SIGNAL(activeChanged(bool, int)));
    disconnect(bufferGroup, SIGNAL(requestFlushNextBuffer(int)), this, SIGNAL(requestFlushNextBuffer(int)));
    disconnect(bufferGroup, SIGNAL(cacheQueueCleard(int)), this, SIGNAL(cacheQueueCleard(int)));
    disconnect(bufferGroup, SIGNAL(currentIndexChanged(int)), this, SLOT(onCurrentIndexChanged(int)));
    disconnect(bufferGroup, SIGNAL(fpsChanged(const QString&, const float)), this,
               SIGNAL(fpsChanged(const QString&, const float)));
    disconnect(bufferGroup, SIGNAL(beforeBufferCleared(int)), this, SIGNAL(beforeBufferCleared(int)));
    disconnect(bufferGroup, SIGNAL(bufferCleared(int)), this, SIGNAL(bufferCleared(int)));
}

ImageBufferGroup* SonoBuffer::currentImageBufferGroup() const
{
    if (m_IsPrepareRestore)
    {
        return m_Buffer;
    }
    else if (m_IsStressEcho)
    {
        return m_StressEchoBuffer;
    }
    else if (m_UseProcessedBufferOnLoad)
    {
        return m_ProcessedBuffer;
    }
    else if (m_IsLoaded)
    {
        return m_LoadBuffer;
    }
    else if (m_UseProcessedBuffer)
    {
        return m_ProcessedBuffer;
    }
    else
    {
        return m_Buffer;
    }
}
void SonoBuffer::setFrameGroupInfo(const QList<FrameUnitInfo>& frameGroupInfo)
{
//    ASSERT_LOG(m_SonoParameters->isRealTime() || m_IsLoaded);
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex
             << "frameGroupInfo.count:" << frameGroupInfo.count();
#endif
    if (m_IsRestore)
    {
        qDebug() << "&&&&&"
                 << "Error:" << PRETTY_FUNCTION << " m_SonoBufferIndex:" << m_SonoBufferIndex;
        return;
    }
    // 由于时序问题m_IsLoaded还未正确赋值，导致判断失效不能正确设置info，同时从现在代码的设计来看
    // 该函数的调用控制不受回调影响：原因是该函数是操作实时的buffer，非回调buffer，但严格来说需要保证时序
    // 时序问题：m_IsLoaded是在解冻/restore中被赋值，但由于切换预设值是模式变更先触发，引发m_IsLoaded未先设置的时序问题
    //    if (!m_IsLoaded)
    {
        emit stopForceFlushTimer();
        {
            m_IsPaused = true;
            qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex;
            IBufferWriteLocker lockerThis(this, PRETTY_FUNCTION);
            IBufferWriteLocker locker(m_Buffer, PRETTY_FUNCTION);
            m_Buffer->setFrameGroupCapacity(m_SonoParameters->pIV(BFPNames::SystemScanModeStr) ==
                                                    SystemScanModeFourDLive
                                                ? m_SonoParameters->pIV(BFPNames::FourDSliceNumStr)
                                                : 1);
            m_Buffer->setAvailableFrameCapacity(m_SonoParameters->pIV(BFPNames::SystemScanModeStr) ==
                                                        SystemScanModeFourDLive
                                                    ? Setting::instance().defaults().fourDMaxVolumes()
                                                    : -1);
            m_Buffer->setFrameGroupInfo(frameGroupInfo);
            m_IsPaused = false;
        }
        if (m_SyncParameters->isChanged(m_SonoParameters))
        {
#ifdef BUFFER_DEBUG_ENABLE
            qDebug() << "&&&&&" << PRETTY_FUNCTION
#else
            log()->debug()
#endif
                     << " m_SonoBufferIndex:" << m_SonoBufferIndex << " Sync for gstimagetile"
                     << " Update SyncParameters";
            m_SyncParameters->update(m_SonoParameters);
            release();
        }
        //        IBufferReadLocker locker(m_Buffer, PRETTY_FUNCTION);
        //        connectClearAction();
    }
}

void SonoBuffer::setFrameUnitInfo(int index, const FrameUnitInfo& frameUnitInfo)
{
#ifdef BUFFER_DEBUG_ENABLE
    qDebug() << "&&&&&" << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex << "index:" << index;
#endif
    //    ASSERT_LOG(m_SonoParameters->isRealTime() || m_IsLoaded);
    if (!m_IsLoaded)
    {
        if (m_SonoParameters->pBV(BFPNames::FreezeStr))
        {
            qDebug() << "&&&&&"
                     << "Error:" << PRETTY_FUNCTION << " m_SonoBufferIndex:" << m_SonoBufferIndex;
            return;
        }
        emit stopForceFlushTimer();
        IBufferWriteLocker locker(m_Buffer, PRETTY_FUNCTION);
        qDebug() << PRETTY_FUNCTION << "m_SonoBufferIndex:" << m_SonoBufferIndex;
        m_Buffer->setFrameUnitInfo(index, frameUnitInfo);
        //        connectClearAction();
    }
}

void SonoBuffer::connectClearAction()
{
    ASSERT_LOG(m_SonoParameters);

    if (m_SonoParameters == NULL)
    {
        log()->warn("create clear trigger with changed sonobuffers, may be logic error");
        return;
    }
    recreateClearTrigger(false);
}

void SonoBuffer::recreateClearTrigger(bool forceCreate)
{
    if ((m_ClearTrigger != NULL) && !forceCreate)
    {
        log()->debug("no raw image type changed, didn't recreate clear trigger");
        return;
    }
    if (m_ClearTrigger != NULL)
    {
        log()->debug("destroying previous clear trigger");
        m_ClearTrigger->blockSignals(true);
        CHECK_DELETE(MultiParamTrigger, m_ClearTrigger);
        delete m_ClearTrigger;
        m_ClearTrigger = NULL;
    }
    if (!m_SonoParameters)
    {
        log()->debug("sono paramters not setted ,can not connect clear trigger");
        return;
    }
    switch (m_Buffer->frameUnitInfo(m_Buffer->displayBufferCount() - 1).at(0).mode())
    {
        //    case ImageBufferDef::D_Data:
    case ImageBufferDef::Sound_Data:
    {
        m_ClearTrigger =
            new MultiParamTrigger(dynamic_cast<ISonoParameters*>(m_SonoParameters), ClearTriggerSource::clearDSource(),
                                  this, true, DEFAULT_STABLE_DELAY_MS, "D", false);
        CHECK_NEW(MultiParamTrigger, m_ClearTrigger);
        break;
    }
    }
    // not all data have clear trigger assigned
    if (!m_ClearTrigger)
    {
        return;
    }

    connect(m_ClearTrigger, SIGNAL(oneTriggered()), m_Buffer, SLOT(onClearOneTriggered()));
    connect(m_ClearTrigger, SIGNAL(triggerCompleted()), m_Buffer, SLOT(onClearTriggerComplete()));

    m_ClearTrigger->blockSignals(false);
}

void SonoBuffer::cloneDataFrom(SonoBuffer* sonoBuffer)
{
    m_IsPaused = true;
    m_Buffer->cloneFrom(sonoBuffer->m_Buffer, m_Memory);
    // TODO:正常按照大机器的逻辑,切换Layout
    // ECG波形应该会接着之前的数据继续绘制.但是因为目前数据拷贝以及帧索引没有管理好.暂时注释掉这部分代码
    //    if(sonoParameters()->pBV(BFPNames::ECGEnStr))
    //    {
    //        m_Buffer->copyEcgData(sonoBuffer->m_Buffer,
    //                              sonoParameters()->pV(BFPNames::ImageSizeStr).toSize().width() / 2);
    //    }
    m_IsPaused = false;
    if (m_SyncParameters->isChanged(m_SonoParameters))
    {
#ifdef BUFFER_DEBUG_ENABLE
        qDebug() << "&&&&&" << PRETTY_FUNCTION
#else
        log()->debug()
#endif
                 << " m_SonoBufferIndex:" << m_SonoBufferIndex << " Sync for gstimagetile"
                 << " Update SyncParameters";
        m_SyncParameters->update(m_SonoParameters);
        release();
    }
    //    IBufferReadLocker locker(m_Buffer, PRETTY_FUNCTION);
    //    connectClearAction();
}

void SonoBuffer::backupWave()
{
    m_Buffer->backupLastLineImageData(true);
}

void SonoBuffer::backupLastLineImageData()
{
    m_Buffer->backupLastLineImageData();
}

void SonoBuffer::backupLastLineImageData(SonoBuffer* sonoBuffer)
{
    m_Buffer->backupLastLineImageData(sonoBuffer->m_Buffer);
}

void SonoBuffer::recoverLastBackup2DLineImageData()
{
    IBufferReadLocker locker(m_Buffer, PRETTY_FUNCTION);
    m_Buffer->recoverLastBackup2DLineImageData();
}

void SonoBuffer::setUseOldBackUpData(const int imageBufferIndex, const bool useOld)
{
    m_Buffer->setUseOldBackUpData(imageBufferIndex, useOld);
}

void SonoBuffer::setEnableLineImageBackUp(const bool enable)
{
    m_Buffer->setEnableLineImageBackUp(enable);
}

void SonoBuffer::connectSonoParameters()
{
    if (m_SonoParameters == NULL)
    {
        return;
    }
    controlPictureModeOnParametersSignal(true);
    //    connect(m_SonoParameters->parameter(BFPNames::PostTGCStr),
    //            SIGNAL(valueChanged(QVariant)),
    //            this, SLOT(onPostTGCChanged(QVariant)));
    foreach (const QString lgcName, BFPNames::PostLGCStrs)
    {
        connect(m_SonoParameters->parameter(lgcName), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onPostLGCChanged(QVariant)));
    }

    foreach (const QString frameAvgName, m_FrameAvgList)
    {
        connect(m_SonoParameters->parameter(frameAvgName), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFrameAvgChanged()));
    }

    connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onProbeIdChanged(QVariant)));

    connect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr),
            SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
            SLOT(onBeforeFreqSpectrumChanged(const QVariant&, QVariant&)));
    connect(m_SonoParameters->parameter(BFPNames::FrameAvgStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFrameAvgChanged(QVariant)));

    connect(m_SonoParameters->parameter(BFPNames::NeedleModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onNeedleModeChanged(QVariant)));

    connect(m_SonoParameters->parameter(BFPNames::AutoEFCurLayoutStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(updateUnActiveRenderPartition()));
}

void SonoBuffer::disconnectSonoParameters()
{
    if (m_SonoParameters == NULL)
    {
        return;
    }

    controlPictureModeOnParametersSignal(false);
    //    disconnect(m_SonoParameters->parameter(BFPNames::PostTGCStr),
    //               SIGNAL(valueChanged(QVariant)),
    //               this, SLOT(onPostTGCChanged(QVariant)));
    foreach (const QString lgcName, BFPNames::PostLGCStrs)
    {
        disconnect(m_SonoParameters->parameter(lgcName), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onPostLGCChanged(QVariant)));
    }

    foreach (const QString frameAvgName, m_FrameAvgList)
    {
        disconnect(m_SonoParameters->parameter(frameAvgName), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFrameAvgChanged()));
    }

    disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onProbeIdChanged(QVariant)));

    //    disconnect(m_SonoParameters->parameter(BFPNames::FrameAvgStr),
    //            SIGNAL(valueChanged(QVariant)),
    //            this, SLOT(onFrameAvgChanged(QVariant)));

    //    disconnect(m_SonoParameters->parameter(BFPNames::FrameAvgColorStr),
    //            SIGNAL(valueChanged(QVariant)),
    //            this, SLOT(onFrameAvgChanged(QVariant)));

    disconnect(m_SonoParameters->parameter(BFPNames::FreqSpectrumStr),
               SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
               SLOT(onBeforeFreqSpectrumChanged(const QVariant&, QVariant&)));
    disconnect(m_SonoParameters->parameter(BFPNames::FrameAvgStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onFrameAvgChanged(QVariant)));

    disconnect(m_SonoParameters->parameter(BFPNames::NeedleModeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onNeedleModeChanged(QVariant)));

    disconnect(m_SonoParameters->parameter(BFPNames::AutoEFCurLayoutStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(updateUnActiveRenderPartition()));
}

void SonoBuffer::updateUnActiveRenderPartitionRGBAImages(ImageEventArgs* imageEventArgs)
{
    //    QMutexLocker lockerImages(&m_RGBAImagesSync);
    if (nullptr == imageEventArgs || m_UnActiveRenderPartition == Partition_None)
    {
        return;
    }

    if (m_UnActiveRenderPartitionRGBAImages.contains(imageEventArgs->imageType()))
    {
        ImageEventArgs* backUp = m_UnActiveRenderPartitionRGBAImages[imageEventArgs->imageType()];
        if (backUp->imageSize() != imageEventArgs->imageSize())
        {
            delete[] backUp->imageData();
            backUp->setImageData(new uchar[imageEventArgs->imageSize()]);
            backUp->setWidth(imageEventArgs->width());
            backUp->setHeight(imageEventArgs->height());
        }
        memcpy(backUp->imageData(), imageEventArgs->imageData(), imageEventArgs->imageSize());
        backUp->setFrameIndex(imageEventArgs->frameIndex());
        backUp->setECGEnd(imageEventArgs->ecgEnd());
        backUp->setImageRenderPartition(m_UnActiveRenderPartition);
    }
    else
    {
        ImageEventArgs* backUp = new ImageEventArgs();
        *backUp = *imageEventArgs;
        backUp->setImageData(new uchar[imageEventArgs->imageSize()]);
        memcpy(backUp->imageData(), imageEventArgs->imageData(), imageEventArgs->imageSize());
        backUp->setImageRenderPartition(m_UnActiveRenderPartition);
        m_UnActiveRenderPartitionRGBAImages.insert(imageEventArgs->imageType(), backUp);
        CHECK_NEW(ImageEventArgs, backUp);
        CHECK_NEW(char, backUp->imageData());
    }
    //    if(1)
    //    {
    //        ImageEventArgs *save = m_UnActiveRenderPartitionRGBAImages[imageEventArgs->imageType()];
    //        QString filePath = QString("../");
    //        Util::Mkdir(filePath.toStdString().c_str());
    //        ImageSaveHelper::instance()->saveImage32((char*)save->imageData(), save->width(), save->height(),
    //        filePath, false, save->imageType());
    //    }
}

void SonoBuffer::updateUnActiveRenderPartitionRGBAImages(const QHash<int, ImageEventArgs*>& images)
{
    QHash<int, ImageEventArgs*>::ConstIterator it = images.constBegin();
    while (it != images.constEnd())
    {
        updateUnActiveRenderPartitionRGBAImages(it.value());
        it++;
    }
}

void SonoBuffer::controlPictureModeOnParametersSignal(bool connectSignal)
{
    if (connectSignal)
    {
        connect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr),
                SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
                SLOT(onBeforeSystemScanModeChanged(const QVariant&, QVariant&)));

        connect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                this, SLOT(onIsDopplerScanLineVisibleChanged(QVariant)));

        connect(m_SonoParameters->parameter(BFPNames::IsCWDScanLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onIsCWDScanLineVisibleChanged(QVariant)));

        connect(m_SonoParameters->parameter(BFPNames::IsMLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onIsMLineVisibleChanged(QVariant)));

        connect(m_SonoParameters->parameter(BFPNames::LayoutStr),
                SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
                SLOT(onBeforeLayoutChanged(const QVariant&, QVariant&)));
    }
    else
    {
        disconnect(m_SonoParameters->parameter(BFPNames::SystemScanModeStr),
                   SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
                   SLOT(onBeforeSystemScanModeChanged(const QVariant&, QVariant&)));

        disconnect(m_SonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr), SIGNAL(valueChanged(QVariant)),
                   this, SLOT(onIsDopplerScanLineVisibleChanged(QVariant)));

        disconnect(m_SonoParameters->parameter(BFPNames::IsCWDScanLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onIsCWDScanLineVisibleChanged(QVariant)));

        disconnect(m_SonoParameters->parameter(BFPNames::IsMLineVisibleStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onIsMLineVisibleChanged(QVariant)));

        disconnect(m_SonoParameters->parameter(BFPNames::LayoutStr),
                   SIGNAL(beforeValueChanged(const QVariant&, QVariant&)), this,
                   SLOT(onBeforeLayoutChanged(const QVariant&, QVariant&)));
    }
}

// void SonoBuffer::onPostTGCChanged(const QVariant &value)
//{
//    if(m_SonoParameters->pBV(BFPNames::FreezeStr))
//    {
//        requestFlush();
//        QByteArray postTGC = value.toByteArray();
//        QStringList tgcStrs;
//        for(int i = 0; i < TGC_COUNT; ++i)
//        {
//            tgcStrs << QString("%1%2").arg(BFPNames::TGCStr).arg(i + 1);
//        }
//        for(int i=0; i<postTGC.count() && i<tgcStrs.count(); i++)
//        {
//            m_SonoParameters->setPV(tgcStrs[i], (uchar)postTGC[i]);
//        }
//    }
//}

void SonoBuffer::onPostLGCChanged(const QVariant& value)
{
    if (m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        requestFlush();
    }
}

void SonoBuffer::onProbeIdChanged(const QVariant& value)
{
    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(value.toInt());
    QSize imageWaveMaxSize = m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    QSize image2DMaxSize =
        QSize(m_SonoParameters->pIV(BFPNames::PointNumPerLineStr), probeDataInfo.IsSupportHighDensity ? 512 : 256);
    /**
     * 更新源于Atom的compound档位扩充，原来的 复合帧数 = scpd + 1 不满足，更新之后，兼容原来的情况
     *    scpd值：O(Off)     1     2     3     4     5    6
     *   复合帧数：  1        2     3     5     7     9    11
     * 下面构建的LineImageBackUpInfo的第一个参数，传入的应该是最大的帧数
     */
    LineImageBackUpInfo libui((m_SonoParameters->pMax(BFPNames::ScpdStr) <= 2
                                   ? m_SonoParameters->pMax(BFPNames::ScpdStr) + 1
                                   : 2 * m_SonoParameters->pMax(BFPNames::ScpdStr) - 1),
                              imageWaveMaxSize, image2DMaxSize);
    m_Buffer->updateLineImageBackUpInfo(libui);
}

void SonoBuffer::onBeforeBuffersChanged()
{
    emit beforeBuffersChanged(m_SonoBufferIndex);
}

void SonoBuffer::onBuffersChanged()
{
    emit buffersChanged(m_SonoBufferIndex);
}
