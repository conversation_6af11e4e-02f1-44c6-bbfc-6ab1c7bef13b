#ifndef BUFFERQUEUE_H
#define BUFFERQUEUE_H

#include "buffer_global.h"

#include <QtGlobal>
#include <QList>
#include <QVector>
#include "bytebuffer.h"
#include "bufferunit.h"

/**
 * @brief 图像帧的循环队列，由于需要实现在固定大小内存块上分配预先
 * 定义的队列长度，所以使用单独的标志位来判断队满和队空，不浪费一个元素的空间
 *
 * BufferQueue长度为size = 9
 * elementSizes.count() = 4
 *
 * elementSizes =   {     11,   2,        20,                17      }
 * ByteBuffer =           1     1         1                  1
 *
 * BufferUnit0       -----------++--------------------+++++++++++++++++
 * BufferUnit1       -----------++--------------------+++++++++++++++++
 * BufferUnit2       -----------++--------------------+++++++++++++++++
 * BufferUnit3       -----------++--------------------+++++++++++++++++
 * BufferUnit4       -----------++--------------------+++++++++++++++++
 * BufferUnit5       -----------++--------------------+++++++++++++++++
 * BufferUnit6       -----------++--------------------+++++++++++++++++
 * BufferUnit7       -----------++--------------------+++++++++++++++++
 * BufferUnit8       -----------++--------------------+++++++++++++++++
 *
 * 上图实例Queue长度为size=9，每一行代表一个BufferUnit，N个-号或者N个+号代表ByteBuffer，一个-号或者一个+号代表一个byte(uchar)
 *
 * 代表一种类型的数据，如B模式下512M内存的BufferQueue全是B的数据，BM模式会有一个B的BufferQueue和一个M的BufferQueue
 */
class USF_INTERFACE_IMAGING_BUFFERSHARED_EXPORT BufferQueue
{
public:
    BufferQueue();
    /**
     * @brief BufferQueue
     * @param 内存地址
     * @param 队列中每一个元素又拆分成N 个ByteBuffer，并描述每个ByteBuffer size
     * @param 队列的size
     */
    BufferQueue(uchar* mem, const QList<int>& elementSizes, const int size);
    ~BufferQueue(void);

    void debugInfo() const;
    /**
     * @brief construct
     * @param 内存地址
     * @param 队列中每一个元素又拆分成N 个ByteBuffer，并描述每个ByteBuffer size
     * @param 队列的size
     */
    void construct(uchar* mem, const QList<int>& elementSizes, const int size);
    /**
     * @brief 队列中每一个元素又拆分成N 个ByteBuffer，并描述每个ByteBuffer size
     * @return
     */
    const QList<int>& elementSizes() const;
    /**
     * @brief elementSize 队列中一个BufferUnit的size, 即elementSizes集合中所有ByteBuffer大小的和
     * @return
     */
    int elementSize() const;
    /**
     * @brief memorySize 队列所有元素总内存大小
     * @return
     */
    int memorySize() const;
    /**
     * @brief fixedSize 队列大小
     * @return
     */
    int fixedSize(void) const;
    /**
     * @brief usedSize 队列可用大小，0 <= usedSize <= fixedSize
     * @return
     */
    int usedSize() const;
    /**
     * @brief setUsedSize 设置队列可用大小，0 <= usedSize <= fixedSize
     * @param value
     */
    void setUsedSize(int value = 0);
    /**
     * @brief length 根据入队出队情况，获取实际有数据的长度 0 <= length <= usedSize <= fixedSize
     * @return
     */
    int length(void) const;
    BufferUnit front(void) const;
    BufferUnit rear(void) const;
    BufferUnit* getRear();
    BufferUnit at(int index) const;
    /**
     * @brief getByIndex
     * @param index 相对frontValue的index,即从用户角度理解的索引，0代表用户角度的第一帧
     * @return
     */
    const BufferUnit* getByIndex(int index) const;
    BufferUnit* getByIndex(int index);
    /**
     * @brief getByRealIndex
     * @param index 相对与m_Queue的index，0不一定是第一帧
     * @return
     */
    const BufferUnit* getByRealIndex(int index) const;
    BufferUnit* getByRealIndex(int index);
    BufferUnit operator[](int index) const;
    QList<BufferUnit> getElements(int index, int n) const;
    bool isEmpty(void) const;
    bool isFull(void) const;
    bool isNull() const;
    bool isValid() const;
    /**
     * @brief nextRear  返回要操作的下一个队尾的元素，让客户端直接操作内存，这样可以避免
     * 客户端单独分配一块这样的内存，此函数配合 \a enqueue() 使用，对数据操作结束，调用
     * enqueue()进队
     *
     * @param enqueue 如果调用nextRear后一定会调用enqueue，必须设为true，
     * 如果调用nextRear后不是用enqueue,必须设为false
     *
     * @note 调用者负责检查queue.isempty
     *
     * @return
     */
    BufferUnit& nextRear(bool enqueue = true);
    /**
     * @brief enqueue 在对 \a nextRear() 操作结束后，调用此函数，维护队尾
     */
    void enqueue();
    void enqueue(const BufferUnit& element);
    bool dequeueRear();
    void clear();
    int frontValue() const;
    int rearValue() const;

private:
    void increaseFront(void);

private:
    QVector<BufferUnit> m_Queue;
    uchar* m_Memory;
    QList<int> m_ElementSizes;
    volatile int m_Front;
    volatile int m_Rear;
    int m_Size;
    int m_FixedSize;
    volatile bool m_EnqueueFlag;
};

#endif // BUFFERQUEUE_H
