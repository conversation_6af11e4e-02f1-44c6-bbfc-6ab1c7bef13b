#ifndef ISONOBUFFER_H
#define ISONOBUFFER_H

#include "irawimagebufferdatagetter.h"
#include "basebufferreadwritelock.h"

#include <QList>
#include <QHash>
#include <QObject>

class SonoParameters;

class USF_INTERFACE_IMAGING_LINEIMAGEDATACOMMONUNIT_EXPORT ISonoBuffer : public QObject,
                                                                         public IRawImageBufferDataGetter,
                                                                         public BaseBufferReadWriteLock
{
    Q_OBJECT
public:
    ISonoBuffer(QObject* parent = 0);
    virtual ~ISonoBuffer();

    virtual SonoParameters* sonoParameters() const = 0;
    virtual QList<FrameUnitInfo> dispalyFrameUnitInfos() = 0;

    virtual void acquire(int n = 1) = 0;
    virtual void release(int n = 1) = 0;

    virtual bool isLoad() const = 0;

    virtual int frameAvgCount() = 0;

    virtual void setRawDataImage(uchar* data, int width, int height) = 0;

    virtual void setGrayRawDataImage(uchar* data, int width, int height) = 0;

public slots:
    virtual void forceFlush() = 0;

    virtual void onRequesFlushByBufferIndex(const int bufferIndex) = 0;

signals:
    /**
     * @brief 在重新构建ImageBuffer、WaveImageBuffer时，由ImageBufferGroup 发出
     *
     * @param frameTypes QHash<ImageBufferIndex,ImageBufferDef::RawDataMode>
     */
    void changeFrameTypes(const QHash<int, int> frameTypes, const bool isLoaded);

    void clearFrameTypes();
    /**
     * @brief 由ImageBuffer发出，通知启动获取数据线程
     */
    void timerStart(const int imageBufferIndex);
    /**
     * @brief 通知修改Fps
     * @param fps
     */
    void changeFps(const int imageBufferIndex, double fps);
    /**
     * @brief 通知修改wavedisplayfps
     * @param fps
     */
    void changeWaveDisplayFps(const int imageBufferIndex, double fps);
    /**
     * @brief 通知队列清空
     */
    void cacheQueueCleard(const int imageBufferIndex);
    /**
     * @brief 通知当前显示index变化
     * @param index
     */
    void currentIndexChanged(const int currentIndex, const int bufferIndex = -1, bool isMeasureData = false);
    /**
     * @brief beforeBuffersChanged
     *  缓冲区被销毁
     */
    void beforeBuffersChanged(int sonobufferIndex);

    void buffersChanged(int sonobufferIndex);
    /**
     * @brief beforeBufferCleared
     * Buffer 被清空
     */
    void beforeBufferCleared();

    void bufferClearing();

    void bufferCleared();
    /**
     * @brief 超声参数发生变化
     */
    void beforeSonoParametersChanged();
    void beforeSonoParametersChanged(SonoParameters* oldSonoParameters, SonoParameters* newSonoParameters);
    void sonoParametersChanging();
    void sonoParametersChanged();
    void sonoParametersChanged(SonoParameters* newSonoParameters);
    //    void clearSonoParameters();
    /**
     * @brief 冻结状态发生变化
     */
    void freezeChanged(bool isFrozen);
    /**
     * @brief 电影播放状态发生变化
     */
    void playCineStatusChanged(bool playCineStatus);

    void lineImageBufferChanged();
    /**
     * @brief clearFrozenIndex
     * 加载完成后需要清空GstImageTile的frozenIndex
     */
    void clearFrozenIndex();

    void activeChanged(const bool activeStatus, const int rawDataMode);
    /**
     * @brief requestFlushNextBuffer
     * 请求BufferPushControler刷新指定Buffer Index区域的图像
     */
    void requestFlushNextBuffer(int imageBufferIndex);

    //当前sonobuffer对应的指定类型的buffer被清空
    void beforeBufferCleared(int bufferGroupIndex);

    void bufferCleared(int bufferGroupIndex);

    void getSyncId(int& syncId);
};

#endif // ISONOBUFFER_H
