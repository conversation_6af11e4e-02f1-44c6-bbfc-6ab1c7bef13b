#include "bytelineimageargs.h"
#include "imageeventargs.h"

ByteLineImageArgs::ByteLineImageArgs()
    : m_ImageEventArgs(new ImageEventArgs)
    , m_IsClearColorPoint(false)
    , m_IsPrepareToDetory(false)
{
}

ByteLineImageArgs::~ByteLineImageArgs()
{
    if (m_ImageEventArgs != NULL)
    {
        delete m_ImageEventArgs;
        m_ImageEventArgs = NULL;
    }
}

ImageEventArgs* ByteLineImageArgs::imageEvnetArgs() const
{
    return m_ImageEventArgs;
}

int ByteLineImageArgs::lineImageArgsType() const
{
    return ByteLineImageArgs::Type_Byte;
}

void ByteLineImageArgs::setImageType(const int imageType)
{
    m_ImageEventArgs->setImageType((ImageEventArgs::ImageType)imageType);
}

int ByteLineImageArgs::imageType() const
{
    return m_ImageEventArgs->imageType();
}

QList<void*> ByteLineImageArgs::imageData()
{
    QList<void*> ret;
    ret.append((void*)m_ImageEventArgs->imageData());
    return ret;
}

int ByteLineImageArgs::width()
{
    return m_ImageEventArgs->width();
}

int ByteLineImageArgs::height()
{
    return m_ImageEventArgs->height();
}

int ByteLineImageArgs::bitCount()
{
    return m_ImageEventArgs->bitCount();
}

void ByteLineImageArgs::release()
{
    m_IsClearColorPoint = false;
    m_IsPrepareToDetory = false;
}

void ByteLineImageArgs::setClearColorPoint(const bool isClear)
{
    m_IsClearColorPoint = isClear;
}

bool ByteLineImageArgs::clearColorPoint() const
{
    return m_IsClearColorPoint;
}

void ByteLineImageArgs::setIndex(const int index)
{
    m_ImageEventArgs->setIndex(index);
}

int ByteLineImageArgs::index() const
{
    return m_ImageEventArgs->index();
}

int ByteLineImageArgs::frameIndex() const
{
    return m_ImageEventArgs->frameIndex();
}

qint64 ByteLineImageArgs::getFrameStartTime() const
{
    return m_ImageEventArgs->getFrameStartTime();
}

bool ByteLineImageArgs::copyFrom(LineImageArgs* lineImageArgs)
{
    if ((lineImageArgs->lineImageArgsType() == ByteLineImageArgs::Type_Byte) && !m_IsPrepareToDetory)
    {
        // 2025-06-13 Write by AlexWang
        *m_ImageEventArgs = *(lineImageArgs->imageEvnetArgs());
        m_IsClearColorPoint = lineImageArgs->clearColorPoint();
        m_IsPrepareToDetory = lineImageArgs->isPrepareToDestory();
        return true;
    }
    return false;
}

bool ByteLineImageArgs::copyFrom(ImageEventArgs* imageEventArgs)
{
    if (!m_IsPrepareToDetory)
    {
        uchar* imageData = imageEventArgs->imageData();
        *m_ImageEventArgs = *imageEventArgs;
        m_ImageEventArgs->setImageData(imageData);
        return true;
    }
    return false;
}

bool ByteLineImageArgs::copyInfo2ImageEventArgs(ImageEventArgs* imageEventArgs)
{
    if (!m_IsPrepareToDetory)
    {
        uchar* imageData = imageEventArgs->imageData();
        *imageEventArgs = *m_ImageEventArgs;
        imageEventArgs->setImageData(imageData);
        return true;
    }
    return false;
}

bool ByteLineImageArgs::isPrepareToDestory() const
{
    return m_IsPrepareToDetory;
}

void ByteLineImageArgs::setPrepareToDestory()
{
    m_IsPrepareToDetory = true;
}
