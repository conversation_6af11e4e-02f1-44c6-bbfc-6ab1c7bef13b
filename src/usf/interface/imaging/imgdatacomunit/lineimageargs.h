#ifndef LINEIMAGEARGS_H
#define LINEIMAGEARGS_H

#include "lineimagedatacommonunit_global.h"
#include <QList>

class ImageEventArgs;

class USF_INTERFACE_IMAGING_LINEIMAGEDATACOMMONUNIT_EXPORT LineImageArgs
{
public:
    enum LineImageArgsType
    {
        Type_UnDefine = 0
    };

    virtual ~LineImageArgs();

    /** 2025-06-10 Write by AlexWang
     * @brief imageEvnetArgs
     * @return
     */
    virtual ImageEventArgs* imageEvnetArgs() const = 0;

    virtual int lineImageArgsType() const = 0;

    virtual void setImageType(const int imageType) = 0;
    virtual int imageType() const = 0;

    virtual QList<void*> imageData() = 0;

    virtual int width() = 0;

    virtual int height() = 0;

    virtual int bitCount() = 0;

    virtual void release() = 0;

    virtual void setClearColorPoint(const bool isClear) = 0;
    virtual bool clearColorPoint() const = 0;

    virtual void setIndex(const int index) = 0;
    virtual int index() const = 0;

    virtual int frameIndex() const = 0;

    /** 2025-06-10 Write by AlexWang
     * @brief getFrameStartTime
     * @return
     */
    virtual qint64 getFrameStartTime() const = 0;

    virtual bool copyFrom(LineImageArgs* lineImageArgs) = 0;
    virtual bool copyFrom(ImageEventArgs* imageEventArgs) = 0;

    virtual bool copyInfo2ImageEventArgs(ImageEventArgs* imageEventArgs) = 0;

    virtual bool isPrepareToDestory() const = 0;
    virtual void setPrepareToDestory() = 0;
};

#endif // LINEIMAGEARGS_H
