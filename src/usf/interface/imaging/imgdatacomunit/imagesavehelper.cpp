#include "imagesavehelper.h"
#include <QImage>
#include <QSettings>
#include <QFile>
#include <QDebug>
#include "assertlog.h"
#include "syncidmanager.h"

static const char* ConfigFile = "./gstreamerAdapterDebugConfig.ini";

static const char* IniField = "gstreamerpipeline";

static const char* IniBoolField[] = {"Bool_SaveOutGstreamerPipelineImage",
                                     "Bool_SaveInGstreamerPipelineImage",
                                     "Bool_SaveOutOpenGlImage",
                                     "Bool_SaveInOpenGlImage",
                                     "Bool_SaveEnqueueImage",
                                     "Bool_SaveSteeringInImage",
                                     "Bool_SaveSteeringOutImage",
                                     "Bool_SaveCompoundOutImage",
                                     NULL};

static const char* IniIntField[] = {"Int_SaveOutGstreamerPipelineImage",
                                    "Int_SaveInGstreamerPipelineImage",
                                    "Int_SaveOutOpenGlImage",
                                    "Int_SaveInOpenGlImage",
                                    "Int_SaveEnqueueImage",
                                    "Int_SaveSteeringInImage",
                                    "Int_SaveSteeringOutImage",
                                    "Int_SaveCompoundOutImage",
                                    NULL};

static const char* IniStringField[] = {"String_SaveOutGstreamerPipelineImage",
                                       "String_SaveInGstreamerPipelineImage",
                                       "String_SaveOutOpenGlImage",
                                       "String_SaveInOpenGlImage",
                                       "String_SaveEnqueueImage",
                                       "String_SaveSteeringInImage",
                                       "String_SaveSteeringOutImage",
                                       "String_SaveCompoundOutImage",
                                       NULL};

ImageSaveHelper::ImageSaveHelper(QObject* parent)
    : IImageSaveHelper(parent)
{
    initSetting();
    if (m_GrayTable.isEmpty())
    {
        for (int i = 0; i < 256; i++)
        {
            m_GrayTable.append(qRgb(i, i, i));
        }
    }
}

ImageSaveHelper::~ImageSaveHelper()
{
}

void ImageSaveHelper::saveImage(ImageEventArgs* imageEventArgs, const QString& path)
{
    if (imageEventArgs->imageSize() <= 0)
    {
        qDebug() << PRETTY_FUNCTION << "imageEventArgs->imageSize() <= 0";
        return;
    }

    uchar* data = (uchar*)malloc(imageEventArgs->imageSize() * sizeof(uchar));
    if (data == NULL)
    {
        return;
    }
    memcpy(data, imageEventArgs->imageData(), imageEventArgs->imageSize());

    QImage* image = NULL;

    if (imageEventArgs->bitCount() == 8)
    {
        image =
            new QImage((const uchar*)data, imageEventArgs->width(), imageEventArgs->height(), QImage::Format_Indexed8);
        image->setColorTable(m_GrayTable);
    }
    else if (imageEventArgs->bitCount() == 24)
    {
        image =
            new QImage((const uchar*)data, imageEventArgs->width(), imageEventArgs->height(), QImage::Format_RGB888);
    }
    else if (imageEventArgs->bitCount() == 32)
    {
        image = new QImage((const uchar*)data, imageEventArgs->width(), imageEventArgs->height(), QImage::Format_RGB32);
    }
    else
    {
        qDebug() << PRETTY_FUNCTION << "Error With imageEventArgs->bitCount()" << imageEventArgs->bitCount();
    }

    if (image == NULL)
    {
        free(data);
        qDebug() << PRETTY_FUNCTION << "image == NULL";

        return;
    }
    if (image->isNull())
    {
        free(data);
        free(image);
        qDebug() << PRETTY_FUNCTION << "image->isNull()";
        return;
    }

    image->save(QString("%1/%2_%3_%4_%5.jpeg")
                    .arg(path)
                    .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
                    .arg(imageEventArgs->imageType())
                    .arg(imageEventArgs->index())
                    .arg(imageEventArgs->frameIndex()));
    free(data);
    free(image);
}

void ImageSaveHelper::saveImage2Hex(ImageEventArgs* imageEventArgs, const QString& path)
{
    if (imageEventArgs->imageSize() <= 0)
    {
        qDebug() << "ImageEventArgs.imageSize is not valid";
        return;
    }

    QString filePath = QString("%1/%2_%3_%d.dat")
                           .arg(path)
                           .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
                           .arg(imageEventArgs->imageType())
                           .arg(imageEventArgs->index());
    QFile file(filePath);
    if (file.open(QFile::WriteOnly))
    {
        file.write((char*)imageEventArgs->imageData(), imageEventArgs->imageSize());
    }
    file.close();
}

void ImageSaveHelper::saveImage(const BufferUnit& bufferUnit, const FrameUnitInfo& frameUnitInfo, const QString& path)
{
    int imageSize = bufferUnit.size();
    int frameSize = frameUnitInfo.sizes().size();
    imageSize = imageSize > frameSize ? frameSize : imageSize;
    for (int i = 0; i < bufferUnit.count(); i++)
    {
        const ByteBuffer& byteBuffer = bufferUnit[i];
        const FrameInfo& frameInfo = frameUnitInfo[i];

        if ((byteBuffer.len() <= 0) || (byteBuffer.data() == NULL))
        {
            qDebug() << "ByteBuffer.len is not valid";
            continue;
        }

        uchar* data = (uchar*)malloc(byteBuffer.len() * sizeof(uchar));
        if (data == NULL)
        {
            return;
        }
        memcpy(data, byteBuffer.data(), byteBuffer.len());

        QImage image((const uchar*)data, byteBuffer.len() / frameInfo.lineNum(), frameInfo.lineNum(),
                     QImage::Format_Indexed8);
        if (image.isNull())
        {
            continue;
        }
        image.setColorTable(m_GrayTable);
        image.save(QString("%1/%2_%3_%4_%5_%7.jpeg")
                       .arg(path)
                       .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
                       .arg(i)
                       .arg(bufferUnit.steering())
                       .arg(frameUnitInfo.first().mode())
                       .arg(bufferUnit.frameIndex()));
        free(data);
    }
}

void ImageSaveHelper::saveImage2Hex(BufferUnit& bufferUnit, const FrameUnitInfo& frameUnitInfo, const QString& path)
{
    int imageSize = bufferUnit.size();
    imageSize = imageSize > frameUnitInfo.size() ? frameUnitInfo.size() : imageSize;
    for (int i = 0; i < imageSize; i++)
    {
        ByteBuffer& byteBuffer = bufferUnit[i];

        if (byteBuffer.len() <= 0)
        {
            qDebug() << "ByteBuffer.len is not valid";
            continue;
        }

        QString filePath = QString("%1/%2_%3_%4.dat")
                               .arg(path)
                               .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
                               .arg(i)
                               .arg(bufferUnit.steering());
        QFile file(filePath);
        if (file.open(QFile::WriteOnly))
        {
            file.write((char*)byteBuffer.data(), byteBuffer.len());
        }
        file.close();
    }
}

void ImageSaveHelper::saveImage(uchar* data, int width, int height, const QString& path, const int index)
{
    if (width * height <= 0)
    {
        qDebug() << "width * height is not valid";
        return;
    }

    uchar* uCharData = (uchar*)malloc(width * height * sizeof(uchar));
    if (data == NULL)
    {
        return;
    }
    memcpy(uCharData, data, width * height);

    QImage image((const uchar*)uCharData, width, height, QImage::Format_Indexed8);
    if (image.isNull())
    {
        free(data);
    }
    image.setColorTable(m_GrayTable);
    image.save(
        QString("%1/%2_%3.jpeg").arg(path).arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz")).arg(index));
    free(uCharData);
}

void ImageSaveHelper::saveImage(char* data, int width, int height, const QString& path, const bool assist,
                                const int index)
{
    if (width * height <= 0)
    {
        qDebug() << "width * height is not valid";
        return;
    }

    uchar* uCharData = (uchar*)malloc(width * height * sizeof(uchar));
    if (data == NULL)
    {
        return;
    }
    memcpy(uCharData, data, width * height);

    QImage image((const uchar*)uCharData, width, height, QImage::Format_Indexed8);
    if (image.isNull())
    {
        free(data);
    }
    image.setColorTable(m_GrayTable);
    image.save(QString("%1/%2_%3_%4.jpeg")
                   .arg(path)
                   .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
                   .arg(index)
                   .arg(assist ? 0 : 1));
    free(uCharData);
}

void ImageSaveHelper::saveImage8(char* data, int width, int height, const QString& path)
{
    if (width * height <= 0)
    {
        qDebug() << "width * height is not valid";
        return;
    }

    uchar* uCharData = (uchar*)malloc(width * height * sizeof(uchar));
    if (data == NULL)
    {
        return;
    }
    memcpy(uCharData, data, width * height);

    QImage image((const uchar*)uCharData, width, height, QImage::Format_Indexed8);
    if (image.isNull())
    {
        free(data);
    }
    image.setColorTable(m_GrayTable);
    image.save(path);
    free(uCharData);
}

void ImageSaveHelper::saveImage32(char* data, int width, int height, const QString& path, const bool assist,
                                  const int index)
{
    if (width * height <= 0)
    {
        qDebug() << "width * height is not valid";
        return;
    }

    static uchar* uCharData = NULL;
    if (uCharData == NULL)
    {
        uCharData = (uchar*)malloc(width * height * sizeof(uchar) * 4);
    }
    memcpy(uCharData, data, width * height * 4);

    QImage image((const uchar*)uCharData, width, height, QImage::Format_RGB32);
    if (image.isNull())
    {
        //        free(data);
    }
    image.save(QString("%1%2_%3_%4_%5.jpeg")
                   .arg(path)
                   .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
                   .arg(index)
                   .arg(assist ? 0 : 1)
                   .arg(SyncIDManager::instance().currentSyncId(SyncIDMode::PrePostSyncId)));
    //    free(uCharData);
}

void ImageSaveHelper::saveImageHex32(char* data, int width, int height, const QString& path, const bool assist,
                                     const int index)
{
    if (width * height <= 0)
    {
        qDebug() << "width * height is not valid";
        return;
    }

    QString filePath = QString("%1/%2_%3_%4.dat")
                           .arg(path)
                           .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
                           .arg(index)
                           .arg(assist ? 0 : 1);

    QFile file(filePath);
    if (file.open(QFile::WriteOnly))
    {
        file.write(data, width * height * 4);
    }
    file.close();
}

void ImageSaveHelper::saveImage2Hex(char* data, int width, int height, const QString& path, const bool assist)
{
    if (width * height <= 0)
    {
        qDebug() << "width * height is not valid";
        return;
    }

    QString filePath = QString("%1/%2_%3.dat")
                           .arg(path)
                           .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"))
                           .arg(assist ? 0 : 1);
    QFile file(filePath);
    if (file.open(QFile::WriteOnly))
    {
        file.write((char*)data, width * height);
    }
    file.close();
}

void ImageSaveHelper::saveImage2Hex(char* data, int width, int height, const QString& path, const int index)
{
    if (width * height <= 0)
    {
        qDebug() << "width * height is not valid";
        return;
    }

    QString filePath =
        QString("%1/%2_%3.dat").arg(path).arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz")).arg(index);
    QFile file(filePath);
    if (file.open(QFile::WriteOnly))
    {
        file.write((char*)data, width * height);
    }
    file.close();
}

void ImageSaveHelper::saveImage8AndHex(char* data, int width, int height, const QString& path, const int index)
{
    if (width * height <= 0)
    {
        qDebug() << "width * height is not valid";
        return;
    }

    if (data == NULL)
    {
        return;
    }
    QString timeStr = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");

    QString filePath = QString("%1/%2_%3.dat").arg(path).arg(timeStr).arg(index);
    QFile file(filePath);
    if (file.open(QFile::WriteOnly))
    {
        file.write((char*)data, width * height);
    }
    file.close();

    uchar* uCharData = (uchar*)malloc(width * height * sizeof(uchar));
    memcpy(uCharData, data, width * height);

    QImage image((const uchar*)uCharData, width, height, QImage::Format_Indexed8);
    if (image.isNull())
    {
        free(data);
    }
    image.setColorTable(m_GrayTable);
    image.save(QString("%1/%2_%3.jpeg").arg(path).arg(timeStr).arg(index));
    free(uCharData);
}

bool ImageSaveHelper::enableSaveOutGstreamerPipelineImage()
{
    return m_BoolMap.value("Bool_SaveOutGstreamerPipelineImage", false);
}

int ImageSaveHelper::saveOutGstreamerPipelineImageCount()
{
    return m_IntMap.value("Int_SaveOutGstreamerPipelineImage", DefalutSaveCounter);
}

QString ImageSaveHelper::saveOutGstreamerPipelineImagePath()
{
    return m_StringMap.value("String_SaveOutGstreamerPipelineImage", "./");
}

bool ImageSaveHelper::enableSaveInGstreamerPipelineImage()
{
    return m_BoolMap.value("Bool_SaveInGstreamerPipelineImage", false);
}

int ImageSaveHelper::saveInGstreamerPipelineImageCount()
{
    return m_IntMap.value("Int_SaveInGstreamerPipelineImage", DefalutSaveCounter);
}

QString ImageSaveHelper::saveInGstreamerPipelineImagePath()
{
    return m_StringMap.value("String_SaveInGstreamerPipelineImage", "./");
}

bool ImageSaveHelper::enableSaveOutOpenGlImage()
{
    return m_BoolMap.value("Bool_SaveOutOpenGlImage", false);
}

int ImageSaveHelper::saveOutOpenGlImageCount()
{
    return m_IntMap.value("Int_SaveOutOpenGlImage", DefalutSaveCounter);
}

QString ImageSaveHelper::saveOutOpenGlImagePath()
{
    return m_StringMap.value("String_SaveOutOpenGlImage", "./");
}

bool ImageSaveHelper::enableSaveInOpenGlImage()
{
    return m_BoolMap.value("Bool_SaveInOpenGlImage", false);
}

int ImageSaveHelper::saveInOpenGlImageCount()
{
    return m_IntMap.value("Int_SaveInOpenGlImage", DefalutSaveCounter);
}

QString ImageSaveHelper::saveInOpenGlImagePath()
{
    return m_StringMap.value("String_SaveInOpenGlImage", "./");
}

void ImageSaveHelper::initSetting()
{
    QFile file(ConfigFile);
    if (file.exists())
    {
        QSettings iniReader(ConfigFile, QSettings::IniFormat);
        const char** field = IniBoolField;
        while (*field != NULL)
        {
            m_BoolMap.insert(*field, iniReader.value(QString("%1/%2").arg(IniField).arg(*field), false).toBool());
            field++;
        }
        field = IniIntField;
        while (*field != NULL)
        {
            m_IntMap.insert(*field,
                            iniReader.value(QString("%1/%2").arg(IniField).arg(*field), DefalutSaveCounter).toInt());
            field++;
        }
        field = IniStringField;
        while (*field != NULL)
        {
            m_StringMap.insert(*field, iniReader.value(QString("%1/%2").arg(IniField).arg(*field), "./").toString());
            field++;
        }
    }
}
