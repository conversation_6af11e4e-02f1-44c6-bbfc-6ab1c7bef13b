#ifndef IRAWIMAGEBUFFERDATASETTER_H
#define IRAWIMAGEBUFFERDATASETTER_H

#include "basebufferreadwritelock.h"
#include <QList>

class FrameUnitInfo;
class BufferStates;
class BufferUnit;
/**
 * @brief The IRawImageBufferDataSetter class
 *
 * 定义IImageDataReceiver写入图像缓冲的接口
 * 所有方法调用在加锁的条件下，所以实现方法的时候不需要加锁
 *
 * 在ImageDataReceiver中，当数据包解析后，通过IRawImageBufferDataSetter获取BufferUnit，并写入图像数据
 *
 */
class USF_INTERFACE_IMAGING_LINEIMAGEDATACOMMONUNIT_EXPORT IRawImageBufferDataSetter : public BaseBufferReadWriteLock
{
public:
    virtual ~IRawImageBufferDataSetter();

    virtual const QList<FrameUnitInfo>& frameGroupInfo() const = 0;
    /**
     * @brief 获取对应类型的FrameInfo
     * @param bufferIndex -- 使用的缓冲区索引
     *                          eg.ImageBufferIndex
     * @return
     */
    virtual const FrameUnitInfo& frameUnitInfo(const int bufferIndex) const = 0;
    /**
     * @brief 获取缓冲区的个数ImageBuffer的个数
     * @return
     */
    virtual int bufferCount() const = 0;
    /**
     * @brief clear 清空缓冲区
     */
    virtual void clear(bool clearUnActive = true) = 0;
    /**
     * @brief 返回对应的缓冲区当前是否可写
     * @param bufferIndex
     * @return
     */
    virtual bool isWriteAble(const int bufferIndex) const = 0;
    /**
     * @brief 设置当前缓冲区数据的状态
     * @param bufferIndex -- 使用的缓冲区索引
     *                          eg.ImageBufferIndex
     * @param frameIndex  -- 使用的缓冲区中的帧索引
     *                          eg. B/C公用一个ImageBuffer， B's frameIndex 0 C's frameIndex 1
     * @param value
     * @return
     */
    virtual bool setCurrentBufferState(const int bufferIndex, const int frameIndex, const int value) = 0;

    virtual const BufferStates& currentBufferStates(const int bufferIndex) const = 0;
    /**
     * @brief 设置新数据缓冲区的数据的状态
     *  新数据缓冲区：在接收BC数据的时候，先接收B，再接收C
     *  在C没有结束的时候，又会有B数据过来，此时B数据状态暂放在NewBufferState中
     *  等C数据接收完成，在依据NewBufferState中数据的状态判断是否将数据拷贝到currentBufferState中
     */
    virtual bool setNewRearState(const int bufferIndex, const int frameIndex, const int value) = 0;

    virtual const BufferStates& newRearStates(const int bufferIndex) const = 0;

    virtual BufferUnit& nextRear(const int bufferIndex, bool enqueue = true) = 0;
    virtual BufferUnit& newRear(const int bufferIndex) = 0;
    virtual void enqueue(const int bufferIndex, const bool refresh = true) = 0;
    virtual bool canEnqueue(const int bufferIndex) = 0;

    virtual void updateNextRearStartTime(const int bufferIndex) = 0;
    /**
     * @brief updateFreqSpectrum 更新上下激活,三同步,四同步
     * @param status
     */
    virtual void updateFreqSpectrum(const bool status) = 0;
    virtual void updateTriplexMode(const bool status) = 0;
    virtual void updateQuadplexMode(const bool status) = 0;

    /** 2025-06-10 Write by AlexWang
     * @brief scpdValue
     * @return
     */
    virtual int scpdValue() const = 0;

    /** 2025-06-10 Write by AlexWang
     * @brief frameCountByFrameType
     * @param frameType
     * @return
     */
    virtual int frameCountByFrameType(int frameType) const = 0;
};

#endif // IRAWIMAGEBUFFERDATASETTER_H
