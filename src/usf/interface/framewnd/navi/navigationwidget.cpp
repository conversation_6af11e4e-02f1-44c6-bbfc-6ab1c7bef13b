#include "navigationwidget.h"
#include "ui_navigationwidget.h"
#include "systemhintmodel.h"
#include "hotkey/hotkeyconfig.h"
#include "hotkey/hotkeycontainer.h"
#include "hotkey/hotkeyfunction.h"
#include "util.h"
#include <QFontMetrics>
#include "hotkeypopupwidget.h"
#include "modelconfig.h"
#include "entitykey.h"

// TODO: 功能开关，功能还没开发好，临时利用这个变量对功能进行限制; 后续放开时只需要revert此提交
static const bool FUNC_SWITCH = false;

NavigationWidget::NavigationWidget(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::NavigationWidget)
    , m_HotKeyContainer(nullptr)
    , m_HotKeyConfig(nullptr)
    , m_Model(nullptr)
    , m_HotkeyPopupDialog(nullptr)
{
    ui->setupUi(this);

    if (!FUNC_SWITCH)
    {
        ui->labelHelpInfo->setText("Help Information!");
    }
}

NavigationWidget::~NavigationWidget()
{
    delete ui;
}

void NavigationWidget::setModel(SystemHintModel* model)
{
    m_Model = model;
    if (m_Model != NULL)
    {
        connect(m_Model, SIGNAL(printKeyFuncChanged()), this, SLOT(updateHotKey()));
        connect(m_Model, SIGNAL(rawTBStatusChanged(bool)), this, SLOT(onRawTBStatusChanged(bool)));
        connect(m_Model, SIGNAL(leftButtonStatusChanged()), this, SLOT(onLeftButtonStatusChanged()));
        connect(m_Model, SIGNAL(rightButtonStatusChanged()), this, SLOT(onRightButtonStatusChanged()));

        onRawTBStatusChanged(false);
        onLeftButtonStatusChanged();
        onRightButtonStatusChanged();
    }
}

void NavigationWidget::setHotKey(HotKeyContainer* hotKeyContainer, HotKeyConfig* config)
{
    m_HotKeyContainer = hotKeyContainer;
    m_HotKeyConfig = config;

    connect(config, &HotKeyConfig::hotkeyChanged, this, &NavigationWidget::updateHotKey);
    updateHotKey();
    initHotKeyUI();
}

bool NavigationWidget::eventFilter(QObject* watched, QEvent* event)
{
    if (event->type() == QEvent::Enter)
    {
        if (m_KeyLabels.count() >
            ModelConfig::instance().value(ModelConfig::NavigationHotKeyDisplayQuantity, 4).toInt())
        {
            m_HotkeyPopupDialog->move(QPoint(0, mapToGlobal(this->pos()).y() - m_HotkeyPopupDialog->height()));
            m_HotkeyPopupDialog->show();
        }
    }
    else if (event->type() == QEvent::Leave)
    {
        m_HotkeyPopupDialog->hide();
    }
    return QWidget::eventFilter(watched, event);
}

void NavigationWidget::onRawTBStatusChanged(bool isMeasuring)
{
    if (m_Model == NULL)
    {
        return;
    }

//    ui->label_trackball_center->setText(m_Model->workStatus());
#if 1
//    ui->label_p1->setText(m_Model->rawTBStatus());
#else
    if (m_Model->rawTBStatus().isEmpty())
    {
        ui->label_nav->setPixmap(QPixmap());
    }
    else
    {
        if (!isMeasuring)
        {
            ui->label_nav->setPixmap(QPixmap(m_TrackballIconHash.value(m_Model->rawTBStatus())));
        }
        else
        {
            ui->label_nav->setPixmap(QPixmap(m_TrackballIconHash.value("Measuring")));
        }
    }
#endif
}

void NavigationWidget::updateText()
{
    for (auto it = m_KeyLabels.begin(); it != m_KeyLabels.end(); ++it)
    {
        if (nullptr != m_HotkeyPopupDialog)
        {
            m_HotkeyPopupDialog->updateItem(it.key(), it.value());
        }

        QLabel* keyLabel = findChild<QLabel*>(QString("keyLabel_%1").arg(it.key()));
        if (nullptr != keyLabel)
        {
            keyLabel->setText(it.key());
        }
        QLabel* valueLabel = findChild<QLabel*>(QString("valueLabel_%1").arg(it.key()));
        if (nullptr != valueLabel)
        {
            QString text =
                valueLabel->fontMetrics().elidedText(it.value(), Qt::ElideRight, valueLabel->sizeHint().width());
            valueLabel->setText(text);
        }
    }
}

void NavigationWidget::onLeftButtonStatusChanged()
{
    if (m_Model == NULL)
    {
        return;
    }

    if (!FUNC_SWITCH)
    {
        ui->label_trackball_left->setText("");
        return;
    }

    ui->label_trackball_left->setText(m_Model->leftButtonStatus());
}

void NavigationWidget::onRightButtonStatusChanged()
{
    if (m_Model == NULL)
    {
        return;
    }

    if (!FUNC_SWITCH)
    {
        ui->label_trackball_right->setText("");
        return;
    }

    ui->label_trackball_right->setText(m_Model->rightButtonStatus());
}

void NavigationWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void NavigationWidget::initHotKeyUI()
{
    ui->widgetFunc->installEventFilter(this);

    m_HotkeyPopupDialog = new HotkeyPopupDialog;
    int count = 0;
    foreach (QString keyStr, m_KeyLabels.keys())
    {
        m_HotkeyPopupDialog->addItem(keyStr, m_KeyLabels.value(keyStr));
        if (count >= ModelConfig::instance().value(ModelConfig::NavigationHotKeyDisplayQuantity, 4).toInt())
        {
            continue;
        }
        if (m_KeyLabels.keys().contains(keyStr))
        {
            QHBoxLayout* itemLayout = new QHBoxLayout(this);

            QLabel* keyLabel = new QLabel(this);
            keyLabel->setAttribute(Qt::WA_TransparentForMouseEvents);
            keyLabel->setObjectName(QString("keyLabel_%1").arg(keyStr));
            keyLabel->setText(keyStr);
            keyLabel->setAlignment(Qt::AlignCenter);
            Util::changeQssWidgetProperty(keyLabel, "isKeyLabel", true);
            itemLayout->addWidget(keyLabel, 0);

            QLabel* valueLabel = new QLabel(this);
            valueLabel->setAttribute(Qt::WA_TransparentForMouseEvents);
            valueLabel->setObjectName(QString("valueLabel_%1").arg(keyStr));
            QString text = valueLabel->fontMetrics().elidedText(m_KeyLabels.value(keyStr), Qt::ElideRight,
                                                                valueLabel->geometry().width());
            valueLabel->setText(text);
            valueLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
            Util::changeQssWidgetProperty(valueLabel, "isValueLabel", true);
            itemLayout->addWidget(valueLabel, 1);

            ui->formLayout->addItem(itemLayout);

            count++;
        }
    }
}

void NavigationWidget::updateHotKey()
{
    m_KeyLabels.clear();
    foreach (QString entityKey, m_HotKeyConfig->avaliableEntityKeys())
    {
        QString functionName = m_HotKeyConfig->function(entityKey);
        if (!functionName.isEmpty())
        {
            HotKeyFunction* func = m_HotKeyContainer->hotKeyFunction(functionName);
            if (func != nullptr && m_HotKeyContainer->entityKey(entityKey)->IsEntityFromGroupOther())
            {
                QString text = Util::translate("HotKey", func->caption());
                m_KeyLabels.insert(m_HotKeyContainer->entityKeyCaption(entityKey), text);
            }
        }
    }

    updateText();
}
