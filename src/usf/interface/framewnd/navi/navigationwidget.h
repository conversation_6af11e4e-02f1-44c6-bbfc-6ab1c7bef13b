#ifndef NAVIGATIONWIDGET_H
#define NAVIGATIONWIDGET_H
#include "usfinterfaceframewnd_global.h"

#include <QWidget>
#include <QMap>

class SystemHintModel;
class HotKeyContainer;
class HotKeyConfig;
class QLabel;
class HotkeyPopupDialog;

namespace Ui
{
class NavigationWidget;
}

class USF_INTERFACE_FRAMEWND_EXPORT NavigationWidget : public QWidget
{
    Q_OBJECT

public:
    explicit NavigationWidget(QWidget* parent = nullptr);
    ~NavigationWidget();
    void setModel(SystemHintModel* model);
    void setHotKey(HotKeyContainer* hotKeyContainer, HotKeyConfig* config);

protected:
    virtual bool eventFilter(QObject* watched, QEvent* event);

private slots:
    void updateHotKey();
    void onRawTBStatusChanged(bool isMeasuring);
    void onLeftButtonStatusChanged();
    void onRightButtonStatusChanged();

private:
    void updateText();
    void retranslateUi();
    void initHotKeyUI();

private:
    Ui::NavigationWidget* ui;
    HotKeyContainer* m_HotKeyContainer;
    HotKeyConfig* m_HotKeyConfig;
    SystemHintModel* m_Model;
    QHash<QString, QString> m_LeftButtonIconHash;
    QHash<QString, QString> m_RightButtonIconHash;
    QMap<QString, QString> m_KeyLabels;
    HotkeyPopupDialog* m_HotkeyPopupDialog;
};

#endif // NAVIGATIONWIDGET_H
