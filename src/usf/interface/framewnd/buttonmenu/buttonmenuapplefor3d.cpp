#include "buttonmenuapplefor3d.h"
#include <QPainter>
#include <QPainterPath>
#include <QMouseEvent>
#include <QDebug>

#include "menusliderbutton.h"
#include "util.h"
#include "toolnames.h"
#include "qvariantcommandargs.h"

static bool m_sign = true;

ButtonMenuAppleFor3D::ButtonMenuAppleFor3D(QWidget* parent)
    : QWidget(parent)
    , m_BackgroundPath(":/images/res/images/grape/bottommenu_click_background.png")
    , m_SliderBackgroundPath(":/images/res/images/grape/bottommenu_background.png")
    , m_IndicatorTurePath(":/images/3D/res/images/3D/buttonmenuapple_true_background.png")
    , m_IndicatorFalsePath(":/images/3D/res/images/3D/buttonmenuapple_false_background.png")
    , m_TopToUpOffset(80)
    , m_TopToMiddleOffset(100)
    , m_TopToBottomOffset(120)
    , m_BottomMenuMargin(50)
    , m_ProgressColor("#2bb3d9")
    , m_ProgressHoverColor("#2bb3d9")
    , m_ProgressDisableColor("#303132")
    , m_ProgressOffset(10)
    , m_ProgressWidth(10)
    , m_Angle(-180)
    , m_ValueOffsetCount(0)
    , m_Tool(NULL)
    , m_IsClickable(false)
    , m_IsPressed(false)
    , m_IsHorizontal(true)
{
    setWindowFlag(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    installEventFilter(this);
}

ButtonMenuAppleFor3D::~ButtonMenuAppleFor3D()
{
}

void ButtonMenuAppleFor3D::setModel(const BottomMenuModel::ItemUnit& value)
{
    if (m_Model != value)
    {
        m_Model = value;

        int nullCount = 0;
        foreach (const BottomMenuModel::Item& item, m_Model)
        {
            if (item.tool != NULL && item.tool->isVisible())
            {
                m_Tool = item.tool;

                if (item.tool->isClickedTool())
                {
                    m_IsClickable = true;
                }
                else
                {
                    m_IsClickable = false;
                }

                setText(item.tool->captionTred());
                setEnabled(item.tool->isAvailable());

                connect(item.tool, SIGNAL(textChanged()), this, SLOT(onTextChanged()));
                connect(item.tool, SIGNAL(valueChanged()), this, SLOT(onValueChanged()));
                connect(item.tool, SIGNAL(availableChanged(bool)), this, SLOT(onToolAvailabelChanged(bool)));

                updateProgress(m_Tool->value()); // 初始化进度条

                if (item.layout.rowSpan == 2)
                {
                    nullCount = 0;
                }
            }
            else if (item.tool == NULL)
            {
                if (item.layout.rowSpan == 2)
                {
                    nullCount = 2;
                }
                else
                {
                    nullCount++;
                }
            }
        }
        if (nullCount == 2) // 当一列内的两行菜单都为null时则认为当前菜单为null
        {
            updateProgress(0, true);
            Util::changeQssWidgetProperty(this, "state", "disabled");
        }
        else
        {
            Util::changeQssWidgetProperty(this, "state", m_Tool->isAvailable() ? "normal" : "disabled");
        }
    }
}

void ButtonMenuAppleFor3D::clearTool()
{
    foreach (const BottomMenuModel::Item& item, m_Model)
    {
        if (item.tool != NULL)
        {
            setText("");
            updateProgress(m_Tool->value());
            Util::changeQssWidgetProperty(this, "state", m_Tool->isAvailable() ? "normal" : "disabled");
            disconnect(item.tool, SIGNAL(textChanged()), this, SLOT(onTextChanged()));
            disconnect(item.tool, SIGNAL(valueChanged()), this, SLOT(onValueChanged()));
            disconnect(item.tool, SIGNAL(availableChanged(bool)), this, SLOT(onToolAvailabelChanged(bool)));
        }
    }

    m_Model = BottomMenuModel::ItemUnit();
}

void ButtonMenuAppleFor3D::setModel(const BottomMenuModel::ItemUnit& value, const int rowIndex)
{
    Q_UNUSED(value);
    Q_UNUSED(rowIndex);
}

bool ButtonMenuAppleFor3D::isLastItem()
{
    return false;
}

void ButtonMenuAppleFor3D::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event)

    QPainter p(this);
    p.setRenderHint(QPainter::Antialiasing); //抗锯齿

    QString curPixPath = m_SliderBackgroundPath;

    if (nullptr != m_Tool)
    {
        if (m_Tool->isClickedTool())
        {
            curPixPath = m_BackgroundPath;
        }

        if (!m_Tool->isAvailable() && property("state") != "disabled")
        {
            Util::changeQssWidgetProperty(this, "state", "disabled");
            return;
        }
    }

    QPixmap pix(curPixPath);

    // 绘制文字
    QPainter pixPainter(&pix);

    pixPainter.setPen(m_TextColor);

    pixPainter.setFont(p.font());

    QString value = (nullptr == m_Tool || property("state").toString() == "disabled") ? "" : m_Tool->valueTred();
    QString showText;
    int offsetToSetValueCenter = pixPainter.fontMetrics().horizontalAdvance(value) > width() - m_BottomMenuMargin
                                     ? width() - m_BottomMenuMargin
                                     : pixPainter.fontMetrics().horizontalAdvance(value);

    if (m_IsClickable)
    {

        if (m_Tool->isBoolValue() && !text().isEmpty() && !value.isEmpty())
        {
            showText = text() + ":" + value;
            pixPainter.drawText(
                QPoint((pix.width() - pixPainter.fontMetrics().horizontalAdvance(showText)) / 2.0, m_TopToBottomOffset),
                showText);

            // 指示器
            QPixmap indicatorPix = QPixmap(m_Tool->value() ? m_IndicatorTurePath : m_IndicatorFalsePath);
            pixPainter.drawPixmap(QRect(QPoint((pix.width() - indicatorPix.width()) / 2.0,
                                               (m_TopToBottomOffset - indicatorPix.height()) / 2.0),
                                        indicatorPix.size()),
                                  indicatorPix);
        }
        else
        {
            showText = value + text();

            // 由于人超下不需要将括号变成换行，因此这里取到值直接画出来
            if (value.contains("("))
            {

                pixPainter.drawText(
                    QPoint((pix.width() - pixPainter.fontMetrics().horizontalAdvance(text())) / 2.0, m_TopToUpOffset),
                    text());
                pixPainter.drawText(
                    QPoint((pix.width() - offsetToSetValueCenter) / 2.0, m_TopToBottomOffset),
                    pixPainter.fontMetrics().elidedText(value, Qt::ElideRight, width() - m_BottomMenuMargin));
            }
            else if (!(showText.contains("(") && showText.count("(") == showText.count(")")))
            {
                if (!value.isEmpty())
                {
                    pixPainter.drawText(QPoint((pix.width() - pixPainter.fontMetrics().horizontalAdvance(text())) / 2.0,
                                               m_TopToUpOffset),
                                        text());
                    pixPainter.drawText(
                        QPoint((pix.width() - offsetToSetValueCenter) / 2.0, m_TopToBottomOffset),
                        pixPainter.fontMetrics().elidedText(value, Qt::ElideRight, width() - m_BottomMenuMargin));
                }
                else
                {
                    pixPainter.drawText(QPoint((pix.width() - pixPainter.fontMetrics().horizontalAdvance(text())) / 2.0,
                                               m_TopToMiddleOffset),
                                        showText);
                }
            }
        }
    }
    else
    {
        if (value != "" && text() != "")
        {
            pixPainter.drawText(
                QPoint((pix.width() - offsetToSetValueCenter) / 2.0, m_TopToUpOffset),
                pixPainter.fontMetrics().elidedText(value, Qt::ElideRight, width() - m_BottomMenuMargin));
            pixPainter.drawText(
                QPoint((pix.width() - pixPainter.fontMetrics().horizontalAdvance(text())) / 2.0, m_TopToBottomOffset),
                text());
        }
        else if (value == "" || text() == "")
        {
            showText = value + text();
            if (!(showText.contains("(") && showText.count("(") == showText.count(")")))
            {
                pixPainter.drawText(QPoint((pix.width() - pixPainter.fontMetrics().horizontalAdvance(showText)) / 2.0,
                                           m_TopToMiddleOffset),
                                    showText);
            }
        }
    }

    // ABC(D)Preset
    // 如果showText存在多行时（存在括弧），将换行显示。 ==> abd(c)
    // 这段代码目前只需要在兽超下做处理，但是由于人超下也出现了带括号的showText，所以需要判断
    if (showText.contains("(") && showText.count("(") == showText.count(")") && !value.contains("("))
    {
        showText.replace(")", "");
        QStringList textList = showText.split("(");

        if (textList.count() >= 2)
        {
            pixPainter.drawText(QPoint((pix.width() - pixPainter.fontMetrics().horizontalAdvance(textList.at(0))) / 2.0,
                                       m_TopToUpOffset),
                                textList.at(0));
            pixPainter.drawText(QPoint((pix.width() - pixPainter.fontMetrics().horizontalAdvance(textList.at(1))) / 2.0,
                                       m_TopToBottomOffset),
                                textList.at(1));
        }
    }

    int a = pix.width();
    int b = pix.height();

    p.drawPixmap(QRect(0, 0, a, b), pix);

    if (!m_IsClickable)
    {
        /************************绘制进度条*************************/
        p.translate(QRect(0, 0, pix.width(), pix.width()).center()); // 移动到圆心
        gradientArc(&p, pix.width() / 2 - m_ProgressOffset, 0, m_Angle, m_ProgressWidth,
                    isEnabled() ? (m_IsPressed ? m_ProgressHoverColor : m_ProgressColor)
                                : m_ProgressDisableColor); // 绘制圆弧
        setFixedHeight(pix.height());                      // 设置高度一半
    }
}

void ButtonMenuAppleFor3D::mousePressEvent(QMouseEvent* event)
{
    m_IsPressed = true;
    m_PressedPt = event->pos();
    if (property("state").toString() == "disabled")
    {
        return;
    }
    emit tipLogoVisible(true);
}

void ButtonMenuAppleFor3D::mouseReleaseEvent(QMouseEvent* event)
{
    Q_UNUSED(event)

    m_IsPressed = false;
    m_ValueOffsetCount = 0;

    if (m_IsClickable)
    {
        emit mousePress(event, true);
        updateProgress(m_Tool->value());
    }

    emit tipLogoVisible(false);
}

void ButtonMenuAppleFor3D::mouseMoveEvent(QMouseEvent* event)
{
    if (property("state").toString() == "disabled")
    {
        return;
    }

    QPoint curPt = event->pos();
    QPoint offset = curPt - m_PressedPt;

    m_IsHorizontal = true;

    int offsetValue = offset.x();

    // 为了简单控制实现圆弧调节交互正常，限制垂直调节范围为靠近中心的区域
    if (curPt.x() >= width() / 5 && curPt.x() <= width() - width() / 5 && qAbs(offset.y()) > qAbs(offset.x()))
    {
        m_IsHorizontal = false;
        offsetValue = offset.y();
    }
    else if (curPt.y() >= height() / 5 && curPt.y() <= height() - height() / 5 && qAbs(offset.x()) > qAbs(offset.y()))
    {
        m_IsHorizontal = false;
        offsetValue = -offset.x();
    }

    bool curSign = offsetValue > 0;
    if (m_sign != curSign)
    {
        m_ValueOffsetCount = 0;
        m_sign = curSign;
        return;
    }

    m_sign = curSign;

    m_PressedPt = event->pos();

    if (!m_IsClickable && m_Tool->maxmValue() != 0)
    {
        int toolValue = m_Tool->isInvertMulti() ? (m_Tool->maxmValue() - m_Tool->value()) : m_Tool->value();
        int value = m_Tool->value();

        m_ValueOffsetCount += qAbs(offsetValue);

        int maxmValue = m_Tool->maxmValue();
        if (m_Tool->minmValue() == 0)
        {
            maxmValue += 1;
        }

        if (m_ValueOffsetCount >= (m_IsHorizontal ? width() : height() / maxmValue))
        {
            if (m_sign)
            {
                value += m_IsHorizontal ? m_Tool->stepValue() : -m_Tool->stepValue();
            }
            else
            {
                value -= m_IsHorizontal ? m_Tool->stepValue() : -m_Tool->stepValue();
            }

            if (value < m_Tool->minmValue() || value > m_Tool->maxmValue())
                return;

            updateProgress(value);

            if (m_Tool->isParameterTool() && value != toolValue)
            {
                QVariantCommandArgs boolArgs;
                boolArgs.setType(COMMAND_TYPE_STRING);
                boolArgs.setValue(QString::number(m_Tool->isInvertMulti() ? (m_Tool->maxmValue() - value) : value));
                m_Tool->setArgs(&boolArgs);
                m_Tool->run();
            }

            m_ValueOffsetCount = 0;
        }
    }

    update();
}

bool ButtonMenuAppleFor3D::eventFilter(QObject* obj, QEvent* event)
{
    if (property("state").toString() != "disabled")
    {
        if (event->type() == QEvent::HoverMove || event->type() == QEvent::Enter ||
            event->type() == QEvent::HoverEnter || event->type() == QEvent::MouseButtonPress)
        {
            Util::changeQssWidgetProperty(this, "state", "hover");
        }
        else if (event->type() == QEvent::HoverLeave || event->type() == QEvent::Leave ||
                 event->type() == QEvent::MouseButtonRelease)
        {
            Util::changeQssWidgetProperty(this, "state", "normal");
        }
    }
    return QWidget::eventFilter(obj, event);
}

void ButtonMenuAppleFor3D::onTextChanged()
{
    ITool* tool = static_cast<ITool*>(sender());
    setText(tool->captionTred());
    update();
}

void ButtonMenuAppleFor3D::onValueChanged()
{
    updateProgress(m_Tool->value());
    update();
}

void ButtonMenuAppleFor3D::onToolAvailabelChanged(bool availabel)
{
    if (availabel)
    {
        Util::changeQssWidgetProperty(this, "state", "normal");
    }
    else
    {
        Util::changeQssWidgetProperty(this, "state", "disabled");
    }
    setEnabled(availabel);
}

void ButtonMenuAppleFor3D::gradientArc(QPainter* painter, int radius, int startAngle, int angleLength, int arcHeight,
                                       QColor& color)
{
    // 渐变色
    QRadialGradient gradient(0, 0, radius);
    gradient.setColorAt(0, Qt::white);
    gradient.setColorAt(1.0, color);
    painter->setBrush(gradient);

    // << 1（左移1位）相当于radius*2 即：150*2=300
    // QRectF(-150, -150, 300, 300)
    QRectF rect(-radius, -radius, radius << 1, radius << 1);
    QPainterPath path;
    path.arcTo(rect, startAngle, angleLength);

    // QRectF(-120, -120, 240, 240)
    QPainterPath subPath;
    subPath.addEllipse(rect.adjusted(arcHeight, arcHeight, -arcHeight, -arcHeight));

    // path为扇形 subPath为椭圆
    path -= subPath;

    painter->setPen(Qt::NoPen);
    painter->drawPath(path);
}

void ButtonMenuAppleFor3D::updateProgress(const int& value, const bool isNull)
{
    if (!isNull)
    {
        if (m_IsClickable)
        {
            m_Angle = value == 1 ? -360 : -180;
        }
        else
        {
            if (value == m_Tool->maxmValue())
            {
                m_Angle = -360;
            }
            else
            {
                m_Angle =
                    -180 + (-180.0 / ((m_Tool->maxmValue() - m_Tool->minmValue())) * (value - m_Tool->minmValue()));
            }
        }
    }
    else
    {
        m_Angle = -180;
    }
    update();
}

void ButtonMenuAppleFor3D::setProgressWidth(int progressWidth)
{
    m_ProgressWidth = progressWidth;
}

void ButtonMenuAppleFor3D::setProgressOffset(int progressOffset)
{
    m_ProgressOffset = progressOffset;
}

void ButtonMenuAppleFor3D::setProgressDisableColor(const QColor& color)
{
    m_ProgressDisableColor = color;
}

void ButtonMenuAppleFor3D::setProgressColor(const QColor& color)
{
    m_ProgressColor = color;
}

void ButtonMenuAppleFor3D::setProgressHoverColor(const QColor& color)
{
    m_ProgressHoverColor = color;
}

int ButtonMenuAppleFor3D::bottomMenuMargin() const
{
    return m_BottomMenuMargin;
}

void ButtonMenuAppleFor3D::setBottomMenuMargin(int value)
{
    m_BottomMenuMargin = value;
}

int ButtonMenuAppleFor3D::topToBottomOffset() const
{
    return m_TopToBottomOffset;
}

void ButtonMenuAppleFor3D::setTopToBottomOffset(int value)
{
    m_TopToBottomOffset = value;
}

int ButtonMenuAppleFor3D::topToMiddleOffset() const
{
    return m_TopToMiddleOffset;
}

void ButtonMenuAppleFor3D::setTopToMiddleOffset(int value)
{
    m_TopToMiddleOffset = value;
}

void ButtonMenuAppleFor3D::setTopToUpOffset(int value)
{
    m_TopToUpOffset = value;
}

QString ButtonMenuAppleFor3D::sliderBackgroundPath() const
{
    return m_SliderBackgroundPath;
}

void ButtonMenuAppleFor3D::setSliderBackgroundPath(const QString& sliderBackgroundPath)
{
    m_SliderBackgroundPath = sliderBackgroundPath;
}

bool ButtonMenuAppleFor3D::isPressed()
{
    return m_IsPressed;
}

QString ButtonMenuAppleFor3D::indicatorFalsePath() const
{
    return m_IndicatorFalsePath;
}

void ButtonMenuAppleFor3D::setIndicatorFalsePath(const QString& indicatorFalsePath)
{
    m_IndicatorFalsePath = indicatorFalsePath;
}

QString ButtonMenuAppleFor3D::indicatorTurePath() const
{
    return m_IndicatorTurePath;
}

void ButtonMenuAppleFor3D::setIndicatorTurePath(const QString& indicatorTurePath)
{
    m_IndicatorTurePath = indicatorTurePath;
}

QString ButtonMenuAppleFor3D::indicatorPath() const
{
    return m_IndicatorPath;
}

void ButtonMenuAppleFor3D::setIndicatorPath(const QString& indicatorPath)
{
    m_IndicatorPath = indicatorPath;
}

QString ButtonMenuAppleFor3D::text() const
{
    return m_Text;
}

void ButtonMenuAppleFor3D::setText(const QString& text)
{
    m_Text = text;
}

QString ButtonMenuAppleFor3D::backgroundPath() const
{
    return m_BackgroundPath;
}

void ButtonMenuAppleFor3D::setBackgroundPath(const QString& backgroundPath)
{
    m_BackgroundPath = backgroundPath;
}

QColor ButtonMenuAppleFor3D::textColor() const
{
    return m_TextColor;
}

void ButtonMenuAppleFor3D::setTextColor(const QColor& textColor)
{
    m_TextColor = textColor;
}
