#include "sonoparaswidgetcontroller.h"
#include "iultrasoundview.h"
#include "bfpnames.h"
#include "dopplerthetaadjustitem.h"
#include "ibufferstoremanager.h"
#include "ilinebuffer.h"
#include "infostruct.h"
#include "istatemanager.h"
#include "mainwindowmiddledownwidget.h"
#include "measurecontext.h"
#include "modelconfig.h"
#include "parameter.h"
#include "sonoparameters.h"
#include "isonoparaswidget.h"
#include "toolnames.h"

SonoParasWidgetController::SonoParasWidgetController(QObject* parent)
    : QObject(parent)
    , m_SonoParasWidget(NULL)
    , m_LineBufferManager(NULL)
    , m_BufferStoreManager(NULL)
    , m_PostSonoParameters(NULL)
    , m_MainWindowMiddleDownWidget(NULL)
    , m_MeasureContext(NULL)
    , m_StateManager(NULL)
{
    initalize();
}

SonoParasWidgetController::~SonoParasWidgetController()
{
}

void SonoParasWidgetController::initalize()
{
    m_PostParameterNames =
        QStringList() << BFPNames::GainStr << BFPNames::GainThiStr << BFPNames::GainShowStr << BFPNames::DynamicRangeStr
                      << ToolNames::BGainGeneralStr << ToolNames::BDynamicRangeGeneralStr << BFPNames::MGainStr
                      << BFPNames::MGainThiStr << BFPNames::MGainShowStr << ToolNames::MGainGeneralStr
                      << BFPNames::MGrayCurveIndexStr << BFPNames::MColorMapIndexStr << BFPNames::CfColorMapIndexShowStr
                      << BFPNames::TDIColorMapIndexStr << BFPNames::PwTDIColorMapIndexStr
                      << BFPNames::PWDynamicRangeTDIStr << BFPNames::PwTDIGrayCurveIndexStr
                      << BFPNames::CwdColorMapIndexStr << BFPNames::PwColorMapIndexStr << BFPNames::BaseLineStr
                      << BFPNames::SpectralInvertStr << ToolNames::QuickAngleStr << BFPNames::DopplerThetaStr
                      << BFPNames::DopplerThetaCWDStr << BFPNames::DopplerThetaTDIStr << BFPNames::RotationStr
                      << BFPNames::LeftStr << BFPNames::UpStr << BFPNames::ImageZoomCoefStr << BFPNames::LGCControlEnStr
                      << BFPNames::BColorMapIndexShowStr << BFPNames::BGammaShowStr << BFPNames::BGrayCurveIndexShowStr
                      << BFPNames::BufferIndexStartPosStr << BFPNames::BufferIndexEndPosStr
                      << BFPNames::CinePlaySpeedAdjustStr << BFPNames::CineNavigateStr << BFPNames::iImageShowStr
                      << BFPNames::BRejectionShowStr << BFPNames::ColorInvertStateStr << BFPNames::BaseLineColorStr
                      << BFPNames::DPDInvertStateStr << BFPNames::TDIInvertStateStr << BFPNames::BaseLineDPDStr
                      << BFPNames::PwGrayCurveIndexStr << BFPNames::CwdGrayCurveIndexStr << ToolNames::TwoBModeStr
                      << ToolNames::FourBModeStr << BFPNames::PWDynamicRangeShowStr << ToolNames::DopplerAngleStr
                      << ToolNames::SonoDiaphStr << ToolNames::SonoIMTStr << BFPNames::MVIColorMapIndexStr
                      << BFPNames::MVIType1ColorMapIndexStr << BFPNames::MVIType2ColorMapIndexStr
                      << BFPNames::MVIType3ColorMapIndexStr << BFPNames::MVIInvertStateStr
                      << ToolNames::SonoNerveQuitFromBModeStr << ToolNames::StressEchoStr << ToolNames::StressEchoEnStr
                      << ToolNames::StressEchoT1ControlStr << ToolNames::StressEchoT2ControlStr
                      << ToolNames::CurvedPanoramicStr << ToolNames::SonoMSKQuitFromBModeStr
                      << ToolNames::SonoGuideSectionStr << ToolNames::SonoCardiacStr
                      << ToolNames::SonoCardiacSecRatingStr << ToolNames::SonoCardiacSecRecognizeStr
                      << ToolNames::SonoCarotidLRStr << ToolNames::AutoEFCurLayoutStr << ToolNames::ESFrameStr
                      << ToolNames::EDFrameStr << ToolNames::AutoEFSingleBStr << ToolNames::AutoEFStr
                      << ToolNames::ClickAutoEFMeasureStr << ToolNames::CinePlayStr << ToolNames::AngleChangeStr
                      << ToolNames::SonoVFStr << ToolNames::ClearDeleteGlyphsStr;

    // SonoAir机型冻结下，走的是CinePlayMouseTool，不是TgcTool
    // Atom机型，冻结下需要走TgcTool
    //因此,添加ModelConfig::TgcToolPostProcess来判断需不需要将TgcTool设置为冻结可调。目前，Atom机型设置为true
    if (ModelConfig::instance().value(ModelConfig::TgcToolPostProcess, false).toBool())
    {
        m_PostParameterNames << ToolNames::TgcStr;
    }
}

void SonoParasWidgetController::setSonoParasWidget(SonoParametersClientBase* sonoParasWidget)
{
    m_SonoParasWidget = sonoParasWidget;
}

void SonoParasWidgetController::setDopplerThetaAdjustItem(DopplerThetaAdjustItem* dopplerThetaAdjustItem)
{
    m_DopplerThetaAdjustItem = dopplerThetaAdjustItem;
}

void SonoParasWidgetController::setBufferManager(ILineBuffer* lineBufferManager)
{
    m_LineBufferManager = lineBufferManager;
    if (m_LineBufferManager != NULL)
    {
        connect(m_LineBufferManager, SIGNAL(beforeSonoParametersChanged(SonoParameters*, SonoParameters*)), this,
                SLOT(onBeforeSonoParametersChanged(SonoParameters*, SonoParameters*)), Qt::DirectConnection);
    }
}

void SonoParasWidgetController::setBufferStoreManager(IBufferStoreManager* bufferStoreManager)
{
    if (bufferStoreManager != NULL)
    {
        m_BufferStoreManager = bufferStoreManager;
        connect(m_BufferStoreManager, SIGNAL(beforeCurSonoParametersChanged()), this,
                SLOT(onBeforeCurSonoParametersChanged()));
        connect(m_BufferStoreManager, SIGNAL(curSonoParametersChanged()), this, SLOT(onCurSonoParametersChanged()));
    }
}

void SonoParasWidgetController::setMainWindowMiddleDownWidget(MainWindowMiddleDownWidget* widget)
{
    m_MainWindowMiddleDownWidget = widget;
}

void SonoParasWidgetController::setMeasureContext(MeasureContext* measureContext)
{
    m_MeasureContext = measureContext;
}

void SonoParasWidgetController::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

const QStringList& SonoParasWidgetController::postParameterNames() const
{
    return m_PostParameterNames;
}

void SonoParasWidgetController::onSetSonoParameters()
{
    qDebug() << PRETTY_FUNCTION << "m_SonoParameters:" << (quintptr)m_SonoParameters;
    //    controlPostParameterEnabled();
}

void SonoParasWidgetController::connectParametersSignals()
{
    connect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFreezeChanged(QVariant)));
}

void SonoParasWidgetController::disconnectParametersSignals()
{
    disconnect(parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this, SLOT(onFreezeChanged(QVariant)));
}

void SonoParasWidgetController::onFreezeActiveBChanged(const QVariant& value)
{
    if (NULL == m_SonoParasWidget || NULL == m_LineBufferManager)
    {
        return;
    }

    if (pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime())
    {
        SonoParameters* sonoParameter = m_LineBufferManager->getSonoParametersByLayoutIndex(value.toInt());
        qDebug() << PRETTY_FUNCTION << "value:" << value << "sonoParameter:" << (quintptr)sonoParameter
                 << "m_SonoParameters:" << (quintptr)m_SonoParameters;
        if (sonoParameter != NULL)
        {
            m_SonoParasWidget->setSonoParameters(sonoParameter);
            m_DopplerThetaAdjustItem->setSonoParameters(sonoParameter);
            m_MeasureContext->setSonoParameter(sonoParameter);
            setToolsSonoParameters(sonoParameter);
            m_PostSonoParameters = sonoParameter;
        }
    }
}

void SonoParasWidgetController::onFreezeChanged(const QVariant& value)
{
    qDebug() << PRETTY_FUNCTION << "value:" << value << "m_SonoParameters:" << (quintptr)m_SonoParameters;

    if (NULL == m_SonoParameters)
    {
        return;
    }
    if (value.toBool() || !m_SonoParameters->isRealTime())
    {
        connect(parameter(BFPNames::ActiveBStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
                SLOT(onBeforeFreezeActiveBChanged(QVariant, QVariant&)), Qt::UniqueConnection);
        connect(parameter(BFPNames::ActiveBStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onFreezeActiveBChanged(QVariant)), Qt::UniqueConnection);
        onFreezeActiveBChanged(m_SonoParameters->pIV(BFPNames::ActiveBStr));
    }
    else
    {
        disconnect(parameter(BFPNames::ActiveBStr), SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
                   SLOT(onBeforeFreezeActiveBChanged(QVariant, QVariant&)));
        disconnect(parameter(BFPNames::ActiveBStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onFreezeActiveBChanged(QVariant)));
        changedMenuSonoParameters(m_SonoParameters);
    }
}

void SonoParasWidgetController::onBeforeCurSonoParametersChanged()
{
    if (NULL == m_BufferStoreManager)
    {
        return;
    }

    disconnect(this->parameter(BFPNames::ActiveBStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onFreezeActiveBChanged(QVariant)));
    disconnect(this->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onFreezeChanged(QVariant)));
}

void SonoParasWidgetController::onCurSonoParametersChanged()
{
    if (NULL == m_BufferStoreManager)
    {
        return;
    }

    SonoParameters* curSonoParameters = m_PostSonoParameters;
    SonoParameters* globleSp = m_BufferStoreManager->curSonoParameters();
    setSonoParameters(globleSp);
    if (!globleSp->isRealTime())
    {
        SonoParameters* spLayout =
            m_LineBufferManager->getSonoParametersByLayoutIndex(globleSp->pIV(BFPNames::ActiveBStr));
        qDebug() << PRETTY_FUNCTION << "spLayout:" << (quintptr)spLayout
                 << "ActiveB:" << globleSp->pIV(BFPNames::ActiveBStr);
        onBeforeSonoParametersChanged(curSonoParameters, spLayout);
    }
    else
    {
        onBeforeSonoParametersChanged(curSonoParameters, globleSp);
    }
    onFreezeChanged(pV(BFPNames::FreezeStr));
    m_PostSonoParameters = m_SonoParameters;
}

void SonoParasWidgetController::onBeforeFreezeActiveBChanged(const QVariant& oldValue, QVariant& newValue)
{
    if (NULL == m_LineBufferManager)
    {
        return;
    }

    if (pBV(BFPNames::FreezeStr) || !m_SonoParameters->isRealTime())
    {
        syncMenuToolValue(m_LineBufferManager->getSonoParametersByLayoutIndex(oldValue.toInt()),
                          m_LineBufferManager->getSonoParametersByLayoutIndex(newValue.toInt()));
    }
}

void SonoParasWidgetController::onBeforeSonoParametersChanged(SonoParameters* oldSonoParameter,
                                                              SonoParameters* newSonoParameter)
{
    qDebug() << PRETTY_FUNCTION << "m_SonoParameters:" << (quintptr)m_SonoParameters
             << "oldSonoParameter:" << (quintptr)oldSonoParameter << "newSonoParameter:" << (quintptr)newSonoParameter;

    if (NULL == newSonoParameter)
    {
        return;
    }

    if (newSonoParameter->isRealTime())
    {
        if (newSonoParameter->parameter(BFPNames::LGCControlEnStr)->isEnabled())
        {
            newSonoParameter->setPV(BFPNames::LGCControlEnStr, false);
            if (m_MainWindowMiddleDownWidget != NULL)
            {
                m_MainWindowMiddleDownWidget->changeWidget(MainWindowMiddleDownWidget::Clipboard);
            }
        }

        if (oldSonoParameter != NULL)
        {
            disconnect(oldSonoParameter->parameter(BFPNames::ActiveBStr),
                       SIGNAL(beforeValueChanged(QVariant, QVariant&)), this,
                       SLOT(onBeforeFreezeActiveBChanged(QVariant, QVariant&)));
            disconnect(oldSonoParameter->parameter(BFPNames::ActiveBStr), SIGNAL(valueChanged(QVariant)), this,
                       SLOT(onFreezeActiveBChanged(QVariant)));
        }
    }
    // 由实时进入冻结
    if (newSonoParameter != NULL && (newSonoParameter->pBV(BFPNames::FreezeStr) || !newSonoParameter->isRealTime()))
    {
        changedMenuSonoParameters(newSonoParameter);
    }
}

void SonoParasWidgetController::syncMenuToolValue(SonoParameters* oldSonoParameter, SonoParameters* newSonoParameter)
{
    if (NULL == oldSonoParameter || NULL == newSonoParameter)
    {
        return;
    }

    if ((quintptr)oldSonoParameter == (quintptr)newSonoParameter)
    {
        return;
    }

    if (newSonoParameter->parameter(BFPNames::LGCControlEnStr)->isEnabled())
    {
        newSonoParameter->parameter(BFPNames::LGCControlEnStr)
            ->cloneFrom(oldSonoParameter->parameter(BFPNames::LGCControlEnStr));
    }
}

void SonoParasWidgetController::controlPostParameterEnabled()
{
    if (NULL == m_SonoParameters || m_SonoParameters->isRealTime())
    {
        return;
    }

    m_SonoParameters->parameter(BFPNames::LGCControlEnStr)->setEnabled(m_SonoParameters->pBV(BFPNames::LGCEnStr));
    m_SonoParameters->parameter(BFPNames::ImageZoomCoefStr)
        ->setEnabled(!m_SonoParameters->pBV(BFPNames::HalfHeightStr));
    m_SonoParameters->parameter(BFPNames::RotationStr)
        ->setEnabled(!m_SonoParameters->pBV(BFPNames::ElastoEnStr) &&
                     !m_SonoParameters->pBV(BFPNames::FreqSpectrumStr));
    m_SonoParameters->parameter(BFPNames::LeftStr)->setEnabled(!m_SonoParameters->pBV(BFPNames::FreqSpectrumStr));
    //添加键盘左右翻转在不同模式下的支持情况
    m_StateManager->setSupportLR(!m_SonoParameters->pBV(BFPNames::FreqSpectrumStr));

    if (m_SonoParameters->parameter(BFPNames::LGCControlEnStr)->isEnabled())
    {
        SonoParameters* curSonoParameters =
            m_LineBufferManager->getSonoParametersByLayoutIndex(pIV(BFPNames::ActiveBStr));
        if (curSonoParameters != NULL)
        {
            curSonoParameters->setPV(BFPNames::LGCControlEnStr, false, true);
        }
    }
}

void SonoParasWidgetController::changedMenuSonoParameters(SonoParameters* sonoParameters)
{
    m_SonoParasWidget->setSonoParameters(sonoParameters);
    m_DopplerThetaAdjustItem->setSonoParameters(sonoParameters);
    m_MeasureContext->setSonoParameter(sonoParameters);
    setToolsSonoParameters(sonoParameters);
    m_PostSonoParameters = sonoParameters;
    //移到此处执行是因为需要等待更新各个tool的sonoParameters后，重新绑定信号槽再更新参数value
    controlPostParameterEnabled();
}

void SonoParasWidgetController::setToolsSonoParameters(SonoParameters* sonoParameters)
{
    IUltrasoundView* ultrasoundView = dynamic_cast<IUltrasoundView*>(this->parent());
    Q_ASSERT(ultrasoundView != NULL);
    ultrasoundView->updateToolsSonoParameters(sonoParameters);
}
