#include "menucontroller.h"
#include "buttonmenupanel.h"
#include "icontext.h"
#include "itool.h"
#include "itoolsfacade.h"
#include "leftmenuwidget.h"
#include "menuitemcontainer.h"
#include "menutoolitem.h"
#include "qvariantcommandargs.h"
#include "stringcommandargs.h"
#include "toollist.h"
#include "toolset.h"
//#include "menutool.h"
#include "applicationinfo.h"
#include "appsetting.h"
#include "bfpnames.h"
#include "bottommenumodel.h"
#include "controlpanel.h"
#include "lightcontroller.h"
#include "menunames.h"
#include "touchablemenutoolitem.h"
#include "modelconfig.h"
#include "toolnames.h"
#include "util.h"
//#include "quickmeasuretool.h"
//#include "tools.h"
#include "diyleftmenuitem.h"
#include <QCoreApplication>
#include "modeluiconfig.h"

#ifndef NONEACTIVE
#define NONEACTIVE -1 // TODO:  该宏在 quick measuretool.h中定义，为了解除 MenuController对dispather模块的依赖，特此设计
#endif

MenuController::MenuController()
    : QObject()
    , m_ToolSet(NULL)
    , m_ButtonMenuPanel(NULL)
    , m_LeftMenuWidget(NULL)
    , m_ModeChangeWidget(NULL)
    , m_CurrentMenuItems(NULL)
    , m_CurrentToolListOfLeftMenu(NULL)
    , m_CurrentToolListOfBottomMenu(NULL)
    , m_IsShowingLeftMenu(false)
    , m_IsShowFourDLeftMenu(false)
    , m_IsShowEFastLeftMenu(false)
    , m_IsShowFastLeftMenu(false)
    , m_currentParemeterMenuIndex(-1)
    , m_IsMousePressDisabled(false)
    , m_IsBottomMenuEnabled(true)
    , m_isSupportRotateButton(false)
    , m_currentBottomMenuIndex(0)
    , m_curButtonMode(MenuOperationModeDef::Normal_Mode)
    , m_ControlPanel(NULL)
    , m_CurvedMenu(false)
    , m_ShowOthers(false)
    , m_ToolsFacade(nullptr)
    , m_ScreenIsTouchable(ModelUiConfig::instance().value(ModelUiConfig::SupportTouchScreen).toBool())
{
    m_CurrentMenuItems = new QList<MenuToolItem*>();

    initializeWithAppSetting();

    m_ControlPanel = new ControlPanel();
}

void MenuController::initializeWithAppSetting()
{
    initFuncNamesInMenu();

    connect(AppSetting::eventObject(), SIGNAL(presetModeChanged(bool)), this, SLOT(onPresetModeChanged(bool)));
    connect(AppSetting::eventObject(), SIGNAL(functionChanged(int, bool)), this, SLOT(onFunctionChanged(int, bool)));

    foreach (int key, m_FuncNames.keys())
    {
        bool isEnable = AppSetting::isFunctionEnabled(key);
        switch (key)
        {
        case LicenseItemKey::KeyChroma:
            isEnable = AppSetting::isChromaSupported();
            break;
        case LicenseItemKey::KeyTHI:
            isEnable = AppSetting::isThiSupported();
            break;
        case LicenseItemKey::KeyStressEcho:
            isEnable = true; /*( tool == NULL ? true : tool->canOpen())*/
            break;
        }

        prepareVisibleToolFilter(m_FuncNames.value(key), isEnable);
    }
}

MenuController::~MenuController()
{
    m_CurrentMenuItems->clear();
    delete m_CurrentMenuItems;

    foreach (QList<MenuToolItem*>* menuItems, m_MenuItemSet.values())
    {
        menuItems->clear();
    }
    qDeleteAll(m_MenuItemSet);
    m_MenuItemSet.clear();

    if (m_ControlPanel != NULL)
    {
        m_ControlPanel->deleteLater();
        m_ControlPanel = NULL;
    }
}

MenuController& MenuController::instance()
{
    static MenuController instance;
    return instance;
}

void MenuController::setToolSet(ToolSet* toolSet)
{
    m_ToolSet = toolSet;
    refreshMenuTool();
}

void MenuController::setMenuPanel(ButtonMenuPanel* menuPanel)
{
    if (m_ButtonMenuPanel != NULL)
    {
        disconnect(m_ButtonMenuPanel, SIGNAL(mousePress(QMouseEvent*, bool, int)), this,
                   SLOT(onButtonMenuMousePress(QMouseEvent*, bool, int)));
    }
    m_ButtonMenuPanel = menuPanel;
    connect(m_ButtonMenuPanel, SIGNAL(mousePress(QMouseEvent*, bool, int)), this,
            SLOT(onButtonMenuMousePress(QMouseEvent*, bool, int)));
    connect(m_ButtonMenuPanel, &ButtonMenuPanel::bottomMenuTipLogoVisible, this,
            &MenuController::bottomMenuTipLogoVisible);
}

void MenuController::setLeftMenuWidget(LeftMenuWidget* menuWidget)
{
    if (m_LeftMenuWidget != menuWidget)
    {
        m_LeftMenuWidget = menuWidget;
        connect(m_LeftMenuWidget, SIGNAL(pageChanged(int)), this, SLOT(onLeftMenuPageChanged(int)));
        connect(m_LeftMenuWidget, SIGNAL(curMenuChanged(QString)), this, SLOT(onCurMenuChanged(QString)));
    }
}

void MenuController::setToolsFacade(IToolsFacade* value)
{
    m_ControlPanel->setToolsFacade(value);
    m_ToolsFacade = value;
    refreshMenuTool(); //此句代码逻辑原来在构造函数中执行，觉得无必要，移到此处执行
}

void MenuController::setStateManager(IStateManager* value)
{
    m_ControlPanel->setStateManager(value);
}

void MenuController::prepareCurrentParementMenuIndex(const QString& categoryName)
{
    int menuIndex = MenuNames::MenuList.indexOf(categoryName);
    if (menuIndex != -1)
    {
        m_currentParemeterMenuIndex = menuIndex;
    }
}

void MenuController::createMenu(const QString& categoryNameEx, const QString& parentCategory,
                                const bool needRefreshHighLight)
{
    QString categoryName = categoryNameEx;

    if (categoryName == MenuNames::BMenuStr)
    {
        m_ToolsFacade->tool(BFPNames::SonoNeedleStr)->setToolText(BFPNames::SonoPleuraStr);
    }
    else if (categoryName == MenuNames::BiopsyMenuStr)
    {
        m_ToolsFacade->tool(BFPNames::SonoNeedleStr)->setToolText(BFPNames::SonoNeedleStr);
    }

    if (categoryName.isEmpty())
        return;
    if (m_ToolSet == NULL)
        return;

    if (MenuNames::FreezeMenuStr == categoryName && m_CurvedMenu)
    {
        categoryName = MenuNames::CurvedPanoramicMenuStr;
    }
    m_CurrentMenuCategory = categoryName;

    //有些菜单在BMenu， CMenu，DMenu中可能都存在，如Utility，ColorMap
    //此时需要通过设置他们的toolCategory来区别
    m_ParentCategory = parentCategory;
    if (parentCategory.isEmpty())
    {
        if (m_ToolsFacade != nullptr)
        {
            m_ToolsFacade->tool("Utility")->setToolCategory(categoryName);
            m_ToolsFacade->tool("ColorMap")->setToolCategory(categoryName);

            // 特殊处理CurvedPanoramic工具，根据菜单类别动态设置显示文本
            ITool* curvedPanoTool = m_ToolsFacade->tool("CurvedPanoramic");
            if (curvedPanoTool != nullptr)
            {
                curvedPanoTool->setToolCategory(categoryName);
                if (categoryName == "CMenu")
                {
                    curvedPanoTool->setToolText("Color Curved Panoramic");
                }
                else if (categoryName == "BMenu")
                {
                    curvedPanoTool->setToolText("Curved Panoramic");
                }
            }
        }
    }
    createLeftMenu(categoryName, needRefreshHighLight);
    ApplicationInfo::instance().setWorkStatus(menuTitleTred(categoryName));
    createBottomMenu(categoryName);
    if (m_ControlPanel != NULL)
    {
        m_ControlPanel->onNTPButClicked("Menu:" + categoryName, QString(), QString());
    }

    if (m_IsShowEFastLeftMenu || m_IsShowFastLeftMenu)
    {
        m_LeftMenuWidget->setActiveWidget(LeftMenuWidget::EFast);
        QString category = m_IsShowEFastLeftMenu ? MenuNames::EFastMenuStr : MenuNames::FastMenuStr;
        m_LeftMenuWidget->setCurrentCategory(category);
        m_LeftMenuWidget->setMenuTitle(menuTitleTred(category));
        m_IsShowEFastLeftMenu = false;
        m_IsShowFastLeftMenu = false;
    }
}

void MenuController::createBottomMenu(const QString& categoryName, bool refresh)
{
    // edit xule
    m_currentBottomMenuIndex = 0;

    //    m_ButtonMenuPanel->setVisibleToolFilter(m_visibleToolFilter);
    createButtonMenu(categoryName, refresh);

    prepareCurrentParementMenuIndex(categoryName);

    // edit xule
    refreshUpAndDownArrowState();
    emit freshModeLabels(categoryName);
}

void MenuController::createTPBottomMenu(const QString& categoryName)
{
    QString elements;
    if (m_ToolSet == NULL)
        return;

    if (!m_ToolSet->toolSetOfBottomMenu().contains(categoryName))
        return;

    m_ToolSet->setCurrentCategoryOfBottomMenu(categoryName);
    // parseTPBottomMenuelements(m_ToolSet,elements);
    // m_ControlPanel->createTPBottomMenu(elements);
    // m_ControlPanel->createTPBottomMenu(m_ToolSet->currentToolListOfBottomMenu()->allTools());
    m_ControlPanel->createTPBottomMenu(categoryName);
}

void MenuController::createButtonMenu(const QString& categoryName, bool refresh)
{
    if (m_ButtonMenuPanel == NULL || m_ToolSet == NULL)
        return;

    if (!m_ToolSet->toolSetOfBottomMenu().contains(categoryName))
        return;

    m_ToolSet->setCurrentCategoryOfBottomMenu(categoryName);
    //    m_CurrentToolListOfBottomMenu = m_ToolSet->currentToolListOfBottomMenu();

    //    m_ButtonMenuPanel->clearTool();
    //    for(int i=0; i< m_CurrentToolListOfBottomMenu->count(); i++)
    //    {
    //        ITool *tool = m_CurrentToolListOfBottomMenu->getTool(i);
    //        m_ButtonMenuPanel->setTool(i, tool);
    //    }

    // edit xule
    if (m_curButtonMode == MenuOperationModeDef::ActiveItem_Mode)
    {
        m_ButtonMenuPanel->setModel(m_ToolSet->currentBottomMenuModel(), m_currentBottomMenuIndex);
    }
    else
    {
        m_ButtonMenuPanel->setModel(m_ToolSet->currentBottomMenuModel(), refresh);
    }
}

void MenuController::createLeftMenu(const QString& categoryName, const bool needRefreshHighLight)
{
    if (m_LeftMenuWidget == NULL || m_ToolSet == NULL)
        return;

    if (MenuNames::SonoCardiacMenuStr == categoryName)
        m_LeftMenuWidget->setShowMaxHeight(true);
    else
        m_LeftMenuWidget->setShowMaxHeight(false);

    m_LeftMenuWidget->stopScrollbarSliding();

    m_LeftMenuWidget->setCurrentCategory(categoryName);

    m_LeftMenuWidget->setMenuTitle(menuTitleTred(categoryName));

    if (!m_ToolSet->toolSetOfLeftMenu().contains(categoryName))
    {
        if (!ModelConfig::instance().value(ModelConfig::LeftMenuAlwaysShow, false).toBool())
        {
            if (m_IsShowingLeftMenu)
            {
                m_IsShowingLeftMenu = false;
                showLeftMenu();
            }
            else if (m_IsShowFourDLeftMenu)
            {
                showLeftMenu();
                m_LeftMenuWidget->setMenuTitle(menuTitleTred(categoryName));
            }
        }
        m_ShowOthers = true;
        emit menuChange(categoryName);
        m_LeftMenuWidget->checkVisible();
        return;
    }

    clearMenuItems();
    getCurrentToolList(categoryName);
    getCurrentMenuItems(categoryName);
    showLeftMenu();

    // 创建菜单时根据需要确定是否默认激活第一个可用菜单
    if (needRefreshHighLight)
    {
        highlightFirst();
    }

    m_CurrentMenuCategory = categoryName;

    emit menuChange(categoryName);

    if (ModelConfig::instance().value(ModelConfig::LeftMenuAlwaysShow, false).toBool() && !m_ShowOthers)
    {
        setShowLeftMenu(true);
    }
}

void MenuController::createLeftMenu(const bool needRefreshHighLight)
{
    createLeftMenu(m_CurrentMenuCategory, needRefreshHighLight);

    emit menuChange(m_CurrentMenuCategory);
}

LeftMenuWidget::WidgetType MenuController::currentActiveWidget() const
{
    if (m_LeftMenuWidget != NULL)
    {
        return LeftMenuWidget::WidgetType(m_LeftMenuWidget->currentActiveWidget());
    }
    return LeftMenuWidget::Blank;
}

void MenuController::setLeftMenuActiveWidget(LeftMenuWidget::WidgetType type)
{
    if (m_LeftMenuWidget != NULL)
    {
        m_LeftMenuWidget->setActiveWidget(type);
    }
}

void MenuController::multi(bool clockwise)
{
    if (m_IsShowingLeftMenu)
    {
        if (m_CurrentToolListOfLeftMenu != NULL)
        {
            ITool* currentTool = m_CurrentToolListOfLeftMenu->currentTool();

            if (currentTool != NULL && currentTool->isAvailable() && currentTool->isActive())
            {
                //可调节菜单
                QVariantCommandArgs arg;
                arg.setArg(clockwise);
                currentTool->setArgs(&arg);
                currentTool->run();
            }
            else
            {
                m_CurrentToolListOfLeftMenu->multiCurrentIndex(clockwise);
            }
        }
    }
    else
    {
        if (m_LeftMenuWidget->currentActiveWidget() == LeftMenuWidget::Blank)
        {
            //如果调节参数时，没显示菜单，默认弹出菜单
            setShowLeftMenu(true);
        }
    }
}

void MenuController::multi(bool clockwise, QString str)
{
    if (m_IsShowingLeftMenu)
    {
        if (m_CurrentToolListOfLeftMenu != NULL)
        {
            ITool* currentTool = m_CurrentToolListOfLeftMenu->currentTool();

            if ((currentTool != NULL) && (str == "Left") && currentTool->isAvailable())
            {
                //可调节菜单左右
                QVariantCommandArgs arg;
                arg.setArg(clockwise);
                currentTool->setArgs(&arg);
                currentTool->run();
            }
            else if ((currentTool != NULL) && (str == "Right") && currentTool->isAvailable())
            {
                QVariantCommandArgs arg;
                arg.setArg(clockwise);
                currentTool->setArgs(&arg);
                currentTool->run();
            }
            else if ((currentTool != NULL) && (str != "Right") && (str != "Left"))
            {
                //调节菜单上下
                m_CurrentToolListOfLeftMenu->multiCurrentIndex(clockwise);
            }
        }
    }
    else
    {
        if (m_LeftMenuWidget->currentActiveWidget() == LeftMenuWidget::Blank)
        {
            //如果调节参数时，没显示菜单，默认弹出菜单
            setShowLeftMenu(true);
        }
    }
}

void MenuController::click()
{
    if (m_IsShowingLeftMenu)
    {
        if (m_CurrentToolListOfLeftMenu != NULL)
        {
            //点击菜单
            ITool* tool = m_CurrentToolListOfLeftMenu->currentTool();
            if (tool->isAvailable())
            {
                if (tool->isClickedTool())
                {
                    tool->run();
                }
                else
                {
                    if (tool->isActive())
                    {
                        tool->setIsActive(false);
                    }
                    else
                    {
                        tool->setIsActive(true);
                    }
                    m_LeftMenuWidget->update();
                }
            }
        }
    }
    else
    {
        if (m_LeftMenuWidget->currentActiveWidget() == LeftMenuWidget::Blank)
        {
            setShowLeftMenu(true);
        }
    }
}

void MenuController::clickUpKey1()
{
    clickUpDownKey(0, true);
}

void MenuController::clickDownKey1()
{
    clickUpDownKey(0, false);
}

void MenuController::clickKey1()
{
    clickKey(0);
}

void MenuController::clickUpKey2()
{
    clickUpDownKey(1, true);
}

void MenuController::clickDownKey2()
{
    clickUpDownKey(1, false);
}

void MenuController::clickKey2()
{
    clickKey(1);
}

void MenuController::clickUpKey3()
{
    clickUpDownKey(2, true);
}

void MenuController::clickDownKey3()
{
    clickUpDownKey(2, false);
}

void MenuController::clickKey3()
{
    clickKey(2);
}

void MenuController::clickUpKey4()
{
    clickUpDownKey(3, true);
}

void MenuController::clickDownKey4()
{
    clickUpDownKey(3, false);
}

void MenuController::clickKey4()
{
    clickKey(3);
}

void MenuController::clickUpKey5()
{
    clickUpDownKey(4, true);
}

void MenuController::clickDownKey5()
{
    clickUpDownKey(4, false);
}

void MenuController::clickKey5()
{
    clickKey(4);
}

void MenuController::clickUpKey6()
{
    clickUpDownKey(5, true);
}

void MenuController::clickDownKey6()
{
    clickUpDownKey(5, false);
}

void MenuController::clickKey6()
{
    clickKey(5);
}

void MenuController::clickKey(int id)
{
    //防止非旋钮模式下事件调用崩溃
    if (m_curButtonMode == MenuOperationModeDef::Rotate_Mode && m_IsBottomMenuEnabled)
    {
        QList<ITool*> tools = buttonMenu()->getTools(id);

        if (!tools.isEmpty())
        {
            ITool* tool = tools.first();

            if (tool != NULL && tool->isAvailable() && tool->isVisible())
            {
                //调用此处前设置的ARGS为clicktool的ARGS,非最终tool的ARGS
                //相同名称tool在其他地方使用时设置了ARGS,而此处未设置NULL
                // tool执行时加载m_args时会指针错误导致崩溃
                tool->setArgs(NULL);
                tool->run();
            }
        }
    }
}

int MenuController::currentParemeterMenuIndex() const
{
    return m_currentParemeterMenuIndex;
}

void MenuController::setToolVisible(const QString& toolName, bool isVisible)
{
    if (!isVisible)
    {
        m_visibleToolFilter.insert(toolName);
    }
    else
    {
        m_visibleToolFilter.remove(toolName);
    }
    refreshMenuTool();
    refreshLeftAndBottomMenu();
}

void MenuController::setToolVisible(const QStringList& toolNameList, bool isVisible)
{
    if (!isVisible)
    {
        foreach (const QString& val, toolNameList)
        {
            m_visibleToolFilter.insert(val);
        }
    }
    else
    {
        foreach (const QString& val, toolNameList)
        {
            m_visibleToolFilter.remove(val);
        }
    }
    refreshMenuTool();
    refreshLeftAndBottomMenu();
}

void MenuController::refreshLeftAndBottomMenu()
{
    refreshLeftMenu();
    refreshBottomMenu();
}

void MenuController::setMousePressDisabled(bool value)
{
    m_IsMousePressDisabled = value;
}

void MenuController::setIsBottomMenuEnabled(bool value)
{
    m_IsBottomMenuEnabled = value;
}

void MenuController::setIsSupportRotateButton(bool value)
{
    m_isSupportRotateButton = value;
}

bool MenuController::isSupportRotateButton() const
{
    return m_isSupportRotateButton;
}

void MenuController::refreshLeftMenu()
{
    createLeftMenu();
}

void MenuController::refreshBottomMenu()
{
    //    m_ButtonMenuPanel->setVisibleToolFilter(m_visibleToolFilter);
    createButtonMenu(m_CurrentMenuCategory);
    createTPBottomMenu(m_CurrentMenuCategory);
}

void MenuController::refreshMenuTool()
{
    if (m_ToolsFacade == nullptr)
    {
        return;
    }

    foreach (const QString& tlname, m_ToolsFacade->toolNames())
    {
        ITool* tool = m_ToolsFacade->tool(tlname);

        if (tool == NULL)
        {
            continue;
        }

        if (m_visibleToolFilter.contains(tlname))
        {
            tool->setIsVisible(false);
        }
        else if (!AppSetting::isPresetMode() && tool->isEngineeringTool())
        {
            tool->setIsVisible(false);
        }
        else
        {
            tool->setIsVisible(true);
        }
    }
}

void MenuController::initFuncNamesInMenu()
{
    QList<int> funcKeys =
        QList<int>() << LicenseItemKey::KeyPw << LicenseItemKey::KeyChroma << LicenseItemKey::KeyTHI
                     << LicenseItemKey::KeySRA << LicenseItemKey::KeyCompound << LicenseItemKey::KeyIImage
                     << LicenseItemKey::Key2DSteer << LicenseItemKey::KeyNeedle << LicenseItemKey::KeyECG
                     << LicenseItemKey::KeyQBeam << LicenseItemKey::KeyQFlow << LicenseItemKey::KeyBBC
                     << LicenseItemKey::KeyTriplex << LicenseItemKey::KeyPD << LicenseItemKey::KeyVolume
                     << LicenseItemKey::KeyCMD << LicenseItemKey::KeyCW << LicenseItemKey::KeyTDI
                     << LicenseItemKey::KeyElastography << LicenseItemKey::KeyAM << LicenseItemKey::KeyStressEcho
                     << LicenseItemKey::KeyFreehand3D << LicenseItemKey::KeyCurvedPanoramic
                     << LicenseItemKey::KeyTrapezoidalMode << LicenseItemKey::KeyCurvedExapanding
                     << LicenseItemKey::KeyXContrast << LicenseItemKey::KeyFourSyncM << LicenseItemKey::KeyRemote
                     << LicenseItemKey::KeyAutoNeedleAngle << LicenseItemKey::KeySonoCoach
                     << LicenseItemKey::KeySonoNeedle << LicenseItemKey::KeyRTIMT << LicenseItemKey::KeySonoNerve
                     << LicenseItemKey::KeyIntelligentDoppler;

    foreach (int key, funcKeys)
    {
        QStringList names = getFuncNames(key);
        if (!m_FuncNames.contains(key) && !names.isEmpty())
        {
            m_FuncNames.insert(key, names);
        }
    }
}

QStringList MenuController::getFuncNames(int key) const
{
    QStringList strList;
    switch (key)
    {
    case LicenseItemKey::KeyPw:
        strList << ToolNames::QuickAngleStr;
        break;

    case LicenseItemKey::KeyChroma:
        strList << BFPNames::BColorMapIndexStr << BFPNames::MColorMapIndexStr;
        break;

    case LicenseItemKey::KeyTHI:
        strList << BFPNames::THIStateStr;
        break;

    case LicenseItemKey::KeySRA:
        strList << BFPNames::FcpdOnStr;
        break;

    case LicenseItemKey::KeyCompound:
        strList << BFPNames::ScpdOnStr;
        break;

    case LicenseItemKey::KeyIImage:
        strList << BFPNames::iImageStr;
        break;

    case LicenseItemKey::Key2DSteer:
        strList << BFPNames::BSteeringScanStr;
        break;

    case LicenseItemKey::KeyNeedle:
        strList << BFPNames::NeedleModeStr << BFPNames::NeedleAngleIndexStr;
        break;

    case LicenseItemKey::KeyVolume:
        //目前只有ECO2会出现需要控制的情况，EBit的其他Volume参数不放入控制
        // AppDescription DefFunction的值为-1
        if (AppSetting::modelId() == License::ECO2 || AppSetting::modelId() == License::ECO2Vet)
        {
            strList << BFPNames::VolumeStr;
        }
        break;

    case LicenseItemKey::KeyCMD:
        strList << BFPNames::EnhanceStr;
        break;

    case LicenseItemKey::KeyPD:
        strList << ToolNames::PDModeStr << ToolNames::DPDModeStr;
        break;

    case LicenseItemKey::KeyECG:
        strList << BFPNames::ECGEnStr << BFPNames::ECGGainStr << BFPNames::ECGInvertStr << BFPNames::ECGPosStr
                << BFPNames::ECGVelocityStr << BFPNames::ECGDlyShowStr;
        break;

    case LicenseItemKey::KeyQBeam:
        strList << BFPNames::QBeamOnStr;
        break;

    case LicenseItemKey::KeyQFlow:
        strList << BFPNames::QFlowOnStr;
        break;

    case LicenseItemKey::KeyBBC:
        strList << BFPNames::BCImagesOnStr;
        break;

    case LicenseItemKey::KeyTriplex:
        strList << BFPNames::TriplexModeStr << BFPNames::BTriplexPrt_DeltaStr;
        break;

    case LicenseItemKey::KeyCW:
        strList << BFPNames::CWEnStr;
        break;

    case LicenseItemKey::KeyTDI:
        strList << BFPNames::TDIEnStr;
        break;

    case LicenseItemKey::KeyElastography:
        strList << ToolNames::ElastoGraphyStr;
        break;

    case LicenseItemKey::KeyAM:
        strList << ToolNames::FreeMModeStr;
        break;

    case LicenseItemKey::KeyCurvedPanoramic:
        strList << ToolNames::CurvedPanoramicStr;
        break;

    case LicenseItemKey::KeyStressEcho:
        strList << ToolNames::StressEchoStr;
        break;
    case LicenseItemKey::KeyTrapezoidalMode:
        strList << BFPNames::TrapezoidalModeStr << BFPNames::VirtualVertexTrapezoidalModeStr;
        break;
    case LicenseItemKey::KeyCurvedExapanding:
        strList << BFPNames::CurvedExapandingStr;
        break;
    case LicenseItemKey::KeyXContrast:
        strList << BFPNames::XContrastValueStr;
        break;
    case LicenseItemKey::KeyFourSyncM:
        strList << BFPNames::QuadplexModeStr;
        break;
    case LicenseItemKey::KeyRemote:
        strList << BFPNames::SonoRemoteStr;
        break;
    case LicenseItemKey::KeyAutoNeedleAngle:
        strList << BFPNames::IsAutoNeedleAngleEnableStr;
        break;
    case LicenseItemKey::KeySonoCoach:
        strList << BFPNames::SonoHelpStr;
        break;
    case LicenseItemKey::KeySonoNeedle:
        strList << BFPNames::SonoNeedleStr;
        break;
    case LicenseItemKey::KeyRTIMT:
        strList << BFPNames::RTIMTStr;
        break;
    case LicenseItemKey::KeySonoNerve:
        strList << ToolNames::SonoNerveStr;
        break;
    case LicenseItemKey::KeyIntelligentDoppler:
        strList << BFPNames::IsEnableAdjustROIStr;
        break;
    }
    return strList;
}

void MenuController::chooseVisibleToolInsert(MenuToolItem* item, bool visible)
{
    if (visible && item->tool()->isVisible())
    {
        m_CurrentMenuItems->insert(0, item);
    }
    else
    {
        item->setVisible(false);
    }
}

void MenuController::clickUpDownKey(int id, bool up)
{
    if (!m_IsBottomMenuEnabled)
    {
        return;
    }

    // edit xule
    // if (!m_isSupportRotateButton)
    if (m_curButtonMode == MenuOperationModeDef::Normal_Mode)
    {
        QList<ITool*> tools = buttonMenu()->getTools(id);
        if (tools.count() == 1)
        {
            ITool* tool = tools.first();
            if (tool != NULL && tool->isAvailable() && tool->isVisible())
            {
                if (tool->isClickedTool())
                {
                    tool->run();
                }
                else
                {
                    QVariantCommandArgs args(up);
                    tool->setArgs(&args);
                    tool->run();
                }
            }
        }
        else if (tools.count() == 2)
        {
            QVariantCommandArgs args(up);
            int index = args.arg() ? 0 : 1;
            ITool* tool = tools.at(index);
            if (tool != NULL && tool->isAvailable() && tool->isVisible())
            {
                tool->setArgs(&args);
                tool->run();
            }
        }
    }
    else if (m_curButtonMode == MenuOperationModeDef::Rotate_Mode)
    {
        QList<ITool*> tools = buttonMenu()->getTools(id);

        if (tools.count() == 2)
        {
            ITool* tool = tools.at(1);

            if (tool != NULL && tool->isAvailable() && tool->isVisible())
            {
                QVariantCommandArgs args(up);
                tool->setArgs(&args);
                tool->run();
            }
        }
    }
    else if (m_curButtonMode == MenuOperationModeDef::ActiveItem_Mode)
    {
        QList<ITool*> tools = buttonMenu()->getTools(id);
        if (m_currentBottomMenuIndex < tools.count())
        {
            ITool* tool = tools.at(m_currentBottomMenuIndex);
            if (tool != NULL && tool->isAvailable() && tool->isVisible())
            {
                if (tool->isLoopMulti())
                {
                    tool->run();
                }
                else
                {
                    QVariantCommandArgs args(up);
                    tool->setArgs(&args);
                    tool->run();
                }
            }
        }
    }

    emitSKLogoVisible(id);
}

void MenuController::clearActiveState()
{
    //每次切换菜单时，把老的已选中的状态清除，否则菜单会出现multi时，会使
    // click菜单执行，导致multi无法实现切换菜单的功能
    if (m_CurrentToolListOfLeftMenu != NULL)
    {
        ITool* tool = m_CurrentToolListOfLeftMenu->currentTool();
        if (tool != NULL)
        {
            if (tool->isActive())
            {
                tool->setIsActive(false);
                m_LeftMenuWidget->update();
            }
        }
    }
}

void MenuController::clearMenuItems()
{
    if (m_CurrentMenuItems != NULL)
    {
        for (int i = 0; i < m_CurrentMenuItems->count(); i++)
        {
            MenuToolItem* menu = m_CurrentMenuItems->at(i);
            menu->setVisible(false);
            disconnect(menu, SIGNAL(mousePress(QMouseEvent*, QString)), this,
                       SLOT(onLeftMenuMousePress(QMouseEvent*, QString)));
        }
        m_CurrentMenuItems->clear();
    }
    m_LeftMenuWidget->clearMenuItems();
}

void MenuController::getCurrentToolList(QString categoryName)
{
    m_ToolSet->setCurrentCategoryOfLeftMenu(categoryName);
    m_CurrentToolListOfLeftMenu = m_ToolSet->currentToolListOfLeftMenu();

    if (m_CurrentToolListOfLeftMenu != NULL)
    {
        //    m_CurrentToolListOfLeftMenu->setVisibleToolFilter(m_visibleToolFilter);
        m_CurrentToolListOfLeftMenu->filterVisibleTools();

        //当toollist中的currentIndex改变时更新leftmenuwidget
        disconnect(m_CurrentToolListOfLeftMenu, SIGNAL(updateWidget(int, bool)), m_LeftMenuWidget,
                   SLOT(onWidgetUpdate(int, bool))); // add by gongdl
        connect(m_CurrentToolListOfLeftMenu, SIGNAL(updateWidget(int, bool)), m_LeftMenuWidget,
                SLOT(onWidgetUpdate(int, bool)));
        //    m_CurrentToolListOfLeftMenu->setCurrentIndex(m_CurrentToolListOfLeftMenu->currentIndex());
        // m_CurrentToolListOfLeftMenu->setIncludeAdvTool(includeAdvTool);
        m_CurrentToolListOfLeftMenu->setParentCategory(m_ParentCategory);
    }
}

void MenuController::getCurrentMenuItems(QString categoryName)
{
    if (!m_MenuItemSet.contains(categoryName))
    {
        QList<MenuToolItem*>* menuList = new QList<MenuToolItem*>();
        m_MenuItemSet.insert(categoryName, menuList);

        if (m_CurrentToolListOfLeftMenu != NULL)
        {
            for (int i = 0; i < m_CurrentToolListOfLeftMenu->count(); i++)
            {
                MenuToolItem* item = NULL;

                if (categoryName == MenuNames::SonoCardiacMenuStr)
                {
                    item = new DiyLeftMenuItem(m_LeftMenuWidget);
                }
                else if (m_ScreenIsTouchable)
                {
                    item = new TouchableMenuToolItem(m_LeftMenuWidget);
                }
                else
                {
                    item = new MenuToolItem(m_LeftMenuWidget);
                }
                item->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
                item->setTool(m_CurrentToolListOfLeftMenu->getTool(i));

                if (!menuList->contains(item))
                {
                    menuList->append(item);
                }
            }
        }
    }

    QList<MenuToolItem*>* items = m_MenuItemSet.value(categoryName);
    if (m_CurrentToolListOfLeftMenu != NULL && m_CurrentToolListOfLeftMenu->isIncludeAdvTool())
    {
        for (int i = 0; i < items->count(); i++)
        {
            if (AppSetting::isPresetMode())
            {
                chooseVisibleToolInsert(items->at(i), true);
            }
            else
            {
                chooseVisibleToolInsert(items->at(i), !items->at(i)->tool()->isEngineeringTool());
            }
        }
    }
    else
    {
        for (int i = 0; i < items->count(); i++)
        {
            if (AppSetting::isPresetMode())
            {
                chooseVisibleToolInsert(items->at(i), !items->at(i)->tool()->isAdvanceTool());
            }
            else
            {
                chooseVisibleToolInsert(items->at(i), !items->at(i)->tool()->isAdvanceTool() &&
                                                          !items->at(i)->tool()->isEngineeringTool());
            }
        }
    }
}

void MenuController::showLeftMenu()
{
    int menusHeight = 0;
    if (m_IsShowingLeftMenu)
    {
        m_LeftMenuWidget->resetMenuItemCount();
        for (int i = 0; i < m_CurrentMenuItems->count(); i++)
        {
            m_CurrentMenuItems->at(i)->setVisible(true);
        }

        for (int i = 0; i < m_CurrentMenuItems->count(); i++)
        {
            MenuToolItem* menu = m_CurrentMenuItems->value(i);
            m_LeftMenuWidget->addMenuItem(menu);
            menusHeight += menu->size().height();
            //左菜单在exit时不显示,但不会清除菜单内容,不会进行disconnect
            connect(menu, SIGNAL(mousePress(QMouseEvent*, QString)), this,
                    SLOT(onLeftMenuMousePress(QMouseEvent*, QString)), Qt::UniqueConnection);
        }
        m_LeftMenuWidget->refresh();
        m_LeftMenuWidget->show();

        m_LeftMenuWidget->setActiveWidget(LeftMenuWidget::Menu);

        //        if (m_CurrentToolListOfLeftMenu != NULL)
        //        {
        //            m_CurrentToolListOfLeftMenu->setCurrentIndex(m_CurrentToolListOfLeftMenu->currentIndex());
        //        }
        emit showing(true);
        LightController::instance().light(LightNames::MenuStr, true);
    }
    else if (m_IsShowFourDLeftMenu)
    {
        m_LeftMenuWidget->refresh();
        m_LeftMenuWidget->show();
        m_LeftMenuWidget->setActiveWidget(LeftMenuWidget::FourDMenu);
        emit showing(false);
        LightController::instance().light(LightNames::MenuStr, false);
    }
    else
    {
        m_LeftMenuWidget->setActiveWidget(LeftMenuWidget::Blank);
        clearActiveState();
        emit showing(false);
        LightController::instance().light(LightNames::MenuStr, false);
    }

    //    if (m_LeftMenuWidget->height() < menusHeight) // 存在垂直滚动条 = 需要支持滑动效果
    //    {
    //        // 不可用的控件需要将鼠标事件传递至父组件从而实现拖拽滚动效果
    //        for(int i=0; i<m_CurrentMenuItems->count(); i++)
    //        {
    //            m_CurrentMenuItems->value(i)->setAttribute(Qt::WA_TransparentForMouseEvents,
    //            !m_CurrentMenuItems->value(i)->tool()->isAvailable());
    //        }
    //    }
}

ToolList* MenuController::currentToolListOfLeftMenu()
{
    return m_CurrentToolListOfLeftMenu;
}

void MenuController::setShowLeftMenu(bool value)
{
    if (m_CurrentToolListOfLeftMenu != NULL)
    {
        if (ModelConfig::instance().value(ModelConfig::LeftMenuAlwaysShow, false).toBool() && !value)
        {
            return;
        }

        {
            m_IsShowingLeftMenu = value;
            emit isShowing(value);

            showLeftMenu();

            ApplicationInfo::instance().toggleWindow(WindowInfo(this), m_IsShowingLeftMenu);
        }
    }
}

void MenuController::setShowFourDLeftMenu(bool value)
{
    m_IsShowFourDLeftMenu = value;
    m_LeftMenuWidget->setActiveWidget(LeftMenuWidget::Blank);
    showLeftMenu();
}

void MenuController::setShowEFastLeftMenu(bool value)
{
    m_IsShowEFastLeftMenu = value;
}

void MenuController::setShowFastLeftMenu(bool value)
{
    m_IsShowFastLeftMenu = value;
}

void MenuController::setAdvOfCurrentToolList(bool value)
{
    if (m_CurrentToolListOfLeftMenu != NULL)
    {
        m_CurrentToolListOfLeftMenu->setIncludeAdvTool(value);
    }
}

bool MenuController::isAdvOfCurrentToolList() const
{
    if (m_CurrentToolListOfLeftMenu != NULL)
    {
        return m_CurrentToolListOfLeftMenu->isIncludeAdvTool();
    }
    else
    {
        return false;
    }
}

void MenuController::initalize(IContextData* context)
{
    if (m_ToolsFacade == nullptr)
    {
        return;
    }

    QList<ITool*> tools;
    int priorityLevel = 0;
    foreach (ICommand* command, m_ToolsFacade->commands().values())
    {
        ITool* tool = dynamic_cast<ITool*>(command);
        if (tool != NULL && tool->isParameterTool())
        {
            if (tool->priorityLevel() > priorityLevel)
            {
                tools.append(tool);
            }
            else
            {
                tool->initialize(context);
            }
        }
    }
    ++priorityLevel;
    while (!tools.isEmpty())
    {
        QList<ITool*> tools2;
        foreach (ITool* tool, tools)
        {
            if (tool->priorityLevel() > priorityLevel)
            {
                tools2.append(tool);
            }
            else
            {
                tool->initialize(context);
            }
        }
        ++priorityLevel;
        tools = tools2;
    }
}

bool MenuController::isShowingLeftMenu() const
{
    return m_IsShowingLeftMenu;
}

void MenuController::reject()
{
    if (!ModelConfig::instance().value(ModelConfig::LeftMenuAlwaysShow, false).toBool())
    {
        setShowLeftMenu(false);
    }
}

void MenuController::onLeftMenuPageChanged(int index)
{
    if (index != LeftMenuWidget::Menu && index != LeftMenuWidget::Blank)
    {
        if (ModelConfig::instance().value(ModelConfig::LeftMenuAlwaysShow, false).toBool())
        {
            return;
        }
        {
            m_IsShowingLeftMenu = false;

            clearActiveState();

            ApplicationInfo::instance().toggleWindow(WindowInfo(this), m_IsShowingLeftMenu);
        }
    }
}

void MenuController::onPresetModeChanged(bool value)
{
    if (value)
    {
        m_visibleToolFilter.clear();
        refreshMenuTool();
        refreshLeftAndBottomMenu();
    }
    else
    {
    }
}

void MenuController::onFunctionChanged(int key, bool value)
{
    prepareVisibleToolFilter(m_FuncNames.value(key), value);
    refreshMenuTool();
    refreshLeftAndBottomMenu();
}

void MenuController::onButtonMenuMousePress(QMouseEvent* event, bool isTopLabel, int index)
{
    if (m_IsMousePressDisabled)
    {
        return;
    }
    if (event->button() != Qt::LeftButton && event->button() != Qt::RightButton)
    {
        return;
    }
    bool add = true;
    if (event->button() == Qt::RightButton)
    {
        add = false;
    }

    if (m_curButtonMode == MenuOperationModeDef::Normal_Mode)
    {
        QList<ITool*> tools = buttonMenu()->getTools(index);
        if (tools.count() == 1)
        {
            clickUpDownKey(index, add);
        }
        else if (tools.count() == 2)
        {
            clickUpDownKey(index, isTopLabel);
        }
    }
    else if (m_curButtonMode == MenuOperationModeDef::Rotate_Mode)
    {
        if (isTopLabel)
        {
            emit startAutoLoop();
            clickKey(index);
        }
        else
        {
            clickUpDownKey(index, add);
        }
    }
    else if (m_curButtonMode == MenuOperationModeDef::ActiveItem_Mode)
    {
        bool isUpLabelActive = ((m_currentBottomMenuIndex % 2) == 0);
        if ((isUpLabelActive && isTopLabel) || (!isUpLabelActive && !isTopLabel))
        {
            clickUpDownKey(index, add);
        }
    }
}

void MenuController::onLeftMenuMousePress(QMouseEvent* event, QString toolName)
{
    if (m_IsMousePressDisabled)
    {
        return;
    }

    for (int i = 0; i < m_CurrentToolListOfLeftMenu->count(); i++)
    {
        if (i != m_currentParemeterMenuIndex)
        {
            m_CurrentToolListOfLeftMenu->getTool(i)->setIsActive(false);
        }
    }

    MenuToolItem* menu = dynamic_cast<MenuToolItem*>(sender());
    if (m_CurrentToolListOfLeftMenu != NULL && menu != NULL)
    {
        if (toolName != m_CurrentToolListOfLeftMenu->currentTool()->toolName())
        {
            m_CurrentToolListOfLeftMenu->onSelected(toolName, true);
        }

        ITool* tool = menu->tool();
        if (tool->isAvailable())
        {
            if (tool->isClickedTool())
            {
                tool->run();
            }
            else
            {
                if (!tool->isActive())
                {
                    tool->setIsActive(true);
                }

                bool value = true;
                if (event->button() == Qt::RightButton)
                {
                    value = false;
                }
                QVariantCommandArgs args(value);
                tool->setArgs(&args);
                tool->run();
            }
        }
    }
}

void MenuController::prepareVisibleToolFilter(const QStringList& strList, bool value)
{
    foreach (QString str, strList)
    {
        if (!value)
        {
            m_visibleToolFilter << str;
        }
        else
        {
            m_visibleToolFilter.remove(str);
        }
    }
}

// add xule
void MenuController::setButtonMenuRow(bool value)
{
    if (m_curButtonMode == MenuOperationModeDef::ActiveItem_Mode)
    {
        if (value)
        {
            if (m_ButtonMenuPanel->isLastItems())
            {
                return;
            }
            m_currentBottomMenuIndex++;
        }
        else
        {
            if (m_currentBottomMenuIndex == 0)
            {
                return;
            }
            m_currentBottomMenuIndex--;
        }

        ApplicationInfo::instance().flashUpAndDownArrow(!value);

        refreshBottomMenu();
        refreshUpAndDownArrowState();
    }
}

void MenuController::onResetBottomMenuRow()
{
    m_currentBottomMenuIndex = 0;
    refreshBottomMenu();
    refreshUpAndDownArrowState();
}

void MenuController::onToolChanged()
{
    ITool* tool = static_cast<ITool*>(sender());
    if (tool != NULL && m_ControlPanel != NULL)
    {
        m_ControlPanel->onNTPButClicked(tool->toolCategory(), tool->captionTred(), tool->valueTred());
    }
}

void MenuController::onCurMenuChanged(const QString& menu)
{
    createMenu(menu);
}

void MenuController::setCurButtonMode(MenuOperationModeDef::OperationMode mode)
{
    m_curButtonMode = mode;
}

MenuOperationModeDef::OperationMode MenuController::getCurButtonMode() const
{
    return m_curButtonMode;
}

QString MenuController::currentMenuCategory() const
{
    return m_CurrentMenuCategory;
}

const QList<ITool*>* MenuController::leftMenuTools(const QString& categoryName)
{
    QString currentCategoryName = m_ToolSet->currentCategoryOfLeftMenu();
    m_ToolSet->setCurrentCategoryOfLeftMenu(categoryName);
    ToolList* curToolsOfLeftMenu = m_ToolSet->currentToolListOfLeftMenu();
    if (curToolsOfLeftMenu != NULL)
    {
        const QList<ITool*>* tools = m_ToolSet->currentToolListOfLeftMenu()->allTools();
        m_ToolSet->setCurrentCategoryOfLeftMenu(currentCategoryName);
        return tools;
    }
    else
    {
        return NULL;
    }
}

const QList<ITool*>* MenuController::bottomMenuTools(const QString& categoryName)
{
    QString currentCategoryName = m_ToolSet->currentCateoryOfBottomMenu();
    m_ToolSet->setCurrentCategoryOfBottomMenu(categoryName);
    ToolList* toolList = m_ToolSet->currentToolListOfBottomMenu();
    if (toolList != NULL)
    {
        const QList<ITool*>* tools = toolList->allTools();
        m_ToolSet->setCurrentCategoryOfBottomMenu(currentCategoryName);
        return tools;
    }
    else
    {
        return NULL;
    }
}

/**
 * @brief MenuController::setCurvedMenu
 * @param value
 * @desc 宽景需求：在实时宽景宽景和冻结宽景下，左侧和底部均显示宽景菜单；回调下显示Freeze菜单
 *       实现方案：在进入宽景状态时将该变量设置为true，在回调动作触发时设置为false
 */
void MenuController::setCurvedMenu(bool value)
{
    m_CurvedMenu = value;
}

void MenuController::refreshUpAndDownArrowState()
{
    if (m_ModeChangeWidget == NULL)
    {
        return;
    }
    bool enUpArrow = true;
    bool enDownArrow = true;
    if (m_ButtonMenuPanel->isLastItems())
    {
        enDownArrow = false;
    }
    if (m_currentBottomMenuIndex == 0)
    {
        enUpArrow = false;
    }
    m_ModeChangeWidget->setUpAndDownArrowState(enUpArrow, enDownArrow);
}

QString MenuController::menuTitleTred(const QString& categoryName) const
{
    QString menuStr = MenuNames::menuCaption(QCoreApplication::translate("MenuController", categoryName.toUtf8()));
    int m = menuStr.indexOf("Menu");
    if (m >= 0)
    {
        // modified by jyq to make menu to translate...
        //        menuStr.insert(m, " ");
        menuStr = menuStr.mid(0, m);
        //        menuStr += tr("Menu");
    }
    //    else
    //    {
    // modifeid by jyq to translate
    //        menuStr = tr(menuStr.toUtf8());
    //        menuStr += QString(" ") + tr("Menu");
    //    }

    return menuStr;
}

void MenuController::emitSKLogoVisible(const int id)
{
    if (m_ButtonMenuPanel->currentButtonMenuState(id))
    {
        m_ButtonMenuPanel->updateCurrentButtonMenuState(id);
        emit bottomMenuTipLogoVisible(true, id, true);
    }
}

void MenuController::expandAdvanceItem()
{
    m_LeftMenuWidget->expandAdvanceItem(m_CurrentToolListOfLeftMenu);
}

void MenuController::highlightFirst()
{
    for (int index = 0; index < m_CurrentToolListOfLeftMenu->visibleToolList()->count(); index++)
    {
        ITool* tool = m_CurrentToolListOfLeftMenu->visibleToolList()->at(index);
        if (tool != nullptr)
        {
            if (tool->isAvailable() && tool->isVisible())
            {
                m_CurrentToolListOfLeftMenu->setCurrentIndex(index);
                return;
            }
        }
    }
}

void MenuController::setActiveButtonPanelStyleSheet(const int index)
{
    if (m_ButtonMenuPanel != nullptr)
    {
        m_ButtonMenuPanel->setActiveButtonPanelStyleSheet(index);
    }
}

void MenuController::slotCurQuickMeasureToolIsActived(int index)
{
    if (!m_IsBottomMenuEnabled)
    {
        return;
    }

    if (buttonMenu() != nullptr)
    {
        buttonMenu()->setActiveButtonPanelStyleSheet(index);
    }

    if (m_curButtonMode == MenuOperationModeDef::Normal_Mode)
    {
        QList<ITool*> tools, toolsQuickDistance, toolsQuickArea, toolsQuickVolume;
        if (index == 0)
        {
            toolsQuickArea = buttonMenu()->getTools(1);
            toolsQuickVolume = buttonMenu()->getTools(2);
        }
        else if (index == 1)
        {
            toolsQuickDistance = buttonMenu()->getTools(0);
            toolsQuickVolume = buttonMenu()->getTools(2);
        }
        else if (index == 2)
        {
            toolsQuickDistance = buttonMenu()->getTools(0);
            toolsQuickArea = buttonMenu()->getTools(1);
        }
        else if (index == NONEACTIVE)
        {
            toolsQuickDistance = buttonMenu()->getTools(0);
            toolsQuickArea = buttonMenu()->getTools(1);
            toolsQuickVolume = buttonMenu()->getTools(2);
        }

        tools.append(toolsQuickDistance);
        tools.append(toolsQuickArea);
        tools.append(toolsQuickVolume);

        for (int i = 0; i < tools.count(); i++)
        {
            ITool* tool = tools.at(i);
            if (tool != NULL && tool->isAvailable() && tool->isVisible())
            {
                tool->setIsFirst(true);
            }
        }
    }
}

void MenuController::setModeChangeWidget(ModeChangeWidget* changeWidget)
{
    if (changeWidget != NULL)
    {
        m_ModeChangeWidget = changeWidget;
        connect(&ApplicationInfo::instance(), SIGNAL(resetBottomMenu()), this, SLOT(onResetBottomMenuRow()));
    }
}
