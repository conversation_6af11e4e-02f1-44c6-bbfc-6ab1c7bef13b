#include "mainwindowkbunit.h"
#include "ui_mainwindowkbunit.h"
#include "widgetfactory.h"
#include "topwidgetcontainer.h"
#include "bottomwidgetcontainer.h"
#include "tpbottomwidgetcontainer.h"
#include "titlebarwidgetcontainer.h"
#include "functionswidgetvisisblecontrolcontainer.h"
#include "functionbuttonswidgetcontainer.h"
#include "controlpanelwidgetcontainer.h"
#include "leftmenuwidgetcontainer.h"
#include "rightmenuwidgetcontainer.h"
#include "middledownwidgetcontainer.h"
#include "renderwidgetcontainer.h"
#include "navigationwidgetcontainer.h"
#include "lefttopmenuwidgetcontainer.h"
#include "auxiliaryfuncbtnwidgetcontainer.h"
#include "realscanuiloader.h"
#include "resource.h"
#include "dopplerviewercontext.h"
#include "mainwindowmiddledownwidget.h"
#include "contextsetuphelperbase.h"
#include "widgetcontainer.h"
#include <QDebug>
#include "uipropertyconfig.h"
#include "uijsonreader.h"
#include "modelconfig.h"
#include "modeluiconfig.h"
#include "topcontextsetuphelper.h"
#include "leftmenucontextsetuphelper.h"
#include "rightmenucontextsetuphelper.h"
#include "bottomcontextsetuphelper.h"
#include "tpbottomcontextsetuphelper.h"
#include "rendercontextsetuphelper.h"
#include "middledowncontextsetuphelper.h"
#include "navigationcontextsetuphelper.h"
#include "lefttopmenucontextsetuphelper.h"
#include "functionbuttonscontextsetuphelper.h"
#include "auxiliaryfunbtncontextsetuphelper.h"
#include "titlebarcontextsetuphelper.h"
#include "functionswidgetvisisblecontrolcontextsetuphelper.h"
#include "controlpanelcontextsetuphelper.h"
#include "uitool/custompropertyhelpercontainer.h"
#include "clipwidgetpropertyhelper.h"
#include "buttonmenupanelpropertyhelper.h"
#include "util.h"
#include "systemstatuswidget.h"
#ifdef USE_4D
#include "chisonfourdproxy.h"
#include "fourdlivewidgetcontainer.h"
#include "fourdrightwidgetcontainer.h"
#include "fourdlivecontextsetuphelper.h"
#include "fourdrightcontextsetuphelper.h"
#endif
#include "uiutil.h"
#include "workstatuscontextsetuphelper.h"
#include "workstatuswidgetcontainer.h"
#include "systemstatuswidgetcontainer.h"
#include "systemstatuscontextsetuphelper.h"
#include "systemtbstatuswidgetcontainer.h"
#include "systemtbstatuscontextsetuphelper.h"
#include "zoomonproxy.h"
#include <QGuiApplication>
#include <QScreen>
#include "popupwidget.h"
#define POSITION_SIZE 4
#define STRETCH_SIZE 2
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
#define Q_DECLARE_QTBUILTIN_METATYPE(TYPE)                                                                             \
    template <> void* qMetaTypeConstructHelper(const TYPE* t)                                                          \
    {                                                                                                                  \
        Q_UNUSED(t)                                                                                                    \
        return new TYPE();                                                                                             \
    }                                                                                                                  \
    Q_DECLARE_METATYPE(TYPE)
#else
#define Q_DECLARE_QTBUILTIN_METATYPE(TYPE)                                                                             \
    namespace QtMetaTypePrivate                                                                                        \
    {                                                                                                                  \
    template <> struct QMetaTypeFunctionHelper<TYPE>                                                                   \
    {                                                                                                                  \
        static void Destruct(void* t)                                                                                  \
        {                                                                                                              \
            Q_UNUSED(t)                                                                                                \
            static_cast<TYPE*>(t)->~TYPE();                                                                            \
        }                                                                                                              \
        static void* Construct(void* where, const void* t)                                                             \
        {                                                                                                              \
            Q_UNUSED(t)                                                                                                \
            return new (where) TYPE;                                                                                   \
        }                                                                                                              \
    };                                                                                                                 \
    }                                                                                                                  \
    Q_DECLARE_METATYPE(TYPE)
#endif

// 声明为qt内置类型,去除QWidget的Q_DISABLE_COPY的定义
Q_DECLARE_QTBUILTIN_METATYPE(TopWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(BottomWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(TPBottomWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(TitleBarWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(FunctionsWidgetVisisbleControlContainer)
Q_DECLARE_QTBUILTIN_METATYPE(FunctionButtonsWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(ControlPanelWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(LeftMenuWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(RightMenuWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(MiddleDownWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(RenderWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(NavigationWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(LeftTopMenuWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(AuxiliaryFuncBtnWidgetContainer)

#ifdef USE_4D
Q_DECLARE_QTBUILTIN_METATYPE(FourDLiveWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(FourDRightWidgetContainer)
#endif

Q_DECLARE_QTBUILTIN_METATYPE(WorkStatusWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(SystemStatusWidgetContainer)
Q_DECLARE_QTBUILTIN_METATYPE(SystemTBStatusWidgetContainer)

MainWindowKBUnit::MainWindowKBUnit(DopplerViewerContext* context, QWidget* parent)
    : MainWindowKBBase(context, parent)
    , ui(new Ui::MainWindowKBUnit)
    , m_FullScreenFlexBtn(new FlexButton(this))
    , m_LockScreenFlexBtn(new FlexButton(this))
    , m_SkTipIcon(new QLabel(this))
    , m_GainTipIcon(new QLabel(this))
    , m_SkVisible(false)
    , m_LockScreenTip(new QLabel(this))
    , m_SKVisibleTimer(new QTimer(this))
    , m_PopUpWidget(new PopUpWidget)
{
    ui->setupUi(this);
    ui->gridLayout->setAlignment(Qt::AlignTop | Qt::AlignHCenter);
    registerWidgets();
    registerSetupHelpers();
    loadUIConfig(Resource::mainWindowUIConfigIniName);
    initializeUI();
    initialSkTipIcon();
    initializeGainTipIcon();
    initializeLockScreenTipLabel();
    initializeSKVisibleTimer();

    setAttribute(Qt::WA_AcceptTouchEvents, true);

    /* 解决全屏对话框下的状态栏和主界面状态栏展示尺寸不一样的问题,
     * 原因是：
     *      主界面展示的状态栏隶属于对应的状态栏容器，uiconfig中对状态栏容器有margin的配置；
     *      全屏对话框直接使用的是systemStatusWidget，并没有任何地方对齐设置margin；
     *      从而导致两种情况下的显示存在差异。
     * 解决方案：在状态栏容器margin加载结束后，需要保存margin值，在systemStatusWidget进行同步设置
     */
    QMargins margins = m_ViewerContext->systemStatusTip()->layout()->contentsMargins();
    QStringList systemStatusWidgetMargins;
    systemStatusWidgetMargins << QString::number(margins.left()) << QString::number(margins.top())
                              << QString::number(margins.right()) << QString::number(margins.bottom());
    qApp->setProperty("systemStatusWidgetMargins", systemStatusWidgetMargins);
}

MainWindowKBUnit::~MainWindowKBUnit()
{
    delete ui;
    m_ContextWidgets.clear();
    delete m_PopUpWidget;
    m_PopUpWidget = nullptr;
}

void MainWindowKBUnit::enterFourDWidget(bool value)
{
#ifdef USE_4D
    m_ContextWidgets["FourDRightWidgetContainer"]->setVisible(false);
    m_ContextWidgets["RenderWidgetContainer"]->setVisible(!value);
    m_ContextWidgets["RightMenuWidgetContainer"]->setVisible(!value);
    m_ContextWidgets["FourDLiveWidgetContainer"]->setVisible(value);
#endif
}

void MainWindowKBUnit::setupRects()
{
}

void MainWindowKBUnit::generateButtonMenu()
{
}

void MainWindowKBUnit::generateBodyMarkWidget()
{
}

void MainWindowKBUnit::loadUIConfig(const QString& configName)
{
    RealScanUILoader uiLoader;
    uiLoader.loadUI(configName);
    setResolution(ModelConfig::instance().value(ModelConfig::MainWindowResolution, QSize(640, 512)).toSize());
    constructUI(uiLoader.itemInfos());
    enterFourDWidget(false);
    updateGeometry();
}

void MainWindowKBUnit::setResolution(const QSize& resolution)
{
    // 主分辨率需要从文件中读出,才能满足不同机型
    this->setFixedSize(resolution);
}

void MainWindowKBUnit::registerWidgets()
{
    qRegisterMetaType<TopWidgetContainer>("TopWidgetContainer");
    qRegisterMetaType<BottomWidgetContainer>("BottomWidgetContainer");
    qRegisterMetaType<TPBottomWidgetContainer>("TPBottomWidgetContainer");
    qRegisterMetaType<TitleBarWidgetContainer>("TitleBarWidgetContainer");
    qRegisterMetaType<FunctionsWidgetVisisbleControlContainer>("FunctionsWidgetVisisbleControlContainer");
    qRegisterMetaType<FunctionButtonsWidgetContainer>("FunctionButtonsWidgetContainer");
    qRegisterMetaType<ControlPanelWidgetContainer>("ControlPanelWidgetContainer");
    qRegisterMetaType<LeftMenuWidgetContainer>("LeftMenuWidgetContainer");
    qRegisterMetaType<RightMenuWidgetContainer>("RightMenuWidgetContainer");
    qRegisterMetaType<MiddleDownWidgetContainer>("MiddleDownWidgetContainer");
    qRegisterMetaType<RenderWidgetContainer>("RenderWidgetContainer");
    qRegisterMetaType<NavigationWidgetContainer>("NavigationWidgetContainer");
    qRegisterMetaType<LeftTopMenuWidgetContainer>("LeftTopMenuWidgetContainer");
    qRegisterMetaType<AuxiliaryFuncBtnWidgetContainer>("AuxiliaryFuncBtnWidgetContainer");

#ifdef USE_4D
    qRegisterMetaType<FourDLiveWidgetContainer>("FourDLiveWidgetContainer");
    qRegisterMetaType<FourDRightWidgetContainer>("FourDRightWidgetContainer");
#endif
    qRegisterMetaType<WorkStatusWidgetContainer>("WorkStatusWidgetContainer");
    qRegisterMetaType<SystemStatusWidgetContainer>("SystemStatusWidgetContainer");
    qRegisterMetaType<SystemTBStatusWidgetContainer>("SystemTBStatusWidgetContainer");
}

void MainWindowKBUnit::registerSetupHelpers()
{
    m_HelperMananager.registerHelper("WorkStatusWidgetContainer", new WorkStatusContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("SystemStatusWidgetContainer",
                                     new SystemStatusContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("SystemTBStatusWidgetContainer",
                                     new SystemTBStatusContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("TopWidgetContainer", new TopContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("BottomWidgetContainer", new BottomContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("TPBottomWidgetContainer", new TPBottomContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("LeftMenuWidgetContainer", new LeftMenuContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("RightMenuWidgetContainer", new RightMenuContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("MiddleDownWidgetContainer", new MiddleDownContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("RenderWidgetContainer", new RenderContextSetupHelper(m_ViewerContext));
#ifdef USE_4D
    m_HelperMananager.registerHelper("FourDLiveWidgetContainer", new FourDLiveContextSetupHelper(m_Viewer));
    m_HelperMananager.registerHelper("FourDRightWidgetContainer", new FourDRightContextSetupHelper(m_Viewer));
#endif

    m_HelperMananager.registerHelper("NavigationWidgetContainer", new NavigationContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("LeftTopMenuWidgetContainer", new LeftTopMenuContextSetuphelper(m_ViewerContext));
    m_HelperMananager.registerHelper("FunctionButtonsWidgetContainer",
                                     new FunctionButtonsContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("AuxiliaryFuncBtnWidgetContainer",
                                     new AuxiliaryFunBtnContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("ControlPanelWidgetContainer",
                                     new ControlPanelContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("FunctionsWidgetVisisbleControlContainer",
                                     new FunctionsWidgetVisisbleControlContextSetupHelper(m_ViewerContext));
    m_HelperMananager.registerHelper("TitleBarWidgetContainer", new TitleBarContextSetupHelper(m_ViewerContext));

    CustomPropertyHelperContainer::instance().addHelper("ClipWidget", new ClipWidgetPropertyHelper());
    CustomPropertyHelperContainer::instance().addHelper("ButtonMenuPanel", new ButtonMenuPanelPropertyHelper());
}

void MainWindowKBUnit::constructUI(const QList<GridItemInfo>& infos)
{
    foreach (const GridItemInfo& info, infos)
    {
        // 通过类名生成对应控件类,加入到布局中
        WidgetContainer* w = WidgetFactory::createWidget<WidgetContainer>(info.m_Context.toStdString().c_str());

        if (w != NULL)
        {
            w->setDomain(info.m_Domain);
            addOneItem(info, w);
        }
    }
}

void MainWindowKBUnit::initializeUI()
{
    setupContext();
}

void MainWindowKBUnit::setupContext()
{
    QMap<QString, WidgetContainer*>::const_iterator iter = m_ContextWidgets.begin();
    for (; iter != m_ContextWidgets.end(); iter++)
    {
        ContextSetupHelperBase* helper = m_HelperMananager.setupHelper(iter.key());
        if (helper != NULL)
        {
            helper->setup(iter.value());
        }
    }

    m_ViewerContext->setMainWindow(this);

    // add xule
    m_ViewerContext->setModeChangeWidget((QWidget*)getModeChangeWidget());

    m_ViewerContext->setMiddleDownWidgetContainer(findWidget("MiddleDownWidgetContainer"));
    // 设置之后清除所以的辅助对象
    m_HelperMananager.deleteAllHelper();
}

void MainWindowKBUnit::addOneItem(const GridItemInfo& info, WidgetContainer* w)
{
    if (NULL == w)
    {
        qWarning() << "add WidgetContainer failed, WidgetName is " << info.m_Context;
        return;
    }

    // 转换布局信息到属性,转换方法有待改进
    if (info.m_Pos.size() == POSITION_SIZE)
    {
        // 加载位置信息
        int r = info.m_Pos[0].toInt(), c = info.m_Pos[1].toInt(), rSpan = info.m_Pos[2].toInt(),
            cSpan = info.m_Pos[3].toInt();

        qDebug() << "DA--DAD" << w->metaObject()->className() << r << c << rSpan << cSpan;
        ui->gridLayout->addWidget(w, r, c, rSpan, cSpan);
        m_ContextWidgets[info.m_Context] = w;

        // 加载比例属性
        if (info.m_Stretch.size() == STRETCH_SIZE)
        {
            ui->gridLayout->setRowStretch(r, info.m_Stretch.first().toInt());
            ui->gridLayout->setColumnStretch(c, info.m_Stretch.last().toInt());
        }
        // 加载各自布局的内置属性
        w->setCustomProperty(info.m_Property);
    }
}

WidgetContainer* MainWindowKBUnit::findWidget(const QString& widgetName) const
{
    if (m_ContextWidgets.contains(widgetName))
    {
        return m_ContextWidgets[widgetName];
    }

    return NULL;
}

void MainWindowKBUnit::initFlexButtonsProperty(FlexButton* btn)
{
    const DomainPropertyInfos infos = UIPropertyConfig::instance().allPropertyInfos().value("MainWindow");
    foreach (WidgetPropertyInfos propertyInfos, infos)
    {
        if (propertyInfos.m_ClassName == "FlexButton")
        {
            if (propertyInfos.m_OwnerName == btn->objectName())
            {
                foreach (PropertyInfo property, propertyInfos.m_CustomPropretys)
                {
                    if (property.m_PropertyName == "supportmove")
                    {
                        btn->setSupportMove(property.m_Property.toBool());
                    }
                    else if (property.m_PropertyName == "normalpos")
                    {
                        btn->setProperty("normalpos", property.m_Property.toPoint());
                    }
                    else if (property.m_PropertyName == "primaryamplificationpos")
                    {
                        btn->setProperty("primaryamplificationpos", property.m_Property.toPoint());
                    }
                    else if (property.m_PropertyName == "secondaryamplificationpos")
                    {
                        btn->setProperty("secondaryamplificationpos", property.m_Property.toPoint());
                    }
                }
                return;
            }
        }
    }
}

QPoint MainWindowKBUnit::flexButtonPos(FlexButton* btn) const
{
    QPoint flexBtnTargetPos = btn->property("normalpos").toPoint();

    if (btn->property("zoomState").toString() == "primary")
    {
        flexBtnTargetPos = btn->property("primaryamplificationpos").toPoint();
    }
    else if (btn->property("zoomState").toString() == "secondary")
    {
        flexBtnTargetPos = btn->property("secondaryamplificationpos").toPoint();
    }

    return flexBtnTargetPos;
}

void MainWindowKBUnit::initialSkTipIcon()
{
    m_SkTipIcon->setPixmap(QPixmap(Resource::skTipIconFilename()));
    m_SkTipIcon->setVisible(false);
    m_SkTipIcon->raise();
    m_SkTipIcon->activateWindow();
    m_SkTipIcon->setFixedSize(QPixmap(Resource::skTipIconFilename()).size());
    m_SkTipIcon->setAttribute(Qt::WA_TransparentForMouseEvents, true);
}

void MainWindowKBUnit::initializeGainTipIcon()
{
    m_GainTipIcon->setObjectName("GainTipIcon");
    m_GainTipIcon->move(ModelConfig::instance().value(ModelConfig::GainTipIconPos, rect().center()).toPoint());
    m_GainTipIcon->setPixmap(QPixmap(Resource::gainTipIconFilename()));
    m_GainTipIcon->setVisible(false);
    m_GainTipIcon->raise();
    m_GainTipIcon->activateWindow();
    m_GainTipIcon->setFixedSize(QPixmap(Resource::gainTipIconFilename()).size());
    m_GainTipIcon->setAttribute(Qt::WA_TransparentForMouseEvents, true);
}

void MainWindowKBUnit::initializeLockScreenTipLabel()
{
    m_LockScreenTip->move(ModelConfig::instance().value(ModelConfig::LockScreenTipPos, rect().center()).toPoint());
    m_LockScreenTip->setObjectName("lockScrrenTipLabel");
    m_LockScreenTip->setText(tr("The full screen area is locked"));
    m_LockScreenTip->setVisible(false);
    m_LockScreenTip->raise();
    m_LockScreenTip->activateWindow();
    m_LockScreenTip->setFixedWidth(this->width());
    m_LockScreenTip->setAlignment(Qt::AlignCenter);
}

void MainWindowKBUnit::initializeSKVisibleTimer()
{
    m_SKVisibleTimer->setSingleShot(true);
    m_SKVisibleTimer->setInterval(ModelConfig::instance().value(ModelConfig::SKTipIconShowDelayTime, 2000).toInt());
    connect(m_SKVisibleTimer, &QTimer::timeout, this, [=]() { m_SkTipIcon->setVisible(false); });
}

QLabel* MainWindowKBUnit::gainLogoLabel()
{
    return m_GainTipIcon;
}

QLabel* MainWindowKBUnit::getLockscreenTipLabel() const
{
    return m_LockScreenTip;
}

QWidget* MainWindowKBUnit::popUpWidget() const
{
    return m_PopUpWidget;
}

FlexButton* MainWindowKBUnit::getLockScreenFlexBtn() const
{
    return m_LockScreenFlexBtn;
}

void MainWindowKBUnit::setFullScreenFlexBtnVisible(bool isVisible)
{
    if (m_FullScreenFlexBtn != nullptr)
    {
        QPoint fullScreenFlexBtnTargetPos = flexButtonPos(m_FullScreenFlexBtn);

        isVisible = isVisible && fullScreenFlexBtnTargetPos != QPoint(0, 0);
        m_FullScreenFlexBtn->setVisible(isVisible);
    }
}

void MainWindowKBUnit::setLockScreenFlexBtnVisible(bool isVisible)
{
    if (m_LockScreenFlexBtn != nullptr)
    {
        QPoint lockScreenFlexBtnTargetPos = flexButtonPos(m_LockScreenFlexBtn);

        isVisible = isVisible && lockScreenFlexBtnTargetPos != QPoint(0, 0);
        m_LockScreenFlexBtn->setVisible(isVisible);
    }
}

void MainWindowKBUnit::controlSkLogo(bool visible, int index, const bool isBySK)
{
    if (ModelUiConfig::instance().value(ModelUiConfig::SupportTouchScreen).toBool())
    {
        m_SkVisible = visible;

        if (visible)
        {
            QPoint pos = ModelConfig::instance().value(ModelConfig::SkTipIconPos, rect().center()).toPoint();
            m_SkTipIcon->move(QPoint(
                pos.x() + index * ModelConfig::instance().value(ModelConfig::SKTipIconMargin, 0).toInt(), pos.y()));
            m_SkTipIcon->setVisible(true);

            m_SKVisibleTimer->stop();
            if (isBySK)
            {
                m_SKVisibleTimer->start();
            }
        }
        else
        {
            QTimer::singleShot(ModelConfig::instance().value(ModelConfig::SKTipIconShowDelayTime, 2000).toInt(), this,
                               [=]() { m_SkTipIcon->setVisible(m_SkVisible); });
        }
    }
}

FlexButton* MainWindowKBUnit::getFullScreenFlexBtn() const
{
    return m_FullScreenFlexBtn;
}

void MainWindowKBUnit::initGlobalFlexButtons()
{
    m_FullScreenFlexBtn->setObjectName("fullScreenBtn");
    Util::changeQssWidgetProperty(m_FullScreenFlexBtn, "state", "off");

    m_LockScreenFlexBtn->setObjectName("lockScreenBtn");
    Util::changeQssWidgetProperty(m_LockScreenFlexBtn, "state", "off");

    initFlexButtonsProperty(m_FullScreenFlexBtn);
    initFlexButtonsProperty(m_LockScreenFlexBtn);

    updateNormalGlobalFlexButtons();
}

void MainWindowKBUnit::updateNormalGlobalFlexButtons()
{
    m_FullScreenFlexBtn->setProperty("zoomState", "normal");
    m_LockScreenFlexBtn->setProperty("zoomState", "normal");

    m_FullScreenFlexBtn->move(flexButtonPos(m_FullScreenFlexBtn));
    Util::changeQssWidgetProperty(m_FullScreenFlexBtn, "state", "off");

    m_LockScreenFlexBtn->move(flexButtonPos(m_LockScreenFlexBtn));

    setFullScreenFlexBtnVisible(true);
    setLockScreenFlexBtnVisible(true);
}

void MainWindowKBUnit::updatePrimaryGlobalFlexButtons()
{
    m_FullScreenFlexBtn->setProperty("zoomState", "primary");
    m_LockScreenFlexBtn->setProperty("zoomState", "primary");

    m_FullScreenFlexBtn->move(flexButtonPos(m_FullScreenFlexBtn));
    Util::changeQssWidgetProperty(m_FullScreenFlexBtn, "state", "on");

    m_LockScreenFlexBtn->move(flexButtonPos(m_LockScreenFlexBtn));

    setFullScreenFlexBtnVisible(true);
    setLockScreenFlexBtnVisible(true);
}

void MainWindowKBUnit::updateSecondaryGlobalFlexButtons()
{
    m_FullScreenFlexBtn->setProperty("zoomState", "secondary");
    m_LockScreenFlexBtn->setProperty("zoomState", "secondary");

    m_FullScreenFlexBtn->move(flexButtonPos(m_FullScreenFlexBtn));
    Util::changeQssWidgetProperty(m_FullScreenFlexBtn, "state", "on");

    m_LockScreenFlexBtn->move(flexButtonPos(m_LockScreenFlexBtn));

    setFullScreenFlexBtnVisible(true);
    setLockScreenFlexBtnVisible(true);
}

void MainWindowKBUnit::showEvent(QShowEvent* e)
{
    IMainWindow::showEvent(e);
    emit posChanged();
}

void MainWindowKBUnit::moveEvent(QMoveEvent* e)
{
    IMainWindow::moveEvent(e);
    emit posChanged();
}

void MainWindowKBUnit::retranslateUi()
{
    m_LockScreenTip->setText(tr("The full screen area is locked"));
    return MainWindowKBBase::retranslateUi();
}
