#ifndef IULTRASOUNDVIEW_H
#define IULTRASOUNDVIEW_H

#include "usfinterfaceframewnd_global.h"
#include "funcstatedescriptiondef.h"
#include "imageeventargs.h"
#include "usfobject.h"
#include "baseaspect.h"
#include <QImage>
#include <QList>
#include <QRect>

class IPeripheralManager;
class IImageManager;
class IExamManager;
class IMarkManager;
class SonoParameters;
class Overlay;
class ImageTile;
class IMainWindow;
class IExamModeMenuModel;
class BFHWInfoModel;
class StressEchoWidgetManager;
class QuickMeasureCaptionsLoop;
class MainWindowTopInfoModel;
class CursorMouseActionsModel;
class ProbeSelectionDispatcher;
class StressEchoWidgetManager;
class SonoParameters;
class DopplerViewerContext;
class IToolsFacade;
class IStateManager;
class IDiskDevice;
class IColorMapManager;
class ISystemInfo;
class ILicenseInitializer;
class StoredData;
class IAdminConfigModel;
class IPatientWorkflowModel;
class TGCMenuWidgetContainer;

class USF_INTERFACE_FRAMEWND_EXPORT IUltrasoundView : public QObject, public USFObject, public BaseAspect
{
public:
    IUltrasoundView(QObject* parent = 0);
    virtual ~IUltrasoundView();

public:
    virtual void init() = 0;
    virtual void release() = 0;
    virtual void setPeripheralManager(IPeripheralManager* value) = 0;
    virtual void setImageManager(IImageManager* value) = 0;
    virtual void setExamManager(IExamManager* value) = 0;
    virtual void setMarkManager(IMarkManager* value) = 0;
    virtual void setToolsFacade(IToolsFacade* value) = 0;
    virtual void setStateManagerFacade(IStateManager* value) = 0;
    virtual IStateManager* stateManagerFacade() const = 0;
    virtual void setDiskDevice(IDiskDevice* diskDevice) = 0;
    virtual void setSystemInfo(ISystemInfo* systemInfo) = 0;
    virtual void setColorMapManager(IColorMapManager* colorMapManager) = 0;
    virtual void setLicenseInitializer(ILicenseInitializer* licenseInitializer) = 0;
    virtual ILicenseInitializer* licenseInitializer() const = 0;
    virtual DopplerViewerContext* viewContext() const = 0;
    virtual IMainWindow* mainWindow() const = 0;
    virtual bool realTimeZoomIn() = 0;
    virtual void pauseUIRefresh(bool pause) = 0;
    /**
     * @brief switchUI: UI切换接口
     * input：scanmode_name
     * input：menu_name
     * input：state_name
     * input：switch_on，键灯等对象的控制是否打开参数，有些场景下调用switchUI时，需要单独控制键灯或旋钮是否可以打开或关闭
     *
     */
    virtual void switchUI(int scanmode_name, const QString& menu_name, const QString& state_name, bool swicth_on = true,
                          bool needRefreshHighLight = true) = 0;
    /**
     * @brief switchUI: UI切换接口,重载接口;
     * 切换layout布局时，UI的切换有别于常规方式，该接口主要供BaseLayoutState及其子类调用 input：scanmode_name
     * input：menu_name
     *
     */
    virtual void switchUI(int scanmode_name, const QString& menu_name, bool needRefreshHighLight) = 0;
    virtual bool enterState(FUNC_STATE state, const FuncStateInput& input = FuncStateInput()) = 0;
    virtual bool exitState(FUNC_STATE state, const FuncStateInput& input = FuncStateInput()) = 0;
    virtual void controlLight(const QString& keyName, bool state) = 0;
    virtual Overlay* imageTileOverlay() const = 0;
    virtual void removeImageTileOverlayAllItems() = 0;
    virtual void setLeftMenuWidgetVisible(bool show) = 0;
    virtual void setMeasurementMenutFetus(int value) = 0;
    virtual void setMeasurementMenuIsToolEnter(bool value) = 0;
    virtual void setBottomMenuWidgetVisible(bool show) = 0;
    virtual void setstressEchoWorkWidgetVisible(bool show) = 0;
    virtual QImage currentImage(ImageEventArgs::ImageType type = ImageEventArgs::WholeImage) const = 0;
    virtual bool isThumbnailDialogVisible() const = 0;
    virtual void setMeasureResultListWidgetOnLoadDisplayControl(bool value) = 0;
    virtual void setMeasureResultListWidgetVisible(bool show) = 0;
    virtual void adjustMeasureResultListWidgetSize() = 0;
    virtual void closeSystemStatusInfoWindows() = 0;
    virtual void setSonoParametersForSonoParasWidget(SonoParameters* sonoparameters) = 0;
    virtual void setSonoParametersForDopplerAdjustItem(SonoParameters* sonoparameters) = 0;
    virtual void setSonoParametersForMainWindowTopWidget(SonoParameters* sonoparameters) = 0;
    virtual void updateGDPRInfoForMainWindowTopWidget(int offsetX = 0) = 0;
    virtual ImageTile* curImageTile() const = 0;
    virtual bool isZoomIn() const = 0;
    virtual const QList<QRect> getGDPRFullScreenCurrentRect() const = 0;
    virtual const QList<QRect> getGDPRImageAndInfoCurrentRect() const = 0;
    virtual const QRect getGDPROBRect() const = 0;
    virtual int getMainWindowTopWidgetGDPRWidth() const = 0;
    virtual void updateImageClipWidget(const QString& patient_path) = 0;
    virtual void clearImageClipWidget() = 0;
    virtual void updateImageClipWidgetImageLable(int index) = 0;
    virtual void showSonoZoomWidgetGlass(bool show, bool isOtherFun = false) = 0;
    virtual QRect imageZoomOnWidgetRect() = 0;
    virtual BFHWInfoModel* bFHWInfoModel() const = 0;
    virtual QList<QuickMeasureCaptionsLoop*> quickMeasureCaptionsLoops() const = 0;
    virtual MainWindowTopInfoModel* mainWindowTopInfoModel() const = 0;
    virtual CursorMouseActionsModel* cursorMouseActionsModel() const = 0;
    virtual ProbeSelectionDispatcher* probeSelectionDispatcher() const = 0;
    virtual IExamModeMenuModel* examModeMenuModel() const = 0;
    virtual StressEchoWidgetManager* stressEchoWidgetManager() const = 0;
    virtual IAdminConfigModel* adminConfigModel() const = 0;
    virtual IPatientWorkflowModel* patientWorkflowModel() const = 0;
    virtual TGCMenuWidgetContainer* widgetOfTGCAdjustment() const = 0;
    virtual void showLoginDlg() = 0;
    virtual void showSNInputWidget() = 0;
    virtual void checkRemainDays() = 0;
    virtual void setupSystemStatusAction() = 0;
    virtual QStringList postParameterNames() const = 0;
    virtual void updateToolsSonoParameters(SonoParameters* sonoParameters) = 0;
    virtual void checkImageRender() = 0;
    virtual void controlGainLogo() = 0;
    virtual void showMainWindowBegin() = 0;
    virtual void showMainWindow() = 0;
    virtual void showMainWindowEnd() = 0;
    virtual void runSystemUserImpl() = 0;
    virtual void bufferStoreOnLoaded() = 0;
    virtual void bufferStoreLoadStoredData(const StoredData& storedData) = 0;
    virtual void bufferStoreSaveGDPRInfo(const QString& fileName) = 0;

    virtual void createSplashWindow() = 0;
    virtual void updateSplashProgress(const QString& status) = 0;
    virtual void enableUpdateMeasureContextImage(bool) = 0;
    virtual void updateMeasureContextImage() = 0;

    virtual bool isInSecondDegree() = 0;

    virtual QWidget* popUpWidget() const = 0;
};

#endif
