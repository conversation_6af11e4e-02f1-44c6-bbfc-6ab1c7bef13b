#include "keyboardsettings.h"
#include "ui_keyboardsettings.h"
#include "hotkey/hotkeyconfig.h"
#include "hotkey/hotkeycontainer.h"
#include "hotkey/hotkeyfunction.h"
#include "util.h"
#include "appsetting.h"
#include <QStyledItemDelegate>
#include "modeluiconfig.h"
#include "applicationinfo.h"
#include <QGuiApplication>
#include <QScreen>
#include "uiutil.h"

KeyboardSettings::KeyboardSettings(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::KeyboardSettings)
    , m_Config(NULL)
    , m_Container(NULL)
    , m_Model(ScreenOrientationModel::getInstance())
{
    ui->setupUi(this);

    ui->tableWidget->setHorizontalHeaderLabels(QStringList() << tr("Key") << tr("Function"));
    ui->tableWidget->setItemDelegate(new QStyledItemDelegate(this));
    ui->tableWidget->verticalHeader()->setDefaultSectionSize(
        ModelUiConfig::instance().value(ModelUiConfig::TableViewItemHeight).toInt());

    connect(ui->hotKeyTabWidget, SIGNAL(hotKeySelected(QString)), this, SLOT(onHotKeySelected(QString)));
    connect(AppSetting::eventObject(), SIGNAL(functionChanged(int, bool)), this,
            SLOT(onFunctionStatusChanged(int, bool)));
}

KeyboardSettings::~KeyboardSettings()
{
    delete ui;
}

void KeyboardSettings::setModels(HotKeyConfig* config, HotKeyContainer* container)
{
    m_Config = config;
    m_Container = container;
    ui->hotKeyTabWidget->setModel(container);

    foreach (const QString& entityKey, m_Config->avaliableEntityKeys())
    {
        int row = ui->tableWidget->rowCount();
        ui->tableWidget->setRowCount(row + 1);
        ui->tableWidget->setItem(
            row, 0, new QTableWidgetItem(Util::translate("HotKey", m_Container->entityKeyCaption(entityKey))));
        HotKeyFunction* key = m_Container->hotKeyFunction(m_Config->function(entityKey));
        ui->tableWidget->setItem(
            row, 1, new QTableWidgetItem(key != NULL ? Util::translate("HotKey", key->caption()) : tr("None")));
    }
}

QString KeyboardSettings::title() const
{
    return this->windowTitle();
}

void KeyboardSettings::reset()
{
    m_Config->load();
    updateConfigTableWidget();
}

void KeyboardSettings::save()
{
    m_Config->save();
    updateConfigTableWidget();
}

void KeyboardSettings::cancel()
{
    m_Config->load();
    updateConfigTableWidget();
}

void KeyboardSettings::setDefault()
{
    m_Config->loadDefault();
    updateConfigTableWidget();
}

void KeyboardSettings::onFunctionStatusChanged(int key, bool isOpen)
{
    Q_UNUSED(key);
    Q_UNUSED(isOpen);
    m_Container->updateHotKeyFunction();
    ui->hotKeyTabWidget->updateView();
    updateConfigTableWidget();
}

void KeyboardSettings::retranslateUi()
{
    ui->retranslateUi(this);

    ui->tableWidget->setHorizontalHeaderLabels(QStringList() << tr("Key") << tr("Function"));
    updateConfigTableWidget();
}

void KeyboardSettings::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
    UiUtil::ScreenHVChangedUpdateUI(this, ui->gridLayout, config);
}

void KeyboardSettings::updateConfigTableWidget()
{
    ApplicationInfo::instance().setPrintKeyFuncStatus(true);
    const QStringList& entityKeys = m_Config->avaliableEntityKeys();
    for (int i = 0; i < ui->tableWidget->rowCount(); i++)
    {
        const QString& entityKey = entityKeys.at(i);
        ui->tableWidget->item(i, 0)->setText(Util::translate("HotKey", m_Container->entityKeyCaption(entityKey)));

        HotKeyFunction* key = m_Container->hotKeyFunction(m_Config->function(entityKey));
        ui->tableWidget->item(i, 1)->setText(key != NULL ? Util::translate("HotKey", key->caption()) : tr("None"));
    }
}

void KeyboardSettings::onHotKeySelected(const QString& hotKey)
{
    QList<QTableWidgetItem*> selectedItems = ui->tableWidget->selectedItems();
    if (!selectedItems.isEmpty())
    {
        int row = selectedItems.first()->row();
        const QString& entityKey = m_Config->avaliableEntityKeys().at(row);

        m_Config->bindFunction(entityKey, hotKey);
        HotKeyFunction* key = m_Container->hotKeyFunction(hotKey);
        ui->tableWidget->item(row, 1)->setText(key != NULL ? Util::translate("HotKey", key->caption()) : tr("None"));
    }
    m_Config->notifyHotKeyChanged();
}

void KeyboardSettings::on_tableWidget_itemClicked(QTableWidgetItem* item)
{
    ui->hotKeyTabWidget->selectHotKeyFunction(m_Config->function(m_Config->avaliableEntityKeys().at(item->row())));
}
