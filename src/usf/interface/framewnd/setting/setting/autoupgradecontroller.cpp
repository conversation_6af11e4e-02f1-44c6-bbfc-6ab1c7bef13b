#include "autoupgradecontroller.h"
#include "autoupgrademodel.h"
#include "upgrademodelitem.h"
#include "upgradeviewinfoitem.h"
#include "updatesetting.h"
#include "upgradeenum.h"
#include "fpgaupgradecontrol.h"
#include "fpgaupdateresdef.h"
#include "logger.h"
#include "fpgaupgrademodel_palm.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, AutoUpgradeController)
AutoUpgradeController::AutoUpgradeController(QObject* parent)
    : QObject(parent)
    , m_Model(nullptr)
    , m_HwUpdateMode(0)
    , m_Updating(false)
{
#ifdef USE_TARGET_PALM
    connect(
        &FPGAUpgradecontrol::instance(), &FPGAUpgradecontrol::probeStateChanged, this,
        [this](int socket) { onModelActiveChanged(Hardware, QString::number(socket)); }, Qt::QueuedConnection);
#endif

    connect(
        &FPGAUpgradecontrol::instance(), &FPGAUpgradecontrol::progressBarValueChanged, this,
        [this](int value) { onUpgradeStatusProgressChanged(Hardware, value); }, Qt::QueuedConnection);

    connect(
        &FPGAUpgradecontrol::instance(), &FPGAUpgradecontrol::upgradeFinalState, this,
        [this](int value, int socket) {
            if (-1 == socket)
            {
                onUpgradeFinalState(Hardware, value);
            }
            else
            {
                onUpgradeFinalState(m_Model->getViewInfoItem(Hardware, QString::number(socket)), value);
            }
        },
        Qt::QueuedConnection);

    connect(
        &FPGAUpgradecontrol::instance(), &FPGAUpgradecontrol::autoUpgradeFinalState, this,
        [this](int value, int socket) {
            onUpgradeFinalState(m_Model->getViewInfoItem(Hardware, QString::number(socket)), value);
        },
        Qt::QueuedConnection);
}

void AutoUpgradeController::setModel(AutoUpgradeModel* model)
{
    if (model == nullptr)
    {
        return;
    }

    m_Model = model;

    foreach (auto type, m_Model->getUpgradeTypes())
    {
        QList<UpgradeModelItem*> modelItems = m_Model->getModelInfoItems(type);
        foreach (auto modelItem, modelItems)
        {
            auto viewItem = m_Model->modelToViewItem(modelItem);
            viewItem->setStatus(getViewState(modelItem->needupdate(), modelItem->active(), viewItem->status()));
            m_Model->addViewInfoItem(type, viewItem);
        }
    }
}

bool AutoUpgradeController::checkNeedUpdated(UpgradeModelItem* item)
{
    if (item == nullptr)
    {
        return false;
    }

    bool ret = false;
    QString fpgaVer;
    // type == 0 非掌超探头 ，type ！= 0 为掌超探头
    if (UpdateSetting::instance().getFPGAVersion(item->field2().toInt()) == 0)
    {
        fpgaVer = UpdateSetting::instance().getFPGAVersion(item->field1().toString());
    }
    else
    {
        // 掌超用type 获取配置的FPGA版本号
        fpgaVer = UpdateSetting::instance().getFPGAVersion(item->field2().toInt());
    }

    if (!fpgaVer.isEmpty() && !item->version().isEmpty() && item->version().compare(fpgaVer))
    {
        item->setNeedupdate(true);
        item->setFilePath(UpdateSetting::instance().getFPGAPath(item->field2().toInt()));
        ret = true;
    }
    else
    {
        item->setNeedupdate(false);
    }

    return ret;
}

/**
 * @brief AutoUpgradeController::changeViewItemByModelItem 根据数据信息修改界面信息
 * @param item
 */
void AutoUpgradeController::changeViewItemByModelItem(UpgradeModelItem* item)
{
    if (item == nullptr)
    {
        return;
    }

    UpgradeViewInfoItem* viewInfoItem = m_Model->getViewInfoItem(item->type(), item->key());
    if (viewInfoItem != nullptr && item != nullptr)
    {
        // 更新中不响应数据插入信息变更
        if (!m_Updating)
        {
            viewInfoItem->setTitle(item->title());
        }

        // 根据当前数据状态, 判断下一个数据是否需要更新
        // 正在升级的数据变更，需要开始下一个数据更新
        isUpgradingAndUpgradeNext(viewInfoItem);

        viewInfoItem->setStatus(getViewState(item->needupdate(), item->active(), viewInfoItem->status()));
    }
}

int AutoUpgradeController::getViewState(bool needUpdate, bool active, int oldState)
{
    switch (oldState)
    {
    case NoNeedUpgrade:
    case NeedUpgrade:
        return !m_Updating && needUpdate && active ? NeedUpgrade : NoNeedUpgrade;
    case Waiting:
    case Upgrading:
        return Failed;
    default:
        return oldState;
    }
}

bool AutoUpgradeController::isItemUpgrading(UpgradeViewInfoItem* item)
{
    return item->status() == Upgrading;
}

bool AutoUpgradeController::checkNeedUpdated()
{
    bool ret = false;

    foreach (auto type, m_Model->getUpgradeTypes())
    {
        foreach (auto item, m_Model->getModelInfoItems(type))
        {
            ret = checkNeedUpdated(item) ? true : ret;
            changeViewItemByModelItem(item);
        }
    }

    return ret;
}

bool AutoUpgradeController::checkNeedUpdated(int type, const QString& key)
{
    auto item = m_Model->getModelInfoItem(type, key);
    bool ret = checkNeedUpdated(item) ? true : false;
    changeViewItemByModelItem(item);

    return ret;
}

int AutoUpgradeController::hwUpdateMode() const
{
    return m_HwUpdateMode;
}

void AutoUpgradeController::setHwUpdateMode(int hwUpdateMode)
{
    m_HwUpdateMode = hwUpdateMode;
}

void AutoUpgradeController::onStartUpdated(const QVector<int>& types)
{
    log()->info() << PRETTY_FUNCTION;
    foreach (auto type, types)
    {
        upgradeType(type);
    }
}

void AutoUpgradeController::setUpdating(bool value)
{
    if (value) // 开始升级时
    {
        emit screenControlTimersStoped();
    }
    else if (m_Updating) // 升级结束
    {
        emit screenControlTimersStarted();
    }

    m_Updating = value;
}

void AutoUpgradeController::resetModel()
{
    foreach (auto type, m_Model->getUpgradeTypes())
    {
        QList<UpgradeModelItem*> modelItems = m_Model->getModelInfoItems(type);
        foreach (auto modelItem, modelItems)
        {
            auto viewItem = m_Model->getViewInfoItem(type, modelItem->key());
            m_Model->setViewItem(modelItem, viewItem);
            viewItem->setStatus(getViewState(modelItem->needupdate(), modelItem->active(), viewItem->status()));
        }
    }
}

bool AutoUpgradeController::upgradeItem(UpgradeViewInfoItem* item)
{
    log()->info() << PRETTY_FUNCTION;
    if (item != nullptr && item->status() == Waiting)
    {
        log()->info() << PRETTY_FUNCTION << item->key();
        item->setStatus(Upgrading);
        switch (item->type())
        {
        case Hardware:
            FPGAUpgradecontrol::instance().autoUpgrade(item->key(), FPGAUpgradecontrol::Palm);
        }
        return true;
    }

    return false;
}

/**
 * @brief AutoUpgradeController::upgradeNext 更新模块中下一个数据
 * @param type
 */
void AutoUpgradeController::upgradeNext(int type)
{
    log()->info() << PRETTY_FUNCTION << "type:" << type;
    // fpgaupgrademodel_palm 升级结束会有个1秒的定时器设置devicebusy ,所以间隔两秒跳过这个间隔
    QTimer::singleShot(2000, this, [this, type]() {
        foreach (auto item, m_Model->getViewInfoItems(type))
        {
            if (upgradeItem(item))
            {
                break;
            }
        }
    });
}

bool AutoUpgradeController::isUpgradingAndUpgradeNext(UpgradeViewInfoItem* item)
{
    if (isItemUpgrading(item))
    {
        upgradeNext(item->type());
        return true;
    }
    return false;
}

/**
 * @brief AutoUpgradeController::upgradeType 更新一个模块
 * @param type
 */
void AutoUpgradeController::upgradeType(int type)
{
    auto viewItmes = m_Model->getViewInfoItems(type);
    foreach (auto viewItem, viewItmes)
    {
        if (viewItem != nullptr && viewItem->status() == NeedUpgrade)
        {
            viewItem->setStatus(Waiting);
        }
    }

    switch (type)
    {
    case Hardware:
#ifdef USE_TARGET_PALM
        upgradeNext(type);
#else
        auto viewItmes = m_Model->getViewInfoItems(type);
        foreach (auto viewItem, viewItmes)
        {
            if (viewItem != nullptr && viewItem->status() == Waiting)
            {
                viewItem->setStatus(Upgrading);
            }
        }
        QString filePath = UpdateSetting::instance().getFPGAPath(type);
        FPGAUpgradecontrol::instance().Upgrade(filePath, FPGAUpgradecontrol::Normal);
#endif
    }
}

void AutoUpgradeController::onUpgradeStatusChanged(int type, QString key, int newStatus)
{
    UpgradeViewInfoItem* viewInfoItem = m_Model->getViewInfoItem(type, key);
    onUpgradeStatusChanged(viewInfoItem, newStatus);
}

void AutoUpgradeController::onUpgradeStatusChanged(UpgradeViewInfoItem* item, int newStatus)
{
    if (item != nullptr && (item->status() == Upgrading || item->status() == Waiting))
    {
        item->setStatus(newStatus);
    }
}

void AutoUpgradeController::onModelActiveChanged(int type, QString key)
{
    // 硬件状态变更时，更新界面和判断是否开始升级下一个
    UpgradeModelItem* modelItem = m_Model->getModelInfoItem(type, key);
    if (nullptr == modelItem)
    {
        qCritical() << PRETTY_FUNCTION << "get model item null with type: " << type << ", key: " << key;
        return;
    }
    checkNeedUpdated(modelItem);
    changeViewItemByModelItem(modelItem);
}

void AutoUpgradeController::onUpgradeStatusProgressChanged(int type, QString key, int value)
{
    UpgradeViewInfoItem* viewInfoItem = m_Model->getViewInfoItem(type, key);
    if (viewInfoItem != nullptr && viewInfoItem->status() == Upgrading)
    {
        viewInfoItem->setProgress(value);
    }
}

void AutoUpgradeController::onUpgradeStatusProgressChanged(int type, int value)
{
    auto viewInfoList = m_Model->getViewInfoItems(type);
    foreach (auto item, viewInfoList)
    {
        onUpgradeStatusProgressChanged(type, item->key(), value);
    }
}

void AutoUpgradeController::onUpgradeFinalState(UpgradeViewInfoItem* item, int value)
{
    if (item == nullptr)
        return;

    int state = value == FPGAUpdateResDef::Update_Success ? Succeed : Failed;

    log()->info() << PRETTY_FUNCTION << item->key() << " update state " << state << " old state " << value;

    // 更新界面和判断是否开始升级下一个
    if (isUpgradingAndUpgradeNext(item))
    {
        onUpgradeStatusChanged(item, state);
    }
}

void AutoUpgradeController::onUpgradeFinalState(int type, int value)
{
    int state = value == FPGAUpdateResDef::Update_Success ? Succeed : Failed;
    auto viewInfoList = m_Model->getViewInfoItems(type);
    foreach (auto item, viewInfoList)
    {
        onUpgradeStatusChanged(item, state);
    }
}
