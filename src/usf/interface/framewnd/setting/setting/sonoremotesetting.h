#ifndef SONOREMOTESETTING_H
#define SONOREMOTESETTING_H

#include "usfinterfaceframewndsetting_global.h"
#include "basewidget.h"
#include "isettings.h"
#include <QCoreApplication>

namespace Ui
{
class SonoRemoteSetting;
}

class USF_INTERFACE_FRAMEWND_SETTING_EXPORT SonoRemoteSetting : public BaseWidget, public ISettings
{
    Q_OBJECT

public:
    explicit SonoRemoteSetting(QWidget* parent = nullptr);
    ~SonoRemoteSetting();
    void createWidget();
    virtual QString title() const;
    virtual void reset();
    virtual void save();
    virtual void cancel();
    virtual void setDefault();

    static bool createFile();
    static void feedBackOfSatisfaction();

protected:
    void setContentOfQRCode(const char* text);
    void setHotLineContent(QString text);
    void switchFeedBackOnOff(bool value);
    void updatePage();
    bool currentOnOff();
    void initBeforeCreateFile();

protected:
    void retranslateUi();

    /** 2025-06-13 Wrie by AlexWang
     * @brief showEvent
     * @param event
     */
    void showEvent(QShowEvent* event);

    /** 2025-06-13 Wrie by AlexWang
     * @brief updateQRCodeLabel
     */
    void updateQRCodeLabel();

    /** 2025-06-13 Wrie by AlexWang
     * @brief updateHotlineLabel
     */
    void updateHotlineLabel();

private slots:
    void on_pushButton_clicked();

private:
    Ui::SonoRemoteSetting* ui;
    QString m_ContentForCode;
    QString m_HotLineText;
    QString m_SoftWareVersion;
    QString m_SerialNumber;
    QString m_EquipmentModel;
    QString m_FileName;
    QImage m_CodeImage;
    bool m_IsFeedBackStart;
};

#endif // SONOREMOTESETTING_H
