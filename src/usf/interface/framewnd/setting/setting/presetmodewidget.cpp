#include "presetmodewidget.h"
#include "appsetting.h"
#include "bfpnames.h"
#include "iexammodemenumodel.h"
#include "iimagesettings.h"
#include "messageboxframe.h"
#include "modelconfig.h"
#include "parameter.h"
#include "presetmodeitemswidget.h"
#include "presetmodeparametersgroup.h"
#include "setting/setting.h"
#include "smoothsettings.h"
#include "sonoparameters.h"
#include "ui_presetmodewidget.h"
#include <QApplication>
#include <QDesktopWidget>
#include <QStringList>
#include "ibeamformertool.h"

#ifdef USE_4D
#include "fourdparasettingwidget.h"
#endif
#include "debuggervieweventnamedef.h"
#include "iprobedataset.h"
#include "presetmodeshortcutatom.h"
#include "presetmodeshortcutbase.h"
#include "probecodeburnersettingwidget.h"
#include "qscreen.h"
#include "qsettings.h"
#include "resource.h"
#include "scrollwidget.h"
#include "virtualprobesettingwidget.h"
#include "presetuitools.h"
#include <QScroller>

PresetModeDialog::PresetModeDialog(QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
    , m_Child(NULL)
    , m_PresetModeShortcutBase(nullptr)
{
    m_Child = new PresetModeWidget(this);
    setIsShowOk(false);
    this->setContent(m_Child);
    connect(m_Child, SIGNAL(editFocusIn(QWidget*)), m_ScrollWidget, SLOT(onEditFocusIn(QWidget*)));
    connect(m_Child, SIGNAL(updateProbeSelectionUI()), this, SIGNAL(updateProbeSelectionUI()));
    setFixedHeight(QGuiApplication::primaryScreen()->geometry().height());
    buildPresetModeShortcut();
}

PresetModeDialog::~PresetModeDialog()
{
    Util::SafeDeletePtr(m_PresetModeShortcutBase);
}

void PresetModeDialog::setSonoParameters(SonoParameters* value)
{
    m_Child->setSonoParameters(value);
}

void PresetModeDialog::setBeamFormerTool(IBeamFormerTool* value)
{
    m_Child->setBeamFormerTool(value);
}

void PresetModeDialog::setIZeusTool(IZeusTool* value)
{
    m_Child->setIZeusTool(value);
}

void PresetModeDialog::setExamModeMenuModel(IExamModeMenuModel* value)
{
    m_Child->setExamModeMenuModel(value);
}

void PresetModeDialog::setSubSystemSubject(USFObject* value)
{
    m_Child->setSubSystemSubject(value);
}

void PresetModeDialog::setDiskDevice(IDiskDevice* diskDevice)
{
    m_Child->setDiskDevice(diskDevice);
}

void PresetModeDialog::setProbeDataSet(IProbeDataSet* value)
{
    m_Child->setProbeDataSet(value);
}

void PresetModeDialog::setPresetTools(PresetTools* presetTools)
{
    m_Child->setPresetTools(presetTools);
}

void PresetModeDialog::setImageInterface(IImageInterfaceForExam* imageInterface)
{
    m_Child->setImageInterface(imageInterface);
}

PresetModeWidget* PresetModeDialog::getPresetModeWidget()
{
    return m_Child;
}

WindowInfo PresetModeDialog::createWindowInfo()
{
    return WindowInfo(this);
}

void PresetModeDialog::buildPresetModeShortcut()
{
    Util::SafeDeletePtr(m_PresetModeShortcutBase);

    int model_id = AppSetting::modelID();
    switch (model_id)
    {
    case 0: // 默认型号
        m_PresetModeShortcutBase = new PresetModeShortcutAtom();
        break;
    default:
        m_PresetModeShortcutBase = new PresetModeShortcutAtom();
        break;
    }

    m_PresetModeShortcutBase->setPresetModeWidget(m_Child);
}

void PresetModeDialog::onDebuggerViewEvent(QString eventName)
{
    if (m_PresetModeShortcutBase)
    {
        if (eventName == DebuggerViewEventName::LineDensity)
        {
            m_PresetModeShortcutBase->enterLineDensityPage();
        }
        else if (eventName == DebuggerViewEventName::AFESetting)
        {
            m_PresetModeShortcutBase->enterAFESettingPage();
        }
        else if (eventName == DebuggerViewEventName::DataAcquisition)
        {
            m_PresetModeShortcutBase->enterDataAcquisitionPage();
        }
        else if (eventName == DebuggerViewEventName::RegionImage)
        {
            m_PresetModeShortcutBase->enterRegionImagePage();
        }
        else if (eventName == DebuggerViewEventName::BTxSetting)
        {
            m_PresetModeShortcutBase->enterBTxSettingPage();
        }
        else if (eventName == DebuggerViewEventName::BPreProcess)
        {
            m_PresetModeShortcutBase->enterBPreProcessSettingPage();
        }
        else if (eventName == DebuggerViewEventName::DebuggingTool)
        {
            m_PresetModeShortcutBase->enterDebuggingToolPage();
        }
        else if (eventName == DebuggerViewEventName::BMidPostProcess)
        {
            m_PresetModeShortcutBase->enterBMidPostProcessPage();
        }
        else if (eventName == DebuggerViewEventName::ProbeAttribute)
        {
            m_PresetModeShortcutBase->enterProbeAttributePage();
        }
        else if (eventName == DebuggerViewEventName::AnalogCW)
        {
            m_PresetModeShortcutBase->enterAnalogCWPage();
        }
        else if (eventName == DebuggerViewEventName::XBF)
        {
            m_PresetModeShortcutBase->enterXBFPage();
        }
    }
}

PresetModeWidget::PresetModeWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::PresetModeWidget)
    , m_ExamModeMenuModel(NULL)
    , m_IZeusTool(NULL)
    , m_Frame(parent)
#ifdef USE_4D
    , m_FourdSettingTab(NULL)
#endif
    , m_FrameHeight(0)
    , m_IsMoreItemsPresetMode(false)
    , m_IsFocusCombine(false)
    , m_IsLineImage(false)
    , m_ComTextList(QStringList())
    , m_ProbeCodeBurnerSettingWidget(NULL)
    , m_VirtualProbeSettingWidget(NULL)
    , m_ProbeDataSet(NULL)
{
    ui->setupUi(this);
    // added to save freq to fs
    connect(ui->toolsTab, SIGNAL(saveOneFreqSetting()), ui->freqSettingTab, SLOT(onSaveOneFreqSetting()));
    connect(ui->toolsTab, SIGNAL(saveOneProbe()), ui->freqSettingTab, SLOT(onSaveOneProbe()));
    connect(ui->toolsTab, SIGNAL(saveProbes()), ui->freqSettingTab, SLOT(onSaveProbes()));
    connect(ui->toolsTab, SIGNAL(updateProbeSelectionUI()), this, SIGNAL(updateProbeSelectionUI()));

    m_IsMoreItemsPresetMode = ModelConfig::instance().value(ModelConfig::IsMoreItemsPresetMode, false).toBool();
    m_IsFocusCombine = ModelConfig::instance().value(ModelConfig::FocusesCombine, false).toBool();
    m_IsLineImage = ModelConfig::instance().value(ModelConfig::IsLineiImage, false).toBool();
    ui->bmidpostprocessPage->setIsMoreItemsPresetMode(m_IsMoreItemsPresetMode);
    ui->bmidpostprocessPage->setIsLineImage(m_IsLineImage);

    m_SonoClients.append(NULL);
    m_SonoClients.append(ui->btxsettingPage);
    m_SonoClients.append(ui->bpreprocessPage);
    if (m_IsMoreItemsPresetMode && m_IsLineImage)
    {
        m_SonoClients.append(ui->lineiImagePage);
        m_SonoClients.append(ui->lineiImageCVPage);
    }
    m_SonoClients.append(ui->smoothPage);
    m_SonoClients.append(ui->freqSettingTab);
    if (m_IsMoreItemsPresetMode)
    {
        m_SonoClients.append(ui->focusSettingTab);
        if (m_IsFocusCombine)
        {
            m_SonoClients.append(ui->focusesCombineTab);
        }
        m_SonoClients.append(ui->fgcSettingTab);
    }
    m_SonoClients.append(ui->edgeTab);
    if (m_IsMoreItemsPresetMode)
    {
        m_SonoClients.append(ui->colorCoefTab);
        m_SonoClients.append(ui->cFreqSettingTab);
        m_SonoClients.append(ui->pdFreqSettingTab);
        m_SonoClients.append(ui->snFreqSettingTab);
        m_SonoClients.append(ui->tdiFreqSettingTab);
        m_SonoClients.append(ui->mviFreqSettingTab);
        m_SonoClients.append(ui->dFreqSettingTab);
        m_SonoClients.append(ui->dTDIOnFreqSettingTab);
        m_SonoClients.append(ui->cwdFreqSettingTab);
        m_SonoClients.append(ui->needleSettingTab);
        m_SonoClients.append(ui->eFreqSettingTab);
        m_SonoClients.append(NULL); // Acoustic Power
        m_SonoClients.append(NULL); // Probe Para
        m_SonoClients.append(NULL); // Palm new para
#ifdef USE_4D
        if (m_FourdSettingTab == NULL)
        {
            m_FourdSettingTab = new FourDParaSettingWidget(this);
            ui->stackedWidget->addWidget(m_FourdSettingTab);
            ui->comboBox->addItem(tr("4D para setting"));
        }
        m_SonoClients.append(m_FourdSettingTab);
#endif
    }
    m_SonoClients.append(NULL); // Tools
    // m_SonoClients size与combox对不上，缺少两项
    m_SonoClients.append(NULL); // AutoFrame
    m_SonoClients.append(NULL); // SRIImage

    m_SonoClients.append(NULL);                    // debugging tool page
    m_SonoClients.append(NULL);                    // bmidpostprocessPage
    m_SonoClients.append(NULL);                    // probe setting page
    m_SonoClients.append(ui->analogCWSettingPage); // Analog-CW page

    if (!m_IsMoreItemsPresetMode)
    {
        /*删除eco系列不需要添加的tab页*/
        //从后往前添加,这样removeitem不会重新计算index值
        QList<QWidget*> removeTabs;

        removeTabs.append(ui->probeParaSettingTab);
#ifdef USE_4D
        removeTabs.append(m_FourdSettingTab);
#endif
        removeTabs.append(ui->apConfigTab);
        removeTabs.append(ui->eFreqSettingTab);
        removeTabs.append(ui->needleSettingTab);
        removeTabs.append(ui->cwdFreqSettingTab);
        removeTabs.append(ui->dTDIOnFreqSettingTab);
        removeTabs.append(ui->dFreqSettingTab);
        removeTabs.append(ui->mviFreqSettingTab);
        removeTabs.append(ui->tdiFreqSettingTab);
        removeTabs.append(ui->snFreqSettingTab);
        removeTabs.append(ui->pdFreqSettingTab);
        removeTabs.append(ui->cFreqSettingTab);
        removeTabs.append(ui->colorCoefTab);
        removeTabs.append(ui->fgcSettingTab);
        removeTabs.append(ui->focusesCombineTab);
        removeTabs.append(ui->focusSettingTab);
        removeTabs.append(ui->analogCWSettingPage);

        foreach (QWidget* tab, removeTabs)
        {
            int index = ui->stackedWidget->indexOf(tab);
            if (index != -1)
            {
                ui->stackedWidget->removeWidget(tab);
                ui->comboBox->removeItem(index);
            }
        }
    }
    else
    {
        if (!m_IsFocusCombine)
        {
            int index = ui->comboBox->findText(tr("Focuses Combine Setting"));
            if (index >= 0)
            {
                ui->stackedWidget->removeWidget(ui->focusesCombineTab);
                ui->comboBox->removeItem(index);
            }
        }
        if (!m_IsLineImage)
        {
            int index = ui->comboBox->findText(tr("Line iImage"));
            if (index >= 0)
            {
                ui->stackedWidget->removeWidget(ui->lineiImagePage);
                ui->comboBox->removeItem(index);
            }

            index = ui->comboBox->findText(tr("Line iImage CV"));
            if (index >= 0)
            {
                ui->stackedWidget->removeWidget(ui->lineiImageCVPage);
                ui->comboBox->removeItem(index);
            }
        }
    }

    for (int i = 0; i < ui->comboBox->count(); i++)
    {
        m_ComTextList.append(ui->comboBox->itemText(i));
        ui->comboBox->setItemText(
            i, QCoreApplication::translate("PresetModeWidget", m_ComTextList.at(i).toLatin1().data(), nullptr));
    }
}

PresetModeWidget::~PresetModeWidget()
{
    delete ui;
#ifdef USE_4D
    if (m_FourdSettingTab != NULL)
    {
        delete m_FourdSettingTab;
        m_FourdSettingTab = NULL;
    }
#endif
}

void PresetModeWidget::setBeamFormerTool(IBeamFormerTool* value)
{
    ui->toolsTab->setBeamFormerTool(value);
    if (value->focusParasModel() != NULL)
    {
        ui->focusSettingTab->setModel(value->focusParasModel());
    }
    if (value->fgcSettingModel() != NULL)
    {
        ui->fgcSettingTab->setModel(value->fgcSettingModel());
    }
    if (value->focusesCombineModel() != NULL)
    {
        ui->focusesCombineTab->setModel(value->focusesCombineModel());
    }
    ui->apConfigTab->setBeamFormerTool(value);
    ui->probeParaSettingTab->setBeamFormerTool(value);
    ui->needleSettingTab->setBeamformerTool(value);
    ui->freqSettingTab->setBeamformerTool(value);
}

void PresetModeWidget::setExamModeMenuModel(IExamModeMenuModel* value)
{
    m_ExamModeMenuModel = value;
}

void PresetModeWidget::setIZeusTool(IZeusTool* value)
{
    ui->toolsTab->setIZeusTool(value);
}

void PresetModeWidget::onSetSonoParameters()
{
    ui->toolsTab->setSonoParameters(m_SonoParameters);

    ui->probeParaSettingTab->setSonoParameters(m_SonoParameters);

    ui->palmParasSettingTab->setSonoParameters(m_SonoParameters);

    ui->bmidpostprocessPage->setSonoParameters(m_SonoParameters);

    QStringList presetNames = m_SonoParameters->preset().names();

    PresetModeParametersGroups groups;
    {
        QStringList strs;
        strs << BFPNames::ScpdStr;
        if (!presetNames.contains(BFPNames::iImageStr))
        {
            strs << BFPNames::VHSiStr << BFPNames::AlphaStr << BFPNames::BetaStr << BFPNames::GammaStr;
        }

        strs << BFPNames::DPulseNumStr << BFPNames::PersistDiffWithCpdOnStr << BFPNames::PersistDiffWithSraOnStr
             << BFPNames::PersistentDeltaTHIStr << BFPNames::PersistentDeltaCFMStr << BFPNames::PersistentDeltaPDStr
             << BFPNames::PersistentDeltaSNStr << BFPNames::PersistentDeltaTDIStr << BFPNames::PersistentDeltaMVIStr
             << BFPNames::AdapPostProcStr << BFPNames::AdapPostProcSmoothStr << BFPNames::AdapPostProcDeltaStr
             << BFPNames::FFocusPrtDeltaStr << BFPNames::WTStr << BFPNames::CVLTStr << BFPNames::TNRStr
             << BFPNames::CHETStr << BFPNames::CETStr << BFPNames::CVRTStr << BFPNames::CTGCStr << BFPNames::THISelStr
             << BFPNames::OptimizeFPSStr;

        if (presetNames.contains(BFPNames::XContrastValueStr))
        {
            strs << BFPNames::XContrastValueStr;
        }

        if (presetNames.contains(BFPNames::SraGainColorDeltaStr))
        {
            strs << BFPNames::SraGainColorDeltaStr;
        }

        if (presetNames.contains(BFPNames::DetailWeightDeltaSraOnStr))
        {
            strs << BFPNames::DetailWeightDeltaSraOnStr;
        }

        if (presetNames.contains(BFPNames::DetailWeightDeltaSraCpdOffStr))
        {
            strs << BFPNames::DetailWeightDeltaSraCpdOffStr;
        }

        strs << BFPNames::NotReduceGainStr << BFPNames::GateSegmentStr << BFPNames::BGammaPosStr
             << BFPNames::BGammaStartStr << BFPNames::BGammaStepStr << BFPNames::TrapezoidalCPDSteerStr;
        //             << BFPNames::TrapezoidalCPDSteer2Str;

        if (presetNames.contains(BFPNames::ROISteerAnglesStr))
        {
            strs << BFPNames::ROISteerAnglesStr;
        }

        if (presetNames.contains(BFPNames::TICEnStr))
        {
            strs << BFPNames::TICEnStr;
        }

        if (presetNames.contains(BFPNames::UnifTgcEnStr))
        {
            strs << BFPNames::UnifTgcEnStr;
        }

        // BeamFormerBase::onGettingGainControlTableValue中有使用到该参数，预设里面也有值，所以在这里加了后台可调
        if (presetNames.contains(BFPNames::BWGainDeltaStr))
        {
            strs << BFPNames::BWGainDeltaStr;
        }

        if (presetNames.contains(BFPNames::GainTDI_DeltaStr))
        {
            strs << BFPNames::GainTDI_DeltaStr;
        }

        if (presetNames.contains(BFPNames::GainColorTM_DeltaStr))
        {
            strs << BFPNames::GainColorTM_DeltaStr;
        }

        if (presetNames.contains(BFPNames::TGCMinStr))
        {
            strs << BFPNames::TGCMinStr;
        }

        if (presetNames.contains(BFPNames::TGCMaxStr))
        {
            strs << BFPNames::TGCMaxStr;
        }

        if (presetNames.contains(BFPNames::LGCMAXStr))
        {
            strs << BFPNames::LGCMAXStr;
        }

        if (presetNames.contains(BFPNames::LGCMINStr))
        {
            strs << BFPNames::LGCMINStr;
        }

        if (m_SonoParameters->pBV(BFPNames::IsSupportIntegerDepthStr))
        {
            if (presetNames.contains(BFPNames::DepthCMListStr))
            {
                strs << BFPNames::DepthCMListStr;
            }
        }
        if (presetNames.contains(BFPNames::MECGDlyDeltasStr))
        {
            strs << BFPNames::MECGDlyDeltasStr;
        }

        if (presetNames.contains(BFPNames::FreeMECGDlyDeltasStr))
        {
            strs << BFPNames::FreeMECGDlyDeltasStr;
        }

        if (presetNames.contains(BFPNames::DECGDlyDeltasStr))
        {
            strs << BFPNames::DECGDlyDeltasStr;
        }

        if (presetNames.contains(BFPNames::CWDECGDlyDeltasStr))
        {
            strs << BFPNames::CWDECGDlyDeltasStr;
        }

        if (presetNames.contains(BFPNames::DTDIECGDlyDeltasStr))
        {
            strs << BFPNames::DTDIECGDlyDeltasStr;
        }

        QHash<QString, QString> labels;
        labels[BFPNames::NotReduceGainStr] = "Fat";

        groups.append(PresetModeParametersGroup("Common", m_SonoParameters->parameters(strs), labels));
    }
    {
        QStringList strs =
            QStringList() << BFPNames::TestSignalStr << BFPNames::TestParameterStr << BFPNames::DebugPara0Str
                          << BFPNames::DebugPara1Str << BFPNames::DebugPara2Str << BFPNames::DebugPara3Str
                          << BFPNames::DebugPara5Str << BFPNames::DebugPara6Str << BFPNames::DebugPara7Str
                          << BFPNames::DebugPara8Str << BFPNames::DebugPara9Str << BFPNames::TxWeightDebugEnableStr
                          << BFPNames::TxWeightValueStr << BFPNames::TxWeightPosStr
                          << BFPNames::TxWeightSaveDataEnableStr;

        groups.append(PresetModeParametersGroup("Debug", m_SonoParameters->parameters(strs)));
    }
    if (m_IsMoreItemsPresetMode)
    {
        {
            QStringList strs = QStringList() << BFPNames::ParaPresetTimeStr << BFPNames::ParaPresetTime_XBFStr
                                             << BFPNames::FrameScapeEnableStr << BFPNames::CFrameScapeTimeStr
                                             << BFPNames::FrameScapeTimeStr << BFPNames::FrameScapeTimeSlowStr
                                             << BFPNames::AdjustmentOfBStr << BFPNames::RvTxBStr << BFPNames::PrtOfBStr
                                             << BFPNames::PrtOfB_DeltaStr << BFPNames::AdjustmentOfCStr
                                             << BFPNames::RvTxCStr << BFPNames::PrtOfCStr << BFPNames::AdjustmentOfDStr
                                             << BFPNames::RvTxDStr << BFPNames::PrtOfDStr << BFPNames::CqyzOfDStr;
            m_SonoParameters->parameter(BFPNames::RvTxBStr)->setEnabled(false);
            m_SonoParameters->parameter(BFPNames::PrtOfBStr)->setEnabled(false);
            m_SonoParameters->parameter(BFPNames::RvTxCStr)->setEnabled(false);
            m_SonoParameters->parameter(BFPNames::PrtOfCStr)->setEnabled(false);
            m_SonoParameters->parameter(BFPNames::RvTxDStr)->setEnabled(false);
            m_SonoParameters->parameter(BFPNames::PrtOfDStr)->setEnabled(false);
            m_SonoParameters->parameter(BFPNames::AdjustmentOfBStr)->setEnabled(true);

            groups.append(PresetModeParametersGroup("PRT", m_SonoParameters->parameters(strs)));
        }

        {
            QStringList strs = QStringList() << BFPNames::DBFResetStr << BFPNames::DSPResetStr << BFPNames::DBFInfoStr;

            groups.append(PresetModeParametersGroup("Other Para", m_SonoParameters->parameters(strs)));
        }

        {
            QStringList strs = QStringList() << BFPNames::BFPipelineNumberStr << BFPNames::ElastoRebootTimeStr
                                             << BFPNames::FanControlSysStr << BFPNames::FanControlCPUStr;

            groups.append(PresetModeParametersGroup("Model Config", m_SonoParameters->parameters(strs)));
        }

        {
            QStringList strs = QStringList() << BFPNames::MotherboardShieldStr << BFPNames::ExtensionPlateShieldStr;

            groups.append(PresetModeParametersGroup("Mother Board", m_SonoParameters->parameters(strs)));
        }

        {
            QStringList strs = QStringList()
                               << BFPNames::XCoordinateStr << BFPNames::XCoordinateEnStr << BFPNames::YCoordinateStr
                               << BFPNames::YCoordinateEnStr << BFPNames::LineNoShieldStr;

            groups.append(PresetModeParametersGroup("Image Test", m_SonoParameters->parameters(strs)));
        }

        // Elasto preset paras
        if (AppSetting::isElastography())
        {
            QStringList strs = QStringList()
                               << BFPNames::ElastoDebug1Str << BFPNames::ElastoDebug2Str << BFPNames::ElastoDebug3Str
                               << BFPNames::ElastoDebug4Str << BFPNames::ElastoDebug5Str
                               << BFPNames::ElastoProcessDelayStr << BFPNames::ElastoSmoothFilterStr
                               << BFPNames::ElastoStretchGainStr << BFPNames::ElastoStrainFilterStr
                               << BFPNames::ElastoMedianPostStr << BFPNames::ElastoMedianPreStr
                               << BFPNames::ElastoPersistentCoefStr << BFPNames::ElastoAxialCoefStr
                               << BFPNames::ElastoLateralCoefStr << BFPNames::ElastoKernelSizeStr
                               << BFPNames::ElastoSearchSizeStr << BFPNames::ElastoThresholdXStr
                               << BFPNames::ElastoThresholdYStr << BFPNames::ElastoDepthDeltaStr
                               << BFPNames::SampleRateDopElastoStr << BFPNames::ElastoQualityLevelStr;

            QHash<QString, QString> labels;
            labels[BFPNames::SampleRateDopElastoStr] = "ElastoPRF";

            groups.append(
                PresetModeParametersGroup("Elastography Setting", m_SonoParameters->parameters(strs), labels));
        }
        {
            QStringList strs = QStringList() << BFPNames::Q_IMAGEStr << BFPNames::WallFilterStr
                                             << BFPNames::WallThresholdSStr << BFPNames::CompoundDebugStr
                                             << BFPNames::PWSoundDelayTimeStr << BFPNames::PWWaveImageDelayTimeStr
                                             << BFPNames::PW2DImageDelayTimeStr
                                             //                    << BFPNames::PWECGDelayTimeStr
                                             << BFPNames::MWaveImageDelayTimeStr << BFPNames::M2DImageDelayTimeStr
                                             << BFPNames::BECGDelayTimeStr
                                             //                    << BFPNames::MECGDelayTimeStr
                                             //                    << BFPNames::ECGDelayTimeStr
                                             //                    << BFPNames::ECG2DImageDelayTimeStr
                                             << BFPNames::LineImageDebugStr << BFPNames::LineImageSteeringDebugStr
                                             << BFPNames::LineColorMapDebugStr;

            groups.append(PresetModeParametersGroup("SOFTWARE", m_SonoParameters->parameters(strs)));
            connect(m_SonoParameters->parameter(BFPNames::ECGVelocityStr), SIGNAL(valueChanged(QVariant)), this,
                    SLOT(onECGVelocityChanged()));
        }
        {
            QStringList strs =
                QStringList() << BFPNames::DTx1_TriplexStr << BFPNames::DTx2_TriplexStr << BFPNames::DTx3_TriplexStr
                              << BFPNames::GateDepth1Str << BFPNames::GateDepth2Str << BFPNames::Delta_triplexStr
                              << BFPNames::PWScanLineDeltaStr << BFPNames::BCRelativePosInDModeStr
                              << BFPNames::PixelRatioShowStr << BFPNames::GateSegmentStr
                              << BFPNames::AudioFilterCoefSelShowStr << BFPNames::DopSampleNumStr
                              << BFPNames::DopAudioSegmentStr << BFPNames::DopAudioPreGainStr
                              << BFPNames::DopAudioPostGainStr << BFPNames::DopMidGainStr << BFPNames::DopTimeFilterStr
                              << BFPNames::DopOutGainStr << BFPNames::DopFilterLengthStr
                              << BFPNames::DopVelocityFilterStr << BFPNames::DSampleRateStr << BFPNames::BCSampleRateStr
                              << BFPNames::BTriplexPrt_DeltaStr << BFPNames::ColorLineDensityStr
                              << BFPNames::PW_DummyCountStr << BFPNames::Triplex_DummyCountStr;

            groups.append(PresetModeParametersGroup("PW Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList() << BFPNames::PixelRatioTDIStr << BFPNames::GateSegmentTDIStr
                                             << BFPNames::AudioFilterCoefSelTDIStr << BFPNames::TDSampleNumStr
                                             << BFPNames::TDAudioSegmentStr << BFPNames::TDAudioPreGainStr
                                             << BFPNames::TDAudioPostGainStr << BFPNames::TDMidGainStr
                                             << BFPNames::TDTimeFilterStr << BFPNames::TDOutGainStr
                                             << BFPNames::TDFilterLengthStr << BFPNames::TDVelocityFilterStr;

            groups.append(PresetModeParametersGroup("DTDI Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList()
                               << BFPNames::PixelRatioShowStr << BFPNames::GateSegmentStr
                               << BFPNames::AudioFilterCoefSelShowStr << BFPNames::DopSampleNumStr
                               << BFPNames::DopAudioSegmentStr << BFPNames::DopAudioPreGainStr
                               << BFPNames::DopAudioPostGainStr << BFPNames::DopMidGainStr << BFPNames::DopTimeFilterStr
                               << BFPNames::DopOutGainStr << BFPNames::DopFilterLengthStr
                               << BFPNames::DopVelocityFilterStr << BFPNames::DSampleRateStr
                               << BFPNames::BCSampleRateStr << BFPNames::BTriplexPrt_DeltaStr
                               << BFPNames::HPrfTopGateDeltaStr << BFPNames::PixelRatioCWDStr
                               << BFPNames::GateSegmentCWDStr << BFPNames::AudioFilterCoefSelCWDStr
                               << BFPNames::CWSampleNumStr << BFPNames::CWAudioSegmentStr << BFPNames::CWAudioPreGainStr
                               << BFPNames::CWAudioPostGainStr << BFPNames::CWPostGainStr << BFPNames::CWMidGainStr
                               << BFPNames::CWTimeFilterStr << BFPNames::CWOutGainStr << BFPNames::CWFilterLengthStr
                               << BFPNames::CWVelocityFilterStr;

            groups.append(PresetModeParametersGroup("CW Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList() << BFPNames::MBStr;
            groups.append(PresetModeParametersGroup("B Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList()
                               << BFPNames::ColorEDAEnableStr << BFPNames::ColorEDASETypeStr
                               << BFPNames::ColorEDASESizeStr << BFPNames::ColorPersistenceAlgorithmStr
                               << BFPNames::ColorPersistenceHighStr << BFPNames::ColorPersistenceLowStr
                               << BFPNames::ColorPersistenceANStr << BFPNames::ColorPersistenceRatioStr
                               << BFPNames::ColorPersistenceUsingN_1Str << BFPNames::IsDPDColorStr
                               << BFPNames::CFVelocityThresholdStr << BFPNames::CETStr << BFPNames::CVRTStr
                               << BFPNames::CVLTStr << BFPNames::CHETStr << BFPNames::CTGCStr << BFPNames::CVRTDeltaStr
                               << BFPNames::CVLTDeltaStr << BFPNames::CETDeltaStr << BFPNames::DummyEnSampleShowStr
                               << BFPNames::PacketSizeStr << BFPNames::WFGainControlStr << BFPNames::ColorCoefStr
                               << BFPNames::LComeBackStr << BFPNames::AComeBackStr << BFPNames::CFMIIReStr
                               << BFPNames::CFMVelLevelShowStr << BFPNames::MBIncStr << BFPNames::MBColorStr
                               << BFPNames::COverlapStr << BFPNames::ColorOverlapWeightStr << BFPNames::QFlowModeStr
                               << BFPNames::QBeamOnStr << BFPNames::PrtDeltaOnLowDensityStr
                               << BFPNames::ColorLineDensityStr << BFPNames::CPriorityStr << BFPNames::CfmSlopeStr
                               << BFPNames::CFMDigitalTgc0Str << BFPNames::CFMDigitalTgc1Str
                               << BFPNames::CFMDigitalTgc2Str << BFPNames::CFMDigitalTgc3Str
                               << BFPNames::CFMDigitalTgc4Str << BFPNames::CFMDigitalTgc5Str
                               << BFPNames::CFMDigitalTgc6Str << BFPNames::CFMDigitalTgc7Str
                               << BFPNames::CFMDigitalTgc8Str << BFPNames::CFMDigitalTgc9Str
                               << BFPNames::CFMDigitalTgc10Str << BFPNames::CFMDigitalTgc11Str
                               << BFPNames::CFMDigitalTgc12Str << BFPNames::CFMDigitalTgc13Str
                               << BFPNames::CFMDigitalTgc14Str << BFPNames::CFMDigitalTgc15Str
                               << BFPNames::ColorVHSITypeStr << BFPNames::ColorHoleFillingDBStr;
            groups.append(PresetModeParametersGroup("Color Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList()
                               << BFPNames::MVITransparencyStr << BFPNames::MVIVelocityThresholdStr
                               << BFPNames::MVICETStr << BFPNames::MVICVRTStr << BFPNames::MVICVLTStr
                               << BFPNames::MVICHETStr << BFPNames::MVICTGCStr << BFPNames::HDCPACETStr
                               << BFPNames::DummyEnSampleMVIStr << BFPNames::PacketSizeMVIStr
                               << BFPNames::WFGainControlMVIStr << BFPNames::HDCPAWFGainCtrlStr
                               << BFPNames::MVIPostGainStr << BFPNames::MVICoefStr << BFPNames::LComeBackMVIStr
                               << BFPNames::AComeBackMVIStr << BFPNames::MVIIIReStr << BFPNames::CFMVelLevelShowStr
                               << BFPNames::MBMVIStr << BFPNames::HDCPAWallFilterStr << BFPNames::BloodEffectionMVIStr
                               << BFPNames::MVIInvertStateStr << BFPNames::PAComeBackStr << BFPNames::PLComeBackStr
                               << BFPNames::MVIDynamicRangeStr;
            groups.append(PresetModeParametersGroup("MVI Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList()
                               << BFPNames::PDCoefStr << BFPNames::CETStr << BFPNames::CVRTStr << BFPNames::CVLTStr
                               << BFPNames::CHETStr << BFPNames::CTGCStr << BFPNames::MulitSlotReadStr
                               << BFPNames::DummyEnSampleStr << BFPNames::WFGainControlStr << BFPNames::PacketSizeStr
                               << BFPNames::AComeBackStr << BFPNames::LComeBackStr << BFPNames::PDIIReStr
                               << BFPNames::PDCETStr << BFPNames::PDCVRTStr << BFPNames::PDCVLTStr
                               << BFPNames::PDCHETStr << BFPNames::PDCTGCStr << BFPNames::MBPDStr
                               << BFPNames::QBeamOnStr << BFPNames::DummyEnSamplePDStr << BFPNames::WFGainControlPDStr
                               << BFPNames::PacketSizePDStr << BFPNames::AComeBackPDStr << BFPNames::LComeBackPDStr;

            groups.append(PresetModeParametersGroup("CPA Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList() << BFPNames::DPDCETStr << BFPNames::DPDCVRTStr << BFPNames::DPDCVLTStr
                                             << BFPNames::DPDCHETStr << BFPNames::DPDCTGCStr;
            groups.append(PresetModeParametersGroup("DPD Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList()
                               << BFPNames::SampleRateDopSNStr << BFPNames::ColorLineDensitySNStr
                               << BFPNames::PowerThresholdSNStr << BFPNames::BloodEffectionSNStr
                               << BFPNames::FrameAvgSNStr << BFPNames::SNCoefStr << BFPNames::SNCETStr
                               << BFPNames::SNCVRTStr << BFPNames::SNCHETStr << BFPNames::SNCTGCStr << BFPNames::MBSNStr
                               << BFPNames::DummyEnSampleSNStr << BFPNames::WFGainControlSNStr
                               << BFPNames::PacketSizeSNStr << BFPNames::AComeBackSNStr << BFPNames::LComeBackSNStr
                               << BFPNames::SNIIReStr << BFPNames::WallFilterSNStr << BFPNames::SNColorMapIndexStr;
            groups.append(PresetModeParametersGroup("SonoNeedle Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList()
                               << BFPNames::TDITransparencyStr << BFPNames::TDILineDensityStr
                               << BFPNames::PowerThresholdTDIStr << BFPNames::TDICETStr << BFPNames::TDICVRTStr
                               << BFPNames::TDICVLTStr << BFPNames::TDICHETStr << BFPNames::TDICTGCStr
                               << BFPNames::DummyEnSampleTDIStr << BFPNames::PacketSizeTDIStr
                               << BFPNames::WFGainControlTDIStr << BFPNames::TDICoefStr << BFPNames::LComeBackTDIStr
                               << BFPNames::AComeBackTDIStr << BFPNames::TDIIIReStr << BFPNames::CFMVelLevelShowStr
                               << BFPNames::MBTDIStr;
            groups.append(PresetModeParametersGroup("TDI Related Preset", m_SonoParameters->parameters(strs)));
        }
        {
            QStringList strs = QStringList() << BFPNames::SystemFanControlStr << BFPNames::CPUFanControlStr
                                             << BFPNames::BottomFanControlStr;

            groups.append(PresetModeParametersGroup("Fan Control", m_SonoParameters->parameters(strs)));
        }
    }

    ui->commonTab->setModel(groups);
    onVS_ModeChanged(m_SonoParameters->pV(BFPNames::VS_ModeStr));

    {
        QStringList strs = QStringList() << BFPNames::SRIEnableStr << BFPNames::SRINoiseLevelStr
                                         << BFPNames::SRIEdgeEnhanceStr << BFPNames::SRIFilterStrengthStr
                                         << BFPNames::SRIDRAdjustStr << BFPNames::SRIHfnoiseStr
                                         << BFPNames::SRIEdgeThresholdStr << BFPNames::SRIDetailPreservationStr
                                         << BFPNames::SRIEdgeRampDownStr << BFPNames::SRIEdgeDirectionThreshStr
                                         << BFPNames::SRIHoleFillerThreshStr << BFPNames::SRIOverallStrengthStr
                                         << BFPNames::SRINoiseFilterTypeStr << BFPNames::SRIEdgeFilterTypeStr;
        ui->sriImagePage->setModel(PresetModeParametersGroup("SRIImage", m_SonoParameters->parameters(strs)));
    }

    initLineiImagePage();

    ui->smoothPage->setGroupName("Smooth Settings");
    ui->smoothPage->setModel(&SingleSmoothSettings::instance().settings());
    ui->smoothPage->setParaName(BFPNames::SmoothStr);
    ui->smoothPage->setSettingIdsStr(BFPNames::SmoothSettingIdsStr);
    ui->smoothPage->setIsReadOnly(false);

    ui->btxsettingPage->setFSType(FreqParasContainer::B);
    ui->bpreprocessPage->setFSType(FreqParasContainer::B);
    ui->freqSettingTab->setFSType(FreqParasContainer::B);
    //    ui->freqSettingTab->setSonoParameters(m_SonoParameters);

    //    ui->focusSettingTab->setSonoParameters(m_SonoParameters);
    //    ui->focusesCombineTab->setSonoParameters(m_SonoParameters);

    //    ui->edgeTab->setSonoParameters(m_SonoParameters);
    //    ui->colorCoefTab->setSonoParameters(m_SonoParameters);

    if (m_IsMoreItemsPresetMode)
    {
        ui->cFreqSettingTab->setFSType(FreqParasContainer::Color);
        //    ui->cFreqSettingTab->setSonoParameters(m_SonoParameters);

        ui->pdFreqSettingTab->setFSType(FreqParasContainer::PD);
        //    ui->pdFreqSettingTab->setSonoParameters(m_SonoParameters);

        ui->snFreqSettingTab->setFSType(FreqParasContainer::SonoNeedle);

        ui->tdiFreqSettingTab->setFSType(FreqParasContainer::TDI);
        //    ui->tdiFreqSettingTab->setSonoParameters(m_SonoParameters);

        ui->dFreqSettingTab->setFSType(FreqParasContainer::Dop);
        //    ui->dFreqSettingTab->setSonoParameters(m_SonoParameters);

        ui->dTDIOnFreqSettingTab->setFSType(FreqParasContainer::TD);
        //    ui->dTDIOnFreqSettingTab->setSonoParameters(m_SonoParameters);

        ui->cwdFreqSettingTab->setFSType(FreqParasContainer::CWD);
        //    ui->cwdFreqSettingTab->setSonoParameters(m_SonoParameters);

        ui->eFreqSettingTab->setFSType(FreqParasContainer::Elasto);

        ui->mviFreqSettingTab->setFSType(FreqParasContainer::MVI);

        ui->analogCWSettingPage->setFSType(FreqParasContainer::CWD);
    }

    connect(m_SonoParameters->parameter(BFPNames::TrapezoidalModeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(changeScpdControlEnabled()));
    changeScpdControlEnabled();

    if (m_IsMoreItemsPresetMode)
    {
        QStringList colorFSConds = QStringList()
                                   << BFPNames::TDIEnStr << BFPNames::ColorImageModeStr << BFPNames::ElastoEnStr
                                   << BFPNames::SonoNeedleStr << BFPNames::MVIModeStr;
        foreach (const QString& name, colorFSConds)
        {
            connect(m_SonoParameters->parameter(name), SIGNAL(valueChanged(QVariant)), this, SLOT(controlColorFSTab()));
        }
        controlColorFSTab();

        QStringList dopFSConds = QStringList() << BFPNames::TDIEnStr << BFPNames::CWEnStr;
        foreach (const QString& name, dopFSConds)
        {
            connect(m_SonoParameters->parameter(name), SIGNAL(valueChanged(QVariant)), this, SLOT(controlDopFSTab()));
        }
        controlDopFSTab();

        connect(m_SonoParameters->parameter(BFPNames::NeedleModeStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(controlNeedleTab()));
        controlNeedleTab();
    }

    initDebuggingToolPage();
    initProbeSettingPage();
}

void PresetModeWidget::retranslateUi()
{
    ui->retranslateUi(this); // added by jyq
    for (int i = 0; i < ui->comboBox->count(); i++)
    {
        ui->comboBox->setItemText(
            i, QCoreApplication::translate("PresetModeWidget", m_ComTextList.at(i).toLatin1().data(), nullptr));
    }
}

void PresetModeWidget::initLineiImagePage()
{
    if (m_IsLineImage)
    {
        if (m_SonoParameters->preset().names().contains(BFPNames::iImageStr))
        {
            ui->lineiImagePage->setGroupName("Line iImage Settings");
            ui->lineiImagePage->setModel(&SingleLineiImageSettings::instance().settings(pIV(BFPNames::ProbeIdStr)));
            ui->lineiImagePage->updateComboBox();
            ui->lineiImagePage->setParaName(BFPNames::iImageStr);
            ui->lineiImagePage->setSettingIdsStr(BFPNames::LineiImageSettingIdsStr);
            ui->lineiImagePage->setIsReadOnly(false);

            ui->lineiImageCVPage->setGroupName("Line iImage CV Settings");
            ui->lineiImageCVPage->setModel(
                &SingleLineiImageSettings::instance().settings_cv(pIV(BFPNames::ProbeIdStr)));
            ui->lineiImageCVPage->setParaName(BFPNames::iImageStr);
            ui->lineiImageCVPage->setSettingIdsStr(BFPNames::LineiImageCVSettingIdsStr);
            ui->lineiImageCVPage->setIsReadOnly(false);
        }
        else
        {
            ui->lineiImagePage->setVisible(false);
            ui->lineiImageCVPage->setVisible(false);
        }
        ui->bmidpostprocessPage->initCVPage();
    }
}

void PresetModeWidget::onProbeIdChanged(const QVariant& value)
{
    initLineiImagePage();
}

void PresetModeWidget::onVS_ModeChanged(const QVariant& value)
{
    //虚拟源布线开关改变的时候，ParaPresetTime需要更换
    //普通B：ParaPresetTime
    //虚拟源开启：ParaPresetTime_XBF
    if (m_IsMoreItemsPresetMode)
    {
        if (ModelConfig::instance().value(ModelConfig::IsSupportVS, false).toBool() && value.toBool())
        {
            ui->commonTab->groupWidget("PRT")->setSliderVisible(QStringList() << BFPNames::ParaPresetTimeStr, false);
            ui->commonTab->groupWidget("PRT")->setSliderVisible(QStringList() << BFPNames::ParaPresetTime_XBFStr, true);
        }
        else
        {
            ui->commonTab->groupWidget("PRT")->setSliderVisible(QStringList() << BFPNames::ParaPresetTimeStr, true);
            ui->commonTab->groupWidget("PRT")->setSliderVisible(QStringList() << BFPNames::ParaPresetTime_XBFStr,
                                                                false);
        }
    }
}

void PresetModeWidget::on_pushButtonMinimize_clicked()
{
    if (m_FrameHeight == 0)
    {
        m_FrameHeight = m_Frame->height();
    }

    ui->frame->setVisible(!ui->frame->isVisible());
    if (ui->frame->isVisible())
    {
        ui->pushButtonMinimize->setText(tr("Hide"));
        m_Frame->setFixedHeight(m_FrameHeight);
    }
    else
    {
        ui->pushButtonMinimize->setText(tr("Show"));
        m_Frame->setFixedHeight(88);
    }
}

void PresetModeWidget::on_pushButtonSaveAll_clicked()
{
    if (MessageBoxFrame::question(this, QString(), tr("Are you sure to save preset and all settings?"),
                                  QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes)
    {
        QStringList successTabs;
        QStringList errorTabs;
        bool ret = true;

        if (!m_ExamModeMenuModel->save())
        {
            ret = false;
            errorTabs << "Preset";
        }
        else
        {
            successTabs << "Preset";
        }

        if (ui->lineiImagePage->sonoParameters() != NULL && ui->lineiImagePage->isEnabled())
        {
            if (!ui->lineiImagePage->save())
            {
                ret = false;
                errorTabs << "LineiImage";
            }
            else
            {
                successTabs << "LineiImage";
            }
        }

        if (ui->lineiImageCVPage->sonoParameters() != NULL && ui->lineiImageCVPage->isEnabled())
        {
            if (!ui->lineiImageCVPage->save())
            {
                ret = false;
                errorTabs << "LineiImageCV";
            }
            else
            {

                successTabs << "LineiImageCV";
            }
        }
        if (ui->freqSettingTab->sonoParameters() != NULL && ui->freqSettingTab->isEnabled())
        {
            if (!ui->freqSettingTab->save())
            {
                ret = false;
                errorTabs << "BFreqSetting";
            }
            else
            {
                successTabs << "BFreqSetting";
            }
        }

        if (m_IsMoreItemsPresetMode)
        {
            if (ui->focusSettingTab->sonoParameters() != NULL && ui->focusSettingTab->model() != NULL)
            {
                if (!ui->focusSettingTab->save(false))
                {
                    ret = false;
                    errorTabs << "FocusSetting";
                }
                else
                {
                    successTabs << "FocusSetting";
                }
            }

            if (ui->fgcSettingTab->sonoParameters() != NULL && ui->fgcSettingTab->model() != NULL)
            {
                if (!ui->fgcSettingTab->save(false))
                {
                    ret = false;
                    errorTabs << "FGCSetting";
                }
                else
                {
                    successTabs << "FGCSetting";
                }
            }

            if (ui->focusesCombineTab->sonoParameters() != NULL && ui->focusesCombineTab->model() != NULL)
            {
                if (!ui->focusesCombineTab->save(false))
                {
                    ret = false;
                    errorTabs << "FocusesCombineSetting";
                }
                else
                {
                    successTabs << "FocusesCombineSetting";
                }
            }

            if (ui->colorCoefTab->sonoParameters() != NULL)
            {
                if (!ui->colorCoefTab->save())
                {
                    ret = false;
                    errorTabs << "ColorCoef";
                }
                else
                {
                    successTabs << "ColorCoef";
                }
            }

            if (ui->cFreqSettingTab->sonoParameters() != NULL && ui->cFreqSettingTab->isEnabled())
            {
                if (!ui->cFreqSettingTab->save())
                {
                    ret = false;
                    errorTabs << "CFreqSetting";
                }
                else
                {
                    successTabs << "CFreqSetting";
                }
            }

            if (ui->pdFreqSettingTab->sonoParameters() != NULL && ui->pdFreqSettingTab->isEnabled())
            {
                if (!ui->pdFreqSettingTab->save())
                {
                    ret = false;
                    errorTabs << "PD&DPDFreqSetting";
                }
                else
                {
                    successTabs << "DPDFreqSetting";
                }
            }

            if (ui->snFreqSettingTab->sonoParameters() != NULL && ui->snFreqSettingTab->isEnabled())
            {
                if (!ui->snFreqSettingTab->save())
                {
                    ret = false;
                    errorTabs << "SNFreqSetting";
                }
                else
                {
                    successTabs << "SNFreqSetting";
                }
            }

            if (ui->tdiFreqSettingTab->sonoParameters() != NULL && ui->tdiFreqSettingTab->isEnabled())
            {
                if (!ui->tdiFreqSettingTab->save())
                {
                    ret = false;
                    errorTabs << "TDIFreqSetting";
                }
                else
                {
                    successTabs << "TDIFreqSetting";
                }
            }

            if (ui->mviFreqSettingTab->sonoParameters() != NULL && ui->mviFreqSettingTab->isEnabled())
            {
                if (!ui->mviFreqSettingTab->save())
                {
                    ret = false;
                    errorTabs << "MVIFreqSetting";
                }
                else
                {
                    successTabs << "MVIFreqSetting";
                }
            }

            if (ui->dFreqSettingTab->sonoParameters() != NULL)
            {
                if (!ui->dFreqSettingTab->save())
                {
                    ret = false;
                    errorTabs << "DFreqSetting";
                }
                else
                {
                    successTabs << "DFreqSetting";
                }
            }

            if (ui->dTDIOnFreqSettingTab->sonoParameters() != NULL)
            {
                if (!ui->dTDIOnFreqSettingTab->save())
                {
                    ret = false;
                    errorTabs << "DTDIOnFreqSetting";
                }
                else
                {
                    successTabs << "DTDIOnFreqSetting";
                }
            }

            if (ui->cwdFreqSettingTab->sonoParameters() != NULL)
            {
                if (!ui->cwdFreqSettingTab->save())
                {
                    ret = false;
                    errorTabs << "CWDFreqSetting";
                }
                else
                {
                    successTabs << "CWDFreqSetting";
                }
            }

            if (ui->eFreqSettingTab->sonoParameters() != NULL && ui->eFreqSettingTab->isEnabled())
            {
                if (!ui->eFreqSettingTab->save())
                {
                    ret = false;
                    errorTabs << "EFreqSetting";
                }
                else
                {
                    successTabs << "EFreqSetting";
                }
            }

            if (!ui->apConfigTab->save())
            {
                ret = false;
                errorTabs << "APConfigSetting";
            }
            else
            {
                successTabs << "APConfigSetting";
            }

            //因为probeParaSettingTab中有needle角度信息，保存后会有刷新的操作，为了确保needle参数保存有效，必须要在probeParaSettingTab保存之前
            if (ui->needleSettingTab->sonoParameters() != NULL && ui->needleSettingTab->isEnabled())
            {
                if (!ui->needleSettingTab->save())
                {
                    ret = false;
                    errorTabs << "NeedleSetting";
                }
                else
                {
                    successTabs << "NeedleSetting";
                }
            }

            if (!ui->probeParaSettingTab->save())
            {
                ret = false;
                errorTabs << "ProbeParaSetting";
            }
            else
            {
                successTabs << "ProbeParaSetting";
            }

            if (!ui->palmParasSettingTab->save())
            {
                ret = false;
                errorTabs << "PalmParasSetting";
            }
            else
            {
                successTabs << "PalmParasSetting";
            }

            if (ui->smoothPage->sonoParameters() != NULL && ui->smoothPage->isEnabled())
            {
                if (!ui->smoothPage->save())
                {
                    ret = false;
                    errorTabs << "SmoothSetting";
                }
                else
                {
                    successTabs << "SmoothSetting";
                }
            }

            if (ui->bpreprocessPage->sonoParameters() != NULL)
            {
                if (!ui->bpreprocessPage->save())
                {
                    ret = false;
                    errorTabs << "BPreProcessSetting";
                }
                else
                {
                    successTabs << "BPreProcessSetting";
                }
            }

            if (ui->btxsettingPage->sonoParameters() != NULL)
            {
                if (!ui->btxsettingPage->save())
                {
                    ret = false;
                    errorTabs << "BTXSetting";
                }
                else
                {
                    successTabs << "BTXSetting";
                }
            }

            if (ui->bmidpostprocessPage->sonoParameters() != NULL)
            {
                if (!ui->bmidpostprocessPage->save())
                {
                    ret = false;
                    errorTabs << "BMidPostprocess";
                }
                else
                {
                    successTabs << "BMidPostprocess";
                }
            }

            if (ui->analogCWSettingPage->sonoParameters() != NULL)
            {
                if (!ui->analogCWSettingPage->save())
                {
                    ret = false;
                    errorTabs << "AnalogCWSetting";
                }
                else
                {
                    successTabs << "AnalogCWSetting";
                }
            }
        }

        m_ProbeDataSet->notifyChanged();
        if (ret)
        {
            MessageBoxFrame::setTipShowTime(3000);
            QString text;
            for (int i = 0; i < successTabs.count(); i++)
            {
                text += successTabs.at(i);
                if (i < (successTabs.count() - 1))
                {
                    if (i % 4 == 0 && i != 0)
                    {
                        text += "\n";
                    }
                    else
                    {
                        text += ", ";
                    }
                }
            }
            MessageBoxFrame::tipInformation(QString("%1 \nhave been saved successfully!").arg(text));
        }
        else
        {
            MessageBoxFrame::tipInformation(QString("%1 \n Save failed!").arg(errorTabs.join(", ")));
        }
        //解决preset界面下修改预设并保存后，点击LineEdit不弹出虚拟键盘的问题
        m_Frame->activateWindow();
    }
}

void PresetModeWidget::changeScpdControlEnabled()
{
    ui->commonTab->groupWidget("Common")->setSliderEnabled(QStringList(BFPNames::ScpdStr),
                                                           !m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr));
}

void PresetModeWidget::controlColorFSTab()
{
    bool cFreqSettingEnable = false;
    bool pdFreqSettingEnable = false;
    bool snFreqSettingEnable = false;
    bool tdiFreqSettingEnable = false;
    bool eFreqSettingEnable = false;
    bool mviFreqSettingEnable = false;

    if (pBV(BFPNames::TDIEnStr))
    {
        tdiFreqSettingEnable = true;
    }
    else if (pBV(BFPNames::MVIModeStr))
    {
        mviFreqSettingEnable = true;
    }
    else if (pBV(BFPNames::ElastoEnStr))
    {
        eFreqSettingEnable = true;
    }
    else if (pIV(BFPNames::ColorImageModeStr) == Color_PD)
    {
        if (pBV(BFPNames::SonoNeedleStr))
        {
            snFreqSettingEnable = true;
        }
        else
        {
            pdFreqSettingEnable = true;
        }
    }
    else
    {
        cFreqSettingEnable = true;
    }

    ui->cFreqSettingTab->setEnabled(cFreqSettingEnable);
    ui->pdFreqSettingTab->setEnabled(pdFreqSettingEnable);
    ui->snFreqSettingTab->setEnabled(snFreqSettingEnable);
    ui->tdiFreqSettingTab->setEnabled(tdiFreqSettingEnable);
    ui->eFreqSettingTab->setEnabled(eFreqSettingEnable);
    ui->mviFreqSettingTab->setEnabled(mviFreqSettingEnable);

    // B和E模式fs设置有参数共用,需要设置相互使能
    ui->freqSettingTab->setEnabled(!pBV(BFPNames::ElastoEnStr));
}

void PresetModeWidget::controlDopFSTab()
{
    if (pBV(BFPNames::TDIEnStr))
    {
        ui->dFreqSettingTab->setEnabled(false);
        ui->dTDIOnFreqSettingTab->setEnabled(true);
        ui->cwdFreqSettingTab->setEnabled(false);
        ui->analogCWSettingPage->setEnabled(false);
    }
    else if (pBV(BFPNames::CWEnStr) && !ModelConfig::instance().value(ModelConfig::HPrfCW, false).toBool())
    {
        ui->dFreqSettingTab->setEnabled(false);
        ui->dTDIOnFreqSettingTab->setEnabled(false);
        ui->cwdFreqSettingTab->setEnabled(true);
        ui->analogCWSettingPage->setEnabled(true);
    }
    else
    {
        ui->dFreqSettingTab->setEnabled(true);
        ui->dTDIOnFreqSettingTab->setEnabled(false);
        ui->cwdFreqSettingTab->setEnabled(false);
        ui->analogCWSettingPage->setEnabled(false);
    }
}

void PresetModeWidget::controlNeedleTab()
{
    // Needle和C模式fs设置有参数共用,需要设置相互使能
    bool isNeedleMode = pBV(BFPNames::NeedleModeStr);
    ui->needleSettingTab->setEnabled(isNeedleMode);
    ui->cFreqSettingTab->setEnabled(!isNeedleMode);
}

/**
 * @brief combobox新增项注意事项
 *
 * ------------------------------------------------------------------------------------
 * comboBox，stackedWidget，m_SonoClients同时新增，并且要保证索引一致
 * m_SonoClients是和超声参数相关，新增的功能如果不继承ISonoParametersClient，则要放到最后
 * ------------------------------------------------------------------------------------
 */
void PresetModeWidget::on_comboBox_currentIndexChanged(int index)
{
    if (index >= 0 && index < ui->comboBox->count() && index < m_SonoClients.count())
    {
        int image_page_index = ui->comboBox->findText(tr("Line iImage"));
        int image_cv_page_index = ui->comboBox->findText(tr("Line iImage CV"));
        if (index == image_page_index)
        {
            m_SonoParameters->setPV(BFPNames::LineiImageTypeStr, 0);
        }
        else if (index == image_cv_page_index)
        {
            m_SonoParameters->setPV(BFPNames::LineiImageTypeStr, 1);
        }

        ISonoParametersClient* client = m_SonoClients.at(index);
        if (client != NULL && client->sonoParameters() == NULL)
        {
            bool ctlFreeze = false;
            if (!m_SonoParameters->pBV(BFPNames::FreezeStr))
            {
                ctlFreeze = true;
                m_SonoParameters->setPV(BFPNames::FreezeStr, true);
            }

            client->setSonoParameters(m_SonoParameters);

            if (ctlFreeze)
            {
                m_SonoParameters->setPV(BFPNames::FreezeStr, false);
            }
        }
    }
}

void PresetModeWidget::onECGVelocityChanged()
{
    QSettings setting(Resource::modelSettingFileName(), QSettings::IniFormat);
    setting.beginGroup("ECGDelayTime");
    QString name = QString("BECGDelayTime%1").arg(m_SonoParameters->pIV(BFPNames::ECGVelocityStr));
    ui->commonTab->uiUpdate("SOFTWARE", BFPNames::BECGDelayTimeStr,
                            m_SonoParameters->parameter(BFPNames::BECGDelayTimeStr)->showValue());
    setting.endGroup();
}

void PresetModeWidget::setCommonBoxIndex(int index)
{
    int modelId = AppSetting::modelID();
    Q_UNUSED(modelId);
    if (ui->comboBox->count() > index)
    {
        ui->comboBox->setCurrentIndex(index);
    }
}

void PresetModeWidget::setBfreqSettingTabIndex(int index)
{
    ui->freqSettingTab->setComBoxIndex(index);
}

void PresetModeWidget::setToolsTabComBoxIndex(int index)
{
    ui->toolsTab->setComBoxIndex(index);
}

void PresetModeWidget::setProbeParaComBoxIndex(int index)
{
    ui->probeParaSettingTab->setComboxIndex(index);
}

void PresetModeWidget::showDataAcquisitonDialog()
{
    ui->toolsTab->showDataAcquisitonDialog();
}

void PresetModeWidget::setSubSystemSubject(USFObject* value)
{
    m_ProbeCodeBurnerSettingWidget->setUSFObject(value);
    m_VirtualProbeSettingWidget->setUSFObject(value);
}

void PresetModeWidget::setDiskDevice(IDiskDevice* diskDevice)
{
    ui->bpreprocessPage->setDiskDevice(diskDevice);
    ui->btxsettingPage->setDiskDevice(diskDevice);
    ui->toolsTab->setDiskDevice(diskDevice);
    ui->apConfigTab->setDiskDevice(diskDevice);
    ui->freqSettingTab->setDiskDevice(diskDevice);
}

void PresetModeWidget::setProbeDataSet(IProbeDataSet* value)
{
    m_ProbeDataSet = value;

    ui->toolsTab->setProbeDataSet(value);
    ui->probeParaSettingTab->setProbeDataSet(value);
    ui->freqSettingTab->setProbeDataSet(value);
    ui->bpreprocessPage->setProbeDataSet(value);
    ui->btxsettingPage->setProbeDataSet(value);
    ui->needleSettingTab->setProbeDataSet(value);
    ui->palmParasSettingTab->setProbeDataSet(value);
    ui->cFreqSettingTab->setProbeDataSet(value);
    ui->cwdFreqSettingTab->setProbeDataSet(value);
    ui->dFreqSettingTab->setProbeDataSet(value);
}

void PresetModeWidget::setPresetTools(PresetTools* presetTools)
{
    m_PresetTools = presetTools;
    ui->toolsTab->setPresetTools(presetTools);
}

void PresetModeWidget::setImageInterface(IImageInterfaceForExam* imageInterface)
{
    ui->AutoFramePage->setImageInterface(imageInterface);
}

void PresetModeWidget::initDebuggingToolPage()
{
    PresetModeParametersGroups groups;

    QStringList fpgaTestStrs = QStringList()
                               << BFPNames::TestSignalStr << BFPNames::TestParameterStr << BFPNames::DebugPara0Str
                               << BFPNames::DebugPara1Str << BFPNames::DebugPara2Str << BFPNames::DebugPara3Str
                               << BFPNames::DebugPara5Str << BFPNames::DebugPara6Str << BFPNames::DebugPara7Str
                               << BFPNames::DebugPara8Str << BFPNames::DebugPara9Str;

    groups.append(PresetModeParametersGroup(tr("FPGA Test"), m_SonoParameters->parameters(fpgaTestStrs)));

    QStringList fanTestStrs = QStringList() << BFPNames::SystemFanControlStr << BFPNames::CPUFanControlStr
                                            << BFPNames::BottomFanControlStr;

    groups.append(PresetModeParametersGroup(tr("Fan Control"), m_SonoParameters->parameters(fanTestStrs)));

    ui->debuggingToolPage->setModel(groups);

    m_VirtualProbeSettingWidget = new VirtualProbeSettingWidget(this);
    ui->debuggingToolPage->addPage(tr("Virtual Probe Code"), m_VirtualProbeSettingWidget);

    m_ProbeCodeBurnerSettingWidget = new ProbeCodeBurnerSettingWidget(this);
    ui->debuggingToolPage->addPage(tr("Probe Code Burning"), m_ProbeCodeBurnerSettingWidget);
}

void PresetModeWidget::initProbeSettingPage()
{
    PresetModeParametersGroups groups;

    //探头基元
    QStringList probeElemStrs = QStringList()
                                << BFPNames::PROB_ELEM_NUMStr << BFPNames::PB_PitchStr << BFPNames::ConvexRadiusStr;

    QStringList probeElemLabelStrs = QStringList()
                                     << BFPNames::PROB_ELEM_NUMStr << BFPNames::PB_PitchStr << tr("Convex Radius");

    QHash<QString, QString> probeElemLabels;
    for (int i = 0; i < probeElemStrs.count(); i++)
    {
        probeElemLabels[probeElemStrs[i]] = probeElemLabelStrs[i];
    }

    groups.append(
        PresetModeParametersGroup(tr("Probe Elements"), m_SonoParameters->parameters(probeElemStrs), probeElemLabels));

    //探头高压开关
    QStringList ShvswStrs = QStringList() << BFPNames::SHVSW_NOStr << BFPNames::EHVSW_NOStr << BFPNames::HVSW_NUMStr
                                          << BFPNames::HVSW_ConfigStr << BFPNames::MEProbStr;
    groups.append(PresetModeParametersGroup(tr("Probe High Voltage Switch"), m_SonoParameters->parameters(ShvswStrs)));

    //探头通道
    QStringList ChannelStrs = QStringList() << BFPNames::ChannelIdentifier_128Str << BFPNames::ChannelIdentifierStr;

    QStringList ChannelLabelStrs = QStringList() << tr("128 Channel Identifier") << tr("64 Channel Identifier");

    QHash<QString, QString> ChannelLabels;
    for (int i = 0; i < ChannelStrs.count(); i++)
    {
        ChannelLabels[ChannelStrs[i]] = ChannelLabelStrs[i];
    }

    groups.append(
        PresetModeParametersGroup(tr("Probe Channel"), m_SonoParameters->parameters(ChannelStrs), ChannelLabels));

    //多维探头标识
    QStringList DimensionXProbeStrs = QStringList()
                                      << BFPNames::OneDimensionXProbeFlagStr
                                      << BFPNames::OneDimensionXProbeOperationModeStr << BFPNames::WaferLengthYStr;

    QStringList DimensionXProbeLabelStrs = QStringList() << tr("1.x Probe Flag") << tr("1.x Probe Operation Mode")
                                                         << tr("Y Direction Element Spacing");

    QHash<QString, QString> DimensionXProbeLabels;
    for (int i = 0; i < DimensionXProbeLabelStrs.count(); i++)
    {
        DimensionXProbeLabels[DimensionXProbeStrs[i]] = DimensionXProbeLabelStrs[i];
    }

    groups.append(PresetModeParametersGroup(tr("Multi-Dimensional Probe"),
                                            m_SonoParameters->parameters(DimensionXProbeStrs), DimensionXProbeLabels));
    ui->probeSettingPage->setModel(groups);

    //曹三-暂时可不支持可调及保存
    ui->probeSettingPage->groupWidget(tr("Probe Elements"))->setSliderEnabled(probeElemStrs, false);
    ui->probeSettingPage->groupWidget(tr("Probe High Voltage Switch"))->setSliderEnabled(ShvswStrs, false);
    ui->probeSettingPage->groupWidget(tr("Probe Channel"))->setSliderEnabled(ChannelStrs, false);
    ui->probeSettingPage->groupWidget(tr("Multi-Dimensional Probe"))->setSliderEnabled(DimensionXProbeStrs, false);
}
