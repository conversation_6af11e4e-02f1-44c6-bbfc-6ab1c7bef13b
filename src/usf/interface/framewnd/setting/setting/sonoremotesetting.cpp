#include "sonoremotesetting.h"
#include "ui_sonoremotesetting.h"
#include "sonoremoteeditwidget.h"
#include "uiutil.h"
#include "generalinfo.h"
#include "setting.h"
#include "appsetting.h"
#include "feedbackwidget.h"
#include <QSettings>
#include "resource.h"

SonoRemoteSetting::SonoRemoteSetting(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::SonoRemoteSetting)
    , m_ContentForCode(Setting::instance().defaultsBak().getWebSite())
    , m_HotLineText(Setting::instance().defaultsBak().getHotLine())
    , m_SoftWareVersion(GeneralInfo::instance().softWareVersion())
    , m_SerialNumber(GeneralInfo::instance().serialNumber())
    , m_EquipmentModel("SonoAir")
    , m_FileName(Resource::remoteIniName)
{
    createWidget();
    this->setAttribute(Qt::WA_StyledBackground, true);
}

SonoRemoteSetting::~SonoRemoteSetting()
{
    delete ui;
}

void SonoRemoteSetting::createWidget()
{
    if (m_isCreated)
    {
        return;
    }
    m_isCreated = true;
    ui->setupUi(this);
    ui->pushButton_2->setChecked(currentOnOff());
    connect(ui->pushButton_2, &SwitchButton::checkedChanged, this, &SonoRemoteSetting::switchFeedBackOnOff);
    updatePage();
    initBeforeCreateFile();
}

QString SonoRemoteSetting::title() const
{
    return this->windowTitle();
}

void SonoRemoteSetting::reset()
{
    updatePage();
    m_IsFeedBackStart = currentOnOff();
    ui->pushButton_2->setChecked(currentOnOff());
}

void SonoRemoteSetting::save()
{
    QSettings setting(m_FileName, QSettings::IniFormat);
    setting.setValue("IsEnable", m_IsFeedBackStart);
    setting.setValue("WebSiteOfChison", m_ContentForCode);
    setting.setValue("HotLineOfChison", m_HotLineText);
}

void SonoRemoteSetting::cancel()
{
    reset();
}

void SonoRemoteSetting::setDefault()
{
    setContentOfQRCode(Setting::instance().defaultsBak().getWebSite().toStdString().c_str());
    setHotLineContent(Setting::instance().defaultsBak().getHotLine());
    ui->pushButton_2->setChecked(true);
    m_IsFeedBackStart = true;
}

void SonoRemoteSetting::setContentOfQRCode(const char* text)
{
    m_ContentForCode = text;
    m_CodeImage = UiUtil::creatQRCode(text);
    updateQRCodeLabel();
}

void SonoRemoteSetting::setHotLineContent(QString text)
{
    m_HotLineText = text;
    updateHotlineLabel();
}

void SonoRemoteSetting::switchFeedBackOnOff(bool value)
{
    m_IsFeedBackStart = value;
}

void SonoRemoteSetting::updatePage()
{
    QSettings setting(m_FileName, QSettings::IniFormat);
    setContentOfQRCode(setting.value("WebSiteOfChison").toString().toStdString().c_str());
    setHotLineContent(setting.value("HotLineOfChison").toString());
}

bool SonoRemoteSetting::currentOnOff()
{
    QSettings setting(m_FileName, QSettings::IniFormat);
    bool isStart = setting.value("IsEnable").toBool();
    return isStart;
}

void SonoRemoteSetting::initBeforeCreateFile()
{
    QFile file(m_FileName);
    if (!file.exists())
    {
        ui->pushButton_2->setChecked(true);
        setContentOfQRCode(Setting::instance().defaultsBak().getWebSite().toStdString().c_str());
        setHotLineContent(Setting::instance().defaultsBak().getHotLine());
    }
}

bool SonoRemoteSetting::createFile()
{
    QFile file(Resource::remoteIniName);
    if (file.exists())
    {
        return false;
    }
    if (!file.exists())
    {
        QSettings setting(Resource::remoteIniName, QSettings::IniFormat);
        setting.setValue("IsEnable", true);
        setting.setValue("FeedBcakStartDate", "0");
        setting.setValue("IsFirstTimeFeedBack", true);
        setting.setValue("WebSiteOfChison", Setting::instance().defaultsBak().getWebSite());
        setting.setValue("HotLineOfChison", Setting::instance().defaultsBak().getHotLine());
        return true;
    }
    return false;
}

void SonoRemoteSetting::feedBackOfSatisfaction()
{
    QSettings setting(Resource::remoteIniName, QSettings::IniFormat);
    if (createFile())
    {
        setting.setValue("FeedBcakStartDate", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
        FeedbackDialog fw;
        fw.exec();
    }
    if (setting.value("IsEnable").toBool())
    {
        QDateTime startTime =
            QDateTime::fromString(setting.value("FeedBcakStartDate").toString(), "yyyy-MM-dd hh:mm:ss");
        QDateTime currentTime = QDateTime::currentDateTime();
        bool isFirst = setting.value("IsFirstTimeFeedBack").toBool();
        //此后，至少距离上一次弹出6个月之后，弹出反馈对话框
        if (!isFirst && startTime.daysTo(currentTime) > 183)
        {
            setting.setValue("FeedBcakStartDate", currentTime.toString("yyyy-MM-dd hh:mm:ss"));
            FeedbackDialog fw;
            fw.exec();
        }
        //距离出厂日期至少满三个月后，第一次弹出反馈对话框
        if (isFirst && startTime.daysTo(currentTime) > 91)
        {
            setting.setValue("FeedBcakStartDate", currentTime.toString("yyyy-MM-dd hh:mm:ss"));
            setting.setValue("IsFirstTimeFeedBack", false);
            FeedbackDialog fw;
            fw.exec();
        }
    }
}

void SonoRemoteSetting::retranslateUi()
{
    ui->retranslateUi(this);
}

void SonoRemoteSetting::showEvent(QShowEvent* event)
{
    updateQRCodeLabel();
    updateHotlineLabel();
    BaseWidget::showEvent(event);
}

void SonoRemoteSetting::updateQRCodeLabel()
{
    if (!this->isVisible())
        return;

    int width = ui->label_code->width() / 5;
    if (!m_CodeImage.isNull())
    {
        m_CodeImage = m_CodeImage.scaled(width, width, Qt::IgnoreAspectRatio);
        ui->label_code->setPixmap(QPixmap::fromImage(m_CodeImage));
    }
}

void SonoRemoteSetting::updateHotlineLabel()
{
    ui->label_hotline->setText(tr("Customer service hotline: ") + m_HotLineText);
}

void SonoRemoteSetting::on_pushButton_clicked()
{
    SonoRemoteEditDialog dlg(this);
    if (dlg.exec() == QDialog::Accepted)
    {
        if (!dlg.text_HotLine().isEmpty())
        {
            setHotLineContent(dlg.text_HotLine());
        }
        if (!dlg.text_WebSite().isEmpty())
        {
            if (dlg.text_WebSite().contains("http"))
            {
                setContentOfQRCode(dlg.text_WebSite().toStdString().c_str());
            }
            else
            {
                setContentOfQRCode(("https://" + dlg.text_WebSite()).toStdString().c_str());
            }
        }
    }
}
