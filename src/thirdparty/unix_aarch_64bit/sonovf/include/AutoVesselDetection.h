#pragma once

#define EXPORT_API __attribute__((visibility("default")))

extern "C"
{

    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /**
     * @brief The BFlow struct 血管检测算法的返回结果
     */
    struct VesselData
    {
        /**
         * @brief Angle 血管角度
         */
        int Angle = 0;
        /**
         * @brief CenterX 血管中心点X坐标
         */
        int CenterX = 0;
        /**
         * @brief CenterY 血管中心点Y坐标
         */
        int CenterY = 0;
        /**
         * @brief WallX 血管前壁X坐标
         */
        int WallNearX = 0;
        /**
         * @brief WallY 血管前壁Y坐标
         */
        int WallNearY = 0;
        /**
         * @brief WallFarX 血管后壁X坐标
         */
        int WallFarX = 0;
        /**
         * @brief WallFarY 血管后壁Y坐标
         */
        int WallFarY = 0;
        /**
         * @brief DetectedNum 检测血管数目(取值为0或1)
         */
        int DetectedNum = 0;
        /**
         * @brief DetectedNum 检测血管数目(取值为0或1)
         */
        // int error_message = 0;
    };
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////// 调用方式一，与Android平台接口一致
    ///**
    //*	@brief	模型初始化接口，传入模型权重文件路径
    //*	@param	modelpath(const char*): 模型文件权重路径
    //*/
    // EXPORT_API void createViewontext(const char* modelpath);
    ///**
    //*	@breif	模型释放接口
    //*/
    // EXPORT_API void removeViewontext();
    ///**
    //*	@brief 模型推理接口，逐帧调用，每帧传入8bit图像数据，执行推理，返回远点，近点，角度信息等 *	@param
    // imgin(unsigned char*):		传入8 bits 图像数据,只传灰度图即可 *	@param	width(int):
    //传入图像数据的宽度 *	@param	height(int):				传入图像数据的高度 *	@param	pointX(int):
    //传入点的x坐标 *	@param	pointY(int):				传入点的y坐标 *	@param	centroid_mode(int):
    //传入模式参数 *	@return VesselData:					以结构体形式返回模型推理结果，结构体见以上定义
    //*/
    // EXPORT_API VesselData vesseldetection(unsigned char* imgin, int width, int height, int pointX, int pointY, int
    // centroid_mode);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////					    调用方式二，保持与windows平台其他算法功能一致
    /**
     *	@brief						模型初始化接口，传入模型预设值，调用成功后，可调用推理接口执行推理
     *	@param	w(int):				传入推理模型宽度，默认值：1024
     *	@param	h(int):				传入推理模型高度，默认值：1024
     *	@param	thresh(int):		传入推理模型阈值，阈值越小，识别灵敏度越好，但精度会降低。默认值：0.8
     *	@return						0:调用成功；
     *-1:调用失败（模型权重不符）模型权重文件放置至"D:\\USExtensionPackage\\vessel_bflow\\sinet_1024_3104_10.mnn"，与其他人工智能模型权重路径一致
     */
    EXPORT_API int vesselBFlow_initdetect(int w, int h, float thresh, const char* segModelPath);

    /**
     *	@brief								模型推理接口，传入图像数据，进行推理并返回推理结果
     *	@param	imgin(unsigned char*):		传入8 bits 图像数据,只传灰度图即可
     *	@param	width(int):					传入图像数据的宽度
     *	@param	height(int):				传入图像数据的高度
     *	@param	pointX(int):				传入点的x坐标
     *	@param	pointY(int):				传入点的y坐标
     *	@param	centroid_mode(int):			传入模式参数
     *	@param	vesOut(VesselData*):		以结构体指针形式传出模型推理结果，结构体见以上定义
     *	@return								返回识别个数，0:没识别成功；  1:识别成功
     */
    EXPORT_API int vesselBFlow_detect(unsigned char* imgin, int width, int height, int pointX, int pointY,
                                      int centroid_mode, VesselData* vesOut);

    /**
     *	@brief	模型释放接口
     */
    EXPORT_API void vesselBFlow_release();
}
