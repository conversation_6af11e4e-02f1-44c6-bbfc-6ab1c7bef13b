#pragma once

#include "zeusinfostruct.h"
#include "stddef.h"
#ifdef _WIN32
#define ZEUS_API __declspec(dllexport)
#else // UNIX (GCC) // __APPLE__
#define ZEUS_API
#endif

extern "C"
{

    ZEUS_API void* ZeusCreate(const CreationInfo* const creationInfo);
    ZEUS_API void ZeusRelease(void* context);

    ZEUS_API void ZeusPushDataForPostProcess(void* context, DataForPostProcess* data, int dataType, int dataCount,
                                             InfoForPostProcess info);

    ZEUS_API void ZeusSetPostProcessDataCallBack(void* context, ZeusPostProcessDataCallBack callback, void* userData);
    ZEUS_API void ZeusSetPostProcessParaCallBack(void* context, PostProcessParaCallBack callback, void* userData);
    ZEUS_API void ZeusSetPostProcessPropCallBack(void* context, PostProcessPropCallBack callback, void* userData);
    ZEUS_API void ZeusSetExternalFunction(void* context, ExternalFunction function, int functionType, void* userData);
    ZEUS_API void ZeusPushDataForPreProcess(void* context, DataForPreProcess* data, int dataType, int dataCount,
                                            InfoForPostProcess* info);
    ZEUS_API void ZeusSetPreProcessDataCallBack(void* context, PreProcessDataCallBack callback, void* userData);

    ZEUS_API void ZeusSetParameterInt(void* context, const char* const name, int value);
    ZEUS_API void ZeusSetParameterFloat(void* context, const char* const name, float value);
    ZEUS_API void ZeusSetParameterString(void* context, const char* const name, const char* const value);
    ZEUS_API void ZeusSetParameterBool(void* context, const char* const name, bool value);
    ZEUS_API void ZeusSetParameterPointer(void* context, const char* const name, void* value, size_t size);
    ZEUS_API void ZeusSetParameterTrigger(void* context, const char* const name);
    ZEUS_API int ZeusGetParameterInt(void* context, const char* const name, int& value);
    ZEUS_API int ZeusGetParameterFloat(void* context, const char* const name, float& value);
    ZEUS_API int ZeusGetParameterString(void* context, const char* const name, char* value);
    ZEUS_API int ZeusGetParameterBool(void* context, const char* const name, bool& value);

    ZEUS_API bool ZeusExportProperties(void* context, const char* const path);
    ZEUS_API bool ZeusLoadProperties(void* context, const char* const path);
}
