#pragma once

typedef struct
{
public:
    char* Data;
    int DataWidth;
    int DataHeight;
    int Steering;
    int SyncId;
} DataForPostProcess;

typedef struct infoForPostProcess
{
    infoForPostProcess()
    {
        for (int i = 0; i < 32; i++)
        {
            info[i] = 0;
        }
    };
    int info[32];
} InfoForPostProcess;

typedef struct
{
    char* Data;
    int DataPointNum;
    int DataLineNum;
    int PointBytesCount;
} DataForPreProcess;

typedef struct
{
    int ImageWidth;
    int ImageHeight;
    void* UserData;
    bool IsMultiOutput;
    int CVIEType;
    bool NetworkEncrypted;
    bool Use16BitPW;
    char* MRDLineDataCacheFilePath;
    bool IsNeedInitOpenGL;
    char* ResPath;
    bool IsSupportPreProcess;
} CreationInfo;

typedef struct
{
    int Type;
    int Length;
    unsigned char* Data;
} AdditionalData;

typedef struct
{
    void* Data;
    int Width;
    int Height;
    int DataType;
    int BitWidth;
    AdditionalData additionalData[3];
    int additionalDataCount;
} CallbackInfo;

typedef enum
{
    TypeInt,
    TypeFloat,
    TypeString,
    TypeBool,
    TypePointer,
    TypeTrigger
} ZeusPropType;

typedef enum
{
    DataTypeB = 0,
    // C
    DataTypeC = 1,
    // E,Dynf,Sflow
    DataTypeELike = 2,
    // CEUS
    DataTypeHarmonic = 3,
    // DualB
    DataTypeDualB = 4,
    // ATI
    DataTypeATI = 7,
    // T
    DataTypeT = 9,

    //-------wave----------
    // M
    DataTypeM = 128,
    // D,CWD
    DataTypePW = 129,
    // CM
    DataTypeCM = 130,
    // Swe2d-1
    DataTypeSwe2D_1 = 131,
    // Swe2d-2
    DataTypeSwe2D_2 = 132,
    // ECG
    DataTypeECG = 133,

    //--------4D--------
    DataType4D = 256

} ZeusDataType;

// FPGA上传的波束合成后的数据类型定义
typedef enum
{
    BData = 0x00,
    CData = 0x01,
    DData = 0x02,
    MData = 0x03,
    EData = 0x04,
    NData = 0x05,
    SData = 0x06,
    CWData = 0x07,
    SOUNDData = 0x08,
    TData = 0x09,

    PlaneBData = 0x80,
    DynamicFlow = 0x81,

    TYPE_MAX
} BFDataType;

typedef enum
{
    FilterCPD = 0,
    PhaseTHI = 1,
    Steering = 2,
    DataSyncID = 3,
    SurfaceNo = 4,
    FourDInfo = 5,
    PWGateNO = 6,
    PWDoplarStartPoint = 7,

    DataFrameIndex = 20,
    DumpDataIndex = 25,
} BFMateInfoType;

typedef enum
{
    ExternalOptimation,
    RawDataForB
} ZeusExternalFunctionType;

typedef enum
{
    Data2D = 0,
    DataPW = 1,
    DataM = 2,
    Data4D = 3,
    DataBBC_B = 4,
    DataSwe = 5,
    DataECG = 6
} ZeusOutputDataType;

typedef enum
{
    AdditionalDataNull = 0,
    AdditionalDataATI = 1,
    AdditionalDataLung = 2,
    AdditionalDataAnalysis = 3,
} AdditionalDataType;

typedef enum
{
    LogDebugOff = 0,
    LogDebugAll,
    LogDebugReceive,
    LogDebugPush,
    LogDebugHeadInfo,
    LogDebugSWE,
    LogDebugFourd
} ZeusDebugFlagEnum;

typedef void (*ZeusPostProcessDataCallBack)(void* UserData, CallbackInfo callbackInfo, InfoForPostProcess info,
                                            int syncId);
typedef void (*PostProcessParaCallBack)(void* userData, const char* content);
typedef void (*PostProcessPropCallBack)(void* userData, const char* name, void* value, int type);
typedef void (*ExternalFunction)(void* userData, void* data, int width, int height, InfoForPostProcess info);
typedef void (*PreProcessDataCallBack)(void* userData, CallbackInfo callbackInfo, InfoForPostProcess info);
