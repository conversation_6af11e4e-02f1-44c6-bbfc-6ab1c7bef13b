#!/bin/sh

touch /tmp/chison_boot_flag
export DISPLAY=:0

# 设置LD_LIBRARY_PATH以及其他环境变量
export QT515_HOME=/opt/Qt5.15.2/
export LD_LIBRARY_PATH="lib:$QT515_HOME/lib:/usr/lib/aarch64-linux-gnu/:/mnt/overlay/ro/root/usr/lib/aarch64-linux-gnu"
export DCMDICTPATH=res/dicom/dicom.dic
export QT_QPA_PLATFORM_PLUGIN_PATH=$QT515_HOME/plugins
export QT_QPA_PLATFORM=xcb

# 允许任何用户连接到X服务器
xhost +

xinput set-prop "hid-over-i2c 36B6:C001 Touchpad" "libinput Tapping Enabled" 1

./fixedfrequency.sh

kill $(fuser -v /dev/hwctrlpcie 2>/dev/null | tr -s ' ' | cut -d ' ' -f 2)

ulimit -s 16384
ulimit -c unlimited
echo 2 > /proc/sys/fs/suid_dumpable
mkdir /var/tmp/corefiles
chmod 777 /var/tmp/corefiles
echo "/var/tmp/corefiles/core-%e">/proc/sys/kernel/core_pattern

./xultrasound
#gdb ./xultrasound
#valgrind --log-file=./leaktrace.log --leak-check=full ./xultrasound
#valgrind --log-file=./leaktrace.log --leak-check=full --show-leak-kinds=all ./xultrasound