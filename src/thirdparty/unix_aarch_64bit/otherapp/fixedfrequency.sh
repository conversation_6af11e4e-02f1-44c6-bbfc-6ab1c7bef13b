#!/bin/sh

# 请切换到root用户
# CPU定频
echo "CPU0-3可用频率/CPU6-7 available frequency:"
cat /sys/devices/system/cpu/cpufreq/policy0/scaling_available_frequencies
#echo userspace > /sys/devices/system/cpu/cpufreq/policy0/scaling_governor
echo performance > /sys/devices/system/cpu/cpufreq/policy0/scaling_governor
#echo 1800000 > /sys/devices/system/cpu/cpufreq/policy0/scaling_setspeed
echo "CPU0-3当前频率/CPU0-3 current frequency:"
cat /sys/devices/system/cpu/cpufreq/policy0/cpuinfo_cur_freq

echo "CPU4-5可用频率/CPU6-7 available frequency:"
cat /sys/devices/system/cpu/cpufreq/policy4/scaling_available_frequencies
#echo userspace > /sys/devices/system/cpu/cpufreq/policy4/scaling_governor
echo performance > /sys/devices/system/cpu/cpufreq/policy4/scaling_governor
#echo 2400000 > /sys/devices/system/cpu/cpufreq/policy4/scaling_setspeed
echo "CPU4-5 当前频率/CPU4-5 current frequency:"
cat /sys/devices/system/cpu/cpufreq/policy4/cpuinfo_cur_freq


echo "CPU6-7可用频率:/CPU6-7 available frequency"
cat /sys/devices/system/cpu/cpufreq/policy6/scaling_available_frequencies
#echo userspace > /sys/devices/system/cpu/cpufreq/policy6/scaling_governor
echo performance > /sys/devices/system/cpu/cpufreq/policy6/scaling_governor
#echo 2400000 > /sys/devices/system/cpu/cpufreq/policy6/scaling_setspeed
echo "CPU6-7 当前频率/CPU6-7 current frequency:"
cat /sys/devices/system/cpu/cpufreq/policy6/cpuinfo_cur_freq

# NPU定频
echo "NPU可用频率/NPU available frequency:"
cat /sys/class/devfreq/fdab0000.npu/available_frequencies    
echo userspace > /sys/class/devfreq/fdab0000.npu/governor
echo 1000000000 > /sys/class/devfreq/fdab0000.npu/userspace/set_freq
echo "NPU当前频率/NPU current frequency:"
cat /sys/class/devfreq/fdab0000.npu/cur_freq

# GPU定频
echo "GPU可用频率/GPU available frequency:"
cat /sys/class/devfreq/fb000000.gpu/available_frequencies
echo performance > /sys/class/devfreq/fb000000.gpu/governor
echo "GPU当前频率/GPU current frequency:"
cat /sys/class/devfreq/fb000000.gpu/cur_freq

ln -s /usr/lib/aarch64-linux-gnu/libmali.so /usr/lib/libOpenCL.so
