#pragma once
// ¹¦ÄÜº¯Êı
//#include "Vesselsegment.h"

// ¶¨ÒåÒ»¸öµ¼³öºê£¬ÓÃÀ´¿ØÖÆ¿âº¯ÊıµÄµ¼³öºÍµ¼Èë
// µ±±àÒë¿âÊ±£¬AUTOBFLOW_EXPORTS½«±»¶¨Òå
// ´ËÊ±£¬__declspec(dllexport)±»Ìí¼Óµ½´ËºêÖĞ
// ÕâÔÊĞí±àÒëÆ÷ÖªµÀÎÒÃÇÕıÔÚ´´½¨Ò»¸ö.DLL£¬²¢½«¿âÖĞµÄº¯ÊıºÍÀàµ¼³öµ½.DLL
// µ±×÷Îª¿Í»§¶ËÊ¹ÓÃ¿âÊ±£¬AUTOBFLOW_EXPORTS½«Î´±»¶¨Òå£¬
// ´ËÊ±£¬__declspec(dllimport)½«±»Ìí¼Óµ½´Ëºê£¬¸æËß±àÒëÆ÷´Ó.DLLÖĞµ¼Èëº¯ÊıºÍÀà

#ifndef EXPORT_API
#if defined(_MSC_VER)
#if defined(AUTOEF_EXPORTS) // Create a DLL library
#define EXPORT_API __declspec(dllexport)
#else // Use a DLL library
#define EXPORT_API __declspec(dllimport)
#endif
#else
#define EXPORT_API __attribute__((visibility("default")))
#endif
#endif

typedef struct
{
    int centerX;    // ¿¿¿¿X¿¿¿¿¿¿
    int centerY;    // ¿¿¿¿Y¿¿¿¿¿¿
    int lowerEdgeX; // ¿¿¿X¿¿¿¿¿¿¿¿¿
    int lowerEdgeY; // ¿¿¿Y¿¿¿¿¿¿¿¿¿
    float angle;    // ¿¿¿¿
} VessPosAng;

typedef struct
{
    int x;      // ¿¿¿¿x¿¿
    int y;      // ¿¿¿¿y¿¿
    int width;  // ¿¿
    int height; // ¿¿
} ValidROI;

// ¶¨ÒåVDHandle¾ä±ú
typedef void* VDHandle;

extern "C"
{
    /**
     * @brief »ñÈ¡Ñª¹Ü¼ì²âÆ÷µÄ°æ±¾ĞÅÏ¢
     *
     * ¸Ãº¯ÊıÓÃÓÚ»ñÈ¡µ±Ç°Ñª¹Ü¼ì²âÆ÷µÄ°æ±¾ĞÅÏ¢£¬²¢½«Æä´æ´¢ÔÚÌá¹©µÄ»º³åÇøÖĞ¡£
     *
     * @param version: Ö¸Ïò´æ´¢°æ±¾×Ö·û´®µÄ»º³åÇøÖ¸Õë¡£
     * @param bufferSize: »º³åÇøµÄ´óĞ¡£¬È·±£°æ±¾×Ö·û´®¿ÉÒÔÍêÈ«´æ´¢¡£
     * @return ³É¹¦·µ»Ø ERROR_CODE_SUCCESS£¬Ê§°ÜÔò·µ»ØÏàÓ¦µÄ´íÎóÂë¡£
     */
    EXPORT_API int getVesselDetectorVersion(char* version, int bufferSize);

    /******************************************************************
    º¯ÊıÃû£ºinitVesselDetectionModel
    ¹¦ÄÜÃèÊö£º³õÊ¼»¯Ñª¹Ü¼ì²âÄ£ĞÍ£¬¼ÓÔØÔ¤ÑµÁ·Ä£ĞÍ²¢ÉèÖÃÏà¹Ø²ÎÊı¡£
    ²ÎÊı£º
        const char* modelPath                        [IN]: Ô¤ÑµÁ·Ä£ĞÍÎÄ¼şÂ·¾¶
        float thresh                                 [IN]: ¼ì²âãĞÖµ
        float avgPixelValue                          [IN]: Æ½¾ùÏñËØÖµ£¬ÓÃÓÚ¼ÆËãµ±Ç°Ö¡ÓëÉÏÒ»Ö¡µÄÆ½¾ùÏñËØÖµ
        float pointThreshold                         [IN]: ¸üĞÂµãµÄãĞÖµ£¬ÓÃÓÚÈ·¶¨ºÎÊ±¸üĞÂ¼ì²â½á¹û
    ·µ»ØÖµ£ºERROR_CODE_SUCCESS(³É¹¦), ERROR_CODE_UNKNOWN(³öÏÖÒì³£)
    ¸üĞÂÈÕÖ¾£º
        - 2024-08-12: ĞÂÔö²ÎÊı avgPixelValue ºÍ pointThreshold£¬ÓÃÓÚÖ§³Ö¸ü¾«È·µÄ¼ì²âºÍ½á¹û¸üĞÂ¡£
    ******************************************************************/
    EXPORT_API int initVesselDetectionModel(VDHandle* handle, const char* modelpath);

    /******************************************************************
    º¯ÊıÃû£ºdetectVesselInImage
    ¹¦ÄÜÃèÊö£ºÔÚ¸ø¶¨µÄÍ¼ÏñÖĞ¼ì²âÑª¹Ü£¬»ñÈ¡Ñª¹ÜÏà¹ØĞÅÏ¢£¬ÈçÑª¹ÜÖĞ¼ä×ø±ê£¬Ñª¹Ü½Ç¶ÈÒÔ¼°Ñª¹ÜµÄÉÏÏÂ±ßÔµ×ø±êµÈ¡£
    ²ÎÊı£º
        unsigned char* imgin                        [IN]: ÊäÈëµÄÍ¼ÏñÊı¾İ
        int width                                   [IN]: Í¼ÏñµÄ¿í¶È
        int height                                  [IN]: Í¼ÏñµÄ¸ß¶È
        int channels                                [IN]: Í¼ÏñµÄÍ¨µÀÊı
        int flipul                                  [IN]: Í¼ÏñÊÇ·ñĞèÒªÉÏÏÂ·­×ª£¨1ÎªÊÇ£¬0Îª·ñ£©
        int fliplr                                  [IN]: Í¼ÏñÊÇ·ñĞèÒª×óÓÒ·­×ª£¨1ÎªÊÇ£¬0Îª·ñ£©
        ROI_RECT imageROI                           [IN]: ĞèÒª´¦ÀíµÄÍ¼ÏñROIÇøÓò
        VessPosAng* vesOut                          [OUT]: Êä³öµÄÑª¹ÜÏà¹ØĞÅÏ¢µÄ½á¹¹Ìå
    ·µ»ØÖµ£ºERROR_CODE_SUCCESS(³É¹¦), ERROR_CODE_UNKNOWN(³öÏÖÒì³£)
    ¸üĞÂÈÕÖ¾£º
        1. 2024-08-27: Ìí¼ÓÁË imageROI ²ÎÊı£¬ÓÃÓÚÖ¸¶¨ĞèÒª´¦ÀíµÄÍ¼ÏñROIÇøÓò¡£
    ******************************************************************/
    EXPORT_API int detectVesselInImage(VDHandle handle, unsigned char* imgin, int width, int height, int channels,
                                       int flipul, int fliplr, ValidROI imageROI, VessPosAng* vesOut);

    /******************************************************************
    º¯ÊıÃû£ºcleanupVesselDetection
    ¹¦ÄÜÃèÊö£ºÇåÀíºÍÊÍ·ÅÑª¹Ü¼ì²âÏà¹ØµÄ×ÊÔ´£¬¶ÔÓÚÊµÀı»¯µÄÄ£ĞÍ¶ÔÏó½øĞĞÖØÖÃ¡£
    ²ÎÊı£ºÎŞ
    ·µ»ØÖµ£ºÎŞ
    ¸üĞÂÈÕÖ¾£º
    ******************************************************************/
    EXPORT_API int cleanupVesselDetection(VDHandle* handle);
}
