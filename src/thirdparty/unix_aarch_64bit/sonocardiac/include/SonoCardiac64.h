#pragma once

// WIN和LINUX差异化处理
#ifdef MY_WINDOWS
#ifdef SONOCARDIACDLL_EXPORTS
#define SONOCARDIACDLL_API __declspec(dllexport)
#else
#define SONOCARDIACDLL_API __declspec(dllimport)
#endif // SONOCARDIACDLL_EXPORTS
#else  // LINUX
#define SONOCARDIACDLL_API
#endif // MY_WINDOWS

//智能心脏参数定义
#include "../../../Common/SonoCardiacDefine/SonoCardiacDefine.h"

//*******************************************************************
//
//                             结构体定义
//
//*******************************************************************
//初始化信息(64位版本)
typedef struct _InitInfo64
{
    int nImageWidth;              //图像宽度
    int nImageHeight;             //图像高度
    char chAlgLogFolderPath[256]; //算法日志文件夹路径
    char chModelsFolderPath[256]; //心脏切面模型文件夹路径
    char chReserve[400];          //预留
} InitInfo64;

//*******************************************************************
//
//                             句柄定义
//
//*******************************************************************
//智能心脏切面句柄
typedef void* SonoCardiacHandle;

//*******************************************************************
//
//                             函数接口定义
//
//*******************************************************************
extern "C"
{
    /******************************************************************
    函数名:CreateSonoCardiacHandle
    功能描述:创建智能心脏句柄
    参数:
        SonoCardiacHandle *pSonoCardiacHandle   [OUT]:智能心脏句柄指针
        InitInfo *pInitInfo                     [IN]:初始化信息
    返回值:ERROR_CODE(见宏定义)
    ******************************************************************/
    SONOCARDIACDLL_API ERROR_CODE CreateSonoCardiacHandle(SonoCardiacHandle* pSonoCardiacHandle, //[OUT]智能心脏句柄指针
                                                          InitInfo64* pInitInfo64); //初始化信息(64版本)

    /******************************************************************
    函数名:ProcessSonoCardiac
    功能描述:处理智能心脏
    参数:
        SonoCardiacHandle hSonoCardiacHandle    [IN]:智能心脏句柄
        UserInfo *pUserInfo                     [IN]:用户信息
        int nImageWidth                         [IN]:图像宽度
        int nImageHeight                        [IN]:图像高度
        int nImageDataSize                      [IN]:图像数据大小(字节数)
        unsigned char *pImageData               [IN]:灰度图像数据
        SonoCardiacResult *pSonoCardiacResult   [OUT]:结果
    返回值:ERROR_CODE(见宏定义)
    ******************************************************************/
    SONOCARDIACDLL_API ERROR_CODE ProcessSonoCardiac(SonoCardiacHandle hSonoCardiacHandle, //智能心脏句柄
                                                     UserInfo* pUserInfo,                  //用户信息
                                                     int nImageWidth,                      //图像宽度
                                                     int nImageHeight,                     //图像高度
                                                     int nImageDataSize,        //图像数据大小(字节数)
                                                     unsigned char* pImageData, //灰度图像数据
                                                     SonoCardiacResult* pSonoCardiacResult); //[OUT]结果

    /******************************************************************
    函数名:ReleaseSonoCardiacHandle
    功能描述:释放智能心脏句柄
    参数:
        SonoCardiacHandle *pSonoCardiacHandle   [OUT]:智能心脏句柄指针
    返回值:ERROR_CODE(见宏定义)
    ******************************************************************/
    SONOCARDIACDLL_API ERROR_CODE
    ReleaseSonoCardiacHandle(SonoCardiacHandle* pSonoCardiacHandle); //[OUT]智能心脏句柄指针
}
