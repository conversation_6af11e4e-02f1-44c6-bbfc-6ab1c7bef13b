<?xml version="1.0"?>
<net name="torch_jit" version="11">
	<layers>
		<layer id="0" name="images" type="Parameter" version="opset1">
			<data shape="1,3,320,320" element_type="f32" />
			<output>
				<port id="0" precision="FP32" names="images">
					<dim>1</dim>
					<dim>3</dim>
					<dim>320</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="model.0.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 3, 6, 6" offset="0" size="6912" />
			<output>
				<port id="0" precision="FP32" names="model.0.conv.weight">
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="/model.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>320</dim>
					<dim>320</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
					<dim>6</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>160</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="Reshape_141" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="6912" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="/model.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>160</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>160</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="/model.0/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>160</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.0/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>160</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="model.1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 16, 3, 3" offset="6976" size="18432" />
			<output>
				<port id="0" precision="FP32" names="model.1.conv.weight">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="/model.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>160</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="Reshape_191" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="25408" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="/model.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="/model.1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="model.2.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 32, 1, 1" offset="25536" size="2048" />
			<output>
				<port id="0" precision="FP32" names="model.2.cv1.conv.weight">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="/model.2/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="Reshape_241" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="27584" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="/model.2/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="/model.2/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="model.2.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 16, 1, 1" offset="27648" size="1024" />
			<output>
				<port id="0" precision="FP32" names="model.2.m.0.cv1.conv.weight">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="/model.2/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="Reshape_291" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="28672" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="/model.2/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="/model.2/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="model.2.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 16, 3, 3" offset="28736" size="9216" />
			<output>
				<port id="0" precision="FP32" names="model.2.m.0.cv2.conv.weight">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="/model.2/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="Reshape_341" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="37952" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="/model.2/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="/model.2/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="/model.2/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="model.2.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 32, 1, 1" offset="38016" size="2048" />
			<output>
				<port id="0" precision="FP32" names="model.2.cv2.conv.weight">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="/model.2/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="Reshape_392" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="40064" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="/model.2/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="/model.2/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="/model.2/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/Concat_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="model.2.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 1, 1" offset="40128" size="4096" />
			<output>
				<port id="0" precision="FP32" names="model.2.cv3.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="/model.2/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="Reshape_443" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="44224" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="/model.2/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="37" name="/model.2/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="model.3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 32, 3, 3" offset="44352" size="73728" />
			<output>
				<port id="0" precision="FP32" names="model.3.conv.weight">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="39" name="/model.3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>80</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="Reshape_493" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="118080" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="41" name="/model.3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="/model.3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.3/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="model.4.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 64, 1, 1" offset="118336" size="8192" />
			<output>
				<port id="0" precision="FP32" names="model.4.cv1.conv.weight">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="/model.4/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="45" name="Reshape_543" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="126528" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="46" name="/model.4/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="47" name="/model.4/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="48" name="model.4.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 1, 1" offset="126656" size="4096" />
			<output>
				<port id="0" precision="FP32" names="model.4.m.0.cv1.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="49" name="/model.4/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="Reshape_593" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="130752" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="/model.4/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="/model.4/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="53" name="model.4.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 3, 3" offset="130880" size="36864" />
			<output>
				<port id="0" precision="FP32" names="model.4.m.0.cv2.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="54" name="/model.4/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="55" name="Reshape_643" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="167744" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="56" name="/model.4/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="/model.4/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="58" name="/model.4/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="model.4.m.1.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 1, 1" offset="167872" size="4096" />
			<output>
				<port id="0" precision="FP32" names="model.4.m.1.cv1.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="60" name="/model.4/m/m.1/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="Reshape_694" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="171968" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="62" name="/model.4/m/m.1/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.1/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="/model.4/m/m.1/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m/m.1/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="64" name="model.4.m.1.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 3, 3" offset="172096" size="36864" />
			<output>
				<port id="0" precision="FP32" names="model.4.m.1.cv2.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="65" name="/model.4/m/m.1/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="66" name="Reshape_744" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="208960" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="/model.4/m/m.1/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.1/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="68" name="/model.4/m/m.1/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m/m.1/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="69" name="/model.4/m/m.1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.1/Add_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="70" name="model.4.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 64, 1, 1" offset="209088" size="8192" />
			<output>
				<port id="0" precision="FP32" names="model.4.cv2.conv.weight">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="71" name="/model.4/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="72" name="Reshape_795" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="217280" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="73" name="/model.4/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="74" name="/model.4/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="75" name="/model.4/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/Concat_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="76" name="model.4.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="217408" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.4.cv3.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="77" name="/model.4/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="78" name="Reshape_846" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="233792" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="79" name="/model.4/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="80" name="/model.4/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="81" name="model.5.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 64, 3, 3" offset="234048" size="294912" />
			<output>
				<port id="0" precision="FP32" names="model.5.conv.weight">
					<dim>128</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="/model.5/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="Reshape_896" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="528960" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="/model.5/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.5/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="85" name="/model.5/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.5/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="86" name="model.6.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 128, 1, 1" offset="529472" size="32768" />
			<output>
				<port id="0" precision="FP32" names="model.6.cv1.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="87" name="/model.6/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="88" name="Reshape_946" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="562240" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="89" name="/model.6/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="/model.6/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="91" name="model.6.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="562496" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.0.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="/model.6/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="93" name="Reshape_996" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="578880" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="94" name="/model.6/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="95" name="/model.6/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="96" name="model.6.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="579136" size="147456" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.0.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="/model.6/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="Reshape_1046" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="726592" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="/model.6/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="/model.6/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="/model.6/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="model.6.m.1.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="726848" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.1.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="/model.6/m/m.1/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="Reshape_1097" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="743232" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="/model.6/m/m.1/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.1/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="/model.6/m/m.1/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.1/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="model.6.m.1.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="743488" size="147456" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.1.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="/model.6/m/m.1/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="Reshape_1147" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="890944" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="110" name="/model.6/m/m.1/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.1/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="111" name="/model.6/m/m.1/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.1/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="112" name="/model.6/m/m.1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.1/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="113" name="model.6.m.2.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="891200" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.2.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="/model.6/m/m.2/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="Reshape_1198" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="907584" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="/model.6/m/m.2/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.2/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="/model.6/m/m.2/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.2/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="model.6.m.2.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="907840" size="147456" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.2.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="119" name="/model.6/m/m.2/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="120" name="Reshape_1248" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="1055296" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="/model.6/m/m.2/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.2/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="/model.6/m/m.2/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.2/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="/model.6/m/m.2/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.2/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="model.6.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 128, 1, 1" offset="1055552" size="32768" />
			<output>
				<port id="0" precision="FP32" names="model.6.cv2.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="/model.6/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="126" name="Reshape_1299" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="1088320" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="127" name="/model.6/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="128" name="/model.6/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="129" name="/model.6/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="model.6.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 1, 1" offset="1088576" size="65536" />
			<output>
				<port id="0" precision="FP32" names="model.6.cv3.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="131" name="/model.6/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="132" name="Reshape_1350" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="1154112" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="133" name="/model.6/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="134" name="/model.6/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="135" name="model.7.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="256, 128, 3, 3" offset="1154624" size="1179648" />
			<output>
				<port id="0" precision="FP32" names="model.7.conv.weight">
					<dim>256</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="136" name="/model.7/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="137" name="Reshape_1400" type="Const" version="opset1">
			<data element_type="f32" shape="1, 256, 1, 1" offset="2334272" size="1024" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="138" name="/model.7/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.7/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="139" name="/model.7/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.7/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="140" name="model.8.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 256, 1, 1" offset="2335296" size="131072" />
			<output>
				<port id="0" precision="FP32" names="model.8.cv1.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="/model.8/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="Reshape_1450" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="2466368" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="143" name="/model.8/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="/model.8/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="model.8.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 1, 1" offset="2466880" size="65536" />
			<output>
				<port id="0" precision="FP32" names="model.8.m.0.cv1.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="146" name="/model.8/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="147" name="Reshape_1500" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="2532416" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="148" name="/model.8/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="149" name="/model.8/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="150" name="model.8.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 3, 3" offset="2532928" size="589824" />
			<output>
				<port id="0" precision="FP32" names="model.8.m.0.cv2.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="151" name="/model.8/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="152" name="Reshape_1550" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="3122752" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="153" name="/model.8/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="154" name="/model.8/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="155" name="/model.8/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="model.8.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 256, 1, 1" offset="3123264" size="131072" />
			<output>
				<port id="0" precision="FP32" names="model.8.cv2.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="157" name="/model.8/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="Reshape_1601" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="3254336" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="/model.8/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="/model.8/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="/model.8/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="162" name="model.8.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="256, 256, 1, 1" offset="3254848" size="262144" />
			<output>
				<port id="0" precision="FP32" names="model.8.cv3.conv.weight">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="163" name="/model.8/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="164" name="Reshape_1652" type="Const" version="opset1">
			<data element_type="f32" shape="1, 256, 1, 1" offset="3516992" size="1024" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="165" name="/model.8/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="/model.8/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="167" name="model.9.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 256, 1, 1" offset="3518016" size="131072" />
			<output>
				<port id="0" precision="FP32" names="model.9.cv1.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="168" name="/model.9/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="169" name="Reshape_1702" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="3649088" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="170" name="/model.9/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.9/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="/model.9/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="/model.9/m/MaxPool" type="MaxPool" version="opset8">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" kernel="5, 5" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/m/MaxPool_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="173" name="/model.9/m_1/MaxPool" type="MaxPool" version="opset8">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" kernel="5, 5" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/m_1/MaxPool_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="/model.9/m_2/MaxPool" type="MaxPool" version="opset8">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" kernel="5, 5" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/m_2/MaxPool_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="175" name="/model.9/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/model.9/Concat_output_0">
					<dim>1</dim>
					<dim>512</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="model.9.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="256, 512, 1, 1" offset="3649600" size="524288" />
			<output>
				<port id="0" precision="FP32" names="model.9.cv2.conv.weight">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="/model.9/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>512</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="Reshape_1756" type="Const" version="opset1">
			<data element_type="f32" shape="1, 256, 1, 1" offset="4173888" size="1024" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="/model.9/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.9/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="/model.9/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="181" name="model.10.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 256, 1, 1" offset="4174912" size="131072" />
			<output>
				<port id="0" precision="FP32" names="model.10.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="182" name="/model.10/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="183" name="Reshape_1806" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="4305984" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="184" name="/model.10/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.10/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="185" name="/model.10/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.10/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="186" name="ShapeOf_1838" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="187" name="Convert_1839" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="188" name="/model.11/Constant" type="Const" version="opset1">
			<data element_type="f32" shape="4" offset="4306496" size="16" />
			<output>
				<port id="0" precision="FP32" names="/model.11/Constant_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="189" name="Multiply_1840" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>4</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="190" name="Convert_1841" type="Convert" version="opset1">
			<data destination_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="191" name="/model.11/Resize" type="Interpolate" version="opset4">
			<data mode="nearest" shape_calculation_mode="scales" coordinate_transformation_mode="asymmetric" nearest_mode="floor" antialias="false" pads_begin="0, 0, 0, 0" pads_end="0, 0, 0, 0" cube_coeff="-0.75" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.11/Resize_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="192" name="/model.12/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.12/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="193" name="model.13.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 256, 1, 1" offset="4306512" size="65536" />
			<output>
				<port id="0" precision="FP32" names="model.13.cv1.conv.weight">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="194" name="/model.13/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="195" name="Reshape_1864" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="4372048" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="196" name="/model.13/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.13/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="197" name="/model.13/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.13/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="198" name="model.13.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="4372304" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.13.m.0.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="199" name="/model.13/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="200" name="Reshape_1914" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="4388688" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="201" name="/model.13/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.13/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="202" name="/model.13/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.13/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="203" name="model.13.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="4388944" size="147456" />
			<output>
				<port id="0" precision="FP32" names="model.13.m.0.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="204" name="/model.13/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="205" name="Reshape_1964" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="4536400" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="206" name="/model.13/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.13/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="207" name="/model.13/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.13/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="208" name="model.13.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 256, 1, 1" offset="4536656" size="65536" />
			<output>
				<port id="0" precision="FP32" names="model.13.cv2.conv.weight">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="209" name="/model.13/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="210" name="Reshape_2014" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="4602192" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="211" name="/model.13/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.13/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="212" name="/model.13/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.13/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="213" name="/model.13/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.13/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="214" name="model.13.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 1, 1" offset="4602448" size="65536" />
			<output>
				<port id="0" precision="FP32" names="model.13.cv3.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="215" name="/model.13/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="216" name="Reshape_2065" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="4667984" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="217" name="/model.13/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.13/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="218" name="/model.13/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.13/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="219" name="model.14.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 128, 1, 1" offset="4668496" size="32768" />
			<output>
				<port id="0" precision="FP32" names="model.14.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="220" name="/model.14/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="221" name="Reshape_2115" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="4701264" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="222" name="/model.14/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.14/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="223" name="/model.14/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.14/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="224" name="ShapeOf_2147" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="225" name="Convert_2148" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="226" name="/model.15/Constant" type="Const" version="opset1">
			<data element_type="f32" shape="4" offset="4306496" size="16" />
			<output>
				<port id="0" precision="FP32" names="/model.15/Constant_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="227" name="Multiply_2149" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>4</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="228" name="Convert_2150" type="Convert" version="opset1">
			<data destination_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="229" name="/model.15/Resize" type="Interpolate" version="opset4">
			<data mode="nearest" shape_calculation_mode="scales" coordinate_transformation_mode="asymmetric" nearest_mode="floor" antialias="false" pads_begin="0, 0, 0, 0" pads_end="0, 0, 0, 0" cube_coeff="-0.75" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.15/Resize_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="230" name="/model.16/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.16/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="231" name="model.17.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 128, 1, 1" offset="4701520" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.17.cv1.conv.weight">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="232" name="/model.17/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="233" name="Reshape_2173" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="4717904" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="234" name="/model.17/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.17/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="235" name="/model.17/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.17/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="236" name="model.17.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 1, 1" offset="4718032" size="4096" />
			<output>
				<port id="0" precision="FP32" names="model.17.m.0.cv1.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="237" name="/model.17/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="238" name="Reshape_2223" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="4722128" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="239" name="/model.17/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.17/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="240" name="/model.17/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.17/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="241" name="model.17.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 3, 3" offset="4722256" size="36864" />
			<output>
				<port id="0" precision="FP32" names="model.17.m.0.cv2.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="242" name="/model.17/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="243" name="Reshape_2273" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="4759120" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="244" name="/model.17/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.17/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="245" name="/model.17/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.17/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="246" name="model.17.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 128, 1, 1" offset="4759248" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.17.cv2.conv.weight">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="247" name="/model.17/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="248" name="Reshape_2323" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="4775632" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="249" name="/model.17/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.17/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="250" name="/model.17/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.17/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="251" name="/model.17/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.17/Concat_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="252" name="model.17.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="4775760" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.17.cv3.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="253" name="/model.17/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="254" name="Reshape_2374" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="4792144" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="255" name="/model.17/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.17/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="256" name="/model.17/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.17/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="257" name="model.24.m.0.weight" type="Const" version="opset1">
			<data element_type="f32" shape="27, 64, 1, 1" offset="4792400" size="6912" />
			<output>
				<port id="0" precision="FP32" names="model.24.m.0.weight">
					<dim>27</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="258" name="/model.24/m.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>27</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="259" name="Reshape_3028" type="Const" version="opset1">
			<data element_type="f32" shape="1, 27, 1, 1" offset="4799312" size="108" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="260" name="/model.24/m.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/m.0/Conv_output_0">
					<dim>1</dim>
					<dim>27</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="261" name="/model.24/Constant" type="Const" version="opset1">
			<data element_type="i64" shape="5" offset="4799420" size="40" />
			<output>
				<port id="0" precision="I64" names="/model.24/Constant_output_0">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="262" name="/model.24/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="I64">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Reshape_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>9</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="263" name="Constant_3063" type="Const" version="opset1">
			<data element_type="i64" shape="5" offset="4799460" size="40" />
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="264" name="/model.24/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>9</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="I64">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Transpose_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="265" name="/model.24/Sigmoid" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>9</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.24/Sigmoid_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="266" name="Constant_3066" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4799500" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="267" name="Constant_3067" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="4799508" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="268" name="/model.24/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>9</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.24/Split_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
				<port id="4" precision="FP32" names="/model.24/Split_output_1">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
				<port id="5" precision="FP32" names="/model.24/Split_output_2">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="269" name="Constant_9414" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1, 1" offset="4799532" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="270" name="Multiply_9327" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="271" name="Constant_9329" type="Const" version="opset1">
			<data element_type="f32" shape="1, 3, 40, 40, 2" offset="4799536" size="38400" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="272" name="/model.24/Mul_1" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Mul_1_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="273" name="Constant_9415" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1, 1" offset="4837936" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="274" name="/model.24/Mul_2" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Mul_2_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="275" name="Constant_9416" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1, 1" offset="4837936" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="276" name="/model.24/Pow" type="Power" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Pow_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="277" name="/model.24/Constant_6" type="Const" version="opset1">
			<data element_type="f32" shape="1, 3, 40, 40, 2" offset="4837940" size="38400" />
			<output>
				<port id="0" precision="FP32" names="/model.24/Constant_6_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="278" name="/model.24/Mul_3" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Mul_3_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="279" name="/model.24/Concat" type="Concat" version="opset1">
			<data axis="4" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>2</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.24/Concat_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="280" name="/model.24/Constant_7" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="4876340" size="24" />
			<output>
				<port id="0" precision="I64" names="/model.24/Constant_7_output_0">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="281" name="/model.24/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>40</dim>
					<dim>40</dim>
					<dim>9</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Reshape_1_output_0">
					<dim>1</dim>
					<dim>4800</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="282" name="model.18.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="4876364" size="147456" />
			<output>
				<port id="0" precision="FP32" names="model.18.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="283" name="/model.18/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>40</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="284" name="Reshape_2424" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="5023820" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="285" name="/model.18/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.18/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="286" name="/model.18/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.18/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="287" name="/model.19/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.19/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="288" name="model.20.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 128, 1, 1" offset="5024076" size="32768" />
			<output>
				<port id="0" precision="FP32" names="model.20.cv1.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="289" name="/model.20/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="290" name="Reshape_2475" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="5056844" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="291" name="/model.20/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.20/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="292" name="/model.20/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.20/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="293" name="model.20.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="5057100" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.20.m.0.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="294" name="/model.20/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="295" name="Reshape_2525" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="5073484" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="296" name="/model.20/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.20/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="297" name="/model.20/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.20/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="298" name="model.20.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="5073740" size="147456" />
			<output>
				<port id="0" precision="FP32" names="model.20.m.0.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="299" name="/model.20/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="300" name="Reshape_2575" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="5221196" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="301" name="/model.20/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.20/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="302" name="/model.20/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.20/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="303" name="model.20.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 128, 1, 1" offset="5221452" size="32768" />
			<output>
				<port id="0" precision="FP32" names="model.20.cv2.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="304" name="/model.20/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="305" name="Reshape_2625" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="5254220" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="306" name="/model.20/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.20/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="307" name="/model.20/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.20/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="308" name="/model.20/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.20/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="309" name="model.20.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 1, 1" offset="5254476" size="65536" />
			<output>
				<port id="0" precision="FP32" names="model.20.cv3.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="310" name="/model.20/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="311" name="Reshape_2676" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="5320012" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="312" name="/model.20/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.20/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="313" name="/model.20/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.20/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="314" name="model.24.m.1.weight" type="Const" version="opset1">
			<data element_type="f32" shape="27, 128, 1, 1" offset="5320524" size="13824" />
			<output>
				<port id="0" precision="FP32" names="model.24.m.1.weight">
					<dim>27</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="315" name="/model.24/m.1/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>27</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="316" name="Reshape_3109" type="Const" version="opset1">
			<data element_type="f32" shape="1, 27, 1, 1" offset="5334348" size="108" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="317" name="/model.24/m.1/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/m.1/Conv_output_0">
					<dim>1</dim>
					<dim>27</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="318" name="/model.24/Constant_8" type="Const" version="opset1">
			<data element_type="i64" shape="5" offset="5334456" size="40" />
			<output>
				<port id="0" precision="I64" names="/model.24/Constant_8_output_0">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="319" name="/model.24/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="I64">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Reshape_2_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>9</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="320" name="Constant_3144" type="Const" version="opset1">
			<data element_type="i64" shape="5" offset="4799460" size="40" />
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="321" name="/model.24/Transpose_1" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>9</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="I64">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Transpose_1_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="322" name="/model.24/Sigmoid_1" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>9</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.24/Sigmoid_1_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="323" name="Constant_3147" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4799500" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="324" name="Constant_3148" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="4799508" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="325" name="/model.24/Split_1" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>9</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.24/Split_1_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
				<port id="4" precision="FP32" names="/model.24/Split_1_output_1">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
				<port id="5" precision="FP32" names="/model.24/Split_1_output_2">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="326" name="Constant_9417" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1, 1" offset="5334496" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="327" name="Multiply_9334" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="328" name="Constant_9336" type="Const" version="opset1">
			<data element_type="f32" shape="1, 3, 20, 20, 2" offset="5334500" size="9600" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="329" name="/model.24/Mul_5" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Mul_5_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="330" name="Constant_9418" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1, 1" offset="4837936" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="331" name="/model.24/Mul_6" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Mul_6_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="332" name="Constant_9419" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1, 1" offset="4837936" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="333" name="/model.24/Pow_1" type="Power" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Pow_1_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="334" name="/model.24/Constant_14" type="Const" version="opset1">
			<data element_type="f32" shape="1, 3, 20, 20, 2" offset="5344100" size="9600" />
			<output>
				<port id="0" precision="FP32" names="/model.24/Constant_14_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="335" name="/model.24/Mul_7" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Mul_7_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="336" name="/model.24/Concat_1" type="Concat" version="opset1">
			<data axis="4" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>2</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.24/Concat_1_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="337" name="/model.24/Constant_15" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="5353700" size="24" />
			<output>
				<port id="0" precision="I64" names="/model.24/Constant_15_output_0">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="338" name="/model.24/Reshape_3" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>20</dim>
					<dim>20</dim>
					<dim>9</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Reshape_3_output_0">
					<dim>1</dim>
					<dim>1200</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="339" name="model.21.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 3, 3" offset="5353724" size="589824" />
			<output>
				<port id="0" precision="FP32" names="model.21.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="340" name="/model.21/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>20</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="341" name="Reshape_2726" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="5943548" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="342" name="/model.21/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.21/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="343" name="/model.21/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.21/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="344" name="/model.22/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="345" name="model.23.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 256, 1, 1" offset="5944060" size="131072" />
			<output>
				<port id="0" precision="FP32" names="model.23.cv1.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="346" name="/model.23/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="347" name="Reshape_2777" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="6075132" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="348" name="/model.23/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.23/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="349" name="/model.23/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.23/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="350" name="model.23.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 1, 1" offset="6075644" size="65536" />
			<output>
				<port id="0" precision="FP32" names="model.23.m.0.cv1.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="351" name="/model.23/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="352" name="Reshape_2827" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="6141180" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="353" name="/model.23/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.23/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="354" name="/model.23/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.23/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="355" name="model.23.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 3, 3" offset="6141692" size="589824" />
			<output>
				<port id="0" precision="FP32" names="model.23.m.0.cv2.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="356" name="/model.23/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="357" name="Reshape_2877" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="6731516" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="358" name="/model.23/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.23/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="359" name="/model.23/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.23/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="360" name="model.23.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 256, 1, 1" offset="6732028" size="131072" />
			<output>
				<port id="0" precision="FP32" names="model.23.cv2.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="361" name="/model.23/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="362" name="Reshape_2927" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="6863100" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="363" name="/model.23/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.23/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="364" name="/model.23/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.23/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="365" name="/model.23/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.23/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="366" name="model.23.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="256, 256, 1, 1" offset="6863612" size="262144" />
			<output>
				<port id="0" precision="FP32" names="model.23.cv3.conv.weight">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="367" name="/model.23/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="368" name="Reshape_2978" type="Const" version="opset1">
			<data element_type="f32" shape="1, 256, 1, 1" offset="7125756" size="1024" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="369" name="/model.23/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.23/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="370" name="/model.23/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.23/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="371" name="model.24.m.2.weight" type="Const" version="opset1">
			<data element_type="f32" shape="27, 256, 1, 1" offset="7126780" size="27648" />
			<output>
				<port id="0" precision="FP32" names="model.24.m.2.weight">
					<dim>27</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="372" name="/model.24/m.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>27</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="373" name="Reshape_3190" type="Const" version="opset1">
			<data element_type="f32" shape="1, 27, 1, 1" offset="7154428" size="108" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="374" name="/model.24/m.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/m.2/Conv_output_0">
					<dim>1</dim>
					<dim>27</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="375" name="/model.24/Constant_16" type="Const" version="opset1">
			<data element_type="i64" shape="5" offset="7154536" size="40" />
			<output>
				<port id="0" precision="I64" names="/model.24/Constant_16_output_0">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="376" name="/model.24/Reshape_4" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>27</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="I64">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Reshape_4_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>9</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="377" name="Constant_3225" type="Const" version="opset1">
			<data element_type="i64" shape="5" offset="4799460" size="40" />
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="378" name="/model.24/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>9</dim>
					<dim>10</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="I64">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Transpose_2_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="379" name="/model.24/Sigmoid_2" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>9</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.24/Sigmoid_2_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="380" name="Constant_3228" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4799500" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="381" name="Constant_3229" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="4799508" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="382" name="/model.24/Split_2" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>9</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.24/Split_2_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
				<port id="4" precision="FP32" names="/model.24/Split_2_output_1">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
				<port id="5" precision="FP32" names="/model.24/Split_2_output_2">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="383" name="Constant_9420" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1, 1" offset="7154576" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="384" name="Multiply_9341" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="385" name="Constant_9343" type="Const" version="opset1">
			<data element_type="f32" shape="1, 3, 10, 10, 2" offset="7154580" size="2400" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="386" name="/model.24/Mul_9" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Mul_9_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="387" name="Constant_9421" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1, 1" offset="4837936" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="388" name="/model.24/Mul_10" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Mul_10_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="389" name="Constant_9422" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1, 1" offset="4837936" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="390" name="/model.24/Pow_2" type="Power" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Pow_2_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="391" name="/model.24/Constant_22" type="Const" version="opset1">
			<data element_type="f32" shape="1, 3, 10, 10, 2" offset="7156980" size="2400" />
			<output>
				<port id="0" precision="FP32" names="/model.24/Constant_22_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="392" name="/model.24/Mul_11" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Mul_11_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="393" name="/model.24/Concat_2" type="Concat" version="opset1">
			<data axis="4" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>2</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.24/Concat_2_output_0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="394" name="/model.24/Constant_23" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="7159380" size="24" />
			<output>
				<port id="0" precision="I64" names="/model.24/Constant_23_output_0">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="395" name="/model.24/Reshape_5" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>10</dim>
					<dim>10</dim>
					<dim>9</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.24/Reshape_5_output_0">
					<dim>1</dim>
					<dim>300</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="396" name="output0" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4800</dim>
					<dim>9</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1200</dim>
					<dim>9</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>300</dim>
					<dim>9</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="output0">
					<dim>1</dim>
					<dim>6300</dim>
					<dim>9</dim>
				</port>
			</output>
		</layer>
		<layer id="397" name="output0/sink_port_0" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>6300</dim>
					<dim>9</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="2" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="2" to-port="1" />
		<edge from-layer="2" from-port="2" to-layer="4" to-port="0" />
		<edge from-layer="3" from-port="0" to-layer="4" to-port="1" />
		<edge from-layer="4" from-port="2" to-layer="5" to-port="0" />
		<edge from-layer="5" from-port="1" to-layer="7" to-port="0" />
		<edge from-layer="6" from-port="0" to-layer="7" to-port="1" />
		<edge from-layer="7" from-port="2" to-layer="9" to-port="0" />
		<edge from-layer="8" from-port="0" to-layer="9" to-port="1" />
		<edge from-layer="9" from-port="2" to-layer="10" to-port="0" />
		<edge from-layer="10" from-port="1" to-layer="12" to-port="0" />
		<edge from-layer="10" from-port="1" to-layer="28" to-port="0" />
		<edge from-layer="11" from-port="0" to-layer="12" to-port="1" />
		<edge from-layer="12" from-port="2" to-layer="14" to-port="0" />
		<edge from-layer="13" from-port="0" to-layer="14" to-port="1" />
		<edge from-layer="14" from-port="2" to-layer="15" to-port="0" />
		<edge from-layer="15" from-port="1" to-layer="17" to-port="0" />
		<edge from-layer="15" from-port="1" to-layer="26" to-port="0" />
		<edge from-layer="16" from-port="0" to-layer="17" to-port="1" />
		<edge from-layer="17" from-port="2" to-layer="19" to-port="0" />
		<edge from-layer="18" from-port="0" to-layer="19" to-port="1" />
		<edge from-layer="19" from-port="2" to-layer="20" to-port="0" />
		<edge from-layer="20" from-port="1" to-layer="22" to-port="0" />
		<edge from-layer="21" from-port="0" to-layer="22" to-port="1" />
		<edge from-layer="22" from-port="2" to-layer="24" to-port="0" />
		<edge from-layer="23" from-port="0" to-layer="24" to-port="1" />
		<edge from-layer="24" from-port="2" to-layer="25" to-port="0" />
		<edge from-layer="25" from-port="1" to-layer="26" to-port="1" />
		<edge from-layer="26" from-port="2" to-layer="32" to-port="0" />
		<edge from-layer="27" from-port="0" to-layer="28" to-port="1" />
		<edge from-layer="28" from-port="2" to-layer="30" to-port="0" />
		<edge from-layer="29" from-port="0" to-layer="30" to-port="1" />
		<edge from-layer="30" from-port="2" to-layer="31" to-port="0" />
		<edge from-layer="31" from-port="1" to-layer="32" to-port="1" />
		<edge from-layer="32" from-port="2" to-layer="34" to-port="0" />
		<edge from-layer="33" from-port="0" to-layer="34" to-port="1" />
		<edge from-layer="34" from-port="2" to-layer="36" to-port="0" />
		<edge from-layer="35" from-port="0" to-layer="36" to-port="1" />
		<edge from-layer="36" from-port="2" to-layer="37" to-port="0" />
		<edge from-layer="37" from-port="1" to-layer="39" to-port="0" />
		<edge from-layer="38" from-port="0" to-layer="39" to-port="1" />
		<edge from-layer="39" from-port="2" to-layer="41" to-port="0" />
		<edge from-layer="40" from-port="0" to-layer="41" to-port="1" />
		<edge from-layer="41" from-port="2" to-layer="42" to-port="0" />
		<edge from-layer="42" from-port="1" to-layer="44" to-port="0" />
		<edge from-layer="42" from-port="1" to-layer="71" to-port="0" />
		<edge from-layer="43" from-port="0" to-layer="44" to-port="1" />
		<edge from-layer="44" from-port="2" to-layer="46" to-port="0" />
		<edge from-layer="45" from-port="0" to-layer="46" to-port="1" />
		<edge from-layer="46" from-port="2" to-layer="47" to-port="0" />
		<edge from-layer="47" from-port="1" to-layer="49" to-port="0" />
		<edge from-layer="47" from-port="1" to-layer="58" to-port="0" />
		<edge from-layer="48" from-port="0" to-layer="49" to-port="1" />
		<edge from-layer="49" from-port="2" to-layer="51" to-port="0" />
		<edge from-layer="50" from-port="0" to-layer="51" to-port="1" />
		<edge from-layer="51" from-port="2" to-layer="52" to-port="0" />
		<edge from-layer="52" from-port="1" to-layer="54" to-port="0" />
		<edge from-layer="53" from-port="0" to-layer="54" to-port="1" />
		<edge from-layer="54" from-port="2" to-layer="56" to-port="0" />
		<edge from-layer="55" from-port="0" to-layer="56" to-port="1" />
		<edge from-layer="56" from-port="2" to-layer="57" to-port="0" />
		<edge from-layer="57" from-port="1" to-layer="58" to-port="1" />
		<edge from-layer="58" from-port="2" to-layer="69" to-port="0" />
		<edge from-layer="58" from-port="2" to-layer="60" to-port="0" />
		<edge from-layer="59" from-port="0" to-layer="60" to-port="1" />
		<edge from-layer="60" from-port="2" to-layer="62" to-port="0" />
		<edge from-layer="61" from-port="0" to-layer="62" to-port="1" />
		<edge from-layer="62" from-port="2" to-layer="63" to-port="0" />
		<edge from-layer="63" from-port="1" to-layer="65" to-port="0" />
		<edge from-layer="64" from-port="0" to-layer="65" to-port="1" />
		<edge from-layer="65" from-port="2" to-layer="67" to-port="0" />
		<edge from-layer="66" from-port="0" to-layer="67" to-port="1" />
		<edge from-layer="67" from-port="2" to-layer="68" to-port="0" />
		<edge from-layer="68" from-port="1" to-layer="69" to-port="1" />
		<edge from-layer="69" from-port="2" to-layer="75" to-port="0" />
		<edge from-layer="70" from-port="0" to-layer="71" to-port="1" />
		<edge from-layer="71" from-port="2" to-layer="73" to-port="0" />
		<edge from-layer="72" from-port="0" to-layer="73" to-port="1" />
		<edge from-layer="73" from-port="2" to-layer="74" to-port="0" />
		<edge from-layer="74" from-port="1" to-layer="75" to-port="1" />
		<edge from-layer="75" from-port="2" to-layer="77" to-port="0" />
		<edge from-layer="76" from-port="0" to-layer="77" to-port="1" />
		<edge from-layer="77" from-port="2" to-layer="79" to-port="0" />
		<edge from-layer="78" from-port="0" to-layer="79" to-port="1" />
		<edge from-layer="79" from-port="2" to-layer="80" to-port="0" />
		<edge from-layer="80" from-port="1" to-layer="82" to-port="0" />
		<edge from-layer="80" from-port="1" to-layer="230" to-port="1" />
		<edge from-layer="81" from-port="0" to-layer="82" to-port="1" />
		<edge from-layer="82" from-port="2" to-layer="84" to-port="0" />
		<edge from-layer="83" from-port="0" to-layer="84" to-port="1" />
		<edge from-layer="84" from-port="2" to-layer="85" to-port="0" />
		<edge from-layer="85" from-port="1" to-layer="87" to-port="0" />
		<edge from-layer="85" from-port="1" to-layer="125" to-port="0" />
		<edge from-layer="86" from-port="0" to-layer="87" to-port="1" />
		<edge from-layer="87" from-port="2" to-layer="89" to-port="0" />
		<edge from-layer="88" from-port="0" to-layer="89" to-port="1" />
		<edge from-layer="89" from-port="2" to-layer="90" to-port="0" />
		<edge from-layer="90" from-port="1" to-layer="92" to-port="0" />
		<edge from-layer="90" from-port="1" to-layer="101" to-port="0" />
		<edge from-layer="91" from-port="0" to-layer="92" to-port="1" />
		<edge from-layer="92" from-port="2" to-layer="94" to-port="0" />
		<edge from-layer="93" from-port="0" to-layer="94" to-port="1" />
		<edge from-layer="94" from-port="2" to-layer="95" to-port="0" />
		<edge from-layer="95" from-port="1" to-layer="97" to-port="0" />
		<edge from-layer="96" from-port="0" to-layer="97" to-port="1" />
		<edge from-layer="97" from-port="2" to-layer="99" to-port="0" />
		<edge from-layer="98" from-port="0" to-layer="99" to-port="1" />
		<edge from-layer="99" from-port="2" to-layer="100" to-port="0" />
		<edge from-layer="100" from-port="1" to-layer="101" to-port="1" />
		<edge from-layer="101" from-port="2" to-layer="103" to-port="0" />
		<edge from-layer="101" from-port="2" to-layer="112" to-port="0" />
		<edge from-layer="102" from-port="0" to-layer="103" to-port="1" />
		<edge from-layer="103" from-port="2" to-layer="105" to-port="0" />
		<edge from-layer="104" from-port="0" to-layer="105" to-port="1" />
		<edge from-layer="105" from-port="2" to-layer="106" to-port="0" />
		<edge from-layer="106" from-port="1" to-layer="108" to-port="0" />
		<edge from-layer="107" from-port="0" to-layer="108" to-port="1" />
		<edge from-layer="108" from-port="2" to-layer="110" to-port="0" />
		<edge from-layer="109" from-port="0" to-layer="110" to-port="1" />
		<edge from-layer="110" from-port="2" to-layer="111" to-port="0" />
		<edge from-layer="111" from-port="1" to-layer="112" to-port="1" />
		<edge from-layer="112" from-port="2" to-layer="114" to-port="0" />
		<edge from-layer="112" from-port="2" to-layer="123" to-port="0" />
		<edge from-layer="113" from-port="0" to-layer="114" to-port="1" />
		<edge from-layer="114" from-port="2" to-layer="116" to-port="0" />
		<edge from-layer="115" from-port="0" to-layer="116" to-port="1" />
		<edge from-layer="116" from-port="2" to-layer="117" to-port="0" />
		<edge from-layer="117" from-port="1" to-layer="119" to-port="0" />
		<edge from-layer="118" from-port="0" to-layer="119" to-port="1" />
		<edge from-layer="119" from-port="2" to-layer="121" to-port="0" />
		<edge from-layer="120" from-port="0" to-layer="121" to-port="1" />
		<edge from-layer="121" from-port="2" to-layer="122" to-port="0" />
		<edge from-layer="122" from-port="1" to-layer="123" to-port="1" />
		<edge from-layer="123" from-port="2" to-layer="129" to-port="0" />
		<edge from-layer="124" from-port="0" to-layer="125" to-port="1" />
		<edge from-layer="125" from-port="2" to-layer="127" to-port="0" />
		<edge from-layer="126" from-port="0" to-layer="127" to-port="1" />
		<edge from-layer="127" from-port="2" to-layer="128" to-port="0" />
		<edge from-layer="128" from-port="1" to-layer="129" to-port="1" />
		<edge from-layer="129" from-port="2" to-layer="131" to-port="0" />
		<edge from-layer="130" from-port="0" to-layer="131" to-port="1" />
		<edge from-layer="131" from-port="2" to-layer="133" to-port="0" />
		<edge from-layer="132" from-port="0" to-layer="133" to-port="1" />
		<edge from-layer="133" from-port="2" to-layer="134" to-port="0" />
		<edge from-layer="134" from-port="1" to-layer="192" to-port="1" />
		<edge from-layer="134" from-port="1" to-layer="136" to-port="0" />
		<edge from-layer="135" from-port="0" to-layer="136" to-port="1" />
		<edge from-layer="136" from-port="2" to-layer="138" to-port="0" />
		<edge from-layer="137" from-port="0" to-layer="138" to-port="1" />
		<edge from-layer="138" from-port="2" to-layer="139" to-port="0" />
		<edge from-layer="139" from-port="1" to-layer="141" to-port="0" />
		<edge from-layer="139" from-port="1" to-layer="157" to-port="0" />
		<edge from-layer="140" from-port="0" to-layer="141" to-port="1" />
		<edge from-layer="141" from-port="2" to-layer="143" to-port="0" />
		<edge from-layer="142" from-port="0" to-layer="143" to-port="1" />
		<edge from-layer="143" from-port="2" to-layer="144" to-port="0" />
		<edge from-layer="144" from-port="1" to-layer="146" to-port="0" />
		<edge from-layer="144" from-port="1" to-layer="155" to-port="0" />
		<edge from-layer="145" from-port="0" to-layer="146" to-port="1" />
		<edge from-layer="146" from-port="2" to-layer="148" to-port="0" />
		<edge from-layer="147" from-port="0" to-layer="148" to-port="1" />
		<edge from-layer="148" from-port="2" to-layer="149" to-port="0" />
		<edge from-layer="149" from-port="1" to-layer="151" to-port="0" />
		<edge from-layer="150" from-port="0" to-layer="151" to-port="1" />
		<edge from-layer="151" from-port="2" to-layer="153" to-port="0" />
		<edge from-layer="152" from-port="0" to-layer="153" to-port="1" />
		<edge from-layer="153" from-port="2" to-layer="154" to-port="0" />
		<edge from-layer="154" from-port="1" to-layer="155" to-port="1" />
		<edge from-layer="155" from-port="2" to-layer="161" to-port="0" />
		<edge from-layer="156" from-port="0" to-layer="157" to-port="1" />
		<edge from-layer="157" from-port="2" to-layer="159" to-port="0" />
		<edge from-layer="158" from-port="0" to-layer="159" to-port="1" />
		<edge from-layer="159" from-port="2" to-layer="160" to-port="0" />
		<edge from-layer="160" from-port="1" to-layer="161" to-port="1" />
		<edge from-layer="161" from-port="2" to-layer="163" to-port="0" />
		<edge from-layer="162" from-port="0" to-layer="163" to-port="1" />
		<edge from-layer="163" from-port="2" to-layer="165" to-port="0" />
		<edge from-layer="164" from-port="0" to-layer="165" to-port="1" />
		<edge from-layer="165" from-port="2" to-layer="166" to-port="0" />
		<edge from-layer="166" from-port="1" to-layer="168" to-port="0" />
		<edge from-layer="167" from-port="0" to-layer="168" to-port="1" />
		<edge from-layer="168" from-port="2" to-layer="170" to-port="0" />
		<edge from-layer="169" from-port="0" to-layer="170" to-port="1" />
		<edge from-layer="170" from-port="2" to-layer="171" to-port="0" />
		<edge from-layer="171" from-port="1" to-layer="175" to-port="0" />
		<edge from-layer="171" from-port="1" to-layer="172" to-port="0" />
		<edge from-layer="172" from-port="1" to-layer="175" to-port="1" />
		<edge from-layer="172" from-port="1" to-layer="173" to-port="0" />
		<edge from-layer="173" from-port="1" to-layer="174" to-port="0" />
		<edge from-layer="173" from-port="1" to-layer="175" to-port="2" />
		<edge from-layer="174" from-port="1" to-layer="175" to-port="3" />
		<edge from-layer="175" from-port="4" to-layer="177" to-port="0" />
		<edge from-layer="176" from-port="0" to-layer="177" to-port="1" />
		<edge from-layer="177" from-port="2" to-layer="179" to-port="0" />
		<edge from-layer="178" from-port="0" to-layer="179" to-port="1" />
		<edge from-layer="179" from-port="2" to-layer="180" to-port="0" />
		<edge from-layer="180" from-port="1" to-layer="182" to-port="0" />
		<edge from-layer="181" from-port="0" to-layer="182" to-port="1" />
		<edge from-layer="182" from-port="2" to-layer="184" to-port="0" />
		<edge from-layer="183" from-port="0" to-layer="184" to-port="1" />
		<edge from-layer="184" from-port="2" to-layer="185" to-port="0" />
		<edge from-layer="185" from-port="1" to-layer="191" to-port="0" />
		<edge from-layer="185" from-port="1" to-layer="344" to-port="1" />
		<edge from-layer="185" from-port="1" to-layer="186" to-port="0" />
		<edge from-layer="186" from-port="1" to-layer="187" to-port="0" />
		<edge from-layer="187" from-port="1" to-layer="189" to-port="0" />
		<edge from-layer="188" from-port="0" to-layer="189" to-port="1" />
		<edge from-layer="188" from-port="0" to-layer="191" to-port="2" />
		<edge from-layer="189" from-port="2" to-layer="190" to-port="0" />
		<edge from-layer="190" from-port="1" to-layer="191" to-port="1" />
		<edge from-layer="191" from-port="3" to-layer="192" to-port="0" />
		<edge from-layer="192" from-port="2" to-layer="209" to-port="0" />
		<edge from-layer="192" from-port="2" to-layer="194" to-port="0" />
		<edge from-layer="193" from-port="0" to-layer="194" to-port="1" />
		<edge from-layer="194" from-port="2" to-layer="196" to-port="0" />
		<edge from-layer="195" from-port="0" to-layer="196" to-port="1" />
		<edge from-layer="196" from-port="2" to-layer="197" to-port="0" />
		<edge from-layer="197" from-port="1" to-layer="199" to-port="0" />
		<edge from-layer="198" from-port="0" to-layer="199" to-port="1" />
		<edge from-layer="199" from-port="2" to-layer="201" to-port="0" />
		<edge from-layer="200" from-port="0" to-layer="201" to-port="1" />
		<edge from-layer="201" from-port="2" to-layer="202" to-port="0" />
		<edge from-layer="202" from-port="1" to-layer="204" to-port="0" />
		<edge from-layer="203" from-port="0" to-layer="204" to-port="1" />
		<edge from-layer="204" from-port="2" to-layer="206" to-port="0" />
		<edge from-layer="205" from-port="0" to-layer="206" to-port="1" />
		<edge from-layer="206" from-port="2" to-layer="207" to-port="0" />
		<edge from-layer="207" from-port="1" to-layer="213" to-port="0" />
		<edge from-layer="208" from-port="0" to-layer="209" to-port="1" />
		<edge from-layer="209" from-port="2" to-layer="211" to-port="0" />
		<edge from-layer="210" from-port="0" to-layer="211" to-port="1" />
		<edge from-layer="211" from-port="2" to-layer="212" to-port="0" />
		<edge from-layer="212" from-port="1" to-layer="213" to-port="1" />
		<edge from-layer="213" from-port="2" to-layer="215" to-port="0" />
		<edge from-layer="214" from-port="0" to-layer="215" to-port="1" />
		<edge from-layer="215" from-port="2" to-layer="217" to-port="0" />
		<edge from-layer="216" from-port="0" to-layer="217" to-port="1" />
		<edge from-layer="217" from-port="2" to-layer="218" to-port="0" />
		<edge from-layer="218" from-port="1" to-layer="220" to-port="0" />
		<edge from-layer="219" from-port="0" to-layer="220" to-port="1" />
		<edge from-layer="220" from-port="2" to-layer="222" to-port="0" />
		<edge from-layer="221" from-port="0" to-layer="222" to-port="1" />
		<edge from-layer="222" from-port="2" to-layer="223" to-port="0" />
		<edge from-layer="223" from-port="1" to-layer="287" to-port="1" />
		<edge from-layer="223" from-port="1" to-layer="229" to-port="0" />
		<edge from-layer="223" from-port="1" to-layer="224" to-port="0" />
		<edge from-layer="224" from-port="1" to-layer="225" to-port="0" />
		<edge from-layer="225" from-port="1" to-layer="227" to-port="0" />
		<edge from-layer="226" from-port="0" to-layer="227" to-port="1" />
		<edge from-layer="226" from-port="0" to-layer="229" to-port="2" />
		<edge from-layer="227" from-port="2" to-layer="228" to-port="0" />
		<edge from-layer="228" from-port="1" to-layer="229" to-port="1" />
		<edge from-layer="229" from-port="3" to-layer="230" to-port="0" />
		<edge from-layer="230" from-port="2" to-layer="247" to-port="0" />
		<edge from-layer="230" from-port="2" to-layer="232" to-port="0" />
		<edge from-layer="231" from-port="0" to-layer="232" to-port="1" />
		<edge from-layer="232" from-port="2" to-layer="234" to-port="0" />
		<edge from-layer="233" from-port="0" to-layer="234" to-port="1" />
		<edge from-layer="234" from-port="2" to-layer="235" to-port="0" />
		<edge from-layer="235" from-port="1" to-layer="237" to-port="0" />
		<edge from-layer="236" from-port="0" to-layer="237" to-port="1" />
		<edge from-layer="237" from-port="2" to-layer="239" to-port="0" />
		<edge from-layer="238" from-port="0" to-layer="239" to-port="1" />
		<edge from-layer="239" from-port="2" to-layer="240" to-port="0" />
		<edge from-layer="240" from-port="1" to-layer="242" to-port="0" />
		<edge from-layer="241" from-port="0" to-layer="242" to-port="1" />
		<edge from-layer="242" from-port="2" to-layer="244" to-port="0" />
		<edge from-layer="243" from-port="0" to-layer="244" to-port="1" />
		<edge from-layer="244" from-port="2" to-layer="245" to-port="0" />
		<edge from-layer="245" from-port="1" to-layer="251" to-port="0" />
		<edge from-layer="246" from-port="0" to-layer="247" to-port="1" />
		<edge from-layer="247" from-port="2" to-layer="249" to-port="0" />
		<edge from-layer="248" from-port="0" to-layer="249" to-port="1" />
		<edge from-layer="249" from-port="2" to-layer="250" to-port="0" />
		<edge from-layer="250" from-port="1" to-layer="251" to-port="1" />
		<edge from-layer="251" from-port="2" to-layer="253" to-port="0" />
		<edge from-layer="252" from-port="0" to-layer="253" to-port="1" />
		<edge from-layer="253" from-port="2" to-layer="255" to-port="0" />
		<edge from-layer="254" from-port="0" to-layer="255" to-port="1" />
		<edge from-layer="255" from-port="2" to-layer="256" to-port="0" />
		<edge from-layer="256" from-port="1" to-layer="258" to-port="0" />
		<edge from-layer="256" from-port="1" to-layer="283" to-port="0" />
		<edge from-layer="257" from-port="0" to-layer="258" to-port="1" />
		<edge from-layer="258" from-port="2" to-layer="260" to-port="0" />
		<edge from-layer="259" from-port="0" to-layer="260" to-port="1" />
		<edge from-layer="260" from-port="2" to-layer="262" to-port="0" />
		<edge from-layer="261" from-port="0" to-layer="262" to-port="1" />
		<edge from-layer="262" from-port="2" to-layer="264" to-port="0" />
		<edge from-layer="263" from-port="0" to-layer="264" to-port="1" />
		<edge from-layer="264" from-port="2" to-layer="265" to-port="0" />
		<edge from-layer="265" from-port="1" to-layer="268" to-port="0" />
		<edge from-layer="266" from-port="0" to-layer="268" to-port="1" />
		<edge from-layer="267" from-port="0" to-layer="268" to-port="2" />
		<edge from-layer="268" from-port="5" to-layer="279" to-port="2" />
		<edge from-layer="268" from-port="4" to-layer="274" to-port="0" />
		<edge from-layer="268" from-port="3" to-layer="270" to-port="0" />
		<edge from-layer="269" from-port="0" to-layer="270" to-port="1" />
		<edge from-layer="270" from-port="2" to-layer="272" to-port="0" />
		<edge from-layer="271" from-port="0" to-layer="272" to-port="1" />
		<edge from-layer="272" from-port="2" to-layer="279" to-port="0" />
		<edge from-layer="273" from-port="0" to-layer="274" to-port="1" />
		<edge from-layer="274" from-port="2" to-layer="276" to-port="0" />
		<edge from-layer="275" from-port="0" to-layer="276" to-port="1" />
		<edge from-layer="276" from-port="2" to-layer="278" to-port="0" />
		<edge from-layer="277" from-port="0" to-layer="278" to-port="1" />
		<edge from-layer="278" from-port="2" to-layer="279" to-port="1" />
		<edge from-layer="279" from-port="3" to-layer="281" to-port="0" />
		<edge from-layer="280" from-port="0" to-layer="281" to-port="1" />
		<edge from-layer="281" from-port="2" to-layer="396" to-port="0" />
		<edge from-layer="282" from-port="0" to-layer="283" to-port="1" />
		<edge from-layer="283" from-port="2" to-layer="285" to-port="0" />
		<edge from-layer="284" from-port="0" to-layer="285" to-port="1" />
		<edge from-layer="285" from-port="2" to-layer="286" to-port="0" />
		<edge from-layer="286" from-port="1" to-layer="287" to-port="0" />
		<edge from-layer="287" from-port="2" to-layer="289" to-port="0" />
		<edge from-layer="287" from-port="2" to-layer="304" to-port="0" />
		<edge from-layer="288" from-port="0" to-layer="289" to-port="1" />
		<edge from-layer="289" from-port="2" to-layer="291" to-port="0" />
		<edge from-layer="290" from-port="0" to-layer="291" to-port="1" />
		<edge from-layer="291" from-port="2" to-layer="292" to-port="0" />
		<edge from-layer="292" from-port="1" to-layer="294" to-port="0" />
		<edge from-layer="293" from-port="0" to-layer="294" to-port="1" />
		<edge from-layer="294" from-port="2" to-layer="296" to-port="0" />
		<edge from-layer="295" from-port="0" to-layer="296" to-port="1" />
		<edge from-layer="296" from-port="2" to-layer="297" to-port="0" />
		<edge from-layer="297" from-port="1" to-layer="299" to-port="0" />
		<edge from-layer="298" from-port="0" to-layer="299" to-port="1" />
		<edge from-layer="299" from-port="2" to-layer="301" to-port="0" />
		<edge from-layer="300" from-port="0" to-layer="301" to-port="1" />
		<edge from-layer="301" from-port="2" to-layer="302" to-port="0" />
		<edge from-layer="302" from-port="1" to-layer="308" to-port="0" />
		<edge from-layer="303" from-port="0" to-layer="304" to-port="1" />
		<edge from-layer="304" from-port="2" to-layer="306" to-port="0" />
		<edge from-layer="305" from-port="0" to-layer="306" to-port="1" />
		<edge from-layer="306" from-port="2" to-layer="307" to-port="0" />
		<edge from-layer="307" from-port="1" to-layer="308" to-port="1" />
		<edge from-layer="308" from-port="2" to-layer="310" to-port="0" />
		<edge from-layer="309" from-port="0" to-layer="310" to-port="1" />
		<edge from-layer="310" from-port="2" to-layer="312" to-port="0" />
		<edge from-layer="311" from-port="0" to-layer="312" to-port="1" />
		<edge from-layer="312" from-port="2" to-layer="313" to-port="0" />
		<edge from-layer="313" from-port="1" to-layer="315" to-port="0" />
		<edge from-layer="313" from-port="1" to-layer="340" to-port="0" />
		<edge from-layer="314" from-port="0" to-layer="315" to-port="1" />
		<edge from-layer="315" from-port="2" to-layer="317" to-port="0" />
		<edge from-layer="316" from-port="0" to-layer="317" to-port="1" />
		<edge from-layer="317" from-port="2" to-layer="319" to-port="0" />
		<edge from-layer="318" from-port="0" to-layer="319" to-port="1" />
		<edge from-layer="319" from-port="2" to-layer="321" to-port="0" />
		<edge from-layer="320" from-port="0" to-layer="321" to-port="1" />
		<edge from-layer="321" from-port="2" to-layer="322" to-port="0" />
		<edge from-layer="322" from-port="1" to-layer="325" to-port="0" />
		<edge from-layer="323" from-port="0" to-layer="325" to-port="1" />
		<edge from-layer="324" from-port="0" to-layer="325" to-port="2" />
		<edge from-layer="325" from-port="3" to-layer="327" to-port="0" />
		<edge from-layer="325" from-port="4" to-layer="331" to-port="0" />
		<edge from-layer="325" from-port="5" to-layer="336" to-port="2" />
		<edge from-layer="326" from-port="0" to-layer="327" to-port="1" />
		<edge from-layer="327" from-port="2" to-layer="329" to-port="0" />
		<edge from-layer="328" from-port="0" to-layer="329" to-port="1" />
		<edge from-layer="329" from-port="2" to-layer="336" to-port="0" />
		<edge from-layer="330" from-port="0" to-layer="331" to-port="1" />
		<edge from-layer="331" from-port="2" to-layer="333" to-port="0" />
		<edge from-layer="332" from-port="0" to-layer="333" to-port="1" />
		<edge from-layer="333" from-port="2" to-layer="335" to-port="0" />
		<edge from-layer="334" from-port="0" to-layer="335" to-port="1" />
		<edge from-layer="335" from-port="2" to-layer="336" to-port="1" />
		<edge from-layer="336" from-port="3" to-layer="338" to-port="0" />
		<edge from-layer="337" from-port="0" to-layer="338" to-port="1" />
		<edge from-layer="338" from-port="2" to-layer="396" to-port="1" />
		<edge from-layer="339" from-port="0" to-layer="340" to-port="1" />
		<edge from-layer="340" from-port="2" to-layer="342" to-port="0" />
		<edge from-layer="341" from-port="0" to-layer="342" to-port="1" />
		<edge from-layer="342" from-port="2" to-layer="343" to-port="0" />
		<edge from-layer="343" from-port="1" to-layer="344" to-port="0" />
		<edge from-layer="344" from-port="2" to-layer="361" to-port="0" />
		<edge from-layer="344" from-port="2" to-layer="346" to-port="0" />
		<edge from-layer="345" from-port="0" to-layer="346" to-port="1" />
		<edge from-layer="346" from-port="2" to-layer="348" to-port="0" />
		<edge from-layer="347" from-port="0" to-layer="348" to-port="1" />
		<edge from-layer="348" from-port="2" to-layer="349" to-port="0" />
		<edge from-layer="349" from-port="1" to-layer="351" to-port="0" />
		<edge from-layer="350" from-port="0" to-layer="351" to-port="1" />
		<edge from-layer="351" from-port="2" to-layer="353" to-port="0" />
		<edge from-layer="352" from-port="0" to-layer="353" to-port="1" />
		<edge from-layer="353" from-port="2" to-layer="354" to-port="0" />
		<edge from-layer="354" from-port="1" to-layer="356" to-port="0" />
		<edge from-layer="355" from-port="0" to-layer="356" to-port="1" />
		<edge from-layer="356" from-port="2" to-layer="358" to-port="0" />
		<edge from-layer="357" from-port="0" to-layer="358" to-port="1" />
		<edge from-layer="358" from-port="2" to-layer="359" to-port="0" />
		<edge from-layer="359" from-port="1" to-layer="365" to-port="0" />
		<edge from-layer="360" from-port="0" to-layer="361" to-port="1" />
		<edge from-layer="361" from-port="2" to-layer="363" to-port="0" />
		<edge from-layer="362" from-port="0" to-layer="363" to-port="1" />
		<edge from-layer="363" from-port="2" to-layer="364" to-port="0" />
		<edge from-layer="364" from-port="1" to-layer="365" to-port="1" />
		<edge from-layer="365" from-port="2" to-layer="367" to-port="0" />
		<edge from-layer="366" from-port="0" to-layer="367" to-port="1" />
		<edge from-layer="367" from-port="2" to-layer="369" to-port="0" />
		<edge from-layer="368" from-port="0" to-layer="369" to-port="1" />
		<edge from-layer="369" from-port="2" to-layer="370" to-port="0" />
		<edge from-layer="370" from-port="1" to-layer="372" to-port="0" />
		<edge from-layer="371" from-port="0" to-layer="372" to-port="1" />
		<edge from-layer="372" from-port="2" to-layer="374" to-port="0" />
		<edge from-layer="373" from-port="0" to-layer="374" to-port="1" />
		<edge from-layer="374" from-port="2" to-layer="376" to-port="0" />
		<edge from-layer="375" from-port="0" to-layer="376" to-port="1" />
		<edge from-layer="376" from-port="2" to-layer="378" to-port="0" />
		<edge from-layer="377" from-port="0" to-layer="378" to-port="1" />
		<edge from-layer="378" from-port="2" to-layer="379" to-port="0" />
		<edge from-layer="379" from-port="1" to-layer="382" to-port="0" />
		<edge from-layer="380" from-port="0" to-layer="382" to-port="1" />
		<edge from-layer="381" from-port="0" to-layer="382" to-port="2" />
		<edge from-layer="382" from-port="3" to-layer="384" to-port="0" />
		<edge from-layer="382" from-port="4" to-layer="388" to-port="0" />
		<edge from-layer="382" from-port="5" to-layer="393" to-port="2" />
		<edge from-layer="383" from-port="0" to-layer="384" to-port="1" />
		<edge from-layer="384" from-port="2" to-layer="386" to-port="0" />
		<edge from-layer="385" from-port="0" to-layer="386" to-port="1" />
		<edge from-layer="386" from-port="2" to-layer="393" to-port="0" />
		<edge from-layer="387" from-port="0" to-layer="388" to-port="1" />
		<edge from-layer="388" from-port="2" to-layer="390" to-port="0" />
		<edge from-layer="389" from-port="0" to-layer="390" to-port="1" />
		<edge from-layer="390" from-port="2" to-layer="392" to-port="0" />
		<edge from-layer="391" from-port="0" to-layer="392" to-port="1" />
		<edge from-layer="392" from-port="2" to-layer="393" to-port="1" />
		<edge from-layer="393" from-port="3" to-layer="395" to-port="0" />
		<edge from-layer="394" from-port="0" to-layer="395" to-port="1" />
		<edge from-layer="395" from-port="2" to-layer="396" to-port="2" />
		<edge from-layer="396" from-port="3" to-layer="397" to-port="0" />
	</edges>
	<rt_info>
		<MO_version value="2022.3.0-9052-9752fafe8eb-releases/2022/3" />
		<Runtime_version value="2022.3.0-9052-9752fafe8eb-releases/2022/3" />
		<conversion_parameters>
			<data_type value="FP32" />
			<framework value="onnx" />
		</conversion_parameters>
		<framework>
			<names value="{0: 'LV', 1: 'LA', 2: 'MV', 3: 'A2C'}" />
			<stride value="32" />
		</framework>
		<legacy_frontend value="False" />
	</rt_info>
</net>
