<?xml version="1.0"?>
<mapping>
	<map>
		<framework name="images" output_port_id="images" />
		<IR name="images" output_port_id="0" />
	</map>
	<map>
		<framework name="model.0.conv.weight" output_port_id="model.0.conv.weight" />
		<IR name="model.0.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.0/conv/Conv" output_port_id="/model.0/conv/Conv_output_0" />
		<IR name="/model.0/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.0/act/Mul" output_port_id="/model.0/act/Mul_output_0" />
		<IR name="/model.0/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.1.conv.weight" output_port_id="model.1.conv.weight" />
		<IR name="model.1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.1/conv/Conv" output_port_id="/model.1/conv/Conv_output_0" />
		<IR name="/model.1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.1/act/Mul" output_port_id="/model.1/act/Mul_output_0" />
		<IR name="/model.1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.2.cv1.conv.weight" output_port_id="model.2.cv1.conv.weight" />
		<IR name="model.2.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.2/cv1/conv/Conv" output_port_id="/model.2/cv1/conv/Conv_output_0" />
		<IR name="/model.2/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.2/cv1/act/Mul" output_port_id="/model.2/cv1/act/Mul_output_0" />
		<IR name="/model.2/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.2.m.0.cv1.conv.weight" output_port_id="model.2.m.0.cv1.conv.weight" />
		<IR name="model.2.m.0.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.2/m/m.0/cv1/conv/Conv" output_port_id="/model.2/m/m.0/cv1/conv/Conv_output_0" />
		<IR name="/model.2/m/m.0/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.2/m/m.0/cv1/act/Mul" output_port_id="/model.2/m/m.0/cv1/act/Mul_output_0" />
		<IR name="/model.2/m/m.0/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.2.m.0.cv2.conv.weight" output_port_id="model.2.m.0.cv2.conv.weight" />
		<IR name="model.2.m.0.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.2/m/m.0/cv2/conv/Conv" output_port_id="/model.2/m/m.0/cv2/conv/Conv_output_0" />
		<IR name="/model.2/m/m.0/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.2/m/m.0/cv2/act/Mul" output_port_id="/model.2/m/m.0/cv2/act/Mul_output_0" />
		<IR name="/model.2/m/m.0/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.2/m/m.0/Add" output_port_id="/model.2/m/m.0/Add_output_0" />
		<IR name="/model.2/m/m.0/Add" output_port_id="2" />
	</map>
	<map>
		<framework name="model.2.cv2.conv.weight" output_port_id="model.2.cv2.conv.weight" />
		<IR name="model.2.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.2/cv2/conv/Conv" output_port_id="/model.2/cv2/conv/Conv_output_0" />
		<IR name="/model.2/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.2/cv2/act/Mul" output_port_id="/model.2/cv2/act/Mul_output_0" />
		<IR name="/model.2/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.2/Concat" output_port_id="/model.2/Concat_output_0" />
		<IR name="/model.2/Concat" output_port_id="2" />
	</map>
	<map>
		<framework name="model.2.cv3.conv.weight" output_port_id="model.2.cv3.conv.weight" />
		<IR name="model.2.cv3.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.2/cv3/conv/Conv" output_port_id="/model.2/cv3/conv/Conv_output_0" />
		<IR name="/model.2/cv3/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.2/cv3/act/Mul" output_port_id="/model.2/cv3/act/Mul_output_0" />
		<IR name="/model.2/cv3/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.3.conv.weight" output_port_id="model.3.conv.weight" />
		<IR name="model.3.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.3/conv/Conv" output_port_id="/model.3/conv/Conv_output_0" />
		<IR name="/model.3/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.3/act/Mul" output_port_id="/model.3/act/Mul_output_0" />
		<IR name="/model.3/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.4.cv1.conv.weight" output_port_id="model.4.cv1.conv.weight" />
		<IR name="model.4.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.4/cv1/conv/Conv" output_port_id="/model.4/cv1/conv/Conv_output_0" />
		<IR name="/model.4/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.4/cv1/act/Mul" output_port_id="/model.4/cv1/act/Mul_output_0" />
		<IR name="/model.4/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.4.m.0.cv1.conv.weight" output_port_id="model.4.m.0.cv1.conv.weight" />
		<IR name="model.4.m.0.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.4/m/m.0/cv1/conv/Conv" output_port_id="/model.4/m/m.0/cv1/conv/Conv_output_0" />
		<IR name="/model.4/m/m.0/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.4/m/m.0/cv1/act/Mul" output_port_id="/model.4/m/m.0/cv1/act/Mul_output_0" />
		<IR name="/model.4/m/m.0/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.4.m.0.cv2.conv.weight" output_port_id="model.4.m.0.cv2.conv.weight" />
		<IR name="model.4.m.0.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.4/m/m.0/cv2/conv/Conv" output_port_id="/model.4/m/m.0/cv2/conv/Conv_output_0" />
		<IR name="/model.4/m/m.0/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.4/m/m.0/cv2/act/Mul" output_port_id="/model.4/m/m.0/cv2/act/Mul_output_0" />
		<IR name="/model.4/m/m.0/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.4/m/m.0/Add" output_port_id="/model.4/m/m.0/Add_output_0" />
		<IR name="/model.4/m/m.0/Add" output_port_id="2" />
	</map>
	<map>
		<framework name="model.4.m.1.cv1.conv.weight" output_port_id="model.4.m.1.cv1.conv.weight" />
		<IR name="model.4.m.1.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.4/m/m.1/cv1/conv/Conv" output_port_id="/model.4/m/m.1/cv1/conv/Conv_output_0" />
		<IR name="/model.4/m/m.1/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.4/m/m.1/cv1/act/Mul" output_port_id="/model.4/m/m.1/cv1/act/Mul_output_0" />
		<IR name="/model.4/m/m.1/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.4.m.1.cv2.conv.weight" output_port_id="model.4.m.1.cv2.conv.weight" />
		<IR name="model.4.m.1.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.4/m/m.1/cv2/conv/Conv" output_port_id="/model.4/m/m.1/cv2/conv/Conv_output_0" />
		<IR name="/model.4/m/m.1/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.4/m/m.1/cv2/act/Mul" output_port_id="/model.4/m/m.1/cv2/act/Mul_output_0" />
		<IR name="/model.4/m/m.1/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.4/m/m.1/Add" output_port_id="/model.4/m/m.1/Add_output_0" />
		<IR name="/model.4/m/m.1/Add" output_port_id="2" />
	</map>
	<map>
		<framework name="model.4.cv2.conv.weight" output_port_id="model.4.cv2.conv.weight" />
		<IR name="model.4.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.4/cv2/conv/Conv" output_port_id="/model.4/cv2/conv/Conv_output_0" />
		<IR name="/model.4/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.4/cv2/act/Mul" output_port_id="/model.4/cv2/act/Mul_output_0" />
		<IR name="/model.4/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.4/Concat" output_port_id="/model.4/Concat_output_0" />
		<IR name="/model.4/Concat" output_port_id="2" />
	</map>
	<map>
		<framework name="model.4.cv3.conv.weight" output_port_id="model.4.cv3.conv.weight" />
		<IR name="model.4.cv3.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.4/cv3/conv/Conv" output_port_id="/model.4/cv3/conv/Conv_output_0" />
		<IR name="/model.4/cv3/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.4/cv3/act/Mul" output_port_id="/model.4/cv3/act/Mul_output_0" />
		<IR name="/model.4/cv3/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.5.conv.weight" output_port_id="model.5.conv.weight" />
		<IR name="model.5.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.5/conv/Conv" output_port_id="/model.5/conv/Conv_output_0" />
		<IR name="/model.5/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.5/act/Mul" output_port_id="/model.5/act/Mul_output_0" />
		<IR name="/model.5/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.6.cv1.conv.weight" output_port_id="model.6.cv1.conv.weight" />
		<IR name="model.6.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.6/cv1/conv/Conv" output_port_id="/model.6/cv1/conv/Conv_output_0" />
		<IR name="/model.6/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.6/cv1/act/Mul" output_port_id="/model.6/cv1/act/Mul_output_0" />
		<IR name="/model.6/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.6.m.0.cv1.conv.weight" output_port_id="model.6.m.0.cv1.conv.weight" />
		<IR name="model.6.m.0.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.6/m/m.0/cv1/conv/Conv" output_port_id="/model.6/m/m.0/cv1/conv/Conv_output_0" />
		<IR name="/model.6/m/m.0/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.6/m/m.0/cv1/act/Mul" output_port_id="/model.6/m/m.0/cv1/act/Mul_output_0" />
		<IR name="/model.6/m/m.0/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.6.m.0.cv2.conv.weight" output_port_id="model.6.m.0.cv2.conv.weight" />
		<IR name="model.6.m.0.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.6/m/m.0/cv2/conv/Conv" output_port_id="/model.6/m/m.0/cv2/conv/Conv_output_0" />
		<IR name="/model.6/m/m.0/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.6/m/m.0/cv2/act/Mul" output_port_id="/model.6/m/m.0/cv2/act/Mul_output_0" />
		<IR name="/model.6/m/m.0/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.6/m/m.0/Add" output_port_id="/model.6/m/m.0/Add_output_0" />
		<IR name="/model.6/m/m.0/Add" output_port_id="2" />
	</map>
	<map>
		<framework name="model.6.m.1.cv1.conv.weight" output_port_id="model.6.m.1.cv1.conv.weight" />
		<IR name="model.6.m.1.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.6/m/m.1/cv1/conv/Conv" output_port_id="/model.6/m/m.1/cv1/conv/Conv_output_0" />
		<IR name="/model.6/m/m.1/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.6/m/m.1/cv1/act/Mul" output_port_id="/model.6/m/m.1/cv1/act/Mul_output_0" />
		<IR name="/model.6/m/m.1/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.6.m.1.cv2.conv.weight" output_port_id="model.6.m.1.cv2.conv.weight" />
		<IR name="model.6.m.1.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.6/m/m.1/cv2/conv/Conv" output_port_id="/model.6/m/m.1/cv2/conv/Conv_output_0" />
		<IR name="/model.6/m/m.1/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.6/m/m.1/cv2/act/Mul" output_port_id="/model.6/m/m.1/cv2/act/Mul_output_0" />
		<IR name="/model.6/m/m.1/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.6/m/m.1/Add" output_port_id="/model.6/m/m.1/Add_output_0" />
		<IR name="/model.6/m/m.1/Add" output_port_id="2" />
	</map>
	<map>
		<framework name="model.6.m.2.cv1.conv.weight" output_port_id="model.6.m.2.cv1.conv.weight" />
		<IR name="model.6.m.2.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.6/m/m.2/cv1/conv/Conv" output_port_id="/model.6/m/m.2/cv1/conv/Conv_output_0" />
		<IR name="/model.6/m/m.2/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.6/m/m.2/cv1/act/Mul" output_port_id="/model.6/m/m.2/cv1/act/Mul_output_0" />
		<IR name="/model.6/m/m.2/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.6.m.2.cv2.conv.weight" output_port_id="model.6.m.2.cv2.conv.weight" />
		<IR name="model.6.m.2.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.6/m/m.2/cv2/conv/Conv" output_port_id="/model.6/m/m.2/cv2/conv/Conv_output_0" />
		<IR name="/model.6/m/m.2/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.6/m/m.2/cv2/act/Mul" output_port_id="/model.6/m/m.2/cv2/act/Mul_output_0" />
		<IR name="/model.6/m/m.2/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.6/m/m.2/Add" output_port_id="/model.6/m/m.2/Add_output_0" />
		<IR name="/model.6/m/m.2/Add" output_port_id="2" />
	</map>
	<map>
		<framework name="model.6.cv2.conv.weight" output_port_id="model.6.cv2.conv.weight" />
		<IR name="model.6.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.6/cv2/conv/Conv" output_port_id="/model.6/cv2/conv/Conv_output_0" />
		<IR name="/model.6/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.6/cv2/act/Mul" output_port_id="/model.6/cv2/act/Mul_output_0" />
		<IR name="/model.6/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.6/Concat" output_port_id="/model.6/Concat_output_0" />
		<IR name="/model.6/Concat" output_port_id="2" />
	</map>
	<map>
		<framework name="model.6.cv3.conv.weight" output_port_id="model.6.cv3.conv.weight" />
		<IR name="model.6.cv3.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.6/cv3/conv/Conv" output_port_id="/model.6/cv3/conv/Conv_output_0" />
		<IR name="/model.6/cv3/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.6/cv3/act/Mul" output_port_id="/model.6/cv3/act/Mul_output_0" />
		<IR name="/model.6/cv3/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.7.conv.weight" output_port_id="model.7.conv.weight" />
		<IR name="model.7.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.7/conv/Conv" output_port_id="/model.7/conv/Conv_output_0" />
		<IR name="/model.7/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.7/act/Mul" output_port_id="/model.7/act/Mul_output_0" />
		<IR name="/model.7/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.8.cv1.conv.weight" output_port_id="model.8.cv1.conv.weight" />
		<IR name="model.8.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.8/cv1/conv/Conv" output_port_id="/model.8/cv1/conv/Conv_output_0" />
		<IR name="/model.8/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.8/cv1/act/Mul" output_port_id="/model.8/cv1/act/Mul_output_0" />
		<IR name="/model.8/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.8.m.0.cv1.conv.weight" output_port_id="model.8.m.0.cv1.conv.weight" />
		<IR name="model.8.m.0.cv1.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.8/m/m.0/cv1/conv/Conv" output_port_id="/model.8/m/m.0/cv1/conv/Conv_output_0" />
		<IR name="/model.8/m/m.0/cv1/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.8/m/m.0/cv1/act/Mul" output_port_id="/model.8/m/m.0/cv1/act/Mul_output_0" />
		<IR name="/model.8/m/m.0/cv1/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.8.m.0.cv2.conv.weight" output_port_id="model.8.m.0.cv2.conv.weight" />
		<IR name="model.8.m.0.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.8/m/m.0/cv2/conv/Conv" output_port_id="/model.8/m/m.0/cv2/conv/Conv_output_0" />
		<IR name="/model.8/m/m.0/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.8/m/m.0/cv2/act/Mul" output_port_id="/model.8/m/m.0/cv2/act/Mul_output_0" />
		<IR name="/model.8/m/m.0/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.8/m/m.0/Add" output_port_id="/model.8/m/m.0/Add_output_0" />
		<IR name="/model.8/m/m.0/Add" output_port_id="2" />
	</map>
	<map>
		<framework name="model.8.cv2.conv.weight" output_port_id="model.8.cv2.conv.weight" />
		<IR name="model.8.cv2.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.8/cv2/conv/Conv" output_port_id="/model.8/cv2/conv/Conv_output_0" />
		<IR name="/model.8/cv2/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.8/cv2/act/Mul" output_port_id="/model.8/cv2/act/Mul_output_0" />
		<IR name="/model.8/cv2/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.8/Concat" output_port_id="/model.8/Concat_output_0" />
		<IR name="/model.8/Concat" output_port_id="2" />
	</map>
	<map>
		<framework name="model.8.cv3.conv.weight" output_port_id="model.8.cv3.conv.weight" />
		<IR name="model.8.cv3.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.8/cv3/conv/Conv" output_port_id="/model.8/cv3/conv/Conv_output_0" />
		<IR name="/model.8/cv3/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.8/cv3/act/Mul" output_port_id="/model.8/cv3/act/Mul_output_0" />
		<IR name="/model.8/cv3/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="model.9.conv.conv.weight" output_port_id="model.9.conv.conv.weight" />
		<IR name="model.9.conv.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/model.9/conv/conv/Conv" output_port_id="/model.9/conv/conv/Conv_output_0" />
		<IR name="/model.9/conv/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.9/conv/act/Mul" output_port_id="/model.9/conv/act/Mul_output_0" />
		<IR name="/model.9/conv/act/Mul" output_port_id="1" />
	</map>
	<map>
		<framework name="/model.9/pool/GlobalAveragePool" output_port_id="/model.9/pool/GlobalAveragePool_output_0" />
		<IR name="/model.9/pool/GlobalAveragePool" output_port_id="2" />
	</map>
	<map>
		<framework name="/model.9/Flatten" output_port_id="/model.9/Flatten_output_0" />
		<IR name="/model.9/Flatten" output_port_id="2" />
	</map>
	<map>
		<framework name="model.9.linear.weight" output_port_id="model.9.linear.weight" />
		<IR name="model.9.linear.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="output0" output_port_id="output0" />
		<IR name="output0" output_port_id="2" />
	</map>
</mapping>
