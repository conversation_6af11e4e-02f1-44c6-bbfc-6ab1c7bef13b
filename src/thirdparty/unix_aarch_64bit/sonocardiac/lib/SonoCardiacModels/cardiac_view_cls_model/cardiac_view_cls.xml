<?xml version="1.0"?>
<net name="torch_jit" version="11">
	<layers>
		<layer id="0" name="images" type="Parameter" version="opset1">
			<data shape="1,3,224,224" element_type="f32" />
			<output>
				<port id="0" precision="FP32" names="images">
					<dim>1</dim>
					<dim>3</dim>
					<dim>224</dim>
					<dim>224</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="model.0.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 3, 6, 6" offset="0" size="6912" />
			<output>
				<port id="0" precision="FP32" names="model.0.conv.weight">
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="/model.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>224</dim>
					<dim>224</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
					<dim>6</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>112</dim>
					<dim>112</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="Reshape_87" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="6912" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="/model.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>112</dim>
					<dim>112</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>112</dim>
					<dim>112</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="/model.0/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>112</dim>
					<dim>112</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.0/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>112</dim>
					<dim>112</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="model.1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 16, 3, 3" offset="6976" size="18432" />
			<output>
				<port id="0" precision="FP32" names="model.1.conv.weight">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="/model.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>112</dim>
					<dim>112</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="Reshape_137" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="25408" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="/model.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="/model.1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="model.2.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 32, 1, 1" offset="25536" size="2048" />
			<output>
				<port id="0" precision="FP32" names="model.2.cv1.conv.weight">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="/model.2/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="Reshape_187" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="27584" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="/model.2/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="/model.2/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="model.2.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 16, 1, 1" offset="27648" size="1024" />
			<output>
				<port id="0" precision="FP32" names="model.2.m.0.cv1.conv.weight">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="/model.2/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="Reshape_237" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="28672" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="/model.2/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="/model.2/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="model.2.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 16, 3, 3" offset="28736" size="9216" />
			<output>
				<port id="0" precision="FP32" names="model.2.m.0.cv2.conv.weight">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="/model.2/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="Reshape_287" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="37952" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="/model.2/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="/model.2/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="/model.2/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="model.2.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 32, 1, 1" offset="38016" size="2048" />
			<output>
				<port id="0" precision="FP32" names="model.2.cv2.conv.weight">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="/model.2/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="Reshape_338" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="40064" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="/model.2/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="/model.2/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="/model.2/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/Concat_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="model.2.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 1, 1" offset="40128" size="4096" />
			<output>
				<port id="0" precision="FP32" names="model.2.cv3.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="/model.2/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="Reshape_389" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="44224" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="/model.2/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="37" name="/model.2/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="model.3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 32, 3, 3" offset="44352" size="73728" />
			<output>
				<port id="0" precision="FP32" names="model.3.conv.weight">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="39" name="/model.3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>56</dim>
					<dim>56</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="Reshape_439" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="118080" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="41" name="/model.3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="/model.3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.3/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="model.4.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 64, 1, 1" offset="118336" size="8192" />
			<output>
				<port id="0" precision="FP32" names="model.4.cv1.conv.weight">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="/model.4/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="45" name="Reshape_489" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="126528" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="46" name="/model.4/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="47" name="/model.4/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="48" name="model.4.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 1, 1" offset="126656" size="4096" />
			<output>
				<port id="0" precision="FP32" names="model.4.m.0.cv1.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="49" name="/model.4/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="Reshape_539" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="130752" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="/model.4/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="/model.4/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="53" name="model.4.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 3, 3" offset="130880" size="36864" />
			<output>
				<port id="0" precision="FP32" names="model.4.m.0.cv2.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="54" name="/model.4/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="55" name="Reshape_589" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="167744" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="56" name="/model.4/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="/model.4/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="58" name="/model.4/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="model.4.m.1.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 1, 1" offset="167872" size="4096" />
			<output>
				<port id="0" precision="FP32" names="model.4.m.1.cv1.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="60" name="/model.4/m/m.1/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="Reshape_640" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="171968" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="62" name="/model.4/m/m.1/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.1/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="/model.4/m/m.1/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m/m.1/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="64" name="model.4.m.1.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 3, 3" offset="172096" size="36864" />
			<output>
				<port id="0" precision="FP32" names="model.4.m.1.cv2.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="65" name="/model.4/m/m.1/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="66" name="Reshape_690" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="208960" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="/model.4/m/m.1/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.1/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="68" name="/model.4/m/m.1/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m/m.1/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="69" name="/model.4/m/m.1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m/m.1/Add_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="70" name="model.4.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 64, 1, 1" offset="209088" size="8192" />
			<output>
				<port id="0" precision="FP32" names="model.4.cv2.conv.weight">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="71" name="/model.4/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="72" name="Reshape_741" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="217280" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="73" name="/model.4/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="74" name="/model.4/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="75" name="/model.4/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/Concat_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="76" name="model.4.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="217408" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.4.cv3.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="77" name="/model.4/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="78" name="Reshape_792" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="233792" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="79" name="/model.4/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="80" name="/model.4/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
			</output>
		</layer>
		<layer id="81" name="model.5.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 64, 3, 3" offset="234048" size="294912" />
			<output>
				<port id="0" precision="FP32" names="model.5.conv.weight">
					<dim>128</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="/model.5/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>28</dim>
					<dim>28</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="Reshape_842" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="528960" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="/model.5/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.5/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="85" name="/model.5/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.5/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="86" name="model.6.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 128, 1, 1" offset="529472" size="32768" />
			<output>
				<port id="0" precision="FP32" names="model.6.cv1.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="87" name="/model.6/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="88" name="Reshape_892" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="562240" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="89" name="/model.6/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="/model.6/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="91" name="model.6.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="562496" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.0.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="/model.6/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="93" name="Reshape_942" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="578880" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="94" name="/model.6/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="95" name="/model.6/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="96" name="model.6.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="579136" size="147456" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.0.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="/model.6/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="Reshape_992" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="726592" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="/model.6/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="/model.6/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="/model.6/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="model.6.m.1.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="726848" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.1.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="/model.6/m/m.1/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="Reshape_1043" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="743232" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="/model.6/m/m.1/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.1/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="/model.6/m/m.1/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.1/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="model.6.m.1.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="743488" size="147456" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.1.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="/model.6/m/m.1/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="Reshape_1093" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="890944" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="110" name="/model.6/m/m.1/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.1/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="111" name="/model.6/m/m.1/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.1/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="112" name="/model.6/m/m.1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.1/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="113" name="model.6.m.2.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 1, 1" offset="891200" size="16384" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.2.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="/model.6/m/m.2/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="Reshape_1144" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="907584" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="/model.6/m/m.2/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.2/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="/model.6/m/m.2/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.2/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="model.6.m.2.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="907840" size="147456" />
			<output>
				<port id="0" precision="FP32" names="model.6.m.2.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="119" name="/model.6/m/m.2/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="120" name="Reshape_1194" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="1055296" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="/model.6/m/m.2/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.2/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="/model.6/m/m.2/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m/m.2/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="/model.6/m/m.2/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m/m.2/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="model.6.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 128, 1, 1" offset="1055552" size="32768" />
			<output>
				<port id="0" precision="FP32" names="model.6.cv2.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="/model.6/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="126" name="Reshape_1245" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="1088320" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="127" name="/model.6/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="128" name="/model.6/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="129" name="/model.6/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="model.6.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 1, 1" offset="1088576" size="65536" />
			<output>
				<port id="0" precision="FP32" names="model.6.cv3.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="131" name="/model.6/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="132" name="Reshape_1296" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="1154112" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="133" name="/model.6/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="134" name="/model.6/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
			</output>
		</layer>
		<layer id="135" name="model.7.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="256, 128, 3, 3" offset="1154624" size="1179648" />
			<output>
				<port id="0" precision="FP32" names="model.7.conv.weight">
					<dim>256</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="136" name="/model.7/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>14</dim>
					<dim>14</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="137" name="Reshape_1346" type="Const" version="opset1">
			<data element_type="f32" shape="1, 256, 1, 1" offset="2334272" size="1024" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="138" name="/model.7/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.7/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="139" name="/model.7/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.7/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="140" name="model.8.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 256, 1, 1" offset="2335296" size="131072" />
			<output>
				<port id="0" precision="FP32" names="model.8.cv1.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="/model.8/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="Reshape_1396" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="2466368" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="143" name="/model.8/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="/model.8/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="model.8.m.0.cv1.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 1, 1" offset="2466880" size="65536" />
			<output>
				<port id="0" precision="FP32" names="model.8.m.0.cv1.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="146" name="/model.8/m/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="147" name="Reshape_1446" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="2532416" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="148" name="/model.8/m/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/m/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="149" name="/model.8/m/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/m/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="150" name="model.8.m.0.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 128, 3, 3" offset="2532928" size="589824" />
			<output>
				<port id="0" precision="FP32" names="model.8.m.0.cv2.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="151" name="/model.8/m/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="152" name="Reshape_1496" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="3122752" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="153" name="/model.8/m/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/m/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="154" name="/model.8/m/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/m/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="155" name="/model.8/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="model.8.cv2.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 256, 1, 1" offset="3123264" size="131072" />
			<output>
				<port id="0" precision="FP32" names="model.8.cv2.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="157" name="/model.8/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="Reshape_1547" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128, 1, 1" offset="3254336" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="/model.8/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="/model.8/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="/model.8/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="162" name="model.8.cv3.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="256, 256, 1, 1" offset="3254848" size="262144" />
			<output>
				<port id="0" precision="FP32" names="model.8.cv3.conv.weight">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="163" name="/model.8/cv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="164" name="Reshape_1598" type="Const" version="opset1">
			<data element_type="f32" shape="1, 256, 1, 1" offset="3516992" size="1024" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="165" name="/model.8/cv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/cv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="/model.8/cv3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/cv3/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="167" name="model.9.conv.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="1280, 256, 1, 1" offset="3518016" size="1310720" />
			<output>
				<port id="0" precision="FP32" names="model.9.conv.conv.weight">
					<dim>1280</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="168" name="/model.9/conv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1280</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="169" name="Reshape_1648" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1280, 1, 1" offset="4828736" size="5120" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="170" name="/model.9/conv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.9/conv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="/model.9/conv/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/conv/act/Mul_output_0">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="Range_1684" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4833856" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="173" name="/model.9/pool/GlobalAveragePool" type="ReduceMean" version="opset1">
			<data keep_dims="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>7</dim>
					<dim>7</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.9/pool/GlobalAveragePool_output_0">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="Constant_1709" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4833872" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="175" name="/model.9/Flatten" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1280</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.9/Flatten_output_0">
					<dim>1</dim>
					<dim>1280</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="model.9.linear.weight" type="Const" version="opset1">
			<data element_type="f32" shape="4, 1280" offset="4833888" size="20480" />
			<output>
				<port id="0" precision="FP32" names="model.9.linear.weight">
					<dim>4</dim>
					<dim>1280</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="/model.9/linear/Gemm/WithoutBiases" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1280</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>1280</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="Constant_5533" type="Const" version="opset1">
			<data element_type="f32" shape="1, 4" offset="4854368" size="16" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="output0" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="output0">
					<dim>1</dim>
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="output0/sink_port_0" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="2" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="2" to-port="1" />
		<edge from-layer="2" from-port="2" to-layer="4" to-port="0" />
		<edge from-layer="3" from-port="0" to-layer="4" to-port="1" />
		<edge from-layer="4" from-port="2" to-layer="5" to-port="0" />
		<edge from-layer="5" from-port="1" to-layer="7" to-port="0" />
		<edge from-layer="6" from-port="0" to-layer="7" to-port="1" />
		<edge from-layer="7" from-port="2" to-layer="9" to-port="0" />
		<edge from-layer="8" from-port="0" to-layer="9" to-port="1" />
		<edge from-layer="9" from-port="2" to-layer="10" to-port="0" />
		<edge from-layer="10" from-port="1" to-layer="28" to-port="0" />
		<edge from-layer="10" from-port="1" to-layer="12" to-port="0" />
		<edge from-layer="11" from-port="0" to-layer="12" to-port="1" />
		<edge from-layer="12" from-port="2" to-layer="14" to-port="0" />
		<edge from-layer="13" from-port="0" to-layer="14" to-port="1" />
		<edge from-layer="14" from-port="2" to-layer="15" to-port="0" />
		<edge from-layer="15" from-port="1" to-layer="17" to-port="0" />
		<edge from-layer="15" from-port="1" to-layer="26" to-port="0" />
		<edge from-layer="16" from-port="0" to-layer="17" to-port="1" />
		<edge from-layer="17" from-port="2" to-layer="19" to-port="0" />
		<edge from-layer="18" from-port="0" to-layer="19" to-port="1" />
		<edge from-layer="19" from-port="2" to-layer="20" to-port="0" />
		<edge from-layer="20" from-port="1" to-layer="22" to-port="0" />
		<edge from-layer="21" from-port="0" to-layer="22" to-port="1" />
		<edge from-layer="22" from-port="2" to-layer="24" to-port="0" />
		<edge from-layer="23" from-port="0" to-layer="24" to-port="1" />
		<edge from-layer="24" from-port="2" to-layer="25" to-port="0" />
		<edge from-layer="25" from-port="1" to-layer="26" to-port="1" />
		<edge from-layer="26" from-port="2" to-layer="32" to-port="0" />
		<edge from-layer="27" from-port="0" to-layer="28" to-port="1" />
		<edge from-layer="28" from-port="2" to-layer="30" to-port="0" />
		<edge from-layer="29" from-port="0" to-layer="30" to-port="1" />
		<edge from-layer="30" from-port="2" to-layer="31" to-port="0" />
		<edge from-layer="31" from-port="1" to-layer="32" to-port="1" />
		<edge from-layer="32" from-port="2" to-layer="34" to-port="0" />
		<edge from-layer="33" from-port="0" to-layer="34" to-port="1" />
		<edge from-layer="34" from-port="2" to-layer="36" to-port="0" />
		<edge from-layer="35" from-port="0" to-layer="36" to-port="1" />
		<edge from-layer="36" from-port="2" to-layer="37" to-port="0" />
		<edge from-layer="37" from-port="1" to-layer="39" to-port="0" />
		<edge from-layer="38" from-port="0" to-layer="39" to-port="1" />
		<edge from-layer="39" from-port="2" to-layer="41" to-port="0" />
		<edge from-layer="40" from-port="0" to-layer="41" to-port="1" />
		<edge from-layer="41" from-port="2" to-layer="42" to-port="0" />
		<edge from-layer="42" from-port="1" to-layer="44" to-port="0" />
		<edge from-layer="42" from-port="1" to-layer="71" to-port="0" />
		<edge from-layer="43" from-port="0" to-layer="44" to-port="1" />
		<edge from-layer="44" from-port="2" to-layer="46" to-port="0" />
		<edge from-layer="45" from-port="0" to-layer="46" to-port="1" />
		<edge from-layer="46" from-port="2" to-layer="47" to-port="0" />
		<edge from-layer="47" from-port="1" to-layer="49" to-port="0" />
		<edge from-layer="47" from-port="1" to-layer="58" to-port="0" />
		<edge from-layer="48" from-port="0" to-layer="49" to-port="1" />
		<edge from-layer="49" from-port="2" to-layer="51" to-port="0" />
		<edge from-layer="50" from-port="0" to-layer="51" to-port="1" />
		<edge from-layer="51" from-port="2" to-layer="52" to-port="0" />
		<edge from-layer="52" from-port="1" to-layer="54" to-port="0" />
		<edge from-layer="53" from-port="0" to-layer="54" to-port="1" />
		<edge from-layer="54" from-port="2" to-layer="56" to-port="0" />
		<edge from-layer="55" from-port="0" to-layer="56" to-port="1" />
		<edge from-layer="56" from-port="2" to-layer="57" to-port="0" />
		<edge from-layer="57" from-port="1" to-layer="58" to-port="1" />
		<edge from-layer="58" from-port="2" to-layer="60" to-port="0" />
		<edge from-layer="58" from-port="2" to-layer="69" to-port="0" />
		<edge from-layer="59" from-port="0" to-layer="60" to-port="1" />
		<edge from-layer="60" from-port="2" to-layer="62" to-port="0" />
		<edge from-layer="61" from-port="0" to-layer="62" to-port="1" />
		<edge from-layer="62" from-port="2" to-layer="63" to-port="0" />
		<edge from-layer="63" from-port="1" to-layer="65" to-port="0" />
		<edge from-layer="64" from-port="0" to-layer="65" to-port="1" />
		<edge from-layer="65" from-port="2" to-layer="67" to-port="0" />
		<edge from-layer="66" from-port="0" to-layer="67" to-port="1" />
		<edge from-layer="67" from-port="2" to-layer="68" to-port="0" />
		<edge from-layer="68" from-port="1" to-layer="69" to-port="1" />
		<edge from-layer="69" from-port="2" to-layer="75" to-port="0" />
		<edge from-layer="70" from-port="0" to-layer="71" to-port="1" />
		<edge from-layer="71" from-port="2" to-layer="73" to-port="0" />
		<edge from-layer="72" from-port="0" to-layer="73" to-port="1" />
		<edge from-layer="73" from-port="2" to-layer="74" to-port="0" />
		<edge from-layer="74" from-port="1" to-layer="75" to-port="1" />
		<edge from-layer="75" from-port="2" to-layer="77" to-port="0" />
		<edge from-layer="76" from-port="0" to-layer="77" to-port="1" />
		<edge from-layer="77" from-port="2" to-layer="79" to-port="0" />
		<edge from-layer="78" from-port="0" to-layer="79" to-port="1" />
		<edge from-layer="79" from-port="2" to-layer="80" to-port="0" />
		<edge from-layer="80" from-port="1" to-layer="82" to-port="0" />
		<edge from-layer="81" from-port="0" to-layer="82" to-port="1" />
		<edge from-layer="82" from-port="2" to-layer="84" to-port="0" />
		<edge from-layer="83" from-port="0" to-layer="84" to-port="1" />
		<edge from-layer="84" from-port="2" to-layer="85" to-port="0" />
		<edge from-layer="85" from-port="1" to-layer="87" to-port="0" />
		<edge from-layer="85" from-port="1" to-layer="125" to-port="0" />
		<edge from-layer="86" from-port="0" to-layer="87" to-port="1" />
		<edge from-layer="87" from-port="2" to-layer="89" to-port="0" />
		<edge from-layer="88" from-port="0" to-layer="89" to-port="1" />
		<edge from-layer="89" from-port="2" to-layer="90" to-port="0" />
		<edge from-layer="90" from-port="1" to-layer="92" to-port="0" />
		<edge from-layer="90" from-port="1" to-layer="101" to-port="0" />
		<edge from-layer="91" from-port="0" to-layer="92" to-port="1" />
		<edge from-layer="92" from-port="2" to-layer="94" to-port="0" />
		<edge from-layer="93" from-port="0" to-layer="94" to-port="1" />
		<edge from-layer="94" from-port="2" to-layer="95" to-port="0" />
		<edge from-layer="95" from-port="1" to-layer="97" to-port="0" />
		<edge from-layer="96" from-port="0" to-layer="97" to-port="1" />
		<edge from-layer="97" from-port="2" to-layer="99" to-port="0" />
		<edge from-layer="98" from-port="0" to-layer="99" to-port="1" />
		<edge from-layer="99" from-port="2" to-layer="100" to-port="0" />
		<edge from-layer="100" from-port="1" to-layer="101" to-port="1" />
		<edge from-layer="101" from-port="2" to-layer="103" to-port="0" />
		<edge from-layer="101" from-port="2" to-layer="112" to-port="0" />
		<edge from-layer="102" from-port="0" to-layer="103" to-port="1" />
		<edge from-layer="103" from-port="2" to-layer="105" to-port="0" />
		<edge from-layer="104" from-port="0" to-layer="105" to-port="1" />
		<edge from-layer="105" from-port="2" to-layer="106" to-port="0" />
		<edge from-layer="106" from-port="1" to-layer="108" to-port="0" />
		<edge from-layer="107" from-port="0" to-layer="108" to-port="1" />
		<edge from-layer="108" from-port="2" to-layer="110" to-port="0" />
		<edge from-layer="109" from-port="0" to-layer="110" to-port="1" />
		<edge from-layer="110" from-port="2" to-layer="111" to-port="0" />
		<edge from-layer="111" from-port="1" to-layer="112" to-port="1" />
		<edge from-layer="112" from-port="2" to-layer="114" to-port="0" />
		<edge from-layer="112" from-port="2" to-layer="123" to-port="0" />
		<edge from-layer="113" from-port="0" to-layer="114" to-port="1" />
		<edge from-layer="114" from-port="2" to-layer="116" to-port="0" />
		<edge from-layer="115" from-port="0" to-layer="116" to-port="1" />
		<edge from-layer="116" from-port="2" to-layer="117" to-port="0" />
		<edge from-layer="117" from-port="1" to-layer="119" to-port="0" />
		<edge from-layer="118" from-port="0" to-layer="119" to-port="1" />
		<edge from-layer="119" from-port="2" to-layer="121" to-port="0" />
		<edge from-layer="120" from-port="0" to-layer="121" to-port="1" />
		<edge from-layer="121" from-port="2" to-layer="122" to-port="0" />
		<edge from-layer="122" from-port="1" to-layer="123" to-port="1" />
		<edge from-layer="123" from-port="2" to-layer="129" to-port="0" />
		<edge from-layer="124" from-port="0" to-layer="125" to-port="1" />
		<edge from-layer="125" from-port="2" to-layer="127" to-port="0" />
		<edge from-layer="126" from-port="0" to-layer="127" to-port="1" />
		<edge from-layer="127" from-port="2" to-layer="128" to-port="0" />
		<edge from-layer="128" from-port="1" to-layer="129" to-port="1" />
		<edge from-layer="129" from-port="2" to-layer="131" to-port="0" />
		<edge from-layer="130" from-port="0" to-layer="131" to-port="1" />
		<edge from-layer="131" from-port="2" to-layer="133" to-port="0" />
		<edge from-layer="132" from-port="0" to-layer="133" to-port="1" />
		<edge from-layer="133" from-port="2" to-layer="134" to-port="0" />
		<edge from-layer="134" from-port="1" to-layer="136" to-port="0" />
		<edge from-layer="135" from-port="0" to-layer="136" to-port="1" />
		<edge from-layer="136" from-port="2" to-layer="138" to-port="0" />
		<edge from-layer="137" from-port="0" to-layer="138" to-port="1" />
		<edge from-layer="138" from-port="2" to-layer="139" to-port="0" />
		<edge from-layer="139" from-port="1" to-layer="141" to-port="0" />
		<edge from-layer="139" from-port="1" to-layer="157" to-port="0" />
		<edge from-layer="140" from-port="0" to-layer="141" to-port="1" />
		<edge from-layer="141" from-port="2" to-layer="143" to-port="0" />
		<edge from-layer="142" from-port="0" to-layer="143" to-port="1" />
		<edge from-layer="143" from-port="2" to-layer="144" to-port="0" />
		<edge from-layer="144" from-port="1" to-layer="146" to-port="0" />
		<edge from-layer="144" from-port="1" to-layer="155" to-port="0" />
		<edge from-layer="145" from-port="0" to-layer="146" to-port="1" />
		<edge from-layer="146" from-port="2" to-layer="148" to-port="0" />
		<edge from-layer="147" from-port="0" to-layer="148" to-port="1" />
		<edge from-layer="148" from-port="2" to-layer="149" to-port="0" />
		<edge from-layer="149" from-port="1" to-layer="151" to-port="0" />
		<edge from-layer="150" from-port="0" to-layer="151" to-port="1" />
		<edge from-layer="151" from-port="2" to-layer="153" to-port="0" />
		<edge from-layer="152" from-port="0" to-layer="153" to-port="1" />
		<edge from-layer="153" from-port="2" to-layer="154" to-port="0" />
		<edge from-layer="154" from-port="1" to-layer="155" to-port="1" />
		<edge from-layer="155" from-port="2" to-layer="161" to-port="0" />
		<edge from-layer="156" from-port="0" to-layer="157" to-port="1" />
		<edge from-layer="157" from-port="2" to-layer="159" to-port="0" />
		<edge from-layer="158" from-port="0" to-layer="159" to-port="1" />
		<edge from-layer="159" from-port="2" to-layer="160" to-port="0" />
		<edge from-layer="160" from-port="1" to-layer="161" to-port="1" />
		<edge from-layer="161" from-port="2" to-layer="163" to-port="0" />
		<edge from-layer="162" from-port="0" to-layer="163" to-port="1" />
		<edge from-layer="163" from-port="2" to-layer="165" to-port="0" />
		<edge from-layer="164" from-port="0" to-layer="165" to-port="1" />
		<edge from-layer="165" from-port="2" to-layer="166" to-port="0" />
		<edge from-layer="166" from-port="1" to-layer="168" to-port="0" />
		<edge from-layer="167" from-port="0" to-layer="168" to-port="1" />
		<edge from-layer="168" from-port="2" to-layer="170" to-port="0" />
		<edge from-layer="169" from-port="0" to-layer="170" to-port="1" />
		<edge from-layer="170" from-port="2" to-layer="171" to-port="0" />
		<edge from-layer="171" from-port="1" to-layer="173" to-port="0" />
		<edge from-layer="172" from-port="0" to-layer="173" to-port="1" />
		<edge from-layer="173" from-port="2" to-layer="175" to-port="0" />
		<edge from-layer="174" from-port="0" to-layer="175" to-port="1" />
		<edge from-layer="175" from-port="2" to-layer="177" to-port="0" />
		<edge from-layer="176" from-port="0" to-layer="177" to-port="1" />
		<edge from-layer="177" from-port="2" to-layer="179" to-port="0" />
		<edge from-layer="178" from-port="0" to-layer="179" to-port="1" />
		<edge from-layer="179" from-port="2" to-layer="180" to-port="0" />
	</edges>
	<rt_info>
		<MO_version value="2022.3.0-9052-9752fafe8eb-releases/2022/3" />
		<Runtime_version value="2022.3.0-9052-9752fafe8eb-releases/2022/3" />
		<conversion_parameters>
			<data_type value="FP32" />
			<framework value="onnx" />
		</conversion_parameters>
		<framework>
			<names value="{0: '0-OTHERS', 1: '1-A2C', 2: '2-A3C', 3: '3-A4C'}" />
			<stride value="32" />
		</framework>
		<legacy_frontend value="False" />
	</rt_info>
</net>
